products:
  core:
    teamLead: <EMAIL>
    teamMembersEmails:
      - <EMAIL>
  allTimeReviewers:
    teamLead: <EMAIL>
    teamMembersEmails:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - am<PERSON><PERSON><PERSON>@wio.io
  neobroker:
    teamLead: <EMAIL>
    teamMembersEmails:
      - r<PERSON><PERSON><PERSON><PERSON><PERSON>.<EMAIL>
      - <EMAIL>
      - n<PERSON><PERSON><PERSON>@wio.io

  backoffice:
    teamLead: r<PERSON><PERSON><PERSON>@wio.io
    teamMembersEmails:
      - <EMAIL>

  retail:
    teamLead: <EMAIL>
    teamMembersEmails:
      - <EMAIL>
      - n<PERSON><PERSON><EMAIL>
      - <EMAIL>
      - <EMAIL>
    features:
      - name: RetailIntegrationTests
        teamLead: <EMAIL>
        teamMembersEmails:
          - n<PERSON><PERSON>@wio.io
  smeMobile:
    teamLead: r<PERSON><PERSON><PERSON>@wio.io
    teamMembersEmails:
      - d<PERSON><PERSON><PERSON>@wio.io
      - <EMAIL> 
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    features:
      - name: SmeCredit
        teamLead: <EMAIL>
        teamMembersEmails:
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>

  uiKit:
    teamLead: <EMAIL>
    teamMembersEmails:
      - <EMAIL>
  smeWeb:
    teamLead: <EMAIL>
    teamMembersEmails:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
    features:
      - name: SmeSubscription
        teamLead: <EMAIL>
        teamMembersEmails:
          - <EMAIL>
      - name: SmeMultiUser
        teamLead: <EMAIL>
        teamMembersEmails:
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
      - name: SMEDashboard
        teamLead: <EMAIL>
        teamMembersEmails:
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
  security:
    teamLead: <EMAIL>
    teamMembersEmails:
      - <EMAIL>

  payments:
    teamLead: <EMAIL>
    teamMembersEmails:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>

  account:
    teamLead: <EMAIL>
    teamMembersEmails:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>

  cards:
    teamLead: <EMAIL>
    teamMembersEmails:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
  
  lending:
    teamLead: <EMAIL>
    teamMembersEmails: 
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>

  ### DESIGNERS BELOW 
  uiKitMobileDesigners:
    teamLead: none
    teamMembersEmails:
      - <EMAIL>

  smeMobileDesigners:
    teamLead: none
    teamMembersEmails:
      - <EMAIL>

review:
  core:
    reviewers: 1
    paths:
      required:
        - path: core

  allTimeReviewers:
    reviewers: 1
    paths:
      required:
        - path: /

  neobroker:
    reviewers: 2
    paths:
      required:
        - path: neobroker
          teamLeadState: required

  retail:
    reviewers: 2
    paths:
      required:
        - path: retail
          teamLeadState: required

  backoffice:
    reviewers: 2
    paths:
      required:
        - path: back_office_platform
          teamLeadState: required

  smeWeb:
    reviewers: 1
    paths:
      exclude:
        - SME/features/feature_subscription
        - SME/core/multiuser
        - SME/features/feature_onboarding_employee
      required:
        - path: SME/desktop
          teamLeadState: optional
        - path: SME/features
          teamLeadState: optional
        - SME/core

  smeMobile:
    reviewers: 1
    paths:
      required:
        - path: SME/mobile
          teamLeadState: optional
        - path: SME/app-mobile-core
          teamLeadState: optional
        - path: SME/fastlane
          teamLeadState: optional

  SmeCredit:
    reviewers: 1
    paths:
      required:
        - path: SME/features/feature_credit
          teamLeadState: required
  
  lending:
    reviewers: 1
    paths:
      required:
        - path: SME/features/feature_credit
          teamLeadState: required
        - path: SME/embedded_lending
          teamLeadState: required
        - path: retail/features/feature_lending
          teamLeadState: required
        - path: common/features/feature_easy_cash
          teamLeadState: required
        - path: common/features/feature_loan
          teamLeadState: required

  uiKitMobileDesigners:
    reviewers: 1
    paths:
      required:
        - path: ui_kit_legacy/ui_kit_mobile/
          filePathRegex: .*\.png$
  smeMobileDesigners:
    reviewers: 1
    paths:
      required:
        - path: ui_kit_legacy/ui_kit_sme_mobile/
          teamLeadState: optional
          filePathRegex: .*\.png$

  SmeSubscription:
    reviewers: 1
    paths:
      required:
        - SME/features/feature_subscription

  SmeMultiUser:
    reviewers: 2
    paths:
      required:
        - common/features/feature_behaviour
        - SME/core/behaviour
        - SME/common/feature_mu_payment_requests
        - SME/features/feature_onboarding_employee
        - SME/features/feature_pending_requests
        - SME/features/feature_invitation
        - SME/features/feature_utap
        - SME/features/feature_wps
        - SME/features/feature_invoices_v2
        - SME/common/feature_faq
        - common/features/feature_faq
        - SME/common/feature_multi_signatory
        - SME/features/settings
        - SME/mobile/app_features/invoice
        - SME/mobile/app_features/settings

  RetailIntegrationTests:
    reviewers: 1
    paths:
      required:
      - retail/mobile/integration_test

  uiKit:
    reviewers: 1
    paths:
      required:
        - ui_kit_legacy

  security:
    reviewers: 1
    paths:
      required:
        - tooling/code_tools/resources/dependencies_standard.yaml
        - core/encryption

  payments:
    reviewers: 3
    paths:
      required:
        - path: common/features/feature_payments
          teamLeadState: required
        - path: SME/features/payment_v2
          teamLeadState: required
        - path: SME/features/feature_cash
          teamLeadState: optional
        - path: SME/features/bill
          teamLeadState: optional
        - path: retail/feature_payments
          teamLeadState: optional
        - path: retail/feature_payments_v2
          teamLeadState: required

  account:
    reviewers: 2
    paths:
      required:
        - path: SME/features/saving_space
          teamLeadState: required
        - path: SME/features/transaction
          teamLeadState: required
        - path: SME/features/statement
          teamLeadState: optional
        - path: SME/features/account
          teamLeadState: required
        - path: SME/features/feature_subscription
          teamLeadState: required
        - path: SME/features/feature_fx
          teamLeadState: optional
        - path: SME/features/feature_saving_space
          teamLeadState: required
        - path: retail/features/feature_account
          teamLeadState: required
        - path: retail/features/feature_carbon_calculator
          teamLeadState: required
        - path: retail/features/feature_saving_spaces
          teamLeadState: required
        - path: retail/features/feature_fx
          teamLeadState: optional
        - path: retail/features/feature_statement
          teamLeadState: optional
        - path: retail/features/feature_pricing_plan
          teamLeadState: optional
        - path: retail/features/feature_family_banking
          teamLeadState: required

  cards:
    reviewers: 2
    paths:
      required:
        - path: SME/features/card
          teamLeadState: required
        - path: retail/features/feature_rewards
          teamLeadState: required
        - path: retail/features/feature_carbon_calculator
          teamLeadState: required
        - path: retail/features/feature_cards
          teamLeadState: required

    SMEDashboard:
      reviewers: 3
      paths:
        required:
          - path: SME/features/feature_dashboard
            teamLeadState: required

  <EMAIL>:
    paths:
      optional:
        - tooling

  <EMAIL>:
    paths:
      required:
        - back_office_platform

  dkovalenko@wio:
    paths:
      required:
        - ui_kit_legacy/ui_kit_sme
        - retail/features/feature_lending
        - SME/features/feature_credit

  <EMAIL>:
    paths:
      required:
        - common/features/feature_transactions

leads:
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>