name: code_tools
description: Tools for code analysis.
version: 0.0.1
publish_to: none
environment:
  sdk: '>=3.6.0 <4.0.0'
dependencies:
  ansicolor: 2.0.3
  args: 2.6.0
  build_cli_annotations: 2.1.0
  code_builder: 4.10.1
  csv: 6.0.0
  dart_style: 2.3.7
  dependency_validator: 4.1.2
  dotenv: 4.2.0
  file: 7.0.0
  freezed_annotation: 2.4.4
  intl: 0.19.0
  json_annotation: 4.9.0
  logging_api:
    path: ../../core/logging/api
  path: 1.9.0
  pdf: 3.11.1
  process_run: 1.2.2+1
  rxdart: 0.28.0
  stack_trace: 1.12.0
  tests:
    path: ../../core/tests/impl
  tuple: 2.0.2
  wio_app_core_api:
    path: ../../core/app_core/api
  yaml: 3.1.3
  yaml_edit: 2.2.2
  yaml_writer: 2.0.1
dev_dependencies:
  build_cli: 2.2.4
  build_runner: 2.4.14
  core_lints:
    path: ../../tooling/core_lints
  fake_async: 1.3.1
  flutter_lints: 4.0.0
  freezed: 2.5.7
  json_serializable: 6.9.0
  mockito: 5.4.5
  mocktail: 1.0.4
  test: 1.25.8
