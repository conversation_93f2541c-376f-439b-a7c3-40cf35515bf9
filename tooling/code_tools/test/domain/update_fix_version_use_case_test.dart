import 'package:code_tools/code_tools.dart';
import 'package:code_tools/src/jira/dto/issue_dto.dart';
import 'package:mockito/mockito.dart';
import 'package:test/test.dart';

import '../utils.mocks.dart';

void main() {
  late MockJiraRepository repository;
  late UpdateFixVersionUseCase updateFixVersion;

  setUp(() async {
    repository = MockJiraRepository();
    updateFixVersion = UpdateFixVersionUseCaseImpl(repository);
  });

  IssueDto createIssue({required String key, required String projectId}) {
    return IssueDto(
      id: '123',
      key: 'SME-123',
      fields: IssueFieldsDto(
        project: ProjectDto(
          id: projectId,
          key: 'key-$projectId',
          name: 'Jira project',
        ),
      ),
    );
  }

  ProjectVersionDto createVersion({
    required String name,
  }) {
    return ProjectVersionDto(name: name);
  }

  test("if version is created, use case doesn't make the new one", () async {
    // arrange
    const projectId = '123';
    const versionName = 'sme-123';
    const issueKey = 'SME-123';
    final issue = createIssue(key: issueKey, projectId: projectId);
    final version = createVersion(name: versionName);

    when(repository.fetchIssue(any)).thenAnswer((_) async => issue);
    when(
      repository.fetchVersions(
        projectId: projectId,
        versionName: versionName,
      ),
    ).thenAnswer((_) async => [version]);

    // act
    await updateFixVersion(
      issueId: issueKey,
      versionName: versionName,
    );

    // assert
    verifyNever(
      repository.createVersion(
        projectId: anyNamed('projectId'),
        versionName: anyNamed('versionName'),
      ),
    );
    verify(
      repository.updateFixVersion(
        issueId: issueKey,
        fixVersionName: versionName,
      ),
    );
  });

  test(
      "if version hasn't been created, "
      'the use case creates the new one', () async {
    // arrange
    const projectId = '123';
    const versionName = 'sme-123';
    const issueKey = 'SME-123';
    final issue = createIssue(key: issueKey, projectId: projectId);

    when(repository.fetchIssue(any)).thenAnswer((_) async => issue);
    when(
      repository.fetchVersions(
        projectId: projectId,
        versionName: versionName,
      ),
    ).thenAnswer((_) async => []);

    when(
      repository.createVersion(
        versionName: versionName,
        projectId: projectId,
      ),
    ).thenAnswer((_) async {});

    // act
    await updateFixVersion(
      issueId: issueKey,
      versionName: versionName,
    );

    // assert
    verify(
      repository.createVersion(
        projectId: projectId,
        versionName: versionName,
      ),
    );
    verify(
      repository.updateFixVersion(
        issueId: issueKey,
        fixVersionName: versionName,
      ),
    );
  });
}
