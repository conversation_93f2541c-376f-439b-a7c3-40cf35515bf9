import 'package:code_tools/src/config/integration_test/integration_test_report_config.dart';
import 'package:code_tools/src/scripts/integration_test_script_runner.dart';

Future<void> main(List<String> arguments) async {
  final config = IntegrationTestReportConfig.parseArguments(arguments);

  await IntegrationTestScriptRunner.generateLocalTestReport(
    workingDirectory: config.path,
    jsonFilePath: config.jsonFilePath,
    outputPath: config.outputPath,
  );
}
