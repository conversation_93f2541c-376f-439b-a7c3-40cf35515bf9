import 'package:code_tools/code_tools.dart';
import 'package:code_tools/src/azure/azure_repository.dart';
import 'package:code_tools/src/azure/dto/commit_dto.dart';
import 'package:code_tools/src/azure/dto/thread_dto.dart';
import 'package:code_tools/src/performance_analayzer/metrics/metric_provider.dart';
import 'package:code_tools/src/performance_analayzer/performance_analyzer.dart';
import 'package:collection/collection.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'pull_request_metrics_provider.freezed.dart';

@freezed
class PullRequestMetrics with _$PullRequestMetrics implements Metric {
  @Implements<Metric>()
  const factory PullRequestMetrics({
    required int pullRequestId,
    required String author,
    required String title,
    required List<String> requiredReviewers,
    // from PR creation date to last commit in PR
    required Duration timeToComplete,
    // from creation to merge time
    required Duration totalPrTime,
    // from last commit to merge PR
    required Duration waitTimeToMerge,
    required int commitsCount,
    required int threadsCount,
    required double averageThreadsLength,
    required int iterationCount,
    required Duration averageIterationTime,
    required PullRequestPayload payload,
  }) = _PullRequestMetrics;
}

class PullRequestPayload {
  final List<CommitDto> commits;
  final List<ThreadDto> threads;

  const PullRequestPayload({
    required this.commits,
    required this.threads,
  });
}

class PullRequestMetricsProvider
    implements MetricProvider<PullRequestDto, PullRequestMetrics> {
  final AzureRepository _azureRepository;
  final WorkTimeCalculator _workTimeCalculator;

  const PullRequestMetricsProvider({
    required AzureRepository azureRepository,
    required WorkTimeCalculator workTimeCalculator,
  })  : _azureRepository = azureRepository,
        _workTimeCalculator = workTimeCalculator;

  @override
  Future<PullRequestMetrics> getMetric(PullRequestDto params) async {
    // threads in PR metrics
    final commits = await _getPullRequestCommits(params.pullRequestId);
    final lastCommitDate = commits.first.committer?.date;

    final totalPrTime =
        _getTimeToComplete(params.creationDate, params.closedDate);
    final timeToComplete = _getTimeToComplete(
      params.creationDate,
      lastCommitDate,
    );

    final waitTimeToMerge = _getTimeToComplete(
      lastCommitDate,
      params.closedDate,
    );
    final threads = await _getPullRequestThreads(params.pullRequestId);
    final averageThreadsLength = _getAvarageThreadsLength(threads);

    // iterations in PR metrics
    final iterations = await _getIterations(params.pullRequestId);

    final averageIterationTime = _getAvarageIterationMilliseconds(iterations);

    return PullRequestMetrics(
      pullRequestId: params.pullRequestId,
      author: params.author.email,
      title: params.title,
      requiredReviewers: params.reviewers
          .where((e) => e.isRequired)
          .map((e) => e.email)
          .toList(),
      totalPrTime: totalPrTime,
      // If PR has one commit that was done before PR creation
      timeToComplete:
          timeToComplete < Duration.zero ? totalPrTime : timeToComplete,
      waitTimeToMerge: waitTimeToMerge,
      commitsCount: commits.length,
      threadsCount: threads.length,
      averageThreadsLength: averageThreadsLength,
      iterationCount: iterations.length,
      averageIterationTime: Duration(milliseconds: averageIterationTime),
      payload: PullRequestPayload(
        commits: commits,
        threads: threads,
      ),
    );
  }

  Future<List<CommitDto>> _getPullRequestCommits(int pullRequestId) async {
    final commits = await _azureRepository.getPullRequestCommits(
      pullRequestId: pullRequestId,
    );

    return commits;
  }

  Future<List<ThreadDto>> _getPullRequestThreads(int pullRequestId) async {
    final threads = await _azureRepository.getPullRequestThreads(
      pullRequestId: pullRequestId,
    );

    return threads
        .where((thread) =>
            thread.comments.isNotEmpty &&
            thread.comments.first.commentType == CommentType.text &&
            !(thread.comments.first.content ?? '').startsWith('#'))
        .toList();
  }

  double _getAvarageThreadsLength(List<ThreadDto> threads) {
    return threads.isNotEmpty
        ? threads
                .map((e) => e.comments.length)
                .reduce((acc, threadLength) => acc + threadLength) /
            threads.length
        : 0.0;
  }

  Future<List<PullRequestIterationDto>> _getIterations(
    int pullRequestId,
  ) async {
    final iterations = await _azureRepository
        .getUserPullRequestIterations(pullRequestId)
        .then((value) => value..sortBy((element) => element.createdDate));

    return iterations.removeBlowouts().toList();
  }

  int _getAvarageIterationMilliseconds(
    List<PullRequestIterationDto> iterations,
  ) {
    return iterations.length >= 2
        ? iterations.reversed.mapIndexed((index, iteration) {
              final nextIteration = iterations.length - index - 2;
              if (nextIteration >= 0) {
                final currentIteration = iteration.createdDate;
                final prevIteration = iterations[nextIteration].createdDate;

                return currentIteration
                    .difference(prevIteration)
                    .inMilliseconds;
              } else {
                return 0;
              }
            }).sum ~/
            iterations.length
        : 0;
  }

  Duration _getTimeToComplete(
    DateTime? firstCommitDate,
    DateTime? lastCommitDate,
  ) {
    if (firstCommitDate == null || lastCommitDate == null) {
      return Duration.zero;
    }

    return _workTimeCalculator.getWorkingTimeBetween(
      firstCommitDate,
      lastCommitDate,
    );
  }
}

extension<T> on List<T> {
  List<T> removeBlowouts() {
    final rate = (length * 0.1).toInt() ~/ 2;
    final filterLowerBound = skip(rate);

    return filterLowerBound.take(filterLowerBound.length - rate).toList();
  }
}
