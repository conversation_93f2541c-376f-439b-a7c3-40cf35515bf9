// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_rc_branch_config.dart';

// **************************************************************************
// CliGenerator
// **************************************************************************

CreateReleaseBranchConfig _$parseCreateReleaseBranchConfigResult(
        ArgResults result) =>
    CreateReleaseBranchConfig(
      product: result['product'] as String,
      version: result['version'] as String,
    );

ArgParser _$populateCreateReleaseBranchConfigParser(ArgParser parser) => parser
  ..addOption(
    'product',
    help: 'Product code. Ex: SME, SME-Web, Retail',
  )
  ..addOption(
    'version',
    help: 'New version to be created. Ex: 2024.78.0',
  );

final _$parserForCreateReleaseBranchConfig =
    _$populateCreateReleaseBranchConfigParser(ArgParser());

CreateReleaseBranchConfig parseCreateReleaseBranchConfig(List<String> args) {
  final result = _$parserForCreateReleaseBranchConfig.parse(args);
  return _$parseCreateReleaseBranchConfigResult(result);
}
