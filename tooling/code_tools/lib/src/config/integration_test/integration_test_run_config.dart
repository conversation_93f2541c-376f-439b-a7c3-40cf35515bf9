import 'package:args/args.dart';

class IntegrationTestRunConfig {
  static const _defaultDriverPath = 'integration_test/driver.dart';
  static const _defaultTargetPath = 'integration_test/runner.dart';
  static const _iosPrebuiltType = 'ios';
  static const _androidPrebuiltType = 'android';
  static const _prebuiltPaths = {
    _iosPrebuiltType: 'build/ios/iphonesimulator/Runner.app',
    _androidPrebuiltType: 'build/app/outputs/flutter-apk/app-dev-debug.apk',
  };

  final String path;
  final String driverPath;
  final String targetPath;
  final String tag;
  final String flavor;
  final String port;
  final String device;
  final bool verbose;
  final bool web;
  final String testEnv;
  final bool cleanCache;
  final String prebuiltAppPath;
  final String consumerSecret;

  const IntegrationTestRunConfig({
    required this.path,
    required this.driverPath,
    required this.targetPath,
    required this.tag,
    required this.flavor,
    required this.port,
    required this.verbose,
    required this.device,
    required this.web,
    required this.testEnv,
    required this.cleanCache,
    required this.prebuiltAppPath,
    required this.consumerSecret,
  });

  //ignore: prefer_constructors_over_static_methods
  static IntegrationTestRunConfig parseArguments(List<String> arguments) {
    final argParser = ArgParser()
      ..addOption(
        'path',
        help: 'Root path for execute integration tests',
      )
      ..addOption('driverPath', help: 'Path to integration test driver')
      ..addOption('targetPath', help: 'Path to integration test runner')
      ..addOption('tag', help: 'Tag for running part of tests')
      ..addOption('flavor', help: 'Flutter application flavor')
      ..addOption('port', help: 'Port for running tests, only for web tests')
      ..addOption('verbose', help: 'Enabling detailed logs')
      ..addOption('device', help: 'Device for tests running')
      ..addOption('web', help: 'Flag for running web tests')
      ..addOption('testEnv', help: 'Flag for set environment')
      ..addOption('cleanCache', help: 'Clean cache before test running')
      ..addOption('usePrebuiltApp', help: 'Flag for prebuilt app')
      ..addOption('consumerSecret', help: 'Secret for prebuilt app');

    final argResults = argParser.parse(arguments);
    final driverPath = (argResults['driverPath'] ?? '') as String;
    final targetPath = (argResults['targetPath'] ?? '') as String;
    final prebuiltAppType = (argResults['usePrebuiltApp'] ?? '') as String;

    return IntegrationTestRunConfig(
      path: (argResults['path'] ?? '.') as String,
      driverPath: driverPath.isEmpty ? _defaultDriverPath : driverPath,
      targetPath: targetPath.isEmpty ? _defaultTargetPath : targetPath,
      tag: (argResults['tag'] ?? '') as String,
      flavor: (argResults['flavor'] ?? '') as String,
      port: (argResults['port'] ?? '') as String,
      device: (argResults['device'] ?? '') as String,
      verbose: argResults['verbose'] == 'true',
      web: argResults['web'] == 'true',
      testEnv: (argResults['testEnv'] ?? '') as String,
      cleanCache: argResults['cleanCache'] != 'false',
      prebuiltAppPath: _prebuiltPaths[prebuiltAppType] ?? prebuiltAppType,
      consumerSecret: (argResults['consumerSecret'] ?? '') as String,
    );
  }
}
