import 'package:args/args.dart';

class FailureReporterConfig {
  final String message;
  final String slackToken;

  const FailureReporterConfig({
    required this.message,
    required this.slackToken,
  });

  factory FailureReporterConfig.parseArguments(List<String> arguments) {
    final argParser = ArgParser()
      ..addOption(
        'message',
        help: 'Failure message',
        mandatory: true,
      )
      ..addOption(
        'slackChannelToken',
        help: 'Slack application channel token',
        mandatory: true,
      );

    final argResults = argParser.parse(arguments);

    return FailureReporterConfig(
      message: argResults['message'] as String,
      slackToken: argResults['slackChannelToken'] as String,
    );
  }
}
