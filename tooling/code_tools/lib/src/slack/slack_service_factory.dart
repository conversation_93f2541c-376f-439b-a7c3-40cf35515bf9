import 'package:code_tools/src/logger/logger_wrapper.dart';
import 'package:code_tools/src/slack/slack_service.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';

class SlackServiceFactory {
  static SlackService get(String token) {
    final httpClient = DioClient()
      ..initialize(
        config: RestApiConfig(
          host: 'https://slack.com',
          headers: <String, String>{
            'Content-Type': 'application/json; charset=utf-8',
            'Authorization': 'Bearer $token',
          },
        ),
        interceptors: [
          LoggingInterceptor(
            logger: LoggerWrapperImpl(),
            doSendRequestBody: true,
            doSendResponseBody: true,
            doFilterHeaders: true,
          ),
        ],
      );

    return SlackService(httpClient);
  }
}
