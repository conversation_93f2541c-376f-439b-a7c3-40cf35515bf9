import 'package:json_annotation/json_annotation.dart';

part 'identity_dto.g.dart';

@JsonSerializable()
class IdentityDto {
  final String id;
  final String email;
  final String providerDisplayName;

  factory IdentityDto.fromJson(Map<String, dynamic> json) {
    return _IdentityDtoExtension.identityFromJson(json);
  }

  const IdentityDto(
    this.id,
    this.email,
    this.providerDisplayName,
  );

  Map<String, dynamic> toJson() => _$IdentityDtoToJson(this);
}

extension _IdentityDtoExtension on IdentityDto {
  static const _idKey = 'id';
  static const _propertiesKey = 'properties';
  static const _mailKey = 'Mail';
  static const _valueKey = '\$value';
  static const _providerDisplayNameKey = 'providerDisplayName';

  static IdentityDto identityFromJson(Map<String, dynamic> json) {
    return IdentityDto(
      json[_idKey] as String,
      json[_propertiesKey][_mailKey][_valueKey] as String,
      //ignore: avoid_dynamic_calls
      json[_providerDisplayNameKey] as String,
    );
  }
}
