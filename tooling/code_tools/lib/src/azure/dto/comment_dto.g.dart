// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'comment_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CommentDto _$CommentDtoFromJson(Map<String, dynamic> json) => CommentDto(
      parentCommentId: json['parentCommentId'] as int?,
      content: json['content'] as String?,
      id: json['id'] as int?,
      commentType: json['commentType'] as String?,
    );

Map<String, dynamic> _$CommentDtoToJson(CommentDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'parentCommentId': instance.parentCommentId,
      'commentType': instance.commentType,
      'content': instance.content,
    };
