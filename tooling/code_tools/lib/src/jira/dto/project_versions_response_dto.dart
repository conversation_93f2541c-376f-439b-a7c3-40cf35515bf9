import 'package:code_tools/src/jira/dto/project_version_dto.dart';
import 'package:json_annotation/json_annotation.dart';

part 'project_versions_response_dto.g.dart';

@JsonSerializable()
class ProjectVersionsResponseDto {
  final int total;
  final List<ProjectVersionDto> values;

  const ProjectVersionsResponseDto({
    required this.total,
    required this.values,
  });

  factory ProjectVersionsResponseDto.fromJson(Map<String, dynamic> json) =>
      _$ProjectVersionsResponseDtoFromJson(json);
}
