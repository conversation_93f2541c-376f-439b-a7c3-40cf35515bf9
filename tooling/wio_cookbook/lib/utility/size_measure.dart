import 'dart:async';

import 'package:flutter/material.dart';

/// An utility widget that displays child widget's size as a text under it.
class SizeMeasure extends StatefulWidget {
  final Widget child;
  final Color? textColor;

  const SizeMeasure({
    Key? key,
    required this.child,
    this.textColor,
  }) : super(key: key);

  @override
  State<SizeMeasure> createState() => _SizeMeasureState();
}

class _SizeMeasureState extends State<SizeMeasure> {
  final GlobalKey _globalKey = GlobalKey();
  final StreamController<Size?> _sizeStreamController =
      StreamController<Size?>();

  @override
  void dispose() {
    super.dispose();
    _sizeStreamController.close();
  }

  @override
  Widget build(BuildContext context) {
    _notifySizeChanged();

    return Column(
      children: [
        NotificationListener<SizeChangedLayoutNotification>(
          onNotification: _onSizeChangeNotification,
          child: SizeChangedLayoutNotifier(
            key: _globalKey,
            child: widget.child,
          ),
        ),
        const SizedBox(height: 8),
        StreamBuilder(
          stream: _sizeStreamController.stream,
          builder: (context, snapshot) {
            final style = TextStyle(color: widget.textColor);
            if (!snapshot.hasData) {
              return Text('No data', style: style);
            }

            return Text(snapshot.data.toString(), style: style);
          },
        ),
      ],
    );
  }

  bool _onSizeChangeNotification(SizeChangedLayoutNotification notification) {
    _notifySizeChanged();

    return false;
  }

  void _notifySizeChanged() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final inputContext = _globalKey.currentContext;
      if (inputContext != null) {
        _sizeStreamController.add(inputContext.size);
      }
    });
  }
}
