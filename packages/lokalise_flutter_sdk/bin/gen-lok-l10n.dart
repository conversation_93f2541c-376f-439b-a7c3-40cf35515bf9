// ignore_for_file: file_names

library lokalise_flutter_sdk;

import 'dart:io';
import 'package:lokalise_flutter_sdk/src/generator/generator.dart';
import 'package:lokalise_flutter_sdk/src/generator/generator_config.dart';
import 'package:lokalise_flutter_sdk/src/generator/exceptions/generator_exception.dart';
import 'package:lokalise_flutter_sdk/src/generator/logger.dart';
import 'package:path/path.dart';

Future<void> main(List<String> args) async {
  try {
    final generator = Generator(config: _getConfig(args));
    await generator.generate();
  } on GeneratorException catch (e) {
    Logger.instance.error(e.message);
    exit(1);
  } catch (e) {
    Logger.instance.error(
      'Failed to generate localization files.\n$e',
    );
    exit(2);
  }
}

GeneratorConfig _getConfig(List<String> args) {
  // Added by wio team to have possibility use args for config generator
  if (args.isNotEmpty) {
    return _getConfigFromArgs(args);
  }
  final configFilePaths = [
    // file in root directory
    GeneratorConfig.configFile,
    // file in lib/l10n (to keep compatibility)
    join(
      Directory.current.path,
      'lib',
      'l10n',
      GeneratorConfig.configFile,
    ),
  ];

  for (final path in configFilePaths) {
    final file = File(path);
    if (file.existsSync()) {
      return GeneratorConfig.fromConfigFile(file: file);
    }
  }

  return GeneratorConfig.byDefault();
}

GeneratorConfig _getConfigFromArgs(List<String> args) {
  String? parseArgument(String prefix) {
    return args
        .firstWhere((arg) => arg.startsWith(prefix), orElse: () => '')
        .substring(prefix.length);
  }

  // Define the argument prefixes
  const outputLocalizationFilePrefix = '--output-localization-file=';
  const outputClassPrefix = '--output-class=';
  const arbDirPrefix = '--arb-dir=';
  const outputDirPrefix = '--output-dir=';

  // Parse the arguments
  final generatedFileName = parseArgument(outputLocalizationFilePrefix);
  final generatedClassName = parseArgument(outputClassPrefix);
  final arbDir = parseArgument(arbDirPrefix);
  final outputDir = parseArgument(outputDirPrefix);

  final config = GeneratorConfig(
    outputLocalizationFile: generatedFileName,
    outputClass: generatedClassName,
    arbDir: arbDir,
    outputDir: outputDir,
  );

  return config;
}
