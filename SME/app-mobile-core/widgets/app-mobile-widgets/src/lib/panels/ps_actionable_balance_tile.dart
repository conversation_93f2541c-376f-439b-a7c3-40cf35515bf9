import 'package:core/utils/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:widget_library/balance/balance_widget.dart';
import 'package:widget_library/image/image_widget.dart';
import 'package:widget_library/page_header/text_ui_data_model.dart';
import 'package:widget_library/static_text/PSText.dart';
import 'package:widget_library/theme/ps_theme.dart';
import 'package:widget_library/vertical_actionbar/ps_vertical_action_bar.dart';

class _Constants {
  static const statusLedSize = 10.0;
  static const statusLedPadding = 8.0;
  static const leftMargin = 20.0;
  static const topMargin = 18.0;
  static const tileWidth = 200.0;
  static const tileHeight = 200.0;
  static const rowHeight = 30.0;
  static const String _PSActionablePanelDefaultActionIconUri =
      'widget_library:assets/images/right_arrow.svg';
}

/// PSActionableBalanceTile
///
/// Stands for a square corner rounded shaped tapable tile
///
/// [Figma link](https://www.figma.com/file/dkgof6uGT1YenCvsmTJBZT/Neobank-App-Sapient-Handoff?node-id=1%3A1097)
///
///```
/// +-----------------------------------+
/// | topTitleImage                     |
/// | amount currency                   |
/// | topSubtitle                       |
/// |                                   |
/// |                                   |
/// | btmSubtitleImage btmSubtitle      |
/// | navActionModel.title        .icon |
/// +-----------------------------------+
/// ```
class PSActionableBalanceTile extends StatelessWidget {
  final PSActionableBalanceTileAttributes attributes;

  const PSActionableBalanceTile({
    Key? key,
    required this.attributes,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => attributes.navigationActionModel.action?.call(),
      child: Container(
        width: _Constants.tileWidth,
        height: _Constants.tileHeight,
        margin: attributes.tileInsets,
        padding: const EdgeInsets.symmetric(
          vertical: _Constants.topMargin,
          horizontal: _Constants.leftMargin,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: ADQTheme().themeData.colorPalette?.panelColorPrimary.toColor(),
        ),
        child: Column(
          children: [
            if (attributes.topSubtitle != null)
              _TileSubtitle(
                topSubtitle: attributes.topSubtitle!,
                topTitleImage: attributes.topTitleImage,
                statusColor: attributes.statusColor,
              ),
            if (attributes.topTitleImage != null)
              _TitleImage(attributes.topTitleImage!),
            _TileBalance(
              amount: attributes.amount,
              currency: attributes.currency,
              topTitleImage: attributes.topTitleImage,
              isCurrencyTopAligned: attributes.isCurrencyTopAligned,
            ),
            if (attributes.bottomSubtitle != null)
              _BottomSubtitle(
                bottomSubtitle: attributes.bottomSubtitle,
                bottomSubtitleImage: attributes.bottomSubtitleImage,
              ),
            const Expanded(child: SizedBox.shrink()),
            _BottomNavigationRow(actionModel: attributes.navigationActionModel),
          ],
        ),
      ),
    );
  }
}

class _TitleImage extends StatelessWidget {
  final PSImageModel topTitleImage;

  const _TitleImage(this.topTitleImage, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: PSImage(topTitleImage),
    );
  }
}

class _TileBalance extends StatelessWidget {
  final TextUIDataModel amount;
  final TextUIDataModel currency;
  final PSImageModel? topTitleImage;
  final bool? isCurrencyTopAligned;
  const _TileBalance({
    Key? key,
    required this.amount,
    required this.currency,
    this.topTitleImage,
    this.isCurrencyTopAligned,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BalanceWidget(
      amount: amount.text,
      amountTextStyleVariant:
          amount.styleVariant ?? PSTextStyleVariant.headline1,
      currency: currency.text,
      currencyTextStyleVariant:
          currency.styleVariant ?? PSTextStyleVariant.subtitle1,
      isCurrencyTopAligned: isCurrencyTopAligned ?? false,
    );
  }
}

class _TileSubtitle extends StatelessWidget {
  final PSImageModel? topTitleImage;
  final TextUIDataModel topSubtitle;
  final Color? statusColor;

  const _TileSubtitle({
    Key? key,
    this.topTitleImage,
    required this.topSubtitle,
    this.statusColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Container(
            margin: const EdgeInsets.only(right: _Constants.statusLedPadding),
            height: _Constants.statusLedSize,
            width: _Constants.statusLedSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: statusColor ?? Colors.red,
            ),
          ),
          PSText(text: topSubtitle),
        ],
      ),
    );
  }
}

class _BottomSubtitle extends StatelessWidget {
  final PSImageModel? bottomSubtitleImage;
  final TextUIDataModel? bottomSubtitle;

  const _BottomSubtitle({
    Key? key,
    this.bottomSubtitleImage,
    this.bottomSubtitle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if (bottomSubtitleImage != null) PSImage(bottomSubtitleImage!),
        if (bottomSubtitle != null) PSText(text: bottomSubtitle!),
      ],
    );
  }
}

class _BottomNavigationRow extends StatelessWidget {
  final ActionModel actionModel;

  const _BottomNavigationRow({
    Key? key,
    required this.actionModel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        PSText(
          text: TextUIDataModel(
            actionModel.title!,
          ),
        ),
        PSImage(
          PSImageModel(
            iconPath: actionModel.iconUri ??
                _Constants._PSActionablePanelDefaultActionIconUri,
            width: 20,
          ),
        ),
      ],
    );
  }
}

class PSActionableBalanceTileAttributes {
  final TextUIDataModel amount;
  final Color? backgroundColor;
  final Color? statusColor;
  final TextUIDataModel? bottomSubtitle;
  final PSImageModel? bottomSubtitleImage;
  final TextUIDataModel currency;
  final Decoration? tileDecoration;
  final EdgeInsets? tileInsets;
  final ActionModel navigationActionModel;
  final double? tileWidth;
  final TextUIDataModel? topSubtitle;
  final PSImageModel? topTitleImage;
  final bool? isCurrencyTopAligned;

  PSActionableBalanceTileAttributes({
    required this.amount,
    this.backgroundColor,
    this.statusColor,
    this.bottomSubtitle,
    this.bottomSubtitleImage,
    required this.currency,
    required this.navigationActionModel,
    this.tileDecoration,
    this.tileInsets,
    this.tileWidth,
    this.topSubtitle,
    this.topTitleImage,
    this.isCurrencyTopAligned,
  });
}
