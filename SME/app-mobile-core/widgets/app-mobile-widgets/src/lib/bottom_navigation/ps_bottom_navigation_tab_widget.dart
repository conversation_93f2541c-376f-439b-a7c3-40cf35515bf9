import 'package:flutter/material.dart';
import 'package:widget_library/bottom_navigation/ps_bottom_navigation_tab_widget_attributes.dart';
import 'package:widget_library/scaffold/ps_scaffold.dart';

class _Constants {
  static const borderRadius = 32.0;
  static const shadowRadius = 20.0;
  static const snackBarMessage = 'Press again to exit app';
}

class PSBottomNavigationTabWidget extends StatefulWidget {
  const PSBottomNavigationTabWidget({
    Key? key,
    required this.attributes,
  }) : super(key: key);

  final PSBottomNavigationAttributes attributes;

  @override
  _PSBottomNavigationTabWidgetState createState() =>
      _PSBottomNavigationTabWidgetState();
}

class _PSBottomNavigationTabWidgetState
    extends State<PSBottomNavigationTabWidget> {
  final radius = const Radius.circular(_Constants.borderRadius);
  DateTime currentBackPressTime = DateTime.now();

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      key: const Key('PSBottomNavigationTabWidget_WillPopScope'),
      onWillPop: () => onWillPop(context),
      child: buildBottomBar(context),
    );
  }

  Future<bool> onWillPop(BuildContext context) {
    if (widget.attributes.tabIndex == 0) {
      if (DateTime.now().difference(currentBackPressTime).inSeconds > 2) {
        currentBackPressTime = DateTime.now();
        _showSnackBar(context);
        return Future.value(false);
      }
      return Future.value(true);
    } else {
      widget.attributes.goBack?.call();
      return Future.value(false);
    }
  }

  void _showSnackBar(BuildContext context) {
    final showMessage = ScaffoldMessenger.of(context);
    showMessage.showSnackBar(
      const SnackBar(
        key: Key('PSBottomNavigationTabWidget_SnackBar'),
        content: Text(_Constants.snackBarMessage, key: Key('Text')),
        duration: Duration(seconds: 2),
      ),
    );
  }

  Widget buildBottomBar(BuildContext context) {
    return PSScaffold(
      themeName: widget.attributes.themeName,
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          IndexedStack(
            key: const Key(
                'PSBottomNavigationTabWidget_BuildBottomBar_IndexedStack'),
            index: widget.attributes.tabIndex,
            children: widget.attributes.tabScreens,
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: bottomNavigationBar,
          ),
        ],
      ),
    );
  }

  Widget get bottomNavigationBar {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topRight: Radius.circular(_Constants.borderRadius),
        topLeft: Radius.circular(_Constants.borderRadius),
      ),
      child: Container(
        decoration: const BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Color.fromRGBO(244, 244, 240, 0.5),
              spreadRadius: 20,
              offset: Offset(0, -10),
              blurRadius: _Constants.shadowRadius,
            ),
          ],
        ),
        child: BottomNavigationBar(
          showSelectedLabels: false,
          showUnselectedLabels: false,
          backgroundColor: widget.attributes.tabBackgroundColor,
          type: BottomNavigationBarType.fixed,
          currentIndex: widget.attributes.tabIndex,
          onTap: (index) {
            widget.attributes.onTap(index);
          },
          items: widget.attributes.bottomNavigation,
        ),
      ),
    );
  }
}
