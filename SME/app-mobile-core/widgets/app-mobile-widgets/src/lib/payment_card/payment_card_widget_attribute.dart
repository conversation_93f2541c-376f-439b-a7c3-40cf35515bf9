import 'package:flutter/material.dart';

class PaymentCardWidgetAttribute {
  final String? headerIcon;
  final String? headerTitle;
  final String? headerCtaIcon;
  final String? rowIcon;
  final String? rowTitlePreFix;
  final String? rowTitle;
  final String? rowSubTitle;
  final String? rowDate;
  final String? amount;
  final String? currency;
  final Color borderColor;
  final double borderWidth;
  final double iconHeight;
  final double iconWidth;
  final Function()? onUnlinkTransactionTap;

  PaymentCardWidgetAttribute({
    this.headerIcon,
    this.rowTitlePreFix,
    this.headerTitle,
    this.headerCtaIcon,
    this.rowIcon,
    this.rowTitle,
    this.rowSubTitle,
    this.rowDate,
    this.amount,
    this.currency,
    this.borderColor = const Color(0xff5500F9),
    this.borderWidth = 1,
    this.iconHeight = 36,
    this.iconWidth = 36,
    this.onUnlinkTransactionTap,
  });

  PaymentCardWidgetAttribute copyWith({
    String? headerIcon,
    String? headerTitle,
    String? headerCtaIcon,
    String? rowIcon,
    String? rowTitlePreFix,
    String? rowTitle,
    String? rowSubTitle,
    String? rowDate,
    String? amount,
    Color? backgroundColor,
    Color? borderColor,
    double? borderWidth,
    double? iconHeight,
    double? iconWidth,
    String? currency,
    Function()? onUnlinkTransactionTap,
  }) {
    return PaymentCardWidgetAttribute(
      headerIcon: headerIcon ?? this.headerIcon,
      headerTitle: headerTitle ?? this.headerTitle,
      headerCtaIcon: headerCtaIcon ?? this.headerCtaIcon,
      rowIcon: rowIcon ?? this.rowIcon,
      rowTitle: rowTitle ?? this.rowTitle,
      rowTitlePreFix: rowTitlePreFix ?? this.rowTitlePreFix,
      rowSubTitle: rowSubTitle ?? this.rowSubTitle,
      rowDate: rowDate ?? this.rowDate,
      amount: amount ?? this.amount,
      borderColor: borderColor ?? this.borderColor,
      borderWidth: borderWidth ?? this.borderWidth,
      iconHeight: iconHeight ?? this.iconHeight,
      iconWidth: iconWidth ?? this.iconWidth,
      onUnlinkTransactionTap:
          onUnlinkTransactionTap ?? this.onUnlinkTransactionTap,
      currency: currency ?? this.currency,
    );
  }
}
