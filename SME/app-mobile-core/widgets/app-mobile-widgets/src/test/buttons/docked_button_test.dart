import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';
import 'package:widget_library/buttons/docked_button.dart';
import 'package:widget_library/buttons/docked_button_bar.dart';

import '../test_helpers.dart';

void main() {
  testWidgets('PSDockedButton renders with correct text', (tester) async {
    const text = 'Button text';

    final dockedButton = PSDockedButton(
      onPressed: () {},
      title: text.toUpperCase(),
    );

    await tester.pumpWidget(PSThemeWrapper(child: dockedButton));

    expect(find.byWidget(dockedButton), findsOneWidget);
    expect(find.text(text.toUpperCase()), findsOneWidget);
  });

  testWidgets('PSDockedButton can be pressed', (tester) async {
    final callbackMock = VoidCallbackMock();

    final dockedButton = PSDockedButton(
      onPressed: callbackMock,
      title: 'Button text',
    );

    final dockedButtonFinder = find.byWidget(dockedButton);

    await tester.pumpWidget(PSThemeWrapper(child: dockedButton));

    await tester.tap(dockedButtonFinder);

    expect(dockedButtonFinder, findsOneWidget);
    verify(callbackMock()).called(1);
  });

  group(
    'Golden Tests',
    () {
      testGoldens('Docked buttons should render correctly', (tester) async {
        final builder = GoldenBuilder.column()
          ..addScenario(
            'Default',
            PSDockedButtonBar(
              attributes: PSDockedButtonBarAttributes([
                PSDockedButtonBarElementDetails(
                  text: 'DEFAULT',
                  onPressed: () {},
                ),
              ]),
            ),
          )
          ..addScenario(
            'Disabled',
            PSDockedButtonBar(
              attributes: PSDockedButtonBarAttributes([
                PSDockedButtonBarElementDetails(
                  text: 'DISABLED',
                ),
              ]),
            ),
          );
        await tester.pumpWidgetBuilder(
          builder.build(),
          wrapper: (child) => PSThemeWrapper(child: child),
        );
        await screenMatchesGolden(tester, 'docked_button_single');
      });

      testGoldens('Two docked buttons should render correctly', (tester) async {
        final builder = GoldenBuilder.column()
          ..addScenario(
            'Default',
            PSDockedButtonBar(
              attributes: PSDockedButtonBarAttributes([
                PSDockedButtonBarElementDetails(
                  text: 'BUTTON 1',
                  onPressed: () {},
                ),
                PSDockedButtonBarElementDetails(
                  text: 'BUTTON 2',
                ),
              ]),
            ),
          );
        await tester.pumpWidgetBuilder(
          builder.build(),
          wrapper: (child) => PSThemeWrapper(child: child),
        );
        await screenMatchesGolden(tester, 'docked_button_multiple');
      });
    },
  );
}
