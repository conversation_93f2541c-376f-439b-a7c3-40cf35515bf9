import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/index.dart';

enum SmeDeepLinkAnalyticsEvent {
  deepLinkProcessingStarted('deep_link_processing_started'),
  deepLinkLaunched('deep_link_launched'),
  deepLinkCompleted('deep_link_completed'),
  deepLinkFailed('deep_link_failed'),
  saveUnauthenticatedUserDeeplink('save_unauthenticated_user_deeplink');

  final String value;

  const SmeDeepLinkAnalyticsEvent(this.value);
}

class SmeDeepLinkAnalytics {
  final AnalyticsEventTracker _analyticsEventTracker;

  const SmeDeepLinkAnalytics({
    required AnalyticsEventTracker analyticsEventTracker,
  }) : _analyticsEventTracker = analyticsEventTracker;

  void trackDeepLinkEvent(
    SmeDeepLinkAnalyticsEvent event,
    DeepLink deepLink, [
    String? error,
  ]) {
    return _analyticsEventTracker.track(
      AnalyticsEvent.simple(
        event.value,
        properties: {
          'deepLink': deepLink.toString(),
          if (error != null) 'error': error,
        },
      ),
    );
  }
}
