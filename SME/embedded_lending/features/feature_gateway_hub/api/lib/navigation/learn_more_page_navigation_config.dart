import 'package:embedded_lending_core_api/embedded_lending_core_api.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_gateway_hub_api/navigation/gateway_hub_feature_navigation_config.dart';

/// Loan learn more page navigation config
class LearnMorePageNavigationConfig extends ScreenNavigationConfig {
  /// screen name
  static const screenName = 'learn_more_page';

  /// the reference id to be used while creating loan application
  final String externalReferenceId;

  /// Partner
  final Partner partner;

  /// check if the onboarding is enabled
  final bool isOnboardingEnabled;

  /// constructor
  const LearnMorePageNavigationConfig({
    required this.externalReferenceId,
    required this.partner,
    required this.isOnboardingEnabled,
  }) : super(
          id: screenName,
          feature: GatewayHubFeatureNavigationConfig.name,
        );

  @override
  String toString() => 'LearnMorePageNavigationConfig';
}
