// ignore_for_file: avoid_implementing_value_types

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:embedded_lending_core_ui/index.dart';
import 'package:feature_login_api/feature_login_api.dart';
import 'package:flutter/widgets.dart';
import 'package:mocktail/mocktail.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_adaptive_status_view_api/feature_status_view_adaptive_api.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_gateway_hub_api/gateway_hub_api.dart';
import 'package:wio_feature_gateway_hub_ui/l10n/gateway_hub_localizations.g.dart';
import 'package:wio_feature_gateway_hub_ui/src/utils/auth_proxy.dart';

class MockLoginInteractor extends Mock implements LoginInteractor {}

class MockBorrowingPowerFlowHandler extends Mock
    implements BorrowingPowerFlowHandler {}

class MockLoanConsentInteractor extends Mock implements LoanConsentInteractor {}

class MockCreditApplicationCreator extends Mock
    implements CreditApplicationCreator {}

class MockCreditApplicationInteractor extends Mock
    implements CreditApplicationInteractor {}

class MockCreditAccountInteractor extends Mock
    implements CreditAccountInteractor {}

class MockNavigationProvider extends Mock implements NavigationProvider {}

class MockGatewayHubLocalizations extends Mock
    implements GatewayHubLocalizations {}

class MockRebirther extends Mock implements Rebirther {}

class MockAuthProxy extends Mock implements AuthProxy {}

class FakeStatusViewConfig extends Fake
    implements StatusViewScreenConfiguration {}

class FakeCreditApplicationNavigationConfig extends Fake
    implements CreditApplicationNavigationConfig {
  @override
  String toString() => 'FakeCreditApplicationNavigationConfig';
}

class MockContentInteractor extends Mock implements ContentInteractor {}

class MockCreditAnalytics extends Mock implements CreditAnalytics {}

class MockStorage extends Mock implements Storage<String?> {}

class MockFeatureToggleProvider extends Mock implements FeatureToggleProvider {}

class MockResponsiveDialogProvider extends Mock
    implements ResponsiveDialogProvider {}

class MockCreditLocalizations extends Mock implements CreditLocalizations {}

class MockEmbeddedLendingThemeResolver extends Mock
    implements EmbeddedLendingThemeResolver {}

class FakeFeatureNavigationConfig extends Fake
    implements FeatureNavigationConfig {
  @override
  String toString() => 'FakeFeatureNavigationConfig';
}

class FakeWidget extends Fake implements Widget {
  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'FakeWidget';
  }
}

class FakeResponsiveModalConfig extends Fake implements ResponsiveModalConfig {}
