import 'package:bloc_test/bloc_test.dart';
import 'package:embedded_lending_core_api/embedded_lending_core_api.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:tests/tests.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_gateway_hub_api/gateway_hub_api.dart';
import 'package:wio_feature_gateway_hub_ui/l10n/gateway_hub_localizations.g.dart';
import 'package:wio_feature_gateway_hub_ui/src/screens/loan_overview/cubit/loan_overview_cubit.dart';
import 'package:wio_feature_gateway_hub_ui/src/utils/auth_proxy.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late Logger logger;
  late CreditApplicationCreator creditApplicationCreator;
  late CreditApplicationInteractor interactor;
  late ContentInteractor contentInteractor;
  late LoanConsentInteractor loanConsentInteractor;
  late NavigationProvider navigationProvider;
  late ResponsiveDialogProvider dialogProvider;
  late GatewayHubLocalizations localizations;
  late CreditAnalytics analytics;
  late String externalReferenceId;
  late LoanOverviewCubit cubit;
  late AuthProxy authProxy;

  const partner = Partner.talabat;

  setUp(() {
    logger = MockLogger();
    creditApplicationCreator = MockCreditApplicationCreator();
    interactor = MockCreditApplicationInteractor();
    contentInteractor = MockContentInteractor();
    loanConsentInteractor = MockLoanConsentInteractor();
    navigationProvider = MockNavigationProvider();
    dialogProvider = MockResponsiveDialogProvider();
    localizations = MockGatewayHubLocalizations();
    analytics = MockCreditAnalytics();
    authProxy = MockAuthProxy();
    externalReferenceId = 'externalReferenceId';

    cubit = LoanOverviewCubit(
      creditApplicationCreator: creditApplicationCreator,
      logger: logger,
      interactor: interactor,
      contentInteractor: contentInteractor,
      loanConsentInteractor: loanConsentInteractor,
      navigationProvider: navigationProvider,
      responsiveDialogProvider: dialogProvider,
      localizations: localizations,
      analytics: analytics,
      authProxy: authProxy,
      externalReferenceId: externalReferenceId,
    );

    when(() => loanConsentInteractor.getConsentScopes(partner)).justAnswerAsync(
      TestEntities.partnerLoanConsentScope,
    );
    when(
      () => loanConsentInteractor.postLoanConsent(
        partnerLoanConsentScope: TestEntities.partnerLoanConsentScope,
        externalReferenceId: externalReferenceId,
      ),
    ).justCompleteAsync();
    registerFallbackValue(ContentDocumentType.creditCardKeyFactStatement);
    registerFallbackValue(ScreenNavigationConfigFake());
    registerFallbackValue(FakeWidget());
    registerFallbackValue(FakeResponsiveModalConfig());
  });

  final loadedState = LoanOverviewState.loaded(
    vatStatementExampleUrl: TestEntities.creditProductInfo.vatTemplateUrl,
    tnc: TestEntities.creditDocument.localizedDocumentLinks!,
    kfs: TestEntities.creditDocument.localizedDocumentLinks!,
    consentScopes: TestEntities.partnerLoanConsentScope,
  );

  group('LoanOverViewCubit >', () {
    blocTest<LoanOverviewCubit, LoanOverviewState>(
      'init should be documents',
      build: () => cubit,
      setUp: () {
        when(
          () => interactor.getProductInfo(
            productType: SmeCreditProductType.businessLoan,
          ),
        ).justAnswerAsync(TestEntities.creditProductInfo);
        when(
          () => contentInteractor.getContentDocument(
            documentType: any(named: 'documentType'),
          ),
        ).justAnswerAsync(TestEntities.creditDocument);
      },
      act: (cubit) => cubit.init(partner),
      expect: () => [loadedState],
    );

    blocTest<LoanOverviewCubit, LoanOverviewState>(
      'init should go to error state incase of any error',
      build: () => cubit,
      setUp: () {
        when(
          () => interactor.getProductInfo(
            productType: SmeCreditProductType.businessLoan,
          ),
        ).justAnswerAsync(TestEntities.creditProductInfo);
        when(
          () => contentInteractor.getContentDocument(
            documentType: any(named: 'documentType'),
          ),
        ).justThrowAsync(Exception);
      },
      act: (cubit) => cubit.init(partner),
      expect: () => [const LoanOverviewState.error()],
    );

    blocTest<LoanOverviewCubit, LoanOverviewState>(
      'on continue',
      build: () => cubit,
      seed: () => loadedState,
      setUp: () {
        when(
          () => creditApplicationCreator
              .createFromReferenceId(externalReferenceId),
        ).justAnswerAsync(TestEntities.loanApplication());
      },
      act: (cubit) => cubit.onContinue(),
      verify: (_) {
        verify(
          () => creditApplicationCreator
              .createFromReferenceId(externalReferenceId),
        ).calledOnce;
      },
    );

    blocTest<LoanOverviewCubit, LoanOverviewState>(
      'on continue goes to error',
      build: () => cubit,
      seed: () => loadedState,
      setUp: () {
        when(
          () => creditApplicationCreator
              .createFromReferenceId(externalReferenceId),
        ).justThrowAsync(Exception());
      },
      act: (cubit) => cubit.onContinue(),
      verify: (_) {
        verify(
          () => creditApplicationCreator
              .createFromReferenceId(externalReferenceId),
        ).calledOnce;
      },
      expect: () => [
        const LoanOverviewState.processing(),
        const LoanOverviewState.error(),
      ],
    );

    blocTest<LoanOverviewCubit, LoanOverviewState>(
      'open vat statement example',
      build: () => cubit,
      seed: () => loadedState,
      setUp: () {
        when(
          () => navigationProvider.push(any()),
        ).justCompleteAsync();
        when(
          () => localizations.vatStatementSampleTitle,
        ).thenReturn('');
      },
      act: (cubit) => cubit.openVatExample(),
      verify: (_) {
        verify(
          () => navigationProvider.push(any()),
        ).calledOnce;
      },
    );

    blocTest<LoanOverviewCubit, LoanOverviewState>(
      'open kfs',
      build: () => cubit,
      seed: () => loadedState,
      setUp: () {
        when(
          () => navigationProvider.push(any()),
        ).justCompleteAsync();
      },
      act: (cubit) => cubit.openDocument(LoanDocumentType.keyFactsStatement),
      verify: (_) {
        verify(
          () => navigationProvider.push(any()),
        ).calledOnce;
      },
    );

    blocTest<LoanOverviewCubit, LoanOverviewState>(
      'open tnc',
      build: () => cubit,
      seed: () => loadedState,
      setUp: () {
        when(
          () => navigationProvider.push(any()),
        ).justCompleteAsync();
      },
      act: (cubit) => cubit.openDocument(LoanDocumentType.termsAndConditions),
      verify: (_) {
        verify(
          () => navigationProvider.push(any()),
        ).calledOnce;
      },
    );

    blocTest<LoanOverviewCubit, LoanOverviewState>(
      'open consent dialog',
      build: () => cubit,
      seed: () => loadedState,
      setUp: () {
        when(
          () => dialogProvider.showBottomSheetOrDialog<void>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).justCompleteAsync();
      },
      act: (cubit) => cubit.openConsentShareLoanInfoScope(),
      verify: (_) {
        verify(
          () => dialogProvider.showBottomSheetOrDialog<void>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).calledOnce;
      },
    );

    blocTest<LoanOverviewCubit, LoanOverviewState>(
      'logout',
      build: () => cubit,
      setUp: () {
        when(
          () => authProxy.logout(),
        ).justCompleteAsync();
      },
      act: (cubit) => cubit.logout(),
      verify: (_) {
        verify(
          () => authProxy.logout(),
        ).calledOnce;
      },
    );
  });
}
