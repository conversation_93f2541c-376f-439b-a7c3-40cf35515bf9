import 'package:bloc_test/bloc_test.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:embedded_lending_core_api/embedded_lending_core_api.dart';
import 'package:embedded_lending_core_ui/index.dart';
import 'package:feature_login_api/feature_login_api.dart';
import 'package:flutter/widgets.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_gateway_hub_api/gateway_hub_api.dart';
import 'package:wio_feature_gateway_hub_ui/l10n/gateway_hub_localizations.g.dart';
import 'package:wio_feature_gateway_hub_ui/src/screens/post_login_redirection/cubit/post_login_redirection_cubit.dart';
import 'package:wio_feature_gateway_hub_ui/src/screens/post_login_redirection/helpers/loan_dashboard_builder.dart';
import 'package:wio_feature_gateway_hub_ui/src/utils/auth_proxy.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/feature_loan_ui.dart';

import '../mocks.dart';
import '../test_entities.dart';

typedef _Cubit = PostLoginRedirectionCubit;

void main() {
  late CreditApplicationInteractor creditApplicationInteractor;
  late CreditAccountInteractor creditAccountInteractor;
  late NavigationProvider navigationProvider;
  late GatewayHubLocalizations localizations;
  late PostLoginRedirectionCubit cubit;
  late Logger logger;
  late Storage<String?> storage;
  late ResponsiveDialogProvider responsiveDialogProvider;
  late AuthProxy authProxy;
  late BorrowingPowerFlowHandler borrowingPowerFlowHandler;
  late LoginInteractor loginInteractor;
  late FeatureToggleProvider featureToggleProvider;

  const externalReferenceId = 'externalReferenceId';
  const partner = Partner.wio;

  setUp(() {
    creditAccountInteractor = MockCreditAccountInteractor();
    creditApplicationInteractor = MockCreditApplicationInteractor();
    navigationProvider = MockNavigationProvider();
    localizations = MockGatewayHubLocalizations();
    authProxy = MockAuthProxy();
    logger = MockLogger();
    storage = MockStorage();
    responsiveDialogProvider = MockResponsiveDialogProvider();
    borrowingPowerFlowHandler = MockBorrowingPowerFlowHandler();
    loginInteractor = MockLoginInteractor();
    featureToggleProvider = MockFeatureToggleProvider();

    cubit = PostLoginRedirectionCubit(
      creditApplicationInteractor: creditApplicationInteractor,
      creditAccountInteractor: creditAccountInteractor,
      navigationProvider: navigationProvider,
      localizations: localizations,
      logger: logger,
      storage: storage,
      authProxy: authProxy,
      borrowingPowerFlowHandler: borrowingPowerFlowHandler,
      loginInteractor: loginInteractor,
      featureToggleProvider: featureToggleProvider,
    );

    when(() => loginInteractor.getUserDetails()).justAnswerAsync(
      const UserDetails(
        status: UserStatus.active,
        name: 'User Name',
        businessStatus: UserStatus.active,
        isFATCACompleted: false,
        isCRSCompleted: false,
        businessName: 'Srup Pt Lt',
        deviceBoundStatus: DeviceBoundStatus.match,
        role: 'Shareholder',
      ),
    );

    registerFallbackValue(FakeResponsiveModalConfig());
  });

  group('PostLoginRedirectionCubit >', () {
    blocTest<_Cubit, void>(
      'with BL account',
      build: () => cubit,
      setUp: () {
        when(
          () => creditAccountInteractor.getAccounts(),
        ).justAnswerAsync([TestEntities.loanAccount]);
      },
      act: (cubit) => cubit.initialize(
        partner: partner,
        externalReferenceId: externalReferenceId,
      ),
      verify: (_) {
        verify(
          () => navigationProvider.clearStackAndReplace(
            LoanFeatureNavigationConfig(
              destination: LoanDashboardNavigationConfig(
                loanDashboardConfig: const LoanDashboardConfig(
                  loanAccountId: 'id',
                  builder: LoanTransactionsWrapper.builder,
                  productIdentifier: LoanProductIdentifier.businessLoan,
                ),
              ),
            ),
          ),
        ).calledOnce;
      },
    );

    blocTest<_Cubit, void>(
      'with BL application in progress',
      build: () => cubit,
      setUp: () {
        registerFallbackValue(const Placeholder());
        registerFallbackValue(FakeCreditApplicationNavigationConfig());
        when(
          () => creditAccountInteractor.getAccounts(),
        ).justAnswerAsync([]);
        when(
          () => creditApplicationInteractor.getCreditApplications(
            productType: SmeCreditProductType.credit,
          ),
        ).justAnswerAsync(
          CreditApplications(
            applications: [
              TestEntities.loanApplication(),
            ],
          ),
        );
        when(
          () => responsiveDialogProvider.showBottomSheetOrDialog<bool>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).justAnswerAsync(true);
        when(
          () => creditApplicationInteractor.getApplication(any()),
        ).justAnswerAsync(TestEntities.loanApplication());
        when(
          () => navigationProvider.push(any()),
        ).justCompleteAsync();
        when(() => navigationProvider.clearStackAndReplace(any()))
            .justComplete();
        when(
          () => borrowingPowerFlowHandler.run(
            application: TestEntities.loanApplication(),
            onBorrowFlowNotRequired: any(named: 'onBorrowFlowNotRequired'),
          ),
        ).justCompleteAsync();
        when(() => localizations.genericErrorScreenTitle).thenReturn('');
        when(() => localizations.genericErrorScreenDescription).thenReturn('');
      },
      act: (cubit) => cubit.initialize(
        partner: partner,
        externalReferenceId: externalReferenceId,
      ),
      verify: (_) {
        verify(
          () => borrowingPowerFlowHandler.run(
            application: TestEntities.loanApplication(),
            onBorrowFlowNotRequired: any(named: 'onBorrowFlowNotRequired'),
          ),
        ).calledOnce;
      },
    );

    blocTest<_Cubit, void>(
      'with BL application rejected',
      build: () => cubit,
      setUp: () {
        when(
          () => creditAccountInteractor.getAccounts(),
        ).justAnswerAsync([]);
        when(
          () => creditApplicationInteractor.getCreditApplications(
            productType: SmeCreditProductType.credit,
          ),
        ).justAnswerAsync(
          CreditApplications(
            applications: [
              TestEntities.loanApplication(
                creditDecisionResult: const CreditDecisionResult.rejected(),
              ),
            ],
          ),
        );
        when(
          () => localizations.loanApplicationRejectedTitle,
        ).thenReturn('');
        when(
          () => localizations.loanApplicationRejectedDescription,
        ).thenReturn('');
        registerFallbackValue(FakeStatusViewConfig());
      },
      act: (cubit) => cubit.initialize(
        partner: partner,
        externalReferenceId: externalReferenceId,
      ),
      verify: (_) {
        verify(
          () => navigationProvider.clearStackAndReplace(any()),
        ).calledOnce;
      },
    );

    blocTest<_Cubit, void>(
      'with BL application in review',
      build: () => cubit,
      setUp: () {
        when(
          () => creditAccountInteractor.getAccounts(),
        ).justAnswerAsync([]);
        when(
          () => creditApplicationInteractor.getCreditApplications(
            productType: SmeCreditProductType.credit,
          ),
        ).justAnswerAsync(
          CreditApplications(
            applications: [
              TestEntities.loanApplication(
                creditDecisionResult: const CreditDecisionResult.inProgress(),
              ),
            ],
          ),
        );
        when(
          () => localizations.applicationSubmittedTitle,
        ).thenReturn('');
        when(
          () => localizations.applicationSubmittedDescription,
        ).thenReturn('');
        registerFallbackValue(FakeStatusViewConfig());
      },
      act: (cubit) => cubit.initialize(
        partner: partner,
        externalReferenceId: externalReferenceId,
      ),
      verify: (_) {
        verify(
          () => navigationProvider.clearStackAndReplace(any()),
        ).calledOnce;
      },
    );

    blocTest<_Cubit, void>(
      'ineligible for BL ',
      build: () => cubit,
      setUp: () {
        when(
          () => creditAccountInteractor.getAccounts(),
        ).justAnswerAsync([]);
        when(
          () => creditApplicationInteractor.getCreditApplications(
            productType: SmeCreditProductType.credit,
          ),
        ).justAnswerAsync(
          const CreditApplications(applications: []),
        );
        when(
          () => creditApplicationInteractor.getOffers(),
        ).justAnswerAsync(
          const CreditOffers(
            offers: [],
            totalOffers: 0,
            creditIneligibiltyDetails: CreditIneligibiltyDetails(
              title: 'title',
              description: 'description',
              isUpdateDocument: false,
              reason: CreditIneligibiltyReason.documentExpired,
            ),
          ),
        );
        registerFallbackValue(ScreenNavigationConfigFake());
      },
      act: (cubit) => cubit.initialize(partner: partner),
      verify: (_) {
        verify(
          () => navigationProvider.clearStackAndReplace(any()),
        ).calledOnce;
      },
    );

    blocTest<_Cubit, void>(
      'with externalReferenceId to start application',
      build: () => cubit,
      setUp: () {
        when(
          () => creditAccountInteractor.getAccounts(),
        ).justAnswerAsync([]);
        when(
          () => creditApplicationInteractor.getOffers(),
        ).justAnswerAsync(TestEntities.offers);
        when(
          () => creditApplicationInteractor.getCreditApplications(
            productType: SmeCreditProductType.credit,
          ),
        ).justAnswerAsync(
          const CreditApplications(
            applications: [],
          ),
        );
        when(
          () => navigationProvider.replace(any()),
        ).justCompleteAsync();
        registerFallbackValue(FakeStatusViewConfig());
      },
      act: (cubit) => cubit.initialize(
        externalReferenceId: externalReferenceId,
        partner: partner,
      ),
      verify: (_) {
        verify(
          () => navigationProvider.replace(any()),
        ).calledOnce;
      },
    );

    blocTest<_Cubit, void>(
      'without externalReferenceId - should check storage',
      build: () => cubit,
      setUp: () {
        when(
          () => creditAccountInteractor.getAccounts(),
        ).justAnswerAsync([]);
        when(
          () => creditApplicationInteractor.getOffers(),
        ).justAnswerAsync(TestEntities.offers);
        when(
          () => creditApplicationInteractor.getCreditApplications(
            productType: SmeCreditProductType.credit,
          ),
        ).justAnswerAsync(
          const CreditApplications(
            applications: [],
          ),
        );
        when(
          () => storage.getByKey(any()),
        ).justAnswerAsync('value');
        when(
          () => navigationProvider.replace(any()),
        ).justCompleteAsync();

        registerFallbackValue(FakeStatusViewConfig());
      },
      act: (cubit) => cubit.initialize(partner: partner),
      verify: (_) {
        verify(
          () => storage
              .getByKey(EmbeddedLendingAppConstants.referenceIdStorageKey),
        ).calledOnce;
        verify(
          () => navigationProvider.replace(any()),
        ).calledOnce;
      },
    );

    blocTest<_Cubit, void>(
      'without externalReferenceId in the storage',
      build: () => cubit,
      setUp: () {
        when(
          () => creditAccountInteractor.getAccounts(),
        ).justAnswerAsync([]);
        when(
          () => creditApplicationInteractor.getOffers(),
        ).justAnswerAsync(TestEntities.offers);
        when(
          () => creditApplicationInteractor.getCreditApplications(
            productType: SmeCreditProductType.credit,
          ),
        ).justAnswerAsync(
          const CreditApplications(
            applications: [],
          ),
        );
        when(
          () => localizations.postLoginErrorCreditNotAvailableTitle,
        ).thenReturn('');
        when(
          () => localizations.postLoginErrorCreditNotAvailableDescription,
        ).thenReturn('');
        when(
          () => storage.getByKey(any()),
        ).justAnswerEmptyAsync();

        registerFallbackValue(FakeStatusViewConfig());
      },
      act: (cubit) => cubit.initialize(partner: partner),
      verify: (_) {
        verify(
          () => storage
              .getByKey(EmbeddedLendingAppConstants.referenceIdStorageKey),
        ).calledOnce;
        verify(
          () => navigationProvider.clearStackAndReplace(any()),
        ).calledOnce;
      },
    );

    blocTest<_Cubit, void>(
      'on error',
      build: () => cubit,
      setUp: () {
        when(
          () => creditAccountInteractor.getAccounts(),
        ).justAnswerAsync([]);
        when(
          () => creditApplicationInteractor.getCreditApplications(
            productType: SmeCreditProductType.credit,
          ),
        ).justThrowAsync(Exception());
        when(
          () => localizations.genericErrorScreenTitle,
        ).thenReturn('');
        when(
          () => localizations.genericErrorScreenDescription,
        ).thenReturn('');

        registerFallbackValue(FakeStatusViewConfig());
      },
      act: (cubit) => cubit.initialize(partner: partner),
      verify: (_) {
        verify(
          () => navigationProvider.clearStackAndReplace(any()),
        ).calledOnce;
        verify(
          () => logger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).calledOnce;
      },
    );

    group('User Status Processing >', () {
      setUp(() {
        registerFallbackValue(FakeStatusViewConfig());
        registerFallbackValue(FakeFeatureNavigationConfig());

        when(() => logger.debug(any())).justComplete();
        when(() => localizations.postLoginRedirectionInReviewTitle)
            .thenReturn('In Review Title');
        when(() => localizations.postLoginRedirectionInReviewDescription)
            .thenReturn('In Review Description');
        when(() => localizations.postLoginRedirectionPrespectiveTitle)
            .thenReturn('Prespective Title');
        when(() => localizations.postLoginRedirectionPrespectiveDescription)
            .thenReturn('Prespective Description');
        when(() => localizations.genericErrorScreenTitle)
            .thenReturn('Error Title');
        when(() => localizations.genericErrorScreenDescription)
            .thenReturn('Error Description');
        when(() => navigationProvider.clearStackAndReplace(any()))
            .justComplete();
        when(
          () => featureToggleProvider
              .get(GatewayHubFeatureToggles.isOnboardingEnabled),
        ).thenReturn(false);
      });

      blocTest<_Cubit, void>(
        'active user - should continue flow',
        build: () => cubit,
        setUp: () {
          when(() => loginInteractor.getUserDetails()).justAnswerAsync(
            const UserDetails(
              status: UserStatus.active,
              name: 'User Name',
              businessStatus: UserStatus.active,
              isFATCACompleted: false,
              isCRSCompleted: false,
              businessName: 'Srup Pt Lt',
              deviceBoundStatus: DeviceBoundStatus.match,
              role: 'Shareholder',
            ),
          );

          // Need to mock further calls since flow will continue
          when(() => creditAccountInteractor.getAccounts()).justAnswerAsync([]);
          when(
            () => creditApplicationInteractor.getCreditApplications(
              productType: SmeCreditProductType.credit,
            ),
          ).justAnswerAsync(const CreditApplications(applications: []));
          when(
            () => creditApplicationInteractor.getOffers(),
          ).justThrowAsync(Exception()); // Will cause early return with error
        },
        act: (cubit) => cubit.initialize(partner: partner),
        verify: (_) {
          verify(() => loginInteractor.getUserDetails()).calledOnce;
          verify(() => logger.debug(any())).called(greaterThanOrEqualTo(1));
          verify(() => creditAccountInteractor.getAccounts()).calledOnce;
        },
      );

      blocTest<_Cubit, void>(
        'inReview user - should show inReview screen',
        build: () => cubit,
        setUp: () {
          when(() => loginInteractor.getUserDetails()).justAnswerAsync(
            const UserDetails(
              status: UserStatus.inReview,
              name: 'User Name',
              businessStatus: UserStatus.active,
              isFATCACompleted: false,
              isCRSCompleted: false,
              businessName: 'Srup Pt Lt',
              deviceBoundStatus: DeviceBoundStatus.match,
              role: 'Shareholder',
            ),
          );
        },
        act: (cubit) => cubit.initialize(partner: partner),
        verify: (_) {
          verify(() => loginInteractor.getUserDetails()).calledOnce;
          verify(() => navigationProvider.clearStackAndReplace(any()))
              .calledOnce;
          verify(() => localizations.postLoginRedirectionInReviewTitle)
              .calledOnce;
          verify(() => localizations.postLoginRedirectionInReviewDescription)
              .calledOnce;

          // Verify flow stopped - no account retrieval
          verifyNever(() => creditAccountInteractor.getAccounts());
        },
      );

      blocTest<_Cubit, void>(
        'prespective user - should show prespective screen',
        build: () => cubit,
        setUp: () {
          when(() => loginInteractor.getUserDetails()).justAnswerAsync(
            const UserDetails(
              status: UserStatus.prespective,
              name: 'User Name',
              businessStatus: UserStatus.active,
              isFATCACompleted: false,
              isCRSCompleted: false,
              businessName: 'Srup Pt Lt',
              deviceBoundStatus: DeviceBoundStatus.match,
              role: 'Shareholder',
            ),
          );
        },
        act: (cubit) => cubit.initialize(partner: partner),
        verify: (_) {
          verify(() => loginInteractor.getUserDetails()).calledOnce;
          verify(() => navigationProvider.clearStackAndReplace(any()))
              .calledOnce;
          verify(() => localizations.postLoginRedirectionPrespectiveTitle)
              .calledOnce;
          verify(() => localizations.postLoginRedirectionPrespectiveDescription)
              .calledOnce;

          // Verify flow stopped - no account retrieval
          verifyNever(() => creditAccountInteractor.getAccounts());
        },
      );

      blocTest<_Cubit, void>(
        'unknown user status - should show generic error',
        build: () => cubit,
        setUp: () {
          when(() => loginInteractor.getUserDetails()).justAnswerAsync(
            const UserDetails(
              status: UserStatus.unknown,
              name: 'User Name',
              businessStatus: UserStatus.active,
              isFATCACompleted: false,
              isCRSCompleted: false,
              businessName: 'Srup Pt Lt',
              deviceBoundStatus: DeviceBoundStatus.match,
              role: 'Shareholder',
            ),
          );
        },
        act: (cubit) => cubit.initialize(partner: partner),
        verify: (_) {
          verify(() => loginInteractor.getUserDetails()).calledOnce;
          verify(() => navigationProvider.clearStackAndReplace(any()))
              .calledOnce;
          verify(() => localizations.genericErrorScreenTitle).calledOnce;
          verify(() => localizations.genericErrorScreenDescription).calledOnce;

          // Verify flow stopped - no account retrieval
          verifyNever(() => creditAccountInteractor.getAccounts());
        },
      );
    });
  });
}
