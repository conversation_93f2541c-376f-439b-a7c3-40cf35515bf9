import 'dart:async';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:embedded_lending_core_api/embedded_lending_core_api.dart';
import 'package:embedded_lending_core_ui/index.dart';
import 'package:feature_login_api/feature_login_api.dart';
import 'package:logging_api/logging.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_adaptive_status_view_api/feature_status_view_adaptive_api.dart';
import 'package:wio_feature_credit_api/credit_api.dart'
    hide LearnMorePageNavigationConfig;
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_gateway_hub_api/gateway_hub_api.dart';
import 'package:wio_feature_gateway_hub_ui/l10n/gateway_hub_localizations.g.dart';
import 'package:wio_feature_gateway_hub_ui/src/screens/post_login_redirection/helpers/loan_dashboard_builder.dart';
import 'package:wio_feature_gateway_hub_ui/src/utils/auth_proxy.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/feature_loan_ui.dart';
import 'package:wio_feature_onboarding_api/onboarding_api.dart';

class PostLoginRedirectionCubit extends BaseCubit<void> {
  final CreditApplicationInteractor _creditApplicationInteractor;
  final CreditAccountInteractor _creditAccountInteractor;
  final NavigationProvider _navigationProvider;
  final GatewayHubLocalizations _localizations;
  final Logger _logger;
  final Storage<String?> _storage;
  final AuthProxy _authProxy;
  final BorrowingPowerFlowHandler _borrowingPowerFlowHandler;
  final LoginInteractor _loginInteractor;
  final FeatureToggleProvider _featureToggleProvider;

  String? _externalReferenceId;

  PostLoginRedirectionCubit({
    required CreditApplicationInteractor creditApplicationInteractor,
    required CreditAccountInteractor creditAccountInteractor,
    required NavigationProvider navigationProvider,
    required GatewayHubLocalizations localizations,
    required Logger logger,
    required Storage<String?> storage,
    required AuthProxy authProxy,
    required BorrowingPowerFlowHandler borrowingPowerFlowHandler,
    required LoginInteractor loginInteractor,
    required FeatureToggleProvider featureToggleProvider,
  })  : _creditApplicationInteractor = creditApplicationInteractor,
        _creditAccountInteractor = creditAccountInteractor,
        _navigationProvider = navigationProvider,
        _localizations = localizations,
        _logger = logger,
        _storage = storage,
        _authProxy = authProxy,
        _borrowingPowerFlowHandler = borrowingPowerFlowHandler,
        _loginInteractor = loginInteractor,
        _featureToggleProvider = featureToggleProvider,
        super(null);

  bool get isOnboardingEnabled =>
      _featureToggleProvider.get(GatewayHubFeatureToggles.isOnboardingEnabled);

  Future<void> initialize({
    required Partner partner,
    String? externalReferenceId,
  }) async {
    try {
      // Has the externalReferenceId to start loan application
      _externalReferenceId = externalReferenceId;

      // Process user details and check status
      final shouldContinue = await _processUserDetails();
      _logger.debug('Should continue: $shouldContinue');
      if (!shouldContinue) return;

      final businessLoanAccount = await _getBusinessLoanAccount();

      // Has a business loan account
      if (businessLoanAccount != null) {
        return _handleBusinessLoanAccount(businessLoanAccount);
      }

      final businessLoanApplication = await _getBusinessLoanApplication();

      // Has a loan application
      if (businessLoanApplication != null) {
        final isApplicationHandled =
            _handleBusinessLoanApplication(businessLoanApplication);

        if (isApplicationHandled) return;
      }

      /// Check for loan eligibility
      final creditOffers = await _getCreditOffers();
      final businessLoanOffer =
          creditOffers.getOffer(SmeCreditProductType.businessLoan);

      /// NO business loan offer
      if (businessLoanOffer == null) {
        return _handleLoanIneligibility(creditOffers.creditIneligibiltyDetails);
      }

      // checking storage if the value exists
      _externalReferenceId ??= await _storage
          .getByKey(EmbeddedLendingAppConstants.referenceIdStorageKey);

      if (_externalReferenceId != null) {
        return unawaited(
          _navigationProvider.replace(
            LearnMorePageNavigationConfig(
              externalReferenceId: _externalReferenceId!,
              partner: partner,
              isOnboardingEnabled: false,
            ),
          ),
        );
      }

      // Incase all the above conditions are not satisfied
      return _navigationProvider.clearStackAndReplace(
        _getStatusScreenNavigationConfig(
          title: _localizations.postLoginErrorCreditNotAvailableTitle,
          subtitle: _localizations.postLoginErrorCreditNotAvailableDescription,
          icon: CompanyPictogramPointer.metaphors_dawn.toGraphicAsset(),
        ),
      );
    } on Exception catch (error, stackTrace) {
      _logger.error(
        'Something went wrong',
        error: error,
        stackTrace: stackTrace,
      );

      _showGenericErrorScreen();
    }
  }

  /// Process user details and handle specific user statuses
  /// Returns true if initialization flow should continue, false otherwise
  Future<bool> _processUserDetails() async {
    final userDetails = await _loginInteractor.getUserDetails();
    _logger.debug('User details: $userDetails');

    return switch (userDetails.status) {
      UserStatus.active => true,
      UserStatus.inReview => () {
          if (isOnboardingEnabled) {
            _showOnboardingScreen();
            return false;
          }
          _navigationProvider.clearStackAndReplace(
            _getStatusScreenNavigationConfig(
              title: _localizations.postLoginRedirectionInReviewTitle,
              subtitle: _localizations.postLoginRedirectionInReviewDescription,
              icon: CompanyPictogramPointer.symbols_time.toGraphicAsset(),
            ),
          );
          return false;
        }(),
      UserStatus.prespective => () {
          if (isOnboardingEnabled) {
            _showOnboardingScreen();
            return false;
          }
          _navigationProvider.clearStackAndReplace(
            _getStatusScreenNavigationConfig(
              title: _localizations.postLoginRedirectionPrespectiveTitle,
              subtitle:
                  _localizations.postLoginRedirectionPrespectiveDescription,
              icon: CompanyPictogramPointer.symbols_time.toGraphicAsset(),
            ),
          );
          return false;
        }(),
      _ => () {
          _showGenericErrorScreen();
          return false;
        }(),
    };
  }

  bool _handleBusinessLoanApplication(
    CreditApplication businessLoanApplication,
  ) {
    // If credit decision is rejected (application declined)
    if (businessLoanApplication.creditDecisionResult.status ==
        CreditDecisionStatus.rejected) {
      _navigationProvider.clearStackAndReplace(
        _getStatusScreenNavigationConfig(
          title: _localizations.loanApplicationRejectedTitle,
          subtitle: _localizations.loanApplicationRejectedDescription,
          icon: CompanyPictogramPointer.metaphors_dawn.toGraphicAsset(),
        ),
      );

      return true;
    }

    // If credit decision is in progress (application is in review)
    if (businessLoanApplication.creditDecisionResult.status ==
        CreditDecisionStatus.inProgress) {
      _navigationProvider.clearStackAndReplace(
        _getStatusScreenNavigationConfig(
          title: _localizations.applicationSubmittedTitle,
          subtitle: _localizations.applicationSubmittedDescription,
          icon: CompanyPictogramPointer.validation_success.toGraphicAsset(),
        ),
      );

      return true;
    }

    // Ongoing application
    if (businessLoanApplication.status == CreditApplicationStatus.inProgress) {
      unawaited(
        _borrowingPowerFlowHandler.run(
          application: businessLoanApplication,
          onBorrowFlowNotRequired: () {
            _navigationProvider.replace(
              CreditApplicationNavigationConfig(
                application: businessLoanApplication,
                productType: SmeCreditProductType.businessLoan,
                sourceApp: CreditApplicationFlowSourceApp.embeddedLending(
                  logout: _logout,
                ),
              ),
            );
          },
        ),
      );

      return true;
    }

    return false;
  }

  void _handleBusinessLoanAccount(LoanAccount account) {
    return _navigationProvider.clearStackAndReplace(
      LoanFeatureNavigationConfig(
        destination: LoanDashboardNavigationConfig(
          loanDashboardConfig: LoanDashboardConfig(
            loanAccountId: account.id,
            builder: LoanTransactionsWrapper.builder,
            onLogout: _logout,
            productIdentifier: account.productIdentifier,
          ),
        ),
      ),
    );
  }

  void _handleLoanIneligibility(
    CreditIneligibiltyDetails? ineligibiltyDetails,
  ) {
    if (ineligibiltyDetails == null) {
      return _navigationProvider.clearStackAndReplace(
        _getStatusScreenNavigationConfig(
          title: _localizations.postLoginErrorCreditNotAvailableTitle,
          subtitle: _localizations.postLoginErrorCreditNotAvailableDescription,
          icon: CompanyPictogramPointer.metaphors_dawn.toGraphicAsset(),
        ),
      );
    }

    return _navigationProvider.clearStackAndReplace(
      _getStatusScreenNavigationConfig(
        title: ineligibiltyDetails.title,
        subtitle: ineligibiltyDetails.description,
        icon: CompanyIconPointer.security.toGraphicAsset(),
      ),
    );
  }

  Future<CreditApplication?> _getBusinessLoanApplication() async {
    final applications = await _creditApplicationInteractor
        .getCreditApplications(productType: SmeCreditProductType.credit);
    final activeApplication = applications.activeApplication;

    return activeApplication?.entryProductType ==
            SmeCreditProductType.businessLoan
        ? activeApplication
        : null;
  }

  Future<LoanAccount?> _getBusinessLoanAccount() async {
    final accounts = await _creditAccountInteractor.getAccounts();

    return accounts
        .where(
          (account) => account.productType == SmeCreditProductType.businessLoan,
        )
        .firstOrNull;
  }

  Future<CreditOffers> _getCreditOffers() =>
      _creditApplicationInteractor.getOffers();

  Future<void> _logout() async {
    await _authProxy.logout();
  }

  FeatureNavigationConfig _getStatusScreenNavigationConfig({
    required String title,
    required String subtitle,
    required GraphicAssetPointer icon,
  }) {
    return AdaptiveStatusViewFeatureNavigationConfig(
      config: StatusViewScreenConfiguration.embeddedLending(
        title: title,
        subtitle: subtitle,
        icon: icon,
        topRightIcon: TopRightIconModel(
          icon: CompanyIconPointer.pin_screen_exit.toGraphicAsset(),
          onPressed: _logout,
        ),
      ),
    );
  }

  void _showGenericErrorScreen() => _navigationProvider.clearStackAndReplace(
        _getStatusScreenNavigationConfig(
          title: _localizations.genericErrorScreenTitle,
          subtitle: _localizations.genericErrorScreenDescription,
          icon: CompanyPictogramPointer.validation_failure.toGraphicAsset(),
        ),
      );

  void _showOnboardingScreen() => _navigationProvider.clearStackAndReplace(
        const OnboardingFeatureNavigationConfig(
          destination: OnboardingScreenNavigationConfig(),
        ),
      );

  @override
  String toString() {
    return 'PostLoginRedirectionCubit';
  }
}
