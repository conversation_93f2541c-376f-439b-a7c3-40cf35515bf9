part of '../pre_login_page.dart';

class _Header extends StatelessWidget {
  final bool isDesktop;

  const _Header({this.isDesktop = false});

  @override
  Widget build(BuildContext context) {
    final localizations = GatewayHubLocalizations.of(context);

    return Center(
      child: Padding(
        padding: EdgeInsets.only(top: isDesktop ? 0 : 24, bottom: 36),
        child: Label(
          model: LabelModel(
            text: localizations.preLoginChooseAnOption,
            color: CompanyColorPointer.secondary4,
          ),
        ),
      ),
    );
  }
}

class _LeftCard extends StatelessWidget {
  final Partner partner;
  final String? externalReferenceId;
  final bool isDesktop;

  const _LeftCard({
    required this.partner,
    this.externalReferenceId,
    this.isDesktop = false,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = GatewayHubLocalizations.of(context);
    final cubit = context.read<PreLoginCubit>();
    final isOnboardingEnabled = cubit.isOnboardingEnabled;

    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: ColoredBox(
        color: context.colorStyling.surface2,
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (isOnboardingEnabled) ...[
                    Tile(
                      model: TileModel.icon(
                        icon: CompanyIconPointer.wio.toGraphicAsset(),
                        backgroundColor: CompanyColorPointer.surface1,
                        borderRadius: 8,
                        iconSize: CompanyIconSize.xLarge,
                        backgroundOpacity: 0.2,
                      ),
                      size: 64,
                    ),
                    const SizedBox(height: 12),
                  ],
                  Label(
                    model: LabelModel(
                      text: localizations.preLoginExistingCustomerTitle,
                      color: CompanyColorPointer.primary3,
                      textStyle: CompanyTextStylePointer.h2medium,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Label(
                    model: LabelModel(
                      text: localizations.preLoginExistingCustomerDescription,
                      color: CompanyColorPointer.secondary4,
                      textStyle: CompanyTextStylePointer.b2,
                    ),
                  ),
                ],
              ),
              LayoutBuilder(
                builder: (context, constraints) {
                  final widthFactor = isDesktop ? 0.57 : 1.0;
                  return SizedBox(
                    width: constraints.maxWidth * widthFactor,
                    child: Button(
                      onPressed: () => cubit.login(
                        partner: partner,
                        referenceId: externalReferenceId,
                      ),
                      model: ButtonModel(
                        title: localizations.preLoginSignInLabel,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _RightCard extends StatelessWidget {
  final Partner partner;
  final String? externalReferenceId;
  final bool isDesktop;

  const _RightCard({
    required this.partner,
    required this.externalReferenceId,
    this.isDesktop = false,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<PreLoginCubit>();
    final isOnboardingEnabled = cubit.isOnboardingEnabled;
    if (isOnboardingEnabled) {
      return _OnboardingCard(
        partner: partner,
        externalReferenceId: externalReferenceId,
        isDesktop: isDesktop,
      );
    }

    return _DownloadAppCard(
      partner: partner,
      isDesktop: isDesktop,
    );
  }
}

class _OnboardingCard extends StatelessWidget {
  final Partner partner;
  final String? externalReferenceId;
  final bool isDesktop;

  const _OnboardingCard({
    required this.partner,
    required this.externalReferenceId,
    required this.isDesktop,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = GatewayHubLocalizations.of(context);
    final cubit = context.read<PreLoginCubit>();

    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: ColoredBox(
        color: context.colorStyling.surface2,
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Tile(
                    model: TileModel.icon(
                      icon: GraphicAssetPointer.pictogram(
                        CompanyPictogramPointer.metaphors_light,
                      ),
                      backgroundColor: CompanyColorPointer.surface1,
                      borderRadius: 8,
                      iconSize: CompanyIconSize.xLarge,
                      backgroundOpacity: 0.2,
                    ),
                    size: 64,
                  ),
                  const SizedBox(height: 12),
                  Label(
                    model: LabelModel(
                      text: localizations.preLoginNonExistingCustomerTitle,
                      color: CompanyColorPointer.primary3,
                      textStyle: CompanyTextStylePointer.h2medium,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Label(
                    model: LabelModel(
                      text:
                          localizations.preLoginNonExistingCustomerDescription,
                      color: CompanyColorPointer.secondary4,
                      textStyle: CompanyTextStylePointer.b2,
                    ),
                  ),
                ],
              ),
              LayoutBuilder(
                builder: (context, constraints) {
                  final widthFactor = isDesktop ? 0.57 : 1.0;
                  return SizedBox(
                    width: constraints.maxWidth * widthFactor,
                    child: Button(
                      onPressed: () => cubit.startOnboarding(
                        partner: partner,
                        referenceId: externalReferenceId,
                      ),
                      model: ButtonModel(
                        title: localizations.continueLabel,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _DownloadAppCard extends StatelessWidget {
  final Partner partner;
  final bool isDesktop;

  const _DownloadAppCard({
    required this.partner,
    required this.isDesktop,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = GatewayHubLocalizations.of(context);
    final cubit = context.read<PreLoginCubit>();

    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: ColoredBox(
        color: context.colorStyling.surface2,
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Label(
                    model: LabelModel(
                      text: localizations.preLoginNewCustomerTitle,
                      color: CompanyColorPointer.primary3,
                      textStyle: CompanyTextStylePointer.h2medium,
                    ),
                  ),
                  const SizedBox(height: 12),
                  BulletList(
                    model: BulletListModel(
                      items: [
                        localizations.preLoginNewCustomerStep1,
                        localizations.preLoginNewCustomerStep2,
                        localizations
                            .preLoginNewCustomerStep3(partner.formattedName),
                      ],
                      bulletStyle: BulletStyle.number,
                      textStyle: CompanyTextStylePointer.b2,
                      bulletTextStyle: CompanyTextStylePointer.b2,
                      textColor: CompanyColorPointer.secondary4,
                      bulletColor: CompanyColorPointer.secondary4,
                    ),
                  ),
                  Button(
                    onPressed: cubit.openWioWebsite,
                    model: ButtonModel(
                      title: localizations.preLoginNewCustomerLearnMore,
                      type: ButtonType.tertiary,
                      styleOverride: const ButtonStyleOverride(
                        active: ButtonStateColorScheme(
                          foreground: CompanyColorPointer.primary1,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              if (isDesktop) ...[
                const _WioBusinessQRCode(),
              ] else ...[
                Button(
                  onPressed: cubit.onDownloadApp,
                  model: ButtonModel(
                    title: localizations.preLoginDownloadApp,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class _WioBusinessQRCode extends StatelessWidget {
  const _WioBusinessQRCode();

  @override
  Widget build(BuildContext context) {
    final localizations = GatewayHubLocalizations.of(context);

    return Wrap(
      runSpacing: 12,
      crossAxisAlignment: WrapCrossAlignment.end,
      children: [
        FittedBox(
          child: Container(
            height: 160,
            width: 160,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: context.colorStyling.secondary5,
              ),
            ),
            child: const Padding(
              padding: EdgeInsets.all(11.0),
              child: CompanyImage(
                CompanyImageModel(
                  image: CompanyImageProvider.svg(
                    name: GatewayHubConstants.wioBusinessAppQrPath,
                    package: GatewayHubConstants.packageName,
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: Wrap(
            children: [
              Label(
                model: LabelModel(
                  text: localizations.preLoginAvailableOnLabel,
                  color: CompanyColorPointer.secondary4,
                  textStyle: CompanyTextStylePointer.b3,
                ),
              ),
              const SizedBox(width: 4),
              CompanyIcon(
                CompanyIconModel(
                  icon: CompanyIconPointer.apple.toGraphicAsset(),
                  size: CompanyIconSize.small,
                ),
              ),
              const SizedBox(width: 4),
              CompanyIcon(
                CompanyIconModel(
                  icon: CompanyIconPointer.google_bw.toGraphicAsset(),
                  size: CompanyIconSize.small,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
