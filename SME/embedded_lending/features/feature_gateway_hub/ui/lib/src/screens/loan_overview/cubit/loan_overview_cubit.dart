import 'dart:async';

import 'package:embedded_lending_core_api/embedded_lending_core_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/cubit/base_cubit.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_gateway_hub_api/gateway_hub_api.dart';
import 'package:wio_feature_gateway_hub_ui/l10n/gateway_hub_localizations.g.dart';
import 'package:wio_feature_gateway_hub_ui/src/dialogs/consent_share_loan_information/consent_share_loan_information_dialog.dart';
import 'package:wio_feature_gateway_hub_ui/src/utils/auth_proxy.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

part 'loan_overview_cubit.freezed.dart';
part 'loan_overview_state.dart';

class LoanOverviewCubit extends BaseCubit<LoanOverviewState> {
  static const SmeCreditProductType _productType =
      SmeCreditProductType.businessLoan;

  final CreditApplicationCreator _creditApplicationCreator;
  final Logger _logger;
  final CreditApplicationInteractor _interactor;
  final ContentInteractor _contentInteractor;
  final LoanConsentInteractor _loanConsentInteractor;
  final NavigationProvider _navigationProvider;
  final ResponsiveDialogProvider _responsiveDialogProvider;
  final GatewayHubLocalizations _localizations;
  final CreditAnalytics _analytics;
  final String _externalReferenceId;
  final AuthProxy _authProxy;

  LoanOverviewCubit({
    required CreditApplicationCreator creditApplicationCreator,
    required Logger logger,
    required CreditApplicationInteractor interactor,
    required ContentInteractor contentInteractor,
    required LoanConsentInteractor loanConsentInteractor,
    required NavigationProvider navigationProvider,
    required ResponsiveDialogProvider responsiveDialogProvider,
    required GatewayHubLocalizations localizations,
    required CreditAnalytics analytics,
    required String externalReferenceId,
    required AuthProxy authProxy,
  })  : _creditApplicationCreator = creditApplicationCreator,
        _logger = logger,
        _interactor = interactor,
        _contentInteractor = contentInteractor,
        _loanConsentInteractor = loanConsentInteractor,
        _navigationProvider = navigationProvider,
        _responsiveDialogProvider = responsiveDialogProvider,
        _localizations = localizations,
        _analytics = analytics,
        _externalReferenceId = externalReferenceId,
        _authProxy = authProxy,
        super(const LoanOverviewState.loading());

  Future<void> init(Partner partner) async {
    try {
      final (productInfo, (tnc, kfs), consentScopes) = await (
        _interactor.getProductInfo(
          productType: _productType,
        ),
        _fetchTncAndKfs(),
        _loanConsentInteractor.getConsentScopes(partner),
      ).wait;

      safeEmit(
        LoanOverviewState.loaded(
          vatStatementExampleUrl: productInfo.vatTemplateUrl,
          tnc: tnc,
          kfs: kfs,
          consentScopes: consentScopes,
        ),
      );
    } on Object catch (_) {
      safeEmit(const LoanOverviewState.error());
    }
  }

  Future<void> onContinue() async {
    _logger.info('Attempting to start application.');
    await state.mapOrNull(
      loaded: (loadedState) async {
        try {
          safeEmit(const LoanOverviewState.processing());

          await _loanConsentInteractor.postLoanConsent(
            partnerLoanConsentScope: loadedState.consentScopes,
            externalReferenceId: _externalReferenceId,
          );
          await _creditApplicationCreator
              .createFromReferenceId(_externalReferenceId);

          _logger.info('Application created.');
          _analytics.createApplication(productType: _productType);

          if (isClosed) return;

          safeEmit(loadedState);
        } on Object catch (error, stackTrace) {
          _logger.error(
            'Failed to post loan consent or create application.',
            error: error,
            stackTrace: stackTrace,
          );
          safeEmit(const LoanOverviewState.error());
        }
      },
    );
  }

  void openVatExample() {
    state.mapOrNull(
      loaded: (loadedState) {
        _navigationProvider.push(
          CreditPdfViewerNavigationConfig(
            config: CreditPdfViewerConfig.url(
              pageTitle: _localizations.vatStatementSampleTitle,
              documentUrl: loadedState.vatStatementExampleUrl,
              sourceUiId: CreditUiId.creditOverview,
              documentType: CreditDocumentType.vatStatement,
              productType: _productType,
            ),
          ),
        );
      },
    );
  }

  void openDocument(LoanDocumentType documentType) {
    state.mapOrNull(
      loaded: (loadedState) {
        final (document, documentViewerType) = switch (documentType) {
          LoanDocumentType.keyFactsStatement => (
              LoanDocument(
                documentType: documentType,
                enUrl: loadedState.kfs.enUrl,
                arUrl: loadedState.kfs.arUrl,
              ),
              ViewerDocumentType.businessLoanKeyFactStatement
            ),
          LoanDocumentType.termsAndConditions => (
              LoanDocument(
                documentType: documentType,
                enUrl: loadedState.tnc.enUrl,
                arUrl: loadedState.tnc.arUrl,
              ),
              ViewerDocumentType.businessLoanTermsAndConditions
            ),
        };

        _navigationProvider.push(
          LoanDocumentPageNavigationConfig(
            delegateConfig: SmeCreditDocumentViewerDelegateWithDocumentConfig(
              document: document,
              sourceUiId: CreditUiId.embeddedLendingLoanOverview,
              type: documentViewerType,
              productIdentifier: LoanProductIdentifier.businessLoan,
            ),
          ),
        );
      },
    );
  }

  void openConsentShareLoanInfoScope() {
    state.mapOrNull(
      loaded: (loadedState) {
        _responsiveDialogProvider.showBottomSheetOrDialog<void>(
          content: ConsentShareLoanInformationDialog(
            partnerLoanConsentScope: loadedState.consentScopes,
          ),
          config: const ResponsiveModalConfig(
            featureName: GatewayHubFeatureNavigationConfig.name,
          ),
        );
      },
    );
  }

  Future<void> logout() async {
    await _authProxy.logout();
  }

  Future<(LocalizedDocumentLinks, LocalizedDocumentLinks)>
      _fetchTncAndKfs() async {
    final (tnc, kfs) = await (
      _contentInteractor.getContentDocument(
        documentType: ContentDocumentType.businessLoanTermsAndConditions,
      ),
      _contentInteractor.getContentDocument(
        documentType: ContentDocumentType.businessLoanKeyFactStatement,
      ),
    ).wait;

    final tncLinks = tnc.localizedDocumentLinks;
    final kfsLinks = kfs.localizedDocumentLinks;

    if (tncLinks == null || kfsLinks == null) {
      throw Exception('Failed to load contentful documents.');
    }

    return (tncLinks, kfsLinks);
  }

  @override
  String toString() => 'LoanOverviewCubit';
}
