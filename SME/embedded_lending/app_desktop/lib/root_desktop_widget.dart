import 'dart:async';

import 'package:flutter/material.dart';
import 'package:sme_core_ui_desktop/index.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';

/// Widget shows loader indicator and snackbar
class RootDesktopWidget extends StatefulWidget {
  final NewNotificationService newNotificationService;
  final Widget child;
  final CompanyTheme theme;

  const RootDesktopWidget({
    required this.newNotificationService,
    required this.child,
    required this.theme,
    super.key,
  });

  @override
  State<RootDesktopWidget> createState() => _RootDesktopWidgetState();
}

class _RootDesktopWidgetState extends State<RootDesktopWidget> {
  StreamSubscription<Object?>? _subscription;

  @override
  void initState() {
    super.initState();
    _subscription =
        widget.newNotificationService.publishSubject.stream.listen((event) {
      final model = event?.snackbarModel;
      if (mounted && event != null && model != null) {
        ScaffoldMessenger.maybeOf(context)
            ?.showSnackBar(SnackbarWrapper(model));
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    _subscription?.cancel();
  }

  @override
  Widget build(BuildContext context) => Scaffold(
        body: CompanyThemeProvider(theme: widget.theme, child: widget.child),
      );
}
