import 'dart:async';

import 'package:account_feature_impl/account_feature_impl.dart';
import 'package:di/di.dart';
import 'package:domain/stream/exhaust_stream_executor.dart';
import 'package:embedded_lending/analytics/embedded_lending_analytics_configurator.dart';
import 'package:embedded_lending/di/deferred_localization_source.dart';
import 'package:embedded_lending/global/global_keys.dart';
import 'package:embedded_lending/navigation/embedded_lending_navigator_router.dart';
import 'package:feature_2fa_api/feature_2fa_api.dart' as two_fa_api;
import 'package:feature_2fa_impl/feature_2fa_impl.dart' as two_fa_impl;
import 'package:feature_2fa_ui_desktop_baas/feature_baas_two_factor_auth_ui_desktop.dart';
import 'package:feature_locale_impl/feature_locale_impl.dart';
import 'package:feature_login_impl/feature_login.dart' as login_impl;
import 'package:feature_remote_file_impl/remote_file.dart';
import 'package:feature_session_monitoring_impl/feature_session_monitoring_impl.dart';
import 'package:feature_tools_impl/feature_tools_impl.dart' as tools_impl
    show FeatureToolsDomainDependencyModuleResolver;
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:sme_configuration/configuration.dart';
import 'package:sme_core_ui_desktop/index.dart';
import 'package:sme_feature_transactions_impl/feature_transactions_impl.dart';
import 'package:sme_feature_transactions_ui/wio_feature_transactions_ui.dart';
import 'package:ui/l10n/common_localizations.g.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_common_feature_context_faq_api/wio_common_feature_context_faq_api.dart';
import 'package:wio_common_feature_context_faq_impl/wio_common_feature_context_faq_impl.dart';
import 'package:wio_common_feature_context_faq_ui/l10n/context_faq_localizations.g.dart';
import 'package:wio_common_feature_context_faq_ui/wio_common_feature_context_faq_ui.dart';
import 'package:wio_common_feature_document_upload_impl/feature_document_upload_impl.dart';
import 'package:wio_common_file_picker_ui/file_picker_ui.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_critical_notification_impl/feature_critical_notification_impl.dart';
import 'package:wio_feature_adaptive_status_view_ui/feature_status_view_adaptive_ui.dart';
import 'package:wio_feature_behaviour_impl/feature_behaviour_impl.dart'
    as behaviour_impl;
import 'package:wio_feature_centralised_questionnaire_service_impl/feature_centralised_questionnaire_service_impl.dart';
import 'package:wio_feature_centralised_questionnaire_service_ui/feature_centralised_questionnaire_service_mobile_ui.dart';
import 'package:wio_feature_clipboard_manager_ui/wio_feature_clipboard_manager_ui.dart';
import 'package:wio_feature_common_toast_message_ui/feature_toast_message_ui.dart';
import 'package:wio_feature_contact_support_ui/feature_contact_support_ui.dart';
import 'package:wio_feature_cqs_ui_desktop/feature_cqs_desktop_ui.dart';
import 'package:wio_feature_credit_impl/feature_credit_impl.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_face_recognition_ui_desktop/feature_face_recognition_ui_desktop.dart';
import 'package:wio_feature_faq_impl/feature_faq_impl.dart';
import 'package:wio_feature_faq_ui/wio_feature_faq_ui.dart';
import 'package:wio_feature_fx_impl/feature_fx_impl.dart';
import 'package:wio_feature_gateway_hub_impl/feature_gateway_hub_impl.dart';
import 'package:wio_feature_gateway_hub_ui/feature_gateway_hub_mobile_ui.dart';
import 'package:wio_feature_gateway_hub_ui/l10n/gateway_hub_localizations.g.dart';
import 'package:wio_feature_home_ui/feature_home_ui.dart';
import 'package:wio_feature_loan_impl/feature_loan_impl.dart';
import 'package:wio_feature_loan_ui/feature_loan_ui.dart';
import 'package:wio_feature_login_consent_ui_desktop/feature_login_consent_ui_desktop.dart';
import 'package:wio_feature_login_consent_ui_desktop/locale/login_ui_consent_desktop_localizations.g.dart';
import 'package:wio_feature_multiuser_impl/multiuser_impl.dart'
    as multiuser_impl;
import 'package:wio_feature_onboarding_licensing_authority_impl/feature_onboarding_licensing_authority_impl.dart';
import 'package:wio_feature_onboarding_ui_desktop/feature_onboarding_ui_desktop.dart';
import 'package:wio_feature_share_ui/feature_share_ui.dart';
import 'package:wio_feature_subscription_impl/feature_subscription_impl.dart';
import 'package:wio_feature_subscription_ui_desktop/feature_subscription_ui_desktop.dart';
import 'package:wio_feature_transaction_ui/feature_transaction_ui.dart';
import 'package:wio_sme_error_handler_api/error_handler_api.dart';
import 'package:wio_sme_error_handler_ui_desktop/baas_error_handler_impl.dart';
import 'package:wio_sme_faq_ui_desktop/faq_feature_ui_desktop.dart';

class ApplicationDependenciesModuleResolver {
  static Future<void> register() async {
    _registerCommon();
    _registerAnalyticsConfigurator();
    _registerApplication();
    _registerLocalizations();
    await _registerFeatures();
    _registerNavigation();
  }

  static void _registerCommon() {
    DependencyProvider.registerFactory<ExhaustStreamExecutor>(
      () => ExhaustStreamExecutorImpl(),
    );

    DependencyProvider.registerLazySingleton<ErrorHandlerTool>(
      () => BaasErrorHandlerTool(
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
        notificationService: DependencyProvider.get<NewNotificationService>(),
        logger: DependencyProvider.get<Logger>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        tracker: DependencyProvider.get<AnalyticsAbstractTrackerFactory>().get(
          screenName: 'toast_error_message',
          tracker: AnalyticsTracker.mixpanel,
        ),
        errorReporter: DependencyProvider.get<ErrorReporter>(),
      ),
    );
  }

  static Future<void> _registerFeatures() async {
    tools_impl.FeatureToolsDomainDependencyModuleResolver.register();

    login_impl.LoginDomainDependencyModuleResolver.register();
    LoginConsentFeatureDependencyModuleResolver.register();

    two_fa_impl.TwoFactorAuthenticationDomainDependencyModuleResolver.register(
      const two_fa_api.IdentityConfig(
        urlConfiguration: two_fa_api.IdentityUrlConfiguration.smeWeb,
        supportedFactors: [
          two_fa_api.TwoFactorAuthType.smsOtp,
          two_fa_api.TwoFactorAuthType.emailOtp,
          two_fa_api.TwoFactorAuthType.efr,
        ],
      ),
    );

    // TODO(core-team): Delete. Used because of dependency in login page
    behaviour_impl.BehaviourDomainDependencyModuleResolver.register();
    multiuser_impl.MultiuserDependencyModuleResolver.register();

    SessionMonitoringDomainDependencyModuleResolver.register(
      sessionDuration: const Duration(milliseconds: 30 * 60 * 1000),
    );

    HomeFeatureDependencyModuleResolver.register();
    BaasTwoFactorAuthFeatureDependencyModuleResolver.register();
    AdaptiveStatusViewFeatureDependencyModuleResolver.register();
    ResponsiveDialogDependencyModuleResolver.register();

    CreditDomainDependencyModuleResolver.register();
    CreditFeatureDependencyModuleResolver.register();
    FaceRecognitionFeatureDependencyModuleResolver.register();
    FAQDomainDependencyModuleResolver.register();
    FaqFeatureDependencyModuleResolver.register();
    LoanFeatureDependencyModuleResolver.register();
    LoanDomainDependencyModuleResolver.register();
    GatewayHubDomainDependencyModuleResolver.register();
    GatewayHubFeatureDependencyModuleResolver.register();
    ToastMessageFeatureDependencyModuleResolver.register();
    FxDomainDependencyModuleResolver.register();
    await AccountDomainDependencyModuleResolver.register(
      useMockRepository: false,
    );

    TransactionsDomainDependencyModuleResolver.register();
    SMETransactionsFeatureDependencyModuleResolver.register();
    TransactionFeatureDependencyModuleResolver.register();
    ClipboardManagerFeatureDependencyModuleResolver.register();
    ShareFeatureDependencyModuleResolver.register();
    FilePickerFeatureDependencyModuleResolver.register();
    ContactSupportFeatureDependencyModuleResolver.register();
    CommonFaqFeatureDependencyModuleResolver.register(
      applicationType: ApplicationType.sme,
    );
    ContextFaqDomainDependencyModuleResolver.register(
      ContextFaqDataConfig(
        subscriptionKey: DependencyProvider.get<EnvProvider>()
            .get(SmeEnvKey.apimSubscriptionKey),
        product: ContextFaqProduct.sme,
        appType: ContextFaqAppType.web,
      ),
    );
    ContextFaqFeatureDependencyModuleResolver.register();
    LocaleDomainDependencyModuleResolver.register();

    // Register Document Upload
    DocumentUploadDomainDependencyModuleResolver.register();
    RemoteFileDomainDependencyModuleResolver.register();

    // Register CQS dependencies
    CentralisedQuestionnaireServiceDomainDependencyModuleResolver.register();
    CentralisedQuestionnaireServiceFeatureDependencyModuleResolver.register();
    CQSFeatureDependencyModuleResolver.register();

    // Register Onboarding
    OnboardingFeatureDependencyModuleResolver.register();

    // Register Critical Notification as it is being used in CQS
    CriticalNotificationDependencyModuleResolver.register();

    // Register Onboarding Licensing Authority (provides AuthTokenService)
    OnboardingEmployeeDomainDependencyModuleResolver.register();

    // Register Subscription feature
    SubscriptionDomainDependencyModuleResolver.register();
    SubscriptionFeatureDependencyModuleResolver.register();
  }

  static void _registerApplication() {
    DependencyProvider.registerLazySingleton<BuildContext>(
      () => GlobalKeys.navigatorKey.currentContext!,
    );

    DependencyProvider.registerLazySingleton<NavigatorState>(
      () => GlobalKeys.navigatorKey.currentState!,
    );

    DependencyProvider.registerLazySingleton<NewNotificationService>(
      () => NewNotificationService(),
    );
  }

  static void _registerAnalyticsConfigurator() {
    DependencyProvider.registerLazySingleton<AnalyticsConfigurator>(() {
      final mixpanelSource = MixpanelConfigurationSource(
        mixpanel: DependencyProvider.get<MixpanelProxy>(),
      );

      final firebaseSource = FirebaseConfigurationSource(
        firebaseAnalytics: DependencyProvider.get<FirebaseAnalytics>(),
        logger: DependencyProvider.get<Logger>(),
      );

      final configurator = EmbeddedLendingAnalyticsConfigurator(
        mixpanelConfiguration: mixpanelSource,
        firebaseConfiguration: firebaseSource,
      );

      return configurator;
    });
  }

  static void _registerLocalizations() {
    DependencyProvider.registerLazySingleton(
      () => CommonLocalizations.of(DependencyProvider.get<BuildContext>()),
    );

    DependencyProvider.registerLazySingleton<DeferredLocalizationSource>(
      DeferredLocalizationSource.new,
    );

    DependencyProvider.registerLazySingleton<
        List<LocalizationsDelegate<Object?>>>(
      () => [
        CommonLocalizations.delegate,
        LoginConsentUILocalizations.delegate,
        TwoFaBaasLocalizations.delegate,
        FaqUiLocalizations.delegate,
        LoanLocalizations.delegate,
        GatewayHubLocalizations.delegate,
        CreditLocalizations.delegate,
        TransactionLocalizations.delegate,
        FaqLocalizations.delegate,
        ContextFaqLocalizations.delegate,
        ContactSupportLocalizations.delegate,
        CQSLocalizations.delegate,
        SubscriptionUiLocalizations.delegate,
      ],
    );
  }

  static void _registerNavigation() {
    final navigationProvider = NavigationProviderImpl(
      navigatorFactory: DependencyProvider.get,
      featureRouterFactory: (feature) =>
          DependencyProvider.get<NavigationRouter>(instanceName: feature),
      buildContextFactory: () => DependencyProvider.get<BuildContext>(),
    );

    DependencyProvider.registerLazySingleton<NavigationProvider>(
      () => SmeWebNavigationProvider(
        navigationProviderImpl: navigationProvider,
      ),
    );

    DependencyProvider.registerLazySingleton<ApplicationNavigatorRouter>(
      () => EmbeddedLendingNavigatorRouter(),
    );
  }
}
