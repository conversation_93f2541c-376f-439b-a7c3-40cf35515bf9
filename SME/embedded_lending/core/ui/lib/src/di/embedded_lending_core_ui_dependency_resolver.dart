import 'package:di/di.dart';
import 'package:embedded_lending_core_api/embedded_lending_core_api.dart';
import 'package:embedded_lending_core_ui/index.dart';
import 'package:wio_app_core_api/index.dart';

abstract class EmbeddedLendingCoreUiDependencyResolver {
  static Future<void> register() async {
    final storage = DependencyProvider.get<Storage<String?>>(
      instanceName: EmbeddedLendingAppConstants.sessionStorage,
    );
    final partnerName = await storage
        .getByKey(EmbeddedLendingAppConstants.partnerNameIdStorageKey);

    DependencyProvider.registerLazySingleton<EmbeddedLendingThemeResolver>(
      () => EmbeddedLendingThemeResolver(
        partner: Partner.fromName(partnerName),
      ),
    );
  }
}
