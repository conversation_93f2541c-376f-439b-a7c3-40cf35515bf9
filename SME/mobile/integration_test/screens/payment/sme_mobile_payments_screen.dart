import 'package:feature_payment_v2_ui/feature_payment_v2_ui.dart';
import 'package:feature_payment_v2_ui/src/screens/payments/views/last_payments.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/src/finders.dart';
import 'package:integration_test_core/integration_test_core.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:wio_common_feature_payments_ui/wio_common_feature_payments_ui.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/currency_selection/currency_selection_page.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import '../sme_mobile_base_screen.dart';

class SMEMobilePaymentsScreen implements BaseScreen {
  final plusIcon = find.descendant(
      of: find.byType(DashboardScreen),
      matching: find.byIcon(CompanyIcons.plus));
  final nextButton = find.byKey(CurrencySelectionPage.numpadNextKey);
  final continueButton = find.textContaining('Next', findRichText: true);
  final addNewPayeeButton = find.byIcon(CompanyIcons.plus);
  final ibanInput = find.byType(TextField);
  final closeIcon = find.byIcon(CompanyIcons.close);
  final deleteIcon = find.byIcon(CompanyPictograms.delete);
  final newAddedPayee = find.byType(PayeeCircleAvatar).first;
  final createPaymentTitle =
      find.textContaining('Create payment', findRichText: true);
  final sendMoneyToTitle =
      find.textContaining('Send money to', findRichText: true);
  final recentPaymentsTitle =
      find.textContaining('Recent payments', findRichText: true);
  final recentPayee = find.descendant(
      of: find.byType(RecentPayeesView).at(0),
      matching: find.byType(RecentPayeeListItem),
      matchRoot: true,
      skipOffstage: false);
  final recentPayment = find.descendant(
      of: find.byType(LastPayments),
      matching: find.byType(PaymentCell),
      matchRoot: true,
      skipOffstage: false);
  final viewAllButton = find.text('View all');
  final internationalTab =
      find.textContaining(RegExp(r'I(nternational|NTERNATIONAL)'));
  final aedAccount = find.text('AED account');
  final billsTab = find.textContaining(RegExp(r'B(ills|ILLS)'));
  final currencyCode = find.byKey(NumpadEntryInputCell.currencyCodeKey);
  static final sumToSendInput =
      find.byKey(CurrencySelectionPage.numpadEntryInputCellKey);
  static final sumInput = find.byKey(CurrencySelectionPage.textFieldKey);

  @override
  Finder get body => plusIcon;

  @override
  Finder element(String element) {
    switch (element.toLowerCase()) {
      case 'plus icon':
        return plusIcon;
      case 'currency chevron':
        return currencyCode;
      case 'next button':
        return nextButton;
      case 'continue':
        return continueButton;
      case 'sum input':
        return sumToSendInput;
      case 'international tab':
        return internationalTab;
      case 'new payee button':
        return addNewPayeeButton;
      case 'iban input':
        return ibanInput;
      case 'close icon':
        return closeIcon;
      case 'delete icon':
        return deleteIcon;
      case 'new added payee':
        return newAddedPayee;
      case 'recent payee':
        return recentPayee;
      case 'recent payment':
        return recentPayment;
      case 'view all payees':
        return viewAllButton.at(0);
      case 'view all payments':
        return viewAllButton.at(1);
      case 'aed account':
        return aedAccount;
      case 'bills tab':
        return billsTab;
      default:
        return find.textContaining(element, findRichText: true);
    }
  }
}
