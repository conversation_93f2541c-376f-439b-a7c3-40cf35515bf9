import 'package:flutter/services.dart';
import 'package:wio_feature_onfido_api/onfido_api.dart';

abstract class OnfidoNativeExceptionsMapper {
  Never handleLegacyException(PlatformException oldException, StackTrace trace);
}

class OnfidoNativeExceptionsMapperImpl implements OnfidoNativeExceptionsMapper {
  @override
  Never handleLegacyException(
      PlatformException oldException, StackTrace stackTrace) {
    switch (oldException.code) {
      case 'cameraPermission':
        Error.throwWithStackTrace(
          OnfidoCameraPermissionException(),
          stackTrace,
        );
      case 'microphonePermission':
        Error.throwWithStackTrace(
          OnfidoMicrophonePermissionException(),
          stackTrace,
        );
      case 'failedToWriteToDisk':
        Error.throwWithStackTrace(
          OnfidoIOException(),
          stackTrace,
        );
      case 'upload':
        Error.throwWithStackTrace(
          OnfidoUploadException(),
          stackTrace,
        );
      case 'geoBlockedRequest':
        Error.throwWithStackTrace(
          OnfidoGeoBlockedRequestException(),
          stackTrace,
        );
      default:
        Error.throwWithStackTrace(
          OnfidoNativeException(),
          stackTrace,
        );
    }
  }
}
