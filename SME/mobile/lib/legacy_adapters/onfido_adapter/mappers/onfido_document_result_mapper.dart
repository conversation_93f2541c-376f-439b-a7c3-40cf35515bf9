import 'package:app/legacy_adapters/onfido_adapter/mappers/onfido_document_result_details_mapper.dart';
import 'package:mobile_kyc/features/onfido/services/onfido/onfido_config.dart'
    as odl_impl;
import 'package:wio_feature_onfido_api/onfido_api.dart';

abstract class OnfidoDocumentResultMapper {
  OnfidoDocumentResult mapLegacyToNew(odl_impl.OnfidoDocumentResult old);

  odl_impl.OnfidoDocumentResult mapNewToLegacy(OnfidoDocumentResult newObject);
}

class OnfidoDocumentResultMapperImpl implements OnfidoDocumentResultMapper {
  final OnfidoDocumentResultDetailsMapper _onfidoDocumentResultDetailsMapper;

  const OnfidoDocumentResultMapperImpl({
    required OnfidoDocumentResultDetailsMapper
        onfidoDocumentResultDetailsMapper,
  }) : _onfidoDocumentResultDetailsMapper = onfidoDocumentResultDetailsMapper;

  @override
  OnfidoDocumentResult mapLegacyToNew(odl_impl.OnfidoDocumentResult old) {
    return OnfidoDocumentResult(
      front: _onfidoDocumentResultDetailsMapper.mapLegacyToNew(old.front),
      back: old.back != null
          ? _onfidoDocumentResultDetailsMapper.mapLegacyToNew(old.back!)
          : null,
    );
  }

  @override
  odl_impl.OnfidoDocumentResult mapNewToLegacy(
    OnfidoDocumentResult newObject,
  ) =>
      odl_impl.OnfidoDocumentResult(
        front: _onfidoDocumentResultDetailsMapper.mapNewToLegacy(
          newObject.front,
        ),
        back: newObject.back != null
            ? _onfidoDocumentResultDetailsMapper.mapNewToLegacy(newObject.back!)
            : null,
      );
}
