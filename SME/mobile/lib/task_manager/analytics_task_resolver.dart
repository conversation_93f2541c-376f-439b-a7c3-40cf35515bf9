import 'package:core/analytics/mix_panel_analytics_adapter.dart';
import 'package:core/ioc/di_container.dart';
import 'package:task_manager/api_task_resolver.dart';

class AnalyticsTaskResolver implements TaskResolver {
  final MixPanelAnalyticsAdapter _analytics;

  AnalyticsTaskResolver()
      : _analytics = DIContainer.container.resolve<MixPanelAnalyticsAdapter>();

  @override
  Future execute(String apiIdentifier, Map<String, dynamic> requestData) async {
    switch (apiIdentifier) {
      case 'analytics_log_event':
        _analytics.logEvent(
          requestData['eventName'] as String,
          requestData['properties'] as Map<String, dynamic>?,
        );
        return Future.value();
      case 'analytics_log_screen':
        _analytics.logScreen(
          requestData['screenName'] as String,
        );
        return Future.value();

      default:
        throw UnimplementedError(
            'Error: [$apiIdentifier] not found for analytics event');
    }
  }
}
