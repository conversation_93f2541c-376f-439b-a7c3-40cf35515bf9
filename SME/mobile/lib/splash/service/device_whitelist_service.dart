import 'package:app/splash/data_model/device_whitelisted_status_data_model.dart';
import 'package:app/splash/service/device_whitelist_graphql_query.dart';
import 'package:network_manager/model/requests/graph_ql/graphql_get_request.dart';

class DeviceWhitelistService {
  GraphQLRequest getGraphQLRequest(String deviceId) {
    final dataModel = DeviceWhitelistedStatusDataModel();
    final request = DeviceWhitelistGraphQLQuery.query(deviceId);
    return GraphQLRequest(
      dataModel,
      request,
      GraphQlRequestType.query,
      name: 'login',
      showBusy: false,
    );
  }
}
