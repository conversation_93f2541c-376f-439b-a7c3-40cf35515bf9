import 'package:account_feature_api/account_feature_api.dart';
import 'package:account_feature_impl/account_feature_impl.dart';
import 'package:app/di/core_dependencies_module_resolver.dart';
import 'package:app/di/features/clearable_dependencies_resolver.dart';
import 'package:app/di/features/dev_menu_dependencies_module_resolver.dart';
import 'package:app/di/features/feature_toggles_dependencies_module_resolver.dart';
import 'package:app/di/network_dependencies_module_resolver.dart';
import 'package:app/feature_toggles/sme_mobile_feature_toggles.dart';
import 'package:app/legacy_adapters/bound_device_adapter/bound_device_adapter.dart';
import 'package:app/route_manager/sme_application_navigation_router.dart';
import 'package:app_mobile_login/features/additional_questions/l10n/additional_questions_localizations.g.dart';
import 'package:app_mobile_login/features/referral_code/l10n/referral_code_localization.g.dart';
import 'package:common_bottom_sheet_impl/common_bottom_sheet_impl.dart';
import 'package:common_bottom_sheet_ui/common_bottom_sheet_ui.dart';
import 'package:common_device_check_api/common_device_check_api.dart';
import 'package:common_device_check_ui/common_device_check.dart';
import 'package:common_dialog_ui/common_dialog_ui.dart';
import 'package:common_feature_analytics_ui/common_feature_analytics_ui.dart';
import 'package:common_feature_fx_api/feature_fx_api.dart';
import 'package:common_feature_toggle_impl/common_feature_toggle_impl.dart';
import 'package:common_feature_user_financial_details_api/feature_user_financial_details_api.dart';
import 'package:common_feature_user_financial_details_impl/feature_user_financial_details_impl.dart';
import 'package:common_feature_user_financial_details_ui/feature_user_financial_details_ui.dart';
import 'package:common_webview_ui/common_web_view_ui.dart';
import 'package:core/storage/storage_service.dart';
import 'package:core/two_factor_authentication/two_factor_authentication.dart';
import 'package:di/di.dart';
import 'package:domain/domain.dart';
import 'package:domain/stream/exhaust_stream_executor.dart';
import 'package:feature_2fa_api/feature_2fa_api.dart';
import 'package:feature_2fa_impl/feature_2fa_impl.dart';
import 'package:feature_2fa_ui/feature_2fa_ui.dart';
import 'package:feature_bottom_sheet_adapter_ui/feature_bottom_sheet_adapter_ui.dart';
import 'package:feature_card_api/card.dart';
import 'package:feature_card_impl/card.dart';
import 'package:feature_counter_party_impl/counter_party.dart';
import 'package:feature_faq_impl/feature_faq_impl.dart';
import 'package:feature_info_impl/feature_info_impl.dart';
import 'package:feature_locale_impl/feature_locale_impl.dart';
import 'package:feature_login_api/feature_login_api.dart';
import 'package:feature_login_impl/feature_login.dart';
import 'package:feature_login_ui/feature_login_mobile_ui.dart';
import 'package:feature_navigation_adapter_ui/feature_navigation_adapter_ui.dart';
import 'package:feature_onboarding_api/feature_onboarding_api.dart';
import 'package:feature_payment_v2_api/feature_payment_v2_api.dart';
import 'package:feature_payment_v2_impl/feature_payment_v2_impl.dart';
import 'package:feature_payment_v2_ui/feature_payment_v2_ui.dart';
import 'package:feature_remote_file_impl/remote_file.dart';
import 'package:feature_tools_impl/feature_tools_impl.dart';
import 'package:feature_user_financial_details_impl/feature_user_financial_details_impl.dart';
import 'package:feature_user_financial_details_ui/feature_user_financial_details_ui.dart';
import 'package:flutter/widgets.dart';
import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:sme_configuration/configuration.dart';
import 'package:sme_core_ui/index.dart';
import 'package:sme_feature_transactions_api/feature_transactions_api.dart';
import 'package:sme_feature_transactions_impl/feature_transactions_impl.dart';
import 'package:sme_feature_transactions_ui/wio_feature_transactions_ui.dart';
import 'package:statement_feature_api/statement_feature_api.dart';
import 'package:statement_feature_impl/statement_feature_impl.dart';
import 'package:ui/cubit/core/error_message_provider.dart';
import 'package:ui/cubit/core/loading_provider.dart';
import 'package:ui/l10n/common_localizations.g.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/security.dart';
import 'package:wio_common_feature_address_api/wio_common_feature_address_api.dart';
import 'package:wio_common_feature_address_ui/l10n/customer_address_localizations.g.dart';
import 'package:wio_common_feature_address_ui/wio_common_feature_address_ui.dart';
import 'package:wio_common_feature_context_faq_api/domain/context_faq_navigation_handler.dart';
import 'package:wio_common_feature_context_faq_api/wio_common_feature_context_faq_api.dart';
import 'package:wio_common_feature_context_faq_impl/wio_common_feature_context_faq_impl.dart';
import 'package:wio_common_feature_context_faq_ui/l10n/context_faq_localizations.g.dart';
import 'package:wio_common_feature_context_faq_ui/wio_common_feature_context_faq_ui.dart';
import 'package:wio_common_feature_customer_address_impl/di/customer_address_domain_dependency_module_resolver.dart';
import 'package:wio_common_feature_customer_address_ui/index.dart';
import 'package:wio_common_feature_document_upload_api/navigation/document_upload_feature_navigation_config.dart'
    as common_document_upload_api;
import 'package:wio_common_feature_document_upload_impl/feature_document_upload_impl.dart'
    as common_document_upload_impl;
import 'package:wio_common_feature_document_upload_ui/feature_document_upload_mobile_ui.dart'
    as common_document_upload_ui;
import 'package:wio_common_feature_locations_impl/di/locations_dependency_module_resolver.dart';
import 'package:wio_common_feature_payments_impl/wio_common_feature_payments_impl.dart'
    as common_payments_impl;
import 'package:wio_common_feature_payments_ui/wio_common_feature_payments_ui.dart';
import 'package:wio_common_feature_tin_validation_impl/tin_validation_impl.dart';
import 'package:wio_common_feature_transaction_impl/index.dart' as common_txn;
import 'package:wio_common_file_picker_api/file_picker_api.dart';
import 'package:wio_common_file_picker_ui/file_picker_ui.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_critical_notification_api/navigation/critical_notification_feature_navigation_config.dart';
import 'package:wio_critical_notification_impl/feature_critical_notification_impl.dart';
import 'package:wio_critical_notification_ui/feature_critical_notification_ui.dart';
import 'package:wio_critical_notification_ui/l10n/critical_notification_localizations.g.dart';
import 'package:wio_digital_trust_api/feature_digital_trust_api.dart';
import 'package:wio_digital_trust_impl/feature_digital_trust_impl.dart';
import 'package:wio_digital_trust_ui/digital_trust_ui.dart';
import 'package:wio_feature_account_closure_api/feature_account_closure_api.dart';
import 'package:wio_feature_account_closure_impl/feature_account_closure_impl.dart';
import 'package:wio_feature_account_closure_ui/feature_account_closure_ui.dart';
import 'package:wio_feature_account_closure_ui/l10n/account_closure_localization.g.dart';
import 'package:wio_feature_account_ui/feature_account_ui.dart';
import 'package:wio_feature_add_shareholder_api/add_shareholder_api.dart';
import 'package:wio_feature_add_shareholder_ui/feature_add_shareholder_ui.dart';
import 'package:wio_feature_animated_status_view_api/animated_status_view.dart';
import 'package:wio_feature_animated_status_view_ui/feature_animated_status_view_ui.dart';
import 'package:wio_feature_annual_kyc_api/annual_kyc_api.dart';
import 'package:wio_feature_annual_kyc_impl/feature_annual_kyc_impl.dart';
import 'package:wio_feature_annual_kyc_ui/feature_annual_kyc_ui.dart';
import 'package:wio_feature_app_rating_api/wio_feature_app_rating_api.dart';
import 'package:wio_feature_app_rating_impl/feature_app_rating_impl.dart';
import 'package:wio_feature_app_rating_ui/feature_app_rating_ui.dart';
import 'package:wio_feature_app_settings_navigator_api/app_settings_navigator_api.dart';
import 'package:wio_feature_app_settings_navigator_ui/feature_app_settings_navigator.dart';
import 'package:wio_feature_application_tracker_api/application_tracker_api.dart';
import 'package:wio_feature_application_tracker_impl/feature_application_tracker_impl.dart';
import 'package:wio_feature_application_tracker_ui/feature_application_tracker_ui.dart';
import 'package:wio_feature_backend_driven_flow_api/backend_driven_flow_api.modular.dart';
import 'package:wio_feature_backend_driven_flow_impl/feature_backend_driven_flow_impl.dart';
import 'package:wio_feature_backend_driven_flow_ui/feature_backend_driven_flow_mobile_ui.dart';
import 'package:wio_feature_be_driven_flow_api/be_driven_flow_api.dart';
import 'package:wio_feature_be_driven_flow_ui/feature_be_driven_flow_mobile_ui.dart';
import 'package:wio_feature_behaviour_impl/feature_behaviour_impl.dart';
import 'package:wio_feature_card_ui/feature_card_ui.dart';
import 'package:wio_feature_centralised_questionnaire_service_api/centralised_questionnaire_service_api.modular.dart';
import 'package:wio_feature_centralised_questionnaire_service_impl/feature_centralised_questionnaire_service_impl.dart';
import 'package:wio_feature_centralised_questionnaire_service_ui/feature_centralised_questionnaire_service_mobile_ui.dart';
import 'package:wio_feature_clipboard_manager_ui/wio_feature_clipboard_manager_ui.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_common_toast_message_ui/feature_toast_message_ui.dart';
import 'package:wio_feature_communications_settings_api/communications_settings_api.dart';
import 'package:wio_feature_communications_settings_impl/feature_communications_settings_impl.dart';
import 'package:wio_feature_communications_settings_ui/wio_feature_communications_settings_ui.dart';
import 'package:wio_feature_company_impl/company.dart';
import 'package:wio_feature_company_ui/company.dart';
import 'package:wio_feature_contact_support_ui/feature_contact_support_ui.dart';
import 'package:wio_feature_core_ui/index.dart';
import 'package:wio_feature_country_api/country_feature_api.dart';
import 'package:wio_feature_country_impl/country_feature_impl.dart';
import 'package:wio_feature_country_ui/feature_country_ui.dart';
import 'package:wio_feature_cqs_ui/feature_cqs_mobile_ui.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_impl/feature_credit_impl.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_customer_feedback_api/navigation/customer_feedback_feature_navigation_config.dart';
import 'package:wio_feature_customer_feedback_impl/index.dart';
import 'package:wio_feature_customer_feedback_ui/index.dart';
import 'package:wio_feature_customer_feedback_ui/l10n/customer_feedback.g.dart';
import 'package:wio_feature_dashboard_api/navigation/dashboard_feature_navigation_config.dart';
import 'package:wio_feature_dashboard_impl/feature_dashboard_impl.dart';
import 'package:wio_feature_dashboard_ui/feature_dashboard_ui.dart';
import 'package:wio_feature_debug_menu_api/feature_debug_menu_api.dart';
import 'package:wio_feature_delivery_api/navigation/delivery_feature_navigation_config.dart';
import 'package:wio_feature_delivery_impl/feature_delivery_impl.dart';
import 'package:wio_feature_delivery_ui/feature_delivery_ui.dart';
import 'package:wio_feature_device_info_collection_impl/index.dart';
import 'package:wio_feature_device_info_collection_ui/index.dart';
import 'package:wio_feature_document_upload_api/navigation/document_upload_feature_navigation_config.dart';
import 'package:wio_feature_document_upload_impl/feature_document_upload_impl.dart';
import 'package:wio_feature_document_upload_ui/feature_document_upload_ui.dart';
import 'package:wio_feature_document_viewer_api/document_viewer_api.dart';
import 'package:wio_feature_document_viewer_ui/feature_document_viewer_ui.dart';
import 'package:wio_feature_easy_cash_api/navigation/easy_cash_feature_navigation_config.dart';
import 'package:wio_feature_easy_cash_ui/feature_easy_cash_mobile_ui.dart';
import 'package:wio_feature_edd_activity_api/edd_activity_api.dart';
import 'package:wio_feature_edd_activity_impl/feature_edd_activity_impl.dart';
import 'package:wio_feature_edd_activity_ui/feature_edd_activity_ui.dart';
import 'package:wio_feature_eligibility_criteria_api/navigation/eligibility_criteria_feature_navigation_config.dart';
import 'package:wio_feature_eligibility_criteria_impl/feature_eligibility_criteria_impl.dart';
import 'package:wio_feature_eligibility_criteria_ui/feature_eligibility_criteria_ui.dart';
import 'package:wio_feature_face_recognition_api/navigation/face_recognition_feature_navigation_config.dart';
import 'package:wio_feature_face_recognition_impl/feature_face_recognition_impl.dart';
import 'package:wio_feature_face_recognition_ui/feature_face_recognition_ui.dart';
import 'package:wio_feature_faq_api/faq_api.dart';
import 'package:wio_feature_faq_impl/feature_faq_impl.dart';
import 'package:wio_feature_faq_ui/wio_feature_faq_ui.dart' as common_faq;
import 'package:wio_feature_fx_impl/feature_fx_impl.dart';
import 'package:wio_feature_fx_ui/feature_fx_ui.dart';
import 'package:wio_feature_fx_ui/src/l10n/feature_fx_localization.g.dart';
import 'package:wio_feature_home_ui/feature_home_ui.dart';
import 'package:wio_feature_invitation_api/navigation/invitation_feature_navigation_config.dart';
import 'package:wio_feature_invitation_impl/feature_invitation_impl.dart';
import 'package:wio_feature_invitation_ui/feature_invitation_ui.dart';
import 'package:wio_feature_invoice_ui/feature_invoice_mobile_ui.dart';
import 'package:wio_feature_invoices_v2_api/feature_invoices_api.dart';
import 'package:wio_feature_invoices_v2_impl/feature_invoices_v2_impl.dart';
import 'package:wio_feature_key_fact_statement_api/wio_feature_key_fact_statement_api.module.dart';
import 'package:wio_feature_key_fact_statement_impl/feature_key_fact_statement_impl.dart';
import 'package:wio_feature_key_fact_statement_ui/feature_key_fact_statement_ui.dart';
import 'package:wio_feature_loan_api/navigation/loan_feature_navigation_config.dart';
import 'package:wio_feature_loan_impl/feature_loan_impl.dart';
import 'package:wio_feature_loan_ui/feature_loan_ui.dart';
import 'package:wio_feature_mu_payment_requests_api/feature_mu_payment_requests_api.module.dart';
import 'package:wio_feature_mu_payment_requests_impl/feature_mu_payment_requests_impl.dart';
import 'package:wio_feature_mu_payment_requests_ui/wio_feature_mu_payment_requests_ui.dart';
import 'package:wio_feature_multi_signatory_api/multi_signatory_api.dart';
import 'package:wio_feature_multi_signatory_impl/feature_multi_signatory_impl.dart';
import 'package:wio_feature_multi_signatory_ui/feature_multi_signatory_ui.dart';
import 'package:wio_feature_multiuser_impl/multiuser_impl.dart';
import 'package:wio_feature_onboarding_api/navigation/onboarding_feature_navigation_config.dart';
import 'package:wio_feature_onboarding_employee_api/wio_feature_onboarding_employee_api.dart';
import 'package:wio_feature_onboarding_employee_impl/feature_onboarding_employee_impl.dart';
import 'package:wio_feature_onboarding_employee_ui/feature_onboarding_employee_ui_desktop.dart';
import 'package:wio_feature_onboarding_impl/feature_onboarding_impl.dart';
import 'package:wio_feature_onboarding_licensing_authority_impl/feature_onboarding_licensing_authority_impl.dart'
    as licensing_authority;
import 'package:wio_feature_onboarding_ui/feature_onboarding_ui.dart';
import 'package:wio_feature_onfido_ui/feature_onfido_ui.dart';
import 'package:wio_feature_phone_contacts_impl/feature_phone_contacts_impl.dart';
import 'package:wio_feature_phone_impl/feature_phone_impl.dart';
import 'package:wio_feature_re_ask_api/navigation/re_ask_feature_navigation_config.dart';
import 'package:wio_feature_re_ask_impl/feature_re_ask_impl.dart';
import 'package:wio_feature_re_ask_ui/feature_re_ask_ui.dart';
import 'package:wio_feature_saving_space_api/navigation/saving_space_feature_navigation_config.dart';
import 'package:wio_feature_saving_space_impl/feature_saving_space_impl.dart';
import 'package:wio_feature_saving_space_ui/feature_saving_space_mobile_ui.dart';
import 'package:wio_feature_scf_impl/feature_scf_impl.dart';
import 'package:wio_feature_scf_ui/feature_scf_ui.dart';
import 'package:wio_feature_settings_api/settings_feature_api.dart';
import 'package:wio_feature_settings_ui/settings_ui.dart';
import 'package:wio_feature_share_ui/feature_share_ui.dart';
import 'package:wio_feature_sof_impl/feature_sof_impl.dart';
import 'package:wio_feature_splash_ui/sme_mobile_splash_ui.dart';
import 'package:wio_feature_statement_ui/feature_statement_ui.dart';
import 'package:wio_feature_status_view_api/feature_status_view.dart';
import 'package:wio_feature_status_view_ui/feature_status_view_ui.dart';
import 'package:wio_feature_subscription_api/navigation/subscription_feature_navigation_config.dart';
import 'package:wio_feature_subscription_impl/feature_subscription_impl.dart';
import 'package:wio_feature_subscription_ui/feature_subscription_ui.dart';
import 'package:wio_feature_tax_declaration_api/navigation/tax_declaration_feature_navigation_config.dart';
import 'package:wio_feature_tax_declaration_impl/feature_tax_declaration_impl.dart';
import 'package:wio_feature_tax_declaration_ui/feature_tax_declaration_mobile_ui.dart';
import 'package:wio_feature_tax_number_api/navigation/tax_number_feature_navigation_config.dart';
import 'package:wio_feature_tax_number_impl/feature_tax_number_impl.dart';
import 'package:wio_feature_tax_number_ui/wio_feature_tax_number_ui.dart';
import 'package:wio_feature_transaction_ui/feature_transaction_ui.dart';
import 'package:wio_feature_turnover_api/navigation/turnover_feature_navigation_config.dart';
import 'package:wio_feature_turnover_impl/feature_turnover_impl.dart';
import 'package:wio_feature_turnover_ui/feature_turnover_ui.dart';
import 'package:wio_feature_update_kyc_api/update_kyc_api.dart';
import 'package:wio_feature_update_kyc_ui/feature_update_kyc_ui.dart';
import 'package:wio_feature_utap_api/navigation/utap_feature_navigation_config.dart';
import 'package:wio_feature_utap_impl/feature_utap_impl.dart';
import 'package:wio_feature_utap_ui/feature_utap_ui.dart';
import 'package:wio_feature_wps_impl/feature_wps_impl.dart';
import 'package:wio_feature_wps_ui/feature_wps_ui.dart';
import 'package:wio_feature_wps_ui/locale/wps_ui_localizations.g.dart';
import 'package:wio_sme_error_handler_api/error_handler_api.dart';
import 'package:wio_sme_error_handler_ui/error_handler_impl.dart';
import 'package:wio_sme_faq_ui/wio_feature_faq_ui.dart';
import 'package:wio_sme_reporting_api/configurator/reporting_configurator.dart';

import '../digital_trust/digital_trust_active_defense_work_delegate.dart';
import '../digital_trust/digitial_trust_critical_view_adaptor.dart';

class ApplicationDependenciesModuleResolver {
  const ApplicationDependenciesModuleResolver._();

  static Future<void> register({
    required AppEnvironment environment,
    required GlobalKey<NavigatorState> navigatorKey,
  }) async {
    _registerCommon();
    await _registerPlatform();
    await CoreDependenciesModuleResolver.register(environment);
    _registerContext(navigatorKey);
    _registerMultiuserCommonFeature();
    _registerCountryFeature();
    _registerTinValidation();
    _registerNavigation(environment);
    _registerBottomSheets();
    _registerLocalizations();
    _registerToastMessages();
    _registerResponsiveDialog();
    _registerShareWindow();
    _registerFeatureToggles(environment);
    _registerClipboardManager();
    _registerDeviceCheck(environment);
    NetworkDependenciesModuleResolver.register();
    _registerSubscription();
    await _registerAccount();
    _registerHomePage();
    _registerCards();
    _registerTwoFactorAuthentication();
    _registerStatusView();
    _registerInvitation();
    _registerSavingSpace();
    _registerSettings();
    _registerStatement();
    _registerLogin();
    _registerPaymentsV2();
    await _registerDashboard();
    _registerPendingRequests();
    await _registerAppRating();
    _registerPermissions();
    _registerPhone();
    _registerPhoneContacts();
    _registerTransactions();
    _registerFx();
    _registerOnboarding();
    _registerOnboardingEmployee();
    _registerKeyFactStatement();
    _registerMuPayments();
    _registerRemoteFile();
    _registerTaxNumber();
    _registerPayroll();
    _registerMultiSignatory();
    _registerApplicationTracker();
    _registerInvoicesV2();
    _registerReAsk();
    _registerDocumentUpload();
    _registerDelivery();
    _registerEligibilityCriteria();
    _registerUpdateKyc();
    _registerEddActivity();
    _registerDocumentViewer();
    _registerLicensingAuthority();
    _registerInfo();
    _registerAnnualKyc();
    _registerContactSupport();
    _registerTools();
    _registerScf();
    _registerLocation();
    _registerFAQ();
    _registerCommunicationSettings();
    _registerTurnover();
    _registerCompany();
    _registerUTap();
    _registerSmeCredit();
    _registerContextFaq();
    _registerSof();
    await _customerAddress();
    _registerCriticalNotification();
    _registerCommonDocumentUpload();
    _registerAccountClosure();
    _registerCounterParty();
    _registerLocale();
    // Clearable should be registered last
    // Because it depends on other features
    _registerClearable();
    _registerCommonFilePicker();
    _registerFaceRecognition();
    _userFinancialDetails();
    _registerEasyCash();
    _registerOnfido();
    _registerCustomerFeedback();
    _registerCommonBackendDrivenFlow();
    _registerBackendDrivenFlow();
    await _registerDevMenu(environment);
    _registerLoan();
    _registerCentralisedQuestionnaireService();
    _registerDigitalTrustConfig();
    _registerDigitalTrust();
    _registerSplashScreen();
    _registerCommonWebView();
    _registerAppEnvironment(environment);
    _registerCommonTaxDeclaration();
  }

  static void _registerAppEnvironment(AppEnvironment appEnvironment) {
    DependencyProvider.registerLazySingleton<AppEnvironment>(
      () => appEnvironment,
    );
  }

  static void _registerLocale() {
    LocaleDomainDependencyModuleResolver.register();
  }

  static void _registerCriticalNotification() {
    CriticalNotificationDependencyModuleResolver.register();
    CriticalNotificationFeatureModuleResolver.register(
        applicationType: ApplicationType.sme);
  }

  static void _registerMultiuserCommonFeature() {
    MultiuserDependencyModuleResolver.register();
    _registerAddShareholder();
  }

  static void _registerDigitalTrustConfig() {
    DependencyProvider.registerLazySingleton(
      () => FeedzaiConfig(
        key: DependencyProvider.get<EnvProvider>().get(
          SmeEnvKey.digitalTrustKey,
        ),
      ),
    );
  }

  static void _registerDigitalTrust() {
    DependencyProvider.registerLazySingleton<SMEDigitalTrustCriticalViewMarker>(
      () => SMEDigitalTrustCriticalViewFacade(
        digitalTrustInteractor:
            DependencyProvider.get<DigitalTrustInteractor>(),
        digitalTrustAnalytics: DependencyProvider.get<DigitalTrustAnalytics>(),
      ),
    );
    DependencyProvider.registerLazySingleton<DigitalTrustActiveDefenseDelegate>(
      () => DigitalTrustActiveDefenseWorkDelegate(
        reportingConfigurator: DependencyProvider.get<ReportingConfigurator>(),
        loginInteractor: DependencyProvider.get<LoginInteractor>(),
        bindDeviceInteractor: DependencyProvider.get<BindDeviceInteractor>(),
        storageService: DependencyProvider.get<StorageService>(),
      ),
    );
    DigitalTrustDomainDependencyModuleResolver.register();
    DigitalTrustFeatureDependencyModuleResolver.register();
  }

  static void _registerCountryFeature() {
    CountryDomainDependencyModuleResolver.register();
    CountryFeatureDependencyResolver.register();
  }

  static void _registerCommon() {
    DependencyProvider.registerLazySingleton<LoadingStreamProvider>(
      CoreLoadingStreamProvider.new,
    );

    DependencyProvider.registerLazySingleton<ErrorMessageProvider>(
      () => ToastErrorMessagePresentationProvider(
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
      ),
    );

    DependencyProvider.registerFactory<LoadingProvider>(
      () => DependencyProvider.get<LoadingStreamProvider>(),
    );

    DependencyProvider.registerFactory<ExhaustStreamExecutor>(
      () => ExhaustStreamExecutorImpl(),
    );

    DependencyProvider.registerLazySingleton<Clock>(() => const Clock());

    DependencyProvider.registerLazySingleton<ErrorHandlerTool>(
      () => MobileErrorHandlerTool(
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        tracker: DependencyProvider.get<AnalyticsAbstractTrackerFactory>().get(
          screenName: 'toast_error_message',
          tracker: AnalyticsTracker.mixpanel,
        ),
        errorReporter: DependencyProvider.get<ErrorReporter>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    DialogFeatureDependencyModuleResolver.register();

    DeviceInfoCollectionDomainDependencyModuleResolver.register(
        WioAppType.smeMobile);
    FeatureDeviceInfoCollectionDomainDependencyModuleResolver.registerAll();
  }

  static Future<void> _registerPlatform() async {
    await PlatformDependencyModuleResolver.register();
  }

  static void _registerContext(GlobalKey<NavigatorState> navigatorKey) {
    DependencyProvider.registerLazySingleton<BuildContext>(
      () => navigatorKey.currentContext!,
    );
    DependencyProvider.registerLazySingleton<NavigatorState>(
      () => navigatorKey.currentState!,
    );
    AppSettingsNavigatorFeatureDependencyModuleResolver.register();
  }

  static void _registerLocalizations() {
    DependencyProvider.registerLazySingleton(() => CommonLocalizations.of(
          DependencyProvider.get<BuildContext>(),
        ));

    DependencyProvider.registerLazySingleton<
        List<LocalizationsDelegate<dynamic>>>(
      () => [
        CommonLocalizations.delegate,
        SubscriptionLocalizations.delegate,
        CardLocalizations.delegate,
        UserInvitationLocalizations.delegate,
        WioPaymentsLocalizations.delegate,
        CompanyLocalizations.delegate,
        DashboardLocalizations.delegate,
        AppRatingLocalizations.delegate,
        SettingsLocalizations.delegate,
        TwoFaLocalizations.delegate,
        AccountLocalizations.delegate,
        ReferralCodeLocalizations.delegate,
        FxLocalizations.delegate,
        StatementLocalizations.delegate,
        OnboardingEmployeeLocalizations.delegate,
        KfsLocalizations.delegate,
        TaxNumberLocalizations.delegate,
        MultiSignatoryLocalizations.delegate,
        AddShareholderLocalizations.delegate,
        ApplicationTrackerLocalizations.delegate,
        ReAskLocalizations.delegate,
        DocumentUploadLocalizations.delegate,
        EligibilityCriteriaLocalizations.delegate,
        UpdateKycLocalizations.delegate,
        EddActivityLocalizations.delegate,
        MultiuserUiLocalizations.delegate,
        AnnualKycUiLocalizations.delegate,
        ContactSupportLocalizations.delegate,
        WpsUiLocalizations.delegate,
        DeliveryLocalizations.delegate,
        ScfLocalizations.delegate,
        CoreUiLocalizations.delegate,
        CustomerAddressLocalizations.delegate,
        ContactSupportLocalizations.delegate,
        TurnoverLocalizations.delegate,
        TransactionLocalizations.delegate,
        UTapLocalizations.delegate,
        AdditionalQuestionsLocalizations.delegate,
        CreditLocalizations.delegate,
        ContextFaqLocalizations.delegate,
        InvoicesLocalizations.delegate,
        UserFinancialDetailsLocalizations.delegate,
        UserFinancialDetailsUiLocalizations.delegate,
        CriticalNotificationLocalizations.delegate,
        SavingSpaceLocalizations.delegate,
        OnboardingLocalizations.delegate,
        EasyCashLocalizations.delegate,
        OnfidoLocalizations.delegate,
        CustomerFeedbackLocalizations.delegate,
        BackendDrivenFlowLocalizations.delegate,
        common_faq.FaqLocalizations.delegate,
        CommunicationsSettingsLocalizations.delegate,
        common_document_upload_ui.DocumentUploadLocalizations.delegate,
        LoanLocalizations.delegate,
        AccountClosureLocalizations.delegate,
        LoginUILocalizations.delegate,
        CQSLocalizations.delegate,
      ],
    );
  }

  static void _registerNavigation(AppEnvironment environment) {
    DependencyProvider.registerLazySingleton(
      () => NavigationProviderImpl(
        navigatorFactory: () => DependencyProvider.get(),
        featureRouterFactory: (feature) =>
            DependencyProvider.get<NavigationRouter>(instanceName: feature),
        buildContextFactory: () => DependencyProvider.get<BuildContext>(),
      ),
    );

    DependencyProvider.registerLazySingleton<AnalyticsRouteObserver>(
      () => AnalyticsRouteObserver(
        analyticsTrackerFactory:
            DependencyProvider.get<AnalyticsTrackerFactory>(),
        logger: DependencyProvider.get<Logger>(),
      ),
    );

    DependencyProvider.registerLazySingleton(
      () => LegacyNavigationProviderImpl(
        navigationProvider: DependencyProvider.get<NavigationProviderImpl>(),
        navigatorFactory: () => DependencyProvider.get(),
      ),
    );

    DependencyProvider.registerLazySingleton<NavigationProvider>(
      () => TwoFactorAuthNavigationProviderImpl(
        navigationProvider:
            DependencyProvider.get<LegacyNavigationProviderImpl>(),
        twoFactorAuthentication:
            DependencyProvider.get<ITwoFactorAuthentication>(),
      ),
    );

    void _registerNavigationFeatures(List<String> names) {
      DependencyProvider.registerLazySingleton(
        () => ApplicationNavigatorConfig(
          ApplicationType.sme.id,
          {
            for (var v in names)
              v: DependencyProvider.get<NavigationRouter>(
                instanceName: v,
              ),
          },
        ),
        instanceName: ApplicationType.sme.id,
      );
    }

    final registerDevMenu =
        DevMenuDependenciesModuleResolver.shouldRegisterDevMenu(environment);
    _registerNavigationFeatures([
      if (registerDevMenu) DebugMenuFeatureNavigationConfig.name,
      OnboardingFeatureNavigationConfig.name,
      SubscriptionFeatureNavigationConfig.name,
      OnboardingFeatureNavigationConfig.name,
      CardFeatureNavigationConfig.name,
      StatusViewFeatureNavigationConfig.name,
      InvitationFeatureNavigationConfig.name,
      TwoFactorFeatureNavigationConfig.name,
      AppRatingFeatureNavigationConfig.name,
      PaymentsFeatureNavigationConfig.name,
      AppSettingsNavigationConfig.name,
      AnimatedStatusViewNavigationConfig.name,
      FXFeatureNavigationConfig.name,
      OnboardingEmployeeFeatureNavigationConfig.name,
      StatementFeatureNavigationConfig.name,
      KeyFactStatementFeatureNavigationConfig.name,
      TaxNumberFeatureNavigationConfig.name,
      MultiSignatoryFeatureNavigationConfig.name,
      AddShareholderFeatureNavigationConfig.name,
      ApplicationTrackerFeatureNavigationConfig.name,
      ReAskFeatureNavigationConfig.name,
      DocumentUploadFeatureNavigationConfig.name,
      EligibilityCriteriaFeatureNavigationConfig.name,
      UpdateKycFeatureNavigationConfig.name,
      EddActivityFeatureNavigationConfig.name,
      DocumentViewerFeatureNavigationConfig.name,
      TwoFaFlowNavigationConfig.name,
      MuPaymentRequestsFeatureNavigationConfig.name,
      AnnualKycFeatureNavigationConfig.name,
      DeliveryFeatureNavigationConfig.name,
      WpsFeatureNavigationConfig.name,
      ScfFeatureNavigationConfig.name,
      CustomerAddressFeatureNavigationConfig.name,
      AddressFeatureNavigationConfig.name,
      FilePickerFeatureNavigationConfig.name,
      TurnoverFeatureNavigationConfig.name,
      TransactionsFeatureNavigationConfig.name,
      UTapFeatureNavigationConfig.name,
      AccountFeatureNavigationConfig.name,
      CommonFaqFeatureNavigationConfig.name,
      CreditFeatureNavigationConfig.name,
      ContextFaqFeatureNavigationConfig.name,
      FaceRecognitionFeatureNavigationConfig.name,
      UserFinancialDetailsFeatureNavigationConfig.name,
      SavingSpaceFeatureNavigationConfig.name,
      CriticalNotificationFeatureNavigationConfig.name,
      CountryFeatureNavigationConfig.name,
      EasyCashFeatureNavigationConfig.name,
      InvoicesV2FeatureNavigationConfig.name,
      DashboardFeatureNavigationConfig.name,
      common_document_upload_api.DocumentUploadFeatureNavigationConfig.name,
      CustomerFeedbackFeatureNavigationConfig.name,
      BeDrivenFlowFeatureNavigationConfig.name,
      CommunicationsSettingsFeatureNavConfig.name,
      LoanFeatureNavigationConfig.name,
      BackendDrivenFlowFeatureNavigationConfig.name,
      AccountClosureFeatureNavigationConfig.name,
      CentralisedQuestionnaireServiceFeatureNavigationConfig.name,
      LoginMobileFeatureNavigationConfig.name,
      SettingsFeatureNavigationConfig.name,
      TaxDeclarationFeatureNavigationConfig.name,
    ]);

    DependencyProvider.registerLazySingleton<SmeApplicationNavigatorRouter>(() {
      final smeApplicationNavigatorConfig =
          DependencyProvider.get<ApplicationNavigatorConfig>(
        instanceName: ApplicationType.sme.id,
      );

      return SmeApplicationNavigatorRouter(
        applications: [smeApplicationNavigatorConfig],
      );
    });

    DependencyProvider.registerFactory<ApplicationNavigatorRouter>(
      () => DependencyProvider.get<SmeApplicationNavigatorRouter>(),
    );

    DependencyProvider.registerLazySingleton<NavigationAdapter>(
      () => NavigationAdapter(
        applicationNavigatorRouter:
            DependencyProvider.get<SmeApplicationNavigatorRouter>(),
      ),
    );
  }

  static void _registerInvoicesV2() {
    InvoicesV2DomainDependencyModuleResolver.register();
    InvoiceFeatureDependencyModuleResolver.register();
  }

  static Future<void> _registerDevMenu(AppEnvironment environment) {
    return DevMenuDependenciesModuleResolver.register(environment);
  }

  static void _registerFeatureToggles(AppEnvironment environment) {
    FeatureToggleDomainDependencyModuleResolver.register();
    FeatureTogglesDependenciesModuleResolver.register(environment);
  }

  static void _registerDeviceCheck(AppEnvironment env) {
    DeviceCheckDomainDependencyModuleResolver.register();

    DependencyProvider.registerLazySingleton<NativeSecurityChecker>(
      () => NativeSecurityChecker(),
    );

    DependencyProvider.registerLazySingleton<DeviceSecurityIssueParser>(
      () => DeviceSecurityIssueParserImpl(
        DependencyProvider.get<Logger>(),
      ),
    );
  }

  static void _registerSubscription() {
    SubscriptionDomainDependencyModuleResolver.register();
    SubscriptionFeatureDependencyModuleResolver.register();
  }

  static void _registerLocation() async {
    LocationsDependencyModuleResolver.register();
  }

  static Future<void> _customerAddress() async {
    DependencyProvider.registerLazySingleton<WioCountryCode>(
        () => WioCountryCode());
    CustomerAddressDomainDependencyModuleResolver.register();
    CustomerAddressFeatureDependencyModuleResolver.register();
    CoreUiFeatureDependencyModuleResolver.register();
    AddressFeatureDependencyModuleResolver.register(isRetail: false);
  }

  static void _registerBottomSheets() {
    BottomSheetDomainDependencyModuleResolver.register();
    BottomSheetFeatureDependencyModuleResolver.register();
    BottomSheetAdapterDependencyModuleResolver.register();
  }

  static void _registerSavingSpace() {
    SavingSpaceDomainDependencyModuleResolver.register();
    SavingSpaceFeatureDependencyModuleResolver.register();
  }

  static void _registerPayroll() {
    WpsDomainDependencyModuleResolver.register(useMockRepository: false);
    WpsFeatureDependencyModuleResolver.register();
  }

  static void _registerScf() {
    ScfDomainDependencyModuleResolver.register();
    ScfFeatureDependencyModuleResolver.register();
  }

  static void _registerSettings() {
    SettingsFeatureDependencyModuleResolver.register();
  }

  static void _registerStatement() {
    StatementDomainDependencyModuleResolver.register(useMockRepository: false);
    StatementFeatureDependencyModuleResolver.register();
  }

  static void _registerToastMessages() {
    ToastMessageFeatureDependencyModuleResolver.register();
  }

  static void _registerResponsiveDialog() {
    ResponsiveDialogDependencyModuleResolver.register();
  }

  static void _registerShareWindow() {
    ShareFeatureDependencyModuleResolver.register();
  }

  static Future<void> _registerAccount() async {
    await MobileAccountDomainDependencyModuleResolver.register();
    AccountFeatureDependencyModuleResolver.register();
  }

  static void _registerHomePage() {
    HomeFeatureDependencyModuleResolver.register();
  }

  static void _registerCards() {
    MobileCardDomainDependencyModuleResolver.register();
    CardFeatureDependencyModuleResolver.register();
  }

  static void _registerClipboardManager() {
    ClipboardManagerFeatureDependencyModuleResolver.register();
  }

  static void _registerTwoFactorAuthentication() {
    TwoFactorAuthenticationDomainDependencyModuleResolver.register(
      const IdentityConfig(
        urlConfiguration: IdentityUrlConfiguration.sme,
        supportedFactors: [
          TwoFactorAuthType.biometrics,
          TwoFactorAuthType.passcode,
          TwoFactorAuthType.trustedDevice,
          TwoFactorAuthType.smsOtp,
          TwoFactorAuthType.emailOtp,
          TwoFactorAuthType.efr,
        ],
      ),
    );
    TwoFaFeatureDependencyModuleResolver.register();

    // ignore: deprecated_member_use
    DependencyProvider.registerLazySingleton<BoundDeviceUseCase>(
      () => BoundDeviceUseCaseImpl(
        bindDeviceInteractor: DependencyProvider.get<BindDeviceInteractor>(),
      ),
    );
  }

  static void _registerStatusView() {
    StatusViewFeatureDependencyModuleResolver.register();
    AnimatedStatusViewFeatureDependencyModuleResolver.register();
  }

  static void _registerInvitation() {
    InvitationFeatureDependencyModuleResolver.register();
    InvitationDomainDependencyModuleResolver.register();
  }

  static void _registerLogin() {
    LoginDomainDependencyModuleResolver.register();
    LoginFeatureDependencyModuleResolver.register();
  }

  static void _registerPaymentsV2() {
    PaymentsV2DomainDependencyModuleResolver.register();
    common_payments_impl.PaymentsDomainDependencyModuleResolver.register();
    PaymentFeatureDependencyModuleResolver.register();
  }

  static Future<void> _registerDashboard() async {
    DashboardDomainDependencyModuleResolver.register();
    await DashboardFeatureDependencyModuleResolver.register();
  }

  static Future<void> _registerAppRating() async {
    await AppRatingDomainDependencyModuleResolver.register();
    await AppRatingFeatureDependencyModuleResolver.register();
  }

  static void _registerPendingRequests() {
    MuPaymentsRequestsFeatureDependencyModuleResolver.register();
  }

  static void _registerPermissions() {
    BehaviourDomainDependencyModuleResolver.register();
  }

  static void _registerPhone() {
    PhoneDomainDependencyModuleResolver.register();
  }

  static void _registerPhoneContacts() {
    PhoneContactsDomainDependencyModuleResolver.register();
  }

  static void _registerTransactions() {
    common_txn.TransactionsDomainDependencyModuleResolver.register();

    // Rest API based transactions
    TransactionsDomainDependencyModuleResolver.register();
    SMETransactionsFeatureDependencyModuleResolver.register();
    TransactionFeatureDependencyModuleResolver.register();
  }

  static void _registerFx() {
    FXFeatureDependencyModuleResolver.register();
    FxDomainDependencyModuleResolver.register();
  }

  static void _registerOnboarding() {
    OnboardingDomainDependencyModuleResolver.register();
    OnboardingFeatureDependencyModuleResolver.register();
  }

  static void _registerOnboardingEmployee() {
    OnboardingEmployeeDomainDependencyModuleResolver.register();
    OnboardingEmployeeFeatureDependencyModuleResolver.register();
  }

  static void _registerKeyFactStatement() {
    KeyFactStatementDomainDependencyModuleResolver.register();
    KeyFactStatementFeatureDependencyModuleResolver.register();
  }

  static void _registerEligibilityCriteria() {
    EligibilityCriteriaDomainDependencyModuleResolver.register();
    EligibilityCriteriaFeatureDependencyModuleResolver.register();
  }

  static void _registerTaxNumber() {
    TaxNumberDomainDependencyModuleResolver.register();
    TaxNumberFeatureDependencyModuleResolver.register();
  }

  static void _registerMuPayments() {
    MuPaymentDomainDependencyModuleResolver.register();
  }

  static void _registerRemoteFile() {
    RemoteFileDomainDependencyModuleResolver.register();
  }

  static void _registerMultiSignatory() {
    MultiSignatoryFeatureDependencyModuleResolver.register();
    MultiSignatoryDomainDependencyModuleResolver.register();
  }

  static void _registerAddShareholder() {
    AddShareholderFeatureDependencyModuleResolver.register();
  }

  static void _registerApplicationTracker() {
    ApplicationTrackerFeatureDependencyModuleResolver.register();
    ApplicationTrackerDomainDependencyModuleResolver.register();
  }

  static void _registerReAsk() {
    ReAskFeatureDependencyModuleResolver.register();
    ReAskDomainDependencyModuleResolver.register();
  }

  static void _registerDocumentUpload() {
    DocumentUploadFeatureDependencyModuleResolver.register();
    DocumentUploadDomainDependencyModuleResolver.register();
  }

  static void _registerUpdateKyc() {
    UpdateKycFeatureDependencyModuleResolver.register();
  }

  static void _registerEddActivity() {
    EddActivityFeatureDependencyModuleResolver.register();
    EddActivityDomainDependencyModuleResolver.register();
  }

  static void _registerDocumentViewer() {
    DocumentViewerFeatureDependencyModuleResolver.register();
  }

  static void _registerInfo() {
    InfoDomainDependencyModuleResolver.register();
  }

  static void _registerLicensingAuthority() {
    licensing_authority.OnboardingEmployeeDomainDependencyModuleResolver
        .register();
  }

  static void _registerAnnualKyc() {
    AnnualKycFeatureDependencyModuleResolver.register();
    AnnualKycDomainDependencyModuleResolver.register();
  }

  static void _registerContactSupport() {
    ContactSupportFeatureDependencyModuleResolver.register();
  }

  static void _registerClearable() {
    ClearableDependenciesResolver.register();
  }

  static void _registerTools() {
    FeatureToolsDomainDependencyModuleResolver.register();
  }

  static void _registerDelivery() {
    DeliveryDomainDependencyModuleResolver.register();
    DeliveryFeatureDependencyModuleResolver.register();
  }

  static void _registerCommonFilePicker() {
    FilePickerFeatureDependencyModuleResolver.register();
  }

  static void _registerFAQ() {
    FAQDomainDependencyModuleResolver.register();
    FaqFeatureDependencyModuleResolver.register();
    common_faq.CommonFaqFeatureDependencyModuleResolver.register(
      applicationType: ApplicationType.sme,
    );
    SmeFaqDomainDependencyModuleResolver.register();
  }

  static void _registerCommunicationSettings() {
    CommunicationsSettingsDomainDependencyModuleResolver.register();

    CommunicationsSettingsFeatureDependencyModuleResolver(
      applicationType: CommunicationsApplicationType.sme,
    ).register();
  }

  static void _registerTurnover() {
    TurnoverDomainDependencyModuleResolver.register();
    TurnoverFeatureDependencyModuleResolver.register();
  }

  static void _registerCompany() {
    CompanyDomainDependencyModuleResolver.register(useMockRepository: false);
    CompanyFeatureDependencyModuleResolver.register();
  }

  static void _registerUTap() {
    UTapFeatureDependencyModuleResolver.register();
    UTapDomainDependencyModuleResolver.register();
  }

  static void _registerSmeCredit() {
    CreditDomainDependencyModuleResolver.register();
    CreditFeatureDependencyModuleResolver.register();
  }

  static void _registerContextFaq() {
    DependencyProvider.registerLazySingleton<ContextFaqNavigationHandler>(
      () => SmeContextFaqHandler(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    ContextFaqFeatureDependencyModuleResolver.register();
    ContextFaqDomainDependencyModuleResolver.register(
      ContextFaqDataConfig(
        product: ContextFaqProduct.sme,
        appType: ContextFaqAppType.mobile,
        subscriptionKey: DependencyProvider.get<EnvProvider>().get(
          SmeEnvKey.apimSubscriptionKey,
        ),
      ),
    );
  }

  static void _registerSof() {
    SofDomainDependencyModuleResolver.register();
  }

  static void _registerFaceRecognition() {
    FaceRecognitionDomainDependencyModuleResolver.register();
    FaceRecognitionFeatureDependencyModuleResolver.register();
  }

  static void _userFinancialDetails() {
    UserFinancialDetailsAppDomainDependencyModuleResolver.register();
    UserFinancialDetailsApUiDependencyModuleResolver.register();
    UserFinancialDetailsDependencyModuleResolver.register();
    UserFinancialDetailsFeatureDependencyModuleResolver.register();
  }

  static void _registerEasyCash() {
    EasyCashFeatureDependencyModuleResolver.register();
  }

  static void _registerOnfido() {
    OnfidoFeatureDependencyModuleResolver.register();
  }

  static void _registerCommonDocumentUpload() {
    common_document_upload_impl.DocumentUploadDomainDependencyModuleResolver
        .register();
    common_document_upload_ui.DocumentUploadFeatureDependencyModuleResolver
        .register();
  }

  static void _registerCustomerFeedback() {
    CustomerFeedbackDomainDependencyModuleResolver.register();
    CustomerFeedbackFeatureDependencyModuleResolver.register();
  }

  static void _registerCommonBackendDrivenFlow() {
    BackendDrivenFlowFeatureDependencyModuleResolver.register();
    BackendDrivenFlowDomainDependencyModuleResolver.register();
  }

  static void _registerCommonWebView() {
    WebViewFeatureDependencyModuleResolver.register(
      toggleKey: SmeMobileFeatureToggles.smeWebViewDomainKey,
    );
  }

  static void _registerBackendDrivenFlow() {
    BeDrivenFlowFeatureDependencyModuleResolver.register();
  }

  static void _registerLoan() {
    LoanFeatureDependencyModuleResolver.register();
    LoanDomainDependencyModuleResolver.register();
  }

  static void _registerAccountClosure() {
    AccountClosureDomainDependencyModuleResolver.register();

    AccountClosureFeatureDependencyModuleResolver.register(
      applicationType: ApplicationType.sme,
    );
  }

  static void _registerCounterParty() {
    CounterPartyDomainDependencyModuleResolver.register();
  }

  static void _registerTinValidation() {
    TinValidationDependencyModuleResolver.register();
  }

  static void _registerCentralisedQuestionnaireService() {
    CentralisedQuestionnaireServiceDomainDependencyModuleResolver.register();
    CentralisedQuestionnaireServiceFeatureDependencyModuleResolver.register();
    CQSFeatureDependencyModuleResolver.register();
  }

  static void _registerSplashScreen() {
    SmeMobileSplashScreenDependencyResolver.register();
  }

  static void _registerCommonTaxDeclaration() {
    TaxDeclarationDomainDependencyModuleResolver.register();
    TaxDeclarationFeatureDependencyModuleResolver.register();
  }
}
