import 'package:app/initialiser.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_driver/driver_extension.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_splash_ui/sme_mobile_splash_ui.dart';

void main() async {
  enableFlutterDriverExtension(enableTextEntryEmulation: false);
  await runTheApp(
    'adq app qa',
    const SMEMobileSplashScreen(),
    AppEnvironment.pre,
    GlobalKey<NavigatorState>(),
  );
}
