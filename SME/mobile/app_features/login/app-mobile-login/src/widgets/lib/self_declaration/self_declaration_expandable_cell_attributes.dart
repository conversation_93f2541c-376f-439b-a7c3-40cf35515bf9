import 'package:login_widgets/self_declaration/tax_payer_ui_model.dart';

class SelfDeclarationExpandableCellAttributes {
  final TaxPayerUiModel taxPayer;
  final int taxPayerNumber;
  List<TaxPayerUiModel> variants;
  final Function() delete;
  final String fastTrackIconPath;
  final String arrowDownIconPath;
  final String arrowUpIconPath;
  final String deleteIconPath;
  final String themeName;
  final String taxPayerTypeHint;

  SelfDeclarationExpandableCellAttributes({
    required this.taxPayer,
    required this.taxPayerTypeHint,
    required this.delete,
    required this.taxPayerNumber,
    required this.themeName,
    required this.variants,
    this.fastTrackIconPath = 'widget_library:assets/images/fast_tracked.svg',
    this.arrowDownIconPath = 'widget_library:assets/images/ic_arrow_down.svg',
    this.arrowUpIconPath = 'widget_library:assets/images/ic_arrow_up.svg',
    this.deleteIconPath = 'app_mobile_login:assets/images/ic_delete.svg',
  });
}
