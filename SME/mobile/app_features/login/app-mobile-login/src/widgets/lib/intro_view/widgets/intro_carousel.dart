import 'package:flutter/material.dart';
import 'package:login_widgets/intro_view/model/intro_carousel_item_model.dart';
import 'package:login_widgets/intro_view/widgets/intro_carousel_item.dart';

class IntroCarousel extends StatelessWidget {
  final List<IntroCarouselItemModel> items;
  final Function(int) _onSelect;

  const IntroCarousel({
    Key? key,
    required this.items,
    required double horizontalPadding,
    required Function(int) onSelect,
  })  : _horizontalPadding = horizontalPadding,
        _onSelect = onSelect,
        super(key: key);

  final double _horizontalPadding;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200.0,
      child: ListView.separated(
        itemCount: items.length,
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: _horizontalPadding),
        separatorBuilder: (BuildContext context, int index) {
          return SizedBox(width: 13.0);
        },
        itemBuilder: (BuildContext context, int index) {
          return GestureDetector(
            onTap: () => _onSelect(index),
            child: IntroCarouselItem(model: items[index]),
          );
        },
      ),
    );
  }
}
