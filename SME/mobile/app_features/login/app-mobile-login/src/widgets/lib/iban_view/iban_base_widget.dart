import 'package:core/utils/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:widget_library/page_header/page_header_text_under_icon/page_header_text_under_icon_widget.dart';
import 'package:widget_library/page_header/page_header_text_under_icon/page_header_text_under_icon_widget_attributes.dart';
import 'package:widget_library/page_header/spannable_text_widget.dart';
import 'package:widget_library/page_header/text_ui_data_model.dart';
import 'package:widget_library/static_text/PSText.dart';
import 'package:widget_library/static_text/ps_text_rich.dart';
import 'package:widget_library/success_screen/ps_success_base_widget_attribute.dart';
import 'package:widget_library/theme/ps_theme.dart';
import 'Iban_base_widget_attribute.dart';

class _Constants {
  static const double marginHorizontal = 32.0;
  static const double marginVertical = 10.0;
  static const double thickness = 1;
  static const double imageHeight = 160;
}

class IbanBaseWidget extends StatelessWidget {
  final IbanBaseWidgetAttribute attribute;

  const IbanBaseWidget({
    Key? key,
    required this.attribute,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Prevent back action. Use the backAction callback to override
        // the default back behaviour which is to swipe to close.ß
        // If neither callback is set, nothing happens.
        if (attribute.onbackAction == null) {
          attribute.onSwipedToConfirm?.call();
        } else {
          attribute.onbackAction!.call();
        }
        return false;
      },
      child: _getWidgetBody(),
    );
  }

  Widget _getWidgetBody() {
    if (attribute.swipeWidget != null &&
        (attribute.bottomButtons == null ||
            attribute.hideSwipeToConfirm == false)) {
      return attribute.swipeWidget!(
        child: _MainBody(attribute: attribute),
        text: attribute.swipeToCloseText,
      );
    }
    return _MainBody(attribute: attribute);
  }
}

class _MainBody extends StatelessWidget {
  final IbanBaseWidgetAttribute attribute;

  const _MainBody({
    Key? key,
    required this.attribute,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        if (attribute.bottomImage != null)
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: attribute.bottomImage!,
          ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (attribute.topHeading != null)
              _TopHeading(
                title: attribute.topHeading!,
                svg: attribute.svgPath!,
              ),
            if (attribute.description != null)
              _Description(description: attribute.description!),
            if (attribute.itemValueAttributes != null)
              Expanded(
                flex: 2,
                child: _BottomPanel(
                  itemValueAttributes: attribute.itemValueAttributes!,
                  scrollPhysics: attribute.itemValueListScrollPhysics,
                  itemValueTextAlign: attribute.itemValueTextAlign,
                  itemValueDescription: attribute.itemValueDescription,
                  onTextLongPressed: attribute.onTextLongPressed,
                ),
              ),
            const Spacer(),
            if (attribute.bottomButtons != null) _getButtons(),
          ],
        ),
      ],
    );
  }

  Widget _getButtons() {
    var buttonsList = <Widget>[];
    final listOfButtonsModel = attribute.bottomButtons!;

    for (int i = 0; i < 2 && i < listOfButtonsModel.length; i++) {
      if (i == 0 && attribute.primaryCTA != null) {
        buttonsList.add(
          attribute.primaryCTA!(
            text: listOfButtonsModel[i].text,
            onPressed: listOfButtonsModel[i].onPressed,
          ),
        );
      }
      if (i == 1 && attribute.secondaryCTA != null) {
        buttonsList.add(
          Padding(
            padding: const EdgeInsets.only(top: 24),
            child: attribute.secondaryCTA!(
              text: listOfButtonsModel[i].text,
              onPressed: listOfButtonsModel[i].onPressed,
            ),
          ),
        );
      }
    }
    return Container(
      margin: const EdgeInsetsDirectional.only(
        top: 12,
        bottom: 46,
        start: 32,
        end: 32,
      ),
      child: Column(children: buttonsList),
    );
  }
}

class _TopHeading extends StatelessWidget {
  final String title;
  final String svg;

  const _TopHeading({
    Key? key,
    required this.title,
    required this.svg,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(_Constants.marginHorizontal),
        child: PSTextRich(
          PSTextRichDataModel(
            textSpanDataModels: [
              PSTextSpanDataModel(
                PSTextDataModel(
                  text: TextUIDataModel(
                    title,
                    styleVariant: PSTextStyleVariant.headline1,
                    textAlign: TextAlign.left,
                  ),
                ),
              ),
              PSTextSpanDataModel(
                PSTextDataModel.icon(
                  iconPath: svg,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _Description extends StatelessWidget {
  final String description;

  const _Description({
    Key? key,
    required this.description,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: _Constants.marginHorizontal),
      child: PSText(
        // padding:EdgeInsets.zero,
        text: TextUIDataModel(
          description,
          styleVariant: PSTextStyleVariant.bodyText2,
          textAlign: TextAlign.left,
        ),
      ),
    );
  }
}

/// Contains the item/value pairs rendered in a scrollable container sandwiched by dividers
class _BottomPanel extends StatelessWidget {
  final List<ItemValueAttribute?> itemValueAttributes;
  final ScrollPhysics? scrollPhysics;
  final TextAlign? itemValueTextAlign;
  final String? itemValueDescription;
  final Function(String)? onTextLongPressed;

  const _BottomPanel({
    Key? key,
    required this.itemValueAttributes,
    this.scrollPhysics,
    this.itemValueTextAlign,
    this.itemValueDescription,
    this.onTextLongPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: _Constants.marginHorizontal),
      child: Column(
        children: [
          SizedBox(height: _Constants.marginVertical),
          Flexible(
            child: SingleChildScrollView(
              physics: scrollPhysics ?? NeverScrollableScrollPhysics(),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ...List.generate(
                    itemValueAttributes.length,
                    (index) {
                      final textAlign = itemValueAttributes[index]!.textAlign;
                      final item = itemValueAttributes[index]!.item;
                      final value = itemValueAttributes[index]!.value;
                      return Padding(
                        padding: const EdgeInsets.only(
                          top: _Constants.marginVertical,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            PSText(
                              text: item,
                            ),
                            SizedBox(height: _Constants.marginVertical),
                            GestureDetector(
                              onLongPress: () => onTextLongPressed!(value.text),
                              child: PSText(
                                text: value,
                              ),
                            ),
                            SizedBox(height: _Constants.marginVertical),
                            if (itemValueAttributes.isNotEmpty)
                              Divider(
                                thickness: _Constants.thickness,
                                color: ADQTheme()
                                    .themeData
                                    .colorPalette!
                                    .primary
                                    .toColor()
                                    .withValues(alpha: 0.1),
                              ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
