import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:widget_library/backgrounds/ps_decorated_background.dart';

class SignUpChevronHeaderBackground extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * .3,
      width: MediaQuery.of(context).size.width,
      child: PSDecoratedBackground(
        backgroundColor: Colors.white,
        decorationIconUri: 'app_mobile_login:assets/images/ic_chevron_down.svg',
        gradientColors: [
          Colors.white.withValues(alpha: 0.01),
          ...List.filled(1, Colors.white.withValues(alpha: 1.0)),
        ],
        stretch: true,
      ),
    );
  }
}
