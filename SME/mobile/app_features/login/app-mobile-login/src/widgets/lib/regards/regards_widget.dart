import 'package:flutter/material.dart';
import 'package:login_widgets/regards/regards_attributes.dart';
import 'package:widget_library/page_header/text_ui_data_model.dart';
import 'package:widget_library/scaffold/ps_scaffold.dart';
import 'package:widget_library/static_text/PSText.dart';
import 'package:widget_library/utils/icon_utils.dart';

class RegardsWidget extends StatelessWidget {
  final RegardsWidgetAttributes attributes;

  const RegardsWidget({Key? key, required this.attributes}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PSScaffold(
      themeName: 'regards',
      body: _RegardsPanel(
        key: Key('_RegardsPanel_Panel'),
        regardsAttribute: attributes,
      ),
    );
  }
}

class _RegardsPanel extends StatelessWidget {
  final RegardsWidgetAttributes regardsAttribute;

  const _RegardsPanel({
    Key? key,
    required this.regardsAttribute,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: regardsAttribute.bg,
      child: Padding(
        padding: const EdgeInsets.only(left: 32),
        child: OverflowBox(
          alignment: Alignment.centerLeft,
          minWidth: 0.0,
          maxWidth: double.infinity,
          minHeight: 0.0,
          maxHeight: double.infinity,
          child: Column(
            key: Key('_Regards_Messages'),
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: regardsAttribute.messages.map((e) {
              return _RegardsRow(
                leftIcon: e.leftIcon,
                message: e.messages,
                rightIcon: e.rightIcon,
                leftSpace: e.leftIcon != null,
                rightSpace: e.rightIcon != null,
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}

class _RegardsRow extends StatelessWidget {
  final TextUIDataModel? message;
  final String? leftIcon;
  final String? rightIcon;
  final bool leftSpace;
  final bool rightSpace;

  const _RegardsRow(
      {Key? key,
      required this.message,
      this.rightIcon,
      this.leftIcon,
      this.leftSpace = false,
      this.rightSpace = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (leftIcon != null) _RegardsIcon(icon: leftIcon!),
        if (message != null)
          _RegardsMessage(
              message: message!, leftSpace: leftSpace, rightSpace: rightSpace),
        if (rightIcon != null) _RegardsIcon(icon: rightIcon!),
        // _RegardsClipMessage(message: message),
      ],
    );
  }
}

class _RegardsMessage extends StatelessWidget {
  final TextUIDataModel message;
  final bool leftSpace;
  final bool rightSpace;

  const _RegardsMessage(
      {Key? key,
      required this.message,
      this.leftSpace = false,
      this.rightSpace = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints.tightFor(height: 50),
      child: Padding(
        padding: EdgeInsets.only(
            left: (leftSpace ? 8 : 0), right: (rightSpace ? 8 : 0)),
        child: PSText(text: message),
      ),
    );
  }
}

class _RegardsIcon extends StatelessWidget {
  final String icon;

  const _RegardsIcon({Key? key, required this.icon}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 1, right: 1),
      child: getSvg(icon,
          color: Theme.of(context).primaryColorLight, width: 44, height: 44),
    );
  }
}
