import 'package:app_mobile_login/features/business_flow/data_provider/business_flow_data_provider.dart';
import 'package:app_mobile_login/features/business_flow/feature_toggle/business_flow_feature_toggles.dart';
import 'package:app_mobile_login/features/business_flow/services/user_document_submit_service.dart';
import 'package:app_mobile_login/features/business_flow/static_data/back_end_enums_holder.dart';
import 'package:app_mobile_login/features/business_flow/view/business_flow_view.dart';
import 'package:app_mobile_login/features/business_flow/view_model/business_flow_view_model.dart';
import 'package:app_mobile_login/features/configurable_mobile_journey/data_model/configurable_mobile_journey_response.dart';
import 'package:app_mobile_login/features/configurable_mobile_journey/service/cmj_data_source.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:core/logging/stub_logger.dart';
import 'package:core/translation/i_app_localization_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:task_manager/task_manager.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';

import 'business_flow_view_model_test.mocks.dart';

@GenerateMocks([
  IAppLocalizationService,
  BackEndEnumsDataHolder,
  TaskManager,
  IUserDocumentSubmitService,
  CMJDataSource,
  BusinessFlowDataProvider,
  FeatureToggleProvider,
  NavigationProvider,
])
void main() {
  final localization = MockIAppLocalizationService();
  final enumsHolder = MockBackEndEnumsDataHolder();
  final taskManager = MockTaskManager();
  final documentSubmitService = MockIUserDocumentSubmitService();
  final cmjDataSource = MockCMJDataSource();
  final logger = StubLogger();
  final mockDataProvider = MockBusinessFlowDataProvider();
  final mockFeatureToggleProvider = MockFeatureToggleProvider();
  final mockNavigationProvider = MockNavigationProvider();

  late BusinessFlowViewModel cut;
  final businessName = 'Mock Biz LTD';

  setUp(() {
    when(localization.getValue(any)).thenReturn('mocked_translation');
    cut = BusinessFlowViewModel(
        localization,
        enumsHolder,
        taskManager,
        documentSubmitService,
        cmjDataSource,
        logger,
        mockDataProvider,
        mockFeatureToggleProvider,
        mockNavigationProvider);
    final businessFlowMode = BusinessFlowMode.business;
    final onKYBCompleteCallBack =
        ({required bool goNextStep, required bool isFastTracked}) {};
    final mockBusinessFlowAttributes = BusinessFlowAttributes(
      businessName: businessName,
      businessFlowMode: businessFlowMode,
      onKYBCompleteCallBack: onKYBCompleteCallBack,
    );

    provideDummy<bool>(true);
    when(mockFeatureToggleProvider
            .get<bool>(BusinessFlowFeatureToggles.enableAdditionalQuestions))
        .thenReturn(true);

    when(mockFeatureToggleProvider
            .get<bool>(BusinessFlowFeatureToggles.businessActivityQuestion))
        .thenReturn(false);

    when(cmjDataSource.getJourneyData()).thenAnswer(
      (_) => Future.value(
        ConfigurableMobileJourneyData.test(
          stages: [
            JourneyStage(
              category: StageCategory.BUSINESS,
              optional: false,
              pages: [
                StagePage(
                  name: 'companyInformation',
                  optional: false,
                ),
                StagePage(
                  name: 'legalType',
                  optional: false,
                ),
                StagePage(
                  name: 'annualTurnover',
                  optional: false,
                ),
                StagePage(
                  name: 'website',
                  optional: true,
                ),
                StagePage(
                  name: 'taxRegistrationNumber',
                  optional: true,
                ),
                StagePage(
                  name: 'taxRegistrationDate',
                  optional: true,
                ),
              ],
              stageIdentifier: 'kybInformation',
              stageName: 'KYB Information',
              stageStatus: StageStatus.INCOMPLETE,
              stageType: StageType.PAGES,
            ),
          ],
        ),
      ),
    );
    cut.initialize(mockBusinessFlowAttributes);
  });

  group('getBusinessName function', () {
    test('should return correct name', () async {
      final actualResult = cut.getBusinessName();
      expect(businessName, actualResult);
    });
  });

  group('Static data getter', () {
    test('companyInformation should return map with all keys', () async {
      final actualResult = cut.companyInformation;
      expect(9, actualResult.length);
      expect(true, actualResult.containsKey('identifier'));
      expect(true, actualResult.containsKey('icon'));
      expect(true, actualResult.containsKey('title'));
      expect(true, actualResult.containsKey('inputType'));
      expect(true, actualResult.containsKey('openPopUpForResult'));
      expect(true, actualResult.containsKey('onReset'));
      expect(true, actualResult.containsKey('otherData'));
    });

    test('legalType should return map with all keys', () async {
      final actualResult = cut.legalType;
      expect(9, actualResult.length);
      expect(true, actualResult.containsKey('identifier'));
      expect(true, actualResult.containsKey('icon'));
      expect(true, actualResult.containsKey('title'));
      expect(true, actualResult.containsKey('hint'));
      expect(true, actualResult.containsKey('appBarAttributes'));
      expect(true, actualResult.containsKey('inputType'));
      expect(true, actualResult.containsKey('openPopUpForResult'));
      expect(true, actualResult.containsKey('onReset'));
      expect(true, actualResult.containsKey('otherData'));
    });

    test('website should return map with all keys', () async {
      final actualResult = cut.website;
      expect(8, actualResult.length);
      expect(true, actualResult.containsKey('identifier'));
      expect(true, actualResult.containsKey('icon'));
      expect(true, actualResult.containsKey('title'));
      expect(true, actualResult.containsKey('hint'));
      expect(true, actualResult.containsKey('appBarAttributes'));
      expect(true, actualResult.containsKey('validationRules'));
      expect(true, actualResult.containsKey('inputType'));
      expect(true, actualResult.containsKey('otherData'));
    });

    test('annual_turnover should return map with all keys', () async {
      final actualResult = cut.annualTurnover;
      expect(7, actualResult.length);
      expect(true, actualResult.containsKey('identifier'));
      expect(true, actualResult.containsKey('icon'));
      expect(true, actualResult.containsKey('title'));
      expect(true, actualResult.containsKey('appBarAttributes'));
      expect(true, actualResult.containsKey('inputType'));
      expect(true, actualResult.containsKey('flowType'));
      expect(true, actualResult.containsKey('otherData'));
      expect(false, actualResult.containsKey('optionalValidationRules'));
    });
  });

  group('translate function', () {
    test('should return correctly appended translated string', () async {
      final actualResult = cut.translate('test');
      expect('mocked_translation', actualResult);
    });
  });

  group('formatToData function', () {
    test('should return correct date if valid string is passed', () async {
      final actualResult = cut.formatToData('13-03-1987');
      final validResult = DateTime(1987, 3, 13);
      expect(validResult, actualResult);
    });

    test('should return null if invalid string is passed', () async {
      final actualResult = cut.formatToData('');
      expect(null, actualResult);
    });
  });
}
