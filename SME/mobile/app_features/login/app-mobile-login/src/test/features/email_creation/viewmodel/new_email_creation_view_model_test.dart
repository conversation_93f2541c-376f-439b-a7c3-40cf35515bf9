import 'package:app_mobile_login/features/email_creation/state/new_email_creation_state.dart';
import 'package:app_mobile_login/features/email_creation/viewmodel/new_email_creation_viewmodel.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  var sut = NewEmailCreationViewModel();

  group('isValidEmail function testing', () {
    test('isValidEmail function testing for correct email', () async {
      var email = '<EMAIL>';
      expect(sut.isValidEmail(email), true);
    });

    test('isValidEmail function testing for wrong email', () async {
      var email = 'luffy.com';
      expect(sut.isValidEmail(email), false);
    });

    test('isValidEmail function testing for empty email', () async {
      var email = '';
      expect(sut.isValidEmail(email), false);
    });
  });

  group('emailSatisfiesAllRules function testing', () {
    test('emailSatisfiesAllRules function empty emailID scenario ', () async {
      var emailText = '';
      expect(sut.emailSatisfiesAllRules(emailText), false);
    });

    test('emailSatisfiesAllRules function success scenario ', () async {
      var emailText = '<EMAIL>';
      expect(sut.emailSatisfiesAllRules(emailText), true);
    });

    test('emailSatisfiesAllRules function wrong emailID scenario ', () async {
      var emailText = 'monkey.com';
      expect(sut.emailSatisfiesAllRules(emailText), false);
    });
  });

  group('getErrorText Function testing', () {
    test('getErrorText type is ALREADY_REGISTERED', () async {
      var type = EmailErrorType.ALREADY_REGISTERED;
      expect(sut.getErrorText(type), 'login:email_error_already_registered');
    });

    test('getErrorText type is INCORRECT_FORMAT', () async {
      var type = EmailErrorType.INCORRECT_FORMAT;
      expect(sut.getErrorText(type), 'login:email_error_incorrect_format');
    });
  });

  test('getEmailLowerCase function test', () async {
    var actual = sut.getEmailLowerCase('<EMAIL>');
    expect(actual, '<EMAIL>');
  });
}
