import 'package:app_mobile_login/features/new_user_registration/company_name_setup/view_model/company_name_view_model.dart';
import 'package:core/translation/i_app_localization_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import './company_name_view_model_test.mocks.dart';

class _Constants {
  static const companyNameHeader = 'login:company_name_header';
}

@GenerateMocks([IAppLocalizationService])
void main() {
  final _mockLocalization = MockIAppLocalizationService();
  final sut = CompanyNameViewModel(_mockLocalization);

  when(_mockLocalization.getValue(_Constants.companyNameHeader))
      .thenReturn('I\'m #,\nthe & of');
  test('company name view model ...', () async {
    final actual = sut.getCompanyHeader('Hello', 'There');
    expect(actual, 'I\'m Hello,\nthe There of');
  });

  test('getTrimmedCompanyName test which returns trimmed string passed to it',
      () async {
    final actual = sut.getTrimedCompanyName(' Hello ');
    expect(actual, 'Hello');
  });

  test(
      'checkIfCompanyNameHasEmoji function test, which returns if company name has emoji',
      () async {
    final actual = sut.checkIfCompanyNameHasEmoji('Hello');
    expect(actual, false);
  });

  test(
      'getHighlightedTextArray function, which returns the strings passed to it in form of list',
      () {
    final actual = sut.getHighlightedArray('Hello', 'There');
    expect(actual.first, 'Hello');
    expect(actual.last, 'There');
  });
}
