<svg width="34" height="24" viewBox="0 0 34 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <filter id="filter0_d" x="9.71484" y="2.39999" width="14.5714" height="13.8519"
            filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feColorMatrix in="SourceAlpha" type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
            <feOffset />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0" />
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow" />
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape" />
        </filter>
        <filter id="filter1_d" x="0" y="9.60001" width="34" height="6.4"
            filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feColorMatrix in="SourceAlpha" type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
            <feOffset />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0" />
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow" />
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape" />
        </filter>
    </defs>
    <rect width="34" height="24" rx="2" fill="white" />
    <mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="34"
        height="24">
        <rect width="34" height="24" rx="2" fill="white" />
    </mask>
    <g mask="url(#mask0)">
        <rect width="34" height="24" fill="#E2243B" />
        <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H34L17 24L0 0Z" fill="#262626" />
        <mask id="mask1" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="34"
            height="24">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H34L17 24L0 0Z" fill="white" />
        </mask>
        <g mask="url(#mask1)">
            <g filter="url(#filter0_d)">
                <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M17.0006 13.2L14.2124 16.2519L14.4247 12.1456L10.2694 12.3553L13.3577 9.59999L9.71484 6.39999L14.4247 7.19999L13.7625 2.39999L17.0006 6.39999L20.2387 2.39999L19.5764 7.19999L24.2863 6.39999L20.6434 9.59999L23.7317 12.3553L19.5764 12.1456L19.7887 16.2519L17.0006 13.2Z"
                    fill="#FFCF3C" />
            </g>
            <g filter="url(#filter1_d)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M0 16H34V9.60001H0V16Z"
                    fill="#1984D8" />
            </g>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M0 24H34V16H0V24Z" fill="white" />
        </g>
    </g>

</svg>
