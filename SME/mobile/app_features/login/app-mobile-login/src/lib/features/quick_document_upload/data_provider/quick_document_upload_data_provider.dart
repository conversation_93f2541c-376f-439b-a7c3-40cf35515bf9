import 'package:task_manager/base_data_provider/base_data_provider.dart';
import 'package:task_manager/task_manager.dart';

abstract class IQuickDocumentUploadDataProvider {
  Future<void> logAnalyticsEvent(
    String eventName, [
    Map<String, dynamic>? properties,
  ]);
}

class QuickDocumentUploadDataProvider extends BaseDataProvider
    implements IQuickDocumentUploadDataProvider {
  QuickDocumentUploadDataProvider(TaskManager taskManager) : super(taskManager);
}
