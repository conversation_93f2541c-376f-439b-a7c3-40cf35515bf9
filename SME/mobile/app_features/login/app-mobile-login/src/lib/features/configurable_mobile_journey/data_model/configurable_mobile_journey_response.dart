import 'package:json_annotation/json_annotation.dart';
import 'package:network_manager/model/requests/graph_ql/base_data_model.dart';

part 'configurable_mobile_journey_response.g.dart';

@JsonSerializable(explicitToJson: true)
class ConfigurableMobileJourneyResponse extends BaseDataModel {
  @JsonKey(name: 'stagesInformation')
  late ConfigurableMobileJourneyData stagesInformation;

  ConfigurableMobileJourneyResponse();

  @override
  void fromJsonToModel(Map<String, dynamic> value) {
    final data = ConfigurableMobileJourneyResponse.fromJson(value);
    stagesInformation = data.stagesInformation;
  }

  factory ConfigurableMobileJourneyResponse.fromJson(
          Map<String, dynamic> json) =>
      _$ConfigurableMobileJourneyResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$ConfigurableMobileJourneyResponseToJson(this);
}

@JsonSerializable(explicitToJson: true)
class ConfigurableMobileJourneyData {
  @JsonKey(name: 'Stages')
  late List<JourneyStage> stages;

  @JsonKey(name: 'Category')
  late Category category;

  @JsonKey(name: 'Origin')
  late String? origin;

  @JsonKey(name: 'Invited')
  late bool? isInvited;

  ConfigurableMobileJourneyData();

  factory ConfigurableMobileJourneyData.test({
    required List<JourneyStage> stages,
  }) {
    final response = ConfigurableMobileJourneyData();
    response.stages = stages;
    return response;
  }

  factory ConfigurableMobileJourneyData.fromJson(Map<String, dynamic> json) =>
      _$ConfigurableMobileJourneyDataFromJson(json);

  Map<String, dynamic> toJson() => _$ConfigurableMobileJourneyDataToJson(this);
}

@JsonSerializable(explicitToJson: true)
class JourneyStage {
  @JsonKey(name: 'StageName')
  final String stageName;

  @JsonKey(name: 'StageType')
  final StageType stageType;

  @JsonKey(name: 'StageStatus')
  final StageStatus stageStatus;

  @JsonKey(name: 'StageIdentifier')
  final String stageIdentifier;

  @JsonKey(name: 'Optional')
  final bool optional;

  @JsonKey(name: 'Category')
  final StageCategory category;

  @JsonKey(name: 'Pages')
  final List<StagePage> pages;

  @JsonKey(name: 'Fasttrack')
  final bool fastTrack;

  @JsonKey(name: 'BusinessDetails')
  final List<BusinessDetails>? businessDetails;

  @JsonKey(name: 'IndividualPartnerDocuments')
  final List<StagePage>? individualPartnerDocumentsFastTrack;

  @JsonKey(name: 'CorporatePartnerDocuments')
  final List<StagePage>? corporatePartnerDocumentsFastTrack;

  JourneyStage({
    required this.stageName,
    required this.stageType,
    required this.stageStatus,
    required this.stageIdentifier,
    required this.optional,
    required this.category,
    required this.pages,
    this.fastTrack = false,
    this.businessDetails,
    this.individualPartnerDocumentsFastTrack,
    this.corporatePartnerDocumentsFastTrack,
  });

  factory JourneyStage.fromJson(Map<String, dynamic> json) =>
      _$JourneyStageFromJson(json);

  Map<String, dynamic> toJson() => _$JourneyStageToJson(this);

  @override
  String toString() {
    return 'JourneyStage{stageName: $stageName, stageType: $stageType, stageStatus: $stageStatus, stageIdentifier: $stageIdentifier, optional: $optional, category: $category, fastTrack: $fastTrack}';
  }
}

@JsonSerializable(explicitToJson: true)
class StagePage {
  @JsonKey(name: 'Name')
  final String name;

  @JsonKey(name: 'Optional')
  final bool optional;

  StagePage({
    required this.name,
    required this.optional,
  });

  factory StagePage.fromJson(Map<String, dynamic> json) =>
      _$StagePageFromJson(json);

  Map<String, dynamic> toJson() => _$StagePageToJson(this);
}

enum StageStatus {
  @JsonValue('COMPLETED')
  COMPLETED,
  @JsonValue('INCOMPLETE')
  INCOMPLETE,
  @JsonValue('BE_IDENTIFIER_MISSING')
  UNKNOWN,
}

enum StageCategory {
  @JsonValue('BUSINESS')
  BUSINESS,
  @JsonValue('INDIVIDUAL')
  INDIVIDUAL,
  @JsonValue('PARTNER')
  PARTNER,
}

enum Category {
  @JsonValue('EMPLOYEE')
  employee,
  @JsonValue('MANAGER')
  manager,
  @JsonValue('OWNER')
  owner,
  @JsonValue('ARTEMIS_UNKNOWN')
  artemisUnknown,
}

enum StageType {
  @JsonValue('PAGES')
  PAGES,
  @JsonValue('DOCUMENTS')
  DOCUMENTS,
}

@JsonSerializable(explicitToJson: true)
class BusinessDetails {
  @JsonKey(name: 'Name')
  final String name;

  @JsonKey(name: 'Value')
  final String? value;

  BusinessDetails({
    required this.name,
    required this.value,
  });

  factory BusinessDetails.fromJson(Map<String, dynamic> json) =>
      _$BusinessDetailsFromJson(json);

  Map<String, dynamic> toJson() => _$BusinessDetailsToJson(this);
}
