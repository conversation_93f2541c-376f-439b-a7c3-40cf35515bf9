import 'package:core/utils/extensions/string_extensions.dart';

class ConfigurableMobileJourneyQuery {
  static String getQuery(Map<String, dynamic> requestData) {
    String? legalType;
    String? licensingAuthority;
    legalType = requestData['legalType'] as String? ?? '';
    licensingAuthority = requestData['licensingAuthority'] as String? ?? '';
    Map<String, dynamic> params = {};
    if (legalType.isNotBlank()) {
      params['legalType'] = legalType;
    }
    if (licensingAuthority.isNotBlank()) {
      params['licensingAuthority'] = licensingAuthority;
    }

    var _paramString = '';
    for (final mapEntry in params.entries) {
      final key = mapEntry.key;
      final value = mapEntry.value;
      if (_paramString.isNotEmpty) {
        _paramString += ', $key: "$value"';
      } else {
        _paramString += '$key: "$value"';
      }
    }

    return '''query {
  stagesInformation($_paramString) {
    Origin
    Category
    Invited
    Stages {
      StageName
      Category
      StageType
      StageIdentifier
      StageStatus
      Optional
      Fasttrack
      Pages {
        Name
        Optional
      }
      BusinessDetails {
        Name
        Value
      } 
      IndividualPartnerDocuments {
        Name
        Optional
      } 
      CorporatePartnerDocuments {
        Name
        Optional
      }   
    }
    }
   }''';
  }
}
