import 'dart:async';

import 'package:app_mobile_login/common/analytics_events.dart';
import 'package:app_mobile_login/features/referral_code/l10n/referral_code_localization.g.dart';
import 'package:app_mobile_login/features/referral_code/referral_code_data_provider.dart';
import 'package:app_mobile_login/features/referral_code/referral_code_state.dart';
import 'package:core/core.dart';
import 'package:core/navigation/break_point_manager.dart';
import 'package:core/navigation/navigation_break_point.dart';
import 'package:core/navigation/navigation_manager.dart';
import 'package:logging_api/logging.dart';
import 'package:network_manager/model/response/graphql/network_graphql_response.dart';
import 'package:task_manager/base_classes/base_view_model.dart';
import 'package:task_manager/task_manager.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart' as ui_kit;
import 'package:widget_library/helpers/error/helper/error_helper.dart';

import 'services/referral_code_response.dart';

int maxReferralCodeLength = 12;

class ReferralCodeCoordinator extends BaseViewModel<ReferralCodeState> {
  final ReferralCodeLocalizations _localization;
  final ReferralCodeDataProvider _dataProvider;
  final BreakPointManager _breakPointManager;
  final Logger _logger;

  ReferralCodeCoordinator(
    TaskManager taskManager,
    this._localization,
    this._dataProvider,
    this._breakPointManager,
    this._logger,
  ) : super(
          taskManager,
          ReferralCodeState(enableNext: false),
        );

  void initialize(ui_kit.InputFieldModel inputFieldModel) {
    state = state.copyWith(inputFieldModel: inputFieldModel);
    unawaited(
        _dataProvider.logAnalyticsEvent(AnalyticsEvents.screenViewOnboarding, {
      AnalyticsEvents.screenNameParam: AnalyticsEvents.referralCodeScreen,
    }));
  }

  void onCodeChange(String changedCode) {
    state = state.copyWith(
        code: changedCode,
        inputFieldModel: state.inputFieldModel?.copyWith(error: ''),
        enableNext: changedCode.length > 3);
  }

  void onSkipPressed() async {
    unawaited(_dataProvider.logAnalyticsEvent(AnalyticsEvents.clickSkipButton, {
      AnalyticsEvents.screenNameParam: AnalyticsEvents.referralCodeScreen,
    }));
    final referralCodeResponse =
        await _runWithRetry<ReferralCodeResponse>(() => submitReferralCode(''));
    if (referralCodeResponse != null) {
      await _moveNext();
    }
  }

  void onDescriptionPressed() {
    unawaited(_dataProvider
        .logAnalyticsEvent(AnalyticsEvents.clickReferralCodeDescription, {
      AnalyticsEvents.screenNameParam: AnalyticsEvents.referralCodeScreen,
    }));
  }

  Future onSubmittedCodePressed(String code) async {
    unawaited(_dataProvider.logAnalyticsEvent(AnalyticsEvents.clickNextButton, {
      AnalyticsEvents.screenNameParam: AnalyticsEvents.referralCodeScreen,
    }));
    if (code.length < 4 || code.length > maxReferralCodeLength) {
      state = state.copyWith(
          inputFieldModel: state.inputFieldModel
              ?.copyWith(error: _localization.referralCodeFormatError));
    } else {
      state = state.copyWith(inputFieldEnabled: false);
      final referralCodeResponse = await _runWithRetry<ReferralCodeResponse>(
          () => submitReferralCode(code));
      if (referralCodeResponse != null && referralCodeResponse.code != null) {
        await _moveNext();
      } else {
        state = state.copyWith(
            inputFieldEnabled: true,
            inputFieldModel: state.inputFieldModel
                ?.copyWith(error: _localization.referralCodeValidationError));
      }
    }
  }

  Future<void> _moveNext() async {
    final bp = await _runWithRetry<NavigationBreakPoint>(
        () => _breakPointManager.getLastBreakPoint());
    if (bp != null) {
      final args = bp.getNavigationArguments();
      await NavigationManager.navigateTo(
        bp.lastRouteName,
        NavigationType.Replace,
        arguments: args == null ? null : Map<String, dynamic>.from(args),
      );
    }
  }

  Future<T?> _runWithRetry<T>(Future<T?> Function() f) async {
    final c = Completer<T?>();
    final r = await f.call();

    if (r == null) {
      await showError(
        package: 'app_mobile_login',
        service: 'login',
        errorCode: 'generic_error_message',
        onNext: (action) async {
          switch (action) {
            case ErrorButtonType.TRY_AGAIN:
              NavigationManager.goBack();

              c.complete(await _runWithRetry(f));

              break;
            default:
              NavigationManager.goBack();
              c.complete(null);
          }
        },
      );
    } else {
      c.complete(r);
    }
    return c.future;
  }

  Future<ReferralCodeResponse?> submitReferralCode(String code) async {
    try {
      appLogger.info('Submit referral code');
      final task = Task(
        apiIdentifier: 'referral_code',
        requestData: {
          'referralCode': code,
          'dateTime': DateTime.now().toUtc().toString(),
        },
        taskType: TaskType.DATA_OPERATION,
        moduleIdentifier: 'app_mobile_login',
      );
      final response =
          await taskManager!.execute(task) as NetworkGraphQLResponse;
      if (response.hasException) {
        final params = {
          'reason': response.errorCode,
        };
        _logger.error('Failed to submit Referral code $params',
            error: response.exception);
        return null;
      }

      return response.baseModel as ReferralCodeResponse;
    } catch (ex, stack) {
      appLogger.error(
        'Failed to submit Referral code',
        error: ex,
        stackTrace: stack,
      );
      _logger.error('Failed to submit Referral code',
          error: ex, stackTrace: stack);

      return null;
    }
  }
}
