import 'package:core/translation/i_app_localization_service.dart';
import 'package:core/utils/extensions/string_extensions.dart';

class _Constants {
  static const companyNameHeader = 'login:company_name_header';
  static const String hashReplacer = '#';
  static const String andReplacer = '&';
}

class CompanyNameViewModel {
  final IAppLocalizationService _localization;

  CompanyNameViewModel(
    this._localization,
  );

  String getCompanyHeader(
    String userName,
    String userRole,
  ) =>
      _localization
          .getValue(_Constants.companyNameHeader)
          .replaceAll(_Constants.hashReplacer, userName)
          .replaceAll(_Constants.andReplacer, userRole);

  String getTrimedCompanyName(
    String companyName,
  ) =>
      companyName.trim();

  bool checkIfCompanyNameHasEmoji(
    String companyName,
  ) =>
      companyName.hasEmoji();

  List<String> getHighlightedArray(
    String userName,
    String userRole,
  ) =>
      [
        userName,
        userRole,
      ];
}
