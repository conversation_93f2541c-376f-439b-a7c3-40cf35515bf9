import 'package:app_mobile_login/features/save_resume_navigator/save_resume_navigator.dart';
import 'package:core/navigation/navigation_manager.dart';
import 'package:widget_library/helpers/error/helper/error_helper.dart';

class QuickActivationHomeNavigationHandler with <PERSON>rror<PERSON>andler {
  final SaveResumeNavigator saveResumeNavigator;

  QuickActivationHomeNavigationHandler(
    this.saveResumeNavigator,
  );

  void navigateToLicence() {
    saveResumeNavigator.navigateNext(forceRefresh: true);
  }
}
