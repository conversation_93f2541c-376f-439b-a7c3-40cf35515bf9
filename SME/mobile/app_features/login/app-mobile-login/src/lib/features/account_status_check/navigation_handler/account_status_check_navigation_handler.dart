import 'package:app_mobile_login/route_manager/login_route_manger.dart';
import 'package:core/navigation/navigation_manager.dart';
import 'package:widget_library/helpers/error/helper/error_helper.dart';

class AccountStatusCheckNavigationHandler with <PERSON>rrorHandler {
  void navigateToLoginPage() {
    NavigationManager.navigateTo('login-login', NavigationType.ReplaceCurrent);
  }

  void navigateToTracker() {
    NavigationManager.navigateTo(
      '${LoginRouteManager.login}-${LoginRouteManager.applicationTrackerScreen}',
      NavigationType.Push,
    );
  }

  Future<void> showErrorMessage(String errorCode) async {
    await showError(
      errorCode: errorCode,
      package: 'app_mobile_login',
      service: 'login',
    );
  }
}
