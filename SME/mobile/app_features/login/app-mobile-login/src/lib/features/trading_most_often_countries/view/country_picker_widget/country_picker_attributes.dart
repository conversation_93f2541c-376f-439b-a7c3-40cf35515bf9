import 'package:core/model/country.dart';

class CountryPickerAttributes {
  List<Country> selectedCountries;
  Function onAddCountriesTapped;
  Function(Country) onRemoveCountryTapped;
  Function onFinish;
  String labelAddCountry;
  String subTitle;
  String description;

  CountryPickerAttributes({
    required this.subTitle,
    required this.description,
    required this.labelAddCountry,
    this.selectedCountries = const [],
    required this.onAddCountriesTapped,
    required this.onRemoveCountryTapped,
    required this.onFinish,
  });
}
