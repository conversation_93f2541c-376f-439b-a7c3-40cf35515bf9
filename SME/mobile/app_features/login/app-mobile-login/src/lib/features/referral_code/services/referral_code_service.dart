import 'package:app_mobile_login/features/referral_code/services/referral_code_query.dart';
import 'package:network_manager/model/requests/graph_ql/graphql_get_request.dart';

import 'referral_code_response.dart';

class ReferralCodeService {
  GraphQLRequest getGraphQLRequest(String code, String dateTime) {
    final response = ReferralCodeResponse();
    final request = ReferralCodeGraphQLQuery.getQuery(code, dateTime);
    return GraphQLRequest(
      response,
      request,
      GraphQlRequestType.mutate,
      name: 'referral-code',
    );
  }
}
