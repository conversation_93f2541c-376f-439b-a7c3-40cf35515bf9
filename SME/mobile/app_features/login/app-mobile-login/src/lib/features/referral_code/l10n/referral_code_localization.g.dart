// GENERATED CODE
//
// After the template files .arb have been changed,
// generate this class by the command in the terminal:
// flutter pub run lokalise_flutter_sdk:gen-lok-l10n
//
// Please see https://pub.dev/packages/lokalise_flutter_sdk

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes
// ignore_for_file: depend_on_referenced_packages

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart';
import 'package:lokalise_flutter_sdk/lokalise_flutter_sdk.dart';
import 'intl/messages_all.dart';

class ReferralCodeLocalizations {
  ReferralCodeLocalizations._internal();

  static const LocalizationsDelegate<ReferralCodeLocalizations> delegate =
      _AppLocalizationDelegate();

  static const List<Locale> supportedLocales = [
    Locale.fromSubtags(languageCode: 'en'),
    Locale.fromSubtags(languageCode: 'ar')
  ];

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  static final Map<String, List<String>> _metadata = {
    'referralCodeBottomSheetDescription': [],
    'referralCodeFormatError': [],
    'referralCodeHeader': [],
    'referralCodeInfoBottomSheetTitle': [],
    'referralCodeInfoButtonText': [],
    'referralCodeInputFieldHint': [],
    'referralCodeNextButtonText': [],
    'referralCodeSkipButtonText': [],
    'referralCodeValidationError': []
  };

  static Future<ReferralCodeLocalizations> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    Lokalise.instance.metadata = _metadata;

    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = ReferralCodeLocalizations._internal();
      return instance;
    });
  }

  static ReferralCodeLocalizations of(BuildContext context) {
    final instance = Localizations.of<ReferralCodeLocalizations>(
        context, ReferralCodeLocalizations);
    assert(instance != null,
        'No instance of ReferralCodeLocalizations present in the widget tree. Did you add ReferralCodeLocalizations.delegate in localizationsDelegates?');
    return instance!;
  }

  /// `This is a unique code that one of our partners has shared with you for identification purposes.\n\nSkip this step if you don't have a referral code.`
  String get referralCodeBottomSheetDescription {
    return Intl.message(
      'This is a unique code that one of our partners has shared with you for identification purposes.\n\nSkip this step if you don\'t have a referral code.',
      name: 'referralCodeBottomSheetDescription',
      desc: '',
      args: [],
    );
  }

  /// `Code must contain min 4 digit and max 12 digits.`
  String get referralCodeFormatError {
    return Intl.message(
      'Code must contain min 4 digit and max 12 digits.',
      name: 'referralCodeFormatError',
      desc: '',
      args: [],
    );
  }

  /// `If you’ve got a referral code let’s fire it up`
  String get referralCodeHeader {
    return Intl.message(
      'If you’ve got a referral code let’s fire it up',
      name: 'referralCodeHeader',
      desc: '',
      args: [],
    );
  }

  /// `What is a referral code?`
  String get referralCodeInfoBottomSheetTitle {
    return Intl.message(
      'What is a referral code?',
      name: 'referralCodeInfoBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `What is a referral code?`
  String get referralCodeInfoButtonText {
    return Intl.message(
      'What is a referral code?',
      name: 'referralCodeInfoButtonText',
      desc: '',
      args: [],
    );
  }

  /// `Referral code`
  String get referralCodeInputFieldHint {
    return Intl.message(
      'Referral code',
      name: 'referralCodeInputFieldHint',
      desc: '',
      args: [],
    );
  }

  /// `Yalla, next`
  String get referralCodeNextButtonText {
    return Intl.message(
      'Yalla, next',
      name: 'referralCodeNextButtonText',
      desc: '',
      args: [],
    );
  }

  /// `Nope, I don’t have a code`
  String get referralCodeSkipButtonText {
    return Intl.message(
      'Nope, I don’t have a code',
      name: 'referralCodeSkipButtonText',
      desc: '',
      args: [],
    );
  }

  /// `Uh oh. We don't recognize this one.`
  String get referralCodeValidationError {
    return Intl.message(
      'Uh oh. We don\'t recognize this one.',
      name: 'referralCodeValidationError',
      desc: '',
      args: [],
    );
  }
}

class _AppLocalizationDelegate
    extends LocalizationsDelegate<ReferralCodeLocalizations> {
  const _AppLocalizationDelegate();

  @override
  bool isSupported(Locale locale) =>
      ReferralCodeLocalizations.supportedLocales.any((supportedLocale) =>
          supportedLocale.languageCode == locale.languageCode);

  @override
  Future<ReferralCodeLocalizations> load(Locale locale) =>
      ReferralCodeLocalizations.load(locale);

  @override
  bool shouldReload(_AppLocalizationDelegate old) => false;
}
