import 'package:app_mobile_login/features/self_declaration/self_declaration_attributes.dart';
import 'package:core/navigation/navigation_manager.dart';
import 'package:widget_library/helpers/error/helper/error_helper.dart';

class FatcaDoneNavigationHandler with ErrorHandler {
  static const argsIdentifier = 'args';

  void navigateToSelfDeclaration(
      SelfDeclarationAttributes _selfDeclarationAttributes) {
    NavigationManager.navigateTo(
      'login-crs_self_declaration',
      NavigationType.Push,
      arguments: {
        argsIdentifier: _selfDeclarationAttributes,
      },
    );
  }
}
