import 'dart:async';

import 'package:app_mobile_login/common/analytics_events.dart';
import 'package:app_mobile_login/features/new_user_registration/user_name_setup/data_provider/user_name_creation_data_provider.dart';
import 'package:app_mobile_login/features/new_user_registration/user_name_setup/screens/user_name_creation_view.dart';
import 'package:app_mobile_login/features/new_user_registration/user_name_setup/state/user_name_creation_state.dart';
import 'package:app_mobile_login/features/new_user_registration/user_name_setup/viewmodel/user_name_creation_view_model.dart';
import 'package:core/translation/i_app_localization_service.dart';
import 'package:task_manager/base_classes/base_view_model.dart';

import '../../user_registration_state.dart';

class _Constants {
  static const arrowIconPath = 'widget_library:assets/images/ic_arrow.svg';
  static const authNotSureText = 'login:signup_not_sure_about_authorisation';
  static const firstChildText = 'login:signup_tap_here';
  static const header = 'login:user_name_Header';
  static const hintText = 'login:user_name_hint';
  static const onboardingEnterFullName = 'onboarding_enter_full_name';
}

class UserNameCreationCoordinator extends BaseViewModel<UserNameCreationState> {
  final IAppLocalizationService _localization;
  final UserNameCreationViewModel _viewModel;
  final IUserNameCreationDataProvider _dataProvider;
  late Function(String) onNextClick;
  late Function? onTapHere;
  String userName = '';

  UserNameCreationCoordinator(
    this._localization,
    this._viewModel,
    this._dataProvider,
  ) : super(
          null,
          UserNameCreationState(
            arrowIconPath: _Constants.arrowIconPath,
            authNotSureText: _localization.getValue(_Constants.authNotSureText),
            firstChildText: _localization.getValue(_Constants.firstChildText),
            header: _localization.getValue(_Constants.header),
            hintText: _Constants.hintText,
            enableButton: false,
          ),
        );

  void initialize(Function(String user) onNextClick, Function? _onTapHere) {
    this.onNextClick = onNextClick;
    onTapHere = _onTapHere;

    unawaited(_dataProvider
        .logAnalyticsEvent(AnalyticsEvents.screenViewUserNameCreation, {
      AnalyticsEvents.screenViewParam: (UserNameCreationView).toString(),
    }));

    unawaited(
        _dataProvider.logAnalyticsEvent(AnalyticsEvents.screenViewOnboarding, {
      AnalyticsEvents.screenNameParam: _Constants.onboardingEnterFullName,
    }));
  }

  void onUserNameChanged(String changeName) {
    if (userName.isEmpty && changeName.isNotEmpty) {
      unawaited(_dataProvider.logAnalyticsEvent(AnalyticsEvents.enterFullName, {
        AnalyticsEvents.screenNameParam: _Constants.onboardingEnterFullName,
      }));
    }
    userName = _viewModel.getUserName(changeName);
    state = state.copyWith(enableButton: _viewModel.enableButton(userName));
  }

  String getLocalizedValue(String key) {
    return _localization.getValue(key);
  }

  void onNextPressed(String name) {
    onNextClick(name);
    unawaited(_dataProvider.logAnalyticsEvent(AnalyticsEvents.clickNextButton, {
      AnalyticsEvents.screenNameParam: _Constants.onboardingEnterFullName,
    }));
  }

  void onSubmitted(String value) {}
}
