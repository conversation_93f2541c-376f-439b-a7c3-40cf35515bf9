// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tax_self_declaration_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TaxDeclarationResponse _$TaxDeclarationResponseFromJson(
        Map<String, dynamic> json) =>
    TaxDeclarationResponse(
      IndividualAndPartnerResponse.fromJson(
          json['readIndividualAndIndividualPartnersInfo']
              as Map<String, dynamic>),
    );

Map<String, dynamic> _$TaxDeclarationResponseToJson(
        TaxDeclarationResponse instance) =>
    <String, dynamic>{
      'readIndividualAndIndividualPartnersInfo': instance.response.toJson(),
    };

IndividualAndPartnerResponse _$IndividualAndPartnerResponseFromJson(
        Map<String, dynamic> json) =>
    IndividualAndPartnerResponse(
      IndividualInfo.fromJson(
          json['AuthorizedIndividual'] as Map<String, dynamic>),
      (json['PartnerIndividuals'] as List<dynamic>?)
          ?.map((e) => IndividualInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$IndividualAndPartnerResponseToJson(
        IndividualAndPartnerResponse instance) =>
    <String, dynamic>{
      'AuthorizedIndividual': instance.individualResponse.toJson(),
      'PartnerIndividuals': instance.partners?.map((e) => e.toJson()).toList(),
    };

PartnerIndividualsResponse _$PartnerIndividualsResponseFromJson(
        Map<String, dynamic> json) =>
    PartnerIndividualsResponse(
      (json['PartnerIndividuals'] as List<dynamic>)
          .map((e) => IndividualInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PartnerIndividualsResponseToJson(
        PartnerIndividualsResponse instance) =>
    <String, dynamic>{
      'PartnerIndividuals': instance.partners.map((e) => e.toJson()).toList(),
    };

IndividualInfo _$IndividualInfoFromJson(Map<String, dynamic> json) =>
    IndividualInfo(
      json['IndividualId'] as String,
      json['FullName'] as String?,
      json['FATCA'] == null
          ? null
          : FatcaDeclaration.fromJson(json['FATCA'] as Map<String, dynamic>),
      (json['CRSList'] as List<dynamic>?)
          ?.map((e) => CRSDeclaration.fromJson(e as Map<String, dynamic>))
          .toList(),
      json['FirstName'] as String?,
      json['LastName'] as String?,
    );

Map<String, dynamic> _$IndividualInfoToJson(IndividualInfo instance) =>
    <String, dynamic>{
      'IndividualId': instance.individualId,
      'FullName': instance.fullName,
      'FirstName': instance.firstName,
      'LastName': instance.lastName,
      'FATCA': instance.fatcaDeclaration?.toJson(),
      'CRSList': instance.crsDeclaration?.map((e) => e.toJson()).toList(),
    };

FatcaDeclaration _$FatcaDeclarationFromJson(Map<String, dynamic> json) =>
    FatcaDeclaration(
      json['SSN'] as String?,
      json['ITIN'] as String?,
      json['Declaration'] as bool?,
    );

Map<String, dynamic> _$FatcaDeclarationToJson(FatcaDeclaration instance) =>
    <String, dynamic>{
      'SSN': instance.ssn,
      'ITIN': instance.itin,
      'Declaration': instance.declaration,
    };

CRSDeclaration _$CRSDeclarationFromJson(Map<String, dynamic> json) =>
    CRSDeclaration(
      json['Country'] as String,
      json['TIN'] as String?,
      json['TINComment'] as String?,
    );

Map<String, dynamic> _$CRSDeclarationToJson(CRSDeclaration instance) =>
    <String, dynamic>{
      'Country': instance.country,
      'TIN': instance.tin,
      'TINComment': instance.reason,
    };
