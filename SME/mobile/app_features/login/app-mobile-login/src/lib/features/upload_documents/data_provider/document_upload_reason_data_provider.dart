import 'package:app_mobile_login/features/upload_documents/services/upload_reason/uploading_document_reason_model.dart';
import 'package:app_mobile_login/features/upload_documents/view/upload_documents_view.dart';
import 'package:task_manager/base_data_provider/base_data_provider.dart';
import 'package:task_manager/task_manager_impl.dart';
import 'package:widget_library/helpers/error/helper/error_helper.dart';

class _Constants {
  static const deviceTypeParam = 'document_type';
  static const partnerFlagParam = 'partner_flag';
  static const getDocumentReasonsApiIdentifier =
      'get_document_uploading_reasons';
  static const loginModuleIdentifier = 'app_mobile_login';
}

abstract class IDocumentUploadingReasonDataProvider {
  Future<List<String>> getDocumentUploadingReasons(
      {required String documentType, UploadDocumentsMode? documentsMode});
  Future<void> logAnalyticsEvent(String eventName,
      [Map<String, dynamic>? properties]);
  void onErrorMessage(String? errorCode,
      {String? defaultErrorCode = 'default'});
}

class DocumentUploadingReasonDataProvider extends BaseDataProvider
    with <PERSON>rrorHandler
    implements IDocumentUploadingReasonDataProvider {
  DocumentUploadingReasonDataProvider(TaskManager taskManager)
      : super(taskManager);

  @override
  Future<List<String>> getDocumentUploadingReasons({
    required String documentType,
    UploadDocumentsMode? documentsMode,
  }) async {
    final partnerFlag =
        documentsMode == UploadDocumentsMode.individualPartnerKYC ||
            documentsMode == UploadDocumentsMode.corporatePartnerKYB;
    final response = await executeApiRequest<
        UploadingDocumentReasonsNetworkResponseDataModel>(
      apiIdentifier: _Constants.getDocumentReasonsApiIdentifier,
      requestData: {
        _Constants.deviceTypeParam: documentType,
        _Constants.partnerFlagParam: partnerFlag,
      },
      moduleIdentifier: _Constants.loginModuleIdentifier,
    );
    return response?.reasons ?? [];
  }

  @override
  void onErrorMessage(String? errorCode,
      {String? defaultErrorCode = 'default'}) {
    showError(
      package: 'app_settings',
      service: 'settings',
      errorCode: errorCode ?? defaultErrorCode,
    );
  }
}
