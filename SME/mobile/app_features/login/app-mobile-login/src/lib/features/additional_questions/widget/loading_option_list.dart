import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

class LoadingOptionList extends StatelessWidget {
  const LoadingOptionList({super.key});

  @override
  Widget build(BuildContext context) {
    final colorStyling = context.colorStyling;

    return ListView.builder(
      shrinkWrap: true,
      itemCount: 5,
      padding: EdgeInsets.zero,
      itemBuilder: (_, __) => Padding(
        padding: const EdgeInsets.symmetric(vertical: 5),
        child: CompanyShimmer(
          model: CompanyShimmerModel(
            baseColor: CompanyColorPointer.background1,
            highlightColor: CompanyColorPointer.surface2,
          ),
          child: Container(
            width: double.maxFinite,
            height: 40,
            decoration: BoxDecoration(
                color: colorStyling.secondary5,
                borderRadius: BorderRadius.circular(10)),
          ),
        ),
      ),
    );
  }
}
