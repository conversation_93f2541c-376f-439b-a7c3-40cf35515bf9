import 'package:http/http.dart' show MultipartFile;

class DocumentGraphQLVariables {
  final String source;
  final String documentType;
  final String acceptedAt;
  final MultipartFile file;

  DocumentGraphQLVariables({
    this.source = 'Manual',
    required this.documentType,
    required this.acceptedAt,
    required this.file,
  });

  DocumentGraphQLVariables.fromMap(Map<String, dynamic> json)
      : source = json['Source'] as String,
        documentType = json['DocumentType'] as String,
        acceptedAt = json['AcceptedAt'] as String,
        file = json['file'] as MultipartFile;

  Map<String, dynamic> toMap() {
    return {
      'Source': source,
      'DocumentType': documentType,
      'AcceptedAt': acceptedAt,
      'file': file,
    };
  }
}
