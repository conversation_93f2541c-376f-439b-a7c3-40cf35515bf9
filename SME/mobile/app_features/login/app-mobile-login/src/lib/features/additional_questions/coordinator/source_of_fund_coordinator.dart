import 'package:app_mobile_login/features/additional_questions/data_model/source_of_fund.dart';
import 'package:app_mobile_login/features/additional_questions/l10n/additional_questions_localizations.g.dart';
import 'package:app_mobile_login/features/additional_questions/state/source_of_fund_state.dart';
import 'package:flutter/cupertino.dart';
import 'package:task_manager/base_classes/base_view_model.dart';
import 'package:task_manager/task_manager.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart' hide NavigationType;
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_onboarding_api/domain/interactors/signup_interactor.dart';
import 'package:wio_feature_onboarding_api/navigation/sof_bottom_sheet_navigation_config.dart';
import 'package:wio_feature_sof_api/sof_api.dart';

class SourceOfFundCoordinator extends BaseViewModel<SourceOfFundState> {
  final SofInteractor sofInteractor;
  final SignupInteractor signupInteractor;
  final ToastMessageProvider toastMessageProvider;
  final AdditionalQuestionsLocalizations additionalQuestionsLocalizations;
  final NavigationProvider navigationProvider;

  SourceOfFundCoordinator(
    TaskManager? taskManager, {
    required this.sofInteractor,
    required this.toastMessageProvider,
    required this.additionalQuestionsLocalizations,
    required this.navigationProvider,
    required this.signupInteractor,
  }) : super(
          taskManager,
          SourceOfFundState.ideal(),
        );

  Future<void> initialize() async {
    try {
      state = state.copyWith(fetchStatus: FetchStatus.loading);
      var options = await sofInteractor.getSoFList();
      state = state.copyWith(
        fetchStatus: FetchStatus.success,
        list: List.generate(
          options.length,
          (index) => OptionsData(id: index, option: options[index]),
        ),
      );
    } catch (e, _) {
      debugPrint(e.toString());
      toastMessageProvider.showRetailMobileThemedToastMessage(
        NotificationToastMessageConfiguration.error(
            additionalQuestionsLocalizations.errorSofFetching),
      );

      state = state.copyWith(fetchStatus: FetchStatus.failed);
    }
  }

  Future<void> navigateToSofBottomSheet(String languageCode) async {
    try {
      final result = await signupInteractor.fetchStaticDocuments(
          documentType: 'sof-video-sme');

      final url = result.localizedUrls[languageCode];
      if (url == null) {
        throw Exception('Video url should not return null');
      }
      await navigationProvider.showBottomSheet(SofBottomSheetNavigationConfig(
        title: additionalQuestionsLocalizations.sofBottomsheetTitle,
        description: additionalQuestionsLocalizations.sofBottomsheetDescription,
        url: url,
      ));
    } on Object catch (e) {
      debugPrint(e.toString());
      toastMessageProvider.showRetailMobileThemedToastMessage(
        NotificationToastMessageConfiguration.error(
            additionalQuestionsLocalizations.errorSofFetching),
      );
    }
  }
}
