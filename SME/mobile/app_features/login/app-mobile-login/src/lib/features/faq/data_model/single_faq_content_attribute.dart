import 'package:widget_library/card/view_card_detail/model/card_detail_bottom_sheet_model.dart';

class SingleFaqContentAttribute {
  final CardRowHeaderModel header;
  final String? title;
  final String? subtitle;
  final String? content;
  final BottomPanelAttributes? bottomPanel;

  SingleFaqContentAttribute({
    required this.header,
    this.title,
    this.subtitle,
    this.content,
    this.bottomPanel,
  });
}

class BottomPanelAttributes {
  final String label;
  final String yeslabel;
  final String nolabel;
  final Function() yesCallback;
  final Function() noCallback;

  BottomPanelAttributes({
    required this.label,
    required this.yeslabel,
    required this.nolabel,
    required this.yesCallback,
    required this.noCallback,
  });
}
