import 'dart:async';

import 'package:app_mobile_login/common/analytics_events.dart';
import 'package:app_mobile_login/features/application_submit/data_provider/submit_application_bottom_sheet_data_provider.dart';
import 'package:app_mobile_login/features/application_submit/state/submit_application_bottom_sheet_state.dart';
import 'package:app_mobile_login/features/application_submit/view/submit_application_bottom_sheet_view.dart';
import 'package:core/storage/keys/storage_keys.dart';
import 'package:task_manager/base_classes/base_view_model.dart';
import 'package:tuple/tuple.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';

class _Constants {
  static const String title = 'login:application_submit_title';
  static const String titleHighlighted =
      'login:application_submit_title_highlighted';
  static const String subtitle = 'login:application_submit_subtitle';
  static const String swipeToDismissTitle = 'login:swipe_to_send';
  static const String onboardingSubmitApplication =
      'onboarding_submit_application';
}

class SubmitApplicationBottomSheetCoordinator
    extends BaseViewModel<SubmitApplicationBottomSheetState> {
  final ISubmitApplicationBottomSheetDataProvider _dataProvider;
  SubmitApplicationBottomSheetCoordinator(
    this._dataProvider,
  ) : super(
            null,
            SubmitApplicationBottomSheetState(
              pictorgramPointer: CompanyPictogramPointer.emotions_happy,
              title: _Constants.title,
              titleHighlighted: _Constants.titleHighlighted,
              subtitle: _Constants.subtitle,
              swipeToDismissTitle: _Constants.swipeToDismissTitle,
            ));

  void initialize() async {
    var result = await readSecureStorageData();
    state.userName = result.item1;
    state.companyName = result.item2;

    unawaited(_dataProvider.logAnalyticsEvent(
        AnalyticsEvents.screenViewSumbitApplicationBottomSheet, {
      AnalyticsEvents.screenViewParam:
          (SubmitApplicationBottomSheetView).toString(),
    }));
  }

  String routingPath() => 'login-application_overview_details';

  Future<Tuple2<String, String>> readSecureStorageData() async {
    final userNameKey = StorageKeys.userFirstNameStorageKey;
    final companyNameKey = StorageKeys.userBusinessNameStorageKey;
    final userName = await _dataProvider.readSecureStorageData(userNameKey);
    final companyName =
        await _dataProvider.readSecureStorageData(companyNameKey);
    return Tuple2(
      userName,
      companyName,
    );
  }

  void logClickSubmitEvent() {
    unawaited(_dataProvider
        .logAnalyticsEvent(AnalyticsEvents.clickSubmitApplication, {
      AnalyticsEvents.screenNameParam: _Constants.onboardingSubmitApplication,
    }));
  }

  void logClickBackEvent() {
    unawaited(_dataProvider.logAnalyticsEvent(AnalyticsEvents.clickBackButton, {
      AnalyticsEvents.screenNameParam: _Constants.onboardingSubmitApplication,
    }));
  }
}
