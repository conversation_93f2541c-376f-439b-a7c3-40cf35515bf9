import 'package:core/model/country.dart';
import 'package:core/model/country.dart';
import 'package:flutter/cupertino.dart';

class ListTextRowUIDataModel {
  Function()? onPressed;
  Function(String text)? onChangesSubmitted;
  VoidCallback? onChanged;
  String title;
  String? info;
  String? icon;
  String? textFieldStatus;
  String? textFieldInputType;
  String? errorText;

  ListTextRowUIDataModel({
    required this.title,
    this.onChanged,
    this.onPressed,
    this.info,
    this.icon,
    this.onChangesSubmitted,
    this.textFieldStatus,
    this.textFieldInputType,
    this.errorText,
  });
}

class CompanyReviewDetailState {
  List<Country> legalPresenceCountries;
  List<Country> tradingCountries;
  final String deleteIcon;
  final String addIcon;
  final String rightIcon;
  final String tradingCountriesSubtitle;
  final String legalCountriesSubtitle;
  final String addCountriesSubtitle;
  final String countryOfOperationsTitle;
  String explainerTextTitle;
  List<String> explainerHighlightedText;
  final String footerConfirmText;
  final String dockedButtonText;
  final String dropdownTitle;
  String headerText;
  String headerHighlightedText;
  List<ListTextRowUIDataModel> generalInfoModels;
  final String generalInfoTitle;
  bool checkBoxStatus;
  bool confirmButtonShow;
  bool checkBoxEnabled;
  String tradingCountriesErrorText;
  String legalPresenceCountriesErrorText;

  CompanyReviewDetailState({
    required this.generalInfoTitle,
    required this.generalInfoModels,
    required this.headerText,
    required this.headerHighlightedText,
    required this.dropdownTitle,
    required this.legalPresenceCountries,
    required this.tradingCountries,
    required this.deleteIcon,
    required this.addIcon,
    required this.rightIcon,
    required this.legalCountriesSubtitle,
    required this.tradingCountriesSubtitle,
    required this.addCountriesSubtitle,
    required this.countryOfOperationsTitle,
    required this.dockedButtonText,
    required this.explainerHighlightedText,
    required this.explainerTextTitle,
    required this.footerConfirmText,
    required this.checkBoxStatus,
    required this.checkBoxEnabled,
    required this.confirmButtonShow,
    required this.tradingCountriesErrorText,
    required this.legalPresenceCountriesErrorText,
  });

  CompanyReviewDetailState copyWith(
      {bool? checkBoxEnabled, bool? confirmButtonShow, bool? checkBoxStatus}) {
    return CompanyReviewDetailState(
      generalInfoTitle: generalInfoTitle,
      generalInfoModels: generalInfoModels,
      headerText: headerText,
      headerHighlightedText: headerHighlightedText,
      dropdownTitle: dropdownTitle,
      legalPresenceCountries: legalPresenceCountries,
      tradingCountries: tradingCountries,
      deleteIcon: deleteIcon,
      addIcon: addIcon,
      rightIcon: rightIcon,
      legalCountriesSubtitle: legalCountriesSubtitle,
      tradingCountriesSubtitle: tradingCountriesSubtitle,
      addCountriesSubtitle: addCountriesSubtitle,
      countryOfOperationsTitle: countryOfOperationsTitle,
      dockedButtonText: dockedButtonText,
      explainerHighlightedText: explainerHighlightedText,
      explainerTextTitle: explainerTextTitle,
      footerConfirmText: footerConfirmText,
      checkBoxStatus: checkBoxStatus ?? this.checkBoxStatus,
      checkBoxEnabled: checkBoxEnabled ?? this.checkBoxEnabled,
      confirmButtonShow: confirmButtonShow ?? this.confirmButtonShow,
      tradingCountriesErrorText: tradingCountriesErrorText,
      legalPresenceCountriesErrorText: legalPresenceCountriesErrorText,
    );
  }
}
