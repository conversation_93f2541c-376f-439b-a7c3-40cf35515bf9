import 'dart:async';
import 'package:app_mobile_login/features/login/data_model/token_model.dart';
import 'package:core/session_management/inactivity_service.dart';
import 'package:network_manager/auth/auth_manager.dart';
import 'package:task_manager/task_manager.dart';

enum AuthBiometricsErrorCodes {
  refreshTokenNull,
  biometricsLoginFailedReasonUnknown,
  biometricsLoginUserCancelled,
  biometricsLoginPPINBlocked,
  biometricsLoginPPINAlreadyBlocked,
  beUserTokenExpired
}

abstract class ILoginDataProvider {
  Future<dynamic> fetchStoreTokenInformation(
    TokenModel token,
  );

  void startInactivityService();

  Future<void> logAnalyticsEvent(String eventName,
      [Map<String, dynamic>? properties]);
}

class LoginDataProvider extends BaseDataProvider implements ILoginDataProvider {
  final IAuthManager _authManager;
  final TaskManager _taskManager;
  final IInactivityService _iInactivityService;

  LoginDataProvider(
    this._taskManager,
    this._authManager,
    this._iInactivityService,
  ) : super(_taskManager);

  @override
  Future<dynamic> fetchStoreTokenInformation(
    TokenModel token,
  ) async {
    await _authManager.storeTokenInformation(
      accessToken: token.accessToken,
      sessionId: token.sessionId,
      refreshToken: token.refreshToken,
      expiresIn: token.expiresIn,
      individualId: token.individualId,
      businessId: token.businessId,
    );
  }

  @override
  void startInactivityService() {
    _iInactivityService.start();
  }
}
