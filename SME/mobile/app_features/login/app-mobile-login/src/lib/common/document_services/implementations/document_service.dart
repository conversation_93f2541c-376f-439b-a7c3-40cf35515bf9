import 'dart:convert';
import 'dart:io';

import 'package:app_mobile_login/common/document_services/interfaces/i_document_holder.dart';
import 'package:app_mobile_login/common/document_services/interfaces/i_document_service.dart';
import 'package:app_mobile_login/common/document_services/models/document_info_model.dart';
import 'package:app_mobile_login/common/document_services/models/document_type.dart';
import 'package:app_mobile_login/common/document_services/models/ps_document.dart';
import 'package:app_mobile_login/common/document_services/models/uploadable.dart';
import 'package:app_mobile_login/features/configurable_mobile_journey/data_model/configurable_mobile_journey_response.dart';
import 'package:app_mobile_login/features/configurable_mobile_journey/service/cmj_data_source.dart';
import 'package:app_mobile_login/features/quick_document_upload/data_model/partner_document_info_model.dart';
import 'package:app_mobile_login/features/update_business/service/update_business_physical_address_information_service.dart';
import 'package:app_mobile_login/features/upload_documents/models/kybc_upload_document_model.dart';
import 'package:app_mobile_login/features/upload_documents/services/send_kybc_documents_collected/kybc_documents_collected_response.dart';
import 'package:app_mobile_login/features/upload_documents/view/upload_documents_view.dart';
import 'package:core/model/partner.dart';
import 'package:core/storage/keys/storage_keys.dart';
import 'package:core/translation/i_app_localization_service.dart';
import 'package:core/utils/extensions/list_extensions.dart';
import 'package:logging_api/logging.dart';
import 'package:network_manager/model/response/graphql/network_graphql_response.dart';
import 'package:path_provider/path_provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:task_manager/task_manager.dart';
import 'package:widget_library/square_panel/ps_file.dart';

class _Constants {
  static const loginModuleIdentifier = 'app_mobile_login';

  static const updateBusinessDocumentCollectedApiIdentifier =
      'update_business_document_collected';
  static const updateBusinessAddressApiIdentifier =
      'update_business_physical_address_information';
  static const sendKYBCDocumentsApiIdentifier = 'send_kybc_documents_collected';
}

class DocumentService extends BaseDataProvider implements IDocumentService {
  final IDocumentHolder documentHolder;
  final TaskManager taskManager;
  final CMJDataSource cmjDataSource;
  final IAppLocalizationService _localization;
  final Logger _logger;

  DocumentService(
    this.documentHolder,
    this.taskManager,
    this.cmjDataSource,
    this._localization,
    this._logger,
  ) : super(taskManager);

  @override
  Future<List<PSDocument>> getUploadDocuments() async {
    final steps = await cmjDataSource.getJourneyData(forceRefresh: true);
    var documents = <PSDocument>[];
    for (var element in steps.stages) {
      final result = await _mapJourneyStage(element);
      documents.addAll(result);
    }
    return documents;
  }

  @override
  Future setDocumentToUploadComplete(PSDocument document) async {
    final streamController = BehaviorSubject<double>();
    streamController.sink.add(1.0);
    document.streamProgress = streamController.stream;
    await streamController.close();
    document.uploadState = UploadState.COMPLETE;
  }

  @override
  Future<List<PSFile>?> getFileFromDocumentInfo(
    String stageName,
  ) async {
    var task = Task(
      apiIdentifier: 'document_information',
      moduleIdentifier: 'app_mobile_login',
      requestData: {
        'DocumentType': stageName,
      },
      subType: TaskSubType.GRAPHQL,
      taskType: TaskType.DATA_OPERATION,
    );
    final response = await taskManager.execute(task) as NetworkGraphQLResponse;
    if (!response.hasException) {
      var docInfo = response.baseModel as DocumentInformationModel;
      return _convertBase64ToPSFile(docInfo.documentName!,
          docInfo.documentContent!, stageName.documentType!);
    }
    _logger.error('Failed to download document. document_type: $stageName',
        error: response.exception);
    return null;
  }

  Future<List<PSFile>> _convertBase64ToPSFile(
      String docName, String documentContent, DocumentType documentType) async {
    var files = <PSFile>[];
    final dir = await getTemporaryDirectory();
    final path = '${dir.path}/$docName';
    final file = await File(path).writeAsBytes(base64Decode(documentContent));
    files.add(
      PSFile(
        key: '${documentType.backEndConstant}_0',
      )..filePath = file.path,
    );
    return files;
  }

  @override
  Future<List<PSDocument>> getPartnerDocuments(
      {required PartnerType partnerType}) async {
    final steps = await cmjDataSource.getJourneyData(forceRefresh: false);
    final filterIndividual = (JourneyStage stage) =>
        stage.stageIdentifier == CMJDataSource.partnersIndividualDocuments;
    final filterCorporate = (JourneyStage stage) =>
        stage.stageIdentifier == CMJDataSource.partnersCorporateDocuments;

    final List<StagePage> pages;

    final partnerStep = steps.stages.firstOrNull(
        partnerType == PartnerType.individual
            ? filterIndividual
            : filterCorporate);

    if (partnerStep == null) {
      final fastTrackPartnerStep = steps.stages.firstOrNull((element) =>
          element.stageIdentifier ==
          CMJDataSource.stageIdentifierFastTrackPartners);
      if (fastTrackPartnerStep == null) {
        throw 'No document configuration present at neither manual partners or fast track partners stage!';
      }
      pages = (partnerType == PartnerType.individual
              ? fastTrackPartnerStep.individualPartnerDocumentsFastTrack
              : fastTrackPartnerStep.corporatePartnerDocumentsFastTrack) ??
          [];

      if (pages.isEmpty) {
        throw 'No documents configuration for fast track partners step';
      }
    } else {
      pages = partnerStep.pages;
    }

    return pages.map(
      (page) {
        List<PSFile>? files;

        if (_toCamelCase(page.name) ==
            DocumentType.EMIRATES_ID.backEndConstant) {
          files = [
            PSFile(
              key: '${DocumentType.EMIRATES_ID.backEndConstant}_0',
              docSide: DocSide.FRONT,
            ),
            PSFile(
              key: '${DocumentType.EMIRATES_ID.backEndConstant}_1',
              docSide: DocSide.BACK,
            ),
          ];
        }
        return PSDocument(
          DocumentType.values.firstWhere(
            (element) => element.backEndConstant == _toCamelCase(page.name),
          ),
          isOptional: page.optional,
          files: files,
          topRightChipLabel: page.optional
              ? _localization.getValue('login:uploadDocument_optional')
              : null,
        );
      },
    ).toList();
  }

  @override
  Future<List<PSFile>?> getFileFromPartnerDocumentInfo(
    String document,
    bool isAccountIndividual,
    String accountId,
  ) async {
    var requestData = <String, dynamic>{};

    if (isAccountIndividual) {
      requestData['IndividualId'] = accountId;
    } else {
      requestData['BusinessId'] = accountId;
    }
    requestData['DocumentType'] = document;
    var task = Task(
      apiIdentifier: isAccountIndividual
          ? 'individual_partner_document_info'
          : 'corporate_partner_document_info',
      moduleIdentifier: 'app_mobile_login',
      requestData: requestData,
      subType: TaskSubType.GRAPHQL,
      taskType: TaskType.DATA_OPERATION,
    );
    final response = await taskManager.execute(task) as NetworkGraphQLResponse;
    if (!response.hasException) {
      var docInfo = response.baseModel as PartnerDocumentInfoModel;
      var files = <PSFile>[];

      final docsArray = isAccountIndividual
          ? docInfo.individualPartnerDocuments
          : docInfo.corporatePartnerDocuments;

      for (var i = 0; i < docsArray.length; i++) {
        var partnerFileDocument = docsArray[i];
        final dir = await getTemporaryDirectory();
        final path = '${dir.path}/${partnerFileDocument.documentName!}';
        final file = await File(path)
            .writeAsBytes(base64Decode(partnerFileDocument.documentContent!));
        var docSide = DocSide.DEFAULT;

        if (DocumentType.EMIRATES_ID.backEndConstant ==
            document.documentType!.backEndConstant) {
          if (partnerFileDocument.documentSide
                  ?.toLowerCase()
                  .contains(DocumentSide.front) ??
              false) {
            docSide = DocSide.FRONT;
          } else if (partnerFileDocument.documentSide
                  ?.toLowerCase()
                  .contains(DocumentSide.back) ??
              false) {
            docSide = DocSide.BACK;
          }
        }
        final psFile = PSFile(
          key: '${document.documentType!.backEndConstant}_$i',
          docSide: docSide,
        )..filePath = file.path;

        files.add(psFile);
      }
      return files;
    } else {
      _logger.error(
          'Failed to download partner document. document_type:$document, is_account_individual: $isAccountIndividual, account_id: $accountId',
          error: response.exception);
    }
    return null;
  }

  @override
  Stream<double> uploadDocument(
    PSDocument document,
    File file, {
    required Function() onChange,
    required Function(String error, [String? message]) onError,
    Function(PSDocument)? onSuccess,
  }) =>
      _uploadDocument(
        document,
        file,
        onChange: onChange,
        onError: onError,
        onSuccess: onSuccess,
        taskBuilder: () => Task(
          apiIdentifier: 'kyb_file_upload',
          moduleIdentifier: _Constants.loginModuleIdentifier,
          requestData: {
            'name': 'kyb_file_upload',
            'parameters': {
              'documentType': document.documentType.backEndConstant,
              'documentCategory': 'KYB',
              'journeyCode': 'ONBOARDING',
              'postProcessor': 'KYB_EVENT',
            },
            'file': file,
          },
          taskType: TaskType.DATA_OPERATION,
          subType: TaskSubType.REST_FILE_UPLOAD,
        ),
      );

  //ignore: long-parameter-list
  Stream<double> _uploadDocument(
    PSDocument document,
    File file, {
    required Function() onChange,
    required Function(String error, [String? message]) onError,
    Function(PSDocument)? onSuccess,
    required Task Function() taskBuilder,
  }) {
    document.uploadState = UploadState.IN_PROGRESS;
    onChange();
    final streamController = BehaviorSubject<double>();
    try {
      document.uploadState = UploadState.IN_PROGRESS;
      onChange();

      taskManager.execute(taskBuilder()).then((response) {
        if (response.statusCode == 200) {
          final uploadedFileKey = response.body as String;
          final uploaded = 1.0;
          streamController.sink.add(uploaded);
          streamController.close();
          document.uploadState = UploadState.COMPLETE;
          final updatedDocument =
              _updateFileFromDocument(document, uploadedFileKey);
          onSuccess?.call(updatedDocument);
        } else {
          document.uploadState = UploadState.FAILED;
          document.streamProgress = null;
          streamController.close();
          if (response.statusCode == 400) {
            onError(
              'be_onboarding_upload_incorrect_document_type',
              response.errorMessage,
            );
          } else {
            onError('upload_file_failed', response.errorMessage);
          }
        }
        onChange();
      }).onError((error, stackTrace) {
        _logger.error('Failed to upload file: client code exception',
            error: error, stackTrace: stackTrace);
      });
    } catch (error, stack) {
      _logger.error('Failed to upload file: error building multipart request',
          error: error, stackTrace: stack);
      throw Exception('error building multipart request: ${error.toString()}');
    }
    return streamController.stream;
  }

  @override
  // ignore: long-parameter-list
  Stream<double> uploadPartnerDocument(
    PSDocument document,
    File file, {
    required PartnerType partnerType,
    required String documentTitle,
    required Function() onChange,
    required Function(String error, [String? message]) onError,
    Function(PSDocument)? onSuccess,
  }) =>
      _uploadDocument(
        document,
        file,
        onChange: onChange,
        onError: onError,
        taskBuilder: () => Task(
          apiIdentifier: 'kyb_file_upload',
          moduleIdentifier: _Constants.loginModuleIdentifier,
          requestData: {
            'name': 'kyb_file_upload',
            'parameters': {
              'documentCategory': 'Partner',
              'documentType': document.documentType.backEndConstant,
              'journeyCode': 'ONBOARDING',
              'partnerType': partnerType.value,
              'returnFileName': 'true',
            },
            'file': file,
          },
          taskType: TaskType.DATA_OPERATION,
          subType: TaskSubType.REST_FILE_UPLOAD,
        ),
      );

  @override
  Future<KYBCDocumentsCollectedResponse?> sendKYBCDocumentsCollected({
    required List<KYBCUploadDocument> uploadDocuments,
    required Function(String error) onError,
  }) async {
    final result = await executeApiRequest<KYBCDocumentsCollectedResponse>(
      apiIdentifier: _Constants.sendKYBCDocumentsApiIdentifier,
      requestData: {
        'documentsList': uploadDocuments,
      },
      taskType: TaskType.DATA_OPERATION,
      moduleIdentifier: _Constants.loginModuleIdentifier,
      onError: onError,
    );

    return result;
  }

  Future<String> getBusinessId() async {
    return await getValueFromMemory(StorageKeys.userBusinessIdStorageKey);
  }

  @override
  // ignore: long-parameter-list
  Stream<double> uploadKYBCDocument(
    UploadDocumentsMode flowType,
    PSDocument document,
    String partnerId,
    File file, {
    required Function() onChange,
    required Function(String error, [String? message]) onError,
    Function(PSDocument)? onSuccess,
  }) =>
      _uploadDocument(
        document,
        file,
        onChange: onChange,
        onError: onError,
        onSuccess: onSuccess,
        taskBuilder: () => Task(
          apiIdentifier: 'kyb_file_upload',
          moduleIdentifier: _Constants.loginModuleIdentifier,
          requestData: {
            'name': 'kyb_file_upload',
            'parameters': {
              'id': partnerId,
              'documentType': document.documentType.backEndConstant,
              'documentCategory': flowType.backEndConstant,
              'journeyCode': 'ACCOUNT_MAINTENANCE',
            },
            'file': file,
          },
          taskType: TaskType.DATA_OPERATION,
          subType: TaskSubType.REST_FILE_UPLOAD,
        ),
      );

  PSDocument _updateFileFromDocument(
      PSDocument document, String uploadedFileKey) {
    if (document.files != null && document.files!.isNotEmpty) {
      final currentFile = document.files!.firstOrNull(
        (element) =>
            element.filePath != null &&
            element.filePath!.isNotEmpty &&
            element.uploadDocumentKey == null,
      );
      if (currentFile != null) {
        currentFile.uploadDocumentKey = uploadedFileKey;
        if (_shouldLimitNumberOfDocuments(document)) {
          return document;
        }
      }
    }
    final currentIndex = document.files?.length ?? 0;
    var documentSide = DocSide.DEFAULT;
    if (document.documentType == DocumentType.EMIRATES_ID) {
      documentSide = currentIndex == 0 ? DocSide.FRONT : DocSide.BACK;
    }
    document.files?.add(
      PSFile(
        key: '${document.documentType.backEndConstant}_$currentIndex',
        docSide: documentSide,
      ),
    );
    return document;
  }

  bool _shouldLimitNumberOfDocuments(PSDocument document) {
    // Visa and Passport should be limited to one document and EmiratesId should be limited to two documents.
    // Other document types don't have limitation when it comes to number of documents that can be uploaded.
    return document.documentType == DocumentType.PASSPORT ||
        document.documentType == DocumentType.VISA ||
        document.documentType == DocumentType.EMIRATES_ID &&
            document.files!.length >= 2;
  }

  @override
  Future sendPhysicalAddressAnswer(bool hasPhysical,
      {Function(String error)? onError}) async {
    final task = Task(
      moduleIdentifier: _Constants.loginModuleIdentifier,
      requestData: {
        UpdateBusinessPhysicalAddressInformationService.hasPhysicalAddressKey:
            hasPhysical
      },
      taskType: TaskType.DATA_OPERATION,
      apiIdentifier: _Constants.updateBusinessAddressApiIdentifier,
    );

    try {
      final result = await taskManager.execute(task);
      if (result is NetworkGraphQLResponse && result.hasException) {
        onError?.call(result.errorCode ?? 'unknown');
      }
    } catch (ex, stacktrace) {
      final message =
          'An error occurred while sending physical address response.';
      onError?.call(message);
      _logger.error('Failed Physical address: client code error',
          error: ex, stackTrace: stacktrace);
    }
  }

  @override
  Future sendDocumentsCollected() {
    final task = Task(
      moduleIdentifier: _Constants.loginModuleIdentifier,
      requestData: {},
      taskType: TaskType.DATA_OPERATION,
      apiIdentifier: _Constants.updateBusinessDocumentCollectedApiIdentifier,
    );

    return taskManager.execute(task);
  }

  @override
  Future<List<String>> getKycDocuments() async {
    final journeyData = await cmjDataSource.getJourneyData();
    final kycDocuments = <String>[];
    journeyData.stages
        .where((element) => element.stageStatus == StageStatus.INCOMPLETE)
        .forEach((element) {
      if (element.category == StageCategory.INDIVIDUAL &&
          element.stageType == StageType.DOCUMENTS) {
        //Converting visa to Visa, passport to Passport
        element.pages.forEach((page) {
          kycDocuments.add(_toCamelCase(page.name));
        });
      }
    });

    return kycDocuments;
  }

  // Helpers
  String _toCamelCase(String name) {
    return '${name[0].toUpperCase()}${name.substring(1)}';
  }

  /// Mapping of the JourneyStage into required (completed) documents.
  ///
  /// This logic is cursed and must be burned down by Satan
  Future<List<PSDocument>> _mapJourneyStage(JourneyStage stage) async {
    final result = <PSDocument>[];
    if (stage.category == StageCategory.BUSINESS &&
        stage.stageType == StageType.DOCUMENTS) {
      /// The legacy mapping from the stageName.
      if (stage.stageName.documentType != null) {
        final document = PSDocument(
          stage.stageName.documentType!,
          isOptional: stage.optional,
        );
        result.add(document);
      }
      final details = stage.businessDetails ?? [];

      for (final details in details) {
        /// Handling case with MissingFasttrackDocuments details
        /// Where all the missing documents are located inside value
        /// Ex: MemorandumOfAssociation,ProofOfAddress
        if (details.name == CMJDataSource.businessDetailsMissingFasttrackDocs) {
          final detailsValue = details.value ?? '';
          final documentTypes = detailsValue
              .split(',')
              .map((name) => name.documentType)
              .whereType<DocumentType>();

          for (final documentType in documentTypes) {
            final document = PSDocument(
              documentType,
              isOptional: stage.optional,
            );
            result.add(document);
          }
        }
      }

      /// Marking documents as completed if the stage has completed
      /// by customer
      if (stage.stageStatus == StageStatus.COMPLETED) {
        for (final document in result) {
          await setDocumentToUploadComplete(document);
        }
      }
    }
    return result;
  }
}

extension Unique<E, Id> on List<E> {
  List<E> unique([Id Function(E element)? id, bool inplace = true]) {
    final ids = <dynamic>{};
    var list = inplace ? this : List<E>.from(this);
    list.retainWhere((x) => ids.add(id != null ? id(x) : x as Id));
    return list;
  }
}
