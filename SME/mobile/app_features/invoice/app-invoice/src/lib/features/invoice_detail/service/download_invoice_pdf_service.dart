import 'package:app_mobile_invoice/features/invoice_detail/datamodel/download_invoice_pdf_data_model.dart';
import 'package:app_mobile_invoice/features/invoice_detail/service/downlaod_invoice_pdf_graphql_query.dart';
import 'package:network_manager/model/requests/graph_ql/graphql_get_request.dart';

class DownloadInvoicePDFService {
  GraphQLRequest getGraphQLRequest(Map<String, dynamic> parameters) {
    final dataModel = DownloadInvoicePdfDataModel();
    final request = DownloadInvoicePDFGraphqlQuery.getQuery(parameters);
    return GraphQLRequest(
      dataModel,
      request,
      GraphQlRequestType.query,
      name: 'download_invoice_pdf',
    );
  }
}
