import 'package:core/utils/extensions/string_extensions.dart';
import 'package:flutter/material.dart';
import 'package:pdfx/pdfx.dart';
import 'package:share_plus/share_plus.dart';
import 'package:widget_library/page_header/text_ui_data_model.dart';
import 'package:widget_library/static_text/PSText.dart';

class PDFViewerWidget extends StatelessWidget {
  final String invoiceNumber;
  final String sentDate;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final String? pdfPath;
  final String errorText;
  final VoidCallback? shareCallBack;

  PDFViewerWidget({
    required this.invoiceNumber,
    required this.sentDate,
    this.backgroundColor = const Color(0xfff8f5ff),
    this.borderColor = const Color(0xff5500F9),
    this.borderWidth = 1,
    this.pdfPath,
    this.errorText = '',
    this.shareCallBack,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 576,
      margin: EdgeInsets.only(left: 32, right: 32, bottom: 32, top: 42),
      padding: EdgeInsets.only(left: 24, right: 24, bottom: 24, top: 20),
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border.all(
          color: borderColor,
          width: borderWidth,
        ),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          renderPDFHeader(),
          renderPDF(),
        ],
      ),
    );
  }

  Widget renderPDFHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            PSText(
              text: TextUIDataModel(invoiceNumber),
            ),
            SizedBox(width: 0, height: 4),
            PSText(
              text: TextUIDataModel(sentDate),
            ),
          ],
        ),
        GestureDetector(
          child: Icon(
            Icons.ios_share,
            color: borderColor,
          ),
          onTap: () {
            if (!pdfPath.isBlank()) {
              Share.shareXFiles([XFile(pdfPath!)], subject: invoiceNumber);
              shareCallBack?.call();
            }
          },
        ),
      ],
    );
  }

  Widget renderPDF() {
    return Container(
      margin: EdgeInsets.only(
        top: 16,
      ),
      child: Center(
        child: pdfPath.isBlank()
            ? Text(errorText)
            : Container(
                height: 472,
                child: PdfView(
                  builders: PdfViewBuilders<DefaultBuilderOptions>(
                    options: const DefaultBuilderOptions(),
                    documentLoaderBuilder: (_) =>
                        Center(child: CircularProgressIndicator()),
                    pageLoaderBuilder: (_) =>
                        Center(child: CircularProgressIndicator()),
                  ),
                  controller: PdfController(
                    document: PdfDocument.openFile(pdfPath!),
                    initialPage: 1,
                    viewportFraction: 1,
                  ),
                ),
              ),
      ),
    );
  }
}
