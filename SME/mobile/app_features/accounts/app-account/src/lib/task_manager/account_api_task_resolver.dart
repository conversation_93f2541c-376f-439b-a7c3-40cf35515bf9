import 'package:app_account/features/dashboard/datamodel/dashboard_data_model.dart';
import 'package:app_account/features/dashboard/service/account_service.dart';
import 'package:app_account/features/dashboard/service/cards_service.dart';
import 'package:core/ioc/di_container.dart';
import 'package:network_manager/model/response/graphql/network_graphql_response.dart';
import 'package:task_manager/task_manager.dart';

class AccountAPITaskResolver implements TaskResolver {
  static const String monthlyTransactions = 'monthly_transactions';
  static const String initialMonthlyTransactions =
      'initial_monthly_transactions';
  static const String notNowReminderRequest = 'not_now_reminder_request';
  static const String cards = 'home_cards';
  static const String firstAccount = 'first_account';
  static const String dashboard = 'dashboard';

  @override
  // ignore: long-method
  Future<dynamic> execute(
    String apiIdentifier,
    Map<String, dynamic> requestData,
  ) async {
    switch (apiIdentifier) {
      case dashboard:
        return const AccountService().getGraphQLRequest(requestData);

      case firstAccount:
        return _fetchFirstAccount();

      case cards:
        return const CardsService().getGraphQLRequest();

      default:
        throw TaskManagerException('apiIdentifier does not match!');
    }
  }

  Future<String?> _fetchFirstAccount() async {
    final task = Task(
      apiIdentifier: 'dashboard',
      requestData: <String, Object>{},
      taskType: TaskType.DATA_OPERATION,
      moduleIdentifier: 'app_account',
    );

    final response = await DIContainer.container
        .resolve<TaskManager>()
        .execute(task) as NetworkGraphQLResponse?;
    final result = response?.baseModel as DashboardNetworkResponseDataModel?;
    if (result != null) {
      final accounts = result.getAccounts?.accountsList ?? [];
      if (accounts.isNotEmpty) {
        return Future.value(accounts.first.id as String);
      }
    }

    return null;
  }
}
