import 'package:app_cash/common/beneficiaries/coordinator/beneficiaries_view_model.dart';
import 'package:app_cash/common/beneficiaries/coordinator/i_beneficiaries_coordinator.dart';
import 'package:app_cash/common/beneficiaries/data_provider/deposit_beneficiaries_data_provider.dart';
import 'package:app_cash/common/beneficiaries/navigation_handler/beneficiaries_navigation_handler.dart';
import 'package:app_cash/common/extensions.dart';
import 'package:app_cash/features/fab/add_depositor_success/screen/add_depositor_success_view.dart';
import 'package:app_cash/features/fab/add_depositor_success/view_model/add_depositor_success_view_model.dart';
import 'package:app_cash/features/fab/cash_deposit_depositors/datamodel/beneficiary_model.dart';
import 'package:app_cash/features/fab/cash_deposit_depositors/datamodel/generate_epay_ref_number_model.dart';
import 'package:app_cash/features/fab/cheque_deposit_depositors/state/fab_cheque_deposit_depositors_state.dart';
import 'package:app_cash/features/lulu/services/add_cash_beneficiary/models/cash_beneficiary_input.dart';
import 'package:core/navigation/navigation_manager.dart';
import 'package:core/translation/i_app_localization_service.dart';
import 'package:flutter/material.dart';
import 'package:network_manager/model/response/graphql/network_graphql_response.dart';
import 'package:sme_feature_transactions_api/feature_transactions_api.dart';
import 'package:task_manager/task_manager.dart';
import 'package:ui_kit_legacy_sme_mobile/ui_kit_sme_mobile.dart';
import 'package:widget_library/helpers/error/helper/error_helper.dart';
import 'package:widget_library/transfers/beneficiary/item/ps_beneficiary_list_item_widget_attributes.dart';
import 'package:wio_feature_delivery_api/delivery_api.dart';

class _Constants {
  static const newDepositorTitle = 'cash:new_depositor';
  static const addDepositorDescription =
      'cash:cheque_add_depositor_description';
  static const depositorDetails = 'cash:depositor_details';
  static const genenerateReferenceNumber = 'cash:generate_reference_number';
  static const belowFormText = 'cash:cheque_add_depositor_below_form_text';
  static const fabChequeDepositDescriptionRoute =
      'cash-fab_cheque_deposit_description';
  static const String title =
      'cash:withdrawal_confirm_confirm_quick_note_title';
  static const String highlightedTitle =
      'cash:withdrawal_confirm_confirm_quick_note_title_highlighted';
  static const String description = 'cash:confirm_screen_description';
  static const String buttonText =
      'cash:withdrawal_confirm_confirm_quick_note_continue_button';
}

class FabChequeDepositDepositorsCoordinator
    extends BeneficiariesViewModel<FabChequeDepositDepositorsState>
    implements IBeneficiariesCoordinator {
  static const _beneficiaryType = CashBeneficiaryType.deposit;

  final IDepositBeneficiariesDataProvider dataProvider;
  final TransactionsMediator mediator;
  final ChequebookInteractor chequebookInteractor;

  FabChequeDepositDepositorsCoordinator(
    IAppLocalizationService localization,
    TaskManager taskManager,
    this.mediator,
    this.chequebookInteractor, {
    IDepositBeneficiariesDataProvider? dataProvider,
    BeneficiariesNavigationHandler? navigationHandler,
  })  : dataProvider =
            dataProvider ?? DepositBeneficiariesDataProvider(taskManager),
        super(
          _beneficiaryType,
          localization,
          taskManager,
          FabChequeDepositDepositorsState(beneficiaries: []),
          dataProvider: dataProvider,
          navigationHandler: navigationHandler,
        );

  void _updateBeneficiariesList(
          List<PSBeneficiaryListItemWidgetAttributes> list) =>
      state = state.copyWith(beneficiaries: list);

  void _onBeneficiaryTap(BuildContext context, BeneficiaryModel beneficiary) =>
      callBottomSheet(context, beneficiary);

  Future _addBeneficiary(String documentId, String beneficiaryName) async {
    final result = await dataProvider.addBeneficiary(CashBeneficiaryInput(
      beneficiaryName: beneficiaryName,
      governmentId: documentId,
      type: _beneficiaryType,
    ));
    final data = result.data;

    if (result.isSuccess && data != null) {
      final beneficiary = BeneficiaryModel.fromJson(
          data['addCashBeneficiary'] as Map<String, dynamic>);
      await _generateEpayNumber(beneficiary);
    } else {
      //TODO: clarify how should error be handled in case of unsuccessful request
      navigationHandler.goBack();
    }
  }

  Future<void> initialize(BuildContext context) async {
    await fetchBeneficiaries(
      _updateBeneficiariesList,
      ((beneficiary) {
        return _onBeneficiaryTap(context, beneficiary);
      }),
    );
  }

  @override
  void addBeneficiaryAction() {
    navigationHandler.goToAddBeneficiaryScreen(
      localization.getValue(_Constants.genenerateReferenceNumber),
      _addBeneficiary,
      appBarTitle: localization.getValue(_Constants.newDepositorTitle),
      formTitle: localization.getValue(_Constants.depositorDetails),
      description: localization.getValue(_Constants.addDepositorDescription),
      belowFormText: localization.getValue(_Constants.belowFormText),
    );
  }

  @override
  void onInfoAction() {
    NavigationManager.navigateTo(
      _Constants.fabChequeDepositDescriptionRoute,
      NavigationType.Push,
      arguments: {
        'isManuallyEnabled': true,
      },
    );
  }

  @override
  void showLocations() {
    NavigationManager.navigateTo(
      'cash-fab_deposit_locations',
      NavigationType.BottomSheet,
      arguments: <String, dynamic>{
        'isScrollControlled': true,
        'shape': RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(50.0),
        ),
      },
    );
  }

  Future<void> _generateEpayNumber(BeneficiaryModel beneficiaryModel) async {
    final result = await dataProvider.makeDeposit(true, beneficiaryModel.id);

    if (result.isNotSuccess) {
      _showReferenceNumberError();
    } else {
      await _handleGenerateEpayNumberResponse(
        result,
        beneficiaryModel,
      );
    }
  }

  Future<void> _handleGenerateEpayNumberResponse(
    NetworkGraphQLResponse result,
    BeneficiaryModel beneficiary,
  ) async {
    final makeDepositData =
        result.baseModel as GenerateEPayNumberResponseDataModel;
    final generateEPayNumberModel = makeDepositData.generateEPayNumberModel;

    await NavigationManager.navigateTo(
      'cash-fab_number_generated',
      NavigationType.Push,
      arguments: DepositSuccessViewArguments(
        referenceNumber: generateEPayNumberModel.epayReferenceNumber,
        expirationDate: DateTime.parse(generateEPayNumberModel.expiryDateTime),
        isCurrentUser:
            beneficiariesListAttributeHelper.isCurrentUser(beneficiary),
        userName: beneficiary.name,
        fabDepositType: FabDeposit.cheque,
      ),
    );

    mediator.updateTransactions();
    Future.delayed(
      const Duration(seconds: 1),
      () => chequebookInteractor.refresh().ignore(),
    );
  }

  void _showReferenceNumberError() async {
    await showError(
      package: 'app_cash',
      service: 'generate_ref_number',
      errorCode: 'default',
      onNext: (ErrorButtonType type) {
        switch (type) {
          case ErrorButtonType.CONTACT_SUPPORT:
            // ignore: deprecated_member_use
            contactSupport();
            break;
          case ErrorButtonType.SWIPE_UP_TO_CLOSE:
            navigationHandler.goHome();
            break;
          case ErrorButtonType.TRY_AGAIN:
            navigationHandler.goBack();
            break;
          default:
            break;
        }
      },
    );
  }

  void callBottomSheet(
    BuildContext context,
    BeneficiaryModel beneficiaryModel,
  ) async {
    await showHighlightHeaderInfoBottomSheet(
      context: context,
      title: _Constants.title.translate(context),
      highlightedTitle: _Constants.highlightedTitle.translate(context),
      description: _Constants.description.translate(context),
      continueButtonText: _Constants.buttonText.translate(context),
      onDone: () async {
        Navigator.pop(context);
        await _generateEpayNumber(beneficiaryModel);
      },
    );
  }
}
