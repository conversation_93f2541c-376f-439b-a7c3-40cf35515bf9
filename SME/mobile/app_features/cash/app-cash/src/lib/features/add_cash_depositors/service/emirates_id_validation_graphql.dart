import 'package:app_cash/features/add_cash_depositors/service/models/validate_emirates_id.dart';

class EmiratesIdValidationGraphQL {
  static const paramsKey = 'validateEmiratesId';

  static String getQuery(Map<String, dynamic> parameters) {
    assert(parameters.containsKey(paramsKey),
        'No $paramsKey model passed to Mu<PERSON>');
    assert(parameters[paramsKey] is ValidateEmiratesId,
        'Recipient model is not of Type ValidateEmiratesId');

    // TODO We should use GraphQL variables to avoid manually building queries here
    ValidateEmiratesId r = parameters[paramsKey] as ValidateEmiratesId;

    return '''
 query {
validateGovernmentId(governmentId: "${r.governmentId}")
}
''';
  }
}
