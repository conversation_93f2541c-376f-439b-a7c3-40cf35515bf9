import 'package:app_cash/features/lulu/cash_deposit_locations/coordinator/cash_deposit_locations_coordinator.dart';
import 'package:app_cash/features/lulu/cash_deposit_locations/state/cash_deposit_location_state.dart';
import 'package:core/view/base_view.dart';
import 'package:flutter/material.dart';
import 'package:widget_library/card/view_card_detail/model/card_detail_bottom_sheet_model.dart';
import 'package:widget_library/deposit_locations/deposit_locations_attributes.dart';
import 'package:widget_library/deposit_locations/deposit_locations_widget.dart';
import 'package:widget_library/page_header/page_header_text_under_icon/page_header_text_under_icon_widget_attributes.dart';
import 'package:widget_library/page_header/text_ui_data_model.dart';

class _Constants {
  static const String labelText = 'cash:deposit_location';
  static const String closeIcon = 'app_account:assets/icons/ic_close.svg';
  static const double headerTopPadding = 10;
  static const double headerMargin = 0;
}

class CashDepositLocationView extends StatelessWidget {
  const CashDepositLocationView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BaseView<CashDepositLocationsCoordinator, CashDepositLocationsState>(
      setupViewModel: (coordinator) async {
        await coordinator.initialize();
      },
      builder: (context, state, coordinator) =>
          _buildWithStateViewModel(context, state, coordinator),
    );
  }

  Widget _buildWithStateViewModel(
    BuildContext context,
    CashDepositLocationsState state,
    CashDepositLocationsCoordinator coordinator,
  ) {
    return DepositLocationsWidget(
      attributes: DepositLocationsAttributes(
        venueLocationIndex: state.currentVenueLocationIndex,
        depositLocationsCardHeaderText: CardRowHeaderModel(
          labelTextAttributes: headerList(),
          iconPath: _Constants.closeIcon,
        ),
        depositLocationVenueList: state.depositLocationVenueList,
        venueListCallBack: (value) {
          coordinator.updateValue(value);
        },
        locations: getDropBoxLocations(state),
      ),
    );
  }

  PSPageHeaderTextUnderIconWidgetAttributes headerList() {
    return PSPageHeaderTextUnderIconWidgetAttributes(
      attributesList: [
        PSPageHeaderTextUnderIconWidgetElementAttributes(
          title: TextUIDataModel(_Constants.labelText),
        ),
      ],
      headerTopPadding: _Constants.headerTopPadding,
      leftMargin: _Constants.headerMargin,
      rightMargin: _Constants.headerMargin,
      headerBottomPadding: _Constants.headerMargin,
    );
  }

  List<DropBoxLocation>? getDropBoxLocations(
    CashDepositLocationsState state,
  ) {
    return state.cashDepositLocationDataModel?.data!.getCashDepositLocations!
        .map((e) => DropBoxLocation(
              region: e.branchLocation,
              location: e.branchName,
              address: e.branchAddress,
            ))
        .toList();
  }
}
