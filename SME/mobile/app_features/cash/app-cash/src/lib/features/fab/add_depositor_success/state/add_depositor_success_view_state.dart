class AddDepositorSuccessViewState {
  final String title;
  final String subtitle;
  final String referenceNumber;
  final String depositInstruction;
  final String referenceNumberPrefix;
  final String shareButtonTitle;

  const AddDepositorSuccessViewState({
    required this.title,
    required this.subtitle,
    required this.referenceNumber,
    required this.depositInstruction,
    required this.referenceNumberPrefix,
    required this.shareButtonTitle,
  });

  AddDepositorSuccessViewState copyWith({
    String? title,
    String? subtitle,
    String? referenceNumber,
    String? depositInstruction,
    String? referenceNumberPrefix,
    String? shareButtonTitle,
  }) {
    return AddDepositorSuccessViewState(
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      depositInstruction: depositInstruction ?? this.depositInstruction,
      referenceNumberPrefix:
          referenceNumberPrefix ?? this.referenceNumberPrefix,
      shareButtonTitle: shareButtonTitle ?? this.shareButtonTitle,
    );
  }
}
