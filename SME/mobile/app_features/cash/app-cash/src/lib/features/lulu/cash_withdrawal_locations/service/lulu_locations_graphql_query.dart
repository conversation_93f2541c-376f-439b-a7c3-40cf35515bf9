class LuluCashWithdrawalLocationsGraphQLQuery {
  static String getQuery({double requestedAmount = 0.0}) {
    final queryString = query(requestedAmount);
    return '''
 query{
  $queryString
}
''';
  }

  static String query(double requestedAmount) {
    return '''
        getLuluLocations(requestedAmount: $requestedAmount) {
                latitude,
                longitude,
                branchName,
                emirate,
                branchId
        }
        ''';
  }
}
