import 'package:app_cash/features/fab/cash_deposit_depositors/datamodel/generate_epay_ref_number_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const epayReferenceNumber = 'epayReferenceNumber';
  const expiryDateTime = 'expiryDateTime';

  group('GenerateEPayNumberResponseDataModel', () {
    test(
        'fromJson should return correct GenerateEPayNumberResponseDataModel object',
        () {
      const args = {
        'epayReferenceNumber': epayReferenceNumber,
        'expiryDateTime': expiryDateTime,
      };
      final result = GenerateEPayNumberModel.fromJson(args);
      final expectedResult = GenerateEPayNumberModel(
          epayReferenceNumber: epayReferenceNumber,
          expiryDateTime: expiryDateTime);
      expect(result.toJson(), expectedResult.toJson());
    });

    test(
      'fromJsonToModel should setup correct GenerateEPayNumberModel on GenerateEPayNumberResponseDataModel object',
      () {
        final expectedResult = GenerateEPayNumberModel(
            epayReferenceNumber: epayReferenceNumber,
            expiryDateTime: expiryDateTime);
        const args = {
          'epayReferenceNumber': epayReferenceNumber,
          'expiryDateTime': expiryDateTime,
        };
        final generateEPayNumberResponseDataModel =
            GenerateEPayNumberResponseDataModel()
              ..generateEPayNumberModel = expectedResult;
        generateEPayNumberResponseDataModel.fromJsonToModel(args);
        expect(generateEPayNumberResponseDataModel.generateEPayNumberModel,
            expectedResult);
      },
    );

    test(
        'toJson should return correct json from GenerateEPayNumberModel object',
        () {
      const args = {
        'epayReferenceNumber': epayReferenceNumber,
        'expiryDateTime': expiryDateTime,
      };
      final result = GenerateEPayNumberModel(
          epayReferenceNumber: epayReferenceNumber,
          expiryDateTime: expiryDateTime);
      expect(result.toJson(), args);
    });
  });
}
