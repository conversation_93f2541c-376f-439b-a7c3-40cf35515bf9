import 'package:app_cash/features/fab/cash_deposite_description/service/deposit_expiration_graphql_query.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const parameters = <String, dynamic>{};

  const expectedResult = '''
      query  {
        getChequeDepositExpiryPeriod()
      }
      ''';

  group('DepositExpirationGraphQLQuery', () {
    test('getQuery should return correct query', () {
      final result = DepositExpirationGraphQLQuery.getQuery(parameters);
      expect(result, expectedResult);
    });
  });
}
