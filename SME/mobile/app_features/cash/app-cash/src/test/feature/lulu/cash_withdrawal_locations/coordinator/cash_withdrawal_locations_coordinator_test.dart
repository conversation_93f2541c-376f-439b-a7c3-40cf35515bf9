import 'package:app_cash/features/lulu/cash_withdrawal_locations/coordinator/cash_withdrawal_locations_coordinator.dart';
import 'package:app_cash/features/lulu/cash_withdrawal_locations/data_provider/cash_withdrawal_locations_data_provider.dart';
import 'package:app_cash/features/lulu/cash_withdrawal_locations/datamodel/cash_withdrawal_location_data_model.dart';
import 'package:app_cash/features/lulu/cash_withdrawal_locations/navigation_handler/cash_withdrawal_location_navigation_handler.dart';
import 'package:core/translation/i_app_localization_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:task_manager/task_manager.dart';

import 'cash_withdrawal_locations_coordinator_test.mocks.dart';

@GenerateMocks([
  TaskManager,
  IAppLocalizationService,
  CashWithdrawalLocationsNavigationHandler,
  CashWithdrawalLocationsDataProvider,
])
void main() {
  late CashWithdrawalLocationsCoordinator cashWithdrawalLocationsCoordinator;
  late MockCashWithdrawalLocationsDataProvider
      mockCashWithdrawalLocationsDataProvider;

  group('CashWithdrawalLocationsCoordinator', () {
    setUp(() {
      final mockTaskManager = MockTaskManager();
      final mockIAppLocalizationService = MockIAppLocalizationService();
      when(mockIAppLocalizationService.getValue(any)).thenReturn('');
      final mockCashWithdrawalLocationsNavigationHandler =
          MockCashWithdrawalLocationsNavigationHandler();
      mockCashWithdrawalLocationsDataProvider =
          MockCashWithdrawalLocationsDataProvider();
      const latitude = 'latitude';
      const longitude = 'longitude';
      const branchId = 'branchId';
      const branchName = 'branchName';
      const emirate = 'emirate';
      const address = 'address';

      final getCashWithdrawalLocations = [
        GetCashWithdrawalLocations(
          latitude,
          longitude,
          branchId,
          branchName,
          emirate,
          address,
        ),
      ];
      final cashWithdrawalLocationData = CashWithdrawalLocationData(
          getLuluLocations: getCashWithdrawalLocations);
      final cashWithdrawalLocationDataModel = CashWithdrawalLocationDataModel()
        ..data = cashWithdrawalLocationData;
      when(mockCashWithdrawalLocationsDataProvider
              .fetchCashWithdrawalLocationsList(any, any))
          .thenAnswer((_) async => cashWithdrawalLocationDataModel);
      cashWithdrawalLocationsCoordinator = CashWithdrawalLocationsCoordinator(
        mockTaskManager,
        mockIAppLocalizationService,
        mockCashWithdrawalLocationsNavigationHandler,
        mockCashWithdrawalLocationsDataProvider,
      );
    });

    test('fetchCashWithdrawalLocationsList should be called on initialize',
        () async {
      await cashWithdrawalLocationsCoordinator.initialize();
      verify(mockCashWithdrawalLocationsDataProvider
              .fetchCashWithdrawalLocationsList(any, any))
          .called(1);
    });

    test('updateValue should emit new state', () {
      const value = 0;
      final oldState = cashWithdrawalLocationsCoordinator.state;
      cashWithdrawalLocationsCoordinator.updateValue(value);
      final newState = cashWithdrawalLocationsCoordinator.state;
      expect(oldState != newState, true);
    });
  });
}
