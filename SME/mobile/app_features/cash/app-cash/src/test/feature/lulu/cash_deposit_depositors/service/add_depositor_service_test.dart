import 'package:app_cash/features/lulu/cash_deposit_depositors/service/add_depositor_graphql.dart';
import 'package:app_cash/features/lulu/cash_deposit_depositors/service/add_depositor_service.dart';
import 'package:app_cash/features/lulu/cash_deposit_depositors/service/models/add_depositor_request.dart';
import 'package:app_cash/features/lulu/cash_deposit_depositors/service/models/add_depositor_response.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:network_manager/model/requests/graph_ql/graphql_get_request.dart';

void main() {
  const beneficiaryName = 'beneficiaryName';
  const governmentId = 'governmentId';

  final parameters = {
    AddDepositorGraphQL.paramsKey: AddDepositorRequest(
      governmentId: governmentId,
      beneficiaryName: beneficiaryName,
    ),
  };

  final expectedResult = GraphQLRequest(
    AddDepositorResponse(),
    AddDepositorGraphQL.getQuery(parameters),
    GraphQlRequestType.mutate,
    name: AddDepositorService.apiIdentifier,
  );

  group('AddDepositorService', () {
    test('getGraphQLRequest should return correct request based on parameters',
        () {
      final result = AddDepositorService().getGraphQLRequest(parameters);

      expect(result.request, expectedResult.request);
    });
  });
}
