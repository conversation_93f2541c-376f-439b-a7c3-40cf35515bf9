import 'package:app_cheques/common/analytics_events.dart';
import 'package:app_cheques/features/cheque_deposit_success/data_provider/cheque_deposit_success_data_provider.dart';
import 'package:app_cheques/features/cheque_deposit_success/screen/cheque_deposit_success_view.dart';
import 'package:app_cheques/features/cheque_deposit_success/view_model/cheque_deposit_success_view_model.dart';
import 'package:core/navigation/i_navigation_handler.dart';
import 'package:core/navigation/navigation_manager.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:task_manager/task_manager.dart';

import 'cheque_deposit_success_view_model_test.mocks.dart';

@GenerateMocks([
  TaskManager,
  IChequeDepositSuccessDataProvider,
  INavigationHandler,
])
void main() {
  late ChequeDepositSuccessViewModel chequeDepositSuccessViewModel;
  late MockINavigationHandler mockINavigationHandler;
  late MockTaskManager mockTaskManager;
  late MockIChequeDepositSuccessDataProvider
      mockIChequeDepositSuccessDataProvider;

  group('ChequeDepositSuccessViewModel', () {
    setUp(() {
      mockINavigationHandler = MockINavigationHandler();
      when(mockINavigationHandler.navigateTo(
        any,
        any,
        arguments: anyNamed('arguments'),
      )).thenAnswer((_) async => null);

      NavigationManager(mockINavigationHandler);
      mockTaskManager = MockTaskManager();
      mockIChequeDepositSuccessDataProvider =
          MockIChequeDepositSuccessDataProvider();

      chequeDepositSuccessViewModel = ChequeDepositSuccessViewModel(
        mockTaskManager,
        mockIChequeDepositSuccessDataProvider,
      );
    });

    test('logAnalyticsEvent should be called on initialize', () {
      const args = {'reference_number': ''};
      chequeDepositSuccessViewModel.initialize(args);
      verify(mockIChequeDepositSuccessDataProvider
          .logAnalyticsEvent(AnalyticsEvents.screenViewChequeDepositSuccess, {
        AnalyticsEvents.screenViewParam: (ChequeDepositSuccessView).toString(),
      })).called(1);
    });

    test('globalHomeRoute should return correct value', () {
      final result = chequeDepositSuccessViewModel.globalHomeRoute;
      final expectedResult = 'global-home';
      expect(result, expectedResult);
    });

    test('logAnalyticsEvent should be called on onViewDropLocations', () {
      chequeDepositSuccessViewModel.onViewDropLocations();
      verify(mockIChequeDepositSuccessDataProvider.logAnalyticsEvent(
              AnalyticsEvents.clickDropboxLocationLink, any))
          .called(1);
    });

    test('navigateTo should be called onSwipe', () async {
      await chequeDepositSuccessViewModel.onSwipe();

      verify(mockINavigationHandler.navigateTo(
          'global-home', NavigationType.PopUntil,
          arguments: {})).called(1);
    });

    test('navigateTo should be called onDepositAnotherCheque', () async {
      await chequeDepositSuccessViewModel.onDepositAnotherCheque();

      verify(mockINavigationHandler.navigateTo(
          'global-home', NavigationType.PopUntil,
          arguments: {})).called(1);
    });

    test('navigateTo should be called onDepositAnotherCheque', () async {
      await chequeDepositSuccessViewModel.onDepositAnotherCheque();

      verify(mockINavigationHandler.navigateTo(
              'cheques-cheque_image_capture', NavigationType.Push))
          .called(1);
    });
  });
}
