import 'package:app_cheques/features/cheque_deposit_locations/datamodel/cheque_deposit_location_data_model.dart';
import 'package:flutter/foundation.dart';

@immutable
class ChequeDepositLocationsState {
  final String provideYourDetails;
  final String depositorDetails;
  final String thePersonWillAuthorized;
  final String depositLocationTitle;
  int? currentVenueLocationIndex;
  final Set<String> depositLocationVenueList;
  ChequeDepositLocationDataModel? chequeDepositLocationDataModel;

  ChequeDepositLocationsState({
    required this.provideYourDetails,
    required this.depositorDetails,
    required this.thePersonWillAuthorized,
    required this.depositLocationVenueList,
    this.depositLocationTitle = '',
    this.currentVenueLocationIndex,
    this.chequeDepositLocationDataModel,
  });

  ChequeDepositLocationsState copyWith({
    String? provideYourDetails,
    String? depositorDetails,
    String? thePersonWillAuthorized,
    int? currentVenueLocationIndex,
    String? depositLocationTitle,
    Set<String>? depositLocationVenueList,
    ChequeDepositLocationDataModel? chequeDepositLocationDataModel,
  }) =>
      ChequeDepositLocationsState(
        thePersonWillAuthorized:
            thePersonWillAuthorized ?? this.thePersonWillAuthorized,
        depositorDetails: depositorDetails ?? this.depositorDetails,
        provideYourDetails: provideYourDetails ?? this.provideYourDetails,
        currentVenueLocationIndex:
            currentVenueLocationIndex ?? this.currentVenueLocationIndex,
        depositLocationVenueList:
            depositLocationVenueList ?? this.depositLocationVenueList,
        depositLocationTitle: depositLocationTitle ?? this.depositLocationTitle,
        chequeDepositLocationDataModel: chequeDepositLocationDataModel ??
            this.chequeDepositLocationDataModel,
      );
}
