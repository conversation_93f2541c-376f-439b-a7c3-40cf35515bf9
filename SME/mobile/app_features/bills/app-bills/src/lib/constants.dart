class Constants {
  static const moduleIdentifier = 'bills';
  static const appMobileBillsIdentifier = 'app_mobile_bills';
  static const telecomBillPaymentFullRoute = 'bills-telecom_bill_payment';
  static const telecomBillPaymentRoute = 'telecom_bill_payment';
  static const otherBillPaymentRoute = 'other_bill_payment';
  static const otherBillPaymentFullRoute = 'bills-other_bill_payment';
  static const utilityBillPaymentRoute = 'utility_bill_payment';
  static const utilityBillPaymentFullRoute = 'bills-utility_bill_payment';
  static const telecomBillPaymentConfirmationRoute = 'payment_confirmation';
  static const telecomVendorPickerFullRoute = 'bills-telecom_vendor_picker';
  static const telecomVendorPickerRoute = 'telecom_vendor_picker';
  static const routeArgumentVendorKey = 'vendor';
  static const queryParameterVendorIdKey = 'vendorId';
  static const queryParameterVendorIdSalikKey = 'salikVendorId';
  static const queryParameterPlanKey = 'plan';
  static const queryParameterPinKey = 'pin';
  static const queryParameterCustomerNumberKey = 'customerUniqueNumber';
  static const queryParameterPhoneNumberKey = 'phoneNumber';
  static const graphQlServiceNameFetchTelecomBalance = 'fetch_telecom_balance';
  static const graphQlServiceNameFetchOtherBalance = 'fetch_other_balance';
  static const graphQlServiceNameFetchUtilityBalance = 'fetch_utility_balance';
  static const graphQlServiceNameDeleteBeneficiary =
      'delete_utility_beneficiary';
  static const telecomBillLandingRoute = 'telecom_bill_landing';
  static const telecomBillLandingFullRoute = 'bills-telecom_bill_landing';
  static const beneficiaryListfullRoute = 'bills-beneficiary_list';
  static const beneficiaryListRoute = 'beneficiary_list';
  static const beneficiaryDeleteRoute = 'beneficiary_delete_bottom_sheet';
  static const beneficiaryList = 'get_all_utility_beneficiaries';
  static const billsLandingFullRoute = 'bills-bills_landing_screen';
  static const billsLandingRoute = 'bills_landing_screen';
  static const searchBeneficiary = 'search_beneficiary';
  static const selectPaymentTypeBottomSheetView = 'select_payment_type';
  static const selectPaymentTypeFullRouteBottomSheetView =
      'bills-select_payment_type';
  static const billPaymentSuccessViewRoute = 'bill_payment_success_view';
  static const graphQlServiceNameBillerLimits = 'biller_limits';
  static const graphQlServiceSaveBeneficiarySalik = 'save_beneficiary_salik';
  static const graphQlServiceSaveBeneficiaryFewa = 'save_beneficiary_fewa';
  static const graphQlServiceSaveBeneficiary = 'save_beneficiary';
  static const graphQlPaymentConfirmation = 'payment_confirmation';
  static const editBeneficiaryRoute = 'edit_beneficiary';
  static const editBeneficiaryFullRoute = 'bills-edit_beneficiary';
  static const editBeneficiaryApiIdentifier = 'edit_beneficiary';
  static const contactSupport = 'contact_support';
}
