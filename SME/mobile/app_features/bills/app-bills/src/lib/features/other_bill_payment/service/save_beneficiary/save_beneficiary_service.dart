import 'package:app_mobile_bills/constants.dart';
import 'package:app_mobile_bills/features/other_bill_payment/data_model/save_beneficiary_data_model.dart';
import 'package:app_mobile_bills/features/other_bill_payment/service/save_beneficiary/save_beneficiary_graphql_query.dart';
import 'package:network_manager/model/requests/graph_ql/graphql_get_request.dart';

class SaveBeneficiarySalikService {
  GraphQLRequest getGraphQLRequest(Map<String, dynamic> parameters) {
    final saveBeneficiaryDataModel = SaveBeneficiaryDataModel();
    final request = SaveBeneficiarySalikGraphQLQuery.getMutation(parameters);
    return GraphQLRequest(
      saveBeneficiaryDataModel,
      request,
      GraphQlRequestType.mutate,
      name: Constants.graphQlServiceSaveBeneficiarySalik,
    );
  }
}
