import 'package:json_annotation/json_annotation.dart';

part 'save_beneficiary_request.g.dart';

@JsonSerializable()
class SaveBeneficiaryRequest {
  final String? beneficiaryName;
  final String? vendorId;
  final String? vendorName;
  final String? vendorType;
  final String? customerNumber;
  final String? vendorServiceType;

  SaveBeneficiaryRequest({
    this.beneficiaryName,
    this.vendorId,
    this.vendorName,
    this.vendorType,
    this.customerNumber,
    this.vendorServiceType,
  });

  factory SaveBeneficiaryRequest.fromJson(Map<String, dynamic> json) =>
      _$SaveBeneficiaryRequestFromJson(json);

  Map<String, dynamic> get toJson => _$SaveBeneficiaryRequestToJson(this);
}
