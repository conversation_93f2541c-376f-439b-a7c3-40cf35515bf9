// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'beneficiary_list_network_response_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BeneficiaryList _$BeneficiaryListFromJson(Map<String, dynamic> json) =>
    BeneficiaryList(
      beneficiaries: (json['getAllUtilityBeneficiaries'] as List<dynamic>?)
          ?.map((e) => BeneficiaryListNetworkDataModel.fromJson(
              e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$BeneficiaryListToJson(BeneficiaryList instance) =>
    <String, dynamic>{
      'getAllUtilityBeneficiaries': instance.beneficiaries,
    };

BeneficiaryListNetworkDataModel _$BeneficiaryListNetworkDataModelFromJson(
        Map<String, dynamic> json) =>
    BeneficiaryListNetworkDataModel(
      beneficiaryName: json['BeneficiaryName'] as String?,
      beneficiaryId: json['BeneficiaryId'] as String?,
      customerNumber: json['CustomerNumber'] as String?,
      vendorId: json['VendorId'] as String?,
      vendorName: json['VendorName'] as String?,
      vendorServiceType: json['VendorServiceType'] as String?,
      vendorType: json['VendorType'] as String?,
      lastTransactionAmount:
          (json['LastTransactionAmount'] as num?)?.toDouble(),
      lastTransactionCurrency: json['LastTransactionCurrency'] as String?,
      billerPin: json['BillerPin'] as String?,
    );

Map<String, dynamic> _$BeneficiaryListNetworkDataModelToJson(
        BeneficiaryListNetworkDataModel instance) =>
    <String, dynamic>{
      'BeneficiaryName': instance.beneficiaryName,
      'BeneficiaryId': instance.beneficiaryId,
      'CustomerNumber': instance.customerNumber,
      'VendorId': instance.vendorId,
      'VendorName': instance.vendorName,
      'VendorType': instance.vendorType,
      'VendorServiceType': instance.vendorServiceType,
      'LastTransactionAmount': instance.lastTransactionAmount,
      'LastTransactionCurrency': instance.lastTransactionCurrency,
      'BillerPin': instance.billerPin,
    };
