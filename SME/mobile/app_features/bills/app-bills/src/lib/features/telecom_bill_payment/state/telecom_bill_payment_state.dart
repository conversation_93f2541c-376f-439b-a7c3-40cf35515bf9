import 'package:app_mobile_bills_widgets/widgets.dart';

/// Telecom bill payment view state
class TelecomBillPaymentState {
  // To hide and show NEXT button.
  bool showNextButton;

  bool isAccountSectionExpanded;
  bool isAmountSectionExpanded;
  bool isAmountSectionVisible;
  bool isAddToBeneficiariesEnabled;
  bool isAddToBeneficiariesVisible;
  bool isDefaultFormFieldsDisabled;

  // State properties to capture balance check API response
  bool isBalanceCheckDone;
  double? availableBalance;
  double? balanceAmount;
  String? transactionId;
  String? providerTransactionId;
  bool? isActive;

  double? topUpAmount;
  String? topUpAmountErrorMessage;
  bool isTopUpAmountCheckDone;

  String? beneficiaryName;
  bool isValidNickName;

  // State properties to capture vendor details
  late final TelecomVendor vendor;
  late final List<String> availablePlans;
  late final String vendorName;
  TelecomPlan? selectedPlan;
  String? selectedPlanText;

  // State properites to capture customer account / phone number details
  String? phoneNumber;
  bool? isValidPhoneNumber;
  String? beneficiaryId;

  TelecomBillPaymentState({
    this.isDefaultFormFieldsDisabled = false,
    this.beneficiaryId,
    this.isAddToBeneficiariesVisible = true,
    required this.showNextButton,
    this.isActive,
    required this.isAccountSectionExpanded,
    required this.isAmountSectionExpanded,
    required this.isAmountSectionVisible,
    required this.isAddToBeneficiariesEnabled,
    required this.isBalanceCheckDone,
    this.balanceAmount,
    this.transactionId,
    this.providerTransactionId,
    this.selectedPlan,
    this.phoneNumber,
    this.isValidPhoneNumber,
    required this.isTopUpAmountCheckDone,
    this.isValidNickName = false,
  });
}
