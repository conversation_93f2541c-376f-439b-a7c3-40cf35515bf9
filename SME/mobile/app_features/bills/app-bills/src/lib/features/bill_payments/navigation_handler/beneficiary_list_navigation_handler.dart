import 'package:app_mobile_bills/constants.dart';
import 'package:app_mobile_bills/features/bill_payments/state/bill_payments_state.dart';
import 'package:app_mobile_bills/features/shared/datamodel/balance_data_model.dart';
import 'package:app_mobile_bills_widgets/features/bill_payments/beneficiary_info.dart';
import 'package:app_mobile_bills_widgets/features/telecom_bill_payment/telecom_bill_payment_widget_attributes.dart';
import 'package:core/core.dart';
import 'package:core/navigation/navigation_manager.dart';
import 'package:core/translation/i_app_localization_service.dart';
import 'package:flutter/material.dart';
import 'package:widget_library/helpers/error/helper/error_helper.dart';

class _Constants {
  static const String isScrollControlled = 'isScrollControlled';
  static const String isDismissible = 'isDismissible';
  static const String shape = 'shape';
  static const double shapeBorder = 30;
  static const String errorMessage = 'Failed To retrieve Utility Beneficiaries';
  static const errorPackage = 'app_transfer_common';
  static const beneficiaryListKey = 'beneficiaryList';
  static const balanceErrorMessage = 'bills:telecom_bill_payment_cant_reach';
  static const billsService = 'bills';
  static const serviceProviderReachError = 'service_provider_reach_error';
  static const globalHomeRoute = 'global-home';
  static const selectedPlanKey = 'selectedPlan';
  static const phoneNumberKey = 'phoneNumber';
  static const balanceDetailsKey = 'balanceDetails';
  static const availableBalanceKey = 'availableBalance';
  static const beneficiaryIdKey = 'beneficiaryId';
  static const accountNumberKey = 'accountNumber';
  static const pinKey = 'pin';
  static const lastTransactionAmountKey = 'lastTransactionAmount';
  static const nolTagIdKey = 'nolTagIdKey';
  static const onTapBeneficiaryKey = 'onTapBeneficiary';
}

class BeneficiaryListNavigationHandler with ErrorHandler {
  final IAppLocalizationService _localization;

  BeneficiaryListNavigationHandler(this._localization);

  void onError() {
    appLogger.error(_Constants.errorMessage);
  }

  void navigateToSelectPaymentType(
    UtilityVendorType type1,
    UtilityVendorType type2,
    UtilityVendorType type3,
    UtilityVendorType type4,
  ) {
    NavigationManager.navigateTo(
      Constants.selectPaymentTypeFullRouteBottomSheetView,
      NavigationType.BottomSheet,
      arguments: <String, dynamic>{
        _Constants.isScrollControlled: true,
        _Constants.isDismissible: false,
        _Constants.shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(_Constants.shapeBorder),
            topLeft: Radius.circular(_Constants.shapeBorder),
          ),
        ),
        'onCreditTap': () {},
        'onUtilityTap': () {
          navigateToUtilityPayment(type2);
        },
        'onTelecomTap': () {
          navigateToTelecomPayment(type3);
        },
        'onOtherTap': () {
          navigateToOtherPayment(type4);
        },
      },
    );
  }

  void navigateToOtherPayment(UtilityVendorType type) async {
    await NavigationManager.navigateTo(
      Constants.telecomBillLandingFullRoute,
      NavigationType.BottomSheet,
      arguments: <String, dynamic>{
        'type': UtilityVendorType.Others,
        _Constants.isScrollControlled: true,
        _Constants.shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_Constants.shapeBorder),
        ),
      },
    );
  }

  void navigateToUtilityPayment(UtilityVendorType type) async {
    await NavigationManager.navigateTo(
      Constants.telecomBillLandingFullRoute,
      NavigationType.BottomSheet,
      arguments: <String, dynamic>{
        'type': UtilityVendorType.Utilities,
        _Constants.isScrollControlled: true,
        _Constants.shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_Constants.shapeBorder),
        ),
      },
    );
  }

  void navigateToTelecomPayment(UtilityVendorType type) async {
    await NavigationManager.navigateTo(
      Constants.telecomBillLandingFullRoute,
      NavigationType.BottomSheet,
      arguments: <String, dynamic>{
        'type': UtilityVendorType.Telecom,
        _Constants.isScrollControlled: true,
        _Constants.shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_Constants.shapeBorder),
        ),
      },
    );
  }

  void showErrorMessage({String? errorCode}) {
    showError(
      package: _Constants.errorPackage,
      service: _Constants.errorPackage,
      errorCode: errorCode,
    );
  }

  void navigateToBeneficiaryList(
    UtilityBeneficiaryListDataModel beneficiaries,
    Function(UtilityBeneficiaryInfo beneficiary) onTapBeneficiary,
  ) {
    NavigationManager.navigateTo(
      Constants.beneficiaryListfullRoute,
      NavigationType.Push,
      arguments: <String, dynamic>{
        _Constants.beneficiaryListKey: beneficiaries,
        _Constants.onTapBeneficiaryKey: onTapBeneficiary,
      },
    );
  }

  void navigateToTelecomPaymentInitiationFromSavedBeneficiary(
    TelecomVendor telecomVendor,
    TelecomPlan? selectedPlan,
    String? phoneNumber,
    BalanceData? balanceDetails,
    double? availableBalance,
    String? beneficiaryId,
  ) async {
    await NavigationManager.navigateTo(
      Constants.telecomBillPaymentFullRoute,
      NavigationType.Push,
      arguments: {
        Constants.routeArgumentVendorKey: telecomVendor,
        _Constants.selectedPlanKey: selectedPlan,
        _Constants.phoneNumberKey: phoneNumber,
        _Constants.balanceDetailsKey: balanceDetails,
        _Constants.availableBalanceKey: availableBalance,
        _Constants.beneficiaryIdKey: beneficiaryId,
      },
    );
  }

  void navigateToSalikPaymentInitiationFromSavedBeneficiary({
    required OtherVendor otherVendor,
    required BalanceData balanceDetails,
    required String accountNumber,
    required String billerPin,
    required double? lastTransactionAmount,
    required String beneficiaryId,
  }) async {
    await NavigationManager.navigateTo(
      Constants.otherBillPaymentFullRoute,
      NavigationType.Push,
      arguments: {
        Constants.routeArgumentVendorKey: otherVendor,
        _Constants.balanceDetailsKey: balanceDetails,
        _Constants.accountNumberKey: accountNumber,
        _Constants.pinKey: billerPin,
        _Constants.lastTransactionAmountKey: lastTransactionAmount,
        _Constants.beneficiaryIdKey: beneficiaryId,
      },
    );
  }

  void navigateToNolPaymentInitiationFromSavedBeneficiary({
    required OtherVendor otherVendor,
    required String beneficiaryId,
    required String nolTagId,
    required BalanceData balanceDetails,
  }) async {
    await NavigationManager.navigateTo(
      Constants.otherBillPaymentFullRoute,
      NavigationType.Push,
      arguments: {
        Constants.routeArgumentVendorKey: otherVendor,
        _Constants.nolTagIdKey: nolTagId,
        _Constants.beneficiaryIdKey: beneficiaryId,
        _Constants.balanceDetailsKey: balanceDetails,
      },
    );
  }

  Future<void> handleError(String? vendorName) async {
    await showError(
      package: Constants.appMobileBillsIdentifier,
      service: _Constants.billsService,
      errorCode: _Constants.serviceProviderReachError,
      message: _localization
          .getValue(_Constants.balanceErrorMessage, args: [vendorName]),
      onNext: (ErrorButtonType type) {
        switch (type) {
          case ErrorButtonType.TRY_AGAIN:
            NavigationManager.goBack();
            break;
          case ErrorButtonType.CONTACT_SUPPORT:
            contactSupport();
            break;
          case ErrorButtonType.SWIPE_UP_TO_CLOSE:
            NavigationManager.navigateTo(
              _Constants.globalHomeRoute,
              NavigationType.PopUntil,
            );
            break;
          default:
            break;
        }
      },
    );
  }

  void navigateToUtilityPaymentInitiationFromSavedBeneficiary(
    UtilityVendor utilityVendor,
    String phoneNumber,
    BalanceData balanceDetails,
    double availableBalance,
    String beneficiaryId,
  ) async {
    await NavigationManager.navigateTo(
      Constants.utilityBillPaymentFullRoute,
      NavigationType.Push,
      arguments: {
        Constants.routeArgumentVendorKey: utilityVendor,
        _Constants.phoneNumberKey: phoneNumber,
        _Constants.balanceDetailsKey: balanceDetails,
        _Constants.availableBalanceKey: availableBalance,
        _Constants.beneficiaryIdKey: beneficiaryId,
      },
    );
  }
}
