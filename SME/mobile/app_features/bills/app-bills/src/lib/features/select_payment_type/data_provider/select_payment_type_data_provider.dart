import 'package:task_manager/base_data_provider/base_data_provider.dart';
import 'package:task_manager/task_manager_impl.dart';

abstract class ISelectPaymentTypeDataProvider {
  Future<void> logAnalyticsEvent(String eventName,
      [Map<String, dynamic>? properties]);
}

class SelectPaymentTypeDataProvider extends BaseDataProvider
    implements ISelectPaymentTypeDataProvider {
  SelectPaymentTypeDataProvider(TaskManager _taskManager) : super(_taskManager);
}
