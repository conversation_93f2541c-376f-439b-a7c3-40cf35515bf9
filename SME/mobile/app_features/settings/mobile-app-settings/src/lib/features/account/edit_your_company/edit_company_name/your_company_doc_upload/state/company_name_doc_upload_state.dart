import 'package:app_mobile_login/features/upload_documents/services/send_kybc_documents_collected/document_status.dart';

class CompanyNameDocUploadState {
  final String pageTitle;
  final DocumentSection? documentSection;

  CompanyNameDocUploadState({
    this.pageTitle = '',
    this.documentSection,
  });

  CompanyNameDocUploadState copyWith({
    String? pageTitle,
    DocumentSection? documentSection,
  }) =>
      CompanyNameDocUploadState(
        pageTitle: pageTitle ?? this.pageTitle,
        documentSection: documentSection ?? this.documentSection,
      );
}

class DocumentSection {
  final String? title;
  final String? description;
  final List<CompanyDocument> documents;

  DocumentSection({this.title, this.description, required this.documents});
}

class CompanyDocument {
  final String title;
  final String subTitle;
  final DocStatus documentStatus;
  final String badge;

  CompanyDocument({
    required this.title,
    required this.subTitle,
    required this.documentStatus,
    required this.badge,
  });
}
