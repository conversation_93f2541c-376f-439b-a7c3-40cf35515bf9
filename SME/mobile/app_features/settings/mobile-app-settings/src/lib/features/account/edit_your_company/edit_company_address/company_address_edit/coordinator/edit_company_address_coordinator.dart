import 'dart:async';

import 'package:app_mobile_login/common/document_services/models/document_type.dart';
import 'package:app_mobile_login/common/document_services/models/ps_document.dart';
import 'package:app_mobile_login/features/upload_documents/view/upload_documents_view.dart';
import 'package:app_settings/common/analytics_events.dart';
import 'package:app_settings/features/account/edit_your_company/edit_company_address/company_address_edit/data_provider/edit_company_address_data_provider.dart';
import 'package:app_settings/features/account/edit_your_company/edit_company_address/company_address_edit/navigation_handler/company_address_navigation_handler.dart';
import 'package:app_settings/features/account/edit_your_company/edit_company_address/company_address_edit/screen/edit_company_address_screen.dart';
import 'package:app_settings/features/account/edit_your_company/edit_company_address/company_address_edit/state/edit_company_address_state.dart';
import 'package:core/navigation/navigation_manager.dart';
import 'package:core/translation/i_app_localization_service.dart';
import 'package:task_manager/base_classes/base_view_model.dart';
import 'package:task_manager/task_manager_impl.dart';

class _Constants {
  static const companyAddressTitle = 'setting:change_company_address_title';
  static const addressChangeHeader = 'setting:change_company_address_header';
  static const addressChangeSubHeader =
      'setting:change_company_address_sub_header';
  static const buttonContinue = 'setting:button_continue';
  static const bannerPath = 'app_settings:assets/images/change_company.webp';
  static const updateAddressTitle = 'setting:update_company_address_title';
}

class EditCompanyAddressCoordinator
    extends BaseViewModel<EditCompanyAddressState> {
  final CompanyAddressNavigationHandler _navigationHandler;
  final IAppLocalizationService _localization;
  final IEditCompanyAddressDataProvider _dataProvider;

  EditCompanyAddressCoordinator(
    TaskManager? taskManager,
    this._localization,
    this._navigationHandler,
    this._dataProvider,
  ) : super(
          taskManager,
          EditCompanyAddressState(
            companyNameTitle:
                _localization.getValue(_Constants.companyAddressTitle),
            nameChangeHeader:
                _localization.getValue(_Constants.addressChangeHeader),
            nameChangeSubHeader:
                _localization.getValue(_Constants.addressChangeSubHeader),
            buttonContinue: _localization.getValue(_Constants.buttonContinue),
            bannerPath: _Constants.bannerPath,
          ),
        );

  void initialize() async {
    unawaited(_dataProvider
        .logAnalyticsEvent(AnalyticsEvents.screenViewEditCompanyAddress, {
      AnalyticsEvents.screenViewParam: (EditCompanyAddressScreen).toString(),
    }));
  }

  Future<void> onDocumentSelected() async {
    await _navigationHandler.navigateToUploadDocument(
      documentType: DocumentType.PROOF_OF_ADDRESS,
      flowType: UploadDocumentsMode.corporateKYB,
      documents: [PSDocument(DocumentType.PROOF_OF_ADDRESS)],
      appBarTitle: _localization.getValue(_Constants.updateAddressTitle),
      twoFaEnabled: true,
    );
    NavigationManager.goBack();
  }
}
