import 'package:app_settings/app_settings_module.dart';
import 'package:app_settings/route_manager/app_settings_route_manager.dart';
import 'package:core/navigation/navigation_manager.dart';
import 'package:widget_library/helpers/error/helper/error_helper.dart';

class ChangeEmailInputNavigationHandler with <PERSON>rrorHandler {
  void navigatToSuccess() {
    NavigationManager.navigateTo(
      '${AppSettingsModule.settingsRoute}-${AppSettingsRouteManager.changeEmailSuccess}',
      NavigationType.Push,
    );
  }

  void navigateToBack() => NavigationManager.goBack();

  Future<void> navigateToError(
    String errorCode, {
    String? package,
    String? service,
  }) async {
    await showError(
      package: package,
      service: service,
      errorCode: errorCode,
      onNext: (ErrorButtonType type) {
        switch (type) {
          case ErrorButtonType.TRY_AGAIN:
            NavigationManager.goBack();
            break;
          case ErrorButtonType.CONTACT_SUPPORT:
            contactSupport();
            break;
          case ErrorButtonType.SWIPE_UP_TO_CLOSE:
            NavigationManager.navigateTo(
              'global-home',
              NavigationType.PopUntil,
              arguments: {'tabIndex': 4},
            );
            break;
          default:
            break;
        }
      },
    );
  }
}
