import 'package:data/auth_manager/auth_data.dart';
import 'package:data/auth_manager/auth_manager.dart';
import 'package:dio/dio.dart';
import 'package:feature_2fa_api/feature_2fa_api.dart';
import 'package:feature_login_api/feature_login_api.dart';
import 'package:mocktail/mocktail.dart';
import 'package:sme_rest_api/models/identity.swagger.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';

// Mock classes
class MockAuthManager extends Mock implements IAuthManager {}

class MockLoginInteractor extends Mock implements LoginInteractor {}

class MockIdentityExceptionMapper extends Mock
    implements IdentityExceptionMapper {}

class MockDio extends Mock implements Dio {}

class MockErrorInterceptorHandler extends Mock
    implements ErrorInterceptorHandler {}

class MockDioException extends Mock implements DioException {}

class MockRebirther extends Mock implements Rebirther {}

class FakeDioException extends Fake implements DioException {}

class MockResponse extends Mock implements Response<Object?> {}

class FakeResponse extends Fake implements Response<dynamic> {}

class MockHttpRequestsException extends Mock implements HttpRequestException {
  @override
  String toString() => 'MockHttpRequestsException{}';
}

// ignore: avoid_implementing_value_types
class FakeErrorMessage extends Fake implements ErrorMessage {}

class FakeRequestOptions extends Fake implements RequestOptions {}

class MockAuthData extends Mock implements AuthData {}
