// DO NOT EDIT THIS FILE MANUALLY
// IT WAS GENERATED BY module_map_generator.dart
// FROM OUT TOOLING PACKAGE
//
// THIS FILE CREATED MODULE MAP OF FEATURE DEPENDENCIES FOR
// SME FEATURES.

enum ModulePackages {
  accountFeatureImpl,
  accountUiDesktop,
  currencyFeatureImpl,
  currencyUiDesktop,
  feature2faImpl,
  feature2faUiDesktop,
  feature2faUiDesktopBaas,
  featureCardImpl,
  featureCardUiDesktop,
  featureCounterPartyImpl,
  featureDevMenuUiDesktop,
  featureFaqImpl,
  featureInfoImpl,
  featureLocaleImpl,
  featureLoginImpl,
  featureLoginUiDesktop,
  featureOnboardingImpl,
  featureOnboardingUiDesktop,
  featurePaymentV2Impl,
  featureQuoteImpl,
  featureReminderImpl,
  featureRemoteFileImpl,
  featureStatementUi,
  featureToolsImpl,
  featureToolsUiDesktop,
  paymentV2UiDesktop,
  postLoginFlowImpl,
  statementFeatureImpl,
  transactionUiDesktop,
  wioCommonFeature2faBaasImpl,
  wioCommonFeatureConsentAuthorizationImpl,
  wioCommonFeatureLocationsImpl,
  wioCommonFeatureTinValidationImpl,
  wioCoreUnblockerImpl,
  wioCoreUnblockerUiDesktop,
  wioFeatureAccountClosureUiDesktop,
  wioFeatureBackendDrivenFlowImpl,
  wioFeatureBackendDrivenFlowUiDesktop,
  wioFeatureCasesImpl,
  wioFeatureCashImpl,
  wioFeatureCashUiDesktop,
  wioFeatureCompanyImpl,
  wioFeatureCompanyUiDesktop,
  wioFeatureConsentAuthorizationUiDesktop,
  wioFeatureCqsUiDesktop,
  wioFeatureCreditImpl,
  wioFeatureCreditUiDesktop,
  wioFeatureDashboardImpl,
  wioFeatureDashboardUiDesktop,
  wioFeatureDocumentUploadImpl,
  wioFeatureDocumentUploadUiDesktop,
  wioFeatureDownloadMobileAppImpl,
  wioFeatureDownloadMobileAppUiDesktop,
  wioFeatureEncryptionImpl,
  wioFeatureFaceRecognitionImpl,
  wioFeatureFaceRecognitionUiDesktop,
  wioFeatureFxImpl,
  wioFeatureFxUiDesktop,
  wioFeatureIdentityVerificationImpl,
  wioFeatureIdentityVerificationUiDesktop,
  wioFeatureInvoicesV2Impl,
  wioFeatureInvoicesV2UiDesktop,
  wioFeatureKeyFactStatementImpl,
  wioFeatureKeyFactStatementUiDesktop,
  wioFeatureMuPaymentRequestsImpl,
  wioFeatureMuPaymentRequestsUiDesktop,
  wioFeatureOnboardingEmployeeImpl,
  wioFeatureOnboardingEmployeeUiDesktop,
  wioFeatureOnboardingImpl,
  wioFeatureOnboardingLicensingAuthorityImpl,
  wioFeatureOnboardingLicensingAuthorityUiDesktop,
  wioFeatureOnboardingUiDesktop,
  wioFeaturePhoneContactsImpl,
  wioFeaturePhoneImpl,
  wioFeatureRfiImpl,
  wioFeatureRfiUiDesktop,
  wioFeatureSavingSpaceImpl,
  wioFeatureSavingSpaceUiDesktop,
  wioFeatureScfImpl,
  wioFeatureScfUiDesktop,
  wioFeatureSettingsUiDesktop,
  wioFeatureSofImpl,
  wioFeatureSubscriptionImpl,
  wioFeatureSubscriptionUiDesktop,
  wioFeatureTermsAndConditionsImpl,
  wioFeatureTermsAndConditionsUiDesktop,
  wioFeatureUtapImpl,
  wioFeatureUtapUiDesktop,
  wioFeatureWpsImpl,
  wioFeatureWpsUiDesktop,
  wioSmeErrorHandlerUiDesktop,
  wioSmeFaqUiDesktop,
  wioSmeFormUi,
  wioSmeStatusUiDesktop,
  commonFeatureFxImpl,
  commonFeatureToggleImpl,
  wioFeatureErrorDomainImpl,
  wioFeatureFaqImpl,
  countryFlag,
  wioFeatureBehaviourImpl,
  wioSmeErrorHandlerImpl,
  wioSmeStatusImpl,
  wioFeatureDeliveryImpl,
  wioWalletImpl,
  wioFeatureClipboardManagerImpl,
  commonFeatureToggleUi,
  wioFeatureDebugMenuImpl,
  smeFeatureTransactionsImpl,
  featureSessionMonitoringImpl,
  wioFeatureDeviceInfoCollectionImpl,
  wioCommonFeaturePaymentsImpl,
  wioCommonFeaturePaymentsUi,
  wioFeatureCountryImpl,
  smeFeatureTransactionsUi,
  wioFeatureBehaviourUi,
  wioFeatureTinValidationImpl,
  wioFeatureAccountClosureImpl,
  wioFeatureSettingsImpl,
  wioCommonFeatureDocumentUploadImpl,
  wioFeatureCentralisedQuestionnaireServiceImpl,
  wioFeatureAnnualKycImpl,
  wioCommonFeatureDocumentUploadUi,
  wioFeatureEasyCashImpl,
  wioFeatureLoanImpl,
  wioFeatureAccountImpl,
  wioFeatureMultiuserImpl,
  wioFeatureInvitationImpl,
  wioFeatureTaxNumberImpl
}

class ModuleDependencies {
  final ModulePackages package;
  final Set<ModulePackages> dependsOn;

  const ModuleDependencies({
    required this.package,
    this.dependsOn = const {},
  });
}

const dependenciesMap = {
  ModuleDependencies(
    package: ModulePackages.accountFeatureImpl,
    dependsOn: {
      ModulePackages.commonFeatureFxImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
      ModulePackages.wioFeatureFaqImpl,
      ModulePackages.wioFeatureMuPaymentRequestsImpl,
      ModulePackages.wioFeatureSavingSpaceImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.accountUiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.countryFlag,
      ModulePackages.featureLoginImpl,
      ModulePackages.wioFeatureBackendDrivenFlowImpl,
      ModulePackages.wioFeatureBackendDrivenFlowUiDesktop,
      ModulePackages.wioFeatureBehaviourImpl,
      ModulePackages.wioFeatureCreditImpl,
      ModulePackages.wioFeatureDashboardImpl,
      ModulePackages.wioFeatureMuPaymentRequestsImpl,
      ModulePackages.wioFeatureMuPaymentRequestsUiDesktop,
      ModulePackages.wioFeatureSavingSpaceImpl,
      ModulePackages.wioFeatureUtapImpl,
      ModulePackages.wioFeatureUtapUiDesktop,
      ModulePackages.wioSmeErrorHandlerImpl,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.currencyFeatureImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.currencyUiDesktop,
    dependsOn: {
      ModulePackages.countryFlag,
      ModulePackages.currencyFeatureImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.feature2faImpl,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.featureLoginImpl,
      ModulePackages.wioFeatureEncryptionImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.feature2faUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.feature2faImpl,
      ModulePackages.wioFeatureFaceRecognitionImpl,
      ModulePackages.wioSmeErrorHandlerImpl,
      ModulePackages.wioSmeFaqUiDesktop,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.feature2faUiDesktopBaas,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.feature2faImpl,
      ModulePackages.wioFeatureFaceRecognitionImpl,
      ModulePackages.wioSmeErrorHandlerImpl,
      ModulePackages.wioSmeFaqUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.featureCardImpl,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.feature2faImpl,
      ModulePackages.wioFeatureCreditImpl,
      ModulePackages.wioFeatureDeliveryImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
      ModulePackages.wioWalletImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.featureCardUiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.feature2faImpl,
      ModulePackages.featureCardImpl,
      ModulePackages.wioFeatureBehaviourImpl,
      ModulePackages.wioFeatureClipboardManagerImpl,
      ModulePackages.wioSmeErrorHandlerImpl,
      ModulePackages.wioSmeFaqUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.featureCounterPartyImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.featureDevMenuUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.commonFeatureToggleUi,
      ModulePackages.featureToolsImpl,
      ModulePackages.wioFeatureDebugMenuImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.featureFaqImpl,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.smeFeatureTransactionsImpl,
      ModulePackages.wioFeatureFaqImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.featureInfoImpl,
    dependsOn: {
      ModulePackages.wioFeatureCompanyImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.featureLocaleImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.featureLoginImpl,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.feature2faImpl,
      ModulePackages.wioFeatureBehaviourImpl,
      ModulePackages.wioFeatureFaqImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.featureLoginUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.commonFeatureToggleUi,
      ModulePackages.feature2faImpl,
      ModulePackages.featureLocaleImpl,
      ModulePackages.featureLoginImpl,
      ModulePackages.featureSessionMonitoringImpl,
      ModulePackages.featureToolsImpl,
      ModulePackages.featureToolsUiDesktop,
      ModulePackages.wioCoreUnblockerUiDesktop,
      ModulePackages.wioFeatureBehaviourImpl,
      ModulePackages.wioFeatureDeviceInfoCollectionImpl,
      ModulePackages.wioFeatureOnboardingImpl,
      ModulePackages.wioFeatureScfImpl,
      ModulePackages.wioSmeErrorHandlerImpl,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.featureOnboardingImpl,
    dependsOn: {
      ModulePackages.featureRemoteFileImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.featureOnboardingUiDesktop,
    dependsOn: {
      ModulePackages.featureOnboardingImpl,
      ModulePackages.featureToolsUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.featurePaymentV2Impl,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioCommonFeaturePaymentsImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.featureQuoteImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.featureReminderImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.featureRemoteFileImpl,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.featureStatementUi,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.statementFeatureImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.featureToolsImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.featureToolsUiDesktop,
    dependsOn: {
      ModulePackages.featureLocaleImpl,
      ModulePackages.featureToolsImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.paymentV2UiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.countryFlag,
      ModulePackages.featurePaymentV2Impl,
      ModulePackages.smeFeatureTransactionsImpl,
      ModulePackages.transactionUiDesktop,
      ModulePackages.wioCommonFeaturePaymentsImpl,
      ModulePackages.wioCommonFeaturePaymentsUi,
      ModulePackages.wioFeatureBehaviourImpl,
      ModulePackages.wioFeatureCountryImpl,
      ModulePackages.wioFeatureMuPaymentRequestsImpl,
      ModulePackages.wioFeatureMuPaymentRequestsUiDesktop,
      ModulePackages.wioSmeErrorHandlerImpl,
      ModulePackages.wioSmeFaqUiDesktop,
      ModulePackages.wioSmeFormUi,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.postLoginFlowImpl,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.feature2faImpl,
      ModulePackages.featureLoginImpl,
      ModulePackages.featureSessionMonitoringImpl,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.statementFeatureImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.transactionUiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.smeFeatureTransactionsImpl,
      ModulePackages.smeFeatureTransactionsUi,
      ModulePackages.wioCommonFeaturePaymentsImpl,
      ModulePackages.wioFeatureBehaviourUi,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioCommonFeature2faBaasImpl,
    dependsOn: {
      ModulePackages.wioFeatureErrorDomainImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioCommonFeatureConsentAuthorizationImpl,
    dependsOn: {
      ModulePackages.wioCommonFeature2faBaasImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioCommonFeatureLocationsImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.wioCommonFeatureTinValidationImpl,
    dependsOn: {
      ModulePackages.wioFeatureTinValidationImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioCoreUnblockerImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.wioCoreUnblockerUiDesktop,
    dependsOn: {
      ModulePackages.wioCoreUnblockerImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureAccountClosureUiDesktop,
    dependsOn: {
      ModulePackages.featureLoginImpl,
      ModulePackages.featureStatementUi,
      ModulePackages.wioFeatureAccountClosureImpl,
      ModulePackages.wioFeatureDashboardImpl,
      ModulePackages.wioFeatureSettingsImpl,
      ModulePackages.wioSmeErrorHandlerImpl,
      ModulePackages.wioSmeFaqUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureBackendDrivenFlowImpl,
    dependsOn: {
      ModulePackages.wioCommonFeatureDocumentUploadImpl,
      ModulePackages.wioFeatureCentralisedQuestionnaireServiceImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureBackendDrivenFlowUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureBackendDrivenFlowImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCasesImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCashImpl,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.featureLoginImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCashUiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.smeFeatureTransactionsImpl,
      ModulePackages.wioFeatureCashImpl,
      ModulePackages.wioFeatureDashboardImpl,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCompanyImpl,
    dependsOn: {
      ModulePackages.wioFeatureAnnualKycImpl,
      ModulePackages.wioFeatureCountryImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCompanyUiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.commonFeatureToggleUi,
      ModulePackages.feature2faImpl,
      ModulePackages.featureLoginImpl,
      ModulePackages.postLoginFlowImpl,
      ModulePackages.wioFeatureCompanyImpl,
      ModulePackages.wioSmeErrorHandlerImpl,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureConsentAuthorizationUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.feature2faImpl,
      ModulePackages.featureLoginImpl,
      ModulePackages.wioCommonFeatureConsentAuthorizationImpl,
      ModulePackages.wioSmeErrorHandlerImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCqsUiDesktop,
    dependsOn: {
      ModulePackages.featureLoginImpl,
      ModulePackages.featureRemoteFileImpl,
      ModulePackages.wioCommonFeatureDocumentUploadImpl,
      ModulePackages.wioCommonFeatureDocumentUploadUi,
      ModulePackages.wioFeatureCentralisedQuestionnaireServiceImpl,
      ModulePackages.wioFeatureSubscriptionImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCreditImpl,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.smeFeatureTransactionsImpl,
      ModulePackages.wioFeatureEasyCashImpl,
      ModulePackages.wioFeatureLoanImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCreditUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureCreditImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureDashboardImpl,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureDashboardUiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.accountUiDesktop,
      ModulePackages.commonFeatureFxImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.countryFlag,
      ModulePackages.feature2faImpl,
      ModulePackages.featureLoginImpl,
      ModulePackages.featurePaymentV2Impl,
      ModulePackages.featureStatementUi,
      ModulePackages.featureToolsUiDesktop,
      ModulePackages.smeFeatureTransactionsImpl,
      ModulePackages.transactionUiDesktop,
      ModulePackages.wioCommonFeaturePaymentsImpl,
      ModulePackages.wioFeatureBackendDrivenFlowImpl,
      ModulePackages.wioFeatureBackendDrivenFlowUiDesktop,
      ModulePackages.wioFeatureBehaviourImpl,
      ModulePackages.wioFeatureBehaviourUi,
      ModulePackages.wioFeatureCashImpl,
      ModulePackages.wioFeatureCompanyImpl,
      ModulePackages.wioFeatureCompanyUiDesktop,
      ModulePackages.wioFeatureCreditImpl,
      ModulePackages.wioFeatureDashboardImpl,
      ModulePackages.wioFeatureLoanImpl,
      ModulePackages.wioFeatureMuPaymentRequestsImpl,
      ModulePackages.wioFeatureMuPaymentRequestsUiDesktop,
      ModulePackages.wioFeatureOnboardingImpl,
      ModulePackages.wioFeatureSavingSpaceImpl,
      ModulePackages.wioFeatureUtapImpl,
      ModulePackages.wioFeatureUtapUiDesktop,
      ModulePackages.wioSmeErrorHandlerImpl,
      ModulePackages.wioSmeFaqUiDesktop,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureDocumentUploadImpl,
    dependsOn: {
      ModulePackages.featureRemoteFileImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureDocumentUploadUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureDocumentUploadImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureDownloadMobileAppImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureDownloadMobileAppUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureDownloadMobileAppImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureEncryptionImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureFaceRecognitionImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureFaceRecognitionUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureFaceRecognitionImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureFxImpl,
    dependsOn: {
      ModulePackages.commonFeatureFxImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureAccountImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureFxUiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.commonFeatureFxImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.countryFlag,
      ModulePackages.feature2faImpl,
      ModulePackages.feature2faUiDesktop,
      ModulePackages.wioFeatureAccountImpl,
      ModulePackages.wioFeatureDashboardImpl,
      ModulePackages.wioFeatureMuPaymentRequestsImpl,
      ModulePackages.wioFeatureMuPaymentRequestsUiDesktop,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureIdentityVerificationImpl,
    dependsOn: {
      ModulePackages.wioFeatureOnboardingLicensingAuthorityImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureIdentityVerificationUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureIdentityVerificationImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureInvoicesV2Impl,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.featureLocaleImpl,
      ModulePackages.featureRemoteFileImpl,
      ModulePackages.wioFeatureCountryImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureInvoicesV2UiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.countryFlag,
      ModulePackages.featureCounterPartyImpl,
      ModulePackages.smeFeatureTransactionsImpl,
      ModulePackages.wioFeatureBehaviourImpl,
      ModulePackages.wioFeatureCountryImpl,
      ModulePackages.wioFeatureDashboardImpl,
      ModulePackages.wioFeatureInvoicesV2Impl,
      ModulePackages.wioSmeFaqUiDesktop,
      ModulePackages.wioSmeFormUi,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureKeyFactStatementImpl,
    dependsOn: {
      ModulePackages.featureLoginImpl,
      ModulePackages.featureRemoteFileImpl,
      ModulePackages.wioFeatureBehaviourImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureKeyFactStatementUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureKeyFactStatementImpl,
      ModulePackages.wioSmeFaqUiDesktop,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureMuPaymentRequestsImpl,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.feature2faImpl,
      ModulePackages.featureRemoteFileImpl,
      ModulePackages.wioCommonFeaturePaymentsImpl,
      ModulePackages.wioFeatureMultiuserImpl,
      ModulePackages.wioFeatureScfImpl,
      ModulePackages.wioFeatureWpsImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureMuPaymentRequestsUiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.commonFeatureFxImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.feature2faImpl,
      ModulePackages.featureInfoImpl,
      ModulePackages.featureLoginImpl,
      ModulePackages.wioCommonFeaturePaymentsImpl,
      ModulePackages.wioFeatureBehaviourImpl,
      ModulePackages.wioFeatureBehaviourUi,
      ModulePackages.wioFeatureCountryImpl,
      ModulePackages.wioFeatureDashboardImpl,
      ModulePackages.wioFeatureMuPaymentRequestsImpl,
      ModulePackages.wioFeatureMultiuserImpl,
      ModulePackages.wioFeatureScfImpl,
      ModulePackages.wioSmeFaqUiDesktop,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureOnboardingEmployeeImpl,
    dependsOn: {
      ModulePackages.featureLoginImpl,
      ModulePackages.featureRemoteFileImpl,
      ModulePackages.wioFeatureBehaviourImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureOnboardingEmployeeUiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.feature2faImpl,
      ModulePackages.featureLoginImpl,
      ModulePackages.wioFeatureBehaviourImpl,
      ModulePackages.wioFeatureBehaviourUi,
      ModulePackages.wioFeatureDashboardImpl,
      ModulePackages.wioFeatureDownloadMobileAppImpl,
      ModulePackages.wioFeatureOnboardingImpl,
      ModulePackages.wioFeatureOnboardingEmployeeImpl,
      ModulePackages.wioSmeErrorHandlerImpl,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureOnboardingImpl,
    dependsOn: {
      ModulePackages.feature2faImpl,
      ModulePackages.featureToolsImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureOnboardingLicensingAuthorityImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureOnboardingLicensingAuthorityUiDesktop,
    dependsOn: {
      ModulePackages.feature2faImpl,
      ModulePackages.featureLoginImpl,
      ModulePackages.wioFeatureIdentityVerificationImpl,
      ModulePackages.wioFeatureKeyFactStatementImpl,
      ModulePackages.wioFeatureOnboardingImpl,
      ModulePackages.wioFeatureOnboardingLicensingAuthorityImpl,
      ModulePackages.wioFeatureSubscriptionImpl,
      ModulePackages.wioFeatureTermsAndConditionsImpl,
      ModulePackages.wioSmeErrorHandlerImpl,
      ModulePackages.wioSmeFaqUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureOnboardingUiDesktop,
    dependsOn: {
      ModulePackages.featureLoginImpl,
      ModulePackages.featureToolsUiDesktop,
      ModulePackages.wioFeatureCentralisedQuestionnaireServiceImpl,
      ModulePackages.wioFeatureOnboardingImpl,
      ModulePackages.wioSmeFaqUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeaturePhoneContactsImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeaturePhoneImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureRfiImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureRfiUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureRfiImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureSavingSpaceImpl,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureSavingSpaceUiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.commonFeatureFxImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.countryFlag,
      ModulePackages.feature2faImpl,
      ModulePackages.smeFeatureTransactionsImpl,
      ModulePackages.transactionUiDesktop,
      ModulePackages.wioFeatureBehaviourUi,
      ModulePackages.wioFeatureMuPaymentRequestsImpl,
      ModulePackages.wioFeatureMuPaymentRequestsUiDesktop,
      ModulePackages.wioFeatureSavingSpaceImpl,
      ModulePackages.wioSmeFaqUiDesktop,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureScfImpl,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureMuPaymentRequestsImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureScfUiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.featureLoginImpl,
      ModulePackages.wioFeatureDashboardImpl,
      ModulePackages.wioFeatureMuPaymentRequestsImpl,
      ModulePackages.wioFeatureMuPaymentRequestsUiDesktop,
      ModulePackages.wioFeatureScfImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureSettingsUiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.feature2faImpl,
      ModulePackages.featureInfoImpl,
      ModulePackages.featureLoginImpl,
      ModulePackages.featureStatementUi,
      ModulePackages.wioFeatureAccountClosureImpl,
      ModulePackages.wioFeatureAnnualKycImpl,
      ModulePackages.wioFeatureBehaviourImpl,
      ModulePackages.wioFeatureBehaviourUi,
      ModulePackages.wioFeatureCompanyImpl,
      ModulePackages.wioFeatureConsentAuthorizationUiDesktop,
      ModulePackages.wioFeatureDocumentUploadImpl,
      ModulePackages.wioFeatureIdentityVerificationImpl,
      ModulePackages.wioFeatureInvitationImpl,
      ModulePackages.wioFeatureKeyFactStatementImpl,
      ModulePackages.wioFeatureKeyFactStatementUiDesktop,
      ModulePackages.wioFeatureMuPaymentRequestsImpl,
      ModulePackages.wioFeatureMultiuserImpl,
      ModulePackages.wioFeatureScfImpl,
      ModulePackages.wioFeatureSettingsImpl,
      ModulePackages.wioFeatureSubscriptionImpl,
      ModulePackages.wioFeatureTaxNumberImpl,
      ModulePackages.wioFeatureTermsAndConditionsImpl,
      ModulePackages.wioFeatureTermsAndConditionsUiDesktop,
      ModulePackages.wioSmeErrorHandlerImpl,
      ModulePackages.wioSmeFaqUiDesktop,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureSofImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureSubscriptionImpl,
    dependsOn: {
      ModulePackages.wioFeatureOnboardingLicensingAuthorityImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureSubscriptionUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureSubscriptionImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureTermsAndConditionsImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureTermsAndConditionsUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureInvitationImpl,
      ModulePackages.wioFeatureTermsAndConditionsImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureUtapImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureUtapUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureSettingsImpl,
      ModulePackages.wioFeatureUtapImpl,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureWpsImpl,
    dependsOn: {
      ModulePackages.wioFeatureMuPaymentRequestsImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureWpsUiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureDashboardImpl,
      ModulePackages.wioFeatureMuPaymentRequestsImpl,
      ModulePackages.wioFeatureMuPaymentRequestsUiDesktop,
      ModulePackages.wioFeatureWpsImpl,
      ModulePackages.wioSmeFaqUiDesktop,
      ModulePackages.wioSmeFormUi,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioSmeErrorHandlerUiDesktop,
    dependsOn: {
      ModulePackages.feature2faImpl,
      ModulePackages.wioCommonFeature2faBaasImpl,
      ModulePackages.wioCommonFeatureConsentAuthorizationImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
      ModulePackages.wioSmeErrorHandlerImpl,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioSmeFaqUiDesktop,
    dependsOn: {
      ModulePackages.accountFeatureImpl,
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.countryFlag,
      ModulePackages.wioFeatureFaqImpl,
      ModulePackages.wioFeatureMultiuserImpl,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioSmeFormUi,
  ),
  ModuleDependencies(
    package: ModulePackages.wioSmeStatusUiDesktop,
    dependsOn: {
      ModulePackages.wioSmeFaqUiDesktop,
      ModulePackages.wioSmeStatusImpl,
    },
  ),
};
