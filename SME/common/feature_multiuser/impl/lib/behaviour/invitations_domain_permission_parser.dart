import 'package:wio_feature_behaviour_api/feature_behaviour_api.dart';
import 'package:wio_sme_behaviour/wio_sme_behaviour.dart';

class InvitationsDomainPermissionParser implements BehaviourPermissionParser {
  const InvitationsDomainPermissionParser();

  @override
  Set<BehaviourPermission> parse({
    required Set<String> permissionCodes,
  }) {
    final result = <BehaviourPermission>{};
    if (permissionCodes.contains('invitations:read')) {
      result.add(const InvitationsReadPermission());
    }
    if (permissionCodes.contains('invitations:write')) {
      result.add(const InvitationsWritePermission());
    }
    if (permissionCodes
        .contains('invitations:add-team-member:request-create')) {
      result.add(const InvitationsAddTeamMemberReqCreatePermission());
    }
    if (permissionCodes
        .contains('invitations:delete-team-member:request-create')) {
      result.add(const InvitationsDelTeamMemberReqCreatePermission());
    }

    return result;
  }
}
