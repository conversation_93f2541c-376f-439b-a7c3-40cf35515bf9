import 'package:wio_feature_behaviour_api/feature_behaviour_api.dart';
import 'package:wio_sme_behaviour/wio_sme_behaviour.dart';

class PricingDomainPermissionParser implements BehaviourPermissionParser {
  const PricingDomainPermissionParser();

  @override
  Set<BehaviourPermission> parse({
    required Set<String> permissionCodes,
  }) {
    final result = <BehaviourPermission>{};
    if (permissionCodes.contains('pricing:read')) {
      result.add(const PricingReadPermission());
    }
    if (permissionCodes.contains('pricing:write')) {
      result.add(const PricingWritePermission());
    }
    if (permissionCodes.contains('pricing:subscription:request-create')) {
      result.add(const PricingSubscriptionRequestCreatePermission());
    }

    return result;
  }
}
