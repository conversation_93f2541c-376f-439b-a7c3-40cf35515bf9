import 'package:sme_graphql_api/graphql_api.dart' as ggl;
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_mu_payment_requests_api/feature_mu_payment_requests_api.dart';

class SavingSpaceStub {
  static const amount = 10000.0;
  static const currency = ggl.CurrencyCode.aed;
  static const destinationAccountId = 'destinationAccountId';
  static const savingSpaceName = '';
  static const sourceAccountId = 'sourceAccountId';
  static const transferOperationType = ggl.TransferType.kw$IN;

  static final ggl
      .GetTransferDepositRequest$Query$MuTransferDepositRequestSummary
      transferDepositRequestSummary =
      ggl.GetTransferDepositRequest$Query$MuTransferDepositRequestSummary()
        ..amount = amount
        ..currency = currency
        ..destinationAccountId = destinationAccountId
        ..savingSpaceName = savingSpaceName
        ..sourceAccountId = sourceAccountId
        ..transferOperationType = transferOperationType;

  static final ggl.GetTransferDepositRequest$Query transferDepositRequestQuery =
      ggl.GetTransferDepositRequest$Query()
        ..getTransferDepositRequest = transferDepositRequestSummary;

  static final internalTransferDomainSummary = InternalTransferDomainSummary(
    operationType: InternalTransferOperation.income,
    amount: Money.fromNumWithCurrency(10000.0, Currency.aed),
    destinationAccountId: 'destinationAccountId',
    savingSpaceName: '',
    sourceAccountId: 'sourceAccountId',
  );

  static const name = 'SavingSpace name';
  static const id = 'id';
  static const businessId = 'businessId';
  static const operation = ggl.SavingSpaceOperation.savingSpaceCreation;
  static final date = DateTime.now().add(const Duration(days: 365));

  static final savingSpaceSummary =
      // ignore: lines_longer_than_80_chars
      ggl.GetSavingSpaceDomainSummary$Query$SavingSpaceAccountDomainSummary$SavingSpaceSummary()
        ..currency = currency
        ..goalAmount = amount
        ..id = id
        ..goalTargetDate = date.toString()
        ..name = name;

  static final savingSpaceAccountDomainSummary =
      ggl.GetSavingSpaceDomainSummary$Query$SavingSpaceAccountDomainSummary()
        ..domainId = id
        ..businessId = businessId
        ..operation = operation
        ..muSavingSpaceSummary = savingSpaceSummary;

  static final savingSpaceDomainSummary =
      ggl.GetSavingSpaceDomainSummary$Query()
        ..getSavingSpaceDomainSummary = savingSpaceAccountDomainSummary;

  static final muSavingSpaceAccountDomainSummary =
      SavingSpaceAccountDomainSummary(
    id: 'id',
    businessId: 'businessId',
    savingSpaceSummary: SavingSpaceSummary(
      goalAmount: Money.fromNumWithCurrency(500, Currency.aed),
      id: 'id',
      name: 'SavingSpace name',
      goalTargetDate: date,
    ),
    operation: SavingSpaceOperation.open,
  );
}
