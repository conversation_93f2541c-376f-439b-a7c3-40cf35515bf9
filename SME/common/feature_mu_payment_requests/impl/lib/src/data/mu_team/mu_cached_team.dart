import 'dart:async';

import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_mu_payment_requests_api/domain/models/filters/mu_team_members_params.dart';
import 'package:wio_feature_mu_payment_requests_api/domain/models/team_member_input.dart';

typedef CachedUsers = (DateTime?, List<TeamMemberInput>?);

abstract class MuCachedTeam {
  List<TeamMemberInput>? getTeamMembers({
    required MuTeamMembersParams muTeamMembersParams,
  });

  void updateTeamMembers({
    required List<TeamMemberInput> teamMembers,
    required MuTeamMembersParams params,
  });

  Future<void> clear();
}

class MuCachedTeamImpl implements MuCachedTeam {
  static const _teamMembersTTL = Duration(minutes: 10);
  final TimeProvider _timeProvider;
  CachedUsers? _onlyActiveUsers;

  /// Any type of MU entity related to multiuser.
  ///
  /// Either invitation or real user.
  CachedUsers? _multiUsers;

  MuCachedTeamImpl({
    required TimeProvider timeProvider,
    CachedUsers? onlyActiveUsers,
    CachedUsers? multiUsers,
  })  : _onlyActiveUsers = onlyActiveUsers,
        _multiUsers = multiUsers,
        _timeProvider = timeProvider;

  @override
  List<TeamMemberInput>? getTeamMembers({
    required MuTeamMembersParams muTeamMembersParams,
  }) {
    final cachedUsers = switch (muTeamMembersParams) {
      CustomTeamMembersParams() => null,
      AllUsersMostInvitations() => null,
      OnlyActiveParams() => _onlyActiveUsers,
      IsMultiUserUsedParams() => _multiUsers,
    };

    final dateTime = cachedUsers?.$1;
    if (dateTime == null) return null;

    if (_isCacheActual(dateTime)) {
      return cachedUsers?.$2;
    } else {
      clear();
    }

    return null;
  }

  @override
  void updateTeamMembers({
    required List<TeamMemberInput> teamMembers,
    required MuTeamMembersParams params,
  }) {
    if (teamMembers.isNotEmpty) {
      switch (params) {
        case CustomTeamMembersParams():
        case AllUsersMostInvitations():
        case OnlyActiveParams():
          final newTeamMembers = List<TeamMemberInput>.from(
            _onlyActiveUsers?.$2 ?? <TeamMemberInput>[],
          )
            ..addAll(teamMembers)
            ..toSet().toList();
          _onlyActiveUsers = (DateTime.now(), newTeamMembers);
        case IsMultiUserUsedParams():
          _multiUsers = (DateTime.now(), teamMembers);
      }
    }
  }

  @override
  Future<void> clear() async {
    _onlyActiveUsers = null;
    _multiUsers = null;
  }

  bool _isCacheActual(DateTime createdDateTime) {
    final diff = _timeProvider.getDateTime().difference(createdDateTime);

    return diff < _teamMembersTTL;
  }
}
