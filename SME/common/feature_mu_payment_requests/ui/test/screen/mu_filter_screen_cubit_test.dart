import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:ui/l10n/common_localizations.g.dart';
import 'package:ui/ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_mu_payment_requests_api/feature_mu_payment_requests_api.dart';
import 'package:wio_feature_mu_payment_requests_ui/src/navigation/filter_navigation_result.dart';
import 'package:wio_feature_mu_payment_requests_ui/src/screens/filters/mu_filter_screen_cubit.dart';
import 'package:wio_feature_mu_payment_requests_ui/src/screens/filters/mu_requests_filter_state.dart';
import 'package:wio_feature_mu_payment_requests_ui/src/screens/filters/utils.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late MuFilterScreenCubit muFilterScreenCubit;
  late NavigationProvider mockNavigationProvider;
  late MuTeamInteractor mockMuTeamInteractor;
  late CommonLocalizations mockCommonLocalizations;
  final selectedTeamMembers = {
    TestEntities.firstMember: false,
    TestEntities.secondMember: false,
    TestEntities.lastMember: false,
  };
  final teamMembers = [
    TestEntities.firstMember,
    TestEntities.secondMember,
    TestEntities.lastMember,
  ];

  final selectedStatus = {
    MuRequestStatus.approved: false,
    MuRequestStatus.active: true,
    MuRequestStatus.rejected: true,
  };

  final selectedDomainTypes = {
    MuDomainType.fx: false,
    MuDomainType.creditContract: true,
    MuDomainType.savingSpaces: false,
  };

  final selectedTeamMembersReset = {
    TestEntities.firstMember: false,
    TestEntities.secondMember: false,
    TestEntities.lastMember: false,
  };

  final selectedStatusReset = {
    MuRequestStatus.approved: false,
    MuRequestStatus.active: false,
    MuRequestStatus.rejected: false,
  };

  final selectedDomainTypesReset = {
    MuDomainType.fx: false,
    MuDomainType.creditContract: false,
    MuDomainType.savingSpaces: false,
  };

  final newTeamMembers = {
    TestEntities.firstMember: true,
  };

  final newStatus = {
    MuRequestStatus.draft: true,
  };

  final newDomainType = {
    MuDomainType.internal: false,
  };

  setUp(() {
    mockNavigationProvider = MockNavigationProvider();
    mockMuTeamInteractor = MockMuTeamInteractor();
    mockCommonLocalizations = MockCommonLocalizations();
    muFilterScreenCubit = MuFilterScreenCubit(
      navigationProvider: mockNavigationProvider,
      muTeamInteractor: mockMuTeamInteractor,
      commonLocalizations: mockCommonLocalizations,
    );
  });

  setUpAll(() {
    registerFallbackValue(
      const MuTeamMembersParams.onlyActive(),
    );
    registerFallbackValue(
      const FilterNavigationResult(
        selectedDomainType: {},
        selectedStatus: {},
        selectedTeamMembers: {},
      ),
    );
  });

  test('When muFilterScreenCubit is created then initial state expected', () {
    // Act & Assert
    expect(muFilterScreenCubit.state, const MuRequestFilterState.initial());
  });

  group('Initialization Tests', () {
    blocTest<MuFilterScreenCubit, MuRequestFilterState>(
      'When muFilterScreenCubit is initialize with empty DomainTypes and Status'
      ' then team members in state change expected',
      // Arrange
      build: () => muFilterScreenCubit,
      seed: () => const MuRequestFilterState.initial(),
      setUp: () => when(
        () => mockMuTeamInteractor.getTeamMembers(
          muTeamMembersParams: any(named: 'muTeamMembersParams'),
        ),
      ).justAnswerAsync(teamMembers),

      // Act
      act: (cubit) async => cubit.init(
        selectedTeamMembers: {},
        selectedStatuses: {},
        selectedDomainTypes: {},
      ),

      // Assert
      expect: () => <MuRequestFilterState>[
        const MuRequestFilterState.loading(),
        MuRequestFilterState.initialized(
          selectedDomainType: {},
          selectedStatus: {},
          selectedTeamMembers: selectedTeamMembers,
        ),
      ],
    );

    blocTest<MuFilterScreenCubit, MuRequestFilterState>(
      'When muFilterScreenCubit is initialize and error ouccr '
      'then error is handled from withErrorMessage expected',
      // Arrange
      build: () => muFilterScreenCubit,
      seed: () => const MuRequestFilterState.initial(),
      setUp: () => [
        when(
          () => mockMuTeamInteractor.getTeamMembers(
            muTeamMembersParams: const MuTeamMembersParams.onlyActive(),
          ),
        ).justThrowAsync(
          Exception('Test Error'),
        ),
        when(
          () => mockCommonLocalizations.common_error_message,
        ).thenReturn('Test Error'),
        when(
          () => mockNavigationProvider.goBack(
            any(),
          ),
        ).justComplete(),
      ],

      // Act
      act: (cubit) async => cubit.init(
        selectedTeamMembers: {},
        selectedStatuses: {},
        selectedDomainTypes: {},
      ),

      // Assert
      expect: () => <MuRequestFilterState>[
        const MuRequestFilterState.loading(),
      ],
      verify: (_) => verifyNever(
        () => mockNavigationProvider.goBack(
          any(),
        ),
      ),
    );
  });

  group('toggleFilter Tests', () {
    blocTest<MuFilterScreenCubit, MuRequestFilterState>(
      'When cubit state is initial and called toggleFilter with empty data '
      'then initialized state with empty detail is expected',
      // Arrange
      build: () => muFilterScreenCubit,
      seed: () => const MuRequestFilterState.initial(),

      // Act
      act: (cubit) => cubit.toggleFilter(),

      // Assert
      expect: () => <MuRequestFilterState>[
        const MuRequestFilterState.initialized(
          selectedDomainType: {},
          selectedStatus: {},
          selectedTeamMembers: {},
        ),
      ],
    );

    blocTest<MuFilterScreenCubit, MuRequestFilterState>(
      'When cubit state is initialized and called toggleFilter with data '
      'then initialized state with extended detail is expected',
      // Arrange
      build: () => muFilterScreenCubit,
      seed: () => MuRequestFilterState.initialized(
        selectedDomainType: selectedDomainTypes,
        selectedStatus: selectedStatus,
        selectedTeamMembers: selectedTeamMembers,
      ),

      // Act
      act: (cubit) => cubit.toggleFilter(
        domainTypes: newDomainType,
        status: newStatus,
        teamMembers: newTeamMembers,
      ),

      // Assert
      expect: () => <MuRequestFilterState>[
        MuRequestFilterState.initialized(
          selectedDomainType: combineMaps<MuDomainType>(
            selectedDomainTypes,
            newDomainType,
          ),
          selectedStatus: combineMaps<MuRequestStatus>(
            selectedStatus,
            newStatus,
          ),
          selectedTeamMembers: combineMaps<TeamMemberInput>(
            selectedTeamMembers,
            newTeamMembers,
          ),
        ),
      ],
    );
  });

  blocTest<MuFilterScreenCubit, MuRequestFilterState>(
    'When cubit state is initialized and called applyFilters '
    'then navigation with filter is expected',
    // Arrange
    build: () => muFilterScreenCubit,
    seed: () => MuRequestFilterState.initialized(
      selectedDomainType: selectedDomainTypes,
      selectedStatus: selectedStatus,
      selectedTeamMembers: selectedTeamMembers,
    ),
    setUp: () => when(
      () => mockNavigationProvider.goBack(
        any(),
      ),
    ).justComplete(),

    // Act
    act: (cubit) => cubit.applyFilters(),

    // Assert
    verify: (_) => verify(
      () => mockNavigationProvider.goBack(
        any(),
      ),
    ).calledOnce,
  );

  blocTest<MuFilterScreenCubit, MuRequestFilterState>(
    'When cubit state is initialized and called resetFilters '
    'then initialized state with reset filters is expected',
    // Arrange
    build: () => muFilterScreenCubit,
    seed: () => MuRequestFilterState.initialized(
      selectedDomainType: selectedDomainTypes,
      selectedStatus: selectedStatus,
      selectedTeamMembers: selectedTeamMembers,
    ),
    setUp: () => when(
      () => mockNavigationProvider.goBack(
        any(),
      ),
    ).justComplete(),

    // Act
    act: (cubit) => cubit.resetFilters(),

    // Assert
    expect: () => <MuRequestFilterState>[
      MuRequestFilterState.initialized(
        selectedDomainType: selectedDomainTypesReset,
        selectedStatus: selectedStatusReset,
        selectedTeamMembers: selectedTeamMembersReset,
      ),
    ],
  );
}
