import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_feature_mu_payment_requests_ui/src/navigation/config/approvers_bottom_sheet_navigation_config.dart';
import 'package:wio_feature_mu_payment_requests_ui/src/widgets/approvers_widget/approvers_cubit.dart';
import 'package:wio_feature_mu_payment_requests_ui/src/widgets/approvers_widget/approvers_state.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late ApproversCubit approversCubit;
  late MockNavigationProvider mockNavigationProvider;
  final approverList = [
    TestEntities.firstApprover,
    TestEntities.secondApprover,
    TestEntities.lastApprover,
  ];

  setUp(() {
    mockNavigationProvider = MockNavigationProvider();
    approversCubit = ApproversCubit(
      navigationProvider: mockNavigationProvider,
    );
  });

  setUpAll(() {
    registerFallbackValue(
      const ApproversBottomSheetNavigationConfig(
        selectedApprovers: [],
      ),
    );
  });

  test('When approver cubit is created then empty state is expected', () {
    // Act & Assert
    expect(approversCubit.state, const ApproversState.empty());
  });

  blocTest<ApproversCubit, ApproversState>(
    'When approver cubit is initialize with approver list '
    'then selected approver state is expected',
    // Arrange
    build: () => approversCubit,

    // Act
    act: (cubit) => cubit.init(approverList),

    // Assert
    expect: () => <ApproversState>[
      ApproversState.selected(
        selectedApprovers: approverList,
      ),
    ],
  );

  blocTest<ApproversCubit, ApproversState>(
    'When approver cubit state is empty '
    'then showApproversBottomSheet not open bottom sheet',
    // Arrange
    build: () => approversCubit,
    seed: () => const ApproversState.empty(),

    // Act
    act: (cubit) => cubit.showApproversBottomSheet(),

    // Assert
    verify: (_) => verifyNever(
      () => mockNavigationProvider.showBottomSheet<void>(any()),
    ),
  );

  blocTest<ApproversCubit, ApproversState>(
    'When approver cubit is initialize with approver list '
    'then showApproversBottomSheet show bottom sheet with approvers',
    // Arrange
    build: () => approversCubit,
    seed: () => ApproversState.selected(selectedApprovers: approverList),
    setUp: () => when(() => mockNavigationProvider.showBottomSheet<void>(any()))
        .justCompleteAsync(),

    // Act
    act: (cubit) => cubit.showApproversBottomSheet(),

    // Assert
    verify: (_) => verify(
      () => mockNavigationProvider.showBottomSheet<void>(any()),
    ).calledOnce,
  );
}
