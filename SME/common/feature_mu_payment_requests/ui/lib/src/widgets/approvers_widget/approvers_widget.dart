import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_feature_mu_payment_requests_api/feature_mu_payment_requests_api.dart';
import 'package:wio_feature_mu_payment_requests_ui/locale/multiuser_ui_localizations.g.dart';
import 'package:wio_feature_mu_payment_requests_ui/src/widgets/approvers_widget/approvers_cubit.dart';
import 'package:wio_feature_mu_payment_requests_ui/src/widgets/approvers_widget/approvers_state.dart';
import 'package:wio_feature_mu_payment_requests_ui/src/widgets/input_list_item/inpit_list_item.dart';

class ApproversWidget extends StatelessWidget {
  final List<MuApproverDetails> approvers;

  const ApproversWidget({
    required this.approvers,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ApproversCubit>(
      create: (context) =>
          DependencyProvider.get<ApproversCubit>()..init(approvers),
      child: _ApproversTileList(),
    );
  }
}

class _ApproversTileList extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ApproversCubit, ApproversState>(
      builder: (context, state) {
        final approversCubit = BlocProvider.of<ApproversCubit>(context);
        final l10n = MultiuserUiLocalizations.of(context);

        return state.map(
          empty: (_) => const SizedBox.shrink(),
          selected: (selectedState) => InkWell(
            onTap: approversCubit.showApproversBottomSheet,
            child: InputListItem(
              title: l10n.approvers,
              onClick: approversCubit.showApproversBottomSheet,
              contentPadding: EdgeInsets.zero,
              content: ApprovalDetailsView(
                headerModel: ApprovalDetailsCardHeaderModel.overview(
                  statusTileGroup: StatusTileGroup(
                    statusTiles: selectedState.selectedApprovers
                        .map<AvatarStatusTile>(
                          (e) => AvatarStatusTile(
                            avatarModel: AvatarModel(
                              text: e.possibleName.acronym(),
                              type: AvatarType.empty,
                            ),
                            state: StatusTileState.inReview,
                          ),
                        )
                        .toList(growable: false),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
