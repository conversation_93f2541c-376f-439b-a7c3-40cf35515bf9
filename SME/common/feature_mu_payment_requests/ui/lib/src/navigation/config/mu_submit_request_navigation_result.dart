import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_mu_payment_requests_api/feature_mu_payment_requests_api.dart';

part 'mu_submit_request_navigation_result.freezed.dart';

@freezed
class MuSubmitRequestNavigationResult with _$MuSubmitRequestNavigationResult {
  const factory MuSubmitRequestNavigationResult({
    required MuDomain updatedResult,
  }) = _MuSubmitRequestNavigationResult;
}
