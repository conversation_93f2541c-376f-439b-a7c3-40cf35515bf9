// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'approvers_bottom_sheet_navigation_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ApproversBottomSheetNavigationConfig {
  List<MuApproverDetails> get selectedApprovers =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ApproversBottomSheetNavigationConfigCopyWith<
          ApproversBottomSheetNavigationConfig>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ApproversBottomSheetNavigationConfigCopyWith<$Res> {
  factory $ApproversBottomSheetNavigationConfigCopyWith(
          ApproversBottomSheetNavigationConfig value,
          $Res Function(ApproversBottomSheetNavigationConfig) then) =
      _$ApproversBottomSheetNavigationConfigCopyWithImpl<$Res,
          ApproversBottomSheetNavigationConfig>;
  @useResult
  $Res call({List<MuApproverDetails> selectedApprovers});
}

/// @nodoc
class _$ApproversBottomSheetNavigationConfigCopyWithImpl<$Res,
        $Val extends ApproversBottomSheetNavigationConfig>
    implements $ApproversBottomSheetNavigationConfigCopyWith<$Res> {
  _$ApproversBottomSheetNavigationConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedApprovers = null,
  }) {
    return _then(_value.copyWith(
      selectedApprovers: null == selectedApprovers
          ? _value.selectedApprovers
          : selectedApprovers // ignore: cast_nullable_to_non_nullable
              as List<MuApproverDetails>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ApproversBottomSheetNavigationConfigCopyWith<$Res>
    implements $ApproversBottomSheetNavigationConfigCopyWith<$Res> {
  factory _$$_ApproversBottomSheetNavigationConfigCopyWith(
          _$_ApproversBottomSheetNavigationConfig value,
          $Res Function(_$_ApproversBottomSheetNavigationConfig) then) =
      __$$_ApproversBottomSheetNavigationConfigCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<MuApproverDetails> selectedApprovers});
}

/// @nodoc
class __$$_ApproversBottomSheetNavigationConfigCopyWithImpl<$Res>
    extends _$ApproversBottomSheetNavigationConfigCopyWithImpl<$Res,
        _$_ApproversBottomSheetNavigationConfig>
    implements _$$_ApproversBottomSheetNavigationConfigCopyWith<$Res> {
  __$$_ApproversBottomSheetNavigationConfigCopyWithImpl(
      _$_ApproversBottomSheetNavigationConfig _value,
      $Res Function(_$_ApproversBottomSheetNavigationConfig) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedApprovers = null,
  }) {
    return _then(_$_ApproversBottomSheetNavigationConfig(
      selectedApprovers: null == selectedApprovers
          ? _value._selectedApprovers
          : selectedApprovers // ignore: cast_nullable_to_non_nullable
              as List<MuApproverDetails>,
    ));
  }
}

/// @nodoc

class _$_ApproversBottomSheetNavigationConfig
    extends _ApproversBottomSheetNavigationConfig {
  const _$_ApproversBottomSheetNavigationConfig(
      {required final List<MuApproverDetails> selectedApprovers})
      : _selectedApprovers = selectedApprovers,
        super._();

  final List<MuApproverDetails> _selectedApprovers;
  @override
  List<MuApproverDetails> get selectedApprovers {
    if (_selectedApprovers is EqualUnmodifiableListView)
      return _selectedApprovers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedApprovers);
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ApproversBottomSheetNavigationConfig &&
            const DeepCollectionEquality()
                .equals(other._selectedApprovers, _selectedApprovers));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_selectedApprovers));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ApproversBottomSheetNavigationConfigCopyWith<
          _$_ApproversBottomSheetNavigationConfig>
      get copyWith => __$$_ApproversBottomSheetNavigationConfigCopyWithImpl<
          _$_ApproversBottomSheetNavigationConfig>(this, _$identity);
}

abstract class _ApproversBottomSheetNavigationConfig
    extends ApproversBottomSheetNavigationConfig {
  const factory _ApproversBottomSheetNavigationConfig(
          {required final List<MuApproverDetails> selectedApprovers}) =
      _$_ApproversBottomSheetNavigationConfig;
  const _ApproversBottomSheetNavigationConfig._() : super._();

  @override
  List<MuApproverDetails> get selectedApprovers;
  @override
  @JsonKey(ignore: true)
  _$$_ApproversBottomSheetNavigationConfigCopyWith<
          _$_ApproversBottomSheetNavigationConfig>
      get copyWith => throw _privateConstructorUsedError;
}
