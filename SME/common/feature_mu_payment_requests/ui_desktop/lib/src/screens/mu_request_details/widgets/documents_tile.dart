import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_feature_mu_payment_requests_api/domain/models/mu_file_representation.dart';
import 'package:wio_feature_mu_payment_requests_ui_desktop/locale/multiuser_ui_desktop_localizations.g.dart';
import 'package:wio_feature_mu_payment_requests_ui_desktop/src/screens/mu_request_details/bloc/mu_request_details_bloc.dart';

class DocumentsTile extends StatefulWidget {
  final List<MuFileServiceDetails> docs;
  final String requestId;

  const DocumentsTile({required this.docs, required this.requestId, Key? key})
      : super(key: key);

  @override
  State<DocumentsTile> createState() => _DocumentsTileState();
}

class _DocumentsTileState extends State<DocumentsTile> {
  final List<int> loadingDocIndexes = [];

  Future<void> onTap(
    MuRequestDetailsCubit bloc,
    MuFileServiceDetails doc,
    int index,
  ) async {
    updateLoadingIndicator(index);
    await bloc.download(doc, widget.requestId);
    updateLoadingIndicator(index);
  }

  void updateLoadingIndicator(int index) {
    setState(() {
      loadingDocIndexes.contains(index)
          ? loadingDocIndexes.remove(index)
          : loadingDocIndexes.add(index);
    });
  }

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<MuRequestDetailsCubit>(context);
    final localization = MultiuserUiLocalizations.of(context);
    final colorStyling = context.colorStyling;
    final textStyling = context.textStyling;

    return DefaultTextStyle(
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
      style: textStyling.b2.copyWith(color: colorStyling.secondary4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 6),
            child: Text(
              localization.multiuserSupportingDocuments,
            ),
          ),
          ...widget.docs.mapIndexed(
            (index, doc) {
              final isLoading = loadingDocIndexes.contains(index);

              return InkWell(
                onTap: () => onTap(bloc, doc, index),
                child: Row(
                  children: [
                    Text(
                      doc.filePickerDocumentName,
                      style: textStyling.b2medium.copyWith(
                        decoration: TextDecoration.underline,
                        color: colorStyling.link2,
                      ),
                    ),
                    if (isLoading)
                      Padding(
                        padding: const EdgeInsetsDirectional.only(start: 8.0),
                        child: InputSpinner(
                          const InputSpinnerModel(
                            size: InputSpinnerSize.xs,
                            style: InputSpinnerStyle.light,
                          ),
                        ),
                      ),
                  ],
                ),
              );
            },
          ).toList(),
          const Divider(),
        ],
      ),
    );
  }
}
