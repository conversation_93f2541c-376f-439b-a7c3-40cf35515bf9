import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_feature_mu_payment_requests_api/domain/models/mu_domain_types.dart';
import 'package:wio_feature_mu_payment_requests_ui_desktop/feature_mu_payment_requests_ui_desktop.dart';

class DomainTypeFiltersWidget extends StatefulWidget {
  final ValueChanged<Set<MuDomainType>> onNewDomainTypes;
  final Set<MuDomainType> initialDomainTypes;

  const DomainTypeFiltersWidget({
    required this.onNewDomainTypes,
    required this.initialDomainTypes,
    super.key,
  });

  @override
  State<DomainTypeFiltersWidget> createState() =>
      _DomainTypeFiltersWidgetState();
}

class _DomainTypeFiltersWidgetState extends State<DomainTypeFiltersWidget> {
  Set<MuDomainType> _selectedDomainTypes = {};

  @override
  void initState() {
    _selectedDomainTypes = Set.of(widget.initialDomainTypes);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant DomainTypeFiltersWidget oldWidget) {
    if (widget.initialDomainTypes != oldWidget.initialDomainTypes) {
      _selectedDomainTypes = Set.of(widget.initialDomainTypes);
    }

    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    final localizations = MultiuserUiLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 24.0, bottom: 12),
            child: ListSubHeader(
              ListSubHeaderModel(
                type: ListSubHeaderType.subtle,
                text: localizations.multiUserRequestType,
                variant: ListSubHeaderVariant.var2,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ToggleItem(
                  text: localizations.multiUserIntlPayment,
                  initialValue: _isSelected(MuDomainType.international),
                  onSelected: _updateIntlDomainType,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12.0),
                  child: ToggleItem(
                    text: localizations.multiUserLocalPayment,
                    initialValue: _isSelected(MuDomainType.local),
                    onSelected: _updateLocalDomainType,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ToggleItem(
                  text: localizations.payrollPayments,
                  initialValue: _isSelected(MuDomainType.payroll),
                  onSelected: _updatePayrollDomainType,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 12.0),
                  child: ToggleItem(
                    text: localizations.fxTransfers,
                    initialValue: _isSelected(MuDomainType.fx),
                    onSelected: _updateFxDomainType,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ToggleItem(
                  text: localizations.savingSpaces,
                  initialValue: _isSelected(MuDomainType.savingSpaces),
                  onSelected: _updateSSDomainType,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 12.0),
                  child: ToggleItem(
                    text: localizations.multiCurrency,
                    initialValue: _isSelected(
                      MuDomainType.multiCurrencyAccount,
                    ),
                    onSelected: _updateMultiCurrencyDomainType,
                  ),
                ),
              ],
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              ToggleItem(
                text: localizations.muFilterScf,
                initialValue: _isSelected(MuDomainType.creditContract),
                onSelected: _updateScfDomainType,
              ),
              const SizedBox(width: 12),
              _FilterItem(
                title: _getFilterName(
                  MuDomainType.currentAccountInternalTransfer,
                ),
                selected: _isSelected(
                  MuDomainType.currentAccountInternalTransfer,
                ),
                onToggle: () => _handleToggleFilter(
                  MuDomainType.currentAccountInternalTransfer,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ToggleItem(
            text: _getFilterName(MuDomainType.fixedSavingSpaceCreation),
            initialValue: _isSelected(MuDomainType.fixedSavingSpaceCreation),
            onSelected: _updateFSSDomainType,
          ),
        ],
      ),
    );
  }

  bool _isSelected(MuDomainType type) => _selectedDomainTypes.contains(type);

  String _getFilterName(MuDomainType type) {
    final l10n = MultiuserUiLocalizations.of(context);

    return switch (type) {
      MuDomainType.international => l10n.multiUserIntlPayment,
      MuDomainType.local => l10n.multiUserLocalPayment,
      MuDomainType.payroll => l10n.payrollPayments,
      MuDomainType.fx => l10n.fxTransfers,
      MuDomainType.savingSpaces || MuDomainType.internal => l10n.savingSpaces,
      MuDomainType.multiCurrencyAccount => l10n.multiCurrency,
      MuDomainType.creditContract => l10n.muFilterScf,
      MuDomainType.currentAccountInternalTransfer =>
        l10n.currentAccountTransferFilterTitle,
      MuDomainType.scfInvoiceFinancingAcceptance => l10n.requestEarlyPayment,
      MuDomainType.fixedSavingSpaceCreation ||
      MuDomainType.toFixedSavingSpaceConversion ||
      MuDomainType.fixedSavingSpacePartialWithdraw ||
      MuDomainType.fixedSavingSpaceClosing =>
        l10n.muFixedSavingSpaceFilterTitle,
      MuDomainType.multiSignatoryCardCreation => throw UnimplementedError(),
    };
  }

  void _handleToggleFilter(
    MuDomainType type, {
    // For cases when a single filter must toggle multiple types
    Iterable<MuDomainType>? additionalTypes,
  }) {
    final types = [type, ...?additionalTypes];
    setState(() {
      if (_isSelected(type)) {
        _selectedDomainTypes.removeAll(types);
      } else {
        _selectedDomainTypes.addAll(types);
      }
    });

    widget.onNewDomainTypes(UnmodifiableSetView(_selectedDomainTypes));
  }

  void _updateIntlDomainType(bool isSelected) {
    _handleToggleFilter(MuDomainType.international);
  }

  void _updateLocalDomainType(bool isSelected) {
    _handleToggleFilter(MuDomainType.local);
  }

  void _updatePayrollDomainType(bool isSelected) {
    _handleToggleFilter(MuDomainType.payroll);
  }

  void _updateFxDomainType(bool isSelected) {
    _handleToggleFilter(MuDomainType.fx);
  }

  void _updateSSDomainType(bool isSelected) {
    _handleToggleFilter(
      MuDomainType.savingSpaces,
      additionalTypes: const [MuDomainType.internal],
    );
  }

  void _updateScfDomainType(bool isSelected) {
    _handleToggleFilter(MuDomainType.creditContract);
  }

  void _updateMultiCurrencyDomainType(bool isSelected) {
    _handleToggleFilter(MuDomainType.multiCurrencyAccount);
  }

  void _updateFSSDomainType(bool isSelected) {
    _handleToggleFilter(
      MuDomainType.fixedSavingSpaceCreation,
      additionalTypes: const [
        MuDomainType.toFixedSavingSpaceConversion,
        MuDomainType.fixedSavingSpacePartialWithdraw,
        MuDomainType.fixedSavingSpaceClosing,
      ],
    );
  }
}

class _FilterItem extends StatelessWidget {
  final String title;
  final bool selected;
  final VoidCallback? onToggle;

  const _FilterItem({
    required this.title,
    this.selected = false,
    this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    return ToggleItem(
      text: title,
      initialValue: selected,
      onSelected: (_) => onToggle?.call(),
    );
  }
}
