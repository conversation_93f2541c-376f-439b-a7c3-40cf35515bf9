import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_mu_payment_requests_api/feature_mu_payment_requests_api.dart';

part 'mu_request_filter_web_state.freezed.dart';

@freezed
class MuRequestFilterWebState with _$MuRequestFilterWebState {
  const factory MuRequestFilterWebState.initial({
    @Default(<MuDomainType>{}) Set<MuDomainType> selectedDomainTypes,
    @Default(<MuRequestStatus>{}) Set<MuRequestStatus> selectedStatuses,
    @Default(<TeamMemberInput>{}) Set<TeamMemberInput> selectedAuthors,
  }) = FiltersInitialWebState;

  const factory MuRequestFilterWebState.debounceLoading({
    required Set<MuDomainType> selectedDomainTypes,
    required Set<MuRequestStatus> selectedStatuses,
    required Set<TeamMemberInput> selectedAuthors,
  }) = FiltersDebounceLoadingWebState;

  const factory MuRequestFilterWebState.loaded({
    required Set<MuDomainType> selectedDomainTypes,
    required Set<MuRequestStatus> selectedStatuses,
    required Set<TeamMemberInput> selectedAuthors,
  }) = FilterLoadedWebState;
}

extension FiltersWebStateSelection on MuRequestFilterWebState {
  bool hasStatusOf(MuRequestStatus expectedStatus) => maybeMap(
        initial: (_) => true,
        orElse: () => selectedStatuses.contains(expectedStatus),
      );

  bool hasDomainTypesOf(MuDomainType expectedDomainType) => maybeMap(
        initial: (_) => false,
        orElse: () => selectedDomainTypes.contains(expectedDomainType),
      );

  bool hasAuthor(TeamMemberInput expectedAuthor) => maybeMap(
        initial: (_) => true,
        orElse: () => selectedAuthors.contains(expectedAuthor),
      );
}
