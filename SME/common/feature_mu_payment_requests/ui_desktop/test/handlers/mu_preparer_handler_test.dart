import 'dart:async';

import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:ui/cubit/base_cubit.dart';
import 'package:wio_feature_mu_payment_requests_api/feature_mu_payment_requests_api.dart';
import 'package:wio_feature_mu_payment_requests_ui_desktop/src/handlers/mu_preparer_handler.dart';

import '../mocks.dart';
import '../wio_feature_mu_payment_requests_mocks.dart';

class MockBaseCubit<T> extends Mock implements BaseCubit<T> {
  @override
  Future<V> safeExecute<V>(Future<V> future) {
    return future.then((value) => value);
  }

  @override
  String toString() => '';
}

void main() {
  late MockLogger logger;
  late MuPreparerHandler muPreparerHandler;
  late MockMuTeamInteractor mockMuTeamInteractor;
  late MockMuPaymentRequestsInteractor mockMuInteractor;
  late MockBaseCubit<void> mockCubit;

  setUpAll(() {
    registerFallbackValue(const MuTeamMembersParams.onlyActive());
  });

  setUp(() {
    mockMuTeamInteractor = MockMuTeamInteractor();
    logger = MockLogger();
    mockCubit = MockBaseCubit();
    mockMuTeamInteractor = MockMuTeamInteractor();
    mockMuInteractor = MockMuPaymentRequestsInteractor();
    muPreparerHandler = MuPreparerHandler(
      muTeamInteractor: mockMuTeamInteractor,
      logger: logger,
      interactor: mockMuInteractor,
    );
  });

  test(
    'when getPotentialApprovers is called then interactor getPotentialApprovers'
    ' is called',
    () async {
      // Arrange
      when(
        () => mockMuInteractor.getPotentialApprovers(
          requestId: anyNamed('requestId'),
          muSortInfos: anyNamed('muSortInfos'),
        ),
      ).justAnswerAsync(['approver1']);

      when(
        () => mockMuTeamInteractor.getTeamMembers(
          muTeamMembersParams: anyNamed('muTeamMembersParams'),
        ),
      ).justAnswerAsync([const TeamMemberInput()]);

      // Act
      final result = await muPreparerHandler.getPotentialApprovers(
        requestId: 'requestId',
        cubit: mockCubit,
      );

      // Assert

      expect(result.length, 1);
      verify(
        () => mockMuInteractor.getPotentialApprovers(
          requestId: anyNamed('requestId'),
          muSortInfos: anyNamed('muSortInfos'),
        ),
      ).calledOnce;
      verify(
        () => mockMuTeamInteractor.getTeamMembers(
          muTeamMembersParams: anyNamed('muTeamMembersParams'),
        ),
      );
    },
  );

  test(
    'when getPotentialApprovers is called and interactor getPotentialApprovers'
    ' returns empty list then exception is thrown',
    () async {
      // Arrange
      when(
        () => mockMuInteractor.getPotentialApprovers(
          requestId: anyNamed('requestId'),
          muSortInfos: anyNamed('muSortInfos'),
        ),
      ).justAnswerAsync([]);

      // Act
      expect(
        () async => muPreparerHandler.getPotentialApprovers(
          requestId: 'requestId',
          cubit: mockCubit,
        ),
        throwsA(isA<Exception>()),
      );

      // Assert
      verify(
        () => mockMuInteractor.getPotentialApprovers(
          requestId: anyNamed('requestId'),
          muSortInfos: anyNamed('muSortInfos'),
        ),
      ).calledOnce;
    },
  );

  test(
    'when getTeamMembers returns empty list then exception is thrown',
    () async {
      // Arrange
      when(
        () => mockMuInteractor.getPotentialApprovers(
          requestId: anyNamed('requestId'),
          muSortInfos: anyNamed('muSortInfos'),
        ),
      ).justAnswerAsync(['approver1']);

      when(
        () => mockMuTeamInteractor.getTeamMembers(
          muTeamMembersParams: anyNamed('muTeamMembersParams'),
        ),
      ).justAnswerAsync([]);

      // Act
      try {
        await muPreparerHandler.getPotentialApprovers(
          requestId: 'requestId',
          cubit: mockCubit,
        );
      } on Object catch (_) {
        // Assert

        verify(
          () => mockMuInteractor.getPotentialApprovers(
            requestId: anyNamed('requestId'),
            muSortInfos: anyNamed('muSortInfos'),
          ),
        ).calledOnce;

        verify(
          () => mockMuTeamInteractor.getTeamMembers(
            muTeamMembersParams: anyNamed('muTeamMembersParams'),
          ),
        ).calledOnce;

        verify(
          () => logger.error(
            any(),
            error: anyNamed('error'),
            stackTrace: anyNamed('stackTrace'),
          ),
        );
      }
    },
  );

  test(
    'when getMuFlowRequirement is called then interactor getMuFlowRequirement'
    ' is called',
    () async {
      // Arrange
      when(
        () => mockMuInteractor.getMuFlowRequirement(
          requestId: anyNamed('requestId'),
        ),
      ).justAnswerAsync(const MuFlowRequirement(description: 'description'));

      // Act
      final result = await muPreparerHandler.getMuFlowRequirement('requestId');

      // Assert
      expect(result, 'description');
      verify(
        () => mockMuInteractor.getMuFlowRequirement(
          requestId: anyNamed('requestId'),
        ),
      ).calledOnce;
    },
  );

  test(
    'when getMuFlowRequirement is called and interactor getMuFlowRequirement'
    ' throws an exception then result is empty',
    () async {
      // Arrange
      when(
        () => mockMuInteractor.getMuFlowRequirement(
          requestId: anyNamed('requestId'),
        ),
      ).justThrowAsync(Exception('dummy'));

      // Act
      final result = await muPreparerHandler.getMuFlowRequirement('requestId');

      // Assert
      expect(result, '');
      verify(
        () => mockMuInteractor.getMuFlowRequirement(
          requestId: anyNamed('requestId'),
        ),
      ).calledOnce;
    },
  );
}
