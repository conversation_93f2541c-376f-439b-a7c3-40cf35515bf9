// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'internal_transfer_summary.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$InternalTransferSummary {
  Money get amount => throw _privateConstructorUsedError;
  Currency get currency => throw _privateConstructorUsedError;
  String get savingSpaceName => throw _privateConstructorUsedError;
  MuInternalTransferOperationType get muInternalTransferOperationType =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $InternalTransferSummaryCopyWith<InternalTransferSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InternalTransferSummaryCopyWith<$Res> {
  factory $InternalTransferSummaryCopyWith(InternalTransferSummary value,
      $Res Function(InternalTransferSummary) then) =
  _$InternalTransferSummaryCopyWithImpl<$Res, InternalTransferSummary>;
  @useResult
  $Res call(
      {Money amount,
        Currency currency,
        String savingSpaceName,
        MuInternalTransferOperationType muInternalTransferOperationType});
}

/// @nodoc
class _$InternalTransferSummaryCopyWithImpl<$Res,
$Val extends InternalTransferSummary>
    implements $InternalTransferSummaryCopyWith<$Res> {
  _$InternalTransferSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? currency = null,
    Object? savingSpaceName = null,
    Object? muInternalTransferOperationType = null,
  }) {
    return _then(_value.copyWith(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
      as Money,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
      as Currency,
      savingSpaceName: null == savingSpaceName
          ? _value.savingSpaceName
          : savingSpaceName // ignore: cast_nullable_to_non_nullable
      as String,
      muInternalTransferOperationType: null == muInternalTransferOperationType
          ? _value.muInternalTransferOperationType
          : muInternalTransferOperationType // ignore: cast_nullable_to_non_nullable
      as MuInternalTransferOperationType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_InternalTransferSummaryCopyWith<$Res>
    implements $InternalTransferSummaryCopyWith<$Res> {
  factory _$$_InternalTransferSummaryCopyWith(_$_InternalTransferSummary value,
      $Res Function(_$_InternalTransferSummary) then) =
  __$$_InternalTransferSummaryCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Money amount,
        Currency currency,
        String savingSpaceName,
        MuInternalTransferOperationType muInternalTransferOperationType});
}

/// @nodoc
class __$$_InternalTransferSummaryCopyWithImpl<$Res>
    extends _$InternalTransferSummaryCopyWithImpl<$Res,
        _$_InternalTransferSummary>
    implements _$$_InternalTransferSummaryCopyWith<$Res> {
  __$$_InternalTransferSummaryCopyWithImpl(_$_InternalTransferSummary _value,
      $Res Function(_$_InternalTransferSummary) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? currency = null,
    Object? savingSpaceName = null,
    Object? muInternalTransferOperationType = null,
  }) {
    return _then(_$_InternalTransferSummary(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
      as Money,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
      as Currency,
      savingSpaceName: null == savingSpaceName
          ? _value.savingSpaceName
          : savingSpaceName // ignore: cast_nullable_to_non_nullable
      as String,
      muInternalTransferOperationType: null == muInternalTransferOperationType
          ? _value.muInternalTransferOperationType
          : muInternalTransferOperationType // ignore: cast_nullable_to_non_nullable
      as MuInternalTransferOperationType,
    ));
  }
}

/// @nodoc

class _$_InternalTransferSummary implements _InternalTransferSummary {
  const _$_InternalTransferSummary(
      {required this.amount,
        required this.currency,
        required this.savingSpaceName,
        required this.muInternalTransferOperationType});

  @override
  final Money amount;
  @override
  final Currency currency;
  @override
  final String savingSpaceName;
  @override
  final MuInternalTransferOperationType muInternalTransferOperationType;

  @override
  String toString() {
    return 'InternalTransferSummary(amount: $amount, currency: $currency, savingSpaceName: $savingSpaceName, muInternalTransferOperationType: $muInternalTransferOperationType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_InternalTransferSummary &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.savingSpaceName, savingSpaceName) ||
                other.savingSpaceName == savingSpaceName) &&
            (identical(other.muInternalTransferOperationType,
                muInternalTransferOperationType) ||
                other.muInternalTransferOperationType ==
                    muInternalTransferOperationType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, amount, currency,
      savingSpaceName, muInternalTransferOperationType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InternalTransferSummaryCopyWith<_$_InternalTransferSummary>
  get copyWith =>
      __$$_InternalTransferSummaryCopyWithImpl<_$_InternalTransferSummary>(
          this, _$identity);
}

abstract class _InternalTransferSummary implements InternalTransferSummary {
  const factory _InternalTransferSummary(
      {required final Money amount,
        required final Currency currency,
        required final String savingSpaceName,
        required final MuInternalTransferOperationType
        muInternalTransferOperationType}) = _$_InternalTransferSummary;

  @override
  Money get amount;
  @override
  Currency get currency;
  @override
  String get savingSpaceName;
  @override
  MuInternalTransferOperationType get muInternalTransferOperationType;
  @override
  @JsonKey(ignore: true)
  _$$_InternalTransferSummaryCopyWith<_$_InternalTransferSummary>
  get copyWith => throw _privateConstructorUsedError;
}
