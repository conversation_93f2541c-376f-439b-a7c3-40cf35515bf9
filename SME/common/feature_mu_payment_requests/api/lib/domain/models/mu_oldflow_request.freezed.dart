// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'mu_oldflow_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MuOldflow {
  /// [muDomain] contains fields pertaining to the request details. For
  /// instance requestId and authorId, list of approvers etc
  MuDomain get muDomain => throw _privateConstructorUsedError;

  /// Payment details
  MuTransferDetails get muTransferDetails => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MuOldflowCopyWith<MuOldflow> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MuOldflowCopyWith<$Res> {
  factory $MuOldflowCopyWith(MuOldflow value, $Res Function(MuOldflow) then) =
      _$MuOldflowCopyWithImpl<$Res, MuOldflow>;
  @useResult
  $Res call({MuDomain muDomain, MuTransferDetails muTransferDetails});

  $MuDomainCopyWith<$Res> get muDomain;
  $MuTransferDetailsCopyWith<$Res> get muTransferDetails;
}

/// @nodoc
class _$MuOldflowCopyWithImpl<$Res, $Val extends MuOldflow>
    implements $MuOldflowCopyWith<$Res> {
  _$MuOldflowCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? muDomain = null,
    Object? muTransferDetails = null,
  }) {
    return _then(_value.copyWith(
      muDomain: null == muDomain
          ? _value.muDomain
          : muDomain // ignore: cast_nullable_to_non_nullable
              as MuDomain,
      muTransferDetails: null == muTransferDetails
          ? _value.muTransferDetails
          : muTransferDetails // ignore: cast_nullable_to_non_nullable
              as MuTransferDetails,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MuDomainCopyWith<$Res> get muDomain {
    return $MuDomainCopyWith<$Res>(_value.muDomain, (value) {
      return _then(_value.copyWith(muDomain: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $MuTransferDetailsCopyWith<$Res> get muTransferDetails {
    return $MuTransferDetailsCopyWith<$Res>(_value.muTransferDetails, (value) {
      return _then(_value.copyWith(muTransferDetails: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PaymentsMuOldflowImplCopyWith<$Res>
    implements $MuOldflowCopyWith<$Res> {
  factory _$$PaymentsMuOldflowImplCopyWith(_$PaymentsMuOldflowImpl value,
          $Res Function(_$PaymentsMuOldflowImpl) then) =
      __$$PaymentsMuOldflowImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({MuDomain muDomain, MuTransferDetails muTransferDetails});

  @override
  $MuDomainCopyWith<$Res> get muDomain;
  @override
  $MuTransferDetailsCopyWith<$Res> get muTransferDetails;
}

/// @nodoc
class __$$PaymentsMuOldflowImplCopyWithImpl<$Res>
    extends _$MuOldflowCopyWithImpl<$Res, _$PaymentsMuOldflowImpl>
    implements _$$PaymentsMuOldflowImplCopyWith<$Res> {
  __$$PaymentsMuOldflowImplCopyWithImpl(_$PaymentsMuOldflowImpl _value,
      $Res Function(_$PaymentsMuOldflowImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? muDomain = null,
    Object? muTransferDetails = null,
  }) {
    return _then(_$PaymentsMuOldflowImpl(
      muDomain: null == muDomain
          ? _value.muDomain
          : muDomain // ignore: cast_nullable_to_non_nullable
              as MuDomain,
      muTransferDetails: null == muTransferDetails
          ? _value.muTransferDetails
          : muTransferDetails // ignore: cast_nullable_to_non_nullable
              as MuTransferDetails,
    ));
  }
}

/// @nodoc

class _$PaymentsMuOldflowImpl implements PaymentsMuOldflow {
  const _$PaymentsMuOldflowImpl(
      {required this.muDomain, required this.muTransferDetails});

  /// [muDomain] contains fields pertaining to the request details. For
  /// instance requestId and authorId, list of approvers etc
  @override
  final MuDomain muDomain;

  /// Payment details
  @override
  final MuTransferDetails muTransferDetails;

  @override
  String toString() {
    return 'MuOldflow(muDomain: $muDomain, muTransferDetails: $muTransferDetails)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentsMuOldflowImpl &&
            (identical(other.muDomain, muDomain) ||
                other.muDomain == muDomain) &&
            (identical(other.muTransferDetails, muTransferDetails) ||
                other.muTransferDetails == muTransferDetails));
  }

  @override
  int get hashCode => Object.hash(runtimeType, muDomain, muTransferDetails);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentsMuOldflowImplCopyWith<_$PaymentsMuOldflowImpl> get copyWith =>
      __$$PaymentsMuOldflowImplCopyWithImpl<_$PaymentsMuOldflowImpl>(
          this, _$identity);
}

abstract class PaymentsMuOldflow implements MuOldflow {
  const factory PaymentsMuOldflow(
          {required final MuDomain muDomain,
          required final MuTransferDetails muTransferDetails}) =
      _$PaymentsMuOldflowImpl;

  @override

  /// [muDomain] contains fields pertaining to the request details. For
  /// instance requestId and authorId, list of approvers etc
  MuDomain get muDomain;
  @override

  /// Payment details
  MuTransferDetails get muTransferDetails;
  @override
  @JsonKey(ignore: true)
  _$$PaymentsMuOldflowImplCopyWith<_$PaymentsMuOldflowImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
