import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_mu_payment_requests_api/feature_mu_payment_requests_api.dart';

part 'mu_workflow_draft_payment_input.freezed.dart';

/// The input for creating a draft payment request in SME workflow
/// manager based on the local transfer's txnReference
@freezed
class MuWorkflowDraftPaymentInput with _$MuWorkflowDraftPaymentInput {
  const factory MuWorkflowDraftPaymentInput({
    /// Local or intl
    required MuDomainType muDomainType,

    /// txnReferenceNumber from the draft transfer request
    required String domainId,
  }) = _MuWorkflowDraftPaymentInput;
}
