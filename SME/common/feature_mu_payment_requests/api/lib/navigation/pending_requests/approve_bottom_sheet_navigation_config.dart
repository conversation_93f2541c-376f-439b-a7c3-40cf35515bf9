import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_mu_payment_requests_api/feature_mu_payment_requests_api.dart';
import 'package:wio_feature_mu_payment_requests_api/navigation/pending_requests/reject_action_bottom_sheet_result.dart';

part 'approve_bottom_sheet_navigation_config.freezed.dart';

@freezed
class ApproveBottomSheetNavigationConfig
    with _$ApproveBottomSheetNavigationConfig
    implements BottomSheetNavigationConfig<RejectActionBottomSheetResult> {
  factory ApproveBottomSheetNavigationConfig({
    required MuDomainType muDomainType,
  }) = _ApproveBottomSheetNavigationConfig;

  const ApproveBottomSheetNavigationConfig._();

  @override
  String get feature => MuPaymentRequestsFeatureNavigationConfig.name;

  @override
  String toString() => 'ApproveBottomSheetNavigationConfig{}';
}
