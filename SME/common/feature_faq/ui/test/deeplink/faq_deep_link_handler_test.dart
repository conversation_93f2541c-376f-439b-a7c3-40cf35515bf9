import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:data/auth_manager/auth_manager.dart';
import 'package:deeplink_manager/deep_link.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_faq_api/domain/model/exports.dart';
import 'package:wio_feature_faq_api/faq_api.dart' as common;
import 'package:wio_sme_faq_ui/src/deeplink/faq_deep_link_handler.dart';

import '../mocks.dart';

void main() {
  late FAQsDeepLinkHandler faQsDeepLinkHandler;
  late NavigationProvider mockNavigationProvider;
  late IAuthManager mockAuthManager;
  late DeepLinkRepository mockDeeplinkRepository;

  const validFAQListSchema = 'wiobusiness://wio.io/faq_list';
  const validFAQSubTopicSchema =
      'wiobusiness://wio.io/faq_sub_category?id=onboarding';
  const inValidScheme = 'wiobusiness://wio.io/some_other_path';

  setUpAll(() {
    registerFallbackValue(
      const FeatureToggleKey(
        key: 'key',
        defaultValue: false,
      ),
    );
  });

  setUp(() {
    mockNavigationProvider = MockNavigationProvider();
    mockDeeplinkRepository = MockDeeplinkRepository();
    mockAuthManager = MockAuthManager();
    faQsDeepLinkHandler = FAQsDeepLinkHandler(
      deepLinkRepository: mockDeeplinkRepository,
      navigationProvider: mockNavigationProvider,
      authManager: mockAuthManager,
    );
    registerFallbackValue(FeatureNavigationConfigFake());
  });

  group('Test FAQs Deeplink', () {
    test('tryHandle returns false if deep link cannot be handled', () async {
      final result = await faQsDeepLinkHandler.tryHandle(
        DeepLink(Uri.parse(inValidScheme)),
      );

      expect(result, isFalse);
    });

    test(
        'tryHandle stores deep link in repository if user is not authenticated',
        () async {
      when(() => mockAuthManager.isUserAuthenticated()).justAnswerAsync(false);

      final deepLink = DeepLink(
        Uri.parse(validFAQSubTopicSchema),
      );

      //Act
      final result = await faQsDeepLinkHandler.tryHandle(deepLink);

      //Verify
      verify(() => mockDeeplinkRepository.authenticatedUserDeepLink = deepLink)
          .calledOnce;
      expect(result, isTrue);
    });

    test(
        'tryHandle returns true if user has common faq feature toggle and is '
        'authenticated and deep link is handle faq list scheme', () async {
      when(() => mockAuthManager.isUserAuthenticated()).justAnswerAsync(true);
      when(
        () => mockNavigationProvider.navigateTo(any()),
      ).justAnswerAsync(true);

      final deepLink = DeepLink(Uri.parse(validFAQListSchema));
      final result = await faQsDeepLinkHandler.tryHandle(deepLink);

      verifyNever(
        () => mockDeeplinkRepository.authenticatedUserDeepLink = any(),
      );
      verify(
        () => mockNavigationProvider.navigateTo(
          const common.CommonFaqFeatureNavigationConfig(),
        ),
      ).calledOnce;
      expect(result, isTrue);
    });

    test(
        'tryHandle with common feature toggle on returns true if user is '
        'authenticated and deep link is handled sub topic schema', () async {
      when(() => mockAuthManager.isUserAuthenticated()).justAnswerAsync(true);
      when(
        () => mockNavigationProvider.navigateTo(any()),
      ).justAnswerAsync(true);

      final deepLink = DeepLink(
        Uri.parse(validFAQSubTopicSchema),
      );
      final result = await faQsDeepLinkHandler.tryHandle(deepLink);

      verifyNever(
        () => mockDeeplinkRepository.authenticatedUserDeepLink = any(),
      );
      verify(
        () => mockNavigationProvider.navigateTo(
          const common.CommonFaqFeatureNavigationConfig(
            topicId: FaqCategoryInput.custom('categoryId'),
          ),
        ),
      ).calledOnce;
      expect(result, isTrue);
    });
  });
}
