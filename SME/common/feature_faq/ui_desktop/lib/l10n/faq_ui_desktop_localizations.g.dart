// GENERATED CODE
//
// After the template files .arb have been changed,
// generate this class by the command in the terminal:
// flutter pub run lokalise_flutter_sdk:gen-lok-l10n
//
// Please see https://pub.dev/packages/lokalise_flutter_sdk

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes
// ignore_for_file: depend_on_referenced_packages

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart';
import 'package:lokalise_flutter_sdk/lokalise_flutter_sdk.dart';
import 'intl/messages_all.dart';

class FaqUiLocalizations {
  FaqUiLocalizations._internal();

  static const LocalizationsDelegate<FaqUiLocalizations> delegate =
      _AppLocalizationDelegate();

  static const List<Locale> supportedLocales = [
    Locale.fromSubtags(languageCode: 'en'),
    Locale.fromSubtags(languageCode: 'ar')
  ];

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  static final Map<String, List<String>> _metadata = {
    'accountButtonDetails': [],
    'accountConfirmationLetterLabel': [],
    'accountContactDrawerCallCentreText': [],
    'accountContactDrawerEmailText': [],
    'accountContactDrawerSubtitle': [],
    'accountContactDrawerTitle1': [],
    'accountContactDrawerWhatsAppText': [],
    'accountContactDrawerWhatsapp': [],
    'accountHolder': [],
    'accountStatementLable': [],
    'accountsConfirmationBalanceLetterLabel': [],
    'addressedToWhom': [],
    'allHelpTopics': [],
    'almostThereSuccessScreenDescription': [],
    'almostThereSuccessScreenSubtitle': [],
    'almostThereSuccessScreenTitle': [],
    'auditLetter': [],
    'balanceConfirmationLetterLabel': [],
    'bic': [],
    'businessAddress': [],
    'callUs800': [],
    'cardGenericErrorScreenTitle': [],
    'checkConfirmationLetterCtaText': [],
    'contactScreenEmail': [],
    'contactScreenPhoneCardTitle': [],
    'continueButton': [],
    'copyBankDetails': [],
    'dateLabel': [],
    'deselectAllButton': [],
    'detailsDrawerCopiedClipboard': [],
    'detailsLabel': [],
    'digitalCopyDownloadButton': [],
    'doneButton': [],
    'downloadAccountConfirmation': [],
    'endDateHintTextField': [],
    'enterNameHintTextField': [],
    'generateLetterButton': [],
    'genericErrorCtaButtonTitle': [],
    'hiYou': ['name'],
    'iban': [],
    'includedAccounts': [],
    'letterErrorScreenDescription': [],
    'liabilityLetter': [],
    'mostUsedServices': [],
    'mostUsedServicesLabel': [],
    'muCurrencyAccountCreationHighlightText': ['currency'],
    'multiUserUnknownWioer': [],
    'needMoreHelp': [],
    'newDocument': [],
    'noLiabilityLetter': [],
    'noMatchingResults': [],
    'oopsLabel': [],
    'pleaseTryToRephrase': [],
    'requestDocumentQuestion': [],
    'requestDocumentScreenTitle': [],
    'retryButton': [],
    'searchDots': [],
    'searchHelpTopics': [],
    'searchSupport': [],
    'selectAllButton': [],
    'somethingWentWrongGlitch': [],
    'sorrySomethingWentWrongTryLaterMessage': [],
    'startDateHintTextField': [],
    'timePeriodLabel': [],
    'uhOh': [],
    'unableToDownloadLetterErrorDescription': [],
    'unableToDownloadLetterErrorSubtitle': [],
    'unableToDownloadScreenButton': [],
    'whatsAppUs': []
  };

  static Future<FaqUiLocalizations> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    Lokalise.instance.metadata = _metadata;

    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = FaqUiLocalizations._internal();
      return instance;
    });
  }

  static FaqUiLocalizations of(BuildContext context) {
    final instance =
        Localizations.of<FaqUiLocalizations>(context, FaqUiLocalizations);
    assert(instance != null,
        'No instance of FaqUiLocalizations present in the widget tree. Did you add FaqUiLocalizations.delegate in localizationsDelegates?');
    return instance!;
  }

  /// `Account details`
  String get accountButtonDetails {
    return Intl.message(
      'Account details',
      name: 'accountButtonDetails',
      desc: '',
      args: [],
    );
  }

  /// `Account confirmation letter`
  String get accountConfirmationLetterLabel {
    return Intl.message(
      'Account confirmation letter',
      name: 'accountConfirmationLetterLabel',
      desc: '',
      args: [],
    );
  }

  /// `Call Wio care on +971 600 500 946 and they’ll help you.`
  String get accountContactDrawerCallCentreText {
    return Intl.message(
      'Call Wio care on +971 600 500 946 and they’ll help you.',
      name: 'accountContactDrawerCallCentreText',
      desc: '',
      args: [],
    );
  }

  /// `Email your issue or <NAME_EMAIL>, and we’ll assign it to the right team.`
  String get accountContactDrawerEmailText {
    return Intl.message(
      'Email your issue or <NAME_EMAIL>, and we’ll assign it to the right team.',
      name: 'accountContactDrawerEmailText',
      desc: '',
      args: [],
    );
  }

  /// `Got any questions? We’re available around \nthe clock!`
  String get accountContactDrawerSubtitle {
    return Intl.message(
      'Got any questions? We’re available around \nthe clock!',
      name: 'accountContactDrawerSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `We're here `
  String get accountContactDrawerTitle1 {
    return Intl.message(
      'We\'re here ',
      name: 'accountContactDrawerTitle1',
      desc: '',
      args: [],
    );
  }

  /// `Click here to chat with Wio care for quick answers.`
  String get accountContactDrawerWhatsAppText {
    return Intl.message(
      'Click here to chat with Wio care for quick answers.',
      name: 'accountContactDrawerWhatsAppText',
      desc: '',
      args: [],
    );
  }

  /// `WhatsApp`
  String get accountContactDrawerWhatsapp {
    return Intl.message(
      'WhatsApp',
      name: 'accountContactDrawerWhatsapp',
      desc: '',
      args: [],
    );
  }

  /// `Account holder`
  String get accountHolder {
    return Intl.message(
      'Account holder',
      name: 'accountHolder',
      desc: '',
      args: [],
    );
  }

  /// `Account statement`
  String get accountStatementLable {
    return Intl.message(
      'Account statement',
      name: 'accountStatementLable',
      desc: '',
      args: [],
    );
  }

  /// `Balance confirmation as of date:`
  String get accountsConfirmationBalanceLetterLabel {
    return Intl.message(
      'Balance confirmation as of date:',
      name: 'accountsConfirmationBalanceLetterLabel',
      desc: '',
      args: [],
    );
  }

  /// `Addressed to whom`
  String get addressedToWhom {
    return Intl.message(
      'Addressed to whom',
      name: 'addressedToWhom',
      desc: '',
      args: [],
    );
  }

  /// `All help topics`
  String get allHelpTopics {
    return Intl.message(
      'All help topics',
      name: 'allHelpTopics',
      desc: '',
      args: [],
    );
  }

  /// `Once ready, you can find it under requests and notification center.`
  String get almostThereSuccessScreenDescription {
    return Intl.message(
      'Once ready, you can find it under requests and notification center.',
      name: 'almostThereSuccessScreenDescription',
      desc: '',
      args: [],
    );
  }

  /// `We’re working on your request, we’ll notify you once it’s done`
  String get almostThereSuccessScreenSubtitle {
    return Intl.message(
      'We’re working on your request, we’ll notify you once it’s done',
      name: 'almostThereSuccessScreenSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Almost there!`
  String get almostThereSuccessScreenTitle {
    return Intl.message(
      'Almost there!',
      name: 'almostThereSuccessScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Audit confirmation letter`
  String get auditLetter {
    return Intl.message(
      'Audit confirmation letter',
      name: 'auditLetter',
      desc: '',
      args: [],
    );
  }

  /// `Balance confirmation letter`
  String get balanceConfirmationLetterLabel {
    return Intl.message(
      'Balance confirmation letter',
      name: 'balanceConfirmationLetterLabel',
      desc: '',
      args: [],
    );
  }

  /// `BIC`
  String get bic {
    return Intl.message(
      'BIC',
      name: 'bic',
      desc: '',
      args: [],
    );
  }

  /// `Business address`
  String get businessAddress {
    return Intl.message(
      'Business address',
      name: 'businessAddress',
      desc: '',
      args: [],
    );
  }

  /// `Call us at 800wio`
  String get callUs800 {
    return Intl.message(
      'Call us at 800wio',
      name: 'callUs800',
      desc: 'Call us at 800wio',
      args: [],
    );
  }

  /// `Oops!`
  String get cardGenericErrorScreenTitle {
    return Intl.message(
      'Oops!',
      name: 'cardGenericErrorScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `ACCOUNT CONFIRMATION LETTER`
  String get checkConfirmationLetterCtaText {
    return Intl.message(
      'ACCOUNT CONFIRMATION LETTER',
      name: 'checkConfirmationLetterCtaText',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get contactScreenEmail {
    return Intl.message(
      'Email',
      name: 'contactScreenEmail',
      desc: '',
      args: [],
    );
  }

  /// `Phone`
  String get contactScreenPhoneCardTitle {
    return Intl.message(
      'Phone',
      name: 'contactScreenPhoneCardTitle',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get continueButton {
    return Intl.message(
      'Continue',
      name: 'continueButton',
      desc: '',
      args: [],
    );
  }

  /// `Copy Bank Details`
  String get copyBankDetails {
    return Intl.message(
      'Copy Bank Details',
      name: 'copyBankDetails',
      desc: '',
      args: [],
    );
  }

  /// `Date`
  String get dateLabel {
    return Intl.message(
      'Date',
      name: 'dateLabel',
      desc: '',
      args: [],
    );
  }

  /// `Deselect all`
  String get deselectAllButton {
    return Intl.message(
      'Deselect all',
      name: 'deselectAllButton',
      desc: '',
      args: [],
    );
  }

  /// `Copied to clipboard`
  String get detailsDrawerCopiedClipboard {
    return Intl.message(
      'Copied to clipboard',
      name: 'detailsDrawerCopiedClipboard',
      desc: '',
      args: [],
    );
  }

  /// `Details`
  String get detailsLabel {
    return Intl.message(
      'Details',
      name: 'detailsLabel',
      desc: '',
      args: [],
    );
  }

  /// `Download digital copy`
  String get digitalCopyDownloadButton {
    return Intl.message(
      'Download digital copy',
      name: 'digitalCopyDownloadButton',
      desc: '',
      args: [],
    );
  }

  /// `Done`
  String get doneButton {
    return Intl.message(
      'Done',
      name: 'doneButton',
      desc: '',
      args: [],
    );
  }

  /// `Download account confirmation letter`
  String get downloadAccountConfirmation {
    return Intl.message(
      'Download account confirmation letter',
      name: 'downloadAccountConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `End date`
  String get endDateHintTextField {
    return Intl.message(
      'End date',
      name: 'endDateHintTextField',
      desc: '',
      args: [],
    );
  }

  /// `Enter name`
  String get enterNameHintTextField {
    return Intl.message(
      'Enter name',
      name: 'enterNameHintTextField',
      desc: '',
      args: [],
    );
  }

  /// `Generate letter`
  String get generateLetterButton {
    return Intl.message(
      'Generate letter',
      name: 'generateLetterButton',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get genericErrorCtaButtonTitle {
    return Intl.message(
      'Close',
      name: 'genericErrorCtaButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Hi {name},\nHow can we help?`
  String hiYou(String name) {
    return Intl.message(
      'Hi $name,\nHow can we help?',
      name: 'hiYou',
      desc: '',
      args: [name],
    );
  }

  /// `IBAN`
  String get iban {
    return Intl.message(
      'IBAN',
      name: 'iban',
      desc: '',
      args: [],
    );
  }

  /// `Included accounts`
  String get includedAccounts {
    return Intl.message(
      'Included accounts',
      name: 'includedAccounts',
      desc: '',
      args: [],
    );
  }

  /// `But it might be us. Please try again, let’s see if this time it works.`
  String get letterErrorScreenDescription {
    return Intl.message(
      'But it might be us. Please try again, let’s see if this time it works.',
      name: 'letterErrorScreenDescription',
      desc: '',
      args: [],
    );
  }

  /// `Liability letter`
  String get liabilityLetter {
    return Intl.message(
      'Liability letter',
      name: 'liabilityLetter',
      desc: '',
      args: [],
    );
  }

  /// `Most used services`
  String get mostUsedServices {
    return Intl.message(
      'Most used services',
      name: 'mostUsedServices',
      desc: '',
      args: [],
    );
  }

  /// `MOST USED SERVICES`
  String get mostUsedServicesLabel {
    return Intl.message(
      'MOST USED SERVICES',
      name: 'mostUsedServicesLabel',
      desc: '',
      args: [],
    );
  }

  /// `{currency} account`
  String muCurrencyAccountCreationHighlightText(String currency) {
    return Intl.message(
      '$currency account',
      name: 'muCurrencyAccountCreationHighlightText',
      desc: '',
      args: [currency],
    );
  }

  /// `Unknown Wioer`
  String get multiUserUnknownWioer {
    return Intl.message(
      'Unknown Wioer',
      name: 'multiUserUnknownWioer',
      desc: '',
      args: [],
    );
  }

  /// `Need more help ?`
  String get needMoreHelp {
    return Intl.message(
      'Need more help ?',
      name: 'needMoreHelp',
      desc: '',
      args: [],
    );
  }

  /// `New document`
  String get newDocument {
    return Intl.message(
      'New document',
      name: 'newDocument',
      desc: '',
      args: [],
    );
  }

  /// `No liability letter`
  String get noLiabilityLetter {
    return Intl.message(
      'No liability letter',
      name: 'noLiabilityLetter',
      desc: '',
      args: [],
    );
  }

  /// `No matching results`
  String get noMatchingResults {
    return Intl.message(
      'No matching results',
      name: 'noMatchingResults',
      desc: '',
      args: [],
    );
  }

  /// `Oops!`
  String get oopsLabel {
    return Intl.message(
      'Oops!',
      name: 'oopsLabel',
      desc: '',
      args: [],
    );
  }

  /// `Please try to rephrase your question\nor contact support`
  String get pleaseTryToRephrase {
    return Intl.message(
      'Please try to rephrase your question\nor contact support',
      name: 'pleaseTryToRephrase',
      desc: '',
      args: [],
    );
  }

  /// `I want to request a document`
  String get requestDocumentQuestion {
    return Intl.message(
      'I want to request a document',
      name: 'requestDocumentQuestion',
      desc: '',
      args: [],
    );
  }

  /// `Select the type of document`
  String get requestDocumentScreenTitle {
    return Intl.message(
      'Select the type of document',
      name: 'requestDocumentScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `TRY AGAIN`
  String get retryButton {
    return Intl.message(
      'TRY AGAIN',
      name: 'retryButton',
      desc: '',
      args: [],
    );
  }

  /// `Search...`
  String get searchDots {
    return Intl.message(
      'Search...',
      name: 'searchDots',
      desc: '',
      args: [],
    );
  }

  /// `Search help topics`
  String get searchHelpTopics {
    return Intl.message(
      'Search help topics',
      name: 'searchHelpTopics',
      desc: '',
      args: [],
    );
  }

  /// `Search support`
  String get searchSupport {
    return Intl.message(
      'Search support',
      name: 'searchSupport',
      desc: '',
      args: [],
    );
  }

  /// `Select all`
  String get selectAllButton {
    return Intl.message(
      'Select all',
      name: 'selectAllButton',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong. It seems there has been \na technical glitch.`
  String get somethingWentWrongGlitch {
    return Intl.message(
      'Something went wrong. It seems there has been \na technical glitch.',
      name: 'somethingWentWrongGlitch',
      desc: '',
      args: [],
    );
  }

  /// `Sorry, something went wrong. Try later...`
  String get sorrySomethingWentWrongTryLaterMessage {
    return Intl.message(
      'Sorry, something went wrong. Try later...',
      name: 'sorrySomethingWentWrongTryLaterMessage',
      desc: '',
      args: [],
    );
  }

  /// `Start date`
  String get startDateHintTextField {
    return Intl.message(
      'Start date',
      name: 'startDateHintTextField',
      desc: '',
      args: [],
    );
  }

  /// `Time period`
  String get timePeriodLabel {
    return Intl.message(
      'Time period',
      name: 'timePeriodLabel',
      desc: '',
      args: [],
    );
  }

  /// `Uh-oh!`
  String get uhOh {
    return Intl.message(
      'Uh-oh!',
      name: 'uhOh',
      desc: '',
      args: [],
    );
  }

  /// `You can view this in your previously requested documents.`
  String get unableToDownloadLetterErrorDescription {
    return Intl.message(
      'You can view this in your previously requested documents.',
      name: 'unableToDownloadLetterErrorDescription',
      desc: '',
      args: [],
    );
  }

  /// `We couldn’t download your document, but we have it saved`
  String get unableToDownloadLetterErrorSubtitle {
    return Intl.message(
      'We couldn’t download your document, but we have it saved',
      name: 'unableToDownloadLetterErrorSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Take me there`
  String get unableToDownloadScreenButton {
    return Intl.message(
      'Take me there',
      name: 'unableToDownloadScreenButton',
      desc: '',
      args: [],
    );
  }

  /// `Whatsapp us`
  String get whatsAppUs {
    return Intl.message(
      'Whatsapp us',
      name: 'whatsAppUs',
      desc: '',
      args: [],
    );
  }
}

class _AppLocalizationDelegate
    extends LocalizationsDelegate<FaqUiLocalizations> {
  const _AppLocalizationDelegate();

  @override
  bool isSupported(Locale locale) => FaqUiLocalizations.supportedLocales.any(
      (supportedLocale) => supportedLocale.languageCode == locale.languageCode);

  @override
  Future<FaqUiLocalizations> load(Locale locale) =>
      FaqUiLocalizations.load(locale);

  @override
  bool shouldReload(_AppLocalizationDelegate old) => false;
}
