// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'letter_list_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LetterListState {
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadedLetterListState value) loaded,
    required TResult Function(_LoadingLetterListState value) loading,
    required TResult Function(_ErrorLetterListState value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadedLetterListState value)? loaded,
    TResult? Function(_LoadingLetterListState value)? loading,
    TResult? Function(_ErrorLetterListState value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadedLetterListState value)? loaded,
    TResult Function(_LoadingLetterListState value)? loading,
    TResult Function(_ErrorLetterListState value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LetterListStateCopyWith<$Res> {
  factory $LetterListStateCopyWith(
          LetterListState value, $Res Function(LetterListState) then) =
      _$LetterListStateCopyWithImpl<$Res, LetterListState>;
}

/// @nodoc
class _$LetterListStateCopyWithImpl<$Res, $Val extends LetterListState>
    implements $LetterListStateCopyWith<$Res> {
  _$LetterListStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LetterListState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadedLetterListStateImplCopyWith<$Res> {
  factory _$$LoadedLetterListStateImplCopyWith(
          _$LoadedLetterListStateImpl value,
          $Res Function(_$LoadedLetterListStateImpl) then) =
      __$$LoadedLetterListStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Services> services});
}

/// @nodoc
class __$$LoadedLetterListStateImplCopyWithImpl<$Res>
    extends _$LetterListStateCopyWithImpl<$Res, _$LoadedLetterListStateImpl>
    implements _$$LoadedLetterListStateImplCopyWith<$Res> {
  __$$LoadedLetterListStateImplCopyWithImpl(_$LoadedLetterListStateImpl _value,
      $Res Function(_$LoadedLetterListStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of LetterListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? services = null,
  }) {
    return _then(_$LoadedLetterListStateImpl(
      services: null == services
          ? _value._services
          : services // ignore: cast_nullable_to_non_nullable
              as List<Services>,
    ));
  }
}

/// @nodoc

class _$LoadedLetterListStateImpl implements _LoadedLetterListState {
  const _$LoadedLetterListStateImpl({required final List<Services> services})
      : _services = services;

  final List<Services> _services;
  @override
  List<Services> get services {
    if (_services is EqualUnmodifiableListView) return _services;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_services);
  }

  @override
  String toString() {
    return 'LetterListState.loaded(services: $services)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedLetterListStateImpl &&
            const DeepCollectionEquality().equals(other._services, _services));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_services));

  /// Create a copy of LetterListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedLetterListStateImplCopyWith<_$LoadedLetterListStateImpl>
      get copyWith => __$$LoadedLetterListStateImplCopyWithImpl<
          _$LoadedLetterListStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadedLetterListState value) loaded,
    required TResult Function(_LoadingLetterListState value) loading,
    required TResult Function(_ErrorLetterListState value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadedLetterListState value)? loaded,
    TResult? Function(_LoadingLetterListState value)? loading,
    TResult? Function(_ErrorLetterListState value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadedLetterListState value)? loaded,
    TResult Function(_LoadingLetterListState value)? loading,
    TResult Function(_ErrorLetterListState value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _LoadedLetterListState implements LetterListState {
  const factory _LoadedLetterListState(
      {required final List<Services> services}) = _$LoadedLetterListStateImpl;

  List<Services> get services;

  /// Create a copy of LetterListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedLetterListStateImplCopyWith<_$LoadedLetterListStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadingLetterListStateImplCopyWith<$Res> {
  factory _$$LoadingLetterListStateImplCopyWith(
          _$LoadingLetterListStateImpl value,
          $Res Function(_$LoadingLetterListStateImpl) then) =
      __$$LoadingLetterListStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingLetterListStateImplCopyWithImpl<$Res>
    extends _$LetterListStateCopyWithImpl<$Res, _$LoadingLetterListStateImpl>
    implements _$$LoadingLetterListStateImplCopyWith<$Res> {
  __$$LoadingLetterListStateImplCopyWithImpl(
      _$LoadingLetterListStateImpl _value,
      $Res Function(_$LoadingLetterListStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of LetterListState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingLetterListStateImpl implements _LoadingLetterListState {
  const _$LoadingLetterListStateImpl();

  @override
  String toString() {
    return 'LetterListState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadingLetterListStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadedLetterListState value) loaded,
    required TResult Function(_LoadingLetterListState value) loading,
    required TResult Function(_ErrorLetterListState value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadedLetterListState value)? loaded,
    TResult? Function(_LoadingLetterListState value)? loading,
    TResult? Function(_ErrorLetterListState value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadedLetterListState value)? loaded,
    TResult Function(_LoadingLetterListState value)? loading,
    TResult Function(_ErrorLetterListState value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _LoadingLetterListState implements LetterListState {
  const factory _LoadingLetterListState() = _$LoadingLetterListStateImpl;
}

/// @nodoc
abstract class _$$ErrorLetterListStateImplCopyWith<$Res> {
  factory _$$ErrorLetterListStateImplCopyWith(_$ErrorLetterListStateImpl value,
          $Res Function(_$ErrorLetterListStateImpl) then) =
      __$$ErrorLetterListStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ErrorLetterListStateImplCopyWithImpl<$Res>
    extends _$LetterListStateCopyWithImpl<$Res, _$ErrorLetterListStateImpl>
    implements _$$ErrorLetterListStateImplCopyWith<$Res> {
  __$$ErrorLetterListStateImplCopyWithImpl(_$ErrorLetterListStateImpl _value,
      $Res Function(_$ErrorLetterListStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of LetterListState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ErrorLetterListStateImpl implements _ErrorLetterListState {
  const _$ErrorLetterListStateImpl();

  @override
  String toString() {
    return 'LetterListState.error()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorLetterListStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadedLetterListState value) loaded,
    required TResult Function(_LoadingLetterListState value) loading,
    required TResult Function(_ErrorLetterListState value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadedLetterListState value)? loaded,
    TResult? Function(_LoadingLetterListState value)? loading,
    TResult? Function(_ErrorLetterListState value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadedLetterListState value)? loaded,
    TResult Function(_LoadingLetterListState value)? loading,
    TResult Function(_ErrorLetterListState value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _ErrorLetterListState implements LetterListState {
  const factory _ErrorLetterListState() = _$ErrorLetterListStateImpl;
}
