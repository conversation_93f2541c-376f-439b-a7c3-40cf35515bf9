import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sme_feature_transactions_api/feature_transactions_api.dart';

part 'all_transactions_state.freezed.dart';

const _initialPage = 0;

@freezed
sealed class AllTransactionsState with _$AllTransactionsState {
  const AllTransactionsState._();

  const factory AllTransactionsState.initial({
    required TransactionParams params,
  }) = AllTransactionsInitialState;

  const factory AllTransactionsState.loading({
    required TransactionParams params,
    @Default(_initialPage) int page,
  }) = AllTransactionsLoadingState;

  const factory AllTransactionsState.idle({
    required TransactionParams params,
    required List<Transaction> transactions,
    @Default(false) bool hasMore,
    @Default(_initialPage) int page,
  }) = AllTransactionsIdleState;

  const factory AllTransactionsState.paginating({
    required TransactionParams params,
    required List<Transaction> transactions,
    @Default(_initialPage) int page,
  }) = AllTransactionsPaginatingState;

  const factory AllTransactionsState.failed({
    required TransactionParams params,
    @Default(<Transaction>[]) List<Transaction> transactions,
    @Default(false) bool hasMore,
    @Default(_initialPage) int page,
    Object? error,
  }) = AllTransactionsFailedState;

  bool get canLoadMore => maybeMap(
        idle: (it) => it.hasMore,
        failed: (it) => it.transactions.isNotEmpty && it.hasMore,
        orElse: () => false,
      );

  bool get isLoadingMore => this is AllTransactionsPaginatingState;

  int get nextPage => map(
        initial: (_) => _initialPage,
        loading: (it) => it.page,
        idle: (it) => it.page + 1,
        paginating: (it) => it.page,
        failed: (it) => it.page,
      );

  /// Possible state transitions:
  /// - Loading -> Idle
  /// - Paginating -> Idle
  AllTransactionsState toIdle(List<Transaction> transactions) {
    assert(transactions.length <= params.pageSize);
    final hasMore = transactions.length == params.pageSize;

    return switch (this) {
      AllTransactionsLoadingState(:final page) => AllTransactionsState.idle(
          params: params,
          transactions: transactions,
          hasMore: hasMore,
          page: page,
        ),
      final AllTransactionsPaginatingState it => AllTransactionsState.idle(
          params: params,
          transactions: [...it.transactions, ...transactions],
          hasMore: hasMore,
          page: it.page,
        ),
      _ => _invalidStateTransition(),
    };
  }

  /// Possible state transitions:
  /// - Initial -> Loading
  /// - Failed with empty transactions (initial loading errors) -> Loading
  /// - Failed with non-empty transactions -> Paginating
  /// - Idle if not all transactions are loaded -> Paginating
  AllTransactionsState toLoading() => switch (this) {
        AllTransactionsInitialState() ||
        AllTransactionsFailedState(transactions: const []) =>
          AllTransactionsState.loading(params: params),
        final AllTransactionsFailedState it => AllTransactionsState.paginating(
            params: params,
            transactions: it.transactions,
            page: it.page,
          ),
        final AllTransactionsIdleState it when it.hasMore =>
          AllTransactionsState.paginating(
            params: params,
            transactions: it.transactions,
            page: it.page + 1,
          ),
        _ => _invalidStateTransition(),
      };

  /// Possible state transitions:
  /// - Loading -> Failed
  /// - Paginating -> Failed
  AllTransactionsState toFailed([Object? error]) => switch (this) {
        AllTransactionsLoadingState(:final page) => AllTransactionsState.failed(
            params: params,
            page: page,
            error: error,
          ),
        final AllTransactionsPaginatingState it => AllTransactionsState.failed(
            params: params,
            error: error,
            transactions: it.transactions,
            page: it.page,
            hasMore: true,
          ),
        _ => _invalidStateTransition(),
      };

  // Release build assertions, nothing more
  Never _invalidStateTransition() =>
      throw AssertionError('Invalid state transition: $this');
}
