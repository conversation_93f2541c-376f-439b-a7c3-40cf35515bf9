// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'all_transactions_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AllTransactionsState {
  TransactionParams get params => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TransactionParams params) initial,
    required TResult Function(TransactionParams params, int page) loading,
    required TResult Function(TransactionParams params,
            List<Transaction> transactions, bool hasMore, int page)
        idle,
    required TResult Function(
            TransactionParams params, List<Transaction> transactions, int page)
        paginating,
    required TResult Function(
            TransactionParams params,
            List<Transaction> transactions,
            bool hasMore,
            int page,
            Object? error)
        failed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TransactionParams params)? initial,
    TResult? Function(TransactionParams params, int page)? loading,
    TResult? Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page)?
        idle,
    TResult? Function(
            TransactionParams params, List<Transaction> transactions, int page)?
        paginating,
    TResult? Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page, Object? error)?
        failed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TransactionParams params)? initial,
    TResult Function(TransactionParams params, int page)? loading,
    TResult Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page)?
        idle,
    TResult Function(
            TransactionParams params, List<Transaction> transactions, int page)?
        paginating,
    TResult Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page, Object? error)?
        failed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AllTransactionsInitialState value) initial,
    required TResult Function(AllTransactionsLoadingState value) loading,
    required TResult Function(AllTransactionsIdleState value) idle,
    required TResult Function(AllTransactionsPaginatingState value) paginating,
    required TResult Function(AllTransactionsFailedState value) failed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AllTransactionsInitialState value)? initial,
    TResult? Function(AllTransactionsLoadingState value)? loading,
    TResult? Function(AllTransactionsIdleState value)? idle,
    TResult? Function(AllTransactionsPaginatingState value)? paginating,
    TResult? Function(AllTransactionsFailedState value)? failed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AllTransactionsInitialState value)? initial,
    TResult Function(AllTransactionsLoadingState value)? loading,
    TResult Function(AllTransactionsIdleState value)? idle,
    TResult Function(AllTransactionsPaginatingState value)? paginating,
    TResult Function(AllTransactionsFailedState value)? failed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AllTransactionsStateCopyWith<AllTransactionsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AllTransactionsStateCopyWith<$Res> {
  factory $AllTransactionsStateCopyWith(AllTransactionsState value,
          $Res Function(AllTransactionsState) then) =
      _$AllTransactionsStateCopyWithImpl<$Res, AllTransactionsState>;
  @useResult
  $Res call({TransactionParams params});

  $TransactionParamsCopyWith<$Res> get params;
}

/// @nodoc
class _$AllTransactionsStateCopyWithImpl<$Res,
        $Val extends AllTransactionsState>
    implements $AllTransactionsStateCopyWith<$Res> {
  _$AllTransactionsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? params = null,
  }) {
    return _then(_value.copyWith(
      params: null == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as TransactionParams,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $TransactionParamsCopyWith<$Res> get params {
    return $TransactionParamsCopyWith<$Res>(_value.params, (value) {
      return _then(_value.copyWith(params: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AllTransactionsInitialStateImplCopyWith<$Res>
    implements $AllTransactionsStateCopyWith<$Res> {
  factory _$$AllTransactionsInitialStateImplCopyWith(
          _$AllTransactionsInitialStateImpl value,
          $Res Function(_$AllTransactionsInitialStateImpl) then) =
      __$$AllTransactionsInitialStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({TransactionParams params});

  @override
  $TransactionParamsCopyWith<$Res> get params;
}

/// @nodoc
class __$$AllTransactionsInitialStateImplCopyWithImpl<$Res>
    extends _$AllTransactionsStateCopyWithImpl<$Res,
        _$AllTransactionsInitialStateImpl>
    implements _$$AllTransactionsInitialStateImplCopyWith<$Res> {
  __$$AllTransactionsInitialStateImplCopyWithImpl(
      _$AllTransactionsInitialStateImpl _value,
      $Res Function(_$AllTransactionsInitialStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? params = null,
  }) {
    return _then(_$AllTransactionsInitialStateImpl(
      params: null == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as TransactionParams,
    ));
  }
}

/// @nodoc

class _$AllTransactionsInitialStateImpl extends AllTransactionsInitialState {
  const _$AllTransactionsInitialStateImpl({required this.params}) : super._();

  @override
  final TransactionParams params;

  @override
  String toString() {
    return 'AllTransactionsState.initial(params: $params)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AllTransactionsInitialStateImpl &&
            (identical(other.params, params) || other.params == params));
  }

  @override
  int get hashCode => Object.hash(runtimeType, params);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AllTransactionsInitialStateImplCopyWith<_$AllTransactionsInitialStateImpl>
      get copyWith => __$$AllTransactionsInitialStateImplCopyWithImpl<
          _$AllTransactionsInitialStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TransactionParams params) initial,
    required TResult Function(TransactionParams params, int page) loading,
    required TResult Function(TransactionParams params,
            List<Transaction> transactions, bool hasMore, int page)
        idle,
    required TResult Function(
            TransactionParams params, List<Transaction> transactions, int page)
        paginating,
    required TResult Function(
            TransactionParams params,
            List<Transaction> transactions,
            bool hasMore,
            int page,
            Object? error)
        failed,
  }) {
    return initial(params);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TransactionParams params)? initial,
    TResult? Function(TransactionParams params, int page)? loading,
    TResult? Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page)?
        idle,
    TResult? Function(
            TransactionParams params, List<Transaction> transactions, int page)?
        paginating,
    TResult? Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page, Object? error)?
        failed,
  }) {
    return initial?.call(params);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TransactionParams params)? initial,
    TResult Function(TransactionParams params, int page)? loading,
    TResult Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page)?
        idle,
    TResult Function(
            TransactionParams params, List<Transaction> transactions, int page)?
        paginating,
    TResult Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page, Object? error)?
        failed,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(params);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AllTransactionsInitialState value) initial,
    required TResult Function(AllTransactionsLoadingState value) loading,
    required TResult Function(AllTransactionsIdleState value) idle,
    required TResult Function(AllTransactionsPaginatingState value) paginating,
    required TResult Function(AllTransactionsFailedState value) failed,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AllTransactionsInitialState value)? initial,
    TResult? Function(AllTransactionsLoadingState value)? loading,
    TResult? Function(AllTransactionsIdleState value)? idle,
    TResult? Function(AllTransactionsPaginatingState value)? paginating,
    TResult? Function(AllTransactionsFailedState value)? failed,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AllTransactionsInitialState value)? initial,
    TResult Function(AllTransactionsLoadingState value)? loading,
    TResult Function(AllTransactionsIdleState value)? idle,
    TResult Function(AllTransactionsPaginatingState value)? paginating,
    TResult Function(AllTransactionsFailedState value)? failed,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class AllTransactionsInitialState extends AllTransactionsState {
  const factory AllTransactionsInitialState(
          {required final TransactionParams params}) =
      _$AllTransactionsInitialStateImpl;
  const AllTransactionsInitialState._() : super._();

  @override
  TransactionParams get params;
  @override
  @JsonKey(ignore: true)
  _$$AllTransactionsInitialStateImplCopyWith<_$AllTransactionsInitialStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AllTransactionsLoadingStateImplCopyWith<$Res>
    implements $AllTransactionsStateCopyWith<$Res> {
  factory _$$AllTransactionsLoadingStateImplCopyWith(
          _$AllTransactionsLoadingStateImpl value,
          $Res Function(_$AllTransactionsLoadingStateImpl) then) =
      __$$AllTransactionsLoadingStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({TransactionParams params, int page});

  @override
  $TransactionParamsCopyWith<$Res> get params;
}

/// @nodoc
class __$$AllTransactionsLoadingStateImplCopyWithImpl<$Res>
    extends _$AllTransactionsStateCopyWithImpl<$Res,
        _$AllTransactionsLoadingStateImpl>
    implements _$$AllTransactionsLoadingStateImplCopyWith<$Res> {
  __$$AllTransactionsLoadingStateImplCopyWithImpl(
      _$AllTransactionsLoadingStateImpl _value,
      $Res Function(_$AllTransactionsLoadingStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? params = null,
    Object? page = null,
  }) {
    return _then(_$AllTransactionsLoadingStateImpl(
      params: null == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as TransactionParams,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$AllTransactionsLoadingStateImpl extends AllTransactionsLoadingState {
  const _$AllTransactionsLoadingStateImpl(
      {required this.params, this.page = _initialPage})
      : super._();

  @override
  final TransactionParams params;
  @override
  @JsonKey()
  final int page;

  @override
  String toString() {
    return 'AllTransactionsState.loading(params: $params, page: $page)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AllTransactionsLoadingStateImpl &&
            (identical(other.params, params) || other.params == params) &&
            (identical(other.page, page) || other.page == page));
  }

  @override
  int get hashCode => Object.hash(runtimeType, params, page);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AllTransactionsLoadingStateImplCopyWith<_$AllTransactionsLoadingStateImpl>
      get copyWith => __$$AllTransactionsLoadingStateImplCopyWithImpl<
          _$AllTransactionsLoadingStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TransactionParams params) initial,
    required TResult Function(TransactionParams params, int page) loading,
    required TResult Function(TransactionParams params,
            List<Transaction> transactions, bool hasMore, int page)
        idle,
    required TResult Function(
            TransactionParams params, List<Transaction> transactions, int page)
        paginating,
    required TResult Function(
            TransactionParams params,
            List<Transaction> transactions,
            bool hasMore,
            int page,
            Object? error)
        failed,
  }) {
    return loading(params, page);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TransactionParams params)? initial,
    TResult? Function(TransactionParams params, int page)? loading,
    TResult? Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page)?
        idle,
    TResult? Function(
            TransactionParams params, List<Transaction> transactions, int page)?
        paginating,
    TResult? Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page, Object? error)?
        failed,
  }) {
    return loading?.call(params, page);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TransactionParams params)? initial,
    TResult Function(TransactionParams params, int page)? loading,
    TResult Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page)?
        idle,
    TResult Function(
            TransactionParams params, List<Transaction> transactions, int page)?
        paginating,
    TResult Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page, Object? error)?
        failed,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(params, page);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AllTransactionsInitialState value) initial,
    required TResult Function(AllTransactionsLoadingState value) loading,
    required TResult Function(AllTransactionsIdleState value) idle,
    required TResult Function(AllTransactionsPaginatingState value) paginating,
    required TResult Function(AllTransactionsFailedState value) failed,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AllTransactionsInitialState value)? initial,
    TResult? Function(AllTransactionsLoadingState value)? loading,
    TResult? Function(AllTransactionsIdleState value)? idle,
    TResult? Function(AllTransactionsPaginatingState value)? paginating,
    TResult? Function(AllTransactionsFailedState value)? failed,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AllTransactionsInitialState value)? initial,
    TResult Function(AllTransactionsLoadingState value)? loading,
    TResult Function(AllTransactionsIdleState value)? idle,
    TResult Function(AllTransactionsPaginatingState value)? paginating,
    TResult Function(AllTransactionsFailedState value)? failed,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class AllTransactionsLoadingState extends AllTransactionsState {
  const factory AllTransactionsLoadingState(
      {required final TransactionParams params,
      final int page}) = _$AllTransactionsLoadingStateImpl;
  const AllTransactionsLoadingState._() : super._();

  @override
  TransactionParams get params;
  int get page;
  @override
  @JsonKey(ignore: true)
  _$$AllTransactionsLoadingStateImplCopyWith<_$AllTransactionsLoadingStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AllTransactionsIdleStateImplCopyWith<$Res>
    implements $AllTransactionsStateCopyWith<$Res> {
  factory _$$AllTransactionsIdleStateImplCopyWith(
          _$AllTransactionsIdleStateImpl value,
          $Res Function(_$AllTransactionsIdleStateImpl) then) =
      __$$AllTransactionsIdleStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {TransactionParams params,
      List<Transaction> transactions,
      bool hasMore,
      int page});

  @override
  $TransactionParamsCopyWith<$Res> get params;
}

/// @nodoc
class __$$AllTransactionsIdleStateImplCopyWithImpl<$Res>
    extends _$AllTransactionsStateCopyWithImpl<$Res,
        _$AllTransactionsIdleStateImpl>
    implements _$$AllTransactionsIdleStateImplCopyWith<$Res> {
  __$$AllTransactionsIdleStateImplCopyWithImpl(
      _$AllTransactionsIdleStateImpl _value,
      $Res Function(_$AllTransactionsIdleStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? params = null,
    Object? transactions = null,
    Object? hasMore = null,
    Object? page = null,
  }) {
    return _then(_$AllTransactionsIdleStateImpl(
      params: null == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as TransactionParams,
      transactions: null == transactions
          ? _value._transactions
          : transactions // ignore: cast_nullable_to_non_nullable
              as List<Transaction>,
      hasMore: null == hasMore
          ? _value.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$AllTransactionsIdleStateImpl extends AllTransactionsIdleState {
  const _$AllTransactionsIdleStateImpl(
      {required this.params,
      required final List<Transaction> transactions,
      this.hasMore = false,
      this.page = _initialPage})
      : _transactions = transactions,
        super._();

  @override
  final TransactionParams params;
  final List<Transaction> _transactions;
  @override
  List<Transaction> get transactions {
    if (_transactions is EqualUnmodifiableListView) return _transactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transactions);
  }

  @override
  @JsonKey()
  final bool hasMore;
  @override
  @JsonKey()
  final int page;

  @override
  String toString() {
    return 'AllTransactionsState.idle(params: $params, transactions: $transactions, hasMore: $hasMore, page: $page)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AllTransactionsIdleStateImpl &&
            (identical(other.params, params) || other.params == params) &&
            const DeepCollectionEquality()
                .equals(other._transactions, _transactions) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.page, page) || other.page == page));
  }

  @override
  int get hashCode => Object.hash(runtimeType, params,
      const DeepCollectionEquality().hash(_transactions), hasMore, page);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AllTransactionsIdleStateImplCopyWith<_$AllTransactionsIdleStateImpl>
      get copyWith => __$$AllTransactionsIdleStateImplCopyWithImpl<
          _$AllTransactionsIdleStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TransactionParams params) initial,
    required TResult Function(TransactionParams params, int page) loading,
    required TResult Function(TransactionParams params,
            List<Transaction> transactions, bool hasMore, int page)
        idle,
    required TResult Function(
            TransactionParams params, List<Transaction> transactions, int page)
        paginating,
    required TResult Function(
            TransactionParams params,
            List<Transaction> transactions,
            bool hasMore,
            int page,
            Object? error)
        failed,
  }) {
    return idle(params, transactions, hasMore, page);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TransactionParams params)? initial,
    TResult? Function(TransactionParams params, int page)? loading,
    TResult? Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page)?
        idle,
    TResult? Function(
            TransactionParams params, List<Transaction> transactions, int page)?
        paginating,
    TResult? Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page, Object? error)?
        failed,
  }) {
    return idle?.call(params, transactions, hasMore, page);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TransactionParams params)? initial,
    TResult Function(TransactionParams params, int page)? loading,
    TResult Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page)?
        idle,
    TResult Function(
            TransactionParams params, List<Transaction> transactions, int page)?
        paginating,
    TResult Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page, Object? error)?
        failed,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(params, transactions, hasMore, page);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AllTransactionsInitialState value) initial,
    required TResult Function(AllTransactionsLoadingState value) loading,
    required TResult Function(AllTransactionsIdleState value) idle,
    required TResult Function(AllTransactionsPaginatingState value) paginating,
    required TResult Function(AllTransactionsFailedState value) failed,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AllTransactionsInitialState value)? initial,
    TResult? Function(AllTransactionsLoadingState value)? loading,
    TResult? Function(AllTransactionsIdleState value)? idle,
    TResult? Function(AllTransactionsPaginatingState value)? paginating,
    TResult? Function(AllTransactionsFailedState value)? failed,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AllTransactionsInitialState value)? initial,
    TResult Function(AllTransactionsLoadingState value)? loading,
    TResult Function(AllTransactionsIdleState value)? idle,
    TResult Function(AllTransactionsPaginatingState value)? paginating,
    TResult Function(AllTransactionsFailedState value)? failed,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class AllTransactionsIdleState extends AllTransactionsState {
  const factory AllTransactionsIdleState(
      {required final TransactionParams params,
      required final List<Transaction> transactions,
      final bool hasMore,
      final int page}) = _$AllTransactionsIdleStateImpl;
  const AllTransactionsIdleState._() : super._();

  @override
  TransactionParams get params;
  List<Transaction> get transactions;
  bool get hasMore;
  int get page;
  @override
  @JsonKey(ignore: true)
  _$$AllTransactionsIdleStateImplCopyWith<_$AllTransactionsIdleStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AllTransactionsPaginatingStateImplCopyWith<$Res>
    implements $AllTransactionsStateCopyWith<$Res> {
  factory _$$AllTransactionsPaginatingStateImplCopyWith(
          _$AllTransactionsPaginatingStateImpl value,
          $Res Function(_$AllTransactionsPaginatingStateImpl) then) =
      __$$AllTransactionsPaginatingStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {TransactionParams params, List<Transaction> transactions, int page});

  @override
  $TransactionParamsCopyWith<$Res> get params;
}

/// @nodoc
class __$$AllTransactionsPaginatingStateImplCopyWithImpl<$Res>
    extends _$AllTransactionsStateCopyWithImpl<$Res,
        _$AllTransactionsPaginatingStateImpl>
    implements _$$AllTransactionsPaginatingStateImplCopyWith<$Res> {
  __$$AllTransactionsPaginatingStateImplCopyWithImpl(
      _$AllTransactionsPaginatingStateImpl _value,
      $Res Function(_$AllTransactionsPaginatingStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? params = null,
    Object? transactions = null,
    Object? page = null,
  }) {
    return _then(_$AllTransactionsPaginatingStateImpl(
      params: null == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as TransactionParams,
      transactions: null == transactions
          ? _value._transactions
          : transactions // ignore: cast_nullable_to_non_nullable
              as List<Transaction>,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$AllTransactionsPaginatingStateImpl
    extends AllTransactionsPaginatingState {
  const _$AllTransactionsPaginatingStateImpl(
      {required this.params,
      required final List<Transaction> transactions,
      this.page = _initialPage})
      : _transactions = transactions,
        super._();

  @override
  final TransactionParams params;
  final List<Transaction> _transactions;
  @override
  List<Transaction> get transactions {
    if (_transactions is EqualUnmodifiableListView) return _transactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transactions);
  }

  @override
  @JsonKey()
  final int page;

  @override
  String toString() {
    return 'AllTransactionsState.paginating(params: $params, transactions: $transactions, page: $page)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AllTransactionsPaginatingStateImpl &&
            (identical(other.params, params) || other.params == params) &&
            const DeepCollectionEquality()
                .equals(other._transactions, _transactions) &&
            (identical(other.page, page) || other.page == page));
  }

  @override
  int get hashCode => Object.hash(runtimeType, params,
      const DeepCollectionEquality().hash(_transactions), page);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AllTransactionsPaginatingStateImplCopyWith<
          _$AllTransactionsPaginatingStateImpl>
      get copyWith => __$$AllTransactionsPaginatingStateImplCopyWithImpl<
          _$AllTransactionsPaginatingStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TransactionParams params) initial,
    required TResult Function(TransactionParams params, int page) loading,
    required TResult Function(TransactionParams params,
            List<Transaction> transactions, bool hasMore, int page)
        idle,
    required TResult Function(
            TransactionParams params, List<Transaction> transactions, int page)
        paginating,
    required TResult Function(
            TransactionParams params,
            List<Transaction> transactions,
            bool hasMore,
            int page,
            Object? error)
        failed,
  }) {
    return paginating(params, transactions, page);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TransactionParams params)? initial,
    TResult? Function(TransactionParams params, int page)? loading,
    TResult? Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page)?
        idle,
    TResult? Function(
            TransactionParams params, List<Transaction> transactions, int page)?
        paginating,
    TResult? Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page, Object? error)?
        failed,
  }) {
    return paginating?.call(params, transactions, page);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TransactionParams params)? initial,
    TResult Function(TransactionParams params, int page)? loading,
    TResult Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page)?
        idle,
    TResult Function(
            TransactionParams params, List<Transaction> transactions, int page)?
        paginating,
    TResult Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page, Object? error)?
        failed,
    required TResult orElse(),
  }) {
    if (paginating != null) {
      return paginating(params, transactions, page);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AllTransactionsInitialState value) initial,
    required TResult Function(AllTransactionsLoadingState value) loading,
    required TResult Function(AllTransactionsIdleState value) idle,
    required TResult Function(AllTransactionsPaginatingState value) paginating,
    required TResult Function(AllTransactionsFailedState value) failed,
  }) {
    return paginating(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AllTransactionsInitialState value)? initial,
    TResult? Function(AllTransactionsLoadingState value)? loading,
    TResult? Function(AllTransactionsIdleState value)? idle,
    TResult? Function(AllTransactionsPaginatingState value)? paginating,
    TResult? Function(AllTransactionsFailedState value)? failed,
  }) {
    return paginating?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AllTransactionsInitialState value)? initial,
    TResult Function(AllTransactionsLoadingState value)? loading,
    TResult Function(AllTransactionsIdleState value)? idle,
    TResult Function(AllTransactionsPaginatingState value)? paginating,
    TResult Function(AllTransactionsFailedState value)? failed,
    required TResult orElse(),
  }) {
    if (paginating != null) {
      return paginating(this);
    }
    return orElse();
  }
}

abstract class AllTransactionsPaginatingState extends AllTransactionsState {
  const factory AllTransactionsPaginatingState(
      {required final TransactionParams params,
      required final List<Transaction> transactions,
      final int page}) = _$AllTransactionsPaginatingStateImpl;
  const AllTransactionsPaginatingState._() : super._();

  @override
  TransactionParams get params;
  List<Transaction> get transactions;
  int get page;
  @override
  @JsonKey(ignore: true)
  _$$AllTransactionsPaginatingStateImplCopyWith<
          _$AllTransactionsPaginatingStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AllTransactionsFailedStateImplCopyWith<$Res>
    implements $AllTransactionsStateCopyWith<$Res> {
  factory _$$AllTransactionsFailedStateImplCopyWith(
          _$AllTransactionsFailedStateImpl value,
          $Res Function(_$AllTransactionsFailedStateImpl) then) =
      __$$AllTransactionsFailedStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {TransactionParams params,
      List<Transaction> transactions,
      bool hasMore,
      int page,
      Object? error});

  @override
  $TransactionParamsCopyWith<$Res> get params;
}

/// @nodoc
class __$$AllTransactionsFailedStateImplCopyWithImpl<$Res>
    extends _$AllTransactionsStateCopyWithImpl<$Res,
        _$AllTransactionsFailedStateImpl>
    implements _$$AllTransactionsFailedStateImplCopyWith<$Res> {
  __$$AllTransactionsFailedStateImplCopyWithImpl(
      _$AllTransactionsFailedStateImpl _value,
      $Res Function(_$AllTransactionsFailedStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? params = null,
    Object? transactions = null,
    Object? hasMore = null,
    Object? page = null,
    Object? error = freezed,
  }) {
    return _then(_$AllTransactionsFailedStateImpl(
      params: null == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as TransactionParams,
      transactions: null == transactions
          ? _value._transactions
          : transactions // ignore: cast_nullable_to_non_nullable
              as List<Transaction>,
      hasMore: null == hasMore
          ? _value.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error ? _value.error : error,
    ));
  }
}

/// @nodoc

class _$AllTransactionsFailedStateImpl extends AllTransactionsFailedState {
  const _$AllTransactionsFailedStateImpl(
      {required this.params,
      final List<Transaction> transactions = const <Transaction>[],
      this.hasMore = false,
      this.page = _initialPage,
      this.error})
      : _transactions = transactions,
        super._();

  @override
  final TransactionParams params;
  final List<Transaction> _transactions;
  @override
  @JsonKey()
  List<Transaction> get transactions {
    if (_transactions is EqualUnmodifiableListView) return _transactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transactions);
  }

  @override
  @JsonKey()
  final bool hasMore;
  @override
  @JsonKey()
  final int page;
  @override
  final Object? error;

  @override
  String toString() {
    return 'AllTransactionsState.failed(params: $params, transactions: $transactions, hasMore: $hasMore, page: $page, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AllTransactionsFailedStateImpl &&
            (identical(other.params, params) || other.params == params) &&
            const DeepCollectionEquality()
                .equals(other._transactions, _transactions) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.page, page) || other.page == page) &&
            const DeepCollectionEquality().equals(other.error, error));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      params,
      const DeepCollectionEquality().hash(_transactions),
      hasMore,
      page,
      const DeepCollectionEquality().hash(error));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AllTransactionsFailedStateImplCopyWith<_$AllTransactionsFailedStateImpl>
      get copyWith => __$$AllTransactionsFailedStateImplCopyWithImpl<
          _$AllTransactionsFailedStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TransactionParams params) initial,
    required TResult Function(TransactionParams params, int page) loading,
    required TResult Function(TransactionParams params,
            List<Transaction> transactions, bool hasMore, int page)
        idle,
    required TResult Function(
            TransactionParams params, List<Transaction> transactions, int page)
        paginating,
    required TResult Function(
            TransactionParams params,
            List<Transaction> transactions,
            bool hasMore,
            int page,
            Object? error)
        failed,
  }) {
    return failed(params, transactions, hasMore, page, error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TransactionParams params)? initial,
    TResult? Function(TransactionParams params, int page)? loading,
    TResult? Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page)?
        idle,
    TResult? Function(
            TransactionParams params, List<Transaction> transactions, int page)?
        paginating,
    TResult? Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page, Object? error)?
        failed,
  }) {
    return failed?.call(params, transactions, hasMore, page, error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TransactionParams params)? initial,
    TResult Function(TransactionParams params, int page)? loading,
    TResult Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page)?
        idle,
    TResult Function(
            TransactionParams params, List<Transaction> transactions, int page)?
        paginating,
    TResult Function(TransactionParams params, List<Transaction> transactions,
            bool hasMore, int page, Object? error)?
        failed,
    required TResult orElse(),
  }) {
    if (failed != null) {
      return failed(params, transactions, hasMore, page, error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AllTransactionsInitialState value) initial,
    required TResult Function(AllTransactionsLoadingState value) loading,
    required TResult Function(AllTransactionsIdleState value) idle,
    required TResult Function(AllTransactionsPaginatingState value) paginating,
    required TResult Function(AllTransactionsFailedState value) failed,
  }) {
    return failed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AllTransactionsInitialState value)? initial,
    TResult? Function(AllTransactionsLoadingState value)? loading,
    TResult? Function(AllTransactionsIdleState value)? idle,
    TResult? Function(AllTransactionsPaginatingState value)? paginating,
    TResult? Function(AllTransactionsFailedState value)? failed,
  }) {
    return failed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AllTransactionsInitialState value)? initial,
    TResult Function(AllTransactionsLoadingState value)? loading,
    TResult Function(AllTransactionsIdleState value)? idle,
    TResult Function(AllTransactionsPaginatingState value)? paginating,
    TResult Function(AllTransactionsFailedState value)? failed,
    required TResult orElse(),
  }) {
    if (failed != null) {
      return failed(this);
    }
    return orElse();
  }
}

abstract class AllTransactionsFailedState extends AllTransactionsState {
  const factory AllTransactionsFailedState(
      {required final TransactionParams params,
      final List<Transaction> transactions,
      final bool hasMore,
      final int page,
      final Object? error}) = _$AllTransactionsFailedStateImpl;
  const AllTransactionsFailedState._() : super._();

  @override
  TransactionParams get params;
  List<Transaction> get transactions;
  bool get hasMore;
  int get page;
  Object? get error;
  @override
  @JsonKey(ignore: true)
  _$$AllTransactionsFailedStateImplCopyWith<_$AllTransactionsFailedStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
