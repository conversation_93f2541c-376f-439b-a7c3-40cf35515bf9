import 'package:sme_feature_transactions_api/feature_transactions_api.dart';

abstract interface class AllTransactionsDelegate {
  void onShowTransaction(Transaction transaction);

  Stream<TransactionListDetails> getTransactionHistory({
    required int pageNumber,
    required TransactionParams params,
  });

  void onSeeAllTransactions({
    TransactionParams params = TransactionParams.unfiltered,
  });
}
