import 'package:freezed_annotation/freezed_annotation.dart';

import 'package:sme_feature_transactions_api/src/domain/model/enums.dart';

part 'transaction_filter.freezed.dart';

@freezed
class TransactionFilter with _$TransactionFilter {
  const factory TransactionFilter({
    TransactionMode? transactionMode,
    DateTime? fromDate,
    DateTime? toDate,
    @Default(SortByProperty.date) SortByProperty sortByProperty,
    @Default(TransactionOrder.desc) TransactionOrder smeTransactionOrder,
    @Default(<TransactionStatus>[]) List<TransactionStatus> byStatus,
    @Default(<TransactionType>[]) List<TransactionType> byTypes,
    @Default(<TransactionSubType>[]) List<TransactionSubType> bySubTypes,
    @Default(true) bool excludedNotSettled,
  }) = _TransactionFilter;
}
