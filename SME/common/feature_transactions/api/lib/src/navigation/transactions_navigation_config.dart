import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

part 'transactions_navigation_config.freezed.dart';

@freezed
class TransactionsFeatureNavigationConfig extends FeatureNavigationConfig
    with _$TransactionsFeatureNavigationConfig {
  static const name = 'transactions_feature';

  const TransactionsFeatureNavigationConfig._() : super(name);

  const factory TransactionsFeatureNavigationConfig({
    required ScreenNavigationConfig destination,
  }) = _TransactionsFeatureNavigationConfig;

  @override
  String get id => name;
}
