import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:di/di.dart';
import 'package:feature_2fa_api/feature_2fa_api.dart';
import 'package:feature_login_api/feature_login_api.dart';
import 'package:logging_api/logging.dart';
import 'package:post_login_flow_api/post_login_flow_api.dart';
import 'package:post_login_flow_impl/post_login_flow_impl.dart';

import 'package:post_login_flow_impl/src/domain/post_login_analytics.dart';
import 'package:wio_app_core_api/index.dart';

class PostLoginFlowDomainDependencyResolver {
  static void register() {
    _registerAnalytics();
    _registerFlow();
  }

  static void _registerAnalytics() {
    DependencyProvider.registerLazySingleton<PostLoginAnalytics>(
      () => PostLoginAnalytics(
        mixpanelTracker:
            DependencyProvider.get<AnalyticsAbstractTrackerFactory>().get(
          screenName: 'post_login_flow',
          tracker: AnalyticsTracker.mixpanel,
        ),
      ),
    );
  }

  static void _registerFlow() {
    DependencyProvider.registerFactory<FactorSetupHandler>(
      () => FactorSetupHandler(
        authenticationInteractor:
            DependencyProvider.get<TwoFactorAuthenticationInteractor>(),
      ),
    );
    DependencyProvider.registerLazySingleton<PostLoginFlow>(
      () => PostLoginFlowManager(
        loginInteractor: DependencyProvider.get<LoginInteractor>(),
        factorSetupHandler: DependencyProvider.get<FactorSetupHandler>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        postLoginFlowAnalytics: DependencyProvider.get<PostLoginAnalytics>(),
        logger: DependencyProvider.get<Logger>(),
      ),
    );
  }
}
