query CheckFutureDatedTransaction(
  $transactionAmount: Float!
  $ipiAllowed: Boolean!
  $transferPurpose: String
) {
  checkFutureDatedTransaction(
    input: {
      TransactionAmount: $transactionAmount
      IpiAllowed: $ipiAllowed
      TransferPurpose: $transferPurpose
    }
  ) {
    FutureDatedTransaction
    ValueDateTime
  }
}

query GetAccountRequirements($requirement: AccountRequirement!) {
  getAccountRequirements(accountRequirement: $requirement) {
    QuoteId
    AccountRequirements {
      Type
      Title
      UsageInfo
      Fields {
        Name
        Group {
          Key
          Name
          Type
          RefreshRequirementsOnChange
          Required
          DisplayFormat
          Example
          MinLength
          MaxLength
          ValidationRegexp
          ValuesAllowed {
            Key
            Name
          }
        }
      }
    }
  }
}

query GetBranches($request: BrankAndBranchRequest!) {
  getBranches(bank: $request) {
    AccountRequirements {
      Type
      Title
      UsageInfo
      Fields {
        Name
        Group {
          Key
          Name
          Type
          RefreshRequirementsOnChange
          Required
          DisplayFormat
          Example
          MinLength
          MaxLength
          ValidationRegexp
          ValuesAllowed {
            Key
            Name
          }
        }
      }
    }
  }
}

query GetEstimateCharge($channel: String!, $transactionTypeCode: String!) {
  getEstimateCharge(
    channel: $channel
    transactionTypeCode: $transactionTypeCode
  ) {
    Id
    Amount
  }
}

query GetStates($request: CountryAndStatesRequest!) {
  getStates(country: $request) {
    AccountRequirements {
      Type
      Title
      UsageInfo
      Fields {
        Name
        Group {
          Key
          Name
          Type
          RefreshRequirementsOnChange
          Required
          DisplayFormat
          Example
          MinLength
          MaxLength
          ValidationRegexp
          ValuesAllowed {
            Key
            Name
          }
        }
      }
    }
  }
}

mutation InitiatePayment($payment: PaymentInitiationRequest!) {
  initiatePayment(payment: $payment) {
    Status
    TransferId
  }
}

mutation InitiateLocalPayment($payment: ClientTransferDetails!) {
  initiateLocalPayment(payment: $payment) {
    Status
    TransferId
  }
}

mutation MakeDeposit($request: CreateDepositRequest!) {
  makeDeposit(deposit: $request) {
    epayReferenceNumber
    expiryDateTime
  }
}
