query getTariffInfoList {
    getTariffInfoList{
        Id,
        Name,
        Description,
        FeesDescription{
            Text,
            HighlightedText
        },
        Logo{
            Id,
            Type
        },
        Status,
        MinimumAccountBalance{
            Amount,
            Currency
        }
        Cashback,
        SubscriptionPrice{
            Base{
                Amount,
                Currency
            },
            Current{
                Amount,
                Currency
            },
            PriceValidTo,
            Description,
            PriceDisplayType
        }
        MainCharges{
            Id,
            Name,
            Description,
            Price{
                Base{
                    Amount,
                    Currency
                },
                Current{
                    Amount,
                    Currency
                },
                PriceValidTo,
                Description,
                PriceDisplayType
            },
            Logo{
                Id,
                Type
            }
        },
        Blocks{
            Name,
            Charges{
                Id,
                Name,
                Price{
                    Base{
                        Amount,
                        Currency
                    },
                    Current{
                        Amount,
                        Currency
                    },
                    PriceValidTo,
                    Description,
                    PriceDisplayType
                }
            }
        },
        Addons{
            Id,
            Name,
            Description,
            Price{
                Base{
                    Amount,
                    Currency
                },
                Current{
                    Amount,
                    Currency
                },
                PriceValidTo,
                Description,
                PriceDisplayType
            },
            Logo{
                Id,
                Type
            }
        },
        NextBillingDate
    }
}