import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_behaviour_api/domain/models/behaviour_permission.dart';

part 'business_permissions.freezed.dart';

/// Permissions for business group
@freezed
class BusinessPermissions
    with _$BusinessPermissions
    implements BehaviourPermission {
  /// Permission for preparer
  const factory BusinessPermissions.execute() = BusinessExecutePermission;

  /// Permission for owner
  const factory BusinessPermissions.own() = BusinessOwnPermission;

  /// Permission for FAM
  const factory BusinessPermissions.manage() = BusinessManagePermission;
}
