// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'full_access_role.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$FullAccessRole {}

/// @nodoc
abstract class $FullAccessRoleCopyWith<$Res> {
  factory $FullAccessRoleCopyWith(
          FullAccessRole value, $Res Function(FullAccessRole) then) =
      _$FullAccessRoleCopyWithImpl<$Res, FullAccessRole>;
}

/// @nodoc
class _$FullAccessRoleCopyWithImpl<$Res, $Val extends FullAccessRole>
    implements $FullAccessRoleCopyWith<$Res> {
  _$FullAccessRoleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_FullAccessRoleCopyWith<$Res> {
  factory _$$_FullAccessRoleCopyWith(
          _$_FullAccessRole value, $Res Function(_$_FullAccessRole) then) =
      __$$_FullAccessRoleCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_FullAccessRoleCopyWithImpl<$Res>
    extends _$FullAccessRoleCopyWithImpl<$Res, _$_FullAccessRole>
    implements _$$_FullAccessRoleCopyWith<$Res> {
  __$$_FullAccessRoleCopyWithImpl(
      _$_FullAccessRole _value, $Res Function(_$_FullAccessRole) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_FullAccessRole implements _FullAccessRole {
  const _$_FullAccessRole();

  @override
  String toString() {
    return 'FullAccessRole()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_FullAccessRole);
  }

  @override
  int get hashCode => runtimeType.hashCode;
}

abstract class _FullAccessRole implements FullAccessRole {
  const factory _FullAccessRole() = _$_FullAccessRole;
}
