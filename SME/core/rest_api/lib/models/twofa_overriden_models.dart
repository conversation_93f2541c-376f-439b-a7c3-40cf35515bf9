// ignore_for_file: depend_on_referenced_packages, avoid_equals_and_hash_code_on_mutable_classes, lines_longer_than_80_chars

import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:sme_rest_api/models/identity.swagger.dart';

part 'twofa_overriden_models.g.dart';

@JsonSerializable(explicitToJson: true)
class Error {
  Error({
    this.id,
    this.code,
    this.message,
    this.errorFormat,
    this.context,
  });

  factory Error.fromJson(Map<String, dynamic> json) {
    return Error(
      id: (json['Id'] ?? json['id']) as String?,
      code: Error._errorCodeFromJson(json['Code'] ?? json['code']),
      message: (json['Message'] ?? json['message']) as String?,
      errorFormat: (json['ErrorFormat'] ?? json['errorFormat']) as String?,
      context: (json['Context'] ?? json['context']) as Map<String, dynamic>?,
    );
  }

  static const toJsonFactory = _$ErrorToJson;
  Map<String, dynamic> toJson() => _$ErrorToJson(this);

  @JsonKey(name: 'Id', includeIfNull: false)
  final String? id;
  @JsonKey(
    name: 'Code',
    includeIfNull: false,
    toJson: _errorCodeToJson,
    fromJson: _errorCodeFromJson,
  )
  final IdentityErrorCode? code;
  @JsonKey(name: 'Message', includeIfNull: false)
  final String? message;
  @JsonKey(name: 'ErrorFormat', includeIfNull: false)
  final String? errorFormat;
  @JsonKey(name: 'Context', includeIfNull: false)
  final Map<String, dynamic>? context;

  @override
  bool operator ==(Object? other) {
    return identical(this, other) ||
        (other is Error &&
            (identical(other.id, id) ||
                const DeepCollectionEquality().equals(other.id, id)) &&
            (identical(other.code, code) ||
                const DeepCollectionEquality().equals(other.code, code)) &&
            (identical(other.message, message) ||
                const DeepCollectionEquality()
                    .equals(other.message, message)) &&
            (identical(other.errorFormat, errorFormat) ||
                const DeepCollectionEquality()
                    .equals(other.errorFormat, errorFormat)) &&
            (identical(other.context, context) ||
                const DeepCollectionEquality().equals(other.context, context)));
  }

  @override
  String toString() => jsonEncode(this);

  @override
  int get hashCode =>
      const DeepCollectionEquality().hash(id) ^
      const DeepCollectionEquality().hash(code) ^
      const DeepCollectionEquality().hash(message) ^
      const DeepCollectionEquality().hash(errorFormat) ^
      const DeepCollectionEquality().hash(context) ^
      runtimeType.hashCode;

  static String? _errorCodeToJson(IdentityErrorCode? errorCode) {
    return errorCode?.value;
  }

  static IdentityErrorCode _errorCodeFromJson(
    Object? errorCode, [
    IdentityErrorCode? defaultValue,
  ]) {
    switch (errorCode) {
      case 'BE_USER_TWO_FA_MISSING':
        return IdentityErrorCode.twoFaMissing;
      case 'BE_USER_RESOURCE_NOT_FOUND':
        return IdentityErrorCode.entityNotFound;
      case 'BE_USER_CHALLENGE_NOT_FOUND':
        return IdentityErrorCode.challengeNotFound;
      case 'BE_USER_FACTOR_BLOCKED':
        return IdentityErrorCode.factorBlocked;
      case 'BE_USER_SOLVE_CHALLENGE_FAILED':
        return IdentityErrorCode.solveChallengeFailed;
      case 'BE_USER_SOLVE_CHALLENGE_NOT_ALLOWED':
        return IdentityErrorCode.solveChallengeNotAllowed;
      default:
        return IdentityErrorCode.values
                .firstWhereOrNull((e) => e.value == errorCode) ??
            defaultValue ??
            IdentityErrorCode.swaggerGeneratedUnknown;
    }
  }

  static Map<String, dynamic> _$ErrorToJson(Error instance) {
    final val = <String, dynamic>{};

    void writeNotNull(String key, Object? value) {
      if (value != null) {
        val[key] = value;
      }
    }

    writeNotNull('Id', instance.id);
    writeNotNull('Code', Error._errorCodeToJson(instance.code));
    writeNotNull('Message', instance.message);
    writeNotNull('ErrorFormat', instance.errorFormat);
    writeNotNull('Context', instance.context);

    return val;
  }
}

extension $ErrorExtension on Error {
  Error copyWith({
    String? id,
    IdentityErrorCode? code,
    String? message,
    String? errorFormat,
    Map<String, dynamic>? context,
  }) {
    return Error(
      id: id ?? this.id,
      code: code ?? this.code,
      message: message ?? this.message,
      errorFormat: errorFormat ?? this.errorFormat,
      context: context ?? this.context,
    );
  }

  Error copyWithWrapped({
    Wrapped<String?>? id,
    Wrapped<IdentityErrorCode?>? code,
    Wrapped<String?>? message,
    Wrapped<String?>? errorFormat,
    Wrapped<Map<String, dynamic>?>? context,
  }) {
    return Error(
      id: (id != null ? id.value : this.id),
      code: (code != null ? code.value : this.code),
      message: (message != null ? message.value : this.message),
      errorFormat: (errorFormat != null ? errorFormat.value : this.errorFormat),
      context: (context != null ? context.value : this.context),
    );
  }
}

enum IdentityErrorCode {
  swaggerGeneratedUnknown(null),
  twoFaMissing('TWO_FA_MISSING'),
  verifyTransactionFailed('VERIFY_TRANSACTION_FAILED'),
  factorBlocked('FACTOR_BLOCKED'),
  inappropriateTransactionStatus('INAPPROPRIATE_TRANSACTION_STATUS'),
  entityNotFound('ENTITY_NOT_FOUND'),
  challengeNotFound('CHALLENGE_NOT_FOUND'),
  solveChallengeFailed('SOLVE_CHALLENGE_FAILED'),
  requestedFactorsNotAvailable('REQUESTED_FACTORS_NOT_AVAILABLE'),
  dataMismatch('DATA_MISMATCH'),
  badRequest('BAD_REQUEST'),
  paymentApprovalFailed('PAYMENT_APPROVAL_FAILED'),
  unexpectedError('UNEXPECTED_ERROR'),
  solveChallengeNotAllowed('SOLVE_CHALLENGE_NOT_ALLOWED');

  final String? value;

  const IdentityErrorCode(this.value);
}
