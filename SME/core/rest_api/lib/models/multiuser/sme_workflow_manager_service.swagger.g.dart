// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sme_workflow_manager_service.swagger.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateAttachment _$CreateAttachmentFromJson(Map<String, dynamic> json) =>
    CreateAttachment(
      reference: json['reference'] as String,
    );

Map<String, dynamic> _$CreateAttachmentToJson(CreateAttachment instance) =>
    <String, dynamic>{
      'reference': instance.reference,
    };

Attachment _$AttachmentFromJson(Map<String, dynamic> json) => Attachment(
      id: json['id'] as String,
      reference: json['reference'] as String,
    );

Map<String, dynamic> _$Attachment<PERSON>o<PERSON>son(Attachment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'reference': instance.reference,
    };

AttachmentDownload _$AttachmentDownloadFromJson(Map<String, dynamic> json) =>
    AttachmentDownload(
      documentMimeType: json['documentMimeType'] as String?,
      base64Format: json['base64Format'] as String?,
    );

Map<String, dynamic> _$AttachmentDownloadToJson(AttachmentDownload instance) =>
    <String, dynamic>{
      if (instance.documentMimeType case final value?)
        'documentMimeType': value,
      if (instance.base64Format case final value?) 'base64Format': value,
    };

CreateRequest _$CreateRequestFromJson(Map<String, dynamic> json) =>
    CreateRequest(
      domainType: domainTypeFromJson(json['domainType']),
      domainId: json['domainId'] as String,
    );

Map<String, dynamic> _$CreateRequestToJson(CreateRequest instance) =>
    <String, dynamic>{
      if (domainTypeToJson(instance.domainType) case final value?)
        'domainType': value,
      'domainId': instance.domainId,
    };

MultiUserRequest _$MultiUserRequestFromJson(Map<String, dynamic> json) =>
    MultiUserRequest(
      id: json['id'] as String?,
      domainType: domainTypeFromJson(json['domainType']),
      status: requestStatusFromJson(json['status']),
      domainId: json['domainId'] as String?,
      authorId: json['authorId'] as String?,
      description: json['description'] as String?,
      approvers: (json['approvers'] as List<dynamic>?)
              ?.map((e) => RequestApprover.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      attachments: (json['attachments'] as List<dynamic>?)
              ?.map((e) => Attachment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$MultiUserRequestToJson(MultiUserRequest instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (domainTypeToJson(instance.domainType) case final value?)
        'domainType': value,
      if (requestStatusToJson(instance.status) case final value?)
        'status': value,
      if (instance.domainId case final value?) 'domainId': value,
      if (instance.authorId case final value?) 'authorId': value,
      if (instance.description case final value?) 'description': value,
      if (instance.approvers?.map((e) => e.toJson()).toList() case final value?)
        'approvers': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
      if (instance.attachments?.map((e) => e.toJson()).toList()
          case final value?)
        'attachments': value,
    };

LocalTransferMultiUserRequest _$LocalTransferMultiUserRequestFromJson(
        Map<String, dynamic> json) =>
    LocalTransferMultiUserRequest(
      localTransfer: json['localTransfer'] == null
          ? null
          : LocalTransfer.fromJson(
              json['localTransfer'] as Map<String, dynamic>),
      id: json['id'] as String?,
      domainType: domainTypeFromJson(json['domainType']),
      status: requestStatusFromJson(json['status']),
      domainId: json['domainId'] as String?,
      authorId: json['authorId'] as String?,
      description: json['description'] as String?,
      approvers: (json['approvers'] as List<dynamic>?)
              ?.map((e) => RequestApprover.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      attachments: (json['attachments'] as List<dynamic>?)
              ?.map((e) => Attachment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$LocalTransferMultiUserRequestToJson(
        LocalTransferMultiUserRequest instance) =>
    <String, dynamic>{
      if (instance.localTransfer?.toJson() case final value?)
        'localTransfer': value,
      if (instance.id case final value?) 'id': value,
      if (domainTypeToJson(instance.domainType) case final value?)
        'domainType': value,
      if (requestStatusToJson(instance.status) case final value?)
        'status': value,
      if (instance.domainId case final value?) 'domainId': value,
      if (instance.authorId case final value?) 'authorId': value,
      if (instance.description case final value?) 'description': value,
      if (instance.approvers?.map((e) => e.toJson()).toList() case final value?)
        'approvers': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
      if (instance.attachments?.map((e) => e.toJson()).toList()
          case final value?)
        'attachments': value,
    };

LocalTransfer _$LocalTransferFromJson(Map<String, dynamic> json) =>
    LocalTransfer(
      beneficiaryDetails: json['beneficiaryDetails'] == null
          ? null
          : IndividualBasicInfo.fromJson(
              json['beneficiaryDetails'] as Map<String, dynamic>),
      transferDetails: json['transferDetails'] == null
          ? null
          : LocalTransferDetails.fromJson(
              json['transferDetails'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$LocalTransferToJson(LocalTransfer instance) =>
    <String, dynamic>{
      if (instance.beneficiaryDetails?.toJson() case final value?)
        'beneficiaryDetails': value,
      if (instance.transferDetails?.toJson() case final value?)
        'transferDetails': value,
    };

IndividualBasicInfo _$IndividualBasicInfoFromJson(Map<String, dynamic> json) =>
    IndividualBasicInfo(
      name: json['name'] as String?,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$IndividualBasicInfoToJson(
        IndividualBasicInfo instance) =>
    <String, dynamic>{
      if (instance.name case final value?) 'name': value,
      if (instance.description case final value?) 'description': value,
    };

IndividualBasicInfoIntl _$IndividualBasicInfoIntlFromJson(
        Map<String, dynamic> json) =>
    IndividualBasicInfoIntl(
      name: json['name'] as String?,
      description: json['description'] as String?,
      iban: json['iban'] as String?,
      accountNumber: json['accountNumber'] as String?,
      bic: json['bic'] as String?,
      routingNumber: json['routingNumber'] as String?,
    );

Map<String, dynamic> _$IndividualBasicInfoIntlToJson(
        IndividualBasicInfoIntl instance) =>
    <String, dynamic>{
      if (instance.name case final value?) 'name': value,
      if (instance.description case final value?) 'description': value,
      if (instance.iban case final value?) 'iban': value,
      if (instance.accountNumber case final value?) 'accountNumber': value,
      if (instance.bic case final value?) 'bic': value,
      if (instance.routingNumber case final value?) 'routingNumber': value,
    };

LocalTransferDetails _$LocalTransferDetailsFromJson(
        Map<String, dynamic> json) =>
    LocalTransferDetails(
      paymentDate: json['paymentDate'] as String?,
      amount: (json['amount'] as num?)?.toDouble(),
      fee: (json['fee'] as num?)?.toDouble(),
      bankName: json['bankName'] as String?,
      beneIban: json['beneIban'] as String?,
      txnReference: json['txnReference'] as String?,
      currency: json['currency'] as String?,
      remittanceInfo1: json['remittanceInfo1'] as String?,
      remittanceInfo2: json['remittanceInfo2'] as String?,
    );

Map<String, dynamic> _$LocalTransferDetailsToJson(
        LocalTransferDetails instance) =>
    <String, dynamic>{
      if (instance.paymentDate case final value?) 'paymentDate': value,
      if (instance.amount case final value?) 'amount': value,
      if (instance.fee case final value?) 'fee': value,
      if (instance.bankName case final value?) 'bankName': value,
      if (instance.beneIban case final value?) 'beneIban': value,
      if (instance.txnReference case final value?) 'txnReference': value,
      if (instance.currency case final value?) 'currency': value,
      if (instance.remittanceInfo1 case final value?) 'remittanceInfo1': value,
      if (instance.remittanceInfo2 case final value?) 'remittanceInfo2': value,
    };

InternationalTransferMultiUserRequest
    _$InternationalTransferMultiUserRequestFromJson(
            Map<String, dynamic> json) =>
        InternationalTransferMultiUserRequest(
          internationalTransfer: json['internationalTransfer'] == null
              ? null
              : InternationalTransfer.fromJson(
                  json['internationalTransfer'] as Map<String, dynamic>),
          id: json['id'] as String?,
          domainType: domainTypeFromJson(json['domainType']),
          status: requestStatusFromJson(json['status']),
          domainId: json['domainId'] as String?,
          authorId: json['authorId'] as String?,
          description: json['description'] as String?,
          approvers: (json['approvers'] as List<dynamic>?)
                  ?.map((e) =>
                      RequestApprover.fromJson(e as Map<String, dynamic>))
                  .toList() ??
              [],
          createdAt: json['createdAt'] == null
              ? null
              : DateTime.parse(json['createdAt'] as String),
          updatedAt: json['updatedAt'] == null
              ? null
              : DateTime.parse(json['updatedAt'] as String),
          attachments: (json['attachments'] as List<dynamic>?)
                  ?.map((e) => Attachment.fromJson(e as Map<String, dynamic>))
                  .toList() ??
              [],
        );

Map<String, dynamic> _$InternationalTransferMultiUserRequestToJson(
        InternationalTransferMultiUserRequest instance) =>
    <String, dynamic>{
      if (instance.internationalTransfer?.toJson() case final value?)
        'internationalTransfer': value,
      if (instance.id case final value?) 'id': value,
      if (domainTypeToJson(instance.domainType) case final value?)
        'domainType': value,
      if (requestStatusToJson(instance.status) case final value?)
        'status': value,
      if (instance.domainId case final value?) 'domainId': value,
      if (instance.authorId case final value?) 'authorId': value,
      if (instance.description case final value?) 'description': value,
      if (instance.approvers?.map((e) => e.toJson()).toList() case final value?)
        'approvers': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
      if (instance.attachments?.map((e) => e.toJson()).toList()
          case final value?)
        'attachments': value,
    };

InternationalTransfer _$InternationalTransferFromJson(
        Map<String, dynamic> json) =>
    InternationalTransfer(
      beneficiaryDetails: json['beneficiaryDetails'] == null
          ? null
          : IndividualBasicInfoIntl.fromJson(
              json['beneficiaryDetails'] as Map<String, dynamic>),
      transferDetails: json['transferDetails'] == null
          ? null
          : InternationalTransferDetails.fromJson(
              json['transferDetails'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$InternationalTransferToJson(
        InternationalTransfer instance) =>
    <String, dynamic>{
      if (instance.beneficiaryDetails?.toJson() case final value?)
        'beneficiaryDetails': value,
      if (instance.transferDetails?.toJson() case final value?)
        'transferDetails': value,
    };

InternationalTransferDetails _$InternationalTransferDetailsFromJson(
        Map<String, dynamic> json) =>
    InternationalTransferDetails(
      paymentDate: json['paymentDate'] as String?,
      iban: json['iban'] as String?,
      note: json['note'] as String?,
      transactionAmount: (json['transactionAmount'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
      exchangeRate: (json['exchangeRate'] as num?)?.toDouble(),
      valueAddedTax: (json['valueAddedTax'] as num?)?.toDouble(),
      transferFee: (json['transferFee'] as num?)?.toDouble(),
      totalFee: (json['totalFee'] as num?)?.toDouble(),
      totalAmount: (json['totalAmount'] as num?)?.toDouble(),
      txnReference: json['txnReference'] as String?,
      countryCode: json['countryCode'] as String?,
    );

Map<String, dynamic> _$InternationalTransferDetailsToJson(
        InternationalTransferDetails instance) =>
    <String, dynamic>{
      if (instance.paymentDate case final value?) 'paymentDate': value,
      if (instance.iban case final value?) 'iban': value,
      if (instance.note case final value?) 'note': value,
      if (instance.transactionAmount case final value?)
        'transactionAmount': value,
      if (instance.currency case final value?) 'currency': value,
      if (instance.exchangeRate case final value?) 'exchangeRate': value,
      if (instance.valueAddedTax case final value?) 'valueAddedTax': value,
      if (instance.transferFee case final value?) 'transferFee': value,
      if (instance.totalFee case final value?) 'totalFee': value,
      if (instance.totalAmount case final value?) 'totalAmount': value,
      if (instance.txnReference case final value?) 'txnReference': value,
      if (instance.countryCode case final value?) 'countryCode': value,
    };

UpdateRequest _$UpdateRequestFromJson(Map<String, dynamic> json) =>
    UpdateRequest(
      domainType: domainTypeFromJson(json['domainType']),
      description: json['description'] as String?,
      approvers: (json['approvers'] as List<dynamic>?)
              ?.map((e) => RequestApprover.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      attachments: (json['attachments'] as List<dynamic>?)
              ?.map((e) => CreateAttachment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$UpdateRequestToJson(UpdateRequest instance) =>
    <String, dynamic>{
      if (domainTypeToJson(instance.domainType) case final value?)
        'domainType': value,
      if (instance.description case final value?) 'description': value,
      if (instance.approvers?.map((e) => e.toJson()).toList() case final value?)
        'approvers': value,
      if (instance.attachments?.map((e) => e.toJson()).toList()
          case final value?)
        'attachments': value,
    };

RequestApprover _$RequestApproverFromJson(Map<String, dynamic> json) =>
    RequestApprover(
      id: json['id'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
    );

Map<String, dynamic> _$RequestApproverToJson(RequestApprover instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.firstName case final value?) 'firstName': value,
      if (instance.lastName case final value?) 'lastName': value,
    };

PotentialRequestApprover _$PotentialRequestApproverFromJson(
        Map<String, dynamic> json) =>
    PotentialRequestApprover(
      id: json['id'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
    );

Map<String, dynamic> _$PotentialRequestApproverToJson(
        PotentialRequestApprover instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.firstName case final value?) 'firstName': value,
      if (instance.lastName case final value?) 'lastName': value,
    };

Error _$ErrorFromJson(Map<String, dynamic> json) => Error(
      id: json['Id'] as String?,
      code: json['Code'] as String?,
      description: json['Description'] as String?,
      path: json['Path'] as String?,
      additionalInfo: json['AdditionalInfo'] as String?,
      context: json['Context'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ErrorToJson(Error instance) => <String, dynamic>{
      if (instance.id case final value?) 'Id': value,
      if (instance.code case final value?) 'Code': value,
      if (instance.description case final value?) 'Description': value,
      if (instance.path case final value?) 'Path': value,
      if (instance.additionalInfo case final value?) 'AdditionalInfo': value,
      if (instance.context case final value?) 'Context': value,
    };
