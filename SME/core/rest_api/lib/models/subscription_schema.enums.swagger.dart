import 'package:json_annotation/json_annotation.dart';
import 'package:collection/collection.dart';

enum LogoType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('IMAGE')
  image('IMAGE'),
  @JsonValue('ICON')
  icon('ICON'),
  @JsonValue('PICTOGRAM')
  pictogram('PICTOGRAM');

  final String? value;

  const LogoType(this.value);
}

enum PricePriceDisplayType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DESCRIPTION_ONLY')
  descriptionOnly('DESCRIPTION_ONLY'),
  @JsonValue('CURRENT')
  current('CURRENT'),
  @JsonValue('CURRENT_UNTIL_DATE')
  currentUntilDate('CURRENT_UNTIL_DATE'),
  @JsonValue('CURRENT_BASE_CROSSED')
  currentBaseCrossed('CURRENT_BASE_CROSSED'),
  @JsonValue('CURRENT_UNTIL_DATE_BASE_CROSSED')
  currentUntilDateBaseCrossed('CURRENT_UNTIL_DATE_BASE_CROSSED'),
  @JsonValue('FREE_FOR_LIFE')
  freeForLife('FREE_FOR_LIFE'),
  @JsonValue('FREE_BASE_CROSSED')
  freeBaseCrossed('FREE_BASE_CROSSED'),
  @JsonValue('FREE_UNTIL_DATE_BASE_CROSSED')
  freeUntilDateBaseCrossed('FREE_UNTIL_DATE_BASE_CROSSED'),
  @JsonValue('FREE_TRIAL')
  freeTrial('FREE_TRIAL'),
  @JsonValue('UNKNOWN')
  unknown('UNKNOWN');

  final String? value;

  const PricePriceDisplayType(this.value);
}

enum PromoBlockType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DEFAULT')
  $default('DEFAULT');

  final String? value;

  const PromoBlockType(this.value);
}

enum TariffInfoStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('ACTIVE')
  active('ACTIVE'),
  @JsonValue('NOT_ACTIVE')
  notActive('NOT_ACTIVE');

  final String? value;

  const TariffInfoStatus(this.value);
}

enum TariffInfoPlanShiftingType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('UPGRADE')
  upgrade('UPGRADE'),
  @JsonValue('DOWNGRADE')
  downgrade('DOWNGRADE'),
  @JsonValue('CURRENT')
  current('CURRENT');

  final String? value;

  const TariffInfoPlanShiftingType(this.value);
}

enum TextFragmentStyle {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('REGULAR')
  regular('REGULAR'),
  @JsonValue('STRIKETHROUGH')
  strikethrough('STRIKETHROUGH'),
  @JsonValue('BOLD')
  bold('BOLD');

  final String? value;

  const TextFragmentStyle(this.value);
}

enum BaseTariffInfoStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('ACTIVE')
  active('ACTIVE'),
  @JsonValue('NOT_ACTIVE')
  notActive('NOT_ACTIVE');

  final String? value;

  const BaseTariffInfoStatus(this.value);
}

enum ApiV1TariffsGetListSource {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('SETTINGS')
  settings('SETTINGS'),
  @JsonValue('ONBOARDING')
  onboarding('ONBOARDING');

  final String? value;

  const ApiV1TariffsGetListSource(this.value);
}

enum ApiV1TariffsGetBusinessType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DEFAULT')
  $default('DEFAULT');

  final String? value;

  const ApiV1TariffsGetBusinessType(this.value);
}
