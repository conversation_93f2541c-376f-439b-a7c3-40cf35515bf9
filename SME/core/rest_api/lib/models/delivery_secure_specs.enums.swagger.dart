import 'package:json_annotation/json_annotation.dart';
import 'package:collection/collection.dart';

enum ErrorMessageCode {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('BE_DELIVERY_INTERNAL_ERROR')
  beDeliveryInternalError('BE_DELIVERY_INTERNAL_ERROR'),
  @JsonValue('BE_DELIVERY_INITIATION_DOWNSTREAM_ERROR')
  beDeliveryInitiationDownstreamError(
      'BE_DELIVERY_INITIATION_DOWNSTREAM_ERROR');

  final String? value;

  const ErrorMessageCode(this.value);
}

enum DeliveryInfoDeliveryFlow {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('ACCOUNT_MAINTENANCE')
  accountMaintenance('ACCOUNT_MAINTENANCE'),
  @JsonValue('NORMAL')
  normal('NORMAL'),
  @JsonValue('OPTIONAL')
  optional('OPTIONAL');

  final String? value;

  const DeliveryInfoDeliveryFlow(this.value);
}

enum ApiV1DeliveryUiElementsGet$Response$ItemName {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('ACTIVATE_CARD_BANNER')
  activateCardBanner('ACTIVATE_CARD_BANNER'),
  @JsonValue('MANAGE_CARD_BANNER')
  manageCardBanner('MANAGE_CARD_BANNER'),
  @JsonValue('ORDER_PHYSICAL_CARD_BANNER')
  orderPhysicalCardBanner('ORDER_PHYSICAL_CARD_BANNER'),
  @JsonValue('RUNNER_DOCUMENT_VERIFICATION_INITIATED_BANNER')
  runnerDocumentVerificationInitiatedBanner(
      'RUNNER_DOCUMENT_VERIFICATION_INITIATED_BANNER'),
  @JsonValue('RUNNER_DOCUMENTS_IN_VERIFICATION_BANNER')
  runnerDocumentsInVerificationBanner(
      'RUNNER_DOCUMENTS_IN_VERIFICATION_BANNER');

  final String? value;

  const ApiV1DeliveryUiElementsGet$Response$ItemName(this.value);
}
