import 'package:json_annotation/json_annotation.dart';
import 'package:collection/collection.dart';

enum RequestStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DRAFT')
  draft('DRAFT'),
  @JsonValue('ACTIVE')
  active('ACTIVE'),
  @JsonValue('REJECTED')
  rejected('REJECTED'),
  @JsonValue('APPROVED')
  approved('APPROVED'),
  @JsonValue('COMPLETED')
  completed('COMPLETED');

  final String? value;

  const RequestStatus(this.value);
}

enum DomainType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('LOCAL_TRANSFER')
  localTransfer('LOCAL_TRANSFER'),
  @JsonValue('INTERNATIONAL_TRANSFER')
  internationalTransfer('INTERNATIONAL_TRANSFER');

  final String? value;

  const DomainType(this.value);
}
