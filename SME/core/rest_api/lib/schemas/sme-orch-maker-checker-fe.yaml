openapi: 3.0.1
info:
  title: Lending SME Public API
  description: |
    The SME Orchestrator Service handles interaction
    between frontend and backend services for SME product.
  version: 1.0.0
servers:
  - url: http://localhost:8085
    description: Generated server url
paths:
  /api/v1/supplier/invoices:
    get:
      tags:
        - sme-orchestrator-service
      operationId: getInvoices
      parameters:
        - name: multiUserRequestId
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JocataViewInvoicesResponse'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetail'
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetail'
  /api/v1/supplier/invoices/summary:
    get:
      tags:
        - sme-orchestrator-service
      operationId: getRequestsSummary
      parameters:
        - name: requestIds
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/JocataInvoiceSummaryResponse'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetail'
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProblemDetail'
components:
  schemas:
    Invoice:
      type: object
      properties:
        invoiceId:
          type: string
        invoiceNumber:
          type: string
        status:
          type: string
        requestDate:
          type: string
        dueDate:
          type: string
        invoiceAmount:
          type: string
        interest:
          type: string
        amountToSupplier:
          type: string
        currency:
          type: string
        supplierName:
          type: string
        preparer:
          type: string
    JocataViewInvoicesResponse:
      type: object
      properties:
        invoicesCount:
          type: integer
          format: int32
        totalInvoiceAmount:
          type: string
        totalDiscount:
          type: string
        currency:
          type: string
        invoices:
          type: array
          items:
            $ref: '#/components/schemas/Invoice'
    InvoicesRequestSummary:
      required:
        - invoicesCount
        - totalInvoiceAmount
        - totalDiscount
        - currency
      type: object
      properties:
        invoicesCount:
          type: integer
          format: int32
        totalInvoiceAmount:
          type: number
        totalDiscount:
          type: number
        currency:
          type: string
          enum:
            - ALL
            - DZD
            - ARS
            - AUD
            - BSD
            - BHD
            - BDT
            - AMD
            - BBD
            - BMD
            - BTN
            - BOB
            - BWP
            - BZD
            - SBD
            - BND
            - MMK
            - BIF
            - KHR
            - CAD
            - CVE
            - KYD
            - LKR
            - CLP
            - CNY
            - COP
            - KMF
            - CRC
            - HRK
            - CUP
            - CZK
            - DKK
            - DOP
            - SVC
            - ETB
            - ERN
            - FKP
            - FJD
            - DJF
            - GMD
            - GIP
            - GTQ
            - GNF
            - GYD
            - HTG
            - HNL
            - HKD
            - HUF
            - ISK
            - INR
            - IDR
            - IRR
            - IQD
            - ILS
            - JMD
            - JPY
            - KZT
            - JOD
            - KES
            - KPW
            - KRW
            - KWD
            - KGS
            - LAK
            - LBP
            - LSL
            - LRD
            - LYD
            - MOP
            - MWK
            - MYR
            - MVR
            - MUR
            - MXN
            - MNT
            - MDL
            - MAD
            - OMR
            - NAD
            - NPR
            - ANG
            - AWG
            - VUV
            - NZD
            - NIO
            - NGN
            - NOK
            - PKR
            - PAB
            - PGK
            - PYG
            - PEN
            - PHP
            - QAR
            - RUB
            - RWF
            - SHP
            - SAR
            - SCR
            - SLL
            - SGD
            - VND
            - SOS
            - ZAR
            - SSP
            - SZL
            - SEK
            - CHF
            - SYP
            - THB
            - TOP
            - TTD
            - AED
            - TND
            - UGX
            - MKD
            - EGP
            - GBP
            - TZS
            - USD
            - UYU
            - UZS
            - WST
            - YER
            - TWD
            - VED
            - UYW
            - VES
            - MRU
            - STN
            - CUC
            - ZWL
            - BYN
            - TMT
            - GHS
            - SDG
            - UYI
            - RSD
            - MZN
            - AZN
            - RON
            - CHE
            - CHW
            - TRY
            - XAF
            - XCD
            - XOF
            - XPF
            - ZMW
            - SRD
            - MGA
            - COU
            - AFN
            - TJS
            - AOA
            - BGN
            - CDF
            - BAM
            - EUR
            - MXV
            - UAH
            - GEL
            - BOV
            - PLN
            - BRL
            - CLF
            - USN
    JocataInvoiceSummaryResponse:
      type: object
      properties:
        response:
          type: array
          items:
            $ref: '#/components/schemas/InvoicesRequestSummary'
    ProblemDetail:
      type: object
      properties:
        type:
          type: string
          format: uri
        title:
          type: string
        status:
          type: integer
          format: int32
        detail:
          type: string
        instance:
          type: string
          format: uri
        properties:
          type: object
          additionalProperties:
            type: object
  securitySchemes:
    jwt:
      type: http
      description: Wio SME
      scheme: bearer
