import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:logging_api/logging.dart';
import 'package:sme_core_ui_desktop/index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_subscription_api/domain/subscription_interactor.dart';
import 'package:wio_feature_subscription_api/navigation/subscription_feature_navigation_config.dart';
import 'package:wio_feature_subscription_ui_desktop/src/analytics/subscription_analytics.dart';
import 'package:wio_feature_subscription_ui_desktop/src/l10n/subscription_ui_desktop_localizations.g.dart';
import 'package:wio_feature_subscription_ui_desktop/src/navigation/subscription_router.dart';
import 'package:wio_feature_subscription_ui_desktop/src/screens/subscription/subscription_cubit.dart';

class SubscriptionFeatureDependencyModuleResolver {
  static void register() {
    _registerRouter();
    _registerLocalizations();
    _registerCubit();
  }

  static void _registerRouter() {
    DependencyProvider.registerLazySingleton(() => SubscriptionRouter());

    DependencyProvider.registerLazySingleton<NavigationRouter>(
      () => DependencyProvider.get<SubscriptionRouter>(),
      instanceName: SubscriptionFeatureNavigationConfig.name,
    );
  }

  static void _registerLocalizations() {
    DependencyProvider.registerLazySingleton(
      () => SubscriptionUiLocalizations.of(
        DependencyProvider.get<BuildContext>(),
      ),
    );
  }

  static void _registerCubit() {
    DependencyProvider.registerLazySingleton<SubscriptionAnalytics>(
      () => SubscriptionAnalyticsImpl(
        analyticsAbstractTrackerFactory:
            DependencyProvider.get<AnalyticsAbstractTrackerFactory>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<SubscriptionCubit, String?,
        void>(
      (ded, _) => SubscriptionCubit(
        subscriptionInteractor:
            DependencyProvider.get<SubscriptionInteractor>(),
        logger: DependencyProvider.get<Logger>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        ded: ded,
        notificationService: DependencyProvider.get<NewNotificationService>(),
        localizations: DependencyProvider.get<SubscriptionUiLocalizations>(),
        subscriptionAnalytics: DependencyProvider.get<SubscriptionAnalytics>(),
      ),
    );
  }
}
