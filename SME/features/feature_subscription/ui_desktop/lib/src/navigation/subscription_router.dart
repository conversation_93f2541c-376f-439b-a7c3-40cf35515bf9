import 'package:flutter/material.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_subscription_api/navigation/subscription_feature_navigation_config.dart';
import 'package:wio_feature_subscription_ui_desktop/src/screens/subscription/subscription_page.dart'
    deferred as subscription_page;

class SubscriptionRouter extends NavigationRouter {
  @override
  Route<Object?> getScreenRoute(RouteSettings settings) {
    final config = settings.arguments;
    if (config is SubscriptionFeatureNavigationConfig) {
      return toDeferredRoute(
        () => subscription_page.SubscriptionPage(
          ded: config.ded,
        ),
        settings,
        subscription_page.loadLibrary,
      );
    }
    throw Exception('Unknown $config for the $runtimeType');
  }
}
