// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'info_block.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$InfoBlock {
  String get title => throw _privateConstructorUsedError;
  List<MainCharge> get charges => throw _privateConstructorUsedError;
  TextBlock? get description => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $InfoBlockCopyWith<InfoBlock> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InfoBlockCopyWith<$Res> {
  factory $InfoBlockCopyWith(InfoBlock value, $Res Function(InfoBlock) then) =
      _$InfoBlockCopyWithImpl<$Res>;
  $Res call({String title, List<MainCharge> charges, TextBlock? description});

  $TextBlockCopyWith<$Res>? get description;
}

/// @nodoc
class _$InfoBlockCopyWithImpl<$Res> implements $InfoBlockCopyWith<$Res> {
  _$InfoBlockCopyWithImpl(this._value, this._then);

  final InfoBlock _value;
  // ignore: unused_field
  final $Res Function(InfoBlock) _then;

  @override
  $Res call({
    Object? title = freezed,
    Object? charges = freezed,
    Object? description = freezed,
  }) {
    return _then(_value.copyWith(
      title: title == freezed
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      charges: charges == freezed
          ? _value.charges
          : charges // ignore: cast_nullable_to_non_nullable
              as List<MainCharge>,
      description: description == freezed
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as TextBlock?,
    ));
  }

  @override
  $TextBlockCopyWith<$Res>? get description {
    if (_value.description == null) {
      return null;
    }

    return $TextBlockCopyWith<$Res>(_value.description!, (value) {
      return _then(_value.copyWith(description: value));
    });
  }
}

/// @nodoc
abstract class _$$_InfoBlockCopyWith<$Res> implements $InfoBlockCopyWith<$Res> {
  factory _$$_InfoBlockCopyWith(
          _$_InfoBlock value, $Res Function(_$_InfoBlock) then) =
      __$$_InfoBlockCopyWithImpl<$Res>;
  @override
  $Res call({String title, List<MainCharge> charges, TextBlock? description});

  @override
  $TextBlockCopyWith<$Res>? get description;
}

/// @nodoc
class __$$_InfoBlockCopyWithImpl<$Res> extends _$InfoBlockCopyWithImpl<$Res>
    implements _$$_InfoBlockCopyWith<$Res> {
  __$$_InfoBlockCopyWithImpl(
      _$_InfoBlock _value, $Res Function(_$_InfoBlock) _then)
      : super(_value, (v) => _then(v as _$_InfoBlock));

  @override
  _$_InfoBlock get _value => super._value as _$_InfoBlock;

  @override
  $Res call({
    Object? title = freezed,
    Object? charges = freezed,
    Object? description = freezed,
  }) {
    return _then(_$_InfoBlock(
      title: title == freezed
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      charges: charges == freezed
          ? _value._charges
          : charges // ignore: cast_nullable_to_non_nullable
              as List<MainCharge>,
      description: description == freezed
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as TextBlock?,
    ));
  }
}

/// @nodoc

class _$_InfoBlock implements _InfoBlock {
  const _$_InfoBlock(
      {required this.title,
      required final List<MainCharge> charges,
      this.description})
      : _charges = charges;

  @override
  final String title;
  final List<MainCharge> _charges;
  @override
  List<MainCharge> get charges {
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_charges);
  }

  @override
  final TextBlock? description;

  @override
  String toString() {
    return 'InfoBlock(title: $title, charges: $charges, description: $description)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_InfoBlock &&
            const DeepCollectionEquality().equals(other.title, title) &&
            const DeepCollectionEquality().equals(other._charges, _charges) &&
            const DeepCollectionEquality()
                .equals(other.description, description));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(title),
      const DeepCollectionEquality().hash(_charges),
      const DeepCollectionEquality().hash(description));

  @JsonKey(ignore: true)
  @override
  _$$_InfoBlockCopyWith<_$_InfoBlock> get copyWith =>
      __$$_InfoBlockCopyWithImpl<_$_InfoBlock>(this, _$identity);
}

abstract class _InfoBlock implements InfoBlock {
  const factory _InfoBlock(
      {required final String title,
      required final List<MainCharge> charges,
      final TextBlock? description}) = _$_InfoBlock;

  @override
  String get title => throw _privateConstructorUsedError;
  @override
  List<MainCharge> get charges => throw _privateConstructorUsedError;
  @override
  TextBlock? get description => throw _privateConstructorUsedError;
  @override
  @JsonKey(ignore: true)
  _$$_InfoBlockCopyWith<_$_InfoBlock> get copyWith =>
      throw _privateConstructorUsedError;
}
