import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_subscription_api/domain/models/subscription_plan/text_fragment.dart';

part 'subscription_info.freezed.dart';

/// Model that is used to display price for subscription plan
/// in format that controlled by backend
@freezed
class SubscriptionInfo with _$SubscriptionInfo {
  const factory SubscriptionInfo({
    required TextFragment firstTextLine,
    TextFragment? secondTextLine,
  }) = _SubscriptionInfo;
}
