import 'package:domain/data/data.dart';
import 'package:wio_feature_subscription_api/subscription_api.dart';

class SubscriptionInteractorImpl implements SubscriptionInteractor {
  final SubscriptionRepository _subscriptionRepository;
  final RestSubscriptionRepository _restSubscriptionRepository;
  final BaseTariffInfoStreamRepository _baseTariffInfoStreamRepository;

  const SubscriptionInteractorImpl({
    required SubscriptionRepository subscriptionRepository,
    required BaseTariffInfoStreamRepository baseTariffInfoStreamRepository,
    required RestSubscriptionRepository restSubscriptionRepository,
  })  : _subscriptionRepository = subscriptionRepository,
        _baseTariffInfoStreamRepository = baseTariffInfoStreamRepository,
        _restSubscriptionRepository = restSubscriptionRepository;

  @override
  Future<TariffInfo> getTariffInfo() {
    return _subscriptionRepository.getTariffInfo();
  }

  @override
  Future<BaseTariffInfo> getBaseTariffInfo() {
    return _subscriptionRepository.getBaseTariffInfo();
  }

  @override
  Future<void> subscribeToTariff(
    String tariffId, {
    SubscriptionConfig? config,
  }) async {
    if (config != null && config.requestMode == RequestMode.rest) {
      return _restSubscriptionRepository.subscribeToPlan(
        planId: tariffId,
        config: config,
      );
    }

    return _subscriptionRepository
        .subscribeToTariff(tariffId)
        .then((_) => _baseTariffInfoStreamRepository.refresh());
  }

  @override
  Future<List<TariffInfo>> getTariffInfoList() {
    return _subscriptionRepository.getTariffInfoList();
  }

  @override
  Future<List<SubscriptionPlan>> getSubscriptionPlanList(
    ListSource source, {
    BusinessType? businessType,
    SubscriptionConfig? config,
  }) {
    if (config != null &&
        config.ded != null &&
        config.requestMode == RequestMode.rest) {
      return _restSubscriptionRepository.fetchSubscriptionPlans(
        listSource: source,
        businessType: businessType,
        config: config,
      );
    }

    return _subscriptionRepository.getSubscriptionPlanList(source);
  }

  @override
  Future<void> submitOnboardingSubscriptionPlan(
    String planId, {
    List<String>? selectedCategories,
  }) {
    return _subscriptionRepository.submitOnboardingSubscriptionPlan(
      planId,
      selectedCategories: selectedCategories,
    );
  }

  @override
  Stream<Data<BaseTariffInfo>> getBaseTariffInfoStream() {
    return _baseTariffInfoStreamRepository.observeBaseTariffInfo();
  }
}
