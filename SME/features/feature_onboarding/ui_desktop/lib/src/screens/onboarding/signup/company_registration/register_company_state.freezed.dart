// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'register_company_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RegisterCompanyState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(BusinessTypes businessTypes) businessTypesLoaded,
    required TResult Function() businessTypesLoadingFailure,
    required TResult Function() registerBusinessSuccess,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(BusinessTypes businessTypes)? businessTypesLoaded,
    TResult? Function()? businessTypesLoadingFailure,
    TResult? Function()? registerBusinessSuccess,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(BusinessTypes businessTypes)? businessTypesLoaded,
    TResult Function()? businessTypesLoadingFailure,
    TResult Function()? registerBusinessSuccess,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(RegisterCompanyLoadingState value) loading,
    required TResult Function(RegisterCompanyBusinessTypesLoadedState value)
        businessTypesLoaded,
    required TResult Function(
            RegisterCompanyBusinessTypesLoadingErrorState value)
        businessTypesLoadingFailure,
    required TResult Function(RegisterCompanySucessState value)
        registerBusinessSuccess,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(RegisterCompanyLoadingState value)? loading,
    TResult? Function(RegisterCompanyBusinessTypesLoadedState value)?
        businessTypesLoaded,
    TResult? Function(RegisterCompanyBusinessTypesLoadingErrorState value)?
        businessTypesLoadingFailure,
    TResult? Function(RegisterCompanySucessState value)?
        registerBusinessSuccess,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(RegisterCompanyLoadingState value)? loading,
    TResult Function(RegisterCompanyBusinessTypesLoadedState value)?
        businessTypesLoaded,
    TResult Function(RegisterCompanyBusinessTypesLoadingErrorState value)?
        businessTypesLoadingFailure,
    TResult Function(RegisterCompanySucessState value)? registerBusinessSuccess,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegisterCompanyStateCopyWith<$Res> {
  factory $RegisterCompanyStateCopyWith(RegisterCompanyState value,
          $Res Function(RegisterCompanyState) then) =
      _$RegisterCompanyStateCopyWithImpl<$Res, RegisterCompanyState>;
}

/// @nodoc
class _$RegisterCompanyStateCopyWithImpl<$Res,
        $Val extends RegisterCompanyState>
    implements $RegisterCompanyStateCopyWith<$Res> {
  _$RegisterCompanyStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$RegisterCompanyLoadingStateImplCopyWith<$Res> {
  factory _$$RegisterCompanyLoadingStateImplCopyWith(
          _$RegisterCompanyLoadingStateImpl value,
          $Res Function(_$RegisterCompanyLoadingStateImpl) then) =
      __$$RegisterCompanyLoadingStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RegisterCompanyLoadingStateImplCopyWithImpl<$Res>
    extends _$RegisterCompanyStateCopyWithImpl<$Res,
        _$RegisterCompanyLoadingStateImpl>
    implements _$$RegisterCompanyLoadingStateImplCopyWith<$Res> {
  __$$RegisterCompanyLoadingStateImplCopyWithImpl(
      _$RegisterCompanyLoadingStateImpl _value,
      $Res Function(_$RegisterCompanyLoadingStateImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$RegisterCompanyLoadingStateImpl implements RegisterCompanyLoadingState {
  const _$RegisterCompanyLoadingStateImpl();

  @override
  String toString() {
    return 'RegisterCompanyState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterCompanyLoadingStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(BusinessTypes businessTypes) businessTypesLoaded,
    required TResult Function() businessTypesLoadingFailure,
    required TResult Function() registerBusinessSuccess,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(BusinessTypes businessTypes)? businessTypesLoaded,
    TResult? Function()? businessTypesLoadingFailure,
    TResult? Function()? registerBusinessSuccess,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(BusinessTypes businessTypes)? businessTypesLoaded,
    TResult Function()? businessTypesLoadingFailure,
    TResult Function()? registerBusinessSuccess,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(RegisterCompanyLoadingState value) loading,
    required TResult Function(RegisterCompanyBusinessTypesLoadedState value)
        businessTypesLoaded,
    required TResult Function(
            RegisterCompanyBusinessTypesLoadingErrorState value)
        businessTypesLoadingFailure,
    required TResult Function(RegisterCompanySucessState value)
        registerBusinessSuccess,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(RegisterCompanyLoadingState value)? loading,
    TResult? Function(RegisterCompanyBusinessTypesLoadedState value)?
        businessTypesLoaded,
    TResult? Function(RegisterCompanyBusinessTypesLoadingErrorState value)?
        businessTypesLoadingFailure,
    TResult? Function(RegisterCompanySucessState value)?
        registerBusinessSuccess,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(RegisterCompanyLoadingState value)? loading,
    TResult Function(RegisterCompanyBusinessTypesLoadedState value)?
        businessTypesLoaded,
    TResult Function(RegisterCompanyBusinessTypesLoadingErrorState value)?
        businessTypesLoadingFailure,
    TResult Function(RegisterCompanySucessState value)? registerBusinessSuccess,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class RegisterCompanyLoadingState implements RegisterCompanyState {
  const factory RegisterCompanyLoadingState() =
      _$RegisterCompanyLoadingStateImpl;
}

/// @nodoc
abstract class _$$RegisterCompanyBusinessTypesLoadedStateImplCopyWith<$Res> {
  factory _$$RegisterCompanyBusinessTypesLoadedStateImplCopyWith(
          _$RegisterCompanyBusinessTypesLoadedStateImpl value,
          $Res Function(_$RegisterCompanyBusinessTypesLoadedStateImpl) then) =
      __$$RegisterCompanyBusinessTypesLoadedStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BusinessTypes businessTypes});

  $BusinessTypesCopyWith<$Res> get businessTypes;
}

/// @nodoc
class __$$RegisterCompanyBusinessTypesLoadedStateImplCopyWithImpl<$Res>
    extends _$RegisterCompanyStateCopyWithImpl<$Res,
        _$RegisterCompanyBusinessTypesLoadedStateImpl>
    implements _$$RegisterCompanyBusinessTypesLoadedStateImplCopyWith<$Res> {
  __$$RegisterCompanyBusinessTypesLoadedStateImplCopyWithImpl(
      _$RegisterCompanyBusinessTypesLoadedStateImpl _value,
      $Res Function(_$RegisterCompanyBusinessTypesLoadedStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? businessTypes = null,
  }) {
    return _then(_$RegisterCompanyBusinessTypesLoadedStateImpl(
      businessTypes: null == businessTypes
          ? _value.businessTypes
          : businessTypes // ignore: cast_nullable_to_non_nullable
              as BusinessTypes,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $BusinessTypesCopyWith<$Res> get businessTypes {
    return $BusinessTypesCopyWith<$Res>(_value.businessTypes, (value) {
      return _then(_value.copyWith(businessTypes: value));
    });
  }
}

/// @nodoc

class _$RegisterCompanyBusinessTypesLoadedStateImpl
    implements RegisterCompanyBusinessTypesLoadedState {
  const _$RegisterCompanyBusinessTypesLoadedStateImpl(
      {required this.businessTypes});

  @override
  final BusinessTypes businessTypes;

  @override
  String toString() {
    return 'RegisterCompanyState.businessTypesLoaded(businessTypes: $businessTypes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterCompanyBusinessTypesLoadedStateImpl &&
            (identical(other.businessTypes, businessTypes) ||
                other.businessTypes == businessTypes));
  }

  @override
  int get hashCode => Object.hash(runtimeType, businessTypes);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RegisterCompanyBusinessTypesLoadedStateImplCopyWith<
          _$RegisterCompanyBusinessTypesLoadedStateImpl>
      get copyWith =>
          __$$RegisterCompanyBusinessTypesLoadedStateImplCopyWithImpl<
              _$RegisterCompanyBusinessTypesLoadedStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(BusinessTypes businessTypes) businessTypesLoaded,
    required TResult Function() businessTypesLoadingFailure,
    required TResult Function() registerBusinessSuccess,
  }) {
    return businessTypesLoaded(businessTypes);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(BusinessTypes businessTypes)? businessTypesLoaded,
    TResult? Function()? businessTypesLoadingFailure,
    TResult? Function()? registerBusinessSuccess,
  }) {
    return businessTypesLoaded?.call(businessTypes);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(BusinessTypes businessTypes)? businessTypesLoaded,
    TResult Function()? businessTypesLoadingFailure,
    TResult Function()? registerBusinessSuccess,
    required TResult orElse(),
  }) {
    if (businessTypesLoaded != null) {
      return businessTypesLoaded(businessTypes);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(RegisterCompanyLoadingState value) loading,
    required TResult Function(RegisterCompanyBusinessTypesLoadedState value)
        businessTypesLoaded,
    required TResult Function(
            RegisterCompanyBusinessTypesLoadingErrorState value)
        businessTypesLoadingFailure,
    required TResult Function(RegisterCompanySucessState value)
        registerBusinessSuccess,
  }) {
    return businessTypesLoaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(RegisterCompanyLoadingState value)? loading,
    TResult? Function(RegisterCompanyBusinessTypesLoadedState value)?
        businessTypesLoaded,
    TResult? Function(RegisterCompanyBusinessTypesLoadingErrorState value)?
        businessTypesLoadingFailure,
    TResult? Function(RegisterCompanySucessState value)?
        registerBusinessSuccess,
  }) {
    return businessTypesLoaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(RegisterCompanyLoadingState value)? loading,
    TResult Function(RegisterCompanyBusinessTypesLoadedState value)?
        businessTypesLoaded,
    TResult Function(RegisterCompanyBusinessTypesLoadingErrorState value)?
        businessTypesLoadingFailure,
    TResult Function(RegisterCompanySucessState value)? registerBusinessSuccess,
    required TResult orElse(),
  }) {
    if (businessTypesLoaded != null) {
      return businessTypesLoaded(this);
    }
    return orElse();
  }
}

abstract class RegisterCompanyBusinessTypesLoadedState
    implements RegisterCompanyState {
  const factory RegisterCompanyBusinessTypesLoadedState(
          {required final BusinessTypes businessTypes}) =
      _$RegisterCompanyBusinessTypesLoadedStateImpl;

  BusinessTypes get businessTypes;
  @JsonKey(ignore: true)
  _$$RegisterCompanyBusinessTypesLoadedStateImplCopyWith<
          _$RegisterCompanyBusinessTypesLoadedStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RegisterCompanyBusinessTypesLoadingErrorStateImplCopyWith<
    $Res> {
  factory _$$RegisterCompanyBusinessTypesLoadingErrorStateImplCopyWith(
          _$RegisterCompanyBusinessTypesLoadingErrorStateImpl value,
          $Res Function(_$RegisterCompanyBusinessTypesLoadingErrorStateImpl)
              then) =
      __$$RegisterCompanyBusinessTypesLoadingErrorStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RegisterCompanyBusinessTypesLoadingErrorStateImplCopyWithImpl<$Res>
    extends _$RegisterCompanyStateCopyWithImpl<$Res,
        _$RegisterCompanyBusinessTypesLoadingErrorStateImpl>
    implements
        _$$RegisterCompanyBusinessTypesLoadingErrorStateImplCopyWith<$Res> {
  __$$RegisterCompanyBusinessTypesLoadingErrorStateImplCopyWithImpl(
      _$RegisterCompanyBusinessTypesLoadingErrorStateImpl _value,
      $Res Function(_$RegisterCompanyBusinessTypesLoadingErrorStateImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$RegisterCompanyBusinessTypesLoadingErrorStateImpl
    implements RegisterCompanyBusinessTypesLoadingErrorState {
  const _$RegisterCompanyBusinessTypesLoadingErrorStateImpl();

  @override
  String toString() {
    return 'RegisterCompanyState.businessTypesLoadingFailure()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterCompanyBusinessTypesLoadingErrorStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(BusinessTypes businessTypes) businessTypesLoaded,
    required TResult Function() businessTypesLoadingFailure,
    required TResult Function() registerBusinessSuccess,
  }) {
    return businessTypesLoadingFailure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(BusinessTypes businessTypes)? businessTypesLoaded,
    TResult? Function()? businessTypesLoadingFailure,
    TResult? Function()? registerBusinessSuccess,
  }) {
    return businessTypesLoadingFailure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(BusinessTypes businessTypes)? businessTypesLoaded,
    TResult Function()? businessTypesLoadingFailure,
    TResult Function()? registerBusinessSuccess,
    required TResult orElse(),
  }) {
    if (businessTypesLoadingFailure != null) {
      return businessTypesLoadingFailure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(RegisterCompanyLoadingState value) loading,
    required TResult Function(RegisterCompanyBusinessTypesLoadedState value)
        businessTypesLoaded,
    required TResult Function(
            RegisterCompanyBusinessTypesLoadingErrorState value)
        businessTypesLoadingFailure,
    required TResult Function(RegisterCompanySucessState value)
        registerBusinessSuccess,
  }) {
    return businessTypesLoadingFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(RegisterCompanyLoadingState value)? loading,
    TResult? Function(RegisterCompanyBusinessTypesLoadedState value)?
        businessTypesLoaded,
    TResult? Function(RegisterCompanyBusinessTypesLoadingErrorState value)?
        businessTypesLoadingFailure,
    TResult? Function(RegisterCompanySucessState value)?
        registerBusinessSuccess,
  }) {
    return businessTypesLoadingFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(RegisterCompanyLoadingState value)? loading,
    TResult Function(RegisterCompanyBusinessTypesLoadedState value)?
        businessTypesLoaded,
    TResult Function(RegisterCompanyBusinessTypesLoadingErrorState value)?
        businessTypesLoadingFailure,
    TResult Function(RegisterCompanySucessState value)? registerBusinessSuccess,
    required TResult orElse(),
  }) {
    if (businessTypesLoadingFailure != null) {
      return businessTypesLoadingFailure(this);
    }
    return orElse();
  }
}

abstract class RegisterCompanyBusinessTypesLoadingErrorState
    implements RegisterCompanyState {
  const factory RegisterCompanyBusinessTypesLoadingErrorState() =
      _$RegisterCompanyBusinessTypesLoadingErrorStateImpl;
}

/// @nodoc
abstract class _$$RegisterCompanySucessStateImplCopyWith<$Res> {
  factory _$$RegisterCompanySucessStateImplCopyWith(
          _$RegisterCompanySucessStateImpl value,
          $Res Function(_$RegisterCompanySucessStateImpl) then) =
      __$$RegisterCompanySucessStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RegisterCompanySucessStateImplCopyWithImpl<$Res>
    extends _$RegisterCompanyStateCopyWithImpl<$Res,
        _$RegisterCompanySucessStateImpl>
    implements _$$RegisterCompanySucessStateImplCopyWith<$Res> {
  __$$RegisterCompanySucessStateImplCopyWithImpl(
      _$RegisterCompanySucessStateImpl _value,
      $Res Function(_$RegisterCompanySucessStateImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$RegisterCompanySucessStateImpl implements RegisterCompanySucessState {
  const _$RegisterCompanySucessStateImpl();

  @override
  String toString() {
    return 'RegisterCompanyState.registerBusinessSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterCompanySucessStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(BusinessTypes businessTypes) businessTypesLoaded,
    required TResult Function() businessTypesLoadingFailure,
    required TResult Function() registerBusinessSuccess,
  }) {
    return registerBusinessSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(BusinessTypes businessTypes)? businessTypesLoaded,
    TResult? Function()? businessTypesLoadingFailure,
    TResult? Function()? registerBusinessSuccess,
  }) {
    return registerBusinessSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(BusinessTypes businessTypes)? businessTypesLoaded,
    TResult Function()? businessTypesLoadingFailure,
    TResult Function()? registerBusinessSuccess,
    required TResult orElse(),
  }) {
    if (registerBusinessSuccess != null) {
      return registerBusinessSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(RegisterCompanyLoadingState value) loading,
    required TResult Function(RegisterCompanyBusinessTypesLoadedState value)
        businessTypesLoaded,
    required TResult Function(
            RegisterCompanyBusinessTypesLoadingErrorState value)
        businessTypesLoadingFailure,
    required TResult Function(RegisterCompanySucessState value)
        registerBusinessSuccess,
  }) {
    return registerBusinessSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(RegisterCompanyLoadingState value)? loading,
    TResult? Function(RegisterCompanyBusinessTypesLoadedState value)?
        businessTypesLoaded,
    TResult? Function(RegisterCompanyBusinessTypesLoadingErrorState value)?
        businessTypesLoadingFailure,
    TResult? Function(RegisterCompanySucessState value)?
        registerBusinessSuccess,
  }) {
    return registerBusinessSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(RegisterCompanyLoadingState value)? loading,
    TResult Function(RegisterCompanyBusinessTypesLoadedState value)?
        businessTypesLoaded,
    TResult Function(RegisterCompanyBusinessTypesLoadingErrorState value)?
        businessTypesLoadingFailure,
    TResult Function(RegisterCompanySucessState value)? registerBusinessSuccess,
    required TResult orElse(),
  }) {
    if (registerBusinessSuccess != null) {
      return registerBusinessSuccess(this);
    }
    return orElse();
  }
}

abstract class RegisterCompanySucessState implements RegisterCompanyState {
  const factory RegisterCompanySucessState() = _$RegisterCompanySucessStateImpl;
}
