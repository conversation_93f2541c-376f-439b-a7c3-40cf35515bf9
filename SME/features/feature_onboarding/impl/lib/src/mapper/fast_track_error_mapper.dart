import 'package:sme_rest_api/models/identity.swagger.dart' as swagger;
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_onboarding_api/domain/exception/trade_license_validation_exception.dart';

class FastTrackErrorMapper {
  static TradeLicenseValidationException? mapToTradeLicenseValidationException(
    Map<String, dynamic> json,
  ) {
    final code = json['Code'] as String?;
    final description = json['Description'] as String?;

    final errorCode = fromBackendString(code);

    if (errorCode == null) {
      return null;
    }

    return TradeLicenseValidationException(
      code: errorCode,
      description: description,
    );
  }

  static HttpRequestException mapToGeneralException(
    Map<String, dynamic> json,
  ) {
    final error = swagger.ErrorMessage.fromJson(json);

    return ApiException<void>(
      id: 'UNKNOWN_ERROR',
      code: error.code ?? '',
      message: error.description,
      subText: error.subtext,
    );
  }
}

DulServiceConsentError? fromBackendString(String? value) {
  switch (value) {
    case 'BE_ONBOARDING_DUL_SERVICE_CONSENT_PENDING':
      return DulServiceConsentError.pending;
    case 'BE_ONBOARDING_DUL_SERVICE_CONSENT_NOT_FOUND':
      return DulServiceConsentError.notFound;
    case 'BE_ONBOARDING_DUL_SERVICE_CONSENT_EXPIRED':
      return DulServiceConsentError.expired;
    case 'BE_ONBOARDING_DUL_SERVICE_CONSENT_REJECTED':
      return DulServiceConsentError.rejected;
    default:
      return null;
  }
}
