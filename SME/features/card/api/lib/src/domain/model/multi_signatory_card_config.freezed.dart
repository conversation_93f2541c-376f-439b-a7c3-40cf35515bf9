// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'multi_signatory_card_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MultiSignatoryCardConfig {
  String get fee => throw _privateConstructorUsedError;
  String get termsEn => throw _privateConstructorUsedError;
  String get termsAr => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MultiSignatoryCardConfigCopyWith<MultiSignatoryCardConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MultiSignatoryCardConfigCopyWith<$Res> {
  factory $MultiSignatoryCardConfigCopyWith(MultiSignatoryCardConfig value,
          $Res Function(MultiSignatoryCardConfig) then) =
      _$MultiSignatoryCardConfigCopyWithImpl<$Res, MultiSignatoryCardConfig>;
  @useResult
  $Res call({String fee, String termsEn, String termsAr});
}

/// @nodoc
class _$MultiSignatoryCardConfigCopyWithImpl<$Res,
        $Val extends MultiSignatoryCardConfig>
    implements $MultiSignatoryCardConfigCopyWith<$Res> {
  _$MultiSignatoryCardConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fee = null,
    Object? termsEn = null,
    Object? termsAr = null,
  }) {
    return _then(_value.copyWith(
      fee: null == fee
          ? _value.fee
          : fee // ignore: cast_nullable_to_non_nullable
              as String,
      termsEn: null == termsEn
          ? _value.termsEn
          : termsEn // ignore: cast_nullable_to_non_nullable
              as String,
      termsAr: null == termsAr
          ? _value.termsAr
          : termsAr // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MultiSignatoryCardConfigImplCopyWith<$Res>
    implements $MultiSignatoryCardConfigCopyWith<$Res> {
  factory _$$MultiSignatoryCardConfigImplCopyWith(
          _$MultiSignatoryCardConfigImpl value,
          $Res Function(_$MultiSignatoryCardConfigImpl) then) =
      __$$MultiSignatoryCardConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String fee, String termsEn, String termsAr});
}

/// @nodoc
class __$$MultiSignatoryCardConfigImplCopyWithImpl<$Res>
    extends _$MultiSignatoryCardConfigCopyWithImpl<$Res,
        _$MultiSignatoryCardConfigImpl>
    implements _$$MultiSignatoryCardConfigImplCopyWith<$Res> {
  __$$MultiSignatoryCardConfigImplCopyWithImpl(
      _$MultiSignatoryCardConfigImpl _value,
      $Res Function(_$MultiSignatoryCardConfigImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fee = null,
    Object? termsEn = null,
    Object? termsAr = null,
  }) {
    return _then(_$MultiSignatoryCardConfigImpl(
      fee: null == fee
          ? _value.fee
          : fee // ignore: cast_nullable_to_non_nullable
              as String,
      termsEn: null == termsEn
          ? _value.termsEn
          : termsEn // ignore: cast_nullable_to_non_nullable
              as String,
      termsAr: null == termsAr
          ? _value.termsAr
          : termsAr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$MultiSignatoryCardConfigImpl implements _MultiSignatoryCardConfig {
  const _$MultiSignatoryCardConfigImpl(
      {required this.fee, required this.termsEn, required this.termsAr});

  @override
  final String fee;
  @override
  final String termsEn;
  @override
  final String termsAr;

  @override
  String toString() {
    return 'MultiSignatoryCardConfig(fee: $fee, termsEn: $termsEn, termsAr: $termsAr)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MultiSignatoryCardConfigImpl &&
            (identical(other.fee, fee) || other.fee == fee) &&
            (identical(other.termsEn, termsEn) || other.termsEn == termsEn) &&
            (identical(other.termsAr, termsAr) || other.termsAr == termsAr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, fee, termsEn, termsAr);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MultiSignatoryCardConfigImplCopyWith<_$MultiSignatoryCardConfigImpl>
      get copyWith => __$$MultiSignatoryCardConfigImplCopyWithImpl<
          _$MultiSignatoryCardConfigImpl>(this, _$identity);
}

abstract class _MultiSignatoryCardConfig implements MultiSignatoryCardConfig {
  const factory _MultiSignatoryCardConfig(
      {required final String fee,
      required final String termsEn,
      required final String termsAr}) = _$MultiSignatoryCardConfigImpl;

  @override
  String get fee;
  @override
  String get termsEn;
  @override
  String get termsAr;
  @override
  @JsonKey(ignore: true)
  _$$MultiSignatoryCardConfigImplCopyWith<_$MultiSignatoryCardConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}
