// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'card.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$Card {
  /// Card's Primary Account Number (i.e. card number)
  String get mpan => throw _privateConstructorUsedError;

  /// Unique card number
  String get id => throw _privateConstructorUsedError;

  /// Type of card: physical or virtual one
  CardType get type => throw _privateConstructorUsedError;

  /// Current card status
  CardStatus get status => throw _privateConstructorUsedError;

  /// Virtual card name or Emboss name
  String get name => throw _privateConstructorUsedError;

  /// Flag that indicate new cards
  ///
  /// New cards require activation before being used
  bool get newCard => throw _privateConstructorUsedError;

  /// Flag that shows whether a new physical card has been delivered to
  /// a client.
  ///
  /// `true` if delivered, `false` if not delivered.
  ///
  /// For virtual cards - `null`. Not applicable.
  bool? get delivered => throw _privateConstructorUsedError;

  /// Common card payment settings
  /// will be deprecated
  Set<CardSettingType> get settings => throw _privateConstructorUsedError;

  /// Whether card is expired.
  bool get expired => throw _privateConstructorUsedError;

  /// Card expiration date.
  DateTime? get expiryDate => throw _privateConstructorUsedError;

  /// Card's spending limit info,
  CardSpendingLimit? get spendingLimit => throw _privateConstructorUsedError;

  /// The phone number of a person who this card is shared with
  String? get assistantPhoneNumber => throw _privateConstructorUsedError;

  /// The reference number to track the delivery of the card
  String? get awbReferenceNumber => throw _privateConstructorUsedError;

  /// Card Delivery status tracker
  DeliveryTracker? get deliveryTracker => throw _privateConstructorUsedError;

  /// Default linked account to the card
  /// It can be either debit or credit
  String? get defaultLinkedAccount => throw _privateConstructorUsedError;

  /// to check is the card replaced earlier or its first time
  /// can be true or false
  /// true for every replaced card
  /// false for the default card which the user has for first time
  bool? get replaced => throw _privateConstructorUsedError;

  /// Settings of the card's security
  /// naming will be changed after [settings] deletion
  CardSettings get settingsV2 => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CardCopyWith<Card> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CardCopyWith<$Res> {
  factory $CardCopyWith(Card value, $Res Function(Card) then) =
      _$CardCopyWithImpl<$Res, Card>;
  @useResult
  $Res call(
      {String mpan,
      String id,
      CardType type,
      CardStatus status,
      String name,
      bool newCard,
      bool? delivered,
      Set<CardSettingType> settings,
      bool expired,
      DateTime? expiryDate,
      CardSpendingLimit? spendingLimit,
      String? assistantPhoneNumber,
      String? awbReferenceNumber,
      DeliveryTracker? deliveryTracker,
      String? defaultLinkedAccount,
      bool? replaced,
      CardSettings settingsV2});

  $CardSpendingLimitCopyWith<$Res>? get spendingLimit;
  $DeliveryTrackerCopyWith<$Res>? get deliveryTracker;
  $CardSettingsCopyWith<$Res> get settingsV2;
}

/// @nodoc
class _$CardCopyWithImpl<$Res, $Val extends Card>
    implements $CardCopyWith<$Res> {
  _$CardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mpan = null,
    Object? id = null,
    Object? type = null,
    Object? status = null,
    Object? name = null,
    Object? newCard = null,
    Object? delivered = freezed,
    Object? settings = null,
    Object? expired = null,
    Object? expiryDate = freezed,
    Object? spendingLimit = freezed,
    Object? assistantPhoneNumber = freezed,
    Object? awbReferenceNumber = freezed,
    Object? deliveryTracker = freezed,
    Object? defaultLinkedAccount = freezed,
    Object? replaced = freezed,
    Object? settingsV2 = null,
  }) {
    return _then(_value.copyWith(
      mpan: null == mpan
          ? _value.mpan
          : mpan // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as CardType,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as CardStatus,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      newCard: null == newCard
          ? _value.newCard
          : newCard // ignore: cast_nullable_to_non_nullable
              as bool,
      delivered: freezed == delivered
          ? _value.delivered
          : delivered // ignore: cast_nullable_to_non_nullable
              as bool?,
      settings: null == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as Set<CardSettingType>,
      expired: null == expired
          ? _value.expired
          : expired // ignore: cast_nullable_to_non_nullable
              as bool,
      expiryDate: freezed == expiryDate
          ? _value.expiryDate
          : expiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      spendingLimit: freezed == spendingLimit
          ? _value.spendingLimit
          : spendingLimit // ignore: cast_nullable_to_non_nullable
              as CardSpendingLimit?,
      assistantPhoneNumber: freezed == assistantPhoneNumber
          ? _value.assistantPhoneNumber
          : assistantPhoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      awbReferenceNumber: freezed == awbReferenceNumber
          ? _value.awbReferenceNumber
          : awbReferenceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryTracker: freezed == deliveryTracker
          ? _value.deliveryTracker
          : deliveryTracker // ignore: cast_nullable_to_non_nullable
              as DeliveryTracker?,
      defaultLinkedAccount: freezed == defaultLinkedAccount
          ? _value.defaultLinkedAccount
          : defaultLinkedAccount // ignore: cast_nullable_to_non_nullable
              as String?,
      replaced: freezed == replaced
          ? _value.replaced
          : replaced // ignore: cast_nullable_to_non_nullable
              as bool?,
      settingsV2: null == settingsV2
          ? _value.settingsV2
          : settingsV2 // ignore: cast_nullable_to_non_nullable
              as CardSettings,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CardSpendingLimitCopyWith<$Res>? get spendingLimit {
    if (_value.spendingLimit == null) {
      return null;
    }

    return $CardSpendingLimitCopyWith<$Res>(_value.spendingLimit!, (value) {
      return _then(_value.copyWith(spendingLimit: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $DeliveryTrackerCopyWith<$Res>? get deliveryTracker {
    if (_value.deliveryTracker == null) {
      return null;
    }

    return $DeliveryTrackerCopyWith<$Res>(_value.deliveryTracker!, (value) {
      return _then(_value.copyWith(deliveryTracker: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CardSettingsCopyWith<$Res> get settingsV2 {
    return $CardSettingsCopyWith<$Res>(_value.settingsV2, (value) {
      return _then(_value.copyWith(settingsV2: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CardImplCopyWith<$Res> implements $CardCopyWith<$Res> {
  factory _$$CardImplCopyWith(
          _$CardImpl value, $Res Function(_$CardImpl) then) =
      __$$CardImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String mpan,
      String id,
      CardType type,
      CardStatus status,
      String name,
      bool newCard,
      bool? delivered,
      Set<CardSettingType> settings,
      bool expired,
      DateTime? expiryDate,
      CardSpendingLimit? spendingLimit,
      String? assistantPhoneNumber,
      String? awbReferenceNumber,
      DeliveryTracker? deliveryTracker,
      String? defaultLinkedAccount,
      bool? replaced,
      CardSettings settingsV2});

  @override
  $CardSpendingLimitCopyWith<$Res>? get spendingLimit;
  @override
  $DeliveryTrackerCopyWith<$Res>? get deliveryTracker;
  @override
  $CardSettingsCopyWith<$Res> get settingsV2;
}

/// @nodoc
class __$$CardImplCopyWithImpl<$Res>
    extends _$CardCopyWithImpl<$Res, _$CardImpl>
    implements _$$CardImplCopyWith<$Res> {
  __$$CardImplCopyWithImpl(_$CardImpl _value, $Res Function(_$CardImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mpan = null,
    Object? id = null,
    Object? type = null,
    Object? status = null,
    Object? name = null,
    Object? newCard = null,
    Object? delivered = freezed,
    Object? settings = null,
    Object? expired = null,
    Object? expiryDate = freezed,
    Object? spendingLimit = freezed,
    Object? assistantPhoneNumber = freezed,
    Object? awbReferenceNumber = freezed,
    Object? deliveryTracker = freezed,
    Object? defaultLinkedAccount = freezed,
    Object? replaced = freezed,
    Object? settingsV2 = null,
  }) {
    return _then(_$CardImpl(
      mpan: null == mpan
          ? _value.mpan
          : mpan // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as CardType,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as CardStatus,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      newCard: null == newCard
          ? _value.newCard
          : newCard // ignore: cast_nullable_to_non_nullable
              as bool,
      delivered: freezed == delivered
          ? _value.delivered
          : delivered // ignore: cast_nullable_to_non_nullable
              as bool?,
      settings: null == settings
          ? _value._settings
          : settings // ignore: cast_nullable_to_non_nullable
              as Set<CardSettingType>,
      expired: null == expired
          ? _value.expired
          : expired // ignore: cast_nullable_to_non_nullable
              as bool,
      expiryDate: freezed == expiryDate
          ? _value.expiryDate
          : expiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      spendingLimit: freezed == spendingLimit
          ? _value.spendingLimit
          : spendingLimit // ignore: cast_nullable_to_non_nullable
              as CardSpendingLimit?,
      assistantPhoneNumber: freezed == assistantPhoneNumber
          ? _value.assistantPhoneNumber
          : assistantPhoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      awbReferenceNumber: freezed == awbReferenceNumber
          ? _value.awbReferenceNumber
          : awbReferenceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryTracker: freezed == deliveryTracker
          ? _value.deliveryTracker
          : deliveryTracker // ignore: cast_nullable_to_non_nullable
              as DeliveryTracker?,
      defaultLinkedAccount: freezed == defaultLinkedAccount
          ? _value.defaultLinkedAccount
          : defaultLinkedAccount // ignore: cast_nullable_to_non_nullable
              as String?,
      replaced: freezed == replaced
          ? _value.replaced
          : replaced // ignore: cast_nullable_to_non_nullable
              as bool?,
      settingsV2: null == settingsV2
          ? _value.settingsV2
          : settingsV2 // ignore: cast_nullable_to_non_nullable
              as CardSettings,
    ));
  }
}

/// @nodoc

class _$CardImpl extends _Card {
  const _$CardImpl(
      {required this.mpan,
      required this.id,
      required this.type,
      required this.status,
      required this.name,
      required this.newCard,
      required this.delivered,
      required final Set<CardSettingType> settings,
      required this.expired,
      required this.expiryDate,
      this.spendingLimit,
      this.assistantPhoneNumber,
      this.awbReferenceNumber,
      this.deliveryTracker,
      this.defaultLinkedAccount,
      this.replaced,
      this.settingsV2 = const CardSettings()})
      : _settings = settings,
        super._();

  /// Card's Primary Account Number (i.e. card number)
  @override
  final String mpan;

  /// Unique card number
  @override
  final String id;

  /// Type of card: physical or virtual one
  @override
  final CardType type;

  /// Current card status
  @override
  final CardStatus status;

  /// Virtual card name or Emboss name
  @override
  final String name;

  /// Flag that indicate new cards
  ///
  /// New cards require activation before being used
  @override
  final bool newCard;

  /// Flag that shows whether a new physical card has been delivered to
  /// a client.
  ///
  /// `true` if delivered, `false` if not delivered.
  ///
  /// For virtual cards - `null`. Not applicable.
  @override
  final bool? delivered;

  /// Common card payment settings
  /// will be deprecated
  final Set<CardSettingType> _settings;

  /// Common card payment settings
  /// will be deprecated
  @override
  Set<CardSettingType> get settings {
    if (_settings is EqualUnmodifiableSetView) return _settings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_settings);
  }

  /// Whether card is expired.
  @override
  final bool expired;

  /// Card expiration date.
  @override
  final DateTime? expiryDate;

  /// Card's spending limit info,
  @override
  final CardSpendingLimit? spendingLimit;

  /// The phone number of a person who this card is shared with
  @override
  final String? assistantPhoneNumber;

  /// The reference number to track the delivery of the card
  @override
  final String? awbReferenceNumber;

  /// Card Delivery status tracker
  @override
  final DeliveryTracker? deliveryTracker;

  /// Default linked account to the card
  /// It can be either debit or credit
  @override
  final String? defaultLinkedAccount;

  /// to check is the card replaced earlier or its first time
  /// can be true or false
  /// true for every replaced card
  /// false for the default card which the user has for first time
  @override
  final bool? replaced;

  /// Settings of the card's security
  /// naming will be changed after [settings] deletion
  @override
  @JsonKey()
  final CardSettings settingsV2;

  @override
  String toString() {
    return 'Card(mpan: $mpan, id: $id, type: $type, status: $status, name: $name, newCard: $newCard, delivered: $delivered, settings: $settings, expired: $expired, expiryDate: $expiryDate, spendingLimit: $spendingLimit, assistantPhoneNumber: $assistantPhoneNumber, awbReferenceNumber: $awbReferenceNumber, deliveryTracker: $deliveryTracker, defaultLinkedAccount: $defaultLinkedAccount, replaced: $replaced, settingsV2: $settingsV2)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CardImpl &&
            (identical(other.mpan, mpan) || other.mpan == mpan) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.newCard, newCard) || other.newCard == newCard) &&
            (identical(other.delivered, delivered) ||
                other.delivered == delivered) &&
            const DeepCollectionEquality().equals(other._settings, _settings) &&
            (identical(other.expired, expired) || other.expired == expired) &&
            (identical(other.expiryDate, expiryDate) ||
                other.expiryDate == expiryDate) &&
            (identical(other.spendingLimit, spendingLimit) ||
                other.spendingLimit == spendingLimit) &&
            (identical(other.assistantPhoneNumber, assistantPhoneNumber) ||
                other.assistantPhoneNumber == assistantPhoneNumber) &&
            (identical(other.awbReferenceNumber, awbReferenceNumber) ||
                other.awbReferenceNumber == awbReferenceNumber) &&
            (identical(other.deliveryTracker, deliveryTracker) ||
                other.deliveryTracker == deliveryTracker) &&
            (identical(other.defaultLinkedAccount, defaultLinkedAccount) ||
                other.defaultLinkedAccount == defaultLinkedAccount) &&
            (identical(other.replaced, replaced) ||
                other.replaced == replaced) &&
            (identical(other.settingsV2, settingsV2) ||
                other.settingsV2 == settingsV2));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      mpan,
      id,
      type,
      status,
      name,
      newCard,
      delivered,
      const DeepCollectionEquality().hash(_settings),
      expired,
      expiryDate,
      spendingLimit,
      assistantPhoneNumber,
      awbReferenceNumber,
      deliveryTracker,
      defaultLinkedAccount,
      replaced,
      settingsV2);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CardImplCopyWith<_$CardImpl> get copyWith =>
      __$$CardImplCopyWithImpl<_$CardImpl>(this, _$identity);
}

abstract class _Card extends Card {
  const factory _Card(
      {required final String mpan,
      required final String id,
      required final CardType type,
      required final CardStatus status,
      required final String name,
      required final bool newCard,
      required final bool? delivered,
      required final Set<CardSettingType> settings,
      required final bool expired,
      required final DateTime? expiryDate,
      final CardSpendingLimit? spendingLimit,
      final String? assistantPhoneNumber,
      final String? awbReferenceNumber,
      final DeliveryTracker? deliveryTracker,
      final String? defaultLinkedAccount,
      final bool? replaced,
      final CardSettings settingsV2}) = _$CardImpl;
  const _Card._() : super._();

  @override

  /// Card's Primary Account Number (i.e. card number)
  String get mpan;
  @override

  /// Unique card number
  String get id;
  @override

  /// Type of card: physical or virtual one
  CardType get type;
  @override

  /// Current card status
  CardStatus get status;
  @override

  /// Virtual card name or Emboss name
  String get name;
  @override

  /// Flag that indicate new cards
  ///
  /// New cards require activation before being used
  bool get newCard;
  @override

  /// Flag that shows whether a new physical card has been delivered to
  /// a client.
  ///
  /// `true` if delivered, `false` if not delivered.
  ///
  /// For virtual cards - `null`. Not applicable.
  bool? get delivered;
  @override

  /// Common card payment settings
  /// will be deprecated
  Set<CardSettingType> get settings;
  @override

  /// Whether card is expired.
  bool get expired;
  @override

  /// Card expiration date.
  DateTime? get expiryDate;
  @override

  /// Card's spending limit info,
  CardSpendingLimit? get spendingLimit;
  @override

  /// The phone number of a person who this card is shared with
  String? get assistantPhoneNumber;
  @override

  /// The reference number to track the delivery of the card
  String? get awbReferenceNumber;
  @override

  /// Card Delivery status tracker
  DeliveryTracker? get deliveryTracker;
  @override

  /// Default linked account to the card
  /// It can be either debit or credit
  String? get defaultLinkedAccount;
  @override

  /// to check is the card replaced earlier or its first time
  /// can be true or false
  /// true for every replaced card
  /// false for the default card which the user has for first time
  bool? get replaced;
  @override

  /// Settings of the card's security
  /// naming will be changed after [settings] deletion
  CardSettings get settingsV2;
  @override
  @JsonKey(ignore: true)
  _$$CardImplCopyWith<_$CardImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$CardSettings {
  CardSettingState get atmPayments => throw _privateConstructorUsedError;
  CardSettingState get ecomPayments => throw _privateConstructorUsedError;
  CardSettingState get contactlessPayments =>
      throw _privateConstructorUsedError;
  CardSettingState get abroadPayments => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CardSettingsCopyWith<CardSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CardSettingsCopyWith<$Res> {
  factory $CardSettingsCopyWith(
          CardSettings value, $Res Function(CardSettings) then) =
      _$CardSettingsCopyWithImpl<$Res, CardSettings>;
  @useResult
  $Res call(
      {CardSettingState atmPayments,
      CardSettingState ecomPayments,
      CardSettingState contactlessPayments,
      CardSettingState abroadPayments});
}

/// @nodoc
class _$CardSettingsCopyWithImpl<$Res, $Val extends CardSettings>
    implements $CardSettingsCopyWith<$Res> {
  _$CardSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? atmPayments = null,
    Object? ecomPayments = null,
    Object? contactlessPayments = null,
    Object? abroadPayments = null,
  }) {
    return _then(_value.copyWith(
      atmPayments: null == atmPayments
          ? _value.atmPayments
          : atmPayments // ignore: cast_nullable_to_non_nullable
              as CardSettingState,
      ecomPayments: null == ecomPayments
          ? _value.ecomPayments
          : ecomPayments // ignore: cast_nullable_to_non_nullable
              as CardSettingState,
      contactlessPayments: null == contactlessPayments
          ? _value.contactlessPayments
          : contactlessPayments // ignore: cast_nullable_to_non_nullable
              as CardSettingState,
      abroadPayments: null == abroadPayments
          ? _value.abroadPayments
          : abroadPayments // ignore: cast_nullable_to_non_nullable
              as CardSettingState,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CardSettingsImplCopyWith<$Res>
    implements $CardSettingsCopyWith<$Res> {
  factory _$$CardSettingsImplCopyWith(
          _$CardSettingsImpl value, $Res Function(_$CardSettingsImpl) then) =
      __$$CardSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CardSettingState atmPayments,
      CardSettingState ecomPayments,
      CardSettingState contactlessPayments,
      CardSettingState abroadPayments});
}

/// @nodoc
class __$$CardSettingsImplCopyWithImpl<$Res>
    extends _$CardSettingsCopyWithImpl<$Res, _$CardSettingsImpl>
    implements _$$CardSettingsImplCopyWith<$Res> {
  __$$CardSettingsImplCopyWithImpl(
      _$CardSettingsImpl _value, $Res Function(_$CardSettingsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? atmPayments = null,
    Object? ecomPayments = null,
    Object? contactlessPayments = null,
    Object? abroadPayments = null,
  }) {
    return _then(_$CardSettingsImpl(
      atmPayments: null == atmPayments
          ? _value.atmPayments
          : atmPayments // ignore: cast_nullable_to_non_nullable
              as CardSettingState,
      ecomPayments: null == ecomPayments
          ? _value.ecomPayments
          : ecomPayments // ignore: cast_nullable_to_non_nullable
              as CardSettingState,
      contactlessPayments: null == contactlessPayments
          ? _value.contactlessPayments
          : contactlessPayments // ignore: cast_nullable_to_non_nullable
              as CardSettingState,
      abroadPayments: null == abroadPayments
          ? _value.abroadPayments
          : abroadPayments // ignore: cast_nullable_to_non_nullable
              as CardSettingState,
    ));
  }
}

/// @nodoc

class _$CardSettingsImpl extends _CardSettings {
  const _$CardSettingsImpl(
      {this.atmPayments = CardSettingState.unavailable,
      this.ecomPayments = CardSettingState.unavailable,
      this.contactlessPayments = CardSettingState.unavailable,
      this.abroadPayments = CardSettingState.unavailable})
      : super._();

  @override
  @JsonKey()
  final CardSettingState atmPayments;
  @override
  @JsonKey()
  final CardSettingState ecomPayments;
  @override
  @JsonKey()
  final CardSettingState contactlessPayments;
  @override
  @JsonKey()
  final CardSettingState abroadPayments;

  @override
  String toString() {
    return 'CardSettings(atmPayments: $atmPayments, ecomPayments: $ecomPayments, contactlessPayments: $contactlessPayments, abroadPayments: $abroadPayments)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CardSettingsImpl &&
            (identical(other.atmPayments, atmPayments) ||
                other.atmPayments == atmPayments) &&
            (identical(other.ecomPayments, ecomPayments) ||
                other.ecomPayments == ecomPayments) &&
            (identical(other.contactlessPayments, contactlessPayments) ||
                other.contactlessPayments == contactlessPayments) &&
            (identical(other.abroadPayments, abroadPayments) ||
                other.abroadPayments == abroadPayments));
  }

  @override
  int get hashCode => Object.hash(runtimeType, atmPayments, ecomPayments,
      contactlessPayments, abroadPayments);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CardSettingsImplCopyWith<_$CardSettingsImpl> get copyWith =>
      __$$CardSettingsImplCopyWithImpl<_$CardSettingsImpl>(this, _$identity);
}

abstract class _CardSettings extends CardSettings {
  const factory _CardSettings(
      {final CardSettingState atmPayments,
      final CardSettingState ecomPayments,
      final CardSettingState contactlessPayments,
      final CardSettingState abroadPayments}) = _$CardSettingsImpl;
  const _CardSettings._() : super._();

  @override
  CardSettingState get atmPayments;
  @override
  CardSettingState get ecomPayments;
  @override
  CardSettingState get contactlessPayments;
  @override
  CardSettingState get abroadPayments;
  @override
  @JsonKey(ignore: true)
  _$$CardSettingsImplCopyWith<_$CardSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
