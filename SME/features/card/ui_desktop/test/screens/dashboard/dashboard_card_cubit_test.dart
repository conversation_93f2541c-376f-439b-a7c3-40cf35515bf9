import 'dart:ui';

import 'package:account_feature_api/account_feature_api.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:domain/domain.dart';
import 'package:feature_card_api/card.dart';
import 'package:feature_card_ui_desktop/feature_card_ui_desktop.dart';
import 'package:feature_card_ui_desktop/src/navigation/create_virtual_card_navigation_config.dart';
import 'package:feature_card_ui_desktop/src/screens/analytics/card_event_analytics.dart';
import 'package:feature_card_ui_desktop/src/screens/dashboard/cubit/dashboard_card_cubit.dart';
import 'package:feature_card_ui_desktop/src/screens/dashboard/cubit/dashboard_card_state.dart';
import 'package:mocktail/mocktail.dart';
import 'package:sme_core_ui_desktop/index.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/ui_tests.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart'
    hide CardType;
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_sme_behaviour/wio_sme_behaviour.dart';

import '../../mocks.dart';
import '../../test_entities.dart';

void main() {
  late AccountInteractor accountInteractor;
  late CardsAnalytics analytics;
  late NavigationProvider navigationProvider;
  late CardInteractor cardInteractor;
  late CardUILocalizations localizations;
  late NewNotificationService notificationService;
  late MockBehaviourProvider behaviourProvider;

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    localizations = await CardUILocalizations.load(const Locale('en'));
  });

  setUp(() {
    accountInteractor = MockAccountInteractor();
    analytics = MockCardsAnalytics();
    navigationProvider = MockNavigationProvider();
    cardInteractor = MockCardInteractor();
    notificationService = MockNewNotificationService();
    behaviourProvider = MockBehaviourProvider();

    when(
      () =>
          behaviourProvider.hasPermission<CardsMultiSignatureReadPermission>(),
    ).thenReturn(true);

    when(() => analytics.clickCardsHome()).justComplete();

    registerFallbackValue(SnackbarModel.errorLowPriority(text: 'text'));
  });

  DashboardCardCubit buildCubit() => DashboardCardCubit(
        accountInteractor: accountInteractor,
        analytics: analytics,
        navigationProvider: navigationProvider,
        cardInteractor: cardInteractor,
        cardUILocalizations: localizations,
        notificationService: notificationService,
        logger: MockLogger(),
        behaviourProvider: behaviourProvider,
        errorHandler: MockErrorHandlerTool(),
      );

  final cards = [
    TestEntities.getCard(id: 'yolo'),
    TestEntities.getCard(id: 'volo', type: CardType.virtual),
    TestEntities.getCard(id: 'solo', type: CardType.virtual),
  ];
  final cardNames = cards.map((card) => card.name).toList();

  final currentAccounts = [TestEntities.getAccount()];

  group('Initialization >', () {
    final accountsData = Data.success([
      TestEntities.getAccount(id: 'active'),
    ]);

    blocTest<DashboardCardCubit, DashboardCardState>(
      'allows card creation if account sub-state is active',
      // Arrange
      build: () => buildCubit(),
      setUp: () {
        when(
          () => cardInteractor.observeCards(
            forceRefresh: any(named: 'forceRefresh'),
          ),
        ).thenAnswer((_) => Stream.value(cards));
        when(
          () => accountInteractor.observeAccounts(
            refresh: any(named: 'refresh'),
          ),
        ).thenAnswer((_) => Stream.value(accountsData));
      },

      // Act
      act: (cubit) => cubit.initialize(),

      // Assert
      verify: (cubit) {
        expect(cubit.state.canCreateNewCard, isTrue);
      },
      expect: () => <DashboardCardState>[
        const DashboardCardState.loading(),
        DashboardCardState.idle(
          cards: cards,
          accountsState: DashboardCardAccountsState.idle(
            currentAccounts: accountsData.content ?? [],
          ),
          isMultiSignature: true,
        ),
      ],
    );

    const restrictedStates = [
      AccountSubState.unknown,
      AccountSubState.restrictedDebit,
      AccountSubState.totalFreeze,
    ];
    for (final state in restrictedStates) {
      final accountsData = Data.success([
        TestEntities.getAccount(id: 'active', subState: state),
      ]);

      blocTest<DashboardCardCubit, DashboardCardState>(
        'does not allow card creation for ${state.name} account sub-state',
        // Arrange
        build: () => buildCubit(),
        setUp: () {
          when(
            () => cardInteractor.observeCards(
              forceRefresh: any(named: 'forceRefresh'),
            ),
          ).thenAnswer((_) => Stream.value(cards));
          when(
            () => accountInteractor.observeAccounts(
              refresh: any(named: 'refresh'),
            ),
          ).thenAnswer((_) => Stream.value(accountsData));
        },

        // Act
        act: (cubit) => cubit.initialize(),

        // Assert
        verify: (cubit) {
          expect(cubit.state.canCreateNewCard, isFalse);
        },
        expect: () => <DashboardCardState>[
          const DashboardCardState.loading(),
          DashboardCardState.idle(
            cards: cards,
            accountsState: DashboardCardAccountsState.idle(
              currentAccounts: accountsData.content ?? [],
            ),
            isMultiSignature: true,
          ),
        ],
      );
    }

    blocTest<DashboardCardCubit, DashboardCardState>(
      'does not allow card creation when fetching accounts fails',
      // Arrange
      build: () => buildCubit(),
      setUp: () {
        when(
          () => cardInteractor.observeCards(
            forceRefresh: any(named: 'forceRefresh'),
          ),
        ).thenAnswer((_) => Stream.value(cards));
        when(
          () => accountInteractor.observeAccounts(
            refresh: any(named: 'refresh'),
          ),
        ).thenAnswer(
          (_) => Stream.value(
            Data.error('Error occurred while fetching accounts'),
          ),
        );
      },

      // Act
      act: (cubit) => cubit.initialize(),

      // Assert
      verify: (cubit) {
        expect(cubit.state.canCreateNewCard, isFalse);
      },
      expect: () => <DashboardCardState>[
        const DashboardCardState.loading(),
        DashboardCardState.idle(
          cards: cards,
          accountsState: const DashboardCardAccountsState.failed(),
          isMultiSignature: true,
        ),
      ],
    );
  });

  blocTest<DashboardCardCubit, DashboardCardState>(
    'selectCard should update the state accordingly',
    // Arrange
    build: () => buildCubit(),
    seed: () => DashboardCardState.idle(
      cards: cards,
      accountsState: const DashboardCardAccountsState.failed(),
      isMultiSignature: true,
    ),

    // Act
    act: (cubit) => cubit.selectCard(1),

    // Assert
    expect: () => <DashboardCardState>[
      DashboardCardState.idle(
        cards: cards,
        activeCardIndex: 1,
        accountsState: const DashboardCardAccountsState.failed(),
        isMultiSignature: true,
      ),
    ],
  );

  blocTest<DashboardCardCubit, DashboardCardState>(
    'createVirtualCard should call the functions accordingly',
    // Arrange
    build: () => buildCubit(),
    seed: () => DashboardCardState.idle(
      cards: cards,
      accountsState:
          DashboardCardAccountsState.idle(currentAccounts: currentAccounts),
      isMultiSignature: true,
    ),

    // Act
    act: (cubit) => cubit.createVirtualCard(),

    // Assert
    verify: (cubit) {
      expect(cubit.state.canCreateNewCard, isTrue);
      verify(() => analytics.addVirtualCard()).calledOnce;
      verify(
        () => navigationProvider
            .push(CreateVirtualCardNavigationConfig(cardNames)),
      ).calledOnce;
    },
    // No state change
    expect: () => <DashboardCardState>[],
  );

  blocTest<DashboardCardCubit, DashboardCardState>(
    'terminateCard should call the functions accordingly',
    // Arrange
    build: () => buildCubit(),
    seed: () => DashboardCardState.idle(
      cards: cards,
      accountsState:
          DashboardCardAccountsState.idle(currentAccounts: currentAccounts),
      isMultiSignature: true,
    ),
    setUp: () {
      when(() => cardInteractor.terminateCard(cardId: any(named: 'cardId')))
          .justCompleteAsync();
      when(() => notificationService.showSnackbar(any())).justComplete();
    },

    // Act
    act: (cubit) => cubit.terminateCard(),

    // Assert
    verify: (cubit) {
      verify(() => analytics.clickTerminateCard()).calledOnce;
      verify(() => cardInteractor.terminateCard(cardId: any(named: 'cardId')))
          .calledOnce;
      verify(() => notificationService.showSnackbar(any())).calledOnce;
    },
    expect: () => <DashboardCardState>[
      DashboardCardState.idle(
        cards: cards,
        accountsState:
            DashboardCardAccountsState.idle(currentAccounts: currentAccounts),
        isMultiSignature: true,
        isProcessing: true,
      ),
      DashboardCardState.idle(
        cards: cards,
        accountsState:
            DashboardCardAccountsState.idle(currentAccounts: currentAccounts),
        isMultiSignature: true,
      ),
    ],
  );
}
