import 'package:feature_card_api/card.dart';
import 'package:feature_card_ui_desktop/src/navigation/confirm_new_virtual_card_screen_config.dart';
import 'package:feature_card_ui_desktop/src/navigation/create_virtual_card_navigation_config.dart';
import 'package:feature_card_ui_desktop/src/screens/create_virtual_card/card_confirmation/confirm_new_virtual_card_screen.dart'
    deferred as confirm_new_virtual_card_screen;
import 'package:feature_card_ui_desktop/src/screens/create_virtual_card/create_virtual_card_page.dart'
    deferred as create_virtual_card_page;
import 'package:feature_card_ui_desktop/src/screens/dashboard/dashboard_card_page.dart'
    deferred as dashboard_card_page;
import 'package:flutter/cupertino.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';

class CardRouter extends NavigationRouter {
  const CardRouter();

  @override
  Route<Object?> getScreenRoute(RouteSettings settings) {
    final config = settings.arguments;

    return switch (config) {
      CardFeatureNavigationConfig() => toDeferredRoute(
          () => dashboard_card_page.DashboardCardPage(),
          settings,
          dashboard_card_page.loadLibrary,
        ),
      CreateVirtualCardNavigationConfig(
        existingCardNames: final cardHolderNames
      ) =>
        toDeferredRoute(
          () => create_virtual_card_page.CreateVirtualCardPage(
            existingCardNames: cardHolderNames,
          ),
          settings,
          create_virtual_card_page.loadLibrary,
        ),
      final ConfirmNewVirtualCardNavigationConfig it => toDeferredRoute(
          () => confirm_new_virtual_card_screen.ConfirmNewVirtualCardScreen(
            cardName: it.cardName,
            cardNumber: it.cardNumber,
            cardLimit: it.cardLimit,
            expiryDate: it.expiryDate,
            onClose: it.onClose,
          ),
          settings,
          confirm_new_virtual_card_screen.loadLibrary,
        ),
      _ => throw Exception('Unknown $config for the $runtimeType'),
    };
  }
}
