import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:feature_2fa_api/feature_2fa_api.dart';
import 'package:feature_card_api/card.dart';
import 'package:flutter/foundation.dart';
import 'package:logging_api/logging.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ui/ui.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_card_ui/src/common/card_wallet_analytics.dart';
import 'package:wio_wallet_api/wallet_api.dart' as wallet;

// NOTE: this should not be an enum because it is possible for a card to be
// in the wallet and be available for adding to the wallet on other devices
// such as Apple Watch. In short, the two fields are not mutually exclusive.
@immutable
class AddToWalletStatus {
  static const unavailable = AddToWalletStatus(
    canBeAdded: false,
    isInWallet: false,
  );

  final bool canBeAdded;
  final bool isInWallet;

  const AddToWalletStatus({
    required this.canBeAdded,
    required this.isInWallet,
  });

  @override
  bool operator ==(Object other) =>
      other is AddToWalletStatus &&
      other.canBeAdded == canBeAdded &&
      other.isInWallet == isInWallet;

  @override
  int get hashCode => Object.hash(canBeAdded, isInWallet);
}

class CardWalletDelegate {
  @visibleForTesting
  static const forbiddenStatuses = <CardStatus>{
    CardStatus.blockedBySupport,
    CardStatus.freeze,
    CardStatus.lost,
    CardStatus.stolen,
    CardStatus.unknown,
    CardStatus.terminated,
    CardStatus.pinBlocked,
  };

  final FeatureToggleProvider _featureToggleProvider;
  final BindDeviceInteractor _bindDeviceInteractor;
  final wallet.WalletAvailableUseCase _walletAvailableUseCase;
  final wallet.AddToWalletUseCase _addToWalletUseCase;
  final Logger _logger;
  final PlatformInfo _platform;
  final CardWalletAnalytics _analytics;

  CardWalletDelegate({
    required FeatureToggleProvider featureToggleProvider,
    required BindDeviceInteractor bindDeviceInteractor,
    required wallet.WalletAvailableUseCase walletAvailableUseCase,
    required wallet.AddToWalletUseCase addToWalletUseCase,
    required Logger logger,
    required PlatformInfo platform,
    required CardWalletAnalytics analytics,
  })  : _featureToggleProvider = featureToggleProvider,
        _bindDeviceInteractor = bindDeviceInteractor,
        _walletAvailableUseCase = walletAvailableUseCase,
        _addToWalletUseCase = addToWalletUseCase,
        _analytics = analytics,
        _logger = logger,
        _platform = platform;

  bool get _isWalletToggleOn {
    if (_platform.isAndroid) {
      return _featureToggleProvider.get(CardsFeatureToggles.googlePay);
    }

    if (_platform.isIOS) {
      return _featureToggleProvider.get(CardsFeatureToggles.applePay);
    }

    return false;
  }

  Future<Map<String, AddToWalletStatus>> getAddToWalletStatuses(
    List<Card> cards,
  ) async {
    if (!await _shouldCheckWalletStatus()) {
      return const {};
    }

    final statuses = {
      for (final card in cards)
        card.id: await _getAddToWalletStatus(
          status: card.status,
          lastDigits: card.lastDigits,
          expired: card.expired,
        ),
    };

    return statuses;
  }

  //FIXME(kanat): revert to Card main model after refactor migration
  Future<AddToWalletStatus> getAddToWalletStatus({
    required CardStatus status,
    required String lastDigits,
    required bool expired,
  }) async {
    if (!await _shouldCheckWalletStatus()) {
      return AddToWalletStatus.unavailable;
    }

    return _getAddToWalletStatus(
      status: status,
      lastDigits: lastDigits,
      expired: expired,
    );
  }

  Stream<AddToWalletStatus> addCardToWallet({
    required String id,
    required String name,
    required String lastDigits,
  }) {
    _analytics.clickAddToWallet();
    _logger.debug('Adding card to wallet: $lastDigits');

    return _addCardToWallet(
      id: id,
      name: name,
      lastDigits: lastDigits,
    )
        .asStream()
        .asyncMap((_) => _getWalletAvailability(lastDigits: lastDigits))
        .map(_mapAvailabilityStatus)
        .doOnError((e, _) => _logger.debug('AddToWallet error: $e'))
        .withError<wallet.AddToWalletGooglePayException>((e) {
      if (e.errorCode != wallet.GPayErrorCode.userCancelledFlow) {
        throw e;
      }
    }).withError<wallet.AddToWalletApplePayException>((e) {
      // NOTE: no error message should be displayed for this case cause
      // it is thrown when the customer closes the flow halfway through.
      if (e.exception is! wallet.AddToWalletAppleCancelledError) {
        throw e;
      }
    });
  }

  // Private
  Future<bool> _shouldCheckWalletStatus() async {
    return _isWalletToggleOn && await _bindDeviceInteractor.deviceBound();
  }

  // NOTE: does not perform FF and device binding checks; for internal use only
  // FIXME(kanat): revert to Card main model after refactor migration
  Future<AddToWalletStatus> _getAddToWalletStatus({
    required CardStatus status,
    required String lastDigits,
    required bool expired,
  }) async {
    _logger.debug('Try to validate card for adding to wallet: $lastDigits');
    if (expired) {
      return AddToWalletStatus.unavailable;
    }

    final isInForbiddenStatus = forbiddenStatuses.contains(status);
    _logger.debug('Card has a forbidden status: $isInForbiddenStatus');
    if (isInForbiddenStatus) {
      return AddToWalletStatus.unavailable;
    }

    _logger.debug('Trying to validate on the native side');
    final result = await _getWalletAvailability(lastDigits: lastDigits);
    _logger.debug('Result from native side: $result');
    _analytics.availabilityStatus(result);
    return _mapAvailabilityStatus(result);
  }

  AddToWalletStatus _mapAvailabilityStatus(
    wallet.WalletAvailabilityResult result,
  ) {
    // FIXME(esultanli): revise once the wallet plugin's logic is updated
    return AddToWalletStatus(
      canBeAdded: result == wallet.WalletAvailabilityResult.available,
      isInWallet:
          result == wallet.WalletAvailabilityResult.unavailableAlreadyAdded,
    );
  }

  Future<wallet.WalletAvailabilityResult> _getWalletAvailability({
    required String lastDigits,
  }) async {
    final param = _getAvailabilityParams(lastDigits: lastDigits);
    final availability = await _walletAvailableUseCase(param);
    return availability;
  }

  Future<void> _addCardToWallet({
    required String id,
    required String name,
    required String lastDigits,
  }) async {
    _logger.debug('Trying to add card to wallet: $id');
    final params = await _getAddToWalletParams(
      id: id,
      name: name,
      lastDigits: lastDigits,
    );

    await _addToWalletUseCase(params);
    _logger.debug('Card was added to wallet successfully');
  }

  Future<wallet.AddToWalletParams> _getAddToWalletParams({
    required String id,
    required String name,
    required String lastDigits,
  }) async {
    if (_platform.isIOS) {
      return wallet.AddToWalletParams.applePay(
        cardHolderName: name,
        cardNumber: lastDigits,
        cardId: id,
      );
    }

    assert(_platform.isAndroid);

    return wallet.AddToWalletParams.googlePay(
      cardId: id,
      cardProvider: wallet.CardProvider.visa,
    );
  }

  wallet.WalletAvailabilityParams _getAvailabilityParams({
    required String lastDigits,
  }) {
    if (_platform.isIOS) {
      return wallet.WalletAvailabilityParams.applePay(
        cardLastDigits: lastDigits,
      );
    }

    assert(_platform.isAndroid);

    return wallet.WalletAvailabilityParams.googlePay(
      cardProvider: wallet.CardProvider.visa,
      cardLastDigits: lastDigits,
    );
  }
}
