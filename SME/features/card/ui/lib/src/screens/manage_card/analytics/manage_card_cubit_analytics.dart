// ignore_for_file: constant_identifier_names

import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_card_ui/src/navigation/manage_card_screen_navigation_config.dart';

part 'manage_card_cubit_analytics.freezed.dart';

enum ManageCardCubitAnalyticsTarget {
  edit_spending_limit,
  remove_spending_limit,
  view_pin,
  change_pin,
  contactless_transaction,
  atm_withdrawal,
  online_transaction,
  terminate_card,
  replace_card,
}

@immutable
class ManageCardCubitAnalytics {
  final AnalyticsEventTracker _analytics;

  ManageCardCubitAnalytics({
    required AnalyticsAbstractTrackerFactory analyticsAbstractTrackerFactory,
  }) : _analytics = analyticsAbstractTrackerFactory.get(
          screenName: ManageCardScreenNavigationConfig.screenId,
          tracker: AnalyticsTracker.mixpanel,
        );

  void editSpendingLimit() {
    _analytics.click(
      targetType: AnalyticsTargetType.button,
      target: ManageCardCubitAnalyticsTarget.edit_spending_limit,
    );
  }

  void removeSpendingLimit() {
    _analytics.click(
      targetType: AnalyticsTargetType.button,
      target: ManageCardCubitAnalyticsTarget.remove_spending_limit,
    );
  }

  void clickViewPin() {
    _analytics.click(
      targetType: AnalyticsTargetType.button,
      target: ManageCardCubitAnalyticsTarget.view_pin,
    );
  }

  void clickSetPin() {
    _analytics.click(
      targetType: AnalyticsTargetType.button,
      target: ManageCardCubitAnalyticsTarget.change_pin,
    );
  }

  void clickContactlessTransaction({
    required bool isOn,
  }) {
    _analytics.click(
      targetType: AnalyticsTargetType.toggle_switch,
      target: ManageCardCubitAnalyticsTarget.contactless_transaction,
      payload: ManageCardCubitAnalyticsTogglePayload(isOn: isOn),
    );
  }

  void clickAtmWithdrawal({
    required bool isOn,
  }) {
    _analytics.click(
      targetType: AnalyticsTargetType.toggle_switch,
      target: ManageCardCubitAnalyticsTarget.atm_withdrawal,
      payload: ManageCardCubitAnalyticsTogglePayload(isOn: isOn),
    );
  }

  void clickOnlineTransaction({
    required bool isOn,
  }) {
    _analytics.click(
      targetType: AnalyticsTargetType.toggle_switch,
      target: ManageCardCubitAnalyticsTarget.online_transaction,
      payload: ManageCardCubitAnalyticsTogglePayload(isOn: isOn),
    );
  }

  void clickTerminateCard() {
    _analytics.click(
      targetType: AnalyticsTargetType.button,
      target: ManageCardCubitAnalyticsTarget.terminate_card,
    );
  }

  void clickReplaceCard() {
    _analytics.click(
      targetType: AnalyticsTargetType.button,
      target: ManageCardCubitAnalyticsTarget.replace_card,
    );
  }
}

@freezed
class ManageCardCubitAnalyticsTogglePayload
    with _$ManageCardCubitAnalyticsTogglePayload
    implements AnalyticsEventPayload {
  const factory ManageCardCubitAnalyticsTogglePayload({
    required bool isOn,
  }) = _ManageCardCubitAnalyticsTogglePayload;

  const ManageCardCubitAnalyticsTogglePayload._();

  @override
  Map<String, dynamic> getEventPayload() {
    return {
      'is_toggle_on': isOn,
    };
  }
}
