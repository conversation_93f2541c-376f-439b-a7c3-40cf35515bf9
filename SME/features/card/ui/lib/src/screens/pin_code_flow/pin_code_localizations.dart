import 'package:wio_common_feature_card_ui/wio_common_feature_card_ui.dart';
import 'package:wio_feature_card_ui/feature_card_ui.dart';

class PinCodeLocalizationsImpl extends PinCodeLocalizations {
  final CardLocalizations _cardLocalizations;

  PinCodeLocalizationsImpl(this._cardLocalizations);

  @override
  String get pinCodePageActivateCardTitle =>
      _cardLocalizations.pinCodePageActivateCardTitle;

  @override
  String get pinCodePageChangePinTitle =>
      _cardLocalizations.pinCodePageChangePinTitle;

  @override
  String get pinCodePageErrorNotMatching =>
      _cardLocalizations.pinCodePageErrorNotMatching;

  @override
  String get pinCodePageErrorRepeatingDigits =>
      _cardLocalizations.pinCodePageErrorRepeatingDigits;
}
