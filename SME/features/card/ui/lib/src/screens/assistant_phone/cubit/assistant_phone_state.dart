import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_card_ui/src/screens/assistant_phone/helpers/string_helper.dart';
import 'package:wio_feature_phone_contacts_api/feature_phone_contacts_api.dart';

part 'assistant_phone_state.freezed.dart';

@freezed
class AssistantPhoneState with _$AssistantPhoneState {
  static AssistantPhoneState initialize() => const AssistantPhoneState.initial(
        country: Country.AE,
      );

  /// The initial state of the bloc.
  const factory AssistantPhoneState.initial({
    required Country country,
  }) = _InitialAssistantPhoneState;

  /// The state when nothing is happening or when a text is being typed.
  const factory AssistantPhoneState.idle({
    required Country country,
    @Default('') String inputText,
    @Default(false) bool canAccessContacts,
    @Default([]) List<PhoneContact> contacts,
    String? validPhoneNumber,
  }) = _IdleAssistantPhoneState;

  /// The state when a contact is selected.
  const factory AssistantPhoneState.contactSelected({
    required Country country,
    required PhoneContact contact,
    String? validPhoneNumber,
    @Default([]) List<PhoneContact> contacts,
  }) = _ContactSelectedAssistantPhoneState;

  /// The state when contacts are being loaded.
  const factory AssistantPhoneState.loading({
    required Country country,
  }) = _LoadingAssistantPhoneState;

  const AssistantPhoneState._();

  String? get phoneNumber {
    return mapOrNull<String?>(
      idle: (state) => state.validPhoneNumber,
      contactSelected: (state) => state.validPhoneNumber,
    );
  }

  bool get hasValidPhone {
    return phoneNumber != null;
  }

  bool get canSubmit {
    return hasValidPhone;
  }

  bool get hasNoAccessToContacts {
    return maybeMap(
      idle: (state) => !state.canAccessContacts,
      orElse: () => false,
    );
  }

  bool get isWaitingForInput {
    return maybeMap(
      initial: (_) => true,
      idle: (state) => state.inputText.isEmpty,
      orElse: () => false,
    );
  }

  bool get isResultEmpty {
    return maybeMap(
      idle: (state) =>
          state.contacts.isNotEmpty &&
          state.inputText.isNotEmpty &&
          !state.hasValidPhone,
      orElse: () => false,
    );
  }

  bool get hasCountryFlag {
    return maybeMap(
      idle: (state) => state.inputText.hasOnlyDigits,
      orElse: () => true,
    );
  }

  bool get canSearchContacts {
    return maybeMap(
      idle: (state) => state.canAccessContacts && state.contacts.isNotEmpty,
      contactSelected: (state) => state.contacts.isNotEmpty,
      orElse: () => false,
    );
  }

  bool get isReadyForInput {
    return maybeMap(
      idle: (_) => true,
      contactSelected: (_) => true,
      orElse: () => false,
    );
  }

  bool get isLoading {
    return maybeMap(
      loading: (_) => true,
      orElse: () => false,
    );
  }

  List<PhoneContact> get filteredContacts {
    return maybeMap(
      idle: (state) {
        if (!state.canAccessContacts || state.hasValidPhone) {
          return const <PhoneContact>[];
        }

        if (state.contacts.isEmpty || state.inputText.isEmpty) {
          return state.contacts;
        }

        return state.contacts.search(state.inputText);
      },
      contactSelected: (state) => [state.contact],
      orElse: () => const <PhoneContact>[],
    );
  }

  AssistantPhoneState toIdleWithoutAccessToContacts() {
    return AssistantPhoneState.idle(
      country: country,
      canAccessContacts: false,
    );
  }

  AssistantPhoneState toIdleWithContacts(List<PhoneContact> contacts) {
    return AssistantPhoneState.idle(
      country: country,
      contacts: contacts,
      canAccessContacts: true,
    );
  }

  AssistantPhoneState toLoadingContacts() {
    return AssistantPhoneState.loading(country: country);
  }
}

extension on List<PhoneContact> {
  static const _anyPattern = '.*';

  // The poor man's fuzzy search
  List<PhoneContact> search(String text) {
    final chars = text
        .split('')
        .where((it) => it.trim().isNotEmpty)
        .map(RegExp.escape)
        .join(_anyPattern);
    final pattern = '$_anyPattern$chars$_anyPattern';
    final query = RegExp(pattern, caseSensitive: false, unicode: true);

    return where(
      (it) => query.hasMatch(it.name) || query.hasMatch(it.phoneNumber),
    ).toList();
  }
}
