import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_card_ui/src/screens/cards_v2/index.dart';
import 'package:wio_feature_card_ui/src/screens/cards_v2/spending_mode/spending_mode_state.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';

class CreditBanners extends StatelessWidget {
  final EdgeInsetsGeometry padding;

  const CreditBanners({
    this.padding = EdgeInsets.zero,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final canShow = context.select<SpendingModeCubit, bool>(
      (cubit) => cubit.state is AvailableSpendingModeState,
    );

    if (!canShow) {
      return const Space.shrink();
    }

    return Padding(
      padding: padding,
      child: const CreditApplicationCardPageEntryBanner(),
    );
  }
}
