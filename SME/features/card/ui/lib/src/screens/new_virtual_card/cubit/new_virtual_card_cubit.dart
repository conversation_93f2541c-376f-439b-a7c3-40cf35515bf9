import 'dart:async';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:domain/stream/exhaust_stream_executor.dart';
import 'package:feature_card_api/card.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:logging_api/logging.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sme_core_utils/utils.dart';
import 'package:tuple/tuple.dart';
import 'package:ui/cubit/base_cubit.dart';
import 'package:ui/cubit/core/loading_provider.dart';
import 'package:ui/cubit/extensions/bloc_extensions.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_card_ui/feature_card_ui.dart';
import 'package:wio_feature_card_ui/src/navigation/spending_limit_screen_navigation_config.dart';
import 'package:wio_feature_card_ui/src/screens/assistant_phone/assistant_phone_router.dart';
import 'package:wio_feature_card_ui/src/screens/new_virtual_card/analytics/new_virtual_card_analytics.dart';
import 'package:wio_feature_card_ui/src/screens/new_virtual_card/cubit/new_virtual_card_state.dart';
import 'package:wio_feature_card_ui/src/screens/new_virtual_card/new_virtual_card_internal_interactor.dart';
import 'package:wio_feature_card_ui/src/screens/new_virtual_card/new_virtual_card_router.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_status_view_api/feature_status_view.dart';
import 'package:wio_sme_error_handler_api/error_handler_api.dart';

part 'new_virtual_card_cubit.freezed.dart';

/// Result object which will be returned from Virtual card creation page
@freezed
class NewVirtualCardResult with _$NewVirtualCardResult {
  const factory NewVirtualCardResult.cancel() = _NewVirtualCardResultCancel;

  const factory NewVirtualCardResult.success({required NewVirtualCard card}) =
      _NewVirtualCardResultSuccess;
}

/// number of max expiry years diff from current year
const _maxCardExpiryYearDifference = 5;

// For document creation notes
// 1. Join all interactors into one
//    1.1 Put semi-domain logic inside
// 2. Put navigation logic in different class -  - 21%
// Result 413 lines before
// Result 345 lines after
// After refactoring reduce code base on 16.5%

class NewVirtualCardCubit extends BaseCubit<NewVirtualCardState> {
  static const _longNameMaxLength = 31;

  final NewVirtualCardRouter _router;
  final NewVirtualCardInternalInteractor _interactor;
  final CardLocalizations _cardLocalizations;
  final ToastMessageProvider _toastMessageProvider;
  final LoadingProvider _loadingProvider;
  final ExhaustStreamExecutor _exhaustStreamExecutor;
  final NewVirtualCardCubitAnalytics _analytics;
  final FeatureToggleProvider _featureToggles;
  final ErrorHandlerTool _errorHandler;
  final Logger _logger;

  final BehaviorSubject<List<String>> _existedCardNamesSubject =
      BehaviorSubject<List<String>>.seeded([]);

  final BehaviorSubject<Tuple2<Money, CardSpendingLimitFrequency>?>
      _spendingLimitSubject = BehaviorSubject();

  NewVirtualCardCubit({
    required NewVirtualCardRouter router,
    required NewVirtualCardInternalInteractor interactor,
    required CardLocalizations cardLocalizations,
    required ToastMessageProvider toastMessageProvider,
    required LoadingProvider loadingProvider,
    required ExhaustStreamExecutor exhaustStreamExecutor,
    required NewVirtualCardCubitAnalytics newVirtualCardCubitAnalytics,
    required FeatureToggleProvider featureToggles,
    required ErrorHandlerTool errorHandler,
    required Logger logger,
  })  : _interactor = interactor,
        _router = router,
        _cardLocalizations = cardLocalizations,
        _toastMessageProvider = toastMessageProvider,
        _loadingProvider = loadingProvider,
        _exhaustStreamExecutor = exhaustStreamExecutor,
        _analytics = newVirtualCardCubitAnalytics,
        _featureToggles = featureToggles,
        _errorHandler = errorHandler,
        _logger = logger,
        super(const NewVirtualCardState());

  @override
  String toString() => 'NewVirtualCardCubit{}';

  bool get hasAssistantPhoneFeature {
    final flag =
        _featureToggles.get(CardsFeatureToggles.assistantPhoneNumberFeature);
    return flag == FeatureVariant.variantA || flag == FeatureVariant.variantB;
  }

  void init({AssistantPhone? assistantPhone}) {
    if (assistantPhone != null) {
      emit(state.copyWith(assistantPhoneNumber: assistantPhone));
    }

    _interactor
        .getExistingCardNames()
        .doOnData(_existedCardNamesSubject.add)
        .withLoading(_loadingProvider)
        .withError(_errorHandler.handleException)
        .listenSafe(this);

    _spendingLimitSubject
        .doOnData(
          (spendingLimit) => emit(
            state.copyWith(
              spendingLimit: spendingLimit?.item1,
              frequencyType: spendingLimit?.item2,
              spendingLimitFormatted:
                  _getFormattedSpendingLimit(spendingLimit?.item1),
            ),
          ),
        )
        .listenSafe(this);
  }

  @override
  Future<void> close() {
    _existedCardNamesSubject.close();
    _spendingLimitSubject.close();

    return super.close();
  }

  void onCardNameInputChange(String cardName) {
    var newName = cardName.trim();
    newName = newName.replaceAll(RegExp(r'\s+'), ' ');

    final cardNames = _existedCardNamesSubject.value;

    final isCardNameExists =
        cardNames.any((e) => e.toLowerCase() == cardName.toLowerCase());

    final cardIsTooLong = newName.length > _longNameMaxLength;

    emit(
      state.copyWith(
        inNameInputMode: true,
        virtualCardName: newName,
        virtualCardNameError: isCardNameExists
            ? _cardLocalizations.newVirtualCardPageCardNameExists
            : cardIsTooLong
                ? _cardLocalizations.newVirtualCardScreenErrorNameTooLong
                : null,
      ),
    );
  }

  void onGoBack() {
    if (!state.inNameInputMode) {
      emit(state.copyWith(inNameInputMode: true));
    } else {
      _router.goBack();
    }
  }

  void onNameInputFocusChange({
    required bool isFocused,
  }) {
    if (isFocused) {
      _analytics.enterCardName();
      emit(state.copyWith(inNameInputMode: true));
    }
  }

  void onSubmit() => _exhaustStreamExecutor.run(_onSubmitInternal);

  void removeExpiryDateTime() {
    _analytics.clickExpiryDateToggle(isOn: false);
    emit(state.copyWith(expiryDate: null));
  }

  void removeSpendingLimit() {
    _analytics.clickSpendingLimitToggle(isOn: false);
    _spendingLimitSubject.add(null);
  }

  Future<void> selectExpiryDateTime() async {
    _analytics.clickExpiryDateToggle(isOn: true);
    final currentDate = DateTime.now();
    final maxDate = currentDate.copyWith(
      year: _maxCardExpiryYearDifference + currentDate.year,
    );

    final result = await _router.showDateTimeSelection(
      initialDate: state.expiryDate,
      minimumDate: currentDate,
      maximumDate: maxDate,
    );

    if (result case DateTimeSelectionBottomSheetResponse(:final dateTime?)) {
      _analytics.clickSelectExpiryDate();
      emit(state.copyWith(expiryDate: dateTime));
    } else {
      _analytics.closeExpiryDateBottomSheet();
    }
  }

  Future<void> selectSpendingLimit() async {
    _analytics.clickSpendingLimitToggle(isOn: true);
    final result = await _router.navigateSpendingLimits(
      state.spendingLimit?.integerPart.toInt(),
      state.frequencyType,
    );

    if (result is SpendingLimitPageResult) {
      result.map(
        selected: (s) {
          _spendingLimitSubject
              .add(Tuple2(s.selectedAmount, s.limitFrequencyType));
        },
        empty: (_) => _spendingLimitSubject.add(null),
      );
    } else {
      _spendingLimitSubject.add(null);
    }
  }

  void toggleAssistantPhoneNumber() {
    assert(hasAssistantPhoneFeature, 'Assistant phone feature is not enabled');
    if (state.isAssistantPhoneNumberSet) {
      _removeAssistantPhoneNumber();
    } else {
      unawaited(selectAssistantPhoneNumber());
    }
  }

  Future<void> selectAssistantPhoneNumber() async {
    assert(hasAssistantPhoneFeature, 'Assistant phone feature is not enabled');

    // TODO(esultanli): send analytic event
    final result = await _router.navigateToAssistantPhone(
      initialPhone: state.assistantPhoneNumber,
    );

    if (result case AssistantPhonePageResult(:final phoneNumber?)) {
      emit(state.copyWith(assistantPhoneNumber: phoneNumber));
    }
  }

  void _removeAssistantPhoneNumber() {
    assert(
      state.assistantPhoneNumber != null,
      'Cannot remove assistant phone number unless it is set',
    );

    emit(state.copyWith(assistantPhoneNumber: null));
  }

  String? _getFormattedSpendingLimit(Money? spendingLimit) {
    if (spendingLimit != null) {
      return spendingLimit.toCodeOnRightFormat();
    }

    return null;
  }

  String? _getFormattedExpiryDate(DateTime? expiryDate) {
    return expiryDate?.formatTo(const DateTimePatternSME.ddMMMMyyyySpace());
  }

  Stream<void> _onSubmitInternal() async* {
    if (state.inNameInputMode) {
      yield _checkCardName();
    } else {
      _analytics.clickCreateCard();
      yield _createVirtualCard();
    }
  }

  Future<void> _checkCardName() async {
    final cardName = state.virtualCardName;
    if (cardName.isEmpty) {
      emit(
        state.copyWith(
          virtualCardNameError:
              _cardLocalizations.newVirtualCardPageCardNameGenericError,
        ),
      );
      return;
    }

    final cardNames = _existedCardNamesSubject.value;

    final isCardNameExists =
        cardNames.any((e) => e.toLowerCase() == cardName.toLowerCase());

    if (isCardNameExists) {
      emit(
        state.copyWith(
          virtualCardNameError:
              _cardLocalizations.newVirtualCardPageCardNameExists,
        ),
      );
    } else if (cardName.length > _longNameMaxLength) {
      emit(
        state.copyWith(
          virtualCardNameError:
              _cardLocalizations.newVirtualCardScreenErrorNameTooLong,
        ),
      );
    } else {
      emit(state.copyWith(inNameInputMode: false));
    }
  }

  Future<void> _createVirtualCard() {
    return _interactor
        .createNewVirtualCard(
          virtualCardName: state.virtualCardName,
          expiryDate: state.expiryDate,
          spendingLimit: state.spendingLimit,
          assistantPhoneNumber: state.assistantPhoneNumber?.toServerFormat(),
          limitFrequencyType: state.frequencyType,
        )
        .withLoading(_loadingProvider)
        .asyncMap((card) async {
          await Future.wait([
            _showSuccessfullyCreatedScreen(card),
            // Show app rating bottom sheet over the success screen.
            Future.delayed(
              const Duration(milliseconds: 100),
              _router.showRateAppFlow,
            ),
          ]);
          return card;
        })
        .logError(_logger)
        .withError<NewCardCreationException>(_handleCardCreationErrors)
        .withError<Exception>(_showErrorScreen)
        .complete();
  }

  Future<void> _showSuccessfullyCreatedScreen(NewVirtualCard card) async {
    final result = await _router.navigateSuccessScreen(
      title: _successfullyCreatedSubtitle(card),
      cardName: card.cardName,
      spendingLimit: _getFormattedSpendingLimit(card.spendingLimit),
      expiryDate: _getFormattedExpiryDate(card.expiryDate),
    );

    if (result is StatusPageResult) {
      result.whenOrNull(
        canceled: (reason) {
          if (reason == StatusPageCancelReason.swipeUp) {
            _analytics.swipedToCloseSuccessScreen();
          }
        },
      );
    }

    _router.finishSuccessfully(card);
  }

  String _successfullyCreatedSubtitle(NewVirtualCard card) {
    final stringBuffer = StringBuffer(
      _cardLocalizations.newVirtualCardPageSuccessSubtitleBase(card.cardName),
    );

    final spendingLimit = card.spendingLimit;
    if (spendingLimit != null && !spendingLimit.isZero) {
      stringBuffer
        ..write('\n\n')
        ..writeln(_cardLocalizations.newVirtualCardPageSpendingLimit)
        ..write(_getFormattedSpendingLimit(spendingLimit));
    }

    late final expiryDate = card.expiryDate;
    // NOTE: need to check both since the server sends an expiry date
    // in any case, even when we don't explicitly set one.
    if (state.expiryDate != null && expiryDate != null) {
      stringBuffer
        ..write('\n\n')
        ..writeln(_cardLocalizations.newVirtualCardPageExpiryDate)
        ..write(_getFormattedExpiryDate(expiryDate));
    }

    return stringBuffer.toString();
  }

  void _handleCardCreationErrors(NewCardCreationException exception) {
    void handleNotAllowedCase(String message) {
      _toastMessageProvider.showRetailMobileThemedToastMessage(
        NotificationToastMessageConfiguration.error(message),
      );

      _router.goBack();
    }

    exception.mapOrNull(
      general: (it) => _errorHandler.handleException(
        exception,
        displayMessage: it.message,
      ),
      noSufficientBalance: (_) => _notEnoughFundsScreen(),
      dailyLimitCardCreationReached: (it) =>
          handleNotAllowedCase(it.additionalInfo),
      notAllowed: (it) => handleNotAllowedCase(it.message),
    );
  }

  Future<void> _showErrorScreen(Exception error) async {
    final result = await _router.navigateErrorScreen();
    if (result is StatusPageResult) {
      result.mapOrNull(
        primaryButtonSelection: (_) {
          _analytics.clickBackToCards();
        },
      );
    }
    _router.finishUnsuccessfully();
  }

  Future<void> _notEnoughFundsScreen() async {
    final pageResult = await _router.navigateNotEnoughFounds();
    _router.finishUnsuccessfully();
    if (pageResult is StatusPageResult) {
      pageResult.mapOrNull(
        primaryButtonSelection: (_) {
          _router.showAddMoney();
        },
        secondaryButtonSelection: (_) {
          _analytics.clickBackToCards();
        },
      );
    }
  }
}
