import 'dart:async';

import 'package:account_feature_api/account_feature_api.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:data/data.dart';
import 'package:deeplink_manager/deep_link.dart';
import 'package:feature_2fa_api/feature_2fa_api.dart';
import 'package:feature_bottom_sheet_adapter_api/feature_bottom_sheet_adapter_api.dart';
import 'package:feature_card_api/card.dart';
import 'package:feature_onboarding_api/feature_onboarding_api.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_card_api/wio_common_feature_card_api.dart';
import 'package:wio_common_feature_customer_address_api/navigation/customer_address_feature_navigation_factory.dart';
import 'package:wio_feature_behaviour_api/feature_behaviour_api.dart';
import 'package:wio_feature_card_ui/src/bottomsheets/card_detaills/card_details_analytics.dart';
import 'package:wio_feature_card_ui/src/common/card_wallet_analytics.dart';
import 'package:wio_feature_card_ui/src/common/card_wallet_delegate.dart';
import 'package:wio_feature_card_ui/src/common/cards_feedback_handler.dart';
import 'package:wio_feature_card_ui/src/common/share_card_handler.dart';
import 'package:wio_feature_card_ui/src/navigation/bottomsheets/view_card_pin_bottomsheet_navigation_config.dart';
import 'package:wio_feature_card_ui/src/screens/assistant_phone/assistant_phone_mapper.dart';
import 'package:wio_feature_card_ui/src/screens/assistant_phone/assistant_phone_router.dart';
import 'package:wio_feature_card_ui/src/screens/assistant_phone/cubit/phone_validator.dart';
import 'package:wio_feature_card_ui/src/screens/assistant_phone/helpers/phone_helper.dart';
import 'package:wio_feature_card_ui/src/screens/card_replacement/card_replacement_flow.dart';
import 'package:wio_feature_card_ui/src/screens/cards/analytics/cards_cubit_analytics.dart';
import 'package:wio_feature_card_ui/src/screens/cards/cards_internal_interactor.dart';
import 'package:wio_feature_card_ui/src/screens/cards/cards_router.dart';
import 'package:wio_feature_card_ui/src/screens/cards/cards_screen_model_mapper.dart';
import 'package:wio_feature_card_ui/src/screens/cards/models/card_view_model.dart';
import 'package:wio_feature_card_ui/src/screens/manage_card/analytics/manage_card_cubit_analytics.dart';
import 'package:wio_feature_card_ui/src/screens/manage_card/manage_card_internal_interactor.dart';
import 'package:wio_feature_card_ui/src/screens/manage_card/manage_card_mapper.dart';
import 'package:wio_feature_card_ui/src/screens/manage_card/manage_card_router.dart';
import 'package:wio_feature_card_ui/src/screens/new_virtual_card/analytics/new_virtual_card_analytics.dart';
import 'package:wio_feature_card_ui/src/screens/new_virtual_card/new_virtual_card_internal_interactor.dart';
import 'package:wio_feature_card_ui/src/screens/new_virtual_card/new_virtual_card_router.dart';
import 'package:wio_feature_card_ui/src/screens/spending_limit/analytics/spending_limit_cubit_analytics.dart';
import 'package:wio_feature_card_ui/src/screens/spending_limit/spending_limit_flow.dart';
import 'package:wio_feature_contact_support_api/common/contact_us_provider.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_delivery_api/domain/interactor/card_delivery_interactor.dart';
import 'package:wio_feature_home_api/wio_feature_home_api.dart';
import 'package:wio_feature_mu_payment_requests_api/domain/mu_payment_requests_interactor.dart';
import 'package:wio_feature_phone_api/domain/use_case/parse_phone_use_case.dart';
import 'package:wio_feature_phone_contacts_api/feature_phone_contacts_api.dart';
import 'package:wio_feature_share_api/models/domain_share_result.dart';
import 'package:wio_feature_status_view_api/feature_status_view.dart';
import 'package:wio_sme_error_handler_api/error_handler_api.dart';
import 'package:wio_wallet_api/wallet_api.dart' as wallet;

class FakeGenericCardBottomSheetConfiguration extends Fake
    implements GenericCardBottomSheetConfiguration {}

class FakeToastMessageConfiguration extends Fake
    implements ToastMessageConfiguration {}

class FakeCardDetailsCopyableFieldConfig extends Fake
    implements CardDetailsCopyableFieldConfig {}

class FakeCard extends Fake implements Card {}

class FakeCardViewModel extends Fake implements CardViewModel {
  @override
  String get cardName => 'card name';
}

class FakeListDetailsBottomSheetModel extends Fake
    implements ListDetailsBottomSheetModel {}

class FakeInfoBottomSheetConfiguration extends Fake
    implements InfoBottomSheetConfiguration {}

class FakeInfoBottomSheetResponse extends Fake
    implements InfoBottomSheetResponse {}

class FakeStatusViewFeatureNavigationConfig extends Fake
    implements StatusViewFeatureNavigationConfig {
  @override
  String toString() => 'FakeStatusViewFeatureNavigationConfig{}';
}

class FakeCardDetails extends Fake implements CardDetails {
  @override
  String get businessName => 'businessName';

  @override
  String get embossName => 'embossName';

  @override
  String get cardNumber => 'cardNumber';

  @override
  DateTime get expiryDate => DateTime(2022, 8);

  @override
  String get cvv => 'cvv';
}

class FakeAuthCredentials extends Fake implements AuthCredentials {}

class MockCardInteractor extends Mock implements CardInteractor {
  MockCardInteractor() {
    registerFallbackValue(FakeAuthCredentials());
    when(refreshCards).justCompleteAsync();
    when(() => freezeCard(cardId: anyNamed('cardId'))).justCompleteAsync();
    when(() => unfreezeCard(cardId: anyNamed('cardId'))).justCompleteAsync();
  }
}

class MockCardsRouter extends Mock implements CardsRouter {
  MockCardsRouter() {
    registerFallbackValue(FakeCardDetails());

    when(dispose).justCompleteAsync();
  }
}

class MockCardSettingsInteractor extends Mock
    implements CardSettingsInteractor {
  MockCardSettingsInteractor() {
    registerFallbackValue(FakeAuthCredentials());

    when(
      () => updateCardPin(
        cardId: any(named: 'cardId'),
        cardPin: any(named: 'cardPin'),
      ),
    ).justCompleteAsync();
  }
}

class MockBottomSheetAdapter extends Mock implements BottomSheetAdapter {}

class MockAccountInteractor extends Mock implements AccountInteractor {}

class MockCardDeliveryInteractor extends Mock
    implements CardDeliveryInteractor {}

class MockNewCardInteractor extends Mock implements NewCardInteractor {}

class MockNewVirtualCardRouter extends Mock implements NewVirtualCardRouter {
  MockNewVirtualCardRouter() : super() {
    when(showRateAppFlow).thenAnswer((_) async {});
  }
}

class MockNewVirtualCardInternalInteractor extends Mock
    implements NewVirtualCardInternalInteractor {}

class MockCardsCubitAnalytics extends Mock implements CardsCubitAnalytics {}

class MockCardWalletAnalytics extends Mock implements CardWalletAnalytics {}

class MockManageCardCubitAnalytics extends Mock
    implements ManageCardCubitAnalytics {}

class MockNewVirtualCardCubitAnalytics extends Mock
    implements NewVirtualCardCubitAnalytics {}

class MockSpendingLimitCubitAnalytics extends Mock
    implements SpendingLimitCubitAnalytics {}

class MockCardsScreenModelMapper extends Mock
    implements CardsScreenModelMapper {
  MockCardsScreenModelMapper() {
    registerFallbackValue(FakeCardDetailsCopyableFieldConfig());
    registerFallbackValue(FakeCardViewModel());
    registerFallbackValue(FakeCardDetails());
    registerFallbackValue(FakeCard());
  }
}

class MockCustomerAddressFeatureNavigationFactory extends Mock
    implements CustomerAddressFeatureNavigationFactory {}

class MockFeatureToggleProvider extends Mock implements FeatureToggleProvider {}

class MockHomeNotificationChannel extends Mock
    implements HomeNotificationChannel {
  final StreamController<TabSwitchEvent> _streamController =
      StreamController<TabSwitchEvent>.broadcast();

  MockHomeNotificationChannel();

  @override
  void addEventToStream(TabSwitchEvent tabEvent) =>
      _streamController.add(tabEvent);

  @override
  Stream<TabSwitchEvent> get homeTabsStream => _streamController.stream;

  @override
  void dispose() {
    _streamController.close();
  }
}

class MockChangeHomeTabChannel extends Mock implements ChangeHomeTabChannel {
  final StreamController<ChangeHomeTabEvent> _streamController =
      StreamController<ChangeHomeTabEvent>.broadcast();

  MockChangeHomeTabChannel();

  @override
  void addEventToStream(ChangeHomeTabEvent tabEvent) =>
      _streamController.add(tabEvent);

  @override
  Stream<ChangeHomeTabEvent> get homeTabsStream => _streamController.stream;

  @override
  void dispose() {
    _streamController.close();
  }
}

class MockCardsGuideInteractor extends Mock implements CardsGuideInteractor {}

class MockCardsInternalInteractor extends Mock
    implements CardsInternalInteractor {}

// ignore: deprecated_member_use
class MockBoundDeviceUseCase extends Mock implements BoundDeviceUseCase {}

class MockWalletAvailableUseCase extends Mock
    implements wallet.WalletAvailableUseCase {}

class MockAddToWalletUseCase extends Mock
    implements wallet.AddToWalletUseCase {}

class MockManageCardInternalInteractor extends Mock
    implements ManageCardInternalInteractor {}

class MockManageCardRouter extends Mock implements ManageCardRouter {}

class MockManageCardMapper extends Mock implements ManageCardMapper {}

class MockAssistantPhoneRouter extends Mock implements AssistantPhoneRouter {}

class MockPhoneValidator extends Mock implements PhoneValidator {}

class MockPhoneHelper extends Mock implements PhoneHelper {}

class MockAssistantPhoneMapper extends Mock implements AssistantPhoneMapper {}

class FakeWalletAvailableParams extends Mock
    implements wallet.WalletAvailabilityParams {}

class MockContactUsProvider extends Mock implements ContactUsProvider {}

class FakeAddToWalletParams extends Mock implements wallet.AddToWalletParams {}

class MockCompanyPermissionResolver extends Mock
    implements CompanyPermissionResolver {
  MockCompanyPermissionResolver() {
    when(openDeviceSettings).justAnswerAsync(true);
  }
}

class MockPhoneContactsInteractor extends Mock
    implements PhoneContactsInteractor {}

class MockParsePhoneUseCase extends Mock implements ParsePhoneUseCase {}

class MockErrorHandlerTool extends Mock implements ErrorHandlerTool {}

void featureCardUiRegisterFallbackValues() {
  registerFallbackValue(PinCodeFlowType.changePin);
  registerFallbackValue(PinCodeFlowStep.inputPin);
  registerFallbackValue(FakeGenericCardBottomSheetConfiguration());
  registerFallbackValue(
    const ViewCardPinBottomSheetNavigationConfig(
      cardPin: 'cardPin',
      canChangePin: false,
    ),
  );
  registerFallbackValue(FakeCard());
  registerFallbackValue(FakeAuthCredentials());
  registerFallbackValue(CardSettingType.atmWithdrawal);
  registerFallbackValue(CardSettingState.disabled);
  registerFallbackValue(FakeToastMessageConfiguration());
  registerFallbackValue(Money.fromIntWithCurrency(0, Currency.aed));
}

class MockPerformanceTraceTransaction extends Mock
    implements PerformanceTraceTransaction {}

class MockPerformanceMonitor extends Mock implements PerformanceMonitor {
  final transaction = MockPerformanceTraceTransaction();

  MockPerformanceMonitor() {
    registerFallbackValue(TraceOperationType.pageLoad);
    when(() => transaction.startChildTrace(any()))
        .thenReturn(MockPerformanceTraceTransaction());

    when(
      () => startTrace(
        operation: any(named: 'operation'),
        traceName: any(named: 'traceName'),
      ),
    ).thenReturn(transaction);
  }
}

class MockCreditAccountInteractor extends Mock
    implements CreditAccountInteractor {}

class MockDeeplinkRepository extends Mock implements DeepLinkRepository {}

class MockAuthManager extends Mock implements IAuthManager {}

class MockCreditAnalytics extends Mock implements CreditAnalytics {}

class MockBehaviourProvider extends Mock implements BehaviourProvider {}

class MockMuPaymentRequestsInteractor extends Mock
    implements MuPaymentRequestsInteractor {}

class MockCardFeedbackHandler extends Mock implements CardsFeedbackHandler {}

class MockCardDetailsAnalytics extends Mock implements CardDetailsAnalytics {}

class MockShareCardHandler extends Mock implements ShareCardHandler {}

class FakeDomainShareResult extends Mock implements DomainShareResult {}

class MockSpendingLimitFlow extends Mock implements SpendingLimitFlow {}

class MockCardReplacementFlow extends Mock implements CardReplacementFlow {}

class MockBindDeviceInteractor extends Mock implements BindDeviceInteractor {}

class MockCardWalletDelegate extends Mock implements CardWalletDelegate {}

class MockCreditStatusInteractor extends Mock
    implements CreditStatusInteractor {}
