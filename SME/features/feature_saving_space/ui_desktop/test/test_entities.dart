import 'package:account_feature_api/account_feature_api.dart';
import 'package:feature_2fa_api/feature_2fa_api.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_mu_payment_requests_api/domain/models/mu_creation_flow.dart';
import 'package:wio_feature_mu_payment_requests_api/domain/models/mu_domain.dart';
import 'package:wio_feature_mu_payment_requests_api/domain/models/mu_domain_types.dart';
import 'package:wio_feature_mu_payment_requests_api/domain/models/mu_request_ids.dart';
import 'package:wio_feature_mu_payment_requests_api/domain/models/mu_request_status.dart';
import 'package:wio_feature_mu_payment_requests_api/navigation/preparer_screen_navigation_config.dart';
import 'package:wio_feature_saving_space_api/domain/models/mu_request.dart';
import 'package:wio_feature_saving_space_api/domain/models/saving_space.dart';
import 'package:wio_feature_saving_space_api/domain/models/saving_space_create_request.dart';
import 'package:wio_feature_saving_space_api/domain/models/saving_space_image.dart';
import 'package:wio_feature_saving_space_ui_desktop/locale/saving_space_ui_desktop_localizations.g.dart';
import 'package:wio_feature_saving_space_ui_desktop/src/common/extension.dart';

class TestEntities {
  static const expectedDomainId = 'domainId';

  static const requestId = 'requestId';

  static Currency defaultCurrency = Currency.aed;

  static MuRequest muRequest = const MuRequest(
    requestId: requestId,
    domainId: expectedDomainId,
  );

  static MuDomain preparerMuDomain = MuDomain(
    requestId: 'requestId',
    transferId: expectedDomainId,
    muDomainType: MuDomainType.international,
    status: MuRequestStatus.completed,
    authorId: 'authorId',
    approvers: [],
    docs: [],
    updatedAt: DateTime(2023),
    createdAt: DateTime(2023),
    creationFlowType: MuCreationFlowType.preparer,
  );

  static Account account = const Account(
    id: 'accountId',
    name: 'accountName',
    balance: 1000,
    currency: 'AED',
  );

  static MuDomain selfApproveMuDomain = MuDomain(
    requestId: 'requestId',
    transferId: expectedDomainId,
    muDomainType: MuDomainType.international,
    status: MuRequestStatus.completed,
    authorId: 'authorId',
    approvers: [],
    docs: [],
    updatedAt: DateTime(2023),
    createdAt: DateTime(2023),
    creationFlowType: MuCreationFlowType.selfApprover,
  );

  static RegularSavingSpaceCreateRequest savingSpaceCreateRequest =
      RegularSavingSpaceCreateRequest(
    name: savingSpaceName,
    image: savingSpace.image,
    currency: savingSpace.currency,
    goalDate: savingSpace.goalDate,
    goal: savingSpace.goalAmount?.toDouble(),
  );

  static String savingSpaceName = 'ssCreateName';

  static String goalTargetDateScreenConfig = 'Dec 12 2025';

  static String goalAmountScreenConfig = '10.00 AED';

  static SavingSpace savingSpace = SavingSpace.regular(
    name: 'savingSpaceName',
    goalAmount: Money.fromNumWithCurrency(10, defaultCurrency),
    id: 'savingSpaceId',
    image: const SavingSpaceImage.local(name: '', path: ''),
    balance: Money.fromNumWithCurrency(5, defaultCurrency),
    goalDate: DateTime(2025, 12, 12),
  );

  static List<SavingSpace> savingSpaces = [
    savingSpace,
  ];

  static AuthCredentials fakeAuthCredentials = const AuthCredentials.twoFA(
    transactionId: 'transactionId',
    deviceCheckTransactionId: 'deviceCheckTransactionId',
    deviceCheckSessionId: 'deviceCheckSessionId',
  );

  TestEntities._();

  static SuccessScreenModel successScreenModel({
    required String spaceName,
    required SavingSpaceUILocalizations localizations,
    double? goalAmount,
    DateTime? goalDate,
  }) =>
      SuccessScreenModel(
        startColumnTitleText: localizations.saveGoalSuccessTitle.toUpperCase(),
        startColumnSubtitleText: localizations.saveGoalSuccessSubtitle,
        ctaText: localizations.successCtaText,
        content: [
          if (goalAmount != null)
            SuccessScreenRowModel(
              partOne: localizations.saveGoalSuccessAmountDescription,
              partTwo: convertNumberToAmount(
                amount: goalAmount,
                currency: 'AED',
              ),
            ),
          if (goalDate != null)
            SuccessScreenRowModel(
              partOne: localizations.saveGoalBy,
              partTwo: formatDate(
                format: 'MMM d, yyyy',
                date: goalDate,
              ),
            ),
          SuccessScreenRowModel(
            partOne: localizations.savingSpaceFor,
            partTwo: spaceName,
          ),
        ],
      );

  static PreparerScreenNavigationConfig get additionalInfoNavigationConfig =>
      PreparerScreenNavigationConfig(
        muRequestIds: MuRequestIds(muRequestIds: [preparerMuDomain.requestId]),
        muDomainType: MuDomainType.savingSpaces,
        flowTitle: 'flowTitle',
        onComplete: (_) {},
      );

  static List<Account> fakeAccounts({bool onlyAed = false}) => [
        const Account(
          id: 'acc-1',
          name: 'TestAccount',
          balance: 500.50,
          currency: 'AED',
        ),
        const Account(
          id: 'acc-2',
          name: 'TestAccount1',
          balance: 200.50,
          currency: 'AED',
        ),
        if (!onlyAed) ...[
          const Account(
            id: 'acc-3',
            name: 'TestAccount3',
            balance: 80.50,
            currency: 'USD',
          ),
        ],
      ];
}
