import 'dart:async';

import 'package:confetti/confetti.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:sme_core_utils/utils.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_feature_behaviour_ui/feature_behaviour_ui.dart';
import 'package:wio_feature_saving_space_api/domain/models/saving_space.dart';
import 'package:wio_feature_saving_space_ui_desktop/locale/saving_space_ui_desktop_localizations.g.dart';
import 'package:wio_feature_saving_space_ui_desktop/src/common/constants.dart';
import 'package:wio_feature_saving_space_ui_desktop/src/common/extension.dart';
import 'package:wio_sme_behaviour/wio_sme_behaviour.dart';

class _MyCustomScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
      };
}

// TODO(amaltsev): refactor this widget, returned old implementation
class SpacesCards extends StatefulWidget {
  final List<SavingSpace> savingSpaces;
  final int cardSelected;
  final void Function(String savingSpaceId) onClick;
  final void Function(SavingSpace savingSpace) onAddFundCallback;
  final void Function(SavingSpace savingSpace) onEditGoalCallback;
  final void Function(SavingSpace savingSpace) onEditSpaceCallback;
  final void Function(SavingSpace savingSpace) onWithdrawCallback;
  final void Function(SavingSpace savingSpace) onDeleteCallback;

  const SpacesCards({
    required this.onAddFundCallback,
    required this.onEditGoalCallback,
    required this.onEditSpaceCallback,
    required this.onWithdrawCallback,
    required this.onDeleteCallback,
    required this.savingSpaces,
    required this.cardSelected,
    required this.onClick,
    super.key,
  });

  @override
  State<SpacesCards> createState() => _SpacesCardsState();
}

class _SpacesCardsState extends State<SpacesCards>
    with TickerProviderStateMixin {
  final _scrollController = ScrollController();

  late AnimationController _controller;
  late ConfettiController _controllerConfetti;
  late AnimationController _emojiAnimationController;
  late AnimationController _tooltipAnimationController;

  int _cardActive = 0;
  int _lastCardActive = 0;

  SavingSpace get activeSavingSpace => widget.savingSpaces[_cardActive];

  bool get hasReachedAmountGoal =>
      activeSavingSpace.goalAmount != null &&
      activeSavingSpace.balance >= activeSavingSpace.goalAmount!;

  bool get shouldShowTooltip =>
      hasReachedAmountGoal || hasExpiredDate(activeSavingSpace.goalDate);

  bool hasExpiredDate(DateTime? date) {
    final now = DateTime.now();
    if (date != null) {
      final isAfter = now.isAfter(date);

      return isAfter &&
          DateUtils.datesOnly(
                DateTimeRange(
                  start: isAfter ? date : now,
                  end: isAfter ? now : date,
                ),
              ).duration.inDays >
              1;
    }

    return false;
  }

  bool hasToShowTooltip() {
    return hasReachedAmountGoal || hasExpiredDate(activeSavingSpace.goalDate);
  }

  int msToWaitBeforeShowTooltip({required bool isGoalTooltip}) {
    return isGoalTooltip ? 500 : 0;
  }

  void listenerToShowEmojiGoal() {
    _controller.addStatusListener((status) async {
      final isGoalReached = hasReachedAmountGoal;
      if (status == AnimationStatus.completed && hasToShowTooltip()) {
        //Await progress bar duration
        await Future<void>.delayed(
          Duration(
            milliseconds:
                msToWaitBeforeShowTooltip(isGoalTooltip: isGoalReached),
          ),
        );
        unawaited(_emojiAnimationController.forward(from: 0.0));
        if (isGoalReached) {
          _controllerConfetti.play();
        }
      }
    });
  }

  Future<void> animateAndExpand(int index) async {
    _emojiAnimationController.reset();
    setState(() {
      _lastCardActive = _cardActive;
      _cardActive = index;
    });

    unawaited(_controller.forward(from: 0.0));

    await Future<void>.delayed(const Duration(milliseconds: 500));
    unawaited(_tooltipAnimationController.forward(from: 0.0));
  }

  SavingSpaceCardModel toSavingSpaceCardModel(
    SavingSpaceUILocalizations localizations,
    int index,
  ) {
    final space = widget.savingSpaces[index];
    final targetDate = space.goalDate;
    final targetDateStr =
        targetDate?.formatTo(const DateTimePatternSME.ddMMMMyyyySpace());
    final imagePath = space.image.path;

    return SavingSpaceCardModel(
      id: space.id,
      amount: space.balance.toDouble(),
      amountFormatted: space.balance.toAmountWithCode(),
      backgroundImage: AssetImage(
        imagePath,
        package: SavingSpaceConstants.imagePackageName,
      ),
      currency: space.balance.currency.code,
      title: space.name,
      goalAmount: space.goalAmount?.toDouble() ?? 0,
      goalAmountFormatted: space.goalAmount?.toAmountWithCode(),
      goalAmountText: localizations.savingSpaceOf,
      goalTargetDateText: targetDateStr != null
          ? '${localizations.savingSpaceGoalDate}: $targetDateStr'
          : null,
      expanded: index == _cardActive,
      goal: space.goalAmount != null,
      haveToCollapse: index == _lastCardActive,
      spotlightCardModel: getTooltip(
        goalTargetDate: targetDate,
        localizations: localizations,
        goalAmount: space.goalAmount?.toDouble(),
      ),
      motivationalText: localizations.savingSpaceMotivational,
      isDateExpired: hasExpiredDate(targetDate),
    );
  }

  SpotlightCardModel? getTooltip({
    required DateTime? goalTargetDate,
    required SavingSpaceUILocalizations localizations,
    required double? goalAmount,
  }) {
    if (hasReachedAmountGoal) {
      return SpotlightCardModel(
        highlightText: '${localizations.goalTooltipHighlightText} ',
        text: localizations.goalTooltipText(goalAmount.toString()),
        isLeftSide: false,
      );
    }

    if (hasExpiredDate(goalTargetDate)) {
      return SpotlightCardModel(
        text: localizations.dateTooltipText,
      );
    }

    return null;
  }

  @override
  void initState() {
    super.initState();
    _controllerConfetti =
        ConfettiController(duration: const Duration(milliseconds: 500));
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _emojiAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _tooltipAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    listenerToShowEmojiGoal();
    widget.onClick(activeSavingSpace.id);
  }

  @override
  void dispose() {
    _controller.dispose();
    _controllerConfetti.dispose();
    _emojiAnimationController.dispose();
    _tooltipAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localization = SavingSpaceUILocalizations.of(context);
    final hasSavingSpaceCreatePermission = BehaviourProviderWrapper.of(context)
        .hasPermission<AccountsSavingSpaceCreatePermission>();
    final hasSavingSpaceEditPermission = BehaviourProviderWrapper.of(context)
        .hasPermission<AccountsEditWritePermission>();
    final hasAtLeastEditOrCreatePermissions =
        hasSavingSpaceEditPermission || hasSavingSpaceCreatePermission;

    return Stack(
      children: [
        ScrollConfiguration(
          behavior: _MyCustomScrollBehavior(),
          child: SizedBox(
            height: 480,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              controller: _scrollController,
              itemCount: widget.savingSpaces.length,
              itemBuilder: (_, i) {
                final model = toSavingSpaceCardModel(localization, i);
                final savingSpace = widget.savingSpaces[i];
                final callbacks = [
                  if (!model.isDateExpired)
                    () => widget.onEditGoalCallback(savingSpace),
                  if (!model.isDateExpired)
                    () => widget.onEditSpaceCallback(savingSpace),
                  () => widget.onDeleteCallback(savingSpace),
                ];

                return Padding(
                  padding: EdgeInsets.only(right: spacingSize(size.m)),
                  child: SavingSpaceCard(
                    model: model,
                    buttonsCluster: hasAtLeastEditOrCreatePermissions &&
                            savingSpace is RegularSavingSpace
                        ? [
                            SquareButton(
                              icon: CompanyIconPointer.plus,
                              variant: SquareButtonType.var7,
                              onClick: () async =>
                                  widget.onAddFundCallback(savingSpace),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.only(
                                start: Sizes.s3,
                              ),
                              child: SquareButton(
                                icon: CompanyIconPointer.arrow_right,
                                variant: SquareButtonType.var7,
                                onClick: () async =>
                                    widget.onWithdrawCallback(savingSpace),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsetsDirectional.only(
                                start: Sizes.s3,
                              ),
                              child: SquareMoreButton(
                                alignment: SquareMoreButtonAlignment.left,
                                icon: CompanyIconPointer.options_vertical,
                                overFlowActionsOptions: [
                                  if (!model.isDateExpired &&
                                      hasSavingSpaceEditPermission)
                                    localization.editGoal,
                                  if (!model.isDateExpired &&
                                      hasSavingSpaceEditPermission)
                                    localization.editSpace,
                                  localization.deleteSpace,
                                ]
                                    .map(
                                      (text) => text.toUpperCase(),
                                    )
                                    .toList(),
                                size: SquareButtonSize.m,
                                onClick: (index) => callbacks[index].call(),
                              ),
                            ),
                          ]
                        : [],
                    animationController: _controller,
                    confettiController: _controllerConfetti,
                    emojiAnimationController: _emojiAnimationController,
                    tooltipAnimationController: _tooltipAnimationController,
                    onClick: () => _onSavingSpaceClick(i),
                  ),
                );
              },
            ),
          ),
        ),
        Center(
          child: ConfettiAnimation(
            ConfettiAnimationModel(
              confettiController: _controllerConfetti,
            ),
          ),
        ),
      ],
    );
  }

  void _onSavingSpaceClick(int i) {
    widget.onClick(widget.savingSpaces[i].id);
    animateAndExpand(i);
  }
}
