// ignore_for_file: constant_identifier_names
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_saving_space_api/saving_space_api.dart';
import 'package:wio_feature_saving_space_ui/src/navigation/configs/create_saving_space_navigation_config.dart';

part 'create_saving_space_analytics.freezed.dart';

enum CreateSavingSpaceAnalyticsTarget {
  promo_completion,
  currency_item,
  name_confirmation,
}

@freezed
class CreateSavingSpaceAnalyticsPayload
    with _$CreateSavingSpaceAnalyticsPayload
    implements AnalyticsEventPayload {
  const CreateSavingSpaceAnalyticsPayload._();

  const factory CreateSavingSpaceAnalyticsPayload.currency({
    required Currency currency,
  }) = _CreateSavingSpaceCurrencySelectedPayload;

  const factory CreateSavingSpaceAnalyticsPayload.image({
    required SavingSpaceImage image,
  }) = _CreateSavingSpaceImageSelectedPayload;

  @override
  Map<String, dynamic> getEventPayload() => map(
        currency: (it) => <String, String>{'currency': it.currency.code},
        image: (it) => <String, String>{'image': it.image.name},
      );
}

class CreateSavingSpaceAnalytics {
  final AnalyticsEventTracker _analytics;

  CreateSavingSpaceAnalytics({
    required AnalyticsAbstractTrackerFactory analyticsFactory,
  }) : _analytics = analyticsFactory.get(
          screenName: CreateSavingSpaceNavigationConfig.screenName,
          tracker: AnalyticsTracker.mixpanel,
        );

  void promoCompleted() {
    _analytics.select(
      targetType: AnalyticsTargetType.button,
      target: CreateSavingSpaceAnalyticsTarget.promo_completion,
    );
  }

  void currencySelected(Currency currency) {
    _analytics.select(
      targetType: AnalyticsTargetType.list,
      target: CreateSavingSpaceAnalyticsTarget.currency_item,
      payload: CreateSavingSpaceAnalyticsPayload.currency(currency: currency),
    );
  }

  void nameSelected() {
    _analytics.click(
      targetType: AnalyticsTargetType.button,
      target: CreateSavingSpaceAnalyticsTarget.name_confirmation,
    );
  }

  void flowCancelled() {
    _analytics.clickBack();
  }

  void savingSpaceCreated(Currency currency) {
    _analytics.view(
      targetType: AnalyticsTargetType.screen,
      status: AnalyticsStatus.success,
      payload: CreateSavingSpaceAnalyticsPayload.currency(currency: currency),
    );
  }
}
