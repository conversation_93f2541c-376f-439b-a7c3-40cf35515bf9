part of '../manage_saving_space_page.dart';

class _NameOptions extends StatelessWidget {
  final String name;
  final VoidCallback? onEdit;

  const _NameOptions({
    required this.name,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    return ListDetailsContainer(
      onValuePressed: (_) => onEdit?.call(),
      model: ListDetailsContainerModel(
        items: [
          ListDetailsModel(
            textLabelModel: ListDetailsTextLabelModel(
              text: name,
              textColor: CompanyColorPointer.secondary1,
            ),
            valueModel: const ListDetailsValueModel.icon(
              iconPointer: _editIcon,
            ),
          ),
        ],
      ),
    );
  }
}
