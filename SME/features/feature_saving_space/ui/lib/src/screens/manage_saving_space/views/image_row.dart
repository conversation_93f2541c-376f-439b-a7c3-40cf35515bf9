part of '../manage_saving_space_page.dart';

class _ImageRow extends StatelessWidget {
  final List<SavingSpaceImage> images;
  final SavingSpaceImage? selectedImage;
  final ValueSetter<SavingSpaceImage>? onSelect;

  const _ImageRow({
    required this.images,
    required this.selectedImage,
    this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: _pagePadding),
      scrollDirection: Axis.horizontal,
      itemCount: images.length,
      separatorBuilder: (_, __) => Space.fromSpacingHorizontal(Spacing.s3),
      itemBuilder: (_, index) {
        final image = images[index];
        final isSelected = image == selectedImage;

        return ImageSelector(
          onTap: isSelected ? null : () => onSelect?.call(image),
          model: ImageSelectorModel(
            selected: isSelected,
            image: image.map(
              local: (it) => CompanyImageProvider.asset(
                name: it.path,
                package: SavingSpaceConstants.packageName,
              ),
            ),
          ),
        );
      },
    );
  }
}
