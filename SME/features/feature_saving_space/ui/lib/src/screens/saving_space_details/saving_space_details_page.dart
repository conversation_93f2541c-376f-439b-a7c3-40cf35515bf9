import 'dart:math';

import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:sme_feature_transactions_api/feature_transactions_api.dart';
import 'package:sme_feature_transactions_ui/wio_feature_transactions_ui.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart' hide SavingSpace;
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_saving_space_api/saving_space_api.dart';
import 'package:wio_feature_saving_space_ui/src/common/extensions.dart';
import 'package:wio_feature_saving_space_ui/src/common/models/interest_status.dart';
import 'package:wio_feature_saving_space_ui/src/common/views/error_view.dart';
import 'package:wio_feature_saving_space_ui/src/common/views/slide_to_view.dart';
import 'package:wio_feature_saving_space_ui/src/l10n/saving_space_localizations.g.dart';
import 'package:wio_feature_saving_space_ui/src/screens/saving_space_details/saving_space_details_cubit.dart';
import 'package:wio_feature_saving_space_ui/src/screens/saving_space_details/saving_space_details_state.dart';
import 'package:wio_feature_saving_space_ui/src/screens/saving_space_details/views/action_list.dart';
import 'package:wio_feature_saving_space_ui/src/screens/saving_space_details/views/common_views.dart';
import 'package:wio_feature_saving_space_ui/src/screens/saving_space_details/views/header_content.dart';
import 'package:wio_feature_saving_space_ui/src/screens/saving_space_details/views/saving_space_banners.dart';
import 'package:wio_feature_transaction_ui/feature_transaction_ui.dart';

part 'views/fixed_saving_space_details_content.dart';
part 'views/regular_saving_space_details_content.dart';
part 'views/saving_space_details_content.dart';
part 'views/saving_space_details_header.dart';
part 'views/saving_space_transactions_content.dart';

class SavingSpaceDetailsPage
    extends BasePage<SavingSpaceDetailsState, SavingSpaceDetailsCubit> {
  final String spaceId;
  final SavingSpace? space;

  const SavingSpaceDetailsPage({
    required this.spaceId,
    this.space,
    super.key,
  });

  @override
  SavingSpaceDetailsCubit createBloc() =>
      DependencyProvider.get<SavingSpaceDetailsCubit>();

  @override
  void initBloc(SavingSpaceDetailsCubit bloc) =>
      super.initBloc(bloc..initialize(spaceId, space: space));

  @override
  Widget buildPage(
    BuildContext context,
    SavingSpaceDetailsCubit bloc,
    SavingSpaceDetailsState state,
  ) =>
      BlocProvider<AllTransactionsCubit>(
        lazy: false, // to immediately start fetching transactions
        create: (_) => DependencyProvider.getWithParams<
            AllTransactionsCubit,
            TransactionParams,
            void>(param1: TransactionParams(accountIds: [spaceId]))
          ..initialize(),
        child: const _SavingSpaceDetailsPage(),
      );
}

class _SavingSpaceDetailsPage extends StatelessWidget {
  const _SavingSpaceDetailsPage();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.colorStyling.background1,
      body: TransactionPaginationListener(
        onPaginate: context.read<AllTransactionsCubit>().onPaginate,
        child: const CustomScrollView(
          slivers: [
            _SavingSpaceHeader(),
            _SavingSpaceContent(),
            _SavingSpaceTransactions(),
          ],
        ),
      ),
    );
  }
}

class _SavingSpaceHeader extends StatelessWidget {
  const _SavingSpaceHeader();

  @override
  Widget build(BuildContext context) {
    final state = context.watch<SavingSpaceDetailsCubit>().state;

    return switch (state) {
      LoadingSavingSpaceDetailsState(space: null) => const _LoadingHeader(),
      IdleSavingSpaceDetailsState(:final details) => _IdleHeader(details),
      FailedSavingSpaceDetailsState() => const _ErrorHeader(),
      _ => const SliverToBoxAdapter(),
    };
  }
}

class _SavingSpaceContent extends StatelessWidget {
  const _SavingSpaceContent();

  @override
  Widget build(BuildContext context) {
    final state = context.watch<SavingSpaceDetailsCubit>().state;

    return switch (state) {
      LoadingSavingSpaceDetailsState(space: null) => const _LoadingDetails(),
      IdleSavingSpaceDetailsState(:final details) =>
        _IdleDetails(details: details),
      FailedSavingSpaceDetailsState() => const _ErrorDetails(),
      _ => const SliverToBoxAdapter(),
    };
  }
}

class _SavingSpaceTransactions extends StatelessWidget {
  const _SavingSpaceTransactions();

  @override
  Widget build(BuildContext context) {
    final l10n = SavingSpaceLocalizations.of(context);
    final state = context.watch<SavingSpaceDetailsCubit>().state;

    return switch (state) {
      IdleSavingSpaceDetailsState() => SliverMainAxisGroup(
          slivers: [
            SliverToBoxAdapter(
              child: SectionTitle(
                title: l10n.savingSpaceDetailsTransactionsSectionTitle,
              ),
            ),
            const _SavingSpaceTransactionsContent(),
          ],
        ),
      _ => const SliverToBoxAdapter(),
    };
  }
}
