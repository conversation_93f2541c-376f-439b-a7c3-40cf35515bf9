import 'package:flutter/material.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_saving_space_ui/feature_saving_space_mobile_ui.dart';
import 'package:wio_feature_saving_space_ui/src/bottom_sheet/saving_space_interest/views/interest_layout.dart';
import 'package:wio_feature_saving_space_ui/src/common/extensions.dart';

class FixedSavingSpaceEndOfInterestBottomSheet extends StatelessWidget {
  final Money expectedAmount;
  final DateTime maturityDate;

  const FixedSavingSpaceEndOfInterestBottomSheet({
    required this.expectedAmount,
    required this.maturityDate,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = SavingSpaceLocalizations.of(context);

    return InterestLayout(
      title: l10n.fixedSavingSpaceEndofPeriodInterest,
      children: [
        InterestCard(
          title: l10n.fixedSavingSpaceEndofPeriod,
          amount: expectedAmount,
          description: l10n.fixedSavingSpaceEndofPeriodBottomSheetDescription(
            expectedAmount.currency.code,
            maturityDate.toMaturityFormat(),
          ),
        ),
      ],
    );
  }
}
