import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart' hide SavingSpace;
import 'package:wio_feature_saving_space_ui/src/l10n/saving_space_localizations.g.dart';

part 'widgets/day_picker.dart';

class SelectDayOfMonthBottomSheet extends StatelessWidget {
  final int? selectedDayOfMonth;

  const SelectDayOfMonthBottomSheet({
    this.selectedDayOfMonth,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return _SelectDayOfMonthContent(
      selectedDayOfMonth: selectedDayOfMonth,
    );
  }
}

class _SelectDayOfMonthContent extends StatelessWidget {
  final int? selectedDayOfMonth;

  const _SelectDayOfMonthContent({this.selectedDayOfMonth});

  @override
  Widget build(BuildContext context) {
    final l10n = SavingSpaceLocalizations.of(context);

    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(24, 8, 24, 32),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Label(
            model: LabelModel(
              text: l10n.recurringTransferSelectDayOfMonthBottomSheetTitle,
              textStyle: CompanyTextStylePointer.h3medium,
              color: CompanyColorPointer.primary3,
              labelRadius: 0,
            ),
          ),
          Space.fromSpacingVertical(Spacing.s2),
          Label(
            model: LabelModel(
              text: l10n.recurringTransferSelectDayOfMonthBottomSheetSubtitle,
              textStyle: CompanyTextStylePointer.b3,
              color: CompanyColorPointer.secondary4,
              labelRadius: 0,
            ),
          ),
          const Space.vertical(20),
          _DayPicker(
            selectedDay: selectedDayOfMonth,
            onChanged: (index) => Navigator.of(context).pop(index),
          ),
        ],
      ),
    );
  }
}
