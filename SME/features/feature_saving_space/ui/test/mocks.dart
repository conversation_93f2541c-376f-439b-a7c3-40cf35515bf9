import 'package:account_feature_api/account_feature_api.dart';
import 'package:common_feature_fx_api/domain/rate_interactor.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:data/auth_manager/auth_manager.dart';
import 'package:deeplink_manager/deep_link.dart';
import 'package:mocktail/mocktail.dart';
import 'package:sme_feature_transactions_api/feature_transactions_api.dart';
import 'package:tests/tests.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_behaviour_api/domain/behaviour_provider.dart';
import 'package:wio_feature_saving_space_api/saving_space_api.dart';
import 'package:wio_feature_saving_space_ui/src/bottom_sheet/close_saving_space/close_saving_space_analytics.dart';
import 'package:wio_feature_saving_space_ui/src/bottom_sheet/edit_saving_space_deadline/edit_saving_space_deadline_analytics.dart';
import 'package:wio_feature_saving_space_ui/src/bottom_sheet/edit_saving_space_goal/edit_saving_space_goal_analytics.dart';
import 'package:wio_feature_saving_space_ui/src/bottom_sheet/edit_saving_space_name/edit_saving_space_name_analytics.dart';
import 'package:wio_feature_saving_space_ui/src/bottom_sheet/remove_saving_space_deadline/remove_saving_space_deadline_analytics.dart';
import 'package:wio_feature_saving_space_ui/src/bottom_sheet/remove_saving_space_goal/remove_saving_space_goal_analytics.dart';
import 'package:wio_feature_saving_space_ui/src/bottom_sheet/remove_saving_space_recurring_transfer/remove_recurring_transfer_analytics.dart';
import 'package:wio_feature_saving_space_ui/src/common/helpers/data_helpers.dart';
import 'package:wio_feature_saving_space_ui/src/common/mu_flow_runner.dart';
import 'package:wio_feature_saving_space_ui/src/common/saving_space_feedback_provider.dart';
import 'package:wio_feature_saving_space_ui/src/navigation/bottomsheets/manage_saving_space_bottom_sheet_config.dart';
import 'package:wio_feature_saving_space_ui/src/navigation/bottomsheets/select_saving_type_bottomsheet_navigation_config.dart';
import 'package:wio_feature_saving_space_ui/src/navigation/configs/manage_saving_space_navigation_config.dart';
import 'package:wio_feature_saving_space_ui/src/screens/create_saving_space/flow/analytics/create_saving_space_analytics.dart';
import 'package:wio_feature_saving_space_ui/src/screens/create_saving_space/name_selection/analytics/name_selection_analytics.dart';
import 'package:wio_feature_saving_space_ui/src/screens/manage_saving_space/analytics/manage_saving_space_analytics.dart';
import 'package:wio_feature_saving_space_ui/src/screens/saving_space_details/saving_space_details_router.dart';
import 'package:wio_feature_saving_space_ui/src/screens/saving_spaces/analytics/saving_spaces_analytics.dart';
import 'package:wio_feature_saving_space_ui/src/screens/withdraw_money/withdraw_money_analytics.dart';
import 'package:wio_sme_error_handler_api/error_handler_api.dart';

class MockSavingSpaceInteractor extends Mock implements SavingSpaceInteractor {
  MockSavingSpaceInteractor() {
    when(refreshSavingSpaces).justCompleteAsync();
    when(() => refreshSavingSpace(any())).justCompleteAsync();
  }
}

class MockRateInteractor extends Mock implements RateInteractor {}

class MockFixedSavingSpaceInteractor extends Mock
    implements FixedSavingSpaceInteractor {}

class MockAccountInteractor extends Mock implements AccountInteractor {
  MockAccountInteractor() {
    when(refreshAccounts).justCompleteAsync();
  }
}

class MockTransactionsMediator extends Mock implements TransactionsMediator {}

class MockMuFlowRunner extends Mock implements MuFlowRunner {}

class MockFeatureToggleProvider extends Mock implements FeatureToggleProvider {}

class MockErrorHandlerTool extends Mock implements ErrorHandlerTool {}

class MockCloseSavingSpaceAnalytics extends Mock
    implements CloseSavingSpaceAnalytics {}

class MockEditSavingSpaceNameAnalytics extends Mock
    implements EditSavingSpaceNameAnalytics {}

class FakeToastMessageConfiguration extends Fake
    implements ToastMessageConfiguration {}

class MockEditSavingSpaceGoalAnalytics extends Mock
    implements EditSavingSpaceGoalAnalytics {}

class MockSavingSpaceFeedbackProvider extends Mock
    implements SavingSpaceFeedbackProvider {}

class MockRemoveSavingSpaceGoalAnalytics extends Mock
    implements RemoveSavingSpaceGoalAnalytics {}

class MockDateHelper extends Mock implements DateHelper {}

class MockEditSavingSpaceDeadlineAnalytics extends Mock
    implements EditSavingSpaceDeadlineAnalytics {}

class MockNameSelectionAnalytics extends Mock
    implements NameSelectionAnalytics {}

class MockSavingSpace extends Mock implements RegularSavingSpace {}

class MockFixedSavingSpace extends Mock implements FixedSavingSpace {}

class MockManageSavingSpaceParams extends Mock
    implements ManageSavingSpaceParams {}

class MockManageSavingSpaceAnalytics extends Mock
    implements ManageSavingSpaceAnalytics {}

class MockSavingSpaceFlow extends Mock implements SavingSpaceFlow {}

class MockSavingSpacesAnalytics extends Mock implements SavingSpacesAnalytics {}

class MockCreateSavingSpaceAnalytics extends Mock
    implements CreateSavingSpaceAnalytics {}

class FakeBottomSheetNavigationConfig extends Fake
    implements BottomSheetNavigationConfig<void> {
  @override
  String toString() => 'FakeBottomSheetNavigationConfig{}';
}

class FakeBottomSheetNavigationConfigClose extends Fake
    implements BottomSheetNavigationConfig<CloseSavingSpaceResult?> {
  @override
  String toString() => 'FakeBottomSheetNavigationConfigClose{}';
}

class FakeSavingTypeBottomSheetNavigationConfig extends Fake
    implements BottomSheetNavigationConfig<SavingTypeResult?> {
  @override
  String toString() => 'FakeSavingTypeBottomSheetNavigationConfig{}';
}

class FakeEditOptionBottomSheetNavigationConfig extends Fake
    implements BottomSheetNavigationConfig<EditOption?> {
  @override
  String toString() => 'FakeEditOptionBottomSheetNavigationConfig{}';
}

class FakeSelectDayOfRecurringTransferBottomSheetConfig extends Fake
    implements BottomSheetNavigationConfig<int?> {
  @override
  String toString() => 'FakeSelectDayOfRecurringTransferBottomSheetConfig{}';
}

class FakeSavingSpaceCreateRequest extends Fake
    implements RegularSavingSpaceCreateRequest {}

class FakeSavingTypeResult extends Fake implements SavingTypeResult {}

class MockRemoveSavingSpaceDeadlineAnalytics extends Mock
    implements RemoveSavingSpaceDeadlineAnalytics {}

class MockSavingSpaceDetailsRouter extends Mock
    implements SavingSpaceDetailsRouter {}

class MockWithdrawMoneyAnalytics extends Mock
    implements WithdrawMoneyAnalytics {}

class MockRemoveSavingSpaceRecurringTransferAnalytics extends Mock
    implements RemoveSavingSpaceRecurringTransferAnalytics {}

class FakeRecurringRuleUpdateRequest extends Fake
    implements RecurringRuleUpdateRequest {}

class MockAuthManager extends Mock implements IAuthManager {}

class MockDeepLinkRepository extends Mock implements DeepLinkRepository {}

class MockBehaviourProvider extends Mock implements BehaviourProvider {}

void registerFallbackValues() {
  registerFallbackValue(FakeToastMessageConfiguration());
  registerFallbackValue(Money.fromNumWithCurrency(200000, Currency.aed));
  registerFallbackValue(FakeSavingSpaceCreateRequest());
  registerFallbackValue(FakeRecurringRuleUpdateRequest());
  registerFallbackValue(FakeSelectDayOfRecurringTransferBottomSheetConfig());
}
