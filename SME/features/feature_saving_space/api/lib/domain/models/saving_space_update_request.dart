import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_saving_space_api/domain/models/saving_space_image.dart';

part 'saving_space_update_request.freezed.dart';

/// Represents a request to update different fields of a saving space.
@freezed
class SavingSpaceUpdateRequest with _$SavingSpaceUpdateRequest {
  const factory SavingSpaceUpdateRequest({
    String? name,
    SavingSpaceImage? image,
    FieldOperation<double>? goalAmount,
    FieldOperation<DateTime>? goalDate,
  }) = _SavingSpaceUpdateRequest;
}

@freezed
class FieldOperation<T> with _$FieldOperation<T> {
  /// Represents an intent to update the value of a field (amount, date, etc.)
  const factory FieldOperation.update({
    required T value,
  }) = _UpdateFieldOperation<T>;

  /// Represents an intent to unset the value of a field (amount, date, etc.)
  const factory FieldOperation.remove() = _RemoveFieldOperation<T>;
}
