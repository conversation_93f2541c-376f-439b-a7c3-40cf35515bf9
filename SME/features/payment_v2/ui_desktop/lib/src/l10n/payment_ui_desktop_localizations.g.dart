// GENERATED CODE
//
// After the template files .arb have been changed,
// generate this class by the command in the terminal:
// flutter pub run lokalise_flutter_sdk:gen-lok-l10n
//
// Please see https://pub.dev/packages/lokalise_flutter_sdk

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes
// ignore_for_file: depend_on_referenced_packages

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart';
import 'package:lokalise_flutter_sdk/lokalise_flutter_sdk.dart';
import 'intl/messages_all.dart';

class PaymentUILocalizations {
  PaymentUILocalizations._internal();

  static const LocalizationsDelegate<PaymentUILocalizations> delegate =
      _AppLocalizationDelegate();

  static const List<Locale> supportedLocales = [
    Locale.fromSubtags(languageCode: 'en'),
    Locale.fromSubtags(languageCode: 'ar')
  ];

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  static final Map<String, List<String>> _metadata = {
    'accSelectContinueBtn': [],
    'accountContactDrawerCallCentreText': [],
    'accountContactDrawerEmailText': [],
    'accountContactDrawerSubtitle': [],
    'accountContactDrawerTitle1': [],
    'accountContactDrawerWhatsAppText': [],
    'accountContactDrawerWhatsapp': [],
    'accountSelectionItemTitle': ['currency'],
    'accountsFetchingException': [],
    'addBeneCountrySelectionTitle': [],
    'addBeneCountrySelectionTitleHighlighted': [],
    'addBeneTitle': [],
    'addRecipientButtonLabel': [],
    'additionalbeneficiaryInfoTitle': [],
    'additionalbeneficiaryInfoTitleHighlighted': [],
    'allInternationalPayees': [],
    'allLocalPayyes': [],
    'allPayments': [],
    'amount': [],
    'amountValidationZeroAmountError': [],
    'beneAdditionalLimitPopTitle': [],
    'beneCooldownAuthorizeCta': [],
    'beneCooldownVerificationSuccessDescription': [],
    'beneCooldownVerificationSuccessTitle': [],
    'beneCreationCooldownPageCloseCta': [],
    'beneCreationCooldownPageSubtitle': [],
    'beneCreationCooldownPageTitle': [],
    'beneficiaryCreationCtaButton': [],
    'beneficiaryCreationException': [],
    'beneficiaryCreationLeftColumnTitle': [],
    'beneficiaryCreationLeftColumnTitleHighlighted': [],
    'beneficiaryCreationSuccess': [],
    'beneficiaryCreationUpdateValueException': [],
    'beneficiaryDeleteDialogTitle': ['name'],
    'beneficiaryDeleteSuccessMsg': [],
    'beneficiaryRemovingDialogNoButtonText': [],
    'beneficiaryRemovingDialogWarningSubtitle': [],
    'beneficiaryRemovingDialogYesButtonText': [],
    'beneficiarySelectionDrawerTitle': [],
    'beneficiarySelectionDrawerTitleHighlighted': [],
    'beneficiarySelectionErrorTitle': [],
    'beneficiarySelectionFetchBeneficiariesException': [],
    'beneficiarySelectionFetchException': [],
    'beneficiarySelectionNewPayee': [],
    'billerRemovingDialogWarningSubtitle': [],
    'billsTabTitle': [],
    'closePopupCancelButton': [],
    'closePopupConfirmButton': [],
    'closePopupSubtitle': [],
    'closePopupTitle': [],
    'confirmationBankLabel': [],
    'contactScreenEmail': [],
    'contactScreenPhoneCardTitle': [],
    'contactSupport': [],
    'continueButton': [],
    'continueConfirm': [],
    'continuePayment': [],
    'countriesFetchingException': [],
    'countrySelectionAllCountriesTitle': [],
    'countrySelectionErrorTitle': [],
    'countrySelectionMostFrequestTitle': [],
    'countrySelectionNoResultsFound': [],
    'countrySelectionText': [],
    'currencyDrawerTiletemporarilyUnavailable': [],
    'currencySelectionDrawerNoResultsFound': [],
    'customerSupport': [],
    'dailyLimit': [],
    'dashboardBeneficiaryTilePayBtn': [],
    'disabledAddBeneficiaryDisclaimer': [],
    'edit': [],
    'emptyBillsSubtitle': [],
    'errorScreenRequestId': ['id'],
    'everyInternationalTransfer': [],
    'everyLocalTransfer': [],
    'feeChargingTypeBeneficiaryDescription': [
      'customerFeeAmount',
      'sharedFeeAmount'
    ],
    'feeChargingTypeBeneficiaryTitle': [],
    'feeChargingTypeCustomerDescription': [
      'customerFeeAmount',
      'sharedFeeAmount'
    ],
    'feeChargingTypeCustomerTitle': [],
    'feeChargingTypeSharedDescription': ['customerFeeAmount'],
    'feeChargingTypeSharedTitle': [],
    'feeChargingTypeTitle': [],
    'feeHintFreeHighlighted': [],
    'feeHintTotal': ['amount'],
    'flowInitErorrTitle': [],
    'internalError': [],
    'internationalBeneficiariesFetchingException': [],
    'internationalPaymentsTabTitle': [],
    'invoiceNumberInputFieldPlaceholder': ['limit'],
    'localBeneficiariesFetchingException': [],
    'localPaymentMainLeftColumnTitle': [],
    'localPaymentsTabTitle': [],
    'multiUserIBAN': [],
    'multiUserUnknownWioer': [],
    'newBillPaymentBtn': [],
    'newBillPaymentDrawerTitle': [],
    'newBillProviderOtherListHeaderTitle': [],
    'newBillProviderTelecomListHeaderTitle': [],
    'newInternationalPaymentHowMuch': [],
    'newInternationalPaymentPurpose': [],
    'newInternationalPaymentSecondTitle': [],
    'newInternationalPaymentSelectPurpose': [],
    'newIntlPaymentBtn': [],
    'newIntlPaymentHeaderTitle': [],
    'newLocalPayeeCtaText': [],
    'newLocalPaymentBtn': [],
    'newLocalPaymentHeaderTitle': [],
    'newLocalPaymentSend': [],
    'newPaymentSelectSubpurpose': [],
    'newPaymentSubpurpose': [],
    'newUtilityBillPaymentHeaderTitle': [],
    'newUtilityBillPaymentHighlightedText': [],
    'newUtilityBillPaymentLeftColumnTitle': [],
    'noAccountsForSelectedCurrencyException': ['currency'],
    'noCurrencyFetchedException': [],
    'noPayeeYet': [],
    'noPayeeYetSubText': [],
    'noTransferYet': [],
    'onboardingEmployeeMabrook': [],
    'optionPostpaidText': [],
    'optionPrepaidText': [],
    'payOrTopUpBtn': [],
    'payeeAddNewPayment': [],
    'payeeDeleteToast': ['payeeName'],
    'payeeUnavailableDescription': [],
    'payeeUnavailableTitle': [],
    'paymentConfirmationAccountNumberLabel': [],
    'paymentConfirmationAmountSentLabel': [],
    'paymentConfirmationChannelLabel': [],
    'paymentConfirmationCtaLabel': [],
    'paymentConfirmationCtaText': [],
    'paymentConfirmationExchangeRateDurationLabel': ['duration'],
    'paymentConfirmationExchangeRateLabel': [],
    'paymentConfirmationFaqSubtitle1': [],
    'paymentConfirmationFaqSubtitle2': [],
    'paymentConfirmationFaqSubtitle3': [],
    'paymentConfirmationFaqTitle1': [],
    'paymentConfirmationFaqTitle2': [],
    'paymentConfirmationFaqTitle3': [],
    'paymentConfirmationFeesLabel': [],
    'paymentConfirmationIntlTransferFetchingFail': [],
    'paymentConfirmationLeftColumnTitle': [],
    'paymentConfirmationLeftColumnTitleHighlighted': [],
    'paymentConfirmationNotesToPayeeLabel': [],
    'paymentConfirmationPageBankLabel': [],
    'paymentConfirmationQuoteUpdateError': [],
    'paymentConfirmationQuoteUpdated': [],
    'paymentConfirmationSubtitle': [],
    'paymentConfirmationTACHighlightedLink': [],
    'paymentConfirmationTACLink': [],
    'paymentConfirmationTitle': [],
    'paymentConfirmationTotalAmountLabel': [],
    'paymentConfirmationTransferException': [],
    'paymentFeesWarningBannerDescription': [],
    'paymentPopupSubtitle1': [],
    'paymentPopupSubtitle2': [],
    'paymentPopupTitle2': [],
    'paymentSuccessTotal': [],
    'paymentSuccessWarningBannerDescription': [],
    'paymentsHomePageTitle': [],
    'paymentsInternationalTextInputExample': ['example'],
    'paymentsInternationalTextInputMaxLength': ['maxLength'],
    'paymentsInternationalTextInputMinLength': ['minLength'],
    'paymentsInternationalTextInputNotValidFormat': [],
    'paymentsInternationalTextInputOptional': [],
    'paymentsInternationalTextInputRequired': [],
    'paymentsTo': [],
    'paymentsToAName': ['name'],
    'pensionInvoiceInputFieldDescription': [],
    'pensionInvoiceNumberTitle': [],
    'purposeSelectionLeftColumnTitle': [],
    'purposeSelectionLeftColumnTitleHighlighted': [],
    'purposeSelectionNotesTitle': [],
    'purposeSelectionOptionalNotesTitle': [],
    'railsSelectionAmountToSend': [],
    'railsSelectionContinueBtn': [],
    'railsSelectionCurrencyFetchException': [],
    'railsSelectionEditAmount': [],
    'railsSelectionLeftColumn': ['country'],
    'railsSelectionLooksGood': [],
    'railsSelectionNoCurrencyFetchedException': ['country'],
    'railsSelectionSeeRates': [],
    'railsSelectionYourRateSubtitle': [],
    'requestHigherTransferDailyLimit': ['value1'],
    'saveChanges': [],
    'searchDots': [],
    'selectAccLeftColumnTitle': [],
    'selectAccLeftColumnTitleHighlighted': [],
    'selectCountryLeftColumnTitle': [],
    'selectCountryLeftColumnTitleHighlighted': [],
    'selectCurrency': [],
    'selectCurrencyBeneficiaryCreationTitle': ['name'],
    'smthWentWrong': [],
    'successPaymentCtaText': [],
    'successPaymentFormTitle': [],
    'successPaymentLeftColumnTitle': [],
    'successPaymentSubtitle': [],
    'successPaymentSubtitleForApproval': [],
    'successPaymentTransferRef': [],
    'taxInvoiceInputFieldDescription': [],
    'taxInvoiceNumberTitle': [],
    'telecomPaymentFirstScreenCTA': [],
    'transactionLimitSettingLeftColumnTitle': [],
    'transferDailyLimitRemaining': [],
    'transferDailyLimitReset': [],
    'transferDailyLimitWidgetSubtitle': [],
    'transferDailyLimitWidgetTitle': ['value1'],
    'transferDetailsFaq': [],
    'transferFeeLabel': [],
    'transferLimitAvailableBalance': [],
    'transferLimitDashboardSubtitle': ['value1'],
    'transferLimitDashboardTitle': [],
    'transferLimitExceeded': [],
    'transferLimitRemaining': ['value1', 'value2'],
    'transferLimitSectionTitle': [],
    'transferLimitWidgetSubtitle': ['value1'],
    'transferOptionEstimatedTimeLabel': [],
    'transferOptionExchangeRateLabel': [],
    'transferOptionSelectContinueBtn': [],
    'transferOptionYouWillPayLabel': [],
    'transferOptionsBalanceLabel': ['amount'],
    'transferOptionsEmptyTitle': ['country'],
    'transferOptionsError': [],
    'transferOptionsErrorTitle': [],
    'transferPurposesFetchingException': [],
    'tryAgain': [],
    'utilityBeneficiaryDeletionErrorText': [],
    'utilityBeneficiarySaveErrorText': [],
    'utilityBillPaymentAccountDetailsInfoText': [],
    'utilityBillPaymentAccountDetailsLeftColumnTitle': [],
    'utilityBillPaymentAccountDetailsValidationErrorText': [],
    'utilityBillPaymentAccountNumberTextFieldText': [],
    'utilityBillPaymentErrorButtonTitle': [],
    'utilityBillPaymentHomeErrorTitle': [],
    'utilityBillPaymentNolTagText': [],
    'utilityBillPaymentOthersOptionSubTitle': [],
    'utilityBillPaymentOthersOptionTitle': [],
    'utilityBillPaymentTabTitle': [],
    'utilityBillPaymentTelecomOptionSubTitle': [],
    'utilityBillPaymentTelecomOptionTitle': [],
    'utilityPaymentAccountDetailsHeaderTitle': ['provider'],
    'utilityPaymentAccountDetailsHighlightedText': [],
    'utilityPaymentAccountDetailsPlaceHolderText': [],
    'utilityPaymentAccountListHeaderTitle': [],
    'utilityPaymentAmountValidationErrorText': [],
    'utilityPaymentBalanceText': ['balance'],
    'utilityPaymentBillerDeletionSuccessText': [],
    'utilityPaymentConfirmationAccNumberText': [],
    'utilityPaymentConfirmationAmountText': [],
    'utilityPaymentConfirmationCtaText': [],
    'utilityPaymentConfirmationHighlightedText': [],
    'utilityPaymentConfirmationLeftColumnText': [],
    'utilityPaymentConfirmationLeftColumnTitleText': [],
    'utilityPaymentConfirmationNolTitle': [],
    'utilityPaymentConfirmationPersonalDetails': [],
    'utilityPaymentConfirmationPhonetNumberText': [],
    'utilityPaymentConfirmationPostPaidHeaderTitle': [],
    'utilityPaymentConfirmationSalikText': [],
    'utilityPaymentConfirmationScreenTitle': [],
    'utilityPaymentConfirmationTypeOfPlanText': [],
    'utilityPaymentDetailsListSubHeaderText': [],
    'utilityPaymentElifeText': [],
    'utilityPaymentEmptyCtaText': [],
    'utilityPaymentEmptySubTitle': [],
    'utilityPaymentEmptyTitleText': [],
    'utilityPaymentErrorSubTitle': [],
    'utilityPaymentErrorTitle': [],
    'utilityPaymentHeaderTitleText': [],
    'utilityPaymentHomeErrorSubtitle': [],
    'utilityPaymentListSubHeaderText': [],
    'utilityPaymentLocalAmountValidationErrorText': [],
    'utilityPaymentNickNameTextFieldPlaceHolder': [],
    'utilityPaymentNolTextFeildPlaceholderText': [],
    'utilityPaymentOtherHeaderTitleText': ['provider'],
    'utilityPaymentPinText': [],
    'utilityPaymentPrepaidHeaderTitle': [],
    'utilityPaymentProvider': [],
    'utilityPaymentReTopupTitleText': ['provider'],
    'utilityPaymentSalikTextFeildPlaceholderText': [],
    'utilityPaymentSuccessAmmountInfo': [],
    'utilityPaymentSuccessCtaText': [],
    'utilityPaymentSuccessProviderTitle': [],
    'utilityPaymentSuccessRefrenceNumberTitleText': ['referenceNumber'],
    'utilityPaymentSuccessTransferReferenceTitle': [],
    'utilityPaymentTopUpCtaText': [],
    'utilityPaymentTopUpPostpaidHighlightText': [],
    'utilityPaymentTopUpScreenPostpaidLeftColumnText': [],
    'utilityPaymentTopUpScreenPrepaidHighlightedText': [],
    'utilityPaymentTopUpScreenPrepaidLeftColumnText': [],
    'utilityPaymentTopUpTitle': [],
    'utilityPaymentTransactionRefNumberText': ['refNumber'],
    'utilityPaymentUnableToVerifyAccountText': [],
    'viewAll': [],
    'wiseAccountCreated': [],
    'wiseAccountCreationError': [],
    'wiseCountriesOfBirthFetchingException': [],
    'wiseCtaText': [],
    'wiseDialogDropdownTitle': [],
    'wiseLoginDialogSubtitleBoldText': [],
    'wiseLoginDialogSubtitleText': [],
    'wiseLoginTitle': [],
    'wisePopUpSubtitleBoldText': [],
    'wisePopUpSubtitleNormalText': [],
    'wiseStatusFetchingException': []
  };

  static Future<PaymentUILocalizations> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    Lokalise.instance.metadata = _metadata;

    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = PaymentUILocalizations._internal();
      return instance;
    });
  }

  static PaymentUILocalizations of(BuildContext context) {
    final instance = Localizations.of<PaymentUILocalizations>(
        context, PaymentUILocalizations);
    assert(instance != null,
        'No instance of PaymentUILocalizations present in the widget tree. Did you add PaymentUILocalizations.delegate in localizationsDelegates?');
    return instance!;
  }

  /// `Continue`
  String get accSelectContinueBtn {
    return Intl.message(
      'Continue',
      name: 'accSelectContinueBtn',
      desc: '',
      args: [],
    );
  }

  /// `Call Wio care on +971 600 500 946 and they’ll help you.`
  String get accountContactDrawerCallCentreText {
    return Intl.message(
      'Call Wio care on +971 600 500 946 and they’ll help you.',
      name: 'accountContactDrawerCallCentreText',
      desc: '',
      args: [],
    );
  }

  /// `Email your issue or <NAME_EMAIL>, and we’ll assign it to the right team.`
  String get accountContactDrawerEmailText {
    return Intl.message(
      'Email your issue or <NAME_EMAIL>, and we’ll assign it to the right team.',
      name: 'accountContactDrawerEmailText',
      desc: '',
      args: [],
    );
  }

  /// `Got any questions? We’re available around \nthe clock!`
  String get accountContactDrawerSubtitle {
    return Intl.message(
      'Got any questions? We’re available around \nthe clock!',
      name: 'accountContactDrawerSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `We're here `
  String get accountContactDrawerTitle1 {
    return Intl.message(
      'We\'re here ',
      name: 'accountContactDrawerTitle1',
      desc: '',
      args: [],
    );
  }

  /// `Click here to chat with Wio care for quick answers.`
  String get accountContactDrawerWhatsAppText {
    return Intl.message(
      'Click here to chat with Wio care for quick answers.',
      name: 'accountContactDrawerWhatsAppText',
      desc: '',
      args: [],
    );
  }

  /// `WhatsApp`
  String get accountContactDrawerWhatsapp {
    return Intl.message(
      'WhatsApp',
      name: 'accountContactDrawerWhatsapp',
      desc: '',
      args: [],
    );
  }

  /// `{currency} account`
  String accountSelectionItemTitle(String currency) {
    return Intl.message(
      '$currency account',
      name: 'accountSelectionItemTitle',
      desc: '',
      args: [currency],
    );
  }

  /// `Accounts fetching exception`
  String get accountsFetchingException {
    return Intl.message(
      'Accounts fetching exception',
      name: 'accountsFetchingException',
      desc: '',
      args: [],
    );
  }

  /// `Select the beneficiary Country`
  String get addBeneCountrySelectionTitle {
    return Intl.message(
      'Select the beneficiary Country',
      name: 'addBeneCountrySelectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Country`
  String get addBeneCountrySelectionTitleHighlighted {
    return Intl.message(
      'Country',
      name: 'addBeneCountrySelectionTitleHighlighted',
      desc: '',
      args: [],
    );
  }

  /// `New payee`
  String get addBeneTitle {
    return Intl.message(
      'New payee',
      name: 'addBeneTitle',
      desc: '',
      args: [],
    );
  }

  /// `Add recipient`
  String get addRecipientButtonLabel {
    return Intl.message(
      'Add recipient',
      name: 'addRecipientButtonLabel',
      desc: '',
      args: [],
    );
  }

  /// `Please add more information`
  String get additionalbeneficiaryInfoTitle {
    return Intl.message(
      'Please add more information',
      name: 'additionalbeneficiaryInfoTitle',
      desc: '',
      args: [],
    );
  }

  /// `information`
  String get additionalbeneficiaryInfoTitleHighlighted {
    return Intl.message(
      'information',
      name: 'additionalbeneficiaryInfoTitleHighlighted',
      desc: '',
      args: [],
    );
  }

  /// `ALL INTERNATIONAL PAYEES`
  String get allInternationalPayees {
    return Intl.message(
      'ALL INTERNATIONAL PAYEES',
      name: 'allInternationalPayees',
      desc: '',
      args: [],
    );
  }

  /// `ALL LOCAL PAYEES`
  String get allLocalPayyes {
    return Intl.message(
      'ALL LOCAL PAYEES',
      name: 'allLocalPayyes',
      desc: '',
      args: [],
    );
  }

  /// `ALL PAYMENTS`
  String get allPayments {
    return Intl.message(
      'ALL PAYMENTS',
      name: 'allPayments',
      desc: '',
      args: [],
    );
  }

  /// `Amount`
  String get amount {
    return Intl.message(
      'Amount',
      name: 'amount',
      desc: '',
      args: [],
    );
  }

  /// `The transfer amount must be greater than zero. Please enter a valid amount.`
  String get amountValidationZeroAmountError {
    return Intl.message(
      'The transfer amount must be greater than zero. Please enter a valid amount.',
      name: 'amountValidationZeroAmountError',
      desc: '',
      args: [],
    );
  }

  /// `Beneficiary addition limit`
  String get beneAdditionalLimitPopTitle {
    return Intl.message(
      'Beneficiary addition limit',
      name: 'beneAdditionalLimitPopTitle',
      desc: '',
      args: [],
    );
  }

  /// `Skip the wait`
  String get beneCooldownAuthorizeCta {
    return Intl.message(
      'Skip the wait',
      name: 'beneCooldownAuthorizeCta',
      desc: '',
      args: [],
    );
  }

  /// `Verification complete. The payee is now available for payments.`
  String get beneCooldownVerificationSuccessDescription {
    return Intl.message(
      'Verification complete. The payee is now available for payments.',
      name: 'beneCooldownVerificationSuccessDescription',
      desc: '',
      args: [],
    );
  }

  /// `Verification complete`
  String get beneCooldownVerificationSuccessTitle {
    return Intl.message(
      'Verification complete',
      name: 'beneCooldownVerificationSuccessTitle',
      desc: '',
      args: [],
    );
  }

  /// `CLOSE`
  String get beneCreationCooldownPageCloseCta {
    return Intl.message(
      'CLOSE',
      name: 'beneCreationCooldownPageCloseCta',
      desc: '',
      args: [],
    );
  }

  /// `New payee saved`
  String get beneCreationCooldownPageSubtitle {
    return Intl.message(
      'New payee saved',
      name: 'beneCreationCooldownPageSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `DONE!`
  String get beneCreationCooldownPageTitle {
    return Intl.message(
      'DONE!',
      name: 'beneCreationCooldownPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Save and continue`
  String get beneficiaryCreationCtaButton {
    return Intl.message(
      'Save and continue',
      name: 'beneficiaryCreationCtaButton',
      desc: '',
      args: [],
    );
  }

  /// `Beneficiary creation exception`
  String get beneficiaryCreationException {
    return Intl.message(
      'Beneficiary creation exception',
      name: 'beneficiaryCreationException',
      desc: '',
      args: [],
    );
  }

  /// `What are the new payee details?`
  String get beneficiaryCreationLeftColumnTitle {
    return Intl.message(
      'What are the new payee details?',
      name: 'beneficiaryCreationLeftColumnTitle',
      desc: '',
      args: [],
    );
  }

  /// `new payee`
  String get beneficiaryCreationLeftColumnTitleHighlighted {
    return Intl.message(
      'new payee',
      name: 'beneficiaryCreationLeftColumnTitleHighlighted',
      desc: '',
      args: [],
    );
  }

  /// `Beneficiary successfully created`
  String get beneficiaryCreationSuccess {
    return Intl.message(
      'Beneficiary successfully created',
      name: 'beneficiaryCreationSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Update value exception`
  String get beneficiaryCreationUpdateValueException {
    return Intl.message(
      'Update value exception',
      name: 'beneficiaryCreationUpdateValueException',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete {name}?`
  String beneficiaryDeleteDialogTitle(String name) {
    return Intl.message(
      'Are you sure you want to delete $name?',
      name: 'beneficiaryDeleteDialogTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Beneficiary successfully deleted`
  String get beneficiaryDeleteSuccessMsg {
    return Intl.message(
      'Beneficiary successfully deleted',
      name: 'beneficiaryDeleteSuccessMsg',
      desc: '',
      args: [],
    );
  }

  /// `No, cancel`
  String get beneficiaryRemovingDialogNoButtonText {
    return Intl.message(
      'No, cancel',
      name: 'beneficiaryRemovingDialogNoButtonText',
      desc: '',
      args: [],
    );
  }

  /// `The beneficiary will be permanently removed from the beneficiary list.`
  String get beneficiaryRemovingDialogWarningSubtitle {
    return Intl.message(
      'The beneficiary will be permanently removed from the beneficiary list.',
      name: 'beneficiaryRemovingDialogWarningSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Yes, delete`
  String get beneficiaryRemovingDialogYesButtonText {
    return Intl.message(
      'Yes, delete',
      name: 'beneficiaryRemovingDialogYesButtonText',
      desc: '',
      args: [],
    );
  }

  /// `Who would you like to pay?`
  String get beneficiarySelectionDrawerTitle {
    return Intl.message(
      'Who would you like to pay?',
      name: 'beneficiarySelectionDrawerTitle',
      desc: '',
      args: [],
    );
  }

  /// `Who`
  String get beneficiarySelectionDrawerTitleHighlighted {
    return Intl.message(
      'Who',
      name: 'beneficiarySelectionDrawerTitleHighlighted',
      desc: '',
      args: [],
    );
  }

  /// `OOPS!`
  String get beneficiarySelectionErrorTitle {
    return Intl.message(
      'OOPS!',
      name: 'beneficiarySelectionErrorTitle',
      desc: '',
      args: [],
    );
  }

  /// `Beneficiaries fetching exception`
  String get beneficiarySelectionFetchBeneficiariesException {
    return Intl.message(
      'Beneficiaries fetching exception',
      name: 'beneficiarySelectionFetchBeneficiariesException',
      desc: '',
      args: [],
    );
  }

  /// `Beneficiaries fetch exception`
  String get beneficiarySelectionFetchException {
    return Intl.message(
      'Beneficiaries fetch exception',
      name: 'beneficiarySelectionFetchException',
      desc: '',
      args: [],
    );
  }

  /// `New payee`
  String get beneficiarySelectionNewPayee {
    return Intl.message(
      'New payee',
      name: 'beneficiarySelectionNewPayee',
      desc: '',
      args: [],
    );
  }

  /// `The biller will be permanently removed from the billers list.`
  String get billerRemovingDialogWarningSubtitle {
    return Intl.message(
      'The biller will be permanently removed from the billers list.',
      name: 'billerRemovingDialogWarningSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Bills`
  String get billsTabTitle {
    return Intl.message(
      'Bills',
      name: 'billsTabTitle',
      desc: '',
      args: [],
    );
  }

  /// `No, stay`
  String get closePopupCancelButton {
    return Intl.message(
      'No, stay',
      name: 'closePopupCancelButton',
      desc: '',
      args: [],
    );
  }

  /// `Yes, leave`
  String get closePopupConfirmButton {
    return Intl.message(
      'Yes, leave',
      name: 'closePopupConfirmButton',
      desc: '',
      args: [],
    );
  }

  /// `By leaving, you’ll cancel the payment and erase everything on this page.`
  String get closePopupSubtitle {
    return Intl.message(
      'By leaving, you’ll cancel the payment and erase everything on this page.',
      name: 'closePopupSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to leave?`
  String get closePopupTitle {
    return Intl.message(
      'Are you sure you want to leave?',
      name: 'closePopupTitle',
      desc: '',
      args: [],
    );
  }

  /// `Bank`
  String get confirmationBankLabel {
    return Intl.message(
      'Bank',
      name: 'confirmationBankLabel',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get contactScreenEmail {
    return Intl.message(
      'Email',
      name: 'contactScreenEmail',
      desc: '',
      args: [],
    );
  }

  /// `Phone`
  String get contactScreenPhoneCardTitle {
    return Intl.message(
      'Phone',
      name: 'contactScreenPhoneCardTitle',
      desc: '',
      args: [],
    );
  }

  /// `Contact support`
  String get contactSupport {
    return Intl.message(
      'Contact support',
      name: 'contactSupport',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get continueButton {
    return Intl.message(
      'Continue',
      name: 'continueButton',
      desc: '',
      args: [],
    );
  }

  /// `Do you want to continue?`
  String get continueConfirm {
    return Intl.message(
      'Do you want to continue?',
      name: 'continueConfirm',
      desc: '',
      args: [],
    );
  }

  /// `Continue with payment`
  String get continuePayment {
    return Intl.message(
      'Continue with payment',
      name: 'continuePayment',
      desc: '',
      args: [],
    );
  }

  /// `Countries fetching exception`
  String get countriesFetchingException {
    return Intl.message(
      'Countries fetching exception',
      name: 'countriesFetchingException',
      desc: '',
      args: [],
    );
  }

  /// `All countries`
  String get countrySelectionAllCountriesTitle {
    return Intl.message(
      'All countries',
      name: 'countrySelectionAllCountriesTitle',
      desc: '',
      args: [],
    );
  }

  /// `OOPS!`
  String get countrySelectionErrorTitle {
    return Intl.message(
      'OOPS!',
      name: 'countrySelectionErrorTitle',
      desc: '',
      args: [],
    );
  }

  /// `Most frequent`
  String get countrySelectionMostFrequestTitle {
    return Intl.message(
      'Most frequent',
      name: 'countrySelectionMostFrequestTitle',
      desc: '',
      args: [],
    );
  }

  /// `No results found`
  String get countrySelectionNoResultsFound {
    return Intl.message(
      'No results found',
      name: 'countrySelectionNoResultsFound',
      desc: '',
      args: [],
    );
  }

  /// `Select your country`
  String get countrySelectionText {
    return Intl.message(
      'Select your country',
      name: 'countrySelectionText',
      desc: '',
      args: [],
    );
  }

  /// `Temporarily unavailable`
  String get currencyDrawerTiletemporarilyUnavailable {
    return Intl.message(
      'Temporarily unavailable',
      name: 'currencyDrawerTiletemporarilyUnavailable',
      desc: '',
      args: [],
    );
  }

  /// `No results found`
  String get currencySelectionDrawerNoResultsFound {
    return Intl.message(
      'No results found',
      name: 'currencySelectionDrawerNoResultsFound',
      desc: '',
      args: [],
    );
  }

  /// `customer support`
  String get customerSupport {
    return Intl.message(
      'customer support',
      name: 'customerSupport',
      desc: '',
      args: [],
    );
  }

  /// `daily limit`
  String get dailyLimit {
    return Intl.message(
      'daily limit',
      name: 'dailyLimit',
      desc: '',
      args: [],
    );
  }

  /// `Pay`
  String get dashboardBeneficiaryTilePayBtn {
    return Intl.message(
      'Pay',
      name: 'dashboardBeneficiaryTilePayBtn',
      desc: '',
      args: [],
    );
  }

  /// `To add New Payee please use Wio Business Mobile App`
  String get disabledAddBeneficiaryDisclaimer {
    return Intl.message(
      'To add New Payee please use Wio Business Mobile App',
      name: 'disabledAddBeneficiaryDisclaimer',
      desc: '',
      args: [],
    );
  }

  /// `Edit`
  String get edit {
    return Intl.message(
      'Edit',
      name: 'edit',
      desc: '',
      args: [],
    );
  }

  /// `Wio simplifies the way you pay your bills or top-up your account.`
  String get emptyBillsSubtitle {
    return Intl.message(
      'Wio simplifies the way you pay your bills or top-up your account.',
      name: 'emptyBillsSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Request Id: {id}`
  String errorScreenRequestId(String id) {
    return Intl.message(
      'Request Id: $id',
      name: 'errorScreenRequestId',
      desc: '',
      args: [id],
    );
  }

  /// `Every international transfer you make will find a home here.`
  String get everyInternationalTransfer {
    return Intl.message(
      'Every international transfer you make will find a home here.',
      name: 'everyInternationalTransfer',
      desc: '',
      args: [],
    );
  }

  /// `Every local transfer you make will find a home here.`
  String get everyLocalTransfer {
    return Intl.message(
      'Every local transfer you make will find a home here.',
      name: 'everyLocalTransfer',
      desc: '',
      args: [],
    );
  }

  /// `- Your payee will pay for Wio’s transfer fees ({customerFeeAmount}).\n- Your payee will pay for the correspondent bank’s fees ({sharedFeeAmount}).\n- Your payee will receive less than the total amount you send.`
  String feeChargingTypeBeneficiaryDescription(
      String customerFeeAmount, String sharedFeeAmount) {
    return Intl.message(
      '- Your payee will pay for Wio’s transfer fees ($customerFeeAmount).\n- Your payee will pay for the correspondent bank’s fees ($sharedFeeAmount).\n- Your payee will receive less than the total amount you send.',
      name: 'feeChargingTypeBeneficiaryDescription',
      desc: '',
      args: [customerFeeAmount, sharedFeeAmount],
    );
  }

  /// `I’d like the payee to pay all fees`
  String get feeChargingTypeBeneficiaryTitle {
    return Intl.message(
      'I’d like the payee to pay all fees',
      name: 'feeChargingTypeBeneficiaryTitle',
      desc: '',
      args: [],
    );
  }

  /// `- You’ll pay for Wio’s transfer fees ({customerFeeAmount}).\n- You’ll pay for the correspondent bank’s fees ({sharedFeeAmount}).\n- Your payee will receive the exact amount you’re transferring.`
  String feeChargingTypeCustomerDescription(
      String customerFeeAmount, String sharedFeeAmount) {
    return Intl.message(
      '- You’ll pay for Wio’s transfer fees ($customerFeeAmount).\n- You’ll pay for the correspondent bank’s fees ($sharedFeeAmount).\n- Your payee will receive the exact amount you’re transferring.',
      name: 'feeChargingTypeCustomerDescription',
      desc: '',
      args: [customerFeeAmount, sharedFeeAmount],
    );
  }

  /// `I’d like to pay all the fees`
  String get feeChargingTypeCustomerTitle {
    return Intl.message(
      'I’d like to pay all the fees',
      name: 'feeChargingTypeCustomerTitle',
      desc: '',
      args: [],
    );
  }

  /// `- You’ll pay for Wio’s transfer fees ({customerFeeAmount}).\n- Your payee will pay for the Wio correspondent bank fees.\n- Your payee might be charged additional fees from other receiver or intermediary banks.\n- Your payee will receive less than the total amount you send.`
  String feeChargingTypeSharedDescription(String customerFeeAmount) {
    return Intl.message(
      '- You’ll pay for Wio’s transfer fees ($customerFeeAmount).\n- Your payee will pay for the Wio correspondent bank fees.\n- Your payee might be charged additional fees from other receiver or intermediary banks.\n- Your payee will receive less than the total amount you send.',
      name: 'feeChargingTypeSharedDescription',
      desc: '',
      args: [customerFeeAmount],
    );
  }

  /// `I’d like to pay my share of the fees`
  String get feeChargingTypeSharedTitle {
    return Intl.message(
      'I’d like to pay my share of the fees',
      name: 'feeChargingTypeSharedTitle',
      desc: '',
      args: [],
    );
  }

  /// `Fees payment`
  String get feeChargingTypeTitle {
    return Intl.message(
      'Fees payment',
      name: 'feeChargingTypeTitle',
      desc: '',
      args: [],
    );
  }

  /// `Free`
  String get feeHintFreeHighlighted {
    return Intl.message(
      'Free',
      name: 'feeHintFreeHighlighted',
      desc: '',
      args: [],
    );
  }

  /// `Total fee: {amount}`
  String feeHintTotal(String amount) {
    return Intl.message(
      'Total fee: $amount',
      name: 'feeHintTotal',
      desc: '',
      args: [amount],
    );
  }

  /// `UH OH!`
  String get flowInitErorrTitle {
    return Intl.message(
      'UH OH!',
      name: 'flowInitErorrTitle',
      desc: '',
      args: [],
    );
  }

  /// `Internal error`
  String get internalError {
    return Intl.message(
      'Internal error',
      name: 'internalError',
      desc: '',
      args: [],
    );
  }

  /// `International beneficiaries fetching exception`
  String get internationalBeneficiariesFetchingException {
    return Intl.message(
      'International beneficiaries fetching exception',
      name: 'internationalBeneficiariesFetchingException',
      desc: '',
      args: [],
    );
  }

  /// `International`
  String get internationalPaymentsTabTitle {
    return Intl.message(
      'International',
      name: 'internationalPaymentsTabTitle',
      desc: '',
      args: [],
    );
  }

  /// `This number should be {limit} digits`
  String invoiceNumberInputFieldPlaceholder(String limit) {
    return Intl.message(
      'This number should be $limit digits',
      name: 'invoiceNumberInputFieldPlaceholder',
      desc: '',
      args: [limit],
    );
  }

  /// `Local beneficiaries fetching exception`
  String get localBeneficiariesFetchingException {
    return Intl.message(
      'Local beneficiaries fetching exception',
      name: 'localBeneficiariesFetchingException',
      desc: '',
      args: [],
    );
  }

  /// `How much would you like to transfer?`
  String get localPaymentMainLeftColumnTitle {
    return Intl.message(
      'How much would you like to transfer?',
      name: 'localPaymentMainLeftColumnTitle',
      desc: '',
      args: [],
    );
  }

  /// `Local`
  String get localPaymentsTabTitle {
    return Intl.message(
      'Local',
      name: 'localPaymentsTabTitle',
      desc: '',
      args: [],
    );
  }

  /// `IBAN`
  String get multiUserIBAN {
    return Intl.message(
      'IBAN',
      name: 'multiUserIBAN',
      desc: '',
      args: [],
    );
  }

  /// `Unknown Wioer`
  String get multiUserUnknownWioer {
    return Intl.message(
      'Unknown Wioer',
      name: 'multiUserUnknownWioer',
      desc: '',
      args: [],
    );
  }

  /// `New bill payment`
  String get newBillPaymentBtn {
    return Intl.message(
      'New bill payment',
      name: 'newBillPaymentBtn',
      desc: '',
      args: [],
    );
  }

  /// `Select the type of payment you’d like to make`
  String get newBillPaymentDrawerTitle {
    return Intl.message(
      'Select the type of payment you’d like to make',
      name: 'newBillPaymentDrawerTitle',
      desc: '',
      args: [],
    );
  }

  /// `Select other provider`
  String get newBillProviderOtherListHeaderTitle {
    return Intl.message(
      'Select other provider',
      name: 'newBillProviderOtherListHeaderTitle',
      desc: '',
      args: [],
    );
  }

  /// `Select telecom provider`
  String get newBillProviderTelecomListHeaderTitle {
    return Intl.message(
      'Select telecom provider',
      name: 'newBillProviderTelecomListHeaderTitle',
      desc: '',
      args: [],
    );
  }

  /// `How much `
  String get newInternationalPaymentHowMuch {
    return Intl.message(
      'How much ',
      name: 'newInternationalPaymentHowMuch',
      desc: '',
      args: [],
    );
  }

  /// `Payment purpose`
  String get newInternationalPaymentPurpose {
    return Intl.message(
      'Payment purpose',
      name: 'newInternationalPaymentPurpose',
      desc: '',
      args: [],
    );
  }

  /// `would you like to transfer?`
  String get newInternationalPaymentSecondTitle {
    return Intl.message(
      'would you like to transfer?',
      name: 'newInternationalPaymentSecondTitle',
      desc: '',
      args: [],
    );
  }

  /// `Select payment purpose`
  String get newInternationalPaymentSelectPurpose {
    return Intl.message(
      'Select payment purpose',
      name: 'newInternationalPaymentSelectPurpose',
      desc: '',
      args: [],
    );
  }

  /// `New international payment`
  String get newIntlPaymentBtn {
    return Intl.message(
      'New international payment',
      name: 'newIntlPaymentBtn',
      desc: '',
      args: [],
    );
  }

  /// `New international payment`
  String get newIntlPaymentHeaderTitle {
    return Intl.message(
      'New international payment',
      name: 'newIntlPaymentHeaderTitle',
      desc: '',
      args: [],
    );
  }

  /// `Save payee`
  String get newLocalPayeeCtaText {
    return Intl.message(
      'Save payee',
      name: 'newLocalPayeeCtaText',
      desc: '',
      args: [],
    );
  }

  /// `New Local Payment`
  String get newLocalPaymentBtn {
    return Intl.message(
      'New Local Payment',
      name: 'newLocalPaymentBtn',
      desc: '',
      args: [],
    );
  }

  /// `New Local Payment`
  String get newLocalPaymentHeaderTitle {
    return Intl.message(
      'New Local Payment',
      name: 'newLocalPaymentHeaderTitle',
      desc: '',
      args: [],
    );
  }

  /// `Send money`
  String get newLocalPaymentSend {
    return Intl.message(
      'Send money',
      name: 'newLocalPaymentSend',
      desc: '',
      args: [],
    );
  }

  /// `Select payment description`
  String get newPaymentSelectSubpurpose {
    return Intl.message(
      'Select payment description',
      name: 'newPaymentSelectSubpurpose',
      desc: '',
      args: [],
    );
  }

  /// `Payment Description`
  String get newPaymentSubpurpose {
    return Intl.message(
      'Payment Description',
      name: 'newPaymentSubpurpose',
      desc: '',
      args: [],
    );
  }

  /// `New bill payment`
  String get newUtilityBillPaymentHeaderTitle {
    return Intl.message(
      'New bill payment',
      name: 'newUtilityBillPaymentHeaderTitle',
      desc: '',
      args: [],
    );
  }

  /// `Which`
  String get newUtilityBillPaymentHighlightedText {
    return Intl.message(
      'Which',
      name: 'newUtilityBillPaymentHighlightedText',
      desc: '',
      args: [],
    );
  }

  /// `Which service would you like to pay for?`
  String get newUtilityBillPaymentLeftColumnTitle {
    return Intl.message(
      'Which service would you like to pay for?',
      name: 'newUtilityBillPaymentLeftColumnTitle',
      desc: '',
      args: [],
    );
  }

  /// `User has no accounts for transfer currency: {currency}`
  String noAccountsForSelectedCurrencyException(String currency) {
    return Intl.message(
      'User has no accounts for transfer currency: $currency',
      name: 'noAccountsForSelectedCurrencyException',
      desc: '',
      args: [currency],
    );
  }

  /// `No currencies have been received for selected beneficiary`
  String get noCurrencyFetchedException {
    return Intl.message(
      'No currencies have been received for selected beneficiary',
      name: 'noCurrencyFetchedException',
      desc: '',
      args: [],
    );
  }

  /// `No payees yet`
  String get noPayeeYet {
    return Intl.message(
      'No payees yet',
      name: 'noPayeeYet',
      desc: '',
      args: [],
    );
  }

  /// `Get started by clicking “Add Payee” above to add your first payee.`
  String get noPayeeYetSubText {
    return Intl.message(
      'Get started by clicking “Add Payee” above to add your first payee.',
      name: 'noPayeeYetSubText',
      desc: '',
      args: [],
    );
  }

  /// `No transfers yet`
  String get noTransferYet {
    return Intl.message(
      'No transfers yet',
      name: 'noTransferYet',
      desc: '',
      args: [],
    );
  }

  /// `Mabrook!`
  String get onboardingEmployeeMabrook {
    return Intl.message(
      'Mabrook!',
      name: 'onboardingEmployeeMabrook',
      desc: '',
      args: [],
    );
  }

  /// `Postpaid`
  String get optionPostpaidText {
    return Intl.message(
      'Postpaid',
      name: 'optionPostpaidText',
      desc: '',
      args: [],
    );
  }

  /// `Prepaid`
  String get optionPrepaidText {
    return Intl.message(
      'Prepaid',
      name: 'optionPrepaidText',
      desc: '',
      args: [],
    );
  }

  /// `PAY OR TOP-UP`
  String get payOrTopUpBtn {
    return Intl.message(
      'PAY OR TOP-UP',
      name: 'payOrTopUpBtn',
      desc: '',
      args: [],
    );
  }

  /// `New payment`
  String get payeeAddNewPayment {
    return Intl.message(
      'New payment',
      name: 'payeeAddNewPayment',
      desc: '',
      args: [],
    );
  }

  /// `{payeeName} has been successfully deleted from your list of payees.`
  String payeeDeleteToast(String payeeName) {
    return Intl.message(
      '$payeeName has been successfully deleted from your list of payees.',
      name: 'payeeDeleteToast',
      desc: '',
      args: [payeeName],
    );
  }

  /// `This customer can’t receive the money for now.`
  String get payeeUnavailableDescription {
    return Intl.message(
      'This customer can’t receive the money for now.',
      name: 'payeeUnavailableDescription',
      desc: '',
      args: [],
    );
  }

  /// `Temporarily unavailable`
  String get payeeUnavailableTitle {
    return Intl.message(
      'Temporarily unavailable',
      name: 'payeeUnavailableTitle',
      desc: '',
      args: [],
    );
  }

  /// `Account No.`
  String get paymentConfirmationAccountNumberLabel {
    return Intl.message(
      'Account No.',
      name: 'paymentConfirmationAccountNumberLabel',
      desc: '',
      args: [],
    );
  }

  /// `Amount sent`
  String get paymentConfirmationAmountSentLabel {
    return Intl.message(
      'Amount sent',
      name: 'paymentConfirmationAmountSentLabel',
      desc: '',
      args: [],
    );
  }

  /// `Channel`
  String get paymentConfirmationChannelLabel {
    return Intl.message(
      'Channel',
      name: 'paymentConfirmationChannelLabel',
      desc: '',
      args: [],
    );
  }

  /// `CONFIRM & PAY`
  String get paymentConfirmationCtaLabel {
    return Intl.message(
      'CONFIRM & PAY',
      name: 'paymentConfirmationCtaLabel',
      desc: '',
      args: [],
    );
  }

  /// `CONFIRM PAYMENT`
  String get paymentConfirmationCtaText {
    return Intl.message(
      'CONFIRM PAYMENT',
      name: 'paymentConfirmationCtaText',
      desc: '',
      args: [],
    );
  }

  /// `Exchange rate (will update in {duration})`
  String paymentConfirmationExchangeRateDurationLabel(String duration) {
    return Intl.message(
      'Exchange rate (will update in $duration)',
      name: 'paymentConfirmationExchangeRateDurationLabel',
      desc: '',
      args: [duration],
    );
  }

  /// `Exchange rate`
  String get paymentConfirmationExchangeRateLabel {
    return Intl.message(
      'Exchange rate',
      name: 'paymentConfirmationExchangeRateLabel',
      desc: '',
      args: [],
    );
  }

  /// `By confirming this transfer, you agree to waive the standard waiting period. This allows us to process your transfer immediately. Once processed, the transfer cannot be reversed.`
  String get paymentConfirmationFaqSubtitle1 {
    return Intl.message(
      'By confirming this transfer, you agree to waive the standard waiting period. This allows us to process your transfer immediately. Once processed, the transfer cannot be reversed.',
      name: 'paymentConfirmationFaqSubtitle1',
      desc: '',
      args: [],
    );
  }

  /// `If you provide incorrect or incomplete information about the recipient, you may be subject to penalties or fees. Additionally, the bank that processes the transfer may charge additional fees.`
  String get paymentConfirmationFaqSubtitle2 {
    return Intl.message(
      'If you provide incorrect or incomplete information about the recipient, you may be subject to penalties or fees. Additionally, the bank that processes the transfer may charge additional fees.',
      name: 'paymentConfirmationFaqSubtitle2',
      desc: '',
      args: [],
    );
  }

  /// `The actual transaction time may vary from our estimates due to increased scrutiny by the intermediary bank.`
  String get paymentConfirmationFaqSubtitle3 {
    return Intl.message(
      'The actual transaction time may vary from our estimates due to increased scrutiny by the intermediary bank.',
      name: 'paymentConfirmationFaqSubtitle3',
      desc: '',
      args: [],
    );
  }

  /// `Transfer is final and not reversible`
  String get paymentConfirmationFaqTitle1 {
    return Intl.message(
      'Transfer is final and not reversible',
      name: 'paymentConfirmationFaqTitle1',
      desc: '',
      args: [],
    );
  }

  /// `Additional fees or penalties may apply`
  String get paymentConfirmationFaqTitle2 {
    return Intl.message(
      'Additional fees or penalties may apply',
      name: 'paymentConfirmationFaqTitle2',
      desc: '',
      args: [],
    );
  }

  /// `Transfer timing may differ`
  String get paymentConfirmationFaqTitle3 {
    return Intl.message(
      'Transfer timing may differ',
      name: 'paymentConfirmationFaqTitle3',
      desc: '',
      args: [],
    );
  }

  /// `Fees (inc. VAT)`
  String get paymentConfirmationFeesLabel {
    return Intl.message(
      'Fees (inc. VAT)',
      name: 'paymentConfirmationFeesLabel',
      desc: '',
      args: [],
    );
  }

  /// `International transfer fetching process has failed`
  String get paymentConfirmationIntlTransferFetchingFail {
    return Intl.message(
      'International transfer fetching process has failed',
      name: 'paymentConfirmationIntlTransferFetchingFail',
      desc: '',
      args: [],
    );
  }

  /// `Review and confirm \r\nyour payment request`
  String get paymentConfirmationLeftColumnTitle {
    return Intl.message(
      'Review and confirm \r\nyour payment request',
      name: 'paymentConfirmationLeftColumnTitle',
      desc: '',
      args: [],
    );
  }

  /// `your payment request`
  String get paymentConfirmationLeftColumnTitleHighlighted {
    return Intl.message(
      'your payment request',
      name: 'paymentConfirmationLeftColumnTitleHighlighted',
      desc: '',
      args: [],
    );
  }

  /// `Notes to payee`
  String get paymentConfirmationNotesToPayeeLabel {
    return Intl.message(
      'Notes to payee',
      name: 'paymentConfirmationNotesToPayeeLabel',
      desc: '',
      args: [],
    );
  }

  /// `Bank`
  String get paymentConfirmationPageBankLabel {
    return Intl.message(
      'Bank',
      name: 'paymentConfirmationPageBankLabel',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong while transfer option update`
  String get paymentConfirmationQuoteUpdateError {
    return Intl.message(
      'Something went wrong while transfer option update',
      name: 'paymentConfirmationQuoteUpdateError',
      desc: '',
      args: [],
    );
  }

  /// `Your quote got updated!`
  String get paymentConfirmationQuoteUpdated {
    return Intl.message(
      'Your quote got updated!',
      name: 'paymentConfirmationQuoteUpdated',
      desc: '',
      args: [],
    );
  }

  /// `PAYMENT DETAILS`
  String get paymentConfirmationSubtitle {
    return Intl.message(
      'PAYMENT DETAILS',
      name: 'paymentConfirmationSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Wise Terms & Conditions`
  String get paymentConfirmationTACHighlightedLink {
    return Intl.message(
      'Wise Terms & Conditions',
      name: 'paymentConfirmationTACHighlightedLink',
      desc: '',
      args: [],
    );
  }

  /// `By initiating this transfer you accept Wise Terms & Conditions`
  String get paymentConfirmationTACLink {
    return Intl.message(
      'By initiating this transfer you accept Wise Terms & Conditions',
      name: 'paymentConfirmationTACLink',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Payment`
  String get paymentConfirmationTitle {
    return Intl.message(
      'Confirm Payment',
      name: 'paymentConfirmationTitle',
      desc: '',
      args: [],
    );
  }

  /// `Total amount debited`
  String get paymentConfirmationTotalAmountLabel {
    return Intl.message(
      'Total amount debited',
      name: 'paymentConfirmationTotalAmountLabel',
      desc: '',
      args: [],
    );
  }

  /// `Transfer initiating exception`
  String get paymentConfirmationTransferException {
    return Intl.message(
      'Transfer initiating exception',
      name: 'paymentConfirmationTransferException',
      desc: '',
      args: [],
    );
  }

  /// `Learn more about fees, cancellation and returns.`
  String get paymentFeesWarningBannerDescription {
    return Intl.message(
      'Learn more about fees, cancellation and returns.',
      name: 'paymentFeesWarningBannerDescription',
      desc: '',
      args: [],
    );
  }

  /// `The amount will be debited on the`
  String get paymentPopupSubtitle1 {
    return Intl.message(
      'The amount will be debited on the',
      name: 'paymentPopupSubtitle1',
      desc: '',
      args: [],
    );
  }

  /// `and you can cancel this request from transaction history anytime before`
  String get paymentPopupSubtitle2 {
    return Intl.message(
      'and you can cancel this request from transaction history anytime before',
      name: 'paymentPopupSubtitle2',
      desc: '',
      args: [],
    );
  }

  /// `business hours`
  String get paymentPopupTitle2 {
    return Intl.message(
      'business hours',
      name: 'paymentPopupTitle2',
      desc: '',
      args: [],
    );
  }

  /// `Total amount debited`
  String get paymentSuccessTotal {
    return Intl.message(
      'Total amount debited',
      name: 'paymentSuccessTotal',
      desc: '',
      args: [],
    );
  }

  /// `Once confirmed, the transaction can't be reversed. Learn more about cancellations, returns, and fees.`
  String get paymentSuccessWarningBannerDescription {
    return Intl.message(
      'Once confirmed, the transaction can\'t be reversed. Learn more about cancellations, returns, and fees.',
      name: 'paymentSuccessWarningBannerDescription',
      desc: '',
      args: [],
    );
  }

  /// `Payments`
  String get paymentsHomePageTitle {
    return Intl.message(
      'Payments',
      name: 'paymentsHomePageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Ex. : {example}`
  String paymentsInternationalTextInputExample(String example) {
    return Intl.message(
      'Ex. : $example',
      name: 'paymentsInternationalTextInputExample',
      desc: '',
      args: [example],
    );
  }

  /// `The field must contain min {maxLength} characters.`
  String paymentsInternationalTextInputMaxLength(String maxLength) {
    return Intl.message(
      'The field must contain min $maxLength characters.',
      name: 'paymentsInternationalTextInputMaxLength',
      desc: '',
      args: [maxLength],
    );
  }

  /// `The field must contain min {minLength} characters.`
  String paymentsInternationalTextInputMinLength(String minLength) {
    return Intl.message(
      'The field must contain min $minLength characters.',
      name: 'paymentsInternationalTextInputMinLength',
      desc: '',
      args: [minLength],
    );
  }

  /// `Not valid format`
  String get paymentsInternationalTextInputNotValidFormat {
    return Intl.message(
      'Not valid format',
      name: 'paymentsInternationalTextInputNotValidFormat',
      desc: '',
      args: [],
    );
  }

  /// `(optional)`
  String get paymentsInternationalTextInputOptional {
    return Intl.message(
      '(optional)',
      name: 'paymentsInternationalTextInputOptional',
      desc: '',
      args: [],
    );
  }

  /// `Please fill out this field.`
  String get paymentsInternationalTextInputRequired {
    return Intl.message(
      'Please fill out this field.',
      name: 'paymentsInternationalTextInputRequired',
      desc: '',
      args: [],
    );
  }

  /// `To`
  String get paymentsTo {
    return Intl.message(
      'To',
      name: 'paymentsTo',
      desc: '',
      args: [],
    );
  }

  /// `To {name}`
  String paymentsToAName(String name) {
    return Intl.message(
      'To $name',
      name: 'paymentsToAName',
      desc: '',
      args: [name],
    );
  }

  /// `The invoice must be registered with the Pension Authority, and the entered amount should exactly match the invoice amount to avoid payment rejection.`
  String get pensionInvoiceInputFieldDescription {
    return Intl.message(
      'The invoice must be registered with the Pension Authority, and the entered amount should exactly match the invoice amount to avoid payment rejection.',
      name: 'pensionInvoiceInputFieldDescription',
      desc: '',
      args: [],
    );
  }

  /// `INVOICE NUMBER`
  String get pensionInvoiceNumberTitle {
    return Intl.message(
      'INVOICE NUMBER',
      name: 'pensionInvoiceNumberTitle',
      desc: '',
      args: [],
    );
  }

  /// `What is the purpose of your transfer?`
  String get purposeSelectionLeftColumnTitle {
    return Intl.message(
      'What is the purpose of your transfer?',
      name: 'purposeSelectionLeftColumnTitle',
      desc: '',
      args: [],
    );
  }

  /// `the purpose`
  String get purposeSelectionLeftColumnTitleHighlighted {
    return Intl.message(
      'the purpose',
      name: 'purposeSelectionLeftColumnTitleHighlighted',
      desc: '',
      args: [],
    );
  }

  /// `Notes to PAYEE`
  String get purposeSelectionNotesTitle {
    return Intl.message(
      'Notes to PAYEE',
      name: 'purposeSelectionNotesTitle',
      desc: '',
      args: [],
    );
  }

  /// `Notes to PAYEE (Optional)`
  String get purposeSelectionOptionalNotesTitle {
    return Intl.message(
      'Notes to PAYEE (Optional)',
      name: 'purposeSelectionOptionalNotesTitle',
      desc: '',
      args: [],
    );
  }

  /// `amount to send`
  String get railsSelectionAmountToSend {
    return Intl.message(
      'amount to send',
      name: 'railsSelectionAmountToSend',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get railsSelectionContinueBtn {
    return Intl.message(
      'Continue',
      name: 'railsSelectionContinueBtn',
      desc: '',
      args: [],
    );
  }

  /// `Currencies fetch exception`
  String get railsSelectionCurrencyFetchException {
    return Intl.message(
      'Currencies fetch exception',
      name: 'railsSelectionCurrencyFetchException',
      desc: '',
      args: [],
    );
  }

  /// `EDIT AMOUNT`
  String get railsSelectionEditAmount {
    return Intl.message(
      'EDIT AMOUNT',
      name: 'railsSelectionEditAmount',
      desc: '',
      args: [],
    );
  }

  /// `How much would you like to send to {country}?`
  String railsSelectionLeftColumn(String country) {
    return Intl.message(
      'How much would you like to send to $country?',
      name: 'railsSelectionLeftColumn',
      desc: '',
      args: [country],
    );
  }

  /// `LOOKS GOOD`
  String get railsSelectionLooksGood {
    return Intl.message(
      'LOOKS GOOD',
      name: 'railsSelectionLooksGood',
      desc: '',
      args: [],
    );
  }

  /// `No currencies have been received for {country}`
  String railsSelectionNoCurrencyFetchedException(String country) {
    return Intl.message(
      'No currencies have been received for $country',
      name: 'railsSelectionNoCurrencyFetchedException',
      desc: '',
      args: [country],
    );
  }

  /// `SEE RATES`
  String get railsSelectionSeeRates {
    return Intl.message(
      'SEE RATES',
      name: 'railsSelectionSeeRates',
      desc: '',
      args: [],
    );
  }

  /// `Your rate`
  String get railsSelectionYourRateSubtitle {
    return Intl.message(
      'Your rate',
      name: 'railsSelectionYourRateSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `To request a higher limit, contact our {value1}`
  String requestHigherTransferDailyLimit(String value1) {
    return Intl.message(
      'To request a higher limit, contact our $value1',
      name: 'requestHigherTransferDailyLimit',
      desc: '',
      args: [value1],
    );
  }

  /// `Save changes`
  String get saveChanges {
    return Intl.message(
      'Save changes',
      name: 'saveChanges',
      desc: '',
      args: [],
    );
  }

  /// `Search...`
  String get searchDots {
    return Intl.message(
      'Search...',
      name: 'searchDots',
      desc: '',
      args: [],
    );
  }

  /// `Which account would you like to send from?`
  String get selectAccLeftColumnTitle {
    return Intl.message(
      'Which account would you like to send from?',
      name: 'selectAccLeftColumnTitle',
      desc: '',
      args: [],
    );
  }

  /// `Which`
  String get selectAccLeftColumnTitleHighlighted {
    return Intl.message(
      'Which',
      name: 'selectAccLeftColumnTitleHighlighted',
      desc: '',
      args: [],
    );
  }

  /// `Where is the payment headed?`
  String get selectCountryLeftColumnTitle {
    return Intl.message(
      'Where is the payment headed?',
      name: 'selectCountryLeftColumnTitle',
      desc: '',
      args: [],
    );
  }

  /// `Where`
  String get selectCountryLeftColumnTitleHighlighted {
    return Intl.message(
      'Where',
      name: 'selectCountryLeftColumnTitleHighlighted',
      desc: '',
      args: [],
    );
  }

  /// `Select currency`
  String get selectCurrency {
    return Intl.message(
      'Select currency',
      name: 'selectCurrency',
      desc: '',
      args: [],
    );
  }

  /// `Available currencies in {name}`
  String selectCurrencyBeneficiaryCreationTitle(String name) {
    return Intl.message(
      'Available currencies in $name',
      name: 'selectCurrencyBeneficiaryCreationTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Looks like something went wrong.`
  String get smthWentWrong {
    return Intl.message(
      'Looks like something went wrong.',
      name: 'smthWentWrong',
      desc: '',
      args: [],
    );
  }

  /// `ALL DONE`
  String get successPaymentCtaText {
    return Intl.message(
      'ALL DONE',
      name: 'successPaymentCtaText',
      desc: '',
      args: [],
    );
  }

  /// `You've sent `
  String get successPaymentFormTitle {
    return Intl.message(
      'You\'ve sent ',
      name: 'successPaymentFormTitle',
      desc: '',
      args: [],
    );
  }

  /// `MABROOK!`
  String get successPaymentLeftColumnTitle {
    return Intl.message(
      'MABROOK!',
      name: 'successPaymentLeftColumnTitle',
      desc: '',
      args: [],
    );
  }

  /// `Payment sent.`
  String get successPaymentSubtitle {
    return Intl.message(
      'Payment sent.',
      name: 'successPaymentSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Payment sent for approval.`
  String get successPaymentSubtitleForApproval {
    return Intl.message(
      'Payment sent for approval.',
      name: 'successPaymentSubtitleForApproval',
      desc: '',
      args: [],
    );
  }

  /// `Transfer Ref.`
  String get successPaymentTransferRef {
    return Intl.message(
      'Transfer Ref.',
      name: 'successPaymentTransferRef',
      desc: '',
      args: [],
    );
  }

  /// `It should be registered with FTA and the amount entered should be exactly the same as the amount in the invoice.`
  String get taxInvoiceInputFieldDescription {
    return Intl.message(
      'It should be registered with FTA and the amount entered should be exactly the same as the amount in the invoice.',
      name: 'taxInvoiceInputFieldDescription',
      desc: '',
      args: [],
    );
  }

  /// `TAX INVOICE NUMBER`
  String get taxInvoiceNumberTitle {
    return Intl.message(
      'TAX INVOICE NUMBER',
      name: 'taxInvoiceNumberTitle',
      desc: '',
      args: [],
    );
  }

  /// `CONTINUE`
  String get telecomPaymentFirstScreenCTA {
    return Intl.message(
      'CONTINUE',
      name: 'telecomPaymentFirstScreenCTA',
      desc: '',
      args: [],
    );
  }

  /// `Transfer daily limit`
  String get transactionLimitSettingLeftColumnTitle {
    return Intl.message(
      'Transfer daily limit',
      name: 'transactionLimitSettingLeftColumnTitle',
      desc: '',
      args: [],
    );
  }

  /// `Remaining daily limit:`
  String get transferDailyLimitRemaining {
    return Intl.message(
      'Remaining daily limit:',
      name: 'transferDailyLimitRemaining',
      desc: '',
      args: [],
    );
  }

  /// `Limit will reset daily`
  String get transferDailyLimitReset {
    return Intl.message(
      'Limit will reset daily',
      name: 'transferDailyLimitReset',
      desc: '',
      args: [],
    );
  }

  /// `Based on your monthly income and average daily expenses, we recommend this limit.`
  String get transferDailyLimitWidgetSubtitle {
    return Intl.message(
      'Based on your monthly income and average daily expenses, we recommend this limit.',
      name: 'transferDailyLimitWidgetSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Suggested limit of {value1}`
  String transferDailyLimitWidgetTitle(String value1) {
    return Intl.message(
      'Suggested limit of $value1',
      name: 'transferDailyLimitWidgetTitle',
      desc: '',
      args: [value1],
    );
  }

  /// `Once confirmed, the transaction can't be reversed. Learn more about cancellations, returns, and fees.`
  String get transferDetailsFaq {
    return Intl.message(
      'Once confirmed, the transaction can\'t be reversed. Learn more about cancellations, returns, and fees.',
      name: 'transferDetailsFaq',
      desc: '',
      args: [],
    );
  }

  /// `Transfer fee (Inc. VAT)`
  String get transferFeeLabel {
    return Intl.message(
      'Transfer fee (Inc. VAT)',
      name: 'transferFeeLabel',
      desc: '',
      args: [],
    );
  }

  /// `Available balance:`
  String get transferLimitAvailableBalance {
    return Intl.message(
      'Available balance:',
      name: 'transferLimitAvailableBalance',
      desc: '',
      args: [],
    );
  }

  /// `of {value1} per day`
  String transferLimitDashboardSubtitle(String value1) {
    return Intl.message(
      'of $value1 per day',
      name: 'transferLimitDashboardSubtitle',
      desc: '',
      args: [value1],
    );
  }

  /// `Remaining limit:`
  String get transferLimitDashboardTitle {
    return Intl.message(
      'Remaining limit:',
      name: 'transferLimitDashboardTitle',
      desc: '',
      args: [],
    );
  }

  /// `You’ve reached your daily limit.`
  String get transferLimitExceeded {
    return Intl.message(
      'You’ve reached your daily limit.',
      name: 'transferLimitExceeded',
      desc: '',
      args: [],
    );
  }

  /// `Remaining daily limit: {value1} of {value2}`
  String transferLimitRemaining(String value1, String value2) {
    return Intl.message(
      'Remaining daily limit: $value1 of $value2',
      name: 'transferLimitRemaining',
      desc: '',
      args: [value1, value2],
    );
  }

  /// `Transfer limit`
  String get transferLimitSectionTitle {
    return Intl.message(
      'Transfer limit',
      name: 'transferLimitSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Total daily limit: {value1}`
  String transferLimitWidgetSubtitle(String value1) {
    return Intl.message(
      'Total daily limit: $value1',
      name: 'transferLimitWidgetSubtitle',
      desc: '',
      args: [value1],
    );
  }

  /// `Estimated time`
  String get transferOptionEstimatedTimeLabel {
    return Intl.message(
      'Estimated time',
      name: 'transferOptionEstimatedTimeLabel',
      desc: '',
      args: [],
    );
  }

  /// `Exchange rate`
  String get transferOptionExchangeRateLabel {
    return Intl.message(
      'Exchange rate',
      name: 'transferOptionExchangeRateLabel',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get transferOptionSelectContinueBtn {
    return Intl.message(
      'Continue',
      name: 'transferOptionSelectContinueBtn',
      desc: '',
      args: [],
    );
  }

  /// `You will pay:`
  String get transferOptionYouWillPayLabel {
    return Intl.message(
      'You will pay:',
      name: 'transferOptionYouWillPayLabel',
      desc: '',
      args: [],
    );
  }

  /// `Balance: {amount}`
  String transferOptionsBalanceLabel(String amount) {
    return Intl.message(
      'Balance: $amount',
      name: 'transferOptionsBalanceLabel',
      desc: '',
      args: [amount],
    );
  }

  /// `Enter the transfer amount to view the rates and estimated transfer times to {country} 💸.`
  String transferOptionsEmptyTitle(String country) {
    return Intl.message(
      'Enter the transfer amount to view the rates and estimated transfer times to $country 💸.',
      name: 'transferOptionsEmptyTitle',
      desc: '',
      args: [country],
    );
  }

  /// `We couldn’t get your rate right now. It seems there has been a technical glitch. Please retry.`
  String get transferOptionsError {
    return Intl.message(
      'We couldn’t get your rate right now. It seems there has been a technical glitch. Please retry.',
      name: 'transferOptionsError',
      desc: '',
      args: [],
    );
  }

  /// `OOPS!`
  String get transferOptionsErrorTitle {
    return Intl.message(
      'OOPS!',
      name: 'transferOptionsErrorTitle',
      desc: '',
      args: [],
    );
  }

  /// `Transfer purposes fetching exception`
  String get transferPurposesFetchingException {
    return Intl.message(
      'Transfer purposes fetching exception',
      name: 'transferPurposesFetchingException',
      desc: '',
      args: [],
    );
  }

  /// `Try again`
  String get tryAgain {
    return Intl.message(
      'Try again',
      name: 'tryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Oops, we couldn’t delete the biller. Please try again.`
  String get utilityBeneficiaryDeletionErrorText {
    return Intl.message(
      'Oops, we couldn’t delete the biller. Please try again.',
      name: 'utilityBeneficiaryDeletionErrorText',
      desc: '',
      args: [],
    );
  }

  /// `Oops, we couldn’t save the payee right now. Please try again.`
  String get utilityBeneficiarySaveErrorText {
    return Intl.message(
      'Oops, we couldn’t save the payee right now. Please try again.',
      name: 'utilityBeneficiarySaveErrorText',
      desc: '',
      args: [],
    );
  }

  /// `Phone number format:`
  String get utilityBillPaymentAccountDetailsInfoText {
    return Intl.message(
      'Phone number format:',
      name: 'utilityBillPaymentAccountDetailsInfoText',
      desc: '',
      args: [],
    );
  }

  /// `Enter your account details?`
  String get utilityBillPaymentAccountDetailsLeftColumnTitle {
    return Intl.message(
      'Enter your account details?',
      name: 'utilityBillPaymentAccountDetailsLeftColumnTitle',
      desc: '',
      args: [],
    );
  }

  /// `Invalid phone or account number. Required phone number format: 05xxxxxxxx, 02xxxxxx.`
  String get utilityBillPaymentAccountDetailsValidationErrorText {
    return Intl.message(
      'Invalid phone or account number. Required phone number format: 05xxxxxxxx, 02xxxxxx.',
      name: 'utilityBillPaymentAccountDetailsValidationErrorText',
      desc: '',
      args: [],
    );
  }

  /// `Account / phone number`
  String get utilityBillPaymentAccountNumberTextFieldText {
    return Intl.message(
      'Account / phone number',
      name: 'utilityBillPaymentAccountNumberTextFieldText',
      desc: '',
      args: [],
    );
  }

  /// `TRY AGAIN`
  String get utilityBillPaymentErrorButtonTitle {
    return Intl.message(
      'TRY AGAIN',
      name: 'utilityBillPaymentErrorButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Oops, we could’t display your billers right now.`
  String get utilityBillPaymentHomeErrorTitle {
    return Intl.message(
      'Oops, we could’t display your billers right now.',
      name: 'utilityBillPaymentHomeErrorTitle',
      desc: '',
      args: [],
    );
  }

  /// `nol tag ID`
  String get utilityBillPaymentNolTagText {
    return Intl.message(
      'nol tag ID',
      name: 'utilityBillPaymentNolTagText',
      desc: '',
      args: [],
    );
  }

  /// `Pay for Salik and NOL services.`
  String get utilityBillPaymentOthersOptionSubTitle {
    return Intl.message(
      'Pay for Salik and NOL services.',
      name: 'utilityBillPaymentOthersOptionSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `Others`
  String get utilityBillPaymentOthersOptionTitle {
    return Intl.message(
      'Others',
      name: 'utilityBillPaymentOthersOptionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Bills`
  String get utilityBillPaymentTabTitle {
    return Intl.message(
      'Bills',
      name: 'utilityBillPaymentTabTitle',
      desc: '',
      args: [],
    );
  }

  /// `Top-up and pay for connectivity bills from Etisalat and Du.`
  String get utilityBillPaymentTelecomOptionSubTitle {
    return Intl.message(
      'Top-up and pay for connectivity bills from Etisalat and Du.',
      name: 'utilityBillPaymentTelecomOptionSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `Telecom`
  String get utilityBillPaymentTelecomOptionTitle {
    return Intl.message(
      'Telecom',
      name: 'utilityBillPaymentTelecomOptionTitle',
      desc: '',
      args: [],
    );
  }

  /// `New {provider} payment`
  String utilityPaymentAccountDetailsHeaderTitle(String provider) {
    return Intl.message(
      'New $provider payment',
      name: 'utilityPaymentAccountDetailsHeaderTitle',
      desc: '',
      args: [provider],
    );
  }

  /// `Enter`
  String get utilityPaymentAccountDetailsHighlightedText {
    return Intl.message(
      'Enter',
      name: 'utilityPaymentAccountDetailsHighlightedText',
      desc: '',
      args: [],
    );
  }

  /// `Type of plan`
  String get utilityPaymentAccountDetailsPlaceHolderText {
    return Intl.message(
      'Type of plan',
      name: 'utilityPaymentAccountDetailsPlaceHolderText',
      desc: '',
      args: [],
    );
  }

  /// `YOUR ACCOUNT`
  String get utilityPaymentAccountListHeaderTitle {
    return Intl.message(
      'YOUR ACCOUNT',
      name: 'utilityPaymentAccountListHeaderTitle',
      desc: '',
      args: [],
    );
  }

  /// `Oops, we couldn’t validate the amount right now. Please try again.`
  String get utilityPaymentAmountValidationErrorText {
    return Intl.message(
      'Oops, we couldn’t validate the amount right now. Please try again.',
      name: 'utilityPaymentAmountValidationErrorText',
      desc: '',
      args: [],
    );
  }

  /// `Your available balance is {balance}`
  String utilityPaymentBalanceText(String balance) {
    return Intl.message(
      'Your available balance is $balance',
      name: 'utilityPaymentBalanceText',
      desc: '',
      args: [balance],
    );
  }

  /// `Biller successfully deleted`
  String get utilityPaymentBillerDeletionSuccessText {
    return Intl.message(
      'Biller successfully deleted',
      name: 'utilityPaymentBillerDeletionSuccessText',
      desc: '',
      args: [],
    );
  }

  /// `Account number`
  String get utilityPaymentConfirmationAccNumberText {
    return Intl.message(
      'Account number',
      name: 'utilityPaymentConfirmationAccNumberText',
      desc: '',
      args: [],
    );
  }

  /// `Payment amount`
  String get utilityPaymentConfirmationAmountText {
    return Intl.message(
      'Payment amount',
      name: 'utilityPaymentConfirmationAmountText',
      desc: '',
      args: [],
    );
  }

  /// `CONFIRM PAYMENT`
  String get utilityPaymentConfirmationCtaText {
    return Intl.message(
      'CONFIRM PAYMENT',
      name: 'utilityPaymentConfirmationCtaText',
      desc: '',
      args: [],
    );
  }

  /// `your payment details`
  String get utilityPaymentConfirmationHighlightedText {
    return Intl.message(
      'your payment details',
      name: 'utilityPaymentConfirmationHighlightedText',
      desc: '',
      args: [],
    );
  }

  /// `Review and confirm your\r\npayment details`
  String get utilityPaymentConfirmationLeftColumnText {
    return Intl.message(
      'Review and confirm your\r\npayment details',
      name: 'utilityPaymentConfirmationLeftColumnText',
      desc: '',
      args: [],
    );
  }

  /// `Review and confirm your payment details`
  String get utilityPaymentConfirmationLeftColumnTitleText {
    return Intl.message(
      'Review and confirm your payment details',
      name: 'utilityPaymentConfirmationLeftColumnTitleText',
      desc: '',
      args: [],
    );
  }

  /// `My nol card`
  String get utilityPaymentConfirmationNolTitle {
    return Intl.message(
      'My nol card',
      name: 'utilityPaymentConfirmationNolTitle',
      desc: '',
      args: [],
    );
  }

  /// `Personal mobile`
  String get utilityPaymentConfirmationPersonalDetails {
    return Intl.message(
      'Personal mobile',
      name: 'utilityPaymentConfirmationPersonalDetails',
      desc: '',
      args: [],
    );
  }

  /// `Phone number`
  String get utilityPaymentConfirmationPhonetNumberText {
    return Intl.message(
      'Phone number',
      name: 'utilityPaymentConfirmationPhonetNumberText',
      desc: '',
      args: [],
    );
  }

  /// `Personal mobile`
  String get utilityPaymentConfirmationPostPaidHeaderTitle {
    return Intl.message(
      'Personal mobile',
      name: 'utilityPaymentConfirmationPostPaidHeaderTitle',
      desc: '',
      args: [],
    );
  }

  /// `Salik`
  String get utilityPaymentConfirmationSalikText {
    return Intl.message(
      'Salik',
      name: 'utilityPaymentConfirmationSalikText',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Payment`
  String get utilityPaymentConfirmationScreenTitle {
    return Intl.message(
      'Confirm Payment',
      name: 'utilityPaymentConfirmationScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Type of plan`
  String get utilityPaymentConfirmationTypeOfPlanText {
    return Intl.message(
      'Type of plan',
      name: 'utilityPaymentConfirmationTypeOfPlanText',
      desc: '',
      args: [],
    );
  }

  /// `Last payments`
  String get utilityPaymentDetailsListSubHeaderText {
    return Intl.message(
      'Last payments',
      name: 'utilityPaymentDetailsListSubHeaderText',
      desc: '',
      args: [],
    );
  }

  /// `Elife`
  String get utilityPaymentElifeText {
    return Intl.message(
      'Elife',
      name: 'utilityPaymentElifeText',
      desc: '',
      args: [],
    );
  }

  /// `PAY OR TOP-UP`
  String get utilityPaymentEmptyCtaText {
    return Intl.message(
      'PAY OR TOP-UP',
      name: 'utilityPaymentEmptyCtaText',
      desc: '',
      args: [],
    );
  }

  /// `Wio simplifies the way you pay your bills or top-up your account.`
  String get utilityPaymentEmptySubTitle {
    return Intl.message(
      'Wio simplifies the way you pay your bills or top-up your account.',
      name: 'utilityPaymentEmptySubTitle',
      desc: '',
      args: [],
    );
  }

  /// `No payments yet`
  String get utilityPaymentEmptyTitleText {
    return Intl.message(
      'No payments yet',
      name: 'utilityPaymentEmptyTitleText',
      desc: '',
      args: [],
    );
  }

  /// `Looks like something went wrong.`
  String get utilityPaymentErrorSubTitle {
    return Intl.message(
      'Looks like something went wrong.',
      name: 'utilityPaymentErrorSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `UH OH!`
  String get utilityPaymentErrorTitle {
    return Intl.message(
      'UH OH!',
      name: 'utilityPaymentErrorTitle',
      desc: '',
      args: [],
    );
  }

  /// `ALL BILLERS`
  String get utilityPaymentHeaderTitleText {
    return Intl.message(
      'ALL BILLERS',
      name: 'utilityPaymentHeaderTitleText',
      desc: '',
      args: [],
    );
  }

  /// `It seems there has been a technical glitch.`
  String get utilityPaymentHomeErrorSubtitle {
    return Intl.message(
      'It seems there has been a technical glitch.',
      name: 'utilityPaymentHomeErrorSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Payment amount`
  String get utilityPaymentListSubHeaderText {
    return Intl.message(
      'Payment amount',
      name: 'utilityPaymentListSubHeaderText',
      desc: '',
      args: [],
    );
  }

  /// `Entered amount is more than available balance.`
  String get utilityPaymentLocalAmountValidationErrorText {
    return Intl.message(
      'Entered amount is more than available balance.',
      name: 'utilityPaymentLocalAmountValidationErrorText',
      desc: '',
      args: [],
    );
  }

  /// `Payee nickname`
  String get utilityPaymentNickNameTextFieldPlaceHolder {
    return Intl.message(
      'Payee nickname',
      name: 'utilityPaymentNickNameTextFieldPlaceHolder',
      desc: '',
      args: [],
    );
  }

  /// `nol tag ID`
  String get utilityPaymentNolTextFeildPlaceholderText {
    return Intl.message(
      'nol tag ID',
      name: 'utilityPaymentNolTextFeildPlaceholderText',
      desc: '',
      args: [],
    );
  }

  /// `Current {provider} balance`
  String utilityPaymentOtherHeaderTitleText(String provider) {
    return Intl.message(
      'Current $provider balance',
      name: 'utilityPaymentOtherHeaderTitleText',
      desc: '',
      args: [provider],
    );
  }

  /// `PIN`
  String get utilityPaymentPinText {
    return Intl.message(
      'PIN',
      name: 'utilityPaymentPinText',
      desc: '',
      args: [],
    );
  }

  /// `Your mobile balance`
  String get utilityPaymentPrepaidHeaderTitle {
    return Intl.message(
      'Your mobile balance',
      name: 'utilityPaymentPrepaidHeaderTitle',
      desc: '',
      args: [],
    );
  }

  /// `Select Utility Provider`
  String get utilityPaymentProvider {
    return Intl.message(
      'Select Utility Provider',
      name: 'utilityPaymentProvider',
      desc: '',
      args: [],
    );
  }

  /// `{provider} payment`
  String utilityPaymentReTopupTitleText(String provider) {
    return Intl.message(
      '$provider payment',
      name: 'utilityPaymentReTopupTitleText',
      desc: '',
      args: [provider],
    );
  }

  /// `Account / Consumer number`
  String get utilityPaymentSalikTextFeildPlaceholderText {
    return Intl.message(
      'Account / Consumer number',
      name: 'utilityPaymentSalikTextFeildPlaceholderText',
      desc: '',
      args: [],
    );
  }

  /// `Amount`
  String get utilityPaymentSuccessAmmountInfo {
    return Intl.message(
      'Amount',
      name: 'utilityPaymentSuccessAmmountInfo',
      desc: '',
      args: [],
    );
  }

  /// `All Done`
  String get utilityPaymentSuccessCtaText {
    return Intl.message(
      'All Done',
      name: 'utilityPaymentSuccessCtaText',
      desc: '',
      args: [],
    );
  }

  /// `To`
  String get utilityPaymentSuccessProviderTitle {
    return Intl.message(
      'To',
      name: 'utilityPaymentSuccessProviderTitle',
      desc: '',
      args: [],
    );
  }

  /// `Transfer ref. number - {referenceNumber}`
  String utilityPaymentSuccessRefrenceNumberTitleText(String referenceNumber) {
    return Intl.message(
      'Transfer ref. number - $referenceNumber',
      name: 'utilityPaymentSuccessRefrenceNumberTitleText',
      desc: '',
      args: [referenceNumber],
    );
  }

  /// `Transfer Ref.`
  String get utilityPaymentSuccessTransferReferenceTitle {
    return Intl.message(
      'Transfer Ref.',
      name: 'utilityPaymentSuccessTransferReferenceTitle',
      desc: '',
      args: [],
    );
  }

  /// `Pay Now`
  String get utilityPaymentTopUpCtaText {
    return Intl.message(
      'Pay Now',
      name: 'utilityPaymentTopUpCtaText',
      desc: '',
      args: [],
    );
  }

  /// `pay`
  String get utilityPaymentTopUpPostpaidHighlightText {
    return Intl.message(
      'pay',
      name: 'utilityPaymentTopUpPostpaidHighlightText',
      desc: '',
      args: [],
    );
  }

  /// `Let’s pay your bill.`
  String get utilityPaymentTopUpScreenPostpaidLeftColumnText {
    return Intl.message(
      'Let’s pay your bill.',
      name: 'utilityPaymentTopUpScreenPostpaidLeftColumnText',
      desc: '',
      args: [],
    );
  }

  /// `top up`
  String get utilityPaymentTopUpScreenPrepaidHighlightedText {
    return Intl.message(
      'top up',
      name: 'utilityPaymentTopUpScreenPrepaidHighlightedText',
      desc: '',
      args: [],
    );
  }

  /// `Let’s top up your account.`
  String get utilityPaymentTopUpScreenPrepaidLeftColumnText {
    return Intl.message(
      'Let’s top up your account.',
      name: 'utilityPaymentTopUpScreenPrepaidLeftColumnText',
      desc: '',
      args: [],
    );
  }

  /// `Your Bill`
  String get utilityPaymentTopUpTitle {
    return Intl.message(
      'Your Bill',
      name: 'utilityPaymentTopUpTitle',
      desc: '',
      args: [],
    );
  }

  /// `Transfer ref. number - {refNumber}`
  String utilityPaymentTransactionRefNumberText(String refNumber) {
    return Intl.message(
      'Transfer ref. number - $refNumber',
      name: 'utilityPaymentTransactionRefNumberText',
      desc: '',
      args: [refNumber],
    );
  }

  /// `Unable to verify account`
  String get utilityPaymentUnableToVerifyAccountText {
    return Intl.message(
      'Unable to verify account',
      name: 'utilityPaymentUnableToVerifyAccountText',
      desc: '',
      args: [],
    );
  }

  /// `View all`
  String get viewAll {
    return Intl.message(
      'View all',
      name: 'viewAll',
      desc: '',
      args: [],
    );
  }

  /// `Wise account created sucessfully`
  String get wiseAccountCreated {
    return Intl.message(
      'Wise account created sucessfully',
      name: 'wiseAccountCreated',
      desc: '',
      args: [],
    );
  }

  /// `Wise account creation failed`
  String get wiseAccountCreationError {
    return Intl.message(
      'Wise account creation failed',
      name: 'wiseAccountCreationError',
      desc: '',
      args: [],
    );
  }

  /// `Wise countries of birth fetching exception`
  String get wiseCountriesOfBirthFetchingException {
    return Intl.message(
      'Wise countries of birth fetching exception',
      name: 'wiseCountriesOfBirthFetchingException',
      desc: '',
      args: [],
    );
  }

  /// `Create account`
  String get wiseCtaText {
    return Intl.message(
      'Create account',
      name: 'wiseCtaText',
      desc: '',
      args: [],
    );
  }

  /// `Your birth country`
  String get wiseDialogDropdownTitle {
    return Intl.message(
      'Your birth country',
      name: 'wiseDialogDropdownTitle',
      desc: '',
      args: [],
    );
  }

  /// `terms of use & privacy policy`
  String get wiseLoginDialogSubtitleBoldText {
    return Intl.message(
      'terms of use & privacy policy',
      name: 'wiseLoginDialogSubtitleBoldText',
      desc: '',
      args: [],
    );
  }

  /// `By creating an account you accept Wise\nterms of use & privacy policy`
  String get wiseLoginDialogSubtitleText {
    return Intl.message(
      'By creating an account you accept Wise\nterms of use & privacy policy',
      name: 'wiseLoginDialogSubtitleText',
      desc: '',
      args: [],
    );
  }

  /// `We use Wise for faster, cheaper\ninternational payments.`
  String get wiseLoginTitle {
    return Intl.message(
      'We use Wise for faster, cheaper\ninternational payments.',
      name: 'wiseLoginTitle',
      desc: '',
      args: [],
    );
  }

  /// ` terms of use & privacy policy`
  String get wisePopUpSubtitleBoldText {
    return Intl.message(
      ' terms of use & privacy policy',
      name: 'wisePopUpSubtitleBoldText',
      desc: '',
      args: [],
    );
  }

  /// `By creating an account you accept Wise`
  String get wisePopUpSubtitleNormalText {
    return Intl.message(
      'By creating an account you accept Wise',
      name: 'wisePopUpSubtitleNormalText',
      desc: '',
      args: [],
    );
  }

  /// `Wise status fetching exception`
  String get wiseStatusFetchingException {
    return Intl.message(
      'Wise status fetching exception',
      name: 'wiseStatusFetchingException',
      desc: '',
      args: [],
    );
  }
}

class _AppLocalizationDelegate
    extends LocalizationsDelegate<PaymentUILocalizations> {
  const _AppLocalizationDelegate();

  @override
  bool isSupported(Locale locale) =>
      PaymentUILocalizations.supportedLocales.any((supportedLocale) =>
          supportedLocale.languageCode == locale.languageCode);

  @override
  Future<PaymentUILocalizations> load(Locale locale) =>
      PaymentUILocalizations.load(locale);

  @override
  bool shouldReload(_AppLocalizationDelegate old) => false;
}
