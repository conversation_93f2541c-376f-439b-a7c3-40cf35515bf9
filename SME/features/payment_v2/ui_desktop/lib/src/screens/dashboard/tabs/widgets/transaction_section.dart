import 'package:flutter/material.dart';
import 'package:payment_v2_ui_desktop/src/screens/dashboard/tabs/widgets/dashboard_transactions_list.dart';
import 'package:sme_feature_transactions_api/feature_transactions_api.dart';
import 'package:transaction_ui_desktop/transaction_ui_desktop.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';

class TransactionSection extends StatelessWidget {
  final String label;
  final TransactionType transactionType;

  const TransactionSection({
    required this.label,
    required this.transactionType,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListHeader(
          ListHeaderModel(
            title: label,
            headerType: ListHeaderType.medium,
          ),
          buttonCluster: const [
            TransactionFilterOptions(),
          ],
        ),
        Expanded(
          child: DashboardTransactionsList(
            transactionType: transactionType,
          ),
        ),
      ],
    );
  }
}
