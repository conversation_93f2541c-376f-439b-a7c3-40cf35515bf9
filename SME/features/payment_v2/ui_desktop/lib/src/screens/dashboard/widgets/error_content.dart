import 'package:flutter/material.dart';
import 'package:payment_v2_ui_desktop/src/l10n/payment_ui_desktop_localizations.g.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_sme_faq_ui_desktop/faq_feature_ui_desktop.dart';

class ErrorContent extends StatelessWidget {
  final String subtitle;
  final VoidCallback tryAgainCallback;

  const ErrorContent({
    required this.subtitle,
    required this.tryAgainCallback,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final localization = PaymentUILocalizations.of(context);

    return ErrorScreen(
      model: ErrorScreenModel(
        errorTitle: localization.flowInitErorrTitle,
        errorSubtitle: subtitle,
        confirmCtaText: localization.tryAgain,
        contactLinkText: localization.contactSupport,
        //ignore: deprecated_member_use
        contactScreenModel: ContactScreenModel(
          title: localization.accountContactDrawerTitle1,
          subtitle: localization.accountContactDrawerSubtitle,
          whatsappCardTitle: localization.accountContactDrawerWhatsapp,
          whatsappCardSubtitle: localization.accountContactDrawerWhatsAppText,
          emailCardTitle: localization.contactScreenEmail,
          emailCardSubtitle: localization.accountContactDrawerEmailText,
          phoneCardTitle: localization.contactScreenPhoneCardTitle,
          phoneCardSubtitle: localization.accountContactDrawerCallCentreText,
        ),
      ),
      onConfirmCtaClick: () async => tryAgainCallback(),
      headerCloseCallback: tryAgainCallback,
      endDrawer: const FaqContent(),
    );
  }
}
