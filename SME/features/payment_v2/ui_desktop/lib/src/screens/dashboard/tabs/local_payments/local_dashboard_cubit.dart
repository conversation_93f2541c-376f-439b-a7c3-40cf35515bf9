import 'dart:async';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:flutter/cupertino.dart';
import 'package:logging_api/logging.dart';
import 'package:payment_v2_ui_desktop/src/l10n/payment_ui_desktop_localizations.g.dart';
import 'package:payment_v2_ui_desktop/src/navigation/payment/add_beneficiary_navigation_config.dart';
import 'package:payment_v2_ui_desktop/src/navigation/payment/new_transfer_navigation_config.dart';
import 'package:payment_v2_ui_desktop/src/screens/dashboard/notifications/notifications.dart';
import 'package:payment_v2_ui_desktop/src/screens/dashboard/tabs/local_payments/local_dashboard_state.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ui/ui.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';

class LocalDashboardCubit extends BaseCubit<LocalDashboardState> {
  static const _logPrefix = 'LOCAL PAYMENTS DASHBOARD: ';

  final BeneficiaryInteractor _interactor;
  final NavigationProvider _navigationProvider;
  final WioPaymentsAnalytics _analytics;
  final PaymentUILocalizations _localization;
  final Logger _logger;
  final PerformanceMonitor _performanceMonitor;
  final FeatureToggleProvider _featureToggleProvider;

  LocalDashboardCubit({
    required BeneficiaryInteractor interactor,
    required NavigationProvider navigationProvider,
    required WioPaymentsAnalytics analytics,
    required PaymentUILocalizations localization,
    required Logger logger,
    required PerformanceMonitor performanceMonitor,
    required FeatureToggleProvider featureToggleProvider,
  })  : _interactor = interactor,
        _navigationProvider = navigationProvider,
        _analytics = analytics,
        _localization = localization,
        _logger = logger,
        _performanceMonitor = performanceMonitor,
        _featureToggleProvider = featureToggleProvider,
        super(const LocalDashboardState.loading());

  bool get _isAddBeneficiaryFlowEnabled => _featureToggleProvider
      .get(PaymentsFeatureToggle.isAddBeneficiaryFlowEnabled);

  Future<void> init() async {
    final loadTrace =
        _performanceMonitor.startPageLoadTrace('LocalPaymentsPage');
    try {
      _analytics.landOnDashboard(PaymentType.local);
      _bindBeneficiariesStream(recent: true);
    } on Exception catch (e) {
      _logger.error(_logMsg('Init exception'), error: e);
    } finally {
      loadTrace.finish();
    }
  }

  Future<void> deleteBeneficiary(String id) async {
    await state.mapOrNull(
      loaded: (state) async {
        final fallbackState = state;
        try {
          await _interactor.deleteBeneficiary(id: id);
          safeEmit(const LocalDashboardState.snackbar());
          safeEmit(
            LocalDashboardState.loaded(
              recentBeneficiaries: state.recentBeneficiaries
                  .where((beneficiary) => beneficiary.id != id)
                  .toList(),
              beneficiaries: state.beneficiaries
                  .where((beneficiary) => beneficiary.id != id)
                  .toList(),
              isAddBeneficiaryFlowEnabled: _isAddBeneficiaryFlowEnabled,
            ),
          );
        } on Exception catch (e) {
          safeEmit(const LocalDashboardState.snackbar(success: false));
          safeEmit(fallbackState);
          _logger.error(
            _logMsg('Delete local beneficiary exception'),
            error: e,
          );
        }
      },
    );
  }

  void navigateToNewPayment([
    CrossborderInternationalBeneficiary? beneficiary,
  ]) {
    _navigationProvider.push(
      NewTransferScreenNavigationConfig(
        beneficiary: beneficiary,
        paymentType: PaymentType.local,
      ),
    );
  }

  void onAddBenePressed() {
    _navigationProvider.push(
      const AddBeneficiaryNavigationConfig(
        country: PaymentCountry.localCountry,
        paymentType: PaymentType.local,
      ),
    );
  }

  void onBeneficiarySelected({
    required BuildContext context,
    required CrossborderInternationalBeneficiary beneficiary,
  }) {
    if (beneficiary.isUnavailable) {
      context.dispatchNotification(
        OpenBeneficiaryDetailsDrawerNotification(
          beneficiary: beneficiary,
          paymentType: PaymentType.local,
        ),
      );
      return;
    }

    navigateToNewPayment(beneficiary);
  }

  void onViewAll({
    required BuildContext context,
  }) {
    context.dispatchNotification(
      const AllBeneficiariesDrawerNotification(
        paymentType: PaymentType.local,
      ),
    );
  }

  void _bindBeneficiariesStream({required bool recent}) {
    _interactor
        .getBeneficiariesStream(
          recent: recent,
          forceRefresh: true,
          paymentType: PaymentType.local,
        )
        .logError(_logger)
        .doOnListen(() => safeEmit(const LocalDashboardState.loading()))
        .withError<Exception>(
          (e) => safeEmit(
            LocalDashboardState.error(
              subtitle: _localization.localBeneficiariesFetchingException,
              tryAgain: init,
            ),
          ),
        )
        .doOnData(
          (event) => safeEmit(
            LocalDashboardState.loaded(
              beneficiaries: event,
              isAddBeneficiaryFlowEnabled: _isAddBeneficiaryFlowEnabled,
            ),
          ),
        )
        .listenSafe(this);
  }

  String _logMsg(String message) => _logPrefix + message;

  @override
  String toString() => 'LocalDashboardCubit';
}
