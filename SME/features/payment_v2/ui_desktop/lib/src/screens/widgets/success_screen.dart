import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';

class SuccessScreenView extends StatelessWidget {
  final SuccessScreenModel model;

  const SuccessScreenView({
    required this.model,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SuccessScreen(
      model: model,
      onClose: () => Navigator.of(context).maybePop(),
      onConfirmCtaClick: () => Navigator.of(context).maybePop(),
    );
  }
}
