// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_requirements_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TransferRequirementModel {
  String get name => throw _privateConstructorUsedError;
  List<TransferRequirementInfoModel> get group =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $TransferRequirementModelCopyWith<TransferRequirementModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransferRequirementModelCopyWith<$Res> {
  factory $TransferRequirementModelCopyWith(TransferRequirementModel value,
          $Res Function(TransferRequirementModel) then) =
      _$TransferRequirementModelCopyWithImpl<$Res, TransferRequirementModel>;
  @useResult
  $Res call({String name, List<TransferRequirementInfoModel> group});
}

/// @nodoc
class _$TransferRequirementModelCopyWithImpl<$Res,
        $Val extends TransferRequirementModel>
    implements $TransferRequirementModelCopyWith<$Res> {
  _$TransferRequirementModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? group = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      group: null == group
          ? _value.group
          : group // ignore: cast_nullable_to_non_nullable
              as List<TransferRequirementInfoModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TransferRequirementsImplCopyWith<$Res>
    implements $TransferRequirementModelCopyWith<$Res> {
  factory _$$TransferRequirementsImplCopyWith(_$TransferRequirementsImpl value,
          $Res Function(_$TransferRequirementsImpl) then) =
      __$$TransferRequirementsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String name, List<TransferRequirementInfoModel> group});
}

/// @nodoc
class __$$TransferRequirementsImplCopyWithImpl<$Res>
    extends _$TransferRequirementModelCopyWithImpl<$Res,
        _$TransferRequirementsImpl>
    implements _$$TransferRequirementsImplCopyWith<$Res> {
  __$$TransferRequirementsImplCopyWithImpl(_$TransferRequirementsImpl _value,
      $Res Function(_$TransferRequirementsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? group = null,
  }) {
    return _then(_$TransferRequirementsImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      group: null == group
          ? _value._group
          : group // ignore: cast_nullable_to_non_nullable
              as List<TransferRequirementInfoModel>,
    ));
  }
}

/// @nodoc

class _$TransferRequirementsImpl extends _TransferRequirements {
  const _$TransferRequirementsImpl(
      {required this.name,
      required final List<TransferRequirementInfoModel> group})
      : _group = group,
        super._();

  @override
  final String name;
  final List<TransferRequirementInfoModel> _group;
  @override
  List<TransferRequirementInfoModel> get group {
    if (_group is EqualUnmodifiableListView) return _group;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_group);
  }

  @override
  String toString() {
    return 'TransferRequirementModel(name: $name, group: $group)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransferRequirementsImpl &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._group, _group));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, name, const DeepCollectionEquality().hash(_group));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TransferRequirementsImplCopyWith<_$TransferRequirementsImpl>
      get copyWith =>
          __$$TransferRequirementsImplCopyWithImpl<_$TransferRequirementsImpl>(
              this, _$identity);
}

abstract class _TransferRequirements extends TransferRequirementModel {
  const factory _TransferRequirements(
          {required final String name,
          required final List<TransferRequirementInfoModel> group}) =
      _$TransferRequirementsImpl;
  const _TransferRequirements._() : super._();

  @override
  String get name;
  @override
  List<TransferRequirementInfoModel> get group;
  @override
  @JsonKey(ignore: true)
  _$$TransferRequirementsImplCopyWith<_$TransferRequirementsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$TransferRequirementInfoModel {
  String get key => throw _privateConstructorUsedError;
  TransferRequirementInfo get requirement => throw _privateConstructorUsedError;
  String? get value => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String key, TransferRequirementInfo requirement, String? value)
        text,
    required TResult Function(
            String key, TransferRequirementInfo requirement, String? value)
        info,
    required TResult Function(
            String key, TransferRequirementInfo requirement, String? value)
        dropdown,
    required TResult Function(String key, Country country,
            TransferRequirementInfo requirement, String? value)
        phonenumber,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String key, TransferRequirementInfo requirement, String? value)?
        text,
    TResult? Function(
            String key, TransferRequirementInfo requirement, String? value)?
        info,
    TResult? Function(
            String key, TransferRequirementInfo requirement, String? value)?
        dropdown,
    TResult? Function(String key, Country country,
            TransferRequirementInfo requirement, String? value)?
        phonenumber,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String key, TransferRequirementInfo requirement, String? value)?
        text,
    TResult Function(
            String key, TransferRequirementInfo requirement, String? value)?
        info,
    TResult Function(
            String key, TransferRequirementInfo requirement, String? value)?
        dropdown,
    TResult Function(String key, Country country,
            TransferRequirementInfo requirement, String? value)?
        phonenumber,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(TransferRequirementInfoTextModel value) text,
    required TResult Function(TransferRequirementInfoInfoModel value) info,
    required TResult Function(TransferRequirementInfoDropdownModel value)
        dropdown,
    required TResult Function(TransferRequirementInfoPhonenumberModel value)
        phonenumber,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(TransferRequirementInfoTextModel value)? text,
    TResult? Function(TransferRequirementInfoInfoModel value)? info,
    TResult? Function(TransferRequirementInfoDropdownModel value)? dropdown,
    TResult? Function(TransferRequirementInfoPhonenumberModel value)?
        phonenumber,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(TransferRequirementInfoTextModel value)? text,
    TResult Function(TransferRequirementInfoInfoModel value)? info,
    TResult Function(TransferRequirementInfoDropdownModel value)? dropdown,
    TResult Function(TransferRequirementInfoPhonenumberModel value)?
        phonenumber,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $TransferRequirementInfoModelCopyWith<TransferRequirementInfoModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransferRequirementInfoModelCopyWith<$Res> {
  factory $TransferRequirementInfoModelCopyWith(
          TransferRequirementInfoModel value,
          $Res Function(TransferRequirementInfoModel) then) =
      _$TransferRequirementInfoModelCopyWithImpl<$Res,
          TransferRequirementInfoModel>;
  @useResult
  $Res call({String key, TransferRequirementInfo requirement, String? value});

  $TransferRequirementInfoCopyWith<$Res> get requirement;
}

/// @nodoc
class _$TransferRequirementInfoModelCopyWithImpl<$Res,
        $Val extends TransferRequirementInfoModel>
    implements $TransferRequirementInfoModelCopyWith<$Res> {
  _$TransferRequirementInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = null,
    Object? requirement = null,
    Object? value = freezed,
  }) {
    return _then(_value.copyWith(
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      requirement: null == requirement
          ? _value.requirement
          : requirement // ignore: cast_nullable_to_non_nullable
              as TransferRequirementInfo,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $TransferRequirementInfoCopyWith<$Res> get requirement {
    return $TransferRequirementInfoCopyWith<$Res>(_value.requirement, (value) {
      return _then(_value.copyWith(requirement: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TransferRequirementInfoTextModelImplCopyWith<$Res>
    implements $TransferRequirementInfoModelCopyWith<$Res> {
  factory _$$TransferRequirementInfoTextModelImplCopyWith(
          _$TransferRequirementInfoTextModelImpl value,
          $Res Function(_$TransferRequirementInfoTextModelImpl) then) =
      __$$TransferRequirementInfoTextModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String key, TransferRequirementInfo requirement, String? value});

  @override
  $TransferRequirementInfoCopyWith<$Res> get requirement;
}

/// @nodoc
class __$$TransferRequirementInfoTextModelImplCopyWithImpl<$Res>
    extends _$TransferRequirementInfoModelCopyWithImpl<$Res,
        _$TransferRequirementInfoTextModelImpl>
    implements _$$TransferRequirementInfoTextModelImplCopyWith<$Res> {
  __$$TransferRequirementInfoTextModelImplCopyWithImpl(
      _$TransferRequirementInfoTextModelImpl _value,
      $Res Function(_$TransferRequirementInfoTextModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = null,
    Object? requirement = null,
    Object? value = freezed,
  }) {
    return _then(_$TransferRequirementInfoTextModelImpl(
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      requirement: null == requirement
          ? _value.requirement
          : requirement // ignore: cast_nullable_to_non_nullable
              as TransferRequirementInfo,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$TransferRequirementInfoTextModelImpl
    extends TransferRequirementInfoTextModel {
  const _$TransferRequirementInfoTextModelImpl(
      {required this.key, required this.requirement, required this.value})
      : super._();

  @override
  final String key;
  @override
  final TransferRequirementInfo requirement;
  @override
  final String? value;

  @override
  String toString() {
    return 'TransferRequirementInfoModel.text(key: $key, requirement: $requirement, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransferRequirementInfoTextModelImpl &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.requirement, requirement) ||
                other.requirement == requirement) &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, key, requirement, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TransferRequirementInfoTextModelImplCopyWith<
          _$TransferRequirementInfoTextModelImpl>
      get copyWith => __$$TransferRequirementInfoTextModelImplCopyWithImpl<
          _$TransferRequirementInfoTextModelImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String key, TransferRequirementInfo requirement, String? value)
        text,
    required TResult Function(
            String key, TransferRequirementInfo requirement, String? value)
        info,
    required TResult Function(
            String key, TransferRequirementInfo requirement, String? value)
        dropdown,
    required TResult Function(String key, Country country,
            TransferRequirementInfo requirement, String? value)
        phonenumber,
  }) {
    return text(key, requirement, value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String key, TransferRequirementInfo requirement, String? value)?
        text,
    TResult? Function(
            String key, TransferRequirementInfo requirement, String? value)?
        info,
    TResult? Function(
            String key, TransferRequirementInfo requirement, String? value)?
        dropdown,
    TResult? Function(String key, Country country,
            TransferRequirementInfo requirement, String? value)?
        phonenumber,
  }) {
    return text?.call(key, requirement, value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String key, TransferRequirementInfo requirement, String? value)?
        text,
    TResult Function(
            String key, TransferRequirementInfo requirement, String? value)?
        info,
    TResult Function(
            String key, TransferRequirementInfo requirement, String? value)?
        dropdown,
    TResult Function(String key, Country country,
            TransferRequirementInfo requirement, String? value)?
        phonenumber,
    required TResult orElse(),
  }) {
    if (text != null) {
      return text(key, requirement, value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(TransferRequirementInfoTextModel value) text,
    required TResult Function(TransferRequirementInfoInfoModel value) info,
    required TResult Function(TransferRequirementInfoDropdownModel value)
        dropdown,
    required TResult Function(TransferRequirementInfoPhonenumberModel value)
        phonenumber,
  }) {
    return text(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(TransferRequirementInfoTextModel value)? text,
    TResult? Function(TransferRequirementInfoInfoModel value)? info,
    TResult? Function(TransferRequirementInfoDropdownModel value)? dropdown,
    TResult? Function(TransferRequirementInfoPhonenumberModel value)?
        phonenumber,
  }) {
    return text?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(TransferRequirementInfoTextModel value)? text,
    TResult Function(TransferRequirementInfoInfoModel value)? info,
    TResult Function(TransferRequirementInfoDropdownModel value)? dropdown,
    TResult Function(TransferRequirementInfoPhonenumberModel value)?
        phonenumber,
    required TResult orElse(),
  }) {
    if (text != null) {
      return text(this);
    }
    return orElse();
  }
}

abstract class TransferRequirementInfoTextModel
    extends TransferRequirementInfoModel {
  const factory TransferRequirementInfoTextModel(
      {required final String key,
      required final TransferRequirementInfo requirement,
      required final String? value}) = _$TransferRequirementInfoTextModelImpl;
  const TransferRequirementInfoTextModel._() : super._();

  @override
  String get key;
  @override
  TransferRequirementInfo get requirement;
  @override
  String? get value;
  @override
  @JsonKey(ignore: true)
  _$$TransferRequirementInfoTextModelImplCopyWith<
          _$TransferRequirementInfoTextModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TransferRequirementInfoInfoModelImplCopyWith<$Res>
    implements $TransferRequirementInfoModelCopyWith<$Res> {
  factory _$$TransferRequirementInfoInfoModelImplCopyWith(
          _$TransferRequirementInfoInfoModelImpl value,
          $Res Function(_$TransferRequirementInfoInfoModelImpl) then) =
      __$$TransferRequirementInfoInfoModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String key, TransferRequirementInfo requirement, String? value});

  @override
  $TransferRequirementInfoCopyWith<$Res> get requirement;
}

/// @nodoc
class __$$TransferRequirementInfoInfoModelImplCopyWithImpl<$Res>
    extends _$TransferRequirementInfoModelCopyWithImpl<$Res,
        _$TransferRequirementInfoInfoModelImpl>
    implements _$$TransferRequirementInfoInfoModelImplCopyWith<$Res> {
  __$$TransferRequirementInfoInfoModelImplCopyWithImpl(
      _$TransferRequirementInfoInfoModelImpl _value,
      $Res Function(_$TransferRequirementInfoInfoModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = null,
    Object? requirement = null,
    Object? value = freezed,
  }) {
    return _then(_$TransferRequirementInfoInfoModelImpl(
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      requirement: null == requirement
          ? _value.requirement
          : requirement // ignore: cast_nullable_to_non_nullable
              as TransferRequirementInfo,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$TransferRequirementInfoInfoModelImpl
    extends TransferRequirementInfoInfoModel {
  const _$TransferRequirementInfoInfoModelImpl(
      {required this.key, required this.requirement, required this.value})
      : super._();

  @override
  final String key;
  @override
  final TransferRequirementInfo requirement;
  @override
  final String? value;

  @override
  String toString() {
    return 'TransferRequirementInfoModel.info(key: $key, requirement: $requirement, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransferRequirementInfoInfoModelImpl &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.requirement, requirement) ||
                other.requirement == requirement) &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, key, requirement, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TransferRequirementInfoInfoModelImplCopyWith<
          _$TransferRequirementInfoInfoModelImpl>
      get copyWith => __$$TransferRequirementInfoInfoModelImplCopyWithImpl<
          _$TransferRequirementInfoInfoModelImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String key, TransferRequirementInfo requirement, String? value)
        text,
    required TResult Function(
            String key, TransferRequirementInfo requirement, String? value)
        info,
    required TResult Function(
            String key, TransferRequirementInfo requirement, String? value)
        dropdown,
    required TResult Function(String key, Country country,
            TransferRequirementInfo requirement, String? value)
        phonenumber,
  }) {
    return info(key, requirement, value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String key, TransferRequirementInfo requirement, String? value)?
        text,
    TResult? Function(
            String key, TransferRequirementInfo requirement, String? value)?
        info,
    TResult? Function(
            String key, TransferRequirementInfo requirement, String? value)?
        dropdown,
    TResult? Function(String key, Country country,
            TransferRequirementInfo requirement, String? value)?
        phonenumber,
  }) {
    return info?.call(key, requirement, value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String key, TransferRequirementInfo requirement, String? value)?
        text,
    TResult Function(
            String key, TransferRequirementInfo requirement, String? value)?
        info,
    TResult Function(
            String key, TransferRequirementInfo requirement, String? value)?
        dropdown,
    TResult Function(String key, Country country,
            TransferRequirementInfo requirement, String? value)?
        phonenumber,
    required TResult orElse(),
  }) {
    if (info != null) {
      return info(key, requirement, value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(TransferRequirementInfoTextModel value) text,
    required TResult Function(TransferRequirementInfoInfoModel value) info,
    required TResult Function(TransferRequirementInfoDropdownModel value)
        dropdown,
    required TResult Function(TransferRequirementInfoPhonenumberModel value)
        phonenumber,
  }) {
    return info(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(TransferRequirementInfoTextModel value)? text,
    TResult? Function(TransferRequirementInfoInfoModel value)? info,
    TResult? Function(TransferRequirementInfoDropdownModel value)? dropdown,
    TResult? Function(TransferRequirementInfoPhonenumberModel value)?
        phonenumber,
  }) {
    return info?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(TransferRequirementInfoTextModel value)? text,
    TResult Function(TransferRequirementInfoInfoModel value)? info,
    TResult Function(TransferRequirementInfoDropdownModel value)? dropdown,
    TResult Function(TransferRequirementInfoPhonenumberModel value)?
        phonenumber,
    required TResult orElse(),
  }) {
    if (info != null) {
      return info(this);
    }
    return orElse();
  }
}

abstract class TransferRequirementInfoInfoModel
    extends TransferRequirementInfoModel {
  const factory TransferRequirementInfoInfoModel(
      {required final String key,
      required final TransferRequirementInfo requirement,
      required final String? value}) = _$TransferRequirementInfoInfoModelImpl;
  const TransferRequirementInfoInfoModel._() : super._();

  @override
  String get key;
  @override
  TransferRequirementInfo get requirement;
  @override
  String? get value;
  @override
  @JsonKey(ignore: true)
  _$$TransferRequirementInfoInfoModelImplCopyWith<
          _$TransferRequirementInfoInfoModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TransferRequirementInfoDropdownModelImplCopyWith<$Res>
    implements $TransferRequirementInfoModelCopyWith<$Res> {
  factory _$$TransferRequirementInfoDropdownModelImplCopyWith(
          _$TransferRequirementInfoDropdownModelImpl value,
          $Res Function(_$TransferRequirementInfoDropdownModelImpl) then) =
      __$$TransferRequirementInfoDropdownModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String key, TransferRequirementInfo requirement, String? value});

  @override
  $TransferRequirementInfoCopyWith<$Res> get requirement;
}

/// @nodoc
class __$$TransferRequirementInfoDropdownModelImplCopyWithImpl<$Res>
    extends _$TransferRequirementInfoModelCopyWithImpl<$Res,
        _$TransferRequirementInfoDropdownModelImpl>
    implements _$$TransferRequirementInfoDropdownModelImplCopyWith<$Res> {
  __$$TransferRequirementInfoDropdownModelImplCopyWithImpl(
      _$TransferRequirementInfoDropdownModelImpl _value,
      $Res Function(_$TransferRequirementInfoDropdownModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = null,
    Object? requirement = null,
    Object? value = freezed,
  }) {
    return _then(_$TransferRequirementInfoDropdownModelImpl(
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      requirement: null == requirement
          ? _value.requirement
          : requirement // ignore: cast_nullable_to_non_nullable
              as TransferRequirementInfo,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$TransferRequirementInfoDropdownModelImpl
    extends TransferRequirementInfoDropdownModel {
  const _$TransferRequirementInfoDropdownModelImpl(
      {required this.key, required this.requirement, required this.value})
      : super._();

  @override
  final String key;
  @override
  final TransferRequirementInfo requirement;
  @override
  final String? value;

  @override
  String toString() {
    return 'TransferRequirementInfoModel.dropdown(key: $key, requirement: $requirement, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransferRequirementInfoDropdownModelImpl &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.requirement, requirement) ||
                other.requirement == requirement) &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, key, requirement, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TransferRequirementInfoDropdownModelImplCopyWith<
          _$TransferRequirementInfoDropdownModelImpl>
      get copyWith => __$$TransferRequirementInfoDropdownModelImplCopyWithImpl<
          _$TransferRequirementInfoDropdownModelImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String key, TransferRequirementInfo requirement, String? value)
        text,
    required TResult Function(
            String key, TransferRequirementInfo requirement, String? value)
        info,
    required TResult Function(
            String key, TransferRequirementInfo requirement, String? value)
        dropdown,
    required TResult Function(String key, Country country,
            TransferRequirementInfo requirement, String? value)
        phonenumber,
  }) {
    return dropdown(key, requirement, value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String key, TransferRequirementInfo requirement, String? value)?
        text,
    TResult? Function(
            String key, TransferRequirementInfo requirement, String? value)?
        info,
    TResult? Function(
            String key, TransferRequirementInfo requirement, String? value)?
        dropdown,
    TResult? Function(String key, Country country,
            TransferRequirementInfo requirement, String? value)?
        phonenumber,
  }) {
    return dropdown?.call(key, requirement, value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String key, TransferRequirementInfo requirement, String? value)?
        text,
    TResult Function(
            String key, TransferRequirementInfo requirement, String? value)?
        info,
    TResult Function(
            String key, TransferRequirementInfo requirement, String? value)?
        dropdown,
    TResult Function(String key, Country country,
            TransferRequirementInfo requirement, String? value)?
        phonenumber,
    required TResult orElse(),
  }) {
    if (dropdown != null) {
      return dropdown(key, requirement, value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(TransferRequirementInfoTextModel value) text,
    required TResult Function(TransferRequirementInfoInfoModel value) info,
    required TResult Function(TransferRequirementInfoDropdownModel value)
        dropdown,
    required TResult Function(TransferRequirementInfoPhonenumberModel value)
        phonenumber,
  }) {
    return dropdown(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(TransferRequirementInfoTextModel value)? text,
    TResult? Function(TransferRequirementInfoInfoModel value)? info,
    TResult? Function(TransferRequirementInfoDropdownModel value)? dropdown,
    TResult? Function(TransferRequirementInfoPhonenumberModel value)?
        phonenumber,
  }) {
    return dropdown?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(TransferRequirementInfoTextModel value)? text,
    TResult Function(TransferRequirementInfoInfoModel value)? info,
    TResult Function(TransferRequirementInfoDropdownModel value)? dropdown,
    TResult Function(TransferRequirementInfoPhonenumberModel value)?
        phonenumber,
    required TResult orElse(),
  }) {
    if (dropdown != null) {
      return dropdown(this);
    }
    return orElse();
  }
}

abstract class TransferRequirementInfoDropdownModel
    extends TransferRequirementInfoModel {
  const factory TransferRequirementInfoDropdownModel(
          {required final String key,
          required final TransferRequirementInfo requirement,
          required final String? value}) =
      _$TransferRequirementInfoDropdownModelImpl;
  const TransferRequirementInfoDropdownModel._() : super._();

  @override
  String get key;
  @override
  TransferRequirementInfo get requirement;
  @override
  String? get value;
  @override
  @JsonKey(ignore: true)
  _$$TransferRequirementInfoDropdownModelImplCopyWith<
          _$TransferRequirementInfoDropdownModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TransferRequirementInfoPhonenumberModelImplCopyWith<$Res>
    implements $TransferRequirementInfoModelCopyWith<$Res> {
  factory _$$TransferRequirementInfoPhonenumberModelImplCopyWith(
          _$TransferRequirementInfoPhonenumberModelImpl value,
          $Res Function(_$TransferRequirementInfoPhonenumberModelImpl) then) =
      __$$TransferRequirementInfoPhonenumberModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String key,
      Country country,
      TransferRequirementInfo requirement,
      String? value});

  @override
  $TransferRequirementInfoCopyWith<$Res> get requirement;
}

/// @nodoc
class __$$TransferRequirementInfoPhonenumberModelImplCopyWithImpl<$Res>
    extends _$TransferRequirementInfoModelCopyWithImpl<$Res,
        _$TransferRequirementInfoPhonenumberModelImpl>
    implements _$$TransferRequirementInfoPhonenumberModelImplCopyWith<$Res> {
  __$$TransferRequirementInfoPhonenumberModelImplCopyWithImpl(
      _$TransferRequirementInfoPhonenumberModelImpl _value,
      $Res Function(_$TransferRequirementInfoPhonenumberModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = null,
    Object? country = null,
    Object? requirement = null,
    Object? value = freezed,
  }) {
    return _then(_$TransferRequirementInfoPhonenumberModelImpl(
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      country: null == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as Country,
      requirement: null == requirement
          ? _value.requirement
          : requirement // ignore: cast_nullable_to_non_nullable
              as TransferRequirementInfo,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$TransferRequirementInfoPhonenumberModelImpl
    extends TransferRequirementInfoPhonenumberModel {
  const _$TransferRequirementInfoPhonenumberModelImpl(
      {required this.key,
      required this.country,
      required this.requirement,
      required this.value})
      : super._();

  @override
  final String key;
  @override
  final Country country;
  @override
  final TransferRequirementInfo requirement;
  @override
  final String? value;

  @override
  String toString() {
    return 'TransferRequirementInfoModel.phonenumber(key: $key, country: $country, requirement: $requirement, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransferRequirementInfoPhonenumberModelImpl &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.requirement, requirement) ||
                other.requirement == requirement) &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, key, country, requirement, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TransferRequirementInfoPhonenumberModelImplCopyWith<
          _$TransferRequirementInfoPhonenumberModelImpl>
      get copyWith =>
          __$$TransferRequirementInfoPhonenumberModelImplCopyWithImpl<
              _$TransferRequirementInfoPhonenumberModelImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String key, TransferRequirementInfo requirement, String? value)
        text,
    required TResult Function(
            String key, TransferRequirementInfo requirement, String? value)
        info,
    required TResult Function(
            String key, TransferRequirementInfo requirement, String? value)
        dropdown,
    required TResult Function(String key, Country country,
            TransferRequirementInfo requirement, String? value)
        phonenumber,
  }) {
    return phonenumber(key, country, requirement, value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String key, TransferRequirementInfo requirement, String? value)?
        text,
    TResult? Function(
            String key, TransferRequirementInfo requirement, String? value)?
        info,
    TResult? Function(
            String key, TransferRequirementInfo requirement, String? value)?
        dropdown,
    TResult? Function(String key, Country country,
            TransferRequirementInfo requirement, String? value)?
        phonenumber,
  }) {
    return phonenumber?.call(key, country, requirement, value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String key, TransferRequirementInfo requirement, String? value)?
        text,
    TResult Function(
            String key, TransferRequirementInfo requirement, String? value)?
        info,
    TResult Function(
            String key, TransferRequirementInfo requirement, String? value)?
        dropdown,
    TResult Function(String key, Country country,
            TransferRequirementInfo requirement, String? value)?
        phonenumber,
    required TResult orElse(),
  }) {
    if (phonenumber != null) {
      return phonenumber(key, country, requirement, value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(TransferRequirementInfoTextModel value) text,
    required TResult Function(TransferRequirementInfoInfoModel value) info,
    required TResult Function(TransferRequirementInfoDropdownModel value)
        dropdown,
    required TResult Function(TransferRequirementInfoPhonenumberModel value)
        phonenumber,
  }) {
    return phonenumber(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(TransferRequirementInfoTextModel value)? text,
    TResult? Function(TransferRequirementInfoInfoModel value)? info,
    TResult? Function(TransferRequirementInfoDropdownModel value)? dropdown,
    TResult? Function(TransferRequirementInfoPhonenumberModel value)?
        phonenumber,
  }) {
    return phonenumber?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(TransferRequirementInfoTextModel value)? text,
    TResult Function(TransferRequirementInfoInfoModel value)? info,
    TResult Function(TransferRequirementInfoDropdownModel value)? dropdown,
    TResult Function(TransferRequirementInfoPhonenumberModel value)?
        phonenumber,
    required TResult orElse(),
  }) {
    if (phonenumber != null) {
      return phonenumber(this);
    }
    return orElse();
  }
}

abstract class TransferRequirementInfoPhonenumberModel
    extends TransferRequirementInfoModel {
  const factory TransferRequirementInfoPhonenumberModel(
          {required final String key,
          required final Country country,
          required final TransferRequirementInfo requirement,
          required final String? value}) =
      _$TransferRequirementInfoPhonenumberModelImpl;
  const TransferRequirementInfoPhonenumberModel._() : super._();

  @override
  String get key;
  Country get country;
  @override
  TransferRequirementInfo get requirement;
  @override
  String? get value;
  @override
  @JsonKey(ignore: true)
  _$$TransferRequirementInfoPhonenumberModelImplCopyWith<
          _$TransferRequirementInfoPhonenumberModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
