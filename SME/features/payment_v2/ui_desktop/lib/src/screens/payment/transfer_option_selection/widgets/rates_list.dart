import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment_v2_ui_desktop/src/l10n/payment_ui_desktop_localizations.g.dart';
import 'package:payment_v2_ui_desktop/src/screens/payment/transfer_option_selection/bloc/transfer_option_selection_cubit.dart';
import 'package:payment_v2_ui_desktop/src/screens/payment/transfer_option_selection/bloc/transfer_option_selection_state.dart';
import 'package:payment_v2_ui_desktop/src/screens/widgets/shimmer.dart';
import 'package:payment_v2_ui_desktop/src/utils/extensions.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';

class RatesList extends StatelessWidget {
  final OptionsState data;
  final FeeChargingType feeChargingType;
  final PaymentCountry country;

  const RatesList({
    required this.data,
    required this.feeChargingType,
    required this.country,
    super.key,
  });

  String? getFeeByType(FeeDetails feeDetails, TransferFeeType feeType) {
    if (feeDetails.transferFees.isEmpty) return null;

    return feeDetails.transferFees
        .reduce((fee1, fee2) => fee1.type == feeType ? fee1 : fee2)
        .amount
        .toCodeOnRightFormat();
  }

  @override
  Widget build(BuildContext context) {
    final localization = PaymentUILocalizations.of(context);
    final bloc = BlocProvider.of<TransferOptionSelectionCubit>(context);
    final showSelection = data.options.length > 1;

    return Column(
      children: data.options.mapIndexed(
        (index, option) {
          final isSelected =
              showSelection ? option == data.selectedOption : null;

          final isLoading = index == data.loadingOptionIdx;

          final feeOptions = option.allowedFeeTypes
              .map((type) {
                final wioFee = getFeeByType(
                  option.feeDetails,
                  TransferFeeType.wio,
                );
                final wioCorrespondentFee = getFeeByType(
                  option.feeDetails,
                  TransferFeeType.wioCorrespondentBank,
                );
                if (wioFee == null || wioCorrespondentFee == null) return null;

                return type.feePaymentOption(
                  l10n: localization,
                  customerFee: wioFee,
                  correspondentBankFee: wioCorrespondentFee,
                  isSelected: feeChargingType == type,
                );
              })
              .whereNotNull()
              .toList();

          return Padding(
            padding: const EdgeInsets.only(top: 16),
            child: isLoading
                ? const ShimmerContainer(size: Size(double.maxFinite, 225))
                : RateCard(
                    model: RateCardModel(
                      system: option.providerType.name.equalsIgnoreCase('swift')
                          ? PaymentSystem.swift
                          : PaymentSystem.wise,
                      exchangeRateTitle:
                          localization.transferOptionExchangeRateLabel,
                      exchangeRate: option.exchangeRateString,
                      estimatedTimeTitle:
                          localization.transferOptionEstimatedTimeLabel,
                      estimatedTime: option.estimatedDeliveryInterval,
                      hintValues: option.feeDetails.hintValue(localization),
                      transferFeeTitle: localization.transferFeeLabel,
                      transferFee: option.feeDetails.total,
                      feesPaymentTitle: localization.feeChargingTypeTitle,
                      feesPaymentOptions: feeOptions,
                      onFeePaymentOptionChange: (fee) => bloc.onUpdateOption(
                        option,
                        option.allowedFeeTypes.elementAt(
                          feeOptions.indexOf(fee),
                        ),
                      ),
                      youWillPayTitle:
                          localization.transferOptionYouWillPayLabel,
                      youWillPay: option.chargedSourceAmount,
                      isSelected: isSelected,
                      errorMessage: option.errorMessage,
                    ),
                    onValueChanged: (value) =>
                        value ? bloc.onTransferOptionSelect(option) : null,
                  ),
          );
        },
      ).toList(),
    );
  }
}
