import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_feature_country_api/country_feature_api.dart';

part 'transfer_requirements_model.freezed.dart';

@freezed
class TransferRequirementModel with _$TransferRequirementModel {
  const factory TransferRequirementModel({
    required String name,
    required List<TransferRequirementInfoModel> group,
  }) = _TransferRequirements;

  const TransferRequirementModel._();
}

@freezed
class TransferRequirementInfoModel with _$TransferRequirementInfoModel {
  const TransferRequirementInfoModel._();

  const factory TransferRequirementInfoModel.text({
    required String key,
    required TransferRequirementInfo requirement,
    required String? value,
  }) = TransferRequirementInfoTextModel;

  const factory TransferRequirementInfoModel.info({
    required String key,
    required TransferRequirementInfo requirement,
    required String? value,
  }) = TransferRequirementInfoInfoModel;

  const factory TransferRequirementInfoModel.dropdown({
    required String key,
    required TransferRequirementInfo requirement,
    required String? value,
  }) = TransferRequirementInfoDropdownModel;

  const factory TransferRequirementInfoModel.phonenumber({
    required String key,
    required Country country,
    required TransferRequirementInfo requirement,
    required String? value,
  }) = TransferRequirementInfoPhonenumberModel;
}
