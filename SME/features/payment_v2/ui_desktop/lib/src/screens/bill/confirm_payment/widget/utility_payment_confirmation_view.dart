import 'package:feature_payment_v2_api/feature_payment_v2_api.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment_v2_ui_desktop/src/l10n/payment_ui_desktop_localizations.g.dart';
import 'package:payment_v2_ui_desktop/src/screens/bill/common_widget/utility_payment_layout.dart';
import 'package:payment_v2_ui_desktop/src/screens/bill/common_widget/utility_payment_left_column.dart';
import 'package:payment_v2_ui_desktop/src/screens/bill/confirm_payment/bloc/utility_payment_confirmation_cubit.dart';
import 'package:payment_v2_ui_desktop/src/screens/bill/confirm_payment/bloc/utility_payment_confirmation_state.dart';
import 'package:payment_v2_ui_desktop/src/screens/bill/confirm_payment/widget/utility_payment_telecom_confirm_transaction_right_column.dart';
import 'package:payment_v2_ui_desktop/src/screens/bill/utility_payment_success/widget/utility_payment_success_view.dart';
import 'package:payment_v2_ui_desktop/src/screens/dashboard/widgets/error_content.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_app_core_api/money.dart';

class UtilityPaymentConfirmationView extends StatelessWidget {
  final VendorDetails vendorDetails;
  final Money amountToBePaid;
  final BalanceDetails balanceDetails;
  final BillerServiceType billerServiceType;
  final String? beneficiaryId;
  final String? pin;
  final String? beneficiaryName;

  const UtilityPaymentConfirmationView({
    Key? key,
    required this.vendorDetails,
    required this.amountToBePaid,
    required this.balanceDetails,
    required this.billerServiceType,
    this.beneficiaryId,
    this.pin,
    this.beneficiaryName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final cubit = BlocProvider.of<UtilityPaymentConfirmationCubit>(context);
    final localization = PaymentUILocalizations.of(context);

    return BlocBuilder<UtilityPaymentConfirmationCubit,
        UtilityPaymentConfirmationState>(
      builder: (context, state) {
        return state.maybeMap(
          orElse: () => const SizedBox.shrink(),
          loaded: (e) => UtilityPaymentLayout.darkTheme(
            leftColumnWidget: UtilityPaymentLeftColumn.darkTheme(
              pictogram: DesktopPictogram(
                const DesktopPictogramModel(
                  size: DesktopPictogramSize.l,
                  color: CompanyColorPointer.primary2,
                  icon: CompanyPictogramPointer.metaphors_see,
                ),
              ),
              richText: CompanyRichText(
                CompanyRichTextModel(
                  text: localization
                      .utilityPaymentConfirmationLeftColumnTitleText,
                  normalStyle: CompanyTextStylePointer.h2,
                  accentStyle: CompanyTextStylePointer.h2,
                  normalTextColor: CompanyColorPointer.secondary2,
                  accentTextColor: CompanyColorPointer.primary2,
                  highlightedTextModels: [
                    HighlightedTextModel(
                      localization.utilityPaymentConfirmationHighlightedText,
                    ),
                  ],
                ),
              ),
            ),
            squareButtonArrowOnClick: () async => _goBack(
              isPaymentInitiated: e.isPaymentInitiated,
              cubit: cubit,
            ),
            rightColumnChild: UtilityPaymentConfirmTransactionRightColumn(
              vendorDetails: vendorDetails,
              balanceDetails: balanceDetails,
              amountToBePaid: amountToBePaid,
              serviceType: billerServiceType,
              beneficiaryId: beneficiaryId,
              pin: pin,
              beneficiaryName: beneficiaryName,
            ),
            headerTitle: localization.utilityPaymentConfirmationScreenTitle,
            onHeaderCloseButtonClick: () async => cubit.closeScreen(),
          ),
          error: (e) => ErrorContent(
            subtitle: localization.smthWentWrong,
            tryAgainCallback: e.tryAgain,
          ),
          success: (e) => UtilityPaymentSuccessView(
            amountPaid: amountToBePaid,
            details: vendorDetails,
            transferReference: balanceDetails.transactionId,
          ),
        );
      },
    );
  }

  void _goBack({
    required UtilityPaymentConfirmationCubit cubit,
    bool isPaymentInitiated = false,
  }) {
    if (!isPaymentInitiated) {
      cubit.goBack();
    }
  }
}
