import 'package:account_feature_api/account_feature_api.dart';
import 'package:feature_payment_v2_api/feature_payment_v2_api.dart';
import 'package:logging_api/logging.dart';
import 'package:payment_v2_ui_desktop/src/l10n/payment_ui_desktop_localizations.g.dart';
import 'package:payment_v2_ui_desktop/src/navigation/bill/bill_confirmation_navigation_config.dart';
import 'package:payment_v2_ui_desktop/src/screens/bill/analytics/utility_payment_analytics.dart';
import 'package:payment_v2_ui_desktop/src/screens/bill/extensions/utility_payment_extensions.dart';
import 'package:payment_v2_ui_desktop/src/screens/bill/utility_payment_beneficiary_details/bloc/utility_payment_beneficiary_details_state.dart';
import 'package:sme_core_ui_desktop/index.dart';
import 'package:ui/cubit/base_cubit.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';

// ignore_for_file: avoid-late-keyword
class UtilityPaymentBeneficiaryDetailsCubit
    extends BaseCubit<UtilityPaymentBeneficiaryDetailState> {
  static const _loggerPrefix = 'UTILITY PAYMENT DETAILS:';

  final AccountInteractor _accountInteractor;
  final NavigationProvider _navigationProvider;
  final UtilityBillPaymentInteractor _utilityBillPaymentInteractor;
  final NewNotificationService _notificationService;
  final PaymentUILocalizations _localizations;
  final UtilityPaymentAnalytics _analytics;
  final Logger _logger;

  UtilityPaymentBeneficiaryDetailsCubit({
    required UtilityBillPaymentInteractor utilityBillPaymentInteractor,
    required NavigationProvider navigationProvider,
    required Logger logger,
    required AccountInteractor accountInteractor,
    required NewNotificationService notificationService,
    required PaymentUILocalizations localizations,
    required UtilityPaymentAnalytics analytics,
  })  : _logger = logger,
        _utilityBillPaymentInteractor = utilityBillPaymentInteractor,
        _accountInteractor = accountInteractor,
        _navigationProvider = navigationProvider,
        _notificationService = notificationService,
        _localizations = localizations,
        _analytics = analytics,
        super(
          const UtilityPaymentBeneficiaryDetailState.initial(),
        );

  @override
  String toString() => 'UtilityPaymentBeneficiaryDetailsCubit';

  Future<void> load({
    required VendorDetails vendorDetails,
    required UtilityBeneficiaryDetails beneficiaryDetails,
    required BillerServiceType serviceType,
  }) async {
    emit(const UtilityPaymentBeneficiaryDetailState.loading());

    try {
      final fetchBalanceInput = FetchBalanceInput(
        vendorId: vendorDetails.vendorId,
        vendorName: vendorDetails.vendorName,
        customerUniqueNumber: beneficiaryDetails.normaliseCustomerNumber,
        vendorServiceTypeId: serviceType.name,
        category: vendorDetails.isTelecom
            ? VendorCategory.telecom
            : VendorCategory.others,
        pin: beneficiaryDetails.billerPin,
      );

      late Account account;
      late BalanceDetails utilityBalance;
      await Future.wait(
        [
          _accountInteractor.getAccount().then((value) => account = value),
          _utilityBillPaymentInteractor
              .fetchBalance(input: fetchBalanceInput)
              .then((value) => utilityBalance = value),
        ],
        eagerError: true,
      );

      emit(
        UtilityPaymentBeneficiaryDetailState.loaded(
          beneficiaryStateDetails: UtilityBeneficiaryStateDetails(
            vendorDetails: vendorDetails,
            payerCurrentBalance: account.availableBalanceMoney,
            serviceType: serviceType,
            utilityBalanceDetails: utilityBalance,
            beneficiaryDetails: beneficiaryDetails,
          ),
        ),
      );
    } on Object catch (e, s) {
      _logger.error(_loggerPrefix, error: e, stackTrace: s);
      emit(
        UtilityPaymentBeneficiaryDetailState.error(
          tryAgain: () => _navigationProvider.moveToPaymentHome(),
        ),
      );
    }
  }

  void goBack() {
    _navigationProvider.goBack();
  }

  void onClearInput() {
    state.mapOrNull(
      loaded: (loadedState) {
        // To avoid unnecessary build updates
        if (loadedState.breachedLimitDetails != null) {
          emit(loadedState.copyWith(breachedLimitDetails: null));
        }
      },
    );
  }

  // ignore: long-method
  void payNow({required Money payeeBalance, required Money amountPaid}) {
    state.mapOrNull(
      loaded: (loadedState) async {
        try {
          emit(loadedState.copyWith(loadingPayNow: true));
          _analytics.initiatePayment(
            vendorName:
                loadedState.beneficiaryStateDetails.vendorDetails.vendorName,
            saveBeneficiaryOn: false,
          );
          final limitBreachedDetails =
              await _utilityBillPaymentInteractor.validateBillLimit(
            transactionAmount: amountPaid,
            billerType:
                loadedState.beneficiaryStateDetails.vendorDetails.getBillerType,
            serviceType: loadedState.beneficiaryStateDetails.serviceType,
          );

          if (limitBreachedDetails.hasBillerLimitBreached) {
            emit(
              loadedState.copyWith(
                breachedLimitDetails: limitBreachedDetails,
                loadingPayNow: false,
              ),
            );

            return;
          }

          emit(loadedState.copyWith(loadingPayNow: false));

          await _navigationProvider.push(
            BillConfirmationScreenNavigationConfig(
              vendorDetails: loadedState.beneficiaryStateDetails.vendorDetails,
              billerServiceType:
                  loadedState.beneficiaryStateDetails.serviceType,
              amountToBePaid: amountPaid,
              balanceDetails:
                  loadedState.beneficiaryStateDetails.utilityBalanceDetails,
              beneficiaryId: loadedState
                  .beneficiaryStateDetails.beneficiaryDetails.beneficiaryId,
              pin: loadedState
                  .beneficiaryStateDetails.beneficiaryDetails.billerPin,
              beneficiaryName: loadedState
                  .beneficiaryStateDetails.beneficiaryDetails.beneficiaryName,
            ),
          );
        } on Object catch (e, s) {
          emit(loadedState.copyWith(loadingPayNow: false));
          _logger.error(_loggerPrefix, error: e, stackTrace: s);
          _notificationService.showSnackbar(
            SnackbarModel.errorLowPriority(text: e.toString()),
          );
          _analytics.initiatePaymentFailure(
            vendorName:
                loadedState.beneficiaryStateDetails.vendorDetails.vendorName,
            error: e.toString(),
          );
        }
      },
    );
  }

  void closeScreen() {
    _navigationProvider.moveToPaymentHome();
  }

  void showValidationErrorState() {
    state.mapOrNull(
      loaded: (loadedState) => emit(
        loadedState.copyWith(
          breachedLimitDetails: BillerLimitDetails(
            limit: _localizations.utilityPaymentLocalAmountValidationErrorText,
            hasBillerLimitBreached: true,
          ),
        ),
      ),
    );
  }
}
