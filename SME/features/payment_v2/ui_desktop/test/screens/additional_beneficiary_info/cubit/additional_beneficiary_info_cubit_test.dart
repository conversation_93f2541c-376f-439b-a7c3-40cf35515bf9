import 'package:bloc_test/bloc_test.dart';
import 'package:flow_builder/flow_builder.dart';
import 'package:mocktail/mocktail.dart';
import 'package:payment_v2_ui_desktop/src/flows/international_payment_flow/international_payment_data.dart';
import 'package:payment_v2_ui_desktop/src/screens/payment/additional_beneficiary_info/cubit/additional_beneficiary_info_cubit.dart';
import 'package:payment_v2_ui_desktop/src/screens/payment/additional_beneficiary_info/cubit/additional_beneficiary_info_state.dart';
import 'package:payment_v2_ui_desktop/src/screens/payment/beneficiary_creation/extensions.dart';
import 'package:payment_v2_ui_desktop/src/screens/payment/beneficiary_creation/models/beneficiary_creation_model.dart';
import 'package:payment_v2_ui_desktop/src/screens/payment/beneficiary_creation/models/transfer_requirements_model.dart';
import 'package:tests/tests.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';

import '../../../mocks.dart';
import '../../../test_entities.dart';

void main() {
  late BeneficiaryCreationInteractor beneficiaryCreationInteractor;
  late BeneficiaryInteractor beneficiaryInteractor;
  late MockRequirementsModelMapper requirementsModelMapper;
  late MockLogger logger;
  late AdditionalBeneficiaryInfoCubit cubit;
  late MockErrorHandler errorHandler;
  late FlowController<IntlPaymentData> flow;
  late IntlPaymentData globalState;
  late ICountryInteractorMock countryInteractor;

  final transferOption = TransferOptionFake();
  final transferRequirements = TransferRequirementsFactory.rand();
  final initialTransferRequirementModelList = <MockTransferRequirementModel>[];
  final beneficiary = TestEntities.getInternationalBeneficiaries()[0];
  const countrySelected = PaymentCountry.localCountry;

  final currencySelected = beneficiary.currencyCode ?? 'AED';

  final key = randomString();
  final initValue = randomString();
  final updValue = randomString();
  final model = TransferRequirementInfoModel.text(
    requirement: TransferRequirementInfo(
      key: key,
      required: true,
      type: TransferRequirementInfoType.text,
      readOnly: false,
    ),
    value: initValue,
    key: key,
  );

  final beneficiaryCreationModel = BeneficiaryCreationModel(
    transferRequirements: transferRequirements,
    transferRequirementsModel: [
      TransferRequirementModel(
        name: randomString(),
        group: [
          model,
        ],
      ),
    ],
    lastRefreshedTransferRequirementsModel: [
      TransferRequirementModel(
        name: randomString(),
        group: [
          TransferRequirementInfoModel.text(
            requirement: TransferRequirementInfo(
              key: key,
              required: true,
              type: TransferRequirementInfoType.text,
              readOnly: false,
            ),
            value: initValue,
            key: key,
          ),
        ],
      ),
    ],
  );

  final beneficiaryRefreshCreationModel = BeneficiaryCreationModel(
    transferRequirements: transferRequirements,
    transferRequirementsModel: [
      TransferRequirementModel(
        name: randomString(),
        group: [
          TransferRequirementInfoModel.text(
            requirement: TransferRequirementInfo(
              key: key,
              required: true,
              type: TransferRequirementInfoType.text,
              refreshRequirementsOnChange: true,
              readOnly: false,
            ),
            value: initValue,
            key: key,
          ),
        ],
      ),
    ],
    lastRefreshedTransferRequirementsModel: [
      TransferRequirementModel(
        name: randomString(),
        group: [
          TransferRequirementInfoModel.text(
            requirement: TransferRequirementInfo(
              key: key,
              required: true,
              type: TransferRequirementInfoType.text,
              refreshRequirementsOnChange: true,
              readOnly: false,
            ),
            value: updValue,
            key: key,
          ),
        ],
      ),
    ],
  );

  final countries = TestEntities.getAllCountries();

  final beneficiaryData = <InternationalBeneficiaryDataEntry>[
    const InternationalBeneficiaryDataEntry(
      key: 'key_1',
      value: 'value_1',
    ),
    const InternationalBeneficiaryDataEntry(
      key: 'key_2',
      value: 'value_2',
    ),
  ];

  final internationalBeneficiary = MockInternationalBeneficiaryFake();

  setUpAll(() async {
    registerFallbackValue(TransferRequirementsFake());
    registerFallbackValue(TransferRequirementInfoFake());
  });

  setUp(() {
    globalState = IntlPaymentData(
      paymentType: PaymentType.international,
      accounts: [],
      selectedCountry: countrySelected,
      selectedTransferOption: transferOption,
      selectedBeneficiary: beneficiary,
    );
    flow = FakeFlowController<IntlPaymentData>(globalState);
    beneficiaryInteractor = BeneficiaryInteractorMock();
    countryInteractor = ICountryInteractorMock();
    beneficiaryCreationInteractor = BeneficiaryCreationInteractorMock();
    logger = MockLogger();
    requirementsModelMapper = MockRequirementsModelMapper();
    errorHandler = MockErrorHandler();

    cubit = AdditionalBeneficiaryInfoCubit(
      beneficiaryCreationInteractor: beneficiaryCreationInteractor,
      countryInteractor: countryInteractor,
      beneficiaryInteractor: beneficiaryInteractor,
      flow: flow,
      requirementsModelMapper: requirementsModelMapper,
      logger: logger,
      errorHandlerTool: errorHandler,
    );
  });

  group('Initial data', () {
    blocTest<AdditionalBeneficiaryInfoCubit, AdditionalBeneficiaryInfoState>(
      'Should load initial loading data correctly',
      build: () => cubit,
      setUp: () async {
        when(
          () => beneficiaryCreationInteractor
              .getAdditionalBeneficiaryRequirements(
            optionId: transferOption.id,
            beneficiaryId: beneficiary.id,
          ),
        ).thenAnswer(
          (_) async => transferRequirements,
        );

        when(
          () => requirementsModelMapper.mapToModels(
            transferRequirements,
          ),
        ).thenAnswer(
          (_) => initialTransferRequirementModelList,
        );
      },
      act: (cubit) async {
        await cubit.initialize();
      },
      verify: (_) {
        verify(
          () => beneficiaryCreationInteractor
              .getAdditionalBeneficiaryRequirements(
            optionId: transferOption.id,
            beneficiaryId: beneficiary.id,
          ),
        ).calledOnce;
      },
    );
  });

  group('On value change', () {
    final modelUpd = beneficiaryCreationModel.transferRequirementsModel
        .map(
          (transferRequirementModel) =>
              transferRequirementModel.copyWithUpdatedGroup(
            key: key,
            value: updValue,
          ),
        )
        .toList();

    blocTest<AdditionalBeneficiaryInfoCubit, AdditionalBeneficiaryInfoState>(
      'Should properly change value by key',
      build: () => cubit,
      seed: () => AdditionalBeneficiaryInfoState.loaded(
        beneficiaryCreationModel: beneficiaryCreationModel,
        countries: countries,
        paymentType: PaymentType.international,
        optionId: transferOption.id,
        beneficiaryId: beneficiary.id,
        countryCode: countrySelected.code,
        currencyCode: currencySelected,
      ),
      setUp: () async {
        when(
          () => beneficiaryCreationInteractor
              .getAdditionalBeneficiaryRequirements(
            optionId: transferOption.id,
            beneficiaryId: beneficiary.id,
          ),
        ).justAnswerAsync(
          const TransferRequirements(
            provider: 'Provider',
            groups: [TransferRequirement(name: 'name', group: [])],
          ),
        );

        when(
          () => requirementsModelMapper.mapToModels(
            transferRequirements,
          ),
        ).thenAnswer(
          (_) => initialTransferRequirementModelList,
        );
      },
      act: (cubit) async {
        cubit.onValueChange(key: key, value: updValue);
      },
      expect: () => [
        AdditionalBeneficiaryInfoState.loaded(
          beneficiaryCreationModel: beneficiaryCreationModel.copyWith(
            transferRequirementsModel: modelUpd,
          ),
          paymentType: PaymentType.international,
          optionId: transferOption.id,
          beneficiaryId: beneficiary.id,
          countryCode: countrySelected.code,
          currencyCode: currencySelected,
          countries: countries,
        ),
      ],
    );

    blocTest<AdditionalBeneficiaryInfoCubit, AdditionalBeneficiaryInfoState>(
      'Should properly refresh requirements on change value',
      build: () => cubit,
      seed: () => AdditionalBeneficiaryInfoState.loaded(
        beneficiaryCreationModel: beneficiaryRefreshCreationModel,
        paymentType: PaymentType.international,
        optionId: transferOption.id,
        beneficiaryId: beneficiary.id,
        countryCode: countrySelected.code,
        currencyCode: currencySelected,
        countries: countries,
      ),
      setUp: () async {
        when(
          () => requirementsModelMapper.mapToRefreshDataEntries(
            [key],
            any(),
          ),
        ).thenAnswer(
          (_) => [],
        );

        when(
          () => requirementsModelMapper.mapToModels(
            transferRequirements,
            any(),
          ),
        ).thenAnswer(
          (_) => [],
        );

        when(
          () => beneficiaryCreationInteractor
              .getAdditionalBeneficiaryRefreshRequirements(
            optionId: transferOption.id,
            beneficiaryId: beneficiary.id,
            fields: any(named: 'fields'),
          ),
        ).justAnswerAsync(
          transferRequirements,
        );
      },
      act: (cubit) {
        cubit.onValueSubmit(key);
      },
      verify: (_) {
        verify(
          () => beneficiaryCreationInteractor
              .getAdditionalBeneficiaryRefreshRequirements(
            optionId: transferOption.id,
            beneficiaryId: beneficiary.id,
            fields: any(named: 'fields'),
          ),
        ).calledOnce;
      },
    );
  });

  group('On Submit', () {
    blocTest<AdditionalBeneficiaryInfoCubit, AdditionalBeneficiaryInfoState>(
      'Successfully Create the Beneficiary ',
      build: () => cubit,
      seed: () => AdditionalBeneficiaryInfoState.loaded(
        beneficiaryCreationModel: beneficiaryCreationModel,
        paymentType: PaymentType.international,
        optionId: transferOption.id,
        beneficiaryId: beneficiary.id,
        countryCode: countrySelected.code,
        currencyCode: currencySelected,
        countries: countries,
      ),
      setUp: () async {
        when(
          () => requirementsModelMapper.mapToRefreshDataEntries(
            [key],
            any(),
          ),
        ).thenAnswer(
          (_) => [],
        );

        when(
          () => requirementsModelMapper.mapToModels(
            transferRequirements,
            any(),
          ),
        ).thenAnswer(
          (_) => [],
        );

        when(
          () => beneficiaryCreationInteractor
              .getAdditionalBeneficiaryRefreshRequirements(
            optionId: transferOption.id,
            beneficiaryId: beneficiary.id,
            fields: any(named: 'fields'),
          ),
        ).justAnswerAsync(
          transferRequirements,
        );

        when(
          () => requirementsModelMapper.mapToBeneficiaryCreationModel(
            transferRequirements,
            any(),
          ),
        ).thenAnswer(
          (_) => beneficiaryCreationModel,
        );

        when(
          () => requirementsModelMapper.mapToInternationalBeneficiaryData(
            beneficiaryCreationModel.transferRequirementsModel,
          ),
        ).thenAnswer(
          (_) => beneficiaryData,
        );

        when(
          () => beneficiaryInteractor.updateInternationalBeneficiary(
            countryCode: any(named: 'countryCode'),
            currencyCode: any(named: 'currencyCode'),
            beneficiaryData: any(named: 'beneficiaryData'),
            transferRequirements: any(named: 'transferRequirements'),
            beneficiaryId: any(named: 'beneficiaryId'),
          ),
        ).thenAnswer(
          (_) async => internationalBeneficiary,
        );
      },
      act: (cubit) async {
        await cubit.onSubmit();
      },
      verify: (_) {
        verify(
          () => beneficiaryInteractor.updateInternationalBeneficiary(
            countryCode: countrySelected.code,
            currencyCode: currencySelected,
            beneficiaryData: any(named: 'beneficiaryData'),
            transferRequirements: any(named: 'transferRequirements'),
            beneficiaryId: beneficiary.id,
          ),
        ).calledOnce;
      },
    );

    blocTest<AdditionalBeneficiaryInfoCubit, AdditionalBeneficiaryInfoState>(
      'Create Beneficiary api throws exception',
      build: () => cubit,
      seed: () => AdditionalBeneficiaryInfoState.loaded(
        beneficiaryCreationModel: beneficiaryCreationModel,
        paymentType: PaymentType.international,
        optionId: transferOption.id,
        beneficiaryId: beneficiary.id,
        countryCode: countrySelected.code,
        currencyCode: currencySelected,
        countries: countries,
      ),
      setUp: () async {
        when(
          () => requirementsModelMapper.mapToInternationalBeneficiaryData(
            beneficiaryCreationModel.transferRequirementsModel,
          ),
        ).thenAnswer(
          (_) => beneficiaryData,
        );

        when(
          () => beneficiaryInteractor.updateInternationalBeneficiary(
            countryCode: any(named: 'countryCode'),
            currencyCode: any(named: 'currencyCode'),
            beneficiaryData: any(named: 'beneficiaryData'),
            transferRequirements: any(named: 'transferRequirements'),
            beneficiaryId: any(named: 'beneficiaryId'),
          ),
        ).justThrowAsync(Exception('Smth went wrong'));
      },
      act: (cubit) async {
        await cubit.onSubmit();
      },
      verify: (_) {
        verify(
          () => beneficiaryInteractor.updateInternationalBeneficiary(
            countryCode: countrySelected.code,
            currencyCode: currencySelected,
            beneficiaryData: any(named: 'beneficiaryData'),
            transferRequirements: any(named: 'transferRequirements'),
            beneficiaryId: beneficiary.id,
          ),
        ).calledOnce;
      },
    );
  });
}
