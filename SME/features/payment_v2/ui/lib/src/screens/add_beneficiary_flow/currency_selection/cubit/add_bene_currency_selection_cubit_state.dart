import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';

part 'add_bene_currency_selection_cubit_state.freezed.dart';

@freezed
class AddBeneCurrencySelectionState with _$AddBeneCurrencySelectionState {
  const AddBeneCurrencySelectionState._();

  const factory AddBeneCurrencySelectionState.loading() =
      AddBeneCurrencySelectionStateLoading;

  const factory AddBeneCurrencySelectionState.loaded({
    required PaymentCountry selectedCountry,
    required List<PaymentCurrency> supportedCurrencies,
  }) = AddBeneCurrencySelectionStateLoaded;

  const factory AddBeneCurrencySelectionState.failure() =
      AddBeneCurrencySelectionStateFailure;
}
