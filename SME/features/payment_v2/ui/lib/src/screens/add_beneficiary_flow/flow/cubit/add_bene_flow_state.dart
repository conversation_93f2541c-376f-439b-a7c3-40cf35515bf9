import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';

part 'add_bene_flow_state.freezed.dart';

@freezed
sealed class AddBeneStage with _$AddBeneStage {
  const factory AddBeneStage.countrySelection() = AddBeneStageCountrySelection;

  const factory AddBeneStage.currencySelection({
    required PaymentCountry country,
  }) = AddBeneStageCurrencySelection;

  const factory AddBeneStage.beneficiaryCreation({
    required PaymentCountry country,
    required PaymentCurrency currency,
  }) = AddBeneStageBeneficiaryCreation;
}

@freezed
sealed class AddBeneFlowState with _$AddBeneFlowState {
  const AddBeneFlowState._();

  const factory AddBeneFlowState({
    @Default(
      [],
    )
    List<AddBeneStage> stages,
  }) = _AddBeneFlowState;

  AddBeneStage get currentStage => stages.last;
}
