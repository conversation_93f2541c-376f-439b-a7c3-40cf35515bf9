import 'package:di/di.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/account_selection/account_selection_page.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/additional_beneficiary_info/additional_beneficiary_info_page.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/beneficiary_creation/beneficiary_creation_page.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/beneficiary_selection/beneficiary_selection_page.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/country_selection/country_selection_page.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/currency_selection/currency_selection_page.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/invoice_number_creation/invoice_number_creation_page.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/note_creation/note_creation_page.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/payment_creation_flow_cubit.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/payment_creation_flow_state.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/purpose_selection/purpose_selection_page.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/rails_selection/rails_selection_page.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/subpurpose_selection/subpurpose_selection_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart' hide Country;
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_common_feature_payments_ui/wio_common_feature_payments_ui.dart';

class PaymentCreationFlow extends StatelessWidget {
  final PaymentType paymentType;

  final String? beneficiaryId;

  const PaymentCreationFlow(
    this.paymentType, {
    this.beneficiaryId,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final localization = WioPaymentsLocalizations.of(context);

    return BlocProvider(
      create: (_) => DependencyProvider.getWithParams<PaymentCreationFlowCubit,
          PaymentType, Object?>(param1: paymentType)
        ..init(beneficiaryId),
      child: Builder(
        builder: (context) => StepHostBuilder<PaymentCreationFlowState>(
          stepper: StepHostStepperType.bottomLeft,
          stepperVariant: CompanyStepperVariant.pages,
          activeStepBgColor: CompanyColorPointer.background2,
          title: paymentType.flowTitle(localization),
          topNavigationBgColor: CompanyColorPointer.surface2,
          hasCloseButton: true,
          isAnalyticsEnabled: false,
          create: () => context.read<PaymentCreationFlowCubit>(),
          builder: (context, screenId, controller) {
            final currentStep = PaymentCreationFlowStep.values.firstWhere(
              (e) => e.name == screenId,
              orElse: () => PaymentCreationFlowStep.values.first,
            );

            switch (currentStep) {
              case PaymentCreationFlowStep.accountSelection:
                return AccountSelectionPage(
                  stepScreenController: controller,
                  accounts: controller.getGlobalState().accounts,
                  paymentType: paymentType,
                  beneficiaryID: controller
                      .getGlobalState()
                      .creationResult
                      ?.beneficiary
                      .id,
                );
              case PaymentCreationFlowStep.countrySelection:
                return CountrySelectionPage(
                  stepScreenController: controller,
                );
              case PaymentCreationFlowStep.currencySelection:
                return CurrencySelectionPage(
                  stepScreenController: controller,
                );
              case PaymentCreationFlowStep.railsSelection:
                return RailsSelectionPage(
                  stepScreenController: controller,
                );
              case PaymentCreationFlowStep.beneficiarySelection:
                return BeneficiarySelectionPage(
                  stepScreenController: controller,
                  paymentType: paymentType,
                );
              case PaymentCreationFlowStep.beneficiaryCreation:
                return BeneficiaryCreationPage(
                  stepScreenController: controller,
                  paymentType: paymentType,
                );
              case PaymentCreationFlowStep.purposeSelection:
                return PurposeSelectionPage(
                  stepScreenController: controller,
                  paymentType: paymentType,
                );
              case PaymentCreationFlowStep.noteCreation:
                return NoteCreationPage(
                  stepController: controller,
                  paymentType: paymentType,
                );
              case PaymentCreationFlowStep.invoiceNumberCreation:
                return InvoiceNumberCreationPage(
                  stepScreenController: controller,
                  paymentType: paymentType,
                );
              case PaymentCreationFlowStep.subpurposeSelection:
                return SubpurposeSelectionPage(
                  stepScreenController: controller,
                  paymentType: paymentType,
                );
              case PaymentCreationFlowStep.additionalBeneficiaryInfo:
                return AdditionalBeneficiaryInfoPage(
                  stepScreenController: controller,
                );
            }
          },
        ),
      ),
    );
  }
}

extension on PaymentType {
  String flowTitle(WioPaymentsLocalizations localization) {
    switch (this) {
      case PaymentType.local:
        return localization.localFlowCreationScreenAppBarTitle;
      case PaymentType.international:
        return localization.flowCreationScreenAppBarTitle;
      default:
        throw Exception('Does not support $this');
    }
  }
}
