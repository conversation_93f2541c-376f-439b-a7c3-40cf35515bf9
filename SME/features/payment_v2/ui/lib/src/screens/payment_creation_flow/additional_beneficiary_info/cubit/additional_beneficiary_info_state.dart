import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_common_feature_payments_ui/wio_common_feature_payments_ui.dart';

part 'additional_beneficiary_info_state.freezed.dart';

@Freezed(fromJson: false, toJson: false)
class AdditionalBeneficiaryInfoState with _$AdditionalBeneficiaryInfoState {
  const AdditionalBeneficiaryInfoState._();

  const factory AdditionalBeneficiaryInfoState.loading() =
      AdditionalBeneficiaryInfoStateLoading;

  const factory AdditionalBeneficiaryInfoState.failure() =
      AdditionalBeneficiaryInfoStateFailure;

  const factory AdditionalBeneficiaryInfoState.loaded({
    required String optionId,
    required String beneficiaryId,
    required String countryCode,
    required String currencyCode,
    required BeneficiaryCreationModel beneficiaryCreationModel,
  }) = AdditionalBeneficiaryInfoStateLoaded;
}
