part of 'account_selection_page.dart';

class _AccountSelectionLoadingStateWidget extends StatelessWidget {
  const _AccountSelectionLoadingStateWidget();

  @override
  Widget build(BuildContext context) {
    final theme = CompanyThemeProvider.of(context);
    final size = MediaQuery.of(context).size;
    const decoration = BoxDecoration(
      borderRadius: BorderRadius.all(Radius.circular(12)),
      color: Colors.white70,
    );

    final section = Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: <PERSON>mmer<PERSON>ontainer(
        child: Container(
          decoration: decoration,
          width: size.width * 0.7,
          height: 74,
        ),
      ),
    );

    return Scaffold(
      backgroundColor: theme.colorScheme.background3,
      body: Safe<PERSON>rea(
        bottom: false,
        child: ListView(
          padding: const EdgeInsets.all(24.0),
          children: List.generate(3, (index) => section),
        ),
      ),
    );
  }
}
