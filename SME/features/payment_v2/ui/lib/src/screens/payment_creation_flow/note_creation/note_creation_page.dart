import 'package:di/di.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/note_creation/cubit/note_creation_cubit.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/payment_creation_flow_base.dart';
import 'package:feature_payment_v2_ui/src/screens/payment_creation_flow/payment_creation_flow_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tuple/tuple.dart';
import 'package:ui/text_input_formatters/text_input_field_formatter.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_common_feature_payments_ui/wio_common_feature_payments_ui.dart'
    hide TextInputType;

class NoteCreationPage extends PaymentCreationFlowStepScreenBase<
    NoteCreationState, NoteCreationCubit> {
  final PaymentType paymentType;

  NoteCreationPage({
    required StepScreenController<PaymentCreationFlowState> stepController,
    required this.paymentType,
    super.key,
  }) : super(stepScreenController: stepController);

  @override
  NoteCreationCubit createBloc() =>
      DependencyProvider.getWithParams<NoteCreationCubit, PaymentType, Object?>(
        param1: paymentType,
      );

  @override
  Tuple2<String, String> buildTitle(
    BuildContext context,
    NoteCreationCubit bloc,
    NoteCreationState state,
  ) {
    final l10n = WioPaymentsLocalizations.of(context);

    return Tuple2(
      l10n.paymentsInternationalAddNoteTitle,
      l10n.paymentsInternationalAddNote,
    );
  }

  @override
  Widget buildStepBody(
    BuildContext context,
    NoteCreationCubit bloc,
    NoteCreationState state,
  ) =>
      _Content(isFieldRequired: state.isFieldRequired);
}

class _Content extends StatefulWidget {
  final bool isFieldRequired;

  const _Content({required this.isFieldRequired});

  @override
  State<_Content> createState() => _ContentState();
}

class _ContentState extends State<_Content> {
  var _value = '';
  bool enabledValidation = false;

  String get value => _value.trim();

  bool get isInputValid => !widget.isFieldRequired || value.isNotEmpty;

  bool get showError => enabledValidation && !isInputValid;

  void onValueChanged(String newValue) {
    setState(() {
      _value = newValue;
      enabledValidation = false;
    });
  }

  void onNextClick(ValueSetter<String> onNextRequested) {
    if (!enabledValidation) {
      setState(() {
        enabledValidation = true;
      });
    }
    if (isInputValid) onNextRequested(value);
  }

  @override
  Widget build(BuildContext context) {
    final l10n = WioPaymentsLocalizations.of(context);
    final bloc = BlocProvider.of<NoteCreationCubit>(context);

    return SafeArea(
      child: FixedButtonsScrollablePageLayout(
        buttonsPadding: const EdgeInsets.fromLTRB(24.0, 0.0, 24.0, 8.0),
        physics: const NeverScrollableScrollPhysics(),
        model: FixedButtonsScrollablePageLayoutModel(
          primaryButton: FixedButtonsScrollablePageLayoutButton(
            label: l10n.paymentsInternationalNoteCreationNext,
            theme: ButtonModelTheme.sme,
            size: ButtonSize.medium,
          ),
          addShadowToButtons: true,
          buttonAlignmentMode:
              FixedButtonScrollablePageLayoutButtonAlignmentMode.aboveContent,
        ),
        onPrimaryButtonPressed: () => onNextClick(bloc.onNextRequested),
        children: [
          _InputText(
            isRequired: widget.isFieldRequired,
            error: showError ? l10n.notesValidationMessage : null,
            onChanged: onValueChanged,
          ),
        ],
      ),
    );
  }
}

class _InputText extends StatelessWidget {
  final String? error;
  final bool isRequired;
  final ValueChanged<String> onChanged;

  const _InputText({
    required this.error,
    required this.isRequired,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = WioPaymentsLocalizations.of(context);

    return SizedBox(
      height: MediaQuery.sizeOf(context).height / 2,
      child: InputField(
        model: InputFieldModel(
          size: InputFieldSize.small,
          error: error,
          label: isRequired ? l10n.labelNotes : l10n.labelNotesOptional,
          hint: l10n.paymentsInternationalWriteNoteHere,
          theme: InputFieldTheme.light,
        ),
        smartDashesType: SmartDashesType.disabled,
        formatters: TextInputFieldFormatter.notes(),
        autoFocus: true,
        maxLines: null,
        keyboardType: TextInputType.multiline,
        onInputChanged: onChanged,
        onFieldSubmitted: onChanged,
        textCapitalization: TextCapitalization.sentences,
      ),
    );
  }
}
