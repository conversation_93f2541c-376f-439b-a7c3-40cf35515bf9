import 'package:feature_payment_v2_api/feature_payment_v2_api.dart';
import 'package:feature_payment_v2_ui/src/constants/index.dart';
import 'package:feature_payment_v2_ui/src/navigation/index.dart';
import 'package:feature_payment_v2_ui/src/screens/bills/bills_dashboard/bills_dashboard_state.dart';
import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

class BillsSelectVendorTypeBottomSheet extends StatelessWidget {
  final UtilityVendorType utilityVendorType;
  final List<BillerTypes> billerTypes;

  const BillsSelectVendorTypeBottomSheet({
    required this.utilityVendorType,
    required this.billerTypes,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final listDetailsList = billerTypes.map((billerType) {
      final iconPath = billerType.logoPath;

      return ListDetailsModel(
        textLabelModel: ListDetailsTextLabelModel(
          tile: TileModel.image(
            image: ImageInTileModel(
              path: iconPath,
              package: Constants.packageName,
              source: ImageSource.asset,
            ),
          ),
          text: billerType.name.toUpperCase(),
          textStyle: CompanyTextStylePointer.b3medium,
        ),
        valueModel: const ListDetailsValueModel.icon(
          iconPointer: CompanyIconPointer.chevron_right,
        ),
      );
    }).toList();

    final title = switch (utilityVendorType) {
      UtilityVendorType.telecom => 'Telecom',
      UtilityVendorType.others => 'Others'
    };

    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: ListDetailsBottomSheet(
        ListDetailsBottomSheetConfiguration(
          title: ListDetailsBottomSheetTitle(
            title: title,
          ),
          listDetails: listDetailsList,
        ),
        onValuePressed: (index) => Navigator.maybePop(
          context,
          BillsSelectVendorTypeBottomSheetResult(
            billerType: billerTypes[index],
          ),
        ),
      ),
    );
  }
}
