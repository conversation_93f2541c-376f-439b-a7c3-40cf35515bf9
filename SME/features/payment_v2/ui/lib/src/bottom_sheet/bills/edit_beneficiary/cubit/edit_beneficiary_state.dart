import 'package:feature_payment_v2_api/feature_payment_v2_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_beneficiary_state.freezed.dart';

@freezed
class EditBeneficiaryState with _$EditBeneficiaryState {
  factory EditBeneficiaryState.initial(
    UtilityBeneficiaryDetails details,
  ) =>
      EditBeneficiaryState(
        details: details,
        accountNumber: details.customerNumberNormalized,
        beneficiaryName: details.beneficiaryName,
        pin: details.billerPin,
      );

  const factory EditBeneficiaryState({
    required UtilityBeneficiaryDetails details,
    required String beneficiaryName,
    required String accountNumber,
    String? pin,
    @Default(false) bool isSubmitInProgress,
  }) = _EditBeneficiaryState;

  const EditBeneficiaryState._();

  bool get isProviderSalik => details.vendorId == VendorIds.salikId;

  bool get isPinValid => pin?.isNotEmpty ?? false;

  bool get isAccountNumberValid => accountNumber.isNotEmpty;

  bool get isBeneNameValid => beneficiaryName.isNotEmpty;

  bool get hasChanged {
    if (!isProviderSalik) {
      return accountNumber != details.customerNumberNormalized ||
          beneficiaryName != details.beneficiaryName;
    }
    return accountNumber != details.customerNumberNormalized ||
        beneficiaryName != details.beneficiaryName ||
        pin != details.billerPin;
  }

  bool get canSubmit {
    if (!hasChanged) return false;

    if (!isProviderSalik) {
      return isAccountNumberValid && isBeneNameValid;
    }
    return isAccountNumberValid && isBeneNameValid && isPinValid;
  }
}
