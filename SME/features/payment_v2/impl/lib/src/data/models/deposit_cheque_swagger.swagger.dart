// ignore_for_file: type=lint

import 'package:json_annotation/json_annotation.dart';
import 'package:collection/collection.dart';
import 'dart:convert';

part 'deposit_cheque_swagger.swagger.g.dart';

@JsonSerializable(explicitToJson: true)
class ErrorMessage {
  const ErrorMessage({
    this.id,
    this.code,
    this.description,
    this.path,
    this.additionalInfo,
    this.context,
  });

  factory ErrorMessage.fromJson(Map<String, dynamic> json) =>
      _$ErrorMessageFromJson(json);

  static const toJsonFactory = _$ErrorMessageToJson;
  Map<String, dynamic> toJson() => _$ErrorMessageToJson(this);

  @<PERSON><PERSON><PERSON><PERSON>(name: 'Id', includeIfNull: false)
  final String? id;
  @JsonKey(name: 'Code', includeIfNull: false)
  final String? code;
  @JsonKey(name: 'Description', includeIfNull: false)
  final String? description;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Path', includeIfNull: false)
  final String? path;
  @J<PERSON><PERSON>ey(name: 'AdditionalInfo', includeIfNull: false)
  final String? additionalInfo;
  @JsonKey(name: 'Context', includeIfNull: false)
  final Object? context;
  static const fromJsonFactory = _$ErrorMessageFromJson;

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other is ErrorMessage &&
            (identical(other.id, id) ||
                const DeepCollectionEquality().equals(other.id, id)) &&
            (identical(other.code, code) ||
                const DeepCollectionEquality().equals(other.code, code)) &&
            (identical(other.description, description) ||
                const DeepCollectionEquality()
                    .equals(other.description, description)) &&
            (identical(other.path, path) ||
                const DeepCollectionEquality().equals(other.path, path)) &&
            (identical(other.additionalInfo, additionalInfo) ||
                const DeepCollectionEquality()
                    .equals(other.additionalInfo, additionalInfo)) &&
            (identical(other.context, context) ||
                const DeepCollectionEquality().equals(other.context, context)));
  }

  @override
  String toString() => jsonEncode(this);

  @override
  int get hashCode =>
      const DeepCollectionEquality().hash(id) ^
      const DeepCollectionEquality().hash(code) ^
      const DeepCollectionEquality().hash(description) ^
      const DeepCollectionEquality().hash(path) ^
      const DeepCollectionEquality().hash(additionalInfo) ^
      const DeepCollectionEquality().hash(context) ^
      runtimeType.hashCode;
}

extension $ErrorMessageExtension on ErrorMessage {
  ErrorMessage copyWith(
      {String? id,
      String? code,
      String? description,
      String? path,
      String? additionalInfo,
      Object? context}) {
    return ErrorMessage(
        id: id ?? this.id,
        code: code ?? this.code,
        description: description ?? this.description,
        path: path ?? this.path,
        additionalInfo: additionalInfo ?? this.additionalInfo,
        context: context ?? this.context);
  }

  ErrorMessage copyWithWrapped(
      {Wrapped<String?>? id,
      Wrapped<String?>? code,
      Wrapped<String?>? description,
      Wrapped<String?>? path,
      Wrapped<String?>? additionalInfo,
      Wrapped<Object?>? context}) {
    return ErrorMessage(
        id: (id != null ? id.value : this.id),
        code: (code != null ? code.value : this.code),
        description:
            (description != null ? description.value : this.description),
        path: (path != null ? path.value : this.path),
        additionalInfo: (additionalInfo != null
            ? additionalInfo.value
            : this.additionalInfo),
        context: (context != null ? context.value : this.context));
  }
}

@JsonSerializable(explicitToJson: true)
class ChequeImage {
  const ChequeImage({
    this.fileType,
    this.fileContentBase64,
  });

  factory ChequeImage.fromJson(Map<String, dynamic> json) =>
      _$ChequeImageFromJson(json);

  static const toJsonFactory = _$ChequeImageToJson;
  Map<String, dynamic> toJson() => _$ChequeImageToJson(this);

  @JsonKey(name: 'fileType', includeIfNull: false)
  final String? fileType;
  @JsonKey(name: 'fileContentBase64', includeIfNull: false)
  final String? fileContentBase64;
  static const fromJsonFactory = _$ChequeImageFromJson;

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other is ChequeImage &&
            (identical(other.fileType, fileType) ||
                const DeepCollectionEquality()
                    .equals(other.fileType, fileType)) &&
            (identical(other.fileContentBase64, fileContentBase64) ||
                const DeepCollectionEquality()
                    .equals(other.fileContentBase64, fileContentBase64)));
  }

  @override
  String toString() => jsonEncode(this);

  @override
  int get hashCode =>
      const DeepCollectionEquality().hash(fileType) ^
      const DeepCollectionEquality().hash(fileContentBase64) ^
      runtimeType.hashCode;
}

extension $ChequeImageExtension on ChequeImage {
  ChequeImage copyWith({String? fileType, String? fileContentBase64}) {
    return ChequeImage(
        fileType: fileType ?? this.fileType,
        fileContentBase64: fileContentBase64 ?? this.fileContentBase64);
  }

  ChequeImage copyWithWrapped(
      {Wrapped<String?>? fileType, Wrapped<String?>? fileContentBase64}) {
    return ChequeImage(
        fileType: (fileType != null ? fileType.value : this.fileType),
        fileContentBase64: (fileContentBase64 != null
            ? fileContentBase64.value
            : this.fileContentBase64));
  }
}

// ignore: unused_element
String? _dateToJson(DateTime? date) {
  if (date == null) {
    return null;
  }

  final year = date.year.toString();
  final month = date.month < 10 ? '0${date.month}' : date.month.toString();
  final day = date.day < 10 ? '0${date.day}' : date.day.toString();

  return '$year-$month-$day';
}

class Wrapped<T> {
  final T value;
  const Wrapped.value(this.value);
}
