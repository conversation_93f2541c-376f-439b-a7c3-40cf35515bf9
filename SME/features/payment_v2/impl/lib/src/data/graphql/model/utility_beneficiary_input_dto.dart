import 'package:feature_payment_v2_impl/src/data/graphql/model/common_info_input_dto.dart';
import 'package:feature_payment_v2_impl/src/data/graphql/model/utility_additional_input_dto.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'utility_beneficiary_input_dto.freezed.dart';
part 'utility_beneficiary_input_dto.g.dart';

@freezed
class UtilityBeneficiaryInputDto with _$UtilityBeneficiaryInputDto {
  @JsonSerializable(fieldRename: FieldRename.pascal)
  const factory UtilityBeneficiaryInputDto({
    required CommonInfoInputDto commonInfoInput,
    required UtilityAdditionalInputDto additionalInput,
  }) = _UtilityBeneficiaryInputDto;

  factory UtilityBeneficiaryInputDto.fromJson(Map<String, dynamic> json) =>
      _$UtilityBeneficiaryInputDtoFromJson(json);
}
