import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_document_upload_api/model/document_upload_model.dart';
import 'package:wio_feature_document_upload_api/model/exports.dart';

part 'document_upload_state.freezed.dart';

@freezed
class DocumentUploadState with _$DocumentUploadState {
  const DocumentUploadState._();

  const factory DocumentUploadState.loaded({
    required DocumentUploadModel uploadModel,
  }) = _DocumentLoadedState;

  const factory DocumentUploadState.fileUploadStatusChanged({
    required DocumentUploadModel uploadModel,
  }) = _FileUploadingInProgressState;

  const factory DocumentUploadState.fileUploadDone({
    required DocumentUploadModel uploadModel,
  }) = _FileUploadingDoneState;

  const factory DocumentUploadState.submitInProgress({
    required DocumentUploadModel uploadModel,
  }) = _SubmitInProgressState;

  const factory DocumentUploadState.submitDone({
    required DocumentUploadModel uploadModel,
  }) = _submitDoneState;

  const factory DocumentUploadState.error({
    required DocumentUploadModel uploadModel,
  }) = DocumentErrorState;

  bool get isSubmitEnabled => maybeWhen(
        fileUploadDone: (model) =>
            _checkAllFileUploadsAreDone(model.filesToUpload),
        submitDone: (_) => true,
        submitInProgress: (_) => true,
        error: (_) => true,
        orElse: () => false,
      );

  bool get isSubmitInProgress => maybeWhen(
        submitInProgress: (_) => true,
        orElse: () => false,
      );

  bool _checkAllFileUploadsAreDone(List<UploadFileModel> fileList) {
    for (final fileItem in fileList) {
      if (fileItem.uploadingStatus != UploadingStatus.completed) {
        return false;
      }
    }

    return true;
  }
}
