import 'package:wio_feature_identity_verification_api/data/onfido_repository.dart';
import 'package:wio_feature_identity_verification_api/domain/models/applicant_details.dart';
import 'package:wio_feature_identity_verification_api/domain/models/document_type.dart';

class KycStage {
  final DocumentType documentType;
  final OnfidoRepository onfidoRepository;
  KycStage? nextStage;
  ApplicantDetails? applicant;

  List<Map<dynamic, dynamic>>? onfidoResult;
  String? reason;

  KycStage({
    required this.documentType,
    required this.onfidoRepository,
    this.nextStage,
  });

  Future<ApplicantDetails> createApplicant() async {
    applicant = await onfidoRepository.createApplicant(
      documentType: documentType,
    );

    return applicant!;
  }

  Future<void> submitOnfidoResult({required String? reason}) async {
    reason = reason;
    switch (documentType) {
      case DocumentType.passport:
        final documentId = onfidoResult?.first['id'] as String?;
        await onfidoRepository.sendDocumentCollected(
          applicantId: applicant!.applicantId,
          documentType: DocumentType.passport,
          reason: reason,
          documentId: documentId,
        );
        final id = onfidoResult![1]['id'] as String;
        await onfidoRepository.sendLivenessCollected(
          livenessId: id,
          applicantId: applicant!.applicantId,
        );

        break;
      case DocumentType.liveness:
        final id = onfidoResult!.first['id'] as String;
        await onfidoRepository.sendLivenessCollected(
          livenessId: id,
          applicantId: applicant!.applicantId,
        );
        break;
      case DocumentType.emiratesId:
        final documentId = onfidoResult?.first['id'] as String?;
        await onfidoRepository.sendDocumentCollected(
          documentType: DocumentType.emiratesId,
          applicantId: applicant!.applicantId,
          reason: reason,
          documentId: documentId,
        );
        if (onfidoResult?.length == 3) {
          final id = onfidoResult![2]['id'] as String;
          await onfidoRepository.sendLivenessCollected(
            livenessId: id,
            applicantId: applicant!.applicantId,
          );
        }
        break;
      default:
      // ignore
    }
  }
}
