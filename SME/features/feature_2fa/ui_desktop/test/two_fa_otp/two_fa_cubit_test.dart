import 'dart:ui';

import 'package:bloc_test/bloc_test.dart';
import 'package:feature_2fa_api/feature_2fa_api.dart';
import 'package:feature_2fa_ui_desktop/feature_2fa_ui_desktop.dart';
import 'package:feature_2fa_ui_desktop/src/screens/otp/model/twofa_otp_input_details.dart';
import 'package:feature_2fa_ui_desktop/src/screens/otp/twofa_otp_cubit.dart';
import 'package:feature_2fa_ui_desktop/src/screens/otp/twofa_otp_state.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart' as tests;
import 'package:ui/ota_lokalisation/ota_lokalization.dart';

import '../mocks.dart';

void main() {
  late TwoFaOtpCubit cubit;
  late MockNewNotificationService mockNewNotificationService;
  late TwoFaUILocalizations mockLocalizations;
  late MockTwoFactorAuthenticationInteractor
      mockTwoFactorAuthenticationInteractor;
  late MockTwoFaOtpEventAnalytics mockTwoFaOtpEventAnalytics;
  late MockOtpDelegate otpDelegate;

  const twoFaParam = TwoFaParams(transactionId: 'transactionId');

  const inputDetails = TwoFaOtpInputDetails(
    twoFaParams: twoFaParam,
    challengeId: 'challengeId',
    source: OtpSource.sms,
    status: TwoFactorAuthChallengeStatus.created,
  );

  const state = TwoFaOtpState();

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    mockLocalizations = await TwoFaUILocalizations.load(const Locale('en'));
  });

  setUp(() {
    mockNewNotificationService = MockNewNotificationService();
    mockTwoFactorAuthenticationInteractor =
        MockTwoFactorAuthenticationInteractor();
    mockTwoFaOtpEventAnalytics = MockTwoFaOtpEventAnalytics();
    otpDelegate = MockOtpDelegate();
    mockTwoFactorAuthenticationInteractor =
        MockTwoFactorAuthenticationInteractor();

    cubit = TwoFaOtpCubit(
      analytics: mockTwoFaOtpEventAnalytics,
      localizations: mockLocalizations,
      otpDelegate: otpDelegate,
      twoFactorAuthInteractor: mockTwoFactorAuthenticationInteractor,
      notificationService: mockNewNotificationService,
    );
  });

  tearDown(() {
    cubit.close();
  });

  group('Phone number SMS Otp tests', () {
    blocTest<TwoFaOtpCubit, TwoFaOtpState>(
      'Generate Otp successfully',
      build: () => cubit,
      act: (cubit) => cubit.initialize(details: inputDetails),
      setUp: () => when(
        () => mockTwoFactorAuthenticationInteractor.initiate(
          transactionId: twoFaParam.transactionId,
          challengeId: inputDetails.challengeId,
          type: TwoFactorAuthType.smsOtp,
        ),
      ).justAnswerAsync('586866907'),
      verify: (_) {
        verify(() => mockTwoFaOtpEventAnalytics.viewOtpPage()).calledOnce;

        verify(
          () => mockTwoFactorAuthenticationInteractor.initiate(
            transactionId: twoFaParam.transactionId,
            challengeId: inputDetails.challengeId,
            type: TwoFactorAuthType.smsOtp,
          ),
        ).calledOnce;

        verify(() => mockTwoFaOtpEventAnalytics.initiateOtp()).calledOnce;
      },
      expect: () => [
        state.copyWith(
          transactionId: twoFaParam.transactionId,
          otpSource: inputDetails.source,
          challengeId: inputDetails.challengeId,
        ),
      ],
    );
  });

  group('Email SMS Otp tests', () {
    blocTest<TwoFaOtpCubit, TwoFaOtpState>(
      'Generate Otp successfully',
      build: () => cubit,
      act: (cubit) => cubit.initialize(
        details: inputDetails.copyWith(
          source: OtpSource.email,
        ),
      ),
      setUp: () => when(
        () => mockTwoFactorAuthenticationInteractor.initiate(
          transactionId: twoFaParam.transactionId,
          challengeId: inputDetails.challengeId,
          type: TwoFactorAuthType.emailOtp,
        ),
      ).justAnswerAsync(
        '<EMAIL>',
      ),
      verify: (_) {
        verify(() => mockTwoFaOtpEventAnalytics.viewOtpPage()).calledOnce;

        verify(
          () => mockTwoFactorAuthenticationInteractor.initiate(
            transactionId: twoFaParam.transactionId,
            challengeId: inputDetails.challengeId,
            type: TwoFactorAuthType.emailOtp,
          ),
        ).calledOnce;

        verify(() => mockTwoFaOtpEventAnalytics.initiateOtp()).calledOnce;
      },
      expect: () => [
        state.copyWith(
          transactionId: twoFaParam.transactionId,
          otpSource: OtpSource.email,
          challengeId: inputDetails.challengeId,
        ),
      ],
    );
  });

  test('Should return proper class name', () {
    expect(cubit.toString(), 'TwoFaOtpCubit{}');
  });
}
