import 'package:feature_2fa_api/feature_2fa_api.dart';
import 'package:wio_app_core_api/index.dart';

// ignore_for_file: constant_identifier_names
enum ErrorAnalyticsEvents {
  continue_button_click,
  on_close_button_click,
  try_again_click,
  error_page,
}

class ErrorAnalytics {
  final AnalyticsEventTracker _analytics;
  final String screenName;

  ErrorAnalytics({
    required AnalyticsAbstractTrackerFactory analyticsAbstractTrackerFactory,
    required this.screenName,
  }) : _analytics = analyticsAbstractTrackerFactory.get(
          screenName: screenName,
          tracker: AnalyticsTracker.mixpanel,
        );

  void viewErrorPage(TwoFaErrorType page) {
    _analytics.view(
      targetType: AnalyticsTargetType.screen,
      target: page,
    );
  }

  void continueButtonClick() {
    _analytics.simple(
      eventName: ErrorAnalyticsEvents.continue_button_click.name,
    );
  }

  void tryAgainClick() {
    _analytics.simple(
      eventName: ErrorAnalyticsEvents.try_again_click.name,
    );
  }

  void onCloseButtonClick() {
    _analytics.simple(
      eventName: ErrorAnalyticsEvents.on_close_button_click.name,
    );
  }

  void clickBack() {
    _analytics.clickBack();
  }
}
