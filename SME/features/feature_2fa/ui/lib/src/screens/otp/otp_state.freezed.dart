// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'otp_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OtpState {
  InputFieldModel get model => throw _privateConstructorUsedError;
  OtpType get type => throw _privateConstructorUsedError;
  bool get suspendedModalIsOpen => throw _privateConstructorUsedError;
  bool get resendAvailable => throw _privateConstructorUsedError;
  int get resendCountdownTime => throw _privateConstructorUsedError;
  DateTime? get resendCountdownStartTime => throw _privateConstructorUsedError;
  String get transactionId => throw _privateConstructorUsedError;
  String get challengeId => throw _privateConstructorUsedError;
  String? get nextChallengeName => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $OtpStateCopyWith<OtpState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtpStateCopyWith<$Res> {
  factory $OtpStateCopyWith(OtpState value, $Res Function(OtpState) then) =
      _$OtpStateCopyWithImpl<$Res, OtpState>;
  @useResult
  $Res call(
      {InputFieldModel model,
      OtpType type,
      bool suspendedModalIsOpen,
      bool resendAvailable,
      int resendCountdownTime,
      DateTime? resendCountdownStartTime,
      String transactionId,
      String challengeId,
      String? nextChallengeName});

  $InputFieldModelCopyWith<$Res> get model;
}

/// @nodoc
class _$OtpStateCopyWithImpl<$Res, $Val extends OtpState>
    implements $OtpStateCopyWith<$Res> {
  _$OtpStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? model = null,
    Object? type = null,
    Object? suspendedModalIsOpen = null,
    Object? resendAvailable = null,
    Object? resendCountdownTime = null,
    Object? resendCountdownStartTime = freezed,
    Object? transactionId = null,
    Object? challengeId = null,
    Object? nextChallengeName = freezed,
  }) {
    return _then(_value.copyWith(
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as InputFieldModel,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as OtpType,
      suspendedModalIsOpen: null == suspendedModalIsOpen
          ? _value.suspendedModalIsOpen
          : suspendedModalIsOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      resendAvailable: null == resendAvailable
          ? _value.resendAvailable
          : resendAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
      resendCountdownTime: null == resendCountdownTime
          ? _value.resendCountdownTime
          : resendCountdownTime // ignore: cast_nullable_to_non_nullable
              as int,
      resendCountdownStartTime: freezed == resendCountdownStartTime
          ? _value.resendCountdownStartTime
          : resendCountdownStartTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      transactionId: null == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String,
      challengeId: null == challengeId
          ? _value.challengeId
          : challengeId // ignore: cast_nullable_to_non_nullable
              as String,
      nextChallengeName: freezed == nextChallengeName
          ? _value.nextChallengeName
          : nextChallengeName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $InputFieldModelCopyWith<$Res> get model {
    return $InputFieldModelCopyWith<$Res>(_value.model, (value) {
      return _then(_value.copyWith(model: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OtpStateImplCopyWith<$Res>
    implements $OtpStateCopyWith<$Res> {
  factory _$$OtpStateImplCopyWith(
          _$OtpStateImpl value, $Res Function(_$OtpStateImpl) then) =
      __$$OtpStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {InputFieldModel model,
      OtpType type,
      bool suspendedModalIsOpen,
      bool resendAvailable,
      int resendCountdownTime,
      DateTime? resendCountdownStartTime,
      String transactionId,
      String challengeId,
      String? nextChallengeName});

  @override
  $InputFieldModelCopyWith<$Res> get model;
}

/// @nodoc
class __$$OtpStateImplCopyWithImpl<$Res>
    extends _$OtpStateCopyWithImpl<$Res, _$OtpStateImpl>
    implements _$$OtpStateImplCopyWith<$Res> {
  __$$OtpStateImplCopyWithImpl(
      _$OtpStateImpl _value, $Res Function(_$OtpStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? model = null,
    Object? type = null,
    Object? suspendedModalIsOpen = null,
    Object? resendAvailable = null,
    Object? resendCountdownTime = null,
    Object? resendCountdownStartTime = freezed,
    Object? transactionId = null,
    Object? challengeId = null,
    Object? nextChallengeName = freezed,
  }) {
    return _then(_$OtpStateImpl(
      null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as InputFieldModel,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as OtpType,
      suspendedModalIsOpen: null == suspendedModalIsOpen
          ? _value.suspendedModalIsOpen
          : suspendedModalIsOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      resendAvailable: null == resendAvailable
          ? _value.resendAvailable
          : resendAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
      resendCountdownTime: null == resendCountdownTime
          ? _value.resendCountdownTime
          : resendCountdownTime // ignore: cast_nullable_to_non_nullable
              as int,
      resendCountdownStartTime: freezed == resendCountdownStartTime
          ? _value.resendCountdownStartTime
          : resendCountdownStartTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      transactionId: null == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String,
      challengeId: null == challengeId
          ? _value.challengeId
          : challengeId // ignore: cast_nullable_to_non_nullable
              as String,
      nextChallengeName: freezed == nextChallengeName
          ? _value.nextChallengeName
          : nextChallengeName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$OtpStateImpl implements _OtpState {
  const _$OtpStateImpl(this.model,
      {required this.type,
      this.suspendedModalIsOpen = false,
      this.resendAvailable = false,
      this.resendCountdownTime = 0,
      this.resendCountdownStartTime,
      this.transactionId = '',
      this.challengeId = '',
      this.nextChallengeName});

  @override
  final InputFieldModel model;
  @override
  final OtpType type;
  @override
  @JsonKey()
  final bool suspendedModalIsOpen;
  @override
  @JsonKey()
  final bool resendAvailable;
  @override
  @JsonKey()
  final int resendCountdownTime;
  @override
  final DateTime? resendCountdownStartTime;
  @override
  @JsonKey()
  final String transactionId;
  @override
  @JsonKey()
  final String challengeId;
  @override
  final String? nextChallengeName;

  @override
  String toString() {
    return 'OtpState(model: $model, type: $type, suspendedModalIsOpen: $suspendedModalIsOpen, resendAvailable: $resendAvailable, resendCountdownTime: $resendCountdownTime, resendCountdownStartTime: $resendCountdownStartTime, transactionId: $transactionId, challengeId: $challengeId, nextChallengeName: $nextChallengeName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtpStateImpl &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.suspendedModalIsOpen, suspendedModalIsOpen) ||
                other.suspendedModalIsOpen == suspendedModalIsOpen) &&
            (identical(other.resendAvailable, resendAvailable) ||
                other.resendAvailable == resendAvailable) &&
            (identical(other.resendCountdownTime, resendCountdownTime) ||
                other.resendCountdownTime == resendCountdownTime) &&
            (identical(
                    other.resendCountdownStartTime, resendCountdownStartTime) ||
                other.resendCountdownStartTime == resendCountdownStartTime) &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.challengeId, challengeId) ||
                other.challengeId == challengeId) &&
            (identical(other.nextChallengeName, nextChallengeName) ||
                other.nextChallengeName == nextChallengeName));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      model,
      type,
      suspendedModalIsOpen,
      resendAvailable,
      resendCountdownTime,
      resendCountdownStartTime,
      transactionId,
      challengeId,
      nextChallengeName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OtpStateImplCopyWith<_$OtpStateImpl> get copyWith =>
      __$$OtpStateImplCopyWithImpl<_$OtpStateImpl>(this, _$identity);
}

abstract class _OtpState implements OtpState {
  const factory _OtpState(final InputFieldModel model,
      {required final OtpType type,
      final bool suspendedModalIsOpen,
      final bool resendAvailable,
      final int resendCountdownTime,
      final DateTime? resendCountdownStartTime,
      final String transactionId,
      final String challengeId,
      final String? nextChallengeName}) = _$OtpStateImpl;

  @override
  InputFieldModel get model;
  @override
  OtpType get type;
  @override
  bool get suspendedModalIsOpen;
  @override
  bool get resendAvailable;
  @override
  int get resendCountdownTime;
  @override
  DateTime? get resendCountdownStartTime;
  @override
  String get transactionId;
  @override
  String get challengeId;
  @override
  String? get nextChallengeName;
  @override
  @JsonKey(ignore: true)
  _$$OtpStateImplCopyWith<_$OtpStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
