abstract class PasscodeValidator {
  bool isValidPasscode(String passcode);
}

class PasscodeValidatorImpl implements PasscodeValidator {
  @override
  bool isValidPasscode(String passcode) {
    final allNumbersAreSameRegex = RegExp(r'^([0-9])\1*$');

    return !(allNumbersAreSameRegex.hasMatch(passcode) ||
        _isPasscodeConsecutive(passcode));
  }

  bool _isPasscodeConsecutive(String passcode) {
    final digitsList = passcode.split('').map(int.parse).toList();

    final consecutiveGrowing =
        _everyPair(digitsList, (prev, cur) => prev + 1 == cur);
    final consecutiveDeclining =
        _everyPair(digitsList, (prev, cur) => prev - 1 == cur);

    return consecutiveGrowing || consecutiveDeclining;
  }

  bool _everyPair(List<int> list, bool Function(int prev, int cur) predicate) {
    for (var idx = 1; idx < list.length; idx++) {
      if (!predicate(list[idx - 1], list[idx])) {
        return false;
      }
    }

    return true;
  }
}
