import 'package:wio_app_core_api/index.dart';

class _Constants {
  static const String requiredActionScreenName = 'setup_passcode_info_screen';
  static const flow = 'setup_passcode_flow';
}

class SetupPasscodeInfoAnalytics {
  final AnalyticsEventTracker _tracker;

  SetupPasscodeInfoAnalytics({
    required AnalyticsAbstractTrackerFactory abstractTrackerFactory,
  }) : _tracker = abstractTrackerFactory.get(
          screenName: _Constants.requiredActionScreenName,
          tracker: AnalyticsTracker.mixpanel,
        );

  void trackEvent(
    RequiredActionsEvents event, {
    Map<String, String>? properties,
  }) {
    final args = {'flow': _Constants.flow};
    if (properties != null) {
      args.addAll(properties);
    }

    _tracker.track(
      AnalyticsEvent.simple(
        event.eventName,
        properties: args,
      ),
    );
  }

  void onBackPressed() => _tracker.clickBack();
}

enum RequiredActionsEvents {
  continueSelected,
  skipSelected,
}

extension RequiredActionsEventsName on RequiredActionsEvents {
  String get eventName => switch (this) {
        RequiredActionsEvents.continueSelected =>
          'setup_passcode_info_continue_button_pressed',
        RequiredActionsEvents.skipSelected =>
          'setup_passcode_info_skip_button_pressed',
      };
}
