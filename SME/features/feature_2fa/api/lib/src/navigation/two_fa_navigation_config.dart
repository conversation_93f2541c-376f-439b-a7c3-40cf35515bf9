import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

class TwoFAFeatureNavigationConfig extends FeatureNavigationConfig {
  static const name = '2fa_feature';

  final String phoneNumber;
  final RedirectedScreenType redirectScreenType;

  const TwoFAFeatureNavigationConfig({
    this.phoneNumber = '',
    this.redirectScreenType = RedirectedScreenType.redirectDefault,
  }) : super(name);

  @override
  String toString() => 'TwoFAFeatureNavigationConfig{}';
}

enum RedirectedScreenType {
  redirectDefault,
  cardDetails,
  loginPage,
}
