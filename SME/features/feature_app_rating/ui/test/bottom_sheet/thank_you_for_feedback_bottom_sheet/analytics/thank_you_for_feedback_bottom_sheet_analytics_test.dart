import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_app_rating_ui/src/bottom_sheet/thank_you_for_feedback_bottom_sheet/analytics/thank_you_for_feedback_bottom_sheet_analytics.dart';

import '../../../feature_app_rating_ui_mocks.dart';

const _expectedScreenName = 'thanks_for_feedback_bottom_sheet';

void main() {
  late MockAnalyticsAbstractTrackerFactory analyticsFactory;
  late MockAnalyticsEventTracker tracker;
  late ThankYouForFeedbackBottomSheetAnalytics analytics;

  setUpAll(featureAppRatingUiRegisterFallbackValues);

  setUp(() {
    analyticsFactory = MockAnalyticsAbstractTrackerFactory();
    tracker = MockAnalyticsEventTracker();
    when(() => tracker.screenName).thenReturn(_expectedScreenName);
    when(
      () => analyticsFactory.get(
        screenName: any(named: 'screenName'),
        tracker: any(named: 'tracker'),
      ),
    ).thenReturn(tracker);

    analytics = ThankYouForFeedbackBottomSheetAnalytics(
      analyticsFactory: analyticsFactory,
    );
  });

  test('Should be created with mixpanel tracker', () {
    // Assert
    verify(
      () => analyticsFactory.get(
        screenName: _expectedScreenName,
        tracker: AnalyticsTracker.mixpanel,
      ),
    );
  });

  test('Should send Show thanks for feedback bottom sheet event', () {
    // Act
    analytics.viewThankYouForFeedbackBottomSheet();

    // Assert
    verify(
      () => tracker.track(
        const AnalyticsEvent(
          screenName: _expectedScreenName,
          action: AnalyticsAction.view,
          targetType: AnalyticsTargetType.bottom_sheet,
          target: 'thank_you_for_feedback_bottom_sheet',
        ),
      ),
    );
  });

  test('Should send Close thanks for feedback bottom sheet event', () {
    // Act
    analytics.clickThankYouBottomSheetDismiss();

    // Assert
    verify(
      () => tracker.track(
        const AnalyticsEvent(
          screenName: _expectedScreenName,
          action: AnalyticsAction.click,
          targetType: AnalyticsTargetType.bottom_sheet,
          target: 'thank_you_for_feedback_bottom_sheet',
        ),
      ),
    );
  });
}
