import 'dart:async';

import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:wio_feature_app_rating_api/wio_feature_app_rating_api.dart';
import 'package:wio_feature_app_rating_ui/src/flow_controller/app_rating_flow_controller.dart';

/// A navigator route used to start app rating flow.
/// It renders an empty overlay stateful widget which initializes a flow of
/// bottom sheets and exits when flow is complete.
class AppRatingRoute extends OverlayRoute<void> {
  final AppRatingTriggerPoint triggerPoint;

  AppRatingRoute({
    required this.triggerPoint,
  }) : super();

  @override
  Iterable<OverlayEntry> createOverlayEntries() {
    return [
      OverlayEntry(
        maintainState: true,
        builder: (context) => _AppRatingFlow(triggerPoint: triggerPoint),
      ),
    ];
  }
}

class _AppRatingFlow extends StatefulWidget {
  final AppRatingTriggerPoint triggerPoint;

  const _AppRatingFlow({
    required this.triggerPoint,
  });

  @override
  State<_AppRatingFlow> createState() => __AppRatingFlowState();
}

class __AppRatingFlowState extends State<_AppRatingFlow> {
  final appRatingFlowController =
      DependencyProvider.get<AppRatingFlowController>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      unawaited(_triggerFlow());
    });
  }

  @override
  Widget build(BuildContext context) {
    // Block interactions with the screen.
    return SizedBox.expand(
      // ignore: use_colored_box
      child: Container(color: Colors.transparent),
    );
  }

  Future<void> _triggerFlow() async {
    final navigator = Navigator.of(context);
    await appRatingFlowController.showFlow(widget.triggerPoint);
    navigator.pop();
  }
}
