import 'package:sme_rest_api/sme_rest_api.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_invoices_v2_api/feature_invoices_api.dart';
import 'package:wio_feature_invoices_v2_impl/src/data/repositories/stripe_mapper.dart';

import '../../stub_data.dart';

void main() {
  late StripeMapperImpl mapper;
  late ErrorReporter errorReporter;

  setUp(() {
    errorReporter = MockErrorReporter();
    mapper = StripeMapperImpl(reporter: errorReporter);
  });

  test(
    'Stripe account info mapped correctly',
    () {
      // Arrange
      const mockUrl = 'https://demo.com';
      const dto = StripeStatusResponse(
        accountReviewUrl: mockUrl,
        status: 'ONBOARDING_REJECTED',
        refreshUrl: mockUrl,
        returnUrl: mockUrl,
        stripeAccountId: 'id',
      );
      final expected = StubData.getStripeAccountInfo(
        stripeAccountId: 'id',
        status: StripeAccountStatus.onboardingRejected,
      );

      // Act
      final result = mapper.mapStatusResponseToAccountInfo(dto);

      // Assert
      expect(result, expected);
    },
  );

  test(
    'Stripe account info pure mapped correctly',
    () {
      // Arrange
      const mockUrl = 'https://demo.com';
      const dto = StripeStatusResponse(
        status: 'ONBOARDING_REJECTED',
        refreshUrl: mockUrl,
        returnUrl: mockUrl,
        stripeAccountId: 'id',
      );

      const expected = StripeAccountInfo.pure(
        status: StripeAccountStatus.onboardingRejected,
      );

      // Act
      final result = mapper.mapStatusResponseToAccountInfo(dto);

      // Assert
      expect(result, expected);
    },
  );

  test('mapAccountLinkToStripeAccountInfo test', () {
    // Arrange
    const mockUrl = 'https://demo.com';
    const dto = AccountLinkResponse(
      accountReviewUrl: mockUrl,
      status: 'ONBOARDING_REJECTED',
      refreshUrl: mockUrl,
      returnUrl: mockUrl,
      stripeAccountId: 'id',
    );
    final expected = StubData.getStripeAccountInfo(
      stripeAccountId: 'id',
      status: StripeAccountStatus.onboardingRejected,
    );

    // Act
    final result = mapper.mapAccountLinkToStripeAccountInfo(dto);

    // Assert
    expect(result, expected);
  });

  test('mapStripeAccountLinkToAccountStatus test', () {
    // Arrange
    const mockUrl = 'https://demo.com';
    const dto = AccountLinkResponse(
      accountReviewUrl: mockUrl,
      status: 'ONBOARDING_REJECTED',
      refreshUrl: mockUrl,
      returnUrl: mockUrl,
      stripeAccountId: 'id',
    );
    const expected = StripeAccountStatus.onboardingRejected;

    // Act
    final result = mapper.mapStripeAccountLinkToAccountStatus(dto);

    // Assert
    expect(result, expected);
  });
}
