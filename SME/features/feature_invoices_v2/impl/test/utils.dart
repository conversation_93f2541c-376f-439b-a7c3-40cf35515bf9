import 'dart:convert';

import 'package:data/graph_ql_network_manager/graph_ql_network_manager.dart';

class Utils {
  /// fully encode and decode json to prevent typed values in map
  /// instead of primitive values
  static GraphQLResponse dataResponse(Map<String, dynamic> map) {
    final jsonString = jsonEncode(map);
    final raw = jsonDecode(jsonString) as Map<String, Object?>;

    return GraphQLResponse(raw, raw);
  }
}
