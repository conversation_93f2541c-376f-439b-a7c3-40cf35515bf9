import 'package:domain/domain.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_invoices_v2_api/feature_invoices_api.dart';

abstract class StripeRepository implements Clearable {
  /// {@macro stripe.getStripeAccountInfo}
  Stream<Data<StripeAccountInfo>> getStripeAccountInfo({
    required bool shouldForce,
  });

  /// {@macro stripe.getStripeAccountStatus}
  Future<StripeCompletedAccountInfo> createStripeAccount();

  /// {@macro stripe.getStripeInfo}
  Future<StripeAccountInfo?> getStripeInfo({bool getCachedData = false});

  /// {@macro stripe.getStripeAccountDetails}
  Future<StripeAccountInfo> getStripeAccountDetails({
    required String stripeAccountId,
  });

  /// {@macro stripe.getStripeAccountStatus}
  Future<StripeAccountStatus> getStripeAccountStatus({
    required String stripeAccountId,
  });
}
