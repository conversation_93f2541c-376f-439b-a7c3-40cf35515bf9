import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sme_feature_transactions_api/feature_transactions_api.dart';

part 'link_payment_list_state.freezed.dart';

@Freezed(
  map: FreezedMapOptions.none,
  fromJson: false,
  toJson: false,
  when: FreezedWhenOptions.none,
)
sealed class LinkPaymentListState {
  const factory LinkPaymentListState.initial() = InitialLinkPaymentListState;

  const factory LinkPaymentListState.loading() = LoadingLinkPaymentListState;

  const factory LinkPaymentListState.empty() = EmptyLinkPaymentListState;

  const factory LinkPaymentListState.error({
    required String accountID,
  }) = ErrorLinkPaymentListState;

  const factory LinkPaymentListState.loaded({
    required String accountID,
    @Default([]) List<String> selectedTransactionIds,
    @Default([]) List<Transaction> transactions,
    @Default(false) bool hasReachedMax,
  }) = LoadedLinkPaymentListState;
}
