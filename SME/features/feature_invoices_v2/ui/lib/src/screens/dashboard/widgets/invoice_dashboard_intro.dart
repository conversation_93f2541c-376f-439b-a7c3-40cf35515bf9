import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_invoice_ui/feature_invoice_mobile_ui.dart';

class InvoiceDashboardIntro extends StatelessWidget {
  final String userName;
  final VoidCallback onPlusPressed;

  const InvoiceDashboardIntro({
    super.key,
    required this.userName,
    required this.onPlusPressed,
  });

  @override
  Widget build(BuildContext context) {
    final localization = InvoicesLocalizations.of(context);
    return ColoredBox(
      color: CompanyColorPointer.background1.colorOf(context),
      child: SafeArea(
        child: Stack(
          children: [
            Positioned(
              bottom: 0,
              right: 0,
              child: CompanyImage(
                const CompanyImageModel(
                  image: CompanyImageProvider.asset(
                    name: 'assets/images/man.webp',
                    package: 'wio_feature_invoice_ui',
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              left: 0,
              height: 150,
              child: RotatedBox(
                quarterTurns: 2,
                child: Container(
                  decoration: BoxDecoration(gradient: CompanyGradients.white()),
                ),
              ),
            ),
            Positioned(
              top: 32,
              left: 32,
              right: 32,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(
                    child: CompanyRichText(
                      CompanyRichTextModel(
                        normalStyle: CompanyTextStylePointer.h1,
                        textAlign: TextAlign.start,
                        maxLines: 10,
                        accentStyle: CompanyTextStylePointer.h1,
                        text: localization.invoiceIntroHeader(userName),
                        highlightedTextModels: [
                          HighlightedTextModel(
                            localization.invoiceIntroHighlight,
                          ),
                        ],
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: onPlusPressed,
                    child: RichTile(
                      model: const RichTileModel(
                        mainSize: 44,
                        mainModel: TileModel.icon(
                          backgroundColor: CompanyColorPointer.primary3,
                          iconColor: CompanyColorPointer.surface6,
                          iconSize: CompanyIconSize.large,
                          icon:
                              GraphicAssetPointer.icon(CompanyIconPointer.plus),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
