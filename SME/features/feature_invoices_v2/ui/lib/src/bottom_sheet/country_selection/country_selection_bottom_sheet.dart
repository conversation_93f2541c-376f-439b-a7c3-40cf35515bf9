import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_feature_invoice_ui/feature_invoice_mobile_ui.dart';
import 'package:wio_feature_invoice_ui/src/navigation/config/country_selection_bottom_sheet_config.dart';

class PhoneCountrySelectionBottomSheet extends StatefulWidget {
  final List<Country> countries;

  const PhoneCountrySelectionBottomSheet({
    required this.countries,
    super.key,
  });

  @override
  State<PhoneCountrySelectionBottomSheet> createState() =>
      _PhoneCountrySelectionBottomSheetState();
}

class _PhoneCountrySelectionBottomSheetState
    extends State<PhoneCountrySelectionBottomSheet> {
  final _controller = TextEditingController();

  String get searchString => _controller.text;

  List<Country> get countries => searchString.isEmpty
      ? widget.countries
      : widget.countries
          .where((country) => country.name.containsIgnoreCase(_controller.text))
          .toList();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = InvoicesLocalizations.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(12.0, 16.0, 12.0, 12.0),
          child: SearchField(
            model: SearchFieldModel(
              labelText: localizations.invoicesAddEditClientSearch,
            ),
            textEditingController: _controller,
            onChanged: (_) => setState(() {}),
          ),
        ),
        Expanded(
          child: _CountriesList(countries: countries),
        ),
      ],
    );
  }
}

class _CountriesList extends StatelessWidget {
  final List<Country> countries;

  const _CountriesList({required this.countries});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: countries.length,
      itemBuilder: (context, index) {
        final country = countries[index];

        return ListBox(
          listBoxModel: ListBoxModel(
            applyPadding: true,
            leftPartModel: ListBoxFlagModel(flag: country.flag),
            textModel: ListBoxTextModel(title: country.name),
            highlightInteraction: true,
          ),
          onPressed: () => Navigator.maybeOf(context)
              ?.pop(CountrySelectionResult.singleCountry(country)),
        );
      },
    );
  }
}
