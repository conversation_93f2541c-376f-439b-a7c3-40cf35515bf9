import 'dart:ui';

import 'package:app_mobile_invoice/route_manager/invoice_route_manager.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:domain/stream/exhaust_stream_executor.dart';
import 'package:feature_navigation_adapter_api/feature_navigation_adapter_api.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:ui/cubit/core/loading_provider.dart';
import 'package:ui/l10n/common_localizations.g.dart';
import 'package:ui/ota_lokalisation/ota_lokalization.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_invoice_ui/src/extension/tab_extension.dart';
import 'package:wio_feature_invoice_ui/src/l10n/invoices_localizations.g.dart';
import 'package:wio_feature_invoice_ui/src/screens/dashboard/invoice_list/invoice_list_cubit.dart';
import 'package:wio_feature_invoice_ui/src/screens/dashboard/invoice_list/invoice_list_state.dart';
import 'package:wio_feature_invoice_ui/src/utils/tab_label.dart';
import 'package:wio_feature_invoices_v2_api/domain/models/invoice_action.dart';
import 'package:wio_feature_invoices_v2_api/domain/models/pagination_invoices.dart';
import 'package:wio_feature_invoices_v2_api/feature_invoices_api.dart';

import '../mocks.dart';
import '../stub_data.dart';

void main() {
  late InvoiceListCubit cubit;
  late InvoicesLocalizations localizations;
  late CommonLocalizations commonLocalizations;
  late Logger logger;
  late InvoicesInteractor invoicesInteractor;
  late NavigationProvider mockNavigationProvider;
  late LoadingProvider mockLoadingProvider;
  late ToastMessageProvider mockToastMessageProvider;
  late ExhaustStreamExecutor exhaustStreamExecutor;

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    localizations = await InvoicesLocalizations.load(const Locale('en'));

    registerFallbackValue(NotificationToastMessageConfiguration.error('error'));
    registerFallbackValue(
      LegacyBottomSheetNavigationConfig<String>(
        id: 'id',
        featureId: 'featureId',
      ),
    );
    registerFallbackValue(
      const LegacyScreenNavigationConfig(
        id: 'id',
        feature: 'feature',
        body: {},
      ),
    );
  });
  setUp(() {
    logger = MockLogger();
    invoicesInteractor = MockInvoicesInteractor();
    commonLocalizations = MockCommonLocalizations();
    mockNavigationProvider = MockNavigationProvider();
    mockLoadingProvider = MockLoadingProvider();
    mockToastMessageProvider = MockToastMessageProvider();
    exhaustStreamExecutor = ExhaustStreamExecutorImpl();

    cubit = InvoiceListCubit(
      invoicesInteractor: invoicesInteractor,
      navigationProvider: mockNavigationProvider,
      loadingProvider: mockLoadingProvider,
      commonLocalizations: commonLocalizations,
      logger: logger,
      toastMessageProvider: mockToastMessageProvider,
      exhaustStreamExecutor: exhaustStreamExecutor,
      invoiceUILocalizations: localizations,
    );
  });

  test(' bloc init state', () {
    expect(cubit.state, const InvoiceListState.loading());
  });

  blocTest<InvoiceListCubit, InvoiceListState>(
    'emit [loaded] state',
    build: () => cubit,
    setUp: () {
      when(() => invoicesInteractor.invoiceActionStream)
          .thenAnswer((_) => Stream.fromIterable([]));
    },
    act: (cubit) => cubit.setInitialInvoiceList(StubData.getInvoiceList(), 2),
    expect: () => [
      InvoiceListState.loaded(
        total: 2,
        invoice: StubData.getInvoiceList(),
      ),
    ],
  );

  blocTest<InvoiceListCubit, InvoiceListState>(
    'navigate to client list screen',
    build: () => cubit,
    setUp: () {
      when(() => mockNavigationProvider.push(any())).justCompleteAsync();
      when(() => invoicesInteractor.invoiceActionStream)
          .thenAnswer((_) => Stream.fromIterable([]));
    },
    act: (cubit) => cubit.navigateToClientList(),
    verify: (_) => {
      verify(
        () => mockNavigationProvider.push(
          const LegacyScreenNavigationConfig(
            id: InvoiceRouteManager.manageClientList,
            feature: InvoiceRouteManager.packageName,
            body: {},
          ),
        ),
      ).calledOnce,
    },
  );

  blocTest<InvoiceListCubit, InvoiceListState>(
    'navigate to invoice detail screen',
    build: () => cubit,
    setUp: () {
      when(() => mockNavigationProvider.push(any())).justCompleteAsync();
    },
    act: (cubit) => cubit.navigateToInvoiceDetails(
      invoiceId: '123456',
      invoiceNumber: 'INV-12-924',
    ),
    verify: (_) => {
      verify(
        () => mockNavigationProvider.push(
          const LegacyScreenNavigationConfig(
            id: InvoiceRouteManager.invoiceDetail,
            feature: InvoiceRouteManager.packageName,
            body: {
              'invoice_id': '123456',
              'invoiceNumber': 'INV-12-924',
            },
          ),
        ),
      ).calledOnce,
    },
  );

  blocTest<InvoiceListCubit, InvoiceListState>(
    'calls deleteInvoice when onDeleteInvoiceTap is called',
    build: () => cubit,
    setUp: () {
      when(
        () => mockNavigationProvider.showBottomSheet<String>(any()),
      ).thenAnswer((_) async => 'delete_invoice');
      when(() => invoicesInteractor.deleteInvoice(any()))
          .justAnswerEmptyAsync();
      when(
        () => invoicesInteractor.addInvoiceEventToStream(
          const InvoiceAction.delete(from: DeleteFrom.dashboardList),
        ),
      ).justComplete();
    },
    seed: () => InvoiceListState.loaded(
      total: 2,
      invoice: StubData.getInvoiceList(),
      selectedTab: TabLabel.draft,
    ),
    act: (cubit) => cubit.onDeleteInvoiceTap(
      invoiceStatus: InvoiceStatus.draft,
      invoiceNumber: 'INV001',
      invoiceId: '121313',
    ),
    verify: (_) {
      verify(() => invoicesInteractor.deleteInvoice('121313')).calledOnce;
    },
    expect: () => [
      InvoiceListState.loaded(
        total: 1,
        selectedTab: TabLabel.draft,
        invoice: StubData.getInvoiceList()
            .where((element) => element.id != '121313')
            .toList(),
      ),
    ],
  );

  blocTest<InvoiceListCubit, InvoiceListState>(
    'calls deleteInvoice when onDeleteInvoiceTap is called '
    'and invoice is paid or partially paid',
    build: () => cubit,
    setUp: () {
      when(() => mockNavigationProvider.showBottomSheet<String>(any()))
          .thenAnswer((_) async => 'delete_invoice');
      when(() => invoicesInteractor.deleteInvoice(any()))
          .justAnswerEmptyAsync();
    },
    seed: () => InvoiceListState.loaded(
      total: 2,
      invoice: StubData.getInvoiceList(),
      selectedTab: TabLabel.draft,
    ),
    act: (cubit) => cubit.onDeleteInvoiceTap(
      invoiceStatus: InvoiceStatus.paid,
      invoiceNumber: 'INV001',
      invoiceId: '121313',
    ),
    verify: (_) {
      verifyZeroInteractions(invoicesInteractor);
    },
  );

  blocTest<InvoiceListCubit, InvoiceListState>(
    'calls deleteInvoice when onDeleteInvoiceTap is called '
    'some exception occurs',
    build: () => cubit,
    setUp: () {
      when(() => mockNavigationProvider.showBottomSheet<String>(any()))
          .thenAnswer((_) async => 'delete_invoice');
      when(() => invoicesInteractor.deleteInvoice(any()))
          .thenThrow(Exception('Failed to delete invoice'));
    },
    seed: () => InvoiceListState.loaded(
      total: 2,
      invoice: StubData.getInvoiceList(),
      selectedTab: TabLabel.draft,
    ),
    act: (cubit) => cubit.onDeleteInvoiceTap(
      invoiceStatus: InvoiceStatus.draft,
      invoiceNumber: 'INV001',
      invoiceId: '121313',
    ),
    verify: (_) {
      verify(() => mockLoadingProvider.loading(false)).calledOnce;
      verify(() => invoicesInteractor.deleteInvoice('121313')).calledOnce;
    },
  );

  blocTest<InvoiceListCubit, InvoiceListState>(
    'emits [InvoiceListLoadedState] when fetchInvoiceList is successful',
    build: () => cubit,
    setUp: () {
      when(
        () => invoicesInteractor.getInvoices(
          offset: 0,
          limit: 20,
          statuses: TabLabel.all.resolveInvoiceStatus,
        ),
      ).thenAnswer(
        (_) async => PaginationInvoices(
          total: 2,
          invoices: StubData.getInvoiceList(),
        ),
      );
    },
    act: (cubit) => cubit.fetchInvoiceList(TabLabel.all),
    expect: () => [
      const InvoiceListState.loading(),
      isA<InvoiceListLoadedState>()
          .having(
            (state) => state.invoice,
            'invoice',
            StubData.getInvoiceList(),
          )
          .having((state) => state.total, 'total', 2)
          .having((state) => state.selectedTab, 'selectedTab', TabLabel.all),
    ],
  );

  blocTest<InvoiceListCubit, InvoiceListState>(
    'emits [InvoiceListErrorState] when fetchInvoiceList fails',
    build: () => cubit,
    setUp: () {
      when(
        () => invoicesInteractor.getInvoices(
          offset: 0,
          limit: 20,
          statuses: TabLabel.all.resolveInvoiceStatus,
        ),
      ).thenThrow(Exception('Failed to fetch invoices'));
    },
    act: (cubit) => cubit.fetchInvoiceList(TabLabel.all),
    expect: () =>
        [const InvoiceListState.loading(), const InvoiceListState.error()],
  );

  blocTest<InvoiceListCubit, InvoiceListState>(
    'navigates to editInvoice when onEditInvoiceTap is called and'
    ' invoice is not paid or partially paid',
    build: () => cubit,
    setUp: () {
      when(() => mockNavigationProvider.push(any())).justCompleteAsync();
    },
    act: (cubit) => cubit.onEditInvoiceTap(
      invoiceNumber: 'INV001',
      invoiceId: 'id123',
      paymentStatus: InvoiceStatus.draft,
    ),
    verify: (_) {
      verify(
        () => mockNavigationProvider.push(
          const LegacyScreenNavigationConfig(
            id: InvoiceRouteManager.editInvoice,
            feature: InvoiceRouteManager.packageName,
            body: {'invoice_id': 'id123'},
          ),
        ),
      ).calledOnce;
    },
  );

  blocTest<InvoiceListCubit, InvoiceListState>(
    'shows rejectionBottomSheet when onEditInvoiceTap is called '
    'and invoice is paid or partially paid',
    build: () => cubit,
    setUp: () {
      when(() => mockNavigationProvider.showBottomSheet<void>(any()))
          .justCompleteAsync();
    },
    act: (cubit) => cubit.onEditInvoiceTap(
      invoiceNumber: 'INV001',
      invoiceId: 'id123',
      paymentStatus: InvoiceStatus.paid,
    ),
    verify: (_) {
      verify(
        () => mockNavigationProvider.showBottomSheet<void>(
          any(
            that: isA<LegacyBottomSheetNavigationConfig<void>>()
                .having(
              (config) => config.id,
              'id',
              InvoiceRouteManager.editInvoiceRejectionBottomSheet,
            )
                .having(
              (config) => config.args,
              'args',
              {'invoiceNumber': 'INV001'},
            ),
          ),
        ),
      ).calledOnce;
    },
  );

  blocTest(
    'Should not call the pagination API if reach to the last page ',
    build: () => cubit,
    seed: () => InvoiceListState.loaded(
      total: 2,
      invoice: StubData.getInvoiceList(),
      selectedTab: TabLabel.draft,
    ),
    act: (cubit) => cubit.getInvoices(selectedTab: TabLabel.draft),
    verify: (_) {
      verifyZeroInteractions(invoicesInteractor);
    },
  );

  test('Should call the pagination API if not reach to the last page',
      () async {
    //setup
    when(
      () => invoicesInteractor.getInvoices(
        limit: any(named: 'limit'),
        offset: any(named: 'offset'),
        statuses: TabLabel.draft.resolveInvoiceStatus,
      ),
    ).justAnswerAsync(
      PaginationInvoices(total: 3, invoices: [StubData.getInvoice()]),
    );

    //seed
    cubit
      ..emit(
        InvoiceListState.loaded(
          total: 3,
          invoice: StubData.getInvoiceList(),
          selectedTab: TabLabel.draft,
        ),
      )

      //act
      ..getInvoices(selectedTab: TabLabel.draft);
    await flushFutures();
    //  assert
    expect(
      cubit.state,
      isA<InvoiceListLoadedState>()
          .having(
            (loadedState) => loadedState.invoice.length,
            'invoice list length',
            3,
          )
          .having(
            (loadedState) => loadedState.selectedTab,
            'selected Tab',
            TabLabel.draft,
          ),
    );
    verify(
      () => invoicesInteractor.getInvoices(
        limit: any(named: 'limit'),
        offset: any(named: 'offset'),
        statuses: TabLabel.draft.resolveInvoiceStatus,
      ),
    ).calledOnce;
  });

  test('return proper toString()', () {
    expect(cubit.toString(), 'invoice_list_cubit');
  });
}
