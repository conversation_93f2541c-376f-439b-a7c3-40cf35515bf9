part of '../invoice_payments_screen.dart';

class _InvoicePaymentsBody extends StatelessWidget {
  const _InvoicePaymentsBody();

  @override
  Widget build(BuildContext context) {
    final localizations = InvoicesUiLocalizations.of(context);
    final isStripeEnabled =
        context.watch<InvoicePaymentCubit>().state.isStripeEnabled;

    return BlocBuilder<InvoiceDetailsHostCubit, InvoiceDetailsHostState>(
      buildWhen: (_, s) => !s.isLoading,
      builder: (context, state) {
        return state.map(
          loading: (_) => const SizedBox.shrink(),
          loaded: (s) {
            final payments = s.invoice.payments;
            final isInvoicePaid = s.invoice.status == InvoiceStatus.paid;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  localizations.invoicePaymentsListSubheader,
                  style: context.textStyling.h6.copyWith(
                    color: context.colorStyling.primary1,
                  ),
                ),
                const SizedBox(height: 12.0),
                if (!isStripeEnabled && payments.isEmpty)
                  const _EmptyPlaceholder(),
                if (!isInvoicePaid) const _StripePaymentCard(),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        for (final payment in payments)
                          _PaymentItem(payment: payment),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}

class _PaymentItem extends StatelessWidget {
  final InvoicePayment payment;

  const _PaymentItem({
    required this.payment,
  });

  @override
  Widget build(BuildContext context) {
    return MainListItem(
      model: _getPaymentItemModel(),
      fixedActions: [
        Text(
          '+${payment.amount.withoutCodeFormat()}',
          style: context.textStyling.b1.copyWith(
            color: context.colorStyling.primary1,
          ),
        ),
        const SizedBox(width: 8.0),
        DesktopIconButton(
          model: const DesktopIconButtonModel.var1(
            icon: CompanyIconPointer.unlink,
          ),
          onClick: () => context.read<InvoicePaymentCubit>().unlinkPayment(
                invoice: context.invoiceDetailsHostLoadedState.invoice,
                payment: payment,
              ),
        ),
        const SizedBox(width: 12.0),
      ],
    );
  }

  MainListItemModel _getPaymentItemModel() {
    return MainListItemModel(
      id: payment.id,
      accountName: payment.debtorName,
      accountNumber: payment.date.formatTo(
        const DateTimePatternSME.ddMMMMySpace(),
      ),
      listIconModel: ListIconModel.text(
        text: nameToInitials(payment.debtorName),
        iconSize: ListIconSize.l,
        smallCircleModel:
            const SmallCircleModel.desktopIcon(icon: CompanyIconPointer.money),
      ),
    );
  }
}

class _StripePaymentCard extends StatelessWidget {
  const _StripePaymentCard();

  @override
  Widget build(BuildContext context) {
    final localizations = InvoicesUiLocalizations.of(context);
    final invoice = context.invoiceDetailsHostLoadedState.invoice;

    return BlocBuilder<InvoicePaymentCubit, InvoicePaymentState>(
      builder: (context, state) {
        final stripeStatus = state.mapOrNull(idle: (s) => s.stripeStatus) ??
            const InvoicePaymentStripeState.disabled();
        final amountText = invoice.invoiceTotal.toCodeOnRightFormat();
        final subtitleModel = CompanyRichTextModel(
          text: localizations.invoicePaymentsStripePaymentSubtitle,
          normalStyle: CompanyTextStylePointer.b3,
          normalTextColor: CompanyColorPointer.secondary3,
          accentStyle: CompanyTextStylePointer.b3,
          accentTextColor: CompanyColorPointer.primary1,
          maxLines: 1,
        );

        final model = stripeStatus.mapOrNull(
          generating: (_) => StripeLinkCardModel.wait(
            title: localizations.invoicePaymentsStripePaymentTitle,
            subtitleModel: subtitleModel,
            amountText: amountText,
          ),
          generated: (s) => StripeLinkCardModel.generated(
            title: localizations.invoicePaymentsStripePaymentTitle,
            subtitleModel: subtitleModel,
            amountText: amountText,
            link: s.uri.toString(),
          ),
          invoicePaid: (_) => StripeLinkCardModel.paid(
            title: localizations.invoicePaymentsStripePaymentTitle,
            subtitleModel: subtitleModel,
            amountText: amountText,
            paidText: localizations.invoicePaymentsStripePaidLabel,
          ),
        );

        if (model != null) {
          return StripeLinkCard(
            model,
            onCopy: () => stripeStatus.generatedLink != null
                ? _onCopyLink(context, stripeStatus.generatedLink!)
                : null,
          );
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  void _onCopyLink(BuildContext context, Uri uri) {
    final localizations = InvoicesUiLocalizations.of(context);

    Clipboard.setData(
      ClipboardData(
        text: uri.toString(),
      ),
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackbarWrapper(
        SnackbarModel.var1LowPriority(
          text: localizations.invoicePaymentsStripeLinkCopiedMessage,
          leftIcon: CompanyIconPointer.selection_selected,
        ),
      ),
    );
  }
}

class _EmptyPlaceholder extends StatelessWidget {
  const _EmptyPlaceholder();

  @override
  Widget build(BuildContext context) {
    final localizations = InvoicesUiLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.only(top: 12.0),
      child: Text(
        localizations.invoicePaymentsNoPaymentsMessage,
        style: context.textStyling.b1.copyWith(
          color: context.colorStyling.secondary4,
        ),
      ),
    );
  }
}
