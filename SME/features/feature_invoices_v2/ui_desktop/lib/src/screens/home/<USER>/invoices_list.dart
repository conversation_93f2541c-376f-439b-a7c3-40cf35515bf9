import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sme_core_utils/utils.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_feature_invoices_v2_api/feature_invoices_api.dart';
import 'package:wio_feature_invoices_v2_ui_desktop/locale/invoices_ui_desktop_localizations.g.dart';
import 'package:wio_feature_invoices_v2_ui_desktop/src/screens/home/<USER>/invoice_home_cubit.dart';
import 'package:wio_feature_invoices_v2_ui_desktop/src/screens/home/<USER>/delete_invoice_popup/delete_invoice_popup.dart';
import 'package:wio_feature_invoices_v2_ui_desktop/src/screens/home/<USER>/start_view.dart';
import 'package:wio_feature_invoices_v2_ui_desktop/src/screens/invoice_details/host/invoice_details_host.dart';
import 'package:wio_feature_invoices_v2_ui_desktop/src/utils/invoice_detail_trigger.dart';
import 'package:wio_feature_invoices_v2_ui_desktop/src/utils/invoice_status_tag_resolver.dart';
import 'package:wio_feature_invoices_v2_ui_desktop/src/utils/tab_utils.dart';
import 'package:wio_feature_invoices_v2_ui_desktop/src/widgets/invoices_main_list_item.dart';

class InvoicesList extends StatefulWidget {
  final List<Invoice> invoices;
  final TabLabel activeTab;

  const InvoicesList({
    required this.invoices,
    required this.activeTab,
    super.key,
  });

  @override
  State<InvoicesList> createState() => _InvoicesListState();
}

class _InvoicesListState extends State<InvoicesList> {
  final ScrollController controller = ScrollController();
  bool hasReachedMax = false;

  @override
  void initState() {
    if (widget.invoices.length < pageSize) {
      hasReachedMax = true;
    }

    controller.addListener(() {
      if (controller.position.maxScrollExtent * 0.8 <=
          controller.position.pixels) {
        if (!hasReachedMax) {
          BlocProvider.of<InvoiceHomeCubit>(context)
              .getInvoices(selectedTab: widget.activeTab);
        }
      }
    });
    super.initState();
  }

  @override
  void didUpdateWidget(covariant InvoicesList oldWidget) {
    if (widget.invoices.length - oldWidget.invoices.length < pageSize) {
      hasReachedMax = true;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bloc = BlocProvider.of<InvoiceHomeCubit>(context);
    final localizations = InvoicesUiLocalizations.of(context);

    return ListView.builder(
      controller: controller,
      itemCount:
          hasReachedMax ? widget.invoices.length : widget.invoices.length + 1,
      itemBuilder: (context, index) {
        if (index == widget.invoices.length && !hasReachedMax) {
          return const Center(child: LazyLoadSpinner());
        }

        final invoice = widget.invoices[index];

        return InvoicesMainListItem(
          title: '${invoice.invoiceNumber}, '
              '${invoice.customer.customerName}',
          dueDate: '${localizations.invoiceMainListItemDue} '
              '${invoice.dueDate.formatTo(
            const DateTimePatternSME.ddMMMMyyyySpace(),
          )}',
          onClick: () => InvoiceDetailsNotification(
            invoiceID: invoice.id,
            trigger: InvoiceDetailsTrigger.none,
            onCloseDrawer: (id) => bloc.onCloseDrawer(invoiceId: id),
            onDeleteInvoiceCloseDrawer: (id) =>
                bloc.onDeleteInvoiceCloseDrawer(invoiceId: id),
          ).dispatch(context),
          menuOptions: [
            if (invoice.status != InvoiceStatus.draft &&
                invoice.status != InvoiceStatus.paid) ...[
              MenuOption(
                label:
                    localizations.invoiceMainListItemAddPayment.toUpperCase(),
                onSelected: () => InvoiceDetailsNotification(
                  invoiceID: invoice.id,
                  trigger: InvoiceDetailsTrigger.invoicePayments,
                  onCloseDrawer: (id) => bloc.onCloseDrawer(invoiceId: id),
                  onDeleteInvoiceCloseDrawer: (id) =>
                      bloc.onDeleteInvoiceCloseDrawer(invoiceId: id),
                ).dispatch(context),
              ),
              MenuOption(
                label:
                    localizations.invoiceMainListItemSendReminder.toUpperCase(),
                onSelected: () => InvoiceDetailsNotification(
                  invoiceID: invoice.id,
                  trigger: InvoiceDetailsTrigger.sendReminder,
                  onCloseDrawer: (id) => bloc.onCloseDrawer(invoiceId: id),
                  onDeleteInvoiceCloseDrawer: (id) =>
                      bloc.onDeleteInvoiceCloseDrawer(invoiceId: id),
                ).dispatch(context),
              ),
            ],
            if (invoice.status != InvoiceStatus.draft)
              MenuOption(
                label:
                    localizations.invoiceMainListItemDownloadPdf.toUpperCase(),
                onSelected: () => bloc.downloadInvoice(
                  invoiceId: invoice.id,
                  invoiceNumber: invoice.invoiceNumber,
                ),
              ),
            if (invoice.status != InvoiceStatus.paid) ...[
              MenuOption(
                label: localizations.invoiceMainListItemEdit.toUpperCase(),
                onSelected: () =>
                    bloc.goToCreateEditNewInvoice(invoiceId: invoice.id),
              ),
              MenuOption(
                label: localizations.invoiceMainListItemDelete.toUpperCase(),
                onSelected: () => _showDeleteInvoicePopup(invoice),
              ),
            ],
          ],
          amount: invoice.invoiceTotal,
          tag: InvoiceStatusTagResolver.resolveTag(
            localizations,
            invoice.status,
          ),
        );
      },
    );
  }

  Future<void> _showDeleteInvoicePopup(Invoice invoice) async {
    final success = await showDialog<bool>(
      context: context,
      builder: (context) => DeleteInvoicePopup(invoice: invoice),
    );
    if (success != null) {
      if (mounted && success) {
        await context.read<InvoiceHomeCubit>().load();
      }
    }
  }
}
