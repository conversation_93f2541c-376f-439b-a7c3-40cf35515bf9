import 'package:freezed_annotation/freezed_annotation.dart';

part 'key_fact_statement_content_dto.freezed.dart';

part 'key_fact_statement_content_dto.g.dart';

@freezed
class KeyFactStatementContentDto with _$KeyFactStatementContentDto {
  factory KeyFactStatementContentDto({
    @JsonKey(name: 'Header') HeaderDto? header,
    @JsonKey(name: 'MainBlocks') List<MainBlockDto>? mainBlocks,
    @JsonKey(name: 'Footer') String? footer,
  }) = _KeyFactStatementContentDto;

  factory KeyFactStatementContentDto.fromJson(Map<String, dynamic> json) =>
      _$KeyFactStatementContentDtoFromJson(json);
}

@freezed
class HeaderDto with _$HeaderDto {
  factory HeaderDto({
    @JsonKey(name: 'Name') String? name,
    @JsonKey(name: 'Statements') List<String>? statements,
  }) = _HeaderDto;

  factory HeaderDto.fromJson(Map<String, dynamic> json) =>
      _$HeaderDtoFromJson(json);
}

@freezed
class MainBlockDto with _$MainBlockDto {
  factory MainBlockDto({
    @JsonKey(name: 'Name') String? name,
    @JsonKey(name: 'LogoUrl') String? icon,
    @JsonKey(name: 'Blocks') List<BlockDto>? blocks,
    @JsonKey(name: 'Statements') List<String>? statements,
  }) = _MainBlockDto;

  factory MainBlockDto.fromJson(Map<String, dynamic> json) =>
      _$MainBlockDtoFromJson(json);
}

@freezed
class BlockDto with _$BlockDto {
  factory BlockDto({
    @JsonKey(name: 'Name') String? name,
    @JsonKey(name: 'Statements') List<String>? statements,
  }) = _BlockDto;

  factory BlockDto.fromJson(Map<String, dynamic> json) =>
      _$BlockDtoFromJson(json);
}
