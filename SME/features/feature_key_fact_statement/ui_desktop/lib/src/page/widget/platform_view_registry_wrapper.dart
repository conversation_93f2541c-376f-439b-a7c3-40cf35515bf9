import 'package:sme_core_ui_desktop/index.dart'
    if (dart.library.html) 'dart:ui_web' show platformViewRegistry;
import 'package:sme_core_ui_desktop/index.dart'
    if (dart.library.html) 'dart:html' show IFrameElement;

class PlatformViewRegistryWrapper {
  static void registerViewFactory(String view, String? source) {
    platformViewRegistry.registerViewFactory(
      view,
      // ignore: avoid_types_on_closure_parameters
      (int viewId) => IFrameElement()
        ..src = source
        ..style.border = 'none',
    );
  }
}
