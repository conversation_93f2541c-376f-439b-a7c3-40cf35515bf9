import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_key_fact_statement_api/domain/model/key_fact_statement_model.dart';

part 'key_fact_statement_state.freezed.dart';

@freezed
class KeyFactStatementState with _$KeyFactStatementState implements BaseState {
  @Implements<InitialState>()
  const factory KeyFactStatementState.initial() = KfsInitialState;

  @Implements<LoadingState>()
  const factory KeyFactStatementState.loading() = KfsLoadingState;

  @Implements<ErrorState>()
  const factory KeyFactStatementState.error(
    String errorMessage,
  ) = KfsErrorState;

  @Implements<SuccessState>()
  const factory KeyFactStatementState.success({
    required KeyFactsStatementModel model,
  }) = KfsSuccessState;
}

/// Base form state is used for polymorphic behaviour
abstract class BaseState {}

/// State when cubit is created
abstract class InitialState extends BaseState {}

/// State when async KFS fetch is in progress
abstract class LoadingState extends BaseState {}

/// State when KFS successfully fetched
abstract class SuccessState extends BaseState {}

/// State when async operation returned error
abstract class ErrorState extends BaseState {
  String get errorMessage;
}
