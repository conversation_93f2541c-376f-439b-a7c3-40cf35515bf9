import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:logging_api/logging.dart';
import 'package:sme_core_utils/utils.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_tax_number_api/domain/interactor/tax_number_interactor.dart';
import 'package:wio_feature_tax_number_ui/src/page/tax_number_cubit.dart';
import 'package:wio_feature_tax_number_ui/src/page/tax_number_state.dart';
import 'package:wio_feature_tax_number_ui/wio_feature_tax_number_ui.dart';

class TaxNumberPage extends StatelessWidget {
  final String? currentTaxNumber;

  const TaxNumberPage({
    this.currentTaxNumber,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => TaxNumberCubit(
        interactor: DependencyProvider.get<TaxNumberInteractor>(),
        localizations: DependencyProvider.get<CommonLocalizations>(),
        navigator: DependencyProvider.get<NavigationProvider>(),
        logger: DependencyProvider.get<Logger>(),
      ),
      child: Scaffold(
        backgroundColor: context.colorStyling.background1,
        appBar: TopNavigation(
          TopNavigationModel(
            state: TopNavigationState.positive,
            title: TaxNumberLocalizations.of(context).taxNumberPageTitle,
          ),
          onLeftIconPressed: Navigator.of(context).pop,
        ),
        body: _PageContent(currentTaxNumber: currentTaxNumber),
      ),
    );
  }
}

class _PageContent extends StatefulWidget {
  final String? currentTaxNumber;

  const _PageContent({this.currentTaxNumber});

  @override
  State<_PageContent> createState() => _PageContentState();
}

class _PageContentState extends State<_PageContent> {
  TextEditingController? _numberController;
  TextEditingController? _dateController;
  bool isNextButtonAvailable = false;
  GlobalKey? _dateFieldKey;

  @override
  void initState() {
    _numberController = TextEditingController(text: widget.currentTaxNumber);
    _dateController = TextEditingController();
    _dateFieldKey = GlobalKey();
    super.initState();
  }

  @override
  void dispose() {
    _numberController?.dispose();
    _dateController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localization = TaxNumberLocalizations.of(context);

    return BlocConsumer<TaxNumberCubit, TaxNumberState>(
      listener: _stateChangeListener,
      builder: (context, state) => Stack(
        children: [
          Container(
            margin: const EdgeInsets.all(8),
            child: FixedButtonsScrollablePageLayout(
              model: FixedButtonsScrollablePageLayoutModel(
                primaryButton: FixedButtonsScrollablePageLayoutButton(
                  label: localization.taxNumberBtnSave,
                  size: ButtonSize.medium,
                  type: ButtonType.primary,
                  theme: ButtonModelTheme.sme,
                ),
              ),
              onPrimaryButtonPressed: isNextButtonAvailable
                  ? () {
                      context.read<TaxNumberCubit>().updateTaxNumber(
                            number: _numberController?.text ?? '',
                            date: _dateController?.text ?? '',
                          );
                    }
                  : null,
              children: [
                PageText(
                  PageTextModel.highlighted(
                    titleModel: PageTextHighlightedTextModel(
                      text: localization.taxNumberPageText,
                      highlightedTexts: [
                        HighlightedTextModel(
                          localization.taxNumberPageTextHighlight1,
                        ),
                        HighlightedTextModel(
                          localization.taxNumberPageTextHighlight2,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 44),
                InputField(
                  controller: _numberController,
                  model: InputFieldModel(
                    hint: localization.inputHintNumber,
                    label: localization.inputHintNumber,
                    size: InputFieldSize.small,
                    theme: InputFieldTheme.light,
                  ),
                  formatters: [
                    FilteringTextInputFormatter.allow(
                      RegExp('[0-9a-zA-Z]'),
                    ),
                    LengthLimitingTextInputFormatter(15),
                  ],
                  autovalidateMode: AutovalidateMode.always,
                  onInputChanged: _onTextFieldChanged,
                  onFieldSubmitted: (val) {},
                  validator: (trn) => _trnValidator(
                    localizations: localization,
                    value: trn ?? '',
                  ),
                ),
                const SizedBox(height: 16),
                InputField(
                  key: _dateFieldKey,
                  controller: _dateController,
                  model: InputFieldModel(
                    hint: localization.inputHintDate,
                    label: localization.inputHintDate,
                    size: InputFieldSize.small,
                    theme: InputFieldTheme.light,
                  ),
                  autovalidateMode: AutovalidateMode.always,
                  keyboardType: TextInputType.number,
                  formatters: [
                    TextInputDashFormatter(),
                    FilteringTextInputFormatter.singleLineFormatter,
                  ],
                  onInputChanged: _onTextFieldChanged,
                  onFieldSubmitted: (val) {},
                  validator: (dateValue) => _dateValidators(
                    localizations: localization,
                    dateTime: dateValue,
                  ),
                ),
              ],
            ),
          ),
          if (state.isLoading)
            ColoredBox(
              color: Colors.grey.withValues(alpha: 0.3),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }

  void _onTextFieldChanged(String value) {
    final isDateValid = _isDateValid(_dateController?.text ?? '');
    final isTrnValid = _numberController?.text.length == 15;
    final areFieldsBusinessValid = isDateValid && isTrnValid;
    if (areFieldsBusinessValid && isNextButtonAvailable == false) {
      setState(() {
        isNextButtonAvailable = true;
      });
    }
    if (!areFieldsBusinessValid && isNextButtonAvailable == true) {
      setState(() {
        isNextButtonAvailable = false;
      });
    }
  }

  DateTime? _getDateFormat(String dateTime) {
    final dateFormat =
        DateFormat(const DateTimePatternSME.ddMMyyyyDash().pattern);

    return dateFormat.tryParse(dateTime);
  }

  bool _isDateValid(String? dateTime) {
    if (dateTime == null) return false;

    final createdDate = _getDateFormat(dateTime);
    if (createdDate == null) return false;

    if (createdDate.isBefore(DateTime(2018)) ||
        createdDate.isAfter(DateTime.now())) {
      return false;
    }

    return true;
  }

  String? _dateValidators({
    required TaxNumberLocalizations localizations,
    String? dateTime,
  }) {
    if (dateTime == null) return null;

    if (_getDateFormat(dateTime) == null) return null;

    if (!_isDateValid(dateTime)) {
      _scrollToInputField(_dateFieldKey!.currentContext);

      return localizations.dateIsInvalid;
    }

    return null;
  }

  void _scrollToInputField(BuildContext? inputFieldContext) {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (inputFieldContext != null) {
        Scrollable.ensureVisible(inputFieldContext);
      }
    });
  }

  String? _trnValidator({
    required TaxNumberLocalizations localizations,
    required String value,
  }) =>
      value.length != 15 ? localizations.invalidTaxNumberLength : null;

  void _stateChangeListener(
    BuildContext context,
    TaxNumberState state,
  ) {
    if (state.error != null) {
      final localization = CommonLocalizations.of(context);

      showDialog<void>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(localization.common_error_title),
          content: Text(state.error!),
          actions: <Widget>[
            TextButton(
              child: Text(localization.common_ok),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
      );
    }
  }
}
