import 'package:di/di.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_tax_number_api/navigation/tax_number_feature_navigation_config.dart';
import 'package:wio_feature_tax_number_ui/src/navigation/tax_number_router.dart';

class TaxNumberFeatureDependencyModuleResolver {
  static void register() {
    DependencyProvider.registerLazySingleton(TaxNumberRouter.new);

    DependencyProvider.registerLazySingleton<NavigationRouter>(
      () => DependencyProvider.get<TaxNumberRouter>(),
      instanceName: TaxNumberFeatureNavigationConfig.name,
    );
  }
}
