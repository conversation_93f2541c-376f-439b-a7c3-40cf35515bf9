// GENERATED CODE
//
// After the template files .arb have been changed,
// generate this class by the command in the terminal:
// flutter pub run lokalise_flutter_sdk:gen-lok-l10n
//
// Please see https://pub.dev/packages/lokalise_flutter_sdk

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes
// ignore_for_file: depend_on_referenced_packages

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart';
import 'package:lokalise_flutter_sdk/lokalise_flutter_sdk.dart';
import 'intl/messages_all.dart';

class SettingsLocalizations {
  SettingsLocalizations._internal();

  static const LocalizationsDelegate<SettingsLocalizations> delegate =
      _AppLocalizationDelegate();

  static const List<Locale> supportedLocales = [
    Locale.fromSubtags(languageCode: 'en'),
    Locale.fromSubtags(languageCode: 'ar')
  ];

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  static final Map<String, List<String>> _metadata = {
    'aboutUs': [],
    'account': [],
    'accountDetails': [],
    'accountDetailsBicSubtitle': [],
    'accountDetailsIbanSubtitle': [],
    'accountOwner': [],
    'app': [],
    'appVersion': [],
    'areYouSureYouWantDisableBio': [],
    'cancel': [],
    'changeEmailErrorText': [],
    'changeEmailInputFieldHint': [],
    'changeEmailLabel': [],
    'changeEmailPageHighlightedText': [],
    'changeEmailPageSubtitle': [],
    'changeEmailPageSuggestionLabel': [],
    'changeEmailSuccessScreenDescription': [],
    'changeEmailSuccessScreenSubtitle': [],
    'changeEmailSuccessScreenSwipeAction': [],
    'changeEmailSuccessScreenTitle': [],
    'changeMobileAdditionalInfoText': [],
    'changeMobileLabel': [],
    'changeMobileSubtitle': [],
    'changeMobileSubtitleHighlightedText': [],
    'changeMobileSuccessScreenSubtitle': [],
    'changeMobileSuccessScreenTitle': [],
    'changePasscode': [],
    'changePassword': [],
    'closeAccount': [],
    'closeAcountLabel': [],
    'communications': [],
    'communicationsSettings': [],
    'disable': [],
    'enterReferralCode': [],
    'faceIdAndTouchId': [],
    'keyFactsStatement': [],
    'logOut': [],
    'monthlyStatement': [],
    'monthlyStatementBottomSheetChooseDocumentFormat': [],
    'phoneInputFieldErrorText': [],
    'phoneInputFieldHintText': [],
    'pleaseCompleteVerification': [],
    'pleaseTryAgainLater': [],
    'privacyPolicy': [],
    'security': [],
    'settings': [],
    'support': [],
    'swipeToClose': [],
    'uhOhLabel': [],
    'vpnDetectionScreenDescription': [],
    'vpnDetectionScreenSubTitle': [],
    'vpnDetectionScreenTitle': [],
    'wioContactInfoEmail': [],
    'wioContactInfoFacebook': [],
    'wioContactInfoInstagram': [],
    'wioContactInfoLinkedIn': [],
    'wioContactInfoPhone': [],
    'wioContactInfoTwitter': [],
    'wioContactInfoWhatsApp': [],
    'wioContactInfoYoutube': [],
    'wioStandardTerms': [],
    'you': [],
    'youWontBeAbleAuthWithBio': [],
    'yourCompany': [],
    'yourPartner': [],
    'yourSubscription': []
  };

  static Future<SettingsLocalizations> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    Lokalise.instance.metadata = _metadata;

    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = SettingsLocalizations._internal();
      return instance;
    });
  }

  static SettingsLocalizations of(BuildContext context) {
    final instance =
        Localizations.of<SettingsLocalizations>(context, SettingsLocalizations);
    assert(instance != null,
        'No instance of SettingsLocalizations present in the widget tree. Did you add SettingsLocalizations.delegate in localizationsDelegates?');
    return instance!;
  }

  /// `About Us`
  String get aboutUs {
    return Intl.message(
      'About Us',
      name: 'aboutUs',
      desc: '',
      args: [],
    );
  }

  /// `Account`
  String get account {
    return Intl.message(
      'Account',
      name: 'account',
      desc: '',
      args: [],
    );
  }

  /// `Account details`
  String get accountDetails {
    return Intl.message(
      'Account details',
      name: 'accountDetails',
      desc: '',
      args: [],
    );
  }

  /// `BIC`
  String get accountDetailsBicSubtitle {
    return Intl.message(
      'BIC',
      name: 'accountDetailsBicSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `IBAN`
  String get accountDetailsIbanSubtitle {
    return Intl.message(
      'IBAN',
      name: 'accountDetailsIbanSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Account owner`
  String get accountOwner {
    return Intl.message(
      'Account owner',
      name: 'accountOwner',
      desc: '',
      args: [],
    );
  }

  /// `App`
  String get app {
    return Intl.message(
      'App',
      name: 'app',
      desc: '',
      args: [],
    );
  }

  /// `App version`
  String get appVersion {
    return Intl.message(
      'App version',
      name: 'appVersion',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to disable biometrics?`
  String get areYouSureYouWantDisableBio {
    return Intl.message(
      'Are you sure you want to disable biometrics?',
      name: 'areYouSureYouWantDisableBio',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get cancel {
    return Intl.message(
      'Cancel',
      name: 'cancel',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid email address`
  String get changeEmailErrorText {
    return Intl.message(
      'Please enter a valid email address',
      name: 'changeEmailErrorText',
      desc: '',
      args: [],
    );
  }

  /// `Your business e-mail`
  String get changeEmailInputFieldHint {
    return Intl.message(
      'Your business e-mail',
      name: 'changeEmailInputFieldHint',
      desc: '',
      args: [],
    );
  }

  /// `CHANGE EMAIL`
  String get changeEmailLabel {
    return Intl.message(
      'CHANGE EMAIL',
      name: 'changeEmailLabel',
      desc: '',
      args: [],
    );
  }

  /// `email`
  String get changeEmailPageHighlightedText {
    return Intl.message(
      'email',
      name: 'changeEmailPageHighlightedText',
      desc: '',
      args: [],
    );
  }

  /// `What's your new email address?`
  String get changeEmailPageSubtitle {
    return Intl.message(
      'What\'s your new email address?',
      name: 'changeEmailPageSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `We'll send a verification code to this email.`
  String get changeEmailPageSuggestionLabel {
    return Intl.message(
      'We\'ll send a verification code to this email.',
      name: 'changeEmailPageSuggestionLabel',
      desc: '',
      args: [],
    );
  }

  /// `Login back into your account with your new email`
  String get changeEmailSuccessScreenDescription {
    return Intl.message(
      'Login back into your account with your new email',
      name: 'changeEmailSuccessScreenDescription',
      desc: '',
      args: [],
    );
  }

  /// `Email updated`
  String get changeEmailSuccessScreenSubtitle {
    return Intl.message(
      'Email updated',
      name: 'changeEmailSuccessScreenSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `SWIPE UP TO GET IN`
  String get changeEmailSuccessScreenSwipeAction {
    return Intl.message(
      'SWIPE UP TO GET IN',
      name: 'changeEmailSuccessScreenSwipeAction',
      desc: '',
      args: [],
    );
  }

  /// `DONE.`
  String get changeEmailSuccessScreenTitle {
    return Intl.message(
      'DONE.',
      name: 'changeEmailSuccessScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `This should be your business number.`
  String get changeMobileAdditionalInfoText {
    return Intl.message(
      'This should be your business number.',
      name: 'changeMobileAdditionalInfoText',
      desc: '',
      args: [],
    );
  }

  /// `CHANGE MOBILE NUMBER`
  String get changeMobileLabel {
    return Intl.message(
      'CHANGE MOBILE NUMBER',
      name: 'changeMobileLabel',
      desc: '',
      args: [],
    );
  }

  /// `What's your new mobile number?`
  String get changeMobileSubtitle {
    return Intl.message(
      'What\'s your new mobile number?',
      name: 'changeMobileSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `mobile`
  String get changeMobileSubtitleHighlightedText {
    return Intl.message(
      'mobile',
      name: 'changeMobileSubtitleHighlightedText',
      desc: '',
      args: [],
    );
  }

  /// `Mobile number updated`
  String get changeMobileSuccessScreenSubtitle {
    return Intl.message(
      'Mobile number updated',
      name: 'changeMobileSuccessScreenSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `DONE.`
  String get changeMobileSuccessScreenTitle {
    return Intl.message(
      'DONE.',
      name: 'changeMobileSuccessScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Change Passcode`
  String get changePasscode {
    return Intl.message(
      'Change Passcode',
      name: 'changePasscode',
      desc: '',
      args: [],
    );
  }

  /// `Change Password`
  String get changePassword {
    return Intl.message(
      'Change Password',
      name: 'changePassword',
      desc: '',
      args: [],
    );
  }

  /// `Close account`
  String get closeAccount {
    return Intl.message(
      'Close account',
      name: 'closeAccount',
      desc: '',
      args: [],
    );
  }

  /// `Close account`
  String get closeAcountLabel {
    return Intl.message(
      'Close account',
      name: 'closeAcountLabel',
      desc: '',
      args: [],
    );
  }

  /// `Communications`
  String get communications {
    return Intl.message(
      'Communications',
      name: 'communications',
      desc: '',
      args: [],
    );
  }

  /// `Communications Settings`
  String get communicationsSettings {
    return Intl.message(
      'Communications Settings',
      name: 'communicationsSettings',
      desc: '',
      args: [],
    );
  }

  /// `Disable`
  String get disable {
    return Intl.message(
      'Disable',
      name: 'disable',
      desc: '',
      args: [],
    );
  }

  /// `Enter referral code`
  String get enterReferralCode {
    return Intl.message(
      'Enter referral code',
      name: 'enterReferralCode',
      desc: '',
      args: [],
    );
  }

  /// `Face ID & Touch ID`
  String get faceIdAndTouchId {
    return Intl.message(
      'Face ID & Touch ID',
      name: 'faceIdAndTouchId',
      desc: '',
      args: [],
    );
  }

  /// `Key facts statement`
  String get keyFactsStatement {
    return Intl.message(
      'Key facts statement',
      name: 'keyFactsStatement',
      desc: '',
      args: [],
    );
  }

  /// `Log out`
  String get logOut {
    return Intl.message(
      'Log out',
      name: 'logOut',
      desc: '',
      args: [],
    );
  }

  /// `Monthly statement`
  String get monthlyStatement {
    return Intl.message(
      'Monthly statement',
      name: 'monthlyStatement',
      desc: '',
      args: [],
    );
  }

  /// `Choose document format`
  String get monthlyStatementBottomSheetChooseDocumentFormat {
    return Intl.message(
      'Choose document format',
      name: 'monthlyStatementBottomSheetChooseDocumentFormat',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid mobile number`
  String get phoneInputFieldErrorText {
    return Intl.message(
      'Please enter a valid mobile number',
      name: 'phoneInputFieldErrorText',
      desc: '',
      args: [],
    );
  }

  /// `Your mobile number`
  String get phoneInputFieldHintText {
    return Intl.message(
      'Your mobile number',
      name: 'phoneInputFieldHintText',
      desc: '',
      args: [],
    );
  }

  /// `Please complete your verification`
  String get pleaseCompleteVerification {
    return Intl.message(
      'Please complete your verification',
      name: 'pleaseCompleteVerification',
      desc: '',
      args: [],
    );
  }

  /// `Please try again later`
  String get pleaseTryAgainLater {
    return Intl.message(
      'Please try again later',
      name: 'pleaseTryAgainLater',
      desc: '',
      args: [],
    );
  }

  /// `Privacy Policy`
  String get privacyPolicy {
    return Intl.message(
      'Privacy Policy',
      name: 'privacyPolicy',
      desc: '',
      args: [],
    );
  }

  /// `Security`
  String get security {
    return Intl.message(
      'Security',
      name: 'security',
      desc: '',
      args: [],
    );
  }

  /// `Settings`
  String get settings {
    return Intl.message(
      'Settings',
      name: 'settings',
      desc: '',
      args: [],
    );
  }

  /// `Support`
  String get support {
    return Intl.message(
      'Support',
      name: 'support',
      desc: '',
      args: [],
    );
  }

  /// `Swipe to close`
  String get swipeToClose {
    return Intl.message(
      'Swipe to close',
      name: 'swipeToClose',
      desc: '',
      args: [],
    );
  }

  /// `UH OH.`
  String get uhOhLabel {
    return Intl.message(
      'UH OH.',
      name: 'uhOhLabel',
      desc: '',
      args: [],
    );
  }

  /// `For your security and in line with our bank’s policies, we cannot allow access at this time. Please try again from a secure and recognized network.`
  String get vpnDetectionScreenDescription {
    return Intl.message(
      'For your security and in line with our bank’s policies, we cannot allow access at this time. Please try again from a secure and recognized network.',
      name: 'vpnDetectionScreenDescription',
      desc: '',
      args: [],
    );
  }

  /// `We have noticed an app access attempt from an unusual network. This could be due to change in network or use of application that create virtual networks.`
  String get vpnDetectionScreenSubTitle {
    return Intl.message(
      'We have noticed an app access attempt from an unusual network. This could be due to change in network or use of application that create virtual networks.',
      name: 'vpnDetectionScreenSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `Oops!`
  String get vpnDetectionScreenTitle {
    return Intl.message(
      'Oops!',
      name: 'vpnDetectionScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `<EMAIL>`
  String get wioContactInfoEmail {
    return Intl.message(
      '<EMAIL>',
      name: 'wioContactInfoEmail',
      desc: '',
      args: [],
    );
  }

  /// `https://www.facebook.com/wiobank`
  String get wioContactInfoFacebook {
    return Intl.message(
      'https://www.facebook.com/wiobank',
      name: 'wioContactInfoFacebook',
      desc: '',
      args: [],
    );
  }

  /// `https://www.instagram.com/wiobank`
  String get wioContactInfoInstagram {
    return Intl.message(
      'https://www.instagram.com/wiobank',
      name: 'wioContactInfoInstagram',
      desc: '',
      args: [],
    );
  }

  /// `https://www.linkedin.com/company/wiobank/`
  String get wioContactInfoLinkedIn {
    return Intl.message(
      'https://www.linkedin.com/company/wiobank/',
      name: 'wioContactInfoLinkedIn',
      desc: '',
      args: [],
    );
  }

  /// `+************`
  String get wioContactInfoPhone {
    return Intl.message(
      '+************',
      name: 'wioContactInfoPhone',
      desc: '',
      args: [],
    );
  }

  /// `https://twitter.com/WioBank`
  String get wioContactInfoTwitter {
    return Intl.message(
      'https://twitter.com/WioBank',
      name: 'wioContactInfoTwitter',
      desc: '',
      args: [],
    );
  }

  /// `+************`
  String get wioContactInfoWhatsApp {
    return Intl.message(
      '+************',
      name: 'wioContactInfoWhatsApp',
      desc: '',
      args: [],
    );
  }

  /// `https://www.youtube.com/channel/UCZQRDNEC9Qso8NXU75KmoKQ`
  String get wioContactInfoYoutube {
    return Intl.message(
      'https://www.youtube.com/channel/UCZQRDNEC9Qso8NXU75KmoKQ',
      name: 'wioContactInfoYoutube',
      desc: '',
      args: [],
    );
  }

  /// `Wio Standard Terms`
  String get wioStandardTerms {
    return Intl.message(
      'Wio Standard Terms',
      name: 'wioStandardTerms',
      desc: '',
      args: [],
    );
  }

  /// `You`
  String get you {
    return Intl.message(
      'You',
      name: 'you',
      desc: '',
      args: [],
    );
  }

  /// `You won't be able to authenticate using your Face ID or Touch ID.`
  String get youWontBeAbleAuthWithBio {
    return Intl.message(
      'You won\'t be able to authenticate using your Face ID or Touch ID.',
      name: 'youWontBeAbleAuthWithBio',
      desc: '',
      args: [],
    );
  }

  /// `Your company`
  String get yourCompany {
    return Intl.message(
      'Your company',
      name: 'yourCompany',
      desc: '',
      args: [],
    );
  }

  /// `Your partner`
  String get yourPartner {
    return Intl.message(
      'Your partner',
      name: 'yourPartner',
      desc: '',
      args: [],
    );
  }

  /// `Your subscription`
  String get yourSubscription {
    return Intl.message(
      'Your subscription',
      name: 'yourSubscription',
      desc: '',
      args: [],
    );
  }
}

class _AppLocalizationDelegate
    extends LocalizationsDelegate<SettingsLocalizations> {
  const _AppLocalizationDelegate();

  @override
  bool isSupported(Locale locale) => SettingsLocalizations.supportedLocales.any(
      (supportedLocale) => supportedLocale.languageCode == locale.languageCode);

  @override
  Future<SettingsLocalizations> load(Locale locale) =>
      SettingsLocalizations.load(locale);

  @override
  bool shouldReload(_AppLocalizationDelegate old) => false;
}
