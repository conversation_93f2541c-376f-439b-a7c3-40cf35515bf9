import 'package:flutter/cupertino.dart';
import 'package:wio_feature_settings_ui_desktop/src/screens/settings_screen/components/your_company/widgets/company_details.dart';
import 'package:wio_feature_settings_ui_desktop/src/screens/settings_screen/components/your_company/widgets/company_documents.dart';
import 'package:wio_feature_settings_ui_desktop/src/screens/settings_screen/components/your_company/widgets/company_documents_subheader.dart';
import 'package:wio_feature_settings_ui_desktop/src/screens/settings_screen/components/your_company/widgets/header_company_name.dart';
import 'package:wio_feature_settings_ui_desktop/src/screens/settings_screen/components/your_company/widgets/share_holders.dart';

class YourCompanyDetailPageContent extends StatelessWidget {
  const YourCompanyDetailPageContent({super.key});

  @override
  Widget build(BuildContext context) => const SingleChildScrollView(
        child: Column(children: [
          HeaderCompanyName(),
          CompanyDetails(),
          ShareHolders(),
          CompanyDocumentsSubheader(),
          CompanyDocuments(),
        ]),
      );
}
