import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_feature_annual_kyc_api/annual_kyc_api.dart';
import 'package:wio_feature_document_upload_api/model/document_type.dart';
import 'package:wio_feature_settings_ui_desktop/feature_settings_ui_desktop.dart';
import 'package:wio_feature_settings_ui_desktop/src/screens/settings_screen/components/upload_partner_document/upload_partner_document_cubit.dart';
import 'package:wio_feature_settings_ui_desktop/src/screens/settings_screen/components/upload_partner_document/upload_partner_document_state.dart';
import 'package:wio_feature_settings_ui_desktop/src/screens/settings_screen/components/upload_partner_document/widget/file_upload_box.dart';

class UploadDocumentEndSide extends StatelessWidget {
  final String reason;
  final DocumentValue documentValue;

  const UploadDocumentEndSide({
    required this.reason,
    required this.documentValue,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = SettingsUiLocalizations.of(context);

    return ConstrainedBox(
      constraints: const BoxConstraints(maxWidth: 504),
      child: Padding(
        padding: const EdgeInsetsDirectional.only(end: 30),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (documentValue.documentType.isPassPort) ...[
              Label(
                model: LabelModel(
                  text: l10n.muSettingUploadTheFirstPagePassport,
                  color: CompanyColorPointer.secondary9,
                ),
              ),
              SizedBox(
                height: spacingSize(size.l),
              ),
            ],
            if (documentValue.documentType.isEmirateID)
              _FileEmpireUploaderRegion(documentValue)
            else
              _SingleSideUploaderRegion(documentValue),
            const Spacer(),
            _SubmitCtaButton(
              reason: reason,
              documentValue: documentValue,
            ),
          ],
        ),
      ),
    );
  }
}

class _SingleSideUploaderRegion extends StatelessWidget {
  final DocumentValue documentValue;

  const _SingleSideUploaderRegion(this.documentValue);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UploadPartnerDocumentCubit, UploadPartnerDocumentState>(
      builder: (context, state) {
        final cubit = context.read<UploadPartnerDocumentCubit>();

        return switch (state) {
          DocumentInitial() => FileUploadBox(
              onFileDropped: (fileByte, fileName) => cubit.onFileDropped(
                fileBytes: fileByte,
                fileName: fileName,
                documentType: documentValue.documentType,
              ),
              onTap: () => cubit.pickDocument(
                documentType: documentValue.documentType,
              ),
            ),
          DocumentUploading(:final UploadDocumentZone firstDocumentZone) =>
            DocListItem(
              fileData: Uint8List.fromList(
                firstDocumentZone.uploadDocument.fileBytes,
              ),
              fileName: firstDocumentZone.uploadDocument.fileName,
              progressValue: state.firstDocumentZone.progressValue,
            ),
          DocumentLoaded(
            :final UploadDocumentZone firstDocumentZone,
          ) =>
            DocListItem(
              fileData: Uint8List.fromList(
                firstDocumentZone.uploadDocument.fileBytes,
              ),
              fileName: firstDocumentZone.uploadDocument.fileName,
              onDelete: cubit.onDelete,
            ),
          DocumentLoaded(firstDocumentZone: null) => const SizedBox.shrink(),
        };
      },
    );
  }
}

class _FileEmpireUploaderRegion extends StatelessWidget {
  final DocumentValue documentValue;

  const _FileEmpireUploaderRegion(this.documentValue);

  @override
  Widget build(BuildContext context) {
    final l10n = SettingsUiLocalizations.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        LabeledSwitch(
          LabeledSwitchModel(
            text: l10n.muSettingUploadAsSingleFile,
            selected:
                context.watch<UploadPartnerDocumentCubit>().state.maybeMap(
                      orElse: () => false,
                      initial: (document) => document.isSinglePage,
                      loaded: (loaded) => loaded.isSinglePage,
                    ),
            textWithUppercase: false,
          ),
          onChanged: (value) => context
              .read<UploadPartnerDocumentCubit>()
              .changeUploadDocumentPageState(isSingleSide: value),
        ),
        SizedBox(
          height: spacingSize(size.l),
        ),
        if (context.watch<UploadPartnerDocumentCubit>().state.isSinglePage)
          _SingleSideUploaderRegion(documentValue)
        else
          _DoubleSideUploaderRegion(
            documentValue: documentValue,
            firstPageHeader:
                '${l10n.emiratesId} ${l10n.muSettingFront}'.toUpperCase(),
            secondPageHeader:
                '${l10n.emiratesId} ${l10n.backButtonText}'.toUpperCase(),
          ),
      ],
    );
  }
}

class _DoubleSideUploaderRegion extends StatelessWidget {
  final String firstPageHeader;
  final String secondPageHeader;
  final DocumentValue documentValue;

  const _DoubleSideUploaderRegion({
    required this.firstPageHeader,
    required this.secondPageHeader,
    required this.documentValue,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          firstPageHeader,
          style: context.textStyling.h6,
        ),
        SizedBox(
          height: spacingSize(size.s),
        ),
        BlocBuilder<UploadPartnerDocumentCubit, UploadPartnerDocumentState>(
          buildWhen: (previous, current) =>
              previous.uploadDocument?.firstDocument !=
              current.uploadDocument?.firstDocument,
          builder: (context, state) {
            final cubit = context.read<UploadPartnerDocumentCubit>();

            return switch (state) {
              DocumentInitial() => FileUploadBox(
                  onFileDropped: (fileByte, fileName) => cubit.onFileDropped(
                    fileBytes: fileByte,
                    fileName: fileName,
                    documentType: documentValue.documentType,
                  ),
                  onTap: () => cubit.pickDocument(
                    documentType: documentValue.documentType,
                  ),
                ),
              DocumentUploading(:final uploadDocument) => DocListItem(
                  fileData: Uint8List.fromList(
                    uploadDocument!.firstDocument!.fileBytes,
                  ),
                  fileName: uploadDocument.firstDocument!.fileName,
                  progressValue: state.firstDocumentZone.progressValue,
                ),
              DocumentLoaded(
                uploadDocument: final uploadDocument,
              ) =>
                DocListItem(
                  fileData: Uint8List.fromList(
                    uploadDocument!.firstDocument!.fileBytes,
                  ),
                  fileName: uploadDocument.firstDocument!.fileName,
                  onDelete: cubit.onDelete,
                ),
            };
          },
        ),
        SizedBox(
          height: spacingSize(size.l),
        ),
        Text(
          secondPageHeader,
          style: context.textStyling.h6,
        ),
        SizedBox(
          height: spacingSize(size.s),
        ),
        BlocBuilder<UploadPartnerDocumentCubit, UploadPartnerDocumentState>(
          buildWhen: (previous, current) =>
              previous.uploadDocument?.secondDocument !=
              current.uploadDocument?.secondDocument,
          builder: (context, state) {
            final cubit = context.read<UploadPartnerDocumentCubit>();

            return switch (state) {
              DocumentInitial() => FileUploadBox(
                  onFileDropped: (fileByte, fileName) => cubit.onFileDropped(
                    fileBytes: fileByte,
                    fileName: fileName,
                    documentType: documentValue.documentType,
                    isSecondDocument: true,
                  ),
                  onTap: () => cubit.pickDocument(
                    isSecondDocument: true,
                    documentType: documentValue.documentType,
                  ),
                ),
              DocumentUploading(:final uploadDocument) => DocListItem(
                  fileData: Uint8List.fromList(
                    uploadDocument!.secondDocument!.fileBytes,
                  ),
                  fileName: uploadDocument.secondDocument!.fileName,
                  progressValue: state.secondDocumentZone?.progressValue,
                ),
              DocumentLoaded(
                uploadDocument: final uploadDocument,
              ) =>
                DocListItem(
                  fileData: Uint8List.fromList(
                    uploadDocument!.secondDocument!.fileBytes,
                  ),
                  fileName: uploadDocument.secondDocument!.fileName,
                  onDelete: cubit.onDelete,
                ),
            };
          },
        ),
      ],
    );
  }
}

class _SubmitCtaButton extends StatelessWidget {
  final String reason;
  final DocumentValue documentValue;

  const _SubmitCtaButton({required this.reason, required this.documentValue});

  @override
  Widget build(BuildContext context) {
    final l10n = SettingsUiLocalizations.of(context);

    return BlocBuilder<UploadPartnerDocumentCubit, UploadPartnerDocumentState>(
      builder: (context, state) {
        final cubit = context.read<UploadPartnerDocumentCubit>();

        final isEnabled = switch (state) {
          DocumentInitial() => false,
          DocumentUploading() => true,
          DocumentLoaded(:final firstDocumentZone, :final secondDocumentZone) =>
            state.isSinglePage ||
                (firstDocumentZone != null && secondDocumentZone != null),
        };

        return Padding(
          padding: const EdgeInsets.only(bottom: 24),
          child: CtaButton.m(
            title: l10n.submit,
            ctaButtonVariant: CtaButtonVariant.primaryType,
            ctaButtonState: isEnabled,
            autoLoadingIndicator: true,
            onTap: () async => cubit.uploadDocument(
              documentValue: documentValue,
              theReason: reason,
            ),
          ),
        );
      },
    );
  }
}
