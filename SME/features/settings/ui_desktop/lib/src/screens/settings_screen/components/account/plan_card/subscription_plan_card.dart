import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_feature_settings_ui_desktop/feature_settings_ui_desktop.dart';
import 'package:wio_feature_settings_ui_desktop/src/screens/settings_screen/components/account/plan_card/subscription_plan_card_cubit.dart';
import 'package:wio_feature_settings_ui_desktop/src/screens/settings_screen/components/account/plan_card/subscription_plan_card_state.dart';
import 'package:wio_feature_settings_ui_desktop/src/screens/settings_screen/components/you_section/widgets/loading_shimmer.dart';

class SubscriptionPlanCard extends StatelessWidget {
  const SubscriptionPlanCard({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = SettingsUiLocalizations.of(context);

    return BlocProvider(
      create: (_) =>
          DependencyProvider.get<SubscriptionPlanCardCubit>()..init(),
      child: Bloc<PERSON>uilder<SubscriptionPlanCardCubit, SubscriptionPlanCardState>(
        builder: (context, state) => switch (state) {
          LoadingPlanCard() => const SizedBox(
              height: 178,
              child: LoadingShimmer(),
            ),
          ErrorPlanCard() => const SizedBox(),
          LoadedPlanCard(
            :final planName,
            :final planDescription,
            :final fee,
          ) =>
            state.canReadPlan
                ? Expanded(
                    child: PlanCard(
                      headerDescription: l10n.yourSubscriptionPlan,
                      description: planDescription,
                      title: planName,
                      tag: Tag.large(
                        type: TagType.overlayLight,
                        text: l10n.amountPerMonth(fee),
                      ),
                      onTapChevron: state.canEditPlan
                          ? context
                              .read<SubscriptionPlanCardCubit>()
                              .navigateToSubscriptionPlanPage
                          : null,
                    ),
                  )
                : const SizedBox(),
        },
      ),
    );
  }
}
