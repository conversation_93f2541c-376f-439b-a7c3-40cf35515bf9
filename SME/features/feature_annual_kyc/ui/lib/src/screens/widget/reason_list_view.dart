import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_annual_kyc_api/annual_kyc_api.dart';

class ReasonListView extends StatelessWidget {
  final List<AnnualKycReason> reasons;
  final ValueChanged<int> onReasonClicked;

  const ReasonListView({
    required this.reasons,
    required this.onReasonClicked,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: reasons.length,
      separatorBuilder: (_, __) => const SizedBox(height: 8),
      itemBuilder: (_, index) {
        final reason = reasons[index];

        return _ReasonItem(
          reason: reason,
          index: index,
          onReasonClicked: onReasonClicked,
        );
      },
    );
  }
}

class _ReasonItem extends StatelessWidget {
  final AnnualKycReason reason;
  final int index;
  final ValueChanged<int> onReasonClicked;

  const _ReasonItem({
    required this.reason,
    required this.index,
    required this.onReasonClicked,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListBox(
      listBoxModel: ListBoxModel(
        textModel: ListBoxTextModel(
          title: reason.reason,
        ),
        isBoxed: true,
        style:
            reason.selected ? ListBoxStyle.notification : ListBoxStyle.positive,
        rightPartModel: ListBoxPartModel.checkbox(
          value: reason.selected,
        ),
      ),
      onPressed: () => onReasonClicked(index),
    );
  }
}
