import 'package:tests/tests.dart';
import 'package:wio_feature_home_api/wio_feature_home_api.dart';
import 'package:wio_feature_home_ui/src/tab_changer/home_notification_channel_impl.dart';

void main() {
  late HomeNotificationChannel homeNotificationChannel;

  setUp(() {
    homeNotificationChannel = HomeNotificationChannelImpl();
  });

  tearDown(() {
    homeNotificationChannel.dispose();
  });

  test('emit events in order', () async {
    // Arrange
    final expectedEvents = [
      const TabSwitchEvent.cards(),
      const TabSwitchEvent.newCard(),
      const TabSwitchEvent.cards(),
      const TabSwitchEvent.payments(),
    ]..shuffle();

    // Assert
    expect(
      homeNotificationChannel.homeTabsStream,
      emitsInOrder(expectedEvents),
    );

    // Act
    for (final event in expectedEvents) {
      homeNotificationChannel.addEventToStream(event);
    }
  });

  test('emits the last event on subscription', () async {
    // Arrange
    final events = [
      const TabSwitchEvent.cards(),
      const TabSwitchEvent.newCard(),
      const TabSwitchEvent.payments(),
    ]..shuffle();
    final expectedEvent = events.last;

    // Act
    for (final event in events) {
      homeNotificationChannel.addEventToStream(event);
    }
    final actual = await homeNotificationChannel.homeTabsStream.first;

    // Assert
    expect(actual, expectedEvent);
  });
}
