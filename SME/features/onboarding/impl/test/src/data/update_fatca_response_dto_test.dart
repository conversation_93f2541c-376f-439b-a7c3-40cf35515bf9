import 'dart:convert';

import 'package:feature_onboarding_impl/src/data/dto/update_fatca_info_response_dto.dart';
import 'package:test/expect.dart';
import 'package:test/scaffolding.dart';

void main() {
  test('validate deserialization of resentOtp response ', () {
    // Prepare
    final jsonString = ''' 
    {
      "updateFATCAInformation" : {
        "IndividualUpdateFATCAResponse" : "Success",
        "PartnerUpdateFATCAResponse" : "Success"
      }
    } 
    ''';

    // action
    final json = jsonDecode(jsonString);
    final dto = UpdateFATCAInformationResponseDto.fromJson(
        json['updateFATCAInformation']);

    // Assert
    expect(dto.individualResponse, 'Success');
    expect(dto.partnerResponse, 'Success');
  });
}
