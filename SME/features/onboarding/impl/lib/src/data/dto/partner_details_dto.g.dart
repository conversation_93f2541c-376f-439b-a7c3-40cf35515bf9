// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'partner_details_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

IndividualPartnerDto _$IndividualPartnerDtoFromJson(
        Map<String, dynamic> json) =>
    IndividualPartnerDto(
      json['dataSource'] as String,
      json['firstName'] as String,
      json['lastName'] as String,
      json['fullName'] as String,
      json['dateOfBirth'] as String,
      json['gender'] as String,
      json['nationality'] as String,
      json['placeOfBirth'] as String,
      json['countryOfResidence'] as String,
      json['addressFreeText'] as String?,
      json['buildingName'] as String,
      json['apartmentNumber'] as String,
      json['street'] as String?,
      json['area'] as String?,
      json['city'] as String,
      json['emirate'] as String?,
      json['country'] as String,
      json['poBox'] as String,
      (json['partnerDocuments'] as List<dynamic>)
          .map((e) => PartnerDocumentDto.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$IndividualPartnerDtoToJson(
        IndividualPartnerDto instance) =>
    <String, dynamic>{
      'dataSource': instance.dataSource,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'fullName': instance.fullName,
      'dateOfBirth': instance.dateOfBirth,
      'gender': instance.gender,
      'nationality': instance.nationality,
      'placeOfBirth': instance.placeOfBirth,
      'countryOfResidence': instance.countryOfResidence,
      'addressFreeText': instance.addressFreeText,
      'buildingName': instance.buildingName,
      'apartmentNumber': instance.apartmentNumber,
      'street': instance.street,
      'area': instance.area,
      'city': instance.city,
      'emirate': instance.emirate,
      'country': instance.country,
      'poBox': instance.poBox,
      'partnerDocuments': instance.partnerDocuments,
    };

CorporatePartnerDto _$CorporatePartnerDtoFromJson(Map<String, dynamic> json) =>
    CorporatePartnerDto(
      json['businessId'] as String,
      json['dataSource'] as String,
      json['countryOfIncorporation'] as String,
      json['companyName'] as String,
      json['tradeLicenseNumber'] as String,
      json['companyRegistrationDate'] as String,
      json['licensingAuthority'] as String,
      (json['partnerDocuments'] as List<dynamic>)
          .map((e) => PartnerDocumentDto.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CorporatePartnerDtoToJson(
        CorporatePartnerDto instance) =>
    <String, dynamic>{
      'businessId': instance.businessId,
      'dataSource': instance.dataSource,
      'countryOfIncorporation': instance.countryOfIncorporation,
      'companyName': instance.companyName,
      'tradeLicenseNumber': instance.tradeLicenseNumber,
      'companyRegistrationDate': instance.companyRegistrationDate,
      'licensingAuthority': instance.licensingAuthority,
      'partnerDocuments': instance.partnerDocuments,
    };

PartnerDocumentDto _$PartnerDocumentDtoFromJson(Map<String, dynamic> json) =>
    PartnerDocumentDto(
      json['documentType'] as String,
      (json['DocumentFiles'] as List<dynamic>)
          .map((e) => DocumentFileDto.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PartnerDocumentDtoToJson(PartnerDocumentDto instance) =>
    <String, dynamic>{
      'documentType': instance.documentType,
      'DocumentFiles': instance.documents,
    };

DocumentFileDto _$DocumentFileDtoFromJson(Map<String, dynamic> json) =>
    DocumentFileDto(
      json['documentName'] as String,
      json['documentKey'] as String,
      json['documentSide'] as String,
    );

Map<String, dynamic> _$DocumentFileDtoToJson(DocumentFileDto instance) =>
    <String, dynamic>{
      'documentName': instance.documentName,
      'documentKey': instance.documentKey,
      'documentSide': instance.documentSide,
    };
