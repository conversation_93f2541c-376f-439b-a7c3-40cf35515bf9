import 'package:feature_login_api/feature_login_api.dart';
import 'package:ui/ui.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_onboarding_licensing_authority_ui_desktop/src/analytics/licensing_authority_analytics.dart';
import 'package:wio_feature_onboarding_licensing_authority_ui_desktop/src/pages/success_page/success_state.dart';

class _Constants {
  static const appStoreUri = 'https://businessapp.wio.io/GcVi/qjeyy2h3';
  static const playStoreUri = 'https://businessapp.wio.io/GcVi/afwxybsd';
}

class SuccessCubit extends BaseCubit<SuccessState> {
  final NavigationProvider _navigationProvider;
  final LicensingAuthorityAnalytics _licensingAuthorityAnalytics;

  SuccessCubit({
    required NavigationProvider navigationProvider,
    required LicensingAuthorityAnalytics licensingAuthorityAnalytics,
  })  : _navigationProvider = navigationProvider,
        _licensingAuthorityAnalytics = licensingAuthorityAnalytics,
        super(const SuccessState.initial());

  void initialize() {
    _licensingAuthorityAnalytics.trackMabrookEvents();
  }

  void goToLoginScreen() {
    _navigationProvider.navigateTo(const LoginFeatureNavigationConfig());
  }

  Future<void> redirectToAppStore() async {
    final url = Uri.parse(_Constants.appStoreUri);

    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> redirectToPlayStore() async {
    final url = Uri.parse(_Constants.playStoreUri);

    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }
}
