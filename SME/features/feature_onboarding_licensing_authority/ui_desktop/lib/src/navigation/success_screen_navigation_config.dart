import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_onboarding_licensing_authority_api/navigation/onboarding_licensing_authority_feature_navigation_config.dart';

class OnboardingLicensingSuccessScreenNavigationConfig
    extends ScreenNavigationConfig {
  static const screenId = 'onboarding_licencing_authority_success_screen';

  final String email;

  const OnboardingLicensingSuccessScreenNavigationConfig({required this.email})
      : super(
          id: screenId,
          feature: OnboardingLicensingAuthorityFeatureNavigationConfig.name,
        );
}
