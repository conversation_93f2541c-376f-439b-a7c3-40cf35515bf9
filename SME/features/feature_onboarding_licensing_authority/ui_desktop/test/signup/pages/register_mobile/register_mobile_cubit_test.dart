import 'package:bloc_test/bloc_test.dart';
import 'package:feature_2fa_api/feature_2fa_api.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_feature_onboarding_licensing_authority_ui_desktop/src/analytics/licensing_authority_analytics.dart';
import 'package:wio_feature_onboarding_licensing_authority_ui_desktop/src/pages/onboarding/signup/register_mobile/register_mobile_cubit.dart';
import 'package:wio_feature_onboarding_licensing_authority_ui_desktop/src/pages/onboarding/signup/register_mobile/register_mobile_state.dart';
import 'package:wio_feature_onboarding_licensing_authority_ui_desktop/src/pages/onboarding/signup/signup_step_cubit.dart';

import '../../../mocks.dart';

void main() {
  late Logger mockLogger;
  late MockTwoFaDedBridge twoFaDedBridge;
  late MockSignUpInteractor signUpInteractor;
  late MockErrorHandlerTool errorHandlerTool;
  late NextButtonViewModel mockNextButtonViewModel;
  late LicensingAuthorityAnalytics mockAnalytics;

  setUpAll(() {
    errorHandlerTool = MockErrorHandlerTool();
    signUpInteractor = MockSignUpInteractor();
    twoFaDedBridge = MockTwoFaDedBridge();
    mockLogger = MockLogger();
    mockNextButtonViewModel = MockNextButtonViewModel();
    mockAnalytics = MockLicensingAuthorityAnalytics();
  });

  const individualId = 'individualId';

  blocTest<RegisterMobileCubit, RegisterMobileState>(
    'Emits nothing when created',
    build: () => RegisterMobileCubit(
      individualId: individualId,
      logger: mockLogger,
      errorHandlerTool: errorHandlerTool,
      twoFaDedBridge: twoFaDedBridge,
      signupInteractor: signUpInteractor,
      nextButtonViewModel: mockNextButtonViewModel,
      licensingAuthorityAnalytics: mockAnalytics,
    ),
    expect: () => <RegisterMobileState>[],
  );

  blocTest<RegisterMobileCubit, RegisterMobileState>(
    'Emit success on correct mobile registration',
    build: () => RegisterMobileCubit(
      individualId: individualId,
      errorHandlerTool: errorHandlerTool,
      twoFaDedBridge: twoFaDedBridge,
      signupInteractor: signUpInteractor,
      logger: mockLogger,
      nextButtonViewModel: mockNextButtonViewModel,
      licensingAuthorityAnalytics: mockAnalytics,
    ),
    setUp: () {
      when(() => twoFaDedBridge.executeWith2Fa<void>(any())).justAnswerAsync(
        const TwoFaCredentialsBridgeResult(twoFaId: '', result: null),
      );
    },
    act: (bloc) => bloc.registerMobile(),
    expect: () => <RegisterMobileState>[
      const RegisterMobileSuccessState(phoneNumber: ''),
    ],
  );

  blocTest<RegisterMobileCubit, RegisterMobileState>(
    'Emit failure on error on mobile registration',
    build: () => RegisterMobileCubit(
      errorHandlerTool: errorHandlerTool,
      twoFaDedBridge: twoFaDedBridge,
      signupInteractor: signUpInteractor,
      logger: mockLogger,
      individualId: individualId,
      nextButtonViewModel: mockNextButtonViewModel,
      licensingAuthorityAnalytics: mockAnalytics,
    ),
    setUp: () {
      when(() => twoFaDedBridge.executeWith2Fa<void>(any()))
          .justThrowAsync(Exception('Test Exception'));
    },
    act: (bloc) => bloc.registerMobile(),
    expect: () => <RegisterMobileState>[
      const RegisterMobileFailureState(phoneNumber: ''),
    ],
  );
}
