import 'dart:math';

import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui/extensions/locale_extension.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_invitation_ui/feature_invitation_ui.dart';
import 'package:wio_feature_invitation_ui/src/bottom_sheet/check_box_bottom_sheet/cubit/check_box_bottom_sheet_cubit.dart';
import 'package:wio_feature_invitation_ui/src/bottom_sheet/check_box_bottom_sheet/invitation_input.dart';

class CheckBoxBottomSheet extends StatelessWidget {
  final InvitationInput _invitationInput;

  const CheckBoxBottomSheet({
    required InvitationInput invitationInput,
    super.key,
  }) : _invitationInput = invitationInput;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => DependencyProvider.get<CheckBoxBottomSheetCubit>(),
      child: _CheckBoxBottomSheetContent(
        invitationInput: _invitationInput,
      ),
    );
  }
}

class _CheckBoxBottomSheetContent extends StatelessWidget {
  final InvitationInput invitationInput;

  const _CheckBoxBottomSheetContent({
    required this.invitationInput,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final double bottomPadding = max(
      mediaQuery.viewInsets.bottom,
      mediaQuery.viewPadding.bottom,
    );

    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(24, 28, 24, bottomPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const _Title(),
          const Space.vertical(8.0),
          const _SubTitle(),
          const Space.vertical(32.0),
          _CheckBoxComponent(invitationInput: invitationInput),
        ],
      ),
    );
  }
}

class _Title extends StatelessWidget {
  const _Title();

  @override
  Widget build(BuildContext context) {
    final localization = UserInvitationLocalizations.of(context);

    return Label(
      model: LabelModel(
        text: localization.beforeYouContinueTitleBottomSheet,
        textStyle: CompanyTextStylePointer.h3medium,
        color: CompanyColorPointer.primary3,
      ),
    );
  }
}

class _SubTitle extends StatelessWidget {
  const _SubTitle();

  @override
  Widget build(BuildContext context) {
    final localization = UserInvitationLocalizations.of(context);

    return Label(
      model: LabelModel(
        text: localization.reviewSubTitleBottomSheet,
        textStyle: CompanyTextStylePointer.b2,
        color: CompanyColorPointer.secondary3,
      ),
    );
  }
}

class _CheckBoxComponent extends StatefulWidget {
  final InvitationInput invitationInput;

  const _CheckBoxComponent({
    required this.invitationInput,
  });

  @override
  State<_CheckBoxComponent> createState() => _CheckBoxComponentState();
}

class _CheckBoxComponentState extends State<_CheckBoxComponent> {
  bool isCheckBoxChecked = false;

  void onCheckBoxChanged() {
    setState(() {
      isCheckBoxChecked = !isCheckBoxChecked;
    });
  }

  @override
  Widget build(BuildContext context) {
    final localization = UserInvitationLocalizations.of(context);
    final cubit = context.watch<CheckBoxBottomSheetCubit>();
    final isAr = context.isArabic;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        CheckboxTile(
          model: CheckboxTileModel(
            value: isCheckBoxChecked,
            labelModel: CompanyRichTextModel(
              accentStyle: CompanyTextStylePointer.b3underline,
              textAlign: TextAlign.start,
              highlightedTextModels: [
                HighlightedTextModel(
                  localization.multiUserTandC,
                  onTap: () => cubit.onTermsAndConditionsTextTapped(isAr: isAr),
                ),
              ],
              text: localization.acceptTAndC,
            ),
          ),
          onChanged: onCheckBoxChanged,
        ),
        const Space.vertical(40.0),
        Button(
          model: ButtonModel(
            title: localization.sendInviteButtonBottomSheet,
            loading: cubit.state.isLoading,
            size: ButtonSize.small,
            theme: ButtonModelTheme.sme,
          ),
          onPressed: isCheckBoxChecked
              ? () async =>
                  cubit.onContinueButtonPressed(widget.invitationInput)
              : null,
        ),
      ],
    );
  }
}
