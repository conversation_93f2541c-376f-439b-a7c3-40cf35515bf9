import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:domain/stream/exhaust_stream_executor.dart';
import 'package:logging_api/logging.dart';
import 'package:ui/cubit/base_cubit.dart';
import 'package:ui/cubit/extensions/bloc_extensions.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_invitation_api/domain/models/is_email_eligible.dart';
import 'package:wio_feature_invitation_api/domain/models/partner.dart';
import 'package:wio_feature_invitation_api/feature_invitation_api.dart';
import 'package:wio_feature_invitation_ui/feature_invitation_ui.dart';
import 'package:wio_feature_invitation_ui/src/bottom_sheet/check_box_bottom_sheet/invitation_input.dart';
import 'package:wio_feature_invitation_ui/src/navigation/check_box_bottom_sheet_navigation_config.dart';
import 'package:wio_feature_invitation_ui/src/navigation/select_role_bottom_sheet_navigation_config.dart';
import 'package:wio_feature_invitation_ui/src/navigation/signatory_bottom_sheet_navigation_config.dart';
import 'package:wio_feature_invitation_ui/src/screens/new_user_form/analytics/new_user_form_analytics.dart';
import 'package:wio_feature_invitation_ui/src/screens/new_user_form/new_user_form_router.dart';
import 'package:wio_feature_invitation_ui/src/screens/new_user_form/new_user_form_state.dart';
import 'package:wio_feature_invitation_ui/src/screens/new_user_form/select_role.dart';
import 'package:wio_feature_multiuser_api/multiuser_api.dart';

class NewUserFormCubit extends BaseCubit<NewUserFormState> {
  final NewUserFormRouter _newUserFormRouter;
  final InvitationInteractor _invitationInteractor;
  final UserInvitationLocalizations _userInvitationLocalizations;
  final ExhaustStreamExecutor _exhaustStreamExecutor;
  final NewUserFormCubitAnalytics _analytics;
  final FeatureToggleProvider _featureToggles;
  final Logger _logger;

  NewUserFormCubit({
    required NewUserFormRouter newUserFormRouter,
    required InvitationInteractor invitationInteractor,
    required UserInvitationLocalizations userInvitationLocalizations,
    required ExhaustStreamExecutor exhaustStreamExecutor,
    required NewUserFormCubitAnalytics analytics,
    required FeatureToggleProvider featureToggles,
    required Logger logger,
  })  : _newUserFormRouter = newUserFormRouter,
        _invitationInteractor = invitationInteractor,
        _userInvitationLocalizations = userInvitationLocalizations,
        _exhaustStreamExecutor = exhaustStreamExecutor,
        _analytics = analytics,
        _featureToggles = featureToggles,
        _logger = logger,
        super(
          NewUserFormState.empty(),
        );

  /// Emits the state with the error text if phone number is not valid
  ///
  /// Currently phone is validated only for UAE phone numbers
  void processPhoneValidation(String? phone) {
    if (phone == null || phone == '') {
      emit(
        state.copyWith(
          isPhoneValid: true,
          phoneNumberInvalidErrorText: null,
        ),
      );

      return;
    }

    emit(
      state.copyWith(
        isPhoneValid:
            DomainRegExps.uaePhoneNumberWithoutPrefix.hasMatch(phone.orEmpty()),
      ),
    );

    emit(
      state.copyWith(
        phoneNumberInvalidErrorText: state.isPhoneValid
            ? null
            : _userInvitationLocalizations.invalidPhoneNumber,
      ),
    );
  }

  /// Returns null if the validators passes or if the email is fine
  /// otherwise sends a message
  String? processEmailValidation(String? email) {
    if (email == null || email == '') {
      return null;
    }

    emit(
      state.copyWith(
        isEmailValid: DomainRegExps.email.hasMatch(email) &&
            !state.alreadyInUseEmails.contains(email),
      ),
    );

    // If it was already sent before an invitation using this email
    if (state.alreadyInUseEmails.contains(email)) {
      return _userInvitationLocalizations.multiUserEmailAlreadyInUse;
    }

    return state.isEmailValid
        ? null
        : _userInvitationLocalizations.invalidEmail;
  }

  /// Returns null if the validators passes or if the name is fine
  /// otherwise sends a message
  String? processNameValidation(String? name) {
    if (name == null) {
      return null;
    }

    if (name != name.trim()) {
      return _userInvitationLocalizations.multiuserInvalidName;
    }

    emit(
      state.copyWith(
        isNameValid: name.isNotEmpty,
      ),
    );

    return state.isNameValid ? null : _userInvitationLocalizations.emptyName;
  }

  Future<void> submit({
    required String name,
    required String email,
    required String phoneNumber,
    Partner? businessPartner,
  }) async {
    _exhaustStreamExecutor.run(
      () => _submitInternal(
        name: name.trim(),
        email: email.trim(),
        phoneNumber: phoneNumber,
        businessPartner: businessPartner,
      ).toStream(),
    );
  }

  Future<void> chooseAssociatedRole() async {
    _analytics.clickSelectRole();
    if (!_featureToggles
        .get(MultiuserFeatureToggles.areRelease1p2FeaturesAvailable)) return;
    final response = await _newUserFormRouter.showSelectRoleBottomSheet(
      const SelectRoleBottomSheetNavigationConfig(),
    );
    if (response == null) return;
    switch (response) {
      case SelectRole.manager:
        _analytics.clickFam();
        emit(state.copyWith(selectRole: SelectRole.manager));
        return;
      case SelectRole.preparer:
        _analytics.clickPreparer();
        emit(state.copyWith(selectRole: SelectRole.preparer));
        return;
      default:
        return;
    }
  }

  void addEmailAddressAnalytics() {
    _analytics.addEmailAddress();
  }

  void onBackButtonPressed() {
    _analytics.clickBackButton();
    _newUserFormRouter.goBack();
  }

  Future<void> _submitInternal({
    required String name,
    required String email,
    required String phoneNumber,
    Partner? businessPartner,
  }) async {
    _analytics.clickNextButton();
    var isEligibleSigningCheques = false;
    if (_featureToggles
            .get(MultiuserFeatureToggles.areRelease1p2FeaturesAvailable) &&
        state.selectRole == SelectRole.manager) {
      final signatoryReply = await _newUserFormRouter.showSignatoryBottomSheet(
        const SignatoryBottomSheetNavigationConfig(),
      );
      if (signatoryReply == null) {
        return;
      }
      isEligibleSigningCheques = signatoryReply;
    }

    return _onTermsConditionsBottomSheet(
      name: name,
      email: email,
      phoneNumber: phoneNumber,
      isEligibleForSigningCheques: isEligibleSigningCheques,
      businessPartner: businessPartner,
    );
  }

  Future<void> _onTermsConditionsBottomSheet({
    required String name,
    required String email,
    required String phoneNumber,
    required bool isEligibleForSigningCheques,
    Partner? businessPartner,
  }) async {
    try {
      emit(state.copyWith(isLoading: true));
      final emailEligibiltyResponse = await _invitationInteractor
          .isEmailEligibleForInvitationCreation(email: email);
      if (emailEligibiltyResponse.isEmailEligible == IsEmailEligible.eligible) {
        final termsAndConditions =
            await _invitationInteractor.getTermsAndConditions();

        final muTermsAndConditions = MuTermsAndConditions(
          termsAndConditionsAccepted: false,
          termsAndConditionsVersion: termsAndConditions.version,
        );

        final inviteeRole = _mapToMuInviteeRole(selectRole: state.selectRole);

        final res = await _newUserFormRouter.showCheckBoxBottomSheet(
          CheckBoxBottomSheetNavigationConfig(
            invitationInput: businessPartner != null
                ? InvitationInput.partnerInvitation(
                    individualId: businessPartner.individualid,
                    inviteeEmail: email,
                    inviteeFirstName: name,
                    inviteeRole: inviteeRole,
                    multiUserTermsAndConditions: muTermsAndConditions,
                    inviteePhoneNumber: phoneNumber,
                  )
                : InvitationInput.newUserInvitation(
                    inviteeFirstName: name,
                    inviteeEmail: email,
                    inviteeRole: inviteeRole,
                    multiUserTermsAndConditions: muTermsAndConditions,
                    isEligibleSigningCheques: isEligibleForSigningCheques,
                    inviteePhoneNumber: phoneNumber,
                  ),
          ),
        );

        if (res != null && res.alreadyInUseEmails.isNotEmpty) {
          emit(
            state.copyWith(
              alreadyInUseEmails: res.alreadyInUseEmails,
            ),
          );
        }
      } else if (emailEligibiltyResponse.isEmailEligible ==
          IsEmailEligible.alreadyUsed) {
        _analytics.emailAlreadyExistsError();
        emit(
          state.copyWith(
            alreadyInUseEmails: [email],
          ),
        );
      }
    } on Exception catch (e, stackTrace) {
      _logger.error('$e', stackTrace: stackTrace);
    } finally {
      emit(state.copyWith(isLoading: false));
    }
  }

  MuInviteeRole _mapToMuInviteeRole({required SelectRole selectRole}) {
    switch (selectRole) {
      case SelectRole.preparer:
        return MuInviteeRole.preparer;
      case SelectRole.manager:
        return MuInviteeRole.manager;
      default:
        throw Exception('Unhandled selected role');
    }
  }

  @override
  String toString() => 'NewUserFormCubit';
}
