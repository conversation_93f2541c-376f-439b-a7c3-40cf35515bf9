import 'package:sme_core_utils/utils.dart';
import 'package:wio_feature_invitation_api/domain/models/cancel_invitation_output.dart';
import 'package:wio_feature_invitation_api/domain/models/cancel_invitation_result.dart';
import 'package:wio_feature_invitation_api/domain/models/create_invitation_input.dart';
import 'package:wio_feature_invitation_api/domain/models/is_email_eligible.dart';
import 'package:wio_feature_invitation_api/domain/models/is_email_eligible_output.dart';
import 'package:wio_feature_invitation_api/domain/models/partner.dart';
import 'package:wio_feature_invitation_api/domain/models/partner_activation_input.dart';
import 'package:wio_feature_invitation_api/domain/models/resend_invitation_output.dart';
import 'package:wio_feature_invitation_api/feature_invitation_api.dart';

class InvitationRepositoryMock implements InvitationRepository {
  const InvitationRepositoryMock();

  @override
  Future<InviteCodeInfo> sendInvitation({
    required CreateInvitationInput createInvitation,
  }) =>
      Future.value(
        InviteCodeInfo(
          inviteCode: randomDouble(start: 100000, end: 999999).toString(),
          id: '',
          creatorLastName: 'Wio',
          creatorFirstName: 'Business',
        ),
      );

  @override
  Future<TermsAndConditions> getTermsAndConditions() => Future.value(
        const TermsAndConditions(
          arUrl:
              'https://assets.ctfassets.net/l65m9bcr2nac/5Fdrq0DAio5GfJ74HlVyk4/468409b01864aa648be90ddde361992d/Multi_User_Terms_And_Conditions.html',
          enUrl:
              'https://assets.ctfassets.net/l65m9bcr2nac/5Fdrq0DAio5GfJ74HlVyk4/468409b01864aa648be90ddde361992d/Multi_User_Terms_And_Conditions.html',
          version: 'v1',
        ),
      );

  @override
  Future<ResendInvitationOutput> resendInvitation({
    required String inviteId,
  }) async {
    return const ResendInvitationOutput(
      invitationCode: 'AEF54F',
      creatorFirstName: 'First',
      creatorSecondName: 'Last',
    );
  }

  @override
  Future<IsEmailEligibleOutput> isEmailEligibleForInvitationCreation({
    required String email,
  }) async {
    return const IsEmailEligibleOutput(
      isEmailEligible: IsEmailEligible.eligible,
    );
  }

  @override
  Future<CancelInvitationOutput> cancelInvitation({
    required String inviteId,
  }) async {
    return const CancelInvitationOutput(
      cancellationResult: CancelInvitationResult.success,
    );
  }

  @override
  Future<List<Partner>> getPartners() async {
    return Future.value([
      const Partner(
        firstName: 'Partner 1',
        lastName: 'Ln',
        individualid: 'individualId',
      ),
      const Partner(
        firstName: 'Partner 2',
        lastName: 'Ln',
        individualid: 'individualId',
      ),
    ]);
  }

  @override
  Future<InviteCodeInfo> sendInvitationForPartnerActivation({
    required PartnerActivationInput partnerActivationInput,
  }) =>
      Future.delayed(
        const Duration(seconds: 2),
        () => InviteCodeInfo(
          id: partnerActivationInput.individualId,
          inviteCode: '312212',
          creatorFirstName: 'User',
          creatorLastName: 'Usr',
        ),
      );
}
