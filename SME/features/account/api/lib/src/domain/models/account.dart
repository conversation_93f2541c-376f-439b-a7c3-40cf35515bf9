import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/money.dart';

part 'account.freezed.dart';

/// A bank account.
@freezed
class Account with _$Account {
  const factory Account({
    required String id,
    required String name,
    required double balance,
    required String currency,
    DateTime? createdDate,
    String? nickname,
    String? type,
    double? availableBalance,
    String? description,
    String? bIC,
    String? iBAN,
    String? address,
    @Default(AccountState.unknown) AccountState state,
    @Default(AccountSubState.unknown) AccountSubState subState,
    @Deprecated('Use the "state" field instead') String? accountState,
    @Deprecated('Use the "subState" field instead') String? substate,
  }) = _Account;

  const Account._();

  Money get balanceMoney => Money.fromNum(balance, code: currency);

  // NOTE: availableBalance should not be null
  Money get availableBalanceMoney =>
      Money.fromNum(availableBalance ?? 0, code: currency);

  /// Indicates whether or not money can be transferred from this account.
  bool get canTransferFrom => !const {
        AccountSubState.restrictedDebit,
        AccountSubState.totalFreeze,
      }.contains(subState);

  /// Indicates whether or not money can be transferred to this account.
  bool get canTransferTo => !const {
        AccountSubState.restrictedCredit,
        AccountSubState.totalFreeze,
      }.contains(subState);
}

// Taken from https://dev.azure.com/neobank/neobank-platform/_git/app-account-common?path=/account-library/src/main/java/io/wio/account/core/model/common/account/AccountState.java
enum AccountState {
  // Used in SME
  approved,
  active,
  locked,
  closed,
  dormant,

  // Not used in SME
  withdrawn,
  pendingApproval,
  closedRejected,
  closedWrittenOff,
  matured,
  activeInArrears,

  // Needed because the server may return null
  unknown,
}

// Taken from https://dev.azure.com/neobank/neobank-platform/_git/app-account-common?path=/account-library/src/main/java/io/wio/account/core/model/common/account/AccountSubState.java
enum AccountSubState {
  active,
  restrictedDebit,
  restrictedCredit,
  totalFreeze,

  // Needed because the server may return null
  unknown,
}
