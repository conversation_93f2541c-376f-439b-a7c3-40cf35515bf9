import 'package:account_feature_api/account_feature_api.dart';
import 'package:data/auth_manager/auth_manager.dart';
import 'package:deeplink_manager/storage/deep_link_repository.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_account_ui/src/navigation/usd_account_deeplink_handler.dart';

import '../feature_mocks.dart';

void main() {
  late UsdAccountDeeplinkHandler handler;
  late IAuthManager authManager;
  late DeepLinkRepository deepLinkRepository;
  late AccountFlow accountFlow;

  setUp(() {
    authManager = MockAuthManager();
    deepLinkRepository = MockDeepLinkRepository();
    accountFlow = MockAccountFlow();

    handler = UsdAccountDeeplinkHandler(
      authManager: authManager,
      deepLinkRepository: deepLinkRepository,
      accountFlow: accountFlow,
    );
  });

  group('UsdAccountDeeplinkHandler', () {
    final validDeepLink = DeepLink(
      Uri.parse('wiobusiness://wio.io/usd_multicurrency_bottom_sheet'),
    );

    final invalidDeepLink = DeepLink(
      Uri.parse('wiobusiness://wio.io/different_path'),
    );

    group('tryHandle', () {
      test('should return false for invalid deep link', () async {
        // Act
        final result = await handler.tryHandle(invalidDeepLink);

        // Assert
        expect(result, isFalse);
        verifyNever(() => authManager.isUserAuthenticated());
        verifyNever(() => accountFlow.openUSDAccountOpeningFlow());
      });

      test(
          'should store deep link and return true '
          'when user is not authenticated', () async {
        // Arrange
        when(() => authManager.isUserAuthenticated()).justAnswerAsync(false);

        // Act
        final result = await handler.tryHandle(validDeepLink);

        // Assert
        expect(result, isTrue);
        verify(() => authManager.isUserAuthenticated()).calledOnce;
        verify(
          () => deepLinkRepository.authenticatedUserDeepLink = validDeepLink,
        ).calledOnce;
        verifyNever(() => accountFlow.openUSDAccountOpeningFlow());
      });

      test(
          'should handle USD account opening flow '
          'when user is authenticated', () async {
        // Arrange
        when(() => authManager.isUserAuthenticated()).justAnswerAsync(true);
        when(() => accountFlow.openUSDAccountOpeningFlow()).justCompleteAsync();

        // Act
        final result = await handler.tryHandle(validDeepLink);

        // Assert
        expect(result, isTrue);
        verify(() => authManager.isUserAuthenticated()).calledOnce;
        verify(() => accountFlow.openUSDAccountOpeningFlow()).calledOnce;
      });
    });

    group('_canHandle', () {
      test('should return false for empty path', () {
        // Arrange
        final emptyPathDeepLink = DeepLink(Uri.parse('wiobusiness://wio.io'));

        // Act
        final result = handler.tryHandle(emptyPathDeepLink);

        // Assert
        expect(result, completion(isFalse));
      });

      test('should return true for valid path and screen name', () {
        // Arrange
        when(() => authManager.isUserAuthenticated()).justAnswerAsync(true);
        when(() => accountFlow.openUSDAccountOpeningFlow())
            .justAnswerEmptyAsync();

        // Act
        final result = handler.tryHandle(validDeepLink);

        // Assert
        expect(result, completion(isTrue));
      });
    });
  });
}
