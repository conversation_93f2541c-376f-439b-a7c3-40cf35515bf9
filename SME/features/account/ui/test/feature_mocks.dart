import 'package:account_feature_api/account_feature_api.dart';
import 'package:common_feature_fx_api/feature_fx_api.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:data/data.dart';
import 'package:deeplink_manager/deep_link.dart';
import 'package:feature_2fa_api/feature_2fa_api.dart';
import 'package:feature_login_api/feature_login_api.dart';
import 'package:mocktail/mocktail.dart';
import 'package:sme_feature_transactions_api/feature_transactions_api.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_account_ui/feature_account_ui.dart';
import 'package:wio_feature_account_ui/src/bottom_sheet/account_creation/analytics/account_creation_analytics.dart';
import 'package:wio_feature_account_ui/src/bottom_sheet/account_selection/analytics/account_selection_analytics.dart';
import 'package:wio_feature_account_ui/src/bottom_sheet/multi_currency_account_closure/helper/multi_currency_account_closure_status_navigation_config.dart';
import 'package:wio_feature_account_ui/src/screens/account_details/analytics/account_details_analytics.dart';
import 'package:wio_feature_account_ui/src/screens/accounts/accounts_router.dart';
import 'package:wio_feature_account_ui/src/screens/accounts/analytics/account_details_analytics.dart';
import 'package:wio_feature_account_ui/src/screens/accounts/guides/account_guides_mediator.dart';
import 'package:wio_feature_account_ui/src/screens/move_money/move_money_handler.dart';
import 'package:wio_feature_behaviour_api/feature_behaviour_api.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_dashboard_api/domain/dashboard_interactor.dart';
import 'package:wio_feature_delivery_api/delivery_api.dart';
import 'package:wio_feature_easy_cash_api/easy_cash_api.dart';
import 'package:wio_feature_home_api/wio_feature_home_api.dart';
import 'package:wio_feature_mu_payment_requests_api/feature_mu_payment_requests_api.dart';
import 'package:wio_feature_share_api/feature_share_api.dart';
import 'package:wio_sme_error_handler_api/error_handler_api.dart';

import 'feature_fakes.dart';

class MockTransactionsMediator extends Mock implements TransactionsMediator {}

class MockMoveMoneyHandler extends Mock implements MoveMoneyHandler {
  @override
  void dispose() {}
}

class MockMultiCurrencyAccountInteractor extends Mock
    implements MultiCurrencyAccountInteractor {
  MockMultiCurrencyAccountInteractor() : super() {
    registerFallbackValue(
      CreateMuMultiCurrencyInput(
        name: '${Currency.euro.code} account',
        currency: Currency.euro.code,
      ),
    );
  }
}

class MockAccountInteractor extends Mock implements AccountInteractor {}

class MockDashboardInteractor extends Mock implements DashboardInteractor {}

class MockSelectedAccountInteractor extends Mock
    implements SelectedAccountInteractor {}

class MockAccountsAnalytics extends Mock implements AccountsAnalytics {}

class MockFxFlow extends Mock implements FXFlow {}

class MockAccountLocalizations extends Mock implements AccountLocalizations {}

class MockAccountActionsProvider extends Mock
    implements AccountActionsProvider {}

class MockAccountDetailsInteractor extends Mock
    implements AccountDetailsInteractor {}

class MockAccountDetailsAnalytics extends Mock
    implements AccountDetailsAnalytics {}

class MockShareProvider extends Mock implements ShareProvider {}

class MockFeatureToggleProvider extends Mock implements FeatureToggleProvider {}

class FakeToastMessageConfiguration extends Fake
    implements ToastMessageConfiguration {}

class MockAccountSelectionAnalytics extends Mock
    implements AccountSelectionAnalytics {
  MockAccountSelectionAnalytics() : super() {
    registerFallbackValue(Currency.aed);
  }
}

class MockCreditAccountInteractor extends Mock
    implements CreditAccountInteractor {}

class MockEasyCashInteractor extends Mock implements EasyCashInteractor {}

class MockMuInteractor extends Mock implements MuPaymentRequestsInteractor {}

class MockMuAccountInteractor extends Mock implements MuAccountInteractor {}

class MockMultiCurrencyInteractor extends Mock
    implements MultiCurrencyAccountInteractor {}

class MockAccountCreationAnalytics extends Mock
    implements AccountCreationAnalytics {
  MockAccountCreationAnalytics() : super() {
    registerFallbackValue(Currency.aed);
  }
}

class MockBehaviourProvider extends Mock implements BehaviourProvider {
  MockBehaviourProvider() : super() {
    registerFallbackValue(true);
  }
}

class MockErrorHandler extends Mock implements ErrorHandlerTool {}

class MockChequebookInteractor extends Mock implements ChequebookInteractor {}

class MockAccountGuidesMediator extends Mock implements AccountGuidesMediator {}

class MockAccountGuidesInteractor extends Mock
    implements AccountGuidesInteractor {}

class MockAccountsRouter extends Mock implements AccountsRouter {}

void registerFallbackValues() {
  registerFallbackValue(FakeToastMessageConfiguration());
}

class MockEasyCashFlow extends Mock implements EasyCashFlow {
  MockEasyCashFlow() : super() {
    registerFallbackValue(easyCashData.easyCashLimit);
  }
}

class FakeBottomSheetNavigationConfig<T> extends Fake
    implements BottomSheetNavigationConfig<T> {
  @override
  String toString() => 'FakeBottomSheetNavigationConfig';
}

class MockLoginInteractor extends Mock implements LoginInteractor {}

class MockChangeHomeTabChannel extends Mock implements ChangeHomeTabChannel {}

class MockAuthManager extends Mock implements IAuthManager {}

class MockAccountFlow extends Mock implements AccountFlow {}

class MockDeepLinkRepository extends Mock implements DeepLinkRepository {}

class MockIdentityNotificationChannel extends Mock
    implements IdentityNotificationChannel {}

class MockErrorHandlerTool extends Mock implements ErrorHandlerTool {}

class MockMultiCurrencyAccountClosureStatusPageNavigationConfig extends Mock
    implements MultiCurrencyAccountClosureStatusPageNavigationConfig {}
