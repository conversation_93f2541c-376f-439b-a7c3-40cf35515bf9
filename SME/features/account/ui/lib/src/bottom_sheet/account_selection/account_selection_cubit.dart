import 'package:account_feature_api/account_feature_api.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:logging_api/logging.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ui/ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_account_ui/src/bottom_sheet/account_selection/account_selection_state.dart';
import 'package:wio_feature_account_ui/src/bottom_sheet/account_selection/analytics/account_selection_analytics.dart';
import 'package:wio_feature_account_ui/src/common/extensions.dart';
import 'package:wio_feature_behaviour_api/feature_behaviour_api.dart';
import 'package:wio_feature_dashboard_api/domain/dashboard_interactor.dart';
import 'package:wio_feature_easy_cash_api/easy_cash_api.dart';
import 'package:wio_feature_mu_payment_requests_api/domain/domain_interactors/multi_currency_account_interactor.dart';
import 'package:wio_feature_mu_payment_requests_api/domain/models/domain_specific_mu_models/account_creation/create_new_account_currency.dart';
import 'package:wio_sme_behaviour/wio_sme_behaviour.dart';

class AccountSelectionCubit extends BaseCubit<AccountSelectionState> {
  final NavigationProvider _navigationProvider;
  final MultiCurrencyAccountInteractor _multiCurrencyAccountInteractor;
  final DashboardInteractor _dashboardInteractor;
  final Logger _logger;
  final AccountSelectionAnalytics _analytics;
  final ErrorMessageProvider _errorMessageProvider;
  final CommonLocalizations _commonLocalizations;
  final BehaviourProvider _behaviourProvider;
  final FeatureToggleProvider _featureToggles;
  final EasyCashFlow _easyCashFlow;

  AccountSelectionCubit({
    required NavigationProvider navigationProvider,
    required MultiCurrencyAccountInteractor multiCurrencyAccountInteractor,
    required DashboardInteractor dashboardInteractor,
    required Logger logger,
    required AccountSelectionAnalytics analytics,
    required ErrorMessageProvider errorMessageProvider,
    required BehaviourProvider behaviourProvider,
    required CommonLocalizations commonLocalizations,
    required FeatureToggleProvider featureToggles,
    required EasyCashFlow easyCashFlow,
  })  : _navigationProvider = navigationProvider,
        _multiCurrencyAccountInteractor = multiCurrencyAccountInteractor,
        _dashboardInteractor = dashboardInteractor,
        _logger = logger,
        _analytics = analytics,
        _errorMessageProvider = errorMessageProvider,
        _behaviourProvider = behaviourProvider,
        _commonLocalizations = commonLocalizations,
        _featureToggles = featureToggles,
        _easyCashFlow = easyCashFlow,
        super(const AccountSelectionState.initial());

  bool get _isCreatingNewAccountAvailable =>
      _behaviourProvider.hasPermission<AccountsAccountOpenCreatePermission>();

  bool get _areAdditionalCurrencyAccountsEnabled => _featureToggles
      .get(AccountFeatureToggles.areAdditionalCurrencyAccountsEnabled);

  void initialize({
    required List<AccountSummary> accounts,
    String? selectedAccountId,
    EasyCashLimit? easyCashLimit,
    String? creditAccountId,
    int? dueDays,
  }) {
    state.mapOrNull(
      initial: (_) {
        final isAccountPresent = selectedAccountId != null &&
            accounts.any((it) => it.accountId == selectedAccountId);

        emit(
          AccountSelectionState.idle(
            accounts: accounts,
            selectedAccountId: isAccountPresent ? selectedAccountId : null,
            isCreatingNewAccountAvailable: _isCreatingNewAccountAvailable,
            areAdditionalCurrencyAccountsEnabled:
                _areAdditionalCurrencyAccountsEnabled,
            easyCashLimit: easyCashLimit,
            creditAccountId: creditAccountId,
            dueDays: dueDays,
          ),
        );
      },
    );
  }

  void selectAccount(AccountSummary account) {
    if (!state.canSelectAccount) {
      return;
    }

    account.map(
      (account) {
        _analytics.accountSelected(account.currency);

        return _navigationProvider.goBack(
          AccountSelectionResult.singleAccount(account),
        );
      },
      all: (_) {
        _analytics.allAccountsSelected();

        return _navigationProvider.goBack(
          const AccountSelectionResult.allAccounts(),
        );
      },
    );
  }

  Future<void> openAccount() async {
    return state.mapOrNull<Future<void>>(
      idle: (idle) async {
        _analytics.openNewAccountSelected();

        return Rx.combineLatest2(
          _multiCurrencyAccountInteractor
              .getAvailableCurrencies()
              .toList()
              .toStream(),
          _getPromoPageSeen(),
          (currencies, promoPageSeen) => (currencies, promoPageSeen),
        )
            .where((_) => !isClosed)
            .doOnListen(() => emit(idle.toLoading()))
            .doOnData((result) async {
          emit(idle);

          final (currencies, promoPageSeen) = result;

          return _handleOpenAccountCreation(
            currencies,
            idle.accounts,
            addMultipleAccountsPromoPageSeen: promoPageSeen,
          );
        }).withError((error) {
          emit(idle);

          return _handleError(error);
        }).complete();
      },
    );
  }

  Stream<bool> _getPromoPageSeen() {
    if (_areAdditionalCurrencyAccountsEnabled) {
      return _dashboardInteractor
          .getHasUserSeenAddMultipleAccountsPromoPage()
          .toStream();
    }

    return Stream.value(true);
  }

  Future<void> _handleOpenAccountCreation(
    List<CreateNewAccountCurrency> currencies,
    List<AccountSummary> accounts, {
    bool addMultipleAccountsPromoPageSeen = true,
  }) async {
    // Should not happen
    if (currencies.isEmpty) {
      _logger.warning('Cannot create a new account - currency list is empty');

      return _navigationProvider.goBack();
    }

    if (_areAdditionalCurrencyAccountsEnabled &&
        !addMultipleAccountsPromoPageSeen) {
      final promoResult = await _navigationProvider
          .push(const AddMultipleAccountsPromoScreenNavigationConfig());

      if (promoResult is AddMultipleAccountsPromoResult) {
        _onAddMultipleAccountsPromoPageSeen();
      }
    }

    return _goBack(currencies, accounts);
  }

  void _goBack(
    List<CreateNewAccountCurrency> currencies,
    List<AccountSummary> accounts,
  ) {
    final allCurrencies = currencies.toCurrencyIntMap();
    final existingCurrencies = accounts.toCurrencyIntMap();

    if (!_areAdditionalCurrencyAccountsEnabled) {
      allCurrencies.overrideCounts(count: 1);
    }

    final availableCurrencies = allCurrencies.subtract(existingCurrencies);

    return _navigationProvider.goBack(
      AccountSelectionResult.newAccountIntent(
        availableCurrencies: availableCurrencies,
      ),
    );
  }

  void _handleError(Object? error, {StackTrace? stackTrace}) {
    _errorMessageProvider.showError(_commonLocalizations.common_error_message);
    _logger.error(
      'Failed to load account currencies',
      error: error,
      stackTrace: stackTrace,
    );
  }

  void _onAddMultipleAccountsPromoPageSeen() =>
      _dashboardInteractor.markAddMultipleAccountsPromoPageAsSeen();

  void startEasyCashFlow() {
    state.mapOrNull(
      idle: (it) {
        final accountId = it.creditAccountId;
        final easyCashLimit = it.easyCashLimit;
        if (accountId != null && easyCashLimit != null) {
          _easyCashFlow.startFlow(
            creditAccountId: accountId,
            easyCashLimit: easyCashLimit,
          );
        }
      },
    );
  }

  @override
  String toString() => 'AccountSelectionCubit';
}
