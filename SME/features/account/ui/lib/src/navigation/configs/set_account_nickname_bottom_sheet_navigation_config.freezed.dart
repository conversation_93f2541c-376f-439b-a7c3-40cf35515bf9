// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'set_account_nickname_bottom_sheet_navigation_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SetAccountNicknameBottomSheetNavigationConfig {
  String get selectedAccountId => throw _privateConstructorUsedError;
  String get selectedAccountNickname => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SetAccountNicknameBottomSheetNavigationConfigCopyWith<
          SetAccountNicknameBottomSheetNavigationConfig>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SetAccountNicknameBottomSheetNavigationConfigCopyWith<$Res> {
  factory $SetAccountNicknameBottomSheetNavigationConfigCopyWith(
          SetAccountNicknameBottomSheetNavigationConfig value,
          $Res Function(SetAccountNicknameBottomSheetNavigationConfig) then) =
      _$SetAccountNicknameBottomSheetNavigationConfigCopyWithImpl<$Res,
          SetAccountNicknameBottomSheetNavigationConfig>;
  @useResult
  $Res call({String selectedAccountId, String selectedAccountNickname});
}

/// @nodoc
class _$SetAccountNicknameBottomSheetNavigationConfigCopyWithImpl<$Res,
        $Val extends SetAccountNicknameBottomSheetNavigationConfig>
    implements $SetAccountNicknameBottomSheetNavigationConfigCopyWith<$Res> {
  _$SetAccountNicknameBottomSheetNavigationConfigCopyWithImpl(
      this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedAccountId = null,
    Object? selectedAccountNickname = null,
  }) {
    return _then(_value.copyWith(
      selectedAccountId: null == selectedAccountId
          ? _value.selectedAccountId
          : selectedAccountId // ignore: cast_nullable_to_non_nullable
              as String,
      selectedAccountNickname: null == selectedAccountNickname
          ? _value.selectedAccountNickname
          : selectedAccountNickname // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SetAccountNicknameBottomSheetNavigationConfigImplCopyWith<
        $Res>
    implements $SetAccountNicknameBottomSheetNavigationConfigCopyWith<$Res> {
  factory _$$SetAccountNicknameBottomSheetNavigationConfigImplCopyWith(
          _$SetAccountNicknameBottomSheetNavigationConfigImpl value,
          $Res Function(_$SetAccountNicknameBottomSheetNavigationConfigImpl)
              then) =
      __$$SetAccountNicknameBottomSheetNavigationConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String selectedAccountId, String selectedAccountNickname});
}

/// @nodoc
class __$$SetAccountNicknameBottomSheetNavigationConfigImplCopyWithImpl<$Res>
    extends _$SetAccountNicknameBottomSheetNavigationConfigCopyWithImpl<$Res,
        _$SetAccountNicknameBottomSheetNavigationConfigImpl>
    implements
        _$$SetAccountNicknameBottomSheetNavigationConfigImplCopyWith<$Res> {
  __$$SetAccountNicknameBottomSheetNavigationConfigImplCopyWithImpl(
      _$SetAccountNicknameBottomSheetNavigationConfigImpl _value,
      $Res Function(_$SetAccountNicknameBottomSheetNavigationConfigImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedAccountId = null,
    Object? selectedAccountNickname = null,
  }) {
    return _then(_$SetAccountNicknameBottomSheetNavigationConfigImpl(
      selectedAccountId: null == selectedAccountId
          ? _value.selectedAccountId
          : selectedAccountId // ignore: cast_nullable_to_non_nullable
              as String,
      selectedAccountNickname: null == selectedAccountNickname
          ? _value.selectedAccountNickname
          : selectedAccountNickname // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SetAccountNicknameBottomSheetNavigationConfigImpl
    extends _SetAccountNicknameBottomSheetNavigationConfig {
  const _$SetAccountNicknameBottomSheetNavigationConfigImpl(
      {required this.selectedAccountId, required this.selectedAccountNickname})
      : super._();

  @override
  final String selectedAccountId;
  @override
  final String selectedAccountNickname;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetAccountNicknameBottomSheetNavigationConfigImpl &&
            (identical(other.selectedAccountId, selectedAccountId) ||
                other.selectedAccountId == selectedAccountId) &&
            (identical(
                    other.selectedAccountNickname, selectedAccountNickname) ||
                other.selectedAccountNickname == selectedAccountNickname));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, selectedAccountId, selectedAccountNickname);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SetAccountNicknameBottomSheetNavigationConfigImplCopyWith<
          _$SetAccountNicknameBottomSheetNavigationConfigImpl>
      get copyWith =>
          __$$SetAccountNicknameBottomSheetNavigationConfigImplCopyWithImpl<
                  _$SetAccountNicknameBottomSheetNavigationConfigImpl>(
              this, _$identity);
}

abstract class _SetAccountNicknameBottomSheetNavigationConfig
    extends SetAccountNicknameBottomSheetNavigationConfig {
  const factory _SetAccountNicknameBottomSheetNavigationConfig(
          {required final String selectedAccountId,
          required final String selectedAccountNickname}) =
      _$SetAccountNicknameBottomSheetNavigationConfigImpl;
  const _SetAccountNicknameBottomSheetNavigationConfig._() : super._();

  @override
  String get selectedAccountId;
  @override
  String get selectedAccountNickname;
  @override
  @JsonKey(ignore: true)
  _$$SetAccountNicknameBottomSheetNavigationConfigImplCopyWith<
          _$SetAccountNicknameBottomSheetNavigationConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}
