import 'dart:ui';

import 'package:account_feature_api/account_feature_api.dart';
import 'package:account_ui_desktop/locale/account_ui_desktop_localizations.g.dart';
import 'package:account_ui_desktop/src/screens/move_money/move_money_handler.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:ui/ota_lokalisation/ota_lokalization.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_mu_payment_requests_api/feature_mu_payment_requests_api.dart';
import 'package:wio_sme_status_api/wio_sme_status_api.dart';

import '../../../mocks.dart';
import '../../../test_entities.dart';

void main() {
  late AccountUILocalizations accountLocalizations;

  late MuPaymentRequestsInteractor muInteractor;
  late MuAccountInteractor muA<PERSON>untInteractor;
  late NavigationProvider navigationProvider;
  late MoveMoneyHandler handler;

  // Test data
  final amount = Money.fromNumWithCurrency(42, Currency.aed);
  final fromAccount = TestEntities.getAccount(id: '1', currency: 'AED');
  final toAccount = TestEntities.getAccount(id: '2', currency: 'AED');

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    accountLocalizations =
        await AccountUILocalizations.load(const Locale('en'));
  });

  setUp(() {
    muInteractor = MockMuRequestInteractor();
    muAccountInteractor = MockMuAccountInteractor();
    navigationProvider = MockNavigationProvider();

    handler = MoveMoneyHandler(
      multiuserInteractor: muInteractor,
      multiuserAccountInteractor: muAccountInteractor,
      accountLocalizations: accountLocalizations,
      navigationProvider: navigationProvider,
    );
  });

  void stubTransferRequest(MoneyTransferRequest request) {
    when(
      () => muAccountInteractor.createMoneyTransferRequest(
        amount: amount,
        fromAccountId: fromAccount.id,
        toAccountId: toAccount.id,
      ),
    ).justAnswerAsync(request);
  }

  group('Multiuser "Approver" flow >', () {
    final domain = TestEntities.testMuDomain(
      flowType: MuCreationFlowType.selfApprover,
    );
    final request = MoneyTransferRequest(
      domainId: randomString(),
      requestId: randomString(),
    );

    test('handles approver flow (success)', () async {
      // Arrange
      stubTransferRequest(request);
      when(
        () => muInteractor.getMuDomain(request.requestId),
      ).justAnswerAsync(domain);
      when(
        () => muInteractor.approveRequest(domain.requestId),
      ).justAnswerAsync(true);
      when(
        () => navigationProvider.replace(
          any(that: isA<SuccessScreenNavigationConfig>()),
          result: MoveMoneyScreenResult.showDashboard,
        ),
      ).justCompleteAsync();

      // Act
      await handler
          .moveMoney(
            amount: amount,
            fromAccount: fromAccount,
            toAccount: toAccount,
          )
          .drain<void>();

      // Assert
      verifyInOrder([
        () => muAccountInteractor.createMoneyTransferRequest(
              amount: amount,
              fromAccountId: fromAccount.id,
              toAccountId: toAccount.id,
            ),
        () => muInteractor.getMuDomain(request.requestId),
        () => muInteractor.approveRequest(domain.requestId),
        () => navigationProvider.replace(
              any(that: isA<SuccessScreenNavigationConfig>()),
              result: MoveMoneyScreenResult.showDashboard,
            ),
      ]);
    });

    test('throws error when request was not approved', () {
      // Arrange
      stubTransferRequest(request);
      when(
        () => muInteractor.getMuDomain(request.requestId),
      ).justAnswerAsync(domain);
      when(
        () => muInteractor.approveRequest(domain.requestId),
      ).justAnswerAsync(false);

      // Act & Assert
      expect(
        () => handler
            .moveMoney(
              amount: amount,
              fromAccount: fromAccount,
              toAccount: toAccount,
            )
            .drain<void>(),
        throwsA(isA<RequestNotSelfApprovedException>()),
      );
    });
  });

  group('Multiuser "Preparer" flow >', () {
    // FIXME(esultanli): add tests once I figure out how to mock the internal
    //  completer
  });
}
