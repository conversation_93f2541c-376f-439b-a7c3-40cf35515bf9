import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:wio_feature_behaviour_api/feature_behaviour_api.dart';
import 'package:wio_feature_utap_api/feature_toggle/utap_feature_toggle.dart';
import 'package:wio_sme_behaviour/wio_sme_behaviour.dart';

mixin PermissionsMixin {
  BehaviourProvider get behaviourProvider;

  FeatureToggleProvider get featureToggles;

  bool get isEnableUTapFeatureToggle =>
      featureToggles.get(UTapFeatureToggles.isUTapFeatureEnabled);

  /// Define if user can see UTap card
  bool get canReadUTap => behaviourProvider.hasPermission<UTapReadPermission>();

  /// Define if user can see UTap card with Apply Now button
  bool get canWriteUTap =>
      behaviourProvider.hasPermission<UTapWritePermission>();
}
