// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_detail_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AccountDetailState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(List<Account> accounts, int selectedAccountIndex,
            bool isMultiAccountEnabled)
        loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(List<Account> accounts, int selectedAccountIndex,
            bool isMultiAccountEnabled)?
        loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(List<Account> accounts, int selectedAccountIndex,
            bool isMultiAccountEnabled)?
        loaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AccountDetailStateInitial value) initial,
    required TResult Function(_AccountDetailStateLoaded value) loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AccountDetailStateInitial value)? initial,
    TResult? Function(_AccountDetailStateLoaded value)? loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AccountDetailStateInitial value)? initial,
    TResult Function(_AccountDetailStateLoaded value)? loaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountDetailStateCopyWith<$Res> {
  factory $AccountDetailStateCopyWith(
          AccountDetailState value, $Res Function(AccountDetailState) then) =
      _$AccountDetailStateCopyWithImpl<$Res, AccountDetailState>;
}

/// @nodoc
class _$AccountDetailStateCopyWithImpl<$Res, $Val extends AccountDetailState>
    implements $AccountDetailStateCopyWith<$Res> {
  _$AccountDetailStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$AccountDetailStateInitialImplCopyWith<$Res> {
  factory _$$AccountDetailStateInitialImplCopyWith(
          _$AccountDetailStateInitialImpl value,
          $Res Function(_$AccountDetailStateInitialImpl) then) =
      __$$AccountDetailStateInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AccountDetailStateInitialImplCopyWithImpl<$Res>
    extends _$AccountDetailStateCopyWithImpl<$Res,
        _$AccountDetailStateInitialImpl>
    implements _$$AccountDetailStateInitialImplCopyWith<$Res> {
  __$$AccountDetailStateInitialImplCopyWithImpl(
      _$AccountDetailStateInitialImpl _value,
      $Res Function(_$AccountDetailStateInitialImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$AccountDetailStateInitialImpl implements _AccountDetailStateInitial {
  const _$AccountDetailStateInitialImpl();

  @override
  String toString() {
    return 'AccountDetailState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountDetailStateInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(List<Account> accounts, int selectedAccountIndex,
            bool isMultiAccountEnabled)
        loaded,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(List<Account> accounts, int selectedAccountIndex,
            bool isMultiAccountEnabled)?
        loaded,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(List<Account> accounts, int selectedAccountIndex,
            bool isMultiAccountEnabled)?
        loaded,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AccountDetailStateInitial value) initial,
    required TResult Function(_AccountDetailStateLoaded value) loaded,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AccountDetailStateInitial value)? initial,
    TResult? Function(_AccountDetailStateLoaded value)? loaded,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AccountDetailStateInitial value)? initial,
    TResult Function(_AccountDetailStateLoaded value)? loaded,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _AccountDetailStateInitial implements AccountDetailState {
  const factory _AccountDetailStateInitial() = _$AccountDetailStateInitialImpl;
}

/// @nodoc
abstract class _$$AccountDetailStateLoadedImplCopyWith<$Res> {
  factory _$$AccountDetailStateLoadedImplCopyWith(
          _$AccountDetailStateLoadedImpl value,
          $Res Function(_$AccountDetailStateLoadedImpl) then) =
      __$$AccountDetailStateLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<Account> accounts,
      int selectedAccountIndex,
      bool isMultiAccountEnabled});
}

/// @nodoc
class __$$AccountDetailStateLoadedImplCopyWithImpl<$Res>
    extends _$AccountDetailStateCopyWithImpl<$Res,
        _$AccountDetailStateLoadedImpl>
    implements _$$AccountDetailStateLoadedImplCopyWith<$Res> {
  __$$AccountDetailStateLoadedImplCopyWithImpl(
      _$AccountDetailStateLoadedImpl _value,
      $Res Function(_$AccountDetailStateLoadedImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accounts = null,
    Object? selectedAccountIndex = null,
    Object? isMultiAccountEnabled = null,
  }) {
    return _then(_$AccountDetailStateLoadedImpl(
      accounts: null == accounts
          ? _value._accounts
          : accounts // ignore: cast_nullable_to_non_nullable
              as List<Account>,
      selectedAccountIndex: null == selectedAccountIndex
          ? _value.selectedAccountIndex
          : selectedAccountIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isMultiAccountEnabled: null == isMultiAccountEnabled
          ? _value.isMultiAccountEnabled
          : isMultiAccountEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$AccountDetailStateLoadedImpl implements _AccountDetailStateLoaded {
  const _$AccountDetailStateLoadedImpl(
      {required final List<Account> accounts,
      required this.selectedAccountIndex,
      this.isMultiAccountEnabled = false})
      : _accounts = accounts;

  final List<Account> _accounts;
  @override
  List<Account> get accounts {
    if (_accounts is EqualUnmodifiableListView) return _accounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_accounts);
  }

  @override
  final int selectedAccountIndex;
  @override
  @JsonKey()
  final bool isMultiAccountEnabled;

  @override
  String toString() {
    return 'AccountDetailState.loaded(accounts: $accounts, selectedAccountIndex: $selectedAccountIndex, isMultiAccountEnabled: $isMultiAccountEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountDetailStateLoadedImpl &&
            const DeepCollectionEquality().equals(other._accounts, _accounts) &&
            (identical(other.selectedAccountIndex, selectedAccountIndex) ||
                other.selectedAccountIndex == selectedAccountIndex) &&
            (identical(other.isMultiAccountEnabled, isMultiAccountEnabled) ||
                other.isMultiAccountEnabled == isMultiAccountEnabled));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_accounts),
      selectedAccountIndex,
      isMultiAccountEnabled);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountDetailStateLoadedImplCopyWith<_$AccountDetailStateLoadedImpl>
      get copyWith => __$$AccountDetailStateLoadedImplCopyWithImpl<
          _$AccountDetailStateLoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(List<Account> accounts, int selectedAccountIndex,
            bool isMultiAccountEnabled)
        loaded,
  }) {
    return loaded(accounts, selectedAccountIndex, isMultiAccountEnabled);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(List<Account> accounts, int selectedAccountIndex,
            bool isMultiAccountEnabled)?
        loaded,
  }) {
    return loaded?.call(accounts, selectedAccountIndex, isMultiAccountEnabled);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(List<Account> accounts, int selectedAccountIndex,
            bool isMultiAccountEnabled)?
        loaded,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(accounts, selectedAccountIndex, isMultiAccountEnabled);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AccountDetailStateInitial value) initial,
    required TResult Function(_AccountDetailStateLoaded value) loaded,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AccountDetailStateInitial value)? initial,
    TResult? Function(_AccountDetailStateLoaded value)? loaded,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AccountDetailStateInitial value)? initial,
    TResult Function(_AccountDetailStateLoaded value)? loaded,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _AccountDetailStateLoaded implements AccountDetailState {
  const factory _AccountDetailStateLoaded(
      {required final List<Account> accounts,
      required final int selectedAccountIndex,
      final bool isMultiAccountEnabled}) = _$AccountDetailStateLoadedImpl;

  List<Account> get accounts;
  int get selectedAccountIndex;
  bool get isMultiAccountEnabled;
  @JsonKey(ignore: true)
  _$$AccountDetailStateLoadedImplCopyWith<_$AccountDetailStateLoadedImpl>
      get copyWith => throw _privateConstructorUsedError;
}
