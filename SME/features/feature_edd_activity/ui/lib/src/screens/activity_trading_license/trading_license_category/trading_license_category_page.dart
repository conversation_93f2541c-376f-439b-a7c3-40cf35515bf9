import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_document_upload_ui/feature_document_upload_ui.dart';
import 'package:wio_feature_edd_activity_ui/feature_edd_activity_ui.dart';
import 'package:wio_feature_edd_activity_ui/src/screens/activity_trading_license/trading_license_category/trading_license_category_cubit.dart';
import 'package:wio_feature_edd_activity_ui/src/screens/activity_trading_license/trading_license_category/trading_license_category_state.dart';
import 'package:wio_feature_edd_activity_ui/src/screens/activity_trading_license/widget/category_list_type.dart';
import 'package:wio_feature_edd_activity_ui/src/screens/activity_trading_license/widget/category_list_view.dart';
import 'package:wio_feature_edd_activity_ui/src/screens/activity_trading_license/widget/trading_license_action_button.dart';

class Constant {
  static const realEstate = 'Real Estate';
  static const other = 'Other';
}

class TradingLicenseCategoryPage
    extends BasePage<TradingLicenseCategoryState, TradingLicenseCategoryCubit> {
  final String documentName;

  const TradingLicenseCategoryPage({
    Key? key,
    required this.documentName,
  }) : super(key: key);

  @override
  TradingLicenseCategoryCubit createBloc() =>
      DependencyProvider.get<TradingLicenseCategoryCubit>()..initialize();

  @override
  Widget buildPage(
    BuildContext context,
    TradingLicenseCategoryCubit bloc,
    TradingLicenseCategoryState state,
  ) {
    final documentUploadLocalization = DocumentUploadLocalizations.of(context);

    return Scaffold(
      backgroundColor: context.colorStyling.background1,
      appBar: TopNavigation(
        TopNavigationModel(
          state: TopNavigationState.positive,
          title:
              documentUploadLocalization.documentUploadItemTitle(documentName),
          backgroundColor: CompanyColorPointer.background1,
        ),
      ),
      body: _BaseTradingLicenseCategoryPage(
        cubit: bloc,
        documentName: documentName,
      ),
    );
  }
}

class _BaseTradingLicenseCategoryPage extends StatelessWidget {
  final TradingLicenseCategoryCubit cubit;
  final String documentName;
  const _BaseTradingLicenseCategoryPage({
    required this.cubit,
    required this.documentName,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final localizations = EddActivityLocalizations.of(context);

    return BlocBuilder<TradingLicenseCategoryCubit,
        TradingLicenseCategoryState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.fromLTRB(24, 20, 24, 28),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 36),
                child: Label(
                  model: LabelModel(
                    text: localizations.eddTradeLicenseTitle,
                    textStyle: CompanyTextStylePointer.h2medium,
                    color: CompanyColorPointer.primary3,
                  ),
                ),
              ),
              Expanded(
                child: CategoryListView(
                  data: state.categories,
                  selectionType: CategoryListType.singleSelection,
                  selectedCategories: [state.selectedCategory],
                  onCategorySelected: (category) =>
                      cubit.handleCategorySelection(category),
                ),
              ),
              const SizedBox(height: 16),
              TradingLicenseActionButton(
                buttonText: _getButtonTextBasedOnCategorySelection(
                  context,
                  state.selectedCategory,
                ),
                isLoading: state.isInProgress,
                onPressed: state.selectedCategory.isEmpty
                    ? null
                    : () => cubit.handleButtonClick(documentName),
              ),
            ],
          ),
        );
      },
    );
  }

  String _getButtonTextBasedOnCategorySelection(
    BuildContext context,
    String category,
  ) {
    final localizations = EddActivityLocalizations.of(context);
    final commonLocalizations = CommonLocalizations.of(context);

    if (category.isEmpty ||
        category == Constant.realEstate ||
        category == Constant.other) {
      return localizations.uploadDocument;
    } else {
      return commonLocalizations.commonContinue;
    }
  }
}
