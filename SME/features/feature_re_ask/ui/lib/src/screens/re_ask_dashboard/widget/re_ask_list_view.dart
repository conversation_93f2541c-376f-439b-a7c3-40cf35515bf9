import 'package:flutter/material.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_re_ask_api/domain/model/application_re_ask_model.dart';
import 'package:wio_feature_re_ask_api/domain/model/application_re_ask_status.dart';
import 'package:wio_feature_re_ask_ui/feature_re_ask_ui.dart';
import 'package:wio_feature_re_ask_ui/src/screens/re_ask_dashboard/re_ask_cubit.dart';
import 'package:wio_feature_re_ask_ui/src/screens/re_ask_dashboard/re_ask_state.dart';

class ReAskListView extends StatelessWidget {
  const ReAskListView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ReAskCubit, ReAskState>(
      builder: (context, state) => Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: ListView.separated(
          itemCount: state.reAskList.length,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          separatorBuilder: (_, __) => const SizedBox(height: 8),
          itemBuilder: (context, index) {
            final reAsk = state.reAskList[index];

            return _ReAskListItem(
              model: reAsk,
            );
          },
        ),
      ),
    );
  }
}

class _ReAskListItem extends StatelessWidget {
  final ApplicationReAskModel model;
  const _ReAskListItem({
    required this.model,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final localizations = ReAskLocalizations.of(context);
    final sectionCardModel = model.status == ApplicationReAskStatus.pending
        ? SectionCardModel.todo(
            title: model.title.trim(),
            buttonModel: ButtonModel(
              title: localizations.viewMore,
              size: ButtonSize.small,
              theme: ButtonModelTheme.sme,
            ),
          )
        : SectionCardModel.completed(
            title: model.title.trim(),
            tileModel: TileModel.icon(
              icon: CompanyIconPointer.success.toGraphicAsset(),
              iconSize: CompanyIconSize.large,
              backgroundColor: CompanyColorPointer.background3,
            ),
          );

    return SectionCard(
      model: sectionCardModel,
      onPressed: () => context.read<ReAskCubit>().navigateToNextScreen(model),
    );
  }
}
