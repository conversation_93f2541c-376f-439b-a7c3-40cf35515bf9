// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.
// @dart=2.12
// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = MessageLookup();

typedef String? MessageIfAbsent(String? messageStr, List<Object>? args);

class MessageLookup extends MessageLookupByLibrary {
  @override
  String get localeName => 'ar';

  static m0(extension) =>
      "عذرًا، نحن لا ندعم ملفات ${extension} . جرب تنسيقًا مختلفًا";

  static m1(name) => "مرحبًا ${name} ، نحتاج إلى مزيد من المعلومات منك";

  static m2(documentName) => "فشل إرسال ${documentName}";

  static m3(completed, total) => "${completed} من ${total} مكتمل";

  @override
  final Map<String, dynamic> messages =
      _notInlinedMessages(_notInlinedMessages);

  static Map<String, dynamic> _notInlinedMessages(_) => {
        'actionRequiredMessage': MessageLookupByLibrary.simpleMessage(
            'يرجى إكمال المهام التالية للمضي قدمًا في طلبك.'),
        'documentTypeNotSupportedError': m0,
        'eddReaskSubmissionSuccessful':
            MessageLookupByLibrary.simpleMessage('تم إرسال المعلومات بنجاح'),
        'fileLimitExceededError': MessageLookupByLibrary.simpleMessage(
            'لقد تجاوزت الحد الأقصى لحجم الملف وهو 10 ميغابايت. يرجى تحميل ملف أصغر.'),
        'filePickerBottomSheet':
            MessageLookupByLibrary.simpleMessage('كيف تريد التحميل؟'),
        'getSupport': MessageLookupByLibrary.simpleMessage('احصل على الدعم'),
        'mabrookTitle': MessageLookupByLibrary.simpleMessage('مبروك!'),
        'questionHint': MessageLookupByLibrary.simpleMessage('اكتب إجابتك هنا'),
        'reaskPageTitle': m1,
        'reaskSubmitFailureMessage': m2,
        'submitButtonLabel': MessageLookupByLibrary.simpleMessage('يُقدِّم'),
        'submitErrorSubMessage': MessageLookupByLibrary.simpleMessage(
            'لم نتمكن من معالجة طلبك الآن. حاول مرة اخرى.'),
        'supportingDoc':
            MessageLookupByLibrary.simpleMessage('تحميل المستندات الداعمة'),
        'totalCompleted': m3,
        'uploadFailureMessage':
            MessageLookupByLibrary.simpleMessage('فشل تحميل المستند'),
        'viewMore': MessageLookupByLibrary.simpleMessage('عرض المزيد')
      };
}
