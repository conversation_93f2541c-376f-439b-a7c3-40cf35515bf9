import 'package:freezed_annotation/freezed_annotation.dart';

part 'download_mobile_app_state.freezed.dart';

@freezed
class DownloadMobileAppState with _$DownloadMobileAppState {
  const factory DownloadMobileAppState.loaded(
    String appStoreUrl,
    String googlePlayUrl,
  ) = _DownloadMobileLoadedState;

  const factory DownloadMobileAppState.initial() = _DownloadMobileInitialState;

  const factory DownloadMobileAppState.error() = _DownloadMobileErrorState;

  const factory DownloadMobileAppState.loading() = _DownloadMobileLoadingState;
}
