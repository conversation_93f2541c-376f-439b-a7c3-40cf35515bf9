// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'download_mobile_app_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$DownloadMobileAppState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String appStoreUrl, String googlePlayUrl) loaded,
    required TResult Function() initial,
    required TResult Function() error,
    required TResult Function() loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult Function(String appStoreUrl, String googlePlayUrl)? loaded,
    TResult Function()? initial,
    TResult Function()? error,
    TResult Function()? loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String appStoreUrl, String googlePlayUrl)? loaded,
    TResult Function()? initial,
    TResult Function()? error,
    TResult Function()? loading,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DownloadMobileLoadedState value) loaded,
    required TResult Function(_DownloadMobileInitialState value) initial,
    required TResult Function(_DownloadMobileErrorState value) error,
    required TResult Function(_DownloadMobileLoadingState value) loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult Function(_DownloadMobileLoadedState value)? loaded,
    TResult Function(_DownloadMobileInitialState value)? initial,
    TResult Function(_DownloadMobileErrorState value)? error,
    TResult Function(_DownloadMobileLoadingState value)? loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DownloadMobileLoadedState value)? loaded,
    TResult Function(_DownloadMobileInitialState value)? initial,
    TResult Function(_DownloadMobileErrorState value)? error,
    TResult Function(_DownloadMobileLoadingState value)? loading,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DownloadMobileAppStateCopyWith<$Res> {
  factory $DownloadMobileAppStateCopyWith(DownloadMobileAppState value,
          $Res Function(DownloadMobileAppState) then) =
      _$DownloadMobileAppStateCopyWithImpl<$Res>;
}

/// @nodoc
class _$DownloadMobileAppStateCopyWithImpl<$Res>
    implements $DownloadMobileAppStateCopyWith<$Res> {
  _$DownloadMobileAppStateCopyWithImpl(this._value, this._then);

  final DownloadMobileAppState _value;
  // ignore: unused_field
  final $Res Function(DownloadMobileAppState) _then;
}

/// @nodoc
abstract class _$$_DownloadMobileLoadedStateCopyWith<$Res> {
  factory _$$_DownloadMobileLoadedStateCopyWith(
          _$_DownloadMobileLoadedState value,
          $Res Function(_$_DownloadMobileLoadedState) then) =
      __$$_DownloadMobileLoadedStateCopyWithImpl<$Res>;
  $Res call({String appStoreUrl, String googlePlayUrl});
}

/// @nodoc
class __$$_DownloadMobileLoadedStateCopyWithImpl<$Res>
    extends _$DownloadMobileAppStateCopyWithImpl<$Res>
    implements _$$_DownloadMobileLoadedStateCopyWith<$Res> {
  __$$_DownloadMobileLoadedStateCopyWithImpl(
      _$_DownloadMobileLoadedState _value,
      $Res Function(_$_DownloadMobileLoadedState) _then)
      : super(_value, (v) => _then(v as _$_DownloadMobileLoadedState));

  @override
  _$_DownloadMobileLoadedState get _value =>
      super._value as _$_DownloadMobileLoadedState;

  @override
  $Res call({
    Object? appStoreUrl = freezed,
    Object? googlePlayUrl = freezed,
  }) {
    return _then(_$_DownloadMobileLoadedState(
      appStoreUrl == freezed
          ? _value.appStoreUrl
          : appStoreUrl // ignore: cast_nullable_to_non_nullable
              as String,
      googlePlayUrl == freezed
          ? _value.googlePlayUrl
          : googlePlayUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_DownloadMobileLoadedState implements _DownloadMobileLoadedState {
  const _$_DownloadMobileLoadedState(this.appStoreUrl, this.googlePlayUrl);

  @override
  final String appStoreUrl;
  @override
  final String googlePlayUrl;

  @override
  String toString() {
    return 'DownloadMobileAppState.loaded(appStoreUrl: $appStoreUrl, googlePlayUrl: $googlePlayUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DownloadMobileLoadedState &&
            const DeepCollectionEquality()
                .equals(other.appStoreUrl, appStoreUrl) &&
            const DeepCollectionEquality()
                .equals(other.googlePlayUrl, googlePlayUrl));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(appStoreUrl),
      const DeepCollectionEquality().hash(googlePlayUrl));

  @JsonKey(ignore: true)
  @override
  _$$_DownloadMobileLoadedStateCopyWith<_$_DownloadMobileLoadedState>
      get copyWith => __$$_DownloadMobileLoadedStateCopyWithImpl<
          _$_DownloadMobileLoadedState>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String appStoreUrl, String googlePlayUrl) loaded,
    required TResult Function() initial,
    required TResult Function() error,
    required TResult Function() loading,
  }) {
    return loaded(appStoreUrl, googlePlayUrl);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult Function(String appStoreUrl, String googlePlayUrl)? loaded,
    TResult Function()? initial,
    TResult Function()? error,
    TResult Function()? loading,
  }) {
    return loaded?.call(appStoreUrl, googlePlayUrl);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String appStoreUrl, String googlePlayUrl)? loaded,
    TResult Function()? initial,
    TResult Function()? error,
    TResult Function()? loading,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(appStoreUrl, googlePlayUrl);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DownloadMobileLoadedState value) loaded,
    required TResult Function(_DownloadMobileInitialState value) initial,
    required TResult Function(_DownloadMobileErrorState value) error,
    required TResult Function(_DownloadMobileLoadingState value) loading,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult Function(_DownloadMobileLoadedState value)? loaded,
    TResult Function(_DownloadMobileInitialState value)? initial,
    TResult Function(_DownloadMobileErrorState value)? error,
    TResult Function(_DownloadMobileLoadingState value)? loading,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DownloadMobileLoadedState value)? loaded,
    TResult Function(_DownloadMobileInitialState value)? initial,
    TResult Function(_DownloadMobileErrorState value)? error,
    TResult Function(_DownloadMobileLoadingState value)? loading,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _DownloadMobileLoadedState implements DownloadMobileAppState {
  const factory _DownloadMobileLoadedState(
          final String appStoreUrl, final String googlePlayUrl) =
      _$_DownloadMobileLoadedState;

  String get appStoreUrl => throw _privateConstructorUsedError;
  String get googlePlayUrl => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  _$$_DownloadMobileLoadedStateCopyWith<_$_DownloadMobileLoadedState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_DownloadMobileInitialStateCopyWith<$Res> {
  factory _$$_DownloadMobileInitialStateCopyWith(
          _$_DownloadMobileInitialState value,
          $Res Function(_$_DownloadMobileInitialState) then) =
      __$$_DownloadMobileInitialStateCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_DownloadMobileInitialStateCopyWithImpl<$Res>
    extends _$DownloadMobileAppStateCopyWithImpl<$Res>
    implements _$$_DownloadMobileInitialStateCopyWith<$Res> {
  __$$_DownloadMobileInitialStateCopyWithImpl(
      _$_DownloadMobileInitialState _value,
      $Res Function(_$_DownloadMobileInitialState) _then)
      : super(_value, (v) => _then(v as _$_DownloadMobileInitialState));

  @override
  _$_DownloadMobileInitialState get _value =>
      super._value as _$_DownloadMobileInitialState;
}

/// @nodoc

class _$_DownloadMobileInitialState implements _DownloadMobileInitialState {
  const _$_DownloadMobileInitialState();

  @override
  String toString() {
    return 'DownloadMobileAppState.initial()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DownloadMobileInitialState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String appStoreUrl, String googlePlayUrl) loaded,
    required TResult Function() initial,
    required TResult Function() error,
    required TResult Function() loading,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult Function(String appStoreUrl, String googlePlayUrl)? loaded,
    TResult Function()? initial,
    TResult Function()? error,
    TResult Function()? loading,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String appStoreUrl, String googlePlayUrl)? loaded,
    TResult Function()? initial,
    TResult Function()? error,
    TResult Function()? loading,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DownloadMobileLoadedState value) loaded,
    required TResult Function(_DownloadMobileInitialState value) initial,
    required TResult Function(_DownloadMobileErrorState value) error,
    required TResult Function(_DownloadMobileLoadingState value) loading,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult Function(_DownloadMobileLoadedState value)? loaded,
    TResult Function(_DownloadMobileInitialState value)? initial,
    TResult Function(_DownloadMobileErrorState value)? error,
    TResult Function(_DownloadMobileLoadingState value)? loading,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DownloadMobileLoadedState value)? loaded,
    TResult Function(_DownloadMobileInitialState value)? initial,
    TResult Function(_DownloadMobileErrorState value)? error,
    TResult Function(_DownloadMobileLoadingState value)? loading,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _DownloadMobileInitialState implements DownloadMobileAppState {
  const factory _DownloadMobileInitialState() = _$_DownloadMobileInitialState;
}

/// @nodoc
abstract class _$$_DownloadMobileErrorStateCopyWith<$Res> {
  factory _$$_DownloadMobileErrorStateCopyWith(
          _$_DownloadMobileErrorState value,
          $Res Function(_$_DownloadMobileErrorState) then) =
      __$$_DownloadMobileErrorStateCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_DownloadMobileErrorStateCopyWithImpl<$Res>
    extends _$DownloadMobileAppStateCopyWithImpl<$Res>
    implements _$$_DownloadMobileErrorStateCopyWith<$Res> {
  __$$_DownloadMobileErrorStateCopyWithImpl(_$_DownloadMobileErrorState _value,
      $Res Function(_$_DownloadMobileErrorState) _then)
      : super(_value, (v) => _then(v as _$_DownloadMobileErrorState));

  @override
  _$_DownloadMobileErrorState get _value =>
      super._value as _$_DownloadMobileErrorState;
}

/// @nodoc

class _$_DownloadMobileErrorState implements _DownloadMobileErrorState {
  const _$_DownloadMobileErrorState();

  @override
  String toString() {
    return 'DownloadMobileAppState.error()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DownloadMobileErrorState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String appStoreUrl, String googlePlayUrl) loaded,
    required TResult Function() initial,
    required TResult Function() error,
    required TResult Function() loading,
  }) {
    return error();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult Function(String appStoreUrl, String googlePlayUrl)? loaded,
    TResult Function()? initial,
    TResult Function()? error,
    TResult Function()? loading,
  }) {
    return error?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String appStoreUrl, String googlePlayUrl)? loaded,
    TResult Function()? initial,
    TResult Function()? error,
    TResult Function()? loading,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DownloadMobileLoadedState value) loaded,
    required TResult Function(_DownloadMobileInitialState value) initial,
    required TResult Function(_DownloadMobileErrorState value) error,
    required TResult Function(_DownloadMobileLoadingState value) loading,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult Function(_DownloadMobileLoadedState value)? loaded,
    TResult Function(_DownloadMobileInitialState value)? initial,
    TResult Function(_DownloadMobileErrorState value)? error,
    TResult Function(_DownloadMobileLoadingState value)? loading,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DownloadMobileLoadedState value)? loaded,
    TResult Function(_DownloadMobileInitialState value)? initial,
    TResult Function(_DownloadMobileErrorState value)? error,
    TResult Function(_DownloadMobileLoadingState value)? loading,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _DownloadMobileErrorState implements DownloadMobileAppState {
  const factory _DownloadMobileErrorState() = _$_DownloadMobileErrorState;
}

/// @nodoc
abstract class _$$_DownloadMobileLoadingStateCopyWith<$Res> {
  factory _$$_DownloadMobileLoadingStateCopyWith(
          _$_DownloadMobileLoadingState value,
          $Res Function(_$_DownloadMobileLoadingState) then) =
      __$$_DownloadMobileLoadingStateCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_DownloadMobileLoadingStateCopyWithImpl<$Res>
    extends _$DownloadMobileAppStateCopyWithImpl<$Res>
    implements _$$_DownloadMobileLoadingStateCopyWith<$Res> {
  __$$_DownloadMobileLoadingStateCopyWithImpl(
      _$_DownloadMobileLoadingState _value,
      $Res Function(_$_DownloadMobileLoadingState) _then)
      : super(_value, (v) => _then(v as _$_DownloadMobileLoadingState));

  @override
  _$_DownloadMobileLoadingState get _value =>
      super._value as _$_DownloadMobileLoadingState;
}

/// @nodoc

class _$_DownloadMobileLoadingState implements _DownloadMobileLoadingState {
  const _$_DownloadMobileLoadingState();

  @override
  String toString() {
    return 'DownloadMobileAppState.loading()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DownloadMobileLoadingState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String appStoreUrl, String googlePlayUrl) loaded,
    required TResult Function() initial,
    required TResult Function() error,
    required TResult Function() loading,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult Function(String appStoreUrl, String googlePlayUrl)? loaded,
    TResult Function()? initial,
    TResult Function()? error,
    TResult Function()? loading,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String appStoreUrl, String googlePlayUrl)? loaded,
    TResult Function()? initial,
    TResult Function()? error,
    TResult Function()? loading,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DownloadMobileLoadedState value) loaded,
    required TResult Function(_DownloadMobileInitialState value) initial,
    required TResult Function(_DownloadMobileErrorState value) error,
    required TResult Function(_DownloadMobileLoadingState value) loading,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult Function(_DownloadMobileLoadedState value)? loaded,
    TResult Function(_DownloadMobileInitialState value)? initial,
    TResult Function(_DownloadMobileErrorState value)? error,
    TResult Function(_DownloadMobileLoadingState value)? loading,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DownloadMobileLoadedState value)? loaded,
    TResult Function(_DownloadMobileInitialState value)? initial,
    TResult Function(_DownloadMobileErrorState value)? error,
    TResult Function(_DownloadMobileLoadingState value)? loading,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _DownloadMobileLoadingState implements DownloadMobileAppState {
  const factory _DownloadMobileLoadingState() = _$_DownloadMobileLoadingState;
}
