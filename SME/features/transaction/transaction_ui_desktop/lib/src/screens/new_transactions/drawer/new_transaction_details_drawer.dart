import 'package:di/di.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sme_feature_transactions_api/feature_transactions_api.dart';
import 'package:sme_feature_transactions_ui/wio_feature_transactions_ui.dart'
    as new_txn_ui;
import 'package:transaction_ui_desktop/src/screens/new_transactions/widgets/details_widget/common_transaction_details.dart';
import 'package:transaction_ui_desktop/src/screens/new_transactions/widgets/details_widget/transaction_details_payment_tracking_body.dart';
import 'package:transaction_ui_desktop/transaction_ui_desktop.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';

class NewTransactionDetailsDrawer extends StatelessWidget {
  final TransactionDetailsNavigationConfig detailsNavigationConfig;

  const NewTransactionDetailsDrawer({
    required this.detailsNavigationConfig,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider<new_txn_ui.TransactionDetailsCubit>(
      create: (_) =>
          DependencyProvider.get<new_txn_ui.TransactionDetailsCubit>()
            ..initialize(detailsNavigationConfig),
      child: const TransactionDetailsUiMapperProvider(
        child: _DetailsContent(),
      ),
    );
  }
}

class _DetailsContent extends StatelessWidget {
  const _DetailsContent();

  @override
  Widget build(BuildContext context) {
    final state = context.watch<new_txn_ui.TransactionDetailsCubit>().state;

    return state.maybeMap(
      orElse: () => const Center(
        child: LazyLoadSpinner(),
      ),
      idle: (it) => LegacyMapperProvider(
        child: _LoadedContent(
          transactionDetail: it.transaction,
        ),
      ),
      // TODO(shobhit): Ask designer for an error view.
      failed: (it) => const SizedBox.shrink(),
    );
  }
}

class _LoadedContent extends StatelessWidget {
  final Transaction transactionDetail;

  const _LoadedContent({
    required this.transactionDetail,
  });

  @override
  Widget build(BuildContext context) {
    final shouldShowTracking = _shouldShowTracking(
      transactionDetail.transactionDetails,
    );

    return shouldShowTracking
        ? TransactionDetailsWithPaymentTracking(
            transaction: transactionDetail,
          )
        : CommonTransactionDetails(
            transaction: transactionDetail,
          );
  }

  bool _shouldShowTracking(TransactionDetails? transactionDetails) {
    if (transactionDetails == null) {
      return false;
    }

    if (transactionDetails is InternationalTransactionDetails) {
      return transactionDetails.isPaymentTrackingAvailable;
    }

    if (transactionDetails is LocalTransactionDetails) {
      return transactionDetails.isPaymentTrackingAvailable;
    }

    return false;
  }
}
