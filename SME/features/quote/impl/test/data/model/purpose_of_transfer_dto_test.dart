import 'dart:convert';

import 'package:feature_quote_impl/src/data/graphql/model/transfer_purpose_dto.dart';
import 'package:test/test.dart';
import 'package:sme_core_utils/utils.dart';

void main() {
  test('validate deserialization of TransferPurposeDto', () {
    // arrange
    final code = randomString();
    final description = randomString();
    final isIpiAllowed = randomBool();

    final jsonString = '''
    {
      "Code": "$code",
      "Description": "$description",
      "IpiAllowed": $isIpiAllowed
    }
    ''';

    // act
    final json = jsonDecode(jsonString);
    final result = TransferPurposeDto.fromJson(json);

    // assert
    expect(result.code, code);
    expect(result.description, description);
    expect(result.isIpiAllowed, isIpiAllowed);
  });
}
