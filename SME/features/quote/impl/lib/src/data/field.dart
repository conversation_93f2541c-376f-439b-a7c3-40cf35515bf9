import 'package:feature_quote_impl/src/data/group.dart';
import 'package:json_annotation/json_annotation.dart';

part 'field.g.dart';

@JsonSerializable()
class Field {
  @JsonKey(name: 'Name')
  final String name;
  @JsonKey(name: 'Group', defaultValue: [])
  final List<Group> groups;

  Field({required this.name, required this.groups});

  factory Field.fromJson(Map<String, dynamic> json) => _$Field<PERSON>rom<PERSON>son(json);

  Map<String, dynamic> toJson() => _$FieldTo<PERSON>son(this);
}
