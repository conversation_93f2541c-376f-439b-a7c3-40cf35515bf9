import 'package:feature_quote_api/feature_quote_api.dart';
import 'package:feature_quote_impl/src/data/quote_data_source.dart';
import 'package:feature_quote_impl/src/data/quote_mapper.dart';
import 'package:feature_quote_impl/src/data/quote_repository_mock.dart';

class QuoteRepositoryImpl implements QuoteRepository {
  final QuoteDataSource _dataSource;
  final QuoteMapper _mapper;

  QuoteRepositoryImpl({
    required QuoteDataSource dataSource,
    required QuoteMapper mapper,
  })  : _dataSource = dataSource,
        _mapper = mapper;

  @override
  Future<List<TransferPurpose>> fetchTransferPurposes() {
    return _dataSource
        .fetchPaymentPurposes()
        .then((items) => items.map(_mapper.mapToTransferPurpose).toList());
  }

  @override
  Future<TransferDetails> getTransactionDetails(String accountId) {
    return _dataSource
        .fetchTransactionDetails(accountId)
        .then(_mapper.mapToTransferDetails);
  }

  @override
  Future<BankDetails> getBankDetails(String iban) {
    return _dataSource.fetchBankDetails(iban).then(_mapper.mapToBankDetails);
  }

  @override
  Future<Quote> getTemporaryQuote(TemporaryQuoteParameters parameters) {
    final requestDto = _mapper.mapToTemporaryQuoteRequestDto(parameters);
    return _dataSource.getTemporaryQuote(requestDto).then(_mapper.mapToQuote);
  }

  @override
  Future<Quote> getQuote(QuoteParameters parameters) {
    final requestDto = _mapper.mapToQuoteRequestDto(parameters);
    return _dataSource.getQuote(requestDto).then(_mapper.mapToQuote);
  }

  @override
  Future<List<TransferPurpose>> fetchIPTransferPurposes(bool business) async {
    return business
        ? QuoteRepositoryMock.businessPurposes
        : QuoteRepositoryMock.personPurposes;
  }
}
