name: wio_feature_utap_ui_desktop
description: UTap feature UI
publish_to: none
version: 0.0.1
environment:
  flutter: 3.27.3
  sdk: '>=3.6.0 <4.0.0'
dependencies:
  collection: 1.19.0
  di:
    path: ../../../../core/di
  flutter:
    sdk: flutter
  flutter_bloc: 9.0.0
  flutter_html: 3.0.0-beta.2
  freezed_annotation: 2.4.4
  intl: 0.19.0
  logging_api:
    path: ../../../../core/logging/api
  sme_core_ui_desktop:
    path: ../../../core/ui_desktop
  sme_core_utils:
    path: ../../../core/utils
  ui:
    path: ../../../../core/ui
  ui_kit_legacy_core:
    path: ../../../../ui_kit_legacy/ui_kit_core
  ui_kit_legacy_sme_desktop:
    path: ../../../../ui_kit_legacy/ui_kit_sme_desktop
  url_launcher: 6.3.1
  wio_app_core_api:
    path: ../../../../core/app_core/api
  wio_core_navigation_api:
    path: ../../../../core/navigation/api
  wio_core_navigation_ui:
    path: ../../../../core/navigation/ui
  wio_feature_settings_api:
    path: ../../settings/api
  wio_feature_utap_api:
    path: ../api
  wio_sme_status_api:
    path: ../../../common/feature_status/api
dev_dependencies:
  bloc_test: 10.0.0
  build_runner: 2.4.14
  core_lints:
    path: ../../../../tooling/core_lints
  fake_async: 1.3.1
  flutter_test:
    sdk: flutter
  freezed: 2.5.7
  mocktail: 1.0.4
  test: 1.25.8
  tests:
    path: ../../../../core/tests/impl
  tests_ui:
    path: ../../../../core/tests/ui
flutter:
  assets:
    - assets/images/
  uses-material-design: true
