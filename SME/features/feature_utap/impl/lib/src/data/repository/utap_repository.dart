import 'package:wio_feature_utap_api/utap_api.dart';
import 'package:wio_feature_utap_impl/src/data/mapper/utap_mapper.dart';
import 'package:wio_feature_utap_impl/src/data/service/utap_cache_data_source.dart';
import 'package:wio_feature_utap_impl/src/data/service/utap_service.dart';

class UTapRepositoryImpl extends UTapRepository {
  final UTapService _service;
  final UTapDataMapper _mapper;

  final CachedUTapDataSource _cachedUTapDataSource;

  UTapRepositoryImpl({
    required UTapService service,
    required UTapDataMapper mapper,
    required CachedUTapDataSource cachedUTapDataSource,
  })  : _service = service,
        _cachedUTapDataSource = cachedUTapDataSource,
        _mapper = mapper;

  @override
  Future<bool> uTapStatus() async {
    try {
      final result = await _service.fetchUTapStatus();
      final isOnBoarded =
          _mapper.mapGenericResponseOnboardCustomerResponseDtoToModel(result);

      return isOnBoarded;
    } on Object catch (error, stack) {
      final exception = _mapper.mapException(error);

      // If the exception indicates the user is not onboarded,
      if (exception is CustomerNotOnBoardedException) {
        return true;
      }
      Error.throwWithStackTrace(exception, stack);
    }
  }

  @override
  Future<UTapDetail> fetchInformation() async {
    try {
      final response = await _service.fetchUTapInfo();

      return _mapper.mapUTapDtoToModel(response);
    } on Exception catch (error, stack) {
      Error.throwWithStackTrace(error, stack);
    }
  }

  @override
  Future<void> downloadFile({
    required String url,
    required String savePath,
  }) async {
    try {
      await _service.downloadFile(
        url: url,
        savePath: savePath,
      );
    } on Exception catch (error, stack) {
      Error.throwWithStackTrace(error, stack);
    }
  }

  @override
  Future<void> onBoarding() async {
    try {
      await _service.onBoardingUTap();
    } on Exception catch (error, stack) {
      final exception = _mapper.mapException(error);
      Error.throwWithStackTrace(exception, stack);
    }
  }

  @override
  Future<void> clear() => _cachedUTapDataSource.clear();

  @override
  Future<String> fetchPostOnBoardingMessage() async {
    final response = await _service.fetchPostOnBoardingMessage();
    return response.fields?.first.about?.replaceAll('\n', '<br>') ?? '';
  }
}
