import 'package:sme_rest_api/models/utap_service.swagger.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_utap_impl/src/data/model/utap_detail_model.dart';
import 'package:wio_feature_utap_impl/src/data/model/utap_message_model.dart';

class _Constant {
  static const apiPath = 'sme/content/unsecure/content-data-locale/';
  static const fetchUTapStatus = 'utap/v1/status';
  static const onBoardUTap = 'utap/v1/onboard';
}

abstract class UTapService {
  /// fetch the UTapStatus
  Future<GenericResponseOnboardCustomerResponseDto> fetchUTapStatus();

  Future<GenericResponseOnboardCustomerResponseDto> onBoardingUTap();

  Future<UTapDetailModel> fetchUTapInfo();

  Future<void> downloadFile({
    required String url,
    required String savePath,
  });

  Future<UTapMessageModel> fetchPostOnBoardingMessage();
}

class UTapServiceImpl extends RestApiService implements UTapService {
  final IRestApiClient _restApiClient;
  final ContentfulProvider _contentfulProvider;

  UTapServiceImpl({
    required IRestApiClient restApiClient,
    required ContentfulProvider contentfulProvider,
  })  : _restApiClient = restApiClient,
        _contentfulProvider = contentfulProvider;

  @override
  Future<GenericResponseOnboardCustomerResponseDto> fetchUTapStatus() {
    return execute(
      _restApiClient.execute<Map<String, dynamic>>(
        RestApiRequest(
          '/${_Constant.fetchUTapStatus}',
          method: HttpRequestMethod.get,
        ),
      ),
      (json) => GenericResponseOnboardCustomerResponseDto.fromJson(
        json as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<UTapDetailModel> fetchUTapInfo() {
    return execute(
      _restApiClient.execute<Map<String, dynamic>>(
        RestApiRequest(
          '/${_Constant.apiPath}${_contentfulProvider.spaceName}/utapmodel',
          method: HttpRequestMethod.get,
        ),
      ),
      (json) {
        return UTapDetailModel.fromJson(json as Map<String, dynamic>);
      },
    );
  }

  @override
  Future<void> downloadFile({required String url, required String savePath}) {
    return execute(
      _restApiClient.execute(
        RestApiRequest(
          url,
          method: HttpRequestMethod.download,
          savePath: savePath,
        ),
      ),
      (_) {},
    );
  }

  @override
  Future<GenericResponseOnboardCustomerResponseDto> onBoardingUTap() {
    return execute(
      _restApiClient.execute<Map<String, dynamic>>(
        RestApiRequest(
          '/${_Constant.onBoardUTap}',
          method: HttpRequestMethod.post,
        ),
      ),
      (json) => GenericResponseOnboardCustomerResponseDto.fromJson(
        json as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<UTapMessageModel> fetchPostOnBoardingMessage() {
    return execute(
      _restApiClient.execute<Map<String, dynamic>>(
        RestApiRequest(
          '/${_Constant.apiPath}${_contentfulProvider.spaceName}/utapPostOnboardingMessage',
          method: HttpRequestMethod.get,
        ),
      ),
      (json) {
        return UTapMessageModel.fromJson(json as Map<String, dynamic>);
      },
    );
  }
}
