import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_delivery_api/domain/model/chequebook/chequebook.dart';

part 'chequebook_request.freezed.dart';

@freezed
class ChequebookRequest with _$ChequebookRequest {
  const ChequebookRequest._();

  const factory ChequebookRequest({
    required String referenceNumber,
    required DateTime createdAt,
    required ChequebookRequestStatus status,
    required String statusDescription,
    required List<ChequebookRequestFee> fees,
  }) = _ChequebookRequest;
}
