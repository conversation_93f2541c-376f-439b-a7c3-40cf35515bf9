import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:ui/page/base_page.dart';
import 'package:wio_feature_delivery_ui/src/screens/optional_delivery/optional_delivery_cubit.dart';
import 'package:wio_feature_delivery_ui/src/screens/optional_delivery/optional_delivery_state.dart';

class OptionalDeliveryPage
    extends BasePage<OptionalDeliveryState, OptionalDeliveryCubit> {
  const OptionalDeliveryPage({Key? key}) : super(key: key);

  @override
  OptionalDeliveryCubit createBloc() =>
      DependencyProvider.get<OptionalDeliveryCubit>()..initialize();

  @override
  Widget buildPage(
    BuildContext context,
    OptionalDeliveryCubit bloc,
    OptionalDeliveryState state,
  ) {
    return Scaffold(
      body: Center(
        child: state.when(
          loaded: (title) => Text(title),
          empty: () => const CircularProgressIndicator(),
        ),
      ),
    );
  }
}
