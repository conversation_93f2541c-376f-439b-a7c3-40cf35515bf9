part of 'chequebook_requests_section_cubit.dart';

@freezed
sealed class ChequebookRequestsSectionState
    with _$ChequebookRequestsSectionState {
  const factory ChequebookRequestsSectionState.loading() =
      _ChequebookRequestsSectionStateLoading;

  const factory ChequebookRequestsSectionState.loaded(
    List<ChequebookRequest> requests,
  ) = _ChequebookRequestsSectionStateLoaded;

  const factory ChequebookRequestsSectionState.error() =
      _ChequebookRequestsSectionStateError;
}
