part of 'chequebook_requests_section.dart';

final class _LoadingView extends StatelessWidget {
  const _LoadingView();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: _hPadding),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              LoadingContainer(
                child: SizedB<PERSON>(width: 163.0, height: 24),
              ),
              LoadingContainer(
                child: SizedB<PERSON>(width: 50, height: 20),
              ),
            ],
          ),
        ),
        const Space.vertical(16),
        ConstrainedBox(
          constraints: const BoxConstraints(maxHeight: _requestsHeight),
          child: ListView.separated(
            itemCount: 3,
            padding: const EdgeInsets.symmetric(horizontal: _hPadding),
            scrollDirection: Axis.horizontal,
            separatorBuilder: (_, __) =>
                const Space.horizontal(_separatorWidth),
            itemBuilder: (_, __) => const LoadingContainer(
              child: <PERSON><PERSON><PERSON><PERSON>(width: 327.0, height: 94.0),
            ),
          ),
        ),
      ],
    );
  }
}
