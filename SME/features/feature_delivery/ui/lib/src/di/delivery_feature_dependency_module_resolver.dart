import 'package:account_feature_api/account_feature_api.dart';
import 'package:di/di.dart';
import 'package:feature_card_api/card.dart';
import 'package:feature_login_api/feature_login_api.dart';
import 'package:flutter/cupertino.dart';
import 'package:logging_api/logging.dart';
import 'package:ui/cubit/core/error_message_provider.dart';
import 'package:ui/l10n/common_localizations.g.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_common_feature_customer_address_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_annual_kyc_api/annual_kyc_api.dart';
import 'package:wio_feature_clipboard_manager_api/wio_feature_clipboard_manager_api.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_delivery_api/delivery_api.dart';
import 'package:wio_feature_delivery_ui/feature_delivery_ui.dart';
import 'package:wio_feature_delivery_ui/src/analytics/card_delivery_analytics.dart';
import 'package:wio_feature_delivery_ui/src/navigation/delivery_router.dart';
import 'package:wio_feature_delivery_ui/src/screens/chequebook/chequebook_dashboard/cubit/chequebook_dashboard_cubit.dart';
import 'package:wio_feature_delivery_ui/src/screens/chequebook/chequebook_requests/chequebook_requests_cubit.dart';
import 'package:wio_feature_delivery_ui/src/screens/chequebook/order_chequebook/order_review/cubit/order_review_cubit.dart';
import 'package:wio_feature_delivery_ui/src/screens/chequebook/order_chequebook/order_review/widgets/signature_section/cubit/signature_section_cubit.dart';
import 'package:wio_feature_delivery_ui/src/screens/chequebook/widgets/chequebook_requests_section/chequebook_requests_section_cubit.dart';
import 'package:wio_feature_delivery_ui/src/screens/delivery_overview/delivery_overview_cubit.dart';
import 'package:wio_feature_delivery_ui/src/screens/optional_delivery/optional_delivery_cubit.dart';
import 'package:wio_feature_delivery_ui/src/screens/reschedule_delivery/card_delivery_reschedule_cubit.dart';
import 'package:wio_feature_document_upload_api/interactor/document_upload_interactor.dart';
import 'package:wio_feature_document_upload_ui/feature_document_upload_ui.dart';
import 'package:wio_sme_error_handler_api/error_handler_api.dart';

class DeliveryFeatureDependencyModuleResolver {
  static void register() {
    DependencyProvider.registerLazySingleton(() => DeliveryRouter());

    DependencyProvider.registerLazySingleton<NavigationRouter>(
      () => DependencyProvider.get<DeliveryRouter>(),
      instanceName: DeliveryFeatureNavigationConfig.name,
    );
    DependencyProvider.registerLazySingleton<CardDeliveryAnalytics>(
      () => CardDeliveryAnalyticsImpl(
        analyticsAbstractTrackerFactory:
            DependencyProvider.get<AnalyticsAbstractTrackerFactory>(),
      ),
    );
    DependencyProvider.registerFactory<OptionalDeliveryCubit>(
      () => OptionalDeliveryCubit(),
    );
    _registerLocalization();
    _rescheduleDelivery();
    _registerDeliveryOverview();
    _registerChequebook();
  }

  static void _registerLocalization() {
    DependencyProvider.registerLazySingleton<DeliveryLocalizations>(
      () => DeliveryLocalizations.of(
        DependencyProvider.get<BuildContext>(),
      ),
    );
  }

  static void _registerDeliveryOverview() {
    DependencyProvider.registerFactory<DeliveryOverviewCubit>(
      () => DeliveryOverviewCubit(
        cardDeliveryInteractor:
            DependencyProvider.get<CardDeliveryInteractor>(),
        cardInteractor: DependencyProvider.get<CardInteractor>(),
        addressNavigationFactory:
            DependencyProvider.get<CustomerAddressFeatureNavigationFactory>(),
        logger: DependencyProvider.get<Logger>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
        cardDeliveryAnalytics: DependencyProvider.get<CardDeliveryAnalytics>(),
        documentUploadLocalizations:
            DependencyProvider.get<DocumentUploadLocalizations>(),
        deliveryLocalizations: DependencyProvider.get<DeliveryLocalizations>(),
      ),
    );
  }

  static void _rescheduleDelivery() {
    DependencyProvider.registerFactory<CardDeliveryRescheduleCubit>(
      () => CardDeliveryRescheduleCubit(
        clipboardManager: DependencyProvider.get<ClipboardManager>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        logger: DependencyProvider.get<Logger>(),
        localizations: DependencyProvider.get<DeliveryLocalizations>(),
        cardDeliveryAnalytics: DependencyProvider.get<CardDeliveryAnalytics>(),
      ),
    );
  }

  static void _registerChequebook() {
    DependencyProvider.registerFactory<ChequebookDashboardCubit>(
      () => ChequebookDashboardCubit(
        chequebookInteractor: DependencyProvider.get<ChequebookInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        loginInteractor: DependencyProvider.get<LoginInteractor>(),
        kycInteractor: DependencyProvider.get<AnnualKycInteractor>(),
        errorHandler: DependencyProvider.get<ErrorHandlerTool>(),
        logger: DependencyProvider.get<Logger>(),
        deliveryLocalizations: DependencyProvider.get<DeliveryLocalizations>(),
      ),
    );

    DependencyProvider.registerFactory<OrderReviewCubit>(
      () => OrderReviewCubit(
        chequebookInteractor: DependencyProvider.get<ChequebookInteractor>(),
        accountInteractor: DependencyProvider.get<AccountInteractor>(),
        loginInteractor: DependencyProvider.get<LoginInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        errorMessageProvider: DependencyProvider.get<ErrorMessageProvider>(),
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
        localizations: DependencyProvider.get<DeliveryLocalizations>(),
        logger: DependencyProvider.get<Logger>(),
        cardDeliveryAnalytics: DependencyProvider.get<CardDeliveryAnalytics>(),
      ),
    );

    DependencyProvider.registerFactory<SignatureSectionCubit>(
      () => SignatureSectionCubit(
        loginInteractor: DependencyProvider.get<LoginInteractor>(),
        kycInteractor: DependencyProvider.get<AnnualKycInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        documentUploadInteractor:
            DependencyProvider.get<DocumentUploadInteractor>(),
        logger: DependencyProvider.get<Logger>(),
      ),
    );

    DependencyProvider.registerFactory<ChequebookRequestsSectionCubit>(
      () => ChequebookRequestsSectionCubit(
        chequebookInteractor: DependencyProvider.get<ChequebookInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        logger: DependencyProvider.get<Logger>(),
      ),
    );

    DependencyProvider.registerFactory<ChequebookRequestsCubit>(
      () => ChequebookRequestsCubit(
        chequebookInteractor: DependencyProvider.get<ChequebookInteractor>(),
        logger: DependencyProvider.get<Logger>(),
        errorHandlerTool: DependencyProvider.get<ErrorHandlerTool>(),
      ),
    );
  }
}
