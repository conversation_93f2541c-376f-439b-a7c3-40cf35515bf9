// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'consent_properties_payload.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ConsentPropertiesPayload {
  String get message => throw _privateConstructorUsedError;
  String get clientId => throw _privateConstructorUsedError;
  String get consentId => throw _privateConstructorUsedError;
  String get psuId => throw _privateConstructorUsedError;
  String get tokenId => throw _privateConstructorUsedError;

  /// Create a copy of ConsentPropertiesPayload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConsentPropertiesPayloadCopyWith<ConsentPropertiesPayload> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConsentPropertiesPayloadCopyWith<$Res> {
  factory $ConsentPropertiesPayloadCopyWith(ConsentPropertiesPayload value,
          $Res Function(ConsentPropertiesPayload) then) =
      _$ConsentPropertiesPayloadCopyWithImpl<$Res, ConsentPropertiesPayload>;
  @useResult
  $Res call(
      {String message,
      String clientId,
      String consentId,
      String psuId,
      String tokenId});
}

/// @nodoc
class _$ConsentPropertiesPayloadCopyWithImpl<$Res,
        $Val extends ConsentPropertiesPayload>
    implements $ConsentPropertiesPayloadCopyWith<$Res> {
  _$ConsentPropertiesPayloadCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConsentPropertiesPayload
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? clientId = null,
    Object? consentId = null,
    Object? psuId = null,
    Object? tokenId = null,
  }) {
    return _then(_value.copyWith(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      clientId: null == clientId
          ? _value.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String,
      consentId: null == consentId
          ? _value.consentId
          : consentId // ignore: cast_nullable_to_non_nullable
              as String,
      psuId: null == psuId
          ? _value.psuId
          : psuId // ignore: cast_nullable_to_non_nullable
              as String,
      tokenId: null == tokenId
          ? _value.tokenId
          : tokenId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ConsentPropertiesPayloadImplCopyWith<$Res>
    implements $ConsentPropertiesPayloadCopyWith<$Res> {
  factory _$$ConsentPropertiesPayloadImplCopyWith(
          _$ConsentPropertiesPayloadImpl value,
          $Res Function(_$ConsentPropertiesPayloadImpl) then) =
      __$$ConsentPropertiesPayloadImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String clientId,
      String consentId,
      String psuId,
      String tokenId});
}

/// @nodoc
class __$$ConsentPropertiesPayloadImplCopyWithImpl<$Res>
    extends _$ConsentPropertiesPayloadCopyWithImpl<$Res,
        _$ConsentPropertiesPayloadImpl>
    implements _$$ConsentPropertiesPayloadImplCopyWith<$Res> {
  __$$ConsentPropertiesPayloadImplCopyWithImpl(
      _$ConsentPropertiesPayloadImpl _value,
      $Res Function(_$ConsentPropertiesPayloadImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConsentPropertiesPayload
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? clientId = null,
    Object? consentId = null,
    Object? psuId = null,
    Object? tokenId = null,
  }) {
    return _then(_$ConsentPropertiesPayloadImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      clientId: null == clientId
          ? _value.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String,
      consentId: null == consentId
          ? _value.consentId
          : consentId // ignore: cast_nullable_to_non_nullable
              as String,
      psuId: null == psuId
          ? _value.psuId
          : psuId // ignore: cast_nullable_to_non_nullable
              as String,
      tokenId: null == tokenId
          ? _value.tokenId
          : tokenId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ConsentPropertiesPayloadImpl extends _ConsentPropertiesPayload {
  const _$ConsentPropertiesPayloadImpl(
      {this.message = '',
      this.clientId = '',
      this.consentId = '',
      this.psuId = '',
      this.tokenId = ''})
      : super._();

  @override
  @JsonKey()
  final String message;
  @override
  @JsonKey()
  final String clientId;
  @override
  @JsonKey()
  final String consentId;
  @override
  @JsonKey()
  final String psuId;
  @override
  @JsonKey()
  final String tokenId;

  @override
  String toString() {
    return 'ConsentPropertiesPayload(message: $message, clientId: $clientId, consentId: $consentId, psuId: $psuId, tokenId: $tokenId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConsentPropertiesPayloadImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.clientId, clientId) ||
                other.clientId == clientId) &&
            (identical(other.consentId, consentId) ||
                other.consentId == consentId) &&
            (identical(other.psuId, psuId) || other.psuId == psuId) &&
            (identical(other.tokenId, tokenId) || other.tokenId == tokenId));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, message, clientId, consentId, psuId, tokenId);

  /// Create a copy of ConsentPropertiesPayload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConsentPropertiesPayloadImplCopyWith<_$ConsentPropertiesPayloadImpl>
      get copyWith => __$$ConsentPropertiesPayloadImplCopyWithImpl<
          _$ConsentPropertiesPayloadImpl>(this, _$identity);
}

abstract class _ConsentPropertiesPayload extends ConsentPropertiesPayload {
  const factory _ConsentPropertiesPayload(
      {final String message,
      final String clientId,
      final String consentId,
      final String psuId,
      final String tokenId}) = _$ConsentPropertiesPayloadImpl;
  const _ConsentPropertiesPayload._() : super._();

  @override
  String get message;
  @override
  String get clientId;
  @override
  String get consentId;
  @override
  String get psuId;
  @override
  String get tokenId;

  /// Create a copy of ConsentPropertiesPayload
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConsentPropertiesPayloadImplCopyWith<_$ConsentPropertiesPayloadImpl>
      get copyWith => throw _privateConstructorUsedError;
}
