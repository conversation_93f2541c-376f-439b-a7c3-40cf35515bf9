import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sme_core_ui_desktop/index.dart';
import 'package:sme_core_utils/utils.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_common_feature_consent_authorization_api/consent_authorization_api.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/locale/consent_authorization_ui_consent_desktop_localizations.g.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/dialog/review_scopes_dialog.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/keys/consent_authorization_keys.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/analytics/consent_analytics.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/analytics/consent_pages.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/consent_authorization/consent_authorization_cubit.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/widgets.dart'
    as cw;

class ConsentAuthorizationTitle extends StatelessWidget {
  final TextStyle titleTextStyle;
  final String companyName;

  const ConsentAuthorizationTitle({
    required this.titleTextStyle,
    required this.companyName,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final companyTheme = CompanyThemeProvider.of(context);
    final localizations = ConsentAuthorizationUILocalizations.of(context);

    return Center(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const DesktopPictogram(
            DesktopPictogramModel(
              icon: CompanyPictogramPointer.metaphors_connect_left,
              size: DesktopPictogramSize.m,
              color: CompanyColorPointer.primary3,
            ),
          ),
          const SizedBox(height: 8),
          cw.WordWrap(
            text: localizations.allowCompany(companyName),
            style: titleTextStyle,
            rest: [
              cw.WordWrap(
                text: localizations.accountDetails,
                style: titleTextStyle.copyWith(
                  color: companyTheme.colorScheme.primary3,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class ConsentAuthorizationAccount extends StatelessWidget {
  final ConsentModel consent;

  final List<String> selectedConsent;
  final List<String> selectedPayout;

  final List<BaasAccount> accountInfoItems;
  final List<BaasAccount> paymentItems;

  const ConsentAuthorizationAccount({
    required this.consent,
    required this.selectedConsent,
    required this.selectedPayout,
    required this.accountInfoItems,
    required this.paymentItems,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final companyTheme = CompanyThemeProvider.of(context);
    final localizations = ConsentAuthorizationUILocalizations.of(context);
    final bloc = BlocProvider.of<ConsentAuthorizationCubit>(context);

    final validTill = consent.expirationDateTime;
    final consentItemWidgets = <AccordionListItem>[];
    final payoutItemWidgets = <AccordionListItem>[];
    final (
      isInitiallyExpandedForAccountInfoItems,
      isInitiallyExpandedForPaymentItems
    ) = getInitiallyExpandedCase(
      isAccountInfoItemsEmpty: accountInfoItems.isEmpty,
      isPaymentItemsEmpty: paymentItems.isEmpty,
      isSelectedAccountsEmpty: selectedConsent.isEmpty,
      isSelectedPayoutsEmpty: selectedPayout.isEmpty,
    );

    for (final consentItem in consent.consentItems) {
      switch (consentItem.type) {
        case ConsentItemType.accountInfo:
          consentItemWidgets.addAll(
            accountInfoItems.map(
              (acc) => AccordionListItem(
                key: ObjectKey(acc),
                title: acc.name,
                // This empty string is provided until other texts appear when
                // there is no IBAN eg: saving spaces.
                subtitle: acc.iban != null
                    ? localizations.consentIban(acc.iban ?? '')
                    : '',
                trailing: CompanySwitch(
                  CompanySwitchModel.var1(
                    isOn: selectedConsent.contains(acc.accountId),
                  ),
                  onChange: (_) => bloc.toggleAccount(acc.accountId),
                ),
              ),
            ),
          );
        case ConsentItemType.payment:
          payoutItemWidgets.addAll(
            paymentItems.map(
              (acc) => AccordionListItem(
                key: ObjectKey(acc),
                title: acc.name,
                subtitle: localizations.consentIban(acc.iban ?? ''),
                trailing: CompanySwitch(
                  CompanySwitchModel.var1(
                    isOn: selectedPayout.contains(acc.accountId),
                  ),
                  onChange: (_) => bloc.togglePayout(acc.accountId),
                ),
              ),
            ),
          );
      }
    }

    final consentWidget = Accordion(
      key: ConsentAuthorizationKeys.accountAccordionListKey,
      title: localizations.accountsDetails,
      subtitle:
          _getAccountSelectedMessage(localizations, selectedConsent.length),
      leadingIconConfiguration: const LeadingIconConfiguration(
        leadingTileIconPointer: CompanyIconPointer.profile,
        leadingIconBgColor: CompanyColorPointer.surface1,
      ),
      onExpansionChanged: (expanded) =>
          bloc.expandedConsent(expanded: expanded),
      initiallyExpanded: isInitiallyExpandedForAccountInfoItems,
      children: consentItemWidgets,
    );

    final payoutWidget = Accordion(
      key: ConsentAuthorizationKeys.payoutAccordionListKey,
      title: localizations.payoutConsent,
      subtitle:
          _getAccountSelectedMessage(localizations, selectedPayout.length),
      leadingIconConfiguration: const LeadingIconConfiguration(
        leadingTileIconPointer: CompanyIconPointer.link,
        leadingIconBgColor: CompanyColorPointer.surface1,
      ),
      onExpansionChanged: (expanded) => bloc.expandedPayout(expanded: expanded),
      initiallyExpanded: isInitiallyExpandedForPaymentItems,
      children: payoutItemWidgets,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...[
          const SizedBox(height: 12),
          if (consentWidget.children.isNotEmpty) consentWidget,
        ],
        ...[
          const SizedBox(height: 12),
          if (payoutWidget.children.isNotEmpty) payoutWidget,
        ],
        const SizedBox(height: 24),
        Text(
          localizations.consentValidity,
          style: companyTheme.textStyles.b2,
        ),
        const SizedBox(height: 12),
        ListBox.label(
          label: localizations.accessValidTill,
          suffix1: Tag(
            type: TagType.mute,
            text: validTill?.formatTo(
                  const DateTimePatternSME.MMMdyyy(),
                  Localizations.localeOf(context),
                ) ??
                '',
            size: TagSize.l,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
      ],
    );
  }

  String? _getAccountSelectedMessage(
    ConsentAuthorizationUILocalizations localizations,
    int count,
  ) {
    if (count == 0) {
      return null;
    }

    return count > 1
        ? localizations.accountsSelected(count)
        : localizations.accountSelected(count);
  }

  (bool initiallyExpandedForAccountInfo, bool initiallyExpandedForPayout)
      getInitiallyExpandedCase({
    required bool isSelectedAccountsEmpty,
    required bool isSelectedPayoutsEmpty,
    required bool isAccountInfoItemsEmpty,
    required bool isPaymentItemsEmpty,
  }) {
    final hasSelectedAccountInfoEmpty = isSelectedAccountsEmpty;
    final hasSelectedPaymentEmpty = isSelectedPayoutsEmpty;
    final hasAccountInfoItemsEmpty = isAccountInfoItemsEmpty;
    final hasPaymentItemsEmpty = isPaymentItemsEmpty;

    // Check account info items not empty and selected account is empty
    if ((!hasAccountInfoItemsEmpty && hasPaymentItemsEmpty) &&
        (hasSelectedAccountInfoEmpty && hasSelectedPaymentEmpty)) {
      return (true, false);
    }
    // Check payment items not empty and selected payment is empty
    else if ((hasAccountInfoItemsEmpty && !hasPaymentItemsEmpty) &&
        (hasSelectedAccountInfoEmpty && hasSelectedPaymentEmpty)) {
      return (false, true);
    }
    // Check selected account is empty
    else if ((!hasAccountInfoItemsEmpty && !hasPaymentItemsEmpty) &&
            (hasSelectedAccountInfoEmpty && !hasSelectedPaymentEmpty) ||
        (hasSelectedAccountInfoEmpty && hasSelectedPaymentEmpty)) {
      return (true, false);
    }
    // Check selected payout is empty
    else if ((!hasAccountInfoItemsEmpty && !hasPaymentItemsEmpty) &&
        (!hasSelectedAccountInfoEmpty && hasSelectedPaymentEmpty)) {
      return (false, true);
    } else {
      return (false, false);
    }
  }
}

class ConsentAuthorizationCancelButton extends StatelessWidget {
  const ConsentAuthorizationCancelButton({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = ConsentAuthorizationUILocalizations.of(context);
    final bloc = BlocProvider.of<ConsentAuthorizationCubit>(context);

    return CtaButton.l(
      ctaButtonVariant: CtaButtonVariant.outlineType,
      onTap: bloc.onCancelClick,
      title: localizations.cancel,
    );
  }
}

enum ConsentCase {
  accountInfoNotEmptySelectedAccountEmpty,
  paymentInfoNotEmptySelectedPaymentEmpty,
  selectedAccountInfoEmpty,
  selectedPaymentInfoEmpty,
  none,
}

class ConsentAuthorizationAllowButton extends StatelessWidget {
  const ConsentAuthorizationAllowButton({
    required this.selectedConsent,
    required this.selectedPayout,
    required this.accountInfoItems,
    required this.paymentItems,
    super.key,
  });

  final List<String> selectedConsent;
  final List<String> selectedPayout;
  final List<BaasAccount> accountInfoItems;
  final List<BaasAccount> paymentItems;

  @override
  Widget build(BuildContext context) {
    final localizations = ConsentAuthorizationUILocalizations.of(context);

    return CtaButton.l(
      ctaButtonVariant: CtaButtonVariant.primaryType,
      onTap: () => onPressedAllow(context),
      title: localizations.allow,
    );
  }

  Future<void> onPressedAllow(BuildContext context) async {
    final localizations = ConsentAuthorizationUILocalizations.of(context);
    final bloc = BlocProvider.of<ConsentAuthorizationCubit>(context);
    final notificationService = NewNotificationService();
    final analytics = ConsentAnalytics(
      screenName: ConsentPages.consentAuthorizationPage,
      analyticsAbstractTrackerFactory:
          DependencyProvider.get<AnalyticsAbstractTrackerFactory>(),
    );

    final consentCase = getConsentCase(
      isAccountInfoItemsEmpty: accountInfoItems.isEmpty,
      isPaymentItemsEmpty: paymentItems.isEmpty,
      isSelectedAccountsEmpty: selectedConsent.isEmpty,
      isSelectedPayoutsEmpty: selectedPayout.isEmpty,
    );

    switch (consentCase) {
      case ConsentCase.selectedAccountInfoEmpty ||
            ConsentCase.accountInfoNotEmptySelectedAccountEmpty:
        await showAccountInfoError(
          notificationService,
          localizations,
          analytics,
        );
        break;
      case ConsentCase.selectedPaymentInfoEmpty ||
            ConsentCase.paymentInfoNotEmptySelectedPaymentEmpty:
        await showPaymentError(
          notificationService,
          localizations,
          analytics,
        );
        break;
      case ConsentCase.none:
        await bloc.allow();
    }
  }

  ConsentCase getConsentCase({
    required bool isSelectedAccountsEmpty,
    required bool isSelectedPayoutsEmpty,
    required bool isAccountInfoItemsEmpty,
    required bool isPaymentItemsEmpty,
  }) {
    final hasSelectedAccountInfoEmpty = isSelectedAccountsEmpty;
    final hasSelectedPaymentEmpty = isSelectedPayoutsEmpty;
    final hasAccountInfoItemsEmpty = isAccountInfoItemsEmpty;
    final hasPaymentItemsEmpty = isPaymentItemsEmpty;

    // Check account info items not empty and selected account is empty
    if ((!hasAccountInfoItemsEmpty && hasPaymentItemsEmpty) &&
        (hasSelectedAccountInfoEmpty && hasSelectedPaymentEmpty)) {
      return ConsentCase.accountInfoNotEmptySelectedAccountEmpty;
    }
    // Check payment items not empty and selected payment is empty
    else if ((hasAccountInfoItemsEmpty && !hasPaymentItemsEmpty) &&
        (hasSelectedAccountInfoEmpty && hasSelectedPaymentEmpty)) {
      return ConsentCase.paymentInfoNotEmptySelectedPaymentEmpty;
    }
    // Check selected account is empty
    else if ((!hasAccountInfoItemsEmpty && !hasPaymentItemsEmpty) &&
            (hasSelectedAccountInfoEmpty && !hasSelectedPaymentEmpty) ||
        (hasSelectedAccountInfoEmpty && hasSelectedPaymentEmpty)) {
      return ConsentCase.selectedAccountInfoEmpty;
    }
    // Check selected payment is empty
    else if ((!hasAccountInfoItemsEmpty && !hasPaymentItemsEmpty) &&
        (!hasSelectedAccountInfoEmpty && hasSelectedPaymentEmpty)) {
      return ConsentCase.selectedPaymentInfoEmpty;
    }

    return ConsentCase.none;
  }

  Future<void> showAccountInfoError(
    NewNotificationService notificationService,
    ConsentAuthorizationUILocalizations localizations,
    ConsentAnalytics analytics,
  ) async {
    if (ConsentAuthorizationKeys.accountAccordionListKey.currentState != null) {
      // Scroll to account widget
      await Scrollable.ensureVisible(
        ConsentAuthorizationKeys.accountAccordionListKey.currentContext!,
      );

      analytics.scrollAccounts();

      // Expand the account accordion
      ConsentAuthorizationKeys.accountAccordionListKey.currentState?.controller
          .expand();
    }

    // Show account error message
    notificationService.showSnackbar(
      SnackbarModel.errorLowPriority(
        text: localizations.noAccountSelectedError,
      ),
    );
  }

  Future<void> showPaymentError(
    NewNotificationService notificationService,
    ConsentAuthorizationUILocalizations localizations,
    ConsentAnalytics analytics,
  ) async {
    if (ConsentAuthorizationKeys.payoutAccordionListKey.currentState != null) {
      // Scroll to payout widget
      await Scrollable.ensureVisible(
        ConsentAuthorizationKeys.payoutAccordionListKey.currentContext!,
      );

      analytics.scrollPayouts();

      // Expand the payout accordion
      ConsentAuthorizationKeys.payoutAccordionListKey.currentState?.controller
          .expand();
    }

    // Show payout error message
    notificationService.showSnackbar(
      SnackbarModel.errorLowPriority(
        text: localizations.noAccountSelectedError,
      ),
    );
  }
}

class ConsentAcknowledgmentsInfo extends StatelessWidget {
  final String companyName;
  final bool accountsItemsExist;
  final bool paymentsItemsExist;

  const ConsentAcknowledgmentsInfo({
    required this.companyName,
    required this.accountsItemsExist,
    required this.paymentsItemsExist,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final companyTheme = CompanyThemeProvider.of(context);
    final localizations = ConsentAuthorizationUILocalizations.of(context);
    final bloc = BlocProvider.of<ConsentAuthorizationCubit>(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          getAcknowledgementMessage(localizations),
          style: companyTheme.textStyles.b2.copyWith(
            color: companyTheme.colorScheme.secondary3,
          ),
        ),
        const SizedBox(height: 24),
        cw.WordWrap(
          text: localizations.managePermissions,
          style: companyTheme.textStyles.b2.copyWith(
            color: companyTheme.colorScheme.secondary3,
          ),
          rest: [
            InkWell(
              onTap: bloc.onNavigateToConsentManagement,
              child: Text(
                localizations.consentManagement,
                style: companyTheme.textStyles.b2.copyWith(
                  color: companyTheme.colorScheme.primary3,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  String getAcknowledgementMessage(
    ConsentAuthorizationUILocalizations localizations,
  ) {
    if (accountsItemsExist && !paymentsItemsExist) {
      return localizations.acknowledgementAccountDetails(companyName);
    } else if (!accountsItemsExist && paymentsItemsExist) {
      return localizations.acknowledgementInitiatePayments(companyName);
    } else {
      return localizations.acknowledgement(companyName);
    }
  }
}

class ConsentAlreadyGivenConsentWarning extends StatelessWidget {
  final void Function({bool isExpanded}) onTap;
  final String companyName;

  const ConsentAlreadyGivenConsentWarning({
    required this.onTap,
    required this.companyName,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = ConsentAuthorizationUILocalizations.of(context);

    return cw.WideItemPictogram(
      title: localizations.alreadyGivenConsent(companyName),
      subtitle: localizations.consentYouCanReview,
      pictogram: CompanyPictogramPointer.validation_alert,
      pictogramBgColor: CompanyColorPointer.surface5,
      onTap: onTap,
    );
  }
}

class ReviewWhatYouAuthorize extends StatelessWidget {
  final List<Scope> scopes;
  final bool mobileView;

  const ReviewWhatYouAuthorize({
    required this.scopes,
    required this.mobileView,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = ConsentAuthorizationUILocalizations.of(context);

    return GestureDetector(
      child: AbsorbPointer(
        child: Accordion(
          title: localizations.reviewWhatYouAreAuthorizing,
          subtitle: localizations.tapHereToSeeWhatIncluded,
          leadingIconConfiguration: const LeadingIconConfiguration(
            leadingTileIconPointer: CompanyIconPointer.safe,
            leadingIconBgColor: CompanyColorPointer.surface4,
          ),
          neverExpand: true,
        ),
      ),
      onTap: () => showDialog<void>(
        context: context,
        builder: (context) => ReviewScopesDialog(
          items: scopes,
          mobileView: mobileView,
        ),
      ),
    );
  }
}
