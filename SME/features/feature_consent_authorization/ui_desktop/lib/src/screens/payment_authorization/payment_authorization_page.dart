import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui/page/page_bloc_provider.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_common_feature_consent_authorization_api/consent_authorization_api.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/payment_authorization/payment_authorization_cubit.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/payment_authorization/payment_authorization_state.dart';

class PaymentAuthorizationPage extends StatelessWidget {
  final PaymentRedirectData model;

  const PaymentAuthorizationPage({
    required this.model,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final companyTheme = CompanyThemeProvider.of(context);

    return PageBlocProvider<PaymentAuthorizationCubit,
        PaymentAuthorizationState>(
      createBloc: () =>
          DependencyProvider.get<PaymentAuthorizationCubit>()..init(model),
      onWillPop: _onWillPop,
      child: Bloc<PERSON>uilder<PaymentAuthorizationCubit, PaymentAuthorizationState>(
        builder: (context, state) {
          return Material(
            color: companyTheme.colorScheme.background3,
            child: const Center(child: LazyLoadSpinner()),
          );
        },
      ),
    );
  }

  Future<bool> _onWillPop(BuildContext context) {
    context.read<PaymentAuthorizationCubit>().onBackClick();

    return Future<bool>.value(false);
  }
}
