import 'dart:async';

import 'package:feature_2fa_api/feature_2fa_api.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ui/ui.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:wio_common_feature_consent_authorization_api/consent_authorization_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/analytics/consent_analytics.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/payment_authorization/payment_authorization_state.dart';
import 'package:wio_sme_error_handler_api/error_handler_api.dart';

class PaymentAuthorizationCubit extends BaseCubit<PaymentAuthorizationState> {
  final IdentityInteractor _identityInteractor;
  final NavigationProvider _navigator;
  final ConsentAnalytics _analytics;
  final ErrorHandlerTool _errorHandlerTool;
  final TwoFaUIParamBridge _twoFaUIParamBridge;

  PaymentAuthorizationCubit({
    required IdentityInteractor identityInteractor,
    required NavigationProvider navigator,
    required ConsentAnalytics analytics,
    required ErrorHandlerTool errorHandlerTool,
    required TwoFaUIParamBridge twoFaUIParamBridge,
  })  : _identityInteractor = identityInteractor,
        _navigator = navigator,
        _analytics = analytics,
        _errorHandlerTool = errorHandlerTool,
        _twoFaUIParamBridge = twoFaUIParamBridge,
        super(const PaymentAuthorizationState.empty());

  Future<void> init(PaymentRedirectData paymentModel) async {
    _analytics.viewPage();

    await _preAuthorizePayment(data: paymentModel)
        .toStream()
        .withError((e) => _handleException(e))
        .complete();
  }

  Future<void> _preAuthorizePayment({
    required PaymentRedirectData data,
  }) async {
    try {
      loading(true);
      final result = await _twoFaUIParamBridge.executeWith2Fa(
        () => _identityInteractor.preAuthorizePayment(
          id: data.paymentTokenModel.id,
          authRequest: data.requestParam,
        ),
        uiParam: 'payment',
      );

      await _authorizePayment(data: data, token: result);
    } on TwoFaCancelledException catch (ex) {
      if (ex.twoFactorErrorCode == TwoFactorErrorCode.deny) {
        await _denyPayment(data: data);
      } else {
        _handleException(ex);
      }
    } on Exception catch (ex) {
      _handleException(ex);
    } finally {
      loading(false);
    }
  }

  void _handleException(Object? exception) {
    _analytics.viewErrorPage(exception);

    if (exception != null) {
      _errorHandlerTool.handleException(
        exception,
        handleGeneralException: () =>
            _navigateToCouldNotConsentErrorScreen(exception),
      );
    } else {
      _navigateToCouldNotConsentErrorScreen(exception);
    }
  }

  void _navigateToCouldNotConsentErrorScreen(Object? error) {
    _navigator.push(
      CouldNotConsentErrorScreenNavigationConfig(
        correlationId: _errorHandlerTool.getCorrelationId(error),
      ),
    );
  }

  Future<void> _authorizePayment({
    required PaymentRedirectData data,
    required Token token,
  }) async {
    _analytics.clickAuthorizePayment();

    await _identityInteractor
        .authorizePayment(
          id: data.paymentTokenModel.id,
          authRequest: data.requestParam,
          token: token,
        )
        .toStream()
        .withError(_handleException)
        .where((_) => !isClosed)
        .doOnData((url) => _navigateToRedirectUrl(redirectUrl: url))
        .complete();
  }

  Future<void> _denyPayment({
    required PaymentRedirectData data,
  }) async {
    _analytics.clickDenyPayment();

    await _identityInteractor
        .denyPayment(
          id: data.paymentTokenModel.id,
          authRequest: data.requestParam,
        )
        .toStream()
        .withError(_handleException)
        .where((_) => !isClosed)
        .doOnData((url) => _navigateToRedirectUrl(redirectUrl: url))
        .complete();
  }

  Future<void> _navigateToRedirectUrl({
    required String redirectUrl,
  }) async {
    try {
      // Extract host part or baseurl of the redirectUrl
      final host = Uri.parse(redirectUrl).host;

      if (await canLaunchUrlString(redirectUrl)) {
        _analytics.navigateToRedirectUrl(host);
        loading(true);
        await launchUrlString(
          redirectUrl,
          webOnlyWindowName: '_self',
        );
      }
    } on Object catch (e, __) {
      // handle exception and navigate to error screen
      loading(false);
      _handleException(e);
    }
  }

  void onBackClick() {
    _analytics.clickBack();
    _navigator.goBack();
  }

  @override
  String toString() => 'PaymentAuthorizationCubit{}';
}
