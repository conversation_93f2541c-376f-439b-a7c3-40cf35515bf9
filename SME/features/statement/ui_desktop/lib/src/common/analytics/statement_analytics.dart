import 'package:feature_statement_ui/src/common/analytics/statement_events.dart';
import 'package:feature_statement_ui/src/common/analytics/statement_payload.dart';
import 'package:statement_feature_api/statement_feature_api.dart';
import 'package:wio_app_core_api/index.dart';

class StatementAnalytics {
  final AnalyticsEventTracker _analytics;

  StatementAnalytics({
    required AnalyticsAbstractTrackerFactory analyticsAbstractTrackerFactory,
  }) : _analytics = analyticsAbstractTrackerFactory.get(
          screenName: StatementFeatureNavigationConfig.name,
          tracker: AnalyticsTracker.mixpanel,
        );

  void clickOnSelectedMonth({
    required String selectedMonth,
    required String selectedYear,
  }) {
    _analytics.click(
      targetType: AnalyticsTargetType.list,
      target: StatementEvents.selected_month,
      payload: StatementPayload(
        selectedYear: selectedYear,
        selectedMonth: selectedMonth,
      ),
    );
  }

  void viewGeneratedStatement() {
    _analytics.view(
      targetType: AnalyticsTargetType.drawer,
      target: StatementEvents.generated_statement,
    );
  }

  void clickDownload(DownloadType downloadType) {
    _analytics.click(
      targetType: AnalyticsTargetType.button,
      target: StatementEvents.download_generated_statement,
      payload: DownloadOptionPayload(downloadType: downloadType),
    );
  }
}
