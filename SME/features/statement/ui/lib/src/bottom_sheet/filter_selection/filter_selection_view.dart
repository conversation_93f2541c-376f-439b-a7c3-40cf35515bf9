import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_statement_ui/feature_statement_ui.dart';
import 'package:wio_feature_statement_ui/src/bottom_sheet/filter_selection/filter_selection_cubit.dart';
import 'package:wio_feature_statement_ui/src/bottom_sheet/filter_selection/filter_selection_state.dart';

typedef FilterItemBuilder<T> = Widget Function(
  BuildContext context,
  T filter,
  bool isSelected,
);

class FilterSelectionView<T> extends StatelessWidget {
  final String title;
  final FilterItemBuilder<T> itemBuilder;

  const FilterSelectionView({
    required this.title,
    required this.itemBuilder,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);

    return Padding(
      padding: EdgeInsets.only(bottom: mediaQuery.bottomPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
            child: _Header<T>(title: title),
          ),
          Flexible(child: _Content<T>(itemBuilder: itemBuilder)),
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(24, 20, 24, 16),
            child: _Footer<T>(),
          ),
        ],
      ),
    );
  }
}

/// A generic widget for filter items.
class DefaultFilterItem extends StatelessWidget {
  final String title;
  final bool selected;
  final VoidCallback? onToggle;

  const DefaultFilterItem({
    required this.title,
    required this.selected,
    this.onToggle,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListBox(
      onPressed: onToggle,
      listBoxModel: ListBoxModel(
        textModel: ListBoxTextModel(title: title),
        rightPartModel: ListBoxPartModel.checkbox(value: selected),
        isBoxed: true,
      ),
    );
  }
}

class _Header<T> extends StatelessWidget {
  final String title;

  const _Header({
    required this.title,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<FilterSelectionCubit<T>>();
    final l10n = StatementLocalizations.of(context);

    return Row(
      children: [
        Expanded(child: _HeaderTitle(title: title)),
        const Space.horizontal(8),
        BlocSelector<FilterSelectionCubit<T>, FilterSelectionState<T>, bool>(
          selector: (state) => state.canSelectMore,
          builder: (context, canSelectMore) => _HeaderAction(
            title: canSelectMore
                ? l10n.statementFilterSelectAllButtonTitle
                : l10n.statementFilterClearAllButtonTitle,
            onPressed: canSelectMore ? cubit.selectAll : cubit.clearAll,
          ),
        ),
      ],
    );
  }
}

class _HeaderTitle extends StatelessWidget {
  final String title;

  const _HeaderTitle({
    required this.title,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Label(
      model: LabelModel(
        text: title,
        textStyle: CompanyTextStylePointer.h3medium,
        color: CompanyColorPointer.primary3,
      ),
    );
  }
}

class _HeaderAction extends StatelessWidget {
  final String title;
  final VoidCallback? onPressed;

  const _HeaderAction({
    required this.title,
    this.onPressed,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Button(
      onPressed: onPressed,
      model: ButtonModel(
        title: title,
        type: ButtonType.tertiary,
        size: ButtonSize.xSmall,
        applyPadding: false,
        styleOverride: const ButtonStyleOverride(
          active: ButtonStateColorScheme(
            foreground: CompanyColorPointer.primary1,
          ),
          textStyle: CompanyTextStylePointer.b2,
        ),
      ),
    );
  }
}

class _Content<T> extends StatelessWidget {
  final FilterItemBuilder<T> itemBuilder;

  const _Content({
    required this.itemBuilder,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final state = context.watch<FilterSelectionCubit<T>>().state;
    final filters = state.filters;

    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      shrinkWrap: true,
      physics: const BouncingScrollPhysics(),
      itemCount: filters.length,
      separatorBuilder: (_, __) => const Space.vertical(8),
      itemBuilder: (context, index) => itemBuilder(
        context,
        filters[index],
        state.isSelected(filters[index]),
      ),
    );
  }
}

class _Footer<T> extends StatelessWidget {
  const _Footer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = StatementLocalizations.of(context);

    return Button(
      onPressed: context.read<FilterSelectionCubit<T>>().submit,
      model: ButtonModel(
        title: l10n.statementFilterSaveButtonTitle,
        size: ButtonSize.medium,
        type: ButtonType.primary,
        theme: ButtonModelTheme.sme,
      ),
    );
  }
}

extension on MediaQueryData {
  double get bottomPadding => max(viewInsets.bottom, viewPadding.bottom);
}
