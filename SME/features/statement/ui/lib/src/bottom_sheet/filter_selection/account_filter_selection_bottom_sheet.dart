import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:statement_feature_api/statement_feature_api.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_statement_ui/feature_statement_ui.dart';
import 'package:wio_feature_statement_ui/src/bottom_sheet/filter_selection/filter_selection_cubit.dart';
import 'package:wio_feature_statement_ui/src/bottom_sheet/filter_selection/filter_selection_view.dart';

class AccountFilterSelectionBottomSheet extends StatelessWidget {
  final List<AccountFilter> accounts;
  final List<AccountFilter>? selectedAccounts;

  const AccountFilterSelectionBottomSheet({
    required this.accounts,
    this.selectedAccounts,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<FilterSelectionCubit<AccountFilter>>(
      create: (_) =>
          DependencyProvider.get<FilterSelectionCubit<AccountFilter>>()
            ..initialize(
              filters: accounts,
              selectedFilters: selectedAccounts,
            ),
      child: const _AccountFilterSelection(),
    );
  }
}

class _AccountFilterSelection extends StatelessWidget {
  const _AccountFilterSelection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = StatementLocalizations.of(context);
    final cubit = context.read<FilterSelectionCubit<AccountFilter>>();

    return FilterSelectionView<AccountFilter>(
      title: l10n.statementFilterAccountsTitle,
      itemBuilder: (context, account, isSelected) => _AccountFilterItem(
        onToggle: () => cubit.toggleFilter(account),
        account: account,
        selected: isSelected,
      ),
    );
  }
}

class _AccountFilterItem extends StatelessWidget {
  final AccountFilter account;
  final bool selected;
  final VoidCallback? onToggle;

  const _AccountFilterItem({
    required this.account,
    required this.selected,
    this.onToggle,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = StatementLocalizations.of(context);
    final subtitle = account.active
        ? account.availableBalance.toCodeOnRightFormat()
        : l10n.statementFilterClosedAccountLabel;

    return ListBox(
      onPressed: onToggle,
      listBoxModel: ListBoxModel(
        isBoxed: true,
        textModel: ListBoxTextModel(
          title: account.name,
          subtitle: subtitle,
        ),
        rightPartModel: ListBoxPartModel.checkbox(value: selected),
      ),
    );
  }
}
