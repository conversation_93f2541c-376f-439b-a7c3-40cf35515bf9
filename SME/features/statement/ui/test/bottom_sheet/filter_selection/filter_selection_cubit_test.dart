import 'package:mocktail/mocktail.dart';
import 'package:statement_feature_api/statement_feature_api.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/ui_tests.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_statement_ui/src/bottom_sheet/filter_selection/filter_selection_cubit.dart';

void main() {
  late NavigationProvider navigationProvider;
  late FilterSelectionCubit<int> cubit;
  final filters = List.generate(10, (i) => i);

  setUp(() {
    navigationProvider = MockNavigationProvider();
    cubit = FilterSelectionCubit(navigationProvider: navigationProvider);
  });

  group('Initialization', () {
    test('cannot initialize with empty filter list', () {
      // Act & Assert
      expect(
        () => cubit.initialize(filters: []),
        throwsA(isA<AssertionError>()),
      );
    });

    test(
      'initialized with all filters selected if no selected filters provided',
      () {
        // Act
        cubit.initialize(filters: filters);

        // Assert
        expect(cubit.state.filters, filters);
        expect(cubit.state.selectedFilters, containsAll(filters));
      },
    );

    test('initialized with given selected filters', () {
      // Arrange
      final selectedFilters = {
        for (var i = 0; i < 2; i++) pickRandomly(filters),
      }.toList();

      // Act
      cubit.initialize(filters: filters, selectedFilters: selectedFilters);

      // Assert
      final actualState = cubit.state;
      expect(actualState.filters, filters);
      expect(actualState.selectedFilters, containsAll(selectedFilters));
      expect(actualState.selectedFilters, isNot(containsAll(filters)));
    });
  });

  group('Selection', () {
    setUp(() {
      cubit
        ..initialize(filters: filters)
        ..clearAll();
    });

    test('can select filters till all of them are selected', () {
      // Act
      for (final filter in filters.skip(1)) {
        cubit.toggleFilter(filter);
      }

      // Assert
      expect(cubit.state.canSelectMore, isTrue);
    });

    test('cannot select anything if all filters are selected', () {
      // Act
      cubit.selectAll();

      // Assert
      expect(cubit.state.canSelectMore, isFalse);
      expect(cubit.state.allSelected, isTrue);
    });
  });

  group('Submission', () {
    setUp(() {
      cubit
        ..initialize(filters: filters)
        ..clearAll();
    });

    test('returns selected filters on submit', () {
      // Arrange
      final selectedFilter = pickRandomly(filters);

      // Act
      cubit
        ..toggleFilter(selectedFilter)
        ..submit();

      // Assert
      final result = verify(
        () => navigationProvider.goBack(captureAny()),
      )..calledOnce;
      expect(result.captured, hasLength(1));
      expect(result.captured.single, isA<FilterSelectionResult<int>>());
    });
  });

  group('Auto-selection on init', () {
    setUp(() {
      cubit = FilterSelectionCubit(
        navigationProvider: navigationProvider,
        autoSelectAll: false,
      );
    });

    test('does not select all filters automatically', () {
      // Act
      cubit.initialize(filters: filters);

      // Assert
      expect(cubit.state.filters, filters);
      expect(cubit.state.selectedFilters, isEmpty);
    });
  });
}
