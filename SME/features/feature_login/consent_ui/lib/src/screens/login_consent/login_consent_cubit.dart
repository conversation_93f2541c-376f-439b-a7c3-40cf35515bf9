import 'dart:async';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:data/auth_manager/auth_data.dart';
import 'package:feature_2fa_api/feature_2fa_api.dart';
import 'package:feature_login_api/feature_login_api.dart';
import 'package:logging_api/logging.dart';
import 'package:sme_core_ui_desktop/index.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_login_consent_ui_desktop/locale/login_ui_consent_desktop_localizations.g.dart';
import 'package:wio_feature_login_consent_ui_desktop/src/analytics/login_consent_event_analytics.dart';
import 'package:wio_feature_login_consent_ui_desktop/src/helpers/validation_helper.dart';
import 'package:wio_feature_login_consent_ui_desktop/src/screens/login_consent/login_consent_state.dart';
import 'package:wio_sme_error_handler_api/error_handler_api.dart';

class _Constants {
  static String rememberBrowser = 'remember_browser';
  static String consentAuthUriKey = 'consent_auth_uri';
}

class LoginConsentCubit extends BaseCubit<LoginConsentState> {
  final NavigationProvider _navigator;
  final LoginInteractor _loginInteractor;
  final RefreshSessionManager _refreshSessionManager;
  final NewNotificationService _notificationService;
  final Storage<String?> _localStorage;
  final LoginConsentsAnalytics _analytics;
  final LoginConsentUILocalizations _localizations;
  final ErrorHandlerTool _errorHandlerTool;
  final Storage<String?> _webSessionStorage;
  final FeatureToggleProvider _featureToggleProvider;
  final Logger _logger;

  LoginConsentCubit({
    required NavigationProvider navigator,
    required LoginInteractor loginInteractor,
    required RefreshSessionManager refreshSessionManager,
    required Storage<String?> localStorage,
    required NewNotificationService notificationService,
    required LoginConsentsAnalytics analytics,
    required LoginConsentUILocalizations localizations,
    required ErrorHandlerTool errorHandlerTool,
    required Storage<String?> webSessionStorage,
    required FeatureToggleProvider featureToggleProvider,
    required Logger logger,
  })  : _navigator = navigator,
        _loginInteractor = loginInteractor,
        _refreshSessionManager = refreshSessionManager,
        _localStorage = localStorage,
        _notificationService = notificationService,
        _analytics = analytics,
        _localizations = localizations,
        _errorHandlerTool = errorHandlerTool,
        _webSessionStorage = webSessionStorage,
        _featureToggleProvider = featureToggleProvider,
        _logger = logger,
        super(const LoginConsentState.empty());

  void init({
    required FeatureNavigationConfig postLoginRedirectConfig,
    String? clientId,
  }) {
    emit(
      LoginConsentState.loaded(
        postLoginRedirectConfig: postLoginRedirectConfig,
        clientId: clientId,
      ),
    );
    _analytics.viewPage();
  }

  Future<void> navigateToResetPassword(FeatureNavigationConfig config) async {
    _navigator.navigateTo(config).ignore();
  }

  void onChangePassword(String password) {
    state.mapOrNull(
      loaded: (s) {
        _validate(s.email, password);
      },
    );
  }

  void onChangeEmail(String email) {
    state.mapOrNull(
      loaded: (s) {
        _validate(email, s.password);
      },
    );
  }

  void _validate(String email, String password) {
    state.mapOrNull(
      loaded: (v) {
        final isValidEmail = ValidationHelper().validateEmail(email);
        if (isValidEmail && password.isNotEmpty) {
          emit(
            v.copyWith(
              fieldsValid: true,
              email: email,
              password: password,
            ),
          );
        } else {
          emit(
            v.copyWith(
              fieldsValid: false,
              email: email,
              password: password,
            ),
          );
        }
      },
    );
  }

  void showLoader({required bool isLoading}) {
    state.mapOrNull(
      loaded: (value) {
        emit(value.copyWith(isLoading: isLoading));
      },
    );
  }

  Future<void> login({
    required String email,
    required String password,
  }) async {
    await state.mapOrNull(
      loaded: (s) async {
        _analytics.onLoginClick(s.clientId);
        showLoader(isLoading: true);
        final rememberBrowser =
            await _localStorage.getByKey(_Constants.rememberBrowser);

        try {
          final result = await _loginInteractor.login(
            email,
            password: password,
          );

          await _handleLoginSuccess(
            result,
            s,
            rememberBrowser == null,
          );
        } on CreateSessionException catch (ex) {
          _analytics.failedToPassOTP(s.clientId);
          _handleCreateSessionException(ex);
        } on Exception catch (ex) {
          _analytics.loginFailed(s.clientId);
          _errorHandlerTool.handleException(
            ex,
            handleGeneralException: () => _handleExceptions(exception: ex),
          );
        } finally {
          showLoader(isLoading: false);
        }
      },
    );
    showLoader(isLoading: false);
  }

  void _handleCreateSessionException(CreateSessionException ex) {
    // TODO(identity): BaaS is out of the box, TBD with the design team.
    ex.maybeMap(
      wrongCredentials: (ex) {
        _notificationService.showSnackbar(
          SnackbarModel.var1LowPriority(
            leftIcon: CompanyIconPointer.selection_crossed,
            text: _localizations.consentInvalidCredentials,
          ),
        );
      },
      factorBlocked: (ex) {
        _notificationService.showSnackbar(
          SnackbarModel.var1LowPriority(
            leftIcon: CompanyIconPointer.selection_crossed,
            text: ex.error.message ?? _localizations.consentFactorNotAvailable,
          ),
        );
      },
      orElse: _handleExceptions,
    );
  }

  Future<void> _handleLoginSuccess(
    AuthData result,
    LoginConsentStateLoaded s,
    bool loginWith2fa,
  ) async {
    if (result.rememberBrowserMarker?.isNotEmpty ?? false) {
      await _localStorage.put(
        _Constants.rememberBrowser,
        result.rememberBrowserMarker,
      );
    }
    _refreshSessionManager.start();

    // Remove consent_auth_uri from session storage which is stored
    // after parsing of deeplink url in deep_link_loader_page.dart
    await _webSessionStorage.delete(
      _Constants.consentAuthUriKey,
    );

    if (loginWith2fa) {
      _analytics.loginWith2faSuccessful();
    } else {
      _analytics.loginWithout2faSuccessful();
    }

    _analytics.loginSuccessful(s.clientId);
    await _fetchFeatureToggles();
    await _navigator.navigateTo(s.postLoginRedirectConfig);
  }

  Future<void> _fetchFeatureToggles() async {
    try {
      await _featureToggleProvider.fetchConfigurations();
    } on Object catch (ex, stackTrace) {
      _logger.error(
        "Can't fetch Remote config",
        error: ex,
        stackTrace: stackTrace,
      );
    }
  }

  void _handleExceptions({Object? exception}) {
    final errorMessage = _errorHandlerTool.getMessageWithCorrelationId(
      exception,
      _localizations.consentErrorLogin,
    );

    _notificationService.showSnackbar(
      SnackbarModel.errorLowPriority(
        text: errorMessage,
      ),
    );
  }

  @override
  String toString() => 'LoginConsentCubit{}';
}
