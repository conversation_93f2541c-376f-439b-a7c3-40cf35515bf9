import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:ui/page/page_bloc_provider.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_feature_login_consent_ui_desktop/src/screens/confirmation_password_consent/confirmation_password_cubit.dart';
import 'package:wio_feature_login_consent_ui_desktop/src/screens/confirmation_password_consent/confirmation_password_desktop_layout.dart';
import 'package:wio_feature_login_consent_ui_desktop/src/screens/confirmation_password_consent/confirmation_password_mobile_layout.dart';

class ConfirmationPasswordPage extends StatefulWidget {
  const ConfirmationPasswordPage({super.key});

  @override
  State<ConfirmationPasswordPage> createState() =>
      _ConfirmationPasswordPageState();
}

class _ConfirmationPasswordPageState extends State<ConfirmationPasswordPage> {
  @override
  Widget build(BuildContext context) {
    return PageBlocProvider<ConfirmationPasswordCubit,
        ConfirmationPasswordState>(
      createBloc: () =>
          DependencyProvider.get<ConfirmationPasswordCubit>()..load(),
      child: ResponsiveLayout(
        desktopLayout: (context) => const ConfirmationPasswordDesktopLayout(),
        mobileLayout: (context) => const ConfirmationPasswordMobileLayout(),
      ),
    );
  }
}
