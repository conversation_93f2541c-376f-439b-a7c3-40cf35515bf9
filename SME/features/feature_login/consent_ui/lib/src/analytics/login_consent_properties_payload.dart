import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/index.dart';

part 'login_consent_properties_payload.freezed.dart';

@freezed
class LoginConsentPropertiesPayload
    with _$LoginConsentPropertiesPayload
    implements AnalyticsEventPayload {
  const factory LoginConsentPropertiesPayload({
    @Default('') String message,
    @Default('') String businessId,
    @Default('') String clientId,
    @Default('') String individualId,
    @Default('') String consentId,
  }) = _LoginConsentPropertiesPayload;

  const LoginConsentPropertiesPayload._();

  @override
  Map<String, String> getEventPayload() {
    return <String, String>{
      'message': message,
      'businessId': businessId,
      'individualId': individualId,
      'clientId': clientId,
      'consentId': consentId,
    };
  }
}
