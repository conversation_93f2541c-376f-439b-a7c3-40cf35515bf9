// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in feature_login_impl/test/utils.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i13;

import 'package:data/auth_manager/auth_data.dart' as _i3;
import 'package:data/auth_manager/auth_manager.dart' as _i15;
import 'package:data/graph_ql_network_manager/environment/environment.dart'
    as _i8;
import 'package:data/graph_ql_network_manager/environment/i_environment_key.dart'
    as _i7;
import 'package:data/graph_ql_network_manager/network_manager/graph_ql_client_factory.dart'
    as _i11;
import 'package:data/graph_ql_network_manager/network_manager/graph_ql_retwork_manager_facade.dart'
    as _i6;
import 'package:data/graph_ql_network_manager/network_manager/graphql_exceptions/app_exception_parser.dart'
    as _i9;
import 'package:data/graph_ql_network_manager/network_manager/links/interceptor_link.dart'
    as _i10;
import 'package:data/graph_ql_network_manager/network_manager/request_response/graph_ql_request.dart'
    as _i14;
import 'package:data/graph_ql_network_manager/network_manager/request_response/graph_ql_response.dart'
    as _i2;
import 'package:feature_2fa_api/feature_2fa_api.dart' as _i5;
import 'package:feature_login_api/feature_login_api.dart' as _i4;
import 'package:graphql/client.dart' as _i12;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i18;
import 'package:wio_app_core_api/index.dart' as _i16;
import 'package:wio_feature_behaviour_api/data/behaviour_repository.dart'
    as _i17;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGraphQLResponse_0 extends _i1.SmartFake
    implements _i2.GraphQLResponse {
  _FakeGraphQLResponse_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeAuthData_1 extends _i1.SmartFake implements _i3.AuthData {
  _FakeAuthData_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUserDetails_2 extends _i1.SmartFake implements _i4.UserDetails {
  _FakeUserDetails_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSessionInfoDetails_3 extends _i1.SmartFake
    implements _i4.SessionInfoDetails {
  _FakeSessionInfoDetails_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeQrCodeData_4 extends _i1.SmartFake implements _i5.QrCodeData {
  _FakeQrCodeData_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeQrCodeStatusData_5 extends _i1.SmartFake
    implements _i5.QrCodeStatusData {
  _FakeQrCodeStatusData_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeQrTransactionResponse_6 extends _i1.SmartFake
    implements _i5.QrTransactionResponse {
  _FakeQrTransactionResponse_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [IGraphQLNetworkManagerFacade].
///
/// See the documentation for Mockito's code generation for more information.
class MockIGraphQLNetworkManagerFacade extends _i1.Mock
    implements _i6.IGraphQLNetworkManagerFacade {
  MockIGraphQLNetworkManagerFacade() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void initialize({
    required Map<_i7.IEnvironmentKey, _i8.GraphQLApiConfig>? environments,
    _i9.AppExceptionParser<Exception>? appExceptionParser,
    List<_i10.InterceptorLink>? links = const [],
    _i11.GraphQLCacheType? cacheStrategyType = _i11.GraphQLCacheType.cache,
    List<_i12.Link>? graphQLlinks = const [],
    Map<String, String>? operationBffPaths,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
          {
            #environments: environments,
            #appExceptionParser: appExceptionParser,
            #links: links,
            #cacheStrategyType: cacheStrategyType,
            #graphQLlinks: graphQLlinks,
            #operationBffPaths: operationBffPaths,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i13.Future<_i2.GraphQLResponse> execute({
    required _i7.IEnvironmentKey? envKey,
    required _i14.GraphQLRequest? request,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #execute,
          [],
          {
            #envKey: envKey,
            #request: request,
          },
        ),
        returnValue:
            _i13.Future<_i2.GraphQLResponse>.value(_FakeGraphQLResponse_0(
          this,
          Invocation.method(
            #execute,
            [],
            {
              #envKey: envKey,
              #request: request,
            },
          ),
        )),
      ) as _i13.Future<_i2.GraphQLResponse>);

  @override
  void setCommonHeaders(Map<String, String>? headers) => super.noSuchMethod(
        Invocation.method(
          #setCommonHeaders,
          [headers],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [LoginInteractor].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginInteractor extends _i1.Mock implements _i4.LoginInteractor {
  MockLoginInteractor() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i13.Future<_i3.AuthData> login(
    String? email, {
    String? password,
    String? twoFaId,
    _i4.LoginPurpose? purpose,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [email],
          {
            #password: password,
            #twoFaId: twoFaId,
            #purpose: purpose,
          },
        ),
        returnValue: _i13.Future<_i3.AuthData>.value(_FakeAuthData_1(
          this,
          Invocation.method(
            #login,
            [email],
            {
              #password: password,
              #twoFaId: twoFaId,
              #purpose: purpose,
            },
          ),
        )),
      ) as _i13.Future<_i3.AuthData>);

  @override
  _i13.Future<_i3.AuthData> switchAccount(String? individualId) =>
      (super.noSuchMethod(
        Invocation.method(
          #switchAccount,
          [individualId],
        ),
        returnValue: _i13.Future<_i3.AuthData>.value(_FakeAuthData_1(
          this,
          Invocation.method(
            #switchAccount,
            [individualId],
          ),
        )),
      ) as _i13.Future<_i3.AuthData>);

  @override
  _i13.Future<void> refreshSession({
    required String? email,
    required String? refreshToken,
    required String? individualId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #refreshSession,
          [],
          {
            #email: email,
            #refreshToken: refreshToken,
            #individualId: individualId,
          },
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<_i4.UserDetails> getUserDetails({
    bool? maskMobile = true,
    bool? getCachedData = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserDetails,
          [],
          {
            #maskMobile: maskMobile,
            #getCachedData: getCachedData,
          },
        ),
        returnValue: _i13.Future<_i4.UserDetails>.value(_FakeUserDetails_2(
          this,
          Invocation.method(
            #getUserDetails,
            [],
            {
              #maskMobile: maskMobile,
              #getCachedData: getCachedData,
            },
          ),
        )),
      ) as _i13.Future<_i4.UserDetails>);

  @override
  _i13.Future<_i4.UserDetails> getUserDetailsAndSaveUserData({
    bool? maskMobile = true,
    bool? getCachedData = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserDetailsAndSaveUserData,
          [],
          {
            #maskMobile: maskMobile,
            #getCachedData: getCachedData,
          },
        ),
        returnValue: _i13.Future<_i4.UserDetails>.value(_FakeUserDetails_2(
          this,
          Invocation.method(
            #getUserDetailsAndSaveUserData,
            [],
            {
              #maskMobile: maskMobile,
              #getCachedData: getCachedData,
            },
          ),
        )),
      ) as _i13.Future<_i4.UserDetails>);

  @override
  _i13.Future<_i4.SessionInfoDetails> getSessionInfo() => (super.noSuchMethod(
        Invocation.method(
          #getSessionInfo,
          [],
        ),
        returnValue:
            _i13.Future<_i4.SessionInfoDetails>.value(_FakeSessionInfoDetails_3(
          this,
          Invocation.method(
            #getSessionInfo,
            [],
          ),
        )),
      ) as _i13.Future<_i4.SessionInfoDetails>);

  @override
  _i13.Future<void> upgradeSession() => (super.noSuchMethod(
        Invocation.method(
          #upgradeSession,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<void> deleteSession() => (super.noSuchMethod(
        Invocation.method(
          #deleteSession,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<_i4.UserData?> getLastLoggedInUser() => (super.noSuchMethod(
        Invocation.method(
          #getLastLoggedInUser,
          [],
        ),
        returnValue: _i13.Future<_i4.UserData?>.value(),
      ) as _i13.Future<_i4.UserData?>);

  @override
  _i13.Future<void> saveUserData(_i4.UserData? userData) => (super.noSuchMethod(
        Invocation.method(
          #saveUserData,
          [userData],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<_i5.QrCodeData> getQRCode() => (super.noSuchMethod(
        Invocation.method(
          #getQRCode,
          [],
        ),
        returnValue: _i13.Future<_i5.QrCodeData>.value(_FakeQrCodeData_4(
          this,
          Invocation.method(
            #getQRCode,
            [],
          ),
        )),
      ) as _i13.Future<_i5.QrCodeData>);

  @override
  _i13.Future<_i5.QrCodeStatusData> getQrStatusById(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #getQrStatusById,
          [id],
        ),
        returnValue:
            _i13.Future<_i5.QrCodeStatusData>.value(_FakeQrCodeStatusData_5(
          this,
          Invocation.method(
            #getQrStatusById,
            [id],
          ),
        )),
      ) as _i13.Future<_i5.QrCodeStatusData>);

  @override
  _i13.Future<_i5.QrTransactionResponse> createQrTransaction(
          {required String? qrCodeId}) =>
      (super.noSuchMethod(
        Invocation.method(
          #createQrTransaction,
          [],
          {#qrCodeId: qrCodeId},
        ),
        returnValue: _i13.Future<_i5.QrTransactionResponse>.value(
            _FakeQrTransactionResponse_6(
          this,
          Invocation.method(
            #createQrTransaction,
            [],
            {#qrCodeId: qrCodeId},
          ),
        )),
      ) as _i13.Future<_i5.QrTransactionResponse>);

  @override
  _i13.Future<void> solveQR({
    required String? qrCodeId,
    required String? transactionId,
    required String? challengeId,
    required String? signature,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #solveQR,
          [],
          {
            #qrCodeId: qrCodeId,
            #transactionId: transactionId,
            #challengeId: challengeId,
            #signature: signature,
          },
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<void> retainUser() => (super.noSuchMethod(
        Invocation.method(
          #retainUser,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);
}

/// A class which mocks [IAuthManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockIAuthManager extends _i1.Mock implements _i15.IAuthManager {
  MockIAuthManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isTokenExist => (super.noSuchMethod(
        Invocation.getter(#isTokenExist),
        returnValue: false,
      ) as bool);

  @override
  _i13.Future<_i3.AuthData?> getAuthData() => (super.noSuchMethod(
        Invocation.method(
          #getAuthData,
          [],
        ),
        returnValue: _i13.Future<_i3.AuthData?>.value(),
      ) as _i13.Future<_i3.AuthData?>);

  @override
  _i13.Future<void> putAuthData(_i3.AuthData? authData) => (super.noSuchMethod(
        Invocation.method(
          #putAuthData,
          [authData],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<void> clear() => (super.noSuchMethod(
        Invocation.method(
          #clear,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<bool> isUserAuthenticated() => (super.noSuchMethod(
        Invocation.method(
          #isUserAuthenticated,
          [],
        ),
        returnValue: _i13.Future<bool>.value(false),
      ) as _i13.Future<bool>);
}

/// A class which mocks [Rebirther].
///
/// See the documentation for Mockito's code generation for more information.
class MockRebirther extends _i1.Mock implements _i16.Rebirther {
  MockRebirther() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void rebirth() => super.noSuchMethod(
        Invocation.method(
          #rebirth,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [BehaviourRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockBehaviourRepository extends _i1.Mock
    implements _i17.BehaviourRepository {
  MockBehaviourRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void initialize(_i17.BehaviourInitialData? data) => super.noSuchMethod(
        Invocation.method(
          #initialize,
          [data],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void clear() => super.noSuchMethod(
        Invocation.method(
          #clear,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [PlatformInfo].
///
/// See the documentation for Mockito's code generation for more information.
class MockPlatformInfo extends _i1.Mock implements _i16.PlatformInfo {
  MockPlatformInfo() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isIOS => (super.noSuchMethod(
        Invocation.getter(#isIOS),
        returnValue: false,
      ) as bool);

  @override
  bool get isAndroid => (super.noSuchMethod(
        Invocation.getter(#isAndroid),
        returnValue: false,
      ) as bool);

  @override
  bool get isWeb => (super.noSuchMethod(
        Invocation.getter(#isWeb),
        returnValue: false,
      ) as bool);

  @override
  String get operatingSystem => (super.noSuchMethod(
        Invocation.getter(#operatingSystem),
        returnValue: _i18.dummyValue<String>(
          this,
          Invocation.getter(#operatingSystem),
        ),
      ) as String);

  @override
  String get operatingSystemVersion => (super.noSuchMethod(
        Invocation.getter(#operatingSystemVersion),
        returnValue: _i18.dummyValue<String>(
          this,
          Invocation.getter(#operatingSystemVersion),
        ),
      ) as String);

  @override
  String get deviceId => (super.noSuchMethod(
        Invocation.getter(#deviceId),
        returnValue: _i18.dummyValue<String>(
          this,
          Invocation.getter(#deviceId),
        ),
      ) as String);

  @override
  String get vendor => (super.noSuchMethod(
        Invocation.getter(#vendor),
        returnValue: _i18.dummyValue<String>(
          this,
          Invocation.getter(#vendor),
        ),
      ) as String);

  @override
  String get model => (super.noSuchMethod(
        Invocation.getter(#model),
        returnValue: _i18.dummyValue<String>(
          this,
          Invocation.getter(#model),
        ),
      ) as String);

  @override
  String get name => (super.noSuchMethod(
        Invocation.getter(#name),
        returnValue: _i18.dummyValue<String>(
          this,
          Invocation.getter(#name),
        ),
      ) as String);

  @override
  _i13.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  bool isDebugMode() => (super.noSuchMethod(
        Invocation.method(
          #isDebugMode,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  bool isReleaseMode() => (super.noSuchMethod(
        Invocation.method(
          #isReleaseMode,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  bool isProfileMode() => (super.noSuchMethod(
        Invocation.method(
          #isProfileMode,
          [],
        ),
        returnValue: false,
      ) as bool);
}
