import 'package:data/auth_manager/auth_data.dart';
import 'package:data/auth_manager/token_data.dart';
import 'package:feature_login_api/feature_login_api.dart';
import 'package:feature_login_impl/domain/jwt_mapper.dart';
import 'package:sme_rest_api/models/identity.swagger.dart' as sw;
import 'package:sme_rest_api/models/identity_v2.swagger.dart' as sw2;
import 'package:sme_rest_api/models/identity_v3.swagger.dart' as sw3;

abstract class AuthDataMapper {
  AuthData mapCreateSessionV3ResultToAuthData(sw3.LoginV3 session);

  AuthData mapCreateSessionResultToAuthData(sw2.LoginV2 session);

  AuthData mapRenewSessionResultToAuthData(sw.Login session);

  AuthData mapSsoLoginResultToAuthData(sw.SsoLogin session);

  SessionInfoDetails mapToDomainSessionInfo(
    sw.SessionInfoDetails sessionInfoDetails,
  );
}

class AuthDataMapperImpl implements AuthDataMapper {
  const AuthDataMapperImpl({
    required JwtMapper jwtMapper,
  }) : _jwtMapper = jwtMapper;

  final JwtMapper _jwtMapper;

  @override
  AuthData mapCreateSessionResultToAuthData(sw2.LoginV2 session) {
    final expiresIn = int.parse(session.expiresIn);
    final businessId = _jwtMapper.getBusinessId(session.accessToken);

    return AuthData(
      rememberBrowserMarker: session.rememberBrowserOtp,
      individualId: session.individualId,
      sessionId: session.sessionId,
      accessRole: session.accessRole,
      businessId: businessId,
      tokenData: TokenData(
        accessToken: session.accessToken,
        refreshToken: session.refreshToken,
        expiresIn: DateTime.now().add(Duration(seconds: expiresIn)),
      ),
      strength: _mapv2StrengthToSessionStrength(session.strength),
    );
  }

  SessionStrength? _mapv2StrengthToSessionStrength(sw2.Strength strength) {
    switch (strength) {
      case sw2.Strength.swaggerGeneratedUnknown:
        return null;
      case sw2.Strength.weak:
        return SessionStrength.weak;
      case sw2.Strength.strong:
        return SessionStrength.strong;
    }
  }

  @override
  AuthData mapCreateSessionV3ResultToAuthData(sw3.LoginV3 session) {
    final expiresIn = int.parse(session.expiresIn);
    final businessId = _jwtMapper.getBusinessId(session.accessToken);

    return AuthData(
      rememberBrowserMarker: session.rememberBrowserOtp,
      individualId: session.individualId,
      sessionId: session.sessionId,
      accessRole: session.accessRole,
      businessId: businessId,
      tokenData: TokenData(
        accessToken: session.accessToken,
        refreshToken: session.refreshToken,
        expiresIn: DateTime.now().add(Duration(seconds: expiresIn)),
      ),
      strength: _mapv3StrengthToSessionStrength(session.strength),
    );
  }

  SessionStrength? _mapv3StrengthToSessionStrength(sw3.Strength strength) {
    switch (strength) {
      case sw3.Strength.swaggerGeneratedUnknown:
        return null;
      case sw3.Strength.weak:
        return SessionStrength.weak;
      case sw3.Strength.strong:
        return SessionStrength.strong;
    }
  }

  @override
  AuthData mapRenewSessionResultToAuthData(sw.Login session) {
    final expiresIn = int.parse(session.expiresIn);
    final businessId = _jwtMapper.getBusinessId(session.accessToken);

    return AuthData(
      rememberBrowserMarker: session.rememberBrowserOtp,
      individualId: session.individualId,
      sessionId: session.sessionId,
      accessRole: session.accessRole,
      businessId: businessId,
      tokenData: TokenData(
        accessToken: session.accessToken,
        refreshToken: session.refreshToken,
        expiresIn: DateTime.now().add(Duration(seconds: expiresIn)),
      ),
      strength: _mapStrengthToSessionStrength(session.strength),
    );
  }

  SessionStrength? _mapStrengthToSessionStrength(sw.Strength strength) {
    switch (strength) {
      case sw.Strength.swaggerGeneratedUnknown:
        return null;
      case sw.Strength.weak:
        return SessionStrength.weak;
      case sw.Strength.strong:
        return SessionStrength.strong;
    }
  }

  @override
  SessionInfoDetails mapToDomainSessionInfo(
    sw.SessionInfoDetails sessionInfoDetails,
  ) {
    return SessionInfoDetails(
      strength: _mapToDomainStrength(sessionInfoDetails.strength),
      description: sessionInfoDetails.description,
      subtext: sessionInfoDetails.subtext,
      weakSessionWarning: sessionInfoDetails.weakSessionWarning != null
          ? _mapToDomainWeakSessionWarning(
              sessionInfoDetails.weakSessionWarning!,
            )
          : null,
    );
  }

  @override
  AuthData mapSsoLoginResultToAuthData(sw.SsoLogin session) {
    final expiresIn = int.parse(session.expiresIn);
    final businessId = _jwtMapper.getBusinessId(session.accessToken);

    return AuthData(
      rememberBrowserMarker: session.rememberBrowserOtp,
      individualId: session.individualId,
      sessionId: session.sessionId,
      accessRole: session.accessRole,
      businessId: businessId,
      tokenData: TokenData(
        accessToken: session.accessToken,
        refreshToken: session.refreshToken,
        expiresIn: DateTime.now().add(Duration(seconds: expiresIn)),
      ),
      email: session.email,
    );
  }

  WeakSessionWarning _mapToDomainWeakSessionWarning(
    sw.WeakSessionWarning weakSessionWarning,
  ) {
    return WeakSessionWarning(
      description: weakSessionWarning.description,
      subtext: weakSessionWarning.subtext,
      restrictedItems: weakSessionWarning.restrictedItemList
              ?.map((e) => _mapToDomainRestrictedItem(e))
              .toList() ??
          [],
    );
  }

  RestrictedItem _mapToDomainRestrictedItem(sw.RestrictedItems item) {
    return RestrictedItem(
      iconName: _mapToDomainIconEnum(item.iconName),
      title: item.title,
      text: item.text,
    );
  }

  RestrictedItemIcon _mapToDomainIconEnum(sw.IconName name) {
    switch (name) {
      case sw.IconName.swaggerGeneratedUnknown:
        return RestrictedItemIcon.unknown;
      case sw.IconName.payments:
        return RestrictedItemIcon.payments;
      case sw.IconName.cards:
        return RestrictedItemIcon.cards;
      case sw.IconName.newUsers:
        return RestrictedItemIcon.newUsers;
      case sw.IconName.contactDetails:
        return RestrictedItemIcon.contactDetails;
    }
  }

  Strength _mapToDomainStrength(sw.Strength? strength) {
    switch (strength) {
      case null:
      case sw.Strength.swaggerGeneratedUnknown:
        return Strength.unknown;
      case sw.Strength.weak:
        return Strength.weak;
      case sw.Strength.strong:
        return Strength.strong;
    }
  }
}
