import 'package:di/di.dart';
import 'package:flutter/widgets.dart';
import 'package:ui/page/base_page.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_feature_onboarding_employee_api/navigation/onboarding_navigation_config_params.dart';
import 'package:wio_feature_onboarding_employee_ui/feature_onboarding_employee_ui_desktop.dart';
import 'package:wio_feature_onboarding_employee_ui/src/pages/success/success_page_cubit.dart';
import 'package:wio_feature_onboarding_employee_ui/src/pages/success/success_page_state.dart';

class SuccessPage extends BasePage<SuccessPageState, SuccessPageCubit> {
  final OnboardingNavigationConfigParams navigationConfigParams;

  const SuccessPage({
    required this.navigationConfigParams,
    super.key,
  });

  @override
  Widget buildPage(
    BuildContext context,
    SuccessPageCubit bloc,
    SuccessPageState state,
  ) {
    final l10n = OnboardingEmployeeLocalizations.of(context);

    final username = state.userInfo?.fullName ??
        navigationConfigParams.onboardingInfo.username;
    final email = state.userInfo?.email ??
        navigationConfigParams.onboardingInfo.email ??
        navigationConfigParams.onboardingInfo.holderName;
    final userRole =
        state.userInfo?.assignedUserGroupForDisplay.join(', ') ?? '';

    return _SuccessPageContainer(
      title: l10n.onboardingAlmostThereTitle,
      subtitle: l10n.onboardingAlmostThereSubtitle(
        email,
      ),
      username: username,
      onButtonPressed: bloc.navigateToLogin,
      buttonLabel: l10n.signOut,
      role: userRole,
    );
  }

  @override
  SuccessPageCubit createBloc() =>
      DependencyProvider.get<SuccessPageCubit>()..init();
}

class _SuccessPageContainer extends StatelessWidget {
  final String title;
  final String subtitle;
  final String username;
  final String role;
  final VoidCallback onButtonPressed;
  final String buttonLabel;

  const _SuccessPageContainer({
    required this.title,
    required this.subtitle,
    required this.username,
    required this.onButtonPressed,
    required this.buttonLabel,
    required this.role,
  });

  @override
  Widget build(BuildContext context) {
    final userSymbols = (username.isNotEmpty) ? username.acronym(limit: 1) : '';

    return GradientScaffold(
      gradient: CompanyGradientPointer.indigo.toGradient(),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            const Expanded(
              child: FittedBox(
                child: CompanyIcon(
                  CompanyIconModel(
                    icon: GraphicAssetPointer.pictogram(
                      CompanyPictogramPointer.metaphors_dawn,
                    ),
                    size: CompanyIconSize.xxxxxxxLarge,
                    gradient: CompanyGradientPointer.peach,
                  ),
                ),
              ),
            ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: context.textStyling.h1.copyWith(
                      color: context.colorStyling.primary2,
                    ),
                  ),
                  const SizedBox(height: 24.0),
                  Text(
                    subtitle,
                    textAlign: TextAlign.center,
                    style: context.textStyling.b1.copyWith(
                      color: context.colorStyling.primary2,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Tile(
                    model: TileModel.label(
                      backgroundColor: CompanyColorPointer.surface2,
                      labelModel: LabelModel(
                        text: userSymbols,
                      ),
                    ),
                    size: 100.0,
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  Text(
                    username,
                    style: context.textStyling.h3medium.copyWith(
                      color: context.colorStyling.primary2,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  Text(
                    role,
                    // overflow: TextOverflow.ellipsis,
                    style: context.textStyling.b1.copyWith(
                      color: context.colorStyling.primary4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            SizedBox(
              width: double.infinity,
              child: Button(
                model: ButtonModel(
                  negative: true,
                  title: buttonLabel,
                ),
                onPressed: onButtonPressed,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
