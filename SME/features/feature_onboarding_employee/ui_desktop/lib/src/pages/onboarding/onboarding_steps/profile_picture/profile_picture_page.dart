import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_feature_onboarding_employee_api/wio_feature_onboarding_employee_api.dart';
import 'package:wio_feature_onboarding_employee_ui_desktop/locale/onboarding_employee_ui_desktop_localizations.g.dart';
import 'package:wio_feature_onboarding_employee_ui_desktop/src/pages/onboarding/onboarding_step_cubit.dart';
import 'package:wio_feature_onboarding_employee_ui_desktop/src/pages/onboarding/onboarding_steps/profile_picture/profile_picture_cubit.dart';

class ProfilePicturePage extends StatelessWidget {
  const ProfilePicturePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => ProfilePictureCubit(
        profilePictureInteractor:
            DependencyProvider.get<ProfilePictureInteractor>(),
      ),
      child: const _ProfilePictureForm(),
    );
  }
}

class _ProfilePictureForm extends StatefulWidget {
  const _ProfilePictureForm({
    Key? key,
  }) : super(key: key);

  @override
  State<_ProfilePictureForm> createState() => _ProfilePictureState();
}

class _ProfilePictureState extends State<_ProfilePictureForm> {
  @override
  Widget build(BuildContext context) {
    final l10n = OnboardingEmployeeUILocalizations.of(context);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 8),
          RichText(
            text: TextSpan(
              text: '${l10n.setA} ',
              style: context.textStyling.h2medium.copyWith(
                color: context.colorStyling.primary1,
              ),
              children: [
                TextSpan(
                  text: l10n.profilePicture.toLowerCase(),
                  style: context.textStyling.h2medium
                      .copyWith(color: context.colorStyling.primary3),
                ),
              ],
            ),
          ),
          const SizedBox(height: 48),
          const _ProfilePictureInputForm(),
        ],
      ),
    );
  }
}

class _ProfilePictureInputForm extends StatefulWidget {
  const _ProfilePictureInputForm({
    Key? key,
  }) : super(key: key);

  @override
  State<_ProfilePictureInputForm> createState() =>
      _ProfilePictureInputFormState();
}

class _ProfilePictureInputFormState extends State<_ProfilePictureInputForm> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ConstrainedBox(
          constraints: const BoxConstraints(
            maxHeight: 150,
            maxWidth: 150,
          ),
          child: InvoicesCompanyLogo(
            const InvoicesCompanyLogoModel(
              type: InvoicesCompanyType.picture,
              url:
                  'data:image/png;base64,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',
            ),
          ),
        ),
        const SizedBox(height: 76.0),
        _NextBackButtons(),
      ],
    );
  }
}

class _NextBackButtons extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SquareButton(
          icon: CompanyIconPointer.arrow_left,
          variant: SquareButtonType.outline,
          onClick: () async =>
              BlocProvider.of<OnboardingStepCubit>(context).goToNextStep(),
        ),
        const SizedBox(width: 12),
        SquareButton(
          icon: CompanyIconPointer.arrow_right,
          variant: SquareButtonType.primary,
          onClick: () async {},
        ),
      ],
    );
  }
}
