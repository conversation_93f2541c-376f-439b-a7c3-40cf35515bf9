// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'onboarding_step_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$OnboardingStepState {
  FlowType get flowType => throw _privateConstructorUsedError;
  int get currentStep => throw _privateConstructorUsedError;
  bool get flowComplete => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $OnboardingStepStateCopyWith<OnboardingStepState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OnboardingStepStateCopyWith<$Res> {
  factory $OnboardingStepStateCopyWith(
          OnboardingStepState value, $Res Function(OnboardingStepState) then) =
      _$OnboardingStepStateCopyWithImpl<$Res, OnboardingStepState>;
  @useResult
  $Res call({FlowType flowType, int currentStep, bool flowComplete});
}

/// @nodoc
class _$OnboardingStepStateCopyWithImpl<$Res, $Val extends OnboardingStepState>
    implements $OnboardingStepStateCopyWith<$Res> {
  _$OnboardingStepStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flowType = null,
    Object? currentStep = null,
    Object? flowComplete = null,
  }) {
    return _then(_value.copyWith(
      flowType: null == flowType
          ? _value.flowType
          : flowType // ignore: cast_nullable_to_non_nullable
              as FlowType,
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as int,
      flowComplete: null == flowComplete
          ? _value.flowComplete
          : flowComplete // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_OnboardingStepStateCopyWith<$Res>
    implements $OnboardingStepStateCopyWith<$Res> {
  factory _$$_OnboardingStepStateCopyWith(_$_OnboardingStepState value,
          $Res Function(_$_OnboardingStepState) then) =
      __$$_OnboardingStepStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({FlowType flowType, int currentStep, bool flowComplete});
}

/// @nodoc
class __$$_OnboardingStepStateCopyWithImpl<$Res>
    extends _$OnboardingStepStateCopyWithImpl<$Res, _$_OnboardingStepState>
    implements _$$_OnboardingStepStateCopyWith<$Res> {
  __$$_OnboardingStepStateCopyWithImpl(_$_OnboardingStepState _value,
      $Res Function(_$_OnboardingStepState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flowType = null,
    Object? currentStep = null,
    Object? flowComplete = null,
  }) {
    return _then(_$_OnboardingStepState(
      flowType: null == flowType
          ? _value.flowType
          : flowType // ignore: cast_nullable_to_non_nullable
              as FlowType,
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as int,
      flowComplete: null == flowComplete
          ? _value.flowComplete
          : flowComplete // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_OnboardingStepState implements _OnboardingStepState {
  _$_OnboardingStepState(
      {required this.flowType,
      this.currentStep = 0,
      this.flowComplete = false});

  @override
  final FlowType flowType;
  @override
  @JsonKey()
  final int currentStep;
  @override
  @JsonKey()
  final bool flowComplete;

  @override
  String toString() {
    return 'OnboardingStepState(flowType: $flowType, currentStep: $currentStep, flowComplete: $flowComplete)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_OnboardingStepState &&
            (identical(other.flowType, flowType) ||
                other.flowType == flowType) &&
            (identical(other.currentStep, currentStep) ||
                other.currentStep == currentStep) &&
            (identical(other.flowComplete, flowComplete) ||
                other.flowComplete == flowComplete));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, flowType, currentStep, flowComplete);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_OnboardingStepStateCopyWith<_$_OnboardingStepState> get copyWith =>
      __$$_OnboardingStepStateCopyWithImpl<_$_OnboardingStepState>(
          this, _$identity);
}

abstract class _OnboardingStepState implements OnboardingStepState {
  factory _OnboardingStepState(
      {required final FlowType flowType,
      final int currentStep,
      final bool flowComplete}) = _$_OnboardingStepState;

  @override
  FlowType get flowType;
  @override
  int get currentStep;
  @override
  bool get flowComplete;
  @override
  @JsonKey(ignore: true)
  _$$_OnboardingStepStateCopyWith<_$_OnboardingStepState> get copyWith =>
      throw _privateConstructorUsedError;
}
