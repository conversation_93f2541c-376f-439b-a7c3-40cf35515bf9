// ignore_for_file: deprecated_member_use

import 'package:sme_core_ui_desktop/index.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_feature_onboarding_employee_ui_desktop/locale/onboarding_employee_ui_desktop_localizations.g.dart';
import 'package:wio_sme_status_api/wio_sme_status_api.dart';

class OnboardingStubs {
  OnboardingStubs._();

  static String get creatorName => 'creatorName';

  static String get preparerName => 'preparerName';

  static SuccessScreenNavigationConfig blockedSuccessScreenConfig({
    required OnboardingEmployeeUILocalizations l10n,
    required SmeDesktopLocalizations baseLocalizations,
  }) =>
      SuccessScreenNavigationConfig.legacy(
        model: SuccessScreenModel(
          headerLayout: InternalHeaderLayout.logo,
          titleText: l10n.inviteBlocked,
          content: [
            SuccessScreenRowModel(
              partOne: l10n.pleaseRequestANewCode(creatorName),
            ),
          ],
          headerModelOverride: const InternalHeaderModel(
            variant: InternalHeaderVariant.var3,
            layout: InternalHeaderLayout.logo,
          ),
          secondaryCtaText: l10n.contactSupport,
          hasRightButtons: false,
          icon: CompanyPictogramPointer.symbols_security,
        ),
        onCtaAction: () {},
      );

  static SuccessScreenNavigationConfig alreadyAcceptedSuccessScreenConfig({
    required OnboardingEmployeeUILocalizations l10n,
    required SmeDesktopLocalizations baseLocalizations,
  }) =>
      SuccessScreenNavigationConfig.legacy(
        model: SuccessScreenModel(
          headerLayout: InternalHeaderLayout.logo,
          titleText: l10n.hiPreparerYouAlreadyAccepted(preparerName),
          content: [
            SuccessScreenRowModel(partOne: l10n.pleaseSignInToYourAccount),
          ],
          headerModelOverride: const InternalHeaderModel(
            variant: InternalHeaderVariant.var3,
            layout: InternalHeaderLayout.logo,
          ),
          secondaryCtaText: l10n.contactSupport,
          ctaText: l10n.signInLabel,
          hasRightButtons: false,
          icon: CompanyPictogramPointer.metaphors_wio_logo,
        ),
        onCtaAction: () {},
      );

  static SuccessScreenNavigationConfig cancelledStatusSuccessScreenConfig({
    required OnboardingEmployeeUILocalizations l10n,
    required SmeDesktopLocalizations baseLocalizations,
  }) =>
      SuccessScreenNavigationConfig.legacy(
        model: SuccessScreenModel(
          headerLayout: InternalHeaderLayout.logo,
          titleText: l10n.thisInviteNoLongerValid,
          content: [
            SuccessScreenRowModel(
              partOne: l10n.pleaseGetInTouchWithOwner(
                OnboardingStubs.creatorName,
              ),
            ),
          ],
          headerModelOverride: const InternalHeaderModel(
            variant: InternalHeaderVariant.var3,
            layout: InternalHeaderLayout.logo,
          ),
          secondaryCtaText: l10n.contactSupport,
          hasRightButtons: false,
          icon: CompanyPictogramPointer.objects_phone,
        ),
        onCtaAction: () {},
      );

  static ErrorScreenNavigationConfig errorSuccessScreenConfig({
    required OnboardingEmployeeUILocalizations l10n,
    required SmeDesktopLocalizations baseLocalizations,
  }) =>
      ErrorScreenNavigationConfig.legacy(
        model: ErrorScreenModel(
          errorTitle: l10n.oopsLabel,
          errorSubtitle: l10n.somethingWentWrongTryLater,
          confirmCtaText: l10n.retryButton,
          contactLinkText: l10n.contactSupport,
        ),
        onCtaAction: () {},
      );
}
