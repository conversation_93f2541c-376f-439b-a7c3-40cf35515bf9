import 'package:freezed_annotation/freezed_annotation.dart';

part 'applicant_details.freezed.dart';

enum DocumentType {
  passport,
  nationalIdentityCard,
}

@freezed
class ApplicantDetails with _$ApplicantDetails {
  const factory ApplicantDetails({
    required String applicantId,
    required String sdkToken,
    required DocumentType documentType,
    required bool documentCollected,
  }) = _ApplicantDetails;
}
