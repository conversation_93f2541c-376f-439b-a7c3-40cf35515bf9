// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'registered_individual.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RegisteredIndividual {
  String get individualId => throw _privateConstructorUsedError;
  String get businessId => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  UserStatusType get status => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RegisteredIndividualCopyWith<RegisteredIndividual> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegisteredIndividualCopyWith<$Res> {
  factory $RegisteredIndividualCopyWith(RegisteredIndividual value,
          $Res Function(RegisteredIndividual) then) =
      _$RegisteredIndividualCopyWithImpl<$Res, RegisteredIndividual>;
  @useResult
  $Res call(
      {String individualId,
      String businessId,
      String email,
      UserStatusType status});
}

/// @nodoc
class _$RegisteredIndividualCopyWithImpl<$Res,
        $Val extends RegisteredIndividual>
    implements $RegisteredIndividualCopyWith<$Res> {
  _$RegisteredIndividualCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? individualId = null,
    Object? businessId = null,
    Object? email = null,
    Object? status = null,
  }) {
    return _then(_value.copyWith(
      individualId: null == individualId
          ? _value.individualId
          : individualId // ignore: cast_nullable_to_non_nullable
              as String,
      businessId: null == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as UserStatusType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RegisteredIndividualCopyWith<$Res>
    implements $RegisteredIndividualCopyWith<$Res> {
  factory _$$_RegisteredIndividualCopyWith(_$_RegisteredIndividual value,
          $Res Function(_$_RegisteredIndividual) then) =
      __$$_RegisteredIndividualCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String individualId,
      String businessId,
      String email,
      UserStatusType status});
}

/// @nodoc
class __$$_RegisteredIndividualCopyWithImpl<$Res>
    extends _$RegisteredIndividualCopyWithImpl<$Res, _$_RegisteredIndividual>
    implements _$$_RegisteredIndividualCopyWith<$Res> {
  __$$_RegisteredIndividualCopyWithImpl(_$_RegisteredIndividual _value,
      $Res Function(_$_RegisteredIndividual) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? individualId = null,
    Object? businessId = null,
    Object? email = null,
    Object? status = null,
  }) {
    return _then(_$_RegisteredIndividual(
      individualId: null == individualId
          ? _value.individualId
          : individualId // ignore: cast_nullable_to_non_nullable
              as String,
      businessId: null == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as UserStatusType,
    ));
  }
}

/// @nodoc

class _$_RegisteredIndividual implements _RegisteredIndividual {
  const _$_RegisteredIndividual(
      {required this.individualId,
      required this.businessId,
      required this.email,
      required this.status});

  @override
  final String individualId;
  @override
  final String businessId;
  @override
  final String email;
  @override
  final UserStatusType status;

  @override
  String toString() {
    return 'RegisteredIndividual(individualId: $individualId, businessId: $businessId, email: $email, status: $status)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RegisteredIndividual &&
            (identical(other.individualId, individualId) ||
                other.individualId == individualId) &&
            (identical(other.businessId, businessId) ||
                other.businessId == businessId) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, individualId, businessId, email, status);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RegisteredIndividualCopyWith<_$_RegisteredIndividual> get copyWith =>
      __$$_RegisteredIndividualCopyWithImpl<_$_RegisteredIndividual>(
          this, _$identity);
}

abstract class _RegisteredIndividual implements RegisteredIndividual {
  const factory _RegisteredIndividual(
      {required final String individualId,
      required final String businessId,
      required final String email,
      required final UserStatusType status}) = _$_RegisteredIndividual;

  @override
  String get individualId;
  @override
  String get businessId;
  @override
  String get email;
  @override
  UserStatusType get status;
  @override
  @JsonKey(ignore: true)
  _$$_RegisteredIndividualCopyWith<_$_RegisteredIndividual> get copyWith =>
      throw _privateConstructorUsedError;
}
