import 'package:feature_counter_party_api/counter_party.dart';
import 'package:feature_counter_party_impl/src/domain/counter_party_validation_interactor.dart';
import 'package:mockito/mockito.dart';
import 'package:sme_core_utils/utils.dart';
import 'package:test/test.dart';

import '../utils.mocks.dart';

void main() {
  late MockCounterPartyRepository repository;
  late MockCounterPartyValidator validator;
  late CounterPartyValidationInteractor interactor;

  setUp(() {
    repository = MockCounterPartyRepository();
    validator = MockCounterPartyValidator();
    interactor = CounterPartyValidationInteractorImpl(repository, validator);
  });

  test('validate name calls repository', () async {
    // arrange
    final name = randomString();
    when(
      repository.validateName(any),
    ).thenAnswer((_) async {});

    // act
    await interactor.validateName(name);

    // assert
    verify(repository.validateName(name));
  });
}
