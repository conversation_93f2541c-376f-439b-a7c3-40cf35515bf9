import 'package:data/graph_ql_network_manager/graph_ql_network_manager.dart';
import 'package:di/di.dart';
import 'package:feature_counter_party_api/counter_party.dart';
import 'package:feature_counter_party_impl/counter_party.dart';
import 'package:feature_counter_party_impl/src/domain/counter_party_interactor.dart';
import 'package:test/test.dart';

import '../utils.mocks.dart';

void main() {
  setUp(() {
    // arrange
    DependencyProvider.reset();

    // ignore: deprecated_member_use
    DependencyProvider.registerSingleton<IGraphQLNetworkManagerFacade>(
      MockIGraphQLNetworkManagerFacade(),
    );

    CounterPartyDomainDependencyModuleResolver.register();
  });

  test('Can resolve interactor from service locator', () {
    // act & assert
    final interactor = DependencyProvider.get<CounterPartyInteractor>();
    expect(interactor, isA<CounterPartyInteractorImpl>());
  });
}
