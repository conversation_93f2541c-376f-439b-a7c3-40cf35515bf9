import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/ui_tests.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_scf_ui/feature_scf_ui.dart';
import 'package:wio_feature_scf_ui/src/screens/onboarding_flow/cubit/scf_onboarding_cubit.dart';

import '../mocks.dart';
import '../stubs.dart';

typedef _Cubit = ScfOnboardingCubit;
typedef _State = ScfOnboardingState;

void main() {
  late ScfOnboardingCubit cubit;
  // ignore:close_sinks
  late MockPublishSubject<ScfOnboardingStage> stageNotifier;
  late NavigationProvider navigationProvider;
  late ScfLocalizations localizations;

  setUp(() {
    stageNotifier = MockPublishSubject();
    navigationProvider = MockNavigationProvider();
    localizations = MockScfLocalizations();

    when(() => stageNotifier.stream).justAnswerEmptyAsync();

    cubit = ScfOnboardingCubit(
      stageNotifier: stageNotifier,
      navigationProvider: navigationProvider,
      localizations: localizations,
    );
  });

  group('init test', () {
    blocTest<_Cubit, _State>(
      'test init with non null domainId',
      build: () => cubit,
      act: (cubit) => cubit.init('domainId'),
      expect: () => <_State>[
        const _State.flow(
          currentStage: ScfOnboardingStage.scfOverview,
          domainId: 'domainId',
        ),
      ],
    );

    blocTest<_Cubit, _State>(
      'test init with null domainId',
      build: () => cubit,
      act: (cubit) => cubit.init(null),
      expect: () => <_State>[],
    );
  });

  group(
    'update stage test',
    () {
      blocTest<_Cubit, _State>(
        'when in referral input go to scf overview',
        build: () => cubit,
        act: (cubit) => cubit.updateStage(
          stage: ScfOnboardingStage.scfOverview,
          referralCode: ScfCubitTestStubs.referralCode,
        ),
        expect: () => [
          const _State.flow(
            currentStage: ScfOnboardingStage.scfOverview,
            referralCode: ScfCubitTestStubs.referralCode,
          ),
        ],
      );

      blocTest<_Cubit, _State>(
        'when in scf overview input go to referral input',
        build: () => cubit,
        seed: () => const _State.flow(
          currentStage: ScfOnboardingStage.scfOverview,
          referralCode: ScfCubitTestStubs.referralCode,
        ),
        act: (cubit) => cubit.updateStage(
          stage: ScfOnboardingStage.referralCodeInput,
          referralCode: ScfCubitTestStubs.referralCode,
        ),
        expect: () => [
          const _State.flow(
            currentStage: ScfOnboardingStage.referralCodeInput,
            referralCode: ScfCubitTestStubs.referralCode,
          ),
        ],
      );
    },
  );

  group('success and error screen navigation tests', () {
    blocTest<_Cubit, _State>(
      'Error Screen Navigation',
      build: () => cubit,
      setUp: () {
        when(() => navigationProvider.navigateTo(
              any(),
              replace: any(named: 'replace'),
            )).justAnswerEmptyAsync();
        when(() => localizations.scfOnboardingFailedScreenGenericTitle)
            .thenReturn('');
        when(() => localizations.scfOnboardingFailedErrorScreenDescription)
            .thenReturn('');
        when(() => localizations.scfGenericErrorScreenCta).thenReturn('');
      },
      act: (cubit) => cubit.navigateToErrorScreen(),
      verify: (_) {
        verify(() => navigationProvider.navigateTo(
              any(),
              replace: any(named: 'replace'),
            )).calledOnce;
      },
      expect: () => <_State>[],
    );

    blocTest<_Cubit, _State>(
      'Success Screen Navigation',
      build: () => cubit,
      setUp: () {
        when(() => navigationProvider.navigateTo(any())).justAnswerEmptyAsync();
        when(() => localizations.scfSuccessScreenMabrook).thenReturn('');
        when(() => localizations.scfSuccessScreenActivatedPrompt)
            .thenReturn('');
        when(() => localizations.scfSuccessPageCloseButtonText).thenReturn('');
      },
      act: (cubit) => cubit.navigateToProgramActivateSuccessScreen(),
      verify: (_) {
        verify(() => navigationProvider.navigateTo(any())).calledOnce;
      },
      expect: () => <_State>[],
    );
  });
}
