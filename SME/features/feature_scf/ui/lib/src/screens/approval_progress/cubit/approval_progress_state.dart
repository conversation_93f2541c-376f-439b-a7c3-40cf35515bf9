part of 'approval_progress_cubit.dart';

@freezed
class ApprovalProgressState with _$ApprovalProgressState {
  const factory ApprovalProgressState.initial() = _ApprovalProgressStateInitial;

  const factory ApprovalProgressState.ready({
    required Referral referral,
    required MuRequest muRequest,
    required List<TeamMemberInput> teamMemberInput,
    required String? businessName,
    required String userId,
    required String domainId,
  }) = _ApprovalProgressStateReady;

  const ApprovalProgressState._();

  String get anchorName => maybeMap(
        ready: (r) => r.referral.anchor,
        orElse: () => '',
      );

  String? get currentBusinessName => mapOrNull(
        ready: (readyState) => readyState.businessName,
      );

  TeamMemberInput? get preparerMember => maybeMap(
        ready: (readyState) => readyState.teamMemberInput.firstWhereOrNull(
          (teamMember) =>
              teamMember.individualId == readyState.muRequest.muDomain.authorId,
        ),
        orElse: () => null,
      );

  List<TeamMemberInput> get approversMembers => maybeMap(
        ready: (readyState) {
          final approversIds = readyState.muRequest.muDomain.approvers
              .map((approver) => approver.id)
              .toList();

          return readyState.teamMemberInput
              .where(
                (teamMember) => approversIds.contains(teamMember.individualId),
              )
              .toList();
        },
        orElse: () => [],
      );

  List<Signatory> get signatories => maybeMap(
        ready: (readyState) => [
          if (preparerMember != null)
            Signatory(
              id: preparerMember!.individualId,
              name: preparerMember!.fullName,
              email: preparerMember!.email,
              status: MuApproverStatus.approved,
            ),
          ...approversMembers.map(
            (approver) => Signatory(
              id: approver.individualId,
              name: approver.fullName,
              email: approver.email,
              status: readyState.muRequest.muDomain.approvers
                  .firstWhere(
                    (muApprover) => muApprover.id == approver.individualId,
                  )
                  .muApproverStatus,
            ),
          ),
        ],
        orElse: () => [],
      );

  bool get isLastApprover => maybeMap(
        ready: (readyState) {
          final pendingSignatories = signatories.where(
            (signatory) => signatory.status == MuApproverStatus.pending,
          );

          return pendingSignatories.length == 1 || pendingSignatories.isEmpty;
        },
        orElse: () => false,
      );

  bool get hasUserSigned => maybeMap(
        ready: (readyState) => signatories.any(
          (signatory) =>
              signatory.id == readyState.userId &&
              signatory.status == MuApproverStatus.approved,
        ),
        orElse: () => false,
      );
}

@freezed
class Signatory with _$Signatory {
  const factory Signatory({
    required String id,
    required String name,
    required String email,
    required MuApproverStatus status,
  }) = _Signatory;
}
