import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_scf_ui/feature_scf_ui.dart';
import 'package:wio_feature_scf_ui/src/screens/onboarding_flow/stages/join_program/join_cf/cubit/join_cf_cubit.dart';

class JoinCfPage extends StatelessWidget {
  final String referralCode;
  final String anchor;
  const JoinCfPage({
    required this.referralCode,
    required this.anchor,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider<JoinCfCubit>(
      create: (context) => DependencyProvider.get<JoinCfCubit>()..initialize(),
      child: _JoinCfPage(
        anchor: anchor,
        referralCode: referralCode,
      ),
    );
  }
}

class _JoinCfPage extends StatelessWidget {
  final String referralCode;
  final String anchor;
  const _JoinCfPage({
    required this.referralCode,
    required this.anchor,
  });

  @override
  Widget build(BuildContext context) {
    final localization = ScfLocalizations.of(context);

    return Scaffold(
      backgroundColor: CompanyColorPointer.background1.colorOf(context),
      appBar: TopNavigation(
        TopNavigationModel(
          backgroundColor: CompanyColorPointer.background1,
          leftAccessory: TopNavigationBackLeftAccessoryModel(),
          state: TopNavigationState.positive,
        ),
        onLeftIconPressed: () =>
            Navigator.of(context, rootNavigator: true).maybePop(),
      ),
      body: FixedButtonsPageLayout(
        model: FixedButtonsScrollablePageLayoutModel(
          primaryButton: FixedButtonsScrollablePageLayoutButton(
            type: ButtonType.primary,
            label: localization.scfJoinProgramScreenCtaText,
            size: ButtonSize.medium,
            theme: ButtonModelTheme.sme,
          ),
        ),
        child: _Content(
          anchor: anchor,
        ),
        onPrimaryButtonPressed: () {
          context.read<JoinCfCubit>().joinProgram(referralCode);
        },
      ),
    );
  }
}

class _Content extends StatelessWidget {
  final String anchor;
  const _Content({required this.anchor});

  @override
  Widget build(BuildContext context) {
    final localization = ScfLocalizations.of(context);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<JoinCfCubit, JoinCfState>(
            builder: (context, state) => switch (state) {
              JoinCfLoaded(:final username) => _NameLabel(username: username),
              _ => const CompanyShimmer(
                  model: CompanyShimmerModel(),
                  child: _NameLabel(username: '     '),
                )
            },
          ),
          const SizedBox(height: 8),
          CompanyRichText(
            CompanyRichTextModel(
              maxLines: 10,
              text: localization.cfJoinProgramScreenMainText(anchor),
              textAlign: TextAlign.start,
              highlightedTextModels: [
                HighlightedTextModel(
                  localization.scfJoinProgramScreenAnchorName(anchor),
                ),
              ],
              normalStyle: CompanyTextStylePointer.h2medium,
              accentStyle: CompanyTextStylePointer.h2medium,
              normalTextColor: CompanyColorPointer.primary3,
              accentTextColor: CompanyColorPointer.primary1,
            ),
          ),
          const SizedBox(height: 36.0),
          const _ProgramDetails(),
        ],
      ),
    );
  }
}

class _NameLabel extends StatelessWidget {
  const _NameLabel({
    required this.username,
  });
  final String username;

  @override
  Widget build(BuildContext context) {
    return Label(
      model: LabelModel(
        text: ScfLocalizations.of(context).scfJoinProgram(username),
        color: CompanyColorPointer.secondary4,
        textStyle: CompanyTextStylePointer.b2,
      ),
    );
  }
}

class _ProgramDetails extends StatelessWidget {
  const _ProgramDetails();

  @override
  Widget build(BuildContext context) {
    final localization = ScfLocalizations.of(context);

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Label(
          model: LabelModel(
            text: localization.scfProgramDescriptionTitle,
            textStyle: CompanyTextStylePointer.h4,
            color: CompanyColorPointer.secondary1,
          ),
        ),
        const SizedBox(height: 12),
        _ProgramDetail(
          title: localization.cfProgramDetailOneTitle,
          subtitle: localization.cfProgramDetailOneDescription,
        ),
        const SizedBox(height: 8),
        _ProgramDetail(
          title: localization.cfProgramDetailTwoTitle,
          subtitle: localization.cfProgramDetailTwoDescription,
        ),
        const SizedBox(height: 8),
        _ProgramDetail(
          title: localization.cfProgramDetailThreeTitle,
          subtitle: localization.cfProgramDetailsThreeDescription,
        ),
        const SizedBox(height: 8),
      ],
    );
  }
}

class _ProgramDetail extends StatelessWidget {
  final String title;
  final String subtitle;

  const _ProgramDetail({
    required this.title,
    required this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Cell(
      CellModel(
        body: CellBodyModel.text(
          title: title,
          subtitle: subtitle,
          subtitleMaxLines: 3,
        ),
        leading: const CellLeadingModel.icon(
          icon: GraphicAssetPointer.icon(CompanyIconPointer.success),
          backgroundColor: CompanyColorPointer.surface13,
          size: CompanyIconSize.large,
        ),
      ),
    );
  }
}
