import 'dart:async';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:common_feature_user_financial_details_api/domain/validation_interactor.dart';
import 'package:common_feature_user_financial_details_api/feature_toggles/financial_details_feature_toggles.dart';
import 'package:common_feature_user_financial_details_api/model/amount_input_state.dart';
import 'package:common_feature_user_financial_details_api/model/validation_payload.dart';
import 'package:logging_api/logging.dart';
import 'package:ui/cubit/base_cubit.dart';
import 'package:ui/l10n/common_localizations.g.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_status_view_api/navigation/status_view_feature_navigation_config.dart';
import 'package:wio_feature_turnover_api/domain/turnover_interactor.dart';
import 'package:wio_feature_turnover_ui/feature_turnover_ui.dart';
import 'package:wio_feature_turnover_ui/src/analytics/turnover_analytics.dart';
import 'package:wio_feature_turnover_ui/src/analytics/turnover_events.dart';
import 'package:wio_feature_turnover_ui/src/screens/turnover/turnover_state.dart';

class TurnoverCubit extends BaseCubit<TurnoverState> {
  final TurnoverInteractor _turnoverInteractor;
  final TurnoverAnalytics _turnoverAnalytics;
  final Logger _logger;
  final NavigationProvider _navigationProvider;
  final CommonLocalizations _commonLocalizations;
  final ValidationInteractor _validationInteractor;
  final TurnoverLocalizations _turnoverLocalizations;
  final FeatureToggleProvider _featureToggles;
  StreamSubscription<AmountInputState>? _validationSubscription;

  TurnoverCubit({
    required TurnoverInteractor turnoverInteractor,
    required TurnoverAnalytics turnoverAnalytics,
    required Logger logger,
    required NavigationProvider navigationProvider,
    required CommonLocalizations commonLocalizations,
    required TurnoverLocalizations turnoverLocalizations,
    required ValidationInteractor validationInteractor,
    required FeatureToggleProvider featureToggles,
  })  : _turnoverInteractor = turnoverInteractor,
        _turnoverAnalytics = turnoverAnalytics,
        _logger = logger,
        _navigationProvider = navigationProvider,
        _commonLocalizations = commonLocalizations,
        _turnoverLocalizations = turnoverLocalizations,
        _validationInteractor = validationInteractor,
        _featureToggles = featureToggles,
        super(const TurnoverState()) {
    _subscribeToValidationStream();
  }

  Future<void> initialize() async {
    _turnoverAnalytics.trackTurnoverEvent(
      event: TurnoverEvents.turnoverScreenOpened,
    );
  }

  bool isInputValidationEnabled() {
    return _featureToggles.get<bool>(
      FinancialDetailsFeatureToggles.isInputValidationEnabled,
    );
  }

  bool isAmountValid(String? amount) {
    if (!isInputValidationEnabled()) {
      return amount != null && amount.isNotEmpty;
    }

    return state.inputState is AmountInputStateValid;
  }

  void _subscribeToValidationStream() {
    _validationSubscription = _validationInteractor.inputStateStream().listen(
      (inputState) {
        emit(state.copyWith(inputState: inputState));
      },
      // ignore: inference_failure_on_untyped_parameter
      onError: (error) {
        _logger.error('Validation stream error: $error');
      },
    );
  }

  void onInputChanged(String amount) {
    if (isInputValidationEnabled()) {
      final parsed = double.tryParse(amount.replaceAll(',', ''));
      _validationInteractor.onAmountChanged(
        validationPayload: ValidationPayload(
          turnover: parsed,
        ),
      );
    } else {
      emit(
        state.copyWith(
          inputState: amount.isNotEmpty
              ? const AmountInputState.valid()
              : const AmountInputState.idle(),
        ),
      );
    }
  }

  Future<void> submitTurnover(String updatedTurnover) async {
    try {
      emit(state.copyWith(isUpdating: true));
      _turnoverAnalytics.trackTurnoverEvent(
        event: TurnoverEvents.submitTurnover,
      );
      final unFormattedAmount = updatedTurnover.replaceAll(',', '');
      await _turnoverInteractor.submitTurnover(unFormattedAmount);
      await _handleSuccess(updatedTurnover);
    } on Object catch (error) {
      await _handleError(error);
    } finally {
      emit(state.copyWith(isUpdating: false));
    }
  }

  Future<void> _handleError(Object error) async {
    _logger.error(
      error.toString(),
      error: error,
      stackTrace: StackTrace.current,
    );
    _turnoverAnalytics.trackError(
      error,
      screen: Constants.turnoverScreen,
    );
    await _navigationProvider.navigateTo(
      StatusViewFeatureNavigationConfig.error(
        icon: const GraphicAssetPointer.pictogram(
          CompanyPictogramPointer.validation_failure,
        ),
        title: _commonLocalizations.transactionCancelStatusErrorTitle,
        subTitleModel: CompanyRichTextModel(
          text: _turnoverLocalizations.updateErrorSubtitle,
        ),
        bottomConfig: StatusPageBottomConfig.button(
          primaryButtonTitle: _commonLocalizations.close,
        ),
      ),
    );
    _navigationProvider.goBack();
  }

  Future<void> _handleSuccess(String turnover) async {
    await _navigationProvider.navigateTo(
      StatusViewFeatureNavigationConfig.alternativeSuccess(
        icon: const GraphicAssetPointer.pictogram(
          CompanyPictogramPointer.validation_success,
        ),
        title: _commonLocalizations.done,
        subTitleModel: CompanyRichTextModel(
          text: _turnoverLocalizations.turnoverSubmitSuccessText,
        ),
        bottomConfig: StatusPageBottomConfig.button(
          primaryButtonTitle: _commonLocalizations.close,
        ),
      ),
    );
    _navigationProvider.goBack(turnover);
  }

  @override
  String toString() => 'TurnoverCubit';

  @override
  Future<void> close() {
    _validationSubscription?.cancel();
    return super.close();
  }
}
