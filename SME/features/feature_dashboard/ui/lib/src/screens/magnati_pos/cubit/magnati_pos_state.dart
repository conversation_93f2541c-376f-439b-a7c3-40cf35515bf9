import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

part 'magnati_pos_state.freezed.dart';

@freezed
class MagnatiPosState with _$MagnatiPosState {
  factory MagnatiPosState.value({
    required InfoListDetailsBottomSheetConfiguration configuration,
    required bool consentCheckboxValue,
    required bool physicalAddressCheckBoxValue,
  }) = MagnatiPosStateValue;

  factory MagnatiPosState.loading({
    required InfoListDetailsBottomSheetConfiguration configuration,
  }) = MagnatiPosStateLoading;
}

extension MagnatiPosStateSelector on MagnatiPosState {
  MagnatiPosStateValue? asValue() => mapOrNull(value: (s) => s);

  MagnatiPosStateLoading? asLoading() => mapOrNull(loading: (s) => s);

  bool isButtonPressable() {
    final currentState = asValue();

    if (currentState != null) {
      return currentState.consentCheckboxValue &&
          currentState.physicalAddressCheckBoxValue;
    }

    return false;
  }
}
