// ignore_for_file: constant_identifier_names

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/index.dart';

part 'assistant_card_payload.freezed.dart';

@freezed
class AssistantCardPayload
    with _$AssistantCardPayload
    implements AnalyticsEventPayload {
  const factory AssistantCardPayload({
    required int position,
    required String product,
  }) = _AssistantCardPayload;

  const AssistantCardPayload._();

  @override
  Map<String, dynamic> getEventPayload() => <String, String>{
        'position': '$position',
        'product': product,
      };
}
