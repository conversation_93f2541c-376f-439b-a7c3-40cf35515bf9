import 'package:flutter/widgets.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_dashboard_api/navigation/add_money_bottom_sheet_navigation_config.dart';
import 'package:wio_feature_dashboard_api/navigation/announcement_screen_navigation_config.dart';
import 'package:wio_feature_dashboard_api/navigation/dashboard_feature_navigation_config.dart';
import 'package:wio_feature_dashboard_api/navigation/magnati_pos_bottom_sheet_navigation_config.dart';
import 'package:wio_feature_dashboard_api/navigation/magnati_pos_navigaton_config.dart';
import 'package:wio_feature_dashboard_api/navigation/post_onboarding_service_navigation.dart';
import 'package:wio_feature_dashboard_ui/feature_dashboard_ui.dart';
import 'package:wio_feature_dashboard_ui/src/bottom_sheet/add_money/add_money_bottom_sheet.dart';
import 'package:wio_feature_dashboard_ui/src/screens/magnati_pos/magnati_pos_page.dart';
import 'package:wio_feature_dashboard_ui/src/screens/magnati_pos/magnati_pos_view.dart';
import 'package:wio_feature_dashboard_ui/src/screens/post_onboarding_service/post_onboarding_service_page.dart';

class DashboardRouter extends NavigationRouter {
  @override
  Route<Object?> getScreenRoute(RouteSettings settings) {
    final config = settings.arguments;

    return switch (config) {
      DashboardFeatureNavigationConfig() =>
        toRoute(const DashboardPage(), settings),
      MagnatiPosScreenNavigationConfig() =>
        toRoute(const MagnatiPosPage(), settings),
      AnnouncementScreenNavigationConfig() => PageRouteBuilder<Object?>(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const AnnouncementPage(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(0.0, 1.0);
            const end = Offset.zero;
            final tween = Tween(begin: begin, end: end)
                .chain(CurveTween(curve: Curves.decelerate));
            final offsetAnimation = animation.drive(tween);
            return SlideTransition(
              position: offsetAnimation,
              child: child,
            );
          },
        ),
      PostOnBoardingServiceNavigationConfig() => toRoute(
          PostOnBoardingServicePage(
            serviceType: config.serviceType,
            imageUrl: config.imageUrl,
            title: config.title,
          ),
          settings,
        ),
      _ => throw Exception('Unknown $config for the $runtimeType')
    };
  }

  @override
  Future<T?> showBottomSheet<T>(
    BuildContext context,
    BottomSheetNavigationConfig<T> config,
    RouteSettings routeSettings,
  ) async {
    return switch (config) {
      MagnatiPosBottomSheetNavigationConfig() =>
        CompanyBottomSheet.showRetailMobileThemeBasedAdaptiveModal(
          context: context,
          routeSettings: routeSettings,
          hasBottomInset: false,
          wrap: false,
          initialChildSize: 0.9,
          builder: (context) => const MagnatiPosView(),
        ),
      AddMoneyBottomSheetNavigationConfig() =>
        CompanyBottomSheet.showRetailMobileThemeBasedAdaptiveModal(
          context: context,
          routeSettings: routeSettings,
          hasBottomInset: false,
          builder: (context) => const AddMoneyBottomSheet(),
        ),
      BottomSheetNavigationConfig<T>() =>
        super.showBottomSheet(context, config, routeSettings),
    };
  }
}
