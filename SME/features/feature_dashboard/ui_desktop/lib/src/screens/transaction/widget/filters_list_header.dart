import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transaction_ui_desktop/transaction_ui_desktop.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_feature_dashboard_ui_desktop/feature_dashboard_desktop_ui.dart';
import 'package:wio_feature_dashboard_ui_desktop/src/screens/dashboard/cubit/dashboard_cubit.dart';
import 'package:wio_feature_dashboard_ui_desktop/src/screens/dashboard/cubit/dashboard_state.dart';

class FiltersListHeader extends StatelessWidget {
  const FiltersListHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final localization = DashboardUILocalizations.of(context);

    return BlocBuilder<DashboardCubit, DashboardState>(
      builder: (context, state) => ListHeader(
        ListHeaderModel(
          title: localization.accountTransactions,
          headerType: ListHeaderType.large,
        ),
        buttonCluster: const [
          TransactionFilterOptions(),
        ],
      ),
    );
  }
}
