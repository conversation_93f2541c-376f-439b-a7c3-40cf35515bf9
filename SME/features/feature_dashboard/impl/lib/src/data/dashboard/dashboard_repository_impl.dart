import 'package:core/storage/keys/storage_keys.dart';
import 'package:core/storage/storage_service.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_dashboard_api/feature_dashboard.dart';
import 'package:wio_feature_dashboard_api/model/service.dart';
import 'package:wio_feature_dashboard_impl/src/data/dashboard/dashboard_data_source.dart';
import 'package:wio_feature_dashboard_impl/src/data/dashboard/service_cache_data_source.dart';
import 'package:wio_feature_dashboard_impl/src/data/mappers/contentful_mapper.dart';

class DashboardRepositoryImpl implements DashboardRepository {
  static const _seenAddMultipleAccountsCoachMark =
      '_seenAddMultipleAccountsCoachMark';
  static const _seenAddMultipleAccountsPromoPage =
      '_seenAddMultipleAccountsPromoPage';
  static const _interactedChequebookReorderingCard =
      'interactedChequebookReorderingCard';

  static const _dashboardCoachMarkSeen = '_dashboardCoachMarkSeen';
  static const _chequebookServiceCoachmarkKey =
      '_chequebookServiceCoachmarkKey';
  static const _chequebookServiceBottomsheetCoachmarkKey =
      '_chequebookServiceBottomsheetCoachmarkKey';
  static const _dashboardWalkThroughCardSkip = '_dashboardWalkThroughCardSkip';

  final CachedServiceDataSource _cachedDataSource;
  final ContentfulMapper _contentfulMapper;
  final DashboardDataSource _dataSource;
  final KeyValueStorage _storage;

  final IStorageService _storageService;

  DashboardRepositoryImpl({
    required CachedServiceDataSource cachedDataSource,
    required ContentfulMapper contentfulMapper,
    required DashboardDataSource dataSource,
    required KeyValueStorage storage,
    required IStorageService storageService,
  })  : _cachedDataSource = cachedDataSource,
        _contentfulMapper = contentfulMapper,
        _dataSource = dataSource,
        _storageService = storageService,
        _storage = storage;

  @override
  Future<bool> getHasUserSeenAddMultipleAccountsCoachMark() async {
    try {
      final value =
          await _storage.getByKey<bool>(_seenAddMultipleAccountsCoachMark);
      final hasSeen = value != null && value == true;

      return hasSeen;
    } on Object {
      // Treat errors (should not happen though) as if the user has seen it
      return true;
    }
  }

  @override
  Future<void> markAddMultipleAccountsCoachMarkAsSeen() async {
    return _storage.put(_seenAddMultipleAccountsCoachMark, true);
  }

  @override
  Future<bool> getHasUserSeenAddMultipleAccountsPromoPage() async {
    try {
      final haveSeen =
          await _storage.getByKey<bool>(_seenAddMultipleAccountsPromoPage) ??
              false;

      return haveSeen;
    } on Object {
      // Treat errors (should not happen though) as if the user has seen it
      return true;
    }
  }

  @override
  Future<void> markAddMultipleAccountsPromoPageAsSeen() {
    return _storage.put(_seenAddMultipleAccountsPromoPage, true);
  }

  @override
  Future<bool> getHasUserInteractedChequebookReorderingCard() async {
    try {
      final haveInteracted =
          await _storage.getByKey<bool>(_interactedChequebookReorderingCard) ??
              false;

      return haveInteracted;
    } on Object {
      // Treat errors (should not happen though) as if the user interacted it
      return true;
    }
  }

  @override
  Future<void> markChequebookReorderingCardAsInteracted() {
    return _storage.put(_interactedChequebookReorderingCard, true);
  }

  @override
  Future<List<Service>> fetchServiceItems() async {
    final cachedItems = _cachedDataSource.fetchServiceItems();
    if (cachedItems.isNotEmpty) return cachedItems;

    final result = await _dataSource.fetchServiceItems();
    final listOfService = _contentfulMapper.mapDtoToService(result as Object);

    _cachedDataSource.updateServiceItems(listOfService);
    return listOfService;
  }

  @override
  Future<void> clear() => _cachedDataSource.clear();

  @override
  Future<String> getUserMailId() => _storageService
      .getValueFromSecureStorage(StorageKeys.userEmailStorageKey);

  @override
  Future<bool> shouldShowAnnouncement() async {
    try {
      final hasSeen = await _storage.getByKey<bool>(_dashboardCoachMarkSeen);
      return hasSeen != true;
    } on Object {
      // Treat errors (should not happen though) as if the user has seen it
      return false;
    }
  }

  @override
  Future<bool> shouldShowWalkThroughCard() async {
    try {
      final hasSeen =
          await _storage.getByKey<bool>(_dashboardWalkThroughCardSkip);
      return hasSeen != true;
    } on Object {
      // Treat errors (should not happen though) as if the user has seen it
      return false;
    }
  }

  @override
  Future<void> markAnnouncementAsSeen() {
    return _storage.put(_dashboardCoachMarkSeen, true);
  }

  @override
  Future<void> markWalkThroughCardSkip() {
    return _storage.put(_dashboardWalkThroughCardSkip, true);
  }

  @override
  Future<bool> shouldShowChequebookServiceCoachmark() async {
    try {
      final hasSeen =
          await _storage.getByKey<bool>(_chequebookServiceCoachmarkKey);
      return hasSeen != true;
    } on Object {
      // Treat errors (should not happen though) as if the user has seen it
      return false;
    }
  }

  @override
  Future<void> markChequebookServiceCoachmarkAsSeen() =>
      _storage.put(_chequebookServiceCoachmarkKey, true);

  @override
  Future<bool> shouldShowChequebookServiceBottomsheetCoachmark() async {
    try {
      final hasSeen = await _storage
          .getByKey<bool>(_chequebookServiceBottomsheetCoachmarkKey);
      return hasSeen != true;
    } on Object {
      // Treat errors (should not happen though) as if the user has seen it
      return false;
    }
  }

  @override
  Future<void> markChequebookServiceBottomsheetCoachmarkAsSeen() =>
      _storage.put(_chequebookServiceBottomsheetCoachmarkKey, true);
}
