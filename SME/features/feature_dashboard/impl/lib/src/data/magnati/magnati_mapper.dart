import 'package:sme_graphql_api/graphql_api.dart';
import 'package:wio_feature_dashboard_api/model/magnati_onboarding_availability_result.dart';
import 'package:wio_feature_dashboard_api/model/magnati_pos_application_result.dart';
import 'package:wio_feature_dashboard_impl/src/data/dashboard/dto/models/magnati_schema.swagger.dart';

abstract class MagnatiMapper {
  MagnatiPosApplicationResult mapToSuccessfulApplicationResult(
    MagnatiOnboarding$Mutation$MagnatiOnboardingResponse response,
  );

  MagnatiPosApplicationResult mapToSuccessfulApplicationResultV2(
    MagnatiOnboardingV2$Mutation$MagnatiOnboardingResponseV2 response,
  );

  MagnatiPosApplicationResult mapToSuccessfulApplicationResultV3(
    MagnatiOnboardingResponseDto response,
  );

  MagnatiOnboardingAvailabilityResult mapToMagnatiOnboardingAvailabilityResult(
    MagnatiOnboardingAvailabilityV2$Query$MagnatiOnboardingResponseV2 response,
  );
}

class MagnatiMapperImpl implements MagnatiMapper {
  @override
  MagnatiPosApplicationResult mapToSuccessfulApplicationResult(
    MagnatiOnboarding$Mutation$MagnatiOnboardingResponse response,
  ) {
    // phone number and email can't be null in [response]
    // ! is used until BE wil make this fields non-nullable
    // BE devs are aware and working on it
    return MagnatiPosApplicationResult.success(
      phoneNumber: response.phoneNumber!,
      email: response.email!,
    );
  }

  @override
  MagnatiOnboardingAvailabilityResult mapToMagnatiOnboardingAvailabilityResult(
    MagnatiOnboardingAvailabilityV2$Query$MagnatiOnboardingResponseV2 response,
  ) {
    // TODO(Nikhil): Need to check which message pass when error message null
    return response.magnati != null
        ? const MagnatiOnboardingAvailabilityResult.success()
        : MagnatiOnboardingAvailabilityResult.error(
            message: response.error?.message ?? '',
          );
  }

  @override
  MagnatiPosApplicationResult mapToSuccessfulApplicationResultV2(
    MagnatiOnboardingV2$Mutation$MagnatiOnboardingResponseV2 response,
  ) {
    // phone number and email can't be null in [response]
    // ! is used until BE wil make this fields non-nullable
    // BE devs are aware and working on it
    if (response.magnati != null) {
      return MagnatiPosApplicationResult.success(
        phoneNumber: response.magnati!.phoneNumber!,
        email: response.magnati!.email!,
      );
    } else {
      // TODO(Nikhil): Check with Arnold about generic message.
      return MagnatiPosApplicationResult.error(
        message: response.error?.message ?? '',
      );
    }
  }

  @override
  MagnatiPosApplicationResult mapToSuccessfulApplicationResultV3(
    MagnatiOnboardingResponseDto response,
  ) {
    final magnatiContent = response.magnati;
    if (magnatiContent != null) {
      return MagnatiPosApplicationResult.success(
        phoneNumber: magnatiContent.phoneNumber ?? '',
        email: magnatiContent.email ?? '',
      );
    }

    final errorContent = response.error;

    return MagnatiPosApplicationResult.error(
      message: errorContent?.message ?? '',
    );
  }
}
