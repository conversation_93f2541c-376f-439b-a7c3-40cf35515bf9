import 'package:wio_feature_credit_api/credit_api.dart';

class VatStageDeterminator {
  final CreditApplicationStage stage;
  final VatReportingInterval? vatReportingInterval;
  const VatStageDeterminator({
    required this.stage,
    required this.vatReportingInterval,
  });

  /// Returns true if credit application stage is [VatStage]
  /// and we should go back in vat stage
  /// instead of going back in credit flow
  bool get shouldReturnInitialPartOfVatStage {
    if (stage is! VatStage) {
      return false;
    }

    final vatSubStage = (stage as VatStage).vatSubStage;

    return vatSubStage == VatSubStage.vatNonRegistrationReasonInput ||
        vatSubStage == VatSubStage.vatStatementsUpload;
  }

  /// Returns the next VAT stage
  /// Based on given [VatReportingInterval] and [CreditApplicationStage]
  ///
  /// returns null if vat stage is completed
  /// and flow can proceed to next stage in order
  VatStage? get nextVatStage {
    /// if current stage vat and entering vat reporting interval input
    /// update the vat stage else continue from next stage from flow
    if (stage
        case VatStage(vatSubStage: VatSubStage.vatReportingIntervalInput)) {
      return switch (vatReportingInterval) {
        VatReportingInterval.monthly ||
        VatReportingInterval.quarterly ||
        VatReportingInterval.annually =>
          const VatStage(vatSubStage: VatSubStage.vatStatementsUpload),
        VatReportingInterval.noReporting => const VatStage(
            vatSubStage: VatSubStage.vatNonRegistrationReasonInput,
          ),

        /// this should never happen
        /// since if next stage from interval input page called
        /// user must be selected interval option hence not null
        null =>
          const VatStage(vatSubStage: VatSubStage.vatReportingIntervalInput),
      };
    } else {
      return null;
    }
  }

  /// Returns the previous VAT stage
  /// Based on given [VatReportingInterval] and [CreditApplicationStage]
  ///
  /// returns null if vat stage is completed
  /// and flow can proceed to next stage in order
  VatStage get previousVatStage {
    if (vatReportingInterval == null) {
      return const VatStage(
        vatSubStage: VatSubStage.vatReportingIntervalInput,
      );
    } else if (vatReportingInterval == VatReportingInterval.noReporting) {
      return const VatStage(
        vatSubStage: VatSubStage.vatNonRegistrationReasonInput,
      );
    } else {
      return const VatStage(
        vatSubStage: VatSubStage.vatStatementsUpload,
      );
    }
  }
}
