// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'credit_application_flow_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CreditApplicationFlowState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetching,
    required TResult Function(FlowOrderState flowOrderState) inProgress,
    required TResult Function() error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetching,
    TResult? Function(FlowOrderState flowOrderState)? inProgress,
    TResult? Function()? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetching,
    TResult Function(FlowOrderState flowOrderState)? inProgress,
    TResult Function()? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CreditApplicationFlowFetchingState value)
        fetching,
    required TResult Function(_CreditApplicationFlowInProgressState value)
        inProgress,
    required TResult Function(_CreditApplicationFlowErrorState value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CreditApplicationFlowFetchingState value)? fetching,
    TResult? Function(_CreditApplicationFlowInProgressState value)? inProgress,
    TResult? Function(_CreditApplicationFlowErrorState value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CreditApplicationFlowFetchingState value)? fetching,
    TResult Function(_CreditApplicationFlowInProgressState value)? inProgress,
    TResult Function(_CreditApplicationFlowErrorState value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreditApplicationFlowStateCopyWith<$Res> {
  factory $CreditApplicationFlowStateCopyWith(CreditApplicationFlowState value,
          $Res Function(CreditApplicationFlowState) then) =
      _$CreditApplicationFlowStateCopyWithImpl<$Res,
          CreditApplicationFlowState>;
}

/// @nodoc
class _$CreditApplicationFlowStateCopyWithImpl<$Res,
        $Val extends CreditApplicationFlowState>
    implements $CreditApplicationFlowStateCopyWith<$Res> {
  _$CreditApplicationFlowStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreditApplicationFlowState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$CreditApplicationFlowFetchingStateImplCopyWith<$Res> {
  factory _$$CreditApplicationFlowFetchingStateImplCopyWith(
          _$CreditApplicationFlowFetchingStateImpl value,
          $Res Function(_$CreditApplicationFlowFetchingStateImpl) then) =
      __$$CreditApplicationFlowFetchingStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CreditApplicationFlowFetchingStateImplCopyWithImpl<$Res>
    extends _$CreditApplicationFlowStateCopyWithImpl<$Res,
        _$CreditApplicationFlowFetchingStateImpl>
    implements _$$CreditApplicationFlowFetchingStateImplCopyWith<$Res> {
  __$$CreditApplicationFlowFetchingStateImplCopyWithImpl(
      _$CreditApplicationFlowFetchingStateImpl _value,
      $Res Function(_$CreditApplicationFlowFetchingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditApplicationFlowState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CreditApplicationFlowFetchingStateImpl
    extends _CreditApplicationFlowFetchingState {
  const _$CreditApplicationFlowFetchingStateImpl() : super._();

  @override
  String toString() {
    return 'CreditApplicationFlowState.fetching()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreditApplicationFlowFetchingStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetching,
    required TResult Function(FlowOrderState flowOrderState) inProgress,
    required TResult Function() error,
  }) {
    return fetching();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetching,
    TResult? Function(FlowOrderState flowOrderState)? inProgress,
    TResult? Function()? error,
  }) {
    return fetching?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetching,
    TResult Function(FlowOrderState flowOrderState)? inProgress,
    TResult Function()? error,
    required TResult orElse(),
  }) {
    if (fetching != null) {
      return fetching();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CreditApplicationFlowFetchingState value)
        fetching,
    required TResult Function(_CreditApplicationFlowInProgressState value)
        inProgress,
    required TResult Function(_CreditApplicationFlowErrorState value) error,
  }) {
    return fetching(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CreditApplicationFlowFetchingState value)? fetching,
    TResult? Function(_CreditApplicationFlowInProgressState value)? inProgress,
    TResult? Function(_CreditApplicationFlowErrorState value)? error,
  }) {
    return fetching?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CreditApplicationFlowFetchingState value)? fetching,
    TResult Function(_CreditApplicationFlowInProgressState value)? inProgress,
    TResult Function(_CreditApplicationFlowErrorState value)? error,
    required TResult orElse(),
  }) {
    if (fetching != null) {
      return fetching(this);
    }
    return orElse();
  }
}

abstract class _CreditApplicationFlowFetchingState
    extends CreditApplicationFlowState {
  const factory _CreditApplicationFlowFetchingState() =
      _$CreditApplicationFlowFetchingStateImpl;
  const _CreditApplicationFlowFetchingState._() : super._();
}

/// @nodoc
abstract class _$$CreditApplicationFlowInProgressStateImplCopyWith<$Res> {
  factory _$$CreditApplicationFlowInProgressStateImplCopyWith(
          _$CreditApplicationFlowInProgressStateImpl value,
          $Res Function(_$CreditApplicationFlowInProgressStateImpl) then) =
      __$$CreditApplicationFlowInProgressStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({FlowOrderState flowOrderState});
}

/// @nodoc
class __$$CreditApplicationFlowInProgressStateImplCopyWithImpl<$Res>
    extends _$CreditApplicationFlowStateCopyWithImpl<$Res,
        _$CreditApplicationFlowInProgressStateImpl>
    implements _$$CreditApplicationFlowInProgressStateImplCopyWith<$Res> {
  __$$CreditApplicationFlowInProgressStateImplCopyWithImpl(
      _$CreditApplicationFlowInProgressStateImpl _value,
      $Res Function(_$CreditApplicationFlowInProgressStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditApplicationFlowState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flowOrderState = null,
  }) {
    return _then(_$CreditApplicationFlowInProgressStateImpl(
      flowOrderState: null == flowOrderState
          ? _value.flowOrderState
          : flowOrderState // ignore: cast_nullable_to_non_nullable
              as FlowOrderState,
    ));
  }
}

/// @nodoc

class _$CreditApplicationFlowInProgressStateImpl
    extends _CreditApplicationFlowInProgressState {
  const _$CreditApplicationFlowInProgressStateImpl(
      {required this.flowOrderState})
      : super._();

  @override
  final FlowOrderState flowOrderState;

  @override
  String toString() {
    return 'CreditApplicationFlowState.inProgress(flowOrderState: $flowOrderState)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreditApplicationFlowInProgressStateImpl &&
            (identical(other.flowOrderState, flowOrderState) ||
                other.flowOrderState == flowOrderState));
  }

  @override
  int get hashCode => Object.hash(runtimeType, flowOrderState);

  /// Create a copy of CreditApplicationFlowState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreditApplicationFlowInProgressStateImplCopyWith<
          _$CreditApplicationFlowInProgressStateImpl>
      get copyWith => __$$CreditApplicationFlowInProgressStateImplCopyWithImpl<
          _$CreditApplicationFlowInProgressStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetching,
    required TResult Function(FlowOrderState flowOrderState) inProgress,
    required TResult Function() error,
  }) {
    return inProgress(flowOrderState);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetching,
    TResult? Function(FlowOrderState flowOrderState)? inProgress,
    TResult? Function()? error,
  }) {
    return inProgress?.call(flowOrderState);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetching,
    TResult Function(FlowOrderState flowOrderState)? inProgress,
    TResult Function()? error,
    required TResult orElse(),
  }) {
    if (inProgress != null) {
      return inProgress(flowOrderState);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CreditApplicationFlowFetchingState value)
        fetching,
    required TResult Function(_CreditApplicationFlowInProgressState value)
        inProgress,
    required TResult Function(_CreditApplicationFlowErrorState value) error,
  }) {
    return inProgress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CreditApplicationFlowFetchingState value)? fetching,
    TResult? Function(_CreditApplicationFlowInProgressState value)? inProgress,
    TResult? Function(_CreditApplicationFlowErrorState value)? error,
  }) {
    return inProgress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CreditApplicationFlowFetchingState value)? fetching,
    TResult Function(_CreditApplicationFlowInProgressState value)? inProgress,
    TResult Function(_CreditApplicationFlowErrorState value)? error,
    required TResult orElse(),
  }) {
    if (inProgress != null) {
      return inProgress(this);
    }
    return orElse();
  }
}

abstract class _CreditApplicationFlowInProgressState
    extends CreditApplicationFlowState {
  const factory _CreditApplicationFlowInProgressState(
          {required final FlowOrderState flowOrderState}) =
      _$CreditApplicationFlowInProgressStateImpl;
  const _CreditApplicationFlowInProgressState._() : super._();

  FlowOrderState get flowOrderState;

  /// Create a copy of CreditApplicationFlowState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreditApplicationFlowInProgressStateImplCopyWith<
          _$CreditApplicationFlowInProgressStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CreditApplicationFlowErrorStateImplCopyWith<$Res> {
  factory _$$CreditApplicationFlowErrorStateImplCopyWith(
          _$CreditApplicationFlowErrorStateImpl value,
          $Res Function(_$CreditApplicationFlowErrorStateImpl) then) =
      __$$CreditApplicationFlowErrorStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CreditApplicationFlowErrorStateImplCopyWithImpl<$Res>
    extends _$CreditApplicationFlowStateCopyWithImpl<$Res,
        _$CreditApplicationFlowErrorStateImpl>
    implements _$$CreditApplicationFlowErrorStateImplCopyWith<$Res> {
  __$$CreditApplicationFlowErrorStateImplCopyWithImpl(
      _$CreditApplicationFlowErrorStateImpl _value,
      $Res Function(_$CreditApplicationFlowErrorStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditApplicationFlowState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CreditApplicationFlowErrorStateImpl
    extends _CreditApplicationFlowErrorState {
  const _$CreditApplicationFlowErrorStateImpl() : super._();

  @override
  String toString() {
    return 'CreditApplicationFlowState.error()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreditApplicationFlowErrorStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fetching,
    required TResult Function(FlowOrderState flowOrderState) inProgress,
    required TResult Function() error,
  }) {
    return error();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fetching,
    TResult? Function(FlowOrderState flowOrderState)? inProgress,
    TResult? Function()? error,
  }) {
    return error?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fetching,
    TResult Function(FlowOrderState flowOrderState)? inProgress,
    TResult Function()? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CreditApplicationFlowFetchingState value)
        fetching,
    required TResult Function(_CreditApplicationFlowInProgressState value)
        inProgress,
    required TResult Function(_CreditApplicationFlowErrorState value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CreditApplicationFlowFetchingState value)? fetching,
    TResult? Function(_CreditApplicationFlowInProgressState value)? inProgress,
    TResult? Function(_CreditApplicationFlowErrorState value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CreditApplicationFlowFetchingState value)? fetching,
    TResult Function(_CreditApplicationFlowInProgressState value)? inProgress,
    TResult Function(_CreditApplicationFlowErrorState value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _CreditApplicationFlowErrorState
    extends CreditApplicationFlowState {
  const factory _CreditApplicationFlowErrorState() =
      _$CreditApplicationFlowErrorStateImpl;
  const _CreditApplicationFlowErrorState._() : super._();
}
