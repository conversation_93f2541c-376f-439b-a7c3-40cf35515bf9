part of 'credit_application_delegate.dart';

class CreditCardApplicationDelegate extends CreditApplicationDelegateBase {
  static const _productType = SmeCreditProductType.creditCard;

  final ChangeHomeTabChannel _changeHomeTabChannel;

  const CreditCardApplicationDelegate({
    required super.applicationId,
    required super.navigationProvider,
    required super.localizations,
    required super.interactor,
    required super.featureToggleProvider,
    required ChangeHomeTabChannel changeHomeTabChannel,
  }) : _changeHomeTabChannel = changeHomeTabChannel;

  @override
  Future<void> handleApplicationCompletion() async {
    return _interactor
        .submitCreditAgreement(
          applicationId: _applicationId,
          productType: _productType,
        )
        .toStream()
        .doOnData(_onAgreementSubmittedAction)
        .complete();
  }

  @override
  FlowOrderState determineFlowOrderState(CreditApplication application) {
    final decision = application.prioritizedApprovedDecision;
    if (decision == null) return super.determineFlowOrderState(application);

    // Triggered only after decision is made
    return CreditCardFlowOrderState.initializeAfterDecision(
      application: application,
      featureToggleProvider: _featureToggleProvider,
    );
  }

  Future<void> _onAgreementSubmittedAction([void _]) async {
    await _navigationProvider.navigateTo(
      StatusScreenConfigFactory.success(
        title:
            _localizations.creditAgreementApplicationSubmitSuccessScreenTitle,
        subtitle: _localizations
            .creditAgreementApplicationSubmitSuccessScreenDescription,
        primaryButtonTitle:
            _localizations.creditAgreementApplicationSubmitSuccessScreenCta,
      ),
    );
    _navigationProvider.popUntilFirstRoute();
    _changeHomeTabChannel.addEventToStream(
      const ChangeHomeTabEvent.triggerCardSpendingSwitcherCoachMark(),
    );
  }

  @override
  Future<void> handlePostCompletionAccess() async {
    /// Credit card application is completed after agreement submission.
    /// So no further steps required.
    return;
  }
}
