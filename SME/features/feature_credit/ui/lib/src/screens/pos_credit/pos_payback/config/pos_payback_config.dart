import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/pos_account_determinator/pos_account_state/pos_account_state.dart';

part 'pos_payback_config.freezed.dart';

@freezed
class PosPaybackConfig with _$PosPaybackConfig {
  const factory PosPaybackConfig({
    required BasePosAccountState posAccountState,
  }) = _PosPaybackConfig;

  const factory PosPaybackConfig.toCloseAccount({
    required BasePosAccountState posAccountState,
  }) = _PosPaybackCloseAccountConfig;

  const PosPaybackConfig._();

  bool get isCloseAccount => map((value) => false, toCloseAccount: (_) => true);
}
