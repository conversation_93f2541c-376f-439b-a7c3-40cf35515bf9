import 'package:account_feature_api/account_feature_api.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/debit_account_selector_bottomsheet/config/debit_account_selector_bottomsheet_config.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/debit_account_selector_bottomsheet/debit_account_selector_bottomsheet.dart';
import 'package:wio_feature_credit_ui/src/flows/credit_application/common/credit_application_flow_handler.dart';
import 'package:wio_feature_credit_ui/src/flows/credit_application/enhanced/handler/application_update_request.dart';
import 'package:wio_feature_credit_ui/src/handlers/credit_error_handler/credit_error_handler.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/widgets/debit_account_selector/config/debit_account_selector_config.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/widgets/debit_account_selector/cubit/debit_account_selector_state.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

class DebitAccountSelectorCubit extends BaseCubit<DebitAccountSelectorState> {
  final AccountInteractor _accountInteractor;
  final CreditAccountInteractor _creditAccountInteractor;
  final ResponsiveDialogProvider _responsiveDialogProvider;
  final CreditErrorHandler _errorHandler;
  final DebitAccountSelectorConfig _config;
  final CreditLocalizations _creditLocalizations;

  DebitAccountSelectorCubit({
    required AccountInteractor accountInteractor,
    required CreditAccountInteractor creditAccountInteractor,
    required ResponsiveDialogProvider responsiveDialogProvider,
    required CreditErrorHandler errorHandler,
    required CreditLocalizations creditLocalizations,
    required DebitAccountSelectorConfig config,
  })  : _accountInteractor = accountInteractor,
        _creditAccountInteractor = creditAccountInteractor,
        _responsiveDialogProvider = responsiveDialogProvider,
        _errorHandler = errorHandler,
        _creditLocalizations = creditLocalizations,
        _config = config,
        super(const DebitAccountSelectorState.loading());

  Future<void> init() async {
    final accounts = await _accountInteractor.getAccounts().toList();

    final selectedAccountId = _config.map(
      account: (accountConfig) => accountConfig.posAccountState.debitAccountId,
      application: (applicationConfig) => applicationConfig.settlementAccountId,
    );

    final aedAccounts = accounts
        .where(
          (account) => account.availableBalanceMoney.currency == Currency.aed,
        )
        .toList();

    final currentAccount = aedAccounts.firstWhere(
      (account) => account.id == selectedAccountId,
      orElse: () => aedAccounts.first,
    );

    safeEmit(
      DebitAccountSelectorState.idle(
        accounts: aedAccounts,
        selectedAccount: currentAccount,
      ),
    );
  }

  Future<void> changeAccount() async {
    await state.mapOrNull(
      idle: (idleState) async {
        try {
          final account =
              await _responsiveDialogProvider.showBottomSheetOrDialog<Account>(
            content: DebitAccountSelectorBottomSheet(
              config: DebitAccountSelectorBottomSheetConfig(
                accounts: idleState.accounts,
                selectedAccount: idleState.selectedAccount,
              ),
            ),
            config: const ResponsiveModalConfig(
              featureName: CreditFeatureNavigationConfig.name,
            ),
          );

          if (account == null) return;

          safeEmit(const DebitAccountSelectorState.loading());
          await _changeAccount(account);
          safeEmit(idleState.copyWith(selectedAccount: account));

          _responsiveDialogProvider.showResponsiveToastMessage(
            NotificationToastMessageConfiguration.success(
              _creditLocalizations.debitAccountUpdateSuccess,
            ),
          );
        } on Object catch (error, stackTrace) {
          _errorHandler.handleError(error, stackTrace: stackTrace);
          safeEmit(idleState);
        }
      },
    );
  }

  Future<void> _changeAccount(Account account) {
    return _config.map(
      account: (it) => _changeDebitAccount(
        account,
        it.posAccountState.account.id,
      ),
      application: (it) => _changeDebitAccountApplicationFlow(
        account,
        it.applicationId,
        it.flowHandler,
        it.productType,
      ),
    );
  }

  Future<void> _changeDebitAccount(
    Account selectedAccount,
    String creditAccountId,
  ) {
    return _creditAccountInteractor.updatePreferences(
      accountId: creditAccountId,
      preferences: LoanPreferences(
        settlementAccountId: selectedAccount.id,
      ),
    );
  }

  Future<void> _changeDebitAccountApplicationFlow(
    Account selectedAccount,
    String applicationId,
    CreditApplicationFlowHandler flowHandler,
    SmeCreditProductType productType,
  ) {
    return flowHandler.updateApplicatonData(
      ApplicationUpdateRequest(
        applicationId: applicationId,
        data: CreditApplicationInputData(
          productPreferences: [
            ProductPreferences(
              productType: productType,
              settlementAccountId: selectedAccount.id,
            ),
          ],
        ),
      ),
    );
  }

  @override
  String toString() => 'DebitAccountSelectorCubit';
}
