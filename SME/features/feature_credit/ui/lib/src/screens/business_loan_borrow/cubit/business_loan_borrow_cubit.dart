import 'package:account_feature_api/account_feature_api.dart';
import 'package:collection/collection.dart';
import 'package:feature_login_api/feature_login_api.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ui/ui.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/src/flows/credit_application/enhanced/handler/application_update_request.dart';
import 'package:wio_feature_credit_ui/src/handlers/credit_error_handler/credit_error_handler.dart';
import 'package:wio_feature_credit_ui/src/screens/business_loan_borrow/config/business_loan_borrow_config.dart';
import 'package:wio_feature_credit_ui/src/screens/business_loan_borrow/cubit/business_loan_borrow_state.dart';
import 'package:wio_feature_credit_ui/src/screens/business_loan_borrow/dialogs/loan_processing_fee_info.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/feature_loan_ui.dart';

class BusinessLoanBorrowCubit extends BaseCubit<BusinessLoanBorrowState> {
  final LoginInteractor _loginInteractor;
  final CreditApplicationInteractor _creditApplicationInteractor;
  final ResponsiveDialogProvider _responsiveDialogProvider;
  final AccountInteractor _accountInteractor;
  final LoanInteractor _loanInteractor;
  final CreditErrorHandler _errorHandler;
  final NavigationProvider _navigationProvider;
  final BusinessLoanBorrowConfig _config;

  BusinessLoanBorrowCubit({
    required LoginInteractor loginInteractor,
    required CreditApplicationInteractor creditApplicationInteractor,
    required ResponsiveDialogProvider responsiveDialogProvider,
    required AccountInteractor accountInteractor,
    required LoanInteractor loanInteractor,
    required CreditErrorHandler errorHandler,
    required NavigationProvider navigationProvider,
    required BusinessLoanBorrowConfig config,
  })  : _loginInteractor = loginInteractor,
        _creditApplicationInteractor = creditApplicationInteractor,
        _responsiveDialogProvider = responsiveDialogProvider,
        _accountInteractor = accountInteractor,
        _loanInteractor = loanInteractor,
        _errorHandler = errorHandler,
        _navigationProvider = navigationProvider,
        _config = config,
        super(const BusinessLoanBorrowState.loading());

  Future<void> init() async {
    final applicationId = _config.applicationId;
    try {
      final (userDetails, application, currentAccount) = await (
        _loginInteractor.getUserDetails(),
        _getApplication(applicationId),
        _getCurrentAccount(),
      ).wait;

      final decisions = application.creditDecisions.loanDecisions;

      // Should not happen if user has a valid loan offer.
      // Just to be sure incase of some exception.
      if (decisions.isEmpty) {
        safeEmit(
          BusinessLoanBorrowState.error(
            applicationId: applicationId,
          ),
        );
      }

      /// Checking for existing product preferences.
      ///
      /// Use the selected loan term and amount if
      /// business loan pereferences are already present
      final businessLoanPreferences = application.currentFlowProductPreferences;
      final selectedLoanDecision =
          _getPreviousLoanPreferences(businessLoanPreferences, decisions);

      // The decision selected by default
      final selectedDecision = selectedLoanDecision ?? decisions[0];
      final selectedAmount = businessLoanPreferences?.selectedAmount ??
          selectedDecision.availableAmount;

      final disbursementEvaluation = await _loanInteractor.evaluateDisbursement(
        request: LoanDisbursementEvaluationRequest(
          amount: selectedAmount,
          loanPeriod: selectedDecision.loanTerm,
          applicationId: applicationId,
          loanProductIdentifier: LoanProductIdentifier.businessLoan,
        ),
      );

      safeEmit(
        BusinessLoanBorrowState.idle(
          application: application,
          userName: userDetails.name,
          loanDecisions: decisions,
          selectedDecision: selectedDecision,
          currentAccountBalance: currentAccount.balanceMoney,
          disbursementEvaluation: disbursementEvaluation,
          selectedAmount: selectedAmount,
        ),
      );
    } on Object catch (_) {
      safeEmit(BusinessLoanBorrowState.error(applicationId: applicationId));
    }
  }

  void selectLoanTerm(LoanDecision selectedDecision) {
    state.mapOrNull(
      idle: (it) {
        // need to set input as valid if user had set
        // some invalid input on previous loan term
        safeEmit(it.copyWith(isInputValid: true));

        _evaluateInstallments(
          selectedDecision,
          selectedDecision.availableAmount,
        );
      },
    );
  }

  void selectLoanAmount(int selectedAmount) {
    state.mapOrNull(
      idle: (it) {
        _evaluateInstallments(
          it.selectedDecision,
          Money.fromNumWithCurrency(
            selectedAmount,
            it.availableAmount.currency,
          ),
        );
      },
    );
  }

  void handleInvalidInput() {
    state.mapOrNull(idle: (it) => safeEmit(it.copyWith(isInputValid: false)));
  }

  void setInputValid() {
    state.mapOrNull(idle: (it) => safeEmit(it.copyWith(isInputValid: true)));
  }

  void submit() {
    state.mapOrNull(
      idle: (it) async {
        try {
          safeEmit(it.toEvaluatingFromIdle());

          await _config.creditApplicationFlowHandler.toNextStage(
            updateRequest: ApplicationUpdateRequest(
              applicationId: _config.applicationId,
              data: CreditApplicationInputData(
                productPreferences: [
                  ProductPreferences(
                    selectedAmount: it.selectedAmount,
                    loanPeriod: it.selectedDecision.loanTerm,
                    productType: SmeCreditProductType.businessLoan,
                  ),
                ],
              ),
            ),
          );

          safeEmit(it.toIdleFromEvaluating());
        } on Object catch (error) {
          _errorHandler.handleError(error);
          safeEmit(it);
        }
      },
    );
  }

  void showLoanAmountInformation() {
    state.mapOrNull(
      idle: (_) {
        _responsiveDialogProvider.showBottomSheetOrDialog<void>(
          content: const LoanAmountSelectionInfoDialog(),
          config: const ResponsiveModalConfig(
            featureName: CreditFeatureNavigationConfig.name,
          ),
        );
      },
    );
  }

  void showLoanProcessingFeeInformation() {
    state.mapOrNull(
      idle: (_) {
        _responsiveDialogProvider.showBottomSheetOrDialog<void>(
          content: const LoanProcessingFeeInfoDialog(),
          config: const ResponsiveModalConfig(
            featureName: CreditFeatureNavigationConfig.name,
          ),
        );
      },
    );
  }

  void goBack() {
    _navigationProvider.goBack();
  }

  LoanDecision? _getPreviousLoanPreferences(
    ProductPreferences? preferences,
    List<LoanDecision> decisions,
  ) {
    final selectedLoanTerm = preferences?.loanPeriod;

    final selectedLoanDecision = decisions.firstWhereOrNull(
      (decision) => decision.loanTerm == selectedLoanTerm,
    );

    return selectedLoanDecision;
  }

  void _evaluateInstallments(
    LoanDecision selectedDecision,
    Money selectedAmount,
  ) {
    state.mapOrNull(
      idle: (it) {
        _loanInteractor
            .evaluateDisbursement(
              request: LoanDisbursementEvaluationRequest(
                amount: selectedAmount,
                loanPeriod: selectedDecision.loanTerm,
                applicationId: _config.applicationId,
                loanProductIdentifier: LoanProductIdentifier.businessLoan,
              ),
            )
            .asStream()
            .doOnListen(
              () => safeEmit(
                it.toEvaluatingFromIdle(
                  selectedDecision: selectedDecision,
                  selectedAmount: selectedAmount,
                ),
              ),
            )
            .withError(
              (error) => _handleEvaluationError(
                error,
                it.selectedDecision,
                it.selectedAmount,
              ),
            )
            .doOnData(_handleEvaluationResult)
            .complete();
      },
    );
  }

  void _handleEvaluationResult(Schedule result) {
    state.mapOrNull(
      idle: (it) {
        safeEmit(it.toIdleFromEvaluating(disbursementEvaluation: result));
      },
    );
  }

  void _handleEvaluationError(
    Object? error,
    LoanDecision? previousSelectedDecision,
    Money? previousSelectedAmount,
  ) {
    state.mapOrNull(
      idle: (it) {
        safeEmit(
          it.toIdleFromEvaluating(
            selectedDecision: previousSelectedDecision,
            selectedAmount: previousSelectedAmount,
          ),
        );
        _errorHandler.handleError(error);
      },
    );
  }

  Future<CreditApplication> _getApplication(String applicationId) =>
      _creditApplicationInteractor.getApplication(applicationId);

  Future<Account> _getCurrentAccount() => _accountInteractor.getAccount();

  @override
  String toString() => 'BusinessLoanBorrowCubit';
}
