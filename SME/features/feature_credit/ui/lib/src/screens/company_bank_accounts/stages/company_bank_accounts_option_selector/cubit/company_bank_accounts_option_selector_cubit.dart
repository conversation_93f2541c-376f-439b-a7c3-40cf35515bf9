import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/cubit/base_cubit.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/bank_accounts_confirmation_bottomsheet/bank_accounts_confirmation_bottomsheet.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/bank_accounts_confirmation_bottomsheet/config/bank_accounts_confirmation_bottomsheet_config.dart';
import 'package:wio_feature_credit_ui/src/common/mixins/duration_timer.dart';
import 'package:wio_feature_credit_ui/src/handlers/credit_error_handler/credit_error_handler.dart';
import 'package:wio_feature_credit_ui/src/navigation/configs/bottom_sheets/bank_accounts_confirmation_bottomsheet_navigation_config.dart';
import 'package:wio_feature_credit_ui/src/screens/company_bank_accounts/config/company_bank_accounts_input_config.dart';
import 'package:wio_feature_credit_ui/src/screens/company_bank_accounts/delegates/company_bank_accounts_input_delegate.dart';
import 'package:wio_feature_credit_ui/src/screens/company_bank_accounts/flow/company_bank_accounts_input.dart';

part 'company_bank_accounts_option_selector_cubit.freezed.dart';
part 'company_bank_accounts_option_selector_state.dart';

class CompanyBankAccountsOptionSelectorCubit
    extends BaseCubit<CompanyBankAccountsOptionSelectorState>
    with DurationTimer {
  final CompanyBankAccountsInputDelegate _delegate;
  final Logger _logger;
  final ResponsiveDialogProvider _responsiveDialogProvider;
  final CreditErrorHandler _errorHandler;
  final CreditLocalizations _localizations;
  final CreditAnalytics _analytics;

  CompanyBankAccountsOptionSelectorCubit({
    required CompanyBankAccountsInputConfig config,
    required CompanyBankAccountsInputDelegate delegate,
    required Logger logger,
    required ResponsiveDialogProvider responsiveDialogProvider,
    required CreditErrorHandler errorHandler,
    required CreditLocalizations localizations,
    required CreditAnalytics analytics,
  })  : _delegate = delegate,
        _logger = logger,
        _responsiveDialogProvider = responsiveDialogProvider,
        _errorHandler = errorHandler,
        _localizations = localizations,
        _analytics = analytics,
        super(
          CompanyBankAccountsOptionSelectorState.ready(
            selectedOption: config.hasOtherBankAccounts
                ? CompanyBankAccountsOption.otherBankAccounts
                : config.application.stage is RecapScreen
                    ? CompanyBankAccountsOption.onlyWioAccounts
                    : null,
          ),
        ) {
    startTimer();
  }

  int _retries = 0;

  void onSelectOption(CompanyBankAccountsOption selectedOption) {
    state.mapOrNull(
      ready: (readyState) {
        final updatedState = readyState.copyWith(
          selectedOption: selectedOption,
        );

        safeEmit(updatedState);
      },
    );
  }

  Future<void> onSubmit() async {
    try {
      await state.mapOrNull(
        ready: (readyState) async {
          final selectedOption = readyState.selectedOption;

          if (selectedOption == null) {
            _logger.error('selectedOption Cannot Be Null');

            _showErrorToast(
              _localizations.creditBankAccountsNoOptionSelectedError,
            );

            return;
          }

          final updatedState = readyState.toProcessing();

          safeEmit(updatedState);

          switch (selectedOption) {
            case CompanyBankAccountsOption.otherBankAccounts:
              throw UnimplementedError();
            case CompanyBankAccountsOption.onlyWioAccounts:
              await _onSubmitOnlyWioAccountsOption();
              safeEmit(readyState);
              break;
          }
        },
      );
    } on Object catch (error, stackTrace) {
      _logger.error(
        'Error submitting iban',
        error: error,
        stackTrace: stackTrace,
      );

      _retries++;
    }
  }

  Future<void> _onSubmitOnlyWioAccountsOption() async {
    final result = await _responsiveDialogProvider
        .showBottomSheetOrDialog<BankAccountsConfirmationBottomsheetResult>(
      content: BankAccountsConfirmationBottomSheet(
        config: BankAccountsConfirmationBottomSheetConfig(
          title: _localizations.creditOnlyWioBankAccountBottomSheetTitle,
          description: _localizations.creditOnlyWioBankAccountBottomSheetBody,
          reviewButtonText:
              _localizations.creditOnlyWioBankAccountBottomSheetCtaChangeText,
        ),
      ),
      config: const ResponsiveModalConfig(
        featureName: CreditFeatureNavigationConfig.name,
      ),
    );

    await result?.mapOrNull(
      confirm: (_) async {
        await _delegate.submitOnlyWioBankAccounts();

        const uiId = CreditUiId.ibanCheck;
        final duration = stopTimer();
        _analytics
          ..applicationStepSubmit(uiId, duration: duration)
          ..ibanSubmit(
            uiId,
            retries: _retries,
            duration: duration,
            pasteUsed:
                false, // There won't be any input of iban for this option
          );
      },
    );
  }

  void _showErrorToast(String message) =>
      _errorHandler.showErrorSnackbar(message: message);

  @override
  String toString() => 'CompanyBankAccountsOptionSelectorCubit';
}
