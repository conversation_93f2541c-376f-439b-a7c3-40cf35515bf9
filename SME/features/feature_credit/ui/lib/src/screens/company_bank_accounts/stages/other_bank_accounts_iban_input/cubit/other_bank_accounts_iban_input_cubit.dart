import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/cubit/base_cubit.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/bank_accounts_confirmation_bottomsheet/bank_accounts_confirmation_bottomsheet.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/bank_accounts_confirmation_bottomsheet/config/bank_accounts_confirmation_bottomsheet_config.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/iban_input_bottomsheet/iban_input_bottomsheet.dart';
import 'package:wio_feature_credit_ui/src/common/mixins/duration_timer.dart';
import 'package:wio_feature_credit_ui/src/handlers/credit_error_handler/credit_error_handler.dart';
import 'package:wio_feature_credit_ui/src/navigation/configs/bottom_sheets/bank_accounts_confirmation_bottomsheet_navigation_config.dart';
import 'package:wio_feature_credit_ui/src/navigation/configs/bottom_sheets/iban_input_bottom_sheet_config.dart';
import 'package:wio_feature_credit_ui/src/screens/company_bank_accounts/config/company_bank_accounts_input_config.dart';
import 'package:wio_feature_credit_ui/src/screens/company_bank_accounts/delegates/company_bank_accounts_input_delegate.dart';

part 'other_bank_accounts_iban_input_cubit.freezed.dart';
part 'other_bank_accounts_iban_input_state.dart';

class OtherBankAccountsIbanInputCubit
    extends BaseCubit<OtherBankAccountsIbanInputState> with DurationTimer {
  final CompanyBankAccountsInputDelegate _delegate;
  final ResponsiveDialogProvider _responsiveDialogProvider;
  final CreditErrorHandler _errorHandler;
  final CreditLocalizations _localization;
  final Logger _logger;
  final CreditAnalytics _analytics;

  OtherBankAccountsIbanInputCubit({
    required CompanyBankAccountsInputConfig config,
    required CompanyBankAccountsInputDelegate delegate,
    required ResponsiveDialogProvider responsiveDialogProvider,
    required CreditErrorHandler errorHandler,
    required CreditLocalizations localization,
    required Logger logger,
    required CreditAnalytics analytics,
  })  : _delegate = delegate,
        _responsiveDialogProvider = responsiveDialogProvider,
        _errorHandler = errorHandler,
        _localization = localization,
        _logger = logger,
        _analytics = analytics,
        super(OtherBankAccountsIbanInputState.ready(ibans: config.ibans)) {
    startTimer();
  }

  bool _pasteUsed = false;
  int _retries = 0;

  Future<void> onAddIban() async {
    await state.mapOrNull(
      ready: (readyState) async {
        final result = await _responsiveDialogProvider
            .showBottomSheetOrDialog<IbanInputBottomSheetResult>(
          content: const IbanInputBottomsheet(),
          config: const ResponsiveModalConfig(
            featureName: CreditFeatureNavigationConfig.name,
          ),
        );

        if (result == null) return;

        final iban = result.iban;
        // If paste is used on any one iban input, we set _pasteUsed as true
        _pasteUsed = _pasteUsed || result.pasteUsed;
        final idx =
            readyState.ibans.indexWhere((ibanModel) => ibanModel.iban == iban);

        if (idx != -1) {
          return;
        }

        final ibans = readyState.ibans;

        final updatedState = readyState.copyWith(
          ibans: [...ibans, Iban(iban: iban)],
        );

        safeEmit(updatedState);
      },
    );
  }

  Future<void> onRemoveIban(Iban deletedIban) async {
    await state.mapOrNull(
      ready: (readyState) async {
        final ibans = List.of(readyState.ibans);

        final succeed = ibans.remove(deletedIban);

        if (succeed) {
          final updatedState = readyState.copyWith(ibans: ibans);

          safeEmit(updatedState);
        }
      },
    );
  }

  Future<void> onSubmit() async {
    await state.mapOrNull(
      ready: (readyState) async {
        try {
          safeEmit(readyState.toProcessing());
          final result = await _onSubmitOtherBankAccountsOption();

          await result?.mapOrNull(
            confirm: (_) async {
              await _delegate.submitOtherBankAccounts(readyState.ibans);

              const uiId = CreditUiId.ibanCheck;
              final duration = stopTimer();
              _analytics
                ..applicationStepSubmit(uiId, duration: duration)
                ..ibanSubmit(
                  uiId,
                  retries: _retries,
                  duration: duration,
                  pasteUsed: _pasteUsed,
                );
            },
          );
        } on Object catch (e, stackTrace) {
          _retries++;
          _logger.error(
            'Something went wrong while submitting other bank accounts',
            error: e,
            stackTrace: stackTrace,
          );

          _showErrorToast(_localization.creditUpdateOtherBankAccountsIbanError);
        } finally {
          _analytics.applicationStepSubmit(CreditUiId.ibanCheck);
          safeEmit(readyState);
        }
      },
    );
  }

  Future<BankAccountsConfirmationBottomsheetResult?>
      _onSubmitOtherBankAccountsOption() async {
    return _responsiveDialogProvider
        .showBottomSheetOrDialog<BankAccountsConfirmationBottomsheetResult>(
      content: BankAccountsConfirmationBottomSheet(
        config: BankAccountsConfirmationBottomSheetConfig(
          title: _localization.creditOtherBankAccountsBottomSheetTitle,
          description:
              _localization.creditOtherBankAccountsBottomSheetDescription,
          reviewButtonText: _localization.reviewCta,
        ),
      ),
      config: const ResponsiveModalConfig(
        featureName: CreditFeatureNavigationConfig.name,
      ),
    );
  }

  void _showErrorToast(String message) {
    _errorHandler.showErrorSnackbar(message: message);
  }

  @override
  String toString() => 'OtherBankAccountsIbanInputCubit';
}
