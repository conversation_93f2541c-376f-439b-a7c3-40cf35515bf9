import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:logging_api/logging.dart';
import 'package:ui/ui.dart';
import 'package:wio_common_feature_context_faq_api/wio_common_feature_context_faq_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/src/navigation/configs/screens/credit_agreement_page_navigation_config.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_agreement/config/credit_agreement_config.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_document_viewer/delegate/sme_credit_document_viewer_delegate_config.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

part 'easy_cash_manage_cubit.freezed.dart';
part 'easy_cash_manage_state.dart';

class EasyCashManageCubit extends BaseCubit<EasyCashManageState> {
  final CreditAccountInteractor _creditAccountInteractor;
  final Logger _logger;
  final NavigationProvider _navigationProvider;
  final FeatureToggleProvider _featureToggleProvider;
  final CreditApplicationInteractor _creditApplicationInteractor;

  EasyCashManageCubit({
    required CreditAccountInteractor creditAccountInteractor,
    required Logger logger,
    required NavigationProvider navigationProvider,
    required FeatureToggleProvider featureToggleProvider,
    required CreditApplicationInteractor creditApplicationInteractor,
  })  : _creditAccountInteractor = creditAccountInteractor,
        _logger = logger,
        _navigationProvider = navigationProvider,
        _featureToggleProvider = featureToggleProvider,
        _creditApplicationInteractor = creditApplicationInteractor,
        super(const EasyCashManageState.loading());

  bool get isCreditCardApplicationFlowSimplified => _featureToggleProvider
      .get(SmeMobileCreditFeatureToggles.isCreditCardApplicationFlowSimplified);

  @override
  String toString() => 'EasyCashManageCubit';

  Future<void> init(String accountId) async {
    try {
      final account = await _getAccount(accountId);
      final lendingApplicationId = account.lendingApplicationId;
      final data = await Future.wait(
        [
          _getAutopayment(account.id),
          if (lendingApplicationId != null)
            _getLendingApplication(lendingApplicationId),
        ],
        eagerError: true,
      );

      final autoPayment = data[0] as Autopay;
      CreditApplication? application;

      if (account.lendingApplicationId != null) {
        application = data[1] as CreditApplication;
      }

      if (isClosed) {
        return;
      }

      safeEmit(
        EasyCashManageState.ready(
          account: account,
          autoPayment: autoPayment,
          lendingApplication: application,
        ),
      );
    } on Object {
      safeEmit(EasyCashManageState.error(accountId: accountId));
    }
  }

  void goToStatements() {
    state.mapOrNull(
      ready: (readyState) {
        final account = readyState.account;

        _navigationProvider.push(
          LoanStatementsPageNavigationConfig(
            loanAccountId: account.id,
            loanProductIdentifier: account.productIdentifier,
          ),
        );
      },
    );
  }

  void goToCreditAgreement() {
    state.mapOrNull(
      ready: (readyState) {
        final applicationId = readyState.account.lendingApplicationId;
        if (applicationId == null) return;

        _navigationProvider.push(
          CreditAgreementPageNavigationConfig(
            config: CreditAgreementConfig.readOnly(
              applicationId: applicationId,
              productType: readyState.account.productType,
            ),
          ),
        );
      },
    );
  }

  void goToKfs() {
    state.mapOrNull(
      ready: (readyState) {
        final applicationId = readyState.account.lendingApplicationId;
        if (applicationId == null) return;

        _navigationProvider.push(
          LoanDocumentPageNavigationConfig(
            delegateConfig: SmeCreditDocumentViewerDelegateApplicationConfig(
              type: ViewerDocumentType.creditCardKeyFactStatement,
              applicationId: applicationId,
              sourceUiId: CreditUiId.manageEasyCash,
              productIdentifier: readyState.account.productIdentifier,
            ),
          ),
        );
      },
    );
  }

  void showFaq() {
    state.mapOrNull(
      ready: (readyState) {
        _navigationProvider.showBottomSheet(
          const ContextFaqBottomSheetNavigationConfig(
            tags: [ContextFaqTags.easyCash],
            fromScreen: CreditFeatureNavigationConfig.name,
          ),
        );
      },
    );
  }

  Future<LoanAccount> _getAccount(String accountId) {
    try {
      return _creditAccountInteractor.getAccountById(accountId);
    } on Object catch (e, stackTrace) {
      _logger.error(
        'Something went wrong while fetching account',
        error: e,
        stackTrace: stackTrace,
      );

      rethrow;
    }
  }

  Future<Autopay> _getAutopayment(String accountId) async {
    try {
      return _creditAccountInteractor.getAutopayInfo(accountId);
    } on Object catch (e, stackTrace) {
      _logger.error(
        'Something went wrong while fetching autopayment info',
        error: e,
        stackTrace: stackTrace,
      );

      rethrow;
    }
  }

  Future<CreditApplication> _getLendingApplication(
    String applicationId,
  ) async {
    try {
      return _creditApplicationInteractor.getApplication(applicationId);
    } on Object catch (e, stackTrace) {
      _logger.error(
        'Something went wrong while fetching loan account',
        error: e,
        stackTrace: stackTrace,
      );

      rethrow;
    }
  }
}
