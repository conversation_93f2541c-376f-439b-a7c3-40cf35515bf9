import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/ui.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/src/flows/credit_application/enhanced/handler/application_update_request.dart';
import 'package:wio_feature_credit_ui/src/screens/loan_type_selection/config/loan_type_selection_config.dart';
import 'package:wio_feature_credit_ui/src/screens/loan_type_selection/secured_loan_confirmation/dialogs/secured_loan_assignment_letter_dialog.dart';
import 'package:wio_feature_credit_ui/src/screens/loan_type_selection/secured_loan_confirmation/dialogs/secured_loan_confirmation_dialog.dart';

class SecuredLoanConfirmationCubit extends BaseCubit<void> {
  final LoanTypeSelectionConfig _config;
  final ResponsiveDialogProvider _responsiveDialogProvider;

  SecuredLoanConfirmationCubit({
    required LoanTypeSelectionConfig config,
    required ResponsiveDialogProvider responsiveDialogProvider,
  })  : _config = config,
        _responsiveDialogProvider = responsiveDialogProvider,
        super(null);

  void showAssignmentLetterInfo() {
    _responsiveDialogProvider.showBottomSheetOrDialog<void>(
      content: const SecuredLoanAssignmentLetterDialog(),
      config: const ResponsiveModalConfig(
        featureName: CreditFeatureNavigationConfig.name,
      ),
    );
  }

  Future<void> showConfirmationDialog() async {
    final isSecuredLoanConfirmed =
        await _responsiveDialogProvider.showBottomSheetOrDialog<bool>(
      content: const SecuredLoanConfirmationDialog(),
      config: const ResponsiveModalConfig(
        featureName: CreditFeatureNavigationConfig.name,
      ),
    );

    if (isSecuredLoanConfirmed == null) return;

    final selectedLoanType =
        isSecuredLoanConfirmed ? LoanType.secured : LoanType.unsecured;

    await _config.applicationFlowHandler.toNextStage(
      updateRequest: ApplicationUpdateRequest(
        applicationId: _config.application.id,
        data: CreditApplicationInputData(loanType: selectedLoanType),
      ),
    );
  }

  @override
  String toString() => 'SecuredLoanConfirmationCubit';
}
