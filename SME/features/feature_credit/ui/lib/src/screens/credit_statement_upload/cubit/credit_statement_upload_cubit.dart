import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_file_picker_api/file_picker_api.dart';
import 'package:wio_common_file_picker_ui/file_picker_ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_credit_ui/src/common/mixins/duration_timer.dart';
import 'package:wio_feature_credit_ui/src/handlers/credit_error_handler/credit_error_handler.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_statement_upload/delegates/credit_statement_upload_delegate.dart';
import 'package:wio_feature_status_view_api/feature_status_view.dart';

part 'credit_statement_upload_cubit.freezed.dart';
part 'credit_statement_upload_state.dart';

// The max. file size in bytes that can be uploaded
const maxFileSizeLimit = 1e7;

class CreditStatementUploadCubit extends BaseCubit<CreditStatementUploadState>
    with DurationTimer {
  final CreditApplication _application;
  final Logger _logger;
  final CreditStatementUploadDelegate _delegate;
  final CreditLocalizations _localizations;
  final NavigationProvider _navigationProvider;
  final CreditErrorHandler _errorHandler;
  final CreditApplicationInteractor _interactor;
  final CreditAnalytics _analytics;
  final ResponsiveDialogProvider _responsiveDialogProvider;

  CreditStatementUploadCubit({
    required CreditApplication application,
    required Logger logger,
    required CreditStatementUploadDelegate delegate,
    required CreditLocalizations localizations,
    required NavigationProvider navigationProvider,
    required CreditErrorHandler errorHandler,
    required CreditApplicationInteractor interactor,
    required CreditAnalytics analytics,
    required ResponsiveDialogProvider responsiveDialogProvider,
  })  : _application = application,
        _logger = logger,
        _delegate = delegate,
        _localizations = localizations,
        _navigationProvider = navigationProvider,
        _errorHandler = errorHandler,
        _interactor = interactor,
        _analytics = analytics,
        _responsiveDialogProvider = responsiveDialogProvider,
        super(const CreditStatementUploadState.loading());

  Future<void> init() async {
    try {
      startTimer();
      _interactor.observeApplication(_application.id).listenSafe(
            this,
            onData: _handleApplicationStreamData,
            onError: _onError,
          );

      final result = await Future.wait([
        _delegate.getListOfStatementDocs(),
        _interactor.getProductInfo(
          productType: SmeCreditProductType.creditCard,
        ),
      ]);
      final documents = result.first as List<StatementDocument>;
      final productInfo = result.last as CreditProductInfo;

      safeEmit(
        CreditStatementUploadState.ready(
          numberOfRequiredStatements: _delegate.getNumberOfRequiredDocuments(),
          statementExampleUrl: productInfo.vatTemplateUrl,
          documents: documents,
        ),
      );
    } on Object catch (error) {
      await _onError(error);
    }
  }

  void openVatExample() {
    state.mapOrNull(
      ready: (readyState) {
        _navigationProvider.push(
          CreditPdfViewerNavigationConfig(
            config: CreditPdfViewerConfig.url(
              pageTitle: _localizations.vatStatementSampleTitle,
              documentUrl: readyState.statementExampleUrl,
              sourceUiId: _uiIdForStatementType,
              documentType: _delegate.statementType,
              productType: _application.currentFlowProductType ??
                  SmeCreditProductType.credit,
            ),
          ),
        );
      },
    );
  }

  Future<void> submit() async {
    await state.mapOrNull(
      ready: (readyState) async {
        try {
          safeEmit(readyState.toProcessing());

          await _delegate.onSubmit();

          _analyticsOnSubmit();

          safeEmit(readyState);

          _logger.info(
            '''Credit statements(${_delegate.statementType}) uploaded successfully.''',
          );
        } on Object catch (e, stackTrace) {
          safeEmit(readyState);
          _errorHandler.handleError(e, stackTrace: stackTrace);
        }
      },
    );
  }

  Future<void> openFilePicker() async {
    await state.mapOrNull(
      ready: (readyState) async {
        _logger.info('Opening file picker...');

        final file = await _responsiveDialogProvider
            .showBottomSheetOrDialog<CrossPlatformFile>(
          content: CrossPlatformFilePickerBottomSheet(
            model: FilePickerBottomSheetModel(
              bottomSheetTitle: _filePickerBottomSheetTitle,
              allowedExtensions: ['pdf'],
              showCameraOption: false,
              showGalleryOption: false,
            ),
            onOptionSelected: (file) {
              _navigationProvider.goBack(file);
            },
          ),
          config: const ResponsiveModalConfig(
            featureName: CreditFeatureNavigationConfig.name,
          ),
        );

        if (file == null) {
          return;
        }

        final (fileName, fileData) = file.map(
          mobile: (value) => (
            value.file.path.split('/').last,
            value.file.readAsBytesSync(),
          ),
          web: (value) => (value.fileName, value.bytes),
        );

        final fileAlreadyAdded = state.statementsDocuments.any(
          (statementsDocument) => statementsDocument.map(
            fromLocal: (document) => document.name == fileName,
            fromRemote: (_) => false,
          ),
        );

        if (fileAlreadyAdded) {
          _errorHandler.showErrorSnackbar(
            message: 'Document already added. Please select another document.',
          );

          return;
        }

        final fileSize = fileData.length;

        if (fileSize > maxFileSizeLimit) {
          const maxSizeInMB = maxFileSizeLimit ~/ 1e6;
          _errorHandler.showErrorSnackbar(
            message: _localizations.fileSizeExceedsLimit('$maxSizeInMB'),
          );

          return;
        }

        try {
          final document = await _delegate.uploadDocument(
            document: StatementDocument.fromLocal(
              fileBytes: fileData,
              name: fileName,
              size: fileSize,
              uploadPercentage: 0,
            ),
            onProgress: (uploaded, total) {
              final uploadPercentage = (uploaded * 100) ~/ total;
              safeEmit(
                CreditStatementUploadState.processing(
                  numberOfRequiredStatements:
                      readyState.numberOfRequiredStatements,
                  statementExampleUrl: readyState.statementExampleUrl,
                  documents: [
                    ...readyState.documents,
                    StatementDocument.fromLocal(
                      fileBytes: fileData,
                      name: fileName,
                      size: fileSize,
                      uploadPercentage: uploadPercentage,
                    ),
                  ],
                ),
              );
            },
          );

          safeEmit(
            readyState.copyWith(
              documents: [
                ...readyState.documents,
                document,
              ],
            ),
          );
        } on Object catch (error) {
          await _onError(error);
        }
      },
    );
  }

  Future<void> navigateToGenericErrorScreen() async {
    _logger.debug('Attempting to navigate to generic error screen...');

    await state.mapOrNull(
      error: (_) async {
        _logger.debug('Navigating to generic error screen...');

        await _navigationProvider.navigateTo(
          StatusViewFeatureNavigationConfig.error(
            title: _localizations.creditGenericErrorScreenTitle,
            subTitleModel: CompanyRichTextModel(
              text: _localizations.creditGenericErrorScreenDescription,
              normalStyle: CompanyTextStylePointer.h4,
              normalTextColor: CompanyColorPointer.primary2,
              maxLines: 10,
            ),
            bottomConfig: StatusPageBottomConfig.customButton(
              model: ButtonModel(
                title: _localizations.creditGenericErrorScreenCtaText,
                negative: true,
              ),
              reversed: true,
            ),
          ),
          replace: true,
        );

        _navigationProvider.popUntilFirstRoute();
      },
    );
  }

  void removeDocument(StatementDocument document) {
    state.mapOrNull(
      ready: (readyState) async {
        final documents = readyState.documents;

        final updatedDocuments =
            await document.map<Future<List<StatementDocument>>>(
          fromLocal: (localFile) async =>
              _removeLocalFile(documents, localFile),
          fromRemote: (remoteFile) async => _deleteRemoteFile(
            documents,
            remoteFile,
          ),
        );

        safeEmit(
          readyState.copyWith(
            documents: updatedDocuments,
          ),
        );
      },
    );
  }

  Future<List<StatementDocument>> _deleteRemoteFile(
    List<StatementDocument> documents,
    _StatementRemoteDocument remoteDocument,
  ) async {
    await _delegate.deleteDocument(remoteDocument);

    return documents.where((doc) {
      return doc.maybeMap(
        fromRemote: (remoteDoc) {
          if (remoteDoc.id == remoteDocument.id) {
            return false;
          }

          return true;
        },
        orElse: () => true,
      );
    }).toList();
  }

  List<StatementDocument> _removeLocalFile(
    List<StatementDocument> documents,
    _StatementLocalDocument localFile,
  ) {
    return documents.where((doc) {
      return doc.maybeMap(
        fromLocal: (localDoc) {
          if (localDoc.name == localFile.name) {
            return false;
          }

          return true;
        },
        orElse: () => true,
      );
    }).toList();
  }

  @override
  String toString() => 'CreditStatementUploadCubit ${_delegate.statementType}';

  Future<void> _onError(
    Object error, {
    String? message,
  }) async {
    _logger.error(
      message ?? 'Something went wrong',
      error: error,
      stackTrace: StackTrace.current,
    );

    /// in case of error state this page navigates to error page
    /// since this logic handled in cubit listener
    /// if we emit error state before UI builded correctly
    /// it creates weird behavior in UI
    /// to avoid this we wait for a second to build UI correctly
    /// before emitting error state
    await Future<void>.delayed(const Duration(seconds: 1));

    safeEmit(
      const CreditStatementUploadState.error(message: 'INITIALISING_ERROR'),
    );
  }

  Future<void> _handleApplicationStreamData(
    Data<CreditApplication> applicationData,
  ) async {
    final application = applicationData.content;

    if (application == null) return;

    _delegate.updateApplication(application);

    state.mapOrNull(
      ready: (readyState) => safeEmit(
        readyState.copyWith(
          numberOfRequiredStatements: _delegate.getNumberOfRequiredDocuments(),
        ),
      ),
      processing: (processingState) => safeEmit(
        processingState.copyWith(
          numberOfRequiredStatements: _delegate.getNumberOfRequiredDocuments(),
        ),
      ),
    );
  }

  String get _filePickerBottomSheetTitle {
    if (_delegate.statementType ==
        CreditDocumentType.auditedFinancialStatement) {
      return _localizations.selectFileFinancialStatementTitle;
    }

    return _localizations.selectFileBorrowingPowerTitle;
  }

  CreditUiId get _uiIdForStatementType {
    if (_delegate.statementType ==
        CreditDocumentType.auditedFinancialStatement) {
      return CreditUiId.auditedFinancialStatementUpload;
    }

    return CreditUiId.vatStatementUpload;
  }

  void _analyticsOnSubmit() {
    final duration = stopTimer();
    _analytics.applicationStepSubmit(
      _uiIdForStatementType,
      duration: duration,
    );
    switch (_delegate.statementType) {
      case CreditDocumentType.auditedFinancialStatement:
        _analytics.uploadAuditedFinancialStatement(
          CreditUiId.auditedFinancialStatementUpload,
          duration: duration,
        );
      case CreditDocumentType.vatStatement:
        _analytics.uploadVatStatement(
          CreditUiId.vatStatementUpload,
          vatReportingInterval: _application.vatReportingInterval ??
              VatReportingInterval.noReporting,
          duration: duration,
        );
      default:
        break;
    }
  }
}
