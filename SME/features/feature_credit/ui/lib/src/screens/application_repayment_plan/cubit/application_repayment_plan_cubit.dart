import 'dart:async';

import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/ui.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_credit_ui/src/common/mixins/duration_timer.dart';
import 'package:wio_feature_credit_ui/src/handlers/credit_error_handler/credit_error_handler.dart';
import 'package:wio_feature_credit_ui/src/screens/application_repayment_plan/config/application_repayment_plan_config.dart';
import 'package:wio_feature_credit_ui/src/screens/business_loan_borrow/dialogs/loan_processing_fee_info.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/feature_loan_ui.dart';

part 'application_repayment_plan_state.dart';

class ApplicationRepaymentPlanCubit
    extends BaseCubit<ApplicationRepaymentPlanState> with DurationTimer {
  final ApplicationRepaymentPlanConfig _config;
  final LoanInteractor _interactor;
  final Logger _logger;
  final ResponsiveDialogProvider _responsiveDialogProvider;
  final CreditErrorHandler _errorHandler;
  final CreditLocalizations _localizations;

  ApplicationRepaymentPlanCubit({
    required ApplicationRepaymentPlanConfig config,
    required LoanInteractor interactor,
    required Logger logger,
    required ResponsiveDialogProvider responsiveDialogProvider,
    required CreditLocalizations localizations,
    required CreditErrorHandler errorHandler,
  })  : _config = config,
        _interactor = interactor,
        _logger = logger,
        _responsiveDialogProvider = responsiveDialogProvider,
        _localizations = localizations,
        _errorHandler = errorHandler,
        super(const ApplicationRepaymentPlanLoading());

  Future<void> initialize() async {
    try {
      final (selectedAmount, period) = _extractSelectedAmountAndPeriod();
      final schedule = await _interactor.evaluateDisbursement(
        request: LoanDisbursementEvaluationRequest(
          amount: selectedAmount,
          applicationId: _config.application.id,
          loanPeriod: period,
          loanProductIdentifier: LoanProductIdentifier.businessLoan,
        ),
      );

      safeEmit(
        ApplicationRepaymentPlanLoaded(
          schedule: schedule,
          period: period,
        ),
      );
    } on PeriodOrAmountAbsentException {
      _errorHandler.showErrorSnackbar(
        message: _localizations
            .creditApplicationRepaymentPlanPeriodOrAmountNullError,
      );
      safeEmit(const ApplicationRepaymentPlanError());
    } on Object catch (e) {
      _logger.error('Error on ApplicationRepaymentPlanCubit $e ', error: e);
      safeEmit(const ApplicationRepaymentPlanError());
    }
  }

  Future<void> retry() {
    safeEmit(const ApplicationRepaymentPlanLoading());
    return initialize();
  }

  Future<void> onNext() => _config.flowHandler.toNextStage();

  Future<void> showInstallmentDetails({
    required Installment installment,
  }) async {
    final config = InstallmentBottomSheetConfig(
      installment: installment,
    );
    await _responsiveDialogProvider.showBottomSheetOrDialog<void>(
      content: InstallmentDetailsBody(config: config),
      config: const ResponsiveModalConfig(
        featureName: CreditFeatureNavigationConfig.name,
      ),
    );
  }

  void showProcessingFeeInfo() {
    if (state is ApplicationRepaymentPlanLoaded) {
      _responsiveDialogProvider.showBottomSheetOrDialog<void>(
        content: const LoanProcessingFeeInfoDialog(),
        config: const ResponsiveModalConfig(
          featureName: CreditFeatureNavigationConfig.name,
        ),
      );
    }
  }

  (Money, Period) _extractSelectedAmountAndPeriod() {
    final application = _config.application;
    final productPreferences = application.selectedProductPreferences;
    final selectedAmount = productPreferences?.selectedAmount;
    final period = productPreferences?.loanPeriod;

    if (period == null || selectedAmount == null) {
      throw PeriodOrAmountAbsentException();
    }

    return (selectedAmount, period);
  }

  @override
  String toString() {
    return 'ApplicationRepaymentPlanCubit';
  }
}

class PeriodOrAmountAbsentException implements Exception {}
