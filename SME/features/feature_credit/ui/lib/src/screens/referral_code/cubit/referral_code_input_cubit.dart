import 'dart:async';

import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/ui.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_api/exceptions/credit_exception.dart';
import 'package:wio_feature_credit_api/exceptions/credit_exception_code.dart';
import 'package:wio_feature_credit_ui/src/flows/credit_application/enhanced/handler/application_update_request.dart';
import 'package:wio_feature_credit_ui/src/handlers/credit_error_handler/credit_error_handler.dart';
import 'package:wio_feature_credit_ui/src/screens/referral_code/config/referral_code_input_config.dart';
import 'package:wio_feature_credit_ui/src/screens/referral_code/cubit/referral_code_input_state.dart';
import 'package:wio_feature_credit_ui/src/screens/referral_code/dialogs/what_is_referral_code.dart';
import 'package:wio_feature_faq_api/navigation/faq_feature_navigation_config.dart';

class ReferralCodeInputCubit extends BaseCubit<ReferralCodeInputState> {
  final ReferralCodeInputConfig _config;
  final CreditErrorHandler _errorHandler;
  final ResponsiveDialogProvider _dialogProvider;

  ReferralCodeInputCubit({
    required ReferralCodeInputConfig config,
    required CreditErrorHandler errorHandler,
    required ResponsiveDialogProvider dialogProvider,
  })  : _config = config,
        _errorHandler = errorHandler,
        _dialogProvider = dialogProvider,
        super(
          ReferralCodeInputState.idle(
            input: config.application.inputData.referralCode ?? '',
            subState: ReferralCodeInputSubState.valid,
          ),
        );

  void setInput(String input) {
    safeEmit(
      ReferralCodeInputState.idle(
        input: input,
        subState: ReferralCodeInputSubState.valid,
      ),
    );
  }

  Future<void> submit() async {
    await state.mapOrNull(
      idle: (it) async {
        try {
          if (it.input.isEmpty) return;

          safeEmit(ReferralCodeInputState.validating(input: it.input));

          await _config.applicationFlowHandler.toNextStage(
            updateRequest: ApplicationUpdateRequest(
              applicationId: _config.application.id,
              data: CreditApplicationInputData(referralCode: it.input),
            ),
          );

          safeEmit(it.copyWith(input: it.input));
        } on CreditException catch (error, stackTrace) {
          switch (error.exceptionCode) {
            case CreditExceptionCode.invalidReferralCode:
              safeEmit(
                it.copyWith(subState: ReferralCodeInputSubState.invalid),
              );
            case CreditExceptionCode.expiredReferralCode:
              safeEmit(
                it.copyWith(subState: ReferralCodeInputSubState.expired),
              );
            default:
              _errorHandler.handleError(error, stackTrace: stackTrace);
          }
        } on Object catch (error, stackTrace) {
          _errorHandler.handleError(error, stackTrace: stackTrace);
          safeEmit(it.copyWith(input: it.input));
        }
      },
    );
  }

  Future<void> skipInput() async {
    try {
      await state.mapOrNull(
        idle: (it) async {
          safeEmit(ReferralCodeInputState.validating(input: it.input));
          await _config.applicationFlowHandler.toNextStage();
          safeEmit(it);
        },
      );
    } on Object catch (error, stackTrace) {
      _errorHandler.handleError(error, stackTrace: stackTrace);
    }
  }

  void showReferralCodeInfo() {
    _dialogProvider.showBottomSheetOrDialog<void>(
      content: const WhatIsReferralCodeDialog(),
      config: const ResponsiveModalConfig(
        featureName: CommonFaqFeatureNavigationConfig.name,
      ),
    );
  }

  @override
  String toString() => 'ReferralCodeInputCubit';
}
