import 'dart:typed_data';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_loan_api/domain/loan_interactor.dart';

part 'credit_agreement_state.freezed.dart';

@freezed
class CreditAgreementState with _$CreditAgreementState {
  const CreditAgreementState._();

  /// The state for loading required initial data (agreement file)
  const factory CreditAgreementState.loading() = _LoadingCreditAgreementState;

  /// The state when nothing is happening.
  const factory CreditAgreementState.idle({
    required FileData fileData,
    required bool opensInWebView,
  }) = _IdleCreditAgreementState;

  /// The state when signing the agreement.
  const factory CreditAgreementState.submitting({
    required FileData fileData,
    required bool opensInWebView,
  }) = _SubmittingCreditAgreementState;

  /// The state when something goes wrong during submitting
  const factory CreditAgreementState.failed() = _FailedCreditAgreementState;

  bool get canSubmit => maybeMap(
        idle: (it) => true,
        orElse: () => false,
      );

  bool get canCancel => maybeMap(
        loading: (_) => false,
        submitting: (_) => false,
        orElse: () => true,
      );

  CreditAgreementState toSubmitting() => maybeMap(
        idle: (it) => CreditAgreementState.submitting(
          fileData: it.fileData,
          opensInWebView: it.opensInWebView,
        ),
        orElse: () => throw Exception('Illegal state transition'),
      );

  CreditAgreementState toIdle() => maybeMap(
        submitting: (it) => CreditAgreementState.idle(
          fileData: it.fileData,
          opensInWebView: it.opensInWebView,
        ),
        orElse: () => throw Exception('Illegal state transition'),
      );
}
