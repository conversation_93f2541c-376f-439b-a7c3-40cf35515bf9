// ignore_for_file: lines_longer_than_80_chars

import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

class BusinessLoanOverviewPageNavigationConfig extends ScreenNavigationConfig {
  final LoanType loanType;

  BusinessLoanOverviewPageNavigationConfig({
    required this.loanType,
  }) : super(
          id: '${LoanProductIdentifier.businessLoan.productName}_${loanType.name}_overview',
          feature: CreditFeatureNavigationConfig.name,
        );

  @override
  String toString() => 'BusinessLoanOverviewPageNavigationConfig';
}
