import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_loan_api/models/models.dart';

part 'autopay_fee_calculation_info_bottom_sheet_navigation_config.freezed.dart';

@freezed
class AutopayFeeCalculationInfoBottomSheetConfiguration
    with _$AutopayFeeCalculationInfoBottomSheetConfiguration {
  const factory AutopayFeeCalculationInfoBottomSheetConfiguration({
    required Autopay autoPayment,
    required LoanAccount loanAccount,
    required CreditProductInfo creditProductInfo,
  }) = _AutopayFeeCalculationInfoBottomSheetConfiguration;
}

@freezed
class AutopayFeeCalculationInfoBottomSheetNavigationConfig
    extends BottomSheetNavigationConfig<void>
    with _$AutopayFeeCalculationInfoBottomSheetNavigationConfig {
  const factory AutopayFeeCalculationInfoBottomSheetNavigationConfig({
    required AutopayFeeCalculationInfoBottomSheetConfiguration configuration,
  }) = _AutopayFeeCalculationInfoBottomSheetNavigationConfig;

  const AutopayFeeCalculationInfoBottomSheetNavigationConfig._();

  @override
  String get feature => CreditFeatureNavigationConfig.name;

  @override
  String toString() => 'AutopayFeeCalculationInfoBottomSheetNavigationConfig';
}
