import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/pos_account_determinator/pos_account_state/pos_account_state.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

/// The navigation configuration for the pos dashboard.
class PosDashboardNavigationConfig extends ScreenNavigationConfig {
  final PosAccountState posAccountState;

  PosDashboardNavigationConfig({
    required this.posAccountState,
  }) : super(
          id: '${LoanProductIdentifier.pos.productName}_dashboard',
          feature: CreditFeatureNavigationConfig.name,
        );

  @override
  String toString() => 'CreditDashboardNavigationConfig';
}
