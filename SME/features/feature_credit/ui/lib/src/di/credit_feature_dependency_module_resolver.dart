import 'package:account_feature_api/account_feature_api.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:data/data.dart';
import 'package:di/di.dart';
import 'package:domain/stream/exhaust_stream_executor.dart';
import 'package:feature_login_api/feature_login_api.dart';
import 'package:flutter/material.dart';
import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:sme_feature_transactions_api/feature_transactions_api.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/account_closure_confirmation/config/account_closure_confirmation_config.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/account_closure_confirmation/cubit/account_closure_confirmation_cubit.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/activate_credit_card_bottomsheet/cubit/activate_credit_card_bottomsheet_cubit.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/autopay_fee_calculation_info/cubit/autopay_fee_calculation_info_cubit.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/channel_finance_agreement_bottomsheet/cubit/channel_finance_agreement_bottom_sheet_cubit.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/credit_locked_account_bottomsheet/credit_locked_account_bottomsheet.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/credit_locked_account_bottomsheet/cubit/credit_locked_account_bottomsheet_cubit.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/debit_account_selector_bottomsheet/config/debit_account_selector_bottomsheet_config.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/debit_account_selector_bottomsheet/cubit/debit_account_selector_bottomsheet_cubit.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/iban_input_bottomsheet/cubit/iban_input_bottomsheet_cubit.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/pos_payment_details_bottomsheet/widgets/cubit/pos_bottom_sheet_amounts_summary_cubit.dart';
import 'package:wio_feature_credit_ui/src/common/credit_constants.dart';
import 'package:wio_feature_credit_ui/src/common/widgets/autopay_from_saving_spaces_switcher/config/autopay_from_saving_spaces_switcher_config.dart';
import 'package:wio_feature_credit_ui/src/common/widgets/autopay_from_saving_spaces_switcher/cubit/autopay_from_saving_spaces_switcher_cubit.dart';
import 'package:wio_feature_credit_ui/src/common/widgets/autopay_from_saving_spaces_switcher/delegate/account_autopay_from_saving_spaces_switcher_delegate.dart';
import 'package:wio_feature_credit_ui/src/common/widgets/autopay_from_saving_spaces_switcher/delegate/application_autopay_from_saving_spaces_switcher_delegate.dart';
import 'package:wio_feature_credit_ui/src/deeplinks/credit_application_deeplink_handler.dart';
import 'package:wio_feature_credit_ui/src/deeplinks/credit_autopay_fee_calculation_deeplink_handler.dart';
import 'package:wio_feature_credit_ui/src/deeplinks/credit_dashboard_deeplink_handler.dart';
import 'package:wio_feature_credit_ui/src/deeplinks/credit_learn_more_deeplink_handler.dart';
import 'package:wio_feature_credit_ui/src/deeplinks/credit_statements_deeplink_handler.dart';
import 'package:wio_feature_credit_ui/src/deeplinks/easy_cash/easy_cash_borrow_deeplink_handler.dart';
import 'package:wio_feature_credit_ui/src/deeplinks/easy_cash/easy_cash_dashboard_deeplink_handler.dart';
import 'package:wio_feature_credit_ui/src/deeplinks/easy_cash/easy_cash_repayment_deeplink_handler.dart';
import 'package:wio_feature_credit_ui/src/deeplinks/enhanced_deeplink_handler/credit_deeplink_handler.dart';
import 'package:wio_feature_credit_ui/src/flows/credit_application/common/credit_application_flow_handler.dart';
import 'package:wio_feature_credit_ui/src/flows/credit_application/enhanced/cubit/credit_application_flow_cubit.dart';
import 'package:wio_feature_credit_ui/src/flows/credit_application/enhanced/delegates/credit_application_delegate.dart';
import 'package:wio_feature_credit_ui/src/flows/credit_application/enhanced/handler/credit_application_flow_handler.dart';
import 'package:wio_feature_credit_ui/src/flows/credit_flow/credit_flow_impl.dart';
import 'package:wio_feature_credit_ui/src/handlers/credit_application_creator/credit_application_creator.dart';
import 'package:wio_feature_credit_ui/src/handlers/credit_application_creator/credit_application_initial_stage_setter.dart';
import 'package:wio_feature_credit_ui/src/handlers/credit_error_handler/credit_error_handler.dart';
import 'package:wio_feature_credit_ui/src/handlers/deeplink_navigation_handlers/credit_application_deeplink_navigation_handler.dart';
import 'package:wio_feature_credit_ui/src/handlers/deeplink_navigation_handlers/credit_dashboard_deeplink_navigation_handler.dart';
import 'package:wio_feature_credit_ui/src/handlers/deeplink_navigation_handlers/easy_cash_deeplink_navigation_handler.dart';
import 'package:wio_feature_credit_ui/src/handlers/deeplink_navigation_handlers/pos_deeplink_navigation_handler.dart';
import 'package:wio_feature_credit_ui/src/navigation/configs/bottom_sheets/autopay_fee_calculation_info_bottom_sheet_navigation_config.dart';
import 'package:wio_feature_credit_ui/src/navigation/configs/screens/credit_things_to_know_page_navigation_config.dart';
import 'package:wio_feature_credit_ui/src/navigation/credit_router.dart';
import 'package:wio_feature_credit_ui/src/screens/application_recap/config/application_recap_config.dart';
import 'package:wio_feature_credit_ui/src/screens/application_recap/cubit/application_recap_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/application_repayment_plan/config/application_repayment_plan_config.dart';
import 'package:wio_feature_credit_ui/src/screens/application_repayment_plan/cubit/application_repayment_plan_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/business_loan_borrow/config/business_loan_borrow_config.dart';
import 'package:wio_feature_credit_ui/src/screens/business_loan_borrow/cubit/business_loan_borrow_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/company_bank_accounts/config/company_bank_accounts_input_config.dart';
import 'package:wio_feature_credit_ui/src/screens/company_bank_accounts/delegates/application_company_bank_accounts_input_delegate.dart';
import 'package:wio_feature_credit_ui/src/screens/company_bank_accounts/delegates/company_bank_accounts_input_delegate.dart';
import 'package:wio_feature_credit_ui/src/screens/company_bank_accounts/stages/company_bank_accounts_option_selector/cubit/company_bank_accounts_option_selector_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/company_bank_accounts/stages/other_bank_accounts_iban_input/cubit/other_bank_accounts_iban_input_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/complete_pos_hypothecation/cubit/complete_pos_hypothecation_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_agreement/config/credit_agreement_config.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_agreement/cubit/credit_agreement_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_dashboard/autopay_setup/autopay_setup_cubit/autopay_setup_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_dashboard/dashboard/credit_dashboard_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_dashboard/dashboard/credit_dashboard_page.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_dashboard/manage_credit/cubit/manage_credit_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_dashboard/pay_credit/config/pay_credit_config.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_dashboard/pay_credit/pay_credit_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_document_viewer/delegate/sme_credit_document_viewer_delegate.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_entry_points/mapper/credit_application_banner_mapper.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_limit_check/config/credit_limit_setup_config.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_limit_check/cubit/credit_limit_setup_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_limit_check/delegates/application_credit_limit_setup_delegate.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_limit_check/delegates/reduce_credit_limit_setup_delegate.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_overview/cubit/credit_overview_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_pdf_viewer/cubit/credit_pdf_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_statement_upload/config/credit_statement_upload_config.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_statement_upload/cubit/credit_statement_upload_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_statement_upload/delegates/application_statement_upload_delegate.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_statement_upload/delegates/audited_statement_upload_delegate.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_statement_upload/delegates/borrowing_power_statement_upload_delegate.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_statement_upload/delegates/credit_statement_upload_delegate.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_things_to_know/cubit/credit_things_to_know_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/easy_cash_manage/cubit/easy_cash_manage_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/learn_more/config/learn_more_config.dart';
import 'package:wio_feature_credit_ui/src/screens/learn_more/cubit/learn_more_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/loan_type_selection/config/loan_type_selection_config.dart';
import 'package:wio_feature_credit_ui/src/screens/loan_type_selection/loan_type_selection_page/cubit/loan_type_selection_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/loan_type_selection/secured_loan_confirmation/cubit/secured_loan_confirmation_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/payment_date_setup/config/payment_date_setup_config.dart';
import 'package:wio_feature_credit_ui/src/screens/payment_date_setup/cubit/payment_date_setup_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/payment_percentage_setup/config/payment_percentage_setup_config.dart';
import 'package:wio_feature_credit_ui/src/screens/payment_percentage_setup/cubit/payment_percentage_setup_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/borrow/config/pos_borrow_page_config.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/borrow/cubit/pos_borrow_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/borrow_confirmation/config/pos_borrow_confirmation_page_config.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/borrow_confirmation/cubit/pos_borrow_confirmation_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/closure_confirmation/cubit/pos_closure_confirmation_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/confirm_autodebit_account/config/confirm_autodebit_account_page_config.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/confirm_autodebit_account/cubit/confirm_autodebit_account_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/dashboard/pos_dashboard_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/manage_pos_credit/cubit/manage_pos_credit_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/pos_account_determinator/pos_account_state/pos_account_state.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/pos_payback/config/pos_payback_config.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/pos_payback/pos_payback_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/things_to_know/cubit/pos_things_to_know_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/widgets/debit_account_selector/config/debit_account_selector_config.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/widgets/debit_account_selector/cubit/debit_account_selector_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/referral_code/config/referral_code_input_config.dart';
import 'package:wio_feature_credit_ui/src/screens/referral_code/cubit/referral_code_input_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/secured_loan_completion/cubit/secured_loan_completion_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/turnover/config/turnover_config.dart';
import 'package:wio_feature_credit_ui/src/screens/turnover/cubit/turnover_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/update_credit_limit/cubit/update_credit_limit_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/vat_non_registration_reason/config/vat_non_registration_reason_config.dart';
import 'package:wio_feature_credit_ui/src/screens/vat_non_registration_reason/cubit/vat_non_registration_reason_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/vat_non_registration_reason/vat_non_registration_reason_delegate/vat_not_registration_reason_delegate.dart';
import 'package:wio_feature_credit_ui/src/screens/vat_non_registration_reason/vat_non_registration_reason_delegate/vat_not_registration_reason_delegate_impl.dart';
import 'package:wio_feature_credit_ui/src/screens/vat_reporting_interval/config/vat_reporting_interval_config.dart';
import 'package:wio_feature_credit_ui/src/screens/vat_reporting_interval/cubit/vat_reporting_interval_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/vat_reporting_interval/vat_reporting_interval_delegate/application_vat_reporting_interval.dart';
import 'package:wio_feature_credit_ui/src/screens/vat_reporting_interval/vat_reporting_interval_delegate/vat_reporting_interval_delegate.dart';
import 'package:wio_feature_easy_cash_api/domain/easy_cash_interactor.dart';
import 'package:wio_feature_easy_cash_api/flow/easy_cash_flow.dart';
import 'package:wio_feature_faq_api/faq_api.dart';
import 'package:wio_feature_home_api/wio_feature_home_api.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_share_api/feature_share_api.dart';

part 'application_flow_delegates_registration_resolver.dart';

class CreditFeatureDependencyModuleResolver {
  static void register() {
    _registerDeeplinks();
    _registerCreditFlow();
    _registerNavigations();
    _registerLocalizations();
    _registerCubits();
    _registerDelegates();
    _registerHandlers();
    _registerBanners();
  }

  static void _registerCubits() {
    _registerApplicationFlowCubits();
    _registerDashboardCubits();
    _registerGeneralCubits();
  }

  static Logger get _logger => DependencyProvider.get<Logger>(
        instanceName: WioDomain.lending.name,
      );

  static void _registerCreditFlow() {
    DependencyProvider.registerLazySingleton<CreditFlow>(
      () => CreditFlowImpl(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        logger: _logger,
        posDeeplinkNavigationHandler:
            DependencyProvider.get<PosDeeplinkNavigationHandler>(),
        easyCashDeeplinkNavigationHandler:
            DependencyProvider.get<EasyCashDeeplinkNavigationHandler>(),
        creditDashboardDeeplinkNavigationHandler:
            DependencyProvider.get<CreditDashboardDeeplinkNavigationHandler>(),
        creditApplicationDeeplinkNavigationHandler: DependencyProvider.get<
            CreditApplicationDeeplinkNavigationHandler>(),
      ),
    );
  }

  static void _registerApplicationFlowCubits() {
    DependencyProvider.registerFactoryWithParams<CreditApplicationFlowCubit,
        CreditApplicationNavigationConfig, void>(
      (config, _) {
        return CreditApplicationFlowCubit(
          initialApplication: config.application,
          logger: _logger,
          navigationProvider: DependencyProvider.get<NavigationProvider>(),
          creditErrorHandler: DependencyProvider.get<CreditErrorHandler>(),
          delegate: DependencyProvider.getWithParams<CreditApplicationDelegate,
              CreditApplication, CreditApplicationFlowSourceApp>(
            param1: config.application,
            param2: config.sourceApp,
          ),
          analytics: DependencyProvider.get<CreditAnalytics>(),
        );
      },
    );

    DependencyProvider.registerFactoryWithParams<CreditOverviewCubit,
        SmeCreditProductType, void>(
      (productType, _) => CreditOverviewCubit(
        productType: productType,
        interactor: DependencyProvider.get<CreditApplicationInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        localizations: DependencyProvider.get<CreditLocalizations>(),
        logger: _logger,
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<CreditThingsToKnowCubit,
        CreditThingsToKnowPageNavigationConfig, void>(
      (config, _) => CreditThingsToKnowCubit(
        config: config,
        applicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
        loanInteractor: DependencyProvider.get<LoanInteractor>(),
        creditApplicationCreator: DependencyProvider.getWithParams<
            CreditApplicationCreator,
            SmeCreditProductType,
            CreditApplicationFlowSourceApp>(
          param1: config.productType,
          param2: const CreditApplicationFlowSourceApp.sme(),
        ),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        logger: _logger,
      ),
    );

    DependencyProvider.registerFactoryWithParams<VatReportingIntervalCubit,
        VatReportingIntervalConfig, String>(
      (config, delegateInstanceName) {
        final delegate = DependencyProvider.getWithParams<
            VatReportingIntervalDelegate, VatReportingIntervalConfig, void>(
          param1: config,
          instanceName: delegateInstanceName,
        );

        return VatReportingIntervalCubit(
          config: config,
          logger: DependencyProvider.get<Logger>(
            instanceName: WioDomain.lending.name,
          ),
          delegate: delegate,
          errorHandler: DependencyProvider.get<CreditErrorHandler>(),
          localizations: DependencyProvider.get<CreditLocalizations>(),
          analytics: DependencyProvider.get<CreditAnalytics>(),
          featureToggleProvider:
              DependencyProvider.get<FeatureToggleProvider>(),
        );
      },
    );

    DependencyProvider.registerFactoryWithParams<VatNonRegistrationReasonCubit,
        VatNonRegistrationReasonConfig, String>(
      (config, delegateInstanceName) {
        final delegate = DependencyProvider.getWithParams<
            VatNotRegistrationReasonDelegate,
            VatNonRegistrationReasonConfig,
            void>(
          param1: config,
          instanceName: delegateInstanceName,
        );

        return VatNonRegistrationReasonCubit(
          interactor: DependencyProvider.get<CreditApplicationInteractor>(),
          logger: DependencyProvider.get<Logger>(
            instanceName: WioDomain.lending.name,
          ),
          delegate: delegate,
          errorHandler: DependencyProvider.get<CreditErrorHandler>(),
          analytics: DependencyProvider.get<CreditAnalytics>(),
        );
      },
    );

    DependencyProvider.registerFactoryWithParams<TurnoverCubit, TurnoverConfig,
        void>(
      (config, _) => TurnoverCubit(
        turnoverConfig: config,
        logger: _logger,
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        localizations: DependencyProvider.get<CreditLocalizations>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
        responsiveDialogProvider:
            DependencyProvider.get<ResponsiveDialogProvider>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<
        OtherBankAccountsIbanInputCubit, CompanyBankAccountsInputConfig, void>(
      (config, _) => OtherBankAccountsIbanInputCubit(
        config: config,
        delegate: DependencyProvider.getWithParams<
            CompanyBankAccountsInputDelegate,
            CompanyBankAccountsInputConfig,
            void>(
          param1: config,
          instanceName: config.instance,
        ),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        localization: DependencyProvider.get<CreditLocalizations>(),
        logger: _logger,
        analytics: DependencyProvider.get<CreditAnalytics>(),
        responsiveDialogProvider:
            DependencyProvider.get<ResponsiveDialogProvider>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<CreditStatementUploadCubit,
        CreditStatementUploadConfig, void>(
      (config, _) => CreditStatementUploadCubit(
        application: config.application,
        logger: _logger,
        delegate: DependencyProvider.getWithParams<
            CreditStatementUploadDelegate, CreditStatementUploadConfig, void>(
          param1: config,
        ),
        interactor: DependencyProvider.get<CreditApplicationInteractor>(),
        localizations: DependencyProvider.get<CreditLocalizations>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
        responsiveDialogProvider:
            DependencyProvider.get<ResponsiveDialogProvider>(),
      ),
    );

    DependencyProvider.registerFactory<IbanInputBottomsheetCubit>(
      () => IbanInputBottomsheetCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<ApplicationRecapCubit,
        ApplicationRecapConfig, void>(
      (config, _) => ApplicationRecapCubit(
        config: config,
        interactor: DependencyProvider.get<CreditApplicationInteractor>(),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        creditLocalizations: DependencyProvider.get<CreditLocalizations>(),
        logger: _logger,
        analytics: DependencyProvider.get<CreditAnalytics>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        applicationDelegate: DependencyProvider.getWithParams<
            CreditApplicationDelegate,
            CreditApplication,
            CreditApplicationFlowSourceApp>(
          param1: config.application,
          param2: config.flowSourceApp,
        ),
      ),
    );

    DependencyProvider.registerFactoryWithParams<CreditLimitSetupCubit,
        CreditLimitSetupConfig, void>(
      (config, _) => CreditLimitSetupCubit(
        delegate: config.map(
          application: (applicationConfig) =>
              ApplicationCreditLimitSetupDelegate(
            application: applicationConfig.application,
            productType: applicationConfig.productType,
            applicationInteractor:
                DependencyProvider.get<CreditApplicationInteractor>(),
            applicationFlowHandler: applicationConfig.applicationFlowHandler,
          ),
          reduce: (reduceConfig) => ReduceCreditLimitSetupDelegate(
            maxAmount: reduceConfig.maxAmount,
            minAmount: reduceConfig.minAmount,
            creditAccountId: reduceConfig.creditAccountId,
            creditApplicationInteractor:
                DependencyProvider.get<CreditApplicationInteractor>(),
            creditAccountInteractor:
                DependencyProvider.get<CreditAccountInteractor>(),
            navigationProvider: DependencyProvider.get<NavigationProvider>(),
            creditLocalizations: DependencyProvider.get<CreditLocalizations>(),
            creditErrorHandler: DependencyProvider.get<CreditErrorHandler>(),
            easyCashLoanAmount: reduceConfig.easyCashLoanAmount,
            responsiveDialogProvider:
                DependencyProvider.get<ResponsiveDialogProvider>(),
          ),
        ),
        logger: _logger,
        creditErrorHandler: DependencyProvider.get<CreditErrorHandler>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<PaymentPercentageSetupCubit,
        PaymentPercentageSetupConfig, void>(
      (config, _) => PaymentPercentageSetupCubit(
        config: config,
        logger: _logger,
        creditErrorHandler: DependencyProvider.get<CreditErrorHandler>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<PaymentDateSetupCubit,
        PaymentDateSetupConfig, void>(
      (config, _) => PaymentDateSetupCubit(
        config: config,
        logger: _logger,
        creditErrorHandler: DependencyProvider.get<CreditErrorHandler>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<CreditAgreementCubit,
        CreditAgreementConfig, void>(
      (config, _) => CreditAgreementCubit(
        config: config,
        interactor: DependencyProvider.get<CreditApplicationInteractor>(),
        logger: _logger,
        shareProvider: DependencyProvider.get<ShareProvider>(),
        platformInfo: DependencyProvider.get<PlatformInfo>(),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<LearnMoreCubit,
        LearnMoreConfig, void>(
      (config, _) => LearnMoreCubit(
        config: config,
        applicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
        logger: _logger,
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        faqsInteractor: DependencyProvider.get<FAQsInteractor>(),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<ConfirmAutodebitAccountCubit,
        ConfirmAutodebitAccountPageConfig, void>(
      (config, _) => ConfirmAutodebitAccountCubit(
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        config: config,
        analytics: DependencyProvider.get<CreditAnalytics>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<DebitAccountSelectorCubit,
        DebitAccountSelectorConfig, void>(
      (config, _) => DebitAccountSelectorCubit(
        accountInteractor: DependencyProvider.get<AccountInteractor>(),
        creditAccountInteractor:
            DependencyProvider.get<CreditAccountInteractor>(),
        responsiveDialogProvider:
            DependencyProvider.get<ResponsiveDialogProvider>(),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        creditLocalizations: DependencyProvider.get<CreditLocalizations>(),
        config: config,
      ),
    );

    DependencyProvider.registerFactoryWithParams<
        DebitAccountSelectorBottomSheetCubit,
        DebitAccountSelectorBottomSheetConfig,
        void>(
      (config, _) => DebitAccountSelectorBottomSheetCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        config: config,
      ),
    );

    DependencyProvider.registerFactory<ActivateCreditCardBottomSheetCubit>(
      () => ActivateCreditCardBottomSheetCubit(
        creditApplicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
      ),
    );

    DependencyProvider.registerFactory<ChannelFinanceAgreementBottomSheetCubit>(
      () => ChannelFinanceAgreementBottomSheetCubit(
        contentInteractor: DependencyProvider.get<ContentInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        logger: _logger,
      ),
    );

    DependencyProvider.registerFactoryWithParams<ApplicationRepaymentPlanCubit,
        ApplicationRepaymentPlanConfig, void>(
      (config, _) => ApplicationRepaymentPlanCubit(
        config: config,
        interactor: DependencyProvider.get<LoanInteractor>(),
        logger: _logger,
        responsiveDialogProvider:
            DependencyProvider.get<ResponsiveDialogProvider>(),
        localizations: DependencyProvider.get<CreditLocalizations>(),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<BusinessLoanBorrowCubit,
        BusinessLoanBorrowConfig, void>(
      (config, _) => BusinessLoanBorrowCubit(
        loginInteractor: DependencyProvider.get<LoginInteractor>(),
        creditApplicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
        responsiveDialogProvider:
            DependencyProvider.get<ResponsiveDialogProvider>(),
        accountInteractor: DependencyProvider.get<AccountInteractor>(),
        loanInteractor: DependencyProvider.get<LoanInteractor>(),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        config: config,
      ),
    );

    DependencyProvider.registerFactoryWithParams<LoanTypeSelectionCubit,
        LoanTypeSelectionConfig, void>(
      (config, _) => LoanTypeSelectionCubit(
        config: config,
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<SecuredLoanConfirmationCubit,
        LoanTypeSelectionConfig, void>(
      (config, _) => SecuredLoanConfirmationCubit(
        config: config,
        responsiveDialogProvider:
            DependencyProvider.get<ResponsiveDialogProvider>(),
      ),
    );

    DependencyProvider.registerFactory<SecuredLoanCompletionCubit>(
      () => SecuredLoanCompletionCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        authManager: DependencyProvider.get<IAuthManager>(),
        faqInteractor: DependencyProvider.get<FAQsInteractor>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<ReferralCodeInputCubit,
        ReferralCodeInputConfig, void>(
      (config, _) => ReferralCodeInputCubit(
        config: config,
        dialogProvider: DependencyProvider.get<ResponsiveDialogProvider>(),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
      ),
    );

    _registerCompanyBankAccountsInputFlowCubit();
  }

  static void _registerDashboardCubits() {
    DependencyProvider.registerFactoryWithParams<CreditDashboardCubit,
        CreditDashboardConfig, void>(
      (config, _) => CreditDashboardCubit(
        config: config,
        accountInteractor: DependencyProvider.get<CreditAccountInteractor>(),
        logger: _logger,
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        localizations: DependencyProvider.get<CreditLocalizations>(),
        transactionsMediator: DependencyProvider.get<TransactionsMediator>(),
        creditApplicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        easyCashInteractor: DependencyProvider.get<EasyCashInteractor>(),
        easyCashFlow: DependencyProvider.get<EasyCashFlow>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
        creditFeeFreePeriodTooltipInteractor:
            DependencyProvider.get<CreditFeeFreePeriodTooltipInteractor>(),
        faqInteractor: DependencyProvider.get<FAQsInteractor>(),
      ),
    );

    DependencyProvider.registerFactory<AutopaySetupCubit>(
      () => AutopaySetupCubit(
        interactor: DependencyProvider.get<CreditAccountInteractor>(),
        logger: _logger,
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        localizations: DependencyProvider.get<CreditLocalizations>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<AutopayFeeCalculationInfoCubit,
        AutopayFeeCalculationInfoBottomSheetConfiguration, void>(
      (config, _) => AutopayFeeCalculationInfoCubit(
        configuration: config,
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<
        CreditLockedAccountBottomSheetCubit,
        CreditLockedAccountBottomSheetConfig,
        void>(
      (config, _) => CreditLockedAccountBottomSheetCubit(
        configuration: config,
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    DependencyProvider.registerFactory(
      () => ManageCreditCubit(
        creditAccountInteractor:
            DependencyProvider.get<CreditAccountInteractor>(),
        logger: _logger,
        creditApplicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        creditErrorHandler: DependencyProvider.get<CreditErrorHandler>(),
        creditLocalizations: DependencyProvider.get<CreditLocalizations>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
        easyCashInteractor: DependencyProvider.get<EasyCashInteractor>(),
        responsiveDialogProvider:
            DependencyProvider.get<ResponsiveDialogProvider>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<PayCreditCubit,
        PayCreditConfig, void>(
      (config, _) => PayCreditCubit(
        config: config,
        accountInteractor: DependencyProvider.get<AccountInteractor>(),
        loanAccountInteractor:
            DependencyProvider.get<CreditAccountInteractor>(),
        logger: _logger,
        localizations: DependencyProvider.get<CreditLocalizations>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<
        AccountClosureConfirmationCubit,
        AccountClosureConfirmationConfig,
        void>(
      (config, _) => AccountClosureConfirmationCubit(
        config: config,
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        accountInteractor: DependencyProvider.get<CreditAccountInteractor>(),
        errorHanler: DependencyProvider.get<CreditErrorHandler>(),
        localizations: DependencyProvider.get<CreditLocalizations>(),
      ),
    );

    DependencyProvider.registerLazySingleton<EasyCashManageCubit>(
      () => EasyCashManageCubit(
        creditAccountInteractor:
            DependencyProvider.get<CreditAccountInteractor>(),
        logger: _logger,
        creditApplicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );

    DependencyProvider.registerLazySingleton<ManagePosCreditCubit>(
      () => ManagePosCreditCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<PosBorrowCubit,
        PosBorrowPageConfig, void>(
      (config, _) => PosBorrowCubit(
        creditApplicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
        posInteractor: DependencyProvider.get<PosInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        config: config,
      ),
    );

    DependencyProvider.registerFactoryWithParams<PosBorrowConfirmationCubit,
        PosBorrowConfirmationPageConfig, void>(
      (config, _) => PosBorrowConfirmationCubit(
        posInteractor: DependencyProvider.get<PosInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        localizations: DependencyProvider.get<CreditLocalizations>(),
        config: config,
      ),
    );

    DependencyProvider.registerFactoryWithParams<PosDashboardCubit,
        PosAccountState, void>(
      (posAccountState, _) => PosDashboardCubit(
        accountInteractor: DependencyProvider.get<CreditAccountInteractor>(),
        logger: _logger,
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        creditApplicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
        posAccountState: posAccountState,
      ),
    );

    DependencyProvider.registerFactoryWithParams<PosThingsToKnowCubit,
        LoanAccount, void>(
      (account, _) => PosThingsToKnowCubit(
        creditApplicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        account: account,
      ),
    );

    DependencyProvider.registerFactory<PosClosureConfirmationCubit>(
      () => PosClosureConfirmationCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        creditAccountInteractor:
            DependencyProvider.get<CreditAccountInteractor>(),
        localizations: DependencyProvider.get<CreditLocalizations>(),
        secureStorage: DependencyProvider.get<SecureStorage>(),
      ),
    );
  }

  static void _registerCompanyBankAccountsInputFlowCubit() {
    DependencyProvider.registerFactoryWithParams<
        CompanyBankAccountsOptionSelectorCubit,
        CompanyBankAccountsInputConfig,
        void>(
      (config, _) => CompanyBankAccountsOptionSelectorCubit(
        config: config,
        delegate: DependencyProvider.getWithParams<
            CompanyBankAccountsInputDelegate,
            CompanyBankAccountsInputConfig,
            void>(
          param1: config,
          instanceName: config.instance,
        ),
        logger: _logger,
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        localizations: DependencyProvider.get<CreditLocalizations>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
        responsiveDialogProvider:
            DependencyProvider.get<ResponsiveDialogProvider>(),
      ),
    );
  }

  static void _registerDelegates() {
    ApplicationFlowDelegatesRegistrationResolver.registerDelegate();
    _registerVatReportingIntervalDelegates();
    _registerVatStatementUploadDelegates();
    _registerCompanyBankAccountsInputDelegates();
    _registerCreditTransactionsDelegate();
    _registerVatNotRegistrationReasonDelegate();
    _registerDocumentViewerDelegate();
  }

  static void _registerCompanyBankAccountsInputDelegates() {
    DependencyProvider.registerFactoryWithParams<
        CompanyBankAccountsInputDelegate, CompanyBankAccountsInputConfig, void>(
      (config, _) => ApplicationCompanyBankAccountsInputDelegate(
        config: config,
      ),
      instanceName: CompanyBankAccountsInputConfig.applicationInstanceName,
    );
  }

  static void _registerVatStatementUploadDelegates() {
    DependencyProvider.registerFactoryWithParams<CreditStatementUploadDelegate,
        CreditStatementUploadConfig, void>(
      (config, _) => switch (config) {
        VatStatementUploadConfig() => ApplicationStatementUploadDelegate(
            creditApplication: config.application,
            applicationFlowHandler: config.applicationFlowHandler,
            applicationInteractor:
                DependencyProvider.get<CreditApplicationInteractor>(),
          ),
        BorrowPowerProofUploadConfig() => BorrowingPowerStatementUploadDelegate(
            creditApplication: config.application,
            navigationProvider: DependencyProvider.get<NavigationProvider>(),
            applicationInteractor:
                DependencyProvider.get<CreditApplicationInteractor>(),
          ),
        AuditedFinancialStatementUploadConfig() =>
          AuditedFinancialStatementUploadDelegate(
            creditApplication: config.application,
            applicationFlowHandler: config.applicationFlowHandler,
            applicationInteractor:
                DependencyProvider.get<CreditApplicationInteractor>(),
          ),
      } as CreditStatementUploadDelegate,
    );
  }

  static void _registerVatReportingIntervalDelegates() {
    DependencyProvider.registerFactoryWithParams<VatReportingIntervalDelegate,
        VatReportingIntervalConfig, void>(
      (config, _) => ApplicationVatReportingIntervalDelegate(
        logger: _logger,
        applicationId: config.application.id,
        applicationFlowHandler: config.applicationFlowHandler,
      ),
      instanceName:
          VatReportingIntervalConfig.applicationVatReportingIntervalConfigType,
    );
  }

  static void _registerDocumentViewerDelegate() {
    DependencyProvider.registerFactoryWithParams<DocumentViewerDelegate,
        DocumentViewerDelegateConfig, void>(
      (config, _) {
        if (config is SmeCreditDocumentViewerDelegateConfig) {
          return SmeCreditDocumentViewerDelegate(
            analytics: DependencyProvider.get<CreditAnalytics>(),
            config: config,
            contentInteractor: DependencyProvider.get<ContentInteractor>(),
            creditApplicationInteractor:
                DependencyProvider.get<CreditApplicationInteractor>(),
            errorHandler: DependencyProvider.get<CreditErrorHandler>(),
          );
        }

        throw ArgumentError.value(
          config,
          'DocumentViewerDelegate',
          'Wrong document viewer delegate passed $config',
        );
      },
    );
  }

  static void _registerVatNotRegistrationReasonDelegate() {
    DependencyProvider.registerFactoryWithParams<
        VatNotRegistrationReasonDelegate, VatNonRegistrationReasonConfig, void>(
      (config, _) => VatNotRegistrationReasonDelegateImpl(
        logger: _logger,
        applicationId: config.application.id,
        applicationFlowHandler: config.applicationFlowHandler,
      ),
      instanceName:
          VatNonRegistrationReasonConfig.vatNonRegistrationReasonConfigType,
    );
  }

  static void _registerNavigations() {
    DependencyProvider.registerLazySingleton<CreditRouter>(
      () => CreditRouter(),
    );

    DependencyProvider.registerLazySingleton<NavigationRouter>(
      () => DependencyProvider.get<CreditRouter>(),
      instanceName: CreditFeatureNavigationConfig.name,
    );
  }

  static void _registerLocalizations() {
    DependencyProvider.registerLazySingleton(
      () => CreditLocalizations.of(DependencyProvider.get<BuildContext>()),
    );
  }

  static void _registerHandlers() {
    DependencyProvider.registerLazySingleton<
        CreditApplicationInitialStageSetter>(
      () => CreditApplicationInitialStageSetterImpl(
        applicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );

    DependencyProvider.registerLazySingleton<CreditErrorHandler>(
      () => CreditErrorHandlerImpl(
        localizations: DependencyProvider.get<CreditLocalizations>(),
        responsiveDialogProvider:
            DependencyProvider.get<ResponsiveDialogProvider>(),
        logger: _logger,
      ),
    );

    DependencyProvider.registerFactoryWithParams<CreditApplicationFlowHandler,
        CreditApplicationFlowCubit, void>(
      (cubit, _) => CreditApplicationFlowHandlerImpl(
        flowCubit: cubit,
        applicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
      ),
      instanceName: CreditConstants.flowInstanceName,
    );

    DependencyProvider.registerFactoryWithParams<BorrowingPowerFlowHandler,
        CreditApplicationFlowSourceApp, void>(
      (flowSourceApp, _) => BorrowingPowerFlowHandler(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        responsiveDialogProvider:
            DependencyProvider.get<ResponsiveDialogProvider>(),
        localizations: DependencyProvider.get<CreditLocalizations>(),
        logger: _logger,
        creditApplicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
        applicationFlowSourceApp: flowSourceApp,
      ),
    );

    DependencyProvider.registerFactoryWithParams<CreditApplicationCreator,
        SmeCreditProductType, CreditApplicationFlowSourceApp>(
      (productType, flowSourceApp) => CreditApplicationCreatorImpl(
        productType: productType,
        sourceApp: flowSourceApp,
        borrowingPowerFlowHandler: DependencyProvider.getWithParams<
            BorrowingPowerFlowHandler, CreditApplicationFlowSourceApp, void>(
          param1: flowSourceApp,
        ),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        contentInteractor: DependencyProvider.get<ContentInteractor>(),
        applicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
        logger: _logger,
        creditAccountInteractor:
            DependencyProvider.get<CreditAccountInteractor>(),
        applicationInitialStageSetter:
            DependencyProvider.get<CreditApplicationInitialStageSetter>(),
      ),
    );
  }

  static void _registerBanners() {
    DependencyProvider.registerLazySingleton<CreditApplicationBannerMapper>(
      () => CreditApplicationBannerMapperImpl(
        errorReporter: DependencyProvider.get<ErrorReporter>(),
      ),
    );

    DependencyProvider.registerFactory<CreditEntryPointsCubit>(
      () => CreditEntryPointsCubit(
        easyCashInteractor: DependencyProvider.get<EasyCashInteractor>(),
        creditAccountInteractor:
            DependencyProvider.get<CreditAccountInteractor>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        logger: _logger,
        creditApplicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
        applicationBannerMapper:
            DependencyProvider.get<CreditApplicationBannerMapper>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
        borrowingPowerFlowHandler: DependencyProvider.getWithParams<
            BorrowingPowerFlowHandler, CreditApplicationFlowSourceApp, void>(
          param1: const CreditApplicationFlowSourceApp.sme(),
        ),
        authManager: DependencyProvider.get<IAuthManager>(),
      ),
    );
  }

  static void _registerGeneralCubits() {
    DependencyProvider.registerFactoryWithParams<CreditPdfCubit,
        CreditPdfViewerConfig, void>(
      (config, _) => CreditPdfCubit(
        config: config,
        shareProvider: DependencyProvider.get<ShareProvider>(),
        analytics: DependencyProvider.get<CreditAnalytics>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<
        AutopayFromSavingSpacesSwitcherCubit,
        AutopayFromSavingSpacesSwitcherConfig,
        void>(
      (config, _) => AutopayFromSavingSpacesSwitcherCubit(
        delegate: config.map(
          application: (applicationConfig) =>
              ApplicationAutopayFromSavingSpacesSwitcherDelegate(
            applicationId: applicationConfig.applicationId,
            productType: applicationConfig.productType,
            applicationFlowHandler: applicationConfig.applicationFlowHandler,
            creditApplicationInteractor:
                DependencyProvider.get<CreditApplicationInteractor>(),
          ),
          account: (accountConfig) =>
              AccountAutopayFromSavingSpacesSwitcherDelegate(
            accountId: accountConfig.accountId,
            creditAccountInteractor:
                DependencyProvider.get<CreditAccountInteractor>(),
          ),
        ),
        creditErrorHandler: DependencyProvider.get<CreditErrorHandler>(),
        config: config,
        analytics: DependencyProvider.get<CreditAnalytics>(),
        responsiveDialogProvider:
            DependencyProvider.get<ResponsiveDialogProvider>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<PosPaybackCubit,
        PosPaybackConfig, void>(
      (config, _) => PosPaybackCubit(
        config: config,
        accountInteractor: DependencyProvider.get<AccountInteractor>(),
        loanAccountInteractor:
            DependencyProvider.get<CreditAccountInteractor>(),
        logger: _logger,
        localizations: DependencyProvider.get<CreditLocalizations>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        exhaustStreamExecutor: DependencyProvider.get<ExhaustStreamExecutor>(),
        secureStorage: DependencyProvider.get<SecureStorage>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<CompletePosHypothecationCubit,
        CompletePosHypothecationConfig, void>(
      (config, _) => CompletePosHypothecationCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        config: config,
        analytics: DependencyProvider.get<CreditAnalytics>(),
        authManager: DependencyProvider.get<IAuthManager>(),
      ),
    );

    DependencyProvider.registerFactory<PosBottomSheetAmountsSummaryCubit>(
      () => PosBottomSheetAmountsSummaryCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    DependencyProvider.registerFactory<UpdateCreditLimitCubit>(
      () => UpdateCreditLimitCubit(
        creditAccountInteractor:
            DependencyProvider.get<CreditAccountInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        errorHandler: DependencyProvider.get<CreditErrorHandler>(),
        creditLocalizations: DependencyProvider.get<CreditLocalizations>(),
        easyCashInteractor: DependencyProvider.get<EasyCashInteractor>(),
      ),
    );
  }

  static void _registerDeeplinks() {
    DependencyProvider.get<DeepLinkHandlerRegister>()
      ..register(
        () => CreditApplicationDeeplinkHandler(
          interactor: DependencyProvider.get<CreditApplicationInteractor>(),
          navigationProvider: DependencyProvider.get<NavigationProvider>(),
          featureToggleProvider:
              DependencyProvider.get<FeatureToggleProvider>(),
          changeHomeTabChannel: DependencyProvider.get<ChangeHomeTabChannel>(),
        ),
      )
      ..register(
        () => CreditDashboardDeeplinkHandler(
          interactor: DependencyProvider.get<CreditAccountInteractor>(),
          navigationProvider: DependencyProvider.get<NavigationProvider>(),
          featureToggleProvider:
              DependencyProvider.get<FeatureToggleProvider>(),
        ),
      )
      ..register(
        () => CreditStatementsDeeplinkHandler(
          interactor: DependencyProvider.get<CreditAccountInteractor>(),
          navigationProvider: DependencyProvider.get<NavigationProvider>(),
          featureToggleProvider:
              DependencyProvider.get<FeatureToggleProvider>(),
        ),
      )
      ..register(
        () => CreditAutopayFeeCalculationDeeplinkHandler(
          interactor: DependencyProvider.get<CreditAccountInteractor>(),
          navigationProvider: DependencyProvider.get<NavigationProvider>(),
          featureToggleProvider:
              DependencyProvider.get<FeatureToggleProvider>(),
        ),
      )
      ..register(
        () => CreditLearnMoreDeeplinkHandler(
          interactor: DependencyProvider.get<CreditApplicationInteractor>(),
          navigationProvider: DependencyProvider.get<NavigationProvider>(),
          featureToggleProvider:
              DependencyProvider.get<FeatureToggleProvider>(),
        ),
      )
      ..register(
        () => EasyCashBorrowDeeplinkHandler(
          creditAccountInteractor:
              DependencyProvider.get<CreditAccountInteractor>(),
          easyCashInteractor: DependencyProvider.get<EasyCashInteractor>(),
          navigationProvider: DependencyProvider.get<NavigationProvider>(),
          featureToggleProvider:
              DependencyProvider.get<FeatureToggleProvider>(),
        ),
      )
      ..register(
        () => EasyCashDashboardDeeplinkHandler(
          creditAccountInteractor:
              DependencyProvider.get<CreditAccountInteractor>(),
          easyCashInteractor: DependencyProvider.get<EasyCashInteractor>(),
          navigationProvider: DependencyProvider.get<NavigationProvider>(),
          featureToggleProvider:
              DependencyProvider.get<FeatureToggleProvider>(),
        ),
      )
      ..register(
        () => EasyCashRepaymentDeeplinkHandler(
          creditAccountInteractor:
              DependencyProvider.get<CreditAccountInteractor>(),
          easyCashInteractor: DependencyProvider.get<EasyCashInteractor>(),
          navigationProvider: DependencyProvider.get<NavigationProvider>(),
          featureToggleProvider:
              DependencyProvider.get<FeatureToggleProvider>(),
        ),
      )
      ..register(
        () => CreditDeeplinkHandler(
          creditFlow: DependencyProvider.get<CreditFlow>(),
          featureToggleProvider:
              DependencyProvider.get<FeatureToggleProvider>(),
        ),
      );

    DependencyProvider.registerLazySingleton<PosDeeplinkNavigationHandler>(
      () => PosDeeplinkNavigationHandlerImpl(
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        creditAccountInteractor:
            DependencyProvider.get<CreditAccountInteractor>(),
        logger: _logger,
      ),
    );

    DependencyProvider.registerLazySingleton<EasyCashDeeplinkNavigationHandler>(
      () => EasyCashDeeplinkNavigationHandlerImpl(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        creditAccountInteractor:
            DependencyProvider.get<CreditAccountInteractor>(),
        logger: _logger,
        easyCashInteractor: DependencyProvider.get<EasyCashInteractor>(),
      ),
    );

    DependencyProvider.registerLazySingleton<
        CreditDashboardDeeplinkNavigationHandler>(
      () => CreditDashboardDeeplinkNavigationHandlerImpl(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        creditAccountInteractor:
            DependencyProvider.get<CreditAccountInteractor>(),
        logger: _logger,
      ),
    );

    DependencyProvider.registerLazySingleton<
        CreditApplicationDeeplinkNavigationHandler>(
      () => CreditApplicationDeeplinkNavigationHandlerImpl(
        creditApplicationInteractor:
            DependencyProvider.get<CreditApplicationInteractor>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        creditAccountInteractor:
            DependencyProvider.get<CreditAccountInteractor>(),
        logger: _logger,
        changeHomeTabChannel: DependencyProvider.get<ChangeHomeTabChannel>(),
      ),
    );
  }

  static void _registerCreditTransactionsDelegate() {
    DependencyProvider.registerLazySingleton<CreditTransactionsDelegate>(
      () => CreditTransactionsDelegate(
        interactor: DependencyProvider.get<CreditTransactionsInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );
  }
}
