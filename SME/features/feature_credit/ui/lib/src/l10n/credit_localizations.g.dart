// GENERATED CODE
//
// After the template files .arb have been changed,
// generate this class by the command in the terminal:
// flutter pub run lokalise_flutter_sdk:gen-lok-l10n
//
// Please see https://pub.dev/packages/lokalise_flutter_sdk

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes
// ignore_for_file: depend_on_referenced_packages

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart';
import 'package:lokalise_flutter_sdk/lokalise_flutter_sdk.dart';
import 'intl/messages_all.dart';

class CreditLocalizations {
  CreditLocalizations._internal();

  static const LocalizationsDelegate<CreditLocalizations> delegate =
      _AppLocalizationDelegate();

  static const List<Locale> supportedLocales = [
    Locale.fromSubtags(languageCode: 'en'),
    Locale.fromSubtags(languageCode: 'ar')
  ];

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  static final Map<String, List<String>> _metadata = {
    'accountClosureConfirmationCancel': [],
    'accountClosureConfirmationConfirm': [],
    'accountClosureConfirmationDescription': [],
    'accountClosureConfirmationTitle': [],
    'accountClosureOutstandingRepaymentCta': [],
    'accountClosureOutstandingRepaymentDescription': ['amount'],
    'accountClosureOutstandingRepaymentTitle': [],
    'accountClosurePendingTransactionDescription': ['amount'],
    'accountClosurePendingTransactionTitle': [],
    'accountClosureSuccessCta': [],
    'accountClosureSuccessSubtitle': [],
    'accountClosureSuccessTitle': [],
    'activateBusinessCreditCardInfo': [],
    'activateCreditCardBSFooterText': [],
    'activateCreditCardBottomsheetSkipCta': [],
    'activateCreditCardBottomsheetSubtitle': [],
    'activateCreditCardBottomsheetTitle': [],
    'activateNowCta': [],
    'applyCreditBannerTitle': [],
    'applyCreditHeader': [],
    'applyNowCta': [],
    'assignmentLetterBottomSheetDescription': [],
    'assignmentLetterBottomSheetDisclaimer': [],
    'assignmentLetterBottomSheetTitle': [],
    'autopayFeeCalculationEditAutopaySettings': [],
    'autopayFeeCalculationEndedAt': ['date'],
    'autopayFeeCalculationFaq': [],
    'autopayFeeCalculationFeeAmountToday': ['feePercentage'],
    'autopayFeeCalculationFeeAmountTodayDescription': [],
    'autopayFeeCalculationFeeFreePeriod': [],
    'autopayFeeCalculationFeeFreePeriodDescription': ['feeFreeMonthCount'],
    'autopayFeeCalculationHowAutopayCalculated': [],
    'autopayFeeCalculationPercentageOfSpentMoney': ['repaymentPercentage'],
    'autopayFeeCalculationSpentAsOfToday': [],
    'autopayFeeCalculationSpentDescription': [],
    'autopayFeeCalculationStillHaveQuestions': [],
    'autopayFeeCalculationTillDate': ['date'],
    'autopayFeeCalculationWhyAsOfToday': [],
    'autopayFeeCalculationWhyAsOfTodayAnswer': ['date'],
    'autopayFeeCalculationYourAmount': [],
    'autopayFeeCalculationYourAmountDescription': [],
    'autopayFeeCalculationYourAutopaySettings': [],
    'autopayFeeCalculationYourAutopaySettingsDescription': [],
    'autopayFeeCalculationYourOutstandingBalance': [],
    'autopayFeeCalculationYourOutstandingBalanceDescription': [],
    'borrowCta': [],
    'borrowingPowerBSCancel': [],
    'borrowingPowerBSConfirm': [],
    'borrowingPowerBSDescription': [],
    'borrowingPowerBSTitle': ['companyName'],
    'borrowingPowerDocumentUploadPageDescription': [],
    'borrowingPowerDocumentUploadPageInstruction1': [],
    'borrowingPowerDocumentUploadPageInstruction2': [],
    'borrowingPowerDocumentUploadPageTitle': ['companyName'],
    'borrowingPowerUploadDocumentBoxSubtitle': [],
    'borrowingPowerUploadDocumentBoxTitle': [],
    'borrowingPowerWarningMessage': [],
    'businessCreditCardLabel': [],
    'businessLoan': [],
    'businessLoanApplicationSubmittedSuccessDescription': [],
    'businessLoanApplicationSubmittedSuccessSubtitle': [],
    'businessLoanApplicationSubmittedSuccessTitle': [],
    'businessLoanApplyBannerDescription': [],
    'businessLoanBorrowLoanTerm': ['monthCount'],
    'businessLoanBorrowSubtitle': [],
    'businessLoanBorrowTitle': ['userName'],
    'businessLoanFirstPaymentDate': [],
    'businessLoanInterest': ['interest'],
    'businessLoanLastPaymentDate': [],
    'businessLoanLearnMoreApply': [],
    'businessLoanLearnMoreFaq1Ans': [],
    'businessLoanLearnMoreFaq1Que': [],
    'businessLoanLearnMoreFaq2Ans': [],
    'businessLoanLearnMoreFaq2Que': [],
    'businessLoanLearnMoreFaq3Ans': [],
    'businessLoanLearnMoreFaq3Que': [],
    'businessLoanLearnMoreFeature1Desc': [],
    'businessLoanLearnMoreFeature1Title': [],
    'businessLoanLearnMoreFeature2Desc': [],
    'businessLoanLearnMoreFeature2Title': [],
    'businessLoanLearnMoreFeature3Desc': [],
    'businessLoanLearnMoreFeature3Title': [],
    'businessLoanLearnMoreHowItWorksDesc': [],
    'businessLoanLearnMoreSubtitle': [],
    'businessLoanLearnMoreTitle': [],
    'businessLoanLoanAmount': [],
    'businessLoanLoanAmountDesc': [],
    'businessLoanMonthlyInstalment': [],
    'businessLoanOfferReadyMessage': [],
    'businessLoanOneTimeFee': [],
    'businessLoanProcessingFee': [],
    'businessLoanProcessingFeeDesc': [],
    'businessLoanProcessingFeeInsufficientBalance': ['processingFee'],
    'businessLoanRejectedBannerDescription': [],
    'businessLoanThingsToKnowFeatureFour': [],
    'businessLoanThingsToKnowFeatureOne': ['maxRate', 'minRate'],
    'businessLoanThingsToKnowFeatureThree': [
      'lateFeeGracePeriodInDays',
      'latePaymentFee'
    ],
    'businessLoanThingsToKnowFeatureTwo': [],
    'businessLoanTotalAmountToRepay': [],
    'cancelCta': [],
    'cannotUpdateAutopayPercentageError': [],
    'channelFinanceAgreementBottomSheetCta': [],
    'channelFinanceAgreementBottomSheetError': [],
    'closeCta': [],
    'completeApplicationCta': [],
    'completeApplicationDescription': [],
    'completeApplicationTitle': [],
    'completeBusinessLoanApplicationDescription': [],
    'completePosHypothecationPageResendEmail': [],
    'completePosHypothecationPageStep1': [],
    'completePosHypothecationPageStep2': ['posPartnerName'],
    'completePosHypothecationPageStep3': [],
    'completePosHypothecationPageStep3Info': [],
    'completePosHypothecationPageSubtitle': ['email'],
    'completePosHypothecationPageTitle': [],
    'completedRefreshLabel': [],
    'confirmAutodebitAccountInformation': [],
    'confirmAutodebitAccountSubtitle': [],
    'confirmAutodebitAccountTitle': [],
    'confirmAutodebitAutopayFromSSSubtitle': [],
    'confirmAutodebitAutopayFromSSTitle': [],
    'confirmAutodebitSelectAccount': [],
    'confirmCta': [],
    'continueCta': [],
    'creditAccountLockedBannerCTA': [],
    'creditAccountLockedBannerTitle': ['minimumRepaymentAmout'],
    'creditAcknowledgeRejectCta': [],
    'creditAgreementApplicationSubmitSuccessScreenCta': [],
    'creditAgreementApplicationSubmitSuccessScreenDescription': [],
    'creditAgreementApplicationSubmitSuccessScreenTitle': [],
    'creditAgreementBLBannerText': [],
    'creditAgreementPageTitle': [],
    'creditAgreementPosBannerText': [],
    'creditAgreementSignCta': [],
    'creditAmountOnHoldInfoBottomSheetDescription': [],
    'creditAmountOnHoldInfoBottomSheetTitle': [],
    'creditAnnualTurnoverCancelCta': [],
    'creditAnnualTurnoverConfirmCta': [],
    'creditAnnualTurnoverInputLabel': [],
    'creditAnnualTurnoverNextCta': [],
    'creditAnnualTurnoverPageDescription': [],
    'creditAnnualTurnoverPageTitle': [],
    'creditAnnualTurnoverUpdateCta': [],
    'creditAnnualTurnoverUpdateErrorToastMessage': [],
    'creditAnnualTurnoverUpdateSuccessToastMessage': [],
    'creditApplicationRecapAccountBanner': [],
    'creditApplicationRecapAnnualTurnover': [],
    'creditApplicationRecapCompanyBankAccounts': [],
    'creditApplicationRecapErrorMessage': [],
    'creditApplicationRecapFiledAuditedFinancialStatements': [],
    'creditApplicationRecapFiledVatReports': [],
    'creditApplicationRecapFromPreviousPageDescription': [],
    'creditApplicationRecapFromPreviousPageTitle': [],
    'creditApplicationRecapFromPreviousSubmit': [],
    'creditApplicationRecapNoVatReporting': [],
    'creditApplicationRecapOnlyWioBankAccount': [],
    'creditApplicationRecapPageDescription': [],
    'creditApplicationRecapPageTitle': [],
    'creditApplicationRecapPaybackDay': ['day'],
    'creditApplicationRecapPaybackDayTitle': [],
    'creditApplicationRecapSubmit': [],
    'creditApplicationRecapSubmitBanner': [],
    'creditApplicationRecapSuccessMessage': [],
    'creditApplicationRecapVatReportingAnnually': [],
    'creditApplicationRecapVatReportingMethod': [],
    'creditApplicationRecapVatReportingMonthly': [],
    'creditApplicationRecapVatReportingQuarterly': [],
    'creditApplicationRepaymentPlanCta': [],
    'creditApplicationRepaymentPlanDescription': [],
    'creditApplicationRepaymentPlanInterest': [],
    'creditApplicationRepaymentPlanLoanAmount': [],
    'creditApplicationRepaymentPlanLoanTermLabel': [],
    'creditApplicationRepaymentPlanLoanTermValue': ['monthCount'],
    'creditApplicationRepaymentPlanPeriodOrAmountNullError': [],
    'creditApplicationRepaymentPlanProcessingFee': [],
    'creditApplicationRepaymentPlanTitle': [],
    'creditApplicationRepaymentPlanTotalPayback': [],
    'creditApplicationSubmitSuccessPageCta': [],
    'creditApplicationSubmitSuccessPageDescription': [],
    'creditApplicationSubmitSuccessPageTitle': [],
    'creditAuditedFinancialStatementUploadScreenTitle': [],
    'creditAuditedStatementUploadScreenDescriptionDetailThree': [],
    'creditAutoPayFeeCalculation': [],
    'creditAutoPayFeeCalculationDetailsBottomSheetFeeAmount': [],
    'creditAutoPayFeeCalculationDetailsBottomSheetSubtitle': ['feePercentage'],
    'creditAutoPayFeeCalculationDetailsBottomSheetTipText': [],
    'creditAutoPayFeeCalculationDetailsBottomSheetTitle': [],
    'creditAutoPayFeeFreePeriodNoFee': ['date'],
    'creditAutoPayMinimumPaymentToAvoidFees': ['amount'],
    'creditAutoPayNextPayment': ['date'],
    'creditAutoPaySetTo': [],
    'creditAutoPayTotalDue': [],
    'creditAutopayFromSavingSpaceConfirmationAccept': [],
    'creditAutopayFromSavingSpaceConfirmationCancel': [],
    'creditAutopayFromSavingSpaceConfirmationDescription': [],
    'creditAutopayFromSavingSpaceDescription': [],
    'creditAutopayFromSavingSpaceTitle': [],
    'creditAutopayPercentageUpdateSuccessfully': [],
    'creditBankAccountsNoOptionSelectedError': [],
    'creditBannerFooterText': ['creditLoanAmount'],
    'creditCommonToastSomethingWentWrong': [],
    'creditCompanyBankAccountInputScreenCtaText': [],
    'creditCompanyBankAccountOptionOtherBanks': [],
    'creditCompanyBankAccountOptionSelectorTitle': [],
    'creditCompanyBankAccountOptionWioOnly': [],
    'creditCreditLimitReduceSuccessPageButtonTitle': [],
    'creditCreditLimitReduceSuccessPageDescription': ['amount'],
    'creditCreditLimitReduceSuccessPageTitle': [],
    'creditDashboardAutopayAmountLabel': [],
    'creditDashboardAutopayEditButtonTitle': [],
    'creditDashboardAutopayFeeLabel': [],
    'creditDashboardAutopayNoFeeLabel': [],
    'creditDashboardAutopaySectionTitle': ['date'],
    'creditDashboardAvailableLimit': [],
    'creditDashboardAvailableToBorrow': [],
    'creditDashboardBorrow': [],
    'creditDashboardBorrowed': [],
    'creditDashboardCreditLockedTitle': [],
    'creditDashboardCreditTab': [],
    'creditDashboardDueDate': ['date'],
    'creditDashboardEasyCashTab': [],
    'creditDashboardFeePerDay': [],
    'creditDashboardFees': [],
    'creditDashboardLastAutoPaidAmount': ['amount'],
    'creditDashboardLastAutoPayDateLabel': ['date'],
    'creditDashboardLastAutoPayDescription': ['amount', 'currency'],
    'creditDashboardLastAutoPayFee': ['amount'],
    'creditDashboardLatePaymentFee': [],
    'creditDashboardLockedCredit': [],
    'creditDashboardManageButtonTitle': [],
    'creditDashboardPageTitle': [],
    'creditDashboardPayButtonTitle': [],
    'creditDashboardPayback': [],
    'creditDashboardRefreshError': [],
    'creditDashboardSpentLabel': [],
    'creditDashboardStatementButtonTitle': [],
    'creditDashboardTakeMore': [],
    'creditDashboardToSpendLabel': [],
    'creditDashboardTotalDue': [],
    'creditDashboardTransactionsSectionTitle': [],
    'creditDashboardYouBorrowed': [],
    'creditFeeFreePeriodTooltipAcknowledgeCta': [],
    'creditFeeFreePeriodTooltipBody': ['date'],
    'creditFeeFreePeriodTooltipTitle': [],
    'creditGenericErrorScreenCtaText': [],
    'creditGenericErrorScreenDescription': [],
    'creditGenericErrorScreenSubtitle': [],
    'creditGenericErrorScreenTitle': [],
    'creditGenericErrorScreenTryAgain': [],
    'creditIbanInputBottomSheetCtaText': [],
    'creditIbanInputBottomSheetDescription': [],
    'creditIbanInputBottomSheetErrorGeneric': [],
    'creditIbanInputBottomSheetErrorInvalidLength': [],
    'creditIbanInputBottomSheetErrorInvalidStructure': [],
    'creditIbanInputBottomSheetErrorNotUaeIban': [],
    'creditIbanInputBottomSheetInputFieldLabel': [],
    'creditIbanInputBottomSheetTitle': [],
    'creditIneligibilityBottomsheetCta': [],
    'creditLearnMoreCta': [],
    'creditLearnMoreCtaText': [],
    'creditLearnMoreFaqTitle': [],
    'creditLearnMoreFirstFaq': [],
    'creditLearnMoreFirstFaqAnswer': [
      'feePercentage',
      'minumumPayPercentage',
      'feeFreeMonthCount',
      'penalty'
    ],
    'creditLearnMoreFirstFeatureDesc': [],
    'creditLearnMoreFirstFeatureTitle': [],
    'creditLearnMoreHowItWorks': [],
    'creditLearnMoreHowItWorksDesc': [],
    'creditLearnMorePageSubtitle': [],
    'creditLearnMorePageTitle': [],
    'creditLearnMoreSecondFaq': [],
    'creditLearnMoreSecondFaqAnswer': [
      'minumumPayPercentage',
      'feeFreeMonthCount'
    ],
    'creditLearnMoreSecondFeatureDesc': ['feePercentage'],
    'creditLearnMoreSecondFeatureTitle': [],
    'creditLearnMoreThirdFaq': [],
    'creditLearnMoreThirdFaqAnswer': ['feePercentage'],
    'creditLearnMoreThirdFeatureDesc': [
      'minumumPayPercentage',
      'feeFreeMonthCount'
    ],
    'creditLearnMoreThirdFeatureTitle': ['feeFreeMonthCount'],
    'creditLearnMoreWhatYouGet': [],
    'creditLimitCondition1Subtitle': [
      'feeFreeMonthCount',
      'minumumPaymentPercentage'
    ],
    'creditLimitCondition1Title': ['feeFreeMonthCount'],
    'creditLimitCondition2Subtitle': ['feePercentage'],
    'creditLimitCondition2Title': [],
    'creditLimitCondition3Subtitle': [],
    'creditLimitCondition3Title': [],
    'creditLimitConditionsTitle': [],
    'creditLimitEditButtonTitle': [],
    'creditLimitReduceErrorDueToEasyCashBalance': [],
    'creditLimitSaveButtonTitle': [],
    'creditLimitSelectorEditTitle': [],
    'creditLimitSelectorTitle': [],
    'creditLimitSubmitButtonTitle': [],
    'creditLockedBottomsheetDescription': [],
    'creditLockedBottomsheetLatePaymentFee': [],
    'creditLockedBottomsheetMinimumAmountToUnlock': [],
    'creditLockedBottomsheetMinimumPaymentDue': [],
    'creditLockedBottomsheetSubtitle': [],
    'creditLockedBottomsheetTotalOutstanding': ['date'],
    'creditOfferReadyMessage': [],
    'creditOnlyWioBankAccountBottomSheetBody': [],
    'creditOnlyWioBankAccountBottomSheetCtaChangeText': [],
    'creditOnlyWioBankAccountBottomSheetCtaConfirmText': [],
    'creditOnlyWioBankAccountBottomSheetTitle': [],
    'creditOtherBankAccountsBottomSheetDescription': [],
    'creditOtherBankAccountsBottomSheetTitle': [],
    'creditOtherBankAccountsInputPageAddButtonText': [],
    'creditOtherBankAccountsInputPageCtaText': [],
    'creditOtherBankAccountsInputPageDescription': [],
    'creditOtherBankAccountsInputPageTitle': [],
    'creditOverviewDocsText': [],
    'creditOverviewDocsTextKfs': [],
    'creditOverviewDocsTextTnc': [],
    'creditOverviewScreenAuditedFinancialStatementsSubtitle': [],
    'creditOverviewScreenAuditedFinancialStatementsTitle': [],
    'creditOverviewScreenCtaText': [],
    'creditOverviewScreenFirstStepSubtitle': [],
    'creditOverviewScreenFirstStepTitle': [],
    'creditOverviewScreenFirstStepVatStatementExample': [],
    'creditOverviewScreenSecondStepSubtitle': [],
    'creditOverviewScreenSecondStepTitle': [],
    'creditOverviewScreenSubtitle': [],
    'creditOverviewScreenThirdStepSubtitle': [],
    'creditOverviewScreenThirdStepTitle': [],
    'creditOverviewScreenTitle': [],
    'creditPayCreditError': [],
    'creditPaybackOverdueWithFee': ['amount'],
    'creditPaybackOverdueWithoutFee': ['date'],
    'creditPaybackPercent': ['percentage'],
    'creditPaymentDateSetupCta': [],
    'creditPaymentDateSetupDaysNumberExplanation': [],
    'creditPaymentDateSetupPageDescription': [],
    'creditPaymentDateSetupPageTitle': [],
    'creditPaymentDateSetupSelectedDate': ['date'],
    'creditPaymentDateSetupTip': [],
    'creditPaymentPercentageSetupCta': [],
    'creditPaymentPercentageSetupPageTitle': [],
    'creditPaymentPercentageSetupPaybackTip': [],
    'creditPaymentPercentageSetupPaymentBreakdown': ['fee', 'payback', 'spent'],
    'creditPaymentPercentageSetupPaymentBreakdownWithoutFees': ['fee', 'spent'],
    'creditPaymentPercentageSetupSelectedPercentage': ['percentage'],
    'creditSelectorLabel': [],
    'creditStatementPageTitle': [],
    'creditStatementUploadBoxSubtitle': [
      'supportedFileSizeInMb',
      'supportedFileTypes'
    ],
    'creditStatementsEmptyDescription': [],
    'creditStatementsEmptyTitle': [],
    'creditStatementsFileViewerDownloadCta': [],
    'creditStatementsFileViewerTitle': [],
    'creditStatementsYearLabel': [],
    'creditThingsToKnowFeature1': ['annualInterest'],
    'creditThingsToKnowFeature2': [],
    'creditThingsToKnowFeature3': ['minThresholdPercentage'],
    'creditThingsToKnowSubtitle': [],
    'creditUpdateLimitAlreadyRequestedDesc': [],
    'creditUpdateLimitAlreadyRequestedTitle': [],
    'creditUpdateLimitCondition1Subtitle': [],
    'creditUpdateLimitCondition1Title': [],
    'creditUpdateLimitCondition2Subtitle': [],
    'creditUpdateLimitCondition2Title': [],
    'creditUpdateLimitCondition3Subtitle': [],
    'creditUpdateLimitCondition3SubtitleAfterCutoff': [],
    'creditUpdateLimitCondition3Title': [],
    'creditUpdateLimitCta': [],
    'creditUpdateLimitFooterText': [],
    'creditUpdateLimitLabel': [],
    'creditUpdateLimitRequestMore': [],
    'creditUpdateLimitRequestMoreConfirm': [],
    'creditUpdateLimitRequestMoreDesc': [],
    'creditUpdateLimitRequestMoreDismiss': [],
    'creditUpdateLimitRequestMoreSuccessText': [],
    'creditUpdateLimitRequestMoreTitle': ['amount'],
    'creditUpdateLimitSuccessDesc': ['amount'],
    'creditUpdateLimitSuccessTitle': [],
    'creditUpdateOtherBankAccountsIbanError': [],
    'creditUploadTileText': ['nextStatement', 'required'],
    'creditVatReportingIntervalCtaText': [],
    'creditVatReportingIntervalMonthlySubtitle': [],
    'creditVatReportingIntervalMonthlyTitle': [],
    'creditVatReportingIntervalNoReportingSubtitle': [],
    'creditVatReportingIntervalNoReportingTitle': [],
    'creditVatReportingIntervalQuarterlySubtitle': [],
    'creditVatReportingIntervalQuarterlyTitle': [],
    'creditVatReportingMustBeSelectedError': [],
    'creditVatScreenSubtitle': [],
    'creditVatScreenTitle': [],
    'creditVatStatementUploadExample': [],
    'creditVatStatementUploadProgress': ['required', 'x'],
    'creditVatStatementUploadScreenDescriptionDetailOne': [],
    'creditVatStatementUploadScreenDescriptionDetailThree': [],
    'creditVatStatementUploadScreenDescriptionDetailTwo': [],
    'creditVatStatementUploadScreenDescriptionIntro': [],
    'creditVatStatementUploadScreenTitleVatReportingIntervalMonthly': [],
    'creditVatStatementUploadScreenTitleVatReportingIntervalQuaterly': [],
    'creditVatUploadInvalidReportingInterval': [],
    'customStatementLabel': [],
    'customStatementPeriodOneMonth': [],
    'customStatementPeriodOneYear': [],
    'customStatementPeriodSixMonths': [],
    'customStatementPeriodThreeMonths': [],
    'dashboardEasyCashBannerSubTitle': ['amount'],
    'dashboardEasyCashBannerTitle': [],
    'debitAccountUpdateSuccess': [],
    'disclaimerLabel': [],
    'documentViewerShare': [],
    'dueOnLabel': ['date'],
    'easyCashManageScreenFaqSectionTitle': [],
    'easyCashManageScreenTitle': [],
    'embeddedLendingAgreementSubmittedSuccessSubtitle': [],
    'embeddedLendingAgreementSubmittedSuccessTitle': [],
    'embeddedLendingApplicationSubmittedTitle': [],
    'endDateLabel': [],
    'errorMessageActiveEasyCashAccount': [],
    'errorMessageExpiredReferralCode': [],
    'errorMessageInvalidReferralCode': [],
    'faq': [],
    'feeCalculationAutopayAmount': ['repaymentPercentage'],
    'feeCalculationCarryOverAmount': [],
    'feeCalculationCarryOverFee': [],
    'feeCalculationDetailsBottomSheetFeeApplyDescription': ['date'],
    'feeCalculationOutstandingBalance': [],
    'fileSizeExceedsLimit': ['size'],
    'firstKey': [],
    'generateStatementLabel': [],
    'getEasyCashLabel': [],
    'helpCta': [],
    'hypothecationAlreadyCompleteSuccessSubtitle': [],
    'hypothecationAlreadyCompleteSuccessTitle': [],
    'inReviewBannerDescription': [],
    'learnMorePageInvalidProductTypeError': [],
    'lendingAmountOnHoldPayCreditContent': ['holdAmount'],
    'lendingAmountOnHoldPayCreditContentHighlighted': [],
    'lendingAutopaySave': [],
    'lendingCreditDashboardAvlToSpend': [],
    'lendingCreditLimitReduceErrorText': [],
    'lendingFAQ': [],
    'lendingManageAnnualFee': [],
    'lendingManageAvailableToSpend': [],
    'lendingManageCashWithdrawal': [],
    'lendingManageCloseCreditAccountCta': [],
    'lendingManageCreditPageDocuments': [],
    'lendingManageCreditPageDownloadStatement': [],
    'lendingManageCreditPageManageCredit': [],
    'lendingManageCreditPagePaymentDate': [],
    'lendingManageCreditPageRollover': [],
    'lendingManageCreditPageYourCredit': [],
    'lendingManageCreditRepaymentDate': ['day'],
    'lendingManageNeedSomeHelp': [],
    'lendingManageNoAnnualFee': [],
    'lendingManageNoCashWithdrawal': [],
    'lendingManageReduceCreditLimit': [],
    'lendingManageRepaymentTitle': [],
    'lendingManageScreenCreditAgreement': [],
    'lendingManageScreenCreditLimit': [],
    'lendingManageScreenKfs': [],
    'lendingManageStatementsCta': [],
    'lendingNextPayment': ['date', 'month'],
    'lendingNoFee': [],
    'lendingPaybackPercent': ['percentage'],
    'lendingPaymentBreakdown': ['amount', 'fee'],
    'lendingPaymentBreakdownNoFee': ['amount'],
    'lendingPlusFees': [],
    'lendingSelectorLabel': [],
    'lendingSetupPaymentThingsSectionTitle2': [],
    'lendingSetupPaymentThingsSectionsTitle1': [],
    'lendingSetupPaymentThingsToKnowTitle': [],
    'lendingSpentOf': ['creditLimit', 'spent'],
    'loanReferralCodeBottomsheetDescription': [],
    'loanReferralCodeInputLabel': [],
    'loanReferralCodeInvalid': [],
    'loanReferralCodePageSkipInput': [],
    'loanReferralCodePageSubtitle': [],
    'loanReferralCodePageTitle': [],
    'loanRepaymentMilestonesDescription': [],
    'loanRepaymentMilestonesTitle': [],
    'loanRepaymentMissedMilestonesDescription': [],
    'loanTypeInputPageTitle': [],
    'loanTypeLabel': [],
    'loanWhatIsReferralCode': [],
    'managePosAutopayFromSSDescription': [],
    'managePosCreditPageTitle': [],
    'managePosCreditPreferedAccount': [],
    'nextAutoPayLabel': ['date'],
    'nextCta': [],
    'nonReportingVatCtaText': [],
    'otherReasonInputLabel': [],
    'overdueAmountLabel': [],
    'payAndCloseCta': [],
    'payCreditAccountBalance': ['amount'],
    'payCreditAccountTitle': ['currency'],
    'payCreditBelowMinimumPaymentAmount': ['amount'],
    'payCreditExcessivePaymentLabel': ['amount'],
    'payCreditFromLabel': [],
    'payCreditFullAmountOption': ['amount'],
    'payCreditInsufficientBalanceLabel': ['amount'],
    'payCreditMinimumAmountOption': ['amount'],
    'payCreditPageButtonTitle': [],
    'payCreditPageCreditLockedButtonTitle': [],
    'payCreditPageTitle': [],
    'payCreditSuccessPageButtonTitle': [],
    'payCreditSuccessPageCreditUnlocked': [],
    'payCreditSuccessPageDescription': ['amount', 'currency'],
    'payCreditSuccessPageSubtitle': [],
    'payCreditSuccessPageTitle': [],
    'payCreditToLabel': [],
    'payCreditUnlockSuccessPageButtonTitle': [],
    'payCreditUnlockSuccessPageSubtitle': [],
    'payCreditUnlockSuccessPageTitle': [],
    'posApplicationApplyBannerSubtitle': [],
    'posApplicationApplyBannerTitle': [],
    'posApplicationInProgressBannerSubtitle': [],
    'posApplicationInProgressBannerTitle': [],
    'posApplicationOfferBannerCta': [],
    'posApplicationOfferBannerSubtitle': [],
    'posApplicationOfferBannerTitle': [],
    'posApplicationRejectedBannerSubtitle': [],
    'posApplicationRejectedBannerTitle': [],
    'posApplicationUnderReviewBannerTitle': [],
    'posApplicationUnderReviewSubtitle': [],
    'posAvailableToBorrow': ['balance'],
    'posBorrowConfirmationPreBorrowDailyPaymentBanner': [
      'preBorrowDailyPaymentAmount'
    ],
    'posBorrowConfirmationTitle': [],
    'posBorrowExceedsLimitMessage': [],
    'posBorrowInformationTip': ['loanTerm'],
    'posBorrowLoanRepaymentUpdatedMessage': [],
    'posBorrowReadyToReceive': [],
    'posBorrowSubtitle': [],
    'posBorrowSuccessSubtitle': ['currency'],
    'posBorrowSwipeRightToConfirm': [],
    'posBorrowTitle': [],
    'posBottomSheetBorrowPausedBannerDesc': [],
    'posBottomSheetCarryoverField': [],
    'posBottomSheetDailyPaymentAmountField': [],
    'posBottomSheetInterestField': [],
    'posBottomSheetLatePaymentFeeField': [],
    'posBottomSheetMilestoneOverdueTotalAmountField': [],
    'posBottomSheetMissedDueBannerTitle': [],
    'posBottomSheetOverdueAmountField': [],
    'posBottomSheetOverdueAmountFieldWithDueDate': ['date'],
    'posBottomSheetOverdueTitle': [],
    'posBottomSheetPrincipalField': [],
    'posBottomSheetTotalDueOn': ['date'],
    'posBottomSheetTotalWithoutDate': [],
    'posClosureConfirmationCta': [],
    'posClosureConfirmationStep1Bullet1': [],
    'posClosureConfirmationStep1Bullet2': [],
    'posClosureConfirmationStep1Info': [],
    'posClosureConfirmationStep1Title': [],
    'posClosureConfirmationStep2Info': [],
    'posClosureConfirmationStep2Title': [],
    'posClosureConfirmationSubtitle': [],
    'posClosureConfirmationText': [],
    'posClosureConfirmationTitle': [],
    'posClosureSuccessDescription': ['email'],
    'posClosureSuccessSubtitle': [],
    'posClosureSuccessTitle': [],
    'posCompleteHypothecation': [],
    'posCompleteHypothecationDescription': [],
    'posCreditBannerLabel': [],
    'posCreditLabel': [],
    'posCreditMissedPayment': [],
    'posCreditPaybackOverdue': [],
    'posCreditPaybackSuccessDescription': ['amount', 'currency'],
    'posCreditPaybackSuccessSubtitle': [],
    'posCreditRepaymentsOnTrack': [],
    'posCreditTapToBorrow': [],
    'posDashboardAllTransactionTabTitle': [],
    'posDashboardAvailableToBorrow': [],
    'posDashboardBorrowButtonText': [],
    'posDashboardManageButtonText': [],
    'posDashboardPaybackButtonText': [],
    'posDashboardRepaymentsTransactionTabTitle': [],
    'posDashboardTitle': [],
    'posDashboardTitleBorrowed': ['borrowedAmount', 'totalLoanAmount'],
    'posDashboardTransactionSectionTitle': [],
    'posDashboardWithdrawalsTransactionTabTitle': [],
    'posHypothecationEmail': [],
    'posInterestInfoBottomsheetDescription': ['interestRate', 'loanPeriod'],
    'posInterestInfoBottomsheetTip': ['loanPeriod'],
    'posInterestInfoBottomsheetTitle': [],
    'posLearnMoreFaq1Answer': ['max', 'min'],
    'posLearnMoreFaq1Question': [],
    'posLearnMoreFaq2Answer': [],
    'posLearnMoreFaq2Question': [],
    'posLearnMoreFaq3Answer': ['days'],
    'posLearnMoreFaq3Question': [],
    'posLearnMoreFaq4Answer': [],
    'posLearnMoreFaq4Question': [],
    'posLearnMoreHowToApplyDescription': [],
    'posLearnMoreHowToApplyTitle': [],
    'posLearnMorePageDescription': [],
    'posLimitCondition1Subtitle': [],
    'posLimitCondition1Title': [],
    'posLimitCondition2Subtitle': ['days'],
    'posLimitCondition2Title': [],
    'posLimitCondition3Subtitle': [],
    'posLimitCondition3Title': [],
    'posLimitOneTimeFeeTitle': [],
    'posLimitOneTimeSubtitle': [],
    'posLoanInterestLabel': ['loanTerm'],
    'posMilestoneOverdueBannerDescription': [
      'latePaymentFee',
      'missedMilestoneDueDate'
    ],
    'posMinimumBorrowAmountMessage': ['minimumBorrowAmount'],
    'posMissedMilestoneBannerDescription': [
      'dateToAvoidAdditionalCharges',
      'missedMilestoneDueDate'
    ],
    'posNextDailyPaymentBottomSheetDesc': [],
    'posNextDailyPaymentBottomSheetLockInfoBanner': [],
    'posNextDailyPaymentBottomSheetMissedPaymentBannerDesc': ['date'],
    'posNextDailyPaymentBottomSheetMissedPaymentWarning': [
      'missedPaymentCount'
    ],
    'posNextDailyPaymentBottomSheetPaybackCta': [],
    'posNextDailyPaymentBottomSheetTitle': [],
    'posPaidAllDebtBannerContent': [],
    'posThingsToKnowContinueCta': [],
    'posThingsToKnowFeature1': ['minAmount'],
    'posThingsToKnowFeature2': [],
    'posThingsToKnowFeature3': ['loanTerm'],
    'posThingsToKnowFeature4': ['loanTerm'],
    'posThingsToKnowFeatureFour': [],
    'posThingsToKnowFeatureOne': [],
    'posThingsToKnowFeatureThree': [],
    'posThingsToKnowFeatureTwo': [],
    'posThingsToKnowTitle': [],
    'posTotalOutstandingAmountBottomSheetDesc': [],
    'posTotalOutstandingAmountBottomSheetTitle': [],
    'pullDownRefreshLabel': [],
    'reasonNotReportingVatQuestion': [],
    'refreshingLabel': [],
    'rejectedBannerDescription': [],
    'releaseToRefreshLabel': [],
    'repaymentDailyBorrowedAmount': [],
    'repaymentDailyPaymentInfo': [],
    'repaymentDetailsAlreadyBorrowed': [],
    'repaymentDetailsDailyPayment': [],
    'repaymentDetailsDueOn': ['date'],
    'repaymentDetailsInterests': [],
    'repaymentDetailsNextDailyPayment': [],
    'repaymentDetailsPaidLabel': [],
    'repaymentDetailsPrincipal': [],
    'repaymentDetailsTotalLoanAmount': [],
    'repaymentDetailsViewDetails': [],
    'reviewCta': [],
    'reviewYourCreditLimit': [],
    'securedBusinessLoanCardFeature1': ['maxLimit'],
    'securedBusinessLoanCardFeature2': ['maxInterest', 'minInterest'],
    'securedBusinessLoanCardFeature3': [],
    'securedBusinessLoanCardSubtitle': [],
    'securedBusinessLoanCardTitle': [],
    'securedBusinessLoanLabel': [],
    'securedLoanAssignementLetterDescription': ['email'],
    'securedLoanAssignementLetterStep1': [],
    'securedLoanAssignementLetterStep1Desc': [],
    'securedLoanAssignementLetterStep2': [],
    'securedLoanAssignementLetterStep2Desc': [],
    'securedLoanAssignementLetterTitle': [],
    'securedLoanAssignmentLetterBannerTitle': [],
    'securedLoanConfirmationDescription': [],
    'securedLoanConfirmationTitle': [],
    'securedLoanPageTitle': [],
    'securedLoanSelectionDescription': [],
    'securedLoanSelectionTitle': [],
    'securedLoanStep1Description': [],
    'securedLoanStep1Title': [],
    'securedLoanStep2Description': [],
    'securedLoanStep2Title': [],
    'securedLoanStep3Description': [],
    'securedLoanStep3Title': [],
    'securedLoanWhatIsAssignmentLetter': [],
    'selectFileBorrowingPowerTitle': [],
    'selectFileFinancialStatementTitle': [],
    'simpleBusinessLoanCardFeature1': ['maxLimit'],
    'simpleBusinessLoanCardFeature2': ['maxInterest', 'minInterest'],
    'simpleBusinessLoanCardFeature3': [],
    'simpleBusinessLoanCardSubtitle': [],
    'simpleBusinessLoanCardTitle': [],
    'startDateLabel': [],
    'timePeriodLabel': [],
    'totalOutstandingLabel': [],
    'unSecuredBusinessLoanLabel': [],
    'unsecuredLoanSelectionDescription': [],
    'unsecuredLoanSelectionTitle': [],
    'updateCreditLimitRequestMoreSuccessButton': [],
    'vatStatementSampleTitle': [],
    'viewOfferLabel': [],
    'wioBusinessLoanLabel': [],
    'wioLineOfCreditLabel': []
  };

  static Future<CreditLocalizations> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    Lokalise.instance.metadata = _metadata;

    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = CreditLocalizations._internal();
      return instance;
    });
  }

  static CreditLocalizations of(BuildContext context) {
    final instance =
        Localizations.of<CreditLocalizations>(context, CreditLocalizations);
    assert(instance != null,
        'No instance of CreditLocalizations present in the widget tree. Did you add CreditLocalizations.delegate in localizationsDelegates?');
    return instance!;
  }

  /// `Keep credit account`
  String get accountClosureConfirmationCancel {
    return Intl.message(
      'Keep credit account',
      name: 'accountClosureConfirmationCancel',
      desc: '',
      args: [],
    );
  }

  /// `Close credit account`
  String get accountClosureConfirmationConfirm {
    return Intl.message(
      'Close credit account',
      name: 'accountClosureConfirmationConfirm',
      desc: '',
      args: [],
    );
  }

  /// `After you close your account all your cards will be switched to “My Money” automatically. You may apply for a new credit account later.`
  String get accountClosureConfirmationDescription {
    return Intl.message(
      'After you close your account all your cards will be switched to “My Money” automatically. You may apply for a new credit account later.',
      name: 'accountClosureConfirmationDescription',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to close your credit account?`
  String get accountClosureConfirmationTitle {
    return Intl.message(
      'Are you sure you want to close your credit account?',
      name: 'accountClosureConfirmationTitle',
      desc: '',
      args: [],
    );
  }

  /// `Repay full`
  String get accountClosureOutstandingRepaymentCta {
    return Intl.message(
      'Repay full',
      name: 'accountClosureOutstandingRepaymentCta',
      desc: '',
      args: [],
    );
  }

  /// `There is an outstanding amount of {amount} on your account. You must repay the full amount to close your credit.`
  String accountClosureOutstandingRepaymentDescription(String amount) {
    return Intl.message(
      'There is an outstanding amount of $amount on your account. You must repay the full amount to close your credit.',
      name: 'accountClosureOutstandingRepaymentDescription',
      desc: '',
      args: [amount],
    );
  }

  /// `Outstanding repayment`
  String get accountClosureOutstandingRepaymentTitle {
    return Intl.message(
      'Outstanding repayment',
      name: 'accountClosureOutstandingRepaymentTitle',
      desc: '',
      args: [],
    );
  }

  /// `The amount {amount} is pending. Wait for the transactions to complete and try again later.`
  String accountClosurePendingTransactionDescription(String amount) {
    return Intl.message(
      'The amount $amount is pending. Wait for the transactions to complete and try again later.',
      name: 'accountClosurePendingTransactionDescription',
      desc: '',
      args: [amount],
    );
  }

  /// `Pending transactions`
  String get accountClosurePendingTransactionTitle {
    return Intl.message(
      'Pending transactions',
      name: 'accountClosurePendingTransactionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Back to dashboard`
  String get accountClosureSuccessCta {
    return Intl.message(
      'Back to dashboard',
      name: 'accountClosureSuccessCta',
      desc: '',
      args: [],
    );
  }

  /// `Your credit account is closed.`
  String get accountClosureSuccessSubtitle {
    return Intl.message(
      'Your credit account is closed.',
      name: 'accountClosureSuccessSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `IT’S DONE!`
  String get accountClosureSuccessTitle {
    return Intl.message(
      'IT’S DONE!',
      name: 'accountClosureSuccessTitle',
      desc: '',
      args: [],
    );
  }

  /// `Turn any of your Wio Business cards into a credit card!`
  String get activateBusinessCreditCardInfo {
    return Intl.message(
      'Turn any of your Wio Business cards into a credit card!',
      name: 'activateBusinessCreditCardInfo',
      desc: '',
      args: [],
    );
  }

  /// `By selecting ’Activate now’, you agree with the \nTerms & Conditions and the Key Fact Statement`
  String get activateCreditCardBSFooterText {
    return Intl.message(
      'By selecting ’Activate now’, you agree with the \nTerms & Conditions and the Key Fact Statement',
      name: 'activateCreditCardBSFooterText',
      desc: '',
      args: [],
    );
  }

  /// `Skip for now`
  String get activateCreditCardBottomsheetSkipCta {
    return Intl.message(
      'Skip for now',
      name: 'activateCreditCardBottomsheetSkipCta',
      desc: '',
      args: [],
    );
  }

  /// `Apply for Wio Credit and turn any of your Wio Business cards into a credit card.`
  String get activateCreditCardBottomsheetSubtitle {
    return Intl.message(
      'Apply for Wio Credit and turn any of your Wio Business cards into a credit card.',
      name: 'activateCreditCardBottomsheetSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Would you also like to activate your Wio Business credit ?`
  String get activateCreditCardBottomsheetTitle {
    return Intl.message(
      'Would you also like to activate your Wio Business credit ?',
      name: 'activateCreditCardBottomsheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Activate now`
  String get activateNowCta {
    return Intl.message(
      'Activate now',
      name: 'activateNowCta',
      desc: '',
      args: [],
    );
  }

  /// `Turn any of your Wio Business cards into a credit card.`
  String get applyCreditBannerTitle {
    return Intl.message(
      'Turn any of your Wio Business cards into a credit card.',
      name: 'applyCreditBannerTitle',
      desc: '',
      args: [],
    );
  }

  /// `Apply for Wio Business Credit`
  String get applyCreditHeader {
    return Intl.message(
      'Apply for Wio Business Credit',
      name: 'applyCreditHeader',
      desc: '',
      args: [],
    );
  }

  /// `Apply now`
  String get applyNowCta {
    return Intl.message(
      'Apply now',
      name: 'applyNowCta',
      desc: '',
      args: [],
    );
  }

  /// `An assignment letter is a document issued by your payment platform provider (e.g., Network International, PayTabs, Stripe, etc.) confirming that the proceeds from your sales—whether through their platform or POS device—will be directed to your Wio account for the duration of the credit facility.\n\nHow to obtain one: Once your credit offer is approved, we’ll send you the borrowing agreement via email. You can then forward this agreement to your payment processor or acquirer—either through their support channel or by contacting your account manager. Based on this, they will issue the assignment letter.`
  String get assignmentLetterBottomSheetDescription {
    return Intl.message(
      'An assignment letter is a document issued by your payment platform provider (e.g., Network International, PayTabs, Stripe, etc.) confirming that the proceeds from your sales—whether through their platform or POS device—will be directed to your Wio account for the duration of the credit facility.\n\nHow to obtain one: Once your credit offer is approved, we’ll send you the borrowing agreement via email. You can then forward this agreement to your payment processor or acquirer—either through their support channel or by contacting your account manager. Based on this, they will issue the assignment letter.',
      name: 'assignmentLetterBottomSheetDescription',
      desc: '',
      args: [],
    );
  }

  /// `The above is an indicative process. Your payment platform provider may follow a different procedure, and timelines or requirements may vary based on their internal policies.`
  String get assignmentLetterBottomSheetDisclaimer {
    return Intl.message(
      'The above is an indicative process. Your payment platform provider may follow a different procedure, and timelines or requirements may vary based on their internal policies.',
      name: 'assignmentLetterBottomSheetDisclaimer',
      desc: '',
      args: [],
    );
  }

  /// `What is the assignment letter?`
  String get assignmentLetterBottomSheetTitle {
    return Intl.message(
      'What is the assignment letter?',
      name: 'assignmentLetterBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Edit autopay settings`
  String get autopayFeeCalculationEditAutopaySettings {
    return Intl.message(
      'Edit autopay settings',
      name: 'autopayFeeCalculationEditAutopaySettings',
      desc: '',
      args: [],
    );
  }

  /// `Ended at {date}`
  String autopayFeeCalculationEndedAt(String date) {
    return Intl.message(
      'Ended at $date',
      name: 'autopayFeeCalculationEndedAt',
      desc: '',
      args: [date],
    );
  }

  /// `FAQ`
  String get autopayFeeCalculationFaq {
    return Intl.message(
      'FAQ',
      name: 'autopayFeeCalculationFaq',
      desc: '',
      args: [],
    );
  }

  /// `Your {feePercentage}% fee amount as of today`
  String autopayFeeCalculationFeeAmountToday(String feePercentage) {
    return Intl.message(
      'Your $feePercentage% fee amount as of today',
      name: 'autopayFeeCalculationFeeAmountToday',
      desc: '',
      args: [feePercentage],
    );
  }

  /// `Your fee is calculated based on how much your outstanding amount is and if you're on a charge free period.`
  String get autopayFeeCalculationFeeAmountTodayDescription {
    return Intl.message(
      'Your fee is calculated based on how much your outstanding amount is and if you\'re on a charge free period.',
      name: 'autopayFeeCalculationFeeAmountTodayDescription',
      desc: '',
      args: [],
    );
  }

  /// `Your charge free period`
  String get autopayFeeCalculationFeeFreePeriod {
    return Intl.message(
      'Your charge free period',
      name: 'autopayFeeCalculationFeeFreePeriod',
      desc: '',
      args: [],
    );
  }

  /// `If you're still within the {feeFreeMonthCount} months free period, you won't be charged.`
  String autopayFeeCalculationFeeFreePeriodDescription(int feeFreeMonthCount) {
    return Intl.message(
      'If you\'re still within the $feeFreeMonthCount months free period, you won\'t be charged.',
      name: 'autopayFeeCalculationFeeFreePeriodDescription',
      desc: '',
      args: [feeFreeMonthCount],
    );
  }

  /// `How your autopay amount and fee are calculated`
  String get autopayFeeCalculationHowAutopayCalculated {
    return Intl.message(
      'How your autopay amount and fee are calculated',
      name: 'autopayFeeCalculationHowAutopayCalculated',
      desc: '',
      args: [],
    );
  }

  /// `{repaymentPercentage}% of spent money`
  String autopayFeeCalculationPercentageOfSpentMoney(
      String repaymentPercentage) {
    return Intl.message(
      '$repaymentPercentage% of spent money',
      name: 'autopayFeeCalculationPercentageOfSpentMoney',
      desc: '',
      args: [repaymentPercentage],
    );
  }

  /// `You spent as of today:`
  String get autopayFeeCalculationSpentAsOfToday {
    return Intl.message(
      'You spent as of today:',
      name: 'autopayFeeCalculationSpentAsOfToday',
      desc: '',
      args: [],
    );
  }

  /// `First, we look at how much you spent from Wio Business Credit.`
  String get autopayFeeCalculationSpentDescription {
    return Intl.message(
      'First, we look at how much you spent from Wio Business Credit.',
      name: 'autopayFeeCalculationSpentDescription',
      desc: '',
      args: [],
    );
  }

  /// `Still have questions?`
  String get autopayFeeCalculationStillHaveQuestions {
    return Intl.message(
      'Still have questions?',
      name: 'autopayFeeCalculationStillHaveQuestions',
      desc: '',
      args: [],
    );
  }

  /// `Till {date}`
  String autopayFeeCalculationTillDate(String date) {
    return Intl.message(
      'Till $date',
      name: 'autopayFeeCalculationTillDate',
      desc: '',
      args: [date],
    );
  }

  /// `Why “as of today”?`
  String get autopayFeeCalculationWhyAsOfToday {
    return Intl.message(
      'Why “as of today”?',
      name: 'autopayFeeCalculationWhyAsOfToday',
      desc: '',
      args: [],
    );
  }

  /// `You might keep spending before your autopay date. Your final autopay amount will be calculated on: {date}.`
  String autopayFeeCalculationWhyAsOfTodayAnswer(String date) {
    return Intl.message(
      'You might keep spending before your autopay date. Your final autopay amount will be calculated on: $date.',
      name: 'autopayFeeCalculationWhyAsOfTodayAnswer',
      desc: '',
      args: [date],
    );
  }

  /// `Your autopay amount as of today:`
  String get autopayFeeCalculationYourAmount {
    return Intl.message(
      'Your autopay amount as of today:',
      name: 'autopayFeeCalculationYourAmount',
      desc: '',
      args: [],
    );
  }

  /// `Your autopay amount is calculate based on how much you spend and how much you set up to pay back.`
  String get autopayFeeCalculationYourAmountDescription {
    return Intl.message(
      'Your autopay amount is calculate based on how much you spend and how much you set up to pay back.',
      name: 'autopayFeeCalculationYourAmountDescription',
      desc: '',
      args: [],
    );
  }

  /// `Your autopay settings:`
  String get autopayFeeCalculationYourAutopaySettings {
    return Intl.message(
      'Your autopay settings:',
      name: 'autopayFeeCalculationYourAutopaySettings',
      desc: '',
      args: [],
    );
  }

  /// `Next, we look at how much you set up to pay back. You can change it at any time.`
  String get autopayFeeCalculationYourAutopaySettingsDescription {
    return Intl.message(
      'Next, we look at how much you set up to pay back. You can change it at any time.',
      name: 'autopayFeeCalculationYourAutopaySettingsDescription',
      desc: '',
      args: [],
    );
  }

  /// `Your outstanding balance`
  String get autopayFeeCalculationYourOutstandingBalance {
    return Intl.message(
      'Your outstanding balance',
      name: 'autopayFeeCalculationYourOutstandingBalance',
      desc: '',
      args: [],
    );
  }

  /// `To calculate the fee, we look at how much is your outstanding amount after you've done your monthly payment.`
  String get autopayFeeCalculationYourOutstandingBalanceDescription {
    return Intl.message(
      'To calculate the fee, we look at how much is your outstanding amount after you\'ve done your monthly payment.',
      name: 'autopayFeeCalculationYourOutstandingBalanceDescription',
      desc: '',
      args: [],
    );
  }

  /// `Borrow`
  String get borrowCta {
    return Intl.message(
      'Borrow',
      name: 'borrowCta',
      desc: '',
      args: [],
    );
  }

  /// `No`
  String get borrowingPowerBSCancel {
    return Intl.message(
      'No',
      name: 'borrowingPowerBSCancel',
      desc: '',
      args: [],
    );
  }

  /// `Yes`
  String get borrowingPowerBSConfirm {
    return Intl.message(
      'Yes',
      name: 'borrowingPowerBSConfirm',
      desc: '',
      args: [],
    );
  }

  /// `Only authorized signatories with borrowing power can apply for Wio Business credit`
  String get borrowingPowerBSDescription {
    return Intl.message(
      'Only authorized signatories with borrowing power can apply for Wio Business credit',
      name: 'borrowingPowerBSDescription',
      desc: '',
      args: [],
    );
  }

  /// `Do you have the borrowing power for {companyName}`
  String borrowingPowerBSTitle(String companyName) {
    return Intl.message(
      'Do you have the borrowing power for $companyName',
      name: 'borrowingPowerBSTitle',
      desc: '',
      args: [companyName],
    );
  }

  /// `Please upload one of the following documents clearly stating you have the borrowing power for your company :`
  String get borrowingPowerDocumentUploadPageDescription {
    return Intl.message(
      'Please upload one of the following documents clearly stating you have the borrowing power for your company :',
      name: 'borrowingPowerDocumentUploadPageDescription',
      desc: '',
      args: [],
    );
  }

  /// `Power of attorney`
  String get borrowingPowerDocumentUploadPageInstruction1 {
    return Intl.message(
      'Power of attorney',
      name: 'borrowingPowerDocumentUploadPageInstruction1',
      desc: '',
      args: [],
    );
  }

  /// `Memorandum of association`
  String get borrowingPowerDocumentUploadPageInstruction2 {
    return Intl.message(
      'Memorandum of association',
      name: 'borrowingPowerDocumentUploadPageInstruction2',
      desc: '',
      args: [],
    );
  }

  /// `Confirm you have the borrowing power for {companyName}`
  String borrowingPowerDocumentUploadPageTitle(String companyName) {
    return Intl.message(
      'Confirm you have the borrowing power for $companyName',
      name: 'borrowingPowerDocumentUploadPageTitle',
      desc: '',
      args: [companyName],
    );
  }

  /// `PDF, JPG, PNG  max 50MB`
  String get borrowingPowerUploadDocumentBoxSubtitle {
    return Intl.message(
      'PDF, JPG, PNG  max 50MB',
      name: 'borrowingPowerUploadDocumentBoxSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Upload document`
  String get borrowingPowerUploadDocumentBoxTitle {
    return Intl.message(
      'Upload document',
      name: 'borrowingPowerUploadDocumentBoxTitle',
      desc: '',
      args: [],
    );
  }

  /// `Only authorized signatories with borrowing power can apply for Wio Business credit`
  String get borrowingPowerWarningMessage {
    return Intl.message(
      'Only authorized signatories with borrowing power can apply for Wio Business credit',
      name: 'borrowingPowerWarningMessage',
      desc: '',
      args: [],
    );
  }

  /// `Business credit card`
  String get businessCreditCardLabel {
    return Intl.message(
      'Business credit card',
      name: 'businessCreditCardLabel',
      desc: '',
      args: [],
    );
  }

  /// `Business Loan`
  String get businessLoan {
    return Intl.message(
      'Business Loan',
      name: 'businessLoan',
      desc: '',
      args: [],
    );
  }

  /// `It will be in your account soon`
  String get businessLoanApplicationSubmittedSuccessDescription {
    return Intl.message(
      'It will be in your account soon',
      name: 'businessLoanApplicationSubmittedSuccessDescription',
      desc: '',
      args: [],
    );
  }

  /// `Your money is on it’s way!`
  String get businessLoanApplicationSubmittedSuccessSubtitle {
    return Intl.message(
      'Your money is on it’s way!',
      name: 'businessLoanApplicationSubmittedSuccessSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Done`
  String get businessLoanApplicationSubmittedSuccessTitle {
    return Intl.message(
      'Done',
      name: 'businessLoanApplicationSubmittedSuccessTitle',
      desc: '',
      args: [],
    );
  }

  /// `Flexible and tailored for you. Apply now to see how much you can borrow.`
  String get businessLoanApplyBannerDescription {
    return Intl.message(
      'Flexible and tailored for you. Apply now to see how much you can borrow.',
      name: 'businessLoanApplyBannerDescription',
      desc: '',
      args: [],
    );
  }

  /// `{monthCount} months`
  String businessLoanBorrowLoanTerm(String monthCount) {
    return Intl.message(
      '$monthCount months',
      name: 'businessLoanBorrowLoanTerm',
      desc: '',
      args: [monthCount],
    );
  }

  /// `Pick your loan term, set your amount, check the details, and get the money instantly in your Wio Business bank account.`
  String get businessLoanBorrowSubtitle {
    return Intl.message(
      'Pick your loan term, set your amount, check the details, and get the money instantly in your Wio Business bank account.',
      name: 'businessLoanBorrowSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Hi {userName}! Here’s your loan offer`
  String businessLoanBorrowTitle(String userName) {
    return Intl.message(
      'Hi $userName! Here’s your loan offer',
      name: 'businessLoanBorrowTitle',
      desc: '',
      args: [userName],
    );
  }

  /// `First payment date`
  String get businessLoanFirstPaymentDate {
    return Intl.message(
      'First payment date',
      name: 'businessLoanFirstPaymentDate',
      desc: '',
      args: [],
    );
  }

  /// `Total interest ({interest}% p.a.)`
  String businessLoanInterest(String interest) {
    return Intl.message(
      'Total interest ($interest% p.a.)',
      name: 'businessLoanInterest',
      desc: '',
      args: [interest],
    );
  }

  /// `Last payment date`
  String get businessLoanLastPaymentDate {
    return Intl.message(
      'Last payment date',
      name: 'businessLoanLastPaymentDate',
      desc: '',
      args: [],
    );
  }

  /// `Apply`
  String get businessLoanLearnMoreApply {
    return Intl.message(
      'Apply',
      name: 'businessLoanLearnMoreApply',
      desc: '',
      args: [],
    );
  }

  /// `You'll pay a fixed monthly installment covering the principal and interest. A loan processing fee and early repayment fee may apply.`
  String get businessLoanLearnMoreFaq1Ans {
    return Intl.message(
      'You\'ll pay a fixed monthly installment covering the principal and interest. A loan processing fee and early repayment fee may apply.',
      name: 'businessLoanLearnMoreFaq1Ans',
      desc: '',
      args: [],
    );
  }

  /// `What are the costs?`
  String get businessLoanLearnMoreFaq1Que {
    return Intl.message(
      'What are the costs?',
      name: 'businessLoanLearnMoreFaq1Que',
      desc: '',
      args: [],
    );
  }

  /// `You'll need to provide a VAT document (if applicable) and the IBAN of your turnover accounts.`
  String get businessLoanLearnMoreFaq2Ans {
    return Intl.message(
      'You\'ll need to provide a VAT document (if applicable) and the IBAN of your turnover accounts.',
      name: 'businessLoanLearnMoreFaq2Ans',
      desc: '',
      args: [],
    );
  }

  /// `What is required?`
  String get businessLoanLearnMoreFaq2Que {
    return Intl.message(
      'What is required?',
      name: 'businessLoanLearnMoreFaq2Que',
      desc: '',
      args: [],
    );
  }

  /// `A late fee may apply, and your credit score may be impacted. Ensure timely payments to avoid penalties.`
  String get businessLoanLearnMoreFaq3Ans {
    return Intl.message(
      'A late fee may apply, and your credit score may be impacted. Ensure timely payments to avoid penalties.',
      name: 'businessLoanLearnMoreFaq3Ans',
      desc: '',
      args: [],
    );
  }

  /// `What happens if I miss a repayment?`
  String get businessLoanLearnMoreFaq3Que {
    return Intl.message(
      'What happens if I miss a repayment?',
      name: 'businessLoanLearnMoreFaq3Que',
      desc: '',
      args: [],
    );
  }

  /// `Your business deserves a solution that fits, and we’re here to make it happen`
  String get businessLoanLearnMoreFeature1Desc {
    return Intl.message(
      'Your business deserves a solution that fits, and we’re here to make it happen',
      name: 'businessLoanLearnMoreFeature1Desc',
      desc: '',
      args: [],
    );
  }

  /// `Tailored to loan`
  String get businessLoanLearnMoreFeature1Title {
    return Intl.message(
      'Tailored to loan',
      name: 'businessLoanLearnMoreFeature1Title',
      desc: '',
      args: [],
    );
  }

  /// `The money is credited directly to your Wio Business account and ready to be spent`
  String get businessLoanLearnMoreFeature2Desc {
    return Intl.message(
      'The money is credited directly to your Wio Business account and ready to be spent',
      name: 'businessLoanLearnMoreFeature2Desc',
      desc: '',
      args: [],
    );
  }

  /// `Money straight in your account`
  String get businessLoanLearnMoreFeature2Title {
    return Intl.message(
      'Money straight in your account',
      name: 'businessLoanLearnMoreFeature2Title',
      desc: '',
      args: [],
    );
  }

  /// `Choose a loan term from 3 to 48 months, then repay with automatic monthly installments`
  String get businessLoanLearnMoreFeature3Desc {
    return Intl.message(
      'Choose a loan term from 3 to 48 months, then repay with automatic monthly installments',
      name: 'businessLoanLearnMoreFeature3Desc',
      desc: '',
      args: [],
    );
  }

  /// `Flexible and effortless repayment`
  String get businessLoanLearnMoreFeature3Title {
    return Intl.message(
      'Flexible and effortless repayment',
      name: 'businessLoanLearnMoreFeature3Title',
      desc: '',
      args: [],
    );
  }

  /// `Submit your application through the app. Approval typically takes 3 business days. After approval, you can adjust your loan amount and choose a repayment term. `
  String get businessLoanLearnMoreHowItWorksDesc {
    return Intl.message(
      'Submit your application through the app. Approval typically takes 3 business days. After approval, you can adjust your loan amount and choose a repayment term. ',
      name: 'businessLoanLearnMoreHowItWorksDesc',
      desc: '',
      args: [],
    );
  }

  /// `Flexible and tailored for you. Apply now to see how much you can borrow.`
  String get businessLoanLearnMoreSubtitle {
    return Intl.message(
      'Flexible and tailored for you. Apply now to see how much you can borrow.',
      name: 'businessLoanLearnMoreSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Get the loan that treats you right`
  String get businessLoanLearnMoreTitle {
    return Intl.message(
      'Get the loan that treats you right',
      name: 'businessLoanLearnMoreTitle',
      desc: '',
      args: [],
    );
  }

  /// `Loan amount`
  String get businessLoanLoanAmount {
    return Intl.message(
      'Loan amount',
      name: 'businessLoanLoanAmount',
      desc: '',
      args: [],
    );
  }

  /// `This is the total amount that will be credited to your Wio Business account. The maximum you can borrow depends on the loan term you choose.`
  String get businessLoanLoanAmountDesc {
    return Intl.message(
      'This is the total amount that will be credited to your Wio Business account. The maximum you can borrow depends on the loan term you choose.',
      name: 'businessLoanLoanAmountDesc',
      desc: '',
      args: [],
    );
  }

  /// `Monthly instalment`
  String get businessLoanMonthlyInstalment {
    return Intl.message(
      'Monthly instalment',
      name: 'businessLoanMonthlyInstalment',
      desc: '',
      args: [],
    );
  }

  /// `Your loan offer's ready!  Let’s take a look.`
  String get businessLoanOfferReadyMessage {
    return Intl.message(
      'Your loan offer\'s ready!  Let’s take a look.',
      name: 'businessLoanOfferReadyMessage',
      desc: '',
      args: [],
    );
  }

  /// `One-time processing fee`
  String get businessLoanOneTimeFee {
    return Intl.message(
      'One-time processing fee',
      name: 'businessLoanOneTimeFee',
      desc: '',
      args: [],
    );
  }

  /// `Processing fee `
  String get businessLoanProcessingFee {
    return Intl.message(
      'Processing fee ',
      name: 'businessLoanProcessingFee',
      desc: '',
      args: [],
    );
  }

  /// `This is a one-time fee that will be charged once you borrow the selected amount and sign the loan agreement.`
  String get businessLoanProcessingFeeDesc {
    return Intl.message(
      'This is a one-time fee that will be charged once you borrow the selected amount and sign the loan agreement.',
      name: 'businessLoanProcessingFeeDesc',
      desc: '',
      args: [],
    );
  }

  /// `Your Wio Account balance is too low. Please make sure you have at least {processingFee} to cover the one-time processing fee and continue with your loan.`
  String businessLoanProcessingFeeInsufficientBalance(String processingFee) {
    return Intl.message(
      'Your Wio Account balance is too low. Please make sure you have at least $processingFee to cover the one-time processing fee and continue with your loan.',
      name: 'businessLoanProcessingFeeInsufficientBalance',
      desc: '',
      args: [processingFee],
    );
  }

  /// `Unfortunately, your application for Wio Business Loan has been declined. Your company doesn't meet all the required criteria. You can try again in three months.`
  String get businessLoanRejectedBannerDescription {
    return Intl.message(
      'Unfortunately, your application for Wio Business Loan has been declined. Your company doesn\'t meet all the required criteria. You can try again in three months.',
      name: 'businessLoanRejectedBannerDescription',
      desc: '',
      args: [],
    );
  }

  /// `Once confirmed, the transaction can't be reversed.`
  String get businessLoanThingsToKnowFeatureFour {
    return Intl.message(
      'Once confirmed, the transaction can\'t be reversed.',
      name: 'businessLoanThingsToKnowFeatureFour',
      desc: '',
      args: [],
    );
  }

  /// `An interest will be applied at an annual interest rate from {minRate}% to {maxRate}%. `
  String businessLoanThingsToKnowFeatureOne(String maxRate, String minRate) {
    return Intl.message(
      'An interest will be applied at an annual interest rate from $minRate% to $maxRate%. ',
      name: 'businessLoanThingsToKnowFeatureOne',
      desc: '',
      args: [maxRate, minRate],
    );
  }

  /// `A late payment fee of {latePaymentFee} will be applied if payment is not made within {lateFeeGracePeriodInDays} days after your due date.`
  String businessLoanThingsToKnowFeatureThree(
      String lateFeeGracePeriodInDays, String latePaymentFee) {
    return Intl.message(
      'A late payment fee of $latePaymentFee will be applied if payment is not made within $lateFeeGracePeriodInDays days after your due date.',
      name: 'businessLoanThingsToKnowFeatureThree',
      desc: '',
      args: [lateFeeGracePeriodInDays, latePaymentFee],
    );
  }

  /// `Monthly payments will be deducted automatically from your current account and/or saving spaces.`
  String get businessLoanThingsToKnowFeatureTwo {
    return Intl.message(
      'Monthly payments will be deducted automatically from your current account and/or saving spaces.',
      name: 'businessLoanThingsToKnowFeatureTwo',
      desc: '',
      args: [],
    );
  }

  /// `Total amount to repay`
  String get businessLoanTotalAmountToRepay {
    return Intl.message(
      'Total amount to repay',
      name: 'businessLoanTotalAmountToRepay',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get cancelCta {
    return Intl.message(
      'Cancel',
      name: 'cancelCta',
      desc: '',
      args: [],
    );
  }

  /// `Please come later after you use some credit money`
  String get cannotUpdateAutopayPercentageError {
    return Intl.message(
      'Please come later after you use some credit money',
      name: 'cannotUpdateAutopayPercentageError',
      desc: '',
      args: [],
    );
  }

  /// `I accept`
  String get channelFinanceAgreementBottomSheetCta {
    return Intl.message(
      'I accept',
      name: 'channelFinanceAgreementBottomSheetCta',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong while getting the agreement`
  String get channelFinanceAgreementBottomSheetError {
    return Intl.message(
      'Something went wrong while getting the agreement',
      name: 'channelFinanceAgreementBottomSheetError',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get closeCta {
    return Intl.message(
      'Close',
      name: 'closeCta',
      desc: '',
      args: [],
    );
  }

  /// `Complete application`
  String get completeApplicationCta {
    return Intl.message(
      'Complete application',
      name: 'completeApplicationCta',
      desc: '',
      args: [],
    );
  }

  /// `You’ve got only a few steps left to complete your Wio Business Credit application`
  String get completeApplicationDescription {
    return Intl.message(
      'You’ve got only a few steps left to complete your Wio Business Credit application',
      name: 'completeApplicationDescription',
      desc: '',
      args: [],
    );
  }

  /// `Complete your application`
  String get completeApplicationTitle {
    return Intl.message(
      'Complete your application',
      name: 'completeApplicationTitle',
      desc: '',
      args: [],
    );
  }

  /// `You’ve got only a few steps left to complete your Wio Business Loan application`
  String get completeBusinessLoanApplicationDescription {
    return Intl.message(
      'You’ve got only a few steps left to complete your Wio Business Loan application',
      name: 'completeBusinessLoanApplicationDescription',
      desc: '',
      args: [],
    );
  }

  /// `Resend email`
  String get completePosHypothecationPageResendEmail {
    return Intl.message(
      'Resend email',
      name: 'completePosHypothecationPageResendEmail',
      desc: '',
      args: [],
    );
  }

  /// `Borrow agreement signed`
  String get completePosHypothecationPageStep1 {
    return Intl.message(
      'Borrow agreement signed',
      name: 'completePosHypothecationPageStep1',
      desc: '',
      args: [],
    );
  }

  /// `Details transmitted to {posPartnerName}`
  String completePosHypothecationPageStep2(String posPartnerName) {
    return Intl.message(
      'Details transmitted to $posPartnerName',
      name: 'completePosHypothecationPageStep2',
      desc: '',
      args: [posPartnerName],
    );
  }

  /// `Complete POS hypothecation`
  String get completePosHypothecationPageStep3 {
    return Intl.message(
      'Complete POS hypothecation',
      name: 'completePosHypothecationPageStep3',
      desc: '',
      args: [],
    );
  }

  /// `This final step secures your credit line and enables repayments through your POS transactions.`
  String get completePosHypothecationPageStep3Info {
    return Intl.message(
      'This final step secures your credit line and enables repayments through your POS transactions.',
      name: 'completePosHypothecationPageStep3Info',
      desc: '',
      args: [],
    );
  }

  /// `To start using your credit line, follow the instruction sent to {email} to complete the hypothecation of your POS.`
  String completePosHypothecationPageSubtitle(String email) {
    return Intl.message(
      'To start using your credit line, follow the instruction sent to $email to complete the hypothecation of your POS.',
      name: 'completePosHypothecationPageSubtitle',
      desc: '',
      args: [email],
    );
  }

  /// `Almost there !`
  String get completePosHypothecationPageTitle {
    return Intl.message(
      'Almost there !',
      name: 'completePosHypothecationPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Completed`
  String get completedRefreshLabel {
    return Intl.message(
      'Completed',
      name: 'completedRefreshLabel',
      desc: '',
      args: [],
    );
  }

  /// `In case of insufficient balance other Wio Business AED accounts will be debited for repayment`
  String get confirmAutodebitAccountInformation {
    return Intl.message(
      'In case of insufficient balance other Wio Business AED accounts will be debited for repayment',
      name: 'confirmAutodebitAccountInformation',
      desc: '',
      args: [],
    );
  }

  /// `Wio will auto-debit this account for your daily repayments and possible overdues.`
  String get confirmAutodebitAccountSubtitle {
    return Intl.message(
      'Wio will auto-debit this account for your daily repayments and possible overdues.',
      name: 'confirmAutodebitAccountSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Confirm the account for withdrawals and repayments`
  String get confirmAutodebitAccountTitle {
    return Intl.message(
      'Confirm the account for withdrawals and repayments',
      name: 'confirmAutodebitAccountTitle',
      desc: '',
      args: [],
    );
  }

  /// `Allow Wio to withdraw money from your saving spaces if you don’t have enough balance in your AED current  accounts`
  String get confirmAutodebitAutopayFromSSSubtitle {
    return Intl.message(
      'Allow Wio to withdraw money from your saving spaces if you don’t have enough balance in your AED current  accounts',
      name: 'confirmAutodebitAutopayFromSSSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Repay from Saving Space`
  String get confirmAutodebitAutopayFromSSTitle {
    return Intl.message(
      'Repay from Saving Space',
      name: 'confirmAutodebitAutopayFromSSTitle',
      desc: '',
      args: [],
    );
  }

  /// `Select account`
  String get confirmAutodebitSelectAccount {
    return Intl.message(
      'Select account',
      name: 'confirmAutodebitSelectAccount',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get confirmCta {
    return Intl.message(
      'Confirm',
      name: 'confirmCta',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get continueCta {
    return Intl.message(
      'Continue',
      name: 'continueCta',
      desc: '',
      args: [],
    );
  }

  /// `Unlock your credit`
  String get creditAccountLockedBannerCTA {
    return Intl.message(
      'Unlock your credit',
      name: 'creditAccountLockedBannerCTA',
      desc: '',
      args: [],
    );
  }

  /// `Your credit has been locked because you\nhaven't paid the minimum amount due. To\nunlock please pay minimum of {minimumRepaymentAmout}.`
  String creditAccountLockedBannerTitle(String minimumRepaymentAmout) {
    return Intl.message(
      'Your credit has been locked because you\nhaven\'t paid the minimum amount due. To\nunlock please pay minimum of $minimumRepaymentAmout.',
      name: 'creditAccountLockedBannerTitle',
      desc: '',
      args: [minimumRepaymentAmout],
    );
  }

  /// `Got it`
  String get creditAcknowledgeRejectCta {
    return Intl.message(
      'Got it',
      name: 'creditAcknowledgeRejectCta',
      desc: '',
      args: [],
    );
  }

  /// `Show me how`
  String get creditAgreementApplicationSubmitSuccessScreenCta {
    return Intl.message(
      'Show me how',
      name: 'creditAgreementApplicationSubmitSuccessScreenCta',
      desc: '',
      args: [],
    );
  }

  /// `From now on, easily choose between debit or credit for your spends.`
  String get creditAgreementApplicationSubmitSuccessScreenDescription {
    return Intl.message(
      'From now on, easily choose between debit or credit for your spends.',
      name: 'creditAgreementApplicationSubmitSuccessScreenDescription',
      desc: '',
      args: [],
    );
  }

  /// `Wio Business Credit Unlocked!`
  String get creditAgreementApplicationSubmitSuccessScreenTitle {
    return Intl.message(
      'Wio Business Credit Unlocked!',
      name: 'creditAgreementApplicationSubmitSuccessScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Once signed, the transaction can’t be reversed`
  String get creditAgreementBLBannerText {
    return Intl.message(
      'Once signed, the transaction can’t be reversed',
      name: 'creditAgreementBLBannerText',
      desc: '',
      args: [],
    );
  }

  /// `Borrow agreement`
  String get creditAgreementPageTitle {
    return Intl.message(
      'Borrow agreement',
      name: 'creditAgreementPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Once confirmed, the transaction can’t be reversed, as we will open a credit account immediately upon approval of your application.`
  String get creditAgreementPosBannerText {
    return Intl.message(
      'Once confirmed, the transaction can’t be reversed, as we will open a credit account immediately upon approval of your application.',
      name: 'creditAgreementPosBannerText',
      desc: '',
      args: [],
    );
  }

  /// `Sign via SMS`
  String get creditAgreementSignCta {
    return Intl.message(
      'Sign via SMS',
      name: 'creditAgreementSignCta',
      desc: '',
      args: [],
    );
  }

  /// `When you use your card to make a payment, the transaction starts with a 'pending' state. After you pay, the seller checks everything is okay, and when they're sure, we change the status to 'completed'. This usually happens with 1-3 days after you make a payment.`
  String get creditAmountOnHoldInfoBottomSheetDescription {
    return Intl.message(
      'When you use your card to make a payment, the transaction starts with a \'pending\' state. After you pay, the seller checks everything is okay, and when they\'re sure, we change the status to \'completed\'. This usually happens with 1-3 days after you make a payment.',
      name: 'creditAmountOnHoldInfoBottomSheetDescription',
      desc: '',
      args: [],
    );
  }

  /// `Pending transactions`
  String get creditAmountOnHoldInfoBottomSheetTitle {
    return Intl.message(
      'Pending transactions',
      name: 'creditAmountOnHoldInfoBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get creditAnnualTurnoverCancelCta {
    return Intl.message(
      'Cancel',
      name: 'creditAnnualTurnoverCancelCta',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get creditAnnualTurnoverConfirmCta {
    return Intl.message(
      'Confirm',
      name: 'creditAnnualTurnoverConfirmCta',
      desc: '',
      args: [],
    );
  }

  /// `Annual turnover `
  String get creditAnnualTurnoverInputLabel {
    return Intl.message(
      'Annual turnover ',
      name: 'creditAnnualTurnoverInputLabel',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get creditAnnualTurnoverNextCta {
    return Intl.message(
      'Next',
      name: 'creditAnnualTurnoverNextCta',
      desc: '',
      args: [],
    );
  }

  /// `Turnover is the total credits your company is expecting to receive across all accounts held by your company in the UAE.`
  String get creditAnnualTurnoverPageDescription {
    return Intl.message(
      'Turnover is the total credits your company is expecting to receive across all accounts held by your company in the UAE.',
      name: 'creditAnnualTurnoverPageDescription',
      desc: '',
      args: [],
    );
  }

  /// `Confirm this your company's annual turnover`
  String get creditAnnualTurnoverPageTitle {
    return Intl.message(
      'Confirm this your company\'s annual turnover',
      name: 'creditAnnualTurnoverPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Update`
  String get creditAnnualTurnoverUpdateCta {
    return Intl.message(
      'Update',
      name: 'creditAnnualTurnoverUpdateCta',
      desc: '',
      args: [],
    );
  }

  /// `Oops we couldn’t update your info. Please try again.`
  String get creditAnnualTurnoverUpdateErrorToastMessage {
    return Intl.message(
      'Oops we couldn’t update your info. Please try again.',
      name: 'creditAnnualTurnoverUpdateErrorToastMessage',
      desc: '',
      args: [],
    );
  }

  /// `Turnover information updated !`
  String get creditAnnualTurnoverUpdateSuccessToastMessage {
    return Intl.message(
      'Turnover information updated !',
      name: 'creditAnnualTurnoverUpdateSuccessToastMessage',
      desc: '',
      args: [],
    );
  }

  /// `A credit account will be created immediately after the approval of your application`
  String get creditApplicationRecapAccountBanner {
    return Intl.message(
      'A credit account will be created immediately after the approval of your application',
      name: 'creditApplicationRecapAccountBanner',
      desc: '',
      args: [],
    );
  }

  /// `Annual turnover`
  String get creditApplicationRecapAnnualTurnover {
    return Intl.message(
      'Annual turnover',
      name: 'creditApplicationRecapAnnualTurnover',
      desc: '',
      args: [],
    );
  }

  /// `Company bank accounts`
  String get creditApplicationRecapCompanyBankAccounts {
    return Intl.message(
      'Company bank accounts',
      name: 'creditApplicationRecapCompanyBankAccounts',
      desc: '',
      args: [],
    );
  }

  /// `Oops we couldn’t update your information. Please try again.`
  String get creditApplicationRecapErrorMessage {
    return Intl.message(
      'Oops we couldn’t update your information. Please try again.',
      name: 'creditApplicationRecapErrorMessage',
      desc: '',
      args: [],
    );
  }

  /// `Audited Financial Statements`
  String get creditApplicationRecapFiledAuditedFinancialStatements {
    return Intl.message(
      'Audited Financial Statements',
      name: 'creditApplicationRecapFiledAuditedFinancialStatements',
      desc: '',
      args: [],
    );
  }

  /// `Filed VAT reports`
  String get creditApplicationRecapFiledVatReports {
    return Intl.message(
      'Filed VAT reports',
      name: 'creditApplicationRecapFiledVatReports',
      desc: '',
      args: [],
    );
  }

  /// `Please review your details to make sure everything’s up-to-date.`
  String get creditApplicationRecapFromPreviousPageDescription {
    return Intl.message(
      'Please review your details to make sure everything’s up-to-date.',
      name: 'creditApplicationRecapFromPreviousPageDescription',
      desc: '',
      args: [],
    );
  }

  /// `Check your info and submit the application`
  String get creditApplicationRecapFromPreviousPageTitle {
    return Intl.message(
      'Check your info and submit the application',
      name: 'creditApplicationRecapFromPreviousPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Confirm and submit`
  String get creditApplicationRecapFromPreviousSubmit {
    return Intl.message(
      'Confirm and submit',
      name: 'creditApplicationRecapFromPreviousSubmit',
      desc: '',
      args: [],
    );
  }

  /// `No VAT reporting`
  String get creditApplicationRecapNoVatReporting {
    return Intl.message(
      'No VAT reporting',
      name: 'creditApplicationRecapNoVatReporting',
      desc: '',
      args: [],
    );
  }

  /// `Only Wio Business account`
  String get creditApplicationRecapOnlyWioBankAccount {
    return Intl.message(
      'Only Wio Business account',
      name: 'creditApplicationRecapOnlyWioBankAccount',
      desc: '',
      args: [],
    );
  }

  /// `You’re all set, check the details and submit your application.`
  String get creditApplicationRecapPageDescription {
    return Intl.message(
      'You’re all set, check the details and submit your application.',
      name: 'creditApplicationRecapPageDescription',
      desc: '',
      args: [],
    );
  }

  /// `Now, let’s review it all`
  String get creditApplicationRecapPageTitle {
    return Intl.message(
      'Now, let’s review it all',
      name: 'creditApplicationRecapPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `{day} of every month`
  String creditApplicationRecapPaybackDay(String day) {
    return Intl.message(
      '$day of every month',
      name: 'creditApplicationRecapPaybackDay',
      desc: '',
      args: [day],
    );
  }

  /// `Pay back day of month`
  String get creditApplicationRecapPaybackDayTitle {
    return Intl.message(
      'Pay back day of month',
      name: 'creditApplicationRecapPaybackDayTitle',
      desc: '',
      args: [],
    );
  }

  /// `Submit application`
  String get creditApplicationRecapSubmit {
    return Intl.message(
      'Submit application',
      name: 'creditApplicationRecapSubmit',
      desc: '',
      args: [],
    );
  }

  /// `Once submitted, the application can’t be reversed`
  String get creditApplicationRecapSubmitBanner {
    return Intl.message(
      'Once submitted, the application can’t be reversed',
      name: 'creditApplicationRecapSubmitBanner',
      desc: '',
      args: [],
    );
  }

  /// `Information updated !`
  String get creditApplicationRecapSuccessMessage {
    return Intl.message(
      'Information updated !',
      name: 'creditApplicationRecapSuccessMessage',
      desc: '',
      args: [],
    );
  }

  /// `Annually`
  String get creditApplicationRecapVatReportingAnnually {
    return Intl.message(
      'Annually',
      name: 'creditApplicationRecapVatReportingAnnually',
      desc: '',
      args: [],
    );
  }

  /// `VAT Reporting method`
  String get creditApplicationRecapVatReportingMethod {
    return Intl.message(
      'VAT Reporting method',
      name: 'creditApplicationRecapVatReportingMethod',
      desc: '',
      args: [],
    );
  }

  /// `Monthly`
  String get creditApplicationRecapVatReportingMonthly {
    return Intl.message(
      'Monthly',
      name: 'creditApplicationRecapVatReportingMonthly',
      desc: '',
      args: [],
    );
  }

  /// `Quarterly`
  String get creditApplicationRecapVatReportingQuarterly {
    return Intl.message(
      'Quarterly',
      name: 'creditApplicationRecapVatReportingQuarterly',
      desc: '',
      args: [],
    );
  }

  /// `Let’s go`
  String get creditApplicationRepaymentPlanCta {
    return Intl.message(
      'Let’s go',
      name: 'creditApplicationRepaymentPlanCta',
      desc: '',
      args: [],
    );
  }

  /// `Ensure timely payments to meet your loan milestones to avoid extra fees.`
  String get creditApplicationRepaymentPlanDescription {
    return Intl.message(
      'Ensure timely payments to meet your loan milestones to avoid extra fees.',
      name: 'creditApplicationRepaymentPlanDescription',
      desc: '',
      args: [],
    );
  }

  /// `Interest`
  String get creditApplicationRepaymentPlanInterest {
    return Intl.message(
      'Interest',
      name: 'creditApplicationRepaymentPlanInterest',
      desc: '',
      args: [],
    );
  }

  /// `Loan amount`
  String get creditApplicationRepaymentPlanLoanAmount {
    return Intl.message(
      'Loan amount',
      name: 'creditApplicationRepaymentPlanLoanAmount',
      desc: '',
      args: [],
    );
  }

  /// `Loan term`
  String get creditApplicationRepaymentPlanLoanTermLabel {
    return Intl.message(
      'Loan term',
      name: 'creditApplicationRepaymentPlanLoanTermLabel',
      desc: '',
      args: [],
    );
  }

  /// `{monthCount} months`
  String creditApplicationRepaymentPlanLoanTermValue(String monthCount) {
    return Intl.message(
      '$monthCount months',
      name: 'creditApplicationRepaymentPlanLoanTermValue',
      desc: '',
      args: [monthCount],
    );
  }

  /// `Please make sure loan amount and term selected successfully.`
  String get creditApplicationRepaymentPlanPeriodOrAmountNullError {
    return Intl.message(
      'Please make sure loan amount and term selected successfully.',
      name: 'creditApplicationRepaymentPlanPeriodOrAmountNullError',
      desc: '',
      args: [],
    );
  }

  /// `One-time processing fee`
  String get creditApplicationRepaymentPlanProcessingFee {
    return Intl.message(
      'One-time processing fee',
      name: 'creditApplicationRepaymentPlanProcessingFee',
      desc: '',
      args: [],
    );
  }

  /// `Great! Here’s your repayment plan`
  String get creditApplicationRepaymentPlanTitle {
    return Intl.message(
      'Great! Here’s your repayment plan',
      name: 'creditApplicationRepaymentPlanTitle',
      desc: '',
      args: [],
    );
  }

  /// `Total payback`
  String get creditApplicationRepaymentPlanTotalPayback {
    return Intl.message(
      'Total payback',
      name: 'creditApplicationRepaymentPlanTotalPayback',
      desc: '',
      args: [],
    );
  }

  /// `Done`
  String get creditApplicationSubmitSuccessPageCta {
    return Intl.message(
      'Done',
      name: 'creditApplicationSubmitSuccessPageCta',
      desc: '',
      args: [],
    );
  }

  /// `Application submitted.\nWe'll review everything and let you know within 2-3 days.`
  String get creditApplicationSubmitSuccessPageDescription {
    return Intl.message(
      'Application submitted.\nWe\'ll review everything and let you know within 2-3 days.',
      name: 'creditApplicationSubmitSuccessPageDescription',
      desc: '',
      args: [],
    );
  }

  /// `MABROOK!`
  String get creditApplicationSubmitSuccessPageTitle {
    return Intl.message(
      'MABROOK!',
      name: 'creditApplicationSubmitSuccessPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Upload audited financial statements for your company`
  String get creditAuditedFinancialStatementUploadScreenTitle {
    return Intl.message(
      'Upload audited financial statements for your company',
      name: 'creditAuditedFinancialStatementUploadScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `1 year of company financial activity`
  String get creditAuditedStatementUploadScreenDescriptionDetailThree {
    return Intl.message(
      '1 year of company financial activity',
      name: 'creditAuditedStatementUploadScreenDescriptionDetailThree',
      desc: '',
      args: [],
    );
  }

  /// `See how we calculate your fee`
  String get creditAutoPayFeeCalculation {
    return Intl.message(
      'See how we calculate your fee',
      name: 'creditAutoPayFeeCalculation',
      desc: '',
      args: [],
    );
  }

  /// `Fee amount:`
  String get creditAutoPayFeeCalculationDetailsBottomSheetFeeAmount {
    return Intl.message(
      'Fee amount:',
      name: 'creditAutoPayFeeCalculationDetailsBottomSheetFeeAmount',
      desc: '',
      args: [],
    );
  }

  /// `Your fee is determined by applying {feePercentage}% to the carry-over amount, which is your current outstanding balance after repayment.`
  String creditAutoPayFeeCalculationDetailsBottomSheetSubtitle(
      String feePercentage) {
    return Intl.message(
      'Your fee is determined by applying $feePercentage% to the carry-over amount, which is your current outstanding balance after repayment.',
      name: 'creditAutoPayFeeCalculationDetailsBottomSheetSubtitle',
      desc: '',
      args: [feePercentage],
    );
  }

  /// `Repay 100% of spent money this month to resume your fee-free period.`
  String get creditAutoPayFeeCalculationDetailsBottomSheetTipText {
    return Intl.message(
      'Repay 100% of spent money this month to resume your fee-free period.',
      name: 'creditAutoPayFeeCalculationDetailsBottomSheetTipText',
      desc: '',
      args: [],
    );
  }

  /// `Your fee as of today`
  String get creditAutoPayFeeCalculationDetailsBottomSheetTitle {
    return Intl.message(
      'Your fee as of today',
      name: 'creditAutoPayFeeCalculationDetailsBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `No Fee till {date}`
  String creditAutoPayFeeFreePeriodNoFee(String date) {
    return Intl.message(
      'No Fee till $date',
      name: 'creditAutoPayFeeFreePeriodNoFee',
      desc: '',
      args: [date],
    );
  }

  /// `*Minimum payment to avoid late fees: {amount}`
  String creditAutoPayMinimumPaymentToAvoidFees(String amount) {
    return Intl.message(
      '*Minimum payment to avoid late fees: $amount',
      name: 'creditAutoPayMinimumPaymentToAvoidFees',
      desc: '',
      args: [amount],
    );
  }

  /// `Your next autopay is on {date}`
  String creditAutoPayNextPayment(String date) {
    return Intl.message(
      'Your next autopay is on $date',
      name: 'creditAutoPayNextPayment',
      desc: '',
      args: [date],
    );
  }

  /// `Autopay set to`
  String get creditAutoPaySetTo {
    return Intl.message(
      'Autopay set to',
      name: 'creditAutoPaySetTo',
      desc: '',
      args: [],
    );
  }

  /// `Total due`
  String get creditAutoPayTotalDue {
    return Intl.message(
      'Total due',
      name: 'creditAutoPayTotalDue',
      desc: '',
      args: [],
    );
  }

  /// `Turn off`
  String get creditAutopayFromSavingSpaceConfirmationAccept {
    return Intl.message(
      'Turn off',
      name: 'creditAutopayFromSavingSpaceConfirmationAccept',
      desc: '',
      args: [],
    );
  }

  /// `Keep`
  String get creditAutopayFromSavingSpaceConfirmationCancel {
    return Intl.message(
      'Keep',
      name: 'creditAutopayFromSavingSpaceConfirmationCancel',
      desc: '',
      args: [],
    );
  }

  /// `If you turn it off you might get charged with fees if you don’t have enough money in your current AED account on autopay date.\n\nYou can change it later in “Manage”`
  String get creditAutopayFromSavingSpaceConfirmationDescription {
    return Intl.message(
      'If you turn it off you might get charged with fees if you don’t have enough money in your current AED account on autopay date.\n\nYou can change it later in “Manage”',
      name: 'creditAutopayFromSavingSpaceConfirmationDescription',
      desc: '',
      args: [],
    );
  }

  /// `Money will be taken from your Saving Space when you don’t have enough money in your current AED account.`
  String get creditAutopayFromSavingSpaceDescription {
    return Intl.message(
      'Money will be taken from your Saving Space when you don’t have enough money in your current AED account.',
      name: 'creditAutopayFromSavingSpaceDescription',
      desc: '',
      args: [],
    );
  }

  /// `Autopay from Saving Space`
  String get creditAutopayFromSavingSpaceTitle {
    return Intl.message(
      'Autopay from Saving Space',
      name: 'creditAutopayFromSavingSpaceTitle',
      desc: '',
      args: [],
    );
  }

  /// `Autopay repayment percentage successfully updated`
  String get creditAutopayPercentageUpdateSuccessfully {
    return Intl.message(
      'Autopay repayment percentage successfully updated',
      name: 'creditAutopayPercentageUpdateSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Please select an option that describes your company's business bank`
  String get creditBankAccountsNoOptionSelectedError {
    return Intl.message(
      'Please select an option that describes your company\'s business bank',
      name: 'creditBankAccountsNoOptionSelectedError',
      desc: '',
      args: [],
    );
  }

  /// `of {creditLoanAmount}`
  String creditBannerFooterText(Object creditLoanAmount) {
    return Intl.message(
      'of $creditLoanAmount',
      name: 'creditBannerFooterText',
      desc: '',
      args: [creditLoanAmount],
    );
  }

  /// `Something went wrong.`
  String get creditCommonToastSomethingWentWrong {
    return Intl.message(
      'Something went wrong.',
      name: 'creditCommonToastSomethingWentWrong',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get creditCompanyBankAccountInputScreenCtaText {
    return Intl.message(
      'Next',
      name: 'creditCompanyBankAccountInputScreenCtaText',
      desc: '',
      args: [],
    );
  }

  /// `My company has other bank accounts in the UAE`
  String get creditCompanyBankAccountOptionOtherBanks {
    return Intl.message(
      'My company has other bank accounts in the UAE',
      name: 'creditCompanyBankAccountOptionOtherBanks',
      desc: '',
      args: [],
    );
  }

  /// `Tell us about your company bank accounts`
  String get creditCompanyBankAccountOptionSelectorTitle {
    return Intl.message(
      'Tell us about your company bank accounts',
      name: 'creditCompanyBankAccountOptionSelectorTitle',
      desc: '',
      args: [],
    );
  }

  /// `Wio Business is my company's only bank account`
  String get creditCompanyBankAccountOptionWioOnly {
    return Intl.message(
      'Wio Business is my company\'s only bank account',
      name: 'creditCompanyBankAccountOptionWioOnly',
      desc: '',
      args: [],
    );
  }

  /// `Done`
  String get creditCreditLimitReduceSuccessPageButtonTitle {
    return Intl.message(
      'Done',
      name: 'creditCreditLimitReduceSuccessPageButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your new Credit limit is {amount}`
  String creditCreditLimitReduceSuccessPageDescription(String amount) {
    return Intl.message(
      'Your new Credit limit is $amount',
      name: 'creditCreditLimitReduceSuccessPageDescription',
      desc: '',
      args: [amount],
    );
  }

  /// `Limit decreased successfully`
  String get creditCreditLimitReduceSuccessPageTitle {
    return Intl.message(
      'Limit decreased successfully',
      name: 'creditCreditLimitReduceSuccessPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Autopay amount`
  String get creditDashboardAutopayAmountLabel {
    return Intl.message(
      'Autopay amount',
      name: 'creditDashboardAutopayAmountLabel',
      desc: '',
      args: [],
    );
  }

  /// `Edit autopay`
  String get creditDashboardAutopayEditButtonTitle {
    return Intl.message(
      'Edit autopay',
      name: 'creditDashboardAutopayEditButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Fee`
  String get creditDashboardAutopayFeeLabel {
    return Intl.message(
      'Fee',
      name: 'creditDashboardAutopayFeeLabel',
      desc: '',
      args: [],
    );
  }

  /// `No fee`
  String get creditDashboardAutopayNoFeeLabel {
    return Intl.message(
      'No fee',
      name: 'creditDashboardAutopayNoFeeLabel',
      desc: '',
      args: [],
    );
  }

  /// `Next autopay on {date}`
  String creditDashboardAutopaySectionTitle(String date) {
    return Intl.message(
      'Next autopay on $date',
      name: 'creditDashboardAutopaySectionTitle',
      desc: '',
      args: [date],
    );
  }

  /// `Available limit`
  String get creditDashboardAvailableLimit {
    return Intl.message(
      'Available limit',
      name: 'creditDashboardAvailableLimit',
      desc: '',
      args: [],
    );
  }

  /// `Available to borrow`
  String get creditDashboardAvailableToBorrow {
    return Intl.message(
      'Available to borrow',
      name: 'creditDashboardAvailableToBorrow',
      desc: '',
      args: [],
    );
  }

  /// `Borrow`
  String get creditDashboardBorrow {
    return Intl.message(
      'Borrow',
      name: 'creditDashboardBorrow',
      desc: '',
      args: [],
    );
  }

  /// `Borrowed`
  String get creditDashboardBorrowed {
    return Intl.message(
      'Borrowed',
      name: 'creditDashboardBorrowed',
      desc: '',
      args: [],
    );
  }

  /// `Credit locked`
  String get creditDashboardCreditLockedTitle {
    return Intl.message(
      'Credit locked',
      name: 'creditDashboardCreditLockedTitle',
      desc: '',
      args: [],
    );
  }

  /// `My credit`
  String get creditDashboardCreditTab {
    return Intl.message(
      'My credit',
      name: 'creditDashboardCreditTab',
      desc: '',
      args: [],
    );
  }

  /// `Due date {date}`
  String creditDashboardDueDate(String date) {
    return Intl.message(
      'Due date $date',
      name: 'creditDashboardDueDate',
      desc: '',
      args: [date],
    );
  }

  /// `Quick Cash`
  String get creditDashboardEasyCashTab {
    return Intl.message(
      'Quick Cash',
      name: 'creditDashboardEasyCashTab',
      desc: '',
      args: [],
    );
  }

  /// `Fee per day`
  String get creditDashboardFeePerDay {
    return Intl.message(
      'Fee per day',
      name: 'creditDashboardFeePerDay',
      desc: '',
      args: [],
    );
  }

  /// `Fees`
  String get creditDashboardFees {
    return Intl.message(
      'Fees',
      name: 'creditDashboardFees',
      desc: '',
      args: [],
    );
  }

  /// `We took {amount}`
  String creditDashboardLastAutoPaidAmount(String amount) {
    return Intl.message(
      'We took $amount',
      name: 'creditDashboardLastAutoPaidAmount',
      desc: '',
      args: [amount],
    );
  }

  /// `Your last autopay, {date}`
  String creditDashboardLastAutoPayDateLabel(String date) {
    return Intl.message(
      'Your last autopay, $date',
      name: 'creditDashboardLastAutoPayDateLabel',
      desc: '',
      args: [date],
    );
  }

  /// `We took an amount closest to your payment of {amount} because there wasn't enough money in your {currency} account on the autopay date.`
  String creditDashboardLastAutoPayDescription(String amount, String currency) {
    return Intl.message(
      'We took an amount closest to your payment of $amount because there wasn\'t enough money in your $currency account on the autopay date.',
      name: 'creditDashboardLastAutoPayDescription',
      desc: '',
      args: [amount, currency],
    );
  }

  /// `+{amount} fee`
  String creditDashboardLastAutoPayFee(String amount) {
    return Intl.message(
      '+$amount fee',
      name: 'creditDashboardLastAutoPayFee',
      desc: '',
      args: [amount],
    );
  }

  /// `Late payment fee`
  String get creditDashboardLatePaymentFee {
    return Intl.message(
      'Late payment fee',
      name: 'creditDashboardLatePaymentFee',
      desc: '',
      args: [],
    );
  }

  /// `Locked credit`
  String get creditDashboardLockedCredit {
    return Intl.message(
      'Locked credit',
      name: 'creditDashboardLockedCredit',
      desc: '',
      args: [],
    );
  }

  /// `Manage`
  String get creditDashboardManageButtonTitle {
    return Intl.message(
      'Manage',
      name: 'creditDashboardManageButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Wio Business Credit`
  String get creditDashboardPageTitle {
    return Intl.message(
      'Wio Business Credit',
      name: 'creditDashboardPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Pay credit`
  String get creditDashboardPayButtonTitle {
    return Intl.message(
      'Pay credit',
      name: 'creditDashboardPayButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Payback`
  String get creditDashboardPayback {
    return Intl.message(
      'Payback',
      name: 'creditDashboardPayback',
      desc: '',
      args: [],
    );
  }

  /// `Sorry, we were unable to refresh the dashboard. Please try again later`
  String get creditDashboardRefreshError {
    return Intl.message(
      'Sorry, we were unable to refresh the dashboard. Please try again later',
      name: 'creditDashboardRefreshError',
      desc: '',
      args: [],
    );
  }

  /// `Already spent`
  String get creditDashboardSpentLabel {
    return Intl.message(
      'Already spent',
      name: 'creditDashboardSpentLabel',
      desc: '',
      args: [],
    );
  }

  /// `Statement`
  String get creditDashboardStatementButtonTitle {
    return Intl.message(
      'Statement',
      name: 'creditDashboardStatementButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Take more`
  String get creditDashboardTakeMore {
    return Intl.message(
      'Take more',
      name: 'creditDashboardTakeMore',
      desc: '',
      args: [],
    );
  }

  /// `Available to spend `
  String get creditDashboardToSpendLabel {
    return Intl.message(
      'Available to spend ',
      name: 'creditDashboardToSpendLabel',
      desc: '',
      args: [],
    );
  }

  /// `Total due`
  String get creditDashboardTotalDue {
    return Intl.message(
      'Total due',
      name: 'creditDashboardTotalDue',
      desc: '',
      args: [],
    );
  }

  /// `Transactions`
  String get creditDashboardTransactionsSectionTitle {
    return Intl.message(
      'Transactions',
      name: 'creditDashboardTransactionsSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `You borrowed`
  String get creditDashboardYouBorrowed {
    return Intl.message(
      'You borrowed',
      name: 'creditDashboardYouBorrowed',
      desc: '',
      args: [],
    );
  }

  /// `Got it, thanks!`
  String get creditFeeFreePeriodTooltipAcknowledgeCta {
    return Intl.message(
      'Got it, thanks!',
      name: 'creditFeeFreePeriodTooltipAcknowledgeCta',
      desc: '',
      args: [],
    );
  }

  /// `Your fee-free period has expired on {date}. You can resume the fee-free period for up to 60 days each time you repay your outstanding balance in full.`
  String creditFeeFreePeriodTooltipBody(String date) {
    return Intl.message(
      'Your fee-free period has expired on $date. You can resume the fee-free period for up to 60 days each time you repay your outstanding balance in full.',
      name: 'creditFeeFreePeriodTooltipBody',
      desc: '',
      args: [date],
    );
  }

  /// `Resume your fee-free period`
  String get creditFeeFreePeriodTooltipTitle {
    return Intl.message(
      'Resume your fee-free period',
      name: 'creditFeeFreePeriodTooltipTitle',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get creditGenericErrorScreenCtaText {
    return Intl.message(
      'Close',
      name: 'creditGenericErrorScreenCtaText',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong on our side. Please try again later.`
  String get creditGenericErrorScreenDescription {
    return Intl.message(
      'Something went wrong on our side. Please try again later.',
      name: 'creditGenericErrorScreenDescription',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong`
  String get creditGenericErrorScreenSubtitle {
    return Intl.message(
      'Something went wrong',
      name: 'creditGenericErrorScreenSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Oops!`
  String get creditGenericErrorScreenTitle {
    return Intl.message(
      'Oops!',
      name: 'creditGenericErrorScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Please try again later`
  String get creditGenericErrorScreenTryAgain {
    return Intl.message(
      'Please try again later',
      name: 'creditGenericErrorScreenTryAgain',
      desc: '',
      args: [],
    );
  }

  /// `Add`
  String get creditIbanInputBottomSheetCtaText {
    return Intl.message(
      'Add',
      name: 'creditIbanInputBottomSheetCtaText',
      desc: '',
      args: [],
    );
  }

  /// `You may receive a notification from your bank. Don’t worry your information security is our priority.`
  String get creditIbanInputBottomSheetDescription {
    return Intl.message(
      'You may receive a notification from your bank. Don’t worry your information security is our priority.',
      name: 'creditIbanInputBottomSheetDescription',
      desc: '',
      args: [],
    );
  }

  /// `Invalid IBAN`
  String get creditIbanInputBottomSheetErrorGeneric {
    return Intl.message(
      'Invalid IBAN',
      name: 'creditIbanInputBottomSheetErrorGeneric',
      desc: '',
      args: [],
    );
  }

  /// `The entered IBAN has an invalid length`
  String get creditIbanInputBottomSheetErrorInvalidLength {
    return Intl.message(
      'The entered IBAN has an invalid length',
      name: 'creditIbanInputBottomSheetErrorInvalidLength',
      desc: '',
      args: [],
    );
  }

  /// `The entered IBAN has an invalid structure`
  String get creditIbanInputBottomSheetErrorInvalidStructure {
    return Intl.message(
      'The entered IBAN has an invalid structure',
      name: 'creditIbanInputBottomSheetErrorInvalidStructure',
      desc: '',
      args: [],
    );
  }

  /// `You can enter only UAE IBANs`
  String get creditIbanInputBottomSheetErrorNotUaeIban {
    return Intl.message(
      'You can enter only UAE IBANs',
      name: 'creditIbanInputBottomSheetErrorNotUaeIban',
      desc: '',
      args: [],
    );
  }

  /// `IBAN number`
  String get creditIbanInputBottomSheetInputFieldLabel {
    return Intl.message(
      'IBAN number',
      name: 'creditIbanInputBottomSheetInputFieldLabel',
      desc: '',
      args: [],
    );
  }

  /// `Add bank account IBAN`
  String get creditIbanInputBottomSheetTitle {
    return Intl.message(
      'Add bank account IBAN',
      name: 'creditIbanInputBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Got it, thanks`
  String get creditIneligibilityBottomsheetCta {
    return Intl.message(
      'Got it, thanks',
      name: 'creditIneligibilityBottomsheetCta',
      desc: '',
      args: [],
    );
  }

  /// `Learn more`
  String get creditLearnMoreCta {
    return Intl.message(
      'Learn more',
      name: 'creditLearnMoreCta',
      desc: '',
      args: [],
    );
  }

  /// `Apply now`
  String get creditLearnMoreCtaText {
    return Intl.message(
      'Apply now',
      name: 'creditLearnMoreCtaText',
      desc: '',
      args: [],
    );
  }

  /// `FAQs`
  String get creditLearnMoreFaqTitle {
    return Intl.message(
      'FAQs',
      name: 'creditLearnMoreFaqTitle',
      desc: '',
      args: [],
    );
  }

  /// `What are the Wio Business Credit costs?`
  String get creditLearnMoreFirstFaq {
    return Intl.message(
      'What are the Wio Business Credit costs?',
      name: 'creditLearnMoreFirstFaq',
      desc: '',
      args: [],
    );
  }

  /// `No annual fee - Wio Credit is included in your pricing plan.\n\nNo monthly carry-over fee if you pay full outstanding amount each month without any carrying over any balance to the next month.\n\n{feePercentage}% monthly carry-over fee applies if you pay at least {minumumPayPercentage}% of your outstanding balance. This fee is calculated as a fixed percentage of your carry-over amount, which is your current outstanding balance after repayment.\n\nPay off one month's balance entirely and enjoy up to {feeFreeMonthCount} months fee-free period.\n\n{penalty} Penalty fee apply when you fail to repay at least {minumumPayPercentage}% of your outstanding balance within 6 days after repayment deadline.`
  String creditLearnMoreFirstFaqAnswer(Object feePercentage,
      Object minumumPayPercentage, Object feeFreeMonthCount, Object penalty) {
    return Intl.message(
      'No annual fee - Wio Credit is included in your pricing plan.\n\nNo monthly carry-over fee if you pay full outstanding amount each month without any carrying over any balance to the next month.\n\n$feePercentage% monthly carry-over fee applies if you pay at least $minumumPayPercentage% of your outstanding balance. This fee is calculated as a fixed percentage of your carry-over amount, which is your current outstanding balance after repayment.\n\nPay off one month\'s balance entirely and enjoy up to $feeFreeMonthCount months fee-free period.\n\n$penalty Penalty fee apply when you fail to repay at least $minumumPayPercentage% of your outstanding balance within 6 days after repayment deadline.',
      name: 'creditLearnMoreFirstFaqAnswer',
      desc: '',
      args: [feePercentage, minumumPayPercentage, feeFreeMonthCount, penalty],
    );
  }

  /// `Easily switch between debit and credit mode on any of your Wio Business cards.`
  String get creditLearnMoreFirstFeatureDesc {
    return Intl.message(
      'Easily switch between debit and credit mode on any of your Wio Business cards.',
      name: 'creditLearnMoreFirstFeatureDesc',
      desc: '',
      args: [],
    );
  }

  /// `One card for debit and credit`
  String get creditLearnMoreFirstFeatureTitle {
    return Intl.message(
      'One card for debit and credit',
      name: 'creditLearnMoreFirstFeatureTitle',
      desc: '',
      args: [],
    );
  }

  /// `How it works`
  String get creditLearnMoreHowItWorks {
    return Intl.message(
      'How it works',
      name: 'creditLearnMoreHowItWorks',
      desc: '',
      args: [],
    );
  }

  /// `Submit your application directly on the app. Upon application approval, customize your repayment preferences and start spending by setting any of your Wio Business cards into credit spending mode.`
  String get creditLearnMoreHowItWorksDesc {
    return Intl.message(
      'Submit your application directly on the app. Upon application approval, customize your repayment preferences and start spending by setting any of your Wio Business cards into credit spending mode.',
      name: 'creditLearnMoreHowItWorksDesc',
      desc: '',
      args: [],
    );
  }

  /// `Apply for Wio Credit and turn any of your Wio Business cards into a credit card.`
  String get creditLearnMorePageSubtitle {
    return Intl.message(
      'Apply for Wio Credit and turn any of your Wio Business cards into a credit card.',
      name: 'creditLearnMorePageSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Wio Business Credit`
  String get creditLearnMorePageTitle {
    return Intl.message(
      'Wio Business Credit',
      name: 'creditLearnMorePageTitle',
      desc: '',
      args: [],
    );
  }

  /// `How does the fee-free period work?`
  String get creditLearnMoreSecondFaq {
    return Intl.message(
      'How does the fee-free period work?',
      name: 'creditLearnMoreSecondFaq',
      desc: '',
      args: [],
    );
  }

  /// `1. Pay as low as {minumumPayPercentage}% of your outstanding balance and carry over the remaining amount to the next period without incurring any fees.\n\n2. You can carry over your balance without any fees for two consecutive billing cycles, and fees will only be charged starting from the third cycle. This allows you to enjoy up to {feeFreeMonthCount} months of fee-free credit!\n\n3. After your fee-free period ends, it will restart each time you repay your outstanding balance in full.\n\nTake a look at this practical example:\nIf your repayment date is the 1st of each month and you spend AED 1,000 on the 2nd of June, you need to repay only the minimum ({minumumPayPercentage}% of AED 1,000)  To avoid fees, the full amount must be repaid  by the 1st of August. This gives you June and July as your fee-free period.`
  String creditLearnMoreSecondFaqAnswer(
      Object minumumPayPercentage, Object feeFreeMonthCount) {
    return Intl.message(
      '1. Pay as low as $minumumPayPercentage% of your outstanding balance and carry over the remaining amount to the next period without incurring any fees.\n\n2. You can carry over your balance without any fees for two consecutive billing cycles, and fees will only be charged starting from the third cycle. This allows you to enjoy up to $feeFreeMonthCount months of fee-free credit!\n\n3. After your fee-free period ends, it will restart each time you repay your outstanding balance in full.\n\nTake a look at this practical example:\nIf your repayment date is the 1st of each month and you spend AED 1,000 on the 2nd of June, you need to repay only the minimum ($minumumPayPercentage% of AED 1,000)  To avoid fees, the full amount must be repaid  by the 1st of August. This gives you June and July as your fee-free period.',
      name: 'creditLearnMoreSecondFaqAnswer',
      desc: '',
      args: [minumumPayPercentage, feeFreeMonthCount],
    );
  }

  /// `Enjoy {feePercentage}% fee on your outstanding balance, and no fees if you repay your spends in full.`
  String creditLearnMoreSecondFeatureDesc(num feePercentage) {
    return Intl.message(
      'Enjoy $feePercentage% fee on your outstanding balance, and no fees if you repay your spends in full.',
      name: 'creditLearnMoreSecondFeatureDesc',
      desc: '',
      args: [feePercentage],
    );
  }

  /// `No hidden fees`
  String get creditLearnMoreSecondFeatureTitle {
    return Intl.message(
      'No hidden fees',
      name: 'creditLearnMoreSecondFeatureTitle',
      desc: '',
      args: [],
    );
  }

  /// `How is Wio Business Credit better than a regular credit card?`
  String get creditLearnMoreThirdFaq {
    return Intl.message(
      'How is Wio Business Credit better than a regular credit card?',
      name: 'creditLearnMoreThirdFaq',
      desc: '',
      args: [],
    );
  }

  /// `- Use with any of your cards physical or virtual.\n\n- Choose your repayment date and amount with custom autopay option.\n\n- Your end of month statements includes your spends no complex statement periods.\n\n- Wio Credit is free and you pay only a flat {feePercentage}% fee on your carryover amount.`
  String creditLearnMoreThirdFaqAnswer(Object feePercentage) {
    return Intl.message(
      '- Use with any of your cards physical or virtual.\n\n- Choose your repayment date and amount with custom autopay option.\n\n- Your end of month statements includes your spends no complex statement periods.\n\n- Wio Credit is free and you pay only a flat $feePercentage% fee on your carryover amount.',
      name: 'creditLearnMoreThirdFaqAnswer',
      desc: '',
      args: [feePercentage],
    );
  }

  /// `Pay as low as {minumumPayPercentage}% of your outstanding amount and enjoy up to {feeFreeMonthCount} months without fees.`
  String creditLearnMoreThirdFeatureDesc(
      Object minumumPayPercentage, Object feeFreeMonthCount) {
    return Intl.message(
      'Pay as low as $minumumPayPercentage% of your outstanding amount and enjoy up to $feeFreeMonthCount months without fees.',
      name: 'creditLearnMoreThirdFeatureDesc',
      desc: '',
      args: [minumumPayPercentage, feeFreeMonthCount],
    );
  }

  /// `Get up to {feeFreeMonthCount} months fee-free credit`
  String creditLearnMoreThirdFeatureTitle(Object feeFreeMonthCount) {
    return Intl.message(
      'Get up to $feeFreeMonthCount months fee-free credit',
      name: 'creditLearnMoreThirdFeatureTitle',
      desc: '',
      args: [feeFreeMonthCount],
    );
  }

  /// `What you’ll get`
  String get creditLearnMoreWhatYouGet {
    return Intl.message(
      'What you’ll get',
      name: 'creditLearnMoreWhatYouGet',
      desc: '',
      args: [],
    );
  }

  /// `Pay as low as {minumumPaymentPercentage}% of your outstanding amount and enjoy up to {feeFreeMonthCount} months without fees.`
  String creditLimitCondition1Subtitle(
      String feeFreeMonthCount, String minumumPaymentPercentage) {
    return Intl.message(
      'Pay as low as $minumumPaymentPercentage% of your outstanding amount and enjoy up to $feeFreeMonthCount months without fees.',
      name: 'creditLimitCondition1Subtitle',
      desc: '',
      args: [feeFreeMonthCount, minumumPaymentPercentage],
    );
  }

  /// `Get up to {feeFreeMonthCount} months fee-free credit`
  String creditLimitCondition1Title(String feeFreeMonthCount) {
    return Intl.message(
      'Get up to $feeFreeMonthCount months fee-free credit',
      name: 'creditLimitCondition1Title',
      desc: '',
      args: [feeFreeMonthCount],
    );
  }

  /// `Enjoy {feePercentage}% fee on your outstanding balance, and no fees if you repay your spends in full.`
  String creditLimitCondition2Subtitle(String feePercentage) {
    return Intl.message(
      'Enjoy $feePercentage% fee on your outstanding balance, and no fees if you repay your spends in full.',
      name: 'creditLimitCondition2Subtitle',
      desc: '',
      args: [feePercentage],
    );
  }

  /// `No hidden fees`
  String get creditLimitCondition2Title {
    return Intl.message(
      'No hidden fees',
      name: 'creditLimitCondition2Title',
      desc: '',
      args: [],
    );
  }

  /// `Switch between debit and credit mode on any of your Wio Business cards.`
  String get creditLimitCondition3Subtitle {
    return Intl.message(
      'Switch between debit and credit mode on any of your Wio Business cards.',
      name: 'creditLimitCondition3Subtitle',
      desc: '',
      args: [],
    );
  }

  /// `One card for debit and credit`
  String get creditLimitCondition3Title {
    return Intl.message(
      'One card for debit and credit',
      name: 'creditLimitCondition3Title',
      desc: '',
      args: [],
    );
  }

  /// `Offer benefits`
  String get creditLimitConditionsTitle {
    return Intl.message(
      'Offer benefits',
      name: 'creditLimitConditionsTitle',
      desc: '',
      args: [],
    );
  }

  /// `Edit limit`
  String get creditLimitEditButtonTitle {
    return Intl.message(
      'Edit limit',
      name: 'creditLimitEditButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `To reduce your Wio Credit limit, please repay your Quick Cash balance`
  String get creditLimitReduceErrorDueToEasyCashBalance {
    return Intl.message(
      'To reduce your Wio Credit limit, please repay your Quick Cash balance',
      name: 'creditLimitReduceErrorDueToEasyCashBalance',
      desc: '',
      args: [],
    );
  }

  /// `Save limit`
  String get creditLimitSaveButtonTitle {
    return Intl.message(
      'Save limit',
      name: 'creditLimitSaveButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Set your new limit`
  String get creditLimitSelectorEditTitle {
    return Intl.message(
      'Set your new limit',
      name: 'creditLimitSelectorEditTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your approved limit is`
  String get creditLimitSelectorTitle {
    return Intl.message(
      'Your approved limit is',
      name: 'creditLimitSelectorTitle',
      desc: '',
      args: [],
    );
  }

  /// `Accept offer`
  String get creditLimitSubmitButtonTitle {
    return Intl.message(
      'Accept offer',
      name: 'creditLimitSubmitButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Pay the minimum amount due now to unlock credit and avoid additional charges.`
  String get creditLockedBottomsheetDescription {
    return Intl.message(
      'Pay the minimum amount due now to unlock credit and avoid additional charges.',
      name: 'creditLockedBottomsheetDescription',
      desc: '',
      args: [],
    );
  }

  /// `Late payment fee:`
  String get creditLockedBottomsheetLatePaymentFee {
    return Intl.message(
      'Late payment fee:',
      name: 'creditLockedBottomsheetLatePaymentFee',
      desc: '',
      args: [],
    );
  }

  /// `Minimum amount due\nto unlock credit:`
  String get creditLockedBottomsheetMinimumAmountToUnlock {
    return Intl.message(
      'Minimum amount due\nto unlock credit:',
      name: 'creditLockedBottomsheetMinimumAmountToUnlock',
      desc: '',
      args: [],
    );
  }

  /// `Minimum payment due:`
  String get creditLockedBottomsheetMinimumPaymentDue {
    return Intl.message(
      'Minimum payment due:',
      name: 'creditLockedBottomsheetMinimumPaymentDue',
      desc: '',
      args: [],
    );
  }

  /// `Your credit is locked because you didn't pay the  due amount within 6 days.`
  String get creditLockedBottomsheetSubtitle {
    return Intl.message(
      'Your credit is locked because you didn\'t pay the  due amount within 6 days.',
      name: 'creditLockedBottomsheetSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Total outstanding as of\n{date} (incl. fees):`
  String creditLockedBottomsheetTotalOutstanding(String date) {
    return Intl.message(
      'Total outstanding as of\n$date (incl. fees):',
      name: 'creditLockedBottomsheetTotalOutstanding',
      desc: '',
      args: [date],
    );
  }

  /// `Your credit offer is ready!`
  String get creditOfferReadyMessage {
    return Intl.message(
      'Your credit offer is ready!',
      name: 'creditOfferReadyMessage',
      desc: '',
      args: [],
    );
  }

  /// `Please confirm that Wio Business is your company's sole account and that you don’t have any other bank accounts under your company name in the UAE.`
  String get creditOnlyWioBankAccountBottomSheetBody {
    return Intl.message(
      'Please confirm that Wio Business is your company\'s sole account and that you don’t have any other bank accounts under your company name in the UAE.',
      name: 'creditOnlyWioBankAccountBottomSheetBody',
      desc: '',
      args: [],
    );
  }

  /// `Change`
  String get creditOnlyWioBankAccountBottomSheetCtaChangeText {
    return Intl.message(
      'Change',
      name: 'creditOnlyWioBankAccountBottomSheetCtaChangeText',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get creditOnlyWioBankAccountBottomSheetCtaConfirmText {
    return Intl.message(
      'Confirm',
      name: 'creditOnlyWioBankAccountBottomSheetCtaConfirmText',
      desc: '',
      args: [],
    );
  }

  /// `Before you continue`
  String get creditOnlyWioBankAccountBottomSheetTitle {
    return Intl.message(
      'Before you continue',
      name: 'creditOnlyWioBankAccountBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Please confirm that you've added all non-Wio bank accounts as this will help us increase your chances of approval or getting a higher credit limit and quicker approval.`
  String get creditOtherBankAccountsBottomSheetDescription {
    return Intl.message(
      'Please confirm that you\'ve added all non-Wio bank accounts as this will help us increase your chances of approval or getting a higher credit limit and quicker approval.',
      name: 'creditOtherBankAccountsBottomSheetDescription',
      desc: '',
      args: [],
    );
  }

  /// `Before you continue`
  String get creditOtherBankAccountsBottomSheetTitle {
    return Intl.message(
      'Before you continue',
      name: 'creditOtherBankAccountsBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Add bank account`
  String get creditOtherBankAccountsInputPageAddButtonText {
    return Intl.message(
      'Add bank account',
      name: 'creditOtherBankAccountsInputPageAddButtonText',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get creditOtherBankAccountsInputPageCtaText {
    return Intl.message(
      'Next',
      name: 'creditOtherBankAccountsInputPageCtaText',
      desc: '',
      args: [],
    );
  }

  /// `Provide all non-Wio bank accounts to increase your chances of approval, speed up the process and get higher credit limit.`
  String get creditOtherBankAccountsInputPageDescription {
    return Intl.message(
      'Provide all non-Wio bank accounts to increase your chances of approval, speed up the process and get higher credit limit.',
      name: 'creditOtherBankAccountsInputPageDescription',
      desc: '',
      args: [],
    );
  }

  /// `Add your company's other bank accounts`
  String get creditOtherBankAccountsInputPageTitle {
    return Intl.message(
      'Add your company\'s other bank accounts',
      name: 'creditOtherBankAccountsInputPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `By continuing, you agree with the Terms & Conditions and the Key Fact Statement`
  String get creditOverviewDocsText {
    return Intl.message(
      'By continuing, you agree with the Terms & Conditions and the Key Fact Statement',
      name: 'creditOverviewDocsText',
      desc: '',
      args: [],
    );
  }

  /// `Key Fact Statement`
  String get creditOverviewDocsTextKfs {
    return Intl.message(
      'Key Fact Statement',
      name: 'creditOverviewDocsTextKfs',
      desc: '',
      args: [],
    );
  }

  /// `Terms & Conditions`
  String get creditOverviewDocsTextTnc {
    return Intl.message(
      'Terms & Conditions',
      name: 'creditOverviewDocsTextTnc',
      desc: '',
      args: [],
    );
  }

  /// `We will ask to upload the last three years of authenticated statements.`
  String get creditOverviewScreenAuditedFinancialStatementsSubtitle {
    return Intl.message(
      'We will ask to upload the last three years of authenticated statements.',
      name: 'creditOverviewScreenAuditedFinancialStatementsSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Audited financial statements`
  String get creditOverviewScreenAuditedFinancialStatementsTitle {
    return Intl.message(
      'Audited financial statements',
      name: 'creditOverviewScreenAuditedFinancialStatementsTitle',
      desc: '',
      args: [],
    );
  }

  /// `Start Application`
  String get creditOverviewScreenCtaText {
    return Intl.message(
      'Start Application',
      name: 'creditOverviewScreenCtaText',
      desc: '',
      args: [],
    );
  }

  /// `If applicable, we’ll ask for VAT statements for the last 6 months or 2 quarters.`
  String get creditOverviewScreenFirstStepSubtitle {
    return Intl.message(
      'If applicable, we’ll ask for VAT statements for the last 6 months or 2 quarters.',
      name: 'creditOverviewScreenFirstStepSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `VAT Information`
  String get creditOverviewScreenFirstStepTitle {
    return Intl.message(
      'VAT Information',
      name: 'creditOverviewScreenFirstStepTitle',
      desc: '',
      args: [],
    );
  }

  /// `View statement example`
  String get creditOverviewScreenFirstStepVatStatementExample {
    return Intl.message(
      'View statement example',
      name: 'creditOverviewScreenFirstStepVatStatementExample',
      desc: '',
      args: [],
    );
  }

  /// `You’ll need to confirm your company’s annual turnover.`
  String get creditOverviewScreenSecondStepSubtitle {
    return Intl.message(
      'You’ll need to confirm your company’s annual turnover.',
      name: 'creditOverviewScreenSecondStepSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Company turnover`
  String get creditOverviewScreenSecondStepTitle {
    return Intl.message(
      'Company turnover',
      name: 'creditOverviewScreenSecondStepTitle',
      desc: '',
      args: [],
    );
  }

  /// `Here's what we need for your credit offer`
  String get creditOverviewScreenSubtitle {
    return Intl.message(
      'Here\'s what we need for your credit offer',
      name: 'creditOverviewScreenSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `We’ll need to know the IBANs of all your company’s bank accounts.`
  String get creditOverviewScreenThirdStepSubtitle {
    return Intl.message(
      'We’ll need to know the IBANs of all your company’s bank accounts.',
      name: 'creditOverviewScreenThirdStepSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Bank accounts`
  String get creditOverviewScreenThirdStepTitle {
    return Intl.message(
      'Bank accounts',
      name: 'creditOverviewScreenThirdStepTitle',
      desc: '',
      args: [],
    );
  }

  /// `Let's start your journey`
  String get creditOverviewScreenTitle {
    return Intl.message(
      'Let\'s start your journey',
      name: 'creditOverviewScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Sorry, we were unable to process your payment at this time. Please try again later.`
  String get creditPayCreditError {
    return Intl.message(
      'Sorry, we were unable to process your payment at this time. Please try again later.',
      name: 'creditPayCreditError',
      desc: '',
      args: [],
    );
  }

  /// `You are charged with late payment fees of {amount}. To unlock your credit make the overdue payment now.`
  String creditPaybackOverdueWithFee(String amount) {
    return Intl.message(
      'You are charged with late payment fees of $amount. To unlock your credit make the overdue payment now.',
      name: 'creditPaybackOverdueWithFee',
      desc: '',
      args: [amount],
    );
  }

  /// `To avoid any additional charges, please make the overdue payment by {date}.`
  String creditPaybackOverdueWithoutFee(String date) {
    return Intl.message(
      'To avoid any additional charges, please make the overdue payment by $date.',
      name: 'creditPaybackOverdueWithoutFee',
      desc: '',
      args: [date],
    );
  }

  /// `{percentage}% of spent money`
  String creditPaybackPercent(String percentage) {
    return Intl.message(
      '$percentage% of spent money',
      name: 'creditPaybackPercent',
      desc: '',
      args: [percentage],
    );
  }

  /// `Continue`
  String get creditPaymentDateSetupCta {
    return Intl.message(
      'Continue',
      name: 'creditPaymentDateSetupCta',
      desc: '',
      args: [],
    );
  }

  /// `There are only 28 days for everything to work correctly even in February`
  String get creditPaymentDateSetupDaysNumberExplanation {
    return Intl.message(
      'There are only 28 days for everything to work correctly even in February',
      name: 'creditPaymentDateSetupDaysNumberExplanation',
      desc: '',
      args: [],
    );
  }

  /// `Select a day between 1 and 28 for your monthly transfers.`
  String get creditPaymentDateSetupPageDescription {
    return Intl.message(
      'Select a day between 1 and 28 for your monthly transfers.',
      name: 'creditPaymentDateSetupPageDescription',
      desc: '',
      args: [],
    );
  }

  /// `When do you want to pay back?`
  String get creditPaymentDateSetupPageTitle {
    return Intl.message(
      'When do you want to pay back?',
      name: 'creditPaymentDateSetupPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `{date} of every month`
  String creditPaymentDateSetupSelectedDate(String date) {
    return Intl.message(
      '$date of every month',
      name: 'creditPaymentDateSetupSelectedDate',
      desc: '',
      args: [date],
    );
  }

  /// `Please note that once selected, the repayment date can't be edited.`
  String get creditPaymentDateSetupTip {
    return Intl.message(
      'Please note that once selected, the repayment date can\'t be edited.',
      name: 'creditPaymentDateSetupTip',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get creditPaymentPercentageSetupCta {
    return Intl.message(
      'Continue',
      name: 'creditPaymentPercentageSetupCta',
      desc: '',
      args: [],
    );
  }

  /// `How much do you want to pay back monthly?`
  String get creditPaymentPercentageSetupPageTitle {
    return Intl.message(
      'How much do you want to pay back monthly?',
      name: 'creditPaymentPercentageSetupPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Pay back the full amount every month and avoid fees and charges.`
  String get creditPaymentPercentageSetupPaybackTip {
    return Intl.message(
      'Pay back the full amount every month and avoid fees and charges.',
      name: 'creditPaymentPercentageSetupPaybackTip',
      desc: '',
      args: [],
    );
  }

  /// `If you spend {spent} and you pay back {payback}, you'll be charged {fee}`
  String creditPaymentPercentageSetupPaymentBreakdown(
      String fee, String payback, String spent) {
    return Intl.message(
      'If you spend $spent and you pay back $payback, you\'ll be charged $fee',
      name: 'creditPaymentPercentageSetupPaymentBreakdown',
      desc: '',
      args: [fee, payback, spent],
    );
  }

  /// `If you spend {spent}, you'll pay back {fee} without fees.`
  String creditPaymentPercentageSetupPaymentBreakdownWithoutFees(
      String fee, String spent) {
    return Intl.message(
      'If you spend $spent, you\'ll pay back $fee without fees.',
      name: 'creditPaymentPercentageSetupPaymentBreakdownWithoutFees',
      desc: '',
      args: [fee, spent],
    );
  }

  /// `{percentage}% of spent money`
  String creditPaymentPercentageSetupSelectedPercentage(String percentage) {
    return Intl.message(
      '$percentage% of spent money',
      name: 'creditPaymentPercentageSetupSelectedPercentage',
      desc: '',
      args: [percentage],
    );
  }

  /// `All spent money`
  String get creditSelectorLabel {
    return Intl.message(
      'All spent money',
      name: 'creditSelectorLabel',
      desc: '',
      args: [],
    );
  }

  /// `Your monthly statements`
  String get creditStatementPageTitle {
    return Intl.message(
      'Your monthly statements',
      name: 'creditStatementPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `{supportedFileTypes} max {supportedFileSizeInMb}MB`
  String creditStatementUploadBoxSubtitle(
      String supportedFileSizeInMb, String supportedFileTypes) {
    return Intl.message(
      '$supportedFileTypes max ${supportedFileSizeInMb}MB',
      name: 'creditStatementUploadBoxSubtitle',
      desc: '',
      args: [supportedFileSizeInMb, supportedFileTypes],
    );
  }

  /// `Your documents will be displayed here\nwhen one is available.`
  String get creditStatementsEmptyDescription {
    return Intl.message(
      'Your documents will be displayed here\nwhen one is available.',
      name: 'creditStatementsEmptyDescription',
      desc: '',
      args: [],
    );
  }

  /// `You don’t have\nany statements yet`
  String get creditStatementsEmptyTitle {
    return Intl.message(
      'You don’t have\nany statements yet',
      name: 'creditStatementsEmptyTitle',
      desc: '',
      args: [],
    );
  }

  /// `Download statement`
  String get creditStatementsFileViewerDownloadCta {
    return Intl.message(
      'Download statement',
      name: 'creditStatementsFileViewerDownloadCta',
      desc: '',
      args: [],
    );
  }

  /// `Credit account statement`
  String get creditStatementsFileViewerTitle {
    return Intl.message(
      'Credit account statement',
      name: 'creditStatementsFileViewerTitle',
      desc: '',
      args: [],
    );
  }

  /// `Year`
  String get creditStatementsYearLabel {
    return Intl.message(
      'Year',
      name: 'creditStatementsYearLabel',
      desc: '',
      args: [],
    );
  }

  /// `A carry-over fee (interest) will be applied at an annual interest rate of up to {annualInterest}%.`
  String creditThingsToKnowFeature1(String annualInterest) {
    return Intl.message(
      'A carry-over fee (interest) will be applied at an annual interest rate of up to $annualInterest%.',
      name: 'creditThingsToKnowFeature1',
      desc: '',
      args: [annualInterest],
    );
  }

  /// `No fees are applied if you pay your amount in full and don’t carry over any balance to the next month`
  String get creditThingsToKnowFeature2 {
    return Intl.message(
      'No fees are applied if you pay your amount in full and don’t carry over any balance to the next month',
      name: 'creditThingsToKnowFeature2',
      desc: '',
      args: [],
    );
  }

  /// `You can waive the carryover fee for two months in a row by paying at least {minThresholdPercentage}% of the balance.`
  String creditThingsToKnowFeature3(Object minThresholdPercentage) {
    return Intl.message(
      'You can waive the carryover fee for two months in a row by paying at least $minThresholdPercentage% of the balance.',
      name: 'creditThingsToKnowFeature3',
      desc: '',
      args: [minThresholdPercentage],
    );
  }

  /// `Please review and accept`
  String get creditThingsToKnowSubtitle {
    return Intl.message(
      'Please review and accept',
      name: 'creditThingsToKnowSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `A limit increase request is pending on your account. You can only decrease your limit.`
  String get creditUpdateLimitAlreadyRequestedDesc {
    return Intl.message(
      'A limit increase request is pending on your account. You can only decrease your limit.',
      name: 'creditUpdateLimitAlreadyRequestedDesc',
      desc: '',
      args: [],
    );
  }

  /// `You have already requested to increase your limit.`
  String get creditUpdateLimitAlreadyRequestedTitle {
    return Intl.message(
      'You have already requested to increase your limit.',
      name: 'creditUpdateLimitAlreadyRequestedTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your limit increase is subject to approval.`
  String get creditUpdateLimitCondition1Subtitle {
    return Intl.message(
      'Your limit increase is subject to approval.',
      name: 'creditUpdateLimitCondition1Subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Subject to approval`
  String get creditUpdateLimitCondition1Title {
    return Intl.message(
      'Subject to approval',
      name: 'creditUpdateLimitCondition1Title',
      desc: '',
      args: [],
    );
  }

  /// `You cannot reduce your limit below amount you’ve already spent.`
  String get creditUpdateLimitCondition2Subtitle {
    return Intl.message(
      'You cannot reduce your limit below amount you’ve already spent.',
      name: 'creditUpdateLimitCondition2Subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Minimum limit`
  String get creditUpdateLimitCondition2Title {
    return Intl.message(
      'Minimum limit',
      name: 'creditUpdateLimitCondition2Title',
      desc: '',
      args: [],
    );
  }

  /// `Your limit is reduced instantly after you click “Update limit”`
  String get creditUpdateLimitCondition3Subtitle {
    return Intl.message(
      'Your limit is reduced instantly after you click “Update limit”',
      name: 'creditUpdateLimitCondition3Subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Your limit is updated after approval.`
  String get creditUpdateLimitCondition3SubtitleAfterCutoff {
    return Intl.message(
      'Your limit is updated after approval.',
      name: 'creditUpdateLimitCondition3SubtitleAfterCutoff',
      desc: '',
      args: [],
    );
  }

  /// `Instant confirmation`
  String get creditUpdateLimitCondition3Title {
    return Intl.message(
      'Instant confirmation',
      name: 'creditUpdateLimitCondition3Title',
      desc: '',
      args: [],
    );
  }

  /// `Update limit`
  String get creditUpdateLimitCta {
    return Intl.message(
      'Update limit',
      name: 'creditUpdateLimitCta',
      desc: '',
      args: [],
    );
  }

  /// `Your credit limit is based on different sources, such as your salary, credit rating, etc.`
  String get creditUpdateLimitFooterText {
    return Intl.message(
      'Your credit limit is based on different sources, such as your salary, credit rating, etc.',
      name: 'creditUpdateLimitFooterText',
      desc: '',
      args: [],
    );
  }

  /// `Update your credit limit`
  String get creditUpdateLimitLabel {
    return Intl.message(
      'Update your credit limit',
      name: 'creditUpdateLimitLabel',
      desc: '',
      args: [],
    );
  }

  /// `Request more`
  String get creditUpdateLimitRequestMore {
    return Intl.message(
      'Request more',
      name: 'creditUpdateLimitRequestMore',
      desc: '',
      args: [],
    );
  }

  /// `Yes, I want more limit`
  String get creditUpdateLimitRequestMoreConfirm {
    return Intl.message(
      'Yes, I want more limit',
      name: 'creditUpdateLimitRequestMoreConfirm',
      desc: '',
      args: [],
    );
  }

  /// `If you want more limit you can request to check your eligibility and we will try to give you the maximum credit limit possible.`
  String get creditUpdateLimitRequestMoreDesc {
    return Intl.message(
      'If you want more limit you can request to check your eligibility and we will try to give you the maximum credit limit possible.',
      name: 'creditUpdateLimitRequestMoreDesc',
      desc: '',
      args: [],
    );
  }

  /// `No, Thanks`
  String get creditUpdateLimitRequestMoreDismiss {
    return Intl.message(
      'No, Thanks',
      name: 'creditUpdateLimitRequestMoreDismiss',
      desc: '',
      args: [],
    );
  }

  /// `We’ve got your request.  We'll notify you once we've reviewed all your information.`
  String get creditUpdateLimitRequestMoreSuccessText {
    return Intl.message(
      'We’ve got your request.  We\'ll notify you once we\'ve reviewed all your information.',
      name: 'creditUpdateLimitRequestMoreSuccessText',
      desc: '',
      args: [],
    );
  }

  /// `Do you want to request for more than {amount}?`
  String creditUpdateLimitRequestMoreTitle(String amount) {
    return Intl.message(
      'Do you want to request for more than $amount?',
      name: 'creditUpdateLimitRequestMoreTitle',
      desc: '',
      args: [amount],
    );
  }

  /// `Your limit has been updated successfully. Your new Credit limit is {amount}`
  String creditUpdateLimitSuccessDesc(String amount) {
    return Intl.message(
      'Your limit has been updated successfully. Your new Credit limit is $amount',
      name: 'creditUpdateLimitSuccessDesc',
      desc: '',
      args: [amount],
    );
  }

  /// `DONE!`
  String get creditUpdateLimitSuccessTitle {
    return Intl.message(
      'DONE!',
      name: 'creditUpdateLimitSuccessTitle',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong while trying to update your application.`
  String get creditUpdateOtherBankAccountsIbanError {
    return Intl.message(
      'Something went wrong while trying to update your application.',
      name: 'creditUpdateOtherBankAccountsIbanError',
      desc: '',
      args: [],
    );
  }

  /// `Upload statement {nextStatement} of {required}`
  String creditUploadTileText(String nextStatement, String required) {
    return Intl.message(
      'Upload statement $nextStatement of $required',
      name: 'creditUploadTileText',
      desc: '',
      args: [nextStatement, required],
    );
  }

  /// `Next`
  String get creditVatReportingIntervalCtaText {
    return Intl.message(
      'Next',
      name: 'creditVatReportingIntervalCtaText',
      desc: '',
      args: [],
    );
  }

  /// `My company submits VAT reports to the tax authority on a monthly basis.`
  String get creditVatReportingIntervalMonthlySubtitle {
    return Intl.message(
      'My company submits VAT reports to the tax authority on a monthly basis.',
      name: 'creditVatReportingIntervalMonthlySubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Yes, we report monthly`
  String get creditVatReportingIntervalMonthlyTitle {
    return Intl.message(
      'Yes, we report monthly',
      name: 'creditVatReportingIntervalMonthlyTitle',
      desc: '',
      args: [],
    );
  }

  /// `My company is not subject to VAT obligations.`
  String get creditVatReportingIntervalNoReportingSubtitle {
    return Intl.message(
      'My company is not subject to VAT obligations.',
      name: 'creditVatReportingIntervalNoReportingSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `No, we don’t report VAT`
  String get creditVatReportingIntervalNoReportingTitle {
    return Intl.message(
      'No, we don’t report VAT',
      name: 'creditVatReportingIntervalNoReportingTitle',
      desc: '',
      args: [],
    );
  }

  /// `My company submits VAT reports to the tax authorities on a quarterly basis.`
  String get creditVatReportingIntervalQuarterlySubtitle {
    return Intl.message(
      'My company submits VAT reports to the tax authorities on a quarterly basis.',
      name: 'creditVatReportingIntervalQuarterlySubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Yes, we report quarterly`
  String get creditVatReportingIntervalQuarterlyTitle {
    return Intl.message(
      'Yes, we report quarterly',
      name: 'creditVatReportingIntervalQuarterlyTitle',
      desc: '',
      args: [],
    );
  }

  /// `Vat Reporting Interval must be selected`
  String get creditVatReportingMustBeSelectedError {
    return Intl.message(
      'Vat Reporting Interval must be selected',
      name: 'creditVatReportingMustBeSelectedError',
      desc: '',
      args: [],
    );
  }

  /// `Here's what we need`
  String get creditVatScreenSubtitle {
    return Intl.message(
      'Here\'s what we need',
      name: 'creditVatScreenSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Does your company report VAT?`
  String get creditVatScreenTitle {
    return Intl.message(
      'Does your company report VAT?',
      name: 'creditVatScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `View statement example`
  String get creditVatStatementUploadExample {
    return Intl.message(
      'View statement example',
      name: 'creditVatStatementUploadExample',
      desc: '',
      args: [],
    );
  }

  /// `{x} of {required} statements uploaded`
  String creditVatStatementUploadProgress(String required, String x) {
    return Intl.message(
      '$x of $required statements uploaded',
      name: 'creditVatStatementUploadProgress',
      desc: '',
      args: [required, x],
    );
  }

  /// `Company name`
  String get creditVatStatementUploadScreenDescriptionDetailOne {
    return Intl.message(
      'Company name',
      name: 'creditVatStatementUploadScreenDescriptionDetailOne',
      desc: '',
      args: [],
    );
  }

  /// `Tax period`
  String get creditVatStatementUploadScreenDescriptionDetailThree {
    return Intl.message(
      'Tax period',
      name: 'creditVatStatementUploadScreenDescriptionDetailThree',
      desc: '',
      args: [],
    );
  }

  /// `Turnover and sales information`
  String get creditVatStatementUploadScreenDescriptionDetailTwo {
    return Intl.message(
      'Turnover and sales information',
      name: 'creditVatStatementUploadScreenDescriptionDetailTwo',
      desc: '',
      args: [],
    );
  }

  /// `Please ensure your statement is in a standard format and includes :`
  String get creditVatStatementUploadScreenDescriptionIntro {
    return Intl.message(
      'Please ensure your statement is in a standard format and includes :',
      name: 'creditVatStatementUploadScreenDescriptionIntro',
      desc: '',
      args: [],
    );
  }

  /// `Upload your company's last six filed VAT statements.`
  String get creditVatStatementUploadScreenTitleVatReportingIntervalMonthly {
    return Intl.message(
      'Upload your company\'s last six filed VAT statements.',
      name: 'creditVatStatementUploadScreenTitleVatReportingIntervalMonthly',
      desc: '',
      args: [],
    );
  }

  /// `Upload your company's last two filed VAT statements.`
  String get creditVatStatementUploadScreenTitleVatReportingIntervalQuaterly {
    return Intl.message(
      'Upload your company\'s last two filed VAT statements.',
      name: 'creditVatStatementUploadScreenTitleVatReportingIntervalQuaterly',
      desc: '',
      args: [],
    );
  }

  /// `The VAT reporting select must be either monthly or quarterly. Please select the appropriate interval from the previous screen`
  String get creditVatUploadInvalidReportingInterval {
    return Intl.message(
      'The VAT reporting select must be either monthly or quarterly. Please select the appropriate interval from the previous screen',
      name: 'creditVatUploadInvalidReportingInterval',
      desc: '',
      args: [],
    );
  }

  /// `Custom statement`
  String get customStatementLabel {
    return Intl.message(
      'Custom statement',
      name: 'customStatementLabel',
      desc: '',
      args: [],
    );
  }

  /// `1 month`
  String get customStatementPeriodOneMonth {
    return Intl.message(
      '1 month',
      name: 'customStatementPeriodOneMonth',
      desc: '',
      args: [],
    );
  }

  /// `1 year`
  String get customStatementPeriodOneYear {
    return Intl.message(
      '1 year',
      name: 'customStatementPeriodOneYear',
      desc: '',
      args: [],
    );
  }

  /// `6 months`
  String get customStatementPeriodSixMonths {
    return Intl.message(
      '6 months',
      name: 'customStatementPeriodSixMonths',
      desc: '',
      args: [],
    );
  }

  /// `3 months`
  String get customStatementPeriodThreeMonths {
    return Intl.message(
      '3 months',
      name: 'customStatementPeriodThreeMonths',
      desc: '',
      args: [],
    );
  }

  /// `You’re eligible to get up to {amount} as Quick cash. The money will be transferred to your AED account immediately.`
  String dashboardEasyCashBannerSubTitle(String amount) {
    return Intl.message(
      'You’re eligible to get up to $amount as Quick cash. The money will be transferred to your AED account immediately.',
      name: 'dashboardEasyCashBannerSubTitle',
      desc: '',
      args: [amount],
    );
  }

  /// `Quick cash`
  String get dashboardEasyCashBannerTitle {
    return Intl.message(
      'Quick cash',
      name: 'dashboardEasyCashBannerTitle',
      desc: '',
      args: [],
    );
  }

  /// `Account updated`
  String get debitAccountUpdateSuccess {
    return Intl.message(
      'Account updated',
      name: 'debitAccountUpdateSuccess',
      desc: '',
      args: [],
    );
  }

  /// `Disclaimer`
  String get disclaimerLabel {
    return Intl.message(
      'Disclaimer',
      name: 'disclaimerLabel',
      desc: '',
      args: [],
    );
  }

  /// `Share document`
  String get documentViewerShare {
    return Intl.message(
      'Share document',
      name: 'documentViewerShare',
      desc: '',
      args: [],
    );
  }

  /// `Due on {date}`
  String dueOnLabel(String date) {
    return Intl.message(
      'Due on $date',
      name: 'dueOnLabel',
      desc: '',
      args: [date],
    );
  }

  /// `Need some help?`
  String get easyCashManageScreenFaqSectionTitle {
    return Intl.message(
      'Need some help?',
      name: 'easyCashManageScreenFaqSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Manage Quick Cash`
  String get easyCashManageScreenTitle {
    return Intl.message(
      'Manage Quick Cash',
      name: 'easyCashManageScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `We’re processing your funds, and they’ll be in your Wio Business account in just a few minutes. We’ll let you know once they’re ready to use. \nWhen the funds arrive, simply sign in here to manage your loan!`
  String get embeddedLendingAgreementSubmittedSuccessSubtitle {
    return Intl.message(
      'We’re processing your funds, and they’ll be in your Wio Business account in just a few minutes. We’ll let you know once they’re ready to use. \nWhen the funds arrive, simply sign in here to manage your loan!',
      name: 'embeddedLendingAgreementSubmittedSuccessSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Done. Your money is on its way!`
  String get embeddedLendingAgreementSubmittedSuccessTitle {
    return Intl.message(
      'Done. Your money is on its way!',
      name: 'embeddedLendingAgreementSubmittedSuccessTitle',
      desc: '',
      args: [],
    );
  }

  /// `Mabrook!`
  String get embeddedLendingApplicationSubmittedTitle {
    return Intl.message(
      'Mabrook!',
      name: 'embeddedLendingApplicationSubmittedTitle',
      desc: '',
      args: [],
    );
  }

  /// `End date`
  String get endDateLabel {
    return Intl.message(
      'End date',
      name: 'endDateLabel',
      desc: '',
      args: [],
    );
  }

  /// `To close your Wio Credit account, you'll need to repay your Quick Cash balance`
  String get errorMessageActiveEasyCashAccount {
    return Intl.message(
      'To close your Wio Credit account, you\'ll need to repay your Quick Cash balance',
      name: 'errorMessageActiveEasyCashAccount',
      desc: '',
      args: [],
    );
  }

  /// `Entered referral code is expired`
  String get errorMessageExpiredReferralCode {
    return Intl.message(
      'Entered referral code is expired',
      name: 'errorMessageExpiredReferralCode',
      desc: '',
      args: [],
    );
  }

  /// `Entered referral code is invalid`
  String get errorMessageInvalidReferralCode {
    return Intl.message(
      'Entered referral code is invalid',
      name: 'errorMessageInvalidReferralCode',
      desc: '',
      args: [],
    );
  }

  /// `FAQ`
  String get faq {
    return Intl.message(
      'FAQ',
      name: 'faq',
      desc: '',
      args: [],
    );
  }

  /// `{repaymentPercentage}% autopay amount:`
  String feeCalculationAutopayAmount(String repaymentPercentage) {
    return Intl.message(
      '$repaymentPercentage% autopay amount:',
      name: 'feeCalculationAutopayAmount',
      desc: '',
      args: [repaymentPercentage],
    );
  }

  /// `Carry-over amount:`
  String get feeCalculationCarryOverAmount {
    return Intl.message(
      'Carry-over amount:',
      name: 'feeCalculationCarryOverAmount',
      desc: '',
      args: [],
    );
  }

  /// `Carry-over fee:`
  String get feeCalculationCarryOverFee {
    return Intl.message(
      'Carry-over fee:',
      name: 'feeCalculationCarryOverFee',
      desc: '',
      args: [],
    );
  }

  /// `Full fee will apply on your carry-over amount after {date}. Your fee-free period will resume each time you repay your outstanding balance in full.`
  String feeCalculationDetailsBottomSheetFeeApplyDescription(String date) {
    return Intl.message(
      'Full fee will apply on your carry-over amount after $date. Your fee-free period will resume each time you repay your outstanding balance in full.',
      name: 'feeCalculationDetailsBottomSheetFeeApplyDescription',
      desc: '',
      args: [date],
    );
  }

  /// `Outstanding balance:`
  String get feeCalculationOutstandingBalance {
    return Intl.message(
      'Outstanding balance:',
      name: 'feeCalculationOutstandingBalance',
      desc: '',
      args: [],
    );
  }

  /// `Looks like the file is too large. The max file size is {size} MB.`
  String fileSizeExceedsLimit(String size) {
    return Intl.message(
      'Looks like the file is too large. The max file size is $size MB.',
      name: 'fileSizeExceedsLimit',
      desc: '',
      args: [size],
    );
  }

  /// `First key`
  String get firstKey {
    return Intl.message(
      'First key',
      name: 'firstKey',
      desc: '',
      args: [],
    );
  }

  /// `Generate statement`
  String get generateStatementLabel {
    return Intl.message(
      'Generate statement',
      name: 'generateStatementLabel',
      desc: '',
      args: [],
    );
  }

  /// `Get Quick Cash`
  String get getEasyCashLabel {
    return Intl.message(
      'Get Quick Cash',
      name: 'getEasyCashLabel',
      desc: '',
      args: [],
    );
  }

  /// `Help`
  String get helpCta {
    return Intl.message(
      'Help',
      name: 'helpCta',
      desc: '',
      args: [],
    );
  }

  /// `Borrowing agreement signed`
  String get hypothecationAlreadyCompleteSuccessSubtitle {
    return Intl.message(
      'Borrowing agreement signed',
      name: 'hypothecationAlreadyCompleteSuccessSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Mabrook!`
  String get hypothecationAlreadyCompleteSuccessTitle {
    return Intl.message(
      'Mabrook!',
      name: 'hypothecationAlreadyCompleteSuccessTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your application is under review. It shouldn't take more than 3 days.`
  String get inReviewBannerDescription {
    return Intl.message(
      'Your application is under review. It shouldn\'t take more than 3 days.',
      name: 'inReviewBannerDescription',
      desc: '',
      args: [],
    );
  }

  /// `Invalid Product type`
  String get learnMorePageInvalidProductTypeError {
    return Intl.message(
      'Invalid Product type',
      name: 'learnMorePageInvalidProductTypeError',
      desc: '',
      args: [],
    );
  }

  /// `The amount of {holdAmount} is pending. It can be paid back once transactions are completed. Learn more`
  String lendingAmountOnHoldPayCreditContent(String holdAmount) {
    return Intl.message(
      'The amount of $holdAmount is pending. It can be paid back once transactions are completed. Learn more',
      name: 'lendingAmountOnHoldPayCreditContent',
      desc: '',
      args: [holdAmount],
    );
  }

  /// `Learn more`
  String get lendingAmountOnHoldPayCreditContentHighlighted {
    return Intl.message(
      'Learn more',
      name: 'lendingAmountOnHoldPayCreditContentHighlighted',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get lendingAutopaySave {
    return Intl.message(
      'Save',
      name: 'lendingAutopaySave',
      desc: '',
      args: [],
    );
  }

  /// `Available to spend`
  String get lendingCreditDashboardAvlToSpend {
    return Intl.message(
      'Available to spend',
      name: 'lendingCreditDashboardAvlToSpend',
      desc: '',
      args: [],
    );
  }

  /// `Sorry, you can’t decrease your credit limit below your spent amount.`
  String get lendingCreditLimitReduceErrorText {
    return Intl.message(
      'Sorry, you can’t decrease your credit limit below your spent amount.',
      name: 'lendingCreditLimitReduceErrorText',
      desc: '',
      args: [],
    );
  }

  /// `FAQ`
  String get lendingFAQ {
    return Intl.message(
      'FAQ',
      name: 'lendingFAQ',
      desc: '',
      args: [],
    );
  }

  /// `Annual fee`
  String get lendingManageAnnualFee {
    return Intl.message(
      'Annual fee',
      name: 'lendingManageAnnualFee',
      desc: '',
      args: [],
    );
  }

  /// `Available to spend`
  String get lendingManageAvailableToSpend {
    return Intl.message(
      'Available to spend',
      name: 'lendingManageAvailableToSpend',
      desc: '',
      args: [],
    );
  }

  /// `Cash withdrawal`
  String get lendingManageCashWithdrawal {
    return Intl.message(
      'Cash withdrawal',
      name: 'lendingManageCashWithdrawal',
      desc: '',
      args: [],
    );
  }

  /// `Close credit account`
  String get lendingManageCloseCreditAccountCta {
    return Intl.message(
      'Close credit account',
      name: 'lendingManageCloseCreditAccountCta',
      desc: '',
      args: [],
    );
  }

  /// `Documents`
  String get lendingManageCreditPageDocuments {
    return Intl.message(
      'Documents',
      name: 'lendingManageCreditPageDocuments',
      desc: '',
      args: [],
    );
  }

  /// `Download statement`
  String get lendingManageCreditPageDownloadStatement {
    return Intl.message(
      'Download statement',
      name: 'lendingManageCreditPageDownloadStatement',
      desc: '',
      args: [],
    );
  }

  /// `Manage credit`
  String get lendingManageCreditPageManageCredit {
    return Intl.message(
      'Manage credit',
      name: 'lendingManageCreditPageManageCredit',
      desc: '',
      args: [],
    );
  }

  /// `Payment date`
  String get lendingManageCreditPagePaymentDate {
    return Intl.message(
      'Payment date',
      name: 'lendingManageCreditPagePaymentDate',
      desc: '',
      args: [],
    );
  }

  /// `Roll-over fee`
  String get lendingManageCreditPageRollover {
    return Intl.message(
      'Roll-over fee',
      name: 'lendingManageCreditPageRollover',
      desc: '',
      args: [],
    );
  }

  /// `Your credit`
  String get lendingManageCreditPageYourCredit {
    return Intl.message(
      'Your credit',
      name: 'lendingManageCreditPageYourCredit',
      desc: '',
      args: [],
    );
  }

  /// `{day} of every month`
  String lendingManageCreditRepaymentDate(String day) {
    return Intl.message(
      '$day of every month',
      name: 'lendingManageCreditRepaymentDate',
      desc: '',
      args: [day],
    );
  }

  /// `Need some help?`
  String get lendingManageNeedSomeHelp {
    return Intl.message(
      'Need some help?',
      name: 'lendingManageNeedSomeHelp',
      desc: '',
      args: [],
    );
  }

  /// `No annual fee`
  String get lendingManageNoAnnualFee {
    return Intl.message(
      'No annual fee',
      name: 'lendingManageNoAnnualFee',
      desc: '',
      args: [],
    );
  }

  /// `No cash withdrawal`
  String get lendingManageNoCashWithdrawal {
    return Intl.message(
      'No cash withdrawal',
      name: 'lendingManageNoCashWithdrawal',
      desc: '',
      args: [],
    );
  }

  /// `Reduce your credit limit`
  String get lendingManageReduceCreditLimit {
    return Intl.message(
      'Reduce your credit limit',
      name: 'lendingManageReduceCreditLimit',
      desc: '',
      args: [],
    );
  }

  /// `Repayment`
  String get lendingManageRepaymentTitle {
    return Intl.message(
      'Repayment',
      name: 'lendingManageRepaymentTitle',
      desc: '',
      args: [],
    );
  }

  /// `Credit agreement`
  String get lendingManageScreenCreditAgreement {
    return Intl.message(
      'Credit agreement',
      name: 'lendingManageScreenCreditAgreement',
      desc: '',
      args: [],
    );
  }

  /// `Credit limit`
  String get lendingManageScreenCreditLimit {
    return Intl.message(
      'Credit limit',
      name: 'lendingManageScreenCreditLimit',
      desc: '',
      args: [],
    );
  }

  /// `Key fact statement`
  String get lendingManageScreenKfs {
    return Intl.message(
      'Key fact statement',
      name: 'lendingManageScreenKfs',
      desc: '',
      args: [],
    );
  }

  /// `Download statement`
  String get lendingManageStatementsCta {
    return Intl.message(
      'Download statement',
      name: 'lendingManageStatementsCta',
      desc: '',
      args: [],
    );
  }

  /// `Your next autopay on {date} of {month}`
  String lendingNextPayment(String date, String month) {
    return Intl.message(
      'Your next autopay on $date of $month',
      name: 'lendingNextPayment',
      desc: '',
      args: [date, month],
    );
  }

  /// `No fees`
  String get lendingNoFee {
    return Intl.message(
      'No fees',
      name: 'lendingNoFee',
      desc: '',
      args: [],
    );
  }

  /// `{percentage} of used money`
  String lendingPaybackPercent(String percentage) {
    return Intl.message(
      '$percentage of used money',
      name: 'lendingPaybackPercent',
      desc: '',
      args: [percentage],
    );
  }

  /// `{amount} and {fee} fee as of today`
  String lendingPaymentBreakdown(String amount, String fee) {
    return Intl.message(
      '$amount and $fee fee as of today',
      name: 'lendingPaymentBreakdown',
      desc: '',
      args: [amount, fee],
    );
  }

  /// `{amount} as of today`
  String lendingPaymentBreakdownNoFee(String amount) {
    return Intl.message(
      '$amount as of today',
      name: 'lendingPaymentBreakdownNoFee',
      desc: '',
      args: [amount],
    );
  }

  /// `Plus fee`
  String get lendingPlusFees {
    return Intl.message(
      'Plus fee',
      name: 'lendingPlusFees',
      desc: '',
      args: [],
    );
  }

  /// `All used money`
  String get lendingSelectorLabel {
    return Intl.message(
      'All used money',
      name: 'lendingSelectorLabel',
      desc: '',
      args: [],
    );
  }

  /// `Amount is taken from your AED saving space if your don’t have enough money in your current AED account to avoid fees and charges.`
  String get lendingSetupPaymentThingsSectionTitle2 {
    return Intl.message(
      'Amount is taken from your AED saving space if your don’t have enough money in your current AED account to avoid fees and charges.',
      name: 'lendingSetupPaymentThingsSectionTitle2',
      desc: '',
      args: [],
    );
  }

  /// `Pay back the full amount every month and avoid fees and charges`
  String get lendingSetupPaymentThingsSectionsTitle1 {
    return Intl.message(
      'Pay back the full amount every month and avoid fees and charges',
      name: 'lendingSetupPaymentThingsSectionsTitle1',
      desc: '',
      args: [],
    );
  }

  /// `Things to know`
  String get lendingSetupPaymentThingsToKnowTitle {
    return Intl.message(
      'Things to know',
      name: 'lendingSetupPaymentThingsToKnowTitle',
      desc: '',
      args: [],
    );
  }

  /// `Spent {spent} of {creditLimit}`
  String lendingSpentOf(String creditLimit, String spent) {
    return Intl.message(
      'Spent $spent of $creditLimit',
      name: 'lendingSpentOf',
      desc: '',
      args: [creditLimit, spent],
    );
  }

  /// `A referral code is a unique identifier shared by Wio partners to help you access credit facilities with added benefits—such as faster application review, personalized support, and more.\n\nIf a third party referred you for a Wio credit facility but didn’t provide a referral code, feel free to ask them—they may have simply forgotten to share it.`
  String get loanReferralCodeBottomsheetDescription {
    return Intl.message(
      'A referral code is a unique identifier shared by Wio partners to help you access credit facilities with added benefits—such as faster application review, personalized support, and more.\n\nIf a third party referred you for a Wio credit facility but didn’t provide a referral code, feel free to ask them—they may have simply forgotten to share it.',
      name: 'loanReferralCodeBottomsheetDescription',
      desc: '',
      args: [],
    );
  }

  /// `Enter code`
  String get loanReferralCodeInputLabel {
    return Intl.message(
      'Enter code',
      name: 'loanReferralCodeInputLabel',
      desc: '',
      args: [],
    );
  }

  /// `Invalid code`
  String get loanReferralCodeInvalid {
    return Intl.message(
      'Invalid code',
      name: 'loanReferralCodeInvalid',
      desc: '',
      args: [],
    );
  }

  /// `I don’t have a referral code`
  String get loanReferralCodePageSkipInput {
    return Intl.message(
      'I don’t have a referral code',
      name: 'loanReferralCodePageSkipInput',
      desc: '',
      args: [],
    );
  }

  /// `If you don’t just select “I don’t have a code”`
  String get loanReferralCodePageSubtitle {
    return Intl.message(
      'If you don’t just select “I don’t have a code”',
      name: 'loanReferralCodePageSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `If you’ve got a referral code let’s fire it up`
  String get loanReferralCodePageTitle {
    return Intl.message(
      'If you’ve got a referral code let’s fire it up',
      name: 'loanReferralCodePageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Ensure timely daily payments to meet your loan milestones and avoid extra charges. Your milestone amounts will automatically adjust with each payment, any new borrowing, or missed payments`
  String get loanRepaymentMilestonesDescription {
    return Intl.message(
      'Ensure timely daily payments to meet your loan milestones and avoid extra charges. Your milestone amounts will automatically adjust with each payment, any new borrowing, or missed payments',
      name: 'loanRepaymentMilestonesDescription',
      desc: '',
      args: [],
    );
  }

  /// `Loan repayment milestones`
  String get loanRepaymentMilestonesTitle {
    return Intl.message(
      'Loan repayment milestones',
      name: 'loanRepaymentMilestonesTitle',
      desc: '',
      args: [],
    );
  }

  /// `You have missed your milestone`
  String get loanRepaymentMissedMilestonesDescription {
    return Intl.message(
      'You have missed your milestone',
      name: 'loanRepaymentMissedMilestonesDescription',
      desc: '',
      args: [],
    );
  }

  /// `What type of loan is right for you?`
  String get loanTypeInputPageTitle {
    return Intl.message(
      'What type of loan is right for you?',
      name: 'loanTypeInputPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Loan type`
  String get loanTypeLabel {
    return Intl.message(
      'Loan type',
      name: 'loanTypeLabel',
      desc: '',
      args: [],
    );
  }

  /// `What is a referral code?`
  String get loanWhatIsReferralCode {
    return Intl.message(
      'What is a referral code?',
      name: 'loanWhatIsReferralCode',
      desc: '',
      args: [],
    );
  }

  /// `Never miss a repayment! Enable it and relax. Meanwhile, grow your wealth by earning up to 5%* interest. Double win!`
  String get managePosAutopayFromSSDescription {
    return Intl.message(
      'Never miss a repayment! Enable it and relax. Meanwhile, grow your wealth by earning up to 5%* interest. Double win!',
      name: 'managePosAutopayFromSSDescription',
      desc: '',
      args: [],
    );
  }

  /// `Manage`
  String get managePosCreditPageTitle {
    return Intl.message(
      'Manage',
      name: 'managePosCreditPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Preferred account`
  String get managePosCreditPreferedAccount {
    return Intl.message(
      'Preferred account',
      name: 'managePosCreditPreferedAccount',
      desc: '',
      args: [],
    );
  }

  /// `Next autopayment is on {date}`
  String nextAutoPayLabel(String date) {
    return Intl.message(
      'Next autopayment is on $date',
      name: 'nextAutoPayLabel',
      desc: '',
      args: [date],
    );
  }

  /// `Next`
  String get nextCta {
    return Intl.message(
      'Next',
      name: 'nextCta',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get nonReportingVatCtaText {
    return Intl.message(
      'Next',
      name: 'nonReportingVatCtaText',
      desc: '',
      args: [],
    );
  }

  /// `Please specify other`
  String get otherReasonInputLabel {
    return Intl.message(
      'Please specify other',
      name: 'otherReasonInputLabel',
      desc: '',
      args: [],
    );
  }

  /// `Overdue amount`
  String get overdueAmountLabel {
    return Intl.message(
      'Overdue amount',
      name: 'overdueAmountLabel',
      desc: '',
      args: [],
    );
  }

  /// `Pay and close`
  String get payAndCloseCta {
    return Intl.message(
      'Pay and close',
      name: 'payAndCloseCta',
      desc: '',
      args: [],
    );
  }

  /// `Balance: {amount}`
  String payCreditAccountBalance(String amount) {
    return Intl.message(
      'Balance: $amount',
      name: 'payCreditAccountBalance',
      desc: '',
      args: [amount],
    );
  }

  /// `{currency} account`
  String payCreditAccountTitle(String currency) {
    return Intl.message(
      '$currency account',
      name: 'payCreditAccountTitle',
      desc: '',
      args: [currency],
    );
  }

  /// `The amount cannot be less than the minimum: {amount}`
  String payCreditBelowMinimumPaymentAmount(String amount) {
    return Intl.message(
      'The amount cannot be less than the minimum: $amount',
      name: 'payCreditBelowMinimumPaymentAmount',
      desc: '',
      args: [amount],
    );
  }

  /// `The amount exceeds full amount: {amount}`
  String payCreditExcessivePaymentLabel(String amount) {
    return Intl.message(
      'The amount exceeds full amount: $amount',
      name: 'payCreditExcessivePaymentLabel',
      desc: '',
      args: [amount],
    );
  }

  /// `From`
  String get payCreditFromLabel {
    return Intl.message(
      'From',
      name: 'payCreditFromLabel',
      desc: '',
      args: [],
    );
  }

  /// `Full amount: {amount}`
  String payCreditFullAmountOption(String amount) {
    return Intl.message(
      'Full amount: $amount',
      name: 'payCreditFullAmountOption',
      desc: '',
      args: [amount],
    );
  }

  /// `The amount exceeds your balance: {amount}`
  String payCreditInsufficientBalanceLabel(String amount) {
    return Intl.message(
      'The amount exceeds your balance: $amount',
      name: 'payCreditInsufficientBalanceLabel',
      desc: '',
      args: [amount],
    );
  }

  /// `Minimum: {amount}`
  String payCreditMinimumAmountOption(String amount) {
    return Intl.message(
      'Minimum: $amount',
      name: 'payCreditMinimumAmountOption',
      desc: '',
      args: [amount],
    );
  }

  /// `Pay credit`
  String get payCreditPageButtonTitle {
    return Intl.message(
      'Pay credit',
      name: 'payCreditPageButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Pay and unlock`
  String get payCreditPageCreditLockedButtonTitle {
    return Intl.message(
      'Pay and unlock',
      name: 'payCreditPageCreditLockedButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Pay credit`
  String get payCreditPageTitle {
    return Intl.message(
      'Pay credit',
      name: 'payCreditPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Go to borrow`
  String get payCreditSuccessPageButtonTitle {
    return Intl.message(
      'Go to borrow',
      name: 'payCreditSuccessPageButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Wio credit unlocked`
  String get payCreditSuccessPageCreditUnlocked {
    return Intl.message(
      'Wio credit unlocked',
      name: 'payCreditSuccessPageCreditUnlocked',
      desc: '',
      args: [],
    );
  }

  /// `You’ve payed {amount}\nto Credit from {currency} account`
  String payCreditSuccessPageDescription(String amount, String currency) {
    return Intl.message(
      'You’ve payed $amount\nto Credit from $currency account',
      name: 'payCreditSuccessPageDescription',
      desc: '',
      args: [amount, currency],
    );
  }

  /// `Credit payment received`
  String get payCreditSuccessPageSubtitle {
    return Intl.message(
      'Credit payment received',
      name: 'payCreditSuccessPageSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `BOOM!`
  String get payCreditSuccessPageTitle {
    return Intl.message(
      'BOOM!',
      name: 'payCreditSuccessPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `To`
  String get payCreditToLabel {
    return Intl.message(
      'To',
      name: 'payCreditToLabel',
      desc: '',
      args: [],
    );
  }

  /// `Done`
  String get payCreditUnlockSuccessPageButtonTitle {
    return Intl.message(
      'Done',
      name: 'payCreditUnlockSuccessPageButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your Credit money has been unlocked successfully.`
  String get payCreditUnlockSuccessPageSubtitle {
    return Intl.message(
      'Your Credit money has been unlocked successfully.',
      name: 'payCreditUnlockSuccessPageSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Wio Business Credit Unlocked`
  String get payCreditUnlockSuccessPageTitle {
    return Intl.message(
      'Wio Business Credit Unlocked',
      name: 'payCreditUnlockSuccessPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Apply now to get a customized offer based on your POS sales  `
  String get posApplicationApplyBannerSubtitle {
    return Intl.message(
      'Apply now to get a customized offer based on your POS sales  ',
      name: 'posApplicationApplyBannerSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Wio line of credit`
  String get posApplicationApplyBannerTitle {
    return Intl.message(
      'Wio line of credit',
      name: 'posApplicationApplyBannerTitle',
      desc: '',
      args: [],
    );
  }

  /// `You’ve got only a few steps left to complete your Wio Line of Credit application`
  String get posApplicationInProgressBannerSubtitle {
    return Intl.message(
      'You’ve got only a few steps left to complete your Wio Line of Credit application',
      name: 'posApplicationInProgressBannerSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Complete your application`
  String get posApplicationInProgressBannerTitle {
    return Intl.message(
      'Complete your application',
      name: 'posApplicationInProgressBannerTitle',
      desc: '',
      args: [],
    );
  }

  /// `Get started`
  String get posApplicationOfferBannerCta {
    return Intl.message(
      'Get started',
      name: 'posApplicationOfferBannerCta',
      desc: '',
      args: [],
    );
  }

  /// `Now let’s review your credit limit and get started by enabling your POS credit `
  String get posApplicationOfferBannerSubtitle {
    return Intl.message(
      'Now let’s review your credit limit and get started by enabling your POS credit ',
      name: 'posApplicationOfferBannerSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Wio line of credit approved !`
  String get posApplicationOfferBannerTitle {
    return Intl.message(
      'Wio line of credit approved !',
      name: 'posApplicationOfferBannerTitle',
      desc: '',
      args: [],
    );
  }

  /// `Unfortunately, your application for Wio line of Credit has been declined. Your company doesn't meet all the required criteria. You can try again in three months.`
  String get posApplicationRejectedBannerSubtitle {
    return Intl.message(
      'Unfortunately, your application for Wio line of Credit has been declined. Your company doesn\'t meet all the required criteria. You can try again in three months.',
      name: 'posApplicationRejectedBannerSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Wio line of credit`
  String get posApplicationRejectedBannerTitle {
    return Intl.message(
      'Wio line of credit',
      name: 'posApplicationRejectedBannerTitle',
      desc: '',
      args: [],
    );
  }

  /// `Wio line of credit application under review`
  String get posApplicationUnderReviewBannerTitle {
    return Intl.message(
      'Wio line of credit application under review',
      name: 'posApplicationUnderReviewBannerTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your application is under review. It shouldn't take more than 3 days.`
  String get posApplicationUnderReviewSubtitle {
    return Intl.message(
      'Your application is under review. It shouldn\'t take more than 3 days.',
      name: 'posApplicationUnderReviewSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Available to borrow {balance}`
  String posAvailableToBorrow(String balance) {
    return Intl.message(
      'Available to borrow $balance',
      name: 'posAvailableToBorrow',
      desc: '',
      args: [balance],
    );
  }

  /// `New daily payment amount will apply from tomorrow. Today you will still pay {preBorrowDailyPaymentAmount}.`
  String posBorrowConfirmationPreBorrowDailyPaymentBanner(
      String preBorrowDailyPaymentAmount) {
    return Intl.message(
      'New daily payment amount will apply from tomorrow. Today you will still pay $preBorrowDailyPaymentAmount.',
      name: 'posBorrowConfirmationPreBorrowDailyPaymentBanner',
      desc: '',
      args: [preBorrowDailyPaymentAmount],
    );
  }

  /// `BORROW`
  String get posBorrowConfirmationTitle {
    return Intl.message(
      'BORROW',
      name: 'posBorrowConfirmationTitle',
      desc: '',
      args: [],
    );
  }

  /// `Amount exceeds your eligibility limit`
  String get posBorrowExceedsLimitMessage {
    return Intl.message(
      'Amount exceeds your eligibility limit',
      name: 'posBorrowExceedsLimitMessage',
      desc: '',
      args: [],
    );
  }

  /// `This amount will be added in your current outstanding balance as of today, with repayment due in the next {loanTerm} days.`
  String posBorrowInformationTip(String loanTerm) {
    return Intl.message(
      'This amount will be added in your current outstanding balance as of today, with repayment due in the next $loanTerm days.',
      name: 'posBorrowInformationTip',
      desc: '',
      args: [loanTerm],
    );
  }

  /// `Your daily amount and loan repayments milestones have been updated.`
  String get posBorrowLoanRepaymentUpdatedMessage {
    return Intl.message(
      'Your daily amount and loan repayments milestones have been updated.',
      name: 'posBorrowLoanRepaymentUpdatedMessage',
      desc: '',
      args: [],
    );
  }

  /// `Ready to receive your money?`
  String get posBorrowReadyToReceive {
    return Intl.message(
      'Ready to receive your money?',
      name: 'posBorrowReadyToReceive',
      desc: '',
      args: [],
    );
  }

  /// `Enter the amount and press “Continue” to review your loan details`
  String get posBorrowSubtitle {
    return Intl.message(
      'Enter the amount and press “Continue” to review your loan details',
      name: 'posBorrowSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Money transferred to  {currency} account`
  String posBorrowSuccessSubtitle(String currency) {
    return Intl.message(
      'Money transferred to  $currency account',
      name: 'posBorrowSuccessSubtitle',
      desc: '',
      args: [currency],
    );
  }

  /// `Swipe right to confirm `
  String get posBorrowSwipeRightToConfirm {
    return Intl.message(
      'Swipe right to confirm ',
      name: 'posBorrowSwipeRightToConfirm',
      desc: '',
      args: [],
    );
  }

  /// `How much would you like to borrow?`
  String get posBorrowTitle {
    return Intl.message(
      'How much would you like to borrow?',
      name: 'posBorrowTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your borrowing is paused until the overdue amount is repaid. Please pay now to avoid missing loan repayment milestone and extra charges.`
  String get posBottomSheetBorrowPausedBannerDesc {
    return Intl.message(
      'Your borrowing is paused until the overdue amount is repaid. Please pay now to avoid missing loan repayment milestone and extra charges.',
      name: 'posBottomSheetBorrowPausedBannerDesc',
      desc: '',
      args: [],
    );
  }

  /// `Carry-over amount`
  String get posBottomSheetCarryoverField {
    return Intl.message(
      'Carry-over amount',
      name: 'posBottomSheetCarryoverField',
      desc: '',
      args: [],
    );
  }

  /// `Daily payment amount`
  String get posBottomSheetDailyPaymentAmountField {
    return Intl.message(
      'Daily payment amount',
      name: 'posBottomSheetDailyPaymentAmountField',
      desc: '',
      args: [],
    );
  }

  /// `Interest`
  String get posBottomSheetInterestField {
    return Intl.message(
      'Interest',
      name: 'posBottomSheetInterestField',
      desc: '',
      args: [],
    );
  }

  /// `Late payment fee`
  String get posBottomSheetLatePaymentFeeField {
    return Intl.message(
      'Late payment fee',
      name: 'posBottomSheetLatePaymentFeeField',
      desc: '',
      args: [],
    );
  }

  /// `Total amount due`
  String get posBottomSheetMilestoneOverdueTotalAmountField {
    return Intl.message(
      'Total amount due',
      name: 'posBottomSheetMilestoneOverdueTotalAmountField',
      desc: '',
      args: [],
    );
  }

  /// `Missed due date`
  String get posBottomSheetMissedDueBannerTitle {
    return Intl.message(
      'Missed due date',
      name: 'posBottomSheetMissedDueBannerTitle',
      desc: '',
      args: [],
    );
  }

  /// `Overdue amount`
  String get posBottomSheetOverdueAmountField {
    return Intl.message(
      'Overdue amount',
      name: 'posBottomSheetOverdueAmountField',
      desc: '',
      args: [],
    );
  }

  /// `Overdue amount as of\n{date}`
  String posBottomSheetOverdueAmountFieldWithDueDate(String date) {
    return Intl.message(
      'Overdue amount as of\n$date',
      name: 'posBottomSheetOverdueAmountFieldWithDueDate',
      desc: '',
      args: [date],
    );
  }

  /// `Overdue amount`
  String get posBottomSheetOverdueTitle {
    return Intl.message(
      'Overdue amount',
      name: 'posBottomSheetOverdueTitle',
      desc: '',
      args: [],
    );
  }

  /// `Principal`
  String get posBottomSheetPrincipalField {
    return Intl.message(
      'Principal',
      name: 'posBottomSheetPrincipalField',
      desc: '',
      args: [],
    );
  }

  /// `Total due on {date}`
  String posBottomSheetTotalDueOn(String date) {
    return Intl.message(
      'Total due on $date',
      name: 'posBottomSheetTotalDueOn',
      desc: '',
      args: [date],
    );
  }

  /// `Total due`
  String get posBottomSheetTotalWithoutDate {
    return Intl.message(
      'Total due',
      name: 'posBottomSheetTotalWithoutDate',
      desc: '',
      args: [],
    );
  }

  /// `Close credit line`
  String get posClosureConfirmationCta {
    return Intl.message(
      'Close credit line',
      name: 'posClosureConfirmationCta',
      desc: '',
      args: [],
    );
  }

  /// `You will no longer have access to your statements.`
  String get posClosureConfirmationStep1Bullet1 {
    return Intl.message(
      'You will no longer have access to your statements.',
      name: 'posClosureConfirmationStep1Bullet1',
      desc: '',
      args: [],
    );
  }

  /// `You may only reapply through an invitation from your acquirer.`
  String get posClosureConfirmationStep1Bullet2 {
    return Intl.message(
      'You may only reapply through an invitation from your acquirer.',
      name: 'posClosureConfirmationStep1Bullet2',
      desc: '',
      args: [],
    );
  }

  /// `Select “Close credit line” to permanently close your Wio line of credit and repay any outstanding balance.  After closure:`
  String get posClosureConfirmationStep1Info {
    return Intl.message(
      'Select “Close credit line” to permanently close your Wio line of credit and repay any outstanding balance.  After closure:',
      name: 'posClosureConfirmationStep1Info',
      desc: '',
      args: [],
    );
  }

  /// `Payback and close`
  String get posClosureConfirmationStep1Title {
    return Intl.message(
      'Payback and close',
      name: 'posClosureConfirmationStep1Title',
      desc: '',
      args: [],
    );
  }

  /// `We will close your credit line and send you the Non Objection Certificate. Share this document with your acquirer to release your POS.`
  String get posClosureConfirmationStep2Info {
    return Intl.message(
      'We will close your credit line and send you the Non Objection Certificate. Share this document with your acquirer to release your POS.',
      name: 'posClosureConfirmationStep2Info',
      desc: '',
      args: [],
    );
  }

  /// `Release your POS`
  String get posClosureConfirmationStep2Title {
    return Intl.message(
      'Release your POS',
      name: 'posClosureConfirmationStep2Title',
      desc: '',
      args: [],
    );
  }

  /// `This is how it works`
  String get posClosureConfirmationSubtitle {
    return Intl.message(
      'This is how it works',
      name: 'posClosureConfirmationSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `I confirm I’d like to close my credit line`
  String get posClosureConfirmationText {
    return Intl.message(
      'I confirm I’d like to close my credit line',
      name: 'posClosureConfirmationText',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to close your Wio line of credit? `
  String get posClosureConfirmationTitle {
    return Intl.message(
      'Are you sure you want to close your Wio line of credit? ',
      name: 'posClosureConfirmationTitle',
      desc: '',
      args: [],
    );
  }

  /// `The non objection certificate for the POS release have been sent to {email}`
  String posClosureSuccessDescription(String email) {
    return Intl.message(
      'The non objection certificate for the POS release have been sent to $email',
      name: 'posClosureSuccessDescription',
      desc: '',
      args: [email],
    );
  }

  /// `Your credit account is closed.`
  String get posClosureSuccessSubtitle {
    return Intl.message(
      'Your credit account is closed.',
      name: 'posClosureSuccessSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `IT’S DONE!`
  String get posClosureSuccessTitle {
    return Intl.message(
      'IT’S DONE!',
      name: 'posClosureSuccessTitle',
      desc: '',
      args: [],
    );
  }

  /// `Complete POS hypothecation`
  String get posCompleteHypothecation {
    return Intl.message(
      'Complete POS hypothecation',
      name: 'posCompleteHypothecation',
      desc: '',
      args: [],
    );
  }

  /// `Follow the instructions sent to your email address to complete the POS hypothecation and activate your credit line.`
  String get posCompleteHypothecationDescription {
    return Intl.message(
      'Follow the instructions sent to your email address to complete the POS hypothecation and activate your credit line.',
      name: 'posCompleteHypothecationDescription',
      desc: '',
      args: [],
    );
  }

  /// `Wio line of credit`
  String get posCreditBannerLabel {
    return Intl.message(
      'Wio line of credit',
      name: 'posCreditBannerLabel',
      desc: '',
      args: [],
    );
  }

  /// `POS credit`
  String get posCreditLabel {
    return Intl.message(
      'POS credit',
      name: 'posCreditLabel',
      desc: '',
      args: [],
    );
  }

  /// `Missed daily payment`
  String get posCreditMissedPayment {
    return Intl.message(
      'Missed daily payment',
      name: 'posCreditMissedPayment',
      desc: '',
      args: [],
    );
  }

  /// `Credit payback overdue`
  String get posCreditPaybackOverdue {
    return Intl.message(
      'Credit payback overdue',
      name: 'posCreditPaybackOverdue',
      desc: '',
      args: [],
    );
  }

  /// `You’ve payed {amount}\nto POS credit from {currency} account\n\nThanks for the early repayment!`
  String posCreditPaybackSuccessDescription(String amount, String currency) {
    return Intl.message(
      'You’ve payed $amount\nto POS credit from $currency account\n\nThanks for the early repayment!',
      name: 'posCreditPaybackSuccessDescription',
      desc: '',
      args: [amount, currency],
    );
  }

  /// `POS credit payment received`
  String get posCreditPaybackSuccessSubtitle {
    return Intl.message(
      'POS credit payment received',
      name: 'posCreditPaybackSuccessSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Payments on track`
  String get posCreditRepaymentsOnTrack {
    return Intl.message(
      'Payments on track',
      name: 'posCreditRepaymentsOnTrack',
      desc: '',
      args: [],
    );
  }

  /// `Tap to borrow`
  String get posCreditTapToBorrow {
    return Intl.message(
      'Tap to borrow',
      name: 'posCreditTapToBorrow',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get posDashboardAllTransactionTabTitle {
    return Intl.message(
      'All',
      name: 'posDashboardAllTransactionTabTitle',
      desc: '',
      args: [],
    );
  }

  /// `Available to borrow`
  String get posDashboardAvailableToBorrow {
    return Intl.message(
      'Available to borrow',
      name: 'posDashboardAvailableToBorrow',
      desc: '',
      args: [],
    );
  }

  /// `Borrow`
  String get posDashboardBorrowButtonText {
    return Intl.message(
      'Borrow',
      name: 'posDashboardBorrowButtonText',
      desc: '',
      args: [],
    );
  }

  /// `Manage`
  String get posDashboardManageButtonText {
    return Intl.message(
      'Manage',
      name: 'posDashboardManageButtonText',
      desc: '',
      args: [],
    );
  }

  /// `Payback`
  String get posDashboardPaybackButtonText {
    return Intl.message(
      'Payback',
      name: 'posDashboardPaybackButtonText',
      desc: '',
      args: [],
    );
  }

  /// `Repayments`
  String get posDashboardRepaymentsTransactionTabTitle {
    return Intl.message(
      'Repayments',
      name: 'posDashboardRepaymentsTransactionTabTitle',
      desc: '',
      args: [],
    );
  }

  /// `Wio line of credit`
  String get posDashboardTitle {
    return Intl.message(
      'Wio line of credit',
      name: 'posDashboardTitle',
      desc: '',
      args: [],
    );
  }

  /// `Borrowed {borrowedAmount} of {totalLoanAmount}`
  String posDashboardTitleBorrowed(
      String borrowedAmount, String totalLoanAmount) {
    return Intl.message(
      'Borrowed $borrowedAmount of $totalLoanAmount',
      name: 'posDashboardTitleBorrowed',
      desc: '',
      args: [borrowedAmount, totalLoanAmount],
    );
  }

  /// `Transactions`
  String get posDashboardTransactionSectionTitle {
    return Intl.message(
      'Transactions',
      name: 'posDashboardTransactionSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Withdrawals`
  String get posDashboardWithdrawalsTransactionTabTitle {
    return Intl.message(
      'Withdrawals',
      name: 'posDashboardWithdrawalsTransactionTabTitle',
      desc: '',
      args: [],
    );
  }

  /// `Email address`
  String get posHypothecationEmail {
    return Intl.message(
      'Email address',
      name: 'posHypothecationEmail',
      desc: '',
      args: [],
    );
  }

  /// `We calculate interest daily based on your outstanding principal balance. Your interest rate for {loanPeriod}-day period is {interestRate}%`
  String posInterestInfoBottomsheetDescription(
      String interestRate, String loanPeriod) {
    return Intl.message(
      'We calculate interest daily based on your outstanding principal balance. Your interest rate for $loanPeriod-day period is $interestRate%',
      name: 'posInterestInfoBottomsheetDescription',
      desc: '',
      args: [interestRate, loanPeriod],
    );
  }

  /// `If you repay before {loanPeriod} days, you'll only pay interest accrued up to that day.`
  String posInterestInfoBottomsheetTip(String loanPeriod) {
    return Intl.message(
      'If you repay before $loanPeriod days, you\'ll only pay interest accrued up to that day.',
      name: 'posInterestInfoBottomsheetTip',
      desc: '',
      args: [loanPeriod],
    );
  }

  /// `How the interest is calculated`
  String get posInterestInfoBottomsheetTitle {
    return Intl.message(
      'How the interest is calculated',
      name: 'posInterestInfoBottomsheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `The interest rate ranges from {min}% to {max}% per annum, depending on your credit history, credit bureau report & POS historical data.`
  String posLearnMoreFaq1Answer(String max, String min) {
    return Intl.message(
      'The interest rate ranges from $min% to $max% per annum, depending on your credit history, credit bureau report & POS historical data.',
      name: 'posLearnMoreFaq1Answer',
      desc: '',
      args: [max, min],
    );
  }

  /// `What is the interest rate on Wio Line of Credit?`
  String get posLearnMoreFaq1Question {
    return Intl.message(
      'What is the interest rate on Wio Line of Credit?',
      name: 'posLearnMoreFaq1Question',
      desc: '',
      args: [],
    );
  }

  /// `Interest is calculated daily on the outstanding principal amount. You will only pay interest on the amount of the limit you have used and for the exact number of days you have used it.`
  String get posLearnMoreFaq2Answer {
    return Intl.message(
      'Interest is calculated daily on the outstanding principal amount. You will only pay interest on the amount of the limit you have used and for the exact number of days you have used it.',
      name: 'posLearnMoreFaq2Answer',
      desc: '',
      args: [],
    );
  }

  /// `How interest is calculated?`
  String get posLearnMoreFaq2Question {
    return Intl.message(
      'How interest is calculated?',
      name: 'posLearnMoreFaq2Question',
      desc: '',
      args: [],
    );
  }

  /// `When you make a new drawdown, it is added to your existing outstanding amount. The updated total outstanding amount will be repaid over the next {days} days, starting from the date of the new withdrawal, through daily repayments. You can withdraw funds as needed, up to one year from the date your credit limit is sanctioned.`
  String posLearnMoreFaq3Answer(String days) {
    return Intl.message(
      'When you make a new drawdown, it is added to your existing outstanding amount. The updated total outstanding amount will be repaid over the next $days days, starting from the date of the new withdrawal, through daily repayments. You can withdraw funds as needed, up to one year from the date your credit limit is sanctioned.',
      name: 'posLearnMoreFaq3Answer',
      desc: '',
      args: [days],
    );
  }

  /// `How does Wio Line of Credit works?`
  String get posLearnMoreFaq3Question {
    return Intl.message(
      'How does Wio Line of Credit works?',
      name: 'posLearnMoreFaq3Question',
      desc: '',
      args: [],
    );
  }

  /// `Repayment will be made through daily equated installments, calculated based on the total outstanding amount at the time of withdrawal. These daily repayments will be automatically debited from your Wio Business account. Additionally, you can also repay your outstanding amount using the "Payback" option in your Wio Business app without any additional pre-payment charges.`
  String get posLearnMoreFaq4Answer {
    return Intl.message(
      'Repayment will be made through daily equated installments, calculated based on the total outstanding amount at the time of withdrawal. These daily repayments will be automatically debited from your Wio Business account. Additionally, you can also repay your outstanding amount using the "Payback" option in your Wio Business app without any additional pre-payment charges.',
      name: 'posLearnMoreFaq4Answer',
      desc: '',
      args: [],
    );
  }

  /// `How will I repay the utilized limit amount?`
  String get posLearnMoreFaq4Question {
    return Intl.message(
      'How will I repay the utilized limit amount?',
      name: 'posLearnMoreFaq4Question',
      desc: '',
      args: [],
    );
  }

  /// `Submit your application through the app. After approval, you can customize your credit limit and sign the contract. Finally, you’ll receive instructions to complete POS hypothecation with your POS provider to activate your Line of Credit.`
  String get posLearnMoreHowToApplyDescription {
    return Intl.message(
      'Submit your application through the app. After approval, you can customize your credit limit and sign the contract. Finally, you’ll receive instructions to complete POS hypothecation with your POS provider to activate your Line of Credit.',
      name: 'posLearnMoreHowToApplyDescription',
      desc: '',
      args: [],
    );
  }

  /// `How to apply`
  String get posLearnMoreHowToApplyTitle {
    return Intl.message(
      'How to apply',
      name: 'posLearnMoreHowToApplyTitle',
      desc: '',
      args: [],
    );
  }

  /// `Say goodbye to long-term loans and blocked funds for EMI payments `
  String get posLearnMorePageDescription {
    return Intl.message(
      'Say goodbye to long-term loans and blocked funds for EMI payments ',
      name: 'posLearnMorePageDescription',
      desc: '',
      args: [],
    );
  }

  /// `Interest is charged only on the amount of the loan you use, for the time you use it.`
  String get posLimitCondition1Subtitle {
    return Intl.message(
      'Interest is charged only on the amount of the loan you use, for the time you use it.',
      name: 'posLimitCondition1Subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Pay as you use`
  String get posLimitCondition1Title {
    return Intl.message(
      'Pay as you use',
      name: 'posLimitCondition1Title',
      desc: '',
      args: [],
    );
  }

  /// `Access funds anytime and repay within {days} days from fresh withdrawal date. Withdraw as needed, each withdrawal adds to total your outstanding balance.`
  String posLimitCondition2Subtitle(String days) {
    return Intl.message(
      'Access funds anytime and repay within $days days from fresh withdrawal date. Withdraw as needed, each withdrawal adds to total your outstanding balance.',
      name: 'posLimitCondition2Subtitle',
      desc: '',
      args: [days],
    );
  }

  /// `Seamless withdrawal`
  String get posLimitCondition2Title {
    return Intl.message(
      'Seamless withdrawal',
      name: 'posLimitCondition2Title',
      desc: '',
      args: [],
    );
  }

  /// `Your outstanding amount is repaid through equated daily payments from your Wio Business account & replenish your limit instantly for fresh withdrawal.`
  String get posLimitCondition3Subtitle {
    return Intl.message(
      'Your outstanding amount is repaid through equated daily payments from your Wio Business account & replenish your limit instantly for fresh withdrawal.',
      name: 'posLimitCondition3Subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Effortless repayment`
  String get posLimitCondition3Title {
    return Intl.message(
      'Effortless repayment',
      name: 'posLimitCondition3Title',
      desc: '',
      args: [],
    );
  }

  /// `One time application fee`
  String get posLimitOneTimeFeeTitle {
    return Intl.message(
      'One time application fee',
      name: 'posLimitOneTimeFeeTitle',
      desc: '',
      args: [],
    );
  }

  /// `The fee will be automatically deducted from your Wio Business account after you sign the borrowing agreement.`
  String get posLimitOneTimeSubtitle {
    return Intl.message(
      'The fee will be automatically deducted from your Wio Business account after you sign the borrowing agreement.',
      name: 'posLimitOneTimeSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `{loanTerm}-Day interest`
  String posLoanInterestLabel(String loanTerm) {
    return Intl.message(
      '$loanTerm-Day interest',
      name: 'posLoanInterestLabel',
      desc: '',
      args: [loanTerm],
    );
  }

  /// `Your loan payment due on {missedMilestoneDueDate} was missed, and as a result, your credit is currently locked. You are charged with late payment fees of {latePaymentFee}. To unlock your limit make the overdue payment now.`
  String posMilestoneOverdueBannerDescription(
      String latePaymentFee, String missedMilestoneDueDate) {
    return Intl.message(
      'Your loan payment due on $missedMilestoneDueDate was missed, and as a result, your credit is currently locked. You are charged with late payment fees of $latePaymentFee. To unlock your limit make the overdue payment now.',
      name: 'posMilestoneOverdueBannerDescription',
      desc: '',
      args: [latePaymentFee, missedMilestoneDueDate],
    );
  }

  /// `The minimum amount to borrow is {minimumBorrowAmount}`
  String posMinimumBorrowAmountMessage(String minimumBorrowAmount) {
    return Intl.message(
      'The minimum amount to borrow is $minimumBorrowAmount',
      name: 'posMinimumBorrowAmountMessage',
      desc: '',
      args: [minimumBorrowAmount],
    );
  }

  /// `Your loan payment due on {missedMilestoneDueDate} was missed, and as a result, your credit is currently locked. To avoid any additional charges, please make the overdue payment by {dateToAvoidAdditionalCharges}.`
  String posMissedMilestoneBannerDescription(
      String dateToAvoidAdditionalCharges, String missedMilestoneDueDate) {
    return Intl.message(
      'Your loan payment due on $missedMilestoneDueDate was missed, and as a result, your credit is currently locked. To avoid any additional charges, please make the overdue payment by $dateToAvoidAdditionalCharges.',
      name: 'posMissedMilestoneBannerDescription',
      desc: '',
      args: [dateToAvoidAdditionalCharges, missedMilestoneDueDate],
    );
  }

  /// `This amount will be deducted daily from your Wio Business account.`
  String get posNextDailyPaymentBottomSheetDesc {
    return Intl.message(
      'This amount will be deducted daily from your Wio Business account.',
      name: 'posNextDailyPaymentBottomSheetDesc',
      desc: '',
      args: [],
    );
  }

  /// `If you skip five consecutive daily payments. On the sixth day, your credit will be locked.`
  String get posNextDailyPaymentBottomSheetLockInfoBanner {
    return Intl.message(
      'If you skip five consecutive daily payments. On the sixth day, your credit will be locked.',
      name: 'posNextDailyPaymentBottomSheetLockInfoBanner',
      desc: '',
      args: [],
    );
  }

  /// `The amount will be carried over to your next daily payment. To avoid credit lock please payback your missed daily payment until {date}`
  String posNextDailyPaymentBottomSheetMissedPaymentBannerDesc(String date) {
    return Intl.message(
      'The amount will be carried over to your next daily payment. To avoid credit lock please payback your missed daily payment until $date',
      name: 'posNextDailyPaymentBottomSheetMissedPaymentBannerDesc',
      desc: '',
      args: [date],
    );
  }

  /// `{missedPaymentCount, plural, =1{You have missed 1 daily payment} other{You have missed {missedPaymentCount} daily payments}}`
  String posNextDailyPaymentBottomSheetMissedPaymentWarning(
      int missedPaymentCount) {
    return Intl.plural(
      missedPaymentCount,
      one: 'You have missed 1 daily payment',
      other: 'You have missed $missedPaymentCount daily payments',
      name: 'posNextDailyPaymentBottomSheetMissedPaymentWarning',
      desc: '',
      args: [missedPaymentCount],
    );
  }

  /// `Payback now`
  String get posNextDailyPaymentBottomSheetPaybackCta {
    return Intl.message(
      'Payback now',
      name: 'posNextDailyPaymentBottomSheetPaybackCta',
      desc: '',
      args: [],
    );
  }

  /// `Next daily payment`
  String get posNextDailyPaymentBottomSheetTitle {
    return Intl.message(
      'Next daily payment',
      name: 'posNextDailyPaymentBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Congrats! You’ve paid off your loan, principal plus interest. Tap 'Borrow' to withdraw more funds.`
  String get posPaidAllDebtBannerContent {
    return Intl.message(
      'Congrats! You’ve paid off your loan, principal plus interest. Tap \'Borrow\' to withdraw more funds.',
      name: 'posPaidAllDebtBannerContent',
      desc: '',
      args: [],
    );
  }

  /// `Got it, Next`
  String get posThingsToKnowContinueCta {
    return Intl.message(
      'Got it, Next',
      name: 'posThingsToKnowContinueCta',
      desc: '',
      args: [],
    );
  }

  /// `Borrow as often as you like, starting from {minAmount}, up to your max limit. `
  String posThingsToKnowFeature1(String minAmount) {
    return Intl.message(
      'Borrow as often as you like, starting from $minAmount, up to your max limit. ',
      name: 'posThingsToKnowFeature1',
      desc: '',
      args: [minAmount],
    );
  }

  /// `Repay with daily automatic payments from your main AED account. `
  String get posThingsToKnowFeature2 {
    return Intl.message(
      'Repay with daily automatic payments from your main AED account. ',
      name: 'posThingsToKnowFeature2',
      desc: '',
      args: [],
    );
  }

  /// `The loan term is {loanTerm} days with minimum amount to be repaid every month in 3 milestones.`
  String posThingsToKnowFeature3(String loanTerm) {
    return Intl.message(
      'The loan term is $loanTerm days with minimum amount to be repaid every month in 3 milestones.',
      name: 'posThingsToKnowFeature3',
      desc: '',
      args: [loanTerm],
    );
  }

  /// `Interest applies daily, so if you repay before {loanTerm} days, you'll only pay the interest up to that day.`
  String posThingsToKnowFeature4(String loanTerm) {
    return Intl.message(
      'Interest applies daily, so if you repay before $loanTerm days, you\'ll only pay the interest up to that day.',
      name: 'posThingsToKnowFeature4',
      desc: '',
      args: [loanTerm],
    );
  }

  /// `Your outstanding amount is repaid through equated daily payments from your Wio Business account & replenish your limit instantly for fresh withdrawal.`
  String get posThingsToKnowFeatureFour {
    return Intl.message(
      'Your outstanding amount is repaid through equated daily payments from your Wio Business account & replenish your limit instantly for fresh withdrawal.',
      name: 'posThingsToKnowFeatureFour',
      desc: '',
      args: [],
    );
  }

  /// `The interest rate on your Line of Credit is up to 22% per annum.`
  String get posThingsToKnowFeatureOne {
    return Intl.message(
      'The interest rate on your Line of Credit is up to 22% per annum.',
      name: 'posThingsToKnowFeatureOne',
      desc: '',
      args: [],
    );
  }

  /// `Access funds anytime and repay within 90 days from fresh withdrawal date. Withdraw as needed, each withdrawal adds to total your outstanding balance.`
  String get posThingsToKnowFeatureThree {
    return Intl.message(
      'Access funds anytime and repay within 90 days from fresh withdrawal date. Withdraw as needed, each withdrawal adds to total your outstanding balance.',
      name: 'posThingsToKnowFeatureThree',
      desc: '',
      args: [],
    );
  }

  /// `The line of credit facility will be available for a 365-day period, starting from the date the borrowing agreement is signed.`
  String get posThingsToKnowFeatureTwo {
    return Intl.message(
      'The line of credit facility will be available for a 365-day period, starting from the date the borrowing agreement is signed.',
      name: 'posThingsToKnowFeatureTwo',
      desc: '',
      args: [],
    );
  }

  /// `Things to know before you start`
  String get posThingsToKnowTitle {
    return Intl.message(
      'Things to know before you start',
      name: 'posThingsToKnowTitle',
      desc: '',
      args: [],
    );
  }

  /// `This is the amount you currently owe as of today.`
  String get posTotalOutstandingAmountBottomSheetDesc {
    return Intl.message(
      'This is the amount you currently owe as of today.',
      name: 'posTotalOutstandingAmountBottomSheetDesc',
      desc: '',
      args: [],
    );
  }

  /// `Current outstanding`
  String get posTotalOutstandingAmountBottomSheetTitle {
    return Intl.message(
      'Current outstanding',
      name: 'posTotalOutstandingAmountBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Pull down to refresh`
  String get pullDownRefreshLabel {
    return Intl.message(
      'Pull down to refresh',
      name: 'pullDownRefreshLabel',
      desc: '',
      args: [],
    );
  }

  /// `What is the reason for not reporting VAT?`
  String get reasonNotReportingVatQuestion {
    return Intl.message(
      'What is the reason for not reporting VAT?',
      name: 'reasonNotReportingVatQuestion',
      desc: '',
      args: [],
    );
  }

  /// `Refreshing...`
  String get refreshingLabel {
    return Intl.message(
      'Refreshing...',
      name: 'refreshingLabel',
      desc: '',
      args: [],
    );
  }

  /// `Unfortunately, your application for Wio Business Credit has been declined. Your company doesn't meet all the required criteria. You can try again in three months.`
  String get rejectedBannerDescription {
    return Intl.message(
      'Unfortunately, your application for Wio Business Credit has been declined. Your company doesn\'t meet all the required criteria. You can try again in three months.',
      name: 'rejectedBannerDescription',
      desc: '',
      args: [],
    );
  }

  /// `Release to refresh`
  String get releaseToRefreshLabel {
    return Intl.message(
      'Release to refresh',
      name: 'releaseToRefreshLabel',
      desc: '',
      args: [],
    );
  }

  /// `Borrowed amount`
  String get repaymentDailyBorrowedAmount {
    return Intl.message(
      'Borrowed amount',
      name: 'repaymentDailyBorrowedAmount',
      desc: '',
      args: [],
    );
  }

  /// `Daily payments will be deducted from your Wio Business account.`
  String get repaymentDailyPaymentInfo {
    return Intl.message(
      'Daily payments will be deducted from your Wio Business account.',
      name: 'repaymentDailyPaymentInfo',
      desc: '',
      args: [],
    );
  }

  /// `Already borrowed`
  String get repaymentDetailsAlreadyBorrowed {
    return Intl.message(
      'Already borrowed',
      name: 'repaymentDetailsAlreadyBorrowed',
      desc: '',
      args: [],
    );
  }

  /// `Daily payment`
  String get repaymentDetailsDailyPayment {
    return Intl.message(
      'Daily payment',
      name: 'repaymentDetailsDailyPayment',
      desc: '',
      args: [],
    );
  }

  /// `Due on {date}`
  String repaymentDetailsDueOn(String date) {
    return Intl.message(
      'Due on $date',
      name: 'repaymentDetailsDueOn',
      desc: '',
      args: [date],
    );
  }

  /// `Interest`
  String get repaymentDetailsInterests {
    return Intl.message(
      'Interest',
      name: 'repaymentDetailsInterests',
      desc: '',
      args: [],
    );
  }

  /// `Next daily payment`
  String get repaymentDetailsNextDailyPayment {
    return Intl.message(
      'Next daily payment',
      name: 'repaymentDetailsNextDailyPayment',
      desc: '',
      args: [],
    );
  }

  /// `Paid`
  String get repaymentDetailsPaidLabel {
    return Intl.message(
      'Paid',
      name: 'repaymentDetailsPaidLabel',
      desc: '',
      args: [],
    );
  }

  /// `Principal`
  String get repaymentDetailsPrincipal {
    return Intl.message(
      'Principal',
      name: 'repaymentDetailsPrincipal',
      desc: '',
      args: [],
    );
  }

  /// `Total loan amount`
  String get repaymentDetailsTotalLoanAmount {
    return Intl.message(
      'Total loan amount',
      name: 'repaymentDetailsTotalLoanAmount',
      desc: '',
      args: [],
    );
  }

  /// `View details`
  String get repaymentDetailsViewDetails {
    return Intl.message(
      'View details',
      name: 'repaymentDetailsViewDetails',
      desc: '',
      args: [],
    );
  }

  /// `Review`
  String get reviewCta {
    return Intl.message(
      'Review',
      name: 'reviewCta',
      desc: '',
      args: [],
    );
  }

  /// `Now let’s review your credit limit and set your repayment preferences.`
  String get reviewYourCreditLimit {
    return Intl.message(
      'Now let’s review your credit limit and set your repayment preferences.',
      name: 'reviewYourCreditLimit',
      desc: '',
      args: [],
    );
  }

  /// `Borrow up to {maxLimit}`
  String securedBusinessLoanCardFeature1(String maxLimit) {
    return Intl.message(
      'Borrow up to $maxLimit',
      name: 'securedBusinessLoanCardFeature1',
      desc: '',
      args: [maxLimit],
    );
  }

  /// `Interest {minInterest}-{maxInterest}%`
  String securedBusinessLoanCardFeature2(
      String maxInterest, String minInterest) {
    return Intl.message(
      'Interest $minInterest-$maxInterest%',
      name: 'securedBusinessLoanCardFeature2',
      desc: '',
      args: [maxInterest, minInterest],
    );
  }

  /// `Assignment letter from acquirer`
  String get securedBusinessLoanCardFeature3 {
    return Intl.message(
      'Assignment letter from acquirer',
      name: 'securedBusinessLoanCardFeature3',
      desc: '',
      args: [],
    );
  }

  /// `For businesses with POS or online payments settled to Wio account.`
  String get securedBusinessLoanCardSubtitle {
    return Intl.message(
      'For businesses with POS or online payments settled to Wio account.',
      name: 'securedBusinessLoanCardSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Sales backed loan`
  String get securedBusinessLoanCardTitle {
    return Intl.message(
      'Sales backed loan',
      name: 'securedBusinessLoanCardTitle',
      desc: '',
      args: [],
    );
  }

  /// `Sales backed loan`
  String get securedBusinessLoanLabel {
    return Intl.message(
      'Sales backed loan',
      name: 'securedBusinessLoanLabel',
      desc: '',
      args: [],
    );
  }

  /// `To start using your business loan, follow the instruction sent to {email} to complete your settlement assignment process.`
  String securedLoanAssignementLetterDescription(String email) {
    return Intl.message(
      'To start using your business loan, follow the instruction sent to $email to complete your settlement assignment process.',
      name: 'securedLoanAssignementLetterDescription',
      desc: '',
      args: [email],
    );
  }

  /// `Get the assignment letter `
  String get securedLoanAssignementLetterStep1 {
    return Intl.message(
      'Get the assignment letter ',
      name: 'securedLoanAssignementLetterStep1',
      desc: '',
      args: [],
    );
  }

  /// `Reach out to your payment provider for an assignment letter confirming that payments are settled into your Wio account.`
  String get securedLoanAssignementLetterStep1Desc {
    return Intl.message(
      'Reach out to your payment provider for an assignment letter confirming that payments are settled into your Wio account.',
      name: 'securedLoanAssignementLetterStep1Desc',
      desc: '',
      args: [],
    );
  }

  /// `Share the letter with us`
  String get securedLoanAssignementLetterStep2 {
    return Intl.message(
      'Share the letter with us',
      name: 'securedLoanAssignementLetterStep2',
      desc: '',
      args: [],
    );
  }

  /// `Follow the detailed instructions sent to your email address on how to submit the assignment letter to Wio`
  String get securedLoanAssignementLetterStep2Desc {
    return Intl.message(
      'Follow the detailed instructions sent to your email address on how to submit the assignment letter to Wio',
      name: 'securedLoanAssignementLetterStep2Desc',
      desc: '',
      args: [],
    );
  }

  /// `Almost there`
  String get securedLoanAssignementLetterTitle {
    return Intl.message(
      'Almost there',
      name: 'securedLoanAssignementLetterTitle',
      desc: '',
      args: [],
    );
  }

  /// `Share assignment letter`
  String get securedLoanAssignmentLetterBannerTitle {
    return Intl.message(
      'Share assignment letter',
      name: 'securedLoanAssignmentLetterBannerTitle',
      desc: '',
      args: [],
    );
  }

  /// `A valid assignment letter will be required to receive the loan and have funds credited to your account. If you can’t request one from your payment provider, select “Cancel” to continue with a simple loan.`
  String get securedLoanConfirmationDescription {
    return Intl.message(
      'A valid assignment letter will be required to receive the loan and have funds credited to your account. If you can’t request one from your payment provider, select “Cancel” to continue with a simple loan.',
      name: 'securedLoanConfirmationDescription',
      desc: '',
      args: [],
    );
  }

  /// `Confirm you can provide an assignment letter from your payment provider`
  String get securedLoanConfirmationTitle {
    return Intl.message(
      'Confirm you can provide an assignment letter from your payment provider',
      name: 'securedLoanConfirmationTitle',
      desc: '',
      args: [],
    );
  }

  /// `Here’s how to get a sales backed loan`
  String get securedLoanPageTitle {
    return Intl.message(
      'Here’s how to get a sales backed loan',
      name: 'securedLoanPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `For businesses with POS or online payments settled to Wio account.`
  String get securedLoanSelectionDescription {
    return Intl.message(
      'For businesses with POS or online payments settled to Wio account.',
      name: 'securedLoanSelectionDescription',
      desc: '',
      args: [],
    );
  }

  /// `Sales backed loan`
  String get securedLoanSelectionTitle {
    return Intl.message(
      'Sales backed loan',
      name: 'securedLoanSelectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Share your latest VAT statements (if applicable), company turnover, and bank account IBANs.`
  String get securedLoanStep1Description {
    return Intl.message(
      'Share your latest VAT statements (if applicable), company turnover, and bank account IBANs.',
      name: 'securedLoanStep1Description',
      desc: '',
      args: [],
    );
  }

  /// `Submit the application`
  String get securedLoanStep1Title {
    return Intl.message(
      'Submit the application',
      name: 'securedLoanStep1Title',
      desc: '',
      args: [],
    );
  }

  /// `Wio will review your application and get back to you with an offer for your maximum loan limit.`
  String get securedLoanStep2Description {
    return Intl.message(
      'Wio will review your application and get back to you with an offer for your maximum loan limit.',
      name: 'securedLoanStep2Description',
      desc: '',
      args: [],
    );
  }

  /// `Receive a loan offer from Wio and select your borrowing amount`
  String get securedLoanStep2Title {
    return Intl.message(
      'Receive a loan offer from Wio and select your borrowing amount',
      name: 'securedLoanStep2Title',
      desc: '',
      args: [],
    );
  }

  /// `To receive the funds, you’ll need to provide an assignment letter from your acquirer confirming that payments are settled into your Wio account.`
  String get securedLoanStep3Description {
    return Intl.message(
      'To receive the funds, you’ll need to provide an assignment letter from your acquirer confirming that payments are settled into your Wio account.',
      name: 'securedLoanStep3Description',
      desc: '',
      args: [],
    );
  }

  /// `Share a valid assignment letter to receive the funds`
  String get securedLoanStep3Title {
    return Intl.message(
      'Share a valid assignment letter to receive the funds',
      name: 'securedLoanStep3Title',
      desc: '',
      args: [],
    );
  }

  /// `What is the assignment letter?`
  String get securedLoanWhatIsAssignmentLetter {
    return Intl.message(
      'What is the assignment letter?',
      name: 'securedLoanWhatIsAssignmentLetter',
      desc: '',
      args: [],
    );
  }

  /// `Select file`
  String get selectFileBorrowingPowerTitle {
    return Intl.message(
      'Select file',
      name: 'selectFileBorrowingPowerTitle',
      desc: '',
      args: [],
    );
  }

  /// `Select Audited Financial Statement`
  String get selectFileFinancialStatementTitle {
    return Intl.message(
      'Select Audited Financial Statement',
      name: 'selectFileFinancialStatementTitle',
      desc: '',
      args: [],
    );
  }

  /// `Borrow up to {maxLimit}`
  String simpleBusinessLoanCardFeature1(String maxLimit) {
    return Intl.message(
      'Borrow up to $maxLimit',
      name: 'simpleBusinessLoanCardFeature1',
      desc: '',
      args: [maxLimit],
    );
  }

  /// `Interest {minInterest}-{maxInterest}%`
  String simpleBusinessLoanCardFeature2(
      String maxInterest, String minInterest) {
    return Intl.message(
      'Interest $minInterest-$maxInterest%',
      name: 'simpleBusinessLoanCardFeature2',
      desc: '',
      args: [maxInterest, minInterest],
    );
  }

  /// `No collateral required`
  String get simpleBusinessLoanCardFeature3 {
    return Intl.message(
      'No collateral required',
      name: 'simpleBusinessLoanCardFeature3',
      desc: '',
      args: [],
    );
  }

  /// `No extra guarantees, every type of business can apply.`
  String get simpleBusinessLoanCardSubtitle {
    return Intl.message(
      'No extra guarantees, every type of business can apply.',
      name: 'simpleBusinessLoanCardSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Simple business loan`
  String get simpleBusinessLoanCardTitle {
    return Intl.message(
      'Simple business loan',
      name: 'simpleBusinessLoanCardTitle',
      desc: '',
      args: [],
    );
  }

  /// `Start date`
  String get startDateLabel {
    return Intl.message(
      'Start date',
      name: 'startDateLabel',
      desc: '',
      args: [],
    );
  }

  /// `Time period`
  String get timePeriodLabel {
    return Intl.message(
      'Time period',
      name: 'timePeriodLabel',
      desc: '',
      args: [],
    );
  }

  /// `Current outstanding`
  String get totalOutstandingLabel {
    return Intl.message(
      'Current outstanding',
      name: 'totalOutstandingLabel',
      desc: '',
      args: [],
    );
  }

  /// `Simple loan`
  String get unSecuredBusinessLoanLabel {
    return Intl.message(
      'Simple loan',
      name: 'unSecuredBusinessLoanLabel',
      desc: '',
      args: [],
    );
  }

  /// `No extra guarantees required every type of business can apply.`
  String get unsecuredLoanSelectionDescription {
    return Intl.message(
      'No extra guarantees required every type of business can apply.',
      name: 'unsecuredLoanSelectionDescription',
      desc: '',
      args: [],
    );
  }

  /// `Simple loan`
  String get unsecuredLoanSelectionTitle {
    return Intl.message(
      'Simple loan',
      name: 'unsecuredLoanSelectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Got it`
  String get updateCreditLimitRequestMoreSuccessButton {
    return Intl.message(
      'Got it',
      name: 'updateCreditLimitRequestMoreSuccessButton',
      desc: '',
      args: [],
    );
  }

  /// `VAT statement`
  String get vatStatementSampleTitle {
    return Intl.message(
      'VAT statement',
      name: 'vatStatementSampleTitle',
      desc: '',
      args: [],
    );
  }

  /// `View offer`
  String get viewOfferLabel {
    return Intl.message(
      'View offer',
      name: 'viewOfferLabel',
      desc: '',
      args: [],
    );
  }

  /// `Wio Business loan`
  String get wioBusinessLoanLabel {
    return Intl.message(
      'Wio Business loan',
      name: 'wioBusinessLoanLabel',
      desc: '',
      args: [],
    );
  }

  /// `Wio line of credit`
  String get wioLineOfCreditLabel {
    return Intl.message(
      'Wio line of credit',
      name: 'wioLineOfCreditLabel',
      desc: '',
      args: [],
    );
  }
}

class _AppLocalizationDelegate
    extends LocalizationsDelegate<CreditLocalizations> {
  const _AppLocalizationDelegate();

  @override
  bool isSupported(Locale locale) => CreditLocalizations.supportedLocales.any(
      (supportedLocale) => supportedLocale.languageCode == locale.languageCode);

  @override
  Future<CreditLocalizations> load(Locale locale) =>
      CreditLocalizations.load(locale);

  @override
  bool shouldReload(_AppLocalizationDelegate old) => false;
}
