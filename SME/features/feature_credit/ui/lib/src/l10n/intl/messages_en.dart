// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.
// @dart=2.12
// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = MessageLookup();

typedef String? MessageIfAbsent(String? messageStr, List<Object>? args);

class MessageLookup extends MessageLookupByLibrary {
  @override
  String get localeName => 'en';

  static m0(amount) =>
      "There is an outstanding amount of ${amount} on your account. You must repay the full amount to close your credit.";

  static m1(amount) =>
      "The amount ${amount} is pending. Wait for the transactions to complete and try again later.";

  static m2(date) => "Ended at ${date}";

  static m3(feePercentage) => "Your ${feePercentage}% fee amount as of today";

  static m4(feeFreeMonthCount) =>
      "If you\'re still within the ${feeFreeMonthCount} months free period, you won\'t be charged.";

  static m5(repaymentPercentage) => "${repaymentPercentage}% of spent money";

  static m6(date) => "Till ${date}";

  static m7(date) =>
      "You might keep spending before your autopay date. Your final autopay amount will be calculated on: ${date}.";

  static m8(companyName) =>
      "Do you have the borrowing power for ${companyName}";

  static m9(companyName) =>
      "Confirm you have the borrowing power for ${companyName}";

  static m10(monthCount) => "${monthCount} months";

  static m11(userName) => "Hi ${userName}! Here’s your loan offer";

  static m12(interest) => "Total interest (${interest}% p.a.)";

  static m13(processingFee) =>
      "Your Wio Account balance is too low. Please make sure you have at least ${processingFee} to cover the one-time processing fee and continue with your loan.";

  static m14(maxRate, minRate) =>
      "An interest will be applied at an annual interest rate from ${minRate}% to ${maxRate}%. ";

  static m15(lateFeeGracePeriodInDays, latePaymentFee) =>
      "A late payment fee of ${latePaymentFee} will be applied if payment is not made within ${lateFeeGracePeriodInDays} days after your due date.";

  static m16(posPartnerName) => "Details transmitted to ${posPartnerName}";

  static m17(email) =>
      "To start using your credit line, follow the instruction sent to ${email} to complete the hypothecation of your POS.";

  static m18(minimumRepaymentAmout) =>
      "Your credit has been locked because you\nhaven\'t paid the minimum amount due. To\nunlock please pay minimum of ${minimumRepaymentAmout}.";

  static m19(day) => "${day} of every month";

  static m20(monthCount) => "${monthCount} months";

  static m21(feePercentage) =>
      "Your fee is determined by applying ${feePercentage}% to the carry-over amount, which is your current outstanding balance after repayment.";

  static m22(date) => "No Fee till ${date}";

  static m23(amount) => "*Minimum payment to avoid late fees: ${amount}";

  static m24(date) => "Your next autopay is on ${date}";

  static m25(creditLoanAmount) => "of ${creditLoanAmount}";

  static m26(amount) => "Your new Credit limit is ${amount}";

  static m27(date) => "Next autopay on ${date}";

  static m28(date) => "Due date ${date}";

  static m29(amount) => "We took ${amount}";

  static m30(date) => "Your last autopay, ${date}";

  static m31(amount, currency) =>
      "We took an amount closest to your payment of ${amount} because there wasn\'t enough money in your ${currency} account on the autopay date.";

  static m32(amount) => "+${amount} fee";

  static m33(date) =>
      "Your fee-free period has expired on ${date}. You can resume the fee-free period for up to 60 days each time you repay your outstanding balance in full.";

  static m34(feePercentage, minumumPayPercentage, feeFreeMonthCount, penalty) =>
      "No annual fee - Wio Credit is included in your pricing plan.\n\nNo monthly carry-over fee if you pay full outstanding amount each month without any carrying over any balance to the next month.\n\n${feePercentage}% monthly carry-over fee applies if you pay at least ${minumumPayPercentage}% of your outstanding balance. This fee is calculated as a fixed percentage of your carry-over amount, which is your current outstanding balance after repayment.\n\nPay off one month\'s balance entirely and enjoy up to ${feeFreeMonthCount} months fee-free period.\n\n${penalty} Penalty fee apply when you fail to repay at least ${minumumPayPercentage}% of your outstanding balance within 6 days after repayment deadline.";

  static m35(minumumPayPercentage, feeFreeMonthCount) =>
      "1. Pay as low as ${minumumPayPercentage}% of your outstanding balance and carry over the remaining amount to the next period without incurring any fees.\n\n2. You can carry over your balance without any fees for two consecutive billing cycles, and fees will only be charged starting from the third cycle. This allows you to enjoy up to ${feeFreeMonthCount} months of fee-free credit!\n\n3. After your fee-free period ends, it will restart each time you repay your outstanding balance in full.\n\nTake a look at this practical example:\nIf your repayment date is the 1st of each month and you spend AED 1,000 on the 2nd of June, you need to repay only the minimum (${minumumPayPercentage}% of AED 1,000)  To avoid fees, the full amount must be repaid  by the 1st of August. This gives you June and July as your fee-free period.";

  static m36(feePercentage) =>
      "Enjoy ${feePercentage}% fee on your outstanding balance, and no fees if you repay your spends in full.";

  static m37(feePercentage) =>
      "- Use with any of your cards physical or virtual.\n\n- Choose your repayment date and amount with custom autopay option.\n\n- Your end of month statements includes your spends no complex statement periods.\n\n- Wio Credit is free and you pay only a flat ${feePercentage}% fee on your carryover amount.";

  static m38(minumumPayPercentage, feeFreeMonthCount) =>
      "Pay as low as ${minumumPayPercentage}% of your outstanding amount and enjoy up to ${feeFreeMonthCount} months without fees.";

  static m39(feeFreeMonthCount) =>
      "Get up to ${feeFreeMonthCount} months fee-free credit";

  static m40(feeFreeMonthCount, minumumPaymentPercentage) =>
      "Pay as low as ${minumumPaymentPercentage}% of your outstanding amount and enjoy up to ${feeFreeMonthCount} months without fees.";

  static m41(feeFreeMonthCount) =>
      "Get up to ${feeFreeMonthCount} months fee-free credit";

  static m42(feePercentage) =>
      "Enjoy ${feePercentage}% fee on your outstanding balance, and no fees if you repay your spends in full.";

  static m43(date) => "Total outstanding as of\n${date} (incl. fees):";

  static m44(amount) =>
      "You are charged with late payment fees of ${amount}. To unlock your credit make the overdue payment now.";

  static m45(date) =>
      "To avoid any additional charges, please make the overdue payment by ${date}.";

  static m46(percentage) => "${percentage}% of spent money";

  static m47(date) => "${date} of every month";

  static m48(fee, payback, spent) =>
      "If you spend ${spent} and you pay back ${payback}, you\'ll be charged ${fee}";

  static m49(fee, spent) =>
      "If you spend ${spent}, you\'ll pay back ${fee} without fees.";

  static m50(percentage) => "${percentage}% of spent money";

  static m51(supportedFileSizeInMb, supportedFileTypes) =>
      "${supportedFileTypes} max ${supportedFileSizeInMb}MB";

  static m52(annualInterest) =>
      "A carry-over fee (interest) will be applied at an annual interest rate of up to ${annualInterest}%.";

  static m53(minThresholdPercentage) =>
      "You can waive the carryover fee for two months in a row by paying at least ${minThresholdPercentage}% of the balance.";

  static m54(amount) => "Do you want to request for more than ${amount}?";

  static m55(amount) =>
      "Your limit has been updated successfully. Your new Credit limit is ${amount}";

  static m56(nextStatement, required) =>
      "Upload statement ${nextStatement} of ${required}";

  static m57(required, x) => "${x} of ${required} statements uploaded";

  static m58(amount) =>
      "You’re eligible to get up to ${amount} as Quick cash. The money will be transferred to your AED account immediately.";

  static m59(date) => "Due on ${date}";

  static m60(repaymentPercentage) => "${repaymentPercentage}% autopay amount:";

  static m61(date) =>
      "Full fee will apply on your carry-over amount after ${date}. Your fee-free period will resume each time you repay your outstanding balance in full.";

  static m62(size) =>
      "Looks like the file is too large. The max file size is ${size} MB.";

  static m63(holdAmount) =>
      "The amount of ${holdAmount} is pending. It can be paid back once transactions are completed. Learn more";

  static m64(day) => "${day} of every month";

  static m65(date, month) => "Your next autopay on ${date} of ${month}";

  static m66(percentage) => "${percentage} of used money";

  static m67(amount, fee) => "${amount} and ${fee} fee as of today";

  static m68(amount) => "${amount} as of today";

  static m69(creditLimit, spent) => "Spent ${spent} of ${creditLimit}";

  static m70(date) => "Next autopayment is on ${date}";

  static m71(amount) => "Balance: ${amount}";

  static m72(currency) => "${currency} account";

  static m73(amount) => "The amount cannot be less than the minimum: ${amount}";

  static m74(amount) => "The amount exceeds full amount: ${amount}";

  static m75(amount) => "Full amount: ${amount}";

  static m76(amount) => "The amount exceeds your balance: ${amount}";

  static m77(amount) => "Minimum: ${amount}";

  static m78(amount, currency) =>
      "You’ve payed ${amount}\nto Credit from ${currency} account";

  static m79(balance) => "Available to borrow ${balance}";

  static m80(preBorrowDailyPaymentAmount) =>
      "New daily payment amount will apply from tomorrow. Today you will still pay ${preBorrowDailyPaymentAmount}.";

  static m81(loanTerm) =>
      "This amount will be added in your current outstanding balance as of today, with repayment due in the next ${loanTerm} days.";

  static m82(currency) => "Money transferred to  ${currency} account";

  static m83(date) => "Overdue amount as of\n${date}";

  static m84(date) => "Total due on ${date}";

  static m85(email) =>
      "The non objection certificate for the POS release have been sent to ${email}";

  static m86(amount, currency) =>
      "You’ve payed ${amount}\nto POS credit from ${currency} account\n\nThanks for the early repayment!";

  static m87(borrowedAmount, totalLoanAmount) =>
      "Borrowed ${borrowedAmount} of ${totalLoanAmount}";

  static m88(interestRate, loanPeriod) =>
      "We calculate interest daily based on your outstanding principal balance. Your interest rate for ${loanPeriod}-day period is ${interestRate}%";

  static m89(loanPeriod) =>
      "If you repay before ${loanPeriod} days, you\'ll only pay interest accrued up to that day.";

  static m90(max, min) =>
      "The interest rate ranges from ${min}% to ${max}% per annum, depending on your credit history, credit bureau report & POS historical data.";

  static m91(days) =>
      "When you make a new drawdown, it is added to your existing outstanding amount. The updated total outstanding amount will be repaid over the next ${days} days, starting from the date of the new withdrawal, through daily repayments. You can withdraw funds as needed, up to one year from the date your credit limit is sanctioned.";

  static m92(days) =>
      "Access funds anytime and repay within ${days} days from fresh withdrawal date. Withdraw as needed, each withdrawal adds to total your outstanding balance.";

  static m93(loanTerm) => "${loanTerm}-Day interest";

  static m94(latePaymentFee, missedMilestoneDueDate) =>
      "Your loan payment due on ${missedMilestoneDueDate} was missed, and as a result, your credit is currently locked. You are charged with late payment fees of ${latePaymentFee}. To unlock your limit make the overdue payment now.";

  static m95(minimumBorrowAmount) =>
      "The minimum amount to borrow is ${minimumBorrowAmount}";

  static m96(dateToAvoidAdditionalCharges, missedMilestoneDueDate) =>
      "Your loan payment due on ${missedMilestoneDueDate} was missed, and as a result, your credit is currently locked. To avoid any additional charges, please make the overdue payment by ${dateToAvoidAdditionalCharges}.";

  static m97(date) =>
      "The amount will be carried over to your next daily payment. To avoid credit lock please payback your missed daily payment until ${date}";

  static m98(missedPaymentCount) =>
      "${Intl.plural(missedPaymentCount, one: 'You have missed 1 daily payment', other: 'You have missed ${missedPaymentCount} daily payments')}";

  static m99(minAmount) =>
      "Borrow as often as you like, starting from ${minAmount}, up to your max limit. ";

  static m100(loanTerm) =>
      "The loan term is ${loanTerm} days with minimum amount to be repaid every month in 3 milestones.";

  static m101(loanTerm) =>
      "Interest applies daily, so if you repay before ${loanTerm} days, you\'ll only pay the interest up to that day.";

  static m102(date) => "Due on ${date}";

  static m103(maxLimit) => "Borrow up to ${maxLimit}";

  static m104(maxInterest, minInterest) =>
      "Interest ${minInterest}-${maxInterest}%";

  static m105(email) =>
      "To start using your business loan, follow the instruction sent to ${email} to complete your settlement assignment process.";

  static m106(maxLimit) => "Borrow up to ${maxLimit}";

  static m107(maxInterest, minInterest) =>
      "Interest ${minInterest}-${maxInterest}%";

  @override
  final Map<String, dynamic> messages =
      _notInlinedMessages(_notInlinedMessages);

  static Map<String, dynamic> _notInlinedMessages(_) => {
        'accountClosureConfirmationCancel':
            MessageLookupByLibrary.simpleMessage('Keep credit account'),
        'accountClosureConfirmationConfirm':
            MessageLookupByLibrary.simpleMessage('Close credit account'),
        'accountClosureConfirmationDescription':
            MessageLookupByLibrary.simpleMessage(
                'After you close your account all your cards will be switched to “My Money” automatically. You may apply for a new credit account later.'),
        'accountClosureConfirmationTitle': MessageLookupByLibrary.simpleMessage(
            'Are you sure you want to close your credit account?'),
        'accountClosureOutstandingRepaymentCta':
            MessageLookupByLibrary.simpleMessage('Repay full'),
        'accountClosureOutstandingRepaymentDescription': m0,
        'accountClosureOutstandingRepaymentTitle':
            MessageLookupByLibrary.simpleMessage('Outstanding repayment'),
        'accountClosurePendingTransactionDescription': m1,
        'accountClosurePendingTransactionTitle':
            MessageLookupByLibrary.simpleMessage('Pending transactions'),
        'accountClosureSuccessCta':
            MessageLookupByLibrary.simpleMessage('Back to dashboard'),
        'accountClosureSuccessSubtitle': MessageLookupByLibrary.simpleMessage(
            'Your credit account is closed.'),
        'accountClosureSuccessTitle':
            MessageLookupByLibrary.simpleMessage('IT’S DONE!'),
        'activateBusinessCreditCardInfo': MessageLookupByLibrary.simpleMessage(
            'Turn any of your Wio Business cards into a credit card!'),
        'activateCreditCardBSFooterText': MessageLookupByLibrary.simpleMessage(
            'By selecting ’Activate now’, you agree with the \nTerms & Conditions and the Key Fact Statement'),
        'activateCreditCardBottomsheetSkipCta':
            MessageLookupByLibrary.simpleMessage('Skip for now'),
        'activateCreditCardBottomsheetSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'Apply for Wio Credit and turn any of your Wio Business cards into a credit card.'),
        'activateCreditCardBottomsheetTitle':
            MessageLookupByLibrary.simpleMessage(
                'Would you also like to activate your Wio Business credit ?'),
        'activateNowCta': MessageLookupByLibrary.simpleMessage('Activate now'),
        'applyCreditBannerTitle': MessageLookupByLibrary.simpleMessage(
            'Turn any of your Wio Business cards into a credit card.'),
        'applyCreditHeader': MessageLookupByLibrary.simpleMessage(
            'Apply for Wio Business Credit'),
        'applyNowCta': MessageLookupByLibrary.simpleMessage('Apply now'),
        'assignmentLetterBottomSheetDescription':
            MessageLookupByLibrary.simpleMessage(
                'An assignment letter is a document issued by your payment platform provider (e.g., Network International, PayTabs, Stripe, etc.) confirming that the proceeds from your sales—whether through their platform or POS device—will be directed to your Wio account for the duration of the credit facility.\n\nHow to obtain one: Once your credit offer is approved, we’ll send you the borrowing agreement via email. You can then forward this agreement to your payment processor or acquirer—either through their support channel or by contacting your account manager. Based on this, they will issue the assignment letter.'),
        'assignmentLetterBottomSheetDisclaimer':
            MessageLookupByLibrary.simpleMessage(
                'The above is an indicative process. Your payment platform provider may follow a different procedure, and timelines or requirements may vary based on their internal policies.'),
        'assignmentLetterBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage(
                'What is the assignment letter?'),
        'autopayFeeCalculationEditAutopaySettings':
            MessageLookupByLibrary.simpleMessage('Edit autopay settings'),
        'autopayFeeCalculationEndedAt': m2,
        'autopayFeeCalculationFaq': MessageLookupByLibrary.simpleMessage('FAQ'),
        'autopayFeeCalculationFeeAmountToday': m3,
        'autopayFeeCalculationFeeAmountTodayDescription':
            MessageLookupByLibrary.simpleMessage(
                'Your fee is calculated based on how much your outstanding amount is and if you\'re on a charge free period.'),
        'autopayFeeCalculationFeeFreePeriod':
            MessageLookupByLibrary.simpleMessage('Your charge free period'),
        'autopayFeeCalculationFeeFreePeriodDescription': m4,
        'autopayFeeCalculationHowAutopayCalculated':
            MessageLookupByLibrary.simpleMessage(
                'How your autopay amount and fee are calculated'),
        'autopayFeeCalculationPercentageOfSpentMoney': m5,
        'autopayFeeCalculationSpentAsOfToday':
            MessageLookupByLibrary.simpleMessage('You spent as of today:'),
        'autopayFeeCalculationSpentDescription':
            MessageLookupByLibrary.simpleMessage(
                'First, we look at how much you spent from Wio Business Credit.'),
        'autopayFeeCalculationStillHaveQuestions':
            MessageLookupByLibrary.simpleMessage('Still have questions?'),
        'autopayFeeCalculationTillDate': m6,
        'autopayFeeCalculationWhyAsOfToday':
            MessageLookupByLibrary.simpleMessage('Why “as of today”?'),
        'autopayFeeCalculationWhyAsOfTodayAnswer': m7,
        'autopayFeeCalculationYourAmount': MessageLookupByLibrary.simpleMessage(
            'Your autopay amount as of today:'),
        'autopayFeeCalculationYourAmountDescription':
            MessageLookupByLibrary.simpleMessage(
                'Your autopay amount is calculate based on how much you spend and how much you set up to pay back.'),
        'autopayFeeCalculationYourAutopaySettings':
            MessageLookupByLibrary.simpleMessage('Your autopay settings:'),
        'autopayFeeCalculationYourAutopaySettingsDescription':
            MessageLookupByLibrary.simpleMessage(
                'Next, we look at how much you set up to pay back. You can change it at any time.'),
        'autopayFeeCalculationYourOutstandingBalance':
            MessageLookupByLibrary.simpleMessage('Your outstanding balance'),
        'autopayFeeCalculationYourOutstandingBalanceDescription':
            MessageLookupByLibrary.simpleMessage(
                'To calculate the fee, we look at how much is your outstanding amount after you\'ve done your monthly payment.'),
        'borrowCta': MessageLookupByLibrary.simpleMessage('Borrow'),
        'borrowingPowerBSCancel': MessageLookupByLibrary.simpleMessage('No'),
        'borrowingPowerBSConfirm': MessageLookupByLibrary.simpleMessage('Yes'),
        'borrowingPowerBSDescription': MessageLookupByLibrary.simpleMessage(
            'Only authorized signatories with borrowing power can apply for Wio Business credit'),
        'borrowingPowerBSTitle': m8,
        'borrowingPowerDocumentUploadPageDescription':
            MessageLookupByLibrary.simpleMessage(
                'Please upload one of the following documents clearly stating you have the borrowing power for your company :'),
        'borrowingPowerDocumentUploadPageInstruction1':
            MessageLookupByLibrary.simpleMessage('Power of attorney'),
        'borrowingPowerDocumentUploadPageInstruction2':
            MessageLookupByLibrary.simpleMessage('Memorandum of association'),
        'borrowingPowerDocumentUploadPageTitle': m9,
        'borrowingPowerUploadDocumentBoxSubtitle':
            MessageLookupByLibrary.simpleMessage('PDF, JPG, PNG  max 50MB'),
        'borrowingPowerUploadDocumentBoxTitle':
            MessageLookupByLibrary.simpleMessage('Upload document'),
        'borrowingPowerWarningMessage': MessageLookupByLibrary.simpleMessage(
            'Only authorized signatories with borrowing power can apply for Wio Business credit'),
        'businessCreditCardLabel':
            MessageLookupByLibrary.simpleMessage('Business credit card'),
        'businessLoan': MessageLookupByLibrary.simpleMessage('Business Loan'),
        'businessLoanApplicationSubmittedSuccessDescription':
            MessageLookupByLibrary.simpleMessage(
                'It will be in your account soon'),
        'businessLoanApplicationSubmittedSuccessSubtitle':
            MessageLookupByLibrary.simpleMessage('Your money is on it’s way!'),
        'businessLoanApplicationSubmittedSuccessTitle':
            MessageLookupByLibrary.simpleMessage('Done'),
        'businessLoanApplyBannerDescription': MessageLookupByLibrary.simpleMessage(
            'Flexible and tailored for you. Apply now to see how much you can borrow.'),
        'businessLoanBorrowLoanTerm': m10,
        'businessLoanBorrowSubtitle': MessageLookupByLibrary.simpleMessage(
            'Pick your loan term, set your amount, check the details, and get the money instantly in your Wio Business bank account.'),
        'businessLoanBorrowTitle': m11,
        'businessLoanFirstPaymentDate':
            MessageLookupByLibrary.simpleMessage('First payment date'),
        'businessLoanInterest': m12,
        'businessLoanLastPaymentDate':
            MessageLookupByLibrary.simpleMessage('Last payment date'),
        'businessLoanLearnMoreApply':
            MessageLookupByLibrary.simpleMessage('Apply'),
        'businessLoanLearnMoreFaq1Ans': MessageLookupByLibrary.simpleMessage(
            'You\'ll pay a fixed monthly installment covering the principal and interest. A loan processing fee and early repayment fee may apply.'),
        'businessLoanLearnMoreFaq1Que':
            MessageLookupByLibrary.simpleMessage('What are the costs?'),
        'businessLoanLearnMoreFaq2Ans': MessageLookupByLibrary.simpleMessage(
            'You\'ll need to provide a VAT document (if applicable) and the IBAN of your turnover accounts.'),
        'businessLoanLearnMoreFaq2Que':
            MessageLookupByLibrary.simpleMessage('What is required?'),
        'businessLoanLearnMoreFaq3Ans': MessageLookupByLibrary.simpleMessage(
            'A late fee may apply, and your credit score may be impacted. Ensure timely payments to avoid penalties.'),
        'businessLoanLearnMoreFaq3Que': MessageLookupByLibrary.simpleMessage(
            'What happens if I miss a repayment?'),
        'businessLoanLearnMoreFeature1Desc': MessageLookupByLibrary.simpleMessage(
            'Your business deserves a solution that fits, and we’re here to make it happen'),
        'businessLoanLearnMoreFeature1Title':
            MessageLookupByLibrary.simpleMessage('Tailored to loan'),
        'businessLoanLearnMoreFeature2Desc': MessageLookupByLibrary.simpleMessage(
            'The money is credited directly to your Wio Business account and ready to be spent'),
        'businessLoanLearnMoreFeature2Title':
            MessageLookupByLibrary.simpleMessage(
                'Money straight in your account'),
        'businessLoanLearnMoreFeature3Desc': MessageLookupByLibrary.simpleMessage(
            'Choose a loan term from 3 to 48 months, then repay with automatic monthly installments'),
        'businessLoanLearnMoreFeature3Title':
            MessageLookupByLibrary.simpleMessage(
                'Flexible and effortless repayment'),
        'businessLoanLearnMoreHowItWorksDesc': MessageLookupByLibrary.simpleMessage(
            'Submit your application through the app. Approval typically takes 3 business days. After approval, you can adjust your loan amount and choose a repayment term. '),
        'businessLoanLearnMoreSubtitle': MessageLookupByLibrary.simpleMessage(
            'Flexible and tailored for you. Apply now to see how much you can borrow.'),
        'businessLoanLearnMoreTitle': MessageLookupByLibrary.simpleMessage(
            'Get the loan that treats you right'),
        'businessLoanLoanAmount':
            MessageLookupByLibrary.simpleMessage('Loan amount'),
        'businessLoanLoanAmountDesc': MessageLookupByLibrary.simpleMessage(
            'This is the total amount that will be credited to your Wio Business account. The maximum you can borrow depends on the loan term you choose.'),
        'businessLoanMonthlyInstalment':
            MessageLookupByLibrary.simpleMessage('Monthly instalment'),
        'businessLoanOfferReadyMessage': MessageLookupByLibrary.simpleMessage(
            'Your loan offer\'s ready!  Let’s take a look.'),
        'businessLoanOneTimeFee':
            MessageLookupByLibrary.simpleMessage('One-time processing fee'),
        'businessLoanProcessingFee':
            MessageLookupByLibrary.simpleMessage('Processing fee '),
        'businessLoanProcessingFeeDesc': MessageLookupByLibrary.simpleMessage(
            'This is a one-time fee that will be charged once you borrow the selected amount and sign the loan agreement.'),
        'businessLoanProcessingFeeInsufficientBalance': m13,
        'businessLoanRejectedBannerDescription':
            MessageLookupByLibrary.simpleMessage(
                'Unfortunately, your application for Wio Business Loan has been declined. Your company doesn\'t meet all the required criteria. You can try again in three months.'),
        'businessLoanThingsToKnowFeatureFour':
            MessageLookupByLibrary.simpleMessage(
                'Once confirmed, the transaction can\'t be reversed.'),
        'businessLoanThingsToKnowFeatureOne': m14,
        'businessLoanThingsToKnowFeatureThree': m15,
        'businessLoanThingsToKnowFeatureTwo': MessageLookupByLibrary.simpleMessage(
            'Monthly payments will be deducted automatically from your current account and/or saving spaces.'),
        'businessLoanTotalAmountToRepay':
            MessageLookupByLibrary.simpleMessage('Total amount to repay'),
        'cancelCta': MessageLookupByLibrary.simpleMessage('Cancel'),
        'cannotUpdateAutopayPercentageError':
            MessageLookupByLibrary.simpleMessage(
                'Please come later after you use some credit money'),
        'channelFinanceAgreementBottomSheetCta':
            MessageLookupByLibrary.simpleMessage('I accept'),
        'channelFinanceAgreementBottomSheetError':
            MessageLookupByLibrary.simpleMessage(
                'Something went wrong while getting the agreement'),
        'closeCta': MessageLookupByLibrary.simpleMessage('Close'),
        'completeApplicationCta':
            MessageLookupByLibrary.simpleMessage('Complete application'),
        'completeApplicationDescription': MessageLookupByLibrary.simpleMessage(
            'You’ve got only a few steps left to complete your Wio Business Credit application'),
        'completeApplicationTitle':
            MessageLookupByLibrary.simpleMessage('Complete your application'),
        'completeBusinessLoanApplicationDescription':
            MessageLookupByLibrary.simpleMessage(
                'You’ve got only a few steps left to complete your Wio Business Loan application'),
        'completePosHypothecationPageResendEmail':
            MessageLookupByLibrary.simpleMessage('Resend email'),
        'completePosHypothecationPageStep1':
            MessageLookupByLibrary.simpleMessage('Borrow agreement signed'),
        'completePosHypothecationPageStep2': m16,
        'completePosHypothecationPageStep3':
            MessageLookupByLibrary.simpleMessage('Complete POS hypothecation'),
        'completePosHypothecationPageStep3Info':
            MessageLookupByLibrary.simpleMessage(
                'This final step secures your credit line and enables repayments through your POS transactions.'),
        'completePosHypothecationPageSubtitle': m17,
        'completePosHypothecationPageTitle':
            MessageLookupByLibrary.simpleMessage('Almost there !'),
        'completedRefreshLabel':
            MessageLookupByLibrary.simpleMessage('Completed'),
        'confirmAutodebitAccountInformation': MessageLookupByLibrary.simpleMessage(
            'In case of insufficient balance other Wio Business AED accounts will be debited for repayment'),
        'confirmAutodebitAccountSubtitle': MessageLookupByLibrary.simpleMessage(
            'Wio will auto-debit this account for your daily repayments and possible overdues.'),
        'confirmAutodebitAccountTitle': MessageLookupByLibrary.simpleMessage(
            'Confirm the account for withdrawals and repayments'),
        'confirmAutodebitAutopayFromSSSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'Allow Wio to withdraw money from your saving spaces if you don’t have enough balance in your AED current  accounts'),
        'confirmAutodebitAutopayFromSSTitle':
            MessageLookupByLibrary.simpleMessage('Repay from Saving Space'),
        'confirmAutodebitSelectAccount':
            MessageLookupByLibrary.simpleMessage('Select account'),
        'confirmCta': MessageLookupByLibrary.simpleMessage('Confirm'),
        'continueCta': MessageLookupByLibrary.simpleMessage('Continue'),
        'creditAccountLockedBannerCTA':
            MessageLookupByLibrary.simpleMessage('Unlock your credit'),
        'creditAccountLockedBannerTitle': m18,
        'creditAcknowledgeRejectCta':
            MessageLookupByLibrary.simpleMessage('Got it'),
        'creditAgreementApplicationSubmitSuccessScreenCta':
            MessageLookupByLibrary.simpleMessage('Show me how'),
        'creditAgreementApplicationSubmitSuccessScreenDescription':
            MessageLookupByLibrary.simpleMessage(
                'From now on, easily choose between debit or credit for your spends.'),
        'creditAgreementApplicationSubmitSuccessScreenTitle':
            MessageLookupByLibrary.simpleMessage(
                'Wio Business Credit Unlocked!'),
        'creditAgreementBLBannerText': MessageLookupByLibrary.simpleMessage(
            'Once signed, the transaction can’t be reversed'),
        'creditAgreementPageTitle':
            MessageLookupByLibrary.simpleMessage('Borrow agreement'),
        'creditAgreementPosBannerText': MessageLookupByLibrary.simpleMessage(
            'Once confirmed, the transaction can’t be reversed, as we will open a credit account immediately upon approval of your application.'),
        'creditAgreementSignCta':
            MessageLookupByLibrary.simpleMessage('Sign via SMS'),
        'creditAmountOnHoldInfoBottomSheetDescription':
            MessageLookupByLibrary.simpleMessage(
                'When you use your card to make a payment, the transaction starts with a \'pending\' state. After you pay, the seller checks everything is okay, and when they\'re sure, we change the status to \'completed\'. This usually happens with 1-3 days after you make a payment.'),
        'creditAmountOnHoldInfoBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('Pending transactions'),
        'creditAnnualTurnoverCancelCta':
            MessageLookupByLibrary.simpleMessage('Cancel'),
        'creditAnnualTurnoverConfirmCta':
            MessageLookupByLibrary.simpleMessage('Confirm'),
        'creditAnnualTurnoverInputLabel':
            MessageLookupByLibrary.simpleMessage('Annual turnover '),
        'creditAnnualTurnoverNextCta':
            MessageLookupByLibrary.simpleMessage('Next'),
        'creditAnnualTurnoverPageDescription': MessageLookupByLibrary.simpleMessage(
            'Turnover is the total credits your company is expecting to receive across all accounts held by your company in the UAE.'),
        'creditAnnualTurnoverPageTitle': MessageLookupByLibrary.simpleMessage(
            'Confirm this your company\'s annual turnover'),
        'creditAnnualTurnoverUpdateCta':
            MessageLookupByLibrary.simpleMessage('Update'),
        'creditAnnualTurnoverUpdateErrorToastMessage':
            MessageLookupByLibrary.simpleMessage(
                'Oops we couldn’t update your info. Please try again.'),
        'creditAnnualTurnoverUpdateSuccessToastMessage':
            MessageLookupByLibrary.simpleMessage(
                'Turnover information updated !'),
        'creditApplicationRecapAccountBanner': MessageLookupByLibrary.simpleMessage(
            'A credit account will be created immediately after the approval of your application'),
        'creditApplicationRecapAnnualTurnover':
            MessageLookupByLibrary.simpleMessage('Annual turnover'),
        'creditApplicationRecapCompanyBankAccounts':
            MessageLookupByLibrary.simpleMessage('Company bank accounts'),
        'creditApplicationRecapErrorMessage':
            MessageLookupByLibrary.simpleMessage(
                'Oops we couldn’t update your information. Please try again.'),
        'creditApplicationRecapFiledAuditedFinancialStatements':
            MessageLookupByLibrary.simpleMessage(
                'Audited Financial Statements'),
        'creditApplicationRecapFiledVatReports':
            MessageLookupByLibrary.simpleMessage('Filed VAT reports'),
        'creditApplicationRecapFromPreviousPageDescription':
            MessageLookupByLibrary.simpleMessage(
                'Please review your details to make sure everything’s up-to-date.'),
        'creditApplicationRecapFromPreviousPageTitle':
            MessageLookupByLibrary.simpleMessage(
                'Check your info and submit the application'),
        'creditApplicationRecapFromPreviousSubmit':
            MessageLookupByLibrary.simpleMessage('Confirm and submit'),
        'creditApplicationRecapNoVatReporting':
            MessageLookupByLibrary.simpleMessage('No VAT reporting'),
        'creditApplicationRecapOnlyWioBankAccount':
            MessageLookupByLibrary.simpleMessage('Only Wio Business account'),
        'creditApplicationRecapPageDescription':
            MessageLookupByLibrary.simpleMessage(
                'You’re all set, check the details and submit your application.'),
        'creditApplicationRecapPageTitle':
            MessageLookupByLibrary.simpleMessage('Now, let’s review it all'),
        'creditApplicationRecapPaybackDay': m19,
        'creditApplicationRecapPaybackDayTitle':
            MessageLookupByLibrary.simpleMessage('Pay back day of month'),
        'creditApplicationRecapSubmit':
            MessageLookupByLibrary.simpleMessage('Submit application'),
        'creditApplicationRecapSubmitBanner':
            MessageLookupByLibrary.simpleMessage(
                'Once submitted, the application can’t be reversed'),
        'creditApplicationRecapSuccessMessage':
            MessageLookupByLibrary.simpleMessage('Information updated !'),
        'creditApplicationRecapVatReportingAnnually':
            MessageLookupByLibrary.simpleMessage('Annually'),
        'creditApplicationRecapVatReportingMethod':
            MessageLookupByLibrary.simpleMessage('VAT Reporting method'),
        'creditApplicationRecapVatReportingMonthly':
            MessageLookupByLibrary.simpleMessage('Monthly'),
        'creditApplicationRecapVatReportingQuarterly':
            MessageLookupByLibrary.simpleMessage('Quarterly'),
        'creditApplicationRepaymentPlanCta':
            MessageLookupByLibrary.simpleMessage('Let’s go'),
        'creditApplicationRepaymentPlanDescription':
            MessageLookupByLibrary.simpleMessage(
                'Ensure timely payments to meet your loan milestones to avoid extra fees.'),
        'creditApplicationRepaymentPlanInterest':
            MessageLookupByLibrary.simpleMessage('Interest'),
        'creditApplicationRepaymentPlanLoanAmount':
            MessageLookupByLibrary.simpleMessage('Loan amount'),
        'creditApplicationRepaymentPlanLoanTermLabel':
            MessageLookupByLibrary.simpleMessage('Loan term'),
        'creditApplicationRepaymentPlanLoanTermValue': m20,
        'creditApplicationRepaymentPlanPeriodOrAmountNullError':
            MessageLookupByLibrary.simpleMessage(
                'Please make sure loan amount and term selected successfully.'),
        'creditApplicationRepaymentPlanProcessingFee':
            MessageLookupByLibrary.simpleMessage('One-time processing fee'),
        'creditApplicationRepaymentPlanTitle':
            MessageLookupByLibrary.simpleMessage(
                'Great! Here’s your repayment plan'),
        'creditApplicationRepaymentPlanTotalPayback':
            MessageLookupByLibrary.simpleMessage('Total payback'),
        'creditApplicationSubmitSuccessPageCta':
            MessageLookupByLibrary.simpleMessage('Done'),
        'creditApplicationSubmitSuccessPageDescription':
            MessageLookupByLibrary.simpleMessage(
                'Application submitted.\nWe\'ll review everything and let you know within 2-3 days.'),
        'creditApplicationSubmitSuccessPageTitle':
            MessageLookupByLibrary.simpleMessage('MABROOK!'),
        'creditAuditedFinancialStatementUploadScreenTitle':
            MessageLookupByLibrary.simpleMessage(
                'Upload audited financial statements for your company'),
        'creditAuditedStatementUploadScreenDescriptionDetailThree':
            MessageLookupByLibrary.simpleMessage(
                '1 year of company financial activity'),
        'creditAutoPayFeeCalculation': MessageLookupByLibrary.simpleMessage(
            'See how we calculate your fee'),
        'creditAutoPayFeeCalculationDetailsBottomSheetFeeAmount':
            MessageLookupByLibrary.simpleMessage('Fee amount:'),
        'creditAutoPayFeeCalculationDetailsBottomSheetSubtitle': m21,
        'creditAutoPayFeeCalculationDetailsBottomSheetTipText':
            MessageLookupByLibrary.simpleMessage(
                'Repay 100% of spent money this month to resume your fee-free period.'),
        'creditAutoPayFeeCalculationDetailsBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('Your fee as of today'),
        'creditAutoPayFeeFreePeriodNoFee': m22,
        'creditAutoPayMinimumPaymentToAvoidFees': m23,
        'creditAutoPayNextPayment': m24,
        'creditAutoPaySetTo':
            MessageLookupByLibrary.simpleMessage('Autopay set to'),
        'creditAutoPayTotalDue':
            MessageLookupByLibrary.simpleMessage('Total due'),
        'creditAutopayFromSavingSpaceConfirmationAccept':
            MessageLookupByLibrary.simpleMessage('Turn off'),
        'creditAutopayFromSavingSpaceConfirmationCancel':
            MessageLookupByLibrary.simpleMessage('Keep'),
        'creditAutopayFromSavingSpaceConfirmationDescription':
            MessageLookupByLibrary.simpleMessage(
                'If you turn it off you might get charged with fees if you don’t have enough money in your current AED account on autopay date.\n\nYou can change it later in “Manage”'),
        'creditAutopayFromSavingSpaceDescription':
            MessageLookupByLibrary.simpleMessage(
                'Money will be taken from your Saving Space when you don’t have enough money in your current AED account.'),
        'creditAutopayFromSavingSpaceTitle':
            MessageLookupByLibrary.simpleMessage('Autopay from Saving Space'),
        'creditAutopayPercentageUpdateSuccessfully':
            MessageLookupByLibrary.simpleMessage(
                'Autopay repayment percentage successfully updated'),
        'creditBankAccountsNoOptionSelectedError':
            MessageLookupByLibrary.simpleMessage(
                'Please select an option that describes your company\'s business bank'),
        'creditBannerFooterText': m25,
        'creditCommonToastSomethingWentWrong':
            MessageLookupByLibrary.simpleMessage('Something went wrong.'),
        'creditCompanyBankAccountInputScreenCtaText':
            MessageLookupByLibrary.simpleMessage('Next'),
        'creditCompanyBankAccountOptionOtherBanks':
            MessageLookupByLibrary.simpleMessage(
                'My company has other bank accounts in the UAE'),
        'creditCompanyBankAccountOptionSelectorTitle':
            MessageLookupByLibrary.simpleMessage(
                'Tell us about your company bank accounts'),
        'creditCompanyBankAccountOptionWioOnly':
            MessageLookupByLibrary.simpleMessage(
                'Wio Business is my company\'s only bank account'),
        'creditCreditLimitReduceSuccessPageButtonTitle':
            MessageLookupByLibrary.simpleMessage('Done'),
        'creditCreditLimitReduceSuccessPageDescription': m26,
        'creditCreditLimitReduceSuccessPageTitle':
            MessageLookupByLibrary.simpleMessage(
                'Limit decreased successfully'),
        'creditDashboardAutopayAmountLabel':
            MessageLookupByLibrary.simpleMessage('Autopay amount'),
        'creditDashboardAutopayEditButtonTitle':
            MessageLookupByLibrary.simpleMessage('Edit autopay'),
        'creditDashboardAutopayFeeLabel':
            MessageLookupByLibrary.simpleMessage('Fee'),
        'creditDashboardAutopayNoFeeLabel':
            MessageLookupByLibrary.simpleMessage('No fee'),
        'creditDashboardAutopaySectionTitle': m27,
        'creditDashboardAvailableLimit':
            MessageLookupByLibrary.simpleMessage('Available limit'),
        'creditDashboardAvailableToBorrow':
            MessageLookupByLibrary.simpleMessage('Available to borrow'),
        'creditDashboardBorrow': MessageLookupByLibrary.simpleMessage('Borrow'),
        'creditDashboardBorrowed':
            MessageLookupByLibrary.simpleMessage('Borrowed'),
        'creditDashboardCreditLockedTitle':
            MessageLookupByLibrary.simpleMessage('Credit locked'),
        'creditDashboardCreditTab':
            MessageLookupByLibrary.simpleMessage('My credit'),
        'creditDashboardDueDate': m28,
        'creditDashboardEasyCashTab':
            MessageLookupByLibrary.simpleMessage('Quick Cash'),
        'creditDashboardFeePerDay':
            MessageLookupByLibrary.simpleMessage('Fee per day'),
        'creditDashboardFees': MessageLookupByLibrary.simpleMessage('Fees'),
        'creditDashboardLastAutoPaidAmount': m29,
        'creditDashboardLastAutoPayDateLabel': m30,
        'creditDashboardLastAutoPayDescription': m31,
        'creditDashboardLastAutoPayFee': m32,
        'creditDashboardLatePaymentFee':
            MessageLookupByLibrary.simpleMessage('Late payment fee'),
        'creditDashboardLockedCredit':
            MessageLookupByLibrary.simpleMessage('Locked credit'),
        'creditDashboardManageButtonTitle':
            MessageLookupByLibrary.simpleMessage('Manage'),
        'creditDashboardPageTitle':
            MessageLookupByLibrary.simpleMessage('Wio Business Credit'),
        'creditDashboardPayButtonTitle':
            MessageLookupByLibrary.simpleMessage('Pay credit'),
        'creditDashboardPayback':
            MessageLookupByLibrary.simpleMessage('Payback'),
        'creditDashboardRefreshError': MessageLookupByLibrary.simpleMessage(
            'Sorry, we were unable to refresh the dashboard. Please try again later'),
        'creditDashboardSpentLabel':
            MessageLookupByLibrary.simpleMessage('Already spent'),
        'creditDashboardStatementButtonTitle':
            MessageLookupByLibrary.simpleMessage('Statement'),
        'creditDashboardTakeMore':
            MessageLookupByLibrary.simpleMessage('Take more'),
        'creditDashboardToSpendLabel':
            MessageLookupByLibrary.simpleMessage('Available to spend '),
        'creditDashboardTotalDue':
            MessageLookupByLibrary.simpleMessage('Total due'),
        'creditDashboardTransactionsSectionTitle':
            MessageLookupByLibrary.simpleMessage('Transactions'),
        'creditDashboardYouBorrowed':
            MessageLookupByLibrary.simpleMessage('You borrowed'),
        'creditFeeFreePeriodTooltipAcknowledgeCta':
            MessageLookupByLibrary.simpleMessage('Got it, thanks!'),
        'creditFeeFreePeriodTooltipBody': m33,
        'creditFeeFreePeriodTooltipTitle':
            MessageLookupByLibrary.simpleMessage('Resume your fee-free period'),
        'creditGenericErrorScreenCtaText':
            MessageLookupByLibrary.simpleMessage('Close'),
        'creditGenericErrorScreenDescription':
            MessageLookupByLibrary.simpleMessage(
                'Something went wrong on our side. Please try again later.'),
        'creditGenericErrorScreenSubtitle':
            MessageLookupByLibrary.simpleMessage('Something went wrong'),
        'creditGenericErrorScreenTitle':
            MessageLookupByLibrary.simpleMessage('Oops!'),
        'creditGenericErrorScreenTryAgain':
            MessageLookupByLibrary.simpleMessage('Please try again later'),
        'creditIbanInputBottomSheetCtaText':
            MessageLookupByLibrary.simpleMessage('Add'),
        'creditIbanInputBottomSheetDescription':
            MessageLookupByLibrary.simpleMessage(
                'You may receive a notification from your bank. Don’t worry your information security is our priority.'),
        'creditIbanInputBottomSheetErrorGeneric':
            MessageLookupByLibrary.simpleMessage('Invalid IBAN'),
        'creditIbanInputBottomSheetErrorInvalidLength':
            MessageLookupByLibrary.simpleMessage(
                'The entered IBAN has an invalid length'),
        'creditIbanInputBottomSheetErrorInvalidStructure':
            MessageLookupByLibrary.simpleMessage(
                'The entered IBAN has an invalid structure'),
        'creditIbanInputBottomSheetErrorNotUaeIban':
            MessageLookupByLibrary.simpleMessage(
                'You can enter only UAE IBANs'),
        'creditIbanInputBottomSheetInputFieldLabel':
            MessageLookupByLibrary.simpleMessage('IBAN number'),
        'creditIbanInputBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('Add bank account IBAN'),
        'creditIneligibilityBottomsheetCta':
            MessageLookupByLibrary.simpleMessage('Got it, thanks'),
        'creditLearnMoreCta':
            MessageLookupByLibrary.simpleMessage('Learn more'),
        'creditLearnMoreCtaText':
            MessageLookupByLibrary.simpleMessage('Apply now'),
        'creditLearnMoreFaqTitle': MessageLookupByLibrary.simpleMessage('FAQs'),
        'creditLearnMoreFirstFaq': MessageLookupByLibrary.simpleMessage(
            'What are the Wio Business Credit costs?'),
        'creditLearnMoreFirstFaqAnswer': m34,
        'creditLearnMoreFirstFeatureDesc': MessageLookupByLibrary.simpleMessage(
            'Easily switch between debit and credit mode on any of your Wio Business cards.'),
        'creditLearnMoreFirstFeatureTitle':
            MessageLookupByLibrary.simpleMessage(
                'One card for debit and credit'),
        'creditLearnMoreHowItWorks':
            MessageLookupByLibrary.simpleMessage('How it works'),
        'creditLearnMoreHowItWorksDesc': MessageLookupByLibrary.simpleMessage(
            'Submit your application directly on the app. Upon application approval, customize your repayment preferences and start spending by setting any of your Wio Business cards into credit spending mode.'),
        'creditLearnMorePageSubtitle': MessageLookupByLibrary.simpleMessage(
            'Apply for Wio Credit and turn any of your Wio Business cards into a credit card.'),
        'creditLearnMorePageTitle':
            MessageLookupByLibrary.simpleMessage('Wio Business Credit'),
        'creditLearnMoreSecondFaq': MessageLookupByLibrary.simpleMessage(
            'How does the fee-free period work?'),
        'creditLearnMoreSecondFaqAnswer': m35,
        'creditLearnMoreSecondFeatureDesc': m36,
        'creditLearnMoreSecondFeatureTitle':
            MessageLookupByLibrary.simpleMessage('No hidden fees'),
        'creditLearnMoreThirdFaq': MessageLookupByLibrary.simpleMessage(
            'How is Wio Business Credit better than a regular credit card?'),
        'creditLearnMoreThirdFaqAnswer': m37,
        'creditLearnMoreThirdFeatureDesc': m38,
        'creditLearnMoreThirdFeatureTitle': m39,
        'creditLearnMoreWhatYouGet':
            MessageLookupByLibrary.simpleMessage('What you’ll get'),
        'creditLimitCondition1Subtitle': m40,
        'creditLimitCondition1Title': m41,
        'creditLimitCondition2Subtitle': m42,
        'creditLimitCondition2Title':
            MessageLookupByLibrary.simpleMessage('No hidden fees'),
        'creditLimitCondition3Subtitle': MessageLookupByLibrary.simpleMessage(
            'Switch between debit and credit mode on any of your Wio Business cards.'),
        'creditLimitCondition3Title': MessageLookupByLibrary.simpleMessage(
            'One card for debit and credit'),
        'creditLimitConditionsTitle':
            MessageLookupByLibrary.simpleMessage('Offer benefits'),
        'creditLimitEditButtonTitle':
            MessageLookupByLibrary.simpleMessage('Edit limit'),
        'creditLimitReduceErrorDueToEasyCashBalance':
            MessageLookupByLibrary.simpleMessage(
                'To reduce your Wio Credit limit, please repay your Quick Cash balance'),
        'creditLimitSaveButtonTitle':
            MessageLookupByLibrary.simpleMessage('Save limit'),
        'creditLimitSelectorEditTitle':
            MessageLookupByLibrary.simpleMessage('Set your new limit'),
        'creditLimitSelectorTitle':
            MessageLookupByLibrary.simpleMessage('Your approved limit is'),
        'creditLimitSubmitButtonTitle':
            MessageLookupByLibrary.simpleMessage('Accept offer'),
        'creditLockedBottomsheetDescription': MessageLookupByLibrary.simpleMessage(
            'Pay the minimum amount due now to unlock credit and avoid additional charges.'),
        'creditLockedBottomsheetLatePaymentFee':
            MessageLookupByLibrary.simpleMessage('Late payment fee:'),
        'creditLockedBottomsheetMinimumAmountToUnlock':
            MessageLookupByLibrary.simpleMessage(
                'Minimum amount due\nto unlock credit:'),
        'creditLockedBottomsheetMinimumPaymentDue':
            MessageLookupByLibrary.simpleMessage('Minimum payment due:'),
        'creditLockedBottomsheetSubtitle': MessageLookupByLibrary.simpleMessage(
            'Your credit is locked because you didn\'t pay the  due amount within 6 days.'),
        'creditLockedBottomsheetTotalOutstanding': m43,
        'creditOfferReadyMessage':
            MessageLookupByLibrary.simpleMessage('Your credit offer is ready!'),
        'creditOnlyWioBankAccountBottomSheetBody':
            MessageLookupByLibrary.simpleMessage(
                'Please confirm that Wio Business is your company\'s sole account and that you don’t have any other bank accounts under your company name in the UAE.'),
        'creditOnlyWioBankAccountBottomSheetCtaChangeText':
            MessageLookupByLibrary.simpleMessage('Change'),
        'creditOnlyWioBankAccountBottomSheetCtaConfirmText':
            MessageLookupByLibrary.simpleMessage('Confirm'),
        'creditOnlyWioBankAccountBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('Before you continue'),
        'creditOtherBankAccountsBottomSheetDescription':
            MessageLookupByLibrary.simpleMessage(
                'Please confirm that you\'ve added all non-Wio bank accounts as this will help us increase your chances of approval or getting a higher credit limit and quicker approval.'),
        'creditOtherBankAccountsBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('Before you continue'),
        'creditOtherBankAccountsInputPageAddButtonText':
            MessageLookupByLibrary.simpleMessage('Add bank account'),
        'creditOtherBankAccountsInputPageCtaText':
            MessageLookupByLibrary.simpleMessage('Next'),
        'creditOtherBankAccountsInputPageDescription':
            MessageLookupByLibrary.simpleMessage(
                'Provide all non-Wio bank accounts to increase your chances of approval, speed up the process and get higher credit limit.'),
        'creditOtherBankAccountsInputPageTitle':
            MessageLookupByLibrary.simpleMessage(
                'Add your company\'s other bank accounts'),
        'creditOverviewDocsText': MessageLookupByLibrary.simpleMessage(
            'By continuing, you agree with the Terms & Conditions and the Key Fact Statement'),
        'creditOverviewDocsTextKfs':
            MessageLookupByLibrary.simpleMessage('Key Fact Statement'),
        'creditOverviewDocsTextTnc':
            MessageLookupByLibrary.simpleMessage('Terms & Conditions'),
        'creditOverviewScreenAuditedFinancialStatementsSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'We will ask to upload the last three years of authenticated statements.'),
        'creditOverviewScreenAuditedFinancialStatementsTitle':
            MessageLookupByLibrary.simpleMessage(
                'Audited financial statements'),
        'creditOverviewScreenCtaText':
            MessageLookupByLibrary.simpleMessage('Start Application'),
        'creditOverviewScreenFirstStepSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'If applicable, we’ll ask for VAT statements for the last 6 months or 2 quarters.'),
        'creditOverviewScreenFirstStepTitle':
            MessageLookupByLibrary.simpleMessage('VAT Information'),
        'creditOverviewScreenFirstStepVatStatementExample':
            MessageLookupByLibrary.simpleMessage('View statement example'),
        'creditOverviewScreenSecondStepSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'You’ll need to confirm your company’s annual turnover.'),
        'creditOverviewScreenSecondStepTitle':
            MessageLookupByLibrary.simpleMessage('Company turnover'),
        'creditOverviewScreenSubtitle': MessageLookupByLibrary.simpleMessage(
            'Here\'s what we need for your credit offer'),
        'creditOverviewScreenThirdStepSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'We’ll need to know the IBANs of all your company’s bank accounts.'),
        'creditOverviewScreenThirdStepTitle':
            MessageLookupByLibrary.simpleMessage('Bank accounts'),
        'creditOverviewScreenTitle':
            MessageLookupByLibrary.simpleMessage('Let\'s start your journey'),
        'creditPayCreditError': MessageLookupByLibrary.simpleMessage(
            'Sorry, we were unable to process your payment at this time. Please try again later.'),
        'creditPaybackOverdueWithFee': m44,
        'creditPaybackOverdueWithoutFee': m45,
        'creditPaybackPercent': m46,
        'creditPaymentDateSetupCta':
            MessageLookupByLibrary.simpleMessage('Continue'),
        'creditPaymentDateSetupDaysNumberExplanation':
            MessageLookupByLibrary.simpleMessage(
                'There are only 28 days for everything to work correctly even in February'),
        'creditPaymentDateSetupPageDescription':
            MessageLookupByLibrary.simpleMessage(
                'Select a day between 1 and 28 for your monthly transfers.'),
        'creditPaymentDateSetupPageTitle': MessageLookupByLibrary.simpleMessage(
            'When do you want to pay back?'),
        'creditPaymentDateSetupSelectedDate': m47,
        'creditPaymentDateSetupTip': MessageLookupByLibrary.simpleMessage(
            'Please note that once selected, the repayment date can\'t be edited.'),
        'creditPaymentPercentageSetupCta':
            MessageLookupByLibrary.simpleMessage('Continue'),
        'creditPaymentPercentageSetupPageTitle':
            MessageLookupByLibrary.simpleMessage(
                'How much do you want to pay back monthly?'),
        'creditPaymentPercentageSetupPaybackTip':
            MessageLookupByLibrary.simpleMessage(
                'Pay back the full amount every month and avoid fees and charges.'),
        'creditPaymentPercentageSetupPaymentBreakdown': m48,
        'creditPaymentPercentageSetupPaymentBreakdownWithoutFees': m49,
        'creditPaymentPercentageSetupSelectedPercentage': m50,
        'creditSelectorLabel':
            MessageLookupByLibrary.simpleMessage('All spent money'),
        'creditStatementPageTitle':
            MessageLookupByLibrary.simpleMessage('Your monthly statements'),
        'creditStatementUploadBoxSubtitle': m51,
        'creditStatementsEmptyDescription': MessageLookupByLibrary.simpleMessage(
            'Your documents will be displayed here\nwhen one is available.'),
        'creditStatementsEmptyTitle': MessageLookupByLibrary.simpleMessage(
            'You don’t have\nany statements yet'),
        'creditStatementsFileViewerDownloadCta':
            MessageLookupByLibrary.simpleMessage('Download statement'),
        'creditStatementsFileViewerTitle':
            MessageLookupByLibrary.simpleMessage('Credit account statement'),
        'creditStatementsYearLabel':
            MessageLookupByLibrary.simpleMessage('Year'),
        'creditThingsToKnowFeature1': m52,
        'creditThingsToKnowFeature2': MessageLookupByLibrary.simpleMessage(
            'No fees are applied if you pay your amount in full and don’t carry over any balance to the next month'),
        'creditThingsToKnowFeature3': m53,
        'creditThingsToKnowSubtitle':
            MessageLookupByLibrary.simpleMessage('Please review and accept'),
        'creditUpdateLimitAlreadyRequestedDesc':
            MessageLookupByLibrary.simpleMessage(
                'A limit increase request is pending on your account. You can only decrease your limit.'),
        'creditUpdateLimitAlreadyRequestedTitle':
            MessageLookupByLibrary.simpleMessage(
                'You have already requested to increase your limit.'),
        'creditUpdateLimitCondition1Subtitle':
            MessageLookupByLibrary.simpleMessage(
                'Your limit increase is subject to approval.'),
        'creditUpdateLimitCondition1Title':
            MessageLookupByLibrary.simpleMessage('Subject to approval'),
        'creditUpdateLimitCondition2Subtitle':
            MessageLookupByLibrary.simpleMessage(
                'You cannot reduce your limit below amount you’ve already spent.'),
        'creditUpdateLimitCondition2Title':
            MessageLookupByLibrary.simpleMessage('Minimum limit'),
        'creditUpdateLimitCondition3Subtitle':
            MessageLookupByLibrary.simpleMessage(
                'Your limit is reduced instantly after you click “Update limit”'),
        'creditUpdateLimitCondition3SubtitleAfterCutoff':
            MessageLookupByLibrary.simpleMessage(
                'Your limit is updated after approval.'),
        'creditUpdateLimitCondition3Title':
            MessageLookupByLibrary.simpleMessage('Instant confirmation'),
        'creditUpdateLimitCta':
            MessageLookupByLibrary.simpleMessage('Update limit'),
        'creditUpdateLimitFooterText': MessageLookupByLibrary.simpleMessage(
            'Your credit limit is based on different sources, such as your salary, credit rating, etc.'),
        'creditUpdateLimitLabel':
            MessageLookupByLibrary.simpleMessage('Update your credit limit'),
        'creditUpdateLimitRequestMore':
            MessageLookupByLibrary.simpleMessage('Request more'),
        'creditUpdateLimitRequestMoreConfirm':
            MessageLookupByLibrary.simpleMessage('Yes, I want more limit'),
        'creditUpdateLimitRequestMoreDesc': MessageLookupByLibrary.simpleMessage(
            'If you want more limit you can request to check your eligibility and we will try to give you the maximum credit limit possible.'),
        'creditUpdateLimitRequestMoreDismiss':
            MessageLookupByLibrary.simpleMessage('No, Thanks'),
        'creditUpdateLimitRequestMoreSuccessText':
            MessageLookupByLibrary.simpleMessage(
                'We’ve got your request.  We\'ll notify you once we\'ve reviewed all your information.'),
        'creditUpdateLimitRequestMoreTitle': m54,
        'creditUpdateLimitSuccessDesc': m55,
        'creditUpdateLimitSuccessTitle':
            MessageLookupByLibrary.simpleMessage('DONE!'),
        'creditUpdateOtherBankAccountsIbanError':
            MessageLookupByLibrary.simpleMessage(
                'Something went wrong while trying to update your application.'),
        'creditUploadTileText': m56,
        'creditVatReportingIntervalCtaText':
            MessageLookupByLibrary.simpleMessage('Next'),
        'creditVatReportingIntervalMonthlySubtitle':
            MessageLookupByLibrary.simpleMessage(
                'My company submits VAT reports to the tax authority on a monthly basis.'),
        'creditVatReportingIntervalMonthlyTitle':
            MessageLookupByLibrary.simpleMessage('Yes, we report monthly'),
        'creditVatReportingIntervalNoReportingSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'My company is not subject to VAT obligations.'),
        'creditVatReportingIntervalNoReportingTitle':
            MessageLookupByLibrary.simpleMessage('No, we don’t report VAT'),
        'creditVatReportingIntervalQuarterlySubtitle':
            MessageLookupByLibrary.simpleMessage(
                'My company submits VAT reports to the tax authorities on a quarterly basis.'),
        'creditVatReportingIntervalQuarterlyTitle':
            MessageLookupByLibrary.simpleMessage('Yes, we report quarterly'),
        'creditVatReportingMustBeSelectedError':
            MessageLookupByLibrary.simpleMessage(
                'Vat Reporting Interval must be selected'),
        'creditVatScreenSubtitle':
            MessageLookupByLibrary.simpleMessage('Here\'s what we need'),
        'creditVatScreenTitle': MessageLookupByLibrary.simpleMessage(
            'Does your company report VAT?'),
        'creditVatStatementUploadExample':
            MessageLookupByLibrary.simpleMessage('View statement example'),
        'creditVatStatementUploadProgress': m57,
        'creditVatStatementUploadScreenDescriptionDetailOne':
            MessageLookupByLibrary.simpleMessage('Company name'),
        'creditVatStatementUploadScreenDescriptionDetailThree':
            MessageLookupByLibrary.simpleMessage('Tax period'),
        'creditVatStatementUploadScreenDescriptionDetailTwo':
            MessageLookupByLibrary.simpleMessage(
                'Turnover and sales information'),
        'creditVatStatementUploadScreenDescriptionIntro':
            MessageLookupByLibrary.simpleMessage(
                'Please ensure your statement is in a standard format and includes :'),
        'creditVatStatementUploadScreenTitleVatReportingIntervalMonthly':
            MessageLookupByLibrary.simpleMessage(
                'Upload your company\'s last six filed VAT statements.'),
        'creditVatStatementUploadScreenTitleVatReportingIntervalQuaterly':
            MessageLookupByLibrary.simpleMessage(
                'Upload your company\'s last two filed VAT statements.'),
        'creditVatUploadInvalidReportingInterval':
            MessageLookupByLibrary.simpleMessage(
                'The VAT reporting select must be either monthly or quarterly. Please select the appropriate interval from the previous screen'),
        'customStatementLabel':
            MessageLookupByLibrary.simpleMessage('Custom statement'),
        'customStatementPeriodOneMonth':
            MessageLookupByLibrary.simpleMessage('1 month'),
        'customStatementPeriodOneYear':
            MessageLookupByLibrary.simpleMessage('1 year'),
        'customStatementPeriodSixMonths':
            MessageLookupByLibrary.simpleMessage('6 months'),
        'customStatementPeriodThreeMonths':
            MessageLookupByLibrary.simpleMessage('3 months'),
        'dashboardEasyCashBannerSubTitle': m58,
        'dashboardEasyCashBannerTitle':
            MessageLookupByLibrary.simpleMessage('Quick cash'),
        'debitAccountUpdateSuccess':
            MessageLookupByLibrary.simpleMessage('Account updated'),
        'disclaimerLabel': MessageLookupByLibrary.simpleMessage('Disclaimer'),
        'documentViewerShare':
            MessageLookupByLibrary.simpleMessage('Share document'),
        'dueOnLabel': m59,
        'easyCashManageScreenFaqSectionTitle':
            MessageLookupByLibrary.simpleMessage('Need some help?'),
        'easyCashManageScreenTitle':
            MessageLookupByLibrary.simpleMessage('Manage Quick Cash'),
        'embeddedLendingAgreementSubmittedSuccessSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'We’re processing your funds, and they’ll be in your Wio Business account in just a few minutes. We’ll let you know once they’re ready to use. \nWhen the funds arrive, simply sign in here to manage your loan!'),
        'embeddedLendingAgreementSubmittedSuccessTitle':
            MessageLookupByLibrary.simpleMessage(
                'Done. Your money is on its way!'),
        'embeddedLendingApplicationSubmittedTitle':
            MessageLookupByLibrary.simpleMessage('Mabrook!'),
        'endDateLabel': MessageLookupByLibrary.simpleMessage('End date'),
        'errorMessageActiveEasyCashAccount': MessageLookupByLibrary.simpleMessage(
            'To close your Wio Credit account, you\'ll need to repay your Quick Cash balance'),
        'errorMessageExpiredReferralCode': MessageLookupByLibrary.simpleMessage(
            'Entered referral code is expired'),
        'errorMessageInvalidReferralCode': MessageLookupByLibrary.simpleMessage(
            'Entered referral code is invalid'),
        'faq': MessageLookupByLibrary.simpleMessage('FAQ'),
        'feeCalculationAutopayAmount': m60,
        'feeCalculationCarryOverAmount':
            MessageLookupByLibrary.simpleMessage('Carry-over amount:'),
        'feeCalculationCarryOverFee':
            MessageLookupByLibrary.simpleMessage('Carry-over fee:'),
        'feeCalculationDetailsBottomSheetFeeApplyDescription': m61,
        'feeCalculationOutstandingBalance':
            MessageLookupByLibrary.simpleMessage('Outstanding balance:'),
        'fileSizeExceedsLimit': m62,
        'firstKey': MessageLookupByLibrary.simpleMessage('First key'),
        'generateStatementLabel':
            MessageLookupByLibrary.simpleMessage('Generate statement'),
        'getEasyCashLabel':
            MessageLookupByLibrary.simpleMessage('Get Quick Cash'),
        'helpCta': MessageLookupByLibrary.simpleMessage('Help'),
        'hypothecationAlreadyCompleteSuccessSubtitle':
            MessageLookupByLibrary.simpleMessage('Borrowing agreement signed'),
        'hypothecationAlreadyCompleteSuccessTitle':
            MessageLookupByLibrary.simpleMessage('Mabrook!'),
        'inReviewBannerDescription': MessageLookupByLibrary.simpleMessage(
            'Your application is under review. It shouldn\'t take more than 3 days.'),
        'learnMorePageInvalidProductTypeError':
            MessageLookupByLibrary.simpleMessage('Invalid Product type'),
        'lendingAmountOnHoldPayCreditContent': m63,
        'lendingAmountOnHoldPayCreditContentHighlighted':
            MessageLookupByLibrary.simpleMessage('Learn more'),
        'lendingAutopaySave': MessageLookupByLibrary.simpleMessage('Save'),
        'lendingCreditDashboardAvlToSpend':
            MessageLookupByLibrary.simpleMessage('Available to spend'),
        'lendingCreditLimitReduceErrorText': MessageLookupByLibrary.simpleMessage(
            'Sorry, you can’t decrease your credit limit below your spent amount.'),
        'lendingFAQ': MessageLookupByLibrary.simpleMessage('FAQ'),
        'lendingManageAnnualFee':
            MessageLookupByLibrary.simpleMessage('Annual fee'),
        'lendingManageAvailableToSpend':
            MessageLookupByLibrary.simpleMessage('Available to spend'),
        'lendingManageCashWithdrawal':
            MessageLookupByLibrary.simpleMessage('Cash withdrawal'),
        'lendingManageCloseCreditAccountCta':
            MessageLookupByLibrary.simpleMessage('Close credit account'),
        'lendingManageCreditPageDocuments':
            MessageLookupByLibrary.simpleMessage('Documents'),
        'lendingManageCreditPageDownloadStatement':
            MessageLookupByLibrary.simpleMessage('Download statement'),
        'lendingManageCreditPageManageCredit':
            MessageLookupByLibrary.simpleMessage('Manage credit'),
        'lendingManageCreditPagePaymentDate':
            MessageLookupByLibrary.simpleMessage('Payment date'),
        'lendingManageCreditPageRollover':
            MessageLookupByLibrary.simpleMessage('Roll-over fee'),
        'lendingManageCreditPageYourCredit':
            MessageLookupByLibrary.simpleMessage('Your credit'),
        'lendingManageCreditRepaymentDate': m64,
        'lendingManageNeedSomeHelp':
            MessageLookupByLibrary.simpleMessage('Need some help?'),
        'lendingManageNoAnnualFee':
            MessageLookupByLibrary.simpleMessage('No annual fee'),
        'lendingManageNoCashWithdrawal':
            MessageLookupByLibrary.simpleMessage('No cash withdrawal'),
        'lendingManageReduceCreditLimit':
            MessageLookupByLibrary.simpleMessage('Reduce your credit limit'),
        'lendingManageRepaymentTitle':
            MessageLookupByLibrary.simpleMessage('Repayment'),
        'lendingManageScreenCreditAgreement':
            MessageLookupByLibrary.simpleMessage('Credit agreement'),
        'lendingManageScreenCreditLimit':
            MessageLookupByLibrary.simpleMessage('Credit limit'),
        'lendingManageScreenKfs':
            MessageLookupByLibrary.simpleMessage('Key fact statement'),
        'lendingManageStatementsCta':
            MessageLookupByLibrary.simpleMessage('Download statement'),
        'lendingNextPayment': m65,
        'lendingNoFee': MessageLookupByLibrary.simpleMessage('No fees'),
        'lendingPaybackPercent': m66,
        'lendingPaymentBreakdown': m67,
        'lendingPaymentBreakdownNoFee': m68,
        'lendingPlusFees': MessageLookupByLibrary.simpleMessage('Plus fee'),
        'lendingSelectorLabel':
            MessageLookupByLibrary.simpleMessage('All used money'),
        'lendingSetupPaymentThingsSectionTitle2':
            MessageLookupByLibrary.simpleMessage(
                'Amount is taken from your AED saving space if your don’t have enough money in your current AED account to avoid fees and charges.'),
        'lendingSetupPaymentThingsSectionsTitle1':
            MessageLookupByLibrary.simpleMessage(
                'Pay back the full amount every month and avoid fees and charges'),
        'lendingSetupPaymentThingsToKnowTitle':
            MessageLookupByLibrary.simpleMessage('Things to know'),
        'lendingSpentOf': m69,
        'loanReferralCodeBottomsheetDescription':
            MessageLookupByLibrary.simpleMessage(
                'A referral code is a unique identifier shared by Wio partners to help you access credit facilities with added benefits—such as faster application review, personalized support, and more.\n\nIf a third party referred you for a Wio credit facility but didn’t provide a referral code, feel free to ask them—they may have simply forgotten to share it.'),
        'loanReferralCodeInputLabel':
            MessageLookupByLibrary.simpleMessage('Enter code'),
        'loanReferralCodeInvalid':
            MessageLookupByLibrary.simpleMessage('Invalid code'),
        'loanReferralCodePageSkipInput': MessageLookupByLibrary.simpleMessage(
            'I don’t have a referral code'),
        'loanReferralCodePageSubtitle': MessageLookupByLibrary.simpleMessage(
            'If you don’t just select “I don’t have a code”'),
        'loanReferralCodePageTitle': MessageLookupByLibrary.simpleMessage(
            'If you’ve got a referral code let’s fire it up'),
        'loanRepaymentMilestonesDescription': MessageLookupByLibrary.simpleMessage(
            'Ensure timely daily payments to meet your loan milestones and avoid extra charges. Your milestone amounts will automatically adjust with each payment, any new borrowing, or missed payments'),
        'loanRepaymentMilestonesTitle':
            MessageLookupByLibrary.simpleMessage('Loan repayment milestones'),
        'loanRepaymentMissedMilestonesDescription':
            MessageLookupByLibrary.simpleMessage(
                'You have missed your milestone'),
        'loanTypeInputPageTitle': MessageLookupByLibrary.simpleMessage(
            'What type of loan is right for you?'),
        'loanTypeLabel': MessageLookupByLibrary.simpleMessage('Loan type'),
        'loanWhatIsReferralCode':
            MessageLookupByLibrary.simpleMessage('What is a referral code?'),
        'managePosAutopayFromSSDescription': MessageLookupByLibrary.simpleMessage(
            'Never miss a repayment! Enable it and relax. Meanwhile, grow your wealth by earning up to 5%* interest. Double win!'),
        'managePosCreditPageTitle':
            MessageLookupByLibrary.simpleMessage('Manage'),
        'managePosCreditPreferedAccount':
            MessageLookupByLibrary.simpleMessage('Preferred account'),
        'nextAutoPayLabel': m70,
        'nextCta': MessageLookupByLibrary.simpleMessage('Next'),
        'nonReportingVatCtaText': MessageLookupByLibrary.simpleMessage('Next'),
        'otherReasonInputLabel':
            MessageLookupByLibrary.simpleMessage('Please specify other'),
        'overdueAmountLabel':
            MessageLookupByLibrary.simpleMessage('Overdue amount'),
        'payAndCloseCta': MessageLookupByLibrary.simpleMessage('Pay and close'),
        'payCreditAccountBalance': m71,
        'payCreditAccountTitle': m72,
        'payCreditBelowMinimumPaymentAmount': m73,
        'payCreditExcessivePaymentLabel': m74,
        'payCreditFromLabel': MessageLookupByLibrary.simpleMessage('From'),
        'payCreditFullAmountOption': m75,
        'payCreditInsufficientBalanceLabel': m76,
        'payCreditMinimumAmountOption': m77,
        'payCreditPageButtonTitle':
            MessageLookupByLibrary.simpleMessage('Pay credit'),
        'payCreditPageCreditLockedButtonTitle':
            MessageLookupByLibrary.simpleMessage('Pay and unlock'),
        'payCreditPageTitle':
            MessageLookupByLibrary.simpleMessage('Pay credit'),
        'payCreditSuccessPageButtonTitle':
            MessageLookupByLibrary.simpleMessage('Go to borrow'),
        'payCreditSuccessPageCreditUnlocked':
            MessageLookupByLibrary.simpleMessage('Wio credit unlocked'),
        'payCreditSuccessPageDescription': m78,
        'payCreditSuccessPageSubtitle':
            MessageLookupByLibrary.simpleMessage('Credit payment received'),
        'payCreditSuccessPageTitle':
            MessageLookupByLibrary.simpleMessage('BOOM!'),
        'payCreditToLabel': MessageLookupByLibrary.simpleMessage('To'),
        'payCreditUnlockSuccessPageButtonTitle':
            MessageLookupByLibrary.simpleMessage('Done'),
        'payCreditUnlockSuccessPageSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'Your Credit money has been unlocked successfully.'),
        'payCreditUnlockSuccessPageTitle': MessageLookupByLibrary.simpleMessage(
            'Wio Business Credit Unlocked'),
        'posApplicationApplyBannerSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'Apply now to get a customized offer based on your POS sales  '),
        'posApplicationApplyBannerTitle':
            MessageLookupByLibrary.simpleMessage('Wio line of credit'),
        'posApplicationInProgressBannerSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'You’ve got only a few steps left to complete your Wio Line of Credit application'),
        'posApplicationInProgressBannerTitle':
            MessageLookupByLibrary.simpleMessage('Complete your application'),
        'posApplicationOfferBannerCta':
            MessageLookupByLibrary.simpleMessage('Get started'),
        'posApplicationOfferBannerSubtitle': MessageLookupByLibrary.simpleMessage(
            'Now let’s review your credit limit and get started by enabling your POS credit '),
        'posApplicationOfferBannerTitle': MessageLookupByLibrary.simpleMessage(
            'Wio line of credit approved !'),
        'posApplicationRejectedBannerSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'Unfortunately, your application for Wio line of Credit has been declined. Your company doesn\'t meet all the required criteria. You can try again in three months.'),
        'posApplicationRejectedBannerTitle':
            MessageLookupByLibrary.simpleMessage('Wio line of credit'),
        'posApplicationUnderReviewBannerTitle':
            MessageLookupByLibrary.simpleMessage(
                'Wio line of credit application under review'),
        'posApplicationUnderReviewSubtitle': MessageLookupByLibrary.simpleMessage(
            'Your application is under review. It shouldn\'t take more than 3 days.'),
        'posAvailableToBorrow': m79,
        'posBorrowConfirmationPreBorrowDailyPaymentBanner': m80,
        'posBorrowConfirmationTitle':
            MessageLookupByLibrary.simpleMessage('BORROW'),
        'posBorrowExceedsLimitMessage': MessageLookupByLibrary.simpleMessage(
            'Amount exceeds your eligibility limit'),
        'posBorrowInformationTip': m81,
        'posBorrowLoanRepaymentUpdatedMessage':
            MessageLookupByLibrary.simpleMessage(
                'Your daily amount and loan repayments milestones have been updated.'),
        'posBorrowReadyToReceive': MessageLookupByLibrary.simpleMessage(
            'Ready to receive your money?'),
        'posBorrowSubtitle': MessageLookupByLibrary.simpleMessage(
            'Enter the amount and press “Continue” to review your loan details'),
        'posBorrowSuccessSubtitle': m82,
        'posBorrowSwipeRightToConfirm':
            MessageLookupByLibrary.simpleMessage('Swipe right to confirm '),
        'posBorrowTitle': MessageLookupByLibrary.simpleMessage(
            'How much would you like to borrow?'),
        'posBottomSheetBorrowPausedBannerDesc':
            MessageLookupByLibrary.simpleMessage(
                'Your borrowing is paused until the overdue amount is repaid. Please pay now to avoid missing loan repayment milestone and extra charges.'),
        'posBottomSheetCarryoverField':
            MessageLookupByLibrary.simpleMessage('Carry-over amount'),
        'posBottomSheetDailyPaymentAmountField':
            MessageLookupByLibrary.simpleMessage('Daily payment amount'),
        'posBottomSheetInterestField':
            MessageLookupByLibrary.simpleMessage('Interest'),
        'posBottomSheetLatePaymentFeeField':
            MessageLookupByLibrary.simpleMessage('Late payment fee'),
        'posBottomSheetMilestoneOverdueTotalAmountField':
            MessageLookupByLibrary.simpleMessage('Total amount due'),
        'posBottomSheetMissedDueBannerTitle':
            MessageLookupByLibrary.simpleMessage('Missed due date'),
        'posBottomSheetOverdueAmountField':
            MessageLookupByLibrary.simpleMessage('Overdue amount'),
        'posBottomSheetOverdueAmountFieldWithDueDate': m83,
        'posBottomSheetOverdueTitle':
            MessageLookupByLibrary.simpleMessage('Overdue amount'),
        'posBottomSheetPrincipalField':
            MessageLookupByLibrary.simpleMessage('Principal'),
        'posBottomSheetTotalDueOn': m84,
        'posBottomSheetTotalWithoutDate':
            MessageLookupByLibrary.simpleMessage('Total due'),
        'posClosureConfirmationCta':
            MessageLookupByLibrary.simpleMessage('Close credit line'),
        'posClosureConfirmationStep1Bullet1':
            MessageLookupByLibrary.simpleMessage(
                'You will no longer have access to your statements.'),
        'posClosureConfirmationStep1Bullet2':
            MessageLookupByLibrary.simpleMessage(
                'You may only reapply through an invitation from your acquirer.'),
        'posClosureConfirmationStep1Info': MessageLookupByLibrary.simpleMessage(
            'Select “Close credit line” to permanently close your Wio line of credit and repay any outstanding balance.  After closure:'),
        'posClosureConfirmationStep1Title':
            MessageLookupByLibrary.simpleMessage('Payback and close'),
        'posClosureConfirmationStep2Info': MessageLookupByLibrary.simpleMessage(
            'We will close your credit line and send you the Non Objection Certificate. Share this document with your acquirer to release your POS.'),
        'posClosureConfirmationStep2Title':
            MessageLookupByLibrary.simpleMessage('Release your POS'),
        'posClosureConfirmationSubtitle':
            MessageLookupByLibrary.simpleMessage('This is how it works'),
        'posClosureConfirmationText': MessageLookupByLibrary.simpleMessage(
            'I confirm I’d like to close my credit line'),
        'posClosureConfirmationTitle': MessageLookupByLibrary.simpleMessage(
            'Are you sure you want to close your Wio line of credit? '),
        'posClosureSuccessDescription': m85,
        'posClosureSuccessSubtitle': MessageLookupByLibrary.simpleMessage(
            'Your credit account is closed.'),
        'posClosureSuccessTitle':
            MessageLookupByLibrary.simpleMessage('IT’S DONE!'),
        'posCompleteHypothecation':
            MessageLookupByLibrary.simpleMessage('Complete POS hypothecation'),
        'posCompleteHypothecationDescription': MessageLookupByLibrary.simpleMessage(
            'Follow the instructions sent to your email address to complete the POS hypothecation and activate your credit line.'),
        'posCreditBannerLabel':
            MessageLookupByLibrary.simpleMessage('Wio line of credit'),
        'posCreditLabel': MessageLookupByLibrary.simpleMessage('POS credit'),
        'posCreditMissedPayment':
            MessageLookupByLibrary.simpleMessage('Missed daily payment'),
        'posCreditPaybackOverdue':
            MessageLookupByLibrary.simpleMessage('Credit payback overdue'),
        'posCreditPaybackSuccessDescription': m86,
        'posCreditPaybackSuccessSubtitle':
            MessageLookupByLibrary.simpleMessage('POS credit payment received'),
        'posCreditRepaymentsOnTrack':
            MessageLookupByLibrary.simpleMessage('Payments on track'),
        'posCreditTapToBorrow':
            MessageLookupByLibrary.simpleMessage('Tap to borrow'),
        'posDashboardAllTransactionTabTitle':
            MessageLookupByLibrary.simpleMessage('All'),
        'posDashboardAvailableToBorrow':
            MessageLookupByLibrary.simpleMessage('Available to borrow'),
        'posDashboardBorrowButtonText':
            MessageLookupByLibrary.simpleMessage('Borrow'),
        'posDashboardManageButtonText':
            MessageLookupByLibrary.simpleMessage('Manage'),
        'posDashboardPaybackButtonText':
            MessageLookupByLibrary.simpleMessage('Payback'),
        'posDashboardRepaymentsTransactionTabTitle':
            MessageLookupByLibrary.simpleMessage('Repayments'),
        'posDashboardTitle':
            MessageLookupByLibrary.simpleMessage('Wio line of credit'),
        'posDashboardTitleBorrowed': m87,
        'posDashboardTransactionSectionTitle':
            MessageLookupByLibrary.simpleMessage('Transactions'),
        'posDashboardWithdrawalsTransactionTabTitle':
            MessageLookupByLibrary.simpleMessage('Withdrawals'),
        'posHypothecationEmail':
            MessageLookupByLibrary.simpleMessage('Email address'),
        'posInterestInfoBottomsheetDescription': m88,
        'posInterestInfoBottomsheetTip': m89,
        'posInterestInfoBottomsheetTitle': MessageLookupByLibrary.simpleMessage(
            'How the interest is calculated'),
        'posLearnMoreFaq1Answer': m90,
        'posLearnMoreFaq1Question': MessageLookupByLibrary.simpleMessage(
            'What is the interest rate on Wio Line of Credit?'),
        'posLearnMoreFaq2Answer': MessageLookupByLibrary.simpleMessage(
            'Interest is calculated daily on the outstanding principal amount. You will only pay interest on the amount of the limit you have used and for the exact number of days you have used it.'),
        'posLearnMoreFaq2Question':
            MessageLookupByLibrary.simpleMessage('How interest is calculated?'),
        'posLearnMoreFaq3Answer': m91,
        'posLearnMoreFaq3Question': MessageLookupByLibrary.simpleMessage(
            'How does Wio Line of Credit works?'),
        'posLearnMoreFaq4Answer': MessageLookupByLibrary.simpleMessage(
            'Repayment will be made through daily equated installments, calculated based on the total outstanding amount at the time of withdrawal. These daily repayments will be automatically debited from your Wio Business account. Additionally, you can also repay your outstanding amount using the \"Payback\" option in your Wio Business app without any additional pre-payment charges.'),
        'posLearnMoreFaq4Question': MessageLookupByLibrary.simpleMessage(
            'How will I repay the utilized limit amount?'),
        'posLearnMoreHowToApplyDescription': MessageLookupByLibrary.simpleMessage(
            'Submit your application through the app. After approval, you can customize your credit limit and sign the contract. Finally, you’ll receive instructions to complete POS hypothecation with your POS provider to activate your Line of Credit.'),
        'posLearnMoreHowToApplyTitle':
            MessageLookupByLibrary.simpleMessage('How to apply'),
        'posLearnMorePageDescription': MessageLookupByLibrary.simpleMessage(
            'Say goodbye to long-term loans and blocked funds for EMI payments '),
        'posLimitCondition1Subtitle': MessageLookupByLibrary.simpleMessage(
            'Interest is charged only on the amount of the loan you use, for the time you use it.'),
        'posLimitCondition1Title':
            MessageLookupByLibrary.simpleMessage('Pay as you use'),
        'posLimitCondition2Subtitle': m92,
        'posLimitCondition2Title':
            MessageLookupByLibrary.simpleMessage('Seamless withdrawal'),
        'posLimitCondition3Subtitle': MessageLookupByLibrary.simpleMessage(
            'Your outstanding amount is repaid through equated daily payments from your Wio Business account & replenish your limit instantly for fresh withdrawal.'),
        'posLimitCondition3Title':
            MessageLookupByLibrary.simpleMessage('Effortless repayment'),
        'posLimitOneTimeFeeTitle':
            MessageLookupByLibrary.simpleMessage('One time application fee'),
        'posLimitOneTimeSubtitle': MessageLookupByLibrary.simpleMessage(
            'The fee will be automatically deducted from your Wio Business account after you sign the borrowing agreement.'),
        'posLoanInterestLabel': m93,
        'posMilestoneOverdueBannerDescription': m94,
        'posMinimumBorrowAmountMessage': m95,
        'posMissedMilestoneBannerDescription': m96,
        'posNextDailyPaymentBottomSheetDesc': MessageLookupByLibrary.simpleMessage(
            'This amount will be deducted daily from your Wio Business account.'),
        'posNextDailyPaymentBottomSheetLockInfoBanner':
            MessageLookupByLibrary.simpleMessage(
                'If you skip five consecutive daily payments. On the sixth day, your credit will be locked.'),
        'posNextDailyPaymentBottomSheetMissedPaymentBannerDesc': m97,
        'posNextDailyPaymentBottomSheetMissedPaymentWarning': m98,
        'posNextDailyPaymentBottomSheetPaybackCta':
            MessageLookupByLibrary.simpleMessage('Payback now'),
        'posNextDailyPaymentBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('Next daily payment'),
        'posPaidAllDebtBannerContent': MessageLookupByLibrary.simpleMessage(
            'Congrats! You’ve paid off your loan, principal plus interest. Tap \'Borrow\' to withdraw more funds.'),
        'posThingsToKnowContinueCta':
            MessageLookupByLibrary.simpleMessage('Got it, Next'),
        'posThingsToKnowFeature1': m99,
        'posThingsToKnowFeature2': MessageLookupByLibrary.simpleMessage(
            'Repay with daily automatic payments from your main AED account. '),
        'posThingsToKnowFeature3': m100,
        'posThingsToKnowFeature4': m101,
        'posThingsToKnowFeatureFour': MessageLookupByLibrary.simpleMessage(
            'Your outstanding amount is repaid through equated daily payments from your Wio Business account & replenish your limit instantly for fresh withdrawal.'),
        'posThingsToKnowFeatureOne': MessageLookupByLibrary.simpleMessage(
            'The interest rate on your Line of Credit is up to 22% per annum.'),
        'posThingsToKnowFeatureThree': MessageLookupByLibrary.simpleMessage(
            'Access funds anytime and repay within 90 days from fresh withdrawal date. Withdraw as needed, each withdrawal adds to total your outstanding balance.'),
        'posThingsToKnowFeatureTwo': MessageLookupByLibrary.simpleMessage(
            'The line of credit facility will be available for a 365-day period, starting from the date the borrowing agreement is signed.'),
        'posThingsToKnowTitle': MessageLookupByLibrary.simpleMessage(
            'Things to know before you start'),
        'posTotalOutstandingAmountBottomSheetDesc':
            MessageLookupByLibrary.simpleMessage(
                'This is the amount you currently owe as of today.'),
        'posTotalOutstandingAmountBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('Current outstanding'),
        'pullDownRefreshLabel':
            MessageLookupByLibrary.simpleMessage('Pull down to refresh'),
        'reasonNotReportingVatQuestion': MessageLookupByLibrary.simpleMessage(
            'What is the reason for not reporting VAT?'),
        'refreshingLabel':
            MessageLookupByLibrary.simpleMessage('Refreshing...'),
        'rejectedBannerDescription': MessageLookupByLibrary.simpleMessage(
            'Unfortunately, your application for Wio Business Credit has been declined. Your company doesn\'t meet all the required criteria. You can try again in three months.'),
        'releaseToRefreshLabel':
            MessageLookupByLibrary.simpleMessage('Release to refresh'),
        'repaymentDailyBorrowedAmount':
            MessageLookupByLibrary.simpleMessage('Borrowed amount'),
        'repaymentDailyPaymentInfo': MessageLookupByLibrary.simpleMessage(
            'Daily payments will be deducted from your Wio Business account.'),
        'repaymentDetailsAlreadyBorrowed':
            MessageLookupByLibrary.simpleMessage('Already borrowed'),
        'repaymentDetailsDailyPayment':
            MessageLookupByLibrary.simpleMessage('Daily payment'),
        'repaymentDetailsDueOn': m102,
        'repaymentDetailsInterests':
            MessageLookupByLibrary.simpleMessage('Interest'),
        'repaymentDetailsNextDailyPayment':
            MessageLookupByLibrary.simpleMessage('Next daily payment'),
        'repaymentDetailsPaidLabel':
            MessageLookupByLibrary.simpleMessage('Paid'),
        'repaymentDetailsPrincipal':
            MessageLookupByLibrary.simpleMessage('Principal'),
        'repaymentDetailsTotalLoanAmount':
            MessageLookupByLibrary.simpleMessage('Total loan amount'),
        'repaymentDetailsViewDetails':
            MessageLookupByLibrary.simpleMessage('View details'),
        'reviewCta': MessageLookupByLibrary.simpleMessage('Review'),
        'reviewYourCreditLimit': MessageLookupByLibrary.simpleMessage(
            'Now let’s review your credit limit and set your repayment preferences.'),
        'securedBusinessLoanCardFeature1': m103,
        'securedBusinessLoanCardFeature2': m104,
        'securedBusinessLoanCardFeature3': MessageLookupByLibrary.simpleMessage(
            'Assignment letter from acquirer'),
        'securedBusinessLoanCardSubtitle': MessageLookupByLibrary.simpleMessage(
            'For businesses with POS or online payments settled to Wio account.'),
        'securedBusinessLoanCardTitle':
            MessageLookupByLibrary.simpleMessage('Sales backed loan'),
        'securedBusinessLoanLabel':
            MessageLookupByLibrary.simpleMessage('Sales backed loan'),
        'securedLoanAssignementLetterDescription': m105,
        'securedLoanAssignementLetterStep1':
            MessageLookupByLibrary.simpleMessage('Get the assignment letter '),
        'securedLoanAssignementLetterStep1Desc':
            MessageLookupByLibrary.simpleMessage(
                'Reach out to your payment provider for an assignment letter confirming that payments are settled into your Wio account.'),
        'securedLoanAssignementLetterStep2':
            MessageLookupByLibrary.simpleMessage('Share the letter with us'),
        'securedLoanAssignementLetterStep2Desc':
            MessageLookupByLibrary.simpleMessage(
                'Follow the detailed instructions sent to your email address on how to submit the assignment letter to Wio'),
        'securedLoanAssignementLetterTitle':
            MessageLookupByLibrary.simpleMessage('Almost there'),
        'securedLoanAssignmentLetterBannerTitle':
            MessageLookupByLibrary.simpleMessage('Share assignment letter'),
        'securedLoanConfirmationDescription': MessageLookupByLibrary.simpleMessage(
            'A valid assignment letter will be required to receive the loan and have funds credited to your account. If you can’t request one from your payment provider, select “Cancel” to continue with a simple loan.'),
        'securedLoanConfirmationTitle': MessageLookupByLibrary.simpleMessage(
            'Confirm you can provide an assignment letter from your payment provider'),
        'securedLoanPageTitle': MessageLookupByLibrary.simpleMessage(
            'Here’s how to get a sales backed loan'),
        'securedLoanSelectionDescription': MessageLookupByLibrary.simpleMessage(
            'For businesses with POS or online payments settled to Wio account.'),
        'securedLoanSelectionTitle':
            MessageLookupByLibrary.simpleMessage('Sales backed loan'),
        'securedLoanStep1Description': MessageLookupByLibrary.simpleMessage(
            'Share your latest VAT statements (if applicable), company turnover, and bank account IBANs.'),
        'securedLoanStep1Title':
            MessageLookupByLibrary.simpleMessage('Submit the application'),
        'securedLoanStep2Description': MessageLookupByLibrary.simpleMessage(
            'Wio will review your application and get back to you with an offer for your maximum loan limit.'),
        'securedLoanStep2Title': MessageLookupByLibrary.simpleMessage(
            'Receive a loan offer from Wio and select your borrowing amount'),
        'securedLoanStep3Description': MessageLookupByLibrary.simpleMessage(
            'To receive the funds, you’ll need to provide an assignment letter from your acquirer confirming that payments are settled into your Wio account.'),
        'securedLoanStep3Title': MessageLookupByLibrary.simpleMessage(
            'Share a valid assignment letter to receive the funds'),
        'securedLoanWhatIsAssignmentLetter':
            MessageLookupByLibrary.simpleMessage(
                'What is the assignment letter?'),
        'selectFileBorrowingPowerTitle':
            MessageLookupByLibrary.simpleMessage('Select file'),
        'selectFileFinancialStatementTitle':
            MessageLookupByLibrary.simpleMessage(
                'Select Audited Financial Statement'),
        'simpleBusinessLoanCardFeature1': m106,
        'simpleBusinessLoanCardFeature2': m107,
        'simpleBusinessLoanCardFeature3':
            MessageLookupByLibrary.simpleMessage('No collateral required'),
        'simpleBusinessLoanCardSubtitle': MessageLookupByLibrary.simpleMessage(
            'No extra guarantees, every type of business can apply.'),
        'simpleBusinessLoanCardTitle':
            MessageLookupByLibrary.simpleMessage('Simple business loan'),
        'startDateLabel': MessageLookupByLibrary.simpleMessage('Start date'),
        'timePeriodLabel': MessageLookupByLibrary.simpleMessage('Time period'),
        'totalOutstandingLabel':
            MessageLookupByLibrary.simpleMessage('Current outstanding'),
        'unSecuredBusinessLoanLabel':
            MessageLookupByLibrary.simpleMessage('Simple loan'),
        'unsecuredLoanSelectionDescription': MessageLookupByLibrary.simpleMessage(
            'No extra guarantees required every type of business can apply.'),
        'unsecuredLoanSelectionTitle':
            MessageLookupByLibrary.simpleMessage('Simple loan'),
        'updateCreditLimitRequestMoreSuccessButton':
            MessageLookupByLibrary.simpleMessage('Got it'),
        'vatStatementSampleTitle':
            MessageLookupByLibrary.simpleMessage('VAT statement'),
        'viewOfferLabel': MessageLookupByLibrary.simpleMessage('View offer'),
        'wioBusinessLoanLabel':
            MessageLookupByLibrary.simpleMessage('Wio Business loan'),
        'wioLineOfCreditLabel':
            MessageLookupByLibrary.simpleMessage('Wio line of credit')
      };
}
