// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.
// @dart=2.12
// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = MessageLookup();

typedef String? MessageIfAbsent(String? messageStr, List<Object>? args);

class MessageLookup extends MessageLookupByLibrary {
  @override
  String get localeName => 'ar';

  static m0(amount) =>
      "هناك مبلغ مستحق قدره ${amount} في حسابك. يجب عليك سداد المبلغ بالكامل لإغلاق رصيدك.";

  static m1(amount) =>
      "المبلغ ${amount} معلق. انتظر حتى تكتمل المعاملات وحاول مرة أخرى لاحقًا.";

  static m2(date) => "انتهت في ${date}";

  static m3(feePercentage) =>
      "مبلغ الرسوم الذي تبلغه ${feePercentage} % اعتبارًا من اليوم";

  static m4(feeFreeMonthCount) =>
      "إذا كنت لا تزال ضمن الفترة المجانية التي ${feeFreeMonthCount} شهرًا، فلن يتم تحصيل رسوم منك.";

  static m5(repaymentPercentage) =>
      "${repaymentPercentage} % من الأموال التي تم إنفاقها";

  static m6(date) => "حتى ${date}";

  static m7(date) =>
      "قد تستمر في الإنفاق قبل تاريخ الدفع التلقائي. سيتم احتساب مبلغ الدفع التلقائي النهائي الخاص بك في: ${date} .";

  static m8(companyName) => "هل لديك القدرة على الاقتراض لـ ${companyName}";

  static m9(companyName) =>
      "تأكد من أن لديك القدرة على الاقتراض لـ ${companyName}";

  static m10(monthCount) => "${monthCount} شهرًا";

  static m11(userName) => "مرحبًا ${userName}! إليك عرض القرض الخاص بك";

  static m12(interest) => "إجمالي الفائدة ( ${interest} % سنويا)";

  static m13(processingFee) =>
      "رصيد حساب Wio الخاص بك منخفض للغاية. يرجى التأكد من أن لديك ${processingFee} على الأقل لتغطية رسوم المعالجة لمرة واحدة ومواصلة القرض.";

  static m14(maxRate, minRate) =>
      "سيتم تطبيق الفائدة بمعدل فائدة سنوي يتراوح من ${minRate} % إلى ${maxRate} %. ";

  static m15(lateFeeGracePeriodInDays, latePaymentFee) =>
      "سيتم تطبيق رسوم تأخير الدفع بقيمة ${latePaymentFee} إذا لم يتم الدفع خلال ${lateFeeGracePeriodInDays} يومًا بعد تاريخ استحقاقك.";

  static m16(posPartnerName) => "التفاصيل المرسلة إلى ${posPartnerName}";

  static m17(email) =>
      "لبدء استخدام خط الائتمان الخاص بك، اتبع التعليمات المرسلة إلى ${email} لاستكمال رهن نقطة البيع الخاصة بك.";

  static m18(minimumRepaymentAmout) =>
      "لقد تم قفل رصيدك لأنك\n لم تدفع الحد الأدنى للمبلغ المستحق. ل\n إلغاء القفل يرجى دفع الحد الأدنى من ${minimumRepaymentAmout} .";

  static m19(day) => "${day} من كل شهر";

  static m20(monthCount) => "${monthCount} شهرًا";

  static m21(feePercentage) =>
      "يتم تحديد الرسوم الخاصة بك عن طريق تطبيق ${feePercentage} % على المبلغ المرحل، وهو رصيدك الحالي المستحق بعد السداد.";

  static m22(date) => "لا توجد رسوم حتى ${date}";

  static m23(amount) => "*الحد الأدنى للدفع لتجنب الرسوم المتأخرة: ${amount}";

  static m24(date) => "الدفع التلقائي التالي سيكون في ${date}";

  static m25(creditLoanAmount) => "من ${creditLoanAmount}";

  static m26(amount) => "الحد الائتماني الجديد الخاص بك هو ${amount}";

  static m27(date) => "الدفع التلقائي التالي في ${date}";

  static m28(date) => "تاريخ الاستحقاق ${date}";

  static m29(amount) => "أخذنا ${amount}";

  static m30(date) => "آخر دفع تلقائي ، ${date}";

  static m31(amount, currency) =>
      "لقد أخذنا مبلغًا أقرب إلى دفعتك وقدره ${amount} لأنه لم يكن هناك ما يكفي من المال في حسابك ${currency} في تاريخ الدفع التلقائي.";

  static m32(amount) => "+ رسوم ${amount}";

  static m33(date) =>
      "لقد انتهت فترة الإعفاء من الرسوم في ${date} . يمكنك استئناف فترة الإعفاء من الرسوم لمدة تصل إلى 60 يومًا في كل مرة تقوم فيها بسداد رصيدك المستحق بالكامل.";

  static m34(feePercentage, minumumPayPercentage, feeFreeMonthCount, penalty) =>
      "لا توجد رسوم سنوية - يتم تضمين رصيد Wio في خطة التسعير الخاصة بك. \n\n لا توجد رسوم ترحيل شهرية إذا كنت تدفع كامل المبلغ المستحق كل شهر دون ترحيل أي رصيد إلى الشهر التالي. \n\n يتم تطبيق رسوم الترحيل الشهرية بنسبة ${feePercentage} إذا كنت تدفع على الأقل ${minumumPayPercentage} % من رصيدك المستحق. يتم احتساب هذه الرسوم كنسبة مئوية ثابتة من المبلغ المرحل، وهو رصيدك الحالي المستحق بعد السداد. \n\n قم بسداد رصيد شهر واحد بالكامل واستمتع بفترة مجانية تصل إلى ${feeFreeMonthCount} أشهر. \n\n ${penalty} يتم تطبيق رسوم العقوبة عندما تفشل في سداد ما لا يقل عن ${minumumPayPercentage} % من رصيدك المستحق خلال 6 أيام بعد الموعد النهائي للسداد.";

  static m35(minumumPayPercentage, feeFreeMonthCount) =>
      "1. ادفع ما يصل إلى ${minumumPayPercentage} % من رصيدك المستحق وقم بترحيل المبلغ المتبقي إلى الفترة التالية دون تحمل أي رسوم. \n\n 2. يمكنك ترحيل رصيدك بدون أي رسوم لدورتين متتاليتين للفوترة، وسيتم تحصيل الرسوم فقط ابتداءً من الدورة الثالثة. يتيح لك هذا الاستمتاع بما يصل إلى ${feeFreeMonthCount} شهرًا من الرصيد بدون رسوم! \n\n 3. بعد انتهاء فترة الإعفاء من الرسوم، سيتم إعادة تشغيلها في كل مرة تقوم فيها بسداد رصيدك المستحق بالكامل. \n\n ألق نظرة على هذا المثال العملي:\n إذا كان تاريخ السداد الخاص بك هو اليوم الأول من كل شهر وأنفقت 1000 درهم في الثاني من يونيو، فستحتاج إلى سداد الحد الأدنى فقط ( ${minumumPayPercentage} ٪ من 1000 درهم) لتجنب الرسوم، يجب سداد المبلغ بالكامل بحلول اليوم الأول. من أغسطس. يمنحك هذا شهري يونيو ويوليو كفترة خالية من الرسوم.";

  static m36(feePercentage) =>
      "استمتع برسوم ${feePercentage} % على رصيدك المستحق، وبدون رسوم إذا قمت بسداد نفقاتك بالكامل.";

  static m37(feePercentage) =>
      "- استخدم مع أي من بطاقاتك المادية أو الافتراضية. \n\n - اختر تاريخ السداد والمبلغ مع خيار الدفع التلقائي المخصص. \n\n - تتضمن كشوفات نهاية الشهر نفقاتك بدون فترات كشف حساب معقدة. \n\n - رصيد Wio مجاني ولا تدفع سوى رسوم ثابتة بنسبة ${feePercentage} على المبلغ المرحل الخاص بك.";

  static m38(minumumPayPercentage, feeFreeMonthCount) =>
      "ادفع ما يصل إلى ${minumumPayPercentage} % من المبلغ المستحق واستمتع بما يصل إلى ${feeFreeMonthCount} شهرًا بدون رسوم.";

  static m39(feeFreeMonthCount) =>
      "احصل على رصيد بدون رسوم يصل إلى ${feeFreeMonthCount} شهرًا";

  static m40(feeFreeMonthCount, minumumPaymentPercentage) =>
      "ادفع ما يصل إلى ${minumumPaymentPercentage} % من المبلغ المستحق واستمتع بما يصل إلى ${feeFreeMonthCount} شهرًا بدون رسوم.";

  static m41(feeFreeMonthCount) =>
      "احصل على رصيد بدون رسوم يصل إلى ${feeFreeMonthCount} شهرًا";

  static m42(feePercentage) =>
      "استمتع برسوم ${feePercentage} % على رصيدك المستحق، وبدون رسوم إذا قمت بسداد نفقاتك بالكامل.";

  static m43(date) =>
      "إجمالي المبلغ المستحق اعتبارًا من\n ${date} (بما في ذلك الرسوم):";

  static m44(amount) =>
      "يتم فرض رسوم تأخير السداد عليك بقيمة ${amount} . لإلغاء قفل رصيدك، قم بسداد الدفعة المتأخرة الآن.";

  static m45(date) =>
      "لتجنب أي رسوم إضافية، يرجى إجراء الدفع المتأخر قبل ${date} .";

  static m46(percentage) => "${percentage} % من الأموال التي تم إنفاقها";

  static m47(date) => "${date} من كل شهر";

  static m48(fee, payback, spent) =>
      "إذا أنفقت ${spent} وقمت بسداد ${payback} ، فسيتم تحصيل ${fee} منك";

  static m49(fee, spent) => "إذا أنفقت ${spent} ، فسوف تسدد ${fee} بدون رسوم.";

  static m50(percentage) => "${percentage} % من الأموال التي تم إنفاقها";

  static m51(supportedFileSizeInMb, supportedFileTypes) =>
      "${supportedFileTypes} الحد الأقصى ${supportedFileSizeInMb} ميجابايت";

  static m52(annualInterest) =>
      "سيتم تطبيق رسوم النقل (الفائدة) بمعدل فائدة سنوي يصل إلى ${annualInterest} %.";

  static m53(minThresholdPercentage) =>
      "يمكنك التنازل عن رسوم الترحيل لمدة شهرين متتاليين عن طريق دفع ما لا يقل عن ${minThresholdPercentage} % من الرصيد.";

  static m54(amount) => "هل تريد طلب أكثر من ${amount} ؟";

  static m55(amount) =>
      "تم تحديث الحد الائتماني الخاص بك بنجاح. الحد الائتماني الجديد الخاص بك هو ${amount}";

  static m56(nextStatement, required) =>
      "تحميل البيان ${nextStatement} من ${required}";

  static m57(required, x) => "تم تحميل ${x} من الكشوفات ${required}";

  static m58(amount) =>
      "أنت مؤهل للحصول على ما يصل إلى ${amount} نقدًا سريعًا. سيتم تحويل الأموال إلى حسابك بالدرهم الإماراتي على الفور.";

  static m59(date) => "مستحق في ${date}";

  static m60(repaymentPercentage) =>
      "${repaymentPercentage} % مبلغ الدفع التلقائي:";

  static m61(date) =>
      "سيتم تطبيق الرسوم الكاملة على المبلغ المرحل الخاص بك بعد ${date} . سيتم استئناف فترة الإعفاء من الرسوم في كل مرة تقوم فيها بسداد رصيدك المستحق بالكامل.";

  static m62(size) =>
      "يبدو أن الملف كبير جدًا. الحد الأقصى لحجم الملف هو ${size} ميغابايت.";

  static m63(holdAmount) =>
      "مبلغ ${holdAmount} معلق. ويمكن سدادها بمجرد الانتهاء من المعاملات. يتعلم أكثر";

  static m64(day) => "${day} من كل شهر";

  static m65(date, month) => "الدفع التلقائي التالي في ${date} من ${month}";

  static m66(percentage) => "${percentage} من الأموال المستخدمة";

  static m67(amount, fee) => "${amount} ورسوم ${fee} اعتبارًا من اليوم";

  static m68(amount) => "${amount} اعتبارًا من اليوم";

  static m69(creditLimit, spent) => "أنفق ${spent} من ${creditLimit}";

  static m70(date) => "سيتم الدفع التلقائي التالي في ${date}";

  static m71(amount) => "الرصيد: ${amount}";

  static m72(currency) => "${currency} الحساب";

  static m73(amount) => "لا يمكن أن يكون المبلغ أقل من الحد الأدنى: ${amount}";

  static m74(amount) => "المبلغ يتجاوز المبلغ الكامل: ${amount}";

  static m75(amount) => "المبلغ الكامل: ${amount}";

  static m76(amount) => "المبلغ يتجاوز رصيدك: ${amount}";

  static m77(amount) => "الحد الأدنى ${amount}";

  static m78(amount, currency) =>
      "لقد دفعت ${amount}\n إلى الائتمان من حساب ${currency}";

  static m79(balance) => "متاح للإقتراض ${balance}";

  static m80(preBorrowDailyPaymentAmount) =>
      "سيتم تطبيق مبلغ الدفع اليومي الجديد اعتبارًا من الغد. ستظل تدفع اليوم ${preBorrowDailyPaymentAmount}.";

  static m81(loanTerm) =>
      "سيتم إضافة هذا المبلغ إلى رصيدك المستحق الحالي اعتبارًا من اليوم، مع سداد المبلغ المستحق خلال ${loanTerm} الأيام القادمة.";

  static m82(currency) => "تم تحويل الأموال إلى حساب ${currency}";

  static m83(date) => "المبلغ المتأخر اعتبارًا من \n ${date}";

  static m84(date) => "المبلغ الإجمالي المستحق في ${date}";

  static m85(email) =>
      "لقد تم إرسال شهادة عدم الممانعة لإطلاق نقطة البيع إلى ${email}";

  static m86(amount, currency) =>
      "لقد دفعت ${amount}\n إلى رصيد نقاط البيع من حساب ${currency} \n\n شكرا على السداد المبكر!";

  static m87(borrowedAmount, totalLoanAmount) =>
      "تم اقتراض ${borrowedAmount} من ${totalLoanAmount}";

  static m88(interestRate, loanPeriod) =>
      "نقوم بحساب الفائدة يوميًا بناءً على رصيدك الأساسي المستحق. معدل الفائدة الخاص بك لفترة ${loanPeriod} يومًا هو ${interestRate} %";

  static m89(loanPeriod) =>
      "إذا قمت بالسداد قبل ${loanPeriod} يومًا، فسوف تدفع فقط الفائدة المتراكمة حتى ذلك اليوم.";

  static m90(max, min) =>
      "يتراوح معدل الفائدة من ${min} % إلى ${max} % سنويًا، اعتمادًا على تاريخك الائتماني وتقرير مكتب الائتمان وبيانات نقاط البيع التاريخية.";

  static m91(days) =>
      "عند إجراء سحب جديد، يتم إضافته إلى المبلغ المستحق الحالي. سيتم سداد المبلغ المستحق الإجمالي المحدث خلال الأيام ${days} التالية، بدءًا من تاريخ السحب الجديد، من خلال سداد يومي. يمكنك سحب الأموال حسب الحاجة، حتى عام واحد من تاريخ الموافقة على حد الائتمان الخاص بك.";

  static m92(days) =>
      "يمكنك الوصول إلى الأموال في أي وقت وسدادها خلال ${days} يومًا من تاريخ السحب الجديد. يمكنك السحب حسب الحاجة، حيث يضيف كل سحب إلى إجمالي رصيدك المستحق.";

  static m93(loanTerm) => "${loanTerm} -فائدة يومية";

  static m94(latePaymentFee, missedMilestoneDueDate) =>
      "لقد فاتتك سداد قرضك المستحق في ${missedMilestoneDueDate} ، ونتيجة لذلك، تم قفل رصيدك الائتماني حاليًا. يتم فرض رسوم تأخير السداد عليك بقيمة ${latePaymentFee} . لفتح الحد الأقصى، قم بسداد الدفعة المتأخرة الآن.";

  static m95(minimumBorrowAmount) =>
      "الحد الأدنى للمبلغ الذي يمكن اقتراضه هو ${minimumBorrowAmount}";

  static m96(dateToAvoidAdditionalCharges, missedMilestoneDueDate) =>
      "لقد تأخرت عن سداد قرضك المستحق في ${missedMilestoneDueDate} ، ونتيجة لذلك، تم قفل رصيدك الائتماني حاليًا. لتجنب أي رسوم إضافية، يرجى سداد الدفعة المتأخرة بحلول ${dateToAvoidAdditionalCharges} .";

  static m97(date) =>
      "سيتم ترحيل المبلغ إلى دفعتك اليومية التالية. لتجنب قفل الائتمان، يرجى سداد دفعتك اليومية المتأخرة حتى ${date}";

  static m98(missedPaymentCount) =>
      "${Intl.plural(missedPaymentCount, one: ' لقد فاتتك دفعة يومية واحدة ', other: ' لقد فاتتك ${missedPaymentCount} دفعة يومية ')}";

  static m99(minAmount) =>
      "يمكنك الاقتراض بقدر ما تريد، بدءًا من ${minAmount} وحتى الحد الأقصى الخاص بك. ";

  static m100(loanTerm) =>
      "مدة القرض هي ${loanTerm} يومًا مع الحد الأدنى للمبلغ الذي يجب سداده كل شهر في 3 مراحل.";

  static m101(loanTerm) =>
      "يتم تطبيق الفائدة يوميًا، لذلك إذا قمت بالسداد قبل ${loanTerm} يومًا، فسوف تدفع الفائدة حتى ذلك اليوم فقط.";

  static m102(date) => "المستحق في ${date}";

  static m103(maxLimit) => "اقتراض حتى ${maxLimit}";

  static m104(maxInterest, minInterest) =>
      "الفائدة ${minInterest} - ${maxInterest} %";

  static m105(email) =>
      "للبدء في استخدام قرض عملك، اتبع التعليمات المرسلة إلى ${email} لإكمال عملية تعيين التسوية الخاصة بك.";

  static m106(maxLimit) => "اقتراض حتى ${maxLimit}";

  static m107(maxInterest, minInterest) =>
      "الفائدة ${minInterest} - ${maxInterest} %";

  @override
  final Map<String, dynamic> messages =
      _notInlinedMessages(_notInlinedMessages);

  static Map<String, dynamic> _notInlinedMessages(_) => {
        'accountClosureConfirmationCancel':
            MessageLookupByLibrary.simpleMessage('الحفاظ على حساب الائتمان'),
        'accountClosureConfirmationConfirm':
            MessageLookupByLibrary.simpleMessage('إغلاق حساب الائتمان'),
        'accountClosureConfirmationDescription':
            MessageLookupByLibrary.simpleMessage(
                'بعد إغلاق حسابك، سيتم تحويل جميع بطاقاتك إلى \"أموالي\" تلقائيًا. يمكنك التقدم بطلب للحصول على حساب ائتماني جديد لاحقًا.'),
        'accountClosureConfirmationTitle': MessageLookupByLibrary.simpleMessage(
            'هل أنت متأكد أنك تريد إغلاق حساب الائتمان الخاص بك؟'),
        'accountClosureOutstandingRepaymentCta':
            MessageLookupByLibrary.simpleMessage('سداد كامل'),
        'accountClosureOutstandingRepaymentDescription': m0,
        'accountClosureOutstandingRepaymentTitle':
            MessageLookupByLibrary.simpleMessage('السداد المستحق'),
        'accountClosurePendingTransactionDescription': m1,
        'accountClosurePendingTransactionTitle':
            MessageLookupByLibrary.simpleMessage('المعاملات المعلقة'),
        'accountClosureSuccessCta':
            MessageLookupByLibrary.simpleMessage('العودة إلى لوحة القيادة'),
        'accountClosureSuccessSubtitle': MessageLookupByLibrary.simpleMessage(
            'حساب الائتمان الخاص بك مغلق.'),
        'accountClosureSuccessTitle':
            MessageLookupByLibrary.simpleMessage('تم التنفيذ!'),
        'activateBusinessCreditCardInfo': MessageLookupByLibrary.simpleMessage(
            'قم بتحويل أي من بطاقات Wio Business الخاصة بك إلى بطاقة ائتمان!'),
        'activateCreditCardBSFooterText': MessageLookupByLibrary.simpleMessage(
            'من خلال تحديد \"تفعيل الآن\"، فإنك توافق على\n الشروط والأحكام وبيان الحقائق الأساسية'),
        'activateCreditCardBottomsheetSkipCta':
            MessageLookupByLibrary.simpleMessage('تخطي الآن'),
        'activateCreditCardBottomsheetSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'قم بتقديم طلب للحصول على بطاقة Wio Credit وتحويل أي من بطاقات Wio Business الخاصة بك إلى بطاقة ائتمان.'),
        'activateCreditCardBottomsheetTitle':
            MessageLookupByLibrary.simpleMessage(
                'هل ترغب أيضًا في تفعيل رصيد Wio Business الخاص بك؟'),
        'activateNowCta': MessageLookupByLibrary.simpleMessage('نشط الآن'),
        'applyCreditBannerTitle': MessageLookupByLibrary.simpleMessage(
            'قم بتحويل أي من بطاقات Wio Business الخاصة بك إلى بطاقة ائتمان.'),
        'applyCreditHeader': MessageLookupByLibrary.simpleMessage(
            'التقدم بطلب للحصول على رصيد Wio للأعمال'),
        'applyNowCta': MessageLookupByLibrary.simpleMessage('قدم الآن'),
        'assignmentLetterBottomSheetDescription':
            MessageLookupByLibrary.simpleMessage(
                'خطاب التنازل هو وثيقة صادرة عن مزود منصة الدفع (مثل Network International، وPayTabs، وStripe، وغيرها) تؤكد تحويل عائدات مبيعاتك - سواءً عبر منصتهم أو أجهزة نقاط البيع - إلى حساب Wio الخاص بك طوال مدة التسهيل الائتماني. \n \n كيفية الحصول على خطاب التنازل: بمجرد الموافقة على عرض الائتمان، سنرسل إليك اتفاقية الاقتراض عبر البريد الإلكتروني. يمكنك بعد ذلك إعادة توجيه هذه الاتفاقية إلى معالج الدفع أو الجهة المُصدرة - إما عبر قناة الدعم الخاصة بهم أو بالتواصل مع مدير حسابك. بناءً على ذلك، سيصدرون خطاب التنازل.'),
        'assignmentLetterBottomSheetDisclaimer':
            MessageLookupByLibrary.simpleMessage(
                'ما سبق هو عملية إرشادية. قد يتبع مزود منصة الدفع الخاص بك إجراءً مختلفًا، وقد تختلف الجداول الزمنية أو المتطلبات بناءً على سياساته الداخلية.'),
        'assignmentLetterBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('ما هي رسالة التكليف؟'),
        'autopayFeeCalculationEditAutopaySettings':
            MessageLookupByLibrary.simpleMessage(
                'تعديل إعدادات الدفع التلقائي'),
        'autopayFeeCalculationEndedAt': m2,
        'autopayFeeCalculationFaq':
            MessageLookupByLibrary.simpleMessage('التعليمات'),
        'autopayFeeCalculationFeeAmountToday': m3,
        'autopayFeeCalculationFeeAmountTodayDescription':
            MessageLookupByLibrary.simpleMessage(
                'يتم احتساب الرسوم الخاصة بك بناءً على المبلغ المستحق عليك وما إذا كنت في فترة مجانية.'),
        'autopayFeeCalculationFeeFreePeriod':
            MessageLookupByLibrary.simpleMessage('الفترة المجانية الخاصة بك'),
        'autopayFeeCalculationFeeFreePeriodDescription': m4,
        'autopayFeeCalculationHowAutopayCalculated':
            MessageLookupByLibrary.simpleMessage(
                'كيف يتم حساب مبلغ ورسوم الدفع التلقائي'),
        'autopayFeeCalculationPercentageOfSpentMoney': m5,
        'autopayFeeCalculationSpentAsOfToday':
            MessageLookupByLibrary.simpleMessage('قضيت اعتبارا من اليوم:'),
        'autopayFeeCalculationSpentDescription':
            MessageLookupByLibrary.simpleMessage(
                'أولاً، ننظر إلى المبلغ الذي أنفقته من Wio Business Credit.'),
        'autopayFeeCalculationStillHaveQuestions':
            MessageLookupByLibrary.simpleMessage('لا تزال لديك أسئلة؟'),
        'autopayFeeCalculationTillDate': m6,
        'autopayFeeCalculationWhyAsOfToday':
            MessageLookupByLibrary.simpleMessage('لماذا \"اعتبارا من اليوم\"؟'),
        'autopayFeeCalculationWhyAsOfTodayAnswer': m7,
        'autopayFeeCalculationYourAmount': MessageLookupByLibrary.simpleMessage(
            'مبلغ الدفع التلقائي الخاص بك اعتبارًا من اليوم:'),
        'autopayFeeCalculationYourAmountDescription':
            MessageLookupByLibrary.simpleMessage(
                'يتم حساب مبلغ الدفع التلقائي الخاص بك بناءً على المبلغ الذي تنفقه والمبلغ الذي قمت بإعداده للسداد.'),
        'autopayFeeCalculationYourAutopaySettings':
            MessageLookupByLibrary.simpleMessage(
                'إعدادات الدفع التلقائي الخاصة بك:'),
        'autopayFeeCalculationYourAutopaySettingsDescription':
            MessageLookupByLibrary.simpleMessage(
                'بعد ذلك، ننظر إلى المبلغ الذي قمت بإعداده لسداده. يمكنك تغييره في أي وقت.'),
        'autopayFeeCalculationYourOutstandingBalance':
            MessageLookupByLibrary.simpleMessage('رصيدك المتميز'),
        'autopayFeeCalculationYourOutstandingBalanceDescription':
            MessageLookupByLibrary.simpleMessage(
                'لحساب الرسوم، فإننا ننظر إلى مقدار المبلغ المستحق عليك بعد قيامك بسداد دفعتك الشهرية.'),
        'borrowCta': MessageLookupByLibrary.simpleMessage('يستعير'),
        'borrowingPowerBSCancel': MessageLookupByLibrary.simpleMessage('لا'),
        'borrowingPowerBSConfirm': MessageLookupByLibrary.simpleMessage('نعم'),
        'borrowingPowerBSDescription': MessageLookupByLibrary.simpleMessage(
            'يمكن فقط للموقّعين المخولين الذين لديهم سلطة الاقتراض التقدم بطلب للحصول على ائتمان أعمال Wio'),
        'borrowingPowerBSTitle': m8,
        'borrowingPowerDocumentUploadPageDescription':
            MessageLookupByLibrary.simpleMessage(
                'يرجى تحميل أحد المستندات التالية التي تنص بوضوح على أن لديك القدرة على الاقتراض لشركتك:'),
        'borrowingPowerDocumentUploadPageInstruction1':
            MessageLookupByLibrary.simpleMessage('تفويض'),
        'borrowingPowerDocumentUploadPageInstruction2':
            MessageLookupByLibrary.simpleMessage('مذكرة التأسيس'),
        'borrowingPowerDocumentUploadPageTitle': m9,
        'borrowingPowerUploadDocumentBoxSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'PDF، JPG، PNG بحد أقصى 50 ميجابايت'),
        'borrowingPowerUploadDocumentBoxTitle':
            MessageLookupByLibrary.simpleMessage('تحميل المستند'),
        'borrowingPowerWarningMessage': MessageLookupByLibrary.simpleMessage(
            'يمكن فقط للموقّعين المخولين الذين لديهم سلطة الاقتراض التقدم بطلب للحصول على ائتمان أعمال Wio'),
        'businessCreditCardLabel':
            MessageLookupByLibrary.simpleMessage('بطاقة ائتمان تجارية'),
        'businessLoan': MessageLookupByLibrary.simpleMessage('قرض تجاري'),
        'businessLoanApplicationSubmittedSuccessDescription':
            MessageLookupByLibrary.simpleMessage('سوف يكون في حسابك قريبا'),
        'businessLoanApplicationSubmittedSuccessSubtitle':
            MessageLookupByLibrary.simpleMessage('أموالك في طريقها!'),
        'businessLoanApplicationSubmittedSuccessTitle':
            MessageLookupByLibrary.simpleMessage('منتهي'),
        'businessLoanApplyBannerDescription': MessageLookupByLibrary.simpleMessage(
            'مرن ومصمم خصيصًا لك. تقدم الآن لمعرفة المبلغ الذي يمكنك اقتراضه.'),
        'businessLoanBorrowLoanTerm': m10,
        'businessLoanBorrowSubtitle': MessageLookupByLibrary.simpleMessage(
            'اختر مدة القرض الخاص بك، وحدد المبلغ، وتحقق من التفاصيل، واحصل على الأموال على الفور في حسابك المصرفي Wio Business.'),
        'businessLoanBorrowTitle': m11,
        'businessLoanFirstPaymentDate':
            MessageLookupByLibrary.simpleMessage('تاريخ الدفعة الأولى'),
        'businessLoanInterest': m12,
        'businessLoanLastPaymentDate':
            MessageLookupByLibrary.simpleMessage('تاريخ الدفع الأخير'),
        'businessLoanLearnMoreApply':
            MessageLookupByLibrary.simpleMessage('يتقدم'),
        'businessLoanLearnMoreFaq1Ans': MessageLookupByLibrary.simpleMessage(
            'ستدفع قسطًا شهريًا ثابتًا يغطي أصل القرض والفائدة. قد تُطبق رسوم معالجة القرض ورسوم السداد المبكر.'),
        'businessLoanLearnMoreFaq1Que':
            MessageLookupByLibrary.simpleMessage('ما هي التكاليف؟'),
        'businessLoanLearnMoreFaq2Ans': MessageLookupByLibrary.simpleMessage(
            'سيتعين عليك تقديم مستند ضريبة القيمة المضافة (إن وجد) ورقم IBAN لحسابات المبيعات الخاصة بك.'),
        'businessLoanLearnMoreFaq2Que':
            MessageLookupByLibrary.simpleMessage('ما هو المطلوب؟'),
        'businessLoanLearnMoreFaq3Ans': MessageLookupByLibrary.simpleMessage(
            'قد تُفرض غرامة تأخير، وقد يتأثر تقييمك الائتماني. احرص على السداد في الوقت المحدد لتجنب العقوبات.'),
        'businessLoanLearnMoreFaq3Que': MessageLookupByLibrary.simpleMessage(
            'ماذا سيحدث إذا فاتني السداد؟'),
        'businessLoanLearnMoreFeature1Desc':
            MessageLookupByLibrary.simpleMessage(
                'عملك يستحق حلاً مناسبًا، ونحن هنا لتحقيق ذلك'),
        'businessLoanLearnMoreFeature1Title':
            MessageLookupByLibrary.simpleMessage('مصممة حسب القرض'),
        'businessLoanLearnMoreFeature2Desc': MessageLookupByLibrary.simpleMessage(
            'يتم تحويل الأموال مباشرة إلى حساب Wio Business الخاص بك وتكون جاهزة للإنفاق'),
        'businessLoanLearnMoreFeature2Title':
            MessageLookupByLibrary.simpleMessage('المال مباشرة في حسابك'),
        'businessLoanLearnMoreFeature3Desc': MessageLookupByLibrary.simpleMessage(
            'اختر مدة القرض من 3 إلى 48 شهرًا، ثم قم بالسداد بأقساط شهرية تلقائية'),
        'businessLoanLearnMoreFeature3Title':
            MessageLookupByLibrary.simpleMessage('سداد مرن وسهل'),
        'businessLoanLearnMoreHowItWorksDesc': MessageLookupByLibrary.simpleMessage(
            'قم بتقديم طلبك عبر التطبيق. عادة ما يستغرق الموافقة 3 أيام عمل. بعد الموافقة، يمكنك تعديل مبلغ القرض واختيار مدة السداد. '),
        'businessLoanLearnMoreSubtitle': MessageLookupByLibrary.simpleMessage(
            'مرن ومصمم خصيصًا لك. تقدم الآن لمعرفة المبلغ الذي يمكنك اقتراضه.'),
        'businessLoanLearnMoreTitle':
            MessageLookupByLibrary.simpleMessage('احصل على القرض الذي يناسبك'),
        'businessLoanLoanAmount':
            MessageLookupByLibrary.simpleMessage('مبلغ القرض'),
        'businessLoanLoanAmountDesc': MessageLookupByLibrary.simpleMessage(
            'هذا هو المبلغ الإجمالي الذي سيتم إضافته إلى حساب Wio Business الخاص بك. الحد الأقصى الذي يمكنك اقتراضه يعتمد على مدة القرض التي تختارها.'),
        'businessLoanMonthlyInstalment':
            MessageLookupByLibrary.simpleMessage('القسط الشهري'),
        'businessLoanOfferReadyMessage': MessageLookupByLibrary.simpleMessage(
            'عرض القرض الخاص بك جاهز! دعنا نلقي نظرة.'),
        'businessLoanOneTimeFee':
            MessageLookupByLibrary.simpleMessage('رسوم المعالجة لمرة واحدة'),
        'businessLoanProcessingFee':
            MessageLookupByLibrary.simpleMessage('رسوم المعالجة '),
        'businessLoanProcessingFeeDesc': MessageLookupByLibrary.simpleMessage(
            'هذه رسوم لمرة واحدة سيتم فرضها بمجرد اقتراض المبلغ المحدد وتوقيع اتفاقية القرض.'),
        'businessLoanProcessingFeeInsufficientBalance': m13,
        'businessLoanRejectedBannerDescription':
            MessageLookupByLibrary.simpleMessage(
                'لسوء الحظ، تم رفض طلبك للحصول على قرض الأعمال من Wio. شركتك لا تفي بجميع المعايير المطلوبة. يمكنك المحاولة مرة أخرى بعد ثلاثة أشهر.'),
        'businessLoanThingsToKnowFeatureFour':
            MessageLookupByLibrary.simpleMessage(
                'بمجرد تأكيد المعاملة، لا يمكن عكسها.'),
        'businessLoanThingsToKnowFeatureOne': m14,
        'businessLoanThingsToKnowFeatureThree': m15,
        'businessLoanThingsToKnowFeatureTwo': MessageLookupByLibrary.simpleMessage(
            'سيتم خصم الدفعات الشهرية تلقائيًا من حسابك الجاري و/أو حسابات التوفير.'),
        'businessLoanTotalAmountToRepay': MessageLookupByLibrary.simpleMessage(
            'المبلغ الإجمالي المطلوب سداده'),
        'cancelCta': MessageLookupByLibrary.simpleMessage('يلغي'),
        'cannotUpdateAutopayPercentageError':
            MessageLookupByLibrary.simpleMessage(
                'يرجى الحضور لاحقًا بعد استخدام بعض أموال الائتمان'),
        'channelFinanceAgreementBottomSheetCta':
            MessageLookupByLibrary.simpleMessage('انا اقبل'),
        'channelFinanceAgreementBottomSheetError':
            MessageLookupByLibrary.simpleMessage(
                'لقد حدث خطأ أثناء الحصول على الاتفاقية'),
        'closeCta': MessageLookupByLibrary.simpleMessage('يغلق'),
        'completeApplicationCta':
            MessageLookupByLibrary.simpleMessage('التطبيق الكامل'),
        'completeApplicationDescription': MessageLookupByLibrary.simpleMessage(
            'لم يتبق أمامك سوى بضع خطوات لإكمال طلب Wio Business Credit الخاص بك'),
        'completeApplicationTitle':
            MessageLookupByLibrary.simpleMessage('أكمل طلبك'),
        'completeBusinessLoanApplicationDescription':
            MessageLookupByLibrary.simpleMessage(
                'لم يتبق لك سوى بضع خطوات لإكمال طلب قرض الأعمال الخاص بك من Wio'),
        'completePosHypothecationPageResendEmail':
            MessageLookupByLibrary.simpleMessage(
                'إعادة إرسال البريد الإلكتروني'),
        'completePosHypothecationPageStep1':
            MessageLookupByLibrary.simpleMessage('تم توقيع اتفاقية الاقتراض'),
        'completePosHypothecationPageStep2': m16,
        'completePosHypothecationPageStep3':
            MessageLookupByLibrary.simpleMessage('رهن كامل لنقاط البيع'),
        'completePosHypothecationPageStep3Info':
            MessageLookupByLibrary.simpleMessage(
                'هذه الخطوة الأخيرة تضمن خط الائتمان الخاص بك وتتيح لك سداد الدفعات من خلال معاملات نقاط البيع الخاصة بك.'),
        'completePosHypothecationPageSubtitle': m17,
        'completePosHypothecationPageTitle':
            MessageLookupByLibrary.simpleMessage('اوشكت على الوصول !'),
        'completedRefreshLabel':
            MessageLookupByLibrary.simpleMessage('تم الاكتمال'),
        'confirmAutodebitAccountInformation': MessageLookupByLibrary.simpleMessage(
            'في حالة عدم وجود رصيد كاف، سيتم خصم الأموال من حسابات Wio Business AED الأخرى لسداد السداد'),
        'confirmAutodebitAccountSubtitle': MessageLookupByLibrary.simpleMessage(
            'سيقوم Wio تلقائيًا بخصم مدفوعاتك اليومية والمتأخرات المحتملة من هذا الحساب.'),
        'confirmAutodebitAccountTitle':
            MessageLookupByLibrary.simpleMessage('تأكيد الحساب للسحب والسداد'),
        'confirmAutodebitAutopayFromSSSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'اسمح لـ Wio بسحب الأموال من حسابات التوفير الخاصة بك إذا لم يكن لديك رصيد كافٍ في حساباتك الجارية بالدرهم الإماراتي'),
        'confirmAutodebitAutopayFromSSTitle':
            MessageLookupByLibrary.simpleMessage('سداد من توفير المساحة'),
        'confirmAutodebitSelectAccount':
            MessageLookupByLibrary.simpleMessage('حدد حساب'),
        'confirmCta': MessageLookupByLibrary.simpleMessage('يتأكد'),
        'continueCta': MessageLookupByLibrary.simpleMessage('يكمل'),
        'creditAccountLockedBannerCTA':
            MessageLookupByLibrary.simpleMessage('افتح رصيدك'),
        'creditAccountLockedBannerTitle': m18,
        'creditAcknowledgeRejectCta':
            MessageLookupByLibrary.simpleMessage('فهمتها'),
        'creditAgreementApplicationSubmitSuccessScreenCta':
            MessageLookupByLibrary.simpleMessage('أرني كيف'),
        'creditAgreementApplicationSubmitSuccessScreenDescription':
            MessageLookupByLibrary.simpleMessage(
                'من الآن فصاعدا، يمكنك الاختيار بسهولة بين الخصم أو الائتمان لإنفاقك.'),
        'creditAgreementApplicationSubmitSuccessScreenTitle':
            MessageLookupByLibrary.simpleMessage('تم فتح رصيد Wio التجاري!'),
        'creditAgreementBLBannerText': MessageLookupByLibrary.simpleMessage(
            'بمجرد التوقيع، لا يمكن عكس المعاملة'),
        'creditAgreementPageTitle':
            MessageLookupByLibrary.simpleMessage('اتفاقية الاقتراض'),
        'creditAgreementPosBannerText': MessageLookupByLibrary.simpleMessage(
            'بمجرد التأكيد، لا يمكن عكس المعاملة، حيث سنقوم بفتح حساب ائتماني فور الموافقة على طلبك.'),
        'creditAgreementSignCta': MessageLookupByLibrary.simpleMessage(
            'قم بالتوقيع عبر الرسائل القصيرة'),
        'creditAmountOnHoldInfoBottomSheetDescription':
            MessageLookupByLibrary.simpleMessage(
                'عندما تستخدم بطاقتك لإجراء الدفع، تبدأ المعاملة بحالة \"معلقة\". بعد أن تقوم بالدفع، يتحقق البائع من أن كل شيء على ما يرام، وعندما يتأكد، نقوم بتغيير الحالة إلى \"مكتمل\". يحدث هذا عادةً خلال 1-3 أيام بعد إجراء الدفع.'),
        'creditAmountOnHoldInfoBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('المعاملات المعلقة'),
        'creditAnnualTurnoverCancelCta':
            MessageLookupByLibrary.simpleMessage('يلغي'),
        'creditAnnualTurnoverConfirmCta':
            MessageLookupByLibrary.simpleMessage('يتأكد'),
        'creditAnnualTurnoverInputLabel':
            MessageLookupByLibrary.simpleMessage('المردود السنوي '),
        'creditAnnualTurnoverNextCta':
            MessageLookupByLibrary.simpleMessage('التالي'),
        'creditAnnualTurnoverPageDescription': MessageLookupByLibrary.simpleMessage(
            'معدل الدوران هو إجمالي الأرصدة التي تتوقع شركتك الحصول عليها عبر جميع الحسابات التي تحتفظ بها شركتك في دولة الإمارات العربية المتحدة.'),
        'creditAnnualTurnoverPageTitle': MessageLookupByLibrary.simpleMessage(
            'تأكد من هذا الرقم السنوي لمبيعات شركتك'),
        'creditAnnualTurnoverUpdateCta':
            MessageLookupByLibrary.simpleMessage('تحديث'),
        'creditAnnualTurnoverUpdateErrorToastMessage':
            MessageLookupByLibrary.simpleMessage(
                'عفوًا، لم نتمكن من تحديث معلوماتك. حاول مرة اخرى.'),
        'creditAnnualTurnoverUpdateSuccessToastMessage':
            MessageLookupByLibrary.simpleMessage('تم تحديث معلومات التداول!'),
        'creditApplicationRecapAccountBanner':
            MessageLookupByLibrary.simpleMessage(
                'سيتم إنشاء حساب ائتماني فورًا بعد الموافقة على طلبك'),
        'creditApplicationRecapAnnualTurnover':
            MessageLookupByLibrary.simpleMessage('المردود السنوي'),
        'creditApplicationRecapCompanyBankAccounts':
            MessageLookupByLibrary.simpleMessage('الحسابات البنكية للشركة'),
        'creditApplicationRecapErrorMessage':
            MessageLookupByLibrary.simpleMessage(
                'عفوًا، لم نتمكن من تحديث معلوماتك. حاول مرة اخرى.'),
        'creditApplicationRecapFiledAuditedFinancialStatements':
            MessageLookupByLibrary.simpleMessage('البيانات المالية المدققة'),
        'creditApplicationRecapFiledVatReports':
            MessageLookupByLibrary.simpleMessage(
                'تقارير ضريبة القيمة المضافة المقدمة'),
        'creditApplicationRecapFromPreviousPageDescription':
            MessageLookupByLibrary.simpleMessage(
                'يرجى مراجعة تفاصيلك للتأكد من أن كل شيء محدث.'),
        'creditApplicationRecapFromPreviousPageTitle':
            MessageLookupByLibrary.simpleMessage(
                'تحقق من معلوماتك وأرسل الطلب'),
        'creditApplicationRecapFromPreviousSubmit':
            MessageLookupByLibrary.simpleMessage('تأكيد وإرسال'),
        'creditApplicationRecapNoVatReporting':
            MessageLookupByLibrary.simpleMessage(
                'لا تقارير ضريبة القيمة المضافة'),
        'creditApplicationRecapOnlyWioBankAccount':
            MessageLookupByLibrary.simpleMessage('حساب Wio Business فقط'),
        'creditApplicationRecapPageDescription':
            MessageLookupByLibrary.simpleMessage(
                'أنت جاهز تمامًا، تحقق من التفاصيل وأرسل طلبك.'),
        'creditApplicationRecapPageTitle':
            MessageLookupByLibrary.simpleMessage('الآن، دعونا نراجع كل شيء'),
        'creditApplicationRecapPaybackDay': m19,
        'creditApplicationRecapPaybackDayTitle':
            MessageLookupByLibrary.simpleMessage('يوم سداد الشهر'),
        'creditApplicationRecapSubmit':
            MessageLookupByLibrary.simpleMessage('قدم الطلب'),
        'creditApplicationRecapSubmitBanner':
            MessageLookupByLibrary.simpleMessage(
                'بمجرد تقديم الطلب، لا يمكن التراجع عنه'),
        'creditApplicationRecapSuccessMessage':
            MessageLookupByLibrary.simpleMessage('معلومات محدثة !'),
        'creditApplicationRecapVatReportingAnnually':
            MessageLookupByLibrary.simpleMessage('سنويا'),
        'creditApplicationRecapVatReportingMethod':
            MessageLookupByLibrary.simpleMessage(
                'طريقة الإبلاغ عن ضريبة القيمة المضافة'),
        'creditApplicationRecapVatReportingMonthly':
            MessageLookupByLibrary.simpleMessage('شهريا'),
        'creditApplicationRecapVatReportingQuarterly':
            MessageLookupByLibrary.simpleMessage('ربعي'),
        'creditApplicationRepaymentPlanCta':
            MessageLookupByLibrary.simpleMessage('دعنا نذهب'),
        'creditApplicationRepaymentPlanDescription':
            MessageLookupByLibrary.simpleMessage(
                'تأكد من سداد الدفعات في الوقت المناسب لتلبية أهداف قرضك لتجنب الرسوم الإضافية.'),
        'creditApplicationRepaymentPlanInterest':
            MessageLookupByLibrary.simpleMessage('اهتمام'),
        'creditApplicationRepaymentPlanLoanAmount':
            MessageLookupByLibrary.simpleMessage('مبلغ القرض'),
        'creditApplicationRepaymentPlanLoanTermLabel':
            MessageLookupByLibrary.simpleMessage('مدة القرض'),
        'creditApplicationRepaymentPlanLoanTermValue': m20,
        'creditApplicationRepaymentPlanPeriodOrAmountNullError':
            MessageLookupByLibrary.simpleMessage(
                'يرجى التأكد من اختيار مبلغ القرض ومدته بنجاح.'),
        'creditApplicationRepaymentPlanProcessingFee':
            MessageLookupByLibrary.simpleMessage('رسوم المعالجة لمرة واحدة'),
        'creditApplicationRepaymentPlanTitle':
            MessageLookupByLibrary.simpleMessage(
                'رائع! إليك خطة السداد الخاصة بك'),
        'creditApplicationRepaymentPlanTotalPayback':
            MessageLookupByLibrary.simpleMessage('إجمالي السداد'),
        'creditApplicationSubmitSuccessPageCta':
            MessageLookupByLibrary.simpleMessage('منتهي'),
        'creditApplicationSubmitSuccessPageDescription':
            MessageLookupByLibrary.simpleMessage(
                'تم تقديم الطلب.\n سنراجع كل شيء ونخبرك بذلك خلال 2-3 أيام.'),
        'creditApplicationSubmitSuccessPageTitle':
            MessageLookupByLibrary.simpleMessage('مبروك!'),
        'creditAuditedFinancialStatementUploadScreenTitle':
            MessageLookupByLibrary.simpleMessage(
                'تحميل البيانات المالية المدققة لشركتك'),
        'creditAuditedStatementUploadScreenDescriptionDetailThree':
            MessageLookupByLibrary.simpleMessage(
                '1 سنة من النشاط المالي للشركة'),
        'creditAutoPayFeeCalculation': MessageLookupByLibrary.simpleMessage(
            'انظر كيف نحسب الرسوم الخاصة بك'),
        'creditAutoPayFeeCalculationDetailsBottomSheetFeeAmount':
            MessageLookupByLibrary.simpleMessage('مبلغ الرسوم:'),
        'creditAutoPayFeeCalculationDetailsBottomSheetSubtitle': m21,
        'creditAutoPayFeeCalculationDetailsBottomSheetTipText':
            MessageLookupByLibrary.simpleMessage(
                'قم بسداد 100% من الأموال التي أنفقتها هذا الشهر لاستئناف فترة الإعفاء من الرسوم.'),
        'creditAutoPayFeeCalculationDetailsBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage(
                'الرسوم الخاصة بك اعتبارا من اليوم'),
        'creditAutoPayFeeFreePeriodNoFee': m22,
        'creditAutoPayMinimumPaymentToAvoidFees': m23,
        'creditAutoPayNextPayment': m24,
        'creditAutoPaySetTo':
            MessageLookupByLibrary.simpleMessage('تم ضبط الدفع التلقائي على'),
        'creditAutoPayTotalDue':
            MessageLookupByLibrary.simpleMessage('الاجمالي المستحق'),
        'creditAutopayFromSavingSpaceConfirmationAccept':
            MessageLookupByLibrary.simpleMessage('أطفأ'),
        'creditAutopayFromSavingSpaceConfirmationCancel':
            MessageLookupByLibrary.simpleMessage('يحفظ'),
        'creditAutopayFromSavingSpaceConfirmationDescription':
            MessageLookupByLibrary.simpleMessage(
                'إذا قمت بإيقاف تشغيله، فقد يتم فرض رسوم عليك إذا لم يكن لديك ما يكفي من المال في حسابك الحالي بالدرهم الإماراتي في تاريخ الدفع التلقائي. \n\n يمكنك تغييره لاحقًا في \"الإدارة\"'),
        'creditAutopayFromSavingSpaceDescription':
            MessageLookupByLibrary.simpleMessage(
                'سيتم أخذ الأموال من مساحة التوفير الخاصة بك عندما لا يكون لديك ما يكفي من المال في حسابك الحالي بالدرهم الإماراتي.'),
        'creditAutopayFromSavingSpaceTitle':
            MessageLookupByLibrary.simpleMessage(
                'الدفع التلقائي من توفير المساحة'),
        'creditAutopayPercentageUpdateSuccessfully':
            MessageLookupByLibrary.simpleMessage(
                'تم تحديث نسبة سداد الدفع التلقائي بنجاح'),
        'creditBankAccountsNoOptionSelectedError':
            MessageLookupByLibrary.simpleMessage(
                'الرجاء تحديد خيار يصف بنك الأعمال الخاص بشركتك'),
        'creditBannerFooterText': m25,
        'creditCommonToastSomethingWentWrong':
            MessageLookupByLibrary.simpleMessage('هناك خطأ ما.'),
        'creditCompanyBankAccountInputScreenCtaText':
            MessageLookupByLibrary.simpleMessage('التالي'),
        'creditCompanyBankAccountOptionOtherBanks':
            MessageLookupByLibrary.simpleMessage(
                'لدى شركتي حسابات مصرفية أخرى في دولة الإمارات العربية المتحدة'),
        'creditCompanyBankAccountOptionSelectorTitle':
            MessageLookupByLibrary.simpleMessage(
                'أخبرنا عن حسابات شركتك المصرفية'),
        'creditCompanyBankAccountOptionWioOnly':
            MessageLookupByLibrary.simpleMessage(
                'Wio Business هو الحساب البنكي الوحيد لشركتي'),
        'creditCreditLimitReduceSuccessPageButtonTitle':
            MessageLookupByLibrary.simpleMessage('منتهي'),
        'creditCreditLimitReduceSuccessPageDescription': m26,
        'creditCreditLimitReduceSuccessPageTitle':
            MessageLookupByLibrary.simpleMessage('تم تخفيض الحد بنجاح'),
        'creditDashboardAutopayAmountLabel':
            MessageLookupByLibrary.simpleMessage('مبلغ الدفع التلقائي'),
        'creditDashboardAutopayEditButtonTitle':
            MessageLookupByLibrary.simpleMessage('تحرير الدفع التلقائي'),
        'creditDashboardAutopayFeeLabel':
            MessageLookupByLibrary.simpleMessage('مصاريف'),
        'creditDashboardAutopayNoFeeLabel':
            MessageLookupByLibrary.simpleMessage('بدون رسوم'),
        'creditDashboardAutopaySectionTitle': m27,
        'creditDashboardAvailableLimit':
            MessageLookupByLibrary.simpleMessage('الحد المتاح'),
        'creditDashboardAvailableToBorrow':
            MessageLookupByLibrary.simpleMessage('متاح للاقتراض'),
        'creditDashboardBorrow': MessageLookupByLibrary.simpleMessage('يستعير'),
        'creditDashboardBorrowed':
            MessageLookupByLibrary.simpleMessage('اقترضت، استعارت'),
        'creditDashboardCreditLockedTitle':
            MessageLookupByLibrary.simpleMessage('الائتمان مغلق'),
        'creditDashboardCreditTab':
            MessageLookupByLibrary.simpleMessage('رصيدي'),
        'creditDashboardDueDate': m28,
        'creditDashboardEasyCashTab':
            MessageLookupByLibrary.simpleMessage('نقد سريع'),
        'creditDashboardFeePerDay':
            MessageLookupByLibrary.simpleMessage('رسوم في اليوم الواحد'),
        'creditDashboardFees': MessageLookupByLibrary.simpleMessage('مصاريف'),
        'creditDashboardLastAutoPaidAmount': m29,
        'creditDashboardLastAutoPayDateLabel': m30,
        'creditDashboardLastAutoPayDescription': m31,
        'creditDashboardLastAutoPayFee': m32,
        'creditDashboardLatePaymentFee':
            MessageLookupByLibrary.simpleMessage('رسم تأخر دفعة'),
        'creditDashboardLockedCredit':
            MessageLookupByLibrary.simpleMessage('الائتمان مغلق'),
        'creditDashboardManageButtonTitle':
            MessageLookupByLibrary.simpleMessage('يدير'),
        'creditDashboardPageTitle':
            MessageLookupByLibrary.simpleMessage('الائتمان التجاري Wio'),
        'creditDashboardPayButtonTitle':
            MessageLookupByLibrary.simpleMessage('دفع الائتمان'),
        'creditDashboardPayback': MessageLookupByLibrary.simpleMessage('تسديد'),
        'creditDashboardRefreshError': MessageLookupByLibrary.simpleMessage(
            'عذرًا، لم نتمكن من تحديث لوحة التحكم. الرجاء معاودة المحاولة في وقت لاحق'),
        'creditDashboardSpentLabel':
            MessageLookupByLibrary.simpleMessage('قضى بالفعل'),
        'creditDashboardStatementButtonTitle':
            MessageLookupByLibrary.simpleMessage('إفادة'),
        'creditDashboardTakeMore':
            MessageLookupByLibrary.simpleMessage('خذ اكثر'),
        'creditDashboardToSpendLabel':
            MessageLookupByLibrary.simpleMessage('متاحة للقضاء'),
        'creditDashboardTotalDue':
            MessageLookupByLibrary.simpleMessage('الاجمالي المستحق'),
        'creditDashboardTransactionsSectionTitle':
            MessageLookupByLibrary.simpleMessage('المعاملات'),
        'creditDashboardYouBorrowed':
            MessageLookupByLibrary.simpleMessage('لقد اقترضت'),
        'creditFeeFreePeriodTooltipAcknowledgeCta':
            MessageLookupByLibrary.simpleMessage('حصلت عليه، وذلك بفضل!'),
        'creditFeeFreePeriodTooltipBody': m33,
        'creditFeeFreePeriodTooltipTitle': MessageLookupByLibrary.simpleMessage(
            'استأنف فترة الإعفاء من الرسوم'),
        'creditGenericErrorScreenCtaText':
            MessageLookupByLibrary.simpleMessage('يغلق'),
        'creditGenericErrorScreenDescription':
            MessageLookupByLibrary.simpleMessage(
                'حدث خطأ ما من جانبنا. الرجاء معاودة المحاولة في وقت لاحق.'),
        'creditGenericErrorScreenSubtitle':
            MessageLookupByLibrary.simpleMessage('لقد حدث خطأ ما'),
        'creditGenericErrorScreenTitle':
            MessageLookupByLibrary.simpleMessage('أُووبس!'),
        'creditGenericErrorScreenTryAgain':
            MessageLookupByLibrary.simpleMessage(
                'يرجى المحاولة مرة أخرى لاحقًا'),
        'creditIbanInputBottomSheetCtaText':
            MessageLookupByLibrary.simpleMessage('يضيف'),
        'creditIbanInputBottomSheetDescription':
            MessageLookupByLibrary.simpleMessage(
                'قد تتلقى إشعارًا من البنك الذي تتعامل معه. لا تقلق، أمن معلوماتك هو أولويتنا.'),
        'creditIbanInputBottomSheetErrorGeneric':
            MessageLookupByLibrary.simpleMessage(
                'رقم الحساب المصرفي الدولي غير صالح'),
        'creditIbanInputBottomSheetErrorInvalidLength':
            MessageLookupByLibrary.simpleMessage(
                'رقم IBAN الذي تم إدخاله له طول غير صالح'),
        'creditIbanInputBottomSheetErrorInvalidStructure':
            MessageLookupByLibrary.simpleMessage(
                'يحتوي رقم IBAN الذي تم إدخاله على بنية غير صالحة'),
        'creditIbanInputBottomSheetErrorNotUaeIban':
            MessageLookupByLibrary.simpleMessage(
                'يمكنك إدخال رقم الحساب المصرفي الدولي (IBAN) في دولة الإمارات العربية المتحدة فقط'),
        'creditIbanInputBottomSheetInputFieldLabel':
            MessageLookupByLibrary.simpleMessage('رقم الآيبان'),
        'creditIbanInputBottomSheetTitle': MessageLookupByLibrary.simpleMessage(
            'إضافة رقم الحساب البنكي IBAN'),
        'creditIneligibilityBottomsheetCta':
            MessageLookupByLibrary.simpleMessage('حصلت عليه، شكرا'),
        'creditLearnMoreCta':
            MessageLookupByLibrary.simpleMessage('يتعلم أكثر'),
        'creditLearnMoreCtaText':
            MessageLookupByLibrary.simpleMessage('قدم الآن'),
        'creditLearnMoreFaqTitle':
            MessageLookupByLibrary.simpleMessage('الأسئلة الشائعة'),
        'creditLearnMoreFirstFaq': MessageLookupByLibrary.simpleMessage(
            'ما هي تكاليف Wio Business Credit؟'),
        'creditLearnMoreFirstFaqAnswer': m34,
        'creditLearnMoreFirstFeatureDesc': MessageLookupByLibrary.simpleMessage(
            'قم بالتبديل بسهولة بين وضع الخصم والائتمان على أي من بطاقات Wio Business الخاصة بك.'),
        'creditLearnMoreFirstFeatureTitle':
            MessageLookupByLibrary.simpleMessage('بطاقة واحدة للخصم والائتمان'),
        'creditLearnMoreHowItWorks':
            MessageLookupByLibrary.simpleMessage('كيف تعمل'),
        'creditLearnMoreHowItWorksDesc': MessageLookupByLibrary.simpleMessage(
            'أرسل طلبك مباشرة على التطبيق. بعد الموافقة على التطبيق، قم بتخصيص تفضيلات السداد الخاصة بك وابدأ في الإنفاق عن طريق ضبط أي من بطاقات Wio Business الخاصة بك في وضع الإنفاق الائتماني.'),
        'creditLearnMorePageSubtitle': MessageLookupByLibrary.simpleMessage(
            'تقدم بطلب للحصول على رصيد Wio وقم بتحويل أي من بطاقات Wio Business الخاصة بك إلى بطاقة ائتمان.'),
        'creditLearnMorePageTitle':
            MessageLookupByLibrary.simpleMessage('الائتمان التجاري Wio'),
        'creditLearnMoreSecondFaq': MessageLookupByLibrary.simpleMessage(
            'كيف تعمل فترة الإعفاء من الرسوم؟'),
        'creditLearnMoreSecondFaqAnswer': m35,
        'creditLearnMoreSecondFeatureDesc': m36,
        'creditLearnMoreSecondFeatureTitle':
            MessageLookupByLibrary.simpleMessage('لا رسوم خفية'),
        'creditLearnMoreThirdFaq': MessageLookupByLibrary.simpleMessage(
            'كيف يكون Wio Business Credit أفضل من بطاقة الائتمان العادية؟'),
        'creditLearnMoreThirdFaqAnswer': m37,
        'creditLearnMoreThirdFeatureDesc': m38,
        'creditLearnMoreThirdFeatureTitle': m39,
        'creditLearnMoreWhatYouGet':
            MessageLookupByLibrary.simpleMessage('ما ستحصل عليه'),
        'creditLimitCondition1Subtitle': m40,
        'creditLimitCondition1Title': m41,
        'creditLimitCondition2Subtitle': m42,
        'creditLimitCondition2Title':
            MessageLookupByLibrary.simpleMessage('لا رسوم خفية'),
        'creditLimitCondition3Subtitle': MessageLookupByLibrary.simpleMessage(
            'قم بالتبديل بين وضع الخصم والائتمان على أي من بطاقات Wio Business الخاصة بك.'),
        'creditLimitCondition3Title':
            MessageLookupByLibrary.simpleMessage('بطاقة واحدة للخصم والائتمان'),
        'creditLimitConditionsTitle':
            MessageLookupByLibrary.simpleMessage('فوائد العرض'),
        'creditLimitEditButtonTitle':
            MessageLookupByLibrary.simpleMessage('تعديل الحد'),
        'creditLimitReduceErrorDueToEasyCashBalance':
            MessageLookupByLibrary.simpleMessage(
                'لتقليل حد الائتمان الخاص بك في Wio، يرجى سداد رصيد Quick Cash الخاص بك'),
        'creditLimitSaveButtonTitle':
            MessageLookupByLibrary.simpleMessage('حفظ الحد'),
        'creditLimitSelectorEditTitle':
            MessageLookupByLibrary.simpleMessage('تعيين الحد الجديد الخاص بك'),
        'creditLimitSelectorTitle':
            MessageLookupByLibrary.simpleMessage('الحد المعتمد الخاص بك هو'),
        'creditLimitSubmitButtonTitle':
            MessageLookupByLibrary.simpleMessage('اقبل العرض'),
        'creditLockedBottomsheetDescription': MessageLookupByLibrary.simpleMessage(
            'ادفع الحد الأدنى للمبلغ المستحق الآن لفتح الرصيد وتجنب الرسوم الإضافية.'),
        'creditLockedBottomsheetLatePaymentFee':
            MessageLookupByLibrary.simpleMessage('رسم تأخر دفعة:'),
        'creditLockedBottomsheetMinimumAmountToUnlock':
            MessageLookupByLibrary.simpleMessage(
                'الحد الأدنى للمبلغ المستحق\n لفتح الائتمان:'),
        'creditLockedBottomsheetMinimumPaymentDue':
            MessageLookupByLibrary.simpleMessage('دفع الحد الأدنى المقرر:'),
        'creditLockedBottomsheetSubtitle': MessageLookupByLibrary.simpleMessage(
            'رصيدك مقفل لأنك لم تسدد المبلغ المستحق خلال 6 أيام.'),
        'creditLockedBottomsheetTotalOutstanding': m43,
        'creditOfferReadyMessage':
            MessageLookupByLibrary.simpleMessage('عرض الائتمان الخاص بك جاهز!'),
        'creditOnlyWioBankAccountBottomSheetBody':
            MessageLookupByLibrary.simpleMessage(
                'يرجى التأكد من أن Wio Business هو الحساب الوحيد لشركتك وأنه ليس لديك أي حسابات مصرفية أخرى تحت اسم شركتك في الإمارات العربية المتحدة.'),
        'creditOnlyWioBankAccountBottomSheetCtaChangeText':
            MessageLookupByLibrary.simpleMessage('يتغير'),
        'creditOnlyWioBankAccountBottomSheetCtaConfirmText':
            MessageLookupByLibrary.simpleMessage('يتأكد'),
        'creditOnlyWioBankAccountBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('قبل المتابعة'),
        'creditOtherBankAccountsBottomSheetDescription':
            MessageLookupByLibrary.simpleMessage(
                'يرجى التأكد من أنك قمت بإضافة جميع الحسابات المصرفية غير التابعة لـ Wio لأن ذلك سيساعدنا على زيادة فرص الموافقة أو الحصول على حد ائتماني أعلى وموافقة أسرع.'),
        'creditOtherBankAccountsBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('قبل المتابعة'),
        'creditOtherBankAccountsInputPageAddButtonText':
            MessageLookupByLibrary.simpleMessage('إضافة حساب مصرفي'),
        'creditOtherBankAccountsInputPageCtaText':
            MessageLookupByLibrary.simpleMessage('التالي'),
        'creditOtherBankAccountsInputPageDescription':
            MessageLookupByLibrary.simpleMessage(
                'قم بتوفير جميع الحسابات المصرفية غير التابعة لـ Wio لزيادة فرص الموافقة، وتسريع العملية والحصول على حد ائتماني أعلى.'),
        'creditOtherBankAccountsInputPageTitle':
            MessageLookupByLibrary.simpleMessage(
                'أضف الحسابات البنكية الأخرى لشركتك'),
        'creditOverviewDocsText': MessageLookupByLibrary.simpleMessage(
            'من خلال الاستمرار، فإنك توافق على الشروط والأحكام وبيان الحقائق الأساسية'),
        'creditOverviewDocsTextKfs':
            MessageLookupByLibrary.simpleMessage('بيان الحقيقة الرئيسية'),
        'creditOverviewDocsTextTnc':
            MessageLookupByLibrary.simpleMessage('البنود و الظروف'),
        'creditOverviewScreenAuditedFinancialStatementsSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'سنطلب منك تحميل البيانات الموثقة للسنوات الثلاث الماضية.'),
        'creditOverviewScreenAuditedFinancialStatementsTitle':
            MessageLookupByLibrary.simpleMessage('البيانات المالية المدققة'),
        'creditOverviewScreenCtaText':
            MessageLookupByLibrary.simpleMessage('بدء التطبيق'),
        'creditOverviewScreenFirstStepSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'إذا كان ذلك ممكنًا، فسنطلب كشوفات ضريبة القيمة المضافة لآخر 6 أشهر أو ربعين.'),
        'creditOverviewScreenFirstStepTitle':
            MessageLookupByLibrary.simpleMessage(
                'معلومات ضريبة القيمة المضافة'),
        'creditOverviewScreenFirstStepVatStatementExample':
            MessageLookupByLibrary.simpleMessage('عرض مثال البيان'),
        'creditOverviewScreenSecondStepSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'ستحتاج إلى تأكيد حجم مبيعات شركتك السنوي.'),
        'creditOverviewScreenSecondStepTitle':
            MessageLookupByLibrary.simpleMessage('دوران شركة'),
        'creditOverviewScreenSubtitle': MessageLookupByLibrary.simpleMessage(
            'إليك ما نحتاجه لعرض الائتمان الخاص بك'),
        'creditOverviewScreenThirdStepSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'سنحتاج إلى معرفة أرقام IBAN الخاصة بجميع الحسابات المصرفية لشركتك.'),
        'creditOverviewScreenThirdStepTitle':
            MessageLookupByLibrary.simpleMessage('حسابات بنكية'),
        'creditOverviewScreenTitle':
            MessageLookupByLibrary.simpleMessage('لنبدأ رحلتك'),
        'creditPayCreditError': MessageLookupByLibrary.simpleMessage(
            'عذرًا، لم نتمكن من معالجة دفعتك في الوقت الحالي. الرجاء معاودة المحاولة في وقت لاحق.'),
        'creditPaybackOverdueWithFee': m44,
        'creditPaybackOverdueWithoutFee': m45,
        'creditPaybackPercent': m46,
        'creditPaymentDateSetupCta':
            MessageLookupByLibrary.simpleMessage('يكمل'),
        'creditPaymentDateSetupDaysNumberExplanation':
            MessageLookupByLibrary.simpleMessage(
                'لا يوجد سوى 28 يومًا لكي يعمل كل شيء بشكل صحيح حتى في شهر فبراير'),
        'creditPaymentDateSetupPageDescription':
            MessageLookupByLibrary.simpleMessage(
                'قم باختيار يوم بين 1 و 28 لتحويلاتك الشهرية.'),
        'creditPaymentDateSetupPageTitle':
            MessageLookupByLibrary.simpleMessage('متى تريد السداد؟'),
        'creditPaymentDateSetupSelectedDate': m47,
        'creditPaymentDateSetupTip': MessageLookupByLibrary.simpleMessage(
            'يرجى ملاحظة أنه بمجرد تحديد تاريخ السداد، لا يمكن تعديله.'),
        'creditPaymentPercentageSetupCta':
            MessageLookupByLibrary.simpleMessage('يكمل'),
        'creditPaymentPercentageSetupPageTitle':
            MessageLookupByLibrary.simpleMessage('كم تريد أن تسدد شهريا؟'),
        'creditPaymentPercentageSetupPaybackTip':
            MessageLookupByLibrary.simpleMessage(
                'قم بسداد المبلغ كاملاً كل شهر وتجنب الرسوم والمصاريف.'),
        'creditPaymentPercentageSetupPaymentBreakdown': m48,
        'creditPaymentPercentageSetupPaymentBreakdownWithoutFees': m49,
        'creditPaymentPercentageSetupSelectedPercentage': m50,
        'creditSelectorLabel':
            MessageLookupByLibrary.simpleMessage('كل الأموال التي أنفقت'),
        'creditStatementPageTitle':
            MessageLookupByLibrary.simpleMessage('بياناتك الشهرية'),
        'creditStatementUploadBoxSubtitle': m51,
        'creditStatementsEmptyDescription':
            MessageLookupByLibrary.simpleMessage(
                'سيتم عرض المستندات الخاصة بك هنا\n عندما يكون واحد متاحا.'),
        'creditStatementsEmptyTitle': MessageLookupByLibrary.simpleMessage(
            'ليس لديك\n أي تصريحات حتى الآن'),
        'creditStatementsFileViewerDownloadCta':
            MessageLookupByLibrary.simpleMessage('تحميل البيان'),
        'creditStatementsFileViewerTitle':
            MessageLookupByLibrary.simpleMessage('كشف حساب الائتمان'),
        'creditStatementsYearLabel':
            MessageLookupByLibrary.simpleMessage('سنة'),
        'creditThingsToKnowFeature1': m52,
        'creditThingsToKnowFeature2': MessageLookupByLibrary.simpleMessage(
            'لا يتم تطبيق أي رسوم إذا قمت بسداد المبلغ بالكامل ولم تقم بنقل أي رصيد إلى الشهر التالي'),
        'creditThingsToKnowFeature3': m53,
        'creditThingsToKnowSubtitle':
            MessageLookupByLibrary.simpleMessage('يرجى المراجعة والقبول'),
        'creditUpdateLimitAlreadyRequestedDesc':
            MessageLookupByLibrary.simpleMessage(
                'هناك طلب زيادة الحد معلق في حسابك. يمكنك فقط تقليل الحد الخاص بك.'),
        'creditUpdateLimitAlreadyRequestedTitle':
            MessageLookupByLibrary.simpleMessage(
                'لقد طلبت بالفعل زيادة الحد الخاص بك.'),
        'creditUpdateLimitCondition1Subtitle':
            MessageLookupByLibrary.simpleMessage(
                'زيادة الحد الأقصى الخاص بك تخضع للموافقة.'),
        'creditUpdateLimitCondition1Title':
            MessageLookupByLibrary.simpleMessage('رهنا بالموافقة'),
        'creditUpdateLimitCondition2Subtitle': MessageLookupByLibrary.simpleMessage(
            'لا يمكنك تقليل الحد الخاص بك إلى أقل من المبلغ الذي أنفقته بالفعل.'),
        'creditUpdateLimitCondition2Title':
            MessageLookupByLibrary.simpleMessage('الحد الأدنى'),
        'creditUpdateLimitCondition3Subtitle':
            MessageLookupByLibrary.simpleMessage(
                'سيتم تخفيض الحد الخاص بك على الفور بعد النقر فوق \"تحديث الحد\"'),
        'creditUpdateLimitCondition3SubtitleAfterCutoff':
            MessageLookupByLibrary.simpleMessage(
                'سيتم تحديث الحد الخاص بك بعد الموافقة.'),
        'creditUpdateLimitCondition3Title':
            MessageLookupByLibrary.simpleMessage('تأكيد فوري'),
        'creditUpdateLimitCta':
            MessageLookupByLibrary.simpleMessage('حد التحديث'),
        'creditUpdateLimitFooterText': MessageLookupByLibrary.simpleMessage(
            'يعتمد حد الائتمان الخاص بك على مصادر مختلفة، مثل راتبك، وتصنيفك الائتماني، وما إلى ذلك.'),
        'creditUpdateLimitLabel':
            MessageLookupByLibrary.simpleMessage('تحديث حد الائتمان الخاص بك'),
        'creditUpdateLimitRequestMore':
            MessageLookupByLibrary.simpleMessage('اطلب المزيد'),
        'creditUpdateLimitRequestMoreConfirm':
            MessageLookupByLibrary.simpleMessage('نعم أريد المزيد من الحد'),
        'creditUpdateLimitRequestMoreDesc': MessageLookupByLibrary.simpleMessage(
            'إذا كنت تريد حدًا أكبر، فيمكنك طلب التحقق من أهليتك وسنحاول أن نمنحك الحد الأقصى للائتمان الممكن.'),
        'creditUpdateLimitRequestMoreDismiss':
            MessageLookupByLibrary.simpleMessage('ًلا شكرا'),
        'creditUpdateLimitRequestMoreSuccessText':
            MessageLookupByLibrary.simpleMessage(
                'لقد تلقينا طلبك. سنخطرك بمجرد مراجعة كافة معلوماتك.'),
        'creditUpdateLimitRequestMoreTitle': m54,
        'creditUpdateLimitSuccessDesc': m55,
        'creditUpdateLimitSuccessTitle':
            MessageLookupByLibrary.simpleMessage('منتهي!'),
        'creditUpdateOtherBankAccountsIbanError':
            MessageLookupByLibrary.simpleMessage(
                'حدث خطأ ما أثناء محاولة تحديث تطبيقك.'),
        'creditUploadTileText': m56,
        'creditVatReportingIntervalCtaText':
            MessageLookupByLibrary.simpleMessage('التالي'),
        'creditVatReportingIntervalMonthlySubtitle':
            MessageLookupByLibrary.simpleMessage(
                'تقوم شركتي بتقديم تقارير ضريبة القيمة المضافة إلى هيئة الضرائب على أساس شهري.'),
        'creditVatReportingIntervalMonthlyTitle':
            MessageLookupByLibrary.simpleMessage('نعم، نحن نقدم تقريرا شهريا'),
        'creditVatReportingIntervalNoReportingSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'شركتي لا تخضع لالتزامات ضريبة القيمة المضافة.'),
        'creditVatReportingIntervalNoReportingTitle':
            MessageLookupByLibrary.simpleMessage(
                'لا، نحن لا نبلغ عن ضريبة القيمة المضافة'),
        'creditVatReportingIntervalQuarterlySubtitle':
            MessageLookupByLibrary.simpleMessage(
                'تقدم شركتي تقارير ضريبة القيمة المضافة إلى السلطات الضريبية على أساس ربع سنوي.'),
        'creditVatReportingIntervalQuarterlyTitle':
            MessageLookupByLibrary.simpleMessage(
                'نعم، نحن نقدم تقارير ربع سنوية'),
        'creditVatReportingMustBeSelectedError':
            MessageLookupByLibrary.simpleMessage(
                'يجب تحديد الفاصل الزمني للإبلاغ عن ضريبة القيمة المضافة'),
        'creditVatScreenSubtitle':
            MessageLookupByLibrary.simpleMessage('هذا ما نحتاجه'),
        'creditVatScreenTitle': MessageLookupByLibrary.simpleMessage(
            'هل تقوم شركتك بالإبلاغ عن ضريبة القيمة المضافة؟'),
        'creditVatStatementUploadExample':
            MessageLookupByLibrary.simpleMessage('عرض مثال البيان'),
        'creditVatStatementUploadProgress': m57,
        'creditVatStatementUploadScreenDescriptionDetailOne':
            MessageLookupByLibrary.simpleMessage('اسم الشركة'),
        'creditVatStatementUploadScreenDescriptionDetailThree':
            MessageLookupByLibrary.simpleMessage('الفترة الضريبية'),
        'creditVatStatementUploadScreenDescriptionDetailTwo':
            MessageLookupByLibrary.simpleMessage('معلومات المبيعات والمبيعات'),
        'creditVatStatementUploadScreenDescriptionIntro':
            MessageLookupByLibrary.simpleMessage(
                'يرجى التأكد من أن بيانك بتنسيق قياسي ويتضمن:'),
        'creditVatStatementUploadScreenTitleVatReportingIntervalMonthly':
            MessageLookupByLibrary.simpleMessage(
                'قم بتحميل بيانات ضريبة القيمة المضافة الستة الأخيرة المقدمة لشركتك.'),
        'creditVatStatementUploadScreenTitleVatReportingIntervalQuaterly':
            MessageLookupByLibrary.simpleMessage(
                'قم بتحميل آخر كشفين لضريبة القيمة المضافة لشركتك.'),
        'creditVatUploadInvalidReportingInterval':
            MessageLookupByLibrary.simpleMessage(
                'يجب أن يكون تحديد تقارير ضريبة القيمة المضافة شهريًا أو ربع سنوي. الرجاء تحديد الفاصل الزمني المناسب من الشاشة السابقة'),
        'customStatementLabel':
            MessageLookupByLibrary.simpleMessage('بيان مخصص'),
        'customStatementPeriodOneMonth':
            MessageLookupByLibrary.simpleMessage('شهر واحد'),
        'customStatementPeriodOneYear':
            MessageLookupByLibrary.simpleMessage('سنة واحدة'),
        'customStatementPeriodSixMonths':
            MessageLookupByLibrary.simpleMessage('6 أشهر'),
        'customStatementPeriodThreeMonths':
            MessageLookupByLibrary.simpleMessage('3 أشهر'),
        'dashboardEasyCashBannerSubTitle': m58,
        'dashboardEasyCashBannerTitle':
            MessageLookupByLibrary.simpleMessage('نقود سريعة'),
        'debitAccountUpdateSuccess':
            MessageLookupByLibrary.simpleMessage('تم تحديث الحساب'),
        'disclaimerLabel': MessageLookupByLibrary.simpleMessage('تنصل'),
        'documentViewerShare':
            MessageLookupByLibrary.simpleMessage('مشاركة الوثيقة'),
        'dueOnLabel': m59,
        'easyCashManageScreenFaqSectionTitle':
            MessageLookupByLibrary.simpleMessage('بحاجة لبعض المساعدة؟'),
        'easyCashManageScreenTitle':
            MessageLookupByLibrary.simpleMessage('إدارة النقد السريع'),
        'embeddedLendingAgreementSubmittedSuccessSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'نحن نعمل على معالجة أموالك، وستصل إلى حساب Wio Business الخاص بك في غضون دقائق قليلة. وسنخبرك بمجرد أن تصبح جاهزة للاستخدام. \n عندما تصل الأموال، ما عليك سوى تسجيل الدخول هنا لإدارة قرضك!'),
        'embeddedLendingAgreementSubmittedSuccessTitle':
            MessageLookupByLibrary.simpleMessage('تم. أموالك في طريقها إليك!'),
        'embeddedLendingApplicationSubmittedTitle':
            MessageLookupByLibrary.simpleMessage('مبروك!'),
        'endDateLabel': MessageLookupByLibrary.simpleMessage('تاريخ النهاية'),
        'errorMessageActiveEasyCashAccount': MessageLookupByLibrary.simpleMessage(
            'لإغلاق حساب Wio Credit الخاص بك، ستحتاج إلى سداد رصيد Quick Cash الخاص بك'),
        'errorMessageExpiredReferralCode': MessageLookupByLibrary.simpleMessage(
            'رمز الإحالة المدخل منتهي الصلاحية'),
        'errorMessageInvalidReferralCode':
            MessageLookupByLibrary.simpleMessage('رمز الإحالة المدخل غير صالح'),
        'faq': MessageLookupByLibrary.simpleMessage('التعليمات'),
        'feeCalculationAutopayAmount': m60,
        'feeCalculationCarryOverAmount':
            MessageLookupByLibrary.simpleMessage('المبلغ المرحل:'),
        'feeCalculationCarryOverFee':
            MessageLookupByLibrary.simpleMessage('رسوم الترحيل:'),
        'feeCalculationDetailsBottomSheetFeeApplyDescription': m61,
        'feeCalculationOutstandingBalance':
            MessageLookupByLibrary.simpleMessage('اتزان رائع:'),
        'fileSizeExceedsLimit': m62,
        'firstKey': MessageLookupByLibrary.simpleMessage('المفتاح الأول'),
        'generateStatementLabel':
            MessageLookupByLibrary.simpleMessage('إنشاء بيان'),
        'getEasyCashLabel':
            MessageLookupByLibrary.simpleMessage('احصل على النقود بسرعة'),
        'helpCta': MessageLookupByLibrary.simpleMessage('يساعد'),
        'hypothecationAlreadyCompleteSuccessSubtitle':
            MessageLookupByLibrary.simpleMessage('تم توقيع اتفاقية الاقتراض'),
        'hypothecationAlreadyCompleteSuccessTitle':
            MessageLookupByLibrary.simpleMessage('مبروك!'),
        'inReviewBannerDescription': MessageLookupByLibrary.simpleMessage(
            'طلبك قيد المراجعة. لا ينبغي أن يستغرق أكثر من 3 أيام.'),
        'learnMorePageInvalidProductTypeError':
            MessageLookupByLibrary.simpleMessage('نوع المنتج غير صالح'),
        'lendingAmountOnHoldPayCreditContent': m63,
        'lendingAmountOnHoldPayCreditContentHighlighted':
            MessageLookupByLibrary.simpleMessage('يتعلم أكثر'),
        'lendingAutopaySave': MessageLookupByLibrary.simpleMessage('يحفظ'),
        'lendingCreditDashboardAvlToSpend':
            MessageLookupByLibrary.simpleMessage('متاحة للقضاء'),
        'lendingCreditLimitReduceErrorText': MessageLookupByLibrary.simpleMessage(
            'عذرًا، لا يمكنك خفض الحد الائتماني الخاص بك إلى أقل من المبلغ الذي أنفقته.'),
        'lendingFAQ': MessageLookupByLibrary.simpleMessage('التعليمات'),
        'lendingManageAnnualFee':
            MessageLookupByLibrary.simpleMessage('رسوم سنوية'),
        'lendingManageAvailableToSpend':
            MessageLookupByLibrary.simpleMessage('متاحة للقضاء'),
        'lendingManageCashWithdrawal':
            MessageLookupByLibrary.simpleMessage('السحب النقدي'),
        'lendingManageCloseCreditAccountCta':
            MessageLookupByLibrary.simpleMessage('إغلاق حساب الائتمان'),
        'lendingManageCreditPageDocuments':
            MessageLookupByLibrary.simpleMessage('وثائق'),
        'lendingManageCreditPageDownloadStatement':
            MessageLookupByLibrary.simpleMessage('تحميل البيان'),
        'lendingManageCreditPageManageCredit':
            MessageLookupByLibrary.simpleMessage('إدارة الائتمان'),
        'lendingManageCreditPagePaymentDate':
            MessageLookupByLibrary.simpleMessage('تاريخ الدفع'),
        'lendingManageCreditPageRollover':
            MessageLookupByLibrary.simpleMessage('رسوم التمديد'),
        'lendingManageCreditPageYourCredit':
            MessageLookupByLibrary.simpleMessage('رصيدك'),
        'lendingManageCreditRepaymentDate': m64,
        'lendingManageNeedSomeHelp':
            MessageLookupByLibrary.simpleMessage('بحاجة لبعض المساعدة؟'),
        'lendingManageNoAnnualFee':
            MessageLookupByLibrary.simpleMessage('لا رسوم سنوية'),
        'lendingManageNoCashWithdrawal':
            MessageLookupByLibrary.simpleMessage('لا يوجد سحب نقدي'),
        'lendingManageReduceCreditLimit':
            MessageLookupByLibrary.simpleMessage('خفض الحد الائتماني الخاص بك'),
        'lendingManageRepaymentTitle':
            MessageLookupByLibrary.simpleMessage('السداد'),
        'lendingManageScreenCreditAgreement':
            MessageLookupByLibrary.simpleMessage('اتفاق الائتمان'),
        'lendingManageScreenCreditLimit':
            MessageLookupByLibrary.simpleMessage('الحد الائتماني'),
        'lendingManageScreenKfs':
            MessageLookupByLibrary.simpleMessage('بيان الحقائق الرئيسية'),
        'lendingManageStatementsCta':
            MessageLookupByLibrary.simpleMessage('تحميل البيان'),
        'lendingNextPayment': m65,
        'lendingNoFee': MessageLookupByLibrary.simpleMessage('بدون رسوم'),
        'lendingPaybackPercent': m66,
        'lendingPaymentBreakdown': m67,
        'lendingPaymentBreakdownNoFee': m68,
        'lendingPlusFees': MessageLookupByLibrary.simpleMessage('رسوم إضافية'),
        'lendingSelectorLabel':
            MessageLookupByLibrary.simpleMessage('كل الأموال المستخدمة'),
        'lendingSetupPaymentThingsSectionTitle2':
            MessageLookupByLibrary.simpleMessage(
                'يتم أخذ المبلغ من مساحة التوفير الخاصة بك بالدرهم الإماراتي إذا لم يكن لديك ما يكفي من المال في حسابك الحالي بالدرهم الإماراتي لتجنب الرسوم والمصاريف.'),
        'lendingSetupPaymentThingsSectionsTitle1':
            MessageLookupByLibrary.simpleMessage(
                'قم بسداد المبلغ كاملاً كل شهر وتجنب الرسوم والمصاريف'),
        'lendingSetupPaymentThingsToKnowTitle':
            MessageLookupByLibrary.simpleMessage('أشياء يجب معرفتها'),
        'lendingSpentOf': m69,
        'loanReferralCodeBottomsheetDescription':
            MessageLookupByLibrary.simpleMessage(
                'رمز الإحالة هو معرف فريد مشترك بين شركاء Wio لمساعدتك في الوصول إلى تسهيلات الائتمان مع فوائد إضافية - مثل مراجعة الطلب بشكل أسرع والدعم المخصص والمزيد. \n \n إذا أحالك طرف ثالث إلى تسهيل ائتمان Wio ولكنه لم يقدم رمز إحالة، فلا تتردد في سؤاله - فقد يكون قد نسي مشاركته ببساطة.'),
        'loanReferralCodeInputLabel':
            MessageLookupByLibrary.simpleMessage('أدخل الرمز'),
        'loanReferralCodeInvalid':
            MessageLookupByLibrary.simpleMessage('رمز غير صالح'),
        'loanReferralCodePageSkipInput':
            MessageLookupByLibrary.simpleMessage('ليس لدي رمز إحالة'),
        'loanReferralCodePageSubtitle': MessageLookupByLibrary.simpleMessage(
            'إذا لم تقم بتحديد \"ليس لدي رمز\"'),
        'loanReferralCodePageTitle': MessageLookupByLibrary.simpleMessage(
            'إذا كان لديك رمز إحالة، فلنقم بتشغيله'),
        'loanRepaymentMilestonesDescription': MessageLookupByLibrary.simpleMessage(
            'تأكد من سداد المدفوعات اليومية في الوقت المناسب لتلبية أهداف قرضك وتجنب الرسوم الإضافية. سيتم تعديل مبالغ الأهداف تلقائيًا مع كل دفعة أو أي اقتراض جديد أو مدفوعات متأخرة'),
        'loanRepaymentMilestonesTitle':
            MessageLookupByLibrary.simpleMessage('مراحل سداد القرض'),
        'loanRepaymentMissedMilestonesDescription':
            MessageLookupByLibrary.simpleMessage('لقد فاتتك مرحلة مهمة'),
        'loanTypeInputPageTitle':
            MessageLookupByLibrary.simpleMessage('ما هو نوع القرض المناسب لك؟'),
        'loanTypeLabel': MessageLookupByLibrary.simpleMessage('نوع القرض'),
        'loanWhatIsReferralCode':
            MessageLookupByLibrary.simpleMessage('ما هو رمز الإحالة؟'),
        'managePosAutopayFromSSDescription': MessageLookupByLibrary.simpleMessage(
            'لا تفوت أي دفعة! قم بتفعيلها واسترخِ. وفي الوقت نفسه، يمكنك تنمية ثروتك من خلال كسب فائدة تصل إلى 5%*. فز مرتين!'),
        'managePosCreditPageTitle':
            MessageLookupByLibrary.simpleMessage('يدير'),
        'managePosCreditPreferedAccount':
            MessageLookupByLibrary.simpleMessage('الحساب المفضل'),
        'nextAutoPayLabel': m70,
        'nextCta': MessageLookupByLibrary.simpleMessage('التالي'),
        'nonReportingVatCtaText':
            MessageLookupByLibrary.simpleMessage('التالي'),
        'otherReasonInputLabel':
            MessageLookupByLibrary.simpleMessage('يرجى تحديد أخرى'),
        'overdueAmountLabel':
            MessageLookupByLibrary.simpleMessage('المبلغ المتأخر'),
        'payAndCloseCta':
            MessageLookupByLibrary.simpleMessage('الدفع والإغلاق'),
        'payCreditAccountBalance': m71,
        'payCreditAccountTitle': m72,
        'payCreditBelowMinimumPaymentAmount': m73,
        'payCreditExcessivePaymentLabel': m74,
        'payCreditFromLabel': MessageLookupByLibrary.simpleMessage('من'),
        'payCreditFullAmountOption': m75,
        'payCreditInsufficientBalanceLabel': m76,
        'payCreditMinimumAmountOption': m77,
        'payCreditPageButtonTitle':
            MessageLookupByLibrary.simpleMessage('دفع الائتمان'),
        'payCreditPageCreditLockedButtonTitle':
            MessageLookupByLibrary.simpleMessage('الدفع وفتح'),
        'payCreditPageTitle':
            MessageLookupByLibrary.simpleMessage('دفع الائتمان'),
        'payCreditSuccessPageButtonTitle':
            MessageLookupByLibrary.simpleMessage('اذهب للاقتراض'),
        'payCreditSuccessPageCreditUnlocked':
            MessageLookupByLibrary.simpleMessage('تم فتح رصيد Wio'),
        'payCreditSuccessPageDescription': m78,
        'payCreditSuccessPageSubtitle':
            MessageLookupByLibrary.simpleMessage('تم استلام دفعة الائتمان'),
        'payCreditSuccessPageTitle':
            MessageLookupByLibrary.simpleMessage('فقاعة!'),
        'payCreditToLabel': MessageLookupByLibrary.simpleMessage('ل'),
        'payCreditUnlockSuccessPageButtonTitle':
            MessageLookupByLibrary.simpleMessage('منتهي'),
        'payCreditUnlockSuccessPageSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'لقد تم فتح أموال الائتمان الخاصة بك بنجاح.'),
        'payCreditUnlockSuccessPageTitle':
            MessageLookupByLibrary.simpleMessage('تم فتح رصيد Wio للأعمال'),
        'posApplicationApplyBannerSubtitle': MessageLookupByLibrary.simpleMessage(
            'تقدم الآن للحصول على عرض مخصص بناءً على مبيعات نقاط البيع الخاصة بك  '),
        'posApplicationApplyBannerTitle':
            MessageLookupByLibrary.simpleMessage('خط ائتمان Wio'),
        'posApplicationInProgressBannerSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'لم يتبق لك سوى بضع خطوات لإكمال طلب الحصول على خط الائتمان الخاص بك من Wio'),
        'posApplicationInProgressBannerTitle':
            MessageLookupByLibrary.simpleMessage('أكمل طلبك'),
        'posApplicationOfferBannerCta':
            MessageLookupByLibrary.simpleMessage('البدء'),
        'posApplicationOfferBannerSubtitle': MessageLookupByLibrary.simpleMessage(
            'الآن دعنا نراجع حد الائتمان الخاص بك ونبدأ بتمكين ائتمان نقاط البيع الخاص بك '),
        'posApplicationOfferBannerTitle': MessageLookupByLibrary.simpleMessage(
            'تمت الموافقة على خط الائتمان Wio!'),
        'posApplicationRejectedBannerSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'لسوء الحظ، تم رفض طلبك للحصول على خط ائتمان Wio. لا تفي شركتك بجميع المعايير المطلوبة. يمكنك المحاولة مرة أخرى بعد ثلاثة أشهر.'),
        'posApplicationRejectedBannerTitle':
            MessageLookupByLibrary.simpleMessage('خط ائتمان Wio'),
        'posApplicationUnderReviewBannerTitle':
            MessageLookupByLibrary.simpleMessage(
                'طلب خط ائتمان Wio قيد المراجعة'),
        'posApplicationUnderReviewSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'طلبك قيد المراجعة. لن يستغرق الأمر أكثر من 3 أيام.'),
        'posAvailableToBorrow': m79,
        'posBorrowConfirmationPreBorrowDailyPaymentBanner': m80,
        'posBorrowConfirmationTitle':
            MessageLookupByLibrary.simpleMessage('يستعير'),
        'posBorrowExceedsLimitMessage': MessageLookupByLibrary.simpleMessage(
            'المبلغ يتجاوز حد الأهلية الخاص بك'),
        'posBorrowInformationTip': m81,
        'posBorrowLoanRepaymentUpdatedMessage':
            MessageLookupByLibrary.simpleMessage(
                'لقد تم تحديث مبلغ القرض اليومي الخاص بك ومعالم سداد القرض.'),
        'posBorrowReadyToReceive': MessageLookupByLibrary.simpleMessage(
            'هل أنت مستعد لاستلام أموالك؟'),
        'posBorrowSubtitle': MessageLookupByLibrary.simpleMessage(
            'أدخل المبلغ ثم اضغط على \"متابعة\" لمراجعة تفاصيل قرضك'),
        'posBorrowSuccessSubtitle': m82,
        'posBorrowSwipeRightToConfirm':
            MessageLookupByLibrary.simpleMessage('اسحب لليمين للتأكيد '),
        'posBorrowTitle':
            MessageLookupByLibrary.simpleMessage('كم تريد أن تقترض؟'),
        'posBottomSheetBorrowPausedBannerDesc':
            MessageLookupByLibrary.simpleMessage(
                'سيتم إيقاف عملية الاقتراض مؤقتًا حتى يتم سداد المبلغ المستحق. يرجى الدفع الآن لتجنب تفويت موعد سداد القرض وفرض رسوم إضافية.'),
        'posBottomSheetCarryoverField':
            MessageLookupByLibrary.simpleMessage('المبلغ المتبقي'),
        'posBottomSheetDailyPaymentAmountField':
            MessageLookupByLibrary.simpleMessage('مبلغ الدفع اليومي'),
        'posBottomSheetInterestField':
            MessageLookupByLibrary.simpleMessage('اهتمام'),
        'posBottomSheetLatePaymentFeeField':
            MessageLookupByLibrary.simpleMessage('رسوم التأخير في السداد'),
        'posBottomSheetMilestoneOverdueTotalAmountField':
            MessageLookupByLibrary.simpleMessage('المبلغ الإجمالي المستحق'),
        'posBottomSheetMissedDueBannerTitle':
            MessageLookupByLibrary.simpleMessage('تأخر الموعد المحدد'),
        'posBottomSheetOverdueAmountField':
            MessageLookupByLibrary.simpleMessage('المبلغ المستحق'),
        'posBottomSheetOverdueAmountFieldWithDueDate': m83,
        'posBottomSheetOverdueTitle':
            MessageLookupByLibrary.simpleMessage('المبلغ المستحق'),
        'posBottomSheetPrincipalField':
            MessageLookupByLibrary.simpleMessage('رئيسي'),
        'posBottomSheetTotalDueOn': m84,
        'posBottomSheetTotalWithoutDate':
            MessageLookupByLibrary.simpleMessage('المجموع المستحق'),
        'posClosureConfirmationCta':
            MessageLookupByLibrary.simpleMessage('إغلاق خط الائتمان'),
        'posClosureConfirmationStep1Bullet1':
            MessageLookupByLibrary.simpleMessage(
                'لن تتمكن بعد الآن من الوصول إلى بياناتك.'),
        'posClosureConfirmationStep1Bullet2':
            MessageLookupByLibrary.simpleMessage(
                'لا يجوز لك إعادة التقديم إلا من خلال دعوة من الجهة المشترية.'),
        'posClosureConfirmationStep1Info': MessageLookupByLibrary.simpleMessage(
            'حدد \"إغلاق خط الائتمان\" لإغلاق خط الائتمان الخاص بك من Wio بشكل دائم وسداد أي رصيد مستحق. بعد الإغلاق:'),
        'posClosureConfirmationStep1Title':
            MessageLookupByLibrary.simpleMessage('السداد والإغلاق'),
        'posClosureConfirmationStep2Info': MessageLookupByLibrary.simpleMessage(
            'سنقوم بإغلاق خط الائتمان الخاص بك وإرسال شهادة عدم الممانعة إليك. قم بمشاركة هذه الوثيقة مع الجهة المصدرة لإصدار شهادة عدم الممانعة الخاصة بك.'),
        'posClosureConfirmationStep2Title':
            MessageLookupByLibrary.simpleMessage(
                'أطلق نظام نقاط البيع الخاص بك'),
        'posClosureConfirmationSubtitle':
            MessageLookupByLibrary.simpleMessage('هكذا تعمل'),
        'posClosureConfirmationText': MessageLookupByLibrary.simpleMessage(
            'أؤكد أنني أرغب في إغلاق خط الائتمان الخاص بي'),
        'posClosureConfirmationTitle': MessageLookupByLibrary.simpleMessage(
            'هل أنت متأكد أنك تريد إغلاق خط الائتمان Wio الخاص بك؟ '),
        'posClosureSuccessDescription': m85,
        'posClosureSuccessSubtitle':
            MessageLookupByLibrary.simpleMessage('تم إغلاق حسابك الإئتماني.'),
        'posClosureSuccessTitle':
            MessageLookupByLibrary.simpleMessage('لقد تم ذلك!'),
        'posCompleteHypothecation':
            MessageLookupByLibrary.simpleMessage('استكمال فرضية نقاط البيع'),
        'posCompleteHypothecationDescription': MessageLookupByLibrary.simpleMessage(
            'اتبع التعليمات المرسلة إلى عنوان بريدك الإلكتروني لإكمال فرضية نقطة البيع وتفعيل حد الائتمان الخاص بك.'),
        'posCreditBannerLabel':
            MessageLookupByLibrary.simpleMessage('خط ائتمان Wio'),
        'posCreditLabel':
            MessageLookupByLibrary.simpleMessage('الائتمان نقاط البيع'),
        'posCreditMissedPayment':
            MessageLookupByLibrary.simpleMessage('عدم سداد الدفعة اليومية'),
        'posCreditPaybackOverdue':
            MessageLookupByLibrary.simpleMessage('سداد الائتمان المتأخر'),
        'posCreditPaybackSuccessDescription': m86,
        'posCreditPaybackSuccessSubtitle': MessageLookupByLibrary.simpleMessage(
            'تم استلام الدفعة الائتمانية من خلال نقاط البيع'),
        'posCreditRepaymentsOnTrack':
            MessageLookupByLibrary.simpleMessage('المدفوعات على المسار الصحيح'),
        'posCreditTapToBorrow':
            MessageLookupByLibrary.simpleMessage('انقر للاستعارة'),
        'posDashboardAllTransactionTabTitle':
            MessageLookupByLibrary.simpleMessage('الجميع'),
        'posDashboardAvailableToBorrow':
            MessageLookupByLibrary.simpleMessage('متاح للإقتراض'),
        'posDashboardBorrowButtonText':
            MessageLookupByLibrary.simpleMessage('يستعير'),
        'posDashboardManageButtonText':
            MessageLookupByLibrary.simpleMessage('يدير'),
        'posDashboardPaybackButtonText':
            MessageLookupByLibrary.simpleMessage('السداد'),
        'posDashboardRepaymentsTransactionTabTitle':
            MessageLookupByLibrary.simpleMessage('السدادات'),
        'posDashboardTitle':
            MessageLookupByLibrary.simpleMessage('خط ائتمان Wio'),
        'posDashboardTitleBorrowed': m87,
        'posDashboardTransactionSectionTitle':
            MessageLookupByLibrary.simpleMessage('المعاملات'),
        'posDashboardWithdrawalsTransactionTabTitle':
            MessageLookupByLibrary.simpleMessage('السحوبات'),
        'posHypothecationEmail':
            MessageLookupByLibrary.simpleMessage('عنوان البريد الإلكتروني'),
        'posInterestInfoBottomsheetDescription': m88,
        'posInterestInfoBottomsheetTip': m89,
        'posInterestInfoBottomsheetTitle':
            MessageLookupByLibrary.simpleMessage('كيف يتم حساب الفائدة'),
        'posLearnMoreFaq1Answer': m90,
        'posLearnMoreFaq1Question': MessageLookupByLibrary.simpleMessage(
            'ما هو معدل الفائدة على خط الائتمان Wio؟'),
        'posLearnMoreFaq2Answer': MessageLookupByLibrary.simpleMessage(
            'يتم احتساب الفائدة يوميًا على المبلغ الأصلي المستحق. ولن تدفع فائدة إلا على مبلغ الحد الأقصى الذي استخدمته وعلى عدد الأيام المحددة التي استخدمته فيها.'),
        'posLearnMoreFaq2Question':
            MessageLookupByLibrary.simpleMessage('كيف يتم حساب الفائدة؟'),
        'posLearnMoreFaq3Answer': m91,
        'posLearnMoreFaq3Question':
            MessageLookupByLibrary.simpleMessage('كيف يعمل خط الائتمان Wio؟'),
        'posLearnMoreFaq4Answer': MessageLookupByLibrary.simpleMessage(
            'سيتم سداد المبلغ المستحق من خلال أقساط يومية متساوية، يتم حسابها بناءً على إجمالي المبلغ المستحق في وقت السحب. سيتم خصم هذه الأقساط اليومية تلقائيًا من حساب Wio Business الخاص بك. بالإضافة إلى ذلك، يمكنك أيضًا سداد المبلغ المستحق باستخدام خيار \"السداد\" في تطبيق Wio Business الخاص بك دون أي رسوم إضافية للدفع المسبق.'),
        'posLearnMoreFaq4Question': MessageLookupByLibrary.simpleMessage(
            'كيف سأقوم بسداد مبلغ الحد المستغل؟'),
        'posLearnMoreHowToApplyDescription': MessageLookupByLibrary.simpleMessage(
            'قم بتقديم طلبك عبر التطبيق. بعد الموافقة، يمكنك تخصيص حد الائتمان الخاص بك وتوقيع العقد. أخيرًا، ستتلقى تعليمات لاستكمال رهن نقاط البيع لدى مزود نقاط البيع الخاص بك لتفعيل خط الائتمان الخاص بك.'),
        'posLearnMoreHowToApplyTitle':
            MessageLookupByLibrary.simpleMessage('كيفية التقديم'),
        'posLearnMorePageDescription': MessageLookupByLibrary.simpleMessage(
            'قل وداعًا للقروض طويلة الأجل والأموال المحجوبة لسداد الأقساط الشهرية المتساوية '),
        'posLimitCondition1Subtitle': MessageLookupByLibrary.simpleMessage(
            'يتم فرض الفائدة فقط على مبلغ القرض الذي تستخدمه طوال فترة استخدامه.'),
        'posLimitCondition1Title':
            MessageLookupByLibrary.simpleMessage('ادفع حسب الاستخدام'),
        'posLimitCondition2Subtitle': m92,
        'posLimitCondition2Title':
            MessageLookupByLibrary.simpleMessage('سحب سلس'),
        'posLimitCondition3Subtitle': MessageLookupByLibrary.simpleMessage(
            'سيتم سداد المبلغ المستحق عليك من خلال دفعات يومية متساوية من حساب Wio Business الخاص بك وتجديد الحد الأقصى الخاص بك على الفور لإجراء عمليات سحب جديدة.'),
        'posLimitCondition3Title':
            MessageLookupByLibrary.simpleMessage('سداد سهل'),
        'posLimitOneTimeFeeTitle':
            MessageLookupByLibrary.simpleMessage('رسوم التقديم لمرة واحدة'),
        'posLimitOneTimeSubtitle': MessageLookupByLibrary.simpleMessage(
            'سيتم خصم الرسوم تلقائيًا من حساب Wio Business الخاص بك بعد توقيع اتفاقية الاقتراض.'),
        'posLoanInterestLabel': m93,
        'posMilestoneOverdueBannerDescription': m94,
        'posMinimumBorrowAmountMessage': m95,
        'posMissedMilestoneBannerDescription': m96,
        'posNextDailyPaymentBottomSheetDesc':
            MessageLookupByLibrary.simpleMessage(
                'سيتم خصم هذا المبلغ يوميًا من حساب Wio Business الخاص بك.'),
        'posNextDailyPaymentBottomSheetLockInfoBanner':
            MessageLookupByLibrary.simpleMessage(
                'إذا قمت بتخطي خمس دفعات يومية متتالية، فسيتم قفل رصيدك في اليوم السادس.'),
        'posNextDailyPaymentBottomSheetMissedPaymentBannerDesc': m97,
        'posNextDailyPaymentBottomSheetMissedPaymentWarning': m98,
        'posNextDailyPaymentBottomSheetPaybackCta':
            MessageLookupByLibrary.simpleMessage('السداد الآن'),
        'posNextDailyPaymentBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('الدفع اليومي التالي'),
        'posPaidAllDebtBannerContent': MessageLookupByLibrary.simpleMessage(
            'مبروك! لقد قمت بسداد قرضك، الأصل والفائدة. اضغط على \"اقتراض\" لسحب المزيد من الأموال.'),
        'posThingsToKnowContinueCta':
            MessageLookupByLibrary.simpleMessage('حصلت عليه، التالي'),
        'posThingsToKnowFeature1': m99,
        'posThingsToKnowFeature2': MessageLookupByLibrary.simpleMessage(
            'قم بالسداد من خلال الدفعات التلقائية اليومية من حسابك الرئيسي بالدرهم الإماراتي. '),
        'posThingsToKnowFeature3': m100,
        'posThingsToKnowFeature4': m101,
        'posThingsToKnowFeatureFour': MessageLookupByLibrary.simpleMessage(
            'سيتم سداد المبلغ المستحق عليك من خلال دفعات يومية متساوية من حساب Wio Business الخاص بك وتجديد الحد الخاص بك على الفور لإجراء سحب جديد.'),
        'posThingsToKnowFeatureOne': MessageLookupByLibrary.simpleMessage(
            'يصل معدل الفائدة على خط الائتمان الخاص بك إلى 22% سنويًا.'),
        'posThingsToKnowFeatureThree': MessageLookupByLibrary.simpleMessage(
            'يمكنك الوصول إلى الأموال في أي وقت وسدادها خلال 90 يومًا من تاريخ السحب الجديد. يمكنك السحب حسب الحاجة، حيث يضيف كل سحب إلى إجمالي رصيدك المستحق.'),
        'posThingsToKnowFeatureTwo': MessageLookupByLibrary.simpleMessage(
            'سيكون تسهيل خط الائتمان متاحًا لمدة 365 يومًا، بدءًا من تاريخ توقيع اتفاقية الاقتراض.'),
        'posThingsToKnowTitle':
            MessageLookupByLibrary.simpleMessage('أشياء يجب معرفتها قبل البدء'),
        'posTotalOutstandingAmountBottomSheetDesc':
            MessageLookupByLibrary.simpleMessage(
                'هذا هو المبلغ الذي تدين به حاليًا اعتبارًا من اليوم.'),
        'posTotalOutstandingAmountBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('الرصيد الحالي المستحق'),
        'pullDownRefreshLabel':
            MessageLookupByLibrary.simpleMessage('اسحب التحديث'),
        'reasonNotReportingVatQuestion': MessageLookupByLibrary.simpleMessage(
            'ما هو سبب عدم الإبلاغ عن ضريبة القيمة المضافة؟'),
        'refreshingLabel': MessageLookupByLibrary.simpleMessage('جاري التحديث'),
        'rejectedBannerDescription': MessageLookupByLibrary.simpleMessage(
            'لسوء الحظ، تم رفض طلبك للحصول على Wio Business Credit. شركتك لا تستوفي جميع المعايير المطلوبة. يمكنك المحاولة مرة أخرى خلال ثلاثة أشهر.'),
        'releaseToRefreshLabel':
            MessageLookupByLibrary.simpleMessage('حرر للتحديث'),
        'repaymentDailyBorrowedAmount':
            MessageLookupByLibrary.simpleMessage('المبلغ المقترض'),
        'repaymentDailyPaymentInfo': MessageLookupByLibrary.simpleMessage(
            'سيتم خصم المدفوعات اليومية من حساب Wio Business الخاص بك.'),
        'repaymentDetailsAlreadyBorrowed':
            MessageLookupByLibrary.simpleMessage('تم اقتراضه بالفعل'),
        'repaymentDetailsDailyPayment':
            MessageLookupByLibrary.simpleMessage('الدفع اليومي'),
        'repaymentDetailsDueOn': m102,
        'repaymentDetailsInterests':
            MessageLookupByLibrary.simpleMessage('اهتمام'),
        'repaymentDetailsNextDailyPayment':
            MessageLookupByLibrary.simpleMessage('الدفع اليومي التالي'),
        'repaymentDetailsPaidLabel':
            MessageLookupByLibrary.simpleMessage('مدفوع'),
        'repaymentDetailsPrincipal':
            MessageLookupByLibrary.simpleMessage('رئيسي'),
        'repaymentDetailsTotalLoanAmount':
            MessageLookupByLibrary.simpleMessage('إجمالي مبلغ القرض'),
        'repaymentDetailsViewDetails':
            MessageLookupByLibrary.simpleMessage('عرض التفاصيل'),
        'reviewCta': MessageLookupByLibrary.simpleMessage('مراجعة'),
        'reviewYourCreditLimit': MessageLookupByLibrary.simpleMessage(
            'لنراجع الآن حد الائتمان الخاص بك ونحدد تفضيلات السداد الخاصة بك.'),
        'securedBusinessLoanCardFeature1': m103,
        'securedBusinessLoanCardFeature2': m104,
        'securedBusinessLoanCardFeature3': MessageLookupByLibrary.simpleMessage(
            'خطاب التنازل من الجهة المشترية'),
        'securedBusinessLoanCardSubtitle': MessageLookupByLibrary.simpleMessage(
            'للشركات التي لديها نقاط بيع أو مدفوعات عبر الإنترنت يتم تسويتها على حساب Wio.'),
        'securedBusinessLoanCardTitle':
            MessageLookupByLibrary.simpleMessage('قرض مدعوم بالمبيعات'),
        'securedBusinessLoanLabel':
            MessageLookupByLibrary.simpleMessage('قرض مدعوم بالمبيعات'),
        'securedLoanAssignementLetterDescription': m105,
        'securedLoanAssignementLetterStep1':
            MessageLookupByLibrary.simpleMessage('احصل على خطاب التكليف '),
        'securedLoanAssignementLetterStep1Desc':
            MessageLookupByLibrary.simpleMessage(
                'تواصل مع مزود الدفع الخاص بك للحصول على خطاب تكليف يؤكد تسوية المدفوعات في حساب Wio الخاص بك.'),
        'securedLoanAssignementLetterStep2':
            MessageLookupByLibrary.simpleMessage('شارك الرسالة معنا'),
        'securedLoanAssignementLetterStep2Desc':
            MessageLookupByLibrary.simpleMessage(
                'اتبع التعليمات التفصيلية المرسلة إلى عنوان بريدك الإلكتروني حول كيفية إرسال خطاب التعيين إلى Wio'),
        'securedLoanAssignementLetterTitle':
            MessageLookupByLibrary.simpleMessage('تقريبا هناك'),
        'securedLoanAssignmentLetterBannerTitle':
            MessageLookupByLibrary.simpleMessage('رسالة تكليف المشاركة'),
        'securedLoanConfirmationDescription': MessageLookupByLibrary.simpleMessage(
            'سيُطلب منك تقديم خطاب تكليف ساري المفعول لاستلام القرض وإيداع الأموال في حسابك. إذا لم تتمكن من طلبه من مزود خدمة الدفع، فانقر على \"إلغاء\" للمتابعة بقرض بسيط.'),
        'securedLoanConfirmationTitle': MessageLookupByLibrary.simpleMessage(
            'تأكد من أنه يمكنك تقديم خطاب تكليف من مزود الدفع الخاص بك'),
        'securedLoanPageTitle': MessageLookupByLibrary.simpleMessage(
            'إليك كيفية الحصول على قرض مدعوم بالمبيعات'),
        'securedLoanSelectionDescription': MessageLookupByLibrary.simpleMessage(
            'للشركات التي لديها نقاط بيع أو مدفوعات عبر الإنترنت يتم تسويتها على حساب Wio.'),
        'securedLoanSelectionTitle':
            MessageLookupByLibrary.simpleMessage('قرض مدعوم بالمبيعات'),
        'securedLoanStep1Description': MessageLookupByLibrary.simpleMessage(
            'شارك ببيانات ضريبة القيمة المضافة الأخيرة (إن وجدت)، وحجم مبيعات الشركة، وأرقام IBAN الخاصة بحسابك المصرفي.'),
        'securedLoanStep1Title':
            MessageLookupByLibrary.simpleMessage('تقديم الطلب'),
        'securedLoanStep2Description': MessageLookupByLibrary.simpleMessage(
            'سوف تقوم Wio بمراجعة طلبك والتواصل معك بعرض للحد الأقصى للقرض الخاص بك.'),
        'securedLoanStep2Title': MessageLookupByLibrary.simpleMessage(
            'احصل على عرض قرض من Wio وحدد مبلغ الاقتراض الخاص بك'),
        'securedLoanStep3Description': MessageLookupByLibrary.simpleMessage(
            'لتلقي الأموال، ستحتاج إلى تقديم خطاب تكليف من الجهة المشترية يؤكد أن المدفوعات تم تسويتها في حساب Wio الخاص بك.'),
        'securedLoanStep3Title': MessageLookupByLibrary.simpleMessage(
            'شارك برسالة تكليف صالحة لتلقي الأموال'),
        'securedLoanWhatIsAssignmentLetter':
            MessageLookupByLibrary.simpleMessage('ما هي رسالة التكليف؟'),
        'selectFileBorrowingPowerTitle':
            MessageLookupByLibrary.simpleMessage('حدد الملف'),
        'selectFileFinancialStatementTitle':
            MessageLookupByLibrary.simpleMessage(
                'اختر القوائم المالية المدققة'),
        'simpleBusinessLoanCardFeature1': m106,
        'simpleBusinessLoanCardFeature2': m107,
        'simpleBusinessLoanCardFeature3':
            MessageLookupByLibrary.simpleMessage('لا حاجة إلى ضمانات'),
        'simpleBusinessLoanCardSubtitle': MessageLookupByLibrary.simpleMessage(
            'لا توجد ضمانات إضافية، يمكن تطبيقها على جميع أنواع الأعمال.'),
        'simpleBusinessLoanCardTitle':
            MessageLookupByLibrary.simpleMessage('قرض تجاري بسيط'),
        'startDateLabel': MessageLookupByLibrary.simpleMessage('تاريخ البدء'),
        'timePeriodLabel':
            MessageLookupByLibrary.simpleMessage('الفترة الزمنية'),
        'totalOutstandingLabel':
            MessageLookupByLibrary.simpleMessage('الرصيد الحالي المستحق'),
        'unSecuredBusinessLoanLabel':
            MessageLookupByLibrary.simpleMessage('قرض بسيط'),
        'unsecuredLoanSelectionDescription': MessageLookupByLibrary.simpleMessage(
            'لا توجد ضمانات إضافية مطلوبة، حيث يمكن تطبيقها على جميع أنواع الأعمال.'),
        'unsecuredLoanSelectionTitle':
            MessageLookupByLibrary.simpleMessage('قرض بسيط'),
        'updateCreditLimitRequestMoreSuccessButton':
            MessageLookupByLibrary.simpleMessage('فهمتها'),
        'vatStatementSampleTitle':
            MessageLookupByLibrary.simpleMessage('بيان ضريبة القيمة المضافة'),
        'viewOfferLabel': MessageLookupByLibrary.simpleMessage('عرض'),
        'wioBusinessLoanLabel':
            MessageLookupByLibrary.simpleMessage('قرض الأعمال Wio'),
        'wioLineOfCreditLabel':
            MessageLookupByLibrary.simpleMessage('خط ائتمان Wio')
      };
}
