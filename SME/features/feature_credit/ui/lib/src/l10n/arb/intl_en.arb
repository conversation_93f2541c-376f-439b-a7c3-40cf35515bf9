{"@@locale": "en", "@@last_modified": "2025-06-04T09:22:46+02:00", "accountClosureConfirmationCancel": "Keep credit account", "accountClosureConfirmationConfirm": "Close credit account", "accountClosureConfirmationDescription": "After you close your account all your cards will be switched to “My Money” automatically. You may apply for a new credit account later.", "accountClosureConfirmationTitle": "Are you sure you want to close your credit account?", "accountClosureOutstandingRepaymentCta": "Repay full", "accountClosureOutstandingRepaymentDescription": "There is an outstanding amount of {amount} on your account. You must repay the full amount to close your credit.", "@accountClosureOutstandingRepaymentDescription": {"placeholders": {"amount": {"example": "235 AED", "type": "String"}}}, "accountClosureOutstandingRepaymentTitle": "Outstanding repayment", "accountClosurePendingTransactionDescription": "The amount {amount} is pending. Wait for the transactions to complete and try again later.", "@accountClosurePendingTransactionDescription": {"placeholders": {"amount": {"example": "235 AED", "type": "String"}}}, "accountClosurePendingTransactionTitle": "Pending transactions", "accountClosureSuccessCta": "Back to dashboard", "accountClosureSuccessSubtitle": "Your credit account is closed.", "accountClosureSuccessTitle": "IT’S DONE!", "activateBusinessCreditCardInfo": "Turn any of your Wio Business cards into a credit card!", "activateCreditCardBSFooterText": "By selecting ’Activate now’, you agree with the \nTerms & Conditions and the Key Fact Statement", "activateCreditCardBottomsheetSkipCta": "Skip for now", "activateCreditCardBottomsheetSubtitle": "Apply for Wio Credit and turn any of your Wio Business cards into a credit card.", "activateCreditCardBottomsheetTitle": "Would you also like to activate your Wio Business credit ?", "activateNowCta": "Activate now", "applyCreditBannerTitle": "Turn any of your Wio Business cards into a credit card.", "applyCreditHeader": "Apply for Wio Business Credit", "applyNowCta": "Apply now", "assignmentLetterBottomSheetDescription": "An assignment letter is a document issued by your payment platform provider (e.g., Network International, PayTabs, Stripe, etc.) confirming that the proceeds from your sales—whether through their platform or POS device—will be directed to your Wio account for the duration of the credit facility.\n\nHow to obtain one: Once your credit offer is approved, we’ll send you the borrowing agreement via email. You can then forward this agreement to your payment processor or acquirer—either through their support channel or by contacting your account manager. Based on this, they will issue the assignment letter.", "assignmentLetterBottomSheetDisclaimer": "The above is an indicative process. Your payment platform provider may follow a different procedure, and timelines or requirements may vary based on their internal policies.", "assignmentLetterBottomSheetTitle": "What is the assignment letter?", "autopayFeeCalculationEditAutopaySettings": "Edit autopay settings", "autopayFeeCalculationEndedAt": "Ended at {date}", "@autopayFeeCalculationEndedAt": {"placeholders": {"date": {"example": "February 27th", "type": "String"}}}, "autopayFeeCalculationFaq": "FAQ", "autopayFeeCalculationFeeAmountToday": "Your {feePercentage}% fee amount as of today", "@autopayFeeCalculationFeeAmountToday": {"placeholders": {"feePercentage": {"example": "3.25", "type": "String"}}}, "autopayFeeCalculationFeeAmountTodayDescription": "Your fee is calculated based on how much your outstanding amount is and if you're on a charge free period.", "autopayFeeCalculationFeeFreePeriod": "Your charge free period", "autopayFeeCalculationFeeFreePeriodDescription": "If you're still within the {feeFreeMonthCount} months free period, you won't be charged.", "@autopayFeeCalculationFeeFreePeriodDescription": {"placeholders": {"feeFreeMonthCount": {"example": "2", "type": "int"}}}, "autopayFeeCalculationHowAutopayCalculated": "How your autopay amount and fee are calculated", "autopayFeeCalculationPercentageOfSpentMoney": "{repaymentPercentage}% of spent money", "@autopayFeeCalculationPercentageOfSpentMoney": {"placeholders": {"repaymentPercentage": {"example": "100", "type": "String"}}}, "autopayFeeCalculationSpentAsOfToday": "You spent as of today:", "autopayFeeCalculationSpentDescription": "First, we look at how much you spent from Wio Business Credit.", "autopayFeeCalculationStillHaveQuestions": "Still have questions?", "autopayFeeCalculationTillDate": "Till {date}", "@autopayFeeCalculationTillDate": {"placeholders": {"date": {"example": "April 27th", "type": "String"}}}, "autopayFeeCalculationWhyAsOfToday": "Why “as of today”?", "autopayFeeCalculationWhyAsOfTodayAnswer": "You might keep spending before your autopay date. Your final autopay amount will be calculated on: {date}.", "@autopayFeeCalculationWhyAsOfTodayAnswer": {"placeholders": {"date": {"example": "March 27th", "type": "String"}}}, "autopayFeeCalculationYourAmount": "Your autopay amount as of today:", "autopayFeeCalculationYourAmountDescription": "Your autopay amount is calculate based on how much you spend and how much you set up to pay back.", "autopayFeeCalculationYourAutopaySettings": "Your autopay settings:", "autopayFeeCalculationYourAutopaySettingsDescription": "Next, we look at how much you set up to pay back. You can change it at any time.", "autopayFeeCalculationYourOutstandingBalance": "Your outstanding balance", "autopayFeeCalculationYourOutstandingBalanceDescription": "To calculate the fee, we look at how much is your outstanding amount after you've done your monthly payment.", "borrowCta": "Borrow", "borrowingPowerBSCancel": "No", "borrowingPowerBSConfirm": "Yes", "borrowingPowerBSDescription": "Only authorized signatories with borrowing power can apply for Wio Business credit", "borrowingPowerBSTitle": "Do you have the borrowing power for {companyName}", "@borrowingPowerBSTitle": {"placeholders": {"companyName": {"example": "Company", "type": "String"}}}, "borrowingPowerDocumentUploadPageDescription": "Please upload one of the following documents clearly stating you have the borrowing power for your company :", "borrowingPowerDocumentUploadPageInstruction1": "Power of attorney", "borrowingPowerDocumentUploadPageInstruction2": "Memorandum of association", "borrowingPowerDocumentUploadPageTitle": "Confirm you have the borrowing power for {companyName}", "@borrowingPowerDocumentUploadPageTitle": {"placeholders": {"companyName": {"example": "Company", "type": "String"}}}, "borrowingPowerUploadDocumentBoxSubtitle": "PDF, JPG, PNG  max 50MB", "borrowingPowerUploadDocumentBoxTitle": "Upload document", "borrowingPowerWarningMessage": "Only authorized signatories with borrowing power can apply for Wio Business credit", "businessCreditCardLabel": "Business credit card", "businessLoan": "Business Loan", "businessLoanApplicationSubmittedSuccessDescription": "It will be in your account soon", "businessLoanApplicationSubmittedSuccessSubtitle": "Your money is on it’s way!", "businessLoanApplicationSubmittedSuccessTitle": "Done", "businessLoanApplyBannerDescription": "Flexible and tailored for you. Apply now to see how much you can borrow.", "businessLoanBorrowLoanTerm": "{monthCount} months", "@businessLoanBorrowLoanTerm": {"placeholders": {"monthCount": {"example": "6", "type": "String"}}}, "businessLoanBorrowSubtitle": "Pick your loan term, set your amount, check the details, and get the money instantly in your Wio Business bank account.", "businessLoanBorrowTitle": "Hi {userName}! Here’s your loan offer", "@businessLoanBorrowTitle": {"placeholders": {"userName": {"example": "<PERSON><PERSON><PERSON>", "type": "String"}}}, "businessLoanFirstPaymentDate": "First payment date", "businessLoanInterest": "Total interest ({interest}% p.a.)", "@businessLoanInterest": {"placeholders": {"interest": {"example": "25", "type": "String"}}}, "businessLoanLastPaymentDate": "Last payment date", "businessLoanLearnMoreApply": "Apply", "businessLoanLearnMoreFaq1Ans": "You'll pay a fixed monthly installment covering the principal and interest. A loan processing fee and early repayment fee may apply.", "businessLoanLearnMoreFaq1Que": "What are the costs?", "businessLoanLearnMoreFaq2Ans": "You'll need to provide a VAT document (if applicable) and the IBAN of your turnover accounts.", "businessLoanLearnMoreFaq2Que": "What is required?", "businessLoanLearnMoreFaq3Ans": "A late fee may apply, and your credit score may be impacted. Ensure timely payments to avoid penalties.", "businessLoanLearnMoreFaq3Que": "What happens if I miss a repayment?", "businessLoanLearnMoreFeature1Desc": "Your business deserves a solution that fits, and we’re here to make it happen", "businessLoanLearnMoreFeature1Title": "Tailored to loan", "businessLoanLearnMoreFeature2Desc": "The money is credited directly to your Wio Business account and ready to be spent", "businessLoanLearnMoreFeature2Title": "Money straight in your account", "businessLoanLearnMoreFeature3Desc": "Choose a loan term from 3 to 48 months, then repay with automatic monthly installments", "businessLoanLearnMoreFeature3Title": "Flexible and effortless repayment", "businessLoanLearnMoreHowItWorksDesc": "Submit your application through the app. Approval typically takes 3 business days. After approval, you can adjust your loan amount and choose a repayment term. ", "businessLoanLearnMoreSubtitle": "Flexible and tailored for you. Apply now to see how much you can borrow.", "businessLoanLearnMoreTitle": "Get the loan that treats you right", "businessLoanLoanAmount": "Loan amount", "businessLoanLoanAmountDesc": "This is the total amount that will be credited to your Wio Business account. The maximum you can borrow depends on the loan term you choose.", "businessLoanMonthlyInstalment": "Monthly instalment", "businessLoanOfferReadyMessage": "Your loan offer's ready!  Let’s take a look.", "businessLoanOneTimeFee": "One-time processing fee", "businessLoanProcessingFee": "Processing fee ", "businessLoanProcessingFeeDesc": "This is a one-time fee that will be charged once you borrow the selected amount and sign the loan agreement.", "businessLoanProcessingFeeInsufficientBalance": "Your Wio Account balance is too low. Please make sure you have at least {processingFee} to cover the one-time processing fee and continue with your loan.", "@businessLoanProcessingFeeInsufficientBalance": {"placeholders": {"processingFee": {"example": "250.00 AED", "type": "String"}}}, "businessLoanRejectedBannerDescription": "Unfortunately, your application for Wio Business Loan has been declined. Your company doesn't meet all the required criteria. You can try again in three months.", "businessLoanThingsToKnowFeatureFour": "Once confirmed, the transaction can't be reversed.", "businessLoanThingsToKnowFeatureOne": "An interest will be applied at an annual interest rate from {minRate}% to {maxRate}%. ", "@businessLoanThingsToKnowFeatureOne": {"placeholders": {"maxRate": {"example": "39", "type": "String"}, "minRate": {"example": "16", "type": "String"}}}, "businessLoanThingsToKnowFeatureThree": "A late payment fee of {latePaymentFee} will be applied if payment is not made within {lateFeeGracePeriodInDays} days after your due date.", "@businessLoanThingsToKnowFeatureThree": {"placeholders": {"lateFeeGracePeriodInDays": {"example": "6", "type": "String"}, "latePaymentFee": {"example": "199 AED", "type": "String"}}}, "businessLoanThingsToKnowFeatureTwo": "Monthly payments will be deducted automatically from your current account and/or saving spaces.", "businessLoanTotalAmountToRepay": "Total amount to repay", "cancelCta": "Cancel", "cannotUpdateAutopayPercentageError": "Please come later after you use some credit money", "channelFinanceAgreementBottomSheetCta": "I accept", "channelFinanceAgreementBottomSheetError": "Something went wrong while getting the agreement", "closeCta": "Close", "completeApplicationCta": "Complete application", "completeApplicationDescription": "You’ve got only a few steps left to complete your Wio Business Credit application", "completeApplicationTitle": "Complete your application", "completeBusinessLoanApplicationDescription": "You’ve got only a few steps left to complete your Wio Business Loan application", "completePosHypothecationPageResendEmail": "Resend email", "completePosHypothecationPageStep1": "Borrow agreement signed", "completePosHypothecationPageStep2": "Details transmitted to {posPartnerName}", "@completePosHypothecationPageStep2": {"placeholders": {"posPartnerName": {"example": "Partner Name", "type": "String"}}}, "completePosHypothecationPageStep3": "Complete POS hypothecation", "completePosHypothecationPageStep3Info": "This final step secures your credit line and enables repayments through your POS transactions.", "completePosHypothecationPageSubtitle": "To start using your credit line, follow the instruction sent to {email} to complete the hypothecation of your POS.", "@completePosHypothecationPageSubtitle": {"placeholders": {"email": {"example": "<EMAIL>", "type": "String"}}}, "completePosHypothecationPageTitle": "Almost there !", "completedRefreshLabel": "Completed", "confirmAutodebitAccountInformation": "In case of insufficient balance other Wio Business AED accounts will be debited for repayment", "confirmAutodebitAccountSubtitle": "Wio will auto-debit this account for your daily repayments and possible overdues.", "confirmAutodebitAccountTitle": "Confirm the account for withdrawals and repayments", "confirmAutodebitAutopayFromSSSubtitle": "Allow Wio to withdraw money from your saving spaces if you don’t have enough balance in your AED current  accounts", "confirmAutodebitAutopayFromSSTitle": "Repay from Saving Space", "confirmAutodebitSelectAccount": "Select account", "confirmCta": "Confirm", "continueCta": "Continue", "creditAccountLockedBannerCTA": "Unlock your credit", "creditAccountLockedBannerTitle": "Your credit has been locked because you\nhaven't paid the minimum amount due. To\nunlock please pay minimum of {minimumRepaymentAmout}.", "@creditAccountLockedBannerTitle": {"placeholders": {"minimumRepaymentAmout": {"example": "235 AED", "type": "String"}}}, "creditAcknowledgeRejectCta": "Got it", "creditAgreementApplicationSubmitSuccessScreenCta": "Show me how", "creditAgreementApplicationSubmitSuccessScreenDescription": "From now on, easily choose between debit or credit for your spends.", "creditAgreementApplicationSubmitSuccessScreenTitle": "Wio Business Credit Unlocked!", "creditAgreementBLBannerText": "Once signed, the transaction can’t be reversed", "creditAgreementPageTitle": "Borrow agreement", "creditAgreementPosBannerText": "Once confirmed, the transaction can’t be reversed, as we will open a credit account immediately upon approval of your application.", "creditAgreementSignCta": "Sign via SMS", "creditAmountOnHoldInfoBottomSheetDescription": "When you use your card to make a payment, the transaction starts with a 'pending' state. After you pay, the seller checks everything is okay, and when they're sure, we change the status to 'completed'. This usually happens with 1-3 days after you make a payment.", "creditAmountOnHoldInfoBottomSheetTitle": "Pending transactions", "creditAnnualTurnoverCancelCta": "Cancel", "creditAnnualTurnoverConfirmCta": "Confirm", "creditAnnualTurnoverInputLabel": "Annual turnover ", "creditAnnualTurnoverNextCta": "Next", "creditAnnualTurnoverPageDescription": "Turnover is the total credits your company is expecting to receive across all accounts held by your company in the UAE.", "creditAnnualTurnoverPageTitle": "Confirm this your company's annual turnover", "creditAnnualTurnoverUpdateCta": "Update", "creditAnnualTurnoverUpdateErrorToastMessage": "Oops we couldn’t update your info. Please try again.", "creditAnnualTurnoverUpdateSuccessToastMessage": "Turnover information updated !", "creditApplicationRecapAccountBanner": "A credit account will be created immediately after the approval of your application", "creditApplicationRecapAnnualTurnover": "Annual turnover", "creditApplicationRecapCompanyBankAccounts": "Company bank accounts", "creditApplicationRecapErrorMessage": "Oops we couldn’t update your information. Please try again.", "creditApplicationRecapFiledAuditedFinancialStatements": "Audited Financial Statements", "creditApplicationRecapFiledVatReports": "Filed VAT reports", "creditApplicationRecapFromPreviousPageDescription": "Please review your details to make sure everything’s up-to-date.", "creditApplicationRecapFromPreviousPageTitle": "Check your info and submit the application", "creditApplicationRecapFromPreviousSubmit": "Confirm and submit", "creditApplicationRecapNoVatReporting": "No VAT reporting", "creditApplicationRecapOnlyWioBankAccount": "Only Wio Business account", "creditApplicationRecapPageDescription": "You’re all set, check the details and submit your application.", "creditApplicationRecapPageTitle": "Now, let’s review it all", "creditApplicationRecapPaybackDay": "{day} of every month", "@creditApplicationRecapPaybackDay": {"placeholders": {"day": {"example": "15", "type": "String"}}}, "creditApplicationRecapPaybackDayTitle": "Pay back day of month", "creditApplicationRecapSubmit": "Submit application", "creditApplicationRecapSubmitBanner": "Once submitted, the application can’t be reversed", "creditApplicationRecapSuccessMessage": "Information updated !", "creditApplicationRecapVatReportingAnnually": "Annually", "creditApplicationRecapVatReportingMethod": "VAT Reporting method", "creditApplicationRecapVatReportingMonthly": "Monthly", "creditApplicationRecapVatReportingQuarterly": "Quarterly", "creditApplicationRepaymentPlanCta": "Let’s go", "creditApplicationRepaymentPlanDescription": "Ensure timely payments to meet your loan milestones to avoid extra fees.", "creditApplicationRepaymentPlanInterest": "Interest", "creditApplicationRepaymentPlanLoanAmount": "Loan amount", "creditApplicationRepaymentPlanLoanTermLabel": "Loan term", "creditApplicationRepaymentPlanLoanTermValue": "{monthCount} months", "@creditApplicationRepaymentPlanLoanTermValue": {"placeholders": {"monthCount": {"example": "24", "type": "String"}}}, "creditApplicationRepaymentPlanPeriodOrAmountNullError": "Please make sure loan amount and term selected successfully.", "creditApplicationRepaymentPlanProcessingFee": "One-time processing fee", "creditApplicationRepaymentPlanTitle": "Great! Here’s your repayment plan", "creditApplicationRepaymentPlanTotalPayback": "Total payback", "creditApplicationSubmitSuccessPageCta": "Done", "creditApplicationSubmitSuccessPageDescription": "Application submitted.\nWe'll review everything and let you know within 2-3 days.", "creditApplicationSubmitSuccessPageTitle": "MABROOK!", "creditAuditedFinancialStatementUploadScreenTitle": "Upload audited financial statements for your company", "creditAuditedStatementUploadScreenDescriptionDetailThree": "1 year of company financial activity", "creditAutoPayFeeCalculation": "See how we calculate your fee", "creditAutoPayFeeCalculationDetailsBottomSheetFeeAmount": "Fee amount:", "creditAutoPayFeeCalculationDetailsBottomSheetSubtitle": "Your fee is determined by applying {feePercentage}% to the carry-over amount, which is your current outstanding balance after repayment.", "@creditAutoPayFeeCalculationDetailsBottomSheetSubtitle": {"placeholders": {"feePercentage": {"example": "3.45", "type": "String"}}}, "creditAutoPayFeeCalculationDetailsBottomSheetTipText": "Repay 100% of spent money this month to resume your fee-free period.", "creditAutoPayFeeCalculationDetailsBottomSheetTitle": "Your fee as of today", "creditAutoPayFeeFreePeriodNoFee": "No Fee till {date}", "@creditAutoPayFeeFreePeriodNoFee": {"placeholders": {"date": {"example": "April 24", "type": "String"}}}, "creditAutoPayMinimumPaymentToAvoidFees": "*Minimum payment to avoid late fees: {amount}", "@creditAutoPayMinimumPaymentToAvoidFees": {"placeholders": {"amount": {"example": "2,116.64 AED", "type": "String"}}}, "creditAutoPayNextPayment": "Your next autopay is on {date}", "@creditAutoPayNextPayment": {"placeholders": {"date": {"example": "March 27", "type": "String"}}}, "creditAutoPaySetTo": "Autopay set to", "creditAutoPayTotalDue": "Total due", "creditAutopayFromSavingSpaceConfirmationAccept": "Turn off", "creditAutopayFromSavingSpaceConfirmationCancel": "Keep", "creditAutopayFromSavingSpaceConfirmationDescription": "If you turn it off you might get charged with fees if you don’t have enough money in your current AED account on autopay date.\n\nYou can change it later in “Manage”", "creditAutopayFromSavingSpaceDescription": "Money will be taken from your Saving Space when you don’t have enough money in your current AED account.", "creditAutopayFromSavingSpaceTitle": "Autopay from Saving Space", "creditAutopayPercentageUpdateSuccessfully": "Autopay repayment percentage successfully updated", "creditBankAccountsNoOptionSelectedError": "Please select an option that describes your company's business bank", "creditBannerFooterText": "of {creditLoanAmount}", "creditCommonToastSomethingWentWrong": "Something went wrong.", "creditCompanyBankAccountInputScreenCtaText": "Next", "creditCompanyBankAccountOptionOtherBanks": "My company has other bank accounts in the UAE", "creditCompanyBankAccountOptionSelectorTitle": "Tell us about your company bank accounts", "creditCompanyBankAccountOptionWioOnly": "Wio Business is my company's only bank account", "creditCreditLimitReduceSuccessPageButtonTitle": "Done", "creditCreditLimitReduceSuccessPageDescription": "Your new Credit limit is {amount}", "@creditCreditLimitReduceSuccessPageDescription": {"placeholders": {"amount": {"example": "15,000 AED", "type": "String"}}}, "creditCreditLimitReduceSuccessPageTitle": "Limit decreased successfully", "creditDashboardAutopayAmountLabel": "Autopay amount", "creditDashboardAutopayEditButtonTitle": "Edit autopay", "creditDashboardAutopayFeeLabel": "Fee", "creditDashboardAutopayNoFeeLabel": "No fee", "creditDashboardAutopaySectionTitle": "Next autopay on {date}", "@creditDashboardAutopaySectionTitle": {"placeholders": {"date": {"example": "March 27th", "type": "String"}}}, "creditDashboardAvailableLimit": "Available limit", "creditDashboardAvailableToBorrow": "Available to borrow", "creditDashboardBorrow": "Borrow", "creditDashboardBorrowed": "Borrowed", "creditDashboardCreditLockedTitle": "Credit locked", "creditDashboardCreditTab": "My credit", "creditDashboardDueDate": "Due date {date}", "@creditDashboardDueDate": {"placeholders": {"date": {"example": "April 15th", "type": "String"}}}, "creditDashboardEasyCashTab": "Quick Cash", "creditDashboardFeePerDay": "Fee per day", "creditDashboardFees": "Fees", "creditDashboardLastAutoPaidAmount": "We took {amount}", "@creditDashboardLastAutoPaidAmount": {"placeholders": {"amount": {"example": "100500 AED", "type": "String"}}}, "creditDashboardLastAutoPayDateLabel": "Your last autopay, {date}", "@creditDashboardLastAutoPayDateLabel": {"placeholders": {"date": {"example": "February 27th", "type": "String"}}}, "creditDashboardLastAutoPayDescription": "We took an amount closest to your payment of {amount} because there wasn't enough money in your {currency} account on the autopay date.", "@creditDashboardLastAutoPayDescription": {"placeholders": {"amount": {"example": "100500 AED", "type": "String"}, "currency": {"example": "AED", "type": "String"}}}, "creditDashboardLastAutoPayFee": "+{amount} fee", "@creditDashboardLastAutoPayFee": {"placeholders": {"amount": {"example": "13.37 AED", "type": "String"}}}, "creditDashboardLatePaymentFee": "Late payment fee", "creditDashboardLockedCredit": "Locked credit", "creditDashboardManageButtonTitle": "Manage", "creditDashboardPageTitle": "Wio Business Credit", "creditDashboardPayButtonTitle": "Pay credit", "creditDashboardPayback": "Payback", "creditDashboardRefreshError": "Sorry, we were unable to refresh the dashboard. Please try again later", "creditDashboardSpentLabel": "Already spent", "creditDashboardStatementButtonTitle": "Statement", "creditDashboardTakeMore": "Take more", "creditDashboardToSpendLabel": "Available to spend ", "creditDashboardTotalDue": "Total due", "creditDashboardTransactionsSectionTitle": "Transactions", "creditDashboardYouBorrowed": "You borrowed", "creditFeeFreePeriodTooltipAcknowledgeCta": "Got it, thanks!", "creditFeeFreePeriodTooltipBody": "Your fee-free period has expired on {date}. You can resume the fee-free period for up to 60 days each time you repay your outstanding balance in full.", "@creditFeeFreePeriodTooltipBody": {"placeholders": {"date": {"example": "April 24", "type": "String"}}}, "creditFeeFreePeriodTooltipTitle": "Resume your fee-free period", "creditGenericErrorScreenCtaText": "Close", "creditGenericErrorScreenDescription": "Something went wrong on our side. Please try again later.", "creditGenericErrorScreenSubtitle": "Something went wrong", "creditGenericErrorScreenTitle": "Oops!", "creditGenericErrorScreenTryAgain": "Please try again later", "creditIbanInputBottomSheetCtaText": "Add", "creditIbanInputBottomSheetDescription": "You may receive a notification from your bank. Don’t worry your information security is our priority.", "creditIbanInputBottomSheetErrorGeneric": "Invalid IBAN", "creditIbanInputBottomSheetErrorInvalidLength": "The entered IBAN has an invalid length", "creditIbanInputBottomSheetErrorInvalidStructure": "The entered IBAN has an invalid structure", "creditIbanInputBottomSheetErrorNotUaeIban": "You can enter only UAE IBANs", "creditIbanInputBottomSheetInputFieldLabel": "IBAN number", "creditIbanInputBottomSheetTitle": "Add bank account IBAN", "creditIneligibilityBottomsheetCta": "Got it, thanks", "creditLearnMoreCta": "Learn more", "creditLearnMoreCtaText": "Apply now", "creditLearnMoreFaqTitle": "FAQs", "creditLearnMoreFirstFaq": "What are the Wio Business Credit costs?", "creditLearnMoreFirstFaqAnswer": "No annual fee - Wio Credit is included in your pricing plan.\n\nNo monthly carry-over fee if you pay full outstanding amount each month without any carrying over any balance to the next month.\n\n{feePercentage}% monthly carry-over fee applies if you pay at least {minumumPayPercentage}% of your outstanding balance. This fee is calculated as a fixed percentage of your carry-over amount, which is your current outstanding balance after repayment.\n\nPay off one month's balance entirely and enjoy up to {feeFreeMonthCount} months fee-free period.\n\n{penalty} Penalty fee apply when you fail to repay at least {minumumPayPercentage}% of your outstanding balance within 6 days after repayment deadline.", "creditLearnMoreFirstFeatureDesc": "Easily switch between debit and credit mode on any of your Wio Business cards.", "creditLearnMoreFirstFeatureTitle": "One card for debit and credit", "creditLearnMoreHowItWorks": "How it works", "creditLearnMoreHowItWorksDesc": "Submit your application directly on the app. Upon application approval, customize your repayment preferences and start spending by setting any of your Wio Business cards into credit spending mode.", "creditLearnMorePageSubtitle": "Apply for Wio Credit and turn any of your Wio Business cards into a credit card.", "creditLearnMorePageTitle": "Wio Business Credit", "creditLearnMoreSecondFaq": "How does the fee-free period work?", "creditLearnMoreSecondFaqAnswer": "1. Pay as low as {minumumPayPercentage}% of your outstanding balance and carry over the remaining amount to the next period without incurring any fees.\n\n2. You can carry over your balance without any fees for two consecutive billing cycles, and fees will only be charged starting from the third cycle. This allows you to enjoy up to {feeFreeMonthCount} months of fee-free credit!\n\n3. After your fee-free period ends, it will restart each time you repay your outstanding balance in full.\n\nTake a look at this practical example:\nIf your repayment date is the 1st of each month and you spend AED 1,000 on the 2nd of June, you need to repay only the minimum ({minumumPayPercentage}% of AED 1,000)  To avoid fees, the full amount must be repaid  by the 1st of August. This gives you June and July as your fee-free period.", "creditLearnMoreSecondFeatureDesc": "Enjoy {feePercentage}% fee on your outstanding balance, and no fees if you repay your spends in full.", "@creditLearnMoreSecondFeatureDesc": {"placeholders": {"feePercentage": {"example": "3.45", "type": "num"}}}, "creditLearnMoreSecondFeatureTitle": "No hidden fees", "creditLearnMoreThirdFaq": "How is Wio Business Credit better than a regular credit card?", "creditLearnMoreThirdFaqAnswer": "- Use with any of your cards physical or virtual.\n\n- Choose your repayment date and amount with custom autopay option.\n\n- Your end of month statements includes your spends no complex statement periods.\n\n- Wio Credit is free and you pay only a flat {feePercentage}% fee on your carryover amount.", "creditLearnMoreThirdFeatureDesc": "Pay as low as {minumumPayPercentage}% of your outstanding amount and enjoy up to {feeFreeMonthCount} months without fees.", "creditLearnMoreThirdFeatureTitle": "Get up to {feeFreeMonthCount} months fee-free credit", "creditLearnMoreWhatYouGet": "What you’ll get", "creditLimitCondition1Subtitle": "Pay as low as {minumumPaymentPercentage}% of your outstanding amount and enjoy up to {feeFreeMonthCount} months without fees.", "@creditLimitCondition1Subtitle": {"placeholders": {"feeFreeMonthCount": {"example": "2", "type": "String"}, "minumumPaymentPercentage": {"example": "3.45", "type": "String"}}}, "creditLimitCondition1Title": "Get up to {feeFreeMonthCount} months fee-free credit", "@creditLimitCondition1Title": {"placeholders": {"feeFreeMonthCount": {"example": "2", "type": "String"}}}, "creditLimitCondition2Subtitle": "Enjoy {feePercentage}% fee on your outstanding balance, and no fees if you repay your spends in full.", "@creditLimitCondition2Subtitle": {"placeholders": {"feePercentage": {"example": "3.45", "type": "String"}}}, "creditLimitCondition2Title": "No hidden fees", "creditLimitCondition3Subtitle": "Switch between debit and credit mode on any of your Wio Business cards.", "creditLimitCondition3Title": "One card for debit and credit", "creditLimitConditionsTitle": "Offer benefits", "creditLimitEditButtonTitle": "Edit limit", "creditLimitReduceErrorDueToEasyCashBalance": "To reduce your Wio Credit limit, please repay your Quick Cash balance", "creditLimitSaveButtonTitle": "Save limit", "creditLimitSelectorEditTitle": "Set your new limit", "creditLimitSelectorTitle": "Your approved limit is", "creditLimitSubmitButtonTitle": "Accept offer", "creditLockedBottomsheetDescription": "Pay the minimum amount due now to unlock credit and avoid additional charges.", "creditLockedBottomsheetLatePaymentFee": "Late payment fee:", "creditLockedBottomsheetMinimumAmountToUnlock": "Minimum amount due\nto unlock credit:", "creditLockedBottomsheetMinimumPaymentDue": "Minimum payment due:", "creditLockedBottomsheetSubtitle": "Your credit is locked because you didn't pay the  due amount within 6 days.", "creditLockedBottomsheetTotalOutstanding": "Total outstanding as of\n{date} (incl. fees):", "@creditLockedBottomsheetTotalOutstanding": {"placeholders": {"date": {"example": "September 27", "type": "String"}}}, "creditOfferReadyMessage": "Your credit offer is ready!", "creditOnlyWioBankAccountBottomSheetBody": "Please confirm that Wio Business is your company's sole account and that you don’t have any other bank accounts under your company name in the UAE.", "creditOnlyWioBankAccountBottomSheetCtaChangeText": "Change", "creditOnlyWioBankAccountBottomSheetCtaConfirmText": "Confirm", "creditOnlyWioBankAccountBottomSheetTitle": "Before you continue", "creditOtherBankAccountsBottomSheetDescription": "Please confirm that you've added all non-Wio bank accounts as this will help us increase your chances of approval or getting a higher credit limit and quicker approval.", "creditOtherBankAccountsBottomSheetTitle": "Before you continue", "creditOtherBankAccountsInputPageAddButtonText": "Add bank account", "creditOtherBankAccountsInputPageCtaText": "Next", "creditOtherBankAccountsInputPageDescription": "Provide all non-Wio bank accounts to increase your chances of approval, speed up the process and get higher credit limit.", "creditOtherBankAccountsInputPageTitle": "Add your company's other bank accounts", "creditOverviewDocsText": "By continuing, you agree with the Terms & Conditions and the Key Fact Statement", "creditOverviewDocsTextKfs": "Key Fact Statement", "creditOverviewDocsTextTnc": "Terms & Conditions", "creditOverviewScreenAuditedFinancialStatementsSubtitle": "We will ask to upload the last three years of authenticated statements.", "creditOverviewScreenAuditedFinancialStatementsTitle": "Audited financial statements", "creditOverviewScreenCtaText": "Start Application", "creditOverviewScreenFirstStepSubtitle": "If applicable, we’ll ask for VAT statements for the last 6 months or 2 quarters.", "creditOverviewScreenFirstStepTitle": "VAT Information", "creditOverviewScreenFirstStepVatStatementExample": "View statement example", "creditOverviewScreenSecondStepSubtitle": "You’ll need to confirm your company’s annual turnover.", "creditOverviewScreenSecondStepTitle": "Company turnover", "creditOverviewScreenSubtitle": "Here's what we need for your credit offer", "creditOverviewScreenThirdStepSubtitle": "We’ll need to know the IBANs of all your company’s bank accounts.", "creditOverviewScreenThirdStepTitle": "Bank accounts", "creditOverviewScreenTitle": "Let's start your journey", "creditPayCreditError": "Sorry, we were unable to process your payment at this time. Please try again later.", "creditPaybackOverdueWithFee": "You are charged with late payment fees of {amount}. To unlock your credit make the overdue payment now.", "@creditPaybackOverdueWithFee": {"placeholders": {"amount": {"example": "AED XXX", "type": "String"}}}, "creditPaybackOverdueWithoutFee": "To avoid any additional charges, please make the overdue payment by {date}.", "@creditPaybackOverdueWithoutFee": {"placeholders": {"date": {"example": "September 9", "type": "String"}}}, "creditPaybackPercent": "{percentage}% of spent money", "@creditPaybackPercent": {"placeholders": {"percentage": {"example": "50", "type": "String"}}}, "creditPaymentDateSetupCta": "Continue", "creditPaymentDateSetupDaysNumberExplanation": "There are only 28 days for everything to work correctly even in February", "creditPaymentDateSetupPageDescription": "Select a day between 1 and 28 for your monthly transfers.", "creditPaymentDateSetupPageTitle": "When do you want to pay back?", "creditPaymentDateSetupSelectedDate": "{date} of every month", "@creditPaymentDateSetupSelectedDate": {"placeholders": {"date": {"example": "21", "type": "String"}}}, "creditPaymentDateSetupTip": "Please note that once selected, the repayment date can't be edited.", "creditPaymentPercentageSetupCta": "Continue", "creditPaymentPercentageSetupPageTitle": "How much do you want to pay back monthly?", "creditPaymentPercentageSetupPaybackTip": "Pay back the full amount every month and avoid fees and charges.", "creditPaymentPercentageSetupPaymentBreakdown": "If you spend {spent} and you pay back {payback}, you'll be charged {fee}", "@creditPaymentPercentageSetupPaymentBreakdown": {"placeholders": {"fee": {"example": "80", "type": "String"}, "payback": {"example": "2500", "type": "String"}, "spent": {"example": "5000", "type": "String"}}}, "creditPaymentPercentageSetupPaymentBreakdownWithoutFees": "If you spend {spent}, you'll pay back {fee} without fees.", "@creditPaymentPercentageSetupPaymentBreakdownWithoutFees": {"placeholders": {"fee": {"example": "AED 5000", "type": "String"}, "spent": {"example": "AED 5000", "type": "String"}}}, "creditPaymentPercentageSetupSelectedPercentage": "{percentage}% of spent money", "@creditPaymentPercentageSetupSelectedPercentage": {"placeholders": {"percentage": {"example": "100", "type": "String"}}}, "creditSelectorLabel": "All spent money", "creditStatementPageTitle": "Your monthly statements", "creditStatementUploadBoxSubtitle": "{supportedFileTypes} max {supportedFileSizeInMb}MB", "@creditStatementUploadBoxSubtitle": {"placeholders": {"supportedFileSizeInMb": {"example": "17", "type": "String"}, "supportedFileTypes": {"example": "PDF, JPG, PNG", "type": "String"}}}, "creditStatementsEmptyDescription": "Your documents will be displayed here\nwhen one is available.", "creditStatementsEmptyTitle": "You don’t have\nany statements yet", "creditStatementsFileViewerDownloadCta": "Download statement", "creditStatementsFileViewerTitle": "Credit account statement", "creditStatementsYearLabel": "Year", "creditThingsToKnowFeature1": "A carry-over fee (interest) will be applied at an annual interest rate of up to {annualInterest}%.", "@creditThingsToKnowFeature1": {"placeholders": {"annualInterest": {"example": "39", "type": "String"}}}, "creditThingsToKnowFeature2": "No fees are applied if you pay your amount in full and don’t carry over any balance to the next month", "creditThingsToKnowFeature3": "You can waive the carryover fee for two months in a row by paying at least {minThresholdPercentage}% of the balance.", "@creditThingsToKnowFeature3": {"placeholders": {"minThresholdPercentage": {"Type": "String", "example": "5"}}}, "creditThingsToKnowSubtitle": "Please review and accept", "creditUpdateLimitAlreadyRequestedDesc": "A limit increase request is pending on your account. You can only decrease your limit.", "creditUpdateLimitAlreadyRequestedTitle": "You have already requested to increase your limit.", "creditUpdateLimitCondition1Subtitle": "Your limit increase is subject to approval.", "creditUpdateLimitCondition1Title": "Subject to approval", "creditUpdateLimitCondition2Subtitle": "You cannot reduce your limit below amount you’ve already spent.", "creditUpdateLimitCondition2Title": "Minimum limit", "creditUpdateLimitCondition3Subtitle": "Your limit is reduced instantly after you click “Update limit”", "creditUpdateLimitCondition3SubtitleAfterCutoff": "Your limit is updated after approval.", "creditUpdateLimitCondition3Title": "Instant confirmation", "creditUpdateLimitCta": "Update limit", "creditUpdateLimitFooterText": "Your credit limit is based on different sources, such as your salary, credit rating, etc.", "creditUpdateLimitLabel": "Update your credit limit", "creditUpdateLimitRequestMore": "Request more", "creditUpdateLimitRequestMoreConfirm": "Yes, I want more limit", "creditUpdateLimitRequestMoreDesc": "If you want more limit you can request to check your eligibility and we will try to give you the maximum credit limit possible.", "creditUpdateLimitRequestMoreDismiss": "No, Thanks", "creditUpdateLimitRequestMoreSuccessText": "We’ve got your request.  We'll notify you once we've reviewed all your information.", "creditUpdateLimitRequestMoreTitle": "Do you want to request for more than {amount}?", "@creditUpdateLimitRequestMoreTitle": {"placeholders": {"amount": {"example": "15,000 AED", "type": "String"}}}, "creditUpdateLimitSuccessDesc": "Your limit has been updated successfully. Your new Credit limit is {amount}", "@creditUpdateLimitSuccessDesc": {"placeholders": {"amount": {"example": "15,000 AED", "type": "String"}}}, "creditUpdateLimitSuccessTitle": "DONE!", "creditUpdateOtherBankAccountsIbanError": "Something went wrong while trying to update your application.", "creditUploadTileText": "Upload statement {nextStatement} of {required}", "@creditUploadTileText": {"placeholders": {"nextStatement": {"example": "apple.pdf", "type": "String"}, "required": {"example": "apple.pdf", "type": "String"}}}, "creditVatReportingIntervalCtaText": "Next", "creditVatReportingIntervalMonthlySubtitle": "My company submits VAT reports to the tax authority on a monthly basis.", "creditVatReportingIntervalMonthlyTitle": "Yes, we report monthly", "creditVatReportingIntervalNoReportingSubtitle": "My company is not subject to VAT obligations.", "creditVatReportingIntervalNoReportingTitle": "No, we don’t report VAT", "creditVatReportingIntervalQuarterlySubtitle": "My company submits VAT reports to the tax authorities on a quarterly basis.", "creditVatReportingIntervalQuarterlyTitle": "Yes, we report quarterly", "creditVatReportingMustBeSelectedError": "Vat Reporting Interval must be selected", "creditVatScreenSubtitle": "Here's what we need", "creditVatScreenTitle": "Does your company report VAT?", "creditVatStatementUploadExample": "View statement example", "creditVatStatementUploadProgress": "{x} of {required} statements uploaded", "@creditVatStatementUploadProgress": {"placeholders": {"required": {"example": "apple.pdf", "type": "String"}, "x": {"example": "apple.pdf", "type": "String"}}}, "creditVatStatementUploadScreenDescriptionDetailOne": "Company name", "creditVatStatementUploadScreenDescriptionDetailThree": "Tax period", "creditVatStatementUploadScreenDescriptionDetailTwo": "Turnover and sales information", "creditVatStatementUploadScreenDescriptionIntro": "Please ensure your statement is in a standard format and includes :", "creditVatStatementUploadScreenTitleVatReportingIntervalMonthly": "Upload your company's last six filed VAT statements.", "creditVatStatementUploadScreenTitleVatReportingIntervalQuaterly": "Upload your company's last two filed VAT statements.", "creditVatUploadInvalidReportingInterval": "The VAT reporting select must be either monthly or quarterly. Please select the appropriate interval from the previous screen", "customStatementLabel": "Custom statement", "customStatementPeriodOneMonth": "1 month", "customStatementPeriodOneYear": "1 year", "customStatementPeriodSixMonths": "6 months", "customStatementPeriodThreeMonths": "3 months", "dashboardEasyCashBannerSubTitle": "You’re eligible to get up to {amount} as Quick cash. The money will be transferred to your AED account immediately.", "@dashboardEasyCashBannerSubTitle": {"placeholders": {"amount": {"example": "10000 AED", "type": "String"}}}, "dashboardEasyCashBannerTitle": "Quick cash", "debitAccountUpdateSuccess": "Account updated", "disclaimerLabel": "Disclaimer", "documentViewerShare": "Share document", "dueOnLabel": "Due on {date}", "@dueOnLabel": {"placeholders": {"date": {"example": "February 24", "type": "String"}}}, "easyCashManageScreenFaqSectionTitle": "Need some help?", "easyCashManageScreenTitle": "Manage Quick Cash", "embeddedLendingAgreementSubmittedSuccessSubtitle": "We’re processing your funds, and they’ll be in your Wio Business account in just a few minutes. We’ll let you know once they’re ready to use. \nWhen the funds arrive, simply sign in here to manage your loan!", "embeddedLendingAgreementSubmittedSuccessTitle": "Done. Your money is on its way!", "embeddedLendingApplicationSubmittedTitle": "<PERSON><PERSON>!", "endDateLabel": "End date", "errorMessageActiveEasyCashAccount": "To close your Wio Credit account, you'll need to repay your Quick Cash balance", "errorMessageExpiredReferralCode": "Entered referral code is expired", "errorMessageInvalidReferralCode": "Entered referral code is invalid", "faq": "FAQ", "feeCalculationAutopayAmount": "{repaymentPercentage}% autopay amount:", "@feeCalculationAutopayAmount": {"placeholders": {"repaymentPercentage": {"example": "75", "type": "String"}}}, "feeCalculationCarryOverAmount": "Carry-over amount:", "feeCalculationCarryOverFee": "Carry-over fee:", "feeCalculationDetailsBottomSheetFeeApplyDescription": "Full fee will apply on your carry-over amount after {date}. Your fee-free period will resume each time you repay your outstanding balance in full.", "@feeCalculationDetailsBottomSheetFeeApplyDescription": {"placeholders": {"date": {"example": "April 24", "type": "String"}}}, "feeCalculationOutstandingBalance": "Outstanding balance:", "fileSizeExceedsLimit": "Looks like the file is too large. The max file size is {size} MB.", "@fileSizeExceedsLimit": {"placeholders": {"size": {"example": "50", "type": "String"}}}, "firstKey": "First key", "generateStatementLabel": "Generate statement", "getEasyCashLabel": "Get Quick Cash", "helpCta": "Help", "hypothecationAlreadyCompleteSuccessSubtitle": "Borrowing agreement signed", "hypothecationAlreadyCompleteSuccessTitle": "<PERSON><PERSON>!", "inReviewBannerDescription": "Your application is under review. It shouldn't take more than 3 days.", "learnMorePageInvalidProductTypeError": "Invalid Product type", "lendingAmountOnHoldPayCreditContent": "The amount of {holdAmount} is pending. It can be paid back once transactions are completed. Learn more", "@lendingAmountOnHoldPayCreditContent": {"placeholders": {"holdAmount": {"example": "AED 100500", "type": "String"}}}, "lendingAmountOnHoldPayCreditContentHighlighted": "Learn more", "lendingAutopaySave": "Save", "lendingCreditDashboardAvlToSpend": "Available to spend", "lendingCreditLimitReduceErrorText": "Sorry, you can’t decrease your credit limit below your spent amount.", "lendingFAQ": "FAQ", "lendingManageAnnualFee": "Annual fee", "lendingManageAvailableToSpend": "Available to spend", "lendingManageCashWithdrawal": "Cash withdrawal", "lendingManageCloseCreditAccountCta": "Close credit account", "lendingManageCreditPageDocuments": "Documents", "lendingManageCreditPageDownloadStatement": "Download statement", "lendingManageCreditPageManageCredit": "Manage credit", "lendingManageCreditPagePaymentDate": "Payment date", "lendingManageCreditPageRollover": "Roll-over fee", "lendingManageCreditPageYourCredit": "Your credit", "lendingManageCreditRepaymentDate": "{day} of every month", "@lendingManageCreditRepaymentDate": {"placeholders": {"day": {"example": "2", "type": "String"}}}, "lendingManageNeedSomeHelp": "Need some help?", "lendingManageNoAnnualFee": "No annual fee", "lendingManageNoCashWithdrawal": "No cash withdrawal", "lendingManageReduceCreditLimit": "Reduce your credit limit", "lendingManageRepaymentTitle": "Repayment", "lendingManageScreenCreditAgreement": "Credit agreement", "lendingManageScreenCreditLimit": "Credit limit", "lendingManageScreenKfs": "Key fact statement", "lendingManageStatementsCta": "Download statement", "lendingNextPayment": "Your next autopay on {date} of {month}", "@lendingNextPayment": {"placeholders": {"date": {"example": "25", "type": "String"}, "month": {"example": "March", "type": "String"}}}, "lendingNoFee": "No fees", "lendingPaybackPercent": "{percentage} of used money", "@lendingPaybackPercent": {"placeholders": {"percentage": {"example": "25%", "type": "String"}}}, "lendingPaymentBreakdown": "{amount} and {fee} fee as of today", "@lendingPaymentBreakdown": {"placeholders": {"amount": {"example": "25", "type": "String"}, "fee": {"example": "March", "type": "String"}}}, "lendingPaymentBreakdownNoFee": "{amount} as of today", "@lendingPaymentBreakdownNoFee": {"placeholders": {"amount": {"example": "25", "type": "String"}}}, "lendingPlusFees": "Plus fee", "lendingSelectorLabel": "All used money", "lendingSetupPaymentThingsSectionTitle2": "Amount is taken from your AED saving space if your don’t have enough money in your current AED account to avoid fees and charges.", "lendingSetupPaymentThingsSectionsTitle1": "Pay back the full amount every month and avoid fees and charges", "lendingSetupPaymentThingsToKnowTitle": "Things to know", "lendingSpentOf": "Spent {spent} of {creditLimit}", "@lendingSpentOf": {"placeholders": {"creditLimit": {"example": "100500 AED", "type": "String"}, "spent": {"example": "100500 AED", "type": "String"}}}, "loanReferralCodeBottomsheetDescription": "A referral code is a unique identifier shared by Wio partners to help you access credit facilities with added benefits—such as faster application review, personalized support, and more.\n\nIf a third party referred you for a Wio credit facility but didn’t provide a referral code, feel free to ask them—they may have simply forgotten to share it.", "loanReferralCodeInputLabel": "Enter code", "loanReferralCodeInvalid": "Invalid code", "loanReferralCodePageSkipInput": "I don’t have a referral code", "loanReferralCodePageSubtitle": "If you don’t just select “I don’t have a code”", "loanReferralCodePageTitle": "If you’ve got a referral code let’s fire it up", "loanRepaymentMilestonesDescription": "Ensure timely daily payments to meet your loan milestones and avoid extra charges. Your milestone amounts will automatically adjust with each payment, any new borrowing, or missed payments", "loanRepaymentMilestonesTitle": "Loan repayment milestones", "loanRepaymentMissedMilestonesDescription": "You have missed your milestone", "loanTypeInputPageTitle": "What type of loan is right for you?", "loanTypeLabel": "Loan type", "loanWhatIsReferralCode": "What is a referral code?", "managePosAutopayFromSSDescription": "Never miss a repayment! Enable it and relax. Meanwhile, grow your wealth by earning up to 5%* interest. Double win!", "managePosCreditPageTitle": "Manage", "managePosCreditPreferedAccount": "Preferred account", "nextAutoPayLabel": "Next autopayment is on {date}", "@nextAutoPayLabel": {"placeholders": {"date": {"example": "23 March", "type": "String"}}}, "nextCta": "Next", "nonReportingVatCtaText": "Next", "otherReasonInputLabel": "Please specify other", "overdueAmountLabel": "Overdue amount", "payAndCloseCta": "Pay and close", "payCreditAccountBalance": "Balance: {amount}", "@payCreditAccountBalance": {"placeholders": {"amount": {"example": "100500 AED", "type": "String"}}}, "payCreditAccountTitle": "{currency} account", "@payCreditAccountTitle": {"placeholders": {"currency": {"example": "AED", "type": "String"}}}, "payCreditBelowMinimumPaymentAmount": "The amount cannot be less than the minimum: {amount}", "@payCreditBelowMinimumPaymentAmount": {"placeholders": {"amount": {"example": "500.00 AED", "type": "String"}}}, "payCreditExcessivePaymentLabel": "The amount exceeds full amount: {amount}", "@payCreditExcessivePaymentLabel": {"placeholders": {"amount": {"example": "100500 AED", "type": "String"}}}, "payCreditFromLabel": "From", "payCreditFullAmountOption": "Full amount: {amount}", "@payCreditFullAmountOption": {"placeholders": {"amount": {"example": "100500", "type": "String"}}}, "payCreditInsufficientBalanceLabel": "The amount exceeds your balance: {amount}", "@payCreditInsufficientBalanceLabel": {"placeholders": {"amount": {"example": "100500 AED", "type": "String"}}}, "payCreditMinimumAmountOption": "Minimum: {amount}", "@payCreditMinimumAmountOption": {"placeholders": {"amount": {"example": "235 AED", "type": "String"}}}, "payCreditPageButtonTitle": "Pay credit", "payCreditPageCreditLockedButtonTitle": "Pay and unlock", "payCreditPageTitle": "Pay credit", "payCreditSuccessPageButtonTitle": "Go to borrow", "payCreditSuccessPageCreditUnlocked": "Wio credit unlocked", "payCreditSuccessPageDescription": "You’ve payed {amount}\nto Credit from {currency} account", "@payCreditSuccessPageDescription": {"placeholders": {"amount": {"example": "100500 AED", "type": "String"}, "currency": {"example": "AED", "type": "String"}}}, "payCreditSuccessPageSubtitle": "Credit payment received", "payCreditSuccessPageTitle": "BOOM!", "payCreditToLabel": "To", "payCreditUnlockSuccessPageButtonTitle": "Done", "payCreditUnlockSuccessPageSubtitle": "Your Credit money has been unlocked successfully.", "payCreditUnlockSuccessPageTitle": "Wio Business Credit Unlocked", "posApplicationApplyBannerSubtitle": "Apply now to get a customized offer based on your POS sales  ", "posApplicationApplyBannerTitle": "Wio line of credit", "posApplicationInProgressBannerSubtitle": "You’ve got only a few steps left to complete your Wio Line of Credit application", "posApplicationInProgressBannerTitle": "Complete your application", "posApplicationOfferBannerCta": "Get started", "posApplicationOfferBannerSubtitle": "Now let’s review your credit limit and get started by enabling your POS credit ", "posApplicationOfferBannerTitle": "Wio line of credit approved !", "posApplicationRejectedBannerSubtitle": "Unfortunately, your application for Wio line of Credit has been declined. Your company doesn't meet all the required criteria. You can try again in three months.", "posApplicationRejectedBannerTitle": "Wio line of credit", "posApplicationUnderReviewBannerTitle": "Wio line of credit application under review", "posApplicationUnderReviewSubtitle": "Your application is under review. It shouldn't take more than 3 days.", "posAvailableToBorrow": "Available to borrow {balance}", "@posAvailableToBorrow": {"placeholders": {"balance": {"example": "15,000 AED", "type": "String"}}}, "posBorrowConfirmationPreBorrowDailyPaymentBanner": "New daily payment amount will apply from tomorrow. Today you will still pay {preBorrowDailyPaymentAmount}.", "@posBorrowConfirmationPreBorrowDailyPaymentBanner": {"placeholders": {"preBorrowDailyPaymentAmount": {"example": "250.00 AED", "type": "String"}}}, "posBorrowConfirmationTitle": "BORROW", "posBorrowExceedsLimitMessage": "Amount exceeds your eligibility limit", "posBorrowInformationTip": "This amount will be added in your current outstanding balance as of today, with repayment due in the next {loanTerm} days.", "@posBorrowInformationTip": {"placeholders": {"loanTerm": {"example": "90", "type": "String"}}}, "posBorrowLoanRepaymentUpdatedMessage": "Your daily amount and loan repayments milestones have been updated.", "posBorrowReadyToReceive": "Ready to receive your money?", "posBorrowSubtitle": "Enter the amount and press “Continue” to review your loan details", "posBorrowSuccessSubtitle": "Money transferred to  {currency} account", "@posBorrowSuccessSubtitle": {"placeholders": {"currency": {"example": "AED", "type": "String"}}}, "posBorrowSwipeRightToConfirm": "Swipe right to confirm ", "posBorrowTitle": "How much would you like to borrow?", "posBottomSheetBorrowPausedBannerDesc": "Your borrowing is paused until the overdue amount is repaid. Please pay now to avoid missing loan repayment milestone and extra charges.", "posBottomSheetCarryoverField": "Carry-over amount", "posBottomSheetDailyPaymentAmountField": "Daily payment amount", "posBottomSheetInterestField": "Interest", "posBottomSheetLatePaymentFeeField": "Late payment fee", "posBottomSheetMilestoneOverdueTotalAmountField": "Total amount due", "posBottomSheetMissedDueBannerTitle": "Missed due date", "posBottomSheetOverdueAmountField": "Overdue amount", "posBottomSheetOverdueAmountFieldWithDueDate": "Overdue amount as of\n{date}", "@posBottomSheetOverdueAmountFieldWithDueDate": {"placeholders": {"date": {"example": "September 9", "type": "String"}}}, "posBottomSheetOverdueTitle": "Overdue amount", "posBottomSheetPrincipalField": "Principal", "posBottomSheetTotalDueOn": "Total due on {date}", "@posBottomSheetTotalDueOn": {"placeholders": {"date": {"example": "September 9", "type": "String"}}}, "posBottomSheetTotalWithoutDate": "Total due", "posClosureConfirmationCta": "Close credit line", "posClosureConfirmationStep1Bullet1": "You will no longer have access to your statements.", "posClosureConfirmationStep1Bullet2": "You may only reapply through an invitation from your acquirer.", "posClosureConfirmationStep1Info": "Select “Close credit line” to permanently close your Wio line of credit and repay any outstanding balance.  After closure:", "posClosureConfirmationStep1Title": "Payback and close", "posClosureConfirmationStep2Info": "We will close your credit line and send you the Non Objection Certificate. Share this document with your acquirer to release your POS.", "posClosureConfirmationStep2Title": "Release your POS", "posClosureConfirmationSubtitle": "This is how it works", "posClosureConfirmationText": "I confirm I’d like to close my credit line", "posClosureConfirmationTitle": "Are you sure you want to close your Wio line of credit? ", "posClosureSuccessDescription": "The non objection certificate for the POS release have been sent to {email}", "@posClosureSuccessDescription": {"placeholders": {"email": {"example": "<EMAIL>", "type": "String"}}}, "posClosureSuccessSubtitle": "Your credit account is closed.", "posClosureSuccessTitle": "IT’S DONE!", "posCompleteHypothecation": "Complete POS hypothecation", "posCompleteHypothecationDescription": "Follow the instructions sent to your email address to complete the POS hypothecation and activate your credit line.", "posCreditBannerLabel": "Wio line of credit", "posCreditLabel": "POS credit", "posCreditMissedPayment": "Missed daily payment", "posCreditPaybackOverdue": "Credit payback overdue", "posCreditPaybackSuccessDescription": "You’ve payed {amount}\nto POS credit from {currency} account\n\nThanks for the early repayment!", "@posCreditPaybackSuccessDescription": {"placeholders": {"amount": {"example": "100500 AED", "type": "String"}, "currency": {"example": "AED", "type": "String"}}}, "posCreditPaybackSuccessSubtitle": "POS credit payment received", "posCreditRepaymentsOnTrack": "Payments on track", "posCreditTapToBorrow": "Tap to borrow", "posDashboardAllTransactionTabTitle": "All", "posDashboardAvailableToBorrow": "Available to borrow", "posDashboardBorrowButtonText": "Borrow", "posDashboardManageButtonText": "Manage", "posDashboardPaybackButtonText": "Payback", "posDashboardRepaymentsTransactionTabTitle": "Repayments", "posDashboardTitle": "Wio line of credit", "posDashboardTitleBorrowed": "Borrowed {borrowedAmount} of {totalLoanAmount}", "@posDashboardTitleBorrowed": {"placeholders": {"borrowedAmount": {"example": "AED 4000", "type": "String"}, "totalLoanAmount": {"example": "AED 4000", "type": "String"}}}, "posDashboardTransactionSectionTitle": "Transactions", "posDashboardWithdrawalsTransactionTabTitle": "<PERSON><PERSON><PERSON><PERSON>", "posHypothecationEmail": "Email address", "posInterestInfoBottomsheetDescription": "We calculate interest daily based on your outstanding principal balance. Your interest rate for {loanPeriod}-day period is {interestRate}%", "@posInterestInfoBottomsheetDescription": {"placeholders": {"interestRate": {"example": "18.5", "type": "String"}, "loanPeriod": {"example": "90", "type": "String"}}}, "posInterestInfoBottomsheetTip": "If you repay before {loanPeriod} days, you'll only pay interest accrued up to that day.", "@posInterestInfoBottomsheetTip": {"placeholders": {"loanPeriod": {"example": "90", "type": "String"}}}, "posInterestInfoBottomsheetTitle": "How the interest is calculated", "posLearnMoreFaq1Answer": "The interest rate ranges from {min}% to {max}% per annum, depending on your credit history, credit bureau report & POS historical data.", "@posLearnMoreFaq1Answer": {"placeholders": {"max": {"example": "24", "type": "String"}, "min": {"example": "18", "type": "String"}}}, "posLearnMoreFaq1Question": "What is the interest rate on Wio Line of Credit?", "posLearnMoreFaq2Answer": "Interest is calculated daily on the outstanding principal amount. You will only pay interest on the amount of the limit you have used and for the exact number of days you have used it.", "posLearnMoreFaq2Question": "How interest is calculated?", "posLearnMoreFaq3Answer": "When you make a new drawdown, it is added to your existing outstanding amount. The updated total outstanding amount will be repaid over the next {days} days, starting from the date of the new withdrawal, through daily repayments. You can withdraw funds as needed, up to one year from the date your credit limit is sanctioned.", "@posLearnMoreFaq3Answer": {"placeholders": {"days": {"example": "90", "type": "String"}}}, "posLearnMoreFaq3Question": "How does Wio Line of Credit works?", "posLearnMoreFaq4Answer": "Repayment will be made through daily equated installments, calculated based on the total outstanding amount at the time of withdrawal. These daily repayments will be automatically debited from your Wio Business account. Additionally, you can also repay your outstanding amount using the \"Payback\" option in your Wio Business app without any additional pre-payment charges.", "posLearnMoreFaq4Question": "How will I repay the utilized limit amount?", "posLearnMoreHowToApplyDescription": "Submit your application through the app. After approval, you can customize your credit limit and sign the contract. Finally, you’ll receive instructions to complete POS hypothecation with your POS provider to activate your Line of Credit.", "posLearnMoreHowToApplyTitle": "How to apply", "posLearnMorePageDescription": "Say goodbye to long-term loans and blocked funds for EMI payments ", "posLimitCondition1Subtitle": "Interest is charged only on the amount of the loan you use, for the time you use it.", "posLimitCondition1Title": "Pay as you use", "posLimitCondition2Subtitle": "Access funds anytime and repay within {days} days from fresh withdrawal date. Withdraw as needed, each withdrawal adds to total your outstanding balance.", "@posLimitCondition2Subtitle": {"placeholders": {"days": {"example": "90", "type": "String"}}}, "posLimitCondition2Title": "Seamless withdrawal", "posLimitCondition3Subtitle": "Your outstanding amount is repaid through equated daily payments from your Wio Business account & replenish your limit instantly for fresh withdrawal.", "posLimitCondition3Title": "Effortless repayment", "posLimitOneTimeFeeTitle": "One time application fee", "posLimitOneTimeSubtitle": "The fee will be automatically deducted from your Wio Business account after you sign the borrowing agreement.", "posLoanInterestLabel": "{loanTerm}-Day interest", "@posLoanInterestLabel": {"placeholders": {"loanTerm": {"example": "90", "type": "String"}}}, "posMilestoneOverdueBannerDescription": "Your loan payment due on {missedMilestoneDueDate} was missed, and as a result, your credit is currently locked. You are charged with late payment fees of {latePaymentFee}. To unlock your limit make the overdue payment now.", "@posMilestoneOverdueBannerDescription": {"placeholders": {"latePaymentFee": {"example": "AED 4000", "type": "String"}, "missedMilestoneDueDate": {"example": "August 29", "type": "String"}}}, "posMinimumBorrowAmountMessage": "The minimum amount to borrow is {minimumBorrowAmount}", "@posMinimumBorrowAmountMessage": {"placeholders": {"minimumBorrowAmount": {"example": "15,000 AED", "type": "String"}}}, "posMissedMilestoneBannerDescription": "Your loan payment due on {missedMilestoneDueDate} was missed, and as a result, your credit is currently locked. To avoid any additional charges, please make the overdue payment by {dateToAvoidAdditionalCharges}.", "@posMissedMilestoneBannerDescription": {"placeholders": {"dateToAvoidAdditionalCharges": {"example": "September 9", "type": "String"}, "missedMilestoneDueDate": {"example": "August 29", "type": "String"}}}, "posNextDailyPaymentBottomSheetDesc": "This amount will be deducted daily from your Wio Business account.", "posNextDailyPaymentBottomSheetLockInfoBanner": "If you skip five consecutive daily payments. On the sixth day, your credit will be locked.", "posNextDailyPaymentBottomSheetMissedPaymentBannerDesc": "The amount will be carried over to your next daily payment. To avoid credit lock please payback your missed daily payment until {date}", "@posNextDailyPaymentBottomSheetMissedPaymentBannerDesc": {"placeholders": {"date": {"example": "September 9", "type": "String"}}}, "posNextDailyPaymentBottomSheetMissedPaymentWarning": "{missedPaymentCount, plural, =1{You have missed 1 daily payment} other{You have missed {missedPaymentCount} daily payments}}", "@posNextDailyPaymentBottomSheetMissedPaymentWarning": {"placeholders": {"missedPaymentCount": {"type": "int"}}}, "posNextDailyPaymentBottomSheetPaybackCta": "Payback now", "posNextDailyPaymentBottomSheetTitle": "Next daily payment", "posPaidAllDebtBannerContent": "Congrats! You’ve paid off your loan, principal plus interest. Tap '<PERSON><PERSON>' to withdraw more funds.", "posThingsToKnowContinueCta": "Got it, Next", "posThingsToKnowFeature1": "Borrow as often as you like, starting from {minAmount}, up to your max limit. ", "@posThingsToKnowFeature1": {"placeholders": {"minAmount": {"example": "2,000 AED", "type": "String"}}}, "posThingsToKnowFeature2": "Repay with daily automatic payments from your main AED account. ", "posThingsToKnowFeature3": "The loan term is {loanTerm} days with minimum amount to be repaid every month in 3 milestones.", "@posThingsToKnowFeature3": {"placeholders": {"loanTerm": {"example": "90", "type": "String"}}}, "posThingsToKnowFeature4": "Interest applies daily, so if you repay before {loanTerm} days, you'll only pay the interest up to that day.", "@posThingsToKnowFeature4": {"placeholders": {"loanTerm": {"example": "90", "type": "String"}}}, "posThingsToKnowFeatureFour": "Your outstanding amount is repaid through equated daily payments from your Wio Business account & replenish your limit instantly for fresh withdrawal.", "posThingsToKnowFeatureOne": "The interest rate on your Line of Credit is up to 22% per annum.", "posThingsToKnowFeatureThree": "Access funds anytime and repay within 90 days from fresh withdrawal date. Withdraw as needed, each withdrawal adds to total your outstanding balance.", "posThingsToKnowFeatureTwo": "The line of credit facility will be available for a 365-day period, starting from the date the borrowing agreement is signed.", "posThingsToKnowTitle": "Things to know before you start", "posTotalOutstandingAmountBottomSheetDesc": "This is the amount you currently owe as of today.", "posTotalOutstandingAmountBottomSheetTitle": "Current outstanding", "pullDownRefreshLabel": "Pull down to refresh", "reasonNotReportingVatQuestion": "What is the reason for not reporting VAT?", "refreshingLabel": "Refreshing...", "rejectedBannerDescription": "Unfortunately, your application for Wio Business Credit has been declined. Your company doesn't meet all the required criteria. You can try again in three months.", "releaseToRefreshLabel": "Release to refresh", "repaymentDailyBorrowedAmount": "Borrowed amount", "repaymentDailyPaymentInfo": "Daily payments will be deducted from your Wio Business account.", "repaymentDetailsAlreadyBorrowed": "Already borrowed", "repaymentDetailsDailyPayment": "Daily payment", "repaymentDetailsDueOn": "Due on {date}", "@repaymentDetailsDueOn": {"placeholders": {"date": {"example": "August 20", "type": "String"}}}, "repaymentDetailsInterests": "Interest", "repaymentDetailsNextDailyPayment": "Next daily payment", "repaymentDetailsPaidLabel": "Paid", "repaymentDetailsPrincipal": "Principal", "repaymentDetailsTotalLoanAmount": "Total loan amount", "repaymentDetailsViewDetails": "View details", "reviewCta": "Review", "reviewYourCreditLimit": "Now let’s review your credit limit and set your repayment preferences.", "securedBusinessLoanCardFeature1": "Borrow up to {maxLimit}", "@securedBusinessLoanCardFeature1": {"placeholders": {"maxLimit": {"example": "AED 1mil", "type": "String"}}}, "securedBusinessLoanCardFeature2": "Interest {minInterest}-{maxInterest}%", "@securedBusinessLoanCardFeature2": {"placeholders": {"maxInterest": {"example": "22", "type": "String"}, "minInterest": {"example": "17", "type": "String"}}}, "securedBusinessLoanCardFeature3": "Assignment letter from acquirer", "securedBusinessLoanCardSubtitle": "For businesses with POS or online payments settled to Wio account.", "securedBusinessLoanCardTitle": "Sales backed loan", "securedBusinessLoanLabel": "Sales backed loan", "securedLoanAssignementLetterDescription": "To start using your business loan, follow the instruction sent to {email} to complete your settlement assignment process.", "@securedLoanAssignementLetterDescription": {"placeholders": {"email": {"example": "<EMAIL>", "type": "String"}}}, "securedLoanAssignementLetterStep1": "Get the assignment letter ", "securedLoanAssignementLetterStep1Desc": "Reach out to your payment provider for an assignment letter confirming that payments are settled into your Wio account.", "securedLoanAssignementLetterStep2": "Share the letter with us", "securedLoanAssignementLetterStep2Desc": "Follow the detailed instructions sent to your email address on how to submit the assignment letter to <PERSON><PERSON>", "securedLoanAssignementLetterTitle": "Almost there", "securedLoanAssignmentLetterBannerTitle": "Share assignment letter", "securedLoanConfirmationDescription": "A valid assignment letter will be required to receive the loan and have funds credited to your account. If you can’t request one from your payment provider, select “Cancel” to continue with a simple loan.", "securedLoanConfirmationTitle": "Confirm you can provide an assignment letter from your payment provider", "securedLoanPageTitle": "Here’s how to get a sales backed loan", "securedLoanSelectionDescription": "For businesses with POS or online payments settled to Wio account.", "securedLoanSelectionTitle": "Sales backed loan", "securedLoanStep1Description": "Share your latest VAT statements (if applicable), company turnover, and bank account IBANs.", "securedLoanStep1Title": "Submit the application", "securedLoanStep2Description": "Wio will review your application and get back to you with an offer for your maximum loan limit.", "securedLoanStep2Title": "Receive a loan offer from Wio and select your borrowing amount", "securedLoanStep3Description": "To receive the funds, you’ll need to provide an assignment letter from your acquirer confirming that payments are settled into your Wio account.", "securedLoanStep3Title": "Share a valid assignment letter to receive the funds", "securedLoanWhatIsAssignmentLetter": "What is the assignment letter?", "selectFileBorrowingPowerTitle": "Select file", "selectFileFinancialStatementTitle": "Select Audited Financial Statement", "simpleBusinessLoanCardFeature1": "Borrow up to {maxLimit}", "@simpleBusinessLoanCardFeature1": {"placeholders": {"maxLimit": {"example": "AED 1mil", "type": "String"}}}, "simpleBusinessLoanCardFeature2": "Interest {minInterest}-{maxInterest}%", "@simpleBusinessLoanCardFeature2": {"placeholders": {"maxInterest": {"example": "22", "type": "String"}, "minInterest": {"example": "17", "type": "String"}}}, "simpleBusinessLoanCardFeature3": "No collateral required", "simpleBusinessLoanCardSubtitle": "No extra guarantees, every type of business can apply.", "simpleBusinessLoanCardTitle": "Simple business loan", "startDateLabel": "Start date", "timePeriodLabel": "Time period", "totalOutstandingLabel": "Current outstanding", "unSecuredBusinessLoanLabel": "Simple loan", "unsecuredLoanSelectionDescription": "No extra guarantees required every type of business can apply.", "unsecuredLoanSelectionTitle": "Simple loan", "updateCreditLimitRequestMoreSuccessButton": "Got it", "vatStatementSampleTitle": "VAT statement", "viewOfferLabel": "View offer", "wioBusinessLoanLabel": "Wio Business loan", "wioLineOfCreditLabel": "Wio line of credit"}