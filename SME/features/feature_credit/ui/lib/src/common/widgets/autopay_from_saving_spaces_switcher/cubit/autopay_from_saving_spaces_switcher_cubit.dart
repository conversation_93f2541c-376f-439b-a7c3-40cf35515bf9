import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ui/ui.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/autopay_from_saving_space_disable_confirmation/autopay_from_saving_space_disable_confirmation_bottom_sheet.dart';
import 'package:wio_feature_credit_ui/src/common/widgets/autopay_from_saving_spaces_switcher/config/autopay_from_saving_spaces_switcher_config.dart';
import 'package:wio_feature_credit_ui/src/common/widgets/autopay_from_saving_spaces_switcher/delegate/autopay_from_saving_spaces_switcher_delegate.dart';
import 'package:wio_feature_credit_ui/src/handlers/credit_error_handler/credit_error_handler.dart';

part 'autopay_from_saving_spaces_switcher_cubit.freezed.dart';
part 'autopay_from_saving_spaces_switcher_state.dart';

class AutopayFromSavingSpacesSwitcherCubit
    extends BaseCubit<AutopayFromSavingSpacesSwitcherState> {
  final AutopayFromSavingSpacesSwitcherDelegate _delegate;
  final ResponsiveDialogProvider _responsiveDialogProvider;
  final CreditErrorHandler _creditErrorHandler;
  final AutopayFromSavingSpacesSwitcherConfig _config;
  final CreditAnalytics _analytics;

  AutopayFromSavingSpacesSwitcherCubit({
    required AutopayFromSavingSpacesSwitcherDelegate delegate,
    required ResponsiveDialogProvider responsiveDialogProvider,
    required CreditErrorHandler creditErrorHandler,
    required AutopayFromSavingSpacesSwitcherConfig config,
    required CreditAnalytics analytics,
  })  : _delegate = delegate,
        _responsiveDialogProvider = responsiveDialogProvider,
        _creditErrorHandler = creditErrorHandler,
        _config = config,
        _analytics = analytics,
        super(const AutopayFromSavingSpacesSwitcherState.loading());

  Future<void> initialize() async {
    final currentValue = await _delegate.fetchCurrentValue();

    safeEmit(
      AutopayFromSavingSpacesSwitcherState.idle(
        isAutopayFromSavingSpacesEnabled: currentValue,
      ),
    );
  }

  void tryToToggleSwitcher() {
    state.mapOrNull(
      idle: (idleState) async {
        if (!idleState.isAutopayFromSavingSpacesEnabled) {
          // from Disabled to Enabled autopay
          _toggleSwitcher();
        } else {
          // from Enabled to Disabled autopay
          final result =
              await _responsiveDialogProvider.showBottomSheetOrDialog<bool>(
            content:
                const AutopayFromSavingSpaceDisableConfirmationBottomSheet(),
            config: const ResponsiveModalConfig(
              featureName: CreditFeatureNavigationConfig.name,
            ),
          );

          if (result ?? false) {
            // if confirmed, disable autopay
            _toggleSwitcher();
          } else {
            // just send analytics
            _analytics.toggleAutopayFromSavingSpaces(
              _config.uiId,
              isEnabled: idleState.isAutopayFromSavingSpacesEnabled,
              cancelledDisableOnConfirmation: true,
            );
          }
        }
      },
    );
  }

  void _toggleSwitcher() {
    state.mapOrNull(
      idle: (idleState) {
        // Toggling to new value
        final isEnabled = !idleState.isAutopayFromSavingSpacesEnabled;
        _delegate
            .switchAutopayFromSavingSpacesTo(value: isEnabled)
            .toStream()
            .doOnListen(
              () => safeEmit(idleState.toProcessing()),
            )
            .doOnData(_onToggleAutopaySuccess)
            .withError<Object>(_handleError)
            .complete();
      },
    );
  }

  void _onToggleAutopaySuccess(bool updatedValue) {
    state.mapOrNull(
      processing: (_) {
        final updatedState = AutopayFromSavingSpacesSwitcherState.idle(
          isAutopayFromSavingSpacesEnabled: updatedValue,
        );
        _analytics.toggleAutopayFromSavingSpaces(
          _config.uiId,
          isEnabled: updatedValue,
          // If autopay from ss is to be disabled, that means the user confirmed
          // the action. If enabled, we didn't ask for confirmation.
          cancelledDisableOnConfirmation: updatedValue ? null : false,
        );
        safeEmit(updatedState);
      },
    );
  }

  void _handleError(Object error) {
    state.mapOrNull(
      processing: (processingState) {
        safeEmit(processingState.toIdle());
      },
    );
    _creditErrorHandler.handleError(error);
  }

  @override
  String toString() {
    return 'AutopayFromSavingSpacesSwitcherCubit';
  }
}
