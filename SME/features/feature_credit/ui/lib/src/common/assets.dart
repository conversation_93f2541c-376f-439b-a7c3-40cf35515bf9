import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

abstract class Assets {
  static const featureCreditPackageName = 'wio_feature_credit_ui';

  static const LottieAnimationModel creditSwitchIntroAnimation =
      LottieAnimationModel(
    name: 'assets/animations/credit_mode_switch_intro.json',
    package: featureCreditPackageName,
  );

  static const CompanyImageModel posIntroImage = CompanyImageModel(
    image: CompanyImageProvider.asset(
      name: 'assets/images/pos_intro.png',
      package: featureCreditPackageName,
    ),
  );

  static const CompanyImageModel businessLoanIntroImage = CompanyImageModel(
    image: CompanyImageProvider.asset(
      name: 'assets/images/business_loan_intro.png',
      package: featureCreditPackageName,
    ),
  );

  static const businessLoanEnhancedIntroImagePath =
      'assets/images/business_loan_enhanced_intro.png';
}
