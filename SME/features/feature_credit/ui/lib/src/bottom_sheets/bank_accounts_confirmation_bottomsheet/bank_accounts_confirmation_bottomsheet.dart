import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/bank_accounts_confirmation_bottomsheet/config/bank_accounts_confirmation_bottomsheet_config.dart';
import 'package:wio_feature_credit_ui/src/navigation/configs/bottom_sheets/bank_accounts_confirmation_bottomsheet_navigation_config.dart';

class BankAccountsConfirmationBottomSheet extends StatelessWidget {
  final BankAccountsConfirmationBottomSheetConfig config;

  const BankAccountsConfirmationBottomSheet({
    required this.config,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Label(
            model: LabelModel(
              text: config.title,
              textStyle: CompanyTextStylePointer.h3medium,
              color: CompanyColorPointer.primary3,
            ),
          ),
          const SizedBox(height: 8),
          Label(
            model: LabelModel(
              text: config.description,
              textStyle: CompanyTextStylePointer.b3,
              color: CompanyColorPointer.secondary4,
            ),
          ),
          const SizedBox(height: 32),
          _ActionsCluster(reviewButtonText: config.reviewButtonText),
        ],
      ),
    );
  }
}

class _ActionsCluster extends StatelessWidget {
  final String reviewButtonText;

  const _ActionsCluster({
    required this.reviewButtonText,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = CreditLocalizations.of(context);
    final navigator = Navigator.of(context);

    return Row(
      children: [
        Expanded(
          child: Button(
            model: ButtonModel(
              title: reviewButtonText,
              theme: ButtonModelTheme.sme,
              type: ButtonType.secondary,
            ),
            onPressed: () =>
                navigator.pop<BankAccountsConfirmationBottomsheetResult>(
              const BankAccountsConfirmationBottomsheetResult.cancel(),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Button(
            model: ButtonModel(
              title: localizations
                  .creditOnlyWioBankAccountBottomSheetCtaConfirmText,
              theme: ButtonModelTheme.sme,
            ),
            onPressed: () =>
                navigator.pop<BankAccountsConfirmationBottomsheetResult>(
              const BankAccountsConfirmationBottomsheetResult.confirm(),
            ),
          ),
        ),
      ],
    );
  }
}
