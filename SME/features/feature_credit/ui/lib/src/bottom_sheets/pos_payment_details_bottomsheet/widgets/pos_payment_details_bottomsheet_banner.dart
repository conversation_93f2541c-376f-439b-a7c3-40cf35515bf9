import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_credit_ui/src/common/extensions/date_extensions.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/pos_account_determinator/pos_account_state/pos_account_state.dart';

class PosPaymentDetailsBottomSheetBanner extends StatelessWidget {
  final BasePosAccountState posAccount;
  const PosPaymentDetailsBottomSheetBanner({
    required this.posAccount,
    super.key,
  });

  (String? title, CompanyColorPointer? titleColor) _getTitleAndTitleColor(
    CreditLocalizations localizations,
  ) {
    return switch (posAccount) {
      NoDebtPosAccountState() => (null, null),
      PaymentsOnTrackPosAccountState() => (null, null),
      final MissedDailyPaymentPosAccountState missedPaymentState => (
          missedPaymentState.missedPaymentCount != null
              ? localizations
                  .posNextDailyPaymentBottomSheetMissedPaymentWarning(
                  missedPaymentState.missedPaymentCount!,
                )
              : null,
          CompanyColorPointer.secondary1,
        ),
      CreditPaybackOverdue() => (
          localizations.posBottomSheetMissedDueBannerTitle,
          CompanyColorPointer.secondary1,
        ),
    };
  }

  (String description, CompanyColorPointer descriptionColor)
      _getDescriptionAndDescriptionColor(
    CreditLocalizations localizations,
  ) {
    return switch (posAccount) {
      NoDebtPosAccountState() => (
          localizations.posNextDailyPaymentBottomSheetLockInfoBanner,
          CompanyColorPointer.secondary3,
        ),
      PaymentsOnTrackPosAccountState() => (
          localizations.posNextDailyPaymentBottomSheetLockInfoBanner,
          CompanyColorPointer.secondary3,
        ),
      final MissedDailyPaymentPosAccountState missedPaymentState => (
          missedPaymentState.isBorrowDisabled
              ? localizations.posBottomSheetBorrowPausedBannerDesc
              : localizations
                  .posNextDailyPaymentBottomSheetMissedPaymentBannerDesc(
                  missedPaymentState
                      .paymentDateToAvoidCreditLock.monthDayFormat,
                ),
          CompanyColorPointer.secondary1,
        ),
      final MissedMilestonePosAccountState missedMilestoneState => (
          localizations.posMissedMilestoneBannerDescription(
            missedMilestoneState.overDuePaymentDate?.monthDayFormat ?? '-',
            missedMilestoneState.dueDate?.monthDayFormat ?? '-',
          ),
          CompanyColorPointer.secondary1,
        ),
      final MilestoneOverduePosAccountState milestoneOverdueState => (
          localizations.posMilestoneOverdueBannerDescription(
            milestoneOverdueState.latePaymentFee.toCodeOnRightFormat(),
            milestoneOverdueState.dueDate?.monthDayFormat ?? '-',
          ),
          CompanyColorPointer.secondary1,
        ),
    };
  }

  CompanyIconPointer get _icon => switch (posAccount) {
        PaymentsOnTrackPosAccountState() => CompanyIconPointer.information,
        NoDebtPosAccountState() => CompanyIconPointer.information,
        MissedDailyPaymentPosAccountState() => CompanyIconPointer.warning,
        CreditPaybackOverdue() => CompanyIconPointer.warning,
      };

  CompanyColorPointer get _backgroundColor => switch (posAccount) {
        PaymentsOnTrackPosAccountState() => CompanyColorPointer.secondary6,
        NoDebtPosAccountState() => CompanyColorPointer.secondary6,
        MissedDailyPaymentPosAccountState() => CompanyColorPointer.secondary14,
        CreditPaybackOverdue() => CompanyColorPointer.secondary14,
      };

  @override
  Widget build(BuildContext context) {
    final localizations = CreditLocalizations.of(context);
    final (title, titleColor) = _getTitleAndTitleColor(localizations);
    final (description, descriptionColor) =
        _getDescriptionAndDescriptionColor(localizations);
    return AnnouncementBanner(
      AnnouncementBannerModel(
        title: title,
        titleColor: titleColor,
        icon: _icon,
        iconColor: CompanyColorPointer.secondary1,
        subtitle: description,
        subTitleColor: descriptionColor,
        backgroundColor: _backgroundColor,
        colorScheme: CompanyColorScheme.retailMobile(),
        subtitleMaxLines: 10,
      ),
    );
  }
}
