import 'dart:ui';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/borrowing_power_confirmation/borrowing_power_confirmation_bottom_sheet.dart';
import 'package:wio_feature_credit_ui/src/navigation/configs/screens/credit_statement_upload_page_navigation_config.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_statement_upload/config/credit_statement_upload_config.dart';

class BorrowingPowerFlowHandler {
  final NavigationProvider _navigationProvider;
  final FeatureToggleProvider _featureToggleProvider;
  final ResponsiveDialogProvider _responsiveDialogProvider;
  final CreditLocalizations _localizations;
  final Logger _logger;
  final CreditApplicationFlowSourceApp _applicationFlowSourceApp;
  final CreditApplicationInteractor _creditApplicationInteractor;

  const BorrowingPowerFlowHandler({
    required NavigationProvider navigationProvider,
    required FeatureToggleProvider featureToggleProvider,
    required ResponsiveDialogProvider responsiveDialogProvider,
    required CreditLocalizations localizations,
    required Logger logger,
    required CreditApplicationFlowSourceApp applicationFlowSourceApp,
    required CreditApplicationInteractor creditApplicationInteractor,
  })  : _navigationProvider = navigationProvider,
        _responsiveDialogProvider = responsiveDialogProvider,
        _featureToggleProvider = featureToggleProvider,
        _localizations = localizations,
        _logger = logger,
        _applicationFlowSourceApp = applicationFlowSourceApp,
        _creditApplicationInteractor = creditApplicationInteractor;

  bool get _borrowingPowerFlowNotEnabled => !_shouldRunBorrowingPowerFlow;

  bool get _shouldRunBorrowingPowerFlow {
    if (_applicationFlowSourceApp
        is EmbeddedLendingCreditApplicationFlowSourceApp) {
      return true;
    }

    return _featureToggleProvider
        .get(SmeMobileCreditFeatureToggles.isBorrowingPowerEnabled);
  }

  // Should be requestes from overview page start application button tap
  // and on in progress application entrypoint tap
  Future<void> run({
    required CreditApplication application,
    required VoidCallback onBorrowFlowNotRequired,
  }) async {
    try {
      final isBorrowingPowerFlowNotRequired =
          await _isBorrowingPowerFlowNotRequired(application);
      if (isBorrowingPowerFlowNotRequired) {
        onBorrowFlowNotRequired();
        return;
      }

      final result =
          await _responsiveDialogProvider.showBottomSheetOrDialog<bool>(
        content: BorrowingPowerConfirmationBottomSheet(
          companyName: application.companyName,
        ),
        config: const ResponsiveModalConfig(
          featureName: CreditFeatureNavigationConfig.name,
        ),
      );

      if (result ?? false) {
        return _navigationProvider.removeUntilFirstAndPush(
          CreditStatementUploadPageNavigationConfig(
            config: BorrowPowerProofUploadConfig(application: application),
          ),
        );
      } else {
        _navigationProvider.popUntilFirstRoute();
        _responsiveDialogProvider.showResponsiveToastMessage(
          _getBorrowingPowerWarningToastMessageConfig(
            _localizations.borrowingPowerWarningMessage,
          ),
        );
      }
    } on Object catch (e, stackTrace) {
      _logger.error(
        'borrow power flow handler failed',
        error: e,
        stackTrace: stackTrace,
      );
      _responsiveDialogProvider.showResponsiveToastMessage(
        NotificationToastMessageConfiguration.error(
          _localizations.creditGenericErrorScreenDescription,
        ),
      );
    }
  }

  /// Checks all the conditions to determine
  /// if the borrowing power flow is not required.
  Future<bool> _isBorrowingPowerFlowNotRequired(
    CreditApplication application,
  ) async {
    if (_borrowingPowerFlowNotEnabled) return true;

    final isApplicationRequiredToUploadBorrowingPowerProof = application
            .onboardingFlowControls
            ?.shouldUploadBorrowingPowerVerificationDocument ??
        false;
    if (!isApplicationRequiredToUploadBorrowingPowerProof) return true;

    // Check for the documents done the last so it would be triggered only if
    // code has not returned yet.
    //
    // We fetch the documents to check if the user has already uploaded
    // the borrowing power proof.
    // This is the edge case where the user has already uploaded the
    // borrowing power proof but poped out of the flow and reentered again
    // without application being refreshed.
    final documents = await _creditApplicationInteractor.getDocuments(
      application.id,
    );
    final isBorrowingPowerProofUploaded = documents.any(
      (doc) => doc.documentType == CreditDocumentType.borrowingPowerProof,
    );

    return isBorrowingPowerProofUploaded;
  }

  NotificationToastMessageConfiguration
      _getBorrowingPowerWarningToastMessageConfig(String message) {
    return NotificationToastMessageConfiguration.icon(
      foregroundColor: CompanyColorPointer.primary2,
      backgroundColor: CompanyColorPointer.surface12,
      iconColor: CompanyColorPointer.secondary9,
      progressColor: CompanyColorPointer.secondary9,
      leadingIcon: const GraphicAssetPointer.icon(
        CompanyIconPointer.warning,
      ),
      label: message,
    );
  }
}
