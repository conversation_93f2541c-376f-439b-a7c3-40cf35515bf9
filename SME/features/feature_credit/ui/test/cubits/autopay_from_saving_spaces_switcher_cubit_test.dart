import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:tests/tests.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/src/common/widgets/autopay_from_saving_spaces_switcher/config/autopay_from_saving_spaces_switcher_config.dart';
import 'package:wio_feature_credit_ui/src/common/widgets/autopay_from_saving_spaces_switcher/cubit/autopay_from_saving_spaces_switcher_cubit.dart';

import '../helpers.dart';
import '../mocks.dart';

void main() {
  group('AutopayFromSavingSpacesSwitcherCubit >', () {
    late AutopayFromSavingSpacesSwitcherCubit cubit;
    late MockAutopayFromSavingSpacesSwitcherDelegate delegate;
    late ResponsiveDialogProvider responsiveDialogProvider;
    late MockCreditErrorHandler creditErrorHandler;
    late AutopayFromSavingSpacesSwitcherConfig config;
    late CreditAnalytics analytics;

    setUp(() {
      delegate = MockAutopayFromSavingSpacesSwitcherDelegate();
      responsiveDialogProvider = MockResponsiveDialogProvider();
      creditErrorHandler = MockCreditErrorHandler();
      analytics = MockCreditAnalytics();
      config = AutopayFromSavingSpacesSwitcherConfig.account(
        accountId: CreditTestsHelpers.account.id,
      );

      cubit = AutopayFromSavingSpacesSwitcherCubit(
        delegate: delegate,
        creditErrorHandler: creditErrorHandler,
        config: config,
        analytics: analytics,
        responsiveDialogProvider: responsiveDialogProvider,
      );

      CreditTestsHelpers.registerFallbackValues();
    });

    test('initial state is loading', () {
      expect(cubit.state, const AutopayFromSavingSpacesSwitcherState.loading());
    });

    blocTest<AutopayFromSavingSpacesSwitcherCubit,
        AutopayFromSavingSpacesSwitcherState>(
      'initialize sets the correct state',
      build: () => cubit,
      setUp: () {
        when(() => delegate.fetchCurrentValue()).justAnswerAsync(true);
      },
      act: (cubit) => cubit.initialize(),
      expect: () => const [
        AutopayFromSavingSpacesSwitcherState.idle(
          isAutopayFromSavingSpacesEnabled: true,
        ),
      ],
    );

    blocTest<AutopayFromSavingSpacesSwitcherCubit,
        AutopayFromSavingSpacesSwitcherState>(
      'tryToToggleSwitcher toggles the switcher with confirmation '
      'from enabled to disabled',
      build: () => cubit,
      seed: () => const AutopayFromSavingSpacesSwitcherState.idle(
        isAutopayFromSavingSpacesEnabled: true,
      ),
      setUp: () {
        when(
          () => responsiveDialogProvider.showBottomSheetOrDialog<bool>(any()),
        ).justAnswerAsync(true);
        when(() => delegate.switchAutopayFromSavingSpacesTo(value: false))
            .justAnswerAsync(false);
      },
      act: (cubit) => cubit.tryToToggleSwitcher(),
      expect: () => [
        const AutopayFromSavingSpacesSwitcherState.processing(
          isAutopayFromSavingSpacesEnabled: true,
        ),
        const AutopayFromSavingSpacesSwitcherState.idle(
          isAutopayFromSavingSpacesEnabled: false,
        ),
      ],
      verify: (cubit) {
        verify(
            () => responsiveDialogProvider.showBottomSheetOrDialog<bool>(
              content: any(named: 'content'),
              config: any(named: 'config'),
            ),
        ).calledOnce;
        verify(
          () => delegate.switchAutopayFromSavingSpacesTo(
            value: any(named: 'value'),
          ),
        ).calledOnce;
        verify(
          () => analytics.toggleAutopayFromSavingSpaces(
            config.uiId,
            isEnabled: false,
            cancelledDisableOnConfirmation: false,
          ),
        ).calledOnce;
      },
    );

    blocTest<AutopayFromSavingSpacesSwitcherCubit,
        AutopayFromSavingSpacesSwitcherState>(
      'tryToToggleSwitcher toggles the switcher with confirmation canceled '
      'from enabled to disabled',
      build: () => cubit,
      seed: () => const AutopayFromSavingSpacesSwitcherState.idle(
        isAutopayFromSavingSpacesEnabled: true,
      ),
      setUp: () {
        when(
            () => responsiveDialogProvider.showBottomSheetOrDialog<bool>(
              content: any(named: 'content'),
              config: any(named: 'config'),
            ),
        ).justAnswerAsync(false);
        when(() => delegate.switchAutopayFromSavingSpacesTo(value: false))
            .justAnswerAsync(false);
      },
      act: (cubit) => cubit.tryToToggleSwitcher(),
      expect: () => const <AutopayFromSavingSpacesSwitcherState>[],
      verify: (cubit) {
        verify(
          () => responsiveDialogProvider.showBottomSheetOrDialog<bool>(any()),
        ).calledOnce;
        verifyNever(
          () => delegate.switchAutopayFromSavingSpacesTo(
            value: any(named: 'value'),
          ),
        );
        verify(
          () => analytics.toggleAutopayFromSavingSpaces(
            config.uiId,
            isEnabled: true,
            cancelledDisableOnConfirmation: true,
          ),
        ).calledOnce;
      },
    );

    blocTest<AutopayFromSavingSpacesSwitcherCubit,
        AutopayFromSavingSpacesSwitcherState>(
      'tryToToggleSwitcher toggles the switcher without confirmation '
      'from disabled to enabled',
      build: () => cubit,
      seed: () => const AutopayFromSavingSpacesSwitcherState.idle(
        isAutopayFromSavingSpacesEnabled: false,
      ),
      setUp: () {
        when(() => delegate.switchAutopayFromSavingSpacesTo(value: true))
            .justAnswerAsync(true);
      },
      act: (cubit) => cubit.tryToToggleSwitcher(),
      expect: () => [
        const AutopayFromSavingSpacesSwitcherState.processing(
          isAutopayFromSavingSpacesEnabled: false,
        ),
        const AutopayFromSavingSpacesSwitcherState.idle(
          isAutopayFromSavingSpacesEnabled: true,
        ),
      ],
      verify: (cubit) {
        verifyNever(
          () => responsiveDialogProvider.showBottomSheetOrDialog<bool>(any()),
        );
        verify(
          () => delegate.switchAutopayFromSavingSpacesTo(
            value: any(named: 'value'),
          ),
        ).calledOnce;
        verify(
          () => analytics.toggleAutopayFromSavingSpaces(
            config.uiId,
            isEnabled: true,
          ),
        ).calledOnce;
      },
    );
  });
}
