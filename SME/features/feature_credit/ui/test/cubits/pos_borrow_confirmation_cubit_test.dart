import 'dart:ui';

import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:ui/ota_lokalisation/ota_lokalization.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_credit_ui/src/bottom_sheets/pos_interest_info_bottomsheet/config/pos_interest_info_bottomsheet_config.dart';
import 'package:wio_feature_credit_ui/src/common/status_screen_config_factory.dart';
import 'package:wio_feature_credit_ui/src/handlers/credit_error_handler/credit_error_handler.dart';
import 'package:wio_feature_credit_ui/src/navigation/configs/bottom_sheets/pos_interest_info_bottom_sheet_navigation_config.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/borrow_confirmation/config/pos_borrow_confirmation_page_config.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/borrow_confirmation/cubit/pos_borrow_confirmation_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/borrow_confirmation/cubit/pos_borrow_confirmation_state.dart';
import 'package:wio_feature_credit_ui/src/screens/pos_credit/pos_account_determinator/pos_account_state/pos_account_state.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

import '../helpers.dart';
import '../mocks.dart';

void main() {
  late PosBorrowConfirmationCubit cubit;
  late PosInteractor posInteractor;
  late NavigationProvider navigationProvider;
  late CreditErrorHandler errorHandler;
  late CreditLocalizations localizations;
  late PosBorrowConfirmationPageConfig config;

  setUp(() async {
    posInteractor = MockPosInteractor();
    navigationProvider = MockNavigationProvider();
    errorHandler = MockCreditErrorHandler();
    config = PosBorrowConfirmationPageConfig(
      posAccountState: FirstTimeBorrowPosAccountState(
        account: CreditTestsHelpers.posCreditAccount,
      ),
      loanAmount: InstallmentAmount(
        fee: CreditTestsHelpers.defaultLimit,
        principal: CreditTestsHelpers.defaultLimit,
        interest: CreditTestsHelpers.defaultLimit,
      ),
      dailyPayment: InstallmentAmount(
        fee: CreditTestsHelpers.defaultLimit,
        principal: CreditTestsHelpers.defaultLimit,
        interest: CreditTestsHelpers.defaultLimit,
      ),
      installments: [CreditTestsHelpers.installment(DateTime(2024))],
      borrowAmount: Money.fromIntWithCurrency(
        1000,
        Currency.aed,
      ),
      loanTermInDays: 90,
    );
    await OtaLocalizationImpl().initMock();
    localizations = await CreditLocalizations.load(const Locale('en'));

    cubit = PosBorrowConfirmationCubit(
      posInteractor: posInteractor,
      navigationProvider: navigationProvider,
      errorHandler: errorHandler,
      localizations: localizations,
      config: config,
    );

    CreditTestsHelpers.registerFallbackValues();
    registerFallbackValue(
      PosInterestInfoBottomSheetNavigationConfig(
        config: PosInterestInfoBottomSheetConfig(
          interestRate: 1,
          loanPeriodInDays: 1,
        ),
      ),
    );
  });

  blocTest<PosBorrowConfirmationCubit, PosBorrowConfirmationState>(
    'on submit',
    setUp: () {
      when(
        () => posInteractor.makeDisbursement(
          accountId: 'id',
          amount: config.borrowAmount,
        ),
      ).justCompleteAsync();
    },
    build: () => cubit,
    act: (cubit) => cubit.onSubmit(),
    verify: (cubit) {
      verify(
        () => posInteractor.makeDisbursement(
          accountId: 'id',
          amount: config.borrowAmount,
        ),
      ).calledOnce;
      verify(
        () => navigationProvider.navigateTo(
          StatusScreenConfigFactory.success(
            title: localizations.payCreditSuccessPageTitle,
            subtitle: localizations
                .posBorrowSuccessSubtitle(Currency.aed.code.toUpperCase()),
            primaryButtonTitle: localizations.closeCta,
          ),
        ),
      ).calledOnce;
    },
  );

  blocTest<PosBorrowConfirmationCubit, PosBorrowConfirmationState>(
    'error on submit',
    setUp: () {
      when(
        () => posInteractor.makeDisbursement(
          accountId: 'id',
          amount: config.borrowAmount,
        ),
      ).justThrowAsync(Exception);
    },
    build: () => cubit,
    act: (cubit) => cubit.onSubmit(),
    verify: (cubit) {
      verify(
        () => posInteractor.makeDisbursement(
          accountId: 'id',
          amount: config.borrowAmount,
        ),
      ).calledOnce;
      verify(
        () => errorHandler.handleError(
          Exception,
          stackTrace: any(named: 'stackTrace'),
        ),
      ).calledOnce;
      verifyZeroInteractions(navigationProvider);
    },
  );

  blocTest<PosBorrowConfirmationCubit, PosBorrowConfirmationState>(
    'on interest info bottomsheet',
    build: () => cubit,
    setUp: () {
      when(
        () => navigationProvider.showBottomSheet<void>(any()),
      ).justCompleteAsync();
    },
    act: (cubit) => cubit.onShowInterestInfoBottomsheet(),
    verify: (cubit) {
      verify(
        () => navigationProvider.showBottomSheet<void>(any()),
      ).calledOnce;
    },
  );
}
