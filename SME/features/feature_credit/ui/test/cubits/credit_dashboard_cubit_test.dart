import 'package:bloc_test/bloc_test.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:domain/data/data.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:sme_feature_transactions_api/feature_transactions_api.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_context_faq_api/wio_common_feature_context_faq_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_credit_ui/src/handlers/credit_error_handler/credit_error_handler.dart';
import 'package:wio_feature_credit_ui/src/navigation/configs/bottom_sheets/amount_on_hold_bottom_sheet_config.dart';
import 'package:wio_feature_credit_ui/src/navigation/configs/screens/autopay_update_page_navigation_config.dart';
import 'package:wio_feature_credit_ui/src/navigation/configs/screens/manage_credit_page_nav_config.dart';
import 'package:wio_feature_credit_ui/src/navigation/configs/screens/pay_credit_page_navigation_config.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_dashboard/dashboard/credit_dashboard_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_dashboard/dashboard/credit_dashboard_page.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_dashboard/dashboard/credit_dashboard_state.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_dashboard/pay_credit/config/pay_credit_config.dart';
import 'package:wio_feature_easy_cash_api/easy_cash_api.dart';
import 'package:wio_feature_faq_api/domain/model/exports.dart';
import 'package:wio_feature_faq_api/faq_api.dart' as common;
import 'package:wio_feature_loan_api/navigation/screens/loan_statements_page_navigation_config.dart';

import '../helpers.dart';
import '../mocks.dart';

typedef _Cubit = CreditDashboardCubit;
typedef _State = CreditDashboardState;

void main() {
  const config = CreditDashboardConfig(accountId: 'id');
  late CreditDashboardCubit cubit;
  late CreditAccountInteractor accountInteractor;
  late Logger logger;
  late NavigationProvider navigationProvider;
  late CreditLocalizations localizations;
  late TransactionsMediator transactionsMediator;
  late CreditApplicationInteractor creditApplicationInteractor;
  late FeatureToggleProvider featureToggleProvider;
  late EasyCashInteractor easyCashInteractor;
  late EasyCashFlow easyCashFlow;
  late CreditErrorHandler errorHandler;
  late CreditAnalytics analytics;
  late CreditFeeFreePeriodTooltipInteractor
      creditFeeFreePeriodTooltipInteractor;
  late common.FAQsInteractor faqInteractor;

  setUpAll(() {
    registerFallbackValue(
      const FeatureToggleKey(
        key: 'key',
        defaultValue: false,
      ),
    );
  });

  final accounts = [
    DetailedCreditAccount(
      account: CreditTestsHelpers.creditAccount,
      autopay: CreditTestsHelpers.autopay,
    ),
    DetailedCreditAccount(
      account: CreditTestsHelpers.easyCashAccount,
      autopay: CreditTestsHelpers.autopay,
    ),
  ];

  final accs = [
    CreditTestsHelpers.creditAccount,
    CreditTestsHelpers.easyCashAccount,
    CreditTestsHelpers.posCreditAccount,
  ];

  const creditFeeFreePeriodFaq = CreditTestsHelpers.creditFeeFreePeriodFaq;

  setUp(() {
    registerFallbackValue(FakeResponsiveModalConfig());
    accountInteractor = MockCreditAccountInteractor();
    logger = MockLogger();
    navigationProvider = MockNavigationProvider();
    localizations = MockCreditLocalizations();
    transactionsMediator = MockSmeTransactionsMediator();
    creditApplicationInteractor = MockCreditApplicationInteractor();
    featureToggleProvider = MockFeatureToggleProvider();
    easyCashInteractor = MockEasyCashInteractor();
    easyCashFlow = MockEasyCashFlow();
    errorHandler = MockCreditErrorHandler();
    analytics = MockCreditAnalytics();
    creditFeeFreePeriodTooltipInteractor =
        MockCreditFeeFreePeriodTooltipInteractor();
    faqInteractor = MockFAQsInteractor();

    when(
      () => accountInteractor.observeCreditAccounts(),
    ).justAnswerAsync(
      Data(),
    );

    when(
      () => featureToggleProvider.get(
        SmeMobileCreditFeatureToggles
            .isCreditFeeFreePeriodExpirationTooltipEnabled,
      ),
    ).thenReturn(false);

    cubit = CreditDashboardCubit(
      config: config,
      accountInteractor: accountInteractor,
      logger: logger,
      navigationProvider: navigationProvider,
      localizations: localizations,
      transactionsMediator: transactionsMediator,
      creditApplicationInteractor: creditApplicationInteractor,
      featureToggleProvider: featureToggleProvider,
      easyCashInteractor: easyCashInteractor,
      easyCashFlow: easyCashFlow,
      errorHandler: errorHandler,
      analytics: analytics,
      creditFeeFreePeriodTooltipInteractor:
          creditFeeFreePeriodTooltipInteractor,
      faqInteractor: faqInteractor,
    );

    CreditTestsHelpers.registerFallbackValues();
  });

  group('initialisation tests', () {
    blocTest<_Cubit, _State>(
      'successful case',
      build: () => cubit,
      setUp: () {
        when(() => accountInteractor.getAccounts()).justAnswerAsync(accs);

        when(() => accountInteractor.getAutopayInfo(any()))
            .justAnswerAsync(CreditTestsHelpers.autopay);

        when(
          () => creditApplicationInteractor.getProductInfo(
            productType: any(
              named: 'productType',
            ),
          ),
        ).justAnswerAsync(CreditTestsHelpers.productInfo);

        when(
          () => featureToggleProvider.get(
            SmeMobileCreditFeatureToggles
                .isCreditFeeFreePeriodExpirationTooltipEnabled,
          ),
        ).thenReturn(true);

        when(
          () => creditFeeFreePeriodTooltipInteractor
              .shouldShowFeeFreePeriodExpirationTooltip(any(), any()),
        ).justAnswerAsync(true);
      },
      act: (cubit) => cubit.initialize(),
      expect: () => [
        _State.idle(
          selectedTabIndex: 0,
          detailedAccounts: accounts,
          creditProductInfo: CreditTestsHelpers.productInfo,
          shouldShowFeeFreePeriodExpirationTooltip: true,
        ),
      ],
      verify: (cubit) {
        verify(
          () => analytics.creditDashboardOpened(CreditUiId.mainDashboard),
        ).calledOnce;
      },
    );

    blocTest<_Cubit, _State>(
      'should set selected tab index based on account id',
      build: () => CreditDashboardCubit(
        config: const CreditDashboardConfig(accountId: 'easyCashAccountId'),
        accountInteractor: accountInteractor,
        logger: logger,
        navigationProvider: navigationProvider,
        localizations: localizations,
        transactionsMediator: transactionsMediator,
        creditApplicationInteractor: creditApplicationInteractor,
        featureToggleProvider: featureToggleProvider,
        easyCashInteractor: easyCashInteractor,
        easyCashFlow: easyCashFlow,
        errorHandler: errorHandler,
        analytics: analytics,
        creditFeeFreePeriodTooltipInteractor:
            creditFeeFreePeriodTooltipInteractor,
        faqInteractor: faqInteractor,
      ),
      setUp: () {
        when(() => accountInteractor.getAccounts()).justAnswerAsync(accs);

        when(() => accountInteractor.getAutopayInfo(any()))
            .justAnswerAsync(CreditTestsHelpers.autopay);

        when(
          () => creditApplicationInteractor.getProductInfo(
            productType: any(
              named: 'productType',
            ),
          ),
        ).justAnswerAsync(CreditTestsHelpers.productInfo);

        when(
          () => featureToggleProvider.get(
            SmeMobileCreditFeatureToggles
                .isCreditFeeFreePeriodExpirationTooltipEnabled,
          ),
        ).thenReturn(true);

        when(
          () => creditFeeFreePeriodTooltipInteractor
              .shouldShowFeeFreePeriodExpirationTooltip(any(), any()),
        ).justAnswerAsync(true);
      },
      act: (cubit) => cubit.initialize(),
      expect: () => [
        _State.idle(
          selectedTabIndex: 1,
          detailedAccounts: accounts,
          creditProductInfo: CreditTestsHelpers.productInfo,
          shouldShowFeeFreePeriodExpirationTooltip: true,
        ),
      ],
      verify: (cubit) {
        verify(
          () => analytics.creditDashboardOpened(CreditUiId.mainDashboard),
        ).calledOnce;
      },
    );

    blocTest<_Cubit, _State>(
      'should emit failed state',
      build: () => cubit,
      setUp: () {
        when(() => accountInteractor.getAccounts()).justThrowAsync(Exception());

        when(() => accountInteractor.getAutopayInfo(any()))
            .justAnswerAsync(CreditTestsHelpers.autopay);

        when(
          () => creditApplicationInteractor.getProductInfo(
            productType: any(
              named: 'productType',
            ),
          ),
        ).justAnswerAsync(CreditTestsHelpers.productInfo);
      },
      act: (cubit) => cubit.initialize(),
      expect: () => <_State>[
        const CreditDashboardState.failed(),
      ],
    );

    blocTest<_Cubit, _State>(
      'easy cash available',
      build: () => cubit,
      setUp: () {
        when(() => accountInteractor.getAccounts()).justAnswerAsync(accs);

        when(() => accountInteractor.getAutopayInfo(any()))
            .justAnswerAsync(CreditTestsHelpers.autopay);

        when(
          () => creditApplicationInteractor.getProductInfo(
            productType: any(
              named: 'productType',
            ),
          ),
        ).justAnswerAsync(CreditTestsHelpers.productInfo);

        when(() => easyCashInteractor.getEasyCashLimit(any())).justAnswerAsync(
          CreditTestsHelpers.easyCashLimit(CreditTestsHelpers.defaultLimit),
        );
      },
      act: (cubit) => cubit.initialize(),
      expect: () => [
        _State.idle(
          selectedTabIndex: 0,
          detailedAccounts: accounts,
          creditProductInfo: CreditTestsHelpers.productInfo,
          easyCashLimit: CreditTestsHelpers.easyCashLimit(
            CreditTestsHelpers.defaultLimit,
          ),
        ),
      ],
    );
  });

  group('on button click tests', () {
    blocTest<_Cubit, _State>(
      'on click pay credit',
      build: () => cubit,
      setUp: () {
        when(() => accountInteractor.getAccounts()).justAnswerAsync(accs);

        when(
          () => navigationProvider.push<CreditPayNavigationResult>(
            PayCreditPageNavigationConfig(
              PayCreditConfig.forAccount(CreditTestsHelpers.creditAccount),
            ),
          ),
        ).justAnswerAsync(
          const CreditPayNavigationResult(isSuccessful: true),
        );
      },
      seed: () => _State.idle(
        selectedTabIndex: 0,
        detailedAccounts: accounts,
        creditProductInfo: CreditTestsHelpers.productInfo,
      ),
      act: (cubit) => cubit.onPayCredit(),
      verify: (_) {
        verify(
          () => navigationProvider.push<CreditPayNavigationResult>(any()),
        ).calledOnce;
        verifyZeroInteractions(logger);
      },
    );

    blocTest<_Cubit, _State>(
      'on click should on amount hold',
      build: () => cubit,
      setUp: () {
        when(() => navigationProvider.showBottomSheet<void>(any()))
            .justCompleteAsync();
      },
      seed: () => _State.idle(
        selectedTabIndex: 0,
        detailedAccounts: accounts,
        creditProductInfo: CreditTestsHelpers.productInfo,
      ),
      act: (cubit) => cubit.showAmountOnHoldBottomSheet(),
      verify: (_) {
        verify(
          () => navigationProvider.showBottomSheet<void>(
            const AmountOnHoldBottomSheetConfig(),
          ),
        ).calledOnce;
        verifyZeroInteractions(logger);
      },
    );

    blocTest<_Cubit, _State>(
      'on click manage credit',
      build: () => cubit,
      setUp: () {
        when(() => navigationProvider.push(any())).justAnswerEmptyAsync();
      },
      seed: () => _State.idle(
        selectedTabIndex: 0,
        detailedAccounts: accounts,
        creditProductInfo: CreditTestsHelpers.productInfo,
      ),
      act: (cubit) => cubit.onManage(),
      verify: (_) {
        verify(
          () => navigationProvider.push(
            ManageCreditPageNavigationConfig(
              account: CreditTestsHelpers.creditAccount,
            ),
          ),
        ).calledOnce;
        verifyZeroInteractions(logger);
      },
    );

    blocTest<_Cubit, _State>(
      'on click statements',
      build: () => cubit,
      setUp: () {
        when(() => navigationProvider.push(any())).justAnswerEmptyAsync();
      },
      seed: () => _State.idle(
        selectedTabIndex: 0,
        detailedAccounts: accounts,
        creditProductInfo: CreditTestsHelpers.productInfo,
      ),
      act: (cubit) => cubit.onStatements(),
      verify: (_) {
        verify(
          () => navigationProvider.push(
            LoanStatementsPageNavigationConfig(
              loanAccountId: CreditTestsHelpers.creditAccount.id,
              loanProductIdentifier:
                  CreditTestsHelpers.creditAccount.productIdentifier,
            ),
          ),
        ).calledOnce;
        verifyZeroInteractions(logger);
      },
    );

    blocTest<_Cubit, _State>(
      'on click edit autopay',
      build: () => cubit,
      setUp: () {
        when(() => navigationProvider.push(any())).justAnswerEmptyAsync();
      },
      seed: () => _State.idle(
        selectedTabIndex: 0,
        detailedAccounts: accounts,
        creditProductInfo: CreditTestsHelpers.productInfo,
      ),
      act: (cubit) => cubit.onEditAutoPay(),
      verify: (_) {
        verify(
          () => navigationProvider.push(
            AutopayUpdatePageNavigationConfig(
              account: CreditTestsHelpers.creditAccount,
            ),
          ),
        ).calledOnce;
        verifyZeroInteractions(logger);
      },
    );

    blocTest<_Cubit, _State>(
      'on click show autopay fee calculation info',
      build: () => cubit,
      setUp: () {
        when(() => navigationProvider.showBottomSheet<void>(any()))
            .justAnswerEmptyAsync();
      },
      seed: () => _State.idle(
        selectedTabIndex: 0,
        detailedAccounts: accounts,
        creditProductInfo: CreditTestsHelpers.productInfo,
      ),
      act: (cubit) => cubit.showAutopayFeeCalculationInfo(),
      verify: (_) {
        verify(() => navigationProvider.showBottomSheet<void>(any()))
            .calledOnce;
      },
    );

    blocTest<_Cubit, _State>(
      'on click no fee label',
      build: () => cubit,
      setUp: () {
        when(() => navigationProvider.showBottomSheet<void>(any()))
            .justAnswerEmptyAsync();
      },
      seed: () => _State.idle(
        selectedTabIndex: 0,
        detailedAccounts: accounts,
        creditProductInfo: CreditTestsHelpers.productInfo,
      ),
      act: (cubit) => cubit.showAutoPayFeeDetailsBottomSheet(),
      verify: (_) {
        verify(() => navigationProvider.showBottomSheet<void>(any()))
            .calledOnce;
        verify(
          () => analytics.helpOpened(CreditUiId.creditDashboard),
        ).calledOnce;
        verify(
          () => analytics.helpClosed(
            CreditUiId.creditDashboard,
            duration: any(named: 'duration'),
          ),
        ).calledOnce;
      },
    );

    blocTest<_Cubit, _State>(
      'on click learn more (pending transaction sum)',
      build: () => cubit,
      setUp: () {
        when(() => navigationProvider.showBottomSheet<void>(any()))
            .justAnswerEmptyAsync();
      },
      seed: () => _State.idle(
        selectedTabIndex: 0,
        detailedAccounts: accounts,
        creditProductInfo: CreditTestsHelpers.productInfo,
      ),
      act: (cubit) => cubit.showAmountOnHoldBottomSheet(),
      verify: (_) {
        verify(
          () => navigationProvider
              .showBottomSheet<void>(const AmountOnHoldBottomSheetConfig()),
        ).calledOnce;
      },
    );

    blocTest<_Cubit, _State>(
      'on help with selected account as credit card',
      build: () => cubit,
      setUp: () {
        when(() => navigationProvider.showBottomSheet<bool>(any()))
            .justCompleteAsync();
      },
      seed: () => _State.idle(
        selectedTabIndex: 0,
        detailedAccounts: accounts,
        creditProductInfo: CreditTestsHelpers.productInfo,
      ),
      act: (cubit) => cubit.onHelp(),
      verify: (_) {
        verify(
          () => navigationProvider.showBottomSheet<bool>(
            const ContextFaqBottomSheetNavigationConfig(
              tags: [ContextFaqTags.smeCredit],
              fromScreen: CreditFeatureNavigationConfig.name,
            ),
          ),
        ).calledOnce;
      },
    );

    blocTest<_Cubit, _State>(
      'on help with selected account as easy cash',
      build: () => cubit,
      setUp: () {
        when(() => navigationProvider.showBottomSheet<bool>(any()))
            .justCompleteAsync();
      },
      seed: () => _State.idle(
        selectedTabIndex: 1,
        detailedAccounts: accounts,
        creditProductInfo: CreditTestsHelpers.productInfo,
      ),
      act: (cubit) => cubit.onHelp(),
      verify: (_) {
        verify(
          () => navigationProvider.showBottomSheet<bool>(
            const ContextFaqBottomSheetNavigationConfig(
              tags: [ContextFaqTags.easyCash],
              fromScreen: CreditFeatureNavigationConfig.name,
            ),
          ),
        ).calledOnce;
      },
    );
  });

  group('on retry', () {
    blocTest<_Cubit, _State>(
      'successful retry',
      build: () => cubit,
      seed: () => const _State.failed(),
      setUp: () {
        when(() => accountInteractor.getAccounts()).justAnswerAsync(accs);
        when(() => accountInteractor.getAutopayInfo(any()))
            .justAnswerAsync(CreditTestsHelpers.autopay);

        when(
          () => creditApplicationInteractor.getProductInfo(
            productType: any(
              named: 'productType',
            ),
          ),
        ).justAnswerAsync(CreditTestsHelpers.productInfo);
      },
      act: (cubit) => cubit.onRetry(),
      expect: () => [
        const _State.loading(),
        _State.idle(
          selectedTabIndex: 0,
          detailedAccounts: accounts,
          creditProductInfo: CreditTestsHelpers.productInfo,
        ),
      ],
    );
  });

  group('refresh tests', () {
    blocTest<_Cubit, _State>(
      'successful refresh',
      build: () => cubit,
      seed: () => _State.idle(
        selectedTabIndex: 0,
        detailedAccounts: accounts,
        creditProductInfo: CreditTestsHelpers.productInfo,
      ),
      setUp: () {
        when(() => accountInteractor.getAccounts()).justAnswerAsync([
          CreditTestsHelpers.creditAccount.copyWith(
            loanAmount: Money.fromNumWithCurrency(100, Currency.aed),
          ),
          CreditTestsHelpers.easyCashAccount,
        ]);

        when(() => accountInteractor.getAutopayInfo(any()))
            .justAnswerAsync(CreditTestsHelpers.autopay);

        when(
          () => creditApplicationInteractor.getProductInfo(
            productType: any(named: 'productType'),
          ),
        ).justAnswerAsync(CreditTestsHelpers.productInfo);

        when(() => localizations.creditDashboardRefreshError)
            .thenReturn('message');
      },
      act: (cubit) => cubit.onRefresh(),
      expect: () => [
        _State.idle(
          selectedTabIndex: 0,
          detailedAccounts: [
            DetailedCreditAccount(
              account: CreditTestsHelpers.creditAccount.copyWith(
                loanAmount: Money.fromNumWithCurrency(100, Currency.aed),
              ),
              autopay: CreditTestsHelpers.autopay,
            ),
            DetailedCreditAccount(
              account: CreditTestsHelpers.easyCashAccount,
              autopay: CreditTestsHelpers.autopay,
            ),
          ],
          creditProductInfo: CreditTestsHelpers.productInfo,
        ),
      ],
    );

    blocTest<_Cubit, _State>(
      'error during refresh',
      build: () => cubit,
      seed: () => _State.idle(
        selectedTabIndex: 0,
        detailedAccounts: accounts,
        creditProductInfo: CreditTestsHelpers.productInfo,
      ),
      setUp: () {
        when(() => accountInteractor.getAccountById(any())).justThrowAsync(
          Exception(),
        );

        when(() => accountInteractor.getAutopayInfo(any()))
            .justAnswerAsync(CreditTestsHelpers.autopay);

        when(() => localizations.creditDashboardRefreshError)
            .thenReturn('message');

        when(
          () => errorHandler.showErrorSnackbar(message: any(named: 'message')),
        ).justComplete();
      },
      act: (cubit) => cubit.onRefresh(),
      expect: () => <_State>[],
      verify: (_) {
        verify(
          () => errorHandler.showErrorSnackbar(message: any(named: 'message')),
        ).calledOnce;
      },
    );
  });

  group('locked credit account tests >', () {
    final account =
        CreditTestsHelpers.creditAccount.copyWith(mambuSubState: 'LOCKED');
    final autopay = CreditTestsHelpers.autopay.copyWith(
      delinquentAccountDetails: CreditTestsHelpers.delinquentAccountDetails,
    );

    blocTest<_Cubit, _State>(
      'on locked account banner info icon click',
      build: () => cubit,
      seed: () => _State.idle(
        selectedTabIndex: 0,
        detailedAccounts: [
          DetailedCreditAccount(
            account: account,
            autopay: autopay,
          ),
          DetailedCreditAccount(
            account: CreditTestsHelpers.easyCashAccount,
            autopay: CreditTestsHelpers.autopay,
          ),
        ],
        creditProductInfo: CreditTestsHelpers.productInfo,
      ),
      setUp: () {
        when(
          () => navigationProvider.showBottomSheet<void>(any()),
        ).justCompleteAsync();
      },
      act: (cubit) => cubit.showCreditLockedAccountBottomSheet(),
      verify: (cubit) {
        verify(
          () => navigationProvider.showBottomSheet<void>(any()),
        ).calledOnce;
      },
    );

    blocTest<_Cubit, _State>(
      'on locked account banner action button click',
      build: () => cubit,
      seed: () => _State.idle(
        selectedTabIndex: 0,
        detailedAccounts: accounts,
        creditProductInfo: CreditTestsHelpers.productInfo,
      ),
      setUp: () {
        when(
          () => navigationProvider.push<CreditPayNavigationResult>(any()),
        ).justCompleteAsync();
      },
      act: (cubit) => cubit.onUnlockCredit(),
      verify: (cubit) {
        verify(
          () => navigationProvider.push<CreditPayNavigationResult>(any()),
        ).calledOnce;
      },
    );
  });

  group(
    'easy cash entry point click',
    () {
      blocTest<_Cubit, _State>(
        'Easy Cash flow started',
        build: () => cubit,
        setUp: () {
          when(
            () => easyCashFlow.startFlow(
              creditAccountId: anyNamed('creditAccountId'),
              easyCashLimit:
                  CreditTestsHelpers.easyCashLimitWithEasyCashAccount(
                CreditTestsHelpers.defaultLimit,
              ),
            ),
          ).justCompleteAsync();
        },
        seed: () => CreditDashboardState.idle(
          selectedTabIndex: 0,
          detailedAccounts: accounts,
          creditProductInfo: CreditTestsHelpers.productInfo,
          easyCashLimit: CreditTestsHelpers.easyCashLimitWithEasyCashAccount(
            CreditTestsHelpers.defaultLimit,
          ),
        ),
        act: (c) => c.onEasyCash(),
        verify: (c) {
          verify(
            () => easyCashFlow.startFlow(
              creditAccountId: anyNamed('creditAccountId'),
              easyCashLimit:
                  CreditTestsHelpers.easyCashLimitWithEasyCashAccount(
                CreditTestsHelpers.defaultLimit,
              ),
            ),
          ).calledOnce;
        },
      );

      blocTest<_Cubit, _State>(
        'Cannot start easy cash flow if credit account not present',
        build: () => cubit,
        setUp: () {
          when(
            () => easyCashFlow.startFlow(
              creditAccountId: anyNamed('creditAccountId'),
              easyCashLimit: CreditTestsHelpers.easyCashLimit(
                CreditTestsHelpers.defaultLimit,
              ),
            ),
          ).justCompleteAsync();
        },
        seed: () => CreditDashboardState.idle(
          selectedTabIndex: 0,
          detailedAccounts: accounts,
          creditProductInfo: CreditTestsHelpers.productInfo,
          easyCashLimit: CreditTestsHelpers.easyCashLimit(
            CreditTestsHelpers.defaultLimit,
          ),
        ),
        act: (c) => c.onEasyCash(),
        verify: (c) {
          verifyZeroInteractions(easyCashFlow);
          verify(() => errorHandler.handleError(any())).calledOnce;
        },
      );

      blocTest<_Cubit, _State>(
        'Payment due bottom sheet shown',
        build: () => cubit,
        setUp: () {
          when(
            () => navigationProvider.showBottomSheet<void>(any()),
          ).justCompleteAsync();
        },
        seed: () => CreditDashboardState.idle(
          selectedTabIndex: 0,
          detailedAccounts: accounts,
          creditProductInfo: CreditTestsHelpers.productInfo,
          easyCashLimit: CreditTestsHelpers.easyCashLimitWithEasyCashAccount(
            CreditTestsHelpers.zeroLimit,
            mambuSubState: 'LOCKED',
          ),
        ),
        act: (c) => c.onEasyCash(),
        verify: (c) {
          verify(
            () => navigationProvider.showBottomSheet<void>(
              EasyCashPaymentDueBottomSheetConfig(
                dueAmount: Money.fromNumWithCurrency(30000, Currency.aed),
                onRepaymentPressed: () {
                  navigationProvider.goBack();
                  cubit.onEasyCashPayback();
                },
              ),
            ),
          ).calledOnce;
        },
      );

      blocTest<_Cubit, _State>(
        'Limit reached bottom sheet shown',
        build: () => cubit,
        setUp: () {
          when(
            () => navigationProvider.showBottomSheet<void>(any()),
          ).justCompleteAsync();
        },
        seed: () => CreditDashboardState.idle(
          selectedTabIndex: 0,
          detailedAccounts: accounts,
          creditProductInfo: CreditTestsHelpers.productInfo,
          easyCashLimit: CreditTestsHelpers.easyCashLimitWithEasyCashAccount(
            CreditTestsHelpers.zeroLimit,
          ),
        ),
        act: (c) => c.onEasyCash(),
        verify: (c) {
          verify(
            () => navigationProvider.showBottomSheet<void>(
              EasyCashLimitReachedBottomSheetConfig(
                rePaymentDate: DateTime(2024),
                dueDays: null,
                onRepaymentPressed: () {
                  navigationProvider.goBack();
                  cubit.onEasyCashPayback();
                },
              ),
            ),
          ).calledOnce;
        },
      );
    },
  );

  blocTest<_Cubit, _State>(
    'should dismiss the fee free period tooltip',
    build: () => cubit,
    seed: () => CreditDashboardState.idle(
      selectedTabIndex: 0,
      detailedAccounts: accounts,
      creditProductInfo: CreditTestsHelpers.productInfo,
    ),
    setUp: () {
      when(
        () => creditFeeFreePeriodTooltipInteractor
            .dismissFeeFreePeriodExpirationTooltip(any(), DateTime(2024)),
      ).justCompleteAsync();
    },
    act: (cubit) => cubit.dismissFeeFreeExpiredTooltip(DateTime(2024)),
    verify: (cubit) {
      verify(
        () => creditFeeFreePeriodTooltipInteractor
            .dismissFeeFreePeriodExpirationTooltip(any(), DateTime(2024)),
      ).calledOnce;
    },
  );

  blocTest<_Cubit, _State>(
    'should show credit fee free period faq',
    build: () => cubit,
    setUp: () {
      when(
        () => faqInteractor.fetchTopFaqs({FaqTags.freeFeeCredit}),
      ).justAnswerAsync([
        const common.FaqItem(
          faqId: '1',
          question: 'question1',
          answer: 'answer1',
        ),
      ]);

      when(
        () => navigationProvider.push<Object?>(
          common.FAQsQuestionDetailScreenNavigationConfig(
            answer: creditFeeFreePeriodFaq.htmlContent,
            question: creditFeeFreePeriodFaq.question,
            videoSupportLink: creditFeeFreePeriodFaq.videoSupportMobile,
            mobileInteractionText: creditFeeFreePeriodFaq.mobileInteractionText,
          ),
        ),
      ).justCompleteAsync();
    },
    act: (cubit) => cubit.showFaq(),
    verify: (cubit) {
      verify(
        () => faqInteractor.fetchTopFaqs({FaqTags.freeFeeCredit}),
      ).calledOnce;
      verify(
        () => navigationProvider.push(
          common.FAQsQuestionDetailScreenNavigationConfig(
            answer: creditFeeFreePeriodFaq.htmlContent,
            question: creditFeeFreePeriodFaq.question,
            videoSupportLink: creditFeeFreePeriodFaq.videoSupportMobile,
            mobileInteractionText: creditFeeFreePeriodFaq.mobileInteractionText,
          ),
        ),
      );
    },
  );

  blocTest<_Cubit, _State>(
    'should show commonn faq credit fee free period faq',
    build: () => cubit,
    setUp: () {
      when(
        () => faqInteractor.fetchTopFaqs({FaqTags.freeFeeCredit}),
      ).justAnswerAsync([
        const common.FaqItem(
          faqId: '1',
          question: 'question1',
          answer: 'answer1',
        ),
      ]);
      when(
        () => navigationProvider.push(
          common.FAQsQuestionDetailScreenNavigationConfig(
            answer: creditFeeFreePeriodFaq.htmlContent,
            question: creditFeeFreePeriodFaq.question,
            videoSupportLink: creditFeeFreePeriodFaq.videoSupportMobile,
            mobileInteractionText: creditFeeFreePeriodFaq.mobileInteractionText,
          ),
        ),
      ).justCompleteAsync();
    },
    act: (cubit) => cubit.showFaq(),
    verify: (cubit) {
      verify(
        () => faqInteractor.fetchTopFaqs({FaqTags.freeFeeCredit}),
      ).calledOnce;
      verify(
        () => navigationProvider.push(
          common.FAQsQuestionDetailScreenNavigationConfig(
            answer: creditFeeFreePeriodFaq.htmlContent,
            question: creditFeeFreePeriodFaq.question,
            videoSupportLink: creditFeeFreePeriodFaq.videoSupportMobile,
            mobileInteractionText: creditFeeFreePeriodFaq.mobileInteractionText,
          ),
        ),
      ).calledOnce;
    },
  );
}
