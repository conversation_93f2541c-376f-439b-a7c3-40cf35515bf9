import 'dart:io';
import 'dart:typed_data';

import 'package:bloc_test/bloc_test.dart';
import 'package:domain/domain.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:tests/tests.dart'
    show
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        PostExpectationExtension,
        PostExpectationExtensionAsync,
        VerificationResultExtensions;
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_common_file_picker_ui/file_picker_ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/feature_credit_ui.dart';
import 'package:wio_feature_credit_ui/src/common/extensions/credit_application_input_data_updater.dart';
import 'package:wio_feature_credit_ui/src/handlers/credit_error_handler/credit_error_handler.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_statement_upload/cubit/credit_statement_upload_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/credit_statement_upload/delegates/credit_statement_upload_delegate.dart';

import '../helpers.dart';
import '../mocks.dart';

typedef _Cubit = CreditStatementUploadCubit;
typedef _State = CreditStatementUploadState;

void main() {
  late _Cubit cubit;
  late Logger logger;
  late CreditApplicationInteractor interactor;
  late CreditLocalizations localizations;
  late NavigationProvider navigationProvider;
  late File file;
  late CreditStatementUploadDelegate delegate;
  late CreditErrorHandler errorHandler;
  late CreditAnalytics analytics;
  late ResponsiveDialogProvider responsiveDialogProvider;

  final application = CreditTestsHelpers.application.copyWithUpdatedInputData(
    const CreditApplicationInputData(
      vatReportingInterval: VatReportingInterval.quarterly,
    ),
  );
  final productInfo = CreditTestsHelpers.productInfo;

  setUpAll(() {
    CreditTestsHelpers.registerFallbackValues();
  });

  setUp(() {
    registerFallbackValue(FakeResponsiveModalConfig());
    logger = MockLogger();
    interactor = MockCreditApplicationInteractor();
    localizations = MockCreditLocalizations();
    navigationProvider = MockNavigationProvider();
    delegate = MockCreditStatementUploadDelegate();
    errorHandler = MockCreditErrorHandler();
    responsiveDialogProvider = MockResponsiveDialogProvider();
    analytics = MockCreditAnalytics();

    cubit = _Cubit(
      application: application,
      delegate: delegate,
      logger: logger,
      interactor: interactor,
      localizations: localizations,
      navigationProvider: navigationProvider,
      errorHandler: errorHandler,
      analytics: analytics,
      responsiveDialogProvider: responsiveDialogProvider,
    );

    when(
      () => interactor.getProductInfo(
        productType: SmeCreditProductType.creditCard,
      ),
    ).justAnswerAsync(productInfo);

    when(() => localizations.vatStatementSampleTitle).thenReturn(
      'vatStatementSampleTitle',
    );
    when(() => localizations.selectFileFinancialStatementTitle).thenReturn('');
    when(() => localizations.selectFileBorrowingPowerTitle).thenReturn('');

    when(
      () => delegate.statementType,
    ).thenReturn(CreditDocumentType.vatStatement);
  });

  group('init cubit tests', () {
    final testCases = {
      VatReportingInterval.quarterly: 2,
      VatReportingInterval.monthly: 6,
    };

    for (final testCase in testCases.entries) {
      final interval = testCase.key;
      final requiredStatements = testCase.value;

      final application = CreditTestsHelpers.application.copyWith(
        inputData: CreditTestsHelpers.application.inputData.copyWith(
          vatReportingInterval: interval,
        ),
      );

      blocTest<_Cubit, _State>(
        'Successful Init with selected interval as: $interval',
        build: () => cubit,
        setUp: () {
          when(() => interactor.observeApplication(any())).thenAnswer(
            (_) {
              return Stream.value(Data.success(application));
            },
          );

          when(() => delegate.getListOfStatementDocs()).justAnswerAsync([]);

          when(() => delegate.getNumberOfRequiredDocuments())
              .thenReturn(requiredStatements);
        },
        act: (cubit) {
          cubit.init();
        },
        expect: () {
          return [
            _State.ready(
              numberOfRequiredStatements: requiredStatements,
              statementExampleUrl: productInfo.vatTemplateUrl,
            ),
          ];
        },
      );
    }

    blocTest<_Cubit, _State>(
      'Error case',
      build: () => cubit,
      setUp: () {
        when(() => interactor.observeApplication(any())).thenAnswer(
          (_) {
            return Stream.value(Data.error('errorMessage'));
          },
        );

        when(() => delegate.getListOfStatementDocs())
            .justThrowAsync(Exception());

        when(() => navigationProvider.navigateTo(any())).justAnswerEmptyAsync();

        when(() => delegate.getNumberOfRequiredDocuments()).thenReturn(6);

        when(() => localizations.creditCommonToastSomethingWentWrong)
            .thenReturn('Something went wrong.');

        when(() => navigationProvider.popUntilFirstRoute()).thenReturn(null);
      },
      act: (cubit) async {
        await cubit.init();
      },
      expect: () {
        return <_State>[
          const _State.error(message: 'INITIALISING_ERROR'),
        ];
      },
    );
  });

  group('openFilePicker test', () {
    blocTest<_Cubit, _State>(
      'select vat statement documents',
      build: () => cubit,
      seed: () => _State.ready(
        numberOfRequiredStatements: 2,
        statementExampleUrl: productInfo.vatTemplateUrl,
        documents: [
          const StatementDocument.fromRemote(
            id: 'id_2.pdf',
            name: 'vat2.pdf',
            document: CreditDocument(
              id: 'id_2.pdf',
              documentType: CreditDocumentType.vatStatement,
              fileName: 'vat2.pdf',
            ),
            fileBytes: 'bytes',
          ),
        ],
      ),
      setUp: () {
        file = MockFile('path/vat1.pdf');
        when(
          () => responsiveDialogProvider
              .showBottomSheetOrDialog<CrossPlatformFile>(any()),
        ).thenAnswer(
          (_) async {
            return CrossPlatformFile.mobile(file: file);
          },
        );
        when(
          () => delegate.uploadDocument(
            document: any(named: 'document'),
            onProgress: any(named: 'onProgress'),
          ),
        ).justAnswerAsync(CreditTestsHelpers.remoteDocument);

        when(() => file.readAsBytesSync()).thenReturn(Uint8List.fromList([]));
        when(() => file.lengthSync()).thenReturn(10000);
      },
      act: (cubit) => cubit.openFilePicker(),
      expect: () => <_State>[
        _State.ready(
          numberOfRequiredStatements: 2,
          statementExampleUrl: productInfo.vatTemplateUrl,
          documents: [
            const StatementDocument.fromRemote(
              id: 'id_2.pdf',
              name: 'vat2.pdf',
              document: CreditDocument(
                id: 'id_2.pdf',
                documentType: CreditDocumentType.vatStatement,
                fileName: 'vat2.pdf',
              ),
              fileBytes: 'bytes',
            ),
            CreditTestsHelpers.remoteDocument,
          ],
        ),
      ],
    );

    blocTest<_Cubit, _State>(
      'select file that already exists vat statement documents',
      build: () => cubit,
      seed: () => _State.ready(
        numberOfRequiredStatements: 2,
        statementExampleUrl: productInfo.vatTemplateUrl,
        documents: [
          StatementDocument.fromLocal(
            name: 'vat1.pdf',
            size: 10000,
            uploadPercentage: 0,
            fileBytes: Uint8List.fromList([]),
          ),
          const StatementDocument.fromRemote(
            id: 'id_2.pdf',
            name: 'vat2.pdf',
            document: CreditDocument(
              id: 'id_2.pdf',
              documentType: CreditDocumentType.vatStatement,
              fileName: 'vat2.pdf',
            ),
            fileBytes: 'bytes',
          ),
        ],
      ),
      setUp: () {
        file = MockFile('path/vat1.pdf');
        when(
          () => responsiveDialogProvider
              .showBottomSheetOrDialog<CrossPlatformFile>(any()),
        ).thenAnswer(
          (_) async {
            return CrossPlatformFile.mobile(file: file);
          },
        );

        when(() => file.readAsBytesSync()).thenReturn(Uint8List.fromList([]));
        when(() => file.lengthSync()).thenReturn(10000);
      },
      act: (cubit) => cubit.openFilePicker(),
      verify: (_) {
        verify(
          () => errorHandler.showErrorSnackbar(message: any(named: 'message')),
        ).called(1);
      },
      expect: () {
        return <_State>[];
      },
    );
  });

  group(
    'submit test',
    () {
      blocTest<_Cubit, _State>(
        'successful submission',
        build: () => cubit,
        setUp: () {
          when(
            () => delegate.onSubmit(),
          ).justAnswerEmptyAsync();
        },
        seed: () => _State.ready(
          numberOfRequiredStatements: 2,
          statementExampleUrl: productInfo.vatTemplateUrl,
          documents: const [
            StatementDocument.fromRemote(
              id: 'id_1.pdf',
              name: 'vat1.pdf',
              document: CreditDocument(
                id: 'id_1.pdf',
                documentType: CreditDocumentType.vatStatement,
                fileName: 'vat1.pdf',
              ),
              fileBytes: 'bytes',
            ),
            StatementDocument.fromRemote(
              id: 'id_2.pdf',
              name: 'vat2.pdf',
              document: CreditDocument(
                id: 'id_2.pdf',
                documentType: CreditDocumentType.vatStatement,
                fileName: 'vat2.pdf',
              ),
              fileBytes: 'bytes',
            ),
          ],
        ),
        act: (cubit) => cubit.submit(),
        verify: (_) {
          verify(
            () => delegate.onSubmit(),
          ).calledOnce;
          verify(
            () => analytics.uploadVatStatement(
              CreditUiId.vatStatementUpload,
              vatReportingInterval: application.vatReportingInterval ??
                  VatReportingInterval.noReporting,
              duration: any(named: 'duration'),
            ),
          ).calledOnce;
          verify(
            () => analytics.applicationStepSubmit(
              CreditUiId.vatStatementUpload,
              duration: any(named: 'duration'),
            ),
          ).calledOnce;
        },
        expect: () => [
          _State.processing(
            numberOfRequiredStatements: 2,
            statementExampleUrl: productInfo.vatTemplateUrl,
            documents: const [
              StatementDocument.fromRemote(
                id: 'id_1.pdf',
                name: 'vat1.pdf',
                document: CreditDocument(
                  id: 'id_1.pdf',
                  documentType: CreditDocumentType.vatStatement,
                  fileName: 'vat1.pdf',
                ),
                fileBytes: 'bytes',
              ),
              StatementDocument.fromRemote(
                id: 'id_2.pdf',
                name: 'vat2.pdf',
                document: CreditDocument(
                  id: 'id_2.pdf',
                  documentType: CreditDocumentType.vatStatement,
                  fileName: 'vat2.pdf',
                ),
                fileBytes: 'bytes',
              ),
            ],
          ),
          _State.ready(
            numberOfRequiredStatements: 2,
            statementExampleUrl: productInfo.vatTemplateUrl,
            documents: const [
              StatementDocument.fromRemote(
                id: 'id_1.pdf',
                name: 'vat1.pdf',
                document: CreditDocument(
                  id: 'id_1.pdf',
                  documentType: CreditDocumentType.vatStatement,
                  fileName: 'vat1.pdf',
                ),
                fileBytes: 'bytes',
              ),
              StatementDocument.fromRemote(
                id: 'id_2.pdf',
                name: 'vat2.pdf',
                document: CreditDocument(
                  id: 'id_2.pdf',
                  documentType: CreditDocumentType.vatStatement,
                  fileName: 'vat2.pdf',
                ),
                fileBytes: 'bytes',
              ),
            ],
          ),
        ],
      );

      blocTest<_Cubit, _State>(
        'successful submission',
        build: () => cubit,
        setUp: () {
          when(
            () => delegate.onSubmit(),
          ).justThrowAsync(Exception());
        },
        seed: () => _State.ready(
          numberOfRequiredStatements: 2,
          statementExampleUrl: productInfo.vatTemplateUrl,
          documents: const [
            StatementDocument.fromRemote(
              id: 'id_1.pdf',
              name: 'vat1.pdf',
              document: CreditDocument(
                id: 'id_1.pdf',
                documentType: CreditDocumentType.vatStatement,
                fileName: 'vat1.pdf',
              ),
              fileBytes: 'bytes',
            ),
            StatementDocument.fromRemote(
              id: 'id_2.pdf',
              name: 'vat2.pdf',
              document: CreditDocument(
                id: 'id_2.pdf',
                documentType: CreditDocumentType.vatStatement,
                fileName: 'vat2.pdf',
              ),
              fileBytes: 'bytes',
            ),
          ],
        ),
        act: (cubit) => cubit.submit(),
        verify: (_) {
          verify(
            () => delegate.onSubmit(),
          ).calledOnce;
          verify(
            () => errorHandler.handleError(
              any(),
              stackTrace: any(named: 'stackTrace'),
            ),
          ).calledOnce;
        },
        expect: () => [
          _State.processing(
            numberOfRequiredStatements: 2,
            statementExampleUrl: productInfo.vatTemplateUrl,
            documents: const [
              StatementDocument.fromRemote(
                id: 'id_1.pdf',
                name: 'vat1.pdf',
                document: CreditDocument(
                  id: 'id_1.pdf',
                  documentType: CreditDocumentType.vatStatement,
                  fileName: 'vat1.pdf',
                ),
                fileBytes: 'bytes',
              ),
              StatementDocument.fromRemote(
                id: 'id_2.pdf',
                name: 'vat2.pdf',
                document: CreditDocument(
                  id: 'id_2.pdf',
                  documentType: CreditDocumentType.vatStatement,
                  fileName: 'vat2.pdf',
                ),
                fileBytes: 'bytes',
              ),
            ],
          ),
          _State.ready(
            numberOfRequiredStatements: 2,
            statementExampleUrl: productInfo.vatTemplateUrl,
            documents: const [
              StatementDocument.fromRemote(
                id: 'id_1.pdf',
                name: 'vat1.pdf',
                document: CreditDocument(
                  id: 'id_1.pdf',
                  documentType: CreditDocumentType.vatStatement,
                  fileName: 'vat1.pdf',
                ),
                fileBytes: 'bytes',
              ),
              StatementDocument.fromRemote(
                id: 'id_2.pdf',
                name: 'vat2.pdf',
                document: CreditDocument(
                  id: 'id_2.pdf',
                  documentType: CreditDocumentType.vatStatement,
                  fileName: 'vat2.pdf',
                ),
                fileBytes: 'bytes',
              ),
            ],
          ),
        ],
      );
    },
  );

  group('removeDocuments test', () {
    blocTest<_Cubit, _State>(
      'remove local vat statement documents',
      build: () => cubit,
      seed: () => _State.ready(
        numberOfRequiredStatements: 2,
        statementExampleUrl: productInfo.vatTemplateUrl,
        documents: [
          StatementDocument.fromLocal(
            name: 'vat1.pdf',
            size: 10000,
            uploadPercentage: 0,
            fileBytes: Uint8List.fromList([]),
          ),
          const StatementDocument.fromRemote(
            id: 'id_2.pdf',
            name: 'vat2.pdf',
            document: CreditDocument(
              id: 'id_2.pdf',
              documentType: CreditDocumentType.vatStatement,
              fileName: 'vat2.pdf',
            ),
            fileBytes: 'bytes',
          ),
        ],
      ),
      act: (cubit) => cubit.removeDocument(
        StatementDocument.fromLocal(
          name: 'vat1.pdf',
          size: 10000,
          uploadPercentage: 0,
          fileBytes: Uint8List.fromList([]),
        ),
      ),
      expect: () {
        return <_State>[
          _State.ready(
            numberOfRequiredStatements: 2,
            statementExampleUrl: productInfo.vatTemplateUrl,
            documents: const [
              StatementDocument.fromRemote(
                id: 'id_2.pdf',
                name: 'vat2.pdf',
                document: CreditDocument(
                  id: 'id_2.pdf',
                  documentType: CreditDocumentType.vatStatement,
                  fileName: 'vat2.pdf',
                ),
                fileBytes: 'bytes',
              ),
            ],
          ),
        ];
      },
    );

    blocTest<_Cubit, _State>(
      'remove remote vat statement documents',
      build: () => cubit,
      seed: () => _State.ready(
        numberOfRequiredStatements: 2,
        statementExampleUrl: productInfo.vatTemplateUrl,
        documents: [
          const StatementDocument.fromRemote(
            id: 'id_1.pdf',
            name: 'vat1.pdf',
            document: CreditDocument(
              id: 'id_1.pdf',
              documentType: CreditDocumentType.vatStatement,
              fileName: 'vat1.pdf',
            ),
            fileBytes: 'bytes',
          ),
          const StatementDocument.fromRemote(
            id: 'id_2.pdf',
            name: 'vat2.pdf',
            document: CreditDocument(
              id: 'id_2.pdf',
              documentType: CreditDocumentType.vatStatement,
              fileName: 'vat2.pdf',
            ),
            fileBytes: 'bytes',
          ),
          StatementDocument.fromLocal(
            name: 'vat3.pdf',
            size: 10000,
            uploadPercentage: 0,
            fileBytes: Uint8List.fromList([]),
          ),
        ],
      ),
      setUp: () {
        when(() => delegate.deleteDocument(any())).justAnswerEmptyAsync();
      },
      act: (cubit) => cubit.removeDocument(
        const StatementDocument.fromRemote(
          id: 'id_1.pdf',
          name: 'vat1.pdf',
          document: CreditDocument(
            id: 'id_1.pdf',
            documentType: CreditDocumentType.vatStatement,
            fileName: 'vat1.pdf',
          ),
          fileBytes: 'bytes',
        ),
      ),
      expect: () {
        return <_State>[
          _State.ready(
            numberOfRequiredStatements: 2,
            statementExampleUrl: productInfo.vatTemplateUrl,
            documents: [
              const StatementDocument.fromRemote(
                id: 'id_2.pdf',
                name: 'vat2.pdf',
                document: CreditDocument(
                  id: 'id_2.pdf',
                  documentType: CreditDocumentType.vatStatement,
                  fileName: 'vat2.pdf',
                ),
                fileBytes: 'bytes',
              ),
              StatementDocument.fromLocal(
                name: 'vat3.pdf',
                size: 10000,
                uploadPercentage: 0,
                fileBytes: Uint8List.fromList([]),
              ),
            ],
          ),
        ];
      },
    );

    blocTest<_Cubit, _State>(
      'remove vat statement document which doesnt exist',
      build: () => cubit,
      seed: () => _State.ready(
        numberOfRequiredStatements: 2,
        statementExampleUrl: productInfo.vatTemplateUrl,
        documents: [
          StatementDocument.fromLocal(
            name: 'vat1.pdf',
            size: 10000,
            uploadPercentage: 0,
            fileBytes: Uint8List.fromList([]),
          ),
        ],
      ),
      act: (cubit) => cubit.removeDocument(
        StatementDocument.fromLocal(
          name: 'vat2.pdf',
          size: 10000,
          uploadPercentage: 0,
          fileBytes: Uint8List.fromList([]),
        ),
      ),
      expect: () {
        return <_State>[];
      },
    );
  });

  group('error screen navigation', () {
    blocTest<_Cubit, _State>(
      'navigate to error screen',
      build: () => cubit,
      seed: () => const _State.error(
        message: 'error message',
      ),
      setUp: () {
        when(
          () => navigationProvider.navigateTo(
            any(),
            replace: any(named: 'replace'),
          ),
        ).justAnswerAsync(Object());
        when(() => navigationProvider.popUntilFirstRoute()).justComplete();

        when(() => localizations.creditGenericErrorScreenTitle)
            .thenReturn('expected');
        when(() => localizations.creditGenericErrorScreenDescription)
            .thenReturn('expected');
        when(() => localizations.creditGenericErrorScreenCtaText)
            .thenReturn('expected');
      },
      act: (cubit) => cubit.navigateToGenericErrorScreen(),
      verify: (_) {
        verify(
          () => navigationProvider.navigateTo(
            any(),
            replace: any(named: 'replace'),
          ),
        ).calledOnce;
        verify(() => navigationProvider.popUntilFirstRoute()).calledOnce;
      },
    );
  });

  blocTest<_Cubit, _State>(
    'should open vat statement example',
    build: () => cubit,
    seed: () => _State.ready(
      numberOfRequiredStatements: 2,
      statementExampleUrl: productInfo.vatTemplateUrl,
      documents: const [
        StatementDocument.fromRemote(
          id: 'id_1.pdf',
          name: 'vat1.pdf',
          document: CreditDocument(
            id: 'id_1.pdf',
            documentType: CreditDocumentType.vatStatement,
            fileName: 'vat1.pdf',
          ),
          fileBytes: 'bytes',
        ),
        StatementDocument.fromRemote(
          id: 'id_2.pdf',
          name: 'vat2.pdf',
          document: CreditDocument(
            id: 'id_2.pdf',
            documentType: CreditDocumentType.vatStatement,
            fileName: 'vat2.pdf',
          ),
          fileBytes: 'bytes',
        ),
      ],
    ),
    act: (cubit) => cubit.openVatExample(),
    verify: (_) {
      verify(
        () => navigationProvider.push(
          CreditPdfViewerNavigationConfig(
            config: CreditPdfViewerConfig.url(
              pageTitle: localizations.vatStatementSampleTitle,
              documentUrl: productInfo.vatTemplateUrl,
              sourceUiId: CreditUiId.vatStatementUpload,
              documentType: CreditDocumentType.vatStatement,
              productType: SmeCreditProductType.creditCard,
            ),
          ),
        ),
      ).calledOnce;
    },
  );
}
