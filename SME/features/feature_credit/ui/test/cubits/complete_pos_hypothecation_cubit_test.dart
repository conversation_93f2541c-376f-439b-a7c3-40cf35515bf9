import 'package:bloc_test/bloc_test.dart';
import 'package:data/data.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_ui/src/screens/complete_pos_hypothecation/cubit/complete_pos_hypothecation_cubit.dart';
import 'package:wio_feature_credit_ui/src/screens/complete_pos_hypothecation/cubit/complete_pos_hypothecation_state.dart';

import '../helpers.dart';
import '../mocks.dart';

void main() {
  late CompletePosHypothecationCubit cubit;
  late NavigationProvider navigationProvider;
  late IAuthManager authManager;
  late CompletePosHypothecationConfig config;
  late CreditAnalytics analytics;

  setUp(() {
    navigationProvider = MockNavigationProvider();
    authManager = MockAuthManager();
    analytics = MockCreditAnalytics();
    config = CompletePosHypothecationConfig.application(
      application: CreditTestsHelpers.application.copyWith(
        creditDecisions: CreditDecisions(
          status: CreditDecisionStatus.approved,
          decisions: [
            CreditDecision(
              approvedAmount: Money.fromNumWithCurrency(100, Currency.aed),
              productType: SmeCreditProductType.pos,
              verificationStatus: VerificationStatus.required,
              partnerName: 'posPartnerName',
            ),
            CreditDecision(
              approvedAmount: Money.fromNumWithCurrency(100, Currency.aed),
              productType: SmeCreditProductType.creditCard,
              verificationStatus: VerificationStatus.notRequired,
              creditAcceptanceStatus: CreditAcceptanceStatus.notStarted,
            ),
          ],
        ),
      ),
    );

    cubit = CompletePosHypothecationCubit(
      navigationProvider: navigationProvider,
      authManager: authManager,
      config: config,
      analytics: analytics,
    );

    registerFallbackValue(FakeDuration());
  });

  group('CompletePosHypothecationCubit >', () {
    blocTest<CompletePosHypothecationCubit, CompletePosHypothecationState>(
      'init',
      build: () => cubit,
      setUp: () {
        when(
          () => authManager.getAuthData(),
        ).justAnswerAsync(
          AuthData(
            tokenData: TokenData(
              accessToken: 'accessToken',
              refreshToken: 'refreshToken',
              expiresIn: DateTime(2024),
            ),
            sessionId: 'sessionId',
            individualId: 'individualId',
            email: 'email',
          ),
        );
      },
      act: (cubit) => cubit.init(),
      expect: () => [
        const CompletePosHypothecationState.idle(
          posPartnerName: 'posPartnerName',
          email: 'email',
        ),
      ],
    );

    blocTest<CompletePosHypothecationCubit, CompletePosHypothecationState>(
      'go back once cubit is closed',
      build: () => CompletePosHypothecationCubit(
        navigationProvider: navigationProvider,
        authManager: authManager,
        analytics: analytics,
        config: const CompletePosHypothecationConfig.application(
          application: CreditTestsHelpers.application,
        ),
      ),
      seed: () => const CompletePosHypothecationState.idle(
        posPartnerName: 'posPartnerName',
        email: 'email',
      ),
      act: (cubit) => cubit.onClose(),
      verify: (cubit) {
        verify(
          () => navigationProvider.goBack(),
        ).calledOnce;
        verify(
          () => analytics.completePosHypothecation(
            CreditUiId.completePosHypothecation,
            duration: any(named: 'duration'),
          ),
        ).calledOnce;
      },
    );
  });
}
