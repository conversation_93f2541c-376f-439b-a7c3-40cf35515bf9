import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

part 'product_terms.freezed.dart';

/// The product terms like the interest rate, late fee, etc
@freezed
class ProductTerms with _$ProductTerms {
  /// Constructor
  const factory ProductTerms({
    double? interestRate,
    Money? annualFee,
    Money? lateFee,
    Money? cashWithdrawalAllowance,

    /// The loan term
    Period? loanPeriod,
    Period? loanExpiryPeriod,
  }) = _ProductTerms;
}
