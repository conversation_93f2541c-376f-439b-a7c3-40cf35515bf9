// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schema.swagger.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PreferencesV2 _$PreferencesV2FromJson(Map<String, dynamic> json) =>
    PreferencesV2(
      settlementAccountId: json['settlementAccountId'] as String?,
      disableAutoPayFromSavings: json['disableAutoPayFromSavings'] as bool?,
    );

Map<String, dynamic> _$PreferencesV2ToJson(PreferencesV2 instance) =>
    <String, dynamic>{
      if (instance.settlementAccountId case final value?)
        'settlementAccountId': value,
      if (instance.disableAutoPayFromSavings case final value?)
        'disableAutoPayFromSavings': value,
    };

UpdatePreferencesRequestV2 _$UpdatePreferencesRequestV2FromJson(
        Map<String, dynamic> json) =>
    UpdatePreferencesRequestV2(
      loanAccountId: json['loanAccountId'] as String,
      preferences:
          PreferencesV2.fromJson(json['preferences'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UpdatePreferencesRequestV2ToJson(
        UpdatePreferencesRequestV2 instance) =>
    <String, dynamic>{
      'loanAccountId': instance.loanAccountId,
      'preferences': instance.preferences.toJson(),
    };

LoanAccount _$LoanAccountFromJson(Map<String, dynamic> json) => LoanAccount(
      accountId: json['accountId'] as String?,
      name: json['name'] as String?,
      loanAmount: (json['loanAmount'] as num?)?.toDouble(),
      customerId: json['customerId'] as String?,
      monthlyRepaymentDay: (json['monthlyRepaymentDay'] as num?)?.toInt(),
      preferences: json['preferences'] == null
          ? null
          : PreferencesV2.fromJson(json['preferences'] as Map<String, dynamic>),
      isActive: json['isActive'] as bool?,
    );

Map<String, dynamic> _$LoanAccountToJson(LoanAccount instance) =>
    <String, dynamic>{
      if (instance.accountId case final value?) 'accountId': value,
      if (instance.name case final value?) 'name': value,
      if (instance.loanAmount case final value?) 'loanAmount': value,
      if (instance.customerId case final value?) 'customerId': value,
      if (instance.monthlyRepaymentDay case final value?)
        'monthlyRepaymentDay': value,
      if (instance.preferences?.toJson() case final value?)
        'preferences': value,
      if (instance.isActive case final value?) 'isActive': value,
    };

Preferences _$PreferencesFromJson(Map<String, dynamic> json) => Preferences(
      disableAutoPayFromSavings: json['disableAutoPayFromSavings'] as bool,
    );

Map<String, dynamic> _$PreferencesToJson(Preferences instance) =>
    <String, dynamic>{
      'disableAutoPayFromSavings': instance.disableAutoPayFromSavings,
    };

UpdatePreferencesRequest _$UpdatePreferencesRequestFromJson(
        Map<String, dynamic> json) =>
    UpdatePreferencesRequest(
      loanAccountId: json['loanAccountId'] as String,
      preferences:
          Preferences.fromJson(json['preferences'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UpdatePreferencesRequestToJson(
        UpdatePreferencesRequest instance) =>
    <String, dynamic>{
      'loanAccountId': instance.loanAccountId,
      'preferences': instance.preferences.toJson(),
    };

FailedEventUpdateDto _$FailedEventUpdateDtoFromJson(
        Map<String, dynamic> json) =>
    FailedEventUpdateDto(
      ids: (json['ids'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          [],
      resolved: json['resolved'] as bool?,
    );

Map<String, dynamic> _$FailedEventUpdateDtoToJson(
        FailedEventUpdateDto instance) =>
    <String, dynamic>{
      if (instance.ids case final value?) 'ids': value,
      if (instance.resolved case final value?) 'resolved': value,
    };

FailedEventsDocument _$FailedEventsDocumentFromJson(
        Map<String, dynamic> json) =>
    FailedEventsDocument(
      id: json['id'] as String?,
      applicationId: json['applicationId'] as String?,
      customerId: json['customerId'] as String?,
      correlationId: json['correlationId'] as String?,
      cloudEventId: json['cloudEventId'] as String?,
      exception: json['exception'] as String?,
      requestURL: json['requestURL'] as String?,
      request: json['request'] as String?,
      type: failedEventsDocumentTypeNullableFromJson(json['type']),
      numberOfRetries: (json['numberOfRetries'] as num?)?.toInt(),
      topic: json['topic'] as String?,
      severity: failedEventsDocumentSeverityNullableFromJson(json['severity']),
      resolved: json['resolved'] as bool?,
      retryable: json['retryable'] == null
          ? null
          : Retryable.fromJson(json['retryable'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FailedEventsDocumentToJson(
        FailedEventsDocument instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.applicationId case final value?) 'applicationId': value,
      if (instance.customerId case final value?) 'customerId': value,
      if (instance.correlationId case final value?) 'correlationId': value,
      if (instance.cloudEventId case final value?) 'cloudEventId': value,
      if (instance.exception case final value?) 'exception': value,
      if (instance.requestURL case final value?) 'requestURL': value,
      if (instance.request case final value?) 'request': value,
      if (failedEventsDocumentTypeNullableToJson(instance.type)
          case final value?)
        'type': value,
      if (instance.numberOfRetries case final value?) 'numberOfRetries': value,
      if (instance.topic case final value?) 'topic': value,
      if (failedEventsDocumentSeverityNullableToJson(instance.severity)
          case final value?)
        'severity': value,
      if (instance.resolved case final value?) 'resolved': value,
      if (instance.retryable?.toJson() case final value?) 'retryable': value,
    };

Retryable _$RetryableFromJson(Map<String, dynamic> json) => Retryable(
      isRetryable: json['isRetryable'] as bool?,
      isCloudEvent: json['isCloudEvent'] as bool?,
    );

Map<String, dynamic> _$RetryableToJson(Retryable instance) => <String, dynamic>{
      if (instance.isRetryable case final value?) 'isRetryable': value,
      if (instance.isCloudEvent case final value?) 'isCloudEvent': value,
    };

UpdateApplicationCommand _$UpdateApplicationCommandFromJson(
        Map<String, dynamic> json) =>
    UpdateApplicationCommand();

Map<String, dynamic> _$UpdateApplicationCommandToJson(
        UpdateApplicationCommand instance) =>
    <String, dynamic>{};

Address _$AddressFromJson(Map<String, dynamic> json) => Address(
      addressType: addressAddressTypeFromJson(json['addressType']),
      buildingName: json['buildingName'] as String,
      street: json['street'] as String,
      city: json['city'] as String,
      emirate: json['emirate'] as String,
      country: json['country'] as String,
      unitNumber: json['unitNumber'] as String,
      poBox: json['poBox'] as String?,
    );

Map<String, dynamic> _$AddressToJson(Address instance) => <String, dynamic>{
      if (addressAddressTypeToJson(instance.addressType) case final value?)
        'addressType': value,
      'buildingName': instance.buildingName,
      'street': instance.street,
      'city': instance.city,
      'emirate': instance.emirate,
      'country': instance.country,
      'unitNumber': instance.unitNumber,
      if (instance.poBox case final value?) 'poBox': value,
    };

Application _$ApplicationFromJson(Map<String, dynamic> json) => Application(
      id: json['id'] as String?,
      businessId: json['businessId'] as String?,
      externalReferenceId: json['externalReferenceId'] as String?,
      companyName: json['companyName'] as String?,
      applicantId: json['applicantId'] as String?,
      referralCode: json['referralCode'] as String?,
      status: applicationStatusNullableFromJson(json['status']),
      uiStatus: applicationUiStatusNullableFromJson(json['uiStatus']),
      productType: productTypeNullableFromJson(json['productType']),
      entryProductType: productTypeNullableFromJson(json['entryProductType']),
      creditDecisionResult: json['creditDecisionResult'] == null
          ? null
          : CreditDecisionResult.fromJson(
              json['creditDecisionResult'] as Map<String, dynamic>),
      creditDecisionResults: json['creditDecisionResults'] == null
          ? null
          : CreditDecisionResults.fromJson(
              json['creditDecisionResults'] as Map<String, dynamic>),
      inputData: json['inputData'] == null
          ? null
          : ApplicationInputDataRequest.fromJson(
              json['inputData'] as Map<String, dynamic>),
      onboardingFlowControls: json['onboardingFlowControls'] == null
          ? null
          : ApplicationOnboardingFlowControls.fromJson(
              json['onboardingFlowControls'] as Map<String, dynamic>),
      documents: (json['documents'] as List<dynamic>?)
              ?.map((e) =>
                  DocumentMetadataResponse.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      productApplications: (json['productApplications'] as List<dynamic>?)
              ?.map(
                  (e) => ProductApplication.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      resubmittedApplicationDetails: json['resubmittedApplicationDetails'] ==
              null
          ? null
          : ResubmittedApplicationDetails.fromJson(
              json['resubmittedApplicationDetails'] as Map<String, dynamic>),
      secureLoanEligibilityDetails: json['secureLoanEligibilityDetails'] == null
          ? null
          : SecureLoanEligibilityDetails.fromJson(
              json['secureLoanEligibilityDetails'] as Map<String, dynamic>),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$ApplicationToJson(Application instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.businessId case final value?) 'businessId': value,
      if (instance.externalReferenceId case final value?)
        'externalReferenceId': value,
      if (instance.companyName case final value?) 'companyName': value,
      if (instance.applicantId case final value?) 'applicantId': value,
      if (instance.referralCode case final value?) 'referralCode': value,
      if (applicationStatusNullableToJson(instance.status) case final value?)
        'status': value,
      if (applicationUiStatusNullableToJson(instance.uiStatus)
          case final value?)
        'uiStatus': value,
      if (productTypeNullableToJson(instance.productType) case final value?)
        'productType': value,
      if (productTypeNullableToJson(instance.entryProductType)
          case final value?)
        'entryProductType': value,
      if (instance.creditDecisionResult?.toJson() case final value?)
        'creditDecisionResult': value,
      if (instance.creditDecisionResults?.toJson() case final value?)
        'creditDecisionResults': value,
      if (instance.inputData?.toJson() case final value?) 'inputData': value,
      if (instance.onboardingFlowControls?.toJson() case final value?)
        'onboardingFlowControls': value,
      if (instance.documents?.map((e) => e.toJson()).toList() case final value?)
        'documents': value,
      if (instance.productApplications?.map((e) => e.toJson()).toList()
          case final value?)
        'productApplications': value,
      if (instance.resubmittedApplicationDetails?.toJson() case final value?)
        'resubmittedApplicationDetails': value,
      if (instance.secureLoanEligibilityDetails?.toJson() case final value?)
        'secureLoanEligibilityDetails': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
    };

ApplicationInputDataRequest _$ApplicationInputDataRequestFromJson(
        Map<String, dynamic> json) =>
    ApplicationInputDataRequest(
      requestedMoney: json['requestedMoney'] == null
          ? null
          : Money.fromJson(json['requestedMoney'] as Map<String, dynamic>),
      annualTurnover: json['annualTurnover'] == null
          ? null
          : Money.fromJson(json['annualTurnover'] as Map<String, dynamic>),
      selectedAmount: json['selectedAmount'] == null
          ? null
          : Money.fromJson(json['selectedAmount'] as Map<String, dynamic>),
      vatReportingMethod:
          applicationInputDataRequestVatReportingMethodNullableFromJson(
              json['vatReportingMethod']),
      ibans: (json['ibans'] as List<dynamic>?)
              ?.map((e) => Iban.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      wioIbans: (json['wioIbans'] as List<dynamic>?)
              ?.map((e) => Iban.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      monthlyPaymentPercentage:
          (json['monthlyPaymentPercentage'] as num?)?.toInt(),
      isAutopayFromSavingSpace: json['isAutopayFromSavingSpace'] as bool?,
      monthlyRepaymentDay: (json['monthlyRepaymentDay'] as num?)?.toInt(),
      uiStatus: applicationUiStatusNullableFromJson(json['uiStatus']),
      nonRegisteredVatReasonType:
          applicationInputDataRequestNonRegisteredVatReasonTypeNullableFromJson(
              json['nonRegisteredVatReasonType']),
      nonRegisteredVatReasonText: json['nonRegisteredVatReasonText'] as String?,
      referralCode: json['referralCode'] as String?,
      securedLoan: json['securedLoan'] as bool?,
      serviceChannels: (json['serviceChannels'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      employeeCountRange: json['employeeCountRange'] as String?,
      businessModelDescription: json['businessModelDescription'] as String?,
      missingProofOfAddressReason:
          json['missingProofOfAddressReason'] as String?,
      missingFinancialStatementReason:
          json['missingFinancialStatementReason'] as String?,
      missingVatReason: json['missingVatReason'] as String?,
      addresses: (json['addresses'] as List<dynamic>?)
              ?.map((e) => Address.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      productPreferences: (json['productPreferences'] as List<dynamic>?)
              ?.map(
                  (e) => ProductPreferences.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$ApplicationInputDataRequestToJson(
        ApplicationInputDataRequest instance) =>
    <String, dynamic>{
      if (instance.requestedMoney?.toJson() case final value?)
        'requestedMoney': value,
      if (instance.annualTurnover?.toJson() case final value?)
        'annualTurnover': value,
      if (instance.selectedAmount?.toJson() case final value?)
        'selectedAmount': value,
      if (applicationInputDataRequestVatReportingMethodNullableToJson(
              instance.vatReportingMethod)
          case final value?)
        'vatReportingMethod': value,
      if (instance.ibans?.map((e) => e.toJson()).toList() case final value?)
        'ibans': value,
      if (instance.wioIbans?.map((e) => e.toJson()).toList() case final value?)
        'wioIbans': value,
      if (instance.monthlyPaymentPercentage case final value?)
        'monthlyPaymentPercentage': value,
      if (instance.isAutopayFromSavingSpace case final value?)
        'isAutopayFromSavingSpace': value,
      if (instance.monthlyRepaymentDay case final value?)
        'monthlyRepaymentDay': value,
      if (applicationUiStatusNullableToJson(instance.uiStatus)
          case final value?)
        'uiStatus': value,
      if (applicationInputDataRequestNonRegisteredVatReasonTypeNullableToJson(
              instance.nonRegisteredVatReasonType)
          case final value?)
        'nonRegisteredVatReasonType': value,
      if (instance.nonRegisteredVatReasonText case final value?)
        'nonRegisteredVatReasonText': value,
      if (instance.referralCode case final value?) 'referralCode': value,
      if (instance.securedLoan case final value?) 'securedLoan': value,
      if (instance.serviceChannels case final value?) 'serviceChannels': value,
      if (instance.employeeCountRange case final value?)
        'employeeCountRange': value,
      if (instance.businessModelDescription case final value?)
        'businessModelDescription': value,
      if (instance.missingProofOfAddressReason case final value?)
        'missingProofOfAddressReason': value,
      if (instance.missingFinancialStatementReason case final value?)
        'missingFinancialStatementReason': value,
      if (instance.missingVatReason case final value?)
        'missingVatReason': value,
      if (instance.addresses?.map((e) => e.toJson()).toList() case final value?)
        'addresses': value,
      if (instance.productPreferences?.map((e) => e.toJson()).toList()
          case final value?)
        'productPreferences': value,
    };

ApplicationOnboardingFlowControls _$ApplicationOnboardingFlowControlsFromJson(
        Map<String, dynamic> json) =>
    ApplicationOnboardingFlowControls(
      shouldUploadFinancialDocument:
          json['shouldUploadFinancialDocument'] as bool?,
      shouldUploadBorrowingPowerVerificationDocument:
          json['shouldUploadBorrowingPowerVerificationDocument'] as bool?,
      shouldCollectMonthlyPaymentDate:
          json['shouldCollectMonthlyPaymentDate'] as bool?,
      shouldCollectRequestedAmount:
          json['shouldCollectRequestedAmount'] as bool?,
    );

Map<String, dynamic> _$ApplicationOnboardingFlowControlsToJson(
        ApplicationOnboardingFlowControls instance) =>
    <String, dynamic>{
      if (instance.shouldUploadFinancialDocument case final value?)
        'shouldUploadFinancialDocument': value,
      if (instance.shouldUploadBorrowingPowerVerificationDocument
          case final value?)
        'shouldUploadBorrowingPowerVerificationDocument': value,
      if (instance.shouldCollectMonthlyPaymentDate case final value?)
        'shouldCollectMonthlyPaymentDate': value,
      if (instance.shouldCollectRequestedAmount case final value?)
        'shouldCollectRequestedAmount': value,
    };

CreditDecisionResult _$CreditDecisionResultFromJson(
        Map<String, dynamic> json) =>
    CreditDecisionResult(
      creditDecisionStatus:
          creditDecisionResultCreditDecisionStatusNullableFromJson(
              json['creditDecisionStatus']),
      approvedAmount: (json['approvedAmount'] as num?)?.toDouble(),
      interestRate: (json['interestRate'] as num?)?.toDouble(),
      annualFee: (json['annualFee'] as num?)?.toDouble(),
      cashWithdrawalAllowance:
          (json['cashWithdrawalAllowance'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$CreditDecisionResultToJson(
        CreditDecisionResult instance) =>
    <String, dynamic>{
      if (creditDecisionResultCreditDecisionStatusNullableToJson(
              instance.creditDecisionStatus)
          case final value?)
        'creditDecisionStatus': value,
      if (instance.approvedAmount case final value?) 'approvedAmount': value,
      if (instance.interestRate case final value?) 'interestRate': value,
      if (instance.annualFee case final value?) 'annualFee': value,
      if (instance.cashWithdrawalAllowance case final value?)
        'cashWithdrawalAllowance': value,
    };

CreditDecisionResultV2 _$CreditDecisionResultV2FromJson(
        Map<String, dynamic> json) =>
    CreditDecisionResultV2(
      creditDecisionStatus:
          creditDecisionResultV2CreditDecisionStatusNullableFromJson(
              json['creditDecisionStatus']),
      approvedAmount: (json['approvedAmount'] as num?)?.toDouble(),
      availableAmount: (json['availableAmount'] as num?)?.toDouble(),
      productType: productTypeFromJson(json['productType']),
      creditAcceptanceStatus:
          creditDecisionResultV2CreditAcceptanceStatusNullableFromJson(
              json['creditAcceptanceStatus']),
      productTerms: json['productTerms'] == null
          ? null
          : ProductTerms.fromJson(json['productTerms'] as Map<String, dynamic>),
      verificationStatus: creditDecisionResultV2VerificationStatusFromJson(
          json['verificationStatus']),
      partnerName: json['partnerName'] as String?,
      isAutoAcceptOffer: json['isAutoAcceptOffer'] as bool?,
    );

Map<String, dynamic> _$CreditDecisionResultV2ToJson(
        CreditDecisionResultV2 instance) =>
    <String, dynamic>{
      if (creditDecisionResultV2CreditDecisionStatusNullableToJson(
              instance.creditDecisionStatus)
          case final value?)
        'creditDecisionStatus': value,
      if (instance.approvedAmount case final value?) 'approvedAmount': value,
      if (instance.availableAmount case final value?) 'availableAmount': value,
      if (productTypeToJson(instance.productType) case final value?)
        'productType': value,
      if (creditDecisionResultV2CreditAcceptanceStatusNullableToJson(
              instance.creditAcceptanceStatus)
          case final value?)
        'creditAcceptanceStatus': value,
      if (instance.productTerms?.toJson() case final value?)
        'productTerms': value,
      if (creditDecisionResultV2VerificationStatusToJson(
              instance.verificationStatus)
          case final value?)
        'verificationStatus': value,
      if (instance.partnerName case final value?) 'partnerName': value,
      if (instance.isAutoAcceptOffer case final value?)
        'isAutoAcceptOffer': value,
    };

CreditDecisionResults _$CreditDecisionResultsFromJson(
        Map<String, dynamic> json) =>
    CreditDecisionResults(
      creditDecisionStatus:
          creditDecisionResultsCreditDecisionStatusNullableFromJson(
              json['creditDecisionStatus']),
      results: (json['results'] as List<dynamic>?)
              ?.map((e) =>
                  CreditDecisionResultV2.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$CreditDecisionResultsToJson(
        CreditDecisionResults instance) =>
    <String, dynamic>{
      if (creditDecisionResultsCreditDecisionStatusNullableToJson(
              instance.creditDecisionStatus)
          case final value?)
        'creditDecisionStatus': value,
      if (instance.results?.map((e) => e.toJson()).toList() case final value?)
        'results': value,
    };

DocumentMetadataResponse _$DocumentMetadataResponseFromJson(
        Map<String, dynamic> json) =>
    DocumentMetadataResponse(
      id: json['id'] as String,
      filename: json['filename'] as String,
      documentType: documentTypeFromJson(json['documentType']),
      contentType: json['contentType'] as String,
      url: json['url'] as String?,
      uploadedAt: json['uploadedAt'] == null
          ? null
          : DateTime.parse(json['uploadedAt'] as String),
      urls: json['urls'] == null
          ? null
          : LocalizedDocumentLink.fromJson(
              json['urls'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DocumentMetadataResponseToJson(
        DocumentMetadataResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'filename': instance.filename,
      if (documentTypeToJson(instance.documentType) case final value?)
        'documentType': value,
      'contentType': instance.contentType,
      if (instance.url case final value?) 'url': value,
      if (instance.uploadedAt?.toIso8601String() case final value?)
        'uploadedAt': value,
      if (instance.urls?.toJson() case final value?) 'urls': value,
    };

FixedDepositInfo _$FixedDepositInfoFromJson(Map<String, dynamic> json) =>
    FixedDepositInfo(
      money: Money.fromJson(json['money'] as Map<String, dynamic>),
      loanProductCode:
          fixedDepositInfoLoanProductCodeFromJson(json['loanProductCode']),
      depositAccountId: json['depositAccountId'] as String,
    );

Map<String, dynamic> _$FixedDepositInfoToJson(FixedDepositInfo instance) =>
    <String, dynamic>{
      'money': instance.money.toJson(),
      if (fixedDepositInfoLoanProductCodeToJson(instance.loanProductCode)
          case final value?)
        'loanProductCode': value,
      'depositAccountId': instance.depositAccountId,
    };

Iban _$IbanFromJson(Map<String, dynamic> json) => Iban(
      iban: json['iban'] as String?,
      name: json['name'] as String?,
    );

Map<String, dynamic> _$IbanToJson(Iban instance) => <String, dynamic>{
      if (instance.iban case final value?) 'iban': value,
      if (instance.name case final value?) 'name': value,
    };

InstallmentFeeParameters _$InstallmentFeeParametersFromJson(
        Map<String, dynamic> json) =>
    InstallmentFeeParameters(
      minimumFee: (json['minimumFee'] as num?)?.toDouble(),
      fixedFee: (json['fixedFee'] as num?)?.toDouble(),
      principalPercentage: (json['principalPercentage'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$InstallmentFeeParametersToJson(
        InstallmentFeeParameters instance) =>
    <String, dynamic>{
      if (instance.minimumFee case final value?) 'minimumFee': value,
      if (instance.fixedFee case final value?) 'fixedFee': value,
      if (instance.principalPercentage case final value?)
        'principalPercentage': value,
    };

LoanSecurityProductDetails _$LoanSecurityProductDetailsFromJson(
        Map<String, dynamic> json) =>
    LoanSecurityProductDetails(
      productCode: loanSecurityProductDetailsProductCodeNullableFromJson(
          json['productCode']),
    );

Map<String, dynamic> _$LoanSecurityProductDetailsToJson(
        LoanSecurityProductDetails instance) =>
    <String, dynamic>{
      if (loanSecurityProductDetailsProductCodeNullableToJson(
              instance.productCode)
          case final value?)
        'productCode': value,
    };

LocalizedDocumentLink _$LocalizedDocumentLinkFromJson(
        Map<String, dynamic> json) =>
    LocalizedDocumentLink(
      en: json['en'] as String,
      ar: json['ar'] as String,
    );

Map<String, dynamic> _$LocalizedDocumentLinkToJson(
        LocalizedDocumentLink instance) =>
    <String, dynamic>{
      'en': instance.en,
      'ar': instance.ar,
    };

Money _$MoneyFromJson(Map<String, dynamic> json) => Money(
      amount: (json['amount'] as num?)?.toDouble(),
      currency: currencyNullableFromJson(json['currency']),
    );

Map<String, dynamic> _$MoneyToJson(Money instance) => <String, dynamic>{
      if (instance.amount case final value?) 'amount': value,
      if (currencyNullableToJson(instance.currency) case final value?)
        'currency': value,
    };

ProductApplication _$ProductApplicationFromJson(Map<String, dynamic> json) =>
    ProductApplication(
      productType: productTypeNullableFromJson(json['productType']),
      accountCreationStatus:
          productApplicationAccountCreationStatusNullableFromJson(
              json['accountCreationStatus']),
      accountId: json['accountId'] as String?,
    );

Map<String, dynamic> _$ProductApplicationToJson(ProductApplication instance) =>
    <String, dynamic>{
      if (productTypeNullableToJson(instance.productType) case final value?)
        'productType': value,
      if (productApplicationAccountCreationStatusNullableToJson(
              instance.accountCreationStatus)
          case final value?)
        'accountCreationStatus': value,
      if (instance.accountId case final value?) 'accountId': value,
    };

ProductPreferences _$ProductPreferencesFromJson(Map<String, dynamic> json) =>
    ProductPreferences(
      isAutopayFromSavingSpace: json['isAutopayFromSavingSpace'] as bool?,
      monthlyRepaymentDay: (json['monthlyRepaymentDay'] as num?)?.toInt(),
      monthlyPaymentPercentage:
          (json['monthlyPaymentPercentage'] as num?)?.toInt(),
      selectedAmount: json['selectedAmount'] == null
          ? null
          : Money.fromJson(json['selectedAmount'] as Map<String, dynamic>),
      reducedCreditCardLimit: json['reducedCreditCardLimit'] == null
          ? null
          : Money.fromJson(
              json['reducedCreditCardLimit'] as Map<String, dynamic>),
      loanPeriod: json['loanPeriod'] as String?,
      productType: productTypeNullableFromJson(json['productType']),
      settlementAccountId: json['settlementAccountId'] as String?,
      uiStatus: applicationUiStatusNullableFromJson(json['uiStatus']),
    );

Map<String, dynamic> _$ProductPreferencesToJson(ProductPreferences instance) =>
    <String, dynamic>{
      if (instance.isAutopayFromSavingSpace case final value?)
        'isAutopayFromSavingSpace': value,
      if (instance.monthlyRepaymentDay case final value?)
        'monthlyRepaymentDay': value,
      if (instance.monthlyPaymentPercentage case final value?)
        'monthlyPaymentPercentage': value,
      if (instance.selectedAmount?.toJson() case final value?)
        'selectedAmount': value,
      if (instance.reducedCreditCardLimit?.toJson() case final value?)
        'reducedCreditCardLimit': value,
      if (instance.loanPeriod case final value?) 'loanPeriod': value,
      if (productTypeNullableToJson(instance.productType) case final value?)
        'productType': value,
      if (instance.settlementAccountId case final value?)
        'settlementAccountId': value,
      if (applicationUiStatusNullableToJson(instance.uiStatus)
          case final value?)
        'uiStatus': value,
    };

ProductTerms _$ProductTermsFromJson(Map<String, dynamic> json) => ProductTerms(
      interestRate: (json['interestRate'] as num?)?.toDouble(),
      annualFee: (json['annualFee'] as num?)?.toDouble(),
      lateFee: (json['lateFee'] as num?)?.toDouble(),
      cashWithdrawalAllowance:
          (json['cashWithdrawalAllowance'] as num?)?.toDouble(),
      dpdPeriod: json['dpdPeriod'] as String?,
      loanExpiryPeriod: json['loanExpiryPeriod'] as String?,
      loanPeriod: json['loanPeriod'] as String?,
      issuanceFeeParameters: json['issuanceFeeParameters'] == null
          ? null
          : InstallmentFeeParameters.fromJson(
              json['issuanceFeeParameters'] as Map<String, dynamic>),
      repaymentFeeParameters: json['repaymentFeeParameters'] == null
          ? null
          : InstallmentFeeParameters.fromJson(
              json['repaymentFeeParameters'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ProductTermsToJson(ProductTerms instance) =>
    <String, dynamic>{
      if (instance.interestRate case final value?) 'interestRate': value,
      if (instance.annualFee case final value?) 'annualFee': value,
      if (instance.lateFee case final value?) 'lateFee': value,
      if (instance.cashWithdrawalAllowance case final value?)
        'cashWithdrawalAllowance': value,
      if (instance.dpdPeriod case final value?) 'dpdPeriod': value,
      if (instance.loanExpiryPeriod case final value?)
        'loanExpiryPeriod': value,
      if (instance.loanPeriod case final value?) 'loanPeriod': value,
      if (instance.issuanceFeeParameters?.toJson() case final value?)
        'issuanceFeeParameters': value,
      if (instance.repaymentFeeParameters?.toJson() case final value?)
        'repaymentFeeParameters': value,
    };

ResubmittedApplicationDetails _$ResubmittedApplicationDetailsFromJson(
        Map<String, dynamic> json) =>
    ResubmittedApplicationDetails(
      fixedDepositInfo: json['fixedDepositInfo'] == null
          ? null
          : FixedDepositInfo.fromJson(
              json['fixedDepositInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ResubmittedApplicationDetailsToJson(
        ResubmittedApplicationDetails instance) =>
    <String, dynamic>{
      if (instance.fixedDepositInfo?.toJson() case final value?)
        'fixedDepositInfo': value,
    };

SecureLoanEligibilityDetails _$SecureLoanEligibilityDetailsFromJson(
        Map<String, dynamic> json) =>
    SecureLoanEligibilityDetails(
      securityProductDetails: (json['securityProductDetails'] as List<dynamic>?)
              ?.map((e) => LoanSecurityProductDetails.fromJson(
                  e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$SecureLoanEligibilityDetailsToJson(
        SecureLoanEligibilityDetails instance) =>
    <String, dynamic>{
      if (instance.securityProductDetails?.map((e) => e.toJson()).toList()
          case final value?)
        'securityProductDetails': value,
    };

ApplicationInputDataRequestDTO _$ApplicationInputDataRequestDTOFromJson(
        Map<String, dynamic> json) =>
    ApplicationInputDataRequestDTO(
      requestedMoney: json['requestedMoney'] == null
          ? null
          : Money.fromJson(json['requestedMoney'] as Map<String, dynamic>),
      annualTurnover: json['annualTurnover'] == null
          ? null
          : Money.fromJson(json['annualTurnover'] as Map<String, dynamic>),
      selectedAmount: json['selectedAmount'] == null
          ? null
          : Money.fromJson(json['selectedAmount'] as Map<String, dynamic>),
      vatReportingMethod:
          applicationInputDataRequestDTOVatReportingMethodNullableFromJson(
              json['vatReportingMethod']),
      ibans: (json['ibans'] as List<dynamic>?)
              ?.map((e) => Iban.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      wioIbans: (json['wioIbans'] as List<dynamic>?)
              ?.map((e) => Iban.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      monthlyPaymentPercentage:
          (json['monthlyPaymentPercentage'] as num?)?.toInt(),
      isAutopayFromSavingSpace: json['isAutopayFromSavingSpace'] as bool?,
      monthlyRepaymentDay: (json['monthlyRepaymentDay'] as num?)?.toInt(),
      uiStatus: applicationUiStatusNullableFromJson(json['uiStatus']),
      nonRegisteredVatReasonType:
          applicationInputDataRequestDTONonRegisteredVatReasonTypeNullableFromJson(
              json['nonRegisteredVatReasonType']),
      nonRegisteredVatReasonText: json['nonRegisteredVatReasonText'] as String?,
      referralCode: json['referralCode'] as String?,
      securedLoan: json['securedLoan'] as bool?,
      serviceChannels: (json['serviceChannels'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      employeeCountRange: json['employeeCountRange'] as String?,
      businessModelDescription: json['businessModelDescription'] as String?,
      missingProofOfAddressReason:
          json['missingProofOfAddressReason'] as String?,
      missingFinancialStatementReason:
          json['missingFinancialStatementReason'] as String?,
      missingVatReason: json['missingVatReason'] as String?,
      addresses: (json['addresses'] as List<dynamic>?)
              ?.map((e) => Address.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      productPreferences: (json['productPreferences'] as List<dynamic>?)
              ?.map(
                  (e) => ProductPreferences.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$ApplicationInputDataRequestDTOToJson(
        ApplicationInputDataRequestDTO instance) =>
    <String, dynamic>{
      if (instance.requestedMoney?.toJson() case final value?)
        'requestedMoney': value,
      if (instance.annualTurnover?.toJson() case final value?)
        'annualTurnover': value,
      if (instance.selectedAmount?.toJson() case final value?)
        'selectedAmount': value,
      if (applicationInputDataRequestDTOVatReportingMethodNullableToJson(
              instance.vatReportingMethod)
          case final value?)
        'vatReportingMethod': value,
      if (instance.ibans?.map((e) => e.toJson()).toList() case final value?)
        'ibans': value,
      if (instance.wioIbans?.map((e) => e.toJson()).toList() case final value?)
        'wioIbans': value,
      if (instance.monthlyPaymentPercentage case final value?)
        'monthlyPaymentPercentage': value,
      if (instance.isAutopayFromSavingSpace case final value?)
        'isAutopayFromSavingSpace': value,
      if (instance.monthlyRepaymentDay case final value?)
        'monthlyRepaymentDay': value,
      if (applicationUiStatusNullableToJson(instance.uiStatus)
          case final value?)
        'uiStatus': value,
      if (applicationInputDataRequestDTONonRegisteredVatReasonTypeNullableToJson(
              instance.nonRegisteredVatReasonType)
          case final value?)
        'nonRegisteredVatReasonType': value,
      if (instance.nonRegisteredVatReasonText case final value?)
        'nonRegisteredVatReasonText': value,
      if (instance.referralCode case final value?) 'referralCode': value,
      if (instance.securedLoan case final value?) 'securedLoan': value,
      if (instance.serviceChannels case final value?) 'serviceChannels': value,
      if (instance.employeeCountRange case final value?)
        'employeeCountRange': value,
      if (instance.businessModelDescription case final value?)
        'businessModelDescription': value,
      if (instance.missingProofOfAddressReason case final value?)
        'missingProofOfAddressReason': value,
      if (instance.missingFinancialStatementReason case final value?)
        'missingFinancialStatementReason': value,
      if (instance.missingVatReason case final value?)
        'missingVatReason': value,
      if (instance.addresses?.map((e) => e.toJson()).toList() case final value?)
        'addresses': value,
      if (instance.productPreferences?.map((e) => e.toJson()).toList()
          case final value?)
        'productPreferences': value,
    };

LoanInstallmentEvaluateCommand _$LoanInstallmentEvaluateCommandFromJson(
        Map<String, dynamic> json) =>
    LoanInstallmentEvaluateCommand(
      amount: Money.fromJson(json['amount'] as Map<String, dynamic>),
      productType: productTypeFromJson(json['productType']),
      loanProductCode:
          loanInstallmentEvaluateCommandLoanProductCodeNullableFromJson(
              json['loanProductCode']),
      evaluationTransactionType:
          loanInstallmentEvaluateCommandEvaluationTransactionTypeNullableFromJson(
              json['evaluationTransactionType']),
      previewScheduleStrategy:
          loanInstallmentEvaluateCommandPreviewScheduleStrategyNullableFromJson(
              json['previewScheduleStrategy']),
      loanPeriod: json['loanPeriod'] as String?,
      applicationId: json['applicationId'] as String?,
      loanAccountId: json['loanAccountId'] as String?,
      firstRepaymentDate: json['firstRepaymentDate'] == null
          ? null
          : DateTime.parse(json['firstRepaymentDate'] as String),
    );

Map<String, dynamic> _$LoanInstallmentEvaluateCommandToJson(
        LoanInstallmentEvaluateCommand instance) =>
    <String, dynamic>{
      'amount': instance.amount.toJson(),
      if (productTypeToJson(instance.productType) case final value?)
        'productType': value,
      if (loanInstallmentEvaluateCommandLoanProductCodeNullableToJson(
              instance.loanProductCode)
          case final value?)
        'loanProductCode': value,
      if (loanInstallmentEvaluateCommandEvaluationTransactionTypeNullableToJson(
              instance.evaluationTransactionType)
          case final value?)
        'evaluationTransactionType': value,
      if (loanInstallmentEvaluateCommandPreviewScheduleStrategyNullableToJson(
              instance.previewScheduleStrategy)
          case final value?)
        'previewScheduleStrategy': value,
      if (instance.loanPeriod case final value?) 'loanPeriod': value,
      if (instance.applicationId case final value?) 'applicationId': value,
      if (instance.loanAccountId case final value?) 'loanAccountId': value,
      if (_dateToJson(instance.firstRepaymentDate) case final value?)
        'firstRepaymentDate': value,
    };

ApplicableFees _$ApplicableFeesFromJson(Map<String, dynamic> json) =>
    ApplicableFees(
      earlyRepaymentFee: json['earlyRepaymentFee'] == null
          ? null
          : EarlyRepaymentFee.fromJson(
              json['earlyRepaymentFee'] as Map<String, dynamic>),
      loanIssuanceFee: json['loanIssuanceFee'] == null
          ? null
          : LoanIssuanceFee.fromJson(
              json['loanIssuanceFee'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ApplicableFeesToJson(ApplicableFees instance) =>
    <String, dynamic>{
      if (instance.earlyRepaymentFee?.toJson() case final value?)
        'earlyRepaymentFee': value,
      if (instance.loanIssuanceFee?.toJson() case final value?)
        'loanIssuanceFee': value,
    };

EarlyRepaymentFee _$EarlyRepaymentFeeFromJson(Map<String, dynamic> json) =>
    EarlyRepaymentFee(
      txnId: json['txnId'] as String?,
      amount: (json['amount'] as num?)?.toDouble(),
      currency: currencyNullableFromJson(json['currency']),
    );

Map<String, dynamic> _$EarlyRepaymentFeeToJson(EarlyRepaymentFee instance) =>
    <String, dynamic>{
      if (instance.txnId case final value?) 'txnId': value,
      if (instance.amount case final value?) 'amount': value,
      if (currencyNullableToJson(instance.currency) case final value?)
        'currency': value,
    };

Installment _$InstallmentFromJson(Map<String, dynamic> json) => Installment(
      dueDate: DateTime.parse(json['dueDate'] as String),
      overDue: json['overDue'] == null
          ? null
          : Money.fromJson(json['overDue'] as Map<String, dynamic>),
      amount:
          InstallmentAmount.fromJson(json['amount'] as Map<String, dynamic>),
      installmentAmount: json['installmentAmount'] == null
          ? null
          : InstallmentAmount.fromJson(
              json['installmentAmount'] as Map<String, dynamic>),
      totalInstallmentAmount: json['totalInstallmentAmount'] == null
          ? null
          : InstallmentAmount.fromJson(
              json['totalInstallmentAmount'] as Map<String, dynamic>),
      totalInstallmentAmountDue: json['totalInstallmentAmountDue'] == null
          ? null
          : InstallmentAmount.fromJson(
              json['totalInstallmentAmountDue'] as Map<String, dynamic>),
      totalInstallmentAmountPaid: json['totalInstallmentAmountPaid'] == null
          ? null
          : InstallmentAmount.fromJson(
              json['totalInstallmentAmountPaid'] as Map<String, dynamic>),
      totalScheduledInterestDue: json['totalScheduledInterestDue'] == null
          ? null
          : Money.fromJson(
              json['totalScheduledInterestDue'] as Map<String, dynamic>),
      totalPenaltyInterest: json['totalPenaltyInterest'] == null
          ? null
          : Money.fromJson(
              json['totalPenaltyInterest'] as Map<String, dynamic>),
      isPast: json['isPast'] as bool?,
      paid: json['paid'] as bool?,
      upcomingInstallment: json['upcomingInstallment'] as bool?,
    );

Map<String, dynamic> _$InstallmentToJson(Installment instance) =>
    <String, dynamic>{
      if (_dateToJson(instance.dueDate) case final value?) 'dueDate': value,
      if (instance.overDue?.toJson() case final value?) 'overDue': value,
      'amount': instance.amount.toJson(),
      if (instance.installmentAmount?.toJson() case final value?)
        'installmentAmount': value,
      if (instance.totalInstallmentAmount?.toJson() case final value?)
        'totalInstallmentAmount': value,
      if (instance.totalInstallmentAmountDue?.toJson() case final value?)
        'totalInstallmentAmountDue': value,
      if (instance.totalInstallmentAmountPaid?.toJson() case final value?)
        'totalInstallmentAmountPaid': value,
      if (instance.totalScheduledInterestDue?.toJson() case final value?)
        'totalScheduledInterestDue': value,
      if (instance.totalPenaltyInterest?.toJson() case final value?)
        'totalPenaltyInterest': value,
      if (instance.isPast case final value?) 'isPast': value,
      if (instance.paid case final value?) 'paid': value,
      if (instance.upcomingInstallment case final value?)
        'upcomingInstallment': value,
    };

InstallmentAmount _$InstallmentAmountFromJson(Map<String, dynamic> json) =>
    InstallmentAmount(
      fee: (json['fee'] as num).toDouble(),
      principal: (json['principal'] as num).toDouble(),
      interest: (json['interest'] as num).toDouble(),
    );

Map<String, dynamic> _$InstallmentAmountToJson(InstallmentAmount instance) =>
    <String, dynamic>{
      'fee': instance.fee,
      'principal': instance.principal,
      'interest': instance.interest,
    };

LoanIssuanceFee _$LoanIssuanceFeeFromJson(Map<String, dynamic> json) =>
    LoanIssuanceFee(
      txnId: json['txnId'] as String?,
      amount: (json['amount'] as num?)?.toDouble(),
      currency: currencyNullableFromJson(json['currency']),
    );

Map<String, dynamic> _$LoanIssuanceFeeToJson(LoanIssuanceFee instance) =>
    <String, dynamic>{
      if (instance.txnId case final value?) 'txnId': value,
      if (instance.amount case final value?) 'amount': value,
      if (currencyNullableToJson(instance.currency) case final value?)
        'currency': value,
    };

NextInstallment _$NextInstallmentFromJson(Map<String, dynamic> json) =>
    NextInstallment(
      interval: nextInstallmentIntervalFromJson(json['interval']),
      installment:
          Installment.fromJson(json['installment'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$NextInstallmentToJson(NextInstallment instance) =>
    <String, dynamic>{
      if (nextInstallmentIntervalToJson(instance.interval) case final value?)
        'interval': value,
      'installment': instance.installment.toJson(),
    };

RepaymentDetails _$RepaymentDetailsFromJson(Map<String, dynamic> json) =>
    RepaymentDetails(
      principalPaid: (json['principalPaid'] as num?)?.toDouble(),
      interestPaid: (json['interestPaid'] as num?)?.toDouble(),
      feesPaid: (json['feesPaid'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$RepaymentDetailsToJson(RepaymentDetails instance) =>
    <String, dynamic>{
      if (instance.principalPaid case final value?) 'principalPaid': value,
      if (instance.interestPaid case final value?) 'interestPaid': value,
      if (instance.feesPaid case final value?) 'feesPaid': value,
    };

Schedule _$ScheduleFromJson(Map<String, dynamic> json) => Schedule(
      overDue: (json['overDue'] as num?)?.toDouble(),
      missedPaymentCount: (json['missedPaymentCount'] as num?)?.toInt(),
      loanAmount: json['loanAmount'] == null
          ? null
          : InstallmentAmount.fromJson(
              json['loanAmount'] as Map<String, dynamic>),
      currency: currencyNullableFromJson(json['currency']),
      nextInstallment: json['nextInstallment'] == null
          ? null
          : NextInstallment.fromJson(
              json['nextInstallment'] as Map<String, dynamic>),
      installmentSummary: (json['installmentSummary'] as List<dynamic>?)
              ?.map((e) => Installment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      applicableFees: json['applicableFees'] == null
          ? null
          : ApplicableFees.fromJson(
              json['applicableFees'] as Map<String, dynamic>),
      repaymentDetails: json['repaymentDetails'] == null
          ? null
          : RepaymentDetails.fromJson(
              json['repaymentDetails'] as Map<String, dynamic>),
      interestRate: (json['interestRate'] as num?)?.toDouble(),
      currentInstallmentDue: json['currentInstallmentDue'] == null
          ? null
          : InstallmentAmount.fromJson(
              json['currentInstallmentDue'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ScheduleToJson(Schedule instance) => <String, dynamic>{
      if (instance.overDue case final value?) 'overDue': value,
      if (instance.missedPaymentCount case final value?)
        'missedPaymentCount': value,
      if (instance.loanAmount?.toJson() case final value?) 'loanAmount': value,
      if (currencyNullableToJson(instance.currency) case final value?)
        'currency': value,
      if (instance.nextInstallment?.toJson() case final value?)
        'nextInstallment': value,
      if (instance.installmentSummary?.map((e) => e.toJson()).toList()
          case final value?)
        'installmentSummary': value,
      if (instance.applicableFees?.toJson() case final value?)
        'applicableFees': value,
      if (instance.repaymentDetails?.toJson() case final value?)
        'repaymentDetails': value,
      if (instance.interestRate case final value?) 'interestRate': value,
      if (instance.currentInstallmentDue?.toJson() case final value?)
        'currentInstallmentDue': value,
    };

LoanInstallmentDisbursementV2 _$LoanInstallmentDisbursementV2FromJson(
        Map<String, dynamic> json) =>
    LoanInstallmentDisbursementV2(
      amount: Money.fromJson(json['amount'] as Map<String, dynamic>),
      transactionId: json['transactionId'] as String?,
      productSubType:
          loanInstallmentDisbursementV2ProductSubTypeNullableFromJson(
              json['productSubType']),
      loanPeriod: json['loanPeriod'] as String?,
      firstRepaymentDate: json['firstRepaymentDate'] == null
          ? null
          : DateTime.parse(json['firstRepaymentDate'] as String),
    );

Map<String, dynamic> _$LoanInstallmentDisbursementV2ToJson(
        LoanInstallmentDisbursementV2 instance) =>
    <String, dynamic>{
      'amount': instance.amount.toJson(),
      if (instance.transactionId case final value?) 'transactionId': value,
      if (loanInstallmentDisbursementV2ProductSubTypeNullableToJson(
              instance.productSubType)
          case final value?)
        'productSubType': value,
      if (instance.loanPeriod case final value?) 'loanPeriod': value,
      if (_dateToJson(instance.firstRepaymentDate) case final value?)
        'firstRepaymentDate': value,
    };

EligibleCreditLimitRequestDto _$EligibleCreditLimitRequestDtoFromJson(
        Map<String, dynamic> json) =>
    EligibleCreditLimitRequestDto(
      productCode: eligibleCreditLimitRequestDtoProductCodeNullableFromJson(
          json['productCode']),
      amount: json['amount'] == null
          ? null
          : Money.fromJson(json['amount'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EligibleCreditLimitRequestDtoToJson(
        EligibleCreditLimitRequestDto instance) =>
    <String, dynamic>{
      if (eligibleCreditLimitRequestDtoProductCodeNullableToJson(
              instance.productCode)
          case final value?)
        'productCode': value,
      if (instance.amount?.toJson() case final value?) 'amount': value,
    };

CreditCardLimitDetails _$CreditCardLimitDetailsFromJson(
        Map<String, dynamic> json) =>
    CreditCardLimitDetails(
      maxLimit: Money.fromJson(json['maxLimit'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CreditCardLimitDetailsToJson(
        CreditCardLimitDetails instance) =>
    <String, dynamic>{
      'maxLimit': instance.maxLimit.toJson(),
    };

EligibleCreditLimitResponseDto _$EligibleCreditLimitResponseDtoFromJson(
        Map<String, dynamic> json) =>
    EligibleCreditLimitResponseDto(
      creditCardLimit: json['creditCardLimit'] == null
          ? null
          : CreditCardLimitDetails.fromJson(
              json['creditCardLimit'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EligibleCreditLimitResponseDtoToJson(
        EligibleCreditLimitResponseDto instance) =>
    <String, dynamic>{
      if (instance.creditCardLimit?.toJson() case final value?)
        'creditCardLimit': value,
    };

StatementFilter _$StatementFilterFromJson(Map<String, dynamic> json) =>
    StatementFilter(
      accountId: json['accountId'] as String,
      dateFrom: DateTime.parse(json['dateFrom'] as String),
      dateTo: DateTime.parse(json['dateTo'] as String),
    );

Map<String, dynamic> _$StatementFilterToJson(StatementFilter instance) =>
    <String, dynamic>{
      'accountId': instance.accountId,
      if (_dateToJson(instance.dateFrom) case final value?) 'dateFrom': value,
      if (_dateToJson(instance.dateTo) case final value?) 'dateTo': value,
    };

ReferralRequestDTO _$ReferralRequestDTOFromJson(Map<String, dynamic> json) =>
    ReferralRequestDTO(
      referralCode: json['referralCode'] as String,
      termsAndConditions: LocalizedDocumentLink.fromJson(
          json['termsAndConditions'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ReferralRequestDTOToJson(ReferralRequestDTO instance) =>
    <String, dynamic>{
      'referralCode': instance.referralCode,
      'termsAndConditions': instance.termsAndConditions.toJson(),
    };

ProblemDetail _$ProblemDetailFromJson(Map<String, dynamic> json) =>
    ProblemDetail(
      type: json['type'] as String?,
      title: json['title'] as String?,
      status: (json['status'] as num?)?.toInt(),
      detail: json['detail'] as String?,
      instance: json['instance'] as String?,
      properties: json['properties'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ProblemDetailToJson(ProblemDetail instance) =>
    <String, dynamic>{
      if (instance.type case final value?) 'type': value,
      if (instance.title case final value?) 'title': value,
      if (instance.status case final value?) 'status': value,
      if (instance.detail case final value?) 'detail': value,
      if (instance.instance case final value?) 'instance': value,
      if (instance.properties case final value?) 'properties': value,
    };

SubmitReferralResponse _$SubmitReferralResponseFromJson(
        Map<String, dynamic> json) =>
    SubmitReferralResponse(
      referralCode: json['referralCode'] as String,
      anchorBusinessName: json['anchorBusinessName'] as String,
      multiUserRequestId: json['multiUserRequestId'] as String?,
      domainId: json['domainId'] as String?,
    );

Map<String, dynamic> _$SubmitReferralResponseToJson(
        SubmitReferralResponse instance) =>
    <String, dynamic>{
      'referralCode': instance.referralCode,
      'anchorBusinessName': instance.anchorBusinessName,
      if (instance.multiUserRequestId case final value?)
        'multiUserRequestId': value,
      if (instance.domainId case final value?) 'domainId': value,
    };

AgreementInputDto _$AgreementInputDtoFromJson(Map<String, dynamic> json) =>
    AgreementInputDto(
      productType: productTypeNullableFromJson(json['productType']),
    );

Map<String, dynamic> _$AgreementInputDtoToJson(AgreementInputDto instance) =>
    <String, dynamic>{
      if (productTypeNullableToJson(instance.productType) case final value?)
        'productType': value,
    };

MultiUserCreateRequestDTO _$MultiUserCreateRequestDTOFromJson(
        Map<String, dynamic> json) =>
    MultiUserCreateRequestDTO(
      individualId: json['individualId'] as String,
      email: json['email'] as String,
      domainType:
          multiUserCreateRequestDTODomainTypeFromJson(json['domainType']),
      domainId: json['domainId'] as String,
      scfRequestSummary: ScfRequestSummary.fromJson(
          json['scfRequestSummary'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MultiUserCreateRequestDTOToJson(
        MultiUserCreateRequestDTO instance) =>
    <String, dynamic>{
      'individualId': instance.individualId,
      'email': instance.email,
      if (multiUserCreateRequestDTODomainTypeToJson(instance.domainType)
          case final value?)
        'domainType': value,
      'domainId': instance.domainId,
      'scfRequestSummary': instance.scfRequestSummary.toJson(),
    };

ScfRequestSummary _$ScfRequestSummaryFromJson(Map<String, dynamic> json) =>
    ScfRequestSummary(
      description: json['description'] as String,
      amount: (json['amount'] as num?)?.toDouble(),
      currency: currencyNullableFromJson(json['currency']),
    );

Map<String, dynamic> _$ScfRequestSummaryToJson(ScfRequestSummary instance) =>
    <String, dynamic>{
      'description': instance.description,
      if (instance.amount case final value?) 'amount': value,
      if (currencyNullableToJson(instance.currency) case final value?)
        'currency': value,
    };

ApproverDTO _$ApproverDTOFromJson(Map<String, dynamic> json) => ApproverDTO(
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
    );

Map<String, dynamic> _$ApproverDTOToJson(ApproverDTO instance) =>
    <String, dynamic>{
      if (instance.firstName case final value?) 'firstName': value,
      if (instance.lastName case final value?) 'lastName': value,
    };

ApproverDetailsDTO _$ApproverDetailsDTOFromJson(Map<String, dynamic> json) =>
    ApproverDetailsDTO(
      approver: json['approver'] == null
          ? null
          : ApproverDTO.fromJson(json['approver'] as Map<String, dynamic>),
      status: json['status'] as String?,
      note: json['note'] as String?,
    );

Map<String, dynamic> _$ApproverDetailsDTOToJson(ApproverDetailsDTO instance) =>
    <String, dynamic>{
      if (instance.approver?.toJson() case final value?) 'approver': value,
      if (instance.status case final value?) 'status': value,
      if (instance.note case final value?) 'note': value,
    };

MultiUserResponseDTO _$MultiUserResponseDTOFromJson(
        Map<String, dynamic> json) =>
    MultiUserResponseDTO(
      domainType:
          multiUserResponseDTODomainTypeNullableFromJson(json['domainType']),
      id: json['id'] as String?,
      status: multiUserResponseDTOStatusNullableFromJson(json['status']),
      rejectionNotes: json['rejectionNotes'] as String?,
      domainId: json['domainId'] as String?,
      authorId: json['authorId'] as String?,
      creationFlow: json['creationFlow'] as String?,
      createdAt: json['createdAt'] as String?,
      updatedAt: json['updatedAt'] as String?,
      twoFaRequired: json['twoFaRequired'] as bool?,
      approvers: (json['approvers'] as List<dynamic>?)
              ?.map((e) => ApproverDTO.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      approvals: (json['approvals'] as List<dynamic>?)
              ?.map(
                  (e) => ApproverDetailsDTO.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      scfSupplierInviteSummary: json['scfSupplierInviteSummary'] == null
          ? null
          : ScfRequestSummary.fromJson(
              json['scfSupplierInviteSummary'] as Map<String, dynamic>),
      scfInvoiceUploadSummary: json['scfInvoiceUploadSummary'] == null
          ? null
          : ScfRequestSummary.fromJson(
              json['scfInvoiceUploadSummary'] as Map<String, dynamic>),
      scfInvoiceRepaymentInitiateSummary:
          json['scfInvoiceRepaymentInitiateSummary'] == null
              ? null
              : ScfRequestSummary.fromJson(
                  json['scfInvoiceRepaymentInitiateSummary']
                      as Map<String, dynamic>),
    );

Map<String, dynamic> _$MultiUserResponseDTOToJson(
        MultiUserResponseDTO instance) =>
    <String, dynamic>{
      if (multiUserResponseDTODomainTypeNullableToJson(instance.domainType)
          case final value?)
        'domainType': value,
      if (instance.id case final value?) 'id': value,
      if (multiUserResponseDTOStatusNullableToJson(instance.status)
          case final value?)
        'status': value,
      if (instance.rejectionNotes case final value?) 'rejectionNotes': value,
      if (instance.domainId case final value?) 'domainId': value,
      if (instance.authorId case final value?) 'authorId': value,
      if (instance.creationFlow case final value?) 'creationFlow': value,
      if (instance.createdAt case final value?) 'createdAt': value,
      if (instance.updatedAt case final value?) 'updatedAt': value,
      if (instance.twoFaRequired case final value?) 'twoFaRequired': value,
      if (instance.approvers?.map((e) => e.toJson()).toList() case final value?)
        'approvers': value,
      if (instance.approvals?.map((e) => e.toJson()).toList() case final value?)
        'approvals': value,
      if (instance.scfSupplierInviteSummary?.toJson() case final value?)
        'scfSupplierInviteSummary': value,
      if (instance.scfInvoiceUploadSummary?.toJson() case final value?)
        'scfInvoiceUploadSummary': value,
      if (instance.scfInvoiceRepaymentInitiateSummary?.toJson()
          case final value?)
        'scfInvoiceRepaymentInitiateSummary': value,
    };

MultiUserRejectRequestDTO _$MultiUserRejectRequestDTOFromJson(
        Map<String, dynamic> json) =>
    MultiUserRejectRequestDTO(
      individualId: json['individualId'] as String,
      note: json['note'] as String?,
    );

Map<String, dynamic> _$MultiUserRejectRequestDTOToJson(
        MultiUserRejectRequestDTO instance) =>
    <String, dynamic>{
      'individualId': instance.individualId,
      if (instance.note case final value?) 'note': value,
    };

MultiUserApproveRequestDTO _$MultiUserApproveRequestDTOFromJson(
        Map<String, dynamic> json) =>
    MultiUserApproveRequestDTO(
      individualId: json['individualId'] as String,
    );

Map<String, dynamic> _$MultiUserApproveRequestDTOToJson(
        MultiUserApproveRequestDTO instance) =>
    <String, dynamic>{
      'individualId': instance.individualId,
    };

LoanInstallmentDisbursement _$LoanInstallmentDisbursementFromJson(
        Map<String, dynamic> json) =>
    LoanInstallmentDisbursement(
      amount: Money.fromJson(json['amount'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$LoanInstallmentDisbursementToJson(
        LoanInstallmentDisbursement instance) =>
    <String, dynamic>{
      'amount': instance.amount.toJson(),
    };

InternalReferralRequestDTO _$InternalReferralRequestDTOFromJson(
        Map<String, dynamic> json) =>
    InternalReferralRequestDTO(
      referralCode: json['referralCode'] as String,
      individualId: json['individualId'] as String,
      termsAndConditions: LocalizedDocumentLink.fromJson(
          json['termsAndConditions'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$InternalReferralRequestDTOToJson(
        InternalReferralRequestDTO instance) =>
    <String, dynamic>{
      'referralCode': instance.referralCode,
      'individualId': instance.individualId,
      'termsAndConditions': instance.termsAndConditions.toJson(),
    };

Data _$DataFromJson(Map<String, dynamic> json) => Data(
      requestType: json['RequestType'] as String?,
      requestId: json['RequestId'] as String?,
      domainId: json['DomainId'] as String?,
      authorId: json['AuthorId'] as String?,
      businessId: json['BusinessId'] as String?,
      domainType: json['DomainType'] as String?,
    );

Map<String, dynamic> _$DataToJson(Data instance) => <String, dynamic>{
      if (instance.requestType case final value?) 'RequestType': value,
      if (instance.requestId case final value?) 'RequestId': value,
      if (instance.domainId case final value?) 'DomainId': value,
      if (instance.authorId case final value?) 'AuthorId': value,
      if (instance.businessId case final value?) 'BusinessId': value,
      if (instance.domainType case final value?) 'DomainType': value,
    };

Header _$HeaderFromJson(Map<String, dynamic> json) => Header(
      schemaVersion: json['SchemaVersion'] as String?,
      eventId: json['EventId'] as String?,
      environment: json['Environment'] as String?,
      eventType: json['EventType'] as String?,
      eventName: json['EventName'] as String?,
      correlationId: json['CorrelationId'] as String?,
      sourceName: json['SourceName'] as String?,
      sourceType: json['SourceType'] as String?,
      channel: json['Channel'] as String?,
      tenant: json['Tenant'] as String?,
      sessionId: json['SessionId'] as String?,
      product: json['Product'] as String?,
      eventTimeUTC: json['EventTime_UTC'] as String?,
      sourceTimeUTC: json['SourceTime_UTC'] as String?,
    );

Map<String, dynamic> _$HeaderToJson(Header instance) => <String, dynamic>{
      if (instance.schemaVersion case final value?) 'SchemaVersion': value,
      if (instance.eventId case final value?) 'EventId': value,
      if (instance.environment case final value?) 'Environment': value,
      if (instance.eventType case final value?) 'EventType': value,
      if (instance.eventName case final value?) 'EventName': value,
      if (instance.correlationId case final value?) 'CorrelationId': value,
      if (instance.sourceName case final value?) 'SourceName': value,
      if (instance.sourceType case final value?) 'SourceType': value,
      if (instance.channel case final value?) 'Channel': value,
      if (instance.tenant case final value?) 'Tenant': value,
      if (instance.sessionId case final value?) 'SessionId': value,
      if (instance.product case final value?) 'Product': value,
      if (instance.eventTimeUTC case final value?) 'EventTime_UTC': value,
      if (instance.sourceTimeUTC case final value?) 'SourceTime_UTC': value,
    };

MultiUserEventData _$MultiUserEventDataFromJson(Map<String, dynamic> json) =>
    MultiUserEventData(
      header: json['Header'] == null
          ? null
          : Header.fromJson(json['Header'] as Map<String, dynamic>),
      payload: json['Payload'] == null
          ? null
          : Payload.fromJson(json['Payload'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MultiUserEventDataToJson(MultiUserEventData instance) =>
    <String, dynamic>{
      if (instance.header?.toJson() case final value?) 'Header': value,
      if (instance.payload?.toJson() case final value?) 'Payload': value,
    };

Payload _$PayloadFromJson(Map<String, dynamic> json) => Payload(
      data: json['Data'] == null
          ? null
          : Data.fromJson(json['Data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PayloadToJson(Payload instance) => <String, dynamic>{
      if (instance.data?.toJson() case final value?) 'Data': value,
    };

EasyCashRepayment _$EasyCashRepaymentFromJson(Map<String, dynamic> json) =>
    EasyCashRepayment(
      easyCashLoanId: json['easyCashLoanId'] as String,
      depositAccountId: json['depositAccountId'] as String,
      amount: Money.fromJson(json['amount'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EasyCashRepaymentToJson(EasyCashRepayment instance) =>
    <String, dynamic>{
      'easyCashLoanId': instance.easyCashLoanId,
      'depositAccountId': instance.depositAccountId,
      'amount': instance.amount.toJson(),
    };

EasyCashEvaluateRepayment _$EasyCashEvaluateRepaymentFromJson(
        Map<String, dynamic> json) =>
    EasyCashEvaluateRepayment(
      easyCashLoanId: json['easyCashLoanId'] as String,
      depositAccountId: json['depositAccountId'] as String,
      amount: Money.fromJson(json['amount'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EasyCashEvaluateRepaymentToJson(
        EasyCashEvaluateRepayment instance) =>
    <String, dynamic>{
      'easyCashLoanId': instance.easyCashLoanId,
      'depositAccountId': instance.depositAccountId,
      'amount': instance.amount.toJson(),
    };

Balances _$BalancesFromJson(Map<String, dynamic> json) => Balances(
      availableAmount: (json['availableAmount'] as num).toDouble(),
      holdAmount: (json['holdAmount'] as num).toDouble(),
      feesBalance: (json['feesBalance'] as num).toDouble(),
      principalBalance: (json['principalBalance'] as num).toDouble(),
      interestBalance: (json['interestBalance'] as num?)?.toDouble(),
      estimatedFee: (json['estimatedFee'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$BalancesToJson(Balances instance) => <String, dynamic>{
      'availableAmount': instance.availableAmount,
      'holdAmount': instance.holdAmount,
      'feesBalance': instance.feesBalance,
      'principalBalance': instance.principalBalance,
      if (instance.interestBalance case final value?) 'interestBalance': value,
      if (instance.estimatedFee case final value?) 'estimatedFee': value,
    };

EasyCashRepaymentEvaluation _$EasyCashRepaymentEvaluationFromJson(
        Map<String, dynamic> json) =>
    EasyCashRepaymentEvaluation(
      feePerDay: Money.fromJson(json['feePerDay'] as Map<String, dynamic>),
      totalFees: Money.fromJson(json['totalFees'] as Map<String, dynamic>),
      feeSaved: Money.fromJson(json['feeSaved'] as Map<String, dynamic>),
      currency: currencyNullableFromJson(json['currency']),
      balances: json['balances'] == null
          ? null
          : Balances.fromJson(json['balances'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EasyCashRepaymentEvaluationToJson(
        EasyCashRepaymentEvaluation instance) =>
    <String, dynamic>{
      'feePerDay': instance.feePerDay.toJson(),
      'totalFees': instance.totalFees.toJson(),
      'feeSaved': instance.feeSaved.toJson(),
      if (currencyNullableToJson(instance.currency) case final value?)
        'currency': value,
      if (instance.balances?.toJson() case final value?) 'balances': value,
    };

EasyCashEvaluateDisbursement _$EasyCashEvaluateDisbursementFromJson(
        Map<String, dynamic> json) =>
    EasyCashEvaluateDisbursement(
      dueDate: DateTime.parse(json['dueDate'] as String),
      amount: Money.fromJson(json['amount'] as Map<String, dynamic>),
      creditCardLoanAccountId: json['creditCardLoanAccountId'] as String,
    );

Map<String, dynamic> _$EasyCashEvaluateDisbursementToJson(
        EasyCashEvaluateDisbursement instance) =>
    <String, dynamic>{
      if (_dateToJson(instance.dueDate) case final value?) 'dueDate': value,
      'amount': instance.amount.toJson(),
      'creditCardLoanAccountId': instance.creditCardLoanAccountId,
    };

EasyCashDisbursementEvaluation _$EasyCashDisbursementEvaluationFromJson(
        Map<String, dynamic> json) =>
    EasyCashDisbursementEvaluation(
      feePerDay: Money.fromJson(json['feePerDay'] as Map<String, dynamic>),
      totalFees: Money.fromJson(json['totalFees'] as Map<String, dynamic>),
      finalTotalFees:
          Money.fromJson(json['finalTotalFees'] as Map<String, dynamic>),
      currentFeePerDay:
          Money.fromJson(json['currentFeePerDay'] as Map<String, dynamic>),
      currentEstimatedTotalFees: Money.fromJson(
          json['currentEstimatedTotalFees'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EasyCashDisbursementEvaluationToJson(
        EasyCashDisbursementEvaluation instance) =>
    <String, dynamic>{
      'feePerDay': instance.feePerDay.toJson(),
      'totalFees': instance.totalFees.toJson(),
      'finalTotalFees': instance.finalTotalFees.toJson(),
      'currentFeePerDay': instance.currentFeePerDay.toJson(),
      'currentEstimatedTotalFees': instance.currentEstimatedTotalFees.toJson(),
    };

EasyCashDisbursement _$EasyCashDisbursementFromJson(
        Map<String, dynamic> json) =>
    EasyCashDisbursement(
      creditCardLoanId: json['creditCardLoanId'] as String,
      amount: Money.fromJson(json['amount'] as Map<String, dynamic>),
      currentAccountId: json['currentAccountId'] as String?,
      preferences:
          Preferences.fromJson(json['preferences'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EasyCashDisbursementToJson(
        EasyCashDisbursement instance) =>
    <String, dynamic>{
      'creditCardLoanId': instance.creditCardLoanId,
      'amount': instance.amount.toJson(),
      if (instance.currentAccountId case final value?)
        'currentAccountId': value,
      'preferences': instance.preferences.toJson(),
    };

CreateApplicationCommand _$CreateApplicationCommandFromJson(
        Map<String, dynamic> json) =>
    CreateApplicationCommand(
      productType: productTypeFromJson(json['productType']),
      referralCode: json['referralCode'] as String?,
      externalReferenceId: json['externalReferenceId'] as String?,
      termsAndConditions: json['termsAndConditions'] == null
          ? null
          : LocalizedDocumentLink.fromJson(
              json['termsAndConditions'] as Map<String, dynamic>),
      keyFactStatement: json['keyFactStatement'] == null
          ? null
          : LocalizedDocumentLink.fromJson(
              json['keyFactStatement'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CreateApplicationCommandToJson(
        CreateApplicationCommand instance) =>
    <String, dynamic>{
      if (productTypeToJson(instance.productType) case final value?)
        'productType': value,
      if (instance.referralCode case final value?) 'referralCode': value,
      if (instance.externalReferenceId case final value?)
        'externalReferenceId': value,
      if (instance.termsAndConditions?.toJson() case final value?)
        'termsAndConditions': value,
      if (instance.keyFactStatement?.toJson() case final value?)
        'keyFactStatement': value,
    };

TransactionFilterRequestDto _$TransactionFilterRequestDtoFromJson(
        Map<String, dynamic> json) =>
    TransactionFilterRequestDto(
      accountId: json['accountId'] as String,
      transactionType:
          transactionFilterRequestDtoTransactionTypeNullableFromJson(
              json['transactionType']),
      dateFrom: json['dateFrom'] == null
          ? null
          : DateTime.parse(json['dateFrom'] as String),
      dateTo: json['dateTo'] == null
          ? null
          : DateTime.parse(json['dateTo'] as String),
    );

Map<String, dynamic> _$TransactionFilterRequestDtoToJson(
        TransactionFilterRequestDto instance) =>
    <String, dynamic>{
      'accountId': instance.accountId,
      if (transactionFilterRequestDtoTransactionTypeNullableToJson(
              instance.transactionType)
          case final value?)
        'transactionType': value,
      if (_dateToJson(instance.dateFrom) case final value?) 'dateFrom': value,
      if (_dateToJson(instance.dateTo) case final value?) 'dateTo': value,
    };

Details _$DetailsFromJson(Map<String, dynamic> json) => Details(
      principalPaid: (json['principalPaid'] as num?)?.toDouble(),
      feePaid: (json['feePaid'] as num?)?.toDouble(),
      interestPaid: (json['interestPaid'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$DetailsToJson(Details instance) => <String, dynamic>{
      if (instance.principalPaid case final value?) 'principalPaid': value,
      if (instance.feePaid case final value?) 'feePaid': value,
      if (instance.interestPaid case final value?) 'interestPaid': value,
    };

PaginatedResponseTransactionResponse
    _$PaginatedResponseTransactionResponseFromJson(Map<String, dynamic> json) =>
        PaginatedResponseTransactionResponse(
          size: (json['size'] as num?)?.toInt(),
          totalPages: (json['totalPages'] as num?)?.toInt(),
          currentPage: (json['currentPage'] as num?)?.toInt(),
          content: (json['content'] as List<dynamic>?)
                  ?.map((e) =>
                      TransactionResponse.fromJson(e as Map<String, dynamic>))
                  .toList() ??
              [],
        );

Map<String, dynamic> _$PaginatedResponseTransactionResponseToJson(
        PaginatedResponseTransactionResponse instance) =>
    <String, dynamic>{
      if (instance.size case final value?) 'size': value,
      if (instance.totalPages case final value?) 'totalPages': value,
      if (instance.currentPage case final value?) 'currentPage': value,
      if (instance.content?.map((e) => e.toJson()).toList() case final value?)
        'content': value,
    };

TransactionResponse _$TransactionResponseFromJson(Map<String, dynamic> json) =>
    TransactionResponse(
      accountId: json['AccountId'] as String,
      id: json['Id'] as String,
      transactionIdentifier: json['TransactionIdentifier'] as String,
      referenceNumber: json['ReferenceNumber'] as String?,
      transactionDateTime:
          DateTime.parse(json['TransactionDateTime'] as String),
      amount: (json['Amount'] as num).toDouble(),
      totalBalance: (json['TotalBalance'] as num?)?.toDouble(),
      availableBalance: (json['AvailableBalance'] as num?)?.toDouble(),
      currency: currencyNullableFromJson(json['Currency']),
      transactionType: transactionResponseTransactionTypeNullableFromJson(
          json['TransactionType']),
      transactionSubType: transactionResponseTransactionSubTypeNullableFromJson(
          json['TransactionSubType']),
      transactionStatus: transactionResponseTransactionStatusNullableFromJson(
          json['TransactionStatus']),
      transactionMode: transactionResponseTransactionModeNullableFromJson(
          json['TransactionMode']),
      details: json['Details'] == null
          ? null
          : Details.fromJson(json['Details'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TransactionResponseToJson(
        TransactionResponse instance) =>
    <String, dynamic>{
      'AccountId': instance.accountId,
      'Id': instance.id,
      'TransactionIdentifier': instance.transactionIdentifier,
      if (instance.referenceNumber case final value?) 'ReferenceNumber': value,
      'TransactionDateTime': instance.transactionDateTime.toIso8601String(),
      'Amount': instance.amount,
      if (instance.totalBalance case final value?) 'TotalBalance': value,
      if (instance.availableBalance case final value?)
        'AvailableBalance': value,
      if (currencyNullableToJson(instance.currency) case final value?)
        'Currency': value,
      if (transactionResponseTransactionTypeNullableToJson(
              instance.transactionType)
          case final value?)
        'TransactionType': value,
      if (transactionResponseTransactionSubTypeNullableToJson(
              instance.transactionSubType)
          case final value?)
        'TransactionSubType': value,
      if (transactionResponseTransactionStatusNullableToJson(
              instance.transactionStatus)
          case final value?)
        'TransactionStatus': value,
      if (transactionResponseTransactionModeNullableToJson(
              instance.transactionMode)
          case final value?)
        'TransactionMode': value,
      if (instance.details?.toJson() case final value?) 'Details': value,
    };

LoanRepaymentCommand _$LoanRepaymentCommandFromJson(
        Map<String, dynamic> json) =>
    LoanRepaymentCommand(
      depositAccountId: json['depositAccountId'] as String,
      loanAccountId: json['loanAccountId'] as String,
      amount: Money.fromJson(json['amount'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$LoanRepaymentCommandToJson(
        LoanRepaymentCommand instance) =>
    <String, dynamic>{
      'depositAccountId': instance.depositAccountId,
      'loanAccountId': instance.loanAccountId,
      'amount': instance.amount.toJson(),
    };

UpdatePaymentSettingsRequest _$UpdatePaymentSettingsRequestFromJson(
        Map<String, dynamic> json) =>
    UpdatePaymentSettingsRequest(
      percentage: (json['percentage'] as num).toInt(),
    );

Map<String, dynamic> _$UpdatePaymentSettingsRequestToJson(
        UpdatePaymentSettingsRequest instance) =>
    <String, dynamic>{
      'percentage': instance.percentage,
    };

Account _$AccountFromJson(Map<String, dynamic> json) => Account(
      id: json['id'] as String,
      customerId: json['customerId'] as String,
      name: json['name'] as String,
      lendingApplicationId: json['lendingApplicationId'] as String?,
      currency: currencyFromJson(json['currency']),
      notes: json['notes'] as String?,
      loanAmount: (json['loanAmount'] as num).toDouble(),
      balances: Balances.fromJson(json['balances'] as Map<String, dynamic>),
      paymentSettings: PaymentSettings.fromJson(
          json['paymentSettings'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      productType: productTypeFromJson(json['productType']),
      mambuState: json['mambuState'] as String,
      mambuSubState: json['mambuSubState'] as String?,
      feePercentage: (json['feePercentage'] as num).toDouble(),
      loanExpiryPeriod: json['loanExpiryPeriod'] as String?,
      loanPeriod: json['loanPeriod'] as String?,
      annualFee: (json['annualFee'] as num?)?.toDouble(),
      cashWithdrawalAllowance:
          (json['cashWithdrawalAllowance'] as num?)?.toDouble(),
      firstRepaymentDate: json['firstRepaymentDate'] == null
          ? null
          : DateTime.parse(json['firstRepaymentDate'] as String),
      minLimit: (json['minLimit'] as num?)?.toDouble(),
      repaymentFeeParameters: json['repaymentFeeParameters'] == null
          ? null
          : InstallmentFeeParameters.fromJson(
              json['repaymentFeeParameters'] as Map<String, dynamic>),
      lateFeeParameters: json['lateFeeParameters'] == null
          ? null
          : InstallmentFeeParameters.fromJson(
              json['lateFeeParameters'] as Map<String, dynamic>),
      nextInstallmentDetails: json['nextInstallmentDetails'] == null
          ? null
          : NextInstallmentDetails.fromJson(
              json['nextInstallmentDetails'] as Map<String, dynamic>),
      feeFreePeriodUntil: json['feeFreePeriodUntil'] == null
          ? null
          : DateTime.parse(json['feeFreePeriodUntil'] as String),
      closeable: json['closeable'] as bool?,
      active: json['active'] as bool?,
    );

Map<String, dynamic> _$AccountToJson(Account instance) => <String, dynamic>{
      'id': instance.id,
      'customerId': instance.customerId,
      'name': instance.name,
      if (instance.lendingApplicationId case final value?)
        'lendingApplicationId': value,
      if (currencyToJson(instance.currency) case final value?)
        'currency': value,
      if (instance.notes case final value?) 'notes': value,
      'loanAmount': instance.loanAmount,
      'balances': instance.balances.toJson(),
      'paymentSettings': instance.paymentSettings.toJson(),
      'createdAt': instance.createdAt.toIso8601String(),
      if (productTypeToJson(instance.productType) case final value?)
        'productType': value,
      'mambuState': instance.mambuState,
      if (instance.mambuSubState case final value?) 'mambuSubState': value,
      'feePercentage': instance.feePercentage,
      if (instance.loanExpiryPeriod case final value?)
        'loanExpiryPeriod': value,
      if (instance.loanPeriod case final value?) 'loanPeriod': value,
      if (instance.annualFee case final value?) 'annualFee': value,
      if (instance.cashWithdrawalAllowance case final value?)
        'cashWithdrawalAllowance': value,
      if (_dateToJson(instance.firstRepaymentDate) case final value?)
        'firstRepaymentDate': value,
      if (instance.minLimit case final value?) 'minLimit': value,
      if (instance.repaymentFeeParameters?.toJson() case final value?)
        'repaymentFeeParameters': value,
      if (instance.lateFeeParameters?.toJson() case final value?)
        'lateFeeParameters': value,
      if (instance.nextInstallmentDetails?.toJson() case final value?)
        'nextInstallmentDetails': value,
      if (_dateToJson(instance.feeFreePeriodUntil) case final value?)
        'feeFreePeriodUntil': value,
      if (instance.closeable case final value?) 'closeable': value,
      if (instance.active case final value?) 'active': value,
    };

NextInstallmentDetails _$NextInstallmentDetailsFromJson(
        Map<String, dynamic> json) =>
    NextInstallmentDetails(
      dueDate: DateTime.parse(json['dueDate'] as String),
      amount:
          InstallmentAmount.fromJson(json['amount'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$NextInstallmentDetailsToJson(
        NextInstallmentDetails instance) =>
    <String, dynamic>{
      if (_dateToJson(instance.dueDate) case final value?) 'dueDate': value,
      'amount': instance.amount.toJson(),
    };

PaymentSettings _$PaymentSettingsFromJson(Map<String, dynamic> json) =>
    PaymentSettings(
      percentage: (json['percentage'] as num).toDouble(),
    );

Map<String, dynamic> _$PaymentSettingsToJson(PaymentSettings instance) =>
    <String, dynamic>{
      'percentage': instance.percentage,
    };

UpdateLoanAmountRequest _$UpdateLoanAmountRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateLoanAmountRequest(
      loanAmount: (json['loanAmount'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$UpdateLoanAmountRequestToJson(
        UpdateLoanAmountRequest instance) =>
    <String, dynamic>{
      if (instance.loanAmount case final value?) 'loanAmount': value,
    };

ProductCodeStaticDetailsDto _$ProductCodeStaticDetailsDtoFromJson(
        Map<String, dynamic> json) =>
    ProductCodeStaticDetailsDto(
      cashbackPercentage: (json['cashbackPercentage'] as num?)?.toDouble(),
      minFDAmount: json['minFDAmount'] == null
          ? null
          : Money.fromJson(json['minFDAmount'] as Map<String, dynamic>),
      limitFromFDPercent: (json['limitFromFDPercent'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$ProductCodeStaticDetailsDtoToJson(
        ProductCodeStaticDetailsDto instance) =>
    <String, dynamic>{
      if (instance.cashbackPercentage case final value?)
        'cashbackPercentage': value,
      if (instance.minFDAmount?.toJson() case final value?)
        'minFDAmount': value,
      if (instance.limitFromFDPercent case final value?)
        'limitFromFDPercent': value,
    };

BusinessDetails _$BusinessDetailsFromJson(Map<String, dynamic> json) =>
    BusinessDetails(
      businessId: json['businessId'] as String?,
      individualId: json['individualId'] as String?,
      userName: json['userName'] as String?,
      email: json['email'] as String?,
    );

Map<String, dynamic> _$BusinessDetailsToJson(BusinessDetails instance) =>
    <String, dynamic>{
      if (instance.businessId case final value?) 'businessId': value,
      if (instance.individualId case final value?) 'individualId': value,
      if (instance.userName case final value?) 'userName': value,
      if (instance.email case final value?) 'email': value,
    };

StatementDto _$StatementDtoFromJson(Map<String, dynamic> json) => StatementDto(
      statementId: json['statementId'] as String,
      month: (json['month'] as num).toInt(),
      year: (json['year'] as num).toInt(),
    );

Map<String, dynamic> _$StatementDtoToJson(StatementDto instance) =>
    <String, dynamic>{
      'statementId': instance.statementId,
      'month': instance.month,
      'year': instance.year,
    };

ReferralValidationResponse _$ReferralValidationResponseFromJson(
        Map<String, dynamic> json) =>
    ReferralValidationResponse(
      valid: json['valid'] as bool,
      anchorBusinessName: json['anchorBusinessName'] as String,
      anchorBusinessId: json['anchorBusinessId'] as String?,
      productType: productTypeNullableFromJson(json['productType']),
    );

Map<String, dynamic> _$ReferralValidationResponseToJson(
        ReferralValidationResponse instance) =>
    <String, dynamic>{
      'valid': instance.valid,
      'anchorBusinessName': instance.anchorBusinessName,
      if (instance.anchorBusinessId case final value?)
        'anchorBusinessId': value,
      if (productTypeNullableToJson(instance.productType) case final value?)
        'productType': value,
    };

Referral _$ReferralFromJson(Map<String, dynamic> json) => Referral(
      referralCode: json['referralCode'] as String?,
      supplierId: json['supplierId'] as String?,
      anchorId: json['anchorId'] as String?,
      anchorBusinessName: json['anchorBusinessName'] as String?,
      productType: productTypeNullableFromJson(json['productType']),
      status: optInStatusNullableFromJson(json['status']),
      multiUserRequestId: json['multiUserRequestId'] as String?,
      domainId: json['domainId'] as String?,
      jocataApplicationId: json['jocataApplicationId'] as String?,
      tncUrls: json['tncUrls'] == null
          ? null
          : LocalizedDocumentLink.fromJson(
              json['tncUrls'] as Map<String, dynamic>),
      pricingTriggered: json['pricingTriggered'] as bool?,
    );

Map<String, dynamic> _$ReferralToJson(Referral instance) => <String, dynamic>{
      if (instance.referralCode case final value?) 'referralCode': value,
      if (instance.supplierId case final value?) 'supplierId': value,
      if (instance.anchorId case final value?) 'anchorId': value,
      if (instance.anchorBusinessName case final value?)
        'anchorBusinessName': value,
      if (productTypeNullableToJson(instance.productType) case final value?)
        'productType': value,
      if (optInStatusNullableToJson(instance.status) case final value?)
        'status': value,
      if (instance.multiUserRequestId case final value?)
        'multiUserRequestId': value,
      if (instance.domainId case final value?) 'domainId': value,
      if (instance.jocataApplicationId case final value?)
        'jocataApplicationId': value,
      if (instance.tncUrls?.toJson() case final value?) 'tncUrls': value,
      if (instance.pricingTriggered case final value?)
        'pricingTriggered': value,
    };

Invoice _$InvoiceFromJson(Map<String, dynamic> json) => Invoice(
      invoiceId: json['invoiceId'] as String?,
      invoiceNumber: json['invoiceNumber'] as String?,
      status: json['status'] as String?,
      requestDate: json['requestDate'] as String?,
      dueDate: json['dueDate'] as String?,
      invoiceAmount: json['invoiceAmount'] as String?,
      interest: json['interest'] as String?,
      amountToSupplier: json['amountToSupplier'] as String?,
      currency: json['currency'] as String?,
      supplierName: json['supplierName'] as String?,
      preparer: json['preparer'] as String?,
    );

Map<String, dynamic> _$InvoiceToJson(Invoice instance) => <String, dynamic>{
      if (instance.invoiceId case final value?) 'invoiceId': value,
      if (instance.invoiceNumber case final value?) 'invoiceNumber': value,
      if (instance.status case final value?) 'status': value,
      if (instance.requestDate case final value?) 'requestDate': value,
      if (instance.dueDate case final value?) 'dueDate': value,
      if (instance.invoiceAmount case final value?) 'invoiceAmount': value,
      if (instance.interest case final value?) 'interest': value,
      if (instance.amountToSupplier case final value?)
        'amountToSupplier': value,
      if (instance.currency case final value?) 'currency': value,
      if (instance.supplierName case final value?) 'supplierName': value,
      if (instance.preparer case final value?) 'preparer': value,
    };

JocataViewInvoicesResponse _$JocataViewInvoicesResponseFromJson(
        Map<String, dynamic> json) =>
    JocataViewInvoicesResponse(
      invoicesCount: (json['invoicesCount'] as num?)?.toInt(),
      totalInvoiceAmount: json['totalInvoiceAmount'] as String?,
      totalDiscount: json['totalDiscount'] as String?,
      currency: json['currency'] as String?,
      invoices: (json['invoices'] as List<dynamic>?)
              ?.map((e) => Invoice.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$JocataViewInvoicesResponseToJson(
        JocataViewInvoicesResponse instance) =>
    <String, dynamic>{
      if (instance.invoicesCount case final value?) 'invoicesCount': value,
      if (instance.totalInvoiceAmount case final value?)
        'totalInvoiceAmount': value,
      if (instance.totalDiscount case final value?) 'totalDiscount': value,
      if (instance.currency case final value?) 'currency': value,
      if (instance.invoices?.map((e) => e.toJson()).toList() case final value?)
        'invoices': value,
    };

InvoicesRequestSummary _$InvoicesRequestSummaryFromJson(
        Map<String, dynamic> json) =>
    InvoicesRequestSummary(
      invoicesCount: (json['invoicesCount'] as num?)?.toInt(),
      totalInvoiceAmount: (json['totalInvoiceAmount'] as num?)?.toDouble(),
      totalDiscount: (json['totalDiscount'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
    );

Map<String, dynamic> _$InvoicesRequestSummaryToJson(
        InvoicesRequestSummary instance) =>
    <String, dynamic>{
      if (instance.invoicesCount case final value?) 'invoicesCount': value,
      if (instance.totalInvoiceAmount case final value?)
        'totalInvoiceAmount': value,
      if (instance.totalDiscount case final value?) 'totalDiscount': value,
      if (instance.currency case final value?) 'currency': value,
    };

JocataInvoiceSummaryResponse _$JocataInvoiceSummaryResponseFromJson(
        Map<String, dynamic> json) =>
    JocataInvoiceSummaryResponse(
      response: (json['response'] as List<dynamic>?)
              ?.map((e) =>
                  InvoicesRequestSummary.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$JocataInvoiceSummaryResponseToJson(
        JocataInvoiceSummaryResponse instance) =>
    <String, dynamic>{
      if (instance.response?.map((e) => e.toJson()).toList() case final value?)
        'response': value,
    };

SalesChannelResponse _$SalesChannelResponseFromJson(
        Map<String, dynamic> json) =>
    SalesChannelResponse(
      code: salesChannelResponseCodeFromJson(json['code']),
      description: json['description'] as String,
    );

Map<String, dynamic> _$SalesChannelResponseToJson(
        SalesChannelResponse instance) =>
    <String, dynamic>{
      if (salesChannelResponseCodeToJson(instance.code) case final value?)
        'code': value,
      'description': instance.description,
    };

AmountRange _$AmountRangeFromJson(Map<String, dynamic> json) => AmountRange(
      min: Money.fromJson(json['min'] as Map<String, dynamic>),
      max: Money.fromJson(json['max'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AmountRangeToJson(AmountRange instance) =>
    <String, dynamic>{
      'min': instance.min.toJson(),
      'max': instance.max.toJson(),
    };

InstallmentInterval _$InstallmentIntervalFromJson(Map<String, dynamic> json) =>
    InstallmentInterval(
      interval: installmentIntervalIntervalNullableFromJson(json['interval']),
      count: (json['count'] as num?)?.toInt(),
    );

Map<String, dynamic> _$InstallmentIntervalToJson(
        InstallmentInterval instance) =>
    <String, dynamic>{
      if (installmentIntervalIntervalNullableToJson(instance.interval)
          case final value?)
        'interval': value,
      if (instance.count case final value?) 'count': value,
    };

InterestRange _$InterestRangeFromJson(Map<String, dynamic> json) =>
    InterestRange(
      min: (json['min'] as num).toInt(),
      max: (json['max'] as num).toInt(),
    );

Map<String, dynamic> _$InterestRangeToJson(InterestRange instance) =>
    <String, dynamic>{
      'min': instance.min,
      'max': instance.max,
    };

LocalTime _$LocalTimeFromJson(Map<String, dynamic> json) => LocalTime(
      hour: (json['hour'] as num?)?.toInt(),
      minute: (json['minute'] as num?)?.toInt(),
      second: (json['second'] as num?)?.toInt(),
      nano: (json['nano'] as num?)?.toInt(),
    );

Map<String, dynamic> _$LocalTimeToJson(LocalTime instance) => <String, dynamic>{
      if (instance.hour case final value?) 'hour': value,
      if (instance.minute case final value?) 'minute': value,
      if (instance.second case final value?) 'second': value,
      if (instance.nano case final value?) 'nano': value,
    };

StaticDetails _$StaticDetailsFromJson(Map<String, dynamic> json) =>
    StaticDetails(
      feeFreePeriod: (json['feeFreePeriod'] as num).toInt(),
      cashbackPercentage: (json['cashbackPercentage'] as num).toDouble(),
      fileTypes: (json['fileTypes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      maxSize: (json['maxSize'] as num).toInt(),
      revolvingFeePercentage:
          (json['revolvingFeePercentage'] as num).toDouble(),
      minimumPrincipalRepaymentPercentage:
          (json['minimumPrincipalRepaymentPercentage'] as num).toDouble(),
      lateFee: Money.fromJson(json['lateFee'] as Map<String, dynamic>),
      minimumCreditLimitAmountOnApproval: Money.fromJson(
          json['minimumCreditLimitAmountOnApproval'] as Map<String, dynamic>),
      vatTemplateUrl: json['vatTemplateUrl'] as String,
      auditedFinancialStatementTemplateUrl:
          json['auditedFinancialStatementTemplateUrl'] as String?,
      interestRange: json['interestRange'] == null
          ? null
          : InterestRange.fromJson(
              json['interestRange'] as Map<String, dynamic>),
      securedInterestRange: json['securedInterestRange'] == null
          ? null
          : InterestRange.fromJson(
              json['securedInterestRange'] as Map<String, dynamic>),
      amountRange: json['amountRange'] == null
          ? null
          : AmountRange.fromJson(json['amountRange'] as Map<String, dynamic>),
      securedAmountRange: json['securedAmountRange'] == null
          ? null
          : AmountRange.fromJson(
              json['securedAmountRange'] as Map<String, dynamic>),
      processingFee: json['processingFee'] == null
          ? null
          : Money.fromJson(json['processingFee'] as Map<String, dynamic>),
      feeInterestPerDay: (json['feeInterestPerDay'] as num?)?.toDouble(),
      lateFeeGracePeriod: (json['lateFeeGracePeriod'] as num?)?.toInt(),
      dueDays: (json['dueDays'] as num?)?.toInt(),
      maxLimitPercentage: (json['maxLimitPercentage'] as num?)?.toDouble(),
      productType: productTypeNullableFromJson(json['productType']),
      installmentInterval: json['installmentInterval'] == null
          ? null
          : InstallmentInterval.fromJson(
              json['installmentInterval'] as Map<String, dynamic>),
      minimumDisbursementAmount: json['minimumDisbursementAmount'] == null
          ? null
          : Money.fromJson(
              json['minimumDisbursementAmount'] as Map<String, dynamic>),
      minimumRepaymentAmount: json['minimumRepaymentAmount'] == null
          ? null
          : Money.fromJson(
              json['minimumRepaymentAmount'] as Map<String, dynamic>),
      autoPaymentTime: json['autoPaymentTime'] == null
          ? null
          : LocalTime.fromJson(json['autoPaymentTime'] as Map<String, dynamic>),
      creditAutoPaymentTime: json['creditAutoPaymentTime'] as String?,
    );

Map<String, dynamic> _$StaticDetailsToJson(StaticDetails instance) =>
    <String, dynamic>{
      'feeFreePeriod': instance.feeFreePeriod,
      'cashbackPercentage': instance.cashbackPercentage,
      'fileTypes': instance.fileTypes,
      'maxSize': instance.maxSize,
      'revolvingFeePercentage': instance.revolvingFeePercentage,
      'minimumPrincipalRepaymentPercentage':
          instance.minimumPrincipalRepaymentPercentage,
      'lateFee': instance.lateFee.toJson(),
      'minimumCreditLimitAmountOnApproval':
          instance.minimumCreditLimitAmountOnApproval.toJson(),
      'vatTemplateUrl': instance.vatTemplateUrl,
      if (instance.auditedFinancialStatementTemplateUrl case final value?)
        'auditedFinancialStatementTemplateUrl': value,
      if (instance.interestRange?.toJson() case final value?)
        'interestRange': value,
      if (instance.securedInterestRange?.toJson() case final value?)
        'securedInterestRange': value,
      if (instance.amountRange?.toJson() case final value?)
        'amountRange': value,
      if (instance.securedAmountRange?.toJson() case final value?)
        'securedAmountRange': value,
      if (instance.processingFee?.toJson() case final value?)
        'processingFee': value,
      if (instance.feeInterestPerDay case final value?)
        'feeInterestPerDay': value,
      if (instance.lateFeeGracePeriod case final value?)
        'lateFeeGracePeriod': value,
      if (instance.dueDays case final value?) 'dueDays': value,
      if (instance.maxLimitPercentage case final value?)
        'maxLimitPercentage': value,
      if (productTypeNullableToJson(instance.productType) case final value?)
        'productType': value,
      if (instance.installmentInterval?.toJson() case final value?)
        'installmentInterval': value,
      if (instance.minimumDisbursementAmount?.toJson() case final value?)
        'minimumDisbursementAmount': value,
      if (instance.minimumRepaymentAmount?.toJson() case final value?)
        'minimumRepaymentAmount': value,
      if (instance.autoPaymentTime?.toJson() case final value?)
        'autoPaymentTime': value,
      if (instance.creditAutoPaymentTime case final value?)
        'creditAutoPaymentTime': value,
    };

NonRegisteredVatReason _$NonRegisteredVatReasonFromJson(
        Map<String, dynamic> json) =>
    NonRegisteredVatReason(
      type: nonRegisteredVatReasonTypeNullableFromJson(json['type']),
      en: json['en'] as String?,
      ar: json['ar'] as String?,
    );

Map<String, dynamic> _$NonRegisteredVatReasonToJson(
        NonRegisteredVatReason instance) =>
    <String, dynamic>{
      if (nonRegisteredVatReasonTypeNullableToJson(instance.type)
          case final value?)
        'type': value,
      if (instance.en case final value?) 'en': value,
      if (instance.ar case final value?) 'ar': value,
    };

EmployeeCountResponse _$EmployeeCountResponseFromJson(
        Map<String, dynamic> json) =>
    EmployeeCountResponse(
      code: employeeCountResponseCodeFromJson(json['code']),
      description: json['description'] as String,
    );

Map<String, dynamic> _$EmployeeCountResponseToJson(
        EmployeeCountResponse instance) =>
    <String, dynamic>{
      if (employeeCountResponseCodeToJson(instance.code) case final value?)
        'code': value,
      'description': instance.description,
    };

GetOffersResponse _$GetOffersResponseFromJson(Map<String, dynamic> json) =>
    GetOffersResponse(
      nonEligibilityDetails: json['nonEligibilityDetails'] == null
          ? null
          : NonEligibilityDetails.fromJson(
              json['nonEligibilityDetails'] as Map<String, dynamic>),
      items: (json['items'] as List<dynamic>?)
              ?.map(
                  (e) => PreQualifiedOffer.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$GetOffersResponseToJson(GetOffersResponse instance) =>
    <String, dynamic>{
      if (instance.nonEligibilityDetails?.toJson() case final value?)
        'nonEligibilityDetails': value,
      if (instance.items?.map((e) => e.toJson()).toList() case final value?)
        'items': value,
      if (instance.total case final value?) 'total': value,
    };

NonEligibilityDetails _$NonEligibilityDetailsFromJson(
        Map<String, dynamic> json) =>
    NonEligibilityDetails(
      description: json['description'] as String,
      subDescription: json['subDescription'] as String,
      isUpdateDocument: json['isUpdateDocument'] as bool,
      reason: nonEligibilityDetailsReasonFromJson(json['reason']),
    );

Map<String, dynamic> _$NonEligibilityDetailsToJson(
        NonEligibilityDetails instance) =>
    <String, dynamic>{
      'description': instance.description,
      'subDescription': instance.subDescription,
      'isUpdateDocument': instance.isUpdateDocument,
      if (nonEligibilityDetailsReasonToJson(instance.reason) case final value?)
        'reason': value,
    };

PreQualifiedOffer _$PreQualifiedOfferFromJson(Map<String, dynamic> json) =>
    PreQualifiedOffer(
      money: Money.fromJson(json['money'] as Map<String, dynamic>),
      productType: productTypeFromJson(json['productType']),
    );

Map<String, dynamic> _$PreQualifiedOfferToJson(PreQualifiedOffer instance) =>
    <String, dynamic>{
      'money': instance.money.toJson(),
      if (productTypeToJson(instance.productType) case final value?)
        'productType': value,
    };

MultiUserApprovalsInfoDto _$MultiUserApprovalsInfoDtoFromJson(
        Map<String, dynamic> json) =>
    MultiUserApprovalsInfoDto(
      name: json['name'] as String?,
      status: multiUserApprovalsInfoDtoStatusNullableFromJson(json['status']),
      timestamp: json['timestamp'] as String?,
    );

Map<String, dynamic> _$MultiUserApprovalsInfoDtoToJson(
        MultiUserApprovalsInfoDto instance) =>
    <String, dynamic>{
      if (instance.name case final value?) 'name': value,
      if (multiUserApprovalsInfoDtoStatusNullableToJson(instance.status)
          case final value?)
        'status': value,
      if (instance.timestamp case final value?) 'timestamp': value,
    };

GetRequestsParams _$GetRequestsParamsFromJson(Map<String, dynamic> json) =>
    GetRequestsParams(
      page: (json['page'] as num?)?.toInt(),
      size: (json['size'] as num?)?.toInt(),
      id: json['id'] as String?,
      sort: json['sort'] as String?,
      domainTypes: getRequestsParamsDomainTypesListFromJson(
          json['domainTypes'] as List?),
      fromDate: json['fromDate'] == null
          ? null
          : DateTime.parse(json['fromDate'] as String),
      toDate: json['toDate'] == null
          ? null
          : DateTime.parse(json['toDate'] as String),
      status: getRequestsParamsStatusListFromJson(json['status'] as List?),
    );

Map<String, dynamic> _$GetRequestsParamsToJson(GetRequestsParams instance) =>
    <String, dynamic>{
      if (instance.page case final value?) 'page': value,
      if (instance.size case final value?) 'size': value,
      if (instance.id case final value?) 'id': value,
      if (instance.sort case final value?) 'sort': value,
      'domainTypes':
          getRequestsParamsDomainTypesListToJson(instance.domainTypes),
      if (_dateToJson(instance.fromDate) case final value?) 'fromDate': value,
      if (_dateToJson(instance.toDate) case final value?) 'toDate': value,
      'status': getRequestsParamsStatusListToJson(instance.status),
    };

GetMultiUserRequestsResponse _$GetMultiUserRequestsResponseFromJson(
        Map<String, dynamic> json) =>
    GetMultiUserRequestsResponse(
      totalNumberRecords: (json['totalNumberRecords'] as num?)?.toInt(),
      totalNumberPages: (json['totalNumberPages'] as num?)?.toInt(),
      requests: (json['requests'] as List<dynamic>?)
              ?.map((e) =>
                  MultiUserResponseDTO.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$GetMultiUserRequestsResponseToJson(
        GetMultiUserRequestsResponse instance) =>
    <String, dynamic>{
      if (instance.totalNumberRecords case final value?)
        'totalNumberRecords': value,
      if (instance.totalNumberPages case final value?)
        'totalNumberPages': value,
      if (instance.requests?.map((e) => e.toJson()).toList() case final value?)
        'requests': value,
    };

JsonNode _$JsonNodeFromJson(Map<String, dynamic> json) => JsonNode();

Map<String, dynamic> _$JsonNodeToJson(JsonNode instance) => <String, dynamic>{};

BankDetails _$BankDetailsFromJson(Map<String, dynamic> json) => BankDetails(
      bankName: json['bankName'] as String?,
      bankCode: json['bankCode'] as String?,
      bic: json['bic'] as String?,
    );

Map<String, dynamic> _$BankDetailsToJson(BankDetails instance) =>
    <String, dynamic>{
      if (instance.bankName case final value?) 'bankName': value,
      if (instance.bankCode case final value?) 'bankCode': value,
      if (instance.bic case final value?) 'bic': value,
    };

CreditArrangement _$CreditArrangementFromJson(Map<String, dynamic> json) =>
    CreditArrangement(
      accounts: (json['accounts'] as List<dynamic>?)
              ?.map((e) => Account.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$CreditArrangementToJson(CreditArrangement instance) =>
    <String, dynamic>{
      if (instance.accounts?.map((e) => e.toJson()).toList() case final value?)
        'accounts': value,
    };

EasyCashLimit _$EasyCashLimitFromJson(Map<String, dynamic> json) =>
    EasyCashLimit(
      easyCashPercentageLimit:
          (json['easyCashPercentageLimit'] as num).toDouble(),
      easyCashInterestRate: (json['easyCashInterestRate'] as num).toDouble(),
      easyCashAvailableLimit: Money.fromJson(
          json['easyCashAvailableLimit'] as Map<String, dynamic>),
      easyCashLimit:
          Money.fromJson(json['easyCashLimit'] as Map<String, dynamic>),
      dueDate: DateTime.parse(json['dueDate'] as String),
      creditArrangement: CreditArrangement.fromJson(
          json['creditArrangement'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EasyCashLimitToJson(EasyCashLimit instance) =>
    <String, dynamic>{
      'easyCashPercentageLimit': instance.easyCashPercentageLimit,
      'easyCashInterestRate': instance.easyCashInterestRate,
      'easyCashAvailableLimit': instance.easyCashAvailableLimit.toJson(),
      'easyCashLimit': instance.easyCashLimit.toJson(),
      if (_dateToJson(instance.dueDate) case final value?) 'dueDate': value,
      'creditArrangement': instance.creditArrangement.toJson(),
    };

DownloadResponse _$DownloadResponseFromJson(Map<String, dynamic> json) =>
    DownloadResponse(
      data: json['data'] as String?,
    );

Map<String, dynamic> _$DownloadResponseToJson(DownloadResponse instance) =>
    <String, dynamic>{
      if (instance.data case final value?) 'data': value,
    };

ApplicationListResponse _$ApplicationListResponseFromJson(
        Map<String, dynamic> json) =>
    ApplicationListResponse(
      data: (json['data'] as List<dynamic>?)
              ?.map((e) => Application.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$ApplicationListResponseToJson(
        ApplicationListResponse instance) =>
    <String, dynamic>{
      if (instance.data?.map((e) => e.toJson()).toList() case final value?)
        'data': value,
    };

DocumentListResponse _$DocumentListResponseFromJson(
        Map<String, dynamic> json) =>
    DocumentListResponse(
      size: (json['size'] as num?)?.toInt(),
      items: (json['items'] as List<dynamic>?)
              ?.map((e) =>
                  DocumentMetadataResponse.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$DocumentListResponseToJson(
        DocumentListResponse instance) =>
    <String, dynamic>{
      if (instance.size case final value?) 'size': value,
      if (instance.items?.map((e) => e.toJson()).toList() case final value?)
        'items': value,
    };

GetCreditAgreementResponse _$GetCreditAgreementResponseFromJson(
        Map<String, dynamic> json) =>
    GetCreditAgreementResponse(
      data: json['data'] as String?,
    );

Map<String, dynamic> _$GetCreditAgreementResponseToJson(
        GetCreditAgreementResponse instance) =>
    <String, dynamic>{
      if (instance.data case final value?) 'data': value,
    };

RepaymentSchedulePdfResponse _$RepaymentSchedulePdfResponseFromJson(
        Map<String, dynamic> json) =>
    RepaymentSchedulePdfResponse(
      pdfString: json['pdfString'] as String?,
    );

Map<String, dynamic> _$RepaymentSchedulePdfResponseToJson(
        RepaymentSchedulePdfResponse instance) =>
    <String, dynamic>{
      if (instance.pdfString case final value?) 'pdfString': value,
    };

AutoPaymentInfo _$AutoPaymentInfoFromJson(Map<String, dynamic> json) =>
    AutoPaymentInfo(
      amount: (json['amount'] as num).toDouble(),
      fee: (json['fee'] as num).toDouble(),
      totalOutstandingBalance:
          (json['totalOutstandingBalance'] as num).toDouble(),
      linkedAccountsOutstanding:
          (json['linkedAccountsOutstanding'] as num).toDouble(),
      feeFreeDate: json['feeFreeDate'] == null
          ? null
          : DateTime.parse(json['feeFreeDate'] as String),
      fullRepaymentDoneInCurrentCycle:
          json['fullRepaymentDoneInCurrentCycle'] as bool,
      currency: currencyFromJson(json['currency']),
      paymentDate: DateTime.parse(json['paymentDate'] as String),
      minimumRepaymentAmount:
          (json['minimumRepaymentAmount'] as num).toDouble(),
      delinquentAccountDetails: json['delinquentAccountDetails'] == null
          ? null
          : DelinquentAccountDetails.fromJson(
              json['delinquentAccountDetails'] as Map<String, dynamic>),
      amountPaidInCurrentCycle:
          (json['amountPaidInCurrentCycle'] as num?)?.toDouble(),
      preferences: json['preferences'] == null
          ? null
          : PreferencesV2.fromJson(json['preferences'] as Map<String, dynamic>),
      feesPerDay: (json['feesPerDay'] as num?)?.toDouble(),
      customerId: json['customerId'] as String?,
      totalFeeBalance: (json['totalFeeBalance'] as num?)?.toDouble(),
      schedule: json['schedule'] == null
          ? null
          : Schedule.fromJson(json['schedule'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AutoPaymentInfoToJson(AutoPaymentInfo instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'fee': instance.fee,
      'totalOutstandingBalance': instance.totalOutstandingBalance,
      'linkedAccountsOutstanding': instance.linkedAccountsOutstanding,
      if (_dateToJson(instance.feeFreeDate) case final value?)
        'feeFreeDate': value,
      'fullRepaymentDoneInCurrentCycle':
          instance.fullRepaymentDoneInCurrentCycle,
      if (currencyToJson(instance.currency) case final value?)
        'currency': value,
      if (_dateToJson(instance.paymentDate) case final value?)
        'paymentDate': value,
      'minimumRepaymentAmount': instance.minimumRepaymentAmount,
      if (instance.delinquentAccountDetails?.toJson() case final value?)
        'delinquentAccountDetails': value,
      if (instance.amountPaidInCurrentCycle case final value?)
        'amountPaidInCurrentCycle': value,
      if (instance.preferences?.toJson() case final value?)
        'preferences': value,
      if (instance.feesPerDay case final value?) 'feesPerDay': value,
      if (instance.customerId case final value?) 'customerId': value,
      if (instance.totalFeeBalance case final value?) 'totalFeeBalance': value,
      if (instance.schedule?.toJson() case final value?) 'schedule': value,
    };

DelinquentAccountDetails _$DelinquentAccountDetailsFromJson(
        Map<String, dynamic> json) =>
    DelinquentAccountDetails(
      minimumRepaymentAmount:
          (json['minimumRepaymentAmount'] as num?)?.toDouble(),
      locked: json['locked'] as bool?,
      daysPastDue: (json['daysPastDue'] as num?)?.toInt(),
      dueDate: DateTime.parse(json['dueDate'] as String),
      latePaymentFee: (json['latePaymentFee'] as num?)?.toDouble(),
      minimumRepaymentAmountExcLateFee:
          (json['minimumRepaymentAmountExcLateFee'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$DelinquentAccountDetailsToJson(
        DelinquentAccountDetails instance) =>
    <String, dynamic>{
      if (instance.minimumRepaymentAmount case final value?)
        'minimumRepaymentAmount': value,
      if (instance.locked case final value?) 'locked': value,
      if (instance.daysPastDue case final value?) 'daysPastDue': value,
      if (_dateToJson(instance.dueDate) case final value?) 'dueDate': value,
      if (instance.latePaymentFee case final value?) 'latePaymentFee': value,
      if (instance.minimumRepaymentAmountExcLateFee case final value?)
        'minimumRepaymentAmountExcLateFee': value,
    };

CreditLimitDetailsDto _$CreditLimitDetailsDtoFromJson(
        Map<String, dynamic> json) =>
    CreditLimitDetailsDto(
      limitDetails: json['limitDetails'] == null
          ? null
          : LimitDetails.fromJson(json['limitDetails'] as Map<String, dynamic>),
      inProgressRequest: json['inProgressRequest'] == null
          ? null
          : InProgressRequest.fromJson(
              json['inProgressRequest'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CreditLimitDetailsDtoToJson(
        CreditLimitDetailsDto instance) =>
    <String, dynamic>{
      if (instance.limitDetails?.toJson() case final value?)
        'limitDetails': value,
      if (instance.inProgressRequest?.toJson() case final value?)
        'inProgressRequest': value,
    };

InProgressRequest _$InProgressRequestFromJson(Map<String, dynamic> json) =>
    InProgressRequest(
      id: json['id'] as String,
      money: Money.fromJson(json['money'] as Map<String, dynamic>),
      date: DateTime.parse(json['date'] as String),
    );

Map<String, dynamic> _$InProgressRequestToJson(InProgressRequest instance) =>
    <String, dynamic>{
      'id': instance.id,
      'money': instance.money.toJson(),
      'date': instance.date.toIso8601String(),
    };

LimitDetails _$LimitDetailsFromJson(Map<String, dynamic> json) => LimitDetails(
      minLimit: Money.fromJson(json['minLimit'] as Map<String, dynamic>),
      maxLimit: Money.fromJson(json['maxLimit'] as Map<String, dynamic>),
      allowIncreaseLimit: json['allowIncreaseLimit'] as bool,
      allowReduceLimit: json['allowReduceLimit'] as bool,
    );

Map<String, dynamic> _$LimitDetailsToJson(LimitDetails instance) =>
    <String, dynamic>{
      'minLimit': instance.minLimit.toJson(),
      'maxLimit': instance.maxLimit.toJson(),
      'allowIncreaseLimit': instance.allowIncreaseLimit,
      'allowReduceLimit': instance.allowReduceLimit,
    };

ApiV1ApplicationsApplicationIdDocumentsPost$RequestBody
    _$ApiV1ApplicationsApplicationIdDocumentsPost$RequestBodyFromJson(
            Map<String, dynamic> json) =>
        ApiV1ApplicationsApplicationIdDocumentsPost$RequestBody(
          file: json['file'] as String,
        );

Map<String, dynamic>
    _$ApiV1ApplicationsApplicationIdDocumentsPost$RequestBodyToJson(
            ApiV1ApplicationsApplicationIdDocumentsPost$RequestBody instance) =>
        <String, dynamic>{
          'file': instance.file,
        };
