// ignore_for_file: type=lint

import 'package:json_annotation/json_annotation.dart';
import 'package:json_annotation/json_annotation.dart' as json;
import 'package:collection/collection.dart';
import 'dart:convert';

import 'schema.enums.swagger.dart' as enums;
export 'schema.enums.swagger.dart';

part 'schema.swagger.g.dart';

@JsonSerializable(explicitToJson: true)
class PreferencesV2 {
  const PreferencesV2({
    this.settlementAccountId,
    this.disableAutoPayFromSavings,
  });

  factory PreferencesV2.fromJson(Map<String, dynamic> json) =>
      _$PreferencesV2FromJson(json);

  static const toJsonFactory = _$PreferencesV2ToJson;
  Map<String, dynamic> toJson() => _$PreferencesV2ToJson(this);

  @JsonKey(name: 'settlementAccountId', includeIfNull: false)
  final String? settlementAccountId;
  @J<PERSON><PERSON><PERSON>(name: 'disableAutoPayFromSavings', includeIfNull: false)
  final bool? disableAutoPayFromSavings;
  static const fromJsonFactory = _$PreferencesV2FromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $PreferencesV2Extension on PreferencesV2 {
  PreferencesV2 copyWith(
      {String? settlementAccountId, bool? disableAutoPayFromSavings}) {
    return PreferencesV2(
        settlementAccountId: settlementAccountId ?? this.settlementAccountId,
        disableAutoPayFromSavings:
            disableAutoPayFromSavings ?? this.disableAutoPayFromSavings);
  }

  PreferencesV2 copyWithWrapped(
      {Wrapped<String?>? settlementAccountId,
      Wrapped<bool?>? disableAutoPayFromSavings}) {
    return PreferencesV2(
        settlementAccountId: (settlementAccountId != null
            ? settlementAccountId.value
            : this.settlementAccountId),
        disableAutoPayFromSavings: (disableAutoPayFromSavings != null
            ? disableAutoPayFromSavings.value
            : this.disableAutoPayFromSavings));
  }
}

@JsonSerializable(explicitToJson: true)
class UpdatePreferencesRequestV2 {
  const UpdatePreferencesRequestV2({
    required this.loanAccountId,
    required this.preferences,
  });

  factory UpdatePreferencesRequestV2.fromJson(Map<String, dynamic> json) =>
      _$UpdatePreferencesRequestV2FromJson(json);

  static const toJsonFactory = _$UpdatePreferencesRequestV2ToJson;
  Map<String, dynamic> toJson() => _$UpdatePreferencesRequestV2ToJson(this);

  @JsonKey(name: 'loanAccountId', includeIfNull: false)
  final String loanAccountId;
  @JsonKey(name: 'preferences', includeIfNull: false)
  final PreferencesV2 preferences;
  static const fromJsonFactory = _$UpdatePreferencesRequestV2FromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UpdatePreferencesRequestV2Extension on UpdatePreferencesRequestV2 {
  UpdatePreferencesRequestV2 copyWith(
      {String? loanAccountId, PreferencesV2? preferences}) {
    return UpdatePreferencesRequestV2(
        loanAccountId: loanAccountId ?? this.loanAccountId,
        preferences: preferences ?? this.preferences);
  }

  UpdatePreferencesRequestV2 copyWithWrapped(
      {Wrapped<String>? loanAccountId, Wrapped<PreferencesV2>? preferences}) {
    return UpdatePreferencesRequestV2(
        loanAccountId:
            (loanAccountId != null ? loanAccountId.value : this.loanAccountId),
        preferences:
            (preferences != null ? preferences.value : this.preferences));
  }
}

@JsonSerializable(explicitToJson: true)
class LoanAccount {
  const LoanAccount({
    this.accountId,
    this.name,
    this.loanAmount,
    this.customerId,
    this.monthlyRepaymentDay,
    this.preferences,
    this.isActive,
  });

  factory LoanAccount.fromJson(Map<String, dynamic> json) =>
      _$LoanAccountFromJson(json);

  static const toJsonFactory = _$LoanAccountToJson;
  Map<String, dynamic> toJson() => _$LoanAccountToJson(this);

  @JsonKey(name: 'accountId', includeIfNull: false)
  final String? accountId;
  @JsonKey(name: 'name', includeIfNull: false)
  final String? name;
  @JsonKey(name: 'loanAmount', includeIfNull: false)
  final double? loanAmount;
  @JsonKey(name: 'customerId', includeIfNull: false)
  final String? customerId;
  @JsonKey(name: 'monthlyRepaymentDay', includeIfNull: false)
  final int? monthlyRepaymentDay;
  @JsonKey(name: 'preferences', includeIfNull: false)
  final PreferencesV2? preferences;
  @JsonKey(name: 'isActive', includeIfNull: false)
  final bool? isActive;
  static const fromJsonFactory = _$LoanAccountFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $LoanAccountExtension on LoanAccount {
  LoanAccount copyWith(
      {String? accountId,
      String? name,
      double? loanAmount,
      String? customerId,
      int? monthlyRepaymentDay,
      PreferencesV2? preferences,
      bool? isActive}) {
    return LoanAccount(
        accountId: accountId ?? this.accountId,
        name: name ?? this.name,
        loanAmount: loanAmount ?? this.loanAmount,
        customerId: customerId ?? this.customerId,
        monthlyRepaymentDay: monthlyRepaymentDay ?? this.monthlyRepaymentDay,
        preferences: preferences ?? this.preferences,
        isActive: isActive ?? this.isActive);
  }

  LoanAccount copyWithWrapped(
      {Wrapped<String?>? accountId,
      Wrapped<String?>? name,
      Wrapped<double?>? loanAmount,
      Wrapped<String?>? customerId,
      Wrapped<int?>? monthlyRepaymentDay,
      Wrapped<PreferencesV2?>? preferences,
      Wrapped<bool?>? isActive}) {
    return LoanAccount(
        accountId: (accountId != null ? accountId.value : this.accountId),
        name: (name != null ? name.value : this.name),
        loanAmount: (loanAmount != null ? loanAmount.value : this.loanAmount),
        customerId: (customerId != null ? customerId.value : this.customerId),
        monthlyRepaymentDay: (monthlyRepaymentDay != null
            ? monthlyRepaymentDay.value
            : this.monthlyRepaymentDay),
        preferences:
            (preferences != null ? preferences.value : this.preferences),
        isActive: (isActive != null ? isActive.value : this.isActive));
  }
}

@JsonSerializable(explicitToJson: true)
class Preferences {
  const Preferences({
    required this.disableAutoPayFromSavings,
  });

  factory Preferences.fromJson(Map<String, dynamic> json) =>
      _$PreferencesFromJson(json);

  static const toJsonFactory = _$PreferencesToJson;
  Map<String, dynamic> toJson() => _$PreferencesToJson(this);

  @JsonKey(name: 'disableAutoPayFromSavings', includeIfNull: false)
  final bool disableAutoPayFromSavings;
  static const fromJsonFactory = _$PreferencesFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $PreferencesExtension on Preferences {
  Preferences copyWith({bool? disableAutoPayFromSavings}) {
    return Preferences(
        disableAutoPayFromSavings:
            disableAutoPayFromSavings ?? this.disableAutoPayFromSavings);
  }

  Preferences copyWithWrapped({Wrapped<bool>? disableAutoPayFromSavings}) {
    return Preferences(
        disableAutoPayFromSavings: (disableAutoPayFromSavings != null
            ? disableAutoPayFromSavings.value
            : this.disableAutoPayFromSavings));
  }
}

@JsonSerializable(explicitToJson: true)
class UpdatePreferencesRequest {
  const UpdatePreferencesRequest({
    required this.loanAccountId,
    required this.preferences,
  });

  factory UpdatePreferencesRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdatePreferencesRequestFromJson(json);

  static const toJsonFactory = _$UpdatePreferencesRequestToJson;
  Map<String, dynamic> toJson() => _$UpdatePreferencesRequestToJson(this);

  @JsonKey(name: 'loanAccountId', includeIfNull: false)
  final String loanAccountId;
  @JsonKey(name: 'preferences', includeIfNull: false)
  final Preferences preferences;
  static const fromJsonFactory = _$UpdatePreferencesRequestFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UpdatePreferencesRequestExtension on UpdatePreferencesRequest {
  UpdatePreferencesRequest copyWith(
      {String? loanAccountId, Preferences? preferences}) {
    return UpdatePreferencesRequest(
        loanAccountId: loanAccountId ?? this.loanAccountId,
        preferences: preferences ?? this.preferences);
  }

  UpdatePreferencesRequest copyWithWrapped(
      {Wrapped<String>? loanAccountId, Wrapped<Preferences>? preferences}) {
    return UpdatePreferencesRequest(
        loanAccountId:
            (loanAccountId != null ? loanAccountId.value : this.loanAccountId),
        preferences:
            (preferences != null ? preferences.value : this.preferences));
  }
}

@JsonSerializable(explicitToJson: true)
class FailedEventUpdateDto {
  const FailedEventUpdateDto({
    this.ids,
    this.resolved,
  });

  factory FailedEventUpdateDto.fromJson(Map<String, dynamic> json) =>
      _$FailedEventUpdateDtoFromJson(json);

  static const toJsonFactory = _$FailedEventUpdateDtoToJson;
  Map<String, dynamic> toJson() => _$FailedEventUpdateDtoToJson(this);

  @JsonKey(name: 'ids', includeIfNull: false, defaultValue: <String>[])
  final List<String>? ids;
  @JsonKey(name: 'resolved', includeIfNull: false)
  final bool? resolved;
  static const fromJsonFactory = _$FailedEventUpdateDtoFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $FailedEventUpdateDtoExtension on FailedEventUpdateDto {
  FailedEventUpdateDto copyWith({List<String>? ids, bool? resolved}) {
    return FailedEventUpdateDto(
        ids: ids ?? this.ids, resolved: resolved ?? this.resolved);
  }

  FailedEventUpdateDto copyWithWrapped(
      {Wrapped<List<String>?>? ids, Wrapped<bool?>? resolved}) {
    return FailedEventUpdateDto(
        ids: (ids != null ? ids.value : this.ids),
        resolved: (resolved != null ? resolved.value : this.resolved));
  }
}

@JsonSerializable(explicitToJson: true)
class FailedEventsDocument {
  const FailedEventsDocument({
    this.id,
    this.applicationId,
    this.customerId,
    this.correlationId,
    this.cloudEventId,
    this.exception,
    this.requestURL,
    this.request,
    this.type,
    this.numberOfRetries,
    this.topic,
    this.severity,
    this.resolved,
    this.retryable,
  });

  factory FailedEventsDocument.fromJson(Map<String, dynamic> json) =>
      _$FailedEventsDocumentFromJson(json);

  static const toJsonFactory = _$FailedEventsDocumentToJson;
  Map<String, dynamic> toJson() => _$FailedEventsDocumentToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String? id;
  @JsonKey(name: 'applicationId', includeIfNull: false)
  final String? applicationId;
  @JsonKey(name: 'customerId', includeIfNull: false)
  final String? customerId;
  @JsonKey(name: 'correlationId', includeIfNull: false)
  final String? correlationId;
  @JsonKey(name: 'cloudEventId', includeIfNull: false)
  final String? cloudEventId;
  @JsonKey(name: 'exception', includeIfNull: false)
  final String? exception;
  @JsonKey(name: 'requestURL', includeIfNull: false)
  final String? requestURL;
  @JsonKey(name: 'request', includeIfNull: false)
  final String? request;
  @JsonKey(
    name: 'type',
    includeIfNull: false,
    toJson: failedEventsDocumentTypeNullableToJson,
    fromJson: failedEventsDocumentTypeNullableFromJson,
  )
  final enums.FailedEventsDocumentType? type;
  @JsonKey(name: 'numberOfRetries', includeIfNull: false)
  final int? numberOfRetries;
  @JsonKey(name: 'topic', includeIfNull: false)
  final String? topic;
  @JsonKey(
    name: 'severity',
    includeIfNull: false,
    toJson: failedEventsDocumentSeverityNullableToJson,
    fromJson: failedEventsDocumentSeverityNullableFromJson,
  )
  final enums.FailedEventsDocumentSeverity? severity;
  @JsonKey(name: 'resolved', includeIfNull: false)
  final bool? resolved;
  @JsonKey(name: 'retryable', includeIfNull: false)
  final Retryable? retryable;
  static const fromJsonFactory = _$FailedEventsDocumentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $FailedEventsDocumentExtension on FailedEventsDocument {
  FailedEventsDocument copyWith(
      {String? id,
      String? applicationId,
      String? customerId,
      String? correlationId,
      String? cloudEventId,
      String? exception,
      String? requestURL,
      String? request,
      enums.FailedEventsDocumentType? type,
      int? numberOfRetries,
      String? topic,
      enums.FailedEventsDocumentSeverity? severity,
      bool? resolved,
      Retryable? retryable}) {
    return FailedEventsDocument(
        id: id ?? this.id,
        applicationId: applicationId ?? this.applicationId,
        customerId: customerId ?? this.customerId,
        correlationId: correlationId ?? this.correlationId,
        cloudEventId: cloudEventId ?? this.cloudEventId,
        exception: exception ?? this.exception,
        requestURL: requestURL ?? this.requestURL,
        request: request ?? this.request,
        type: type ?? this.type,
        numberOfRetries: numberOfRetries ?? this.numberOfRetries,
        topic: topic ?? this.topic,
        severity: severity ?? this.severity,
        resolved: resolved ?? this.resolved,
        retryable: retryable ?? this.retryable);
  }

  FailedEventsDocument copyWithWrapped(
      {Wrapped<String?>? id,
      Wrapped<String?>? applicationId,
      Wrapped<String?>? customerId,
      Wrapped<String?>? correlationId,
      Wrapped<String?>? cloudEventId,
      Wrapped<String?>? exception,
      Wrapped<String?>? requestURL,
      Wrapped<String?>? request,
      Wrapped<enums.FailedEventsDocumentType?>? type,
      Wrapped<int?>? numberOfRetries,
      Wrapped<String?>? topic,
      Wrapped<enums.FailedEventsDocumentSeverity?>? severity,
      Wrapped<bool?>? resolved,
      Wrapped<Retryable?>? retryable}) {
    return FailedEventsDocument(
        id: (id != null ? id.value : this.id),
        applicationId:
            (applicationId != null ? applicationId.value : this.applicationId),
        customerId: (customerId != null ? customerId.value : this.customerId),
        correlationId:
            (correlationId != null ? correlationId.value : this.correlationId),
        cloudEventId:
            (cloudEventId != null ? cloudEventId.value : this.cloudEventId),
        exception: (exception != null ? exception.value : this.exception),
        requestURL: (requestURL != null ? requestURL.value : this.requestURL),
        request: (request != null ? request.value : this.request),
        type: (type != null ? type.value : this.type),
        numberOfRetries: (numberOfRetries != null
            ? numberOfRetries.value
            : this.numberOfRetries),
        topic: (topic != null ? topic.value : this.topic),
        severity: (severity != null ? severity.value : this.severity),
        resolved: (resolved != null ? resolved.value : this.resolved),
        retryable: (retryable != null ? retryable.value : this.retryable));
  }
}

@JsonSerializable(explicitToJson: true)
class Retryable {
  const Retryable({
    this.isRetryable,
    this.isCloudEvent,
  });

  factory Retryable.fromJson(Map<String, dynamic> json) =>
      _$RetryableFromJson(json);

  static const toJsonFactory = _$RetryableToJson;
  Map<String, dynamic> toJson() => _$RetryableToJson(this);

  @JsonKey(name: 'isRetryable', includeIfNull: false)
  final bool? isRetryable;
  @JsonKey(name: 'isCloudEvent', includeIfNull: false)
  final bool? isCloudEvent;
  static const fromJsonFactory = _$RetryableFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $RetryableExtension on Retryable {
  Retryable copyWith({bool? isRetryable, bool? isCloudEvent}) {
    return Retryable(
        isRetryable: isRetryable ?? this.isRetryable,
        isCloudEvent: isCloudEvent ?? this.isCloudEvent);
  }

  Retryable copyWithWrapped(
      {Wrapped<bool?>? isRetryable, Wrapped<bool?>? isCloudEvent}) {
    return Retryable(
        isRetryable:
            (isRetryable != null ? isRetryable.value : this.isRetryable),
        isCloudEvent:
            (isCloudEvent != null ? isCloudEvent.value : this.isCloudEvent));
  }
}

@JsonSerializable(explicitToJson: true)
class UpdateApplicationCommand {
  const UpdateApplicationCommand();

  factory UpdateApplicationCommand.fromJson(Map<String, dynamic> json) =>
      _$UpdateApplicationCommandFromJson(json);

  static const toJsonFactory = _$UpdateApplicationCommandToJson;
  Map<String, dynamic> toJson() => _$UpdateApplicationCommandToJson(this);

  static const fromJsonFactory = _$UpdateApplicationCommandFromJson;

  @override
  String toString() => jsonEncode(this);
}

@JsonSerializable(explicitToJson: true)
class Address {
  const Address({
    required this.addressType,
    required this.buildingName,
    required this.street,
    required this.city,
    required this.emirate,
    required this.country,
    required this.unitNumber,
    this.poBox,
  });

  factory Address.fromJson(Map<String, dynamic> json) =>
      _$AddressFromJson(json);

  static const toJsonFactory = _$AddressToJson;
  Map<String, dynamic> toJson() => _$AddressToJson(this);

  @JsonKey(
    name: 'addressType',
    includeIfNull: false,
    toJson: addressAddressTypeToJson,
    fromJson: addressAddressTypeFromJson,
  )
  final enums.AddressAddressType addressType;
  @JsonKey(name: 'buildingName', includeIfNull: false)
  final String buildingName;
  @JsonKey(name: 'street', includeIfNull: false)
  final String street;
  @JsonKey(name: 'city', includeIfNull: false)
  final String city;
  @JsonKey(name: 'emirate', includeIfNull: false)
  final String emirate;
  @JsonKey(name: 'country', includeIfNull: false)
  final String country;
  @JsonKey(name: 'unitNumber', includeIfNull: false)
  final String unitNumber;
  @JsonKey(name: 'poBox', includeIfNull: false)
  final String? poBox;
  static const fromJsonFactory = _$AddressFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AddressExtension on Address {
  Address copyWith(
      {enums.AddressAddressType? addressType,
      String? buildingName,
      String? street,
      String? city,
      String? emirate,
      String? country,
      String? unitNumber,
      String? poBox}) {
    return Address(
        addressType: addressType ?? this.addressType,
        buildingName: buildingName ?? this.buildingName,
        street: street ?? this.street,
        city: city ?? this.city,
        emirate: emirate ?? this.emirate,
        country: country ?? this.country,
        unitNumber: unitNumber ?? this.unitNumber,
        poBox: poBox ?? this.poBox);
  }

  Address copyWithWrapped(
      {Wrapped<enums.AddressAddressType>? addressType,
      Wrapped<String>? buildingName,
      Wrapped<String>? street,
      Wrapped<String>? city,
      Wrapped<String>? emirate,
      Wrapped<String>? country,
      Wrapped<String>? unitNumber,
      Wrapped<String?>? poBox}) {
    return Address(
        addressType:
            (addressType != null ? addressType.value : this.addressType),
        buildingName:
            (buildingName != null ? buildingName.value : this.buildingName),
        street: (street != null ? street.value : this.street),
        city: (city != null ? city.value : this.city),
        emirate: (emirate != null ? emirate.value : this.emirate),
        country: (country != null ? country.value : this.country),
        unitNumber: (unitNumber != null ? unitNumber.value : this.unitNumber),
        poBox: (poBox != null ? poBox.value : this.poBox));
  }
}

@JsonSerializable(explicitToJson: true)
class Application {
  const Application({
    this.id,
    this.businessId,
    this.externalReferenceId,
    this.companyName,
    this.applicantId,
    this.referralCode,
    this.status,
    this.uiStatus,
    this.productType,
    this.entryProductType,
    this.creditDecisionResult,
    this.creditDecisionResults,
    this.inputData,
    this.onboardingFlowControls,
    this.documents,
    this.productApplications,
    this.resubmittedApplicationDetails,
    this.secureLoanEligibilityDetails,
    this.createdAt,
  });

  factory Application.fromJson(Map<String, dynamic> json) =>
      _$ApplicationFromJson(json);

  static const toJsonFactory = _$ApplicationToJson;
  Map<String, dynamic> toJson() => _$ApplicationToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String? id;
  @JsonKey(name: 'businessId', includeIfNull: false)
  final String? businessId;
  @JsonKey(name: 'externalReferenceId', includeIfNull: false)
  final String? externalReferenceId;
  @JsonKey(name: 'companyName', includeIfNull: false)
  final String? companyName;
  @JsonKey(name: 'applicantId', includeIfNull: false)
  final String? applicantId;
  @JsonKey(name: 'referralCode', includeIfNull: false)
  final String? referralCode;
  @JsonKey(
    name: 'status',
    includeIfNull: false,
    toJson: applicationStatusNullableToJson,
    fromJson: applicationStatusNullableFromJson,
  )
  final enums.ApplicationStatus? status;
  @JsonKey(
    name: 'uiStatus',
    includeIfNull: false,
    toJson: applicationUiStatusNullableToJson,
    fromJson: applicationUiStatusNullableFromJson,
  )
  final enums.ApplicationUiStatus? uiStatus;
  @JsonKey(
    name: 'productType',
    includeIfNull: false,
    toJson: productTypeNullableToJson,
    fromJson: productTypeNullableFromJson,
  )
  final enums.ProductType? productType;
  @JsonKey(
    name: 'entryProductType',
    includeIfNull: false,
    toJson: productTypeNullableToJson,
    fromJson: productTypeNullableFromJson,
  )
  final enums.ProductType? entryProductType;
  @JsonKey(name: 'creditDecisionResult', includeIfNull: false)
  final CreditDecisionResult? creditDecisionResult;
  @JsonKey(name: 'creditDecisionResults', includeIfNull: false)
  final CreditDecisionResults? creditDecisionResults;
  @JsonKey(name: 'inputData', includeIfNull: false)
  final ApplicationInputDataRequest? inputData;
  @JsonKey(name: 'onboardingFlowControls', includeIfNull: false)
  final ApplicationOnboardingFlowControls? onboardingFlowControls;
  @JsonKey(
      name: 'documents',
      includeIfNull: false,
      defaultValue: <DocumentMetadataResponse>[])
  final List<DocumentMetadataResponse>? documents;
  @JsonKey(
      name: 'productApplications',
      includeIfNull: false,
      defaultValue: <ProductApplication>[])
  final List<ProductApplication>? productApplications;
  @JsonKey(name: 'resubmittedApplicationDetails', includeIfNull: false)
  final ResubmittedApplicationDetails? resubmittedApplicationDetails;
  @JsonKey(name: 'secureLoanEligibilityDetails', includeIfNull: false)
  final SecureLoanEligibilityDetails? secureLoanEligibilityDetails;
  @JsonKey(name: 'createdAt', includeIfNull: false)
  final DateTime? createdAt;
  static const fromJsonFactory = _$ApplicationFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ApplicationExtension on Application {
  Application copyWith(
      {String? id,
      String? businessId,
      String? externalReferenceId,
      String? companyName,
      String? applicantId,
      String? referralCode,
      enums.ApplicationStatus? status,
      enums.ApplicationUiStatus? uiStatus,
      enums.ProductType? productType,
      enums.ProductType? entryProductType,
      CreditDecisionResult? creditDecisionResult,
      CreditDecisionResults? creditDecisionResults,
      ApplicationInputDataRequest? inputData,
      ApplicationOnboardingFlowControls? onboardingFlowControls,
      List<DocumentMetadataResponse>? documents,
      List<ProductApplication>? productApplications,
      ResubmittedApplicationDetails? resubmittedApplicationDetails,
      SecureLoanEligibilityDetails? secureLoanEligibilityDetails,
      DateTime? createdAt}) {
    return Application(
        id: id ?? this.id,
        businessId: businessId ?? this.businessId,
        externalReferenceId: externalReferenceId ?? this.externalReferenceId,
        companyName: companyName ?? this.companyName,
        applicantId: applicantId ?? this.applicantId,
        referralCode: referralCode ?? this.referralCode,
        status: status ?? this.status,
        uiStatus: uiStatus ?? this.uiStatus,
        productType: productType ?? this.productType,
        entryProductType: entryProductType ?? this.entryProductType,
        creditDecisionResult: creditDecisionResult ?? this.creditDecisionResult,
        creditDecisionResults:
            creditDecisionResults ?? this.creditDecisionResults,
        inputData: inputData ?? this.inputData,
        onboardingFlowControls:
            onboardingFlowControls ?? this.onboardingFlowControls,
        documents: documents ?? this.documents,
        productApplications: productApplications ?? this.productApplications,
        resubmittedApplicationDetails:
            resubmittedApplicationDetails ?? this.resubmittedApplicationDetails,
        secureLoanEligibilityDetails:
            secureLoanEligibilityDetails ?? this.secureLoanEligibilityDetails,
        createdAt: createdAt ?? this.createdAt);
  }

  Application copyWithWrapped(
      {Wrapped<String?>? id,
      Wrapped<String?>? businessId,
      Wrapped<String?>? externalReferenceId,
      Wrapped<String?>? companyName,
      Wrapped<String?>? applicantId,
      Wrapped<String?>? referralCode,
      Wrapped<enums.ApplicationStatus?>? status,
      Wrapped<enums.ApplicationUiStatus?>? uiStatus,
      Wrapped<enums.ProductType?>? productType,
      Wrapped<enums.ProductType?>? entryProductType,
      Wrapped<CreditDecisionResult?>? creditDecisionResult,
      Wrapped<CreditDecisionResults?>? creditDecisionResults,
      Wrapped<ApplicationInputDataRequest?>? inputData,
      Wrapped<ApplicationOnboardingFlowControls?>? onboardingFlowControls,
      Wrapped<List<DocumentMetadataResponse>?>? documents,
      Wrapped<List<ProductApplication>?>? productApplications,
      Wrapped<ResubmittedApplicationDetails?>? resubmittedApplicationDetails,
      Wrapped<SecureLoanEligibilityDetails?>? secureLoanEligibilityDetails,
      Wrapped<DateTime?>? createdAt}) {
    return Application(
        id: (id != null ? id.value : this.id),
        businessId: (businessId != null ? businessId.value : this.businessId),
        externalReferenceId: (externalReferenceId != null
            ? externalReferenceId.value
            : this.externalReferenceId),
        companyName:
            (companyName != null ? companyName.value : this.companyName),
        applicantId:
            (applicantId != null ? applicantId.value : this.applicantId),
        referralCode:
            (referralCode != null ? referralCode.value : this.referralCode),
        status: (status != null ? status.value : this.status),
        uiStatus: (uiStatus != null ? uiStatus.value : this.uiStatus),
        productType:
            (productType != null ? productType.value : this.productType),
        entryProductType: (entryProductType != null
            ? entryProductType.value
            : this.entryProductType),
        creditDecisionResult: (creditDecisionResult != null
            ? creditDecisionResult.value
            : this.creditDecisionResult),
        creditDecisionResults: (creditDecisionResults != null
            ? creditDecisionResults.value
            : this.creditDecisionResults),
        inputData: (inputData != null ? inputData.value : this.inputData),
        onboardingFlowControls: (onboardingFlowControls != null
            ? onboardingFlowControls.value
            : this.onboardingFlowControls),
        documents: (documents != null ? documents.value : this.documents),
        productApplications: (productApplications != null
            ? productApplications.value
            : this.productApplications),
        resubmittedApplicationDetails: (resubmittedApplicationDetails != null
            ? resubmittedApplicationDetails.value
            : this.resubmittedApplicationDetails),
        secureLoanEligibilityDetails: (secureLoanEligibilityDetails != null
            ? secureLoanEligibilityDetails.value
            : this.secureLoanEligibilityDetails),
        createdAt: (createdAt != null ? createdAt.value : this.createdAt));
  }
}

@JsonSerializable(explicitToJson: true)
class ApplicationInputDataRequest {
  const ApplicationInputDataRequest({
    this.requestedMoney,
    this.annualTurnover,
    this.selectedAmount,
    this.vatReportingMethod,
    this.ibans,
    this.wioIbans,
    this.monthlyPaymentPercentage,
    this.isAutopayFromSavingSpace,
    this.monthlyRepaymentDay,
    this.uiStatus,
    this.nonRegisteredVatReasonType,
    this.nonRegisteredVatReasonText,
    this.referralCode,
    this.securedLoan,
    this.serviceChannels,
    this.employeeCountRange,
    this.businessModelDescription,
    this.missingProofOfAddressReason,
    this.missingFinancialStatementReason,
    this.missingVatReason,
    this.addresses,
    this.productPreferences,
  });

  factory ApplicationInputDataRequest.fromJson(Map<String, dynamic> json) =>
      _$ApplicationInputDataRequestFromJson(json);

  static const toJsonFactory = _$ApplicationInputDataRequestToJson;
  Map<String, dynamic> toJson() => _$ApplicationInputDataRequestToJson(this);

  @JsonKey(name: 'requestedMoney', includeIfNull: false)
  final Money? requestedMoney;
  @JsonKey(name: 'annualTurnover', includeIfNull: false)
  final Money? annualTurnover;
  @JsonKey(name: 'selectedAmount', includeIfNull: false)
  final Money? selectedAmount;
  @JsonKey(
    name: 'vatReportingMethod',
    includeIfNull: false,
    toJson: applicationInputDataRequestVatReportingMethodNullableToJson,
    fromJson: applicationInputDataRequestVatReportingMethodNullableFromJson,
  )
  final enums.ApplicationInputDataRequestVatReportingMethod? vatReportingMethod;
  @JsonKey(name: 'ibans', includeIfNull: false, defaultValue: <Iban>[])
  final List<Iban>? ibans;
  @JsonKey(name: 'wioIbans', includeIfNull: false, defaultValue: <Iban>[])
  final List<Iban>? wioIbans;
  @JsonKey(name: 'monthlyPaymentPercentage', includeIfNull: false)
  final int? monthlyPaymentPercentage;
  @JsonKey(name: 'isAutopayFromSavingSpace', includeIfNull: false)
  final bool? isAutopayFromSavingSpace;
  @JsonKey(name: 'monthlyRepaymentDay', includeIfNull: false)
  final int? monthlyRepaymentDay;
  @JsonKey(
    name: 'uiStatus',
    includeIfNull: false,
    toJson: applicationUiStatusNullableToJson,
    fromJson: applicationUiStatusNullableFromJson,
  )
  final enums.ApplicationUiStatus? uiStatus;
  @JsonKey(
    name: 'nonRegisteredVatReasonType',
    includeIfNull: false,
    toJson: applicationInputDataRequestNonRegisteredVatReasonTypeNullableToJson,
    fromJson:
        applicationInputDataRequestNonRegisteredVatReasonTypeNullableFromJson,
  )
  final enums.ApplicationInputDataRequestNonRegisteredVatReasonType?
      nonRegisteredVatReasonType;
  @JsonKey(name: 'nonRegisteredVatReasonText', includeIfNull: false)
  final String? nonRegisteredVatReasonText;
  @JsonKey(name: 'referralCode', includeIfNull: false)
  final String? referralCode;
  @JsonKey(name: 'securedLoan', includeIfNull: false)
  final bool? securedLoan;
  @JsonKey(
      name: 'serviceChannels', includeIfNull: false, defaultValue: <String>[])
  final List<String>? serviceChannels;
  @JsonKey(name: 'employeeCountRange', includeIfNull: false)
  final String? employeeCountRange;
  @JsonKey(name: 'businessModelDescription', includeIfNull: false)
  final String? businessModelDescription;
  @JsonKey(name: 'missingProofOfAddressReason', includeIfNull: false)
  final String? missingProofOfAddressReason;
  @JsonKey(name: 'missingFinancialStatementReason', includeIfNull: false)
  final String? missingFinancialStatementReason;
  @JsonKey(name: 'missingVatReason', includeIfNull: false)
  final String? missingVatReason;
  @JsonKey(name: 'addresses', includeIfNull: false, defaultValue: <Address>[])
  final List<Address>? addresses;
  @JsonKey(
      name: 'productPreferences',
      includeIfNull: false,
      defaultValue: <ProductPreferences>[])
  final List<ProductPreferences>? productPreferences;
  static const fromJsonFactory = _$ApplicationInputDataRequestFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ApplicationInputDataRequestExtension on ApplicationInputDataRequest {
  ApplicationInputDataRequest copyWith(
      {Money? requestedMoney,
      Money? annualTurnover,
      Money? selectedAmount,
      enums.ApplicationInputDataRequestVatReportingMethod? vatReportingMethod,
      List<Iban>? ibans,
      List<Iban>? wioIbans,
      int? monthlyPaymentPercentage,
      bool? isAutopayFromSavingSpace,
      int? monthlyRepaymentDay,
      enums.ApplicationUiStatus? uiStatus,
      enums.ApplicationInputDataRequestNonRegisteredVatReasonType?
          nonRegisteredVatReasonType,
      String? nonRegisteredVatReasonText,
      String? referralCode,
      bool? securedLoan,
      List<String>? serviceChannels,
      String? employeeCountRange,
      String? businessModelDescription,
      String? missingProofOfAddressReason,
      String? missingFinancialStatementReason,
      String? missingVatReason,
      List<Address>? addresses,
      List<ProductPreferences>? productPreferences}) {
    return ApplicationInputDataRequest(
        requestedMoney: requestedMoney ?? this.requestedMoney,
        annualTurnover: annualTurnover ?? this.annualTurnover,
        selectedAmount: selectedAmount ?? this.selectedAmount,
        vatReportingMethod: vatReportingMethod ?? this.vatReportingMethod,
        ibans: ibans ?? this.ibans,
        wioIbans: wioIbans ?? this.wioIbans,
        monthlyPaymentPercentage:
            monthlyPaymentPercentage ?? this.monthlyPaymentPercentage,
        isAutopayFromSavingSpace:
            isAutopayFromSavingSpace ?? this.isAutopayFromSavingSpace,
        monthlyRepaymentDay: monthlyRepaymentDay ?? this.monthlyRepaymentDay,
        uiStatus: uiStatus ?? this.uiStatus,
        nonRegisteredVatReasonType:
            nonRegisteredVatReasonType ?? this.nonRegisteredVatReasonType,
        nonRegisteredVatReasonText:
            nonRegisteredVatReasonText ?? this.nonRegisteredVatReasonText,
        referralCode: referralCode ?? this.referralCode,
        securedLoan: securedLoan ?? this.securedLoan,
        serviceChannels: serviceChannels ?? this.serviceChannels,
        employeeCountRange: employeeCountRange ?? this.employeeCountRange,
        businessModelDescription:
            businessModelDescription ?? this.businessModelDescription,
        missingProofOfAddressReason:
            missingProofOfAddressReason ?? this.missingProofOfAddressReason,
        missingFinancialStatementReason: missingFinancialStatementReason ??
            this.missingFinancialStatementReason,
        missingVatReason: missingVatReason ?? this.missingVatReason,
        addresses: addresses ?? this.addresses,
        productPreferences: productPreferences ?? this.productPreferences);
  }

  ApplicationInputDataRequest copyWithWrapped(
      {Wrapped<Money?>? requestedMoney,
      Wrapped<Money?>? annualTurnover,
      Wrapped<Money?>? selectedAmount,
      Wrapped<enums.ApplicationInputDataRequestVatReportingMethod?>?
          vatReportingMethod,
      Wrapped<List<Iban>?>? ibans,
      Wrapped<List<Iban>?>? wioIbans,
      Wrapped<int?>? monthlyPaymentPercentage,
      Wrapped<bool?>? isAutopayFromSavingSpace,
      Wrapped<int?>? monthlyRepaymentDay,
      Wrapped<enums.ApplicationUiStatus?>? uiStatus,
      Wrapped<enums.ApplicationInputDataRequestNonRegisteredVatReasonType?>?
          nonRegisteredVatReasonType,
      Wrapped<String?>? nonRegisteredVatReasonText,
      Wrapped<String?>? referralCode,
      Wrapped<bool?>? securedLoan,
      Wrapped<List<String>?>? serviceChannels,
      Wrapped<String?>? employeeCountRange,
      Wrapped<String?>? businessModelDescription,
      Wrapped<String?>? missingProofOfAddressReason,
      Wrapped<String?>? missingFinancialStatementReason,
      Wrapped<String?>? missingVatReason,
      Wrapped<List<Address>?>? addresses,
      Wrapped<List<ProductPreferences>?>? productPreferences}) {
    return ApplicationInputDataRequest(
        requestedMoney: (requestedMoney != null
            ? requestedMoney.value
            : this.requestedMoney),
        annualTurnover: (annualTurnover != null
            ? annualTurnover.value
            : this.annualTurnover),
        selectedAmount: (selectedAmount != null
            ? selectedAmount.value
            : this.selectedAmount),
        vatReportingMethod: (vatReportingMethod != null
            ? vatReportingMethod.value
            : this.vatReportingMethod),
        ibans: (ibans != null ? ibans.value : this.ibans),
        wioIbans: (wioIbans != null ? wioIbans.value : this.wioIbans),
        monthlyPaymentPercentage: (monthlyPaymentPercentage != null
            ? monthlyPaymentPercentage.value
            : this.monthlyPaymentPercentage),
        isAutopayFromSavingSpace: (isAutopayFromSavingSpace != null
            ? isAutopayFromSavingSpace.value
            : this.isAutopayFromSavingSpace),
        monthlyRepaymentDay: (monthlyRepaymentDay != null
            ? monthlyRepaymentDay.value
            : this.monthlyRepaymentDay),
        uiStatus: (uiStatus != null ? uiStatus.value : this.uiStatus),
        nonRegisteredVatReasonType: (nonRegisteredVatReasonType != null
            ? nonRegisteredVatReasonType.value
            : this.nonRegisteredVatReasonType),
        nonRegisteredVatReasonText: (nonRegisteredVatReasonText != null
            ? nonRegisteredVatReasonText.value
            : this.nonRegisteredVatReasonText),
        referralCode:
            (referralCode != null ? referralCode.value : this.referralCode),
        securedLoan:
            (securedLoan != null ? securedLoan.value : this.securedLoan),
        serviceChannels: (serviceChannels != null
            ? serviceChannels.value
            : this.serviceChannels),
        employeeCountRange: (employeeCountRange != null
            ? employeeCountRange.value
            : this.employeeCountRange),
        businessModelDescription: (businessModelDescription != null
            ? businessModelDescription.value
            : this.businessModelDescription),
        missingProofOfAddressReason: (missingProofOfAddressReason != null
            ? missingProofOfAddressReason.value
            : this.missingProofOfAddressReason),
        missingFinancialStatementReason:
            (missingFinancialStatementReason != null
                ? missingFinancialStatementReason.value
                : this.missingFinancialStatementReason),
        missingVatReason: (missingVatReason != null
            ? missingVatReason.value
            : this.missingVatReason),
        addresses: (addresses != null ? addresses.value : this.addresses),
        productPreferences: (productPreferences != null
            ? productPreferences.value
            : this.productPreferences));
  }
}

@JsonSerializable(explicitToJson: true)
class ApplicationOnboardingFlowControls {
  const ApplicationOnboardingFlowControls({
    this.shouldUploadFinancialDocument,
    this.shouldUploadBorrowingPowerVerificationDocument,
    this.shouldCollectMonthlyPaymentDate,
    this.shouldCollectRequestedAmount,
  });

  factory ApplicationOnboardingFlowControls.fromJson(
          Map<String, dynamic> json) =>
      _$ApplicationOnboardingFlowControlsFromJson(json);

  static const toJsonFactory = _$ApplicationOnboardingFlowControlsToJson;
  Map<String, dynamic> toJson() =>
      _$ApplicationOnboardingFlowControlsToJson(this);

  @JsonKey(name: 'shouldUploadFinancialDocument', includeIfNull: false)
  final bool? shouldUploadFinancialDocument;
  @JsonKey(
      name: 'shouldUploadBorrowingPowerVerificationDocument',
      includeIfNull: false)
  final bool? shouldUploadBorrowingPowerVerificationDocument;
  @JsonKey(name: 'shouldCollectMonthlyPaymentDate', includeIfNull: false)
  final bool? shouldCollectMonthlyPaymentDate;
  @JsonKey(name: 'shouldCollectRequestedAmount', includeIfNull: false)
  final bool? shouldCollectRequestedAmount;
  static const fromJsonFactory = _$ApplicationOnboardingFlowControlsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ApplicationOnboardingFlowControlsExtension
    on ApplicationOnboardingFlowControls {
  ApplicationOnboardingFlowControls copyWith(
      {bool? shouldUploadFinancialDocument,
      bool? shouldUploadBorrowingPowerVerificationDocument,
      bool? shouldCollectMonthlyPaymentDate,
      bool? shouldCollectRequestedAmount}) {
    return ApplicationOnboardingFlowControls(
        shouldUploadFinancialDocument:
            shouldUploadFinancialDocument ?? this.shouldUploadFinancialDocument,
        shouldUploadBorrowingPowerVerificationDocument:
            shouldUploadBorrowingPowerVerificationDocument ??
                this.shouldUploadBorrowingPowerVerificationDocument,
        shouldCollectMonthlyPaymentDate: shouldCollectMonthlyPaymentDate ??
            this.shouldCollectMonthlyPaymentDate,
        shouldCollectRequestedAmount:
            shouldCollectRequestedAmount ?? this.shouldCollectRequestedAmount);
  }

  ApplicationOnboardingFlowControls copyWithWrapped(
      {Wrapped<bool?>? shouldUploadFinancialDocument,
      Wrapped<bool?>? shouldUploadBorrowingPowerVerificationDocument,
      Wrapped<bool?>? shouldCollectMonthlyPaymentDate,
      Wrapped<bool?>? shouldCollectRequestedAmount}) {
    return ApplicationOnboardingFlowControls(
        shouldUploadFinancialDocument: (shouldUploadFinancialDocument != null
            ? shouldUploadFinancialDocument.value
            : this.shouldUploadFinancialDocument),
        shouldUploadBorrowingPowerVerificationDocument:
            (shouldUploadBorrowingPowerVerificationDocument != null
                ? shouldUploadBorrowingPowerVerificationDocument.value
                : this.shouldUploadBorrowingPowerVerificationDocument),
        shouldCollectMonthlyPaymentDate:
            (shouldCollectMonthlyPaymentDate != null
                ? shouldCollectMonthlyPaymentDate.value
                : this.shouldCollectMonthlyPaymentDate),
        shouldCollectRequestedAmount: (shouldCollectRequestedAmount != null
            ? shouldCollectRequestedAmount.value
            : this.shouldCollectRequestedAmount));
  }
}

@JsonSerializable(explicitToJson: true)
class CreditDecisionResult {
  const CreditDecisionResult({
    this.creditDecisionStatus,
    this.approvedAmount,
    this.interestRate,
    this.annualFee,
    this.cashWithdrawalAllowance,
  });

  factory CreditDecisionResult.fromJson(Map<String, dynamic> json) =>
      _$CreditDecisionResultFromJson(json);

  static const toJsonFactory = _$CreditDecisionResultToJson;
  Map<String, dynamic> toJson() => _$CreditDecisionResultToJson(this);

  @JsonKey(
    name: 'creditDecisionStatus',
    includeIfNull: false,
    toJson: creditDecisionResultCreditDecisionStatusNullableToJson,
    fromJson: creditDecisionResultCreditDecisionStatusNullableFromJson,
  )
  final enums.CreditDecisionResultCreditDecisionStatus? creditDecisionStatus;
  @JsonKey(name: 'approvedAmount', includeIfNull: false)
  final double? approvedAmount;
  @JsonKey(name: 'interestRate', includeIfNull: false)
  final double? interestRate;
  @JsonKey(name: 'annualFee', includeIfNull: false)
  final double? annualFee;
  @JsonKey(name: 'cashWithdrawalAllowance', includeIfNull: false)
  final double? cashWithdrawalAllowance;
  static const fromJsonFactory = _$CreditDecisionResultFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CreditDecisionResultExtension on CreditDecisionResult {
  CreditDecisionResult copyWith(
      {enums.CreditDecisionResultCreditDecisionStatus? creditDecisionStatus,
      double? approvedAmount,
      double? interestRate,
      double? annualFee,
      double? cashWithdrawalAllowance}) {
    return CreditDecisionResult(
        creditDecisionStatus: creditDecisionStatus ?? this.creditDecisionStatus,
        approvedAmount: approvedAmount ?? this.approvedAmount,
        interestRate: interestRate ?? this.interestRate,
        annualFee: annualFee ?? this.annualFee,
        cashWithdrawalAllowance:
            cashWithdrawalAllowance ?? this.cashWithdrawalAllowance);
  }

  CreditDecisionResult copyWithWrapped(
      {Wrapped<enums.CreditDecisionResultCreditDecisionStatus?>?
          creditDecisionStatus,
      Wrapped<double?>? approvedAmount,
      Wrapped<double?>? interestRate,
      Wrapped<double?>? annualFee,
      Wrapped<double?>? cashWithdrawalAllowance}) {
    return CreditDecisionResult(
        creditDecisionStatus: (creditDecisionStatus != null
            ? creditDecisionStatus.value
            : this.creditDecisionStatus),
        approvedAmount: (approvedAmount != null
            ? approvedAmount.value
            : this.approvedAmount),
        interestRate:
            (interestRate != null ? interestRate.value : this.interestRate),
        annualFee: (annualFee != null ? annualFee.value : this.annualFee),
        cashWithdrawalAllowance: (cashWithdrawalAllowance != null
            ? cashWithdrawalAllowance.value
            : this.cashWithdrawalAllowance));
  }
}

@JsonSerializable(explicitToJson: true)
class CreditDecisionResultV2 {
  const CreditDecisionResultV2({
    this.creditDecisionStatus,
    this.approvedAmount,
    this.availableAmount,
    required this.productType,
    this.creditAcceptanceStatus,
    this.productTerms,
    required this.verificationStatus,
    this.partnerName,
    this.isAutoAcceptOffer,
  });

  factory CreditDecisionResultV2.fromJson(Map<String, dynamic> json) =>
      _$CreditDecisionResultV2FromJson(json);

  static const toJsonFactory = _$CreditDecisionResultV2ToJson;
  Map<String, dynamic> toJson() => _$CreditDecisionResultV2ToJson(this);

  @JsonKey(
    name: 'creditDecisionStatus',
    includeIfNull: false,
    toJson: creditDecisionResultV2CreditDecisionStatusNullableToJson,
    fromJson: creditDecisionResultV2CreditDecisionStatusNullableFromJson,
  )
  final enums.CreditDecisionResultV2CreditDecisionStatus? creditDecisionStatus;
  @JsonKey(name: 'approvedAmount', includeIfNull: false)
  final double? approvedAmount;
  @JsonKey(name: 'availableAmount', includeIfNull: false)
  final double? availableAmount;
  @JsonKey(
    name: 'productType',
    includeIfNull: false,
    toJson: productTypeToJson,
    fromJson: productTypeFromJson,
  )
  final enums.ProductType productType;
  @JsonKey(
    name: 'creditAcceptanceStatus',
    includeIfNull: false,
    toJson: creditDecisionResultV2CreditAcceptanceStatusNullableToJson,
    fromJson: creditDecisionResultV2CreditAcceptanceStatusNullableFromJson,
  )
  final enums.CreditDecisionResultV2CreditAcceptanceStatus?
      creditAcceptanceStatus;
  @JsonKey(name: 'productTerms', includeIfNull: false)
  final ProductTerms? productTerms;
  @JsonKey(
    name: 'verificationStatus',
    includeIfNull: false,
    toJson: creditDecisionResultV2VerificationStatusToJson,
    fromJson: creditDecisionResultV2VerificationStatusFromJson,
  )
  final enums.CreditDecisionResultV2VerificationStatus verificationStatus;
  @JsonKey(name: 'partnerName', includeIfNull: false)
  final String? partnerName;
  @JsonKey(name: 'isAutoAcceptOffer', includeIfNull: false)
  final bool? isAutoAcceptOffer;
  static const fromJsonFactory = _$CreditDecisionResultV2FromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CreditDecisionResultV2Extension on CreditDecisionResultV2 {
  CreditDecisionResultV2 copyWith(
      {enums.CreditDecisionResultV2CreditDecisionStatus? creditDecisionStatus,
      double? approvedAmount,
      double? availableAmount,
      enums.ProductType? productType,
      enums.CreditDecisionResultV2CreditAcceptanceStatus?
          creditAcceptanceStatus,
      ProductTerms? productTerms,
      enums.CreditDecisionResultV2VerificationStatus? verificationStatus,
      String? partnerName,
      bool? isAutoAcceptOffer}) {
    return CreditDecisionResultV2(
        creditDecisionStatus: creditDecisionStatus ?? this.creditDecisionStatus,
        approvedAmount: approvedAmount ?? this.approvedAmount,
        availableAmount: availableAmount ?? this.availableAmount,
        productType: productType ?? this.productType,
        creditAcceptanceStatus:
            creditAcceptanceStatus ?? this.creditAcceptanceStatus,
        productTerms: productTerms ?? this.productTerms,
        verificationStatus: verificationStatus ?? this.verificationStatus,
        partnerName: partnerName ?? this.partnerName,
        isAutoAcceptOffer: isAutoAcceptOffer ?? this.isAutoAcceptOffer);
  }

  CreditDecisionResultV2 copyWithWrapped(
      {Wrapped<enums.CreditDecisionResultV2CreditDecisionStatus?>?
          creditDecisionStatus,
      Wrapped<double?>? approvedAmount,
      Wrapped<double?>? availableAmount,
      Wrapped<enums.ProductType>? productType,
      Wrapped<enums.CreditDecisionResultV2CreditAcceptanceStatus?>?
          creditAcceptanceStatus,
      Wrapped<ProductTerms?>? productTerms,
      Wrapped<enums.CreditDecisionResultV2VerificationStatus>?
          verificationStatus,
      Wrapped<String?>? partnerName,
      Wrapped<bool?>? isAutoAcceptOffer}) {
    return CreditDecisionResultV2(
        creditDecisionStatus: (creditDecisionStatus != null
            ? creditDecisionStatus.value
            : this.creditDecisionStatus),
        approvedAmount: (approvedAmount != null
            ? approvedAmount.value
            : this.approvedAmount),
        availableAmount: (availableAmount != null
            ? availableAmount.value
            : this.availableAmount),
        productType:
            (productType != null ? productType.value : this.productType),
        creditAcceptanceStatus: (creditAcceptanceStatus != null
            ? creditAcceptanceStatus.value
            : this.creditAcceptanceStatus),
        productTerms:
            (productTerms != null ? productTerms.value : this.productTerms),
        verificationStatus: (verificationStatus != null
            ? verificationStatus.value
            : this.verificationStatus),
        partnerName:
            (partnerName != null ? partnerName.value : this.partnerName),
        isAutoAcceptOffer: (isAutoAcceptOffer != null
            ? isAutoAcceptOffer.value
            : this.isAutoAcceptOffer));
  }
}

@JsonSerializable(explicitToJson: true)
class CreditDecisionResults {
  const CreditDecisionResults({
    this.creditDecisionStatus,
    this.results,
  });

  factory CreditDecisionResults.fromJson(Map<String, dynamic> json) =>
      _$CreditDecisionResultsFromJson(json);

  static const toJsonFactory = _$CreditDecisionResultsToJson;
  Map<String, dynamic> toJson() => _$CreditDecisionResultsToJson(this);

  @JsonKey(
    name: 'creditDecisionStatus',
    includeIfNull: false,
    toJson: creditDecisionResultsCreditDecisionStatusNullableToJson,
    fromJson: creditDecisionResultsCreditDecisionStatusNullableFromJson,
  )
  final enums.CreditDecisionResultsCreditDecisionStatus? creditDecisionStatus;
  @JsonKey(
      name: 'results',
      includeIfNull: false,
      defaultValue: <CreditDecisionResultV2>[])
  final List<CreditDecisionResultV2>? results;
  static const fromJsonFactory = _$CreditDecisionResultsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CreditDecisionResultsExtension on CreditDecisionResults {
  CreditDecisionResults copyWith(
      {enums.CreditDecisionResultsCreditDecisionStatus? creditDecisionStatus,
      List<CreditDecisionResultV2>? results}) {
    return CreditDecisionResults(
        creditDecisionStatus: creditDecisionStatus ?? this.creditDecisionStatus,
        results: results ?? this.results);
  }

  CreditDecisionResults copyWithWrapped(
      {Wrapped<enums.CreditDecisionResultsCreditDecisionStatus?>?
          creditDecisionStatus,
      Wrapped<List<CreditDecisionResultV2>?>? results}) {
    return CreditDecisionResults(
        creditDecisionStatus: (creditDecisionStatus != null
            ? creditDecisionStatus.value
            : this.creditDecisionStatus),
        results: (results != null ? results.value : this.results));
  }
}

@JsonSerializable(explicitToJson: true)
class DocumentMetadataResponse {
  const DocumentMetadataResponse({
    required this.id,
    required this.filename,
    required this.documentType,
    required this.contentType,
    this.url,
    this.uploadedAt,
    this.urls,
  });

  factory DocumentMetadataResponse.fromJson(Map<String, dynamic> json) =>
      _$DocumentMetadataResponseFromJson(json);

  static const toJsonFactory = _$DocumentMetadataResponseToJson;
  Map<String, dynamic> toJson() => _$DocumentMetadataResponseToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'filename', includeIfNull: false)
  final String filename;
  @JsonKey(
    name: 'documentType',
    includeIfNull: false,
    toJson: documentTypeToJson,
    fromJson: documentTypeFromJson,
  )
  final enums.DocumentType documentType;
  @JsonKey(name: 'contentType', includeIfNull: false)
  final String contentType;
  @JsonKey(name: 'url', includeIfNull: false)
  final String? url;
  @JsonKey(name: 'uploadedAt', includeIfNull: false)
  final DateTime? uploadedAt;
  @JsonKey(name: 'urls', includeIfNull: false)
  final LocalizedDocumentLink? urls;
  static const fromJsonFactory = _$DocumentMetadataResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $DocumentMetadataResponseExtension on DocumentMetadataResponse {
  DocumentMetadataResponse copyWith(
      {String? id,
      String? filename,
      enums.DocumentType? documentType,
      String? contentType,
      String? url,
      DateTime? uploadedAt,
      LocalizedDocumentLink? urls}) {
    return DocumentMetadataResponse(
        id: id ?? this.id,
        filename: filename ?? this.filename,
        documentType: documentType ?? this.documentType,
        contentType: contentType ?? this.contentType,
        url: url ?? this.url,
        uploadedAt: uploadedAt ?? this.uploadedAt,
        urls: urls ?? this.urls);
  }

  DocumentMetadataResponse copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? filename,
      Wrapped<enums.DocumentType>? documentType,
      Wrapped<String>? contentType,
      Wrapped<String?>? url,
      Wrapped<DateTime?>? uploadedAt,
      Wrapped<LocalizedDocumentLink?>? urls}) {
    return DocumentMetadataResponse(
        id: (id != null ? id.value : this.id),
        filename: (filename != null ? filename.value : this.filename),
        documentType:
            (documentType != null ? documentType.value : this.documentType),
        contentType:
            (contentType != null ? contentType.value : this.contentType),
        url: (url != null ? url.value : this.url),
        uploadedAt: (uploadedAt != null ? uploadedAt.value : this.uploadedAt),
        urls: (urls != null ? urls.value : this.urls));
  }
}

@JsonSerializable(explicitToJson: true)
class FixedDepositInfo {
  const FixedDepositInfo({
    required this.money,
    required this.loanProductCode,
    required this.depositAccountId,
  });

  factory FixedDepositInfo.fromJson(Map<String, dynamic> json) =>
      _$FixedDepositInfoFromJson(json);

  static const toJsonFactory = _$FixedDepositInfoToJson;
  Map<String, dynamic> toJson() => _$FixedDepositInfoToJson(this);

  @JsonKey(name: 'money', includeIfNull: false)
  final Money money;
  @JsonKey(
    name: 'loanProductCode',
    includeIfNull: false,
    toJson: fixedDepositInfoLoanProductCodeToJson,
    fromJson: fixedDepositInfoLoanProductCodeFromJson,
  )
  final enums.FixedDepositInfoLoanProductCode loanProductCode;
  @JsonKey(name: 'depositAccountId', includeIfNull: false)
  final String depositAccountId;
  static const fromJsonFactory = _$FixedDepositInfoFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $FixedDepositInfoExtension on FixedDepositInfo {
  FixedDepositInfo copyWith(
      {Money? money,
      enums.FixedDepositInfoLoanProductCode? loanProductCode,
      String? depositAccountId}) {
    return FixedDepositInfo(
        money: money ?? this.money,
        loanProductCode: loanProductCode ?? this.loanProductCode,
        depositAccountId: depositAccountId ?? this.depositAccountId);
  }

  FixedDepositInfo copyWithWrapped(
      {Wrapped<Money>? money,
      Wrapped<enums.FixedDepositInfoLoanProductCode>? loanProductCode,
      Wrapped<String>? depositAccountId}) {
    return FixedDepositInfo(
        money: (money != null ? money.value : this.money),
        loanProductCode: (loanProductCode != null
            ? loanProductCode.value
            : this.loanProductCode),
        depositAccountId: (depositAccountId != null
            ? depositAccountId.value
            : this.depositAccountId));
  }
}

@JsonSerializable(explicitToJson: true)
class Iban {
  const Iban({
    this.iban,
    this.name,
  });

  factory Iban.fromJson(Map<String, dynamic> json) => _$IbanFromJson(json);

  static const toJsonFactory = _$IbanToJson;
  Map<String, dynamic> toJson() => _$IbanToJson(this);

  @JsonKey(name: 'iban', includeIfNull: false)
  final String? iban;
  @JsonKey(name: 'name', includeIfNull: false)
  final String? name;
  static const fromJsonFactory = _$IbanFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $IbanExtension on Iban {
  Iban copyWith({String? iban, String? name}) {
    return Iban(iban: iban ?? this.iban, name: name ?? this.name);
  }

  Iban copyWithWrapped({Wrapped<String?>? iban, Wrapped<String?>? name}) {
    return Iban(
        iban: (iban != null ? iban.value : this.iban),
        name: (name != null ? name.value : this.name));
  }
}

@JsonSerializable(explicitToJson: true)
class InstallmentFeeParameters {
  const InstallmentFeeParameters({
    this.minimumFee,
    this.fixedFee,
    this.principalPercentage,
  });

  factory InstallmentFeeParameters.fromJson(Map<String, dynamic> json) =>
      _$InstallmentFeeParametersFromJson(json);

  static const toJsonFactory = _$InstallmentFeeParametersToJson;
  Map<String, dynamic> toJson() => _$InstallmentFeeParametersToJson(this);

  @JsonKey(name: 'minimumFee', includeIfNull: false)
  final double? minimumFee;
  @JsonKey(name: 'fixedFee', includeIfNull: false)
  final double? fixedFee;
  @JsonKey(name: 'principalPercentage', includeIfNull: false)
  final double? principalPercentage;
  static const fromJsonFactory = _$InstallmentFeeParametersFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $InstallmentFeeParametersExtension on InstallmentFeeParameters {
  InstallmentFeeParameters copyWith(
      {double? minimumFee, double? fixedFee, double? principalPercentage}) {
    return InstallmentFeeParameters(
        minimumFee: minimumFee ?? this.minimumFee,
        fixedFee: fixedFee ?? this.fixedFee,
        principalPercentage: principalPercentage ?? this.principalPercentage);
  }

  InstallmentFeeParameters copyWithWrapped(
      {Wrapped<double?>? minimumFee,
      Wrapped<double?>? fixedFee,
      Wrapped<double?>? principalPercentage}) {
    return InstallmentFeeParameters(
        minimumFee: (minimumFee != null ? minimumFee.value : this.minimumFee),
        fixedFee: (fixedFee != null ? fixedFee.value : this.fixedFee),
        principalPercentage: (principalPercentage != null
            ? principalPercentage.value
            : this.principalPercentage));
  }
}

@JsonSerializable(explicitToJson: true)
class LoanSecurityProductDetails {
  const LoanSecurityProductDetails({
    this.productCode,
  });

  factory LoanSecurityProductDetails.fromJson(Map<String, dynamic> json) =>
      _$LoanSecurityProductDetailsFromJson(json);

  static const toJsonFactory = _$LoanSecurityProductDetailsToJson;
  Map<String, dynamic> toJson() => _$LoanSecurityProductDetailsToJson(this);

  @JsonKey(
    name: 'productCode',
    includeIfNull: false,
    toJson: loanSecurityProductDetailsProductCodeNullableToJson,
    fromJson: loanSecurityProductDetailsProductCodeNullableFromJson,
  )
  final enums.LoanSecurityProductDetailsProductCode? productCode;
  static const fromJsonFactory = _$LoanSecurityProductDetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $LoanSecurityProductDetailsExtension on LoanSecurityProductDetails {
  LoanSecurityProductDetails copyWith(
      {enums.LoanSecurityProductDetailsProductCode? productCode}) {
    return LoanSecurityProductDetails(
        productCode: productCode ?? this.productCode);
  }

  LoanSecurityProductDetails copyWithWrapped(
      {Wrapped<enums.LoanSecurityProductDetailsProductCode?>? productCode}) {
    return LoanSecurityProductDetails(
        productCode:
            (productCode != null ? productCode.value : this.productCode));
  }
}

@JsonSerializable(explicitToJson: true)
class LocalizedDocumentLink {
  const LocalizedDocumentLink({
    required this.en,
    required this.ar,
  });

  factory LocalizedDocumentLink.fromJson(Map<String, dynamic> json) =>
      _$LocalizedDocumentLinkFromJson(json);

  static const toJsonFactory = _$LocalizedDocumentLinkToJson;
  Map<String, dynamic> toJson() => _$LocalizedDocumentLinkToJson(this);

  @JsonKey(name: 'en', includeIfNull: false)
  final String en;
  @JsonKey(name: 'ar', includeIfNull: false)
  final String ar;
  static const fromJsonFactory = _$LocalizedDocumentLinkFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $LocalizedDocumentLinkExtension on LocalizedDocumentLink {
  LocalizedDocumentLink copyWith({String? en, String? ar}) {
    return LocalizedDocumentLink(en: en ?? this.en, ar: ar ?? this.ar);
  }

  LocalizedDocumentLink copyWithWrapped(
      {Wrapped<String>? en, Wrapped<String>? ar}) {
    return LocalizedDocumentLink(
        en: (en != null ? en.value : this.en),
        ar: (ar != null ? ar.value : this.ar));
  }
}

@JsonSerializable(explicitToJson: true)
class Money {
  const Money({
    this.amount,
    this.currency,
  });

  factory Money.fromJson(Map<String, dynamic> json) => _$MoneyFromJson(json);

  static const toJsonFactory = _$MoneyToJson;
  Map<String, dynamic> toJson() => _$MoneyToJson(this);

  @JsonKey(name: 'amount', includeIfNull: false)
  final double? amount;
  @JsonKey(
    name: 'currency',
    includeIfNull: false,
    toJson: currencyNullableToJson,
    fromJson: currencyNullableFromJson,
  )
  final enums.Currency? currency;
  static const fromJsonFactory = _$MoneyFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MoneyExtension on Money {
  Money copyWith({double? amount, enums.Currency? currency}) {
    return Money(
        amount: amount ?? this.amount, currency: currency ?? this.currency);
  }

  Money copyWithWrapped(
      {Wrapped<double?>? amount, Wrapped<enums.Currency?>? currency}) {
    return Money(
        amount: (amount != null ? amount.value : this.amount),
        currency: (currency != null ? currency.value : this.currency));
  }
}

@JsonSerializable(explicitToJson: true)
class ProductApplication {
  const ProductApplication({
    this.productType,
    this.accountCreationStatus,
    this.accountId,
  });

  factory ProductApplication.fromJson(Map<String, dynamic> json) =>
      _$ProductApplicationFromJson(json);

  static const toJsonFactory = _$ProductApplicationToJson;
  Map<String, dynamic> toJson() => _$ProductApplicationToJson(this);

  @JsonKey(
    name: 'productType',
    includeIfNull: false,
    toJson: productTypeNullableToJson,
    fromJson: productTypeNullableFromJson,
  )
  final enums.ProductType? productType;
  @JsonKey(
    name: 'accountCreationStatus',
    includeIfNull: false,
    toJson: productApplicationAccountCreationStatusNullableToJson,
    fromJson: productApplicationAccountCreationStatusNullableFromJson,
  )
  final enums.ProductApplicationAccountCreationStatus? accountCreationStatus;
  @JsonKey(name: 'accountId', includeIfNull: false)
  final String? accountId;
  static const fromJsonFactory = _$ProductApplicationFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ProductApplicationExtension on ProductApplication {
  ProductApplication copyWith(
      {enums.ProductType? productType,
      enums.ProductApplicationAccountCreationStatus? accountCreationStatus,
      String? accountId}) {
    return ProductApplication(
        productType: productType ?? this.productType,
        accountCreationStatus:
            accountCreationStatus ?? this.accountCreationStatus,
        accountId: accountId ?? this.accountId);
  }

  ProductApplication copyWithWrapped(
      {Wrapped<enums.ProductType?>? productType,
      Wrapped<enums.ProductApplicationAccountCreationStatus?>?
          accountCreationStatus,
      Wrapped<String?>? accountId}) {
    return ProductApplication(
        productType:
            (productType != null ? productType.value : this.productType),
        accountCreationStatus: (accountCreationStatus != null
            ? accountCreationStatus.value
            : this.accountCreationStatus),
        accountId: (accountId != null ? accountId.value : this.accountId));
  }
}

@JsonSerializable(explicitToJson: true)
class ProductPreferences {
  const ProductPreferences({
    this.isAutopayFromSavingSpace,
    this.monthlyRepaymentDay,
    this.monthlyPaymentPercentage,
    this.selectedAmount,
    this.reducedCreditCardLimit,
    this.loanPeriod,
    this.productType,
    this.settlementAccountId,
    this.uiStatus,
  });

  factory ProductPreferences.fromJson(Map<String, dynamic> json) =>
      _$ProductPreferencesFromJson(json);

  static const toJsonFactory = _$ProductPreferencesToJson;
  Map<String, dynamic> toJson() => _$ProductPreferencesToJson(this);

  @JsonKey(name: 'isAutopayFromSavingSpace', includeIfNull: false)
  final bool? isAutopayFromSavingSpace;
  @JsonKey(name: 'monthlyRepaymentDay', includeIfNull: false)
  final int? monthlyRepaymentDay;
  @JsonKey(name: 'monthlyPaymentPercentage', includeIfNull: false)
  final int? monthlyPaymentPercentage;
  @JsonKey(name: 'selectedAmount', includeIfNull: false)
  final Money? selectedAmount;
  @JsonKey(name: 'reducedCreditCardLimit', includeIfNull: false)
  final Money? reducedCreditCardLimit;
  @JsonKey(name: 'loanPeriod', includeIfNull: false)
  final String? loanPeriod;
  @JsonKey(
    name: 'productType',
    includeIfNull: false,
    toJson: productTypeNullableToJson,
    fromJson: productTypeNullableFromJson,
  )
  final enums.ProductType? productType;
  @JsonKey(name: 'settlementAccountId', includeIfNull: false)
  final String? settlementAccountId;
  @JsonKey(
    name: 'uiStatus',
    includeIfNull: false,
    toJson: applicationUiStatusNullableToJson,
    fromJson: applicationUiStatusNullableFromJson,
  )
  final enums.ApplicationUiStatus? uiStatus;
  static const fromJsonFactory = _$ProductPreferencesFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ProductPreferencesExtension on ProductPreferences {
  ProductPreferences copyWith(
      {bool? isAutopayFromSavingSpace,
      int? monthlyRepaymentDay,
      int? monthlyPaymentPercentage,
      Money? selectedAmount,
      Money? reducedCreditCardLimit,
      String? loanPeriod,
      enums.ProductType? productType,
      String? settlementAccountId,
      enums.ApplicationUiStatus? uiStatus}) {
    return ProductPreferences(
        isAutopayFromSavingSpace:
            isAutopayFromSavingSpace ?? this.isAutopayFromSavingSpace,
        monthlyRepaymentDay: monthlyRepaymentDay ?? this.monthlyRepaymentDay,
        monthlyPaymentPercentage:
            monthlyPaymentPercentage ?? this.monthlyPaymentPercentage,
        selectedAmount: selectedAmount ?? this.selectedAmount,
        reducedCreditCardLimit:
            reducedCreditCardLimit ?? this.reducedCreditCardLimit,
        loanPeriod: loanPeriod ?? this.loanPeriod,
        productType: productType ?? this.productType,
        settlementAccountId: settlementAccountId ?? this.settlementAccountId,
        uiStatus: uiStatus ?? this.uiStatus);
  }

  ProductPreferences copyWithWrapped(
      {Wrapped<bool?>? isAutopayFromSavingSpace,
      Wrapped<int?>? monthlyRepaymentDay,
      Wrapped<int?>? monthlyPaymentPercentage,
      Wrapped<Money?>? selectedAmount,
      Wrapped<Money?>? reducedCreditCardLimit,
      Wrapped<String?>? loanPeriod,
      Wrapped<enums.ProductType?>? productType,
      Wrapped<String?>? settlementAccountId,
      Wrapped<enums.ApplicationUiStatus?>? uiStatus}) {
    return ProductPreferences(
        isAutopayFromSavingSpace: (isAutopayFromSavingSpace != null
            ? isAutopayFromSavingSpace.value
            : this.isAutopayFromSavingSpace),
        monthlyRepaymentDay: (monthlyRepaymentDay != null
            ? monthlyRepaymentDay.value
            : this.monthlyRepaymentDay),
        monthlyPaymentPercentage: (monthlyPaymentPercentage != null
            ? monthlyPaymentPercentage.value
            : this.monthlyPaymentPercentage),
        selectedAmount: (selectedAmount != null
            ? selectedAmount.value
            : this.selectedAmount),
        reducedCreditCardLimit: (reducedCreditCardLimit != null
            ? reducedCreditCardLimit.value
            : this.reducedCreditCardLimit),
        loanPeriod: (loanPeriod != null ? loanPeriod.value : this.loanPeriod),
        productType:
            (productType != null ? productType.value : this.productType),
        settlementAccountId: (settlementAccountId != null
            ? settlementAccountId.value
            : this.settlementAccountId),
        uiStatus: (uiStatus != null ? uiStatus.value : this.uiStatus));
  }
}

@JsonSerializable(explicitToJson: true)
class ProductTerms {
  const ProductTerms({
    this.interestRate,
    this.annualFee,
    this.lateFee,
    this.cashWithdrawalAllowance,
    this.dpdPeriod,
    this.loanExpiryPeriod,
    this.loanPeriod,
    this.issuanceFeeParameters,
    this.repaymentFeeParameters,
  });

  factory ProductTerms.fromJson(Map<String, dynamic> json) =>
      _$ProductTermsFromJson(json);

  static const toJsonFactory = _$ProductTermsToJson;
  Map<String, dynamic> toJson() => _$ProductTermsToJson(this);

  @JsonKey(name: 'interestRate', includeIfNull: false)
  final double? interestRate;
  @JsonKey(name: 'annualFee', includeIfNull: false)
  final double? annualFee;
  @JsonKey(name: 'lateFee', includeIfNull: false)
  final double? lateFee;
  @JsonKey(name: 'cashWithdrawalAllowance', includeIfNull: false)
  final double? cashWithdrawalAllowance;
  @JsonKey(name: 'dpdPeriod', includeIfNull: false)
  final String? dpdPeriod;
  @JsonKey(name: 'loanExpiryPeriod', includeIfNull: false)
  final String? loanExpiryPeriod;
  @JsonKey(name: 'loanPeriod', includeIfNull: false)
  final String? loanPeriod;
  @JsonKey(name: 'issuanceFeeParameters', includeIfNull: false)
  final InstallmentFeeParameters? issuanceFeeParameters;
  @JsonKey(name: 'repaymentFeeParameters', includeIfNull: false)
  final InstallmentFeeParameters? repaymentFeeParameters;
  static const fromJsonFactory = _$ProductTermsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ProductTermsExtension on ProductTerms {
  ProductTerms copyWith(
      {double? interestRate,
      double? annualFee,
      double? lateFee,
      double? cashWithdrawalAllowance,
      String? dpdPeriod,
      String? loanExpiryPeriod,
      String? loanPeriod,
      InstallmentFeeParameters? issuanceFeeParameters,
      InstallmentFeeParameters? repaymentFeeParameters}) {
    return ProductTerms(
        interestRate: interestRate ?? this.interestRate,
        annualFee: annualFee ?? this.annualFee,
        lateFee: lateFee ?? this.lateFee,
        cashWithdrawalAllowance:
            cashWithdrawalAllowance ?? this.cashWithdrawalAllowance,
        dpdPeriod: dpdPeriod ?? this.dpdPeriod,
        loanExpiryPeriod: loanExpiryPeriod ?? this.loanExpiryPeriod,
        loanPeriod: loanPeriod ?? this.loanPeriod,
        issuanceFeeParameters:
            issuanceFeeParameters ?? this.issuanceFeeParameters,
        repaymentFeeParameters:
            repaymentFeeParameters ?? this.repaymentFeeParameters);
  }

  ProductTerms copyWithWrapped(
      {Wrapped<double?>? interestRate,
      Wrapped<double?>? annualFee,
      Wrapped<double?>? lateFee,
      Wrapped<double?>? cashWithdrawalAllowance,
      Wrapped<String?>? dpdPeriod,
      Wrapped<String?>? loanExpiryPeriod,
      Wrapped<String?>? loanPeriod,
      Wrapped<InstallmentFeeParameters?>? issuanceFeeParameters,
      Wrapped<InstallmentFeeParameters?>? repaymentFeeParameters}) {
    return ProductTerms(
        interestRate:
            (interestRate != null ? interestRate.value : this.interestRate),
        annualFee: (annualFee != null ? annualFee.value : this.annualFee),
        lateFee: (lateFee != null ? lateFee.value : this.lateFee),
        cashWithdrawalAllowance: (cashWithdrawalAllowance != null
            ? cashWithdrawalAllowance.value
            : this.cashWithdrawalAllowance),
        dpdPeriod: (dpdPeriod != null ? dpdPeriod.value : this.dpdPeriod),
        loanExpiryPeriod: (loanExpiryPeriod != null
            ? loanExpiryPeriod.value
            : this.loanExpiryPeriod),
        loanPeriod: (loanPeriod != null ? loanPeriod.value : this.loanPeriod),
        issuanceFeeParameters: (issuanceFeeParameters != null
            ? issuanceFeeParameters.value
            : this.issuanceFeeParameters),
        repaymentFeeParameters: (repaymentFeeParameters != null
            ? repaymentFeeParameters.value
            : this.repaymentFeeParameters));
  }
}

@JsonSerializable(explicitToJson: true)
class ResubmittedApplicationDetails {
  const ResubmittedApplicationDetails({
    this.fixedDepositInfo,
  });

  factory ResubmittedApplicationDetails.fromJson(Map<String, dynamic> json) =>
      _$ResubmittedApplicationDetailsFromJson(json);

  static const toJsonFactory = _$ResubmittedApplicationDetailsToJson;
  Map<String, dynamic> toJson() => _$ResubmittedApplicationDetailsToJson(this);

  @JsonKey(name: 'fixedDepositInfo', includeIfNull: false)
  final FixedDepositInfo? fixedDepositInfo;
  static const fromJsonFactory = _$ResubmittedApplicationDetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ResubmittedApplicationDetailsExtension
    on ResubmittedApplicationDetails {
  ResubmittedApplicationDetails copyWith({FixedDepositInfo? fixedDepositInfo}) {
    return ResubmittedApplicationDetails(
        fixedDepositInfo: fixedDepositInfo ?? this.fixedDepositInfo);
  }

  ResubmittedApplicationDetails copyWithWrapped(
      {Wrapped<FixedDepositInfo?>? fixedDepositInfo}) {
    return ResubmittedApplicationDetails(
        fixedDepositInfo: (fixedDepositInfo != null
            ? fixedDepositInfo.value
            : this.fixedDepositInfo));
  }
}

@JsonSerializable(explicitToJson: true)
class SecureLoanEligibilityDetails {
  const SecureLoanEligibilityDetails({
    this.securityProductDetails,
  });

  factory SecureLoanEligibilityDetails.fromJson(Map<String, dynamic> json) =>
      _$SecureLoanEligibilityDetailsFromJson(json);

  static const toJsonFactory = _$SecureLoanEligibilityDetailsToJson;
  Map<String, dynamic> toJson() => _$SecureLoanEligibilityDetailsToJson(this);

  @JsonKey(
      name: 'securityProductDetails',
      includeIfNull: false,
      defaultValue: <LoanSecurityProductDetails>[])
  final List<LoanSecurityProductDetails>? securityProductDetails;
  static const fromJsonFactory = _$SecureLoanEligibilityDetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $SecureLoanEligibilityDetailsExtension
    on SecureLoanEligibilityDetails {
  SecureLoanEligibilityDetails copyWith(
      {List<LoanSecurityProductDetails>? securityProductDetails}) {
    return SecureLoanEligibilityDetails(
        securityProductDetails:
            securityProductDetails ?? this.securityProductDetails);
  }

  SecureLoanEligibilityDetails copyWithWrapped(
      {Wrapped<List<LoanSecurityProductDetails>?>? securityProductDetails}) {
    return SecureLoanEligibilityDetails(
        securityProductDetails: (securityProductDetails != null
            ? securityProductDetails.value
            : this.securityProductDetails));
  }
}

@JsonSerializable(explicitToJson: true)
class ApplicationInputDataRequestDTO {
  const ApplicationInputDataRequestDTO({
    this.requestedMoney,
    this.annualTurnover,
    this.selectedAmount,
    this.vatReportingMethod,
    this.ibans,
    this.wioIbans,
    this.monthlyPaymentPercentage,
    this.isAutopayFromSavingSpace,
    this.monthlyRepaymentDay,
    this.uiStatus,
    this.nonRegisteredVatReasonType,
    this.nonRegisteredVatReasonText,
    this.referralCode,
    this.securedLoan,
    this.serviceChannels,
    this.employeeCountRange,
    this.businessModelDescription,
    this.missingProofOfAddressReason,
    this.missingFinancialStatementReason,
    this.missingVatReason,
    this.addresses,
    this.productPreferences,
  });

  factory ApplicationInputDataRequestDTO.fromJson(Map<String, dynamic> json) =>
      _$ApplicationInputDataRequestDTOFromJson(json);

  static const toJsonFactory = _$ApplicationInputDataRequestDTOToJson;
  Map<String, dynamic> toJson() => _$ApplicationInputDataRequestDTOToJson(this);

  @JsonKey(name: 'requestedMoney', includeIfNull: false)
  final Money? requestedMoney;
  @JsonKey(name: 'annualTurnover', includeIfNull: false)
  final Money? annualTurnover;
  @JsonKey(name: 'selectedAmount', includeIfNull: false)
  final Money? selectedAmount;
  @JsonKey(
    name: 'vatReportingMethod',
    includeIfNull: false,
    toJson: applicationInputDataRequestDTOVatReportingMethodNullableToJson,
    fromJson: applicationInputDataRequestDTOVatReportingMethodNullableFromJson,
  )
  final enums.ApplicationInputDataRequestDTOVatReportingMethod?
      vatReportingMethod;
  @JsonKey(name: 'ibans', includeIfNull: false, defaultValue: <Iban>[])
  final List<Iban>? ibans;
  @JsonKey(name: 'wioIbans', includeIfNull: false, defaultValue: <Iban>[])
  final List<Iban>? wioIbans;
  @JsonKey(name: 'monthlyPaymentPercentage', includeIfNull: false)
  final int? monthlyPaymentPercentage;
  @JsonKey(name: 'isAutopayFromSavingSpace', includeIfNull: false)
  final bool? isAutopayFromSavingSpace;
  @JsonKey(name: 'monthlyRepaymentDay', includeIfNull: false)
  final int? monthlyRepaymentDay;
  @JsonKey(
    name: 'uiStatus',
    includeIfNull: false,
    toJson: applicationUiStatusNullableToJson,
    fromJson: applicationUiStatusNullableFromJson,
  )
  final enums.ApplicationUiStatus? uiStatus;
  @JsonKey(
    name: 'nonRegisteredVatReasonType',
    includeIfNull: false,
    toJson:
        applicationInputDataRequestDTONonRegisteredVatReasonTypeNullableToJson,
    fromJson:
        applicationInputDataRequestDTONonRegisteredVatReasonTypeNullableFromJson,
  )
  final enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType?
      nonRegisteredVatReasonType;
  @JsonKey(name: 'nonRegisteredVatReasonText', includeIfNull: false)
  final String? nonRegisteredVatReasonText;
  @JsonKey(name: 'referralCode', includeIfNull: false)
  final String? referralCode;
  @JsonKey(name: 'securedLoan', includeIfNull: false)
  final bool? securedLoan;
  @JsonKey(
      name: 'serviceChannels', includeIfNull: false, defaultValue: <String>[])
  final List<String>? serviceChannels;
  @JsonKey(name: 'employeeCountRange', includeIfNull: false)
  final String? employeeCountRange;
  @JsonKey(name: 'businessModelDescription', includeIfNull: false)
  final String? businessModelDescription;
  @JsonKey(name: 'missingProofOfAddressReason', includeIfNull: false)
  final String? missingProofOfAddressReason;
  @JsonKey(name: 'missingFinancialStatementReason', includeIfNull: false)
  final String? missingFinancialStatementReason;
  @JsonKey(name: 'missingVatReason', includeIfNull: false)
  final String? missingVatReason;
  @JsonKey(name: 'addresses', includeIfNull: false, defaultValue: <Address>[])
  final List<Address>? addresses;
  @JsonKey(
      name: 'productPreferences',
      includeIfNull: false,
      defaultValue: <ProductPreferences>[])
  final List<ProductPreferences>? productPreferences;
  static const fromJsonFactory = _$ApplicationInputDataRequestDTOFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ApplicationInputDataRequestDTOExtension
    on ApplicationInputDataRequestDTO {
  ApplicationInputDataRequestDTO copyWith(
      {Money? requestedMoney,
      Money? annualTurnover,
      Money? selectedAmount,
      enums.ApplicationInputDataRequestDTOVatReportingMethod?
          vatReportingMethod,
      List<Iban>? ibans,
      List<Iban>? wioIbans,
      int? monthlyPaymentPercentage,
      bool? isAutopayFromSavingSpace,
      int? monthlyRepaymentDay,
      enums.ApplicationUiStatus? uiStatus,
      enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType?
          nonRegisteredVatReasonType,
      String? nonRegisteredVatReasonText,
      String? referralCode,
      bool? securedLoan,
      List<String>? serviceChannels,
      String? employeeCountRange,
      String? businessModelDescription,
      String? missingProofOfAddressReason,
      String? missingFinancialStatementReason,
      String? missingVatReason,
      List<Address>? addresses,
      List<ProductPreferences>? productPreferences}) {
    return ApplicationInputDataRequestDTO(
        requestedMoney: requestedMoney ?? this.requestedMoney,
        annualTurnover: annualTurnover ?? this.annualTurnover,
        selectedAmount: selectedAmount ?? this.selectedAmount,
        vatReportingMethod: vatReportingMethod ?? this.vatReportingMethod,
        ibans: ibans ?? this.ibans,
        wioIbans: wioIbans ?? this.wioIbans,
        monthlyPaymentPercentage:
            monthlyPaymentPercentage ?? this.monthlyPaymentPercentage,
        isAutopayFromSavingSpace:
            isAutopayFromSavingSpace ?? this.isAutopayFromSavingSpace,
        monthlyRepaymentDay: monthlyRepaymentDay ?? this.monthlyRepaymentDay,
        uiStatus: uiStatus ?? this.uiStatus,
        nonRegisteredVatReasonType:
            nonRegisteredVatReasonType ?? this.nonRegisteredVatReasonType,
        nonRegisteredVatReasonText:
            nonRegisteredVatReasonText ?? this.nonRegisteredVatReasonText,
        referralCode: referralCode ?? this.referralCode,
        securedLoan: securedLoan ?? this.securedLoan,
        serviceChannels: serviceChannels ?? this.serviceChannels,
        employeeCountRange: employeeCountRange ?? this.employeeCountRange,
        businessModelDescription:
            businessModelDescription ?? this.businessModelDescription,
        missingProofOfAddressReason:
            missingProofOfAddressReason ?? this.missingProofOfAddressReason,
        missingFinancialStatementReason: missingFinancialStatementReason ??
            this.missingFinancialStatementReason,
        missingVatReason: missingVatReason ?? this.missingVatReason,
        addresses: addresses ?? this.addresses,
        productPreferences: productPreferences ?? this.productPreferences);
  }

  ApplicationInputDataRequestDTO copyWithWrapped(
      {Wrapped<Money?>? requestedMoney,
      Wrapped<Money?>? annualTurnover,
      Wrapped<Money?>? selectedAmount,
      Wrapped<enums.ApplicationInputDataRequestDTOVatReportingMethod?>?
          vatReportingMethod,
      Wrapped<List<Iban>?>? ibans,
      Wrapped<List<Iban>?>? wioIbans,
      Wrapped<int?>? monthlyPaymentPercentage,
      Wrapped<bool?>? isAutopayFromSavingSpace,
      Wrapped<int?>? monthlyRepaymentDay,
      Wrapped<enums.ApplicationUiStatus?>? uiStatus,
      Wrapped<enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType?>?
          nonRegisteredVatReasonType,
      Wrapped<String?>? nonRegisteredVatReasonText,
      Wrapped<String?>? referralCode,
      Wrapped<bool?>? securedLoan,
      Wrapped<List<String>?>? serviceChannels,
      Wrapped<String?>? employeeCountRange,
      Wrapped<String?>? businessModelDescription,
      Wrapped<String?>? missingProofOfAddressReason,
      Wrapped<String?>? missingFinancialStatementReason,
      Wrapped<String?>? missingVatReason,
      Wrapped<List<Address>?>? addresses,
      Wrapped<List<ProductPreferences>?>? productPreferences}) {
    return ApplicationInputDataRequestDTO(
        requestedMoney: (requestedMoney != null
            ? requestedMoney.value
            : this.requestedMoney),
        annualTurnover: (annualTurnover != null
            ? annualTurnover.value
            : this.annualTurnover),
        selectedAmount: (selectedAmount != null
            ? selectedAmount.value
            : this.selectedAmount),
        vatReportingMethod: (vatReportingMethod != null
            ? vatReportingMethod.value
            : this.vatReportingMethod),
        ibans: (ibans != null ? ibans.value : this.ibans),
        wioIbans: (wioIbans != null ? wioIbans.value : this.wioIbans),
        monthlyPaymentPercentage: (monthlyPaymentPercentage != null
            ? monthlyPaymentPercentage.value
            : this.monthlyPaymentPercentage),
        isAutopayFromSavingSpace: (isAutopayFromSavingSpace != null
            ? isAutopayFromSavingSpace.value
            : this.isAutopayFromSavingSpace),
        monthlyRepaymentDay: (monthlyRepaymentDay != null
            ? monthlyRepaymentDay.value
            : this.monthlyRepaymentDay),
        uiStatus: (uiStatus != null ? uiStatus.value : this.uiStatus),
        nonRegisteredVatReasonType: (nonRegisteredVatReasonType != null
            ? nonRegisteredVatReasonType.value
            : this.nonRegisteredVatReasonType),
        nonRegisteredVatReasonText: (nonRegisteredVatReasonText != null
            ? nonRegisteredVatReasonText.value
            : this.nonRegisteredVatReasonText),
        referralCode:
            (referralCode != null ? referralCode.value : this.referralCode),
        securedLoan:
            (securedLoan != null ? securedLoan.value : this.securedLoan),
        serviceChannels: (serviceChannels != null
            ? serviceChannels.value
            : this.serviceChannels),
        employeeCountRange: (employeeCountRange != null
            ? employeeCountRange.value
            : this.employeeCountRange),
        businessModelDescription: (businessModelDescription != null
            ? businessModelDescription.value
            : this.businessModelDescription),
        missingProofOfAddressReason: (missingProofOfAddressReason != null
            ? missingProofOfAddressReason.value
            : this.missingProofOfAddressReason),
        missingFinancialStatementReason:
            (missingFinancialStatementReason != null
                ? missingFinancialStatementReason.value
                : this.missingFinancialStatementReason),
        missingVatReason: (missingVatReason != null
            ? missingVatReason.value
            : this.missingVatReason),
        addresses: (addresses != null ? addresses.value : this.addresses),
        productPreferences: (productPreferences != null
            ? productPreferences.value
            : this.productPreferences));
  }
}

@JsonSerializable(explicitToJson: true)
class LoanInstallmentEvaluateCommand {
  const LoanInstallmentEvaluateCommand({
    required this.amount,
    required this.productType,
    this.loanProductCode,
    this.evaluationTransactionType,
    this.previewScheduleStrategy,
    this.loanPeriod,
    this.applicationId,
    this.loanAccountId,
    this.firstRepaymentDate,
  });

  factory LoanInstallmentEvaluateCommand.fromJson(Map<String, dynamic> json) =>
      _$LoanInstallmentEvaluateCommandFromJson(json);

  static const toJsonFactory = _$LoanInstallmentEvaluateCommandToJson;
  Map<String, dynamic> toJson() => _$LoanInstallmentEvaluateCommandToJson(this);

  @JsonKey(name: 'amount', includeIfNull: false)
  final Money amount;
  @JsonKey(
    name: 'productType',
    includeIfNull: false,
    toJson: productTypeToJson,
    fromJson: productTypeFromJson,
  )
  final enums.ProductType productType;
  @JsonKey(
    name: 'loanProductCode',
    includeIfNull: false,
    toJson: loanInstallmentEvaluateCommandLoanProductCodeNullableToJson,
    fromJson: loanInstallmentEvaluateCommandLoanProductCodeNullableFromJson,
  )
  final enums.LoanInstallmentEvaluateCommandLoanProductCode? loanProductCode;
  @JsonKey(
    name: 'evaluationTransactionType',
    includeIfNull: false,
    toJson:
        loanInstallmentEvaluateCommandEvaluationTransactionTypeNullableToJson,
    fromJson:
        loanInstallmentEvaluateCommandEvaluationTransactionTypeNullableFromJson,
  )
  final enums.LoanInstallmentEvaluateCommandEvaluationTransactionType?
      evaluationTransactionType;
  @JsonKey(
    name: 'previewScheduleStrategy',
    includeIfNull: false,
    toJson: loanInstallmentEvaluateCommandPreviewScheduleStrategyNullableToJson,
    fromJson:
        loanInstallmentEvaluateCommandPreviewScheduleStrategyNullableFromJson,
  )
  final enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy?
      previewScheduleStrategy;
  @JsonKey(name: 'loanPeriod', includeIfNull: false)
  final String? loanPeriod;
  @JsonKey(name: 'applicationId', includeIfNull: false)
  final String? applicationId;
  @JsonKey(name: 'loanAccountId', includeIfNull: false)
  final String? loanAccountId;
  @JsonKey(
      name: 'firstRepaymentDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime? firstRepaymentDate;
  static const fromJsonFactory = _$LoanInstallmentEvaluateCommandFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $LoanInstallmentEvaluateCommandExtension
    on LoanInstallmentEvaluateCommand {
  LoanInstallmentEvaluateCommand copyWith(
      {Money? amount,
      enums.ProductType? productType,
      enums.LoanInstallmentEvaluateCommandLoanProductCode? loanProductCode,
      enums.LoanInstallmentEvaluateCommandEvaluationTransactionType?
          evaluationTransactionType,
      enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy?
          previewScheduleStrategy,
      String? loanPeriod,
      String? applicationId,
      String? loanAccountId,
      DateTime? firstRepaymentDate}) {
    return LoanInstallmentEvaluateCommand(
        amount: amount ?? this.amount,
        productType: productType ?? this.productType,
        loanProductCode: loanProductCode ?? this.loanProductCode,
        evaluationTransactionType:
            evaluationTransactionType ?? this.evaluationTransactionType,
        previewScheduleStrategy:
            previewScheduleStrategy ?? this.previewScheduleStrategy,
        loanPeriod: loanPeriod ?? this.loanPeriod,
        applicationId: applicationId ?? this.applicationId,
        loanAccountId: loanAccountId ?? this.loanAccountId,
        firstRepaymentDate: firstRepaymentDate ?? this.firstRepaymentDate);
  }

  LoanInstallmentEvaluateCommand copyWithWrapped(
      {Wrapped<Money>? amount,
      Wrapped<enums.ProductType>? productType,
      Wrapped<enums.LoanInstallmentEvaluateCommandLoanProductCode?>?
          loanProductCode,
      Wrapped<enums.LoanInstallmentEvaluateCommandEvaluationTransactionType?>?
          evaluationTransactionType,
      Wrapped<enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy?>?
          previewScheduleStrategy,
      Wrapped<String?>? loanPeriod,
      Wrapped<String?>? applicationId,
      Wrapped<String?>? loanAccountId,
      Wrapped<DateTime?>? firstRepaymentDate}) {
    return LoanInstallmentEvaluateCommand(
        amount: (amount != null ? amount.value : this.amount),
        productType:
            (productType != null ? productType.value : this.productType),
        loanProductCode: (loanProductCode != null
            ? loanProductCode.value
            : this.loanProductCode),
        evaluationTransactionType: (evaluationTransactionType != null
            ? evaluationTransactionType.value
            : this.evaluationTransactionType),
        previewScheduleStrategy: (previewScheduleStrategy != null
            ? previewScheduleStrategy.value
            : this.previewScheduleStrategy),
        loanPeriod: (loanPeriod != null ? loanPeriod.value : this.loanPeriod),
        applicationId:
            (applicationId != null ? applicationId.value : this.applicationId),
        loanAccountId:
            (loanAccountId != null ? loanAccountId.value : this.loanAccountId),
        firstRepaymentDate: (firstRepaymentDate != null
            ? firstRepaymentDate.value
            : this.firstRepaymentDate));
  }
}

@JsonSerializable(explicitToJson: true)
class ApplicableFees {
  const ApplicableFees({
    this.earlyRepaymentFee,
    this.loanIssuanceFee,
  });

  factory ApplicableFees.fromJson(Map<String, dynamic> json) =>
      _$ApplicableFeesFromJson(json);

  static const toJsonFactory = _$ApplicableFeesToJson;
  Map<String, dynamic> toJson() => _$ApplicableFeesToJson(this);

  @JsonKey(name: 'earlyRepaymentFee', includeIfNull: false)
  final EarlyRepaymentFee? earlyRepaymentFee;
  @JsonKey(name: 'loanIssuanceFee', includeIfNull: false)
  final LoanIssuanceFee? loanIssuanceFee;
  static const fromJsonFactory = _$ApplicableFeesFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ApplicableFeesExtension on ApplicableFees {
  ApplicableFees copyWith(
      {EarlyRepaymentFee? earlyRepaymentFee,
      LoanIssuanceFee? loanIssuanceFee}) {
    return ApplicableFees(
        earlyRepaymentFee: earlyRepaymentFee ?? this.earlyRepaymentFee,
        loanIssuanceFee: loanIssuanceFee ?? this.loanIssuanceFee);
  }

  ApplicableFees copyWithWrapped(
      {Wrapped<EarlyRepaymentFee?>? earlyRepaymentFee,
      Wrapped<LoanIssuanceFee?>? loanIssuanceFee}) {
    return ApplicableFees(
        earlyRepaymentFee: (earlyRepaymentFee != null
            ? earlyRepaymentFee.value
            : this.earlyRepaymentFee),
        loanIssuanceFee: (loanIssuanceFee != null
            ? loanIssuanceFee.value
            : this.loanIssuanceFee));
  }
}

@JsonSerializable(explicitToJson: true)
class EarlyRepaymentFee {
  const EarlyRepaymentFee({
    this.txnId,
    this.amount,
    this.currency,
  });

  factory EarlyRepaymentFee.fromJson(Map<String, dynamic> json) =>
      _$EarlyRepaymentFeeFromJson(json);

  static const toJsonFactory = _$EarlyRepaymentFeeToJson;
  Map<String, dynamic> toJson() => _$EarlyRepaymentFeeToJson(this);

  @JsonKey(name: 'txnId', includeIfNull: false)
  final String? txnId;
  @JsonKey(name: 'amount', includeIfNull: false)
  final double? amount;
  @JsonKey(
    name: 'currency',
    includeIfNull: false,
    toJson: currencyNullableToJson,
    fromJson: currencyNullableFromJson,
  )
  final enums.Currency? currency;
  static const fromJsonFactory = _$EarlyRepaymentFeeFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EarlyRepaymentFeeExtension on EarlyRepaymentFee {
  EarlyRepaymentFee copyWith(
      {String? txnId, double? amount, enums.Currency? currency}) {
    return EarlyRepaymentFee(
        txnId: txnId ?? this.txnId,
        amount: amount ?? this.amount,
        currency: currency ?? this.currency);
  }

  EarlyRepaymentFee copyWithWrapped(
      {Wrapped<String?>? txnId,
      Wrapped<double?>? amount,
      Wrapped<enums.Currency?>? currency}) {
    return EarlyRepaymentFee(
        txnId: (txnId != null ? txnId.value : this.txnId),
        amount: (amount != null ? amount.value : this.amount),
        currency: (currency != null ? currency.value : this.currency));
  }
}

@JsonSerializable(explicitToJson: true)
class Installment {
  const Installment({
    required this.dueDate,
    this.overDue,
    required this.amount,
    this.installmentAmount,
    this.totalInstallmentAmount,
    this.totalInstallmentAmountDue,
    this.totalInstallmentAmountPaid,
    this.totalScheduledInterestDue,
    this.totalPenaltyInterest,
    this.isPast,
    this.paid,
    this.upcomingInstallment,
  });

  factory Installment.fromJson(Map<String, dynamic> json) =>
      _$InstallmentFromJson(json);

  static const toJsonFactory = _$InstallmentToJson;
  Map<String, dynamic> toJson() => _$InstallmentToJson(this);

  @JsonKey(name: 'dueDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime dueDate;
  @JsonKey(name: 'overDue', includeIfNull: false)
  final Money? overDue;
  @JsonKey(name: 'amount', includeIfNull: false)
  final InstallmentAmount amount;
  @JsonKey(name: 'installmentAmount', includeIfNull: false)
  final InstallmentAmount? installmentAmount;
  @JsonKey(name: 'totalInstallmentAmount', includeIfNull: false)
  final InstallmentAmount? totalInstallmentAmount;
  @JsonKey(name: 'totalInstallmentAmountDue', includeIfNull: false)
  final InstallmentAmount? totalInstallmentAmountDue;
  @JsonKey(name: 'totalInstallmentAmountPaid', includeIfNull: false)
  final InstallmentAmount? totalInstallmentAmountPaid;
  @JsonKey(name: 'totalScheduledInterestDue', includeIfNull: false)
  final Money? totalScheduledInterestDue;
  @JsonKey(name: 'totalPenaltyInterest', includeIfNull: false)
  final Money? totalPenaltyInterest;
  @JsonKey(name: 'isPast', includeIfNull: false)
  final bool? isPast;
  @JsonKey(name: 'paid', includeIfNull: false)
  final bool? paid;
  @JsonKey(name: 'upcomingInstallment', includeIfNull: false)
  final bool? upcomingInstallment;
  static const fromJsonFactory = _$InstallmentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $InstallmentExtension on Installment {
  Installment copyWith(
      {DateTime? dueDate,
      Money? overDue,
      InstallmentAmount? amount,
      InstallmentAmount? installmentAmount,
      InstallmentAmount? totalInstallmentAmount,
      InstallmentAmount? totalInstallmentAmountDue,
      InstallmentAmount? totalInstallmentAmountPaid,
      Money? totalScheduledInterestDue,
      Money? totalPenaltyInterest,
      bool? isPast,
      bool? paid,
      bool? upcomingInstallment}) {
    return Installment(
        dueDate: dueDate ?? this.dueDate,
        overDue: overDue ?? this.overDue,
        amount: amount ?? this.amount,
        installmentAmount: installmentAmount ?? this.installmentAmount,
        totalInstallmentAmount:
            totalInstallmentAmount ?? this.totalInstallmentAmount,
        totalInstallmentAmountDue:
            totalInstallmentAmountDue ?? this.totalInstallmentAmountDue,
        totalInstallmentAmountPaid:
            totalInstallmentAmountPaid ?? this.totalInstallmentAmountPaid,
        totalScheduledInterestDue:
            totalScheduledInterestDue ?? this.totalScheduledInterestDue,
        totalPenaltyInterest: totalPenaltyInterest ?? this.totalPenaltyInterest,
        isPast: isPast ?? this.isPast,
        paid: paid ?? this.paid,
        upcomingInstallment: upcomingInstallment ?? this.upcomingInstallment);
  }

  Installment copyWithWrapped(
      {Wrapped<DateTime>? dueDate,
      Wrapped<Money?>? overDue,
      Wrapped<InstallmentAmount>? amount,
      Wrapped<InstallmentAmount?>? installmentAmount,
      Wrapped<InstallmentAmount?>? totalInstallmentAmount,
      Wrapped<InstallmentAmount?>? totalInstallmentAmountDue,
      Wrapped<InstallmentAmount?>? totalInstallmentAmountPaid,
      Wrapped<Money?>? totalScheduledInterestDue,
      Wrapped<Money?>? totalPenaltyInterest,
      Wrapped<bool?>? isPast,
      Wrapped<bool?>? paid,
      Wrapped<bool?>? upcomingInstallment}) {
    return Installment(
        dueDate: (dueDate != null ? dueDate.value : this.dueDate),
        overDue: (overDue != null ? overDue.value : this.overDue),
        amount: (amount != null ? amount.value : this.amount),
        installmentAmount: (installmentAmount != null
            ? installmentAmount.value
            : this.installmentAmount),
        totalInstallmentAmount: (totalInstallmentAmount != null
            ? totalInstallmentAmount.value
            : this.totalInstallmentAmount),
        totalInstallmentAmountDue: (totalInstallmentAmountDue != null
            ? totalInstallmentAmountDue.value
            : this.totalInstallmentAmountDue),
        totalInstallmentAmountPaid: (totalInstallmentAmountPaid != null
            ? totalInstallmentAmountPaid.value
            : this.totalInstallmentAmountPaid),
        totalScheduledInterestDue: (totalScheduledInterestDue != null
            ? totalScheduledInterestDue.value
            : this.totalScheduledInterestDue),
        totalPenaltyInterest: (totalPenaltyInterest != null
            ? totalPenaltyInterest.value
            : this.totalPenaltyInterest),
        isPast: (isPast != null ? isPast.value : this.isPast),
        paid: (paid != null ? paid.value : this.paid),
        upcomingInstallment: (upcomingInstallment != null
            ? upcomingInstallment.value
            : this.upcomingInstallment));
  }
}

@JsonSerializable(explicitToJson: true)
class InstallmentAmount {
  const InstallmentAmount({
    required this.fee,
    required this.principal,
    required this.interest,
  });

  factory InstallmentAmount.fromJson(Map<String, dynamic> json) =>
      _$InstallmentAmountFromJson(json);

  static const toJsonFactory = _$InstallmentAmountToJson;
  Map<String, dynamic> toJson() => _$InstallmentAmountToJson(this);

  @JsonKey(name: 'fee', includeIfNull: false)
  final double fee;
  @JsonKey(name: 'principal', includeIfNull: false)
  final double principal;
  @JsonKey(name: 'interest', includeIfNull: false)
  final double interest;
  static const fromJsonFactory = _$InstallmentAmountFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $InstallmentAmountExtension on InstallmentAmount {
  InstallmentAmount copyWith(
      {double? fee, double? principal, double? interest}) {
    return InstallmentAmount(
        fee: fee ?? this.fee,
        principal: principal ?? this.principal,
        interest: interest ?? this.interest);
  }

  InstallmentAmount copyWithWrapped(
      {Wrapped<double>? fee,
      Wrapped<double>? principal,
      Wrapped<double>? interest}) {
    return InstallmentAmount(
        fee: (fee != null ? fee.value : this.fee),
        principal: (principal != null ? principal.value : this.principal),
        interest: (interest != null ? interest.value : this.interest));
  }
}

@JsonSerializable(explicitToJson: true)
class LoanIssuanceFee {
  const LoanIssuanceFee({
    this.txnId,
    this.amount,
    this.currency,
  });

  factory LoanIssuanceFee.fromJson(Map<String, dynamic> json) =>
      _$LoanIssuanceFeeFromJson(json);

  static const toJsonFactory = _$LoanIssuanceFeeToJson;
  Map<String, dynamic> toJson() => _$LoanIssuanceFeeToJson(this);

  @JsonKey(name: 'txnId', includeIfNull: false)
  final String? txnId;
  @JsonKey(name: 'amount', includeIfNull: false)
  final double? amount;
  @JsonKey(
    name: 'currency',
    includeIfNull: false,
    toJson: currencyNullableToJson,
    fromJson: currencyNullableFromJson,
  )
  final enums.Currency? currency;
  static const fromJsonFactory = _$LoanIssuanceFeeFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $LoanIssuanceFeeExtension on LoanIssuanceFee {
  LoanIssuanceFee copyWith(
      {String? txnId, double? amount, enums.Currency? currency}) {
    return LoanIssuanceFee(
        txnId: txnId ?? this.txnId,
        amount: amount ?? this.amount,
        currency: currency ?? this.currency);
  }

  LoanIssuanceFee copyWithWrapped(
      {Wrapped<String?>? txnId,
      Wrapped<double?>? amount,
      Wrapped<enums.Currency?>? currency}) {
    return LoanIssuanceFee(
        txnId: (txnId != null ? txnId.value : this.txnId),
        amount: (amount != null ? amount.value : this.amount),
        currency: (currency != null ? currency.value : this.currency));
  }
}

@JsonSerializable(explicitToJson: true)
class NextInstallment {
  const NextInstallment({
    required this.interval,
    required this.installment,
  });

  factory NextInstallment.fromJson(Map<String, dynamic> json) =>
      _$NextInstallmentFromJson(json);

  static const toJsonFactory = _$NextInstallmentToJson;
  Map<String, dynamic> toJson() => _$NextInstallmentToJson(this);

  @JsonKey(
    name: 'interval',
    includeIfNull: false,
    toJson: nextInstallmentIntervalToJson,
    fromJson: nextInstallmentIntervalFromJson,
  )
  final enums.NextInstallmentInterval interval;
  @JsonKey(name: 'installment', includeIfNull: false)
  final Installment installment;
  static const fromJsonFactory = _$NextInstallmentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $NextInstallmentExtension on NextInstallment {
  NextInstallment copyWith(
      {enums.NextInstallmentInterval? interval, Installment? installment}) {
    return NextInstallment(
        interval: interval ?? this.interval,
        installment: installment ?? this.installment);
  }

  NextInstallment copyWithWrapped(
      {Wrapped<enums.NextInstallmentInterval>? interval,
      Wrapped<Installment>? installment}) {
    return NextInstallment(
        interval: (interval != null ? interval.value : this.interval),
        installment:
            (installment != null ? installment.value : this.installment));
  }
}

@JsonSerializable(explicitToJson: true)
class RepaymentDetails {
  const RepaymentDetails({
    this.principalPaid,
    this.interestPaid,
    this.feesPaid,
  });

  factory RepaymentDetails.fromJson(Map<String, dynamic> json) =>
      _$RepaymentDetailsFromJson(json);

  static const toJsonFactory = _$RepaymentDetailsToJson;
  Map<String, dynamic> toJson() => _$RepaymentDetailsToJson(this);

  @JsonKey(name: 'principalPaid', includeIfNull: false)
  final double? principalPaid;
  @JsonKey(name: 'interestPaid', includeIfNull: false)
  final double? interestPaid;
  @JsonKey(name: 'feesPaid', includeIfNull: false)
  final double? feesPaid;
  static const fromJsonFactory = _$RepaymentDetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $RepaymentDetailsExtension on RepaymentDetails {
  RepaymentDetails copyWith(
      {double? principalPaid, double? interestPaid, double? feesPaid}) {
    return RepaymentDetails(
        principalPaid: principalPaid ?? this.principalPaid,
        interestPaid: interestPaid ?? this.interestPaid,
        feesPaid: feesPaid ?? this.feesPaid);
  }

  RepaymentDetails copyWithWrapped(
      {Wrapped<double?>? principalPaid,
      Wrapped<double?>? interestPaid,
      Wrapped<double?>? feesPaid}) {
    return RepaymentDetails(
        principalPaid:
            (principalPaid != null ? principalPaid.value : this.principalPaid),
        interestPaid:
            (interestPaid != null ? interestPaid.value : this.interestPaid),
        feesPaid: (feesPaid != null ? feesPaid.value : this.feesPaid));
  }
}

@JsonSerializable(explicitToJson: true)
class Schedule {
  const Schedule({
    this.overDue,
    this.missedPaymentCount,
    this.loanAmount,
    this.currency,
    this.nextInstallment,
    this.installmentSummary,
    this.applicableFees,
    this.repaymentDetails,
    this.interestRate,
    this.currentInstallmentDue,
  });

  factory Schedule.fromJson(Map<String, dynamic> json) =>
      _$ScheduleFromJson(json);

  static const toJsonFactory = _$ScheduleToJson;
  Map<String, dynamic> toJson() => _$ScheduleToJson(this);

  @JsonKey(name: 'overDue', includeIfNull: false)
  final double? overDue;
  @JsonKey(name: 'missedPaymentCount', includeIfNull: false)
  final int? missedPaymentCount;
  @JsonKey(name: 'loanAmount', includeIfNull: false)
  final InstallmentAmount? loanAmount;
  @JsonKey(
    name: 'currency',
    includeIfNull: false,
    toJson: currencyNullableToJson,
    fromJson: currencyNullableFromJson,
  )
  final enums.Currency? currency;
  @JsonKey(name: 'nextInstallment', includeIfNull: false)
  final NextInstallment? nextInstallment;
  @JsonKey(
      name: 'installmentSummary',
      includeIfNull: false,
      defaultValue: <Installment>[])
  final List<Installment>? installmentSummary;
  @JsonKey(name: 'applicableFees', includeIfNull: false)
  final ApplicableFees? applicableFees;
  @JsonKey(name: 'repaymentDetails', includeIfNull: false)
  final RepaymentDetails? repaymentDetails;
  @JsonKey(name: 'interestRate', includeIfNull: false)
  final double? interestRate;
  @JsonKey(name: 'currentInstallmentDue', includeIfNull: false)
  final InstallmentAmount? currentInstallmentDue;
  static const fromJsonFactory = _$ScheduleFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ScheduleExtension on Schedule {
  Schedule copyWith(
      {double? overDue,
      int? missedPaymentCount,
      InstallmentAmount? loanAmount,
      enums.Currency? currency,
      NextInstallment? nextInstallment,
      List<Installment>? installmentSummary,
      ApplicableFees? applicableFees,
      RepaymentDetails? repaymentDetails,
      double? interestRate,
      InstallmentAmount? currentInstallmentDue}) {
    return Schedule(
        overDue: overDue ?? this.overDue,
        missedPaymentCount: missedPaymentCount ?? this.missedPaymentCount,
        loanAmount: loanAmount ?? this.loanAmount,
        currency: currency ?? this.currency,
        nextInstallment: nextInstallment ?? this.nextInstallment,
        installmentSummary: installmentSummary ?? this.installmentSummary,
        applicableFees: applicableFees ?? this.applicableFees,
        repaymentDetails: repaymentDetails ?? this.repaymentDetails,
        interestRate: interestRate ?? this.interestRate,
        currentInstallmentDue:
            currentInstallmentDue ?? this.currentInstallmentDue);
  }

  Schedule copyWithWrapped(
      {Wrapped<double?>? overDue,
      Wrapped<int?>? missedPaymentCount,
      Wrapped<InstallmentAmount?>? loanAmount,
      Wrapped<enums.Currency?>? currency,
      Wrapped<NextInstallment?>? nextInstallment,
      Wrapped<List<Installment>?>? installmentSummary,
      Wrapped<ApplicableFees?>? applicableFees,
      Wrapped<RepaymentDetails?>? repaymentDetails,
      Wrapped<double?>? interestRate,
      Wrapped<InstallmentAmount?>? currentInstallmentDue}) {
    return Schedule(
        overDue: (overDue != null ? overDue.value : this.overDue),
        missedPaymentCount: (missedPaymentCount != null
            ? missedPaymentCount.value
            : this.missedPaymentCount),
        loanAmount: (loanAmount != null ? loanAmount.value : this.loanAmount),
        currency: (currency != null ? currency.value : this.currency),
        nextInstallment: (nextInstallment != null
            ? nextInstallment.value
            : this.nextInstallment),
        installmentSummary: (installmentSummary != null
            ? installmentSummary.value
            : this.installmentSummary),
        applicableFees: (applicableFees != null
            ? applicableFees.value
            : this.applicableFees),
        repaymentDetails: (repaymentDetails != null
            ? repaymentDetails.value
            : this.repaymentDetails),
        interestRate:
            (interestRate != null ? interestRate.value : this.interestRate),
        currentInstallmentDue: (currentInstallmentDue != null
            ? currentInstallmentDue.value
            : this.currentInstallmentDue));
  }
}

@JsonSerializable(explicitToJson: true)
class LoanInstallmentDisbursementV2 {
  const LoanInstallmentDisbursementV2({
    required this.amount,
    this.transactionId,
    this.productSubType,
    this.loanPeriod,
    this.firstRepaymentDate,
  });

  factory LoanInstallmentDisbursementV2.fromJson(Map<String, dynamic> json) =>
      _$LoanInstallmentDisbursementV2FromJson(json);

  static const toJsonFactory = _$LoanInstallmentDisbursementV2ToJson;
  Map<String, dynamic> toJson() => _$LoanInstallmentDisbursementV2ToJson(this);

  @JsonKey(name: 'amount', includeIfNull: false)
  final Money amount;
  @JsonKey(name: 'transactionId', includeIfNull: false)
  final String? transactionId;
  @JsonKey(
    name: 'productSubType',
    includeIfNull: false,
    toJson: loanInstallmentDisbursementV2ProductSubTypeNullableToJson,
    fromJson: loanInstallmentDisbursementV2ProductSubTypeNullableFromJson,
  )
  final enums.LoanInstallmentDisbursementV2ProductSubType? productSubType;
  @JsonKey(name: 'loanPeriod', includeIfNull: false)
  final String? loanPeriod;
  @JsonKey(
      name: 'firstRepaymentDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime? firstRepaymentDate;
  static const fromJsonFactory = _$LoanInstallmentDisbursementV2FromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $LoanInstallmentDisbursementV2Extension
    on LoanInstallmentDisbursementV2 {
  LoanInstallmentDisbursementV2 copyWith(
      {Money? amount,
      String? transactionId,
      enums.LoanInstallmentDisbursementV2ProductSubType? productSubType,
      String? loanPeriod,
      DateTime? firstRepaymentDate}) {
    return LoanInstallmentDisbursementV2(
        amount: amount ?? this.amount,
        transactionId: transactionId ?? this.transactionId,
        productSubType: productSubType ?? this.productSubType,
        loanPeriod: loanPeriod ?? this.loanPeriod,
        firstRepaymentDate: firstRepaymentDate ?? this.firstRepaymentDate);
  }

  LoanInstallmentDisbursementV2 copyWithWrapped(
      {Wrapped<Money>? amount,
      Wrapped<String?>? transactionId,
      Wrapped<enums.LoanInstallmentDisbursementV2ProductSubType?>?
          productSubType,
      Wrapped<String?>? loanPeriod,
      Wrapped<DateTime?>? firstRepaymentDate}) {
    return LoanInstallmentDisbursementV2(
        amount: (amount != null ? amount.value : this.amount),
        transactionId:
            (transactionId != null ? transactionId.value : this.transactionId),
        productSubType: (productSubType != null
            ? productSubType.value
            : this.productSubType),
        loanPeriod: (loanPeriod != null ? loanPeriod.value : this.loanPeriod),
        firstRepaymentDate: (firstRepaymentDate != null
            ? firstRepaymentDate.value
            : this.firstRepaymentDate));
  }
}

@JsonSerializable(explicitToJson: true)
class EligibleCreditLimitRequestDto {
  const EligibleCreditLimitRequestDto({
    this.productCode,
    this.amount,
  });

  factory EligibleCreditLimitRequestDto.fromJson(Map<String, dynamic> json) =>
      _$EligibleCreditLimitRequestDtoFromJson(json);

  static const toJsonFactory = _$EligibleCreditLimitRequestDtoToJson;
  Map<String, dynamic> toJson() => _$EligibleCreditLimitRequestDtoToJson(this);

  @JsonKey(
    name: 'productCode',
    includeIfNull: false,
    toJson: eligibleCreditLimitRequestDtoProductCodeNullableToJson,
    fromJson: eligibleCreditLimitRequestDtoProductCodeNullableFromJson,
  )
  final enums.EligibleCreditLimitRequestDtoProductCode? productCode;
  @JsonKey(name: 'amount', includeIfNull: false)
  final Money? amount;
  static const fromJsonFactory = _$EligibleCreditLimitRequestDtoFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EligibleCreditLimitRequestDtoExtension
    on EligibleCreditLimitRequestDto {
  EligibleCreditLimitRequestDto copyWith(
      {enums.EligibleCreditLimitRequestDtoProductCode? productCode,
      Money? amount}) {
    return EligibleCreditLimitRequestDto(
        productCode: productCode ?? this.productCode,
        amount: amount ?? this.amount);
  }

  EligibleCreditLimitRequestDto copyWithWrapped(
      {Wrapped<enums.EligibleCreditLimitRequestDtoProductCode?>? productCode,
      Wrapped<Money?>? amount}) {
    return EligibleCreditLimitRequestDto(
        productCode:
            (productCode != null ? productCode.value : this.productCode),
        amount: (amount != null ? amount.value : this.amount));
  }
}

@JsonSerializable(explicitToJson: true)
class CreditCardLimitDetails {
  const CreditCardLimitDetails({
    required this.maxLimit,
  });

  factory CreditCardLimitDetails.fromJson(Map<String, dynamic> json) =>
      _$CreditCardLimitDetailsFromJson(json);

  static const toJsonFactory = _$CreditCardLimitDetailsToJson;
  Map<String, dynamic> toJson() => _$CreditCardLimitDetailsToJson(this);

  @JsonKey(name: 'maxLimit', includeIfNull: false)
  final Money maxLimit;
  static const fromJsonFactory = _$CreditCardLimitDetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CreditCardLimitDetailsExtension on CreditCardLimitDetails {
  CreditCardLimitDetails copyWith({Money? maxLimit}) {
    return CreditCardLimitDetails(maxLimit: maxLimit ?? this.maxLimit);
  }

  CreditCardLimitDetails copyWithWrapped({Wrapped<Money>? maxLimit}) {
    return CreditCardLimitDetails(
        maxLimit: (maxLimit != null ? maxLimit.value : this.maxLimit));
  }
}

@JsonSerializable(explicitToJson: true)
class EligibleCreditLimitResponseDto {
  const EligibleCreditLimitResponseDto({
    this.creditCardLimit,
  });

  factory EligibleCreditLimitResponseDto.fromJson(Map<String, dynamic> json) =>
      _$EligibleCreditLimitResponseDtoFromJson(json);

  static const toJsonFactory = _$EligibleCreditLimitResponseDtoToJson;
  Map<String, dynamic> toJson() => _$EligibleCreditLimitResponseDtoToJson(this);

  @JsonKey(name: 'creditCardLimit', includeIfNull: false)
  final CreditCardLimitDetails? creditCardLimit;
  static const fromJsonFactory = _$EligibleCreditLimitResponseDtoFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EligibleCreditLimitResponseDtoExtension
    on EligibleCreditLimitResponseDto {
  EligibleCreditLimitResponseDto copyWith(
      {CreditCardLimitDetails? creditCardLimit}) {
    return EligibleCreditLimitResponseDto(
        creditCardLimit: creditCardLimit ?? this.creditCardLimit);
  }

  EligibleCreditLimitResponseDto copyWithWrapped(
      {Wrapped<CreditCardLimitDetails?>? creditCardLimit}) {
    return EligibleCreditLimitResponseDto(
        creditCardLimit: (creditCardLimit != null
            ? creditCardLimit.value
            : this.creditCardLimit));
  }
}

@JsonSerializable(explicitToJson: true)
class StatementFilter {
  const StatementFilter({
    required this.accountId,
    required this.dateFrom,
    required this.dateTo,
  });

  factory StatementFilter.fromJson(Map<String, dynamic> json) =>
      _$StatementFilterFromJson(json);

  static const toJsonFactory = _$StatementFilterToJson;
  Map<String, dynamic> toJson() => _$StatementFilterToJson(this);

  @JsonKey(name: 'accountId', includeIfNull: false)
  final String accountId;
  @JsonKey(name: 'dateFrom', includeIfNull: false, toJson: _dateToJson)
  final DateTime dateFrom;
  @JsonKey(name: 'dateTo', includeIfNull: false, toJson: _dateToJson)
  final DateTime dateTo;
  static const fromJsonFactory = _$StatementFilterFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $StatementFilterExtension on StatementFilter {
  StatementFilter copyWith(
      {String? accountId, DateTime? dateFrom, DateTime? dateTo}) {
    return StatementFilter(
        accountId: accountId ?? this.accountId,
        dateFrom: dateFrom ?? this.dateFrom,
        dateTo: dateTo ?? this.dateTo);
  }

  StatementFilter copyWithWrapped(
      {Wrapped<String>? accountId,
      Wrapped<DateTime>? dateFrom,
      Wrapped<DateTime>? dateTo}) {
    return StatementFilter(
        accountId: (accountId != null ? accountId.value : this.accountId),
        dateFrom: (dateFrom != null ? dateFrom.value : this.dateFrom),
        dateTo: (dateTo != null ? dateTo.value : this.dateTo));
  }
}

@JsonSerializable(explicitToJson: true)
class ReferralRequestDTO {
  const ReferralRequestDTO({
    required this.referralCode,
    required this.termsAndConditions,
  });

  factory ReferralRequestDTO.fromJson(Map<String, dynamic> json) =>
      _$ReferralRequestDTOFromJson(json);

  static const toJsonFactory = _$ReferralRequestDTOToJson;
  Map<String, dynamic> toJson() => _$ReferralRequestDTOToJson(this);

  @JsonKey(name: 'referralCode', includeIfNull: false)
  final String referralCode;
  @JsonKey(name: 'termsAndConditions', includeIfNull: false)
  final LocalizedDocumentLink termsAndConditions;
  static const fromJsonFactory = _$ReferralRequestDTOFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ReferralRequestDTOExtension on ReferralRequestDTO {
  ReferralRequestDTO copyWith(
      {String? referralCode, LocalizedDocumentLink? termsAndConditions}) {
    return ReferralRequestDTO(
        referralCode: referralCode ?? this.referralCode,
        termsAndConditions: termsAndConditions ?? this.termsAndConditions);
  }

  ReferralRequestDTO copyWithWrapped(
      {Wrapped<String>? referralCode,
      Wrapped<LocalizedDocumentLink>? termsAndConditions}) {
    return ReferralRequestDTO(
        referralCode:
            (referralCode != null ? referralCode.value : this.referralCode),
        termsAndConditions: (termsAndConditions != null
            ? termsAndConditions.value
            : this.termsAndConditions));
  }
}

@JsonSerializable(explicitToJson: true)
class ProblemDetail {
  const ProblemDetail({
    this.type,
    this.title,
    this.status,
    this.detail,
    this.instance,
    this.properties,
  });

  factory ProblemDetail.fromJson(Map<String, dynamic> json) =>
      _$ProblemDetailFromJson(json);

  static const toJsonFactory = _$ProblemDetailToJson;
  Map<String, dynamic> toJson() => _$ProblemDetailToJson(this);

  @JsonKey(name: 'type', includeIfNull: false)
  final String? type;
  @JsonKey(name: 'title', includeIfNull: false)
  final String? title;
  @JsonKey(name: 'status', includeIfNull: false)
  final int? status;
  @JsonKey(name: 'detail', includeIfNull: false)
  final String? detail;
  @JsonKey(name: 'instance', includeIfNull: false)
  final String? instance;
  @JsonKey(name: 'properties', includeIfNull: false)
  final Map<String, dynamic>? properties;
  static const fromJsonFactory = _$ProblemDetailFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ProblemDetailExtension on ProblemDetail {
  ProblemDetail copyWith(
      {String? type,
      String? title,
      int? status,
      String? detail,
      String? instance,
      Map<String, dynamic>? properties}) {
    return ProblemDetail(
        type: type ?? this.type,
        title: title ?? this.title,
        status: status ?? this.status,
        detail: detail ?? this.detail,
        instance: instance ?? this.instance,
        properties: properties ?? this.properties);
  }

  ProblemDetail copyWithWrapped(
      {Wrapped<String?>? type,
      Wrapped<String?>? title,
      Wrapped<int?>? status,
      Wrapped<String?>? detail,
      Wrapped<String?>? instance,
      Wrapped<Map<String, dynamic>?>? properties}) {
    return ProblemDetail(
        type: (type != null ? type.value : this.type),
        title: (title != null ? title.value : this.title),
        status: (status != null ? status.value : this.status),
        detail: (detail != null ? detail.value : this.detail),
        instance: (instance != null ? instance.value : this.instance),
        properties: (properties != null ? properties.value : this.properties));
  }
}

@JsonSerializable(explicitToJson: true)
class SubmitReferralResponse {
  const SubmitReferralResponse({
    required this.referralCode,
    required this.anchorBusinessName,
    this.multiUserRequestId,
    this.domainId,
  });

  factory SubmitReferralResponse.fromJson(Map<String, dynamic> json) =>
      _$SubmitReferralResponseFromJson(json);

  static const toJsonFactory = _$SubmitReferralResponseToJson;
  Map<String, dynamic> toJson() => _$SubmitReferralResponseToJson(this);

  @JsonKey(name: 'referralCode', includeIfNull: false)
  final String referralCode;
  @JsonKey(name: 'anchorBusinessName', includeIfNull: false)
  final String anchorBusinessName;
  @JsonKey(name: 'multiUserRequestId', includeIfNull: false)
  final String? multiUserRequestId;
  @JsonKey(name: 'domainId', includeIfNull: false)
  final String? domainId;
  static const fromJsonFactory = _$SubmitReferralResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $SubmitReferralResponseExtension on SubmitReferralResponse {
  SubmitReferralResponse copyWith(
      {String? referralCode,
      String? anchorBusinessName,
      String? multiUserRequestId,
      String? domainId}) {
    return SubmitReferralResponse(
        referralCode: referralCode ?? this.referralCode,
        anchorBusinessName: anchorBusinessName ?? this.anchorBusinessName,
        multiUserRequestId: multiUserRequestId ?? this.multiUserRequestId,
        domainId: domainId ?? this.domainId);
  }

  SubmitReferralResponse copyWithWrapped(
      {Wrapped<String>? referralCode,
      Wrapped<String>? anchorBusinessName,
      Wrapped<String?>? multiUserRequestId,
      Wrapped<String?>? domainId}) {
    return SubmitReferralResponse(
        referralCode:
            (referralCode != null ? referralCode.value : this.referralCode),
        anchorBusinessName: (anchorBusinessName != null
            ? anchorBusinessName.value
            : this.anchorBusinessName),
        multiUserRequestId: (multiUserRequestId != null
            ? multiUserRequestId.value
            : this.multiUserRequestId),
        domainId: (domainId != null ? domainId.value : this.domainId));
  }
}

@JsonSerializable(explicitToJson: true)
class AgreementInputDto {
  const AgreementInputDto({
    this.productType,
  });

  factory AgreementInputDto.fromJson(Map<String, dynamic> json) =>
      _$AgreementInputDtoFromJson(json);

  static const toJsonFactory = _$AgreementInputDtoToJson;
  Map<String, dynamic> toJson() => _$AgreementInputDtoToJson(this);

  @JsonKey(
    name: 'productType',
    includeIfNull: false,
    toJson: productTypeNullableToJson,
    fromJson: productTypeNullableFromJson,
  )
  final enums.ProductType? productType;
  static const fromJsonFactory = _$AgreementInputDtoFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AgreementInputDtoExtension on AgreementInputDto {
  AgreementInputDto copyWith({enums.ProductType? productType}) {
    return AgreementInputDto(productType: productType ?? this.productType);
  }

  AgreementInputDto copyWithWrapped(
      {Wrapped<enums.ProductType?>? productType}) {
    return AgreementInputDto(
        productType:
            (productType != null ? productType.value : this.productType));
  }
}

@JsonSerializable(explicitToJson: true)
class MultiUserCreateRequestDTO {
  const MultiUserCreateRequestDTO({
    required this.individualId,
    required this.email,
    required this.domainType,
    required this.domainId,
    required this.scfRequestSummary,
  });

  factory MultiUserCreateRequestDTO.fromJson(Map<String, dynamic> json) =>
      _$MultiUserCreateRequestDTOFromJson(json);

  static const toJsonFactory = _$MultiUserCreateRequestDTOToJson;
  Map<String, dynamic> toJson() => _$MultiUserCreateRequestDTOToJson(this);

  @JsonKey(name: 'individualId', includeIfNull: false)
  final String individualId;
  @JsonKey(name: 'email', includeIfNull: false)
  final String email;
  @JsonKey(
    name: 'domainType',
    includeIfNull: false,
    toJson: multiUserCreateRequestDTODomainTypeToJson,
    fromJson: multiUserCreateRequestDTODomainTypeFromJson,
  )
  final enums.MultiUserCreateRequestDTODomainType domainType;
  @JsonKey(name: 'domainId', includeIfNull: false)
  final String domainId;
  @JsonKey(name: 'scfRequestSummary', includeIfNull: false)
  final ScfRequestSummary scfRequestSummary;
  static const fromJsonFactory = _$MultiUserCreateRequestDTOFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MultiUserCreateRequestDTOExtension on MultiUserCreateRequestDTO {
  MultiUserCreateRequestDTO copyWith(
      {String? individualId,
      String? email,
      enums.MultiUserCreateRequestDTODomainType? domainType,
      String? domainId,
      ScfRequestSummary? scfRequestSummary}) {
    return MultiUserCreateRequestDTO(
        individualId: individualId ?? this.individualId,
        email: email ?? this.email,
        domainType: domainType ?? this.domainType,
        domainId: domainId ?? this.domainId,
        scfRequestSummary: scfRequestSummary ?? this.scfRequestSummary);
  }

  MultiUserCreateRequestDTO copyWithWrapped(
      {Wrapped<String>? individualId,
      Wrapped<String>? email,
      Wrapped<enums.MultiUserCreateRequestDTODomainType>? domainType,
      Wrapped<String>? domainId,
      Wrapped<ScfRequestSummary>? scfRequestSummary}) {
    return MultiUserCreateRequestDTO(
        individualId:
            (individualId != null ? individualId.value : this.individualId),
        email: (email != null ? email.value : this.email),
        domainType: (domainType != null ? domainType.value : this.domainType),
        domainId: (domainId != null ? domainId.value : this.domainId),
        scfRequestSummary: (scfRequestSummary != null
            ? scfRequestSummary.value
            : this.scfRequestSummary));
  }
}

@JsonSerializable(explicitToJson: true)
class ScfRequestSummary {
  const ScfRequestSummary({
    required this.description,
    this.amount,
    this.currency,
  });

  factory ScfRequestSummary.fromJson(Map<String, dynamic> json) =>
      _$ScfRequestSummaryFromJson(json);

  static const toJsonFactory = _$ScfRequestSummaryToJson;
  Map<String, dynamic> toJson() => _$ScfRequestSummaryToJson(this);

  @JsonKey(name: 'description', includeIfNull: false)
  final String description;
  @JsonKey(name: 'amount', includeIfNull: false)
  final double? amount;
  @JsonKey(
    name: 'currency',
    includeIfNull: false,
    toJson: currencyNullableToJson,
    fromJson: currencyNullableFromJson,
  )
  final enums.Currency? currency;
  static const fromJsonFactory = _$ScfRequestSummaryFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ScfRequestSummaryExtension on ScfRequestSummary {
  ScfRequestSummary copyWith(
      {String? description, double? amount, enums.Currency? currency}) {
    return ScfRequestSummary(
        description: description ?? this.description,
        amount: amount ?? this.amount,
        currency: currency ?? this.currency);
  }

  ScfRequestSummary copyWithWrapped(
      {Wrapped<String>? description,
      Wrapped<double?>? amount,
      Wrapped<enums.Currency?>? currency}) {
    return ScfRequestSummary(
        description:
            (description != null ? description.value : this.description),
        amount: (amount != null ? amount.value : this.amount),
        currency: (currency != null ? currency.value : this.currency));
  }
}

@JsonSerializable(explicitToJson: true)
class ApproverDTO {
  const ApproverDTO({
    this.firstName,
    this.lastName,
  });

  factory ApproverDTO.fromJson(Map<String, dynamic> json) =>
      _$ApproverDTOFromJson(json);

  static const toJsonFactory = _$ApproverDTOToJson;
  Map<String, dynamic> toJson() => _$ApproverDTOToJson(this);

  @JsonKey(name: 'firstName', includeIfNull: false)
  final String? firstName;
  @JsonKey(name: 'lastName', includeIfNull: false)
  final String? lastName;
  static const fromJsonFactory = _$ApproverDTOFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ApproverDTOExtension on ApproverDTO {
  ApproverDTO copyWith({String? firstName, String? lastName}) {
    return ApproverDTO(
        firstName: firstName ?? this.firstName,
        lastName: lastName ?? this.lastName);
  }

  ApproverDTO copyWithWrapped(
      {Wrapped<String?>? firstName, Wrapped<String?>? lastName}) {
    return ApproverDTO(
        firstName: (firstName != null ? firstName.value : this.firstName),
        lastName: (lastName != null ? lastName.value : this.lastName));
  }
}

@JsonSerializable(explicitToJson: true)
class ApproverDetailsDTO {
  const ApproverDetailsDTO({
    this.approver,
    this.status,
    this.note,
  });

  factory ApproverDetailsDTO.fromJson(Map<String, dynamic> json) =>
      _$ApproverDetailsDTOFromJson(json);

  static const toJsonFactory = _$ApproverDetailsDTOToJson;
  Map<String, dynamic> toJson() => _$ApproverDetailsDTOToJson(this);

  @JsonKey(name: 'approver', includeIfNull: false)
  final ApproverDTO? approver;
  @JsonKey(name: 'status', includeIfNull: false)
  final String? status;
  @JsonKey(name: 'note', includeIfNull: false)
  final String? note;
  static const fromJsonFactory = _$ApproverDetailsDTOFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ApproverDetailsDTOExtension on ApproverDetailsDTO {
  ApproverDetailsDTO copyWith(
      {ApproverDTO? approver, String? status, String? note}) {
    return ApproverDetailsDTO(
        approver: approver ?? this.approver,
        status: status ?? this.status,
        note: note ?? this.note);
  }

  ApproverDetailsDTO copyWithWrapped(
      {Wrapped<ApproverDTO?>? approver,
      Wrapped<String?>? status,
      Wrapped<String?>? note}) {
    return ApproverDetailsDTO(
        approver: (approver != null ? approver.value : this.approver),
        status: (status != null ? status.value : this.status),
        note: (note != null ? note.value : this.note));
  }
}

@JsonSerializable(explicitToJson: true)
class MultiUserResponseDTO {
  const MultiUserResponseDTO({
    this.domainType,
    this.id,
    this.status,
    this.rejectionNotes,
    this.domainId,
    this.authorId,
    this.creationFlow,
    this.createdAt,
    this.updatedAt,
    this.twoFaRequired,
    this.approvers,
    this.approvals,
    this.scfSupplierInviteSummary,
    this.scfInvoiceUploadSummary,
    this.scfInvoiceRepaymentInitiateSummary,
  });

  factory MultiUserResponseDTO.fromJson(Map<String, dynamic> json) =>
      _$MultiUserResponseDTOFromJson(json);

  static const toJsonFactory = _$MultiUserResponseDTOToJson;
  Map<String, dynamic> toJson() => _$MultiUserResponseDTOToJson(this);

  @JsonKey(
    name: 'domainType',
    includeIfNull: false,
    toJson: multiUserResponseDTODomainTypeNullableToJson,
    fromJson: multiUserResponseDTODomainTypeNullableFromJson,
  )
  final enums.MultiUserResponseDTODomainType? domainType;
  @JsonKey(name: 'id', includeIfNull: false)
  final String? id;
  @JsonKey(
    name: 'status',
    includeIfNull: false,
    toJson: multiUserResponseDTOStatusNullableToJson,
    fromJson: multiUserResponseDTOStatusNullableFromJson,
  )
  final enums.MultiUserResponseDTOStatus? status;
  @JsonKey(name: 'rejectionNotes', includeIfNull: false)
  final String? rejectionNotes;
  @JsonKey(name: 'domainId', includeIfNull: false)
  final String? domainId;
  @JsonKey(name: 'authorId', includeIfNull: false)
  final String? authorId;
  @JsonKey(name: 'creationFlow', includeIfNull: false)
  final String? creationFlow;
  @JsonKey(name: 'createdAt', includeIfNull: false)
  final String? createdAt;
  @JsonKey(name: 'updatedAt', includeIfNull: false)
  final String? updatedAt;
  @JsonKey(name: 'twoFaRequired', includeIfNull: false)
  final bool? twoFaRequired;
  @JsonKey(
      name: 'approvers', includeIfNull: false, defaultValue: <ApproverDTO>[])
  final List<ApproverDTO>? approvers;
  @JsonKey(
      name: 'approvals',
      includeIfNull: false,
      defaultValue: <ApproverDetailsDTO>[])
  final List<ApproverDetailsDTO>? approvals;
  @JsonKey(name: 'scfSupplierInviteSummary', includeIfNull: false)
  final ScfRequestSummary? scfSupplierInviteSummary;
  @JsonKey(name: 'scfInvoiceUploadSummary', includeIfNull: false)
  final ScfRequestSummary? scfInvoiceUploadSummary;
  @JsonKey(name: 'scfInvoiceRepaymentInitiateSummary', includeIfNull: false)
  final ScfRequestSummary? scfInvoiceRepaymentInitiateSummary;
  static const fromJsonFactory = _$MultiUserResponseDTOFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MultiUserResponseDTOExtension on MultiUserResponseDTO {
  MultiUserResponseDTO copyWith(
      {enums.MultiUserResponseDTODomainType? domainType,
      String? id,
      enums.MultiUserResponseDTOStatus? status,
      String? rejectionNotes,
      String? domainId,
      String? authorId,
      String? creationFlow,
      String? createdAt,
      String? updatedAt,
      bool? twoFaRequired,
      List<ApproverDTO>? approvers,
      List<ApproverDetailsDTO>? approvals,
      ScfRequestSummary? scfSupplierInviteSummary,
      ScfRequestSummary? scfInvoiceUploadSummary,
      ScfRequestSummary? scfInvoiceRepaymentInitiateSummary}) {
    return MultiUserResponseDTO(
        domainType: domainType ?? this.domainType,
        id: id ?? this.id,
        status: status ?? this.status,
        rejectionNotes: rejectionNotes ?? this.rejectionNotes,
        domainId: domainId ?? this.domainId,
        authorId: authorId ?? this.authorId,
        creationFlow: creationFlow ?? this.creationFlow,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        twoFaRequired: twoFaRequired ?? this.twoFaRequired,
        approvers: approvers ?? this.approvers,
        approvals: approvals ?? this.approvals,
        scfSupplierInviteSummary:
            scfSupplierInviteSummary ?? this.scfSupplierInviteSummary,
        scfInvoiceUploadSummary:
            scfInvoiceUploadSummary ?? this.scfInvoiceUploadSummary,
        scfInvoiceRepaymentInitiateSummary:
            scfInvoiceRepaymentInitiateSummary ??
                this.scfInvoiceRepaymentInitiateSummary);
  }

  MultiUserResponseDTO copyWithWrapped(
      {Wrapped<enums.MultiUserResponseDTODomainType?>? domainType,
      Wrapped<String?>? id,
      Wrapped<enums.MultiUserResponseDTOStatus?>? status,
      Wrapped<String?>? rejectionNotes,
      Wrapped<String?>? domainId,
      Wrapped<String?>? authorId,
      Wrapped<String?>? creationFlow,
      Wrapped<String?>? createdAt,
      Wrapped<String?>? updatedAt,
      Wrapped<bool?>? twoFaRequired,
      Wrapped<List<ApproverDTO>?>? approvers,
      Wrapped<List<ApproverDetailsDTO>?>? approvals,
      Wrapped<ScfRequestSummary?>? scfSupplierInviteSummary,
      Wrapped<ScfRequestSummary?>? scfInvoiceUploadSummary,
      Wrapped<ScfRequestSummary?>? scfInvoiceRepaymentInitiateSummary}) {
    return MultiUserResponseDTO(
        domainType: (domainType != null ? domainType.value : this.domainType),
        id: (id != null ? id.value : this.id),
        status: (status != null ? status.value : this.status),
        rejectionNotes: (rejectionNotes != null
            ? rejectionNotes.value
            : this.rejectionNotes),
        domainId: (domainId != null ? domainId.value : this.domainId),
        authorId: (authorId != null ? authorId.value : this.authorId),
        creationFlow:
            (creationFlow != null ? creationFlow.value : this.creationFlow),
        createdAt: (createdAt != null ? createdAt.value : this.createdAt),
        updatedAt: (updatedAt != null ? updatedAt.value : this.updatedAt),
        twoFaRequired:
            (twoFaRequired != null ? twoFaRequired.value : this.twoFaRequired),
        approvers: (approvers != null ? approvers.value : this.approvers),
        approvals: (approvals != null ? approvals.value : this.approvals),
        scfSupplierInviteSummary: (scfSupplierInviteSummary != null
            ? scfSupplierInviteSummary.value
            : this.scfSupplierInviteSummary),
        scfInvoiceUploadSummary: (scfInvoiceUploadSummary != null
            ? scfInvoiceUploadSummary.value
            : this.scfInvoiceUploadSummary),
        scfInvoiceRepaymentInitiateSummary:
            (scfInvoiceRepaymentInitiateSummary != null
                ? scfInvoiceRepaymentInitiateSummary.value
                : this.scfInvoiceRepaymentInitiateSummary));
  }
}

@JsonSerializable(explicitToJson: true)
class MultiUserRejectRequestDTO {
  const MultiUserRejectRequestDTO({
    required this.individualId,
    this.note,
  });

  factory MultiUserRejectRequestDTO.fromJson(Map<String, dynamic> json) =>
      _$MultiUserRejectRequestDTOFromJson(json);

  static const toJsonFactory = _$MultiUserRejectRequestDTOToJson;
  Map<String, dynamic> toJson() => _$MultiUserRejectRequestDTOToJson(this);

  @JsonKey(name: 'individualId', includeIfNull: false)
  final String individualId;
  @JsonKey(name: 'note', includeIfNull: false)
  final String? note;
  static const fromJsonFactory = _$MultiUserRejectRequestDTOFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MultiUserRejectRequestDTOExtension on MultiUserRejectRequestDTO {
  MultiUserRejectRequestDTO copyWith({String? individualId, String? note}) {
    return MultiUserRejectRequestDTO(
        individualId: individualId ?? this.individualId,
        note: note ?? this.note);
  }

  MultiUserRejectRequestDTO copyWithWrapped(
      {Wrapped<String>? individualId, Wrapped<String?>? note}) {
    return MultiUserRejectRequestDTO(
        individualId:
            (individualId != null ? individualId.value : this.individualId),
        note: (note != null ? note.value : this.note));
  }
}

@JsonSerializable(explicitToJson: true)
class MultiUserApproveRequestDTO {
  const MultiUserApproveRequestDTO({
    required this.individualId,
  });

  factory MultiUserApproveRequestDTO.fromJson(Map<String, dynamic> json) =>
      _$MultiUserApproveRequestDTOFromJson(json);

  static const toJsonFactory = _$MultiUserApproveRequestDTOToJson;
  Map<String, dynamic> toJson() => _$MultiUserApproveRequestDTOToJson(this);

  @JsonKey(name: 'individualId', includeIfNull: false)
  final String individualId;
  static const fromJsonFactory = _$MultiUserApproveRequestDTOFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MultiUserApproveRequestDTOExtension on MultiUserApproveRequestDTO {
  MultiUserApproveRequestDTO copyWith({String? individualId}) {
    return MultiUserApproveRequestDTO(
        individualId: individualId ?? this.individualId);
  }

  MultiUserApproveRequestDTO copyWithWrapped({Wrapped<String>? individualId}) {
    return MultiUserApproveRequestDTO(
        individualId:
            (individualId != null ? individualId.value : this.individualId));
  }
}

@JsonSerializable(explicitToJson: true)
class LoanInstallmentDisbursement {
  const LoanInstallmentDisbursement({
    required this.amount,
  });

  factory LoanInstallmentDisbursement.fromJson(Map<String, dynamic> json) =>
      _$LoanInstallmentDisbursementFromJson(json);

  static const toJsonFactory = _$LoanInstallmentDisbursementToJson;
  Map<String, dynamic> toJson() => _$LoanInstallmentDisbursementToJson(this);

  @JsonKey(name: 'amount', includeIfNull: false)
  final Money amount;
  static const fromJsonFactory = _$LoanInstallmentDisbursementFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $LoanInstallmentDisbursementExtension on LoanInstallmentDisbursement {
  LoanInstallmentDisbursement copyWith({Money? amount}) {
    return LoanInstallmentDisbursement(amount: amount ?? this.amount);
  }

  LoanInstallmentDisbursement copyWithWrapped({Wrapped<Money>? amount}) {
    return LoanInstallmentDisbursement(
        amount: (amount != null ? amount.value : this.amount));
  }
}

@JsonSerializable(explicitToJson: true)
class InternalReferralRequestDTO {
  const InternalReferralRequestDTO({
    required this.referralCode,
    required this.individualId,
    required this.termsAndConditions,
  });

  factory InternalReferralRequestDTO.fromJson(Map<String, dynamic> json) =>
      _$InternalReferralRequestDTOFromJson(json);

  static const toJsonFactory = _$InternalReferralRequestDTOToJson;
  Map<String, dynamic> toJson() => _$InternalReferralRequestDTOToJson(this);

  @JsonKey(name: 'referralCode', includeIfNull: false)
  final String referralCode;
  @JsonKey(name: 'individualId', includeIfNull: false)
  final String individualId;
  @JsonKey(name: 'termsAndConditions', includeIfNull: false)
  final LocalizedDocumentLink termsAndConditions;
  static const fromJsonFactory = _$InternalReferralRequestDTOFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $InternalReferralRequestDTOExtension on InternalReferralRequestDTO {
  InternalReferralRequestDTO copyWith(
      {String? referralCode,
      String? individualId,
      LocalizedDocumentLink? termsAndConditions}) {
    return InternalReferralRequestDTO(
        referralCode: referralCode ?? this.referralCode,
        individualId: individualId ?? this.individualId,
        termsAndConditions: termsAndConditions ?? this.termsAndConditions);
  }

  InternalReferralRequestDTO copyWithWrapped(
      {Wrapped<String>? referralCode,
      Wrapped<String>? individualId,
      Wrapped<LocalizedDocumentLink>? termsAndConditions}) {
    return InternalReferralRequestDTO(
        referralCode:
            (referralCode != null ? referralCode.value : this.referralCode),
        individualId:
            (individualId != null ? individualId.value : this.individualId),
        termsAndConditions: (termsAndConditions != null
            ? termsAndConditions.value
            : this.termsAndConditions));
  }
}

@JsonSerializable(explicitToJson: true)
class Data {
  const Data({
    this.requestType,
    this.requestId,
    this.domainId,
    this.authorId,
    this.businessId,
    this.domainType,
  });

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  static const toJsonFactory = _$DataToJson;
  Map<String, dynamic> toJson() => _$DataToJson(this);

  @JsonKey(name: 'RequestType', includeIfNull: false)
  final String? requestType;
  @JsonKey(name: 'RequestId', includeIfNull: false)
  final String? requestId;
  @JsonKey(name: 'DomainId', includeIfNull: false)
  final String? domainId;
  @JsonKey(name: 'AuthorId', includeIfNull: false)
  final String? authorId;
  @JsonKey(name: 'BusinessId', includeIfNull: false)
  final String? businessId;
  @JsonKey(name: 'DomainType', includeIfNull: false)
  final String? domainType;
  static const fromJsonFactory = _$DataFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $DataExtension on Data {
  Data copyWith(
      {String? requestType,
      String? requestId,
      String? domainId,
      String? authorId,
      String? businessId,
      String? domainType}) {
    return Data(
        requestType: requestType ?? this.requestType,
        requestId: requestId ?? this.requestId,
        domainId: domainId ?? this.domainId,
        authorId: authorId ?? this.authorId,
        businessId: businessId ?? this.businessId,
        domainType: domainType ?? this.domainType);
  }

  Data copyWithWrapped(
      {Wrapped<String?>? requestType,
      Wrapped<String?>? requestId,
      Wrapped<String?>? domainId,
      Wrapped<String?>? authorId,
      Wrapped<String?>? businessId,
      Wrapped<String?>? domainType}) {
    return Data(
        requestType:
            (requestType != null ? requestType.value : this.requestType),
        requestId: (requestId != null ? requestId.value : this.requestId),
        domainId: (domainId != null ? domainId.value : this.domainId),
        authorId: (authorId != null ? authorId.value : this.authorId),
        businessId: (businessId != null ? businessId.value : this.businessId),
        domainType: (domainType != null ? domainType.value : this.domainType));
  }
}

@JsonSerializable(explicitToJson: true)
class Header {
  const Header({
    this.schemaVersion,
    this.eventId,
    this.environment,
    this.eventType,
    this.eventName,
    this.correlationId,
    this.sourceName,
    this.sourceType,
    this.channel,
    this.tenant,
    this.sessionId,
    this.product,
    this.eventTimeUTC,
    this.sourceTimeUTC,
  });

  factory Header.fromJson(Map<String, dynamic> json) => _$HeaderFromJson(json);

  static const toJsonFactory = _$HeaderToJson;
  Map<String, dynamic> toJson() => _$HeaderToJson(this);

  @JsonKey(name: 'SchemaVersion', includeIfNull: false)
  final String? schemaVersion;
  @JsonKey(name: 'EventId', includeIfNull: false)
  final String? eventId;
  @JsonKey(name: 'Environment', includeIfNull: false)
  final String? environment;
  @JsonKey(name: 'EventType', includeIfNull: false)
  final String? eventType;
  @JsonKey(name: 'EventName', includeIfNull: false)
  final String? eventName;
  @JsonKey(name: 'CorrelationId', includeIfNull: false)
  final String? correlationId;
  @JsonKey(name: 'SourceName', includeIfNull: false)
  final String? sourceName;
  @JsonKey(name: 'SourceType', includeIfNull: false)
  final String? sourceType;
  @JsonKey(name: 'Channel', includeIfNull: false)
  final String? channel;
  @JsonKey(name: 'Tenant', includeIfNull: false)
  final String? tenant;
  @JsonKey(name: 'SessionId', includeIfNull: false)
  final String? sessionId;
  @JsonKey(name: 'Product', includeIfNull: false)
  final String? product;
  @JsonKey(name: 'EventTime_UTC', includeIfNull: false)
  final String? eventTimeUTC;
  @JsonKey(name: 'SourceTime_UTC', includeIfNull: false)
  final String? sourceTimeUTC;
  static const fromJsonFactory = _$HeaderFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $HeaderExtension on Header {
  Header copyWith(
      {String? schemaVersion,
      String? eventId,
      String? environment,
      String? eventType,
      String? eventName,
      String? correlationId,
      String? sourceName,
      String? sourceType,
      String? channel,
      String? tenant,
      String? sessionId,
      String? product,
      String? eventTimeUTC,
      String? sourceTimeUTC}) {
    return Header(
        schemaVersion: schemaVersion ?? this.schemaVersion,
        eventId: eventId ?? this.eventId,
        environment: environment ?? this.environment,
        eventType: eventType ?? this.eventType,
        eventName: eventName ?? this.eventName,
        correlationId: correlationId ?? this.correlationId,
        sourceName: sourceName ?? this.sourceName,
        sourceType: sourceType ?? this.sourceType,
        channel: channel ?? this.channel,
        tenant: tenant ?? this.tenant,
        sessionId: sessionId ?? this.sessionId,
        product: product ?? this.product,
        eventTimeUTC: eventTimeUTC ?? this.eventTimeUTC,
        sourceTimeUTC: sourceTimeUTC ?? this.sourceTimeUTC);
  }

  Header copyWithWrapped(
      {Wrapped<String?>? schemaVersion,
      Wrapped<String?>? eventId,
      Wrapped<String?>? environment,
      Wrapped<String?>? eventType,
      Wrapped<String?>? eventName,
      Wrapped<String?>? correlationId,
      Wrapped<String?>? sourceName,
      Wrapped<String?>? sourceType,
      Wrapped<String?>? channel,
      Wrapped<String?>? tenant,
      Wrapped<String?>? sessionId,
      Wrapped<String?>? product,
      Wrapped<String?>? eventTimeUTC,
      Wrapped<String?>? sourceTimeUTC}) {
    return Header(
        schemaVersion:
            (schemaVersion != null ? schemaVersion.value : this.schemaVersion),
        eventId: (eventId != null ? eventId.value : this.eventId),
        environment:
            (environment != null ? environment.value : this.environment),
        eventType: (eventType != null ? eventType.value : this.eventType),
        eventName: (eventName != null ? eventName.value : this.eventName),
        correlationId:
            (correlationId != null ? correlationId.value : this.correlationId),
        sourceName: (sourceName != null ? sourceName.value : this.sourceName),
        sourceType: (sourceType != null ? sourceType.value : this.sourceType),
        channel: (channel != null ? channel.value : this.channel),
        tenant: (tenant != null ? tenant.value : this.tenant),
        sessionId: (sessionId != null ? sessionId.value : this.sessionId),
        product: (product != null ? product.value : this.product),
        eventTimeUTC:
            (eventTimeUTC != null ? eventTimeUTC.value : this.eventTimeUTC),
        sourceTimeUTC:
            (sourceTimeUTC != null ? sourceTimeUTC.value : this.sourceTimeUTC));
  }
}

@JsonSerializable(explicitToJson: true)
class MultiUserEventData {
  const MultiUserEventData({
    this.header,
    this.payload,
  });

  factory MultiUserEventData.fromJson(Map<String, dynamic> json) =>
      _$MultiUserEventDataFromJson(json);

  static const toJsonFactory = _$MultiUserEventDataToJson;
  Map<String, dynamic> toJson() => _$MultiUserEventDataToJson(this);

  @JsonKey(name: 'Header', includeIfNull: false)
  final Header? header;
  @JsonKey(name: 'Payload', includeIfNull: false)
  final Payload? payload;
  static const fromJsonFactory = _$MultiUserEventDataFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MultiUserEventDataExtension on MultiUserEventData {
  MultiUserEventData copyWith({Header? header, Payload? payload}) {
    return MultiUserEventData(
        header: header ?? this.header, payload: payload ?? this.payload);
  }

  MultiUserEventData copyWithWrapped(
      {Wrapped<Header?>? header, Wrapped<Payload?>? payload}) {
    return MultiUserEventData(
        header: (header != null ? header.value : this.header),
        payload: (payload != null ? payload.value : this.payload));
  }
}

@JsonSerializable(explicitToJson: true)
class Payload {
  const Payload({
    this.data,
  });

  factory Payload.fromJson(Map<String, dynamic> json) =>
      _$PayloadFromJson(json);

  static const toJsonFactory = _$PayloadToJson;
  Map<String, dynamic> toJson() => _$PayloadToJson(this);

  @JsonKey(name: 'Data', includeIfNull: false)
  final Data? data;
  static const fromJsonFactory = _$PayloadFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $PayloadExtension on Payload {
  Payload copyWith({Data? data}) {
    return Payload(data: data ?? this.data);
  }

  Payload copyWithWrapped({Wrapped<Data?>? data}) {
    return Payload(data: (data != null ? data.value : this.data));
  }
}

@JsonSerializable(explicitToJson: true)
class EasyCashRepayment {
  const EasyCashRepayment({
    required this.easyCashLoanId,
    required this.depositAccountId,
    required this.amount,
  });

  factory EasyCashRepayment.fromJson(Map<String, dynamic> json) =>
      _$EasyCashRepaymentFromJson(json);

  static const toJsonFactory = _$EasyCashRepaymentToJson;
  Map<String, dynamic> toJson() => _$EasyCashRepaymentToJson(this);

  @JsonKey(name: 'easyCashLoanId', includeIfNull: false)
  final String easyCashLoanId;
  @JsonKey(name: 'depositAccountId', includeIfNull: false)
  final String depositAccountId;
  @JsonKey(name: 'amount', includeIfNull: false)
  final Money amount;
  static const fromJsonFactory = _$EasyCashRepaymentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EasyCashRepaymentExtension on EasyCashRepayment {
  EasyCashRepayment copyWith(
      {String? easyCashLoanId, String? depositAccountId, Money? amount}) {
    return EasyCashRepayment(
        easyCashLoanId: easyCashLoanId ?? this.easyCashLoanId,
        depositAccountId: depositAccountId ?? this.depositAccountId,
        amount: amount ?? this.amount);
  }

  EasyCashRepayment copyWithWrapped(
      {Wrapped<String>? easyCashLoanId,
      Wrapped<String>? depositAccountId,
      Wrapped<Money>? amount}) {
    return EasyCashRepayment(
        easyCashLoanId: (easyCashLoanId != null
            ? easyCashLoanId.value
            : this.easyCashLoanId),
        depositAccountId: (depositAccountId != null
            ? depositAccountId.value
            : this.depositAccountId),
        amount: (amount != null ? amount.value : this.amount));
  }
}

@JsonSerializable(explicitToJson: true)
class EasyCashEvaluateRepayment {
  const EasyCashEvaluateRepayment({
    required this.easyCashLoanId,
    required this.depositAccountId,
    required this.amount,
  });

  factory EasyCashEvaluateRepayment.fromJson(Map<String, dynamic> json) =>
      _$EasyCashEvaluateRepaymentFromJson(json);

  static const toJsonFactory = _$EasyCashEvaluateRepaymentToJson;
  Map<String, dynamic> toJson() => _$EasyCashEvaluateRepaymentToJson(this);

  @JsonKey(name: 'easyCashLoanId', includeIfNull: false)
  final String easyCashLoanId;
  @JsonKey(name: 'depositAccountId', includeIfNull: false)
  final String depositAccountId;
  @JsonKey(name: 'amount', includeIfNull: false)
  final Money amount;
  static const fromJsonFactory = _$EasyCashEvaluateRepaymentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EasyCashEvaluateRepaymentExtension on EasyCashEvaluateRepayment {
  EasyCashEvaluateRepayment copyWith(
      {String? easyCashLoanId, String? depositAccountId, Money? amount}) {
    return EasyCashEvaluateRepayment(
        easyCashLoanId: easyCashLoanId ?? this.easyCashLoanId,
        depositAccountId: depositAccountId ?? this.depositAccountId,
        amount: amount ?? this.amount);
  }

  EasyCashEvaluateRepayment copyWithWrapped(
      {Wrapped<String>? easyCashLoanId,
      Wrapped<String>? depositAccountId,
      Wrapped<Money>? amount}) {
    return EasyCashEvaluateRepayment(
        easyCashLoanId: (easyCashLoanId != null
            ? easyCashLoanId.value
            : this.easyCashLoanId),
        depositAccountId: (depositAccountId != null
            ? depositAccountId.value
            : this.depositAccountId),
        amount: (amount != null ? amount.value : this.amount));
  }
}

@JsonSerializable(explicitToJson: true)
class Balances {
  const Balances({
    required this.availableAmount,
    required this.holdAmount,
    required this.feesBalance,
    required this.principalBalance,
    this.interestBalance,
    this.estimatedFee,
  });

  factory Balances.fromJson(Map<String, dynamic> json) =>
      _$BalancesFromJson(json);

  static const toJsonFactory = _$BalancesToJson;
  Map<String, dynamic> toJson() => _$BalancesToJson(this);

  @JsonKey(name: 'availableAmount', includeIfNull: false)
  final double availableAmount;
  @JsonKey(name: 'holdAmount', includeIfNull: false)
  final double holdAmount;
  @JsonKey(name: 'feesBalance', includeIfNull: false)
  final double feesBalance;
  @JsonKey(name: 'principalBalance', includeIfNull: false)
  final double principalBalance;
  @JsonKey(name: 'interestBalance', includeIfNull: false)
  final double? interestBalance;
  @JsonKey(name: 'estimatedFee', includeIfNull: false)
  final double? estimatedFee;
  static const fromJsonFactory = _$BalancesFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $BalancesExtension on Balances {
  Balances copyWith(
      {double? availableAmount,
      double? holdAmount,
      double? feesBalance,
      double? principalBalance,
      double? interestBalance,
      double? estimatedFee}) {
    return Balances(
        availableAmount: availableAmount ?? this.availableAmount,
        holdAmount: holdAmount ?? this.holdAmount,
        feesBalance: feesBalance ?? this.feesBalance,
        principalBalance: principalBalance ?? this.principalBalance,
        interestBalance: interestBalance ?? this.interestBalance,
        estimatedFee: estimatedFee ?? this.estimatedFee);
  }

  Balances copyWithWrapped(
      {Wrapped<double>? availableAmount,
      Wrapped<double>? holdAmount,
      Wrapped<double>? feesBalance,
      Wrapped<double>? principalBalance,
      Wrapped<double?>? interestBalance,
      Wrapped<double?>? estimatedFee}) {
    return Balances(
        availableAmount: (availableAmount != null
            ? availableAmount.value
            : this.availableAmount),
        holdAmount: (holdAmount != null ? holdAmount.value : this.holdAmount),
        feesBalance:
            (feesBalance != null ? feesBalance.value : this.feesBalance),
        principalBalance: (principalBalance != null
            ? principalBalance.value
            : this.principalBalance),
        interestBalance: (interestBalance != null
            ? interestBalance.value
            : this.interestBalance),
        estimatedFee:
            (estimatedFee != null ? estimatedFee.value : this.estimatedFee));
  }
}

@JsonSerializable(explicitToJson: true)
class EasyCashRepaymentEvaluation {
  const EasyCashRepaymentEvaluation({
    required this.feePerDay,
    required this.totalFees,
    required this.feeSaved,
    this.currency,
    this.balances,
  });

  factory EasyCashRepaymentEvaluation.fromJson(Map<String, dynamic> json) =>
      _$EasyCashRepaymentEvaluationFromJson(json);

  static const toJsonFactory = _$EasyCashRepaymentEvaluationToJson;
  Map<String, dynamic> toJson() => _$EasyCashRepaymentEvaluationToJson(this);

  @JsonKey(name: 'feePerDay', includeIfNull: false)
  final Money feePerDay;
  @JsonKey(name: 'totalFees', includeIfNull: false)
  final Money totalFees;
  @JsonKey(name: 'feeSaved', includeIfNull: false)
  final Money feeSaved;
  @JsonKey(
    name: 'currency',
    includeIfNull: false,
    toJson: currencyNullableToJson,
    fromJson: currencyNullableFromJson,
  )
  final enums.Currency? currency;
  @JsonKey(name: 'balances', includeIfNull: false)
  final Balances? balances;
  static const fromJsonFactory = _$EasyCashRepaymentEvaluationFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EasyCashRepaymentEvaluationExtension on EasyCashRepaymentEvaluation {
  EasyCashRepaymentEvaluation copyWith(
      {Money? feePerDay,
      Money? totalFees,
      Money? feeSaved,
      enums.Currency? currency,
      Balances? balances}) {
    return EasyCashRepaymentEvaluation(
        feePerDay: feePerDay ?? this.feePerDay,
        totalFees: totalFees ?? this.totalFees,
        feeSaved: feeSaved ?? this.feeSaved,
        currency: currency ?? this.currency,
        balances: balances ?? this.balances);
  }

  EasyCashRepaymentEvaluation copyWithWrapped(
      {Wrapped<Money>? feePerDay,
      Wrapped<Money>? totalFees,
      Wrapped<Money>? feeSaved,
      Wrapped<enums.Currency?>? currency,
      Wrapped<Balances?>? balances}) {
    return EasyCashRepaymentEvaluation(
        feePerDay: (feePerDay != null ? feePerDay.value : this.feePerDay),
        totalFees: (totalFees != null ? totalFees.value : this.totalFees),
        feeSaved: (feeSaved != null ? feeSaved.value : this.feeSaved),
        currency: (currency != null ? currency.value : this.currency),
        balances: (balances != null ? balances.value : this.balances));
  }
}

@JsonSerializable(explicitToJson: true)
class EasyCashEvaluateDisbursement {
  const EasyCashEvaluateDisbursement({
    required this.dueDate,
    required this.amount,
    required this.creditCardLoanAccountId,
  });

  factory EasyCashEvaluateDisbursement.fromJson(Map<String, dynamic> json) =>
      _$EasyCashEvaluateDisbursementFromJson(json);

  static const toJsonFactory = _$EasyCashEvaluateDisbursementToJson;
  Map<String, dynamic> toJson() => _$EasyCashEvaluateDisbursementToJson(this);

  @JsonKey(name: 'dueDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime dueDate;
  @JsonKey(name: 'amount', includeIfNull: false)
  final Money amount;
  @JsonKey(name: 'creditCardLoanAccountId', includeIfNull: false)
  final String creditCardLoanAccountId;
  static const fromJsonFactory = _$EasyCashEvaluateDisbursementFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EasyCashEvaluateDisbursementExtension
    on EasyCashEvaluateDisbursement {
  EasyCashEvaluateDisbursement copyWith(
      {DateTime? dueDate, Money? amount, String? creditCardLoanAccountId}) {
    return EasyCashEvaluateDisbursement(
        dueDate: dueDate ?? this.dueDate,
        amount: amount ?? this.amount,
        creditCardLoanAccountId:
            creditCardLoanAccountId ?? this.creditCardLoanAccountId);
  }

  EasyCashEvaluateDisbursement copyWithWrapped(
      {Wrapped<DateTime>? dueDate,
      Wrapped<Money>? amount,
      Wrapped<String>? creditCardLoanAccountId}) {
    return EasyCashEvaluateDisbursement(
        dueDate: (dueDate != null ? dueDate.value : this.dueDate),
        amount: (amount != null ? amount.value : this.amount),
        creditCardLoanAccountId: (creditCardLoanAccountId != null
            ? creditCardLoanAccountId.value
            : this.creditCardLoanAccountId));
  }
}

@JsonSerializable(explicitToJson: true)
class EasyCashDisbursementEvaluation {
  const EasyCashDisbursementEvaluation({
    required this.feePerDay,
    required this.totalFees,
    required this.finalTotalFees,
    required this.currentFeePerDay,
    required this.currentEstimatedTotalFees,
  });

  factory EasyCashDisbursementEvaluation.fromJson(Map<String, dynamic> json) =>
      _$EasyCashDisbursementEvaluationFromJson(json);

  static const toJsonFactory = _$EasyCashDisbursementEvaluationToJson;
  Map<String, dynamic> toJson() => _$EasyCashDisbursementEvaluationToJson(this);

  @JsonKey(name: 'feePerDay', includeIfNull: false)
  final Money feePerDay;
  @JsonKey(name: 'totalFees', includeIfNull: false)
  final Money totalFees;
  @JsonKey(name: 'finalTotalFees', includeIfNull: false)
  final Money finalTotalFees;
  @JsonKey(name: 'currentFeePerDay', includeIfNull: false)
  final Money currentFeePerDay;
  @JsonKey(name: 'currentEstimatedTotalFees', includeIfNull: false)
  final Money currentEstimatedTotalFees;
  static const fromJsonFactory = _$EasyCashDisbursementEvaluationFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EasyCashDisbursementEvaluationExtension
    on EasyCashDisbursementEvaluation {
  EasyCashDisbursementEvaluation copyWith(
      {Money? feePerDay,
      Money? totalFees,
      Money? finalTotalFees,
      Money? currentFeePerDay,
      Money? currentEstimatedTotalFees}) {
    return EasyCashDisbursementEvaluation(
        feePerDay: feePerDay ?? this.feePerDay,
        totalFees: totalFees ?? this.totalFees,
        finalTotalFees: finalTotalFees ?? this.finalTotalFees,
        currentFeePerDay: currentFeePerDay ?? this.currentFeePerDay,
        currentEstimatedTotalFees:
            currentEstimatedTotalFees ?? this.currentEstimatedTotalFees);
  }

  EasyCashDisbursementEvaluation copyWithWrapped(
      {Wrapped<Money>? feePerDay,
      Wrapped<Money>? totalFees,
      Wrapped<Money>? finalTotalFees,
      Wrapped<Money>? currentFeePerDay,
      Wrapped<Money>? currentEstimatedTotalFees}) {
    return EasyCashDisbursementEvaluation(
        feePerDay: (feePerDay != null ? feePerDay.value : this.feePerDay),
        totalFees: (totalFees != null ? totalFees.value : this.totalFees),
        finalTotalFees: (finalTotalFees != null
            ? finalTotalFees.value
            : this.finalTotalFees),
        currentFeePerDay: (currentFeePerDay != null
            ? currentFeePerDay.value
            : this.currentFeePerDay),
        currentEstimatedTotalFees: (currentEstimatedTotalFees != null
            ? currentEstimatedTotalFees.value
            : this.currentEstimatedTotalFees));
  }
}

@JsonSerializable(explicitToJson: true)
class EasyCashDisbursement {
  const EasyCashDisbursement({
    required this.creditCardLoanId,
    required this.amount,
    this.currentAccountId,
    required this.preferences,
  });

  factory EasyCashDisbursement.fromJson(Map<String, dynamic> json) =>
      _$EasyCashDisbursementFromJson(json);

  static const toJsonFactory = _$EasyCashDisbursementToJson;
  Map<String, dynamic> toJson() => _$EasyCashDisbursementToJson(this);

  @JsonKey(name: 'creditCardLoanId', includeIfNull: false)
  final String creditCardLoanId;
  @JsonKey(name: 'amount', includeIfNull: false)
  final Money amount;
  @JsonKey(name: 'currentAccountId', includeIfNull: false)
  final String? currentAccountId;
  @JsonKey(name: 'preferences', includeIfNull: false)
  final Preferences preferences;
  static const fromJsonFactory = _$EasyCashDisbursementFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EasyCashDisbursementExtension on EasyCashDisbursement {
  EasyCashDisbursement copyWith(
      {String? creditCardLoanId,
      Money? amount,
      String? currentAccountId,
      Preferences? preferences}) {
    return EasyCashDisbursement(
        creditCardLoanId: creditCardLoanId ?? this.creditCardLoanId,
        amount: amount ?? this.amount,
        currentAccountId: currentAccountId ?? this.currentAccountId,
        preferences: preferences ?? this.preferences);
  }

  EasyCashDisbursement copyWithWrapped(
      {Wrapped<String>? creditCardLoanId,
      Wrapped<Money>? amount,
      Wrapped<String?>? currentAccountId,
      Wrapped<Preferences>? preferences}) {
    return EasyCashDisbursement(
        creditCardLoanId: (creditCardLoanId != null
            ? creditCardLoanId.value
            : this.creditCardLoanId),
        amount: (amount != null ? amount.value : this.amount),
        currentAccountId: (currentAccountId != null
            ? currentAccountId.value
            : this.currentAccountId),
        preferences:
            (preferences != null ? preferences.value : this.preferences));
  }
}

@JsonSerializable(explicitToJson: true)
class CreateApplicationCommand {
  const CreateApplicationCommand({
    required this.productType,
    this.referralCode,
    this.externalReferenceId,
    this.termsAndConditions,
    this.keyFactStatement,
  });

  factory CreateApplicationCommand.fromJson(Map<String, dynamic> json) =>
      _$CreateApplicationCommandFromJson(json);

  static const toJsonFactory = _$CreateApplicationCommandToJson;
  Map<String, dynamic> toJson() => _$CreateApplicationCommandToJson(this);

  @JsonKey(
    name: 'productType',
    includeIfNull: false,
    toJson: productTypeToJson,
    fromJson: productTypeFromJson,
  )
  final enums.ProductType productType;
  @JsonKey(name: 'referralCode', includeIfNull: false)
  final String? referralCode;
  @JsonKey(name: 'externalReferenceId', includeIfNull: false)
  final String? externalReferenceId;
  @JsonKey(name: 'termsAndConditions', includeIfNull: false)
  final LocalizedDocumentLink? termsAndConditions;
  @JsonKey(name: 'keyFactStatement', includeIfNull: false)
  final LocalizedDocumentLink? keyFactStatement;
  static const fromJsonFactory = _$CreateApplicationCommandFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CreateApplicationCommandExtension on CreateApplicationCommand {
  CreateApplicationCommand copyWith(
      {enums.ProductType? productType,
      String? referralCode,
      String? externalReferenceId,
      LocalizedDocumentLink? termsAndConditions,
      LocalizedDocumentLink? keyFactStatement}) {
    return CreateApplicationCommand(
        productType: productType ?? this.productType,
        referralCode: referralCode ?? this.referralCode,
        externalReferenceId: externalReferenceId ?? this.externalReferenceId,
        termsAndConditions: termsAndConditions ?? this.termsAndConditions,
        keyFactStatement: keyFactStatement ?? this.keyFactStatement);
  }

  CreateApplicationCommand copyWithWrapped(
      {Wrapped<enums.ProductType>? productType,
      Wrapped<String?>? referralCode,
      Wrapped<String?>? externalReferenceId,
      Wrapped<LocalizedDocumentLink?>? termsAndConditions,
      Wrapped<LocalizedDocumentLink?>? keyFactStatement}) {
    return CreateApplicationCommand(
        productType:
            (productType != null ? productType.value : this.productType),
        referralCode:
            (referralCode != null ? referralCode.value : this.referralCode),
        externalReferenceId: (externalReferenceId != null
            ? externalReferenceId.value
            : this.externalReferenceId),
        termsAndConditions: (termsAndConditions != null
            ? termsAndConditions.value
            : this.termsAndConditions),
        keyFactStatement: (keyFactStatement != null
            ? keyFactStatement.value
            : this.keyFactStatement));
  }
}

@JsonSerializable(explicitToJson: true)
class TransactionFilterRequestDto {
  const TransactionFilterRequestDto({
    required this.accountId,
    this.transactionType,
    this.dateFrom,
    this.dateTo,
  });

  factory TransactionFilterRequestDto.fromJson(Map<String, dynamic> json) =>
      _$TransactionFilterRequestDtoFromJson(json);

  static const toJsonFactory = _$TransactionFilterRequestDtoToJson;
  Map<String, dynamic> toJson() => _$TransactionFilterRequestDtoToJson(this);

  @JsonKey(name: 'accountId', includeIfNull: false)
  final String accountId;
  @JsonKey(
    name: 'transactionType',
    includeIfNull: false,
    toJson: transactionFilterRequestDtoTransactionTypeNullableToJson,
    fromJson: transactionFilterRequestDtoTransactionTypeNullableFromJson,
  )
  final enums.TransactionFilterRequestDtoTransactionType? transactionType;
  @JsonKey(name: 'dateFrom', includeIfNull: false, toJson: _dateToJson)
  final DateTime? dateFrom;
  @JsonKey(name: 'dateTo', includeIfNull: false, toJson: _dateToJson)
  final DateTime? dateTo;
  static const fromJsonFactory = _$TransactionFilterRequestDtoFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $TransactionFilterRequestDtoExtension on TransactionFilterRequestDto {
  TransactionFilterRequestDto copyWith(
      {String? accountId,
      enums.TransactionFilterRequestDtoTransactionType? transactionType,
      DateTime? dateFrom,
      DateTime? dateTo}) {
    return TransactionFilterRequestDto(
        accountId: accountId ?? this.accountId,
        transactionType: transactionType ?? this.transactionType,
        dateFrom: dateFrom ?? this.dateFrom,
        dateTo: dateTo ?? this.dateTo);
  }

  TransactionFilterRequestDto copyWithWrapped(
      {Wrapped<String>? accountId,
      Wrapped<enums.TransactionFilterRequestDtoTransactionType?>?
          transactionType,
      Wrapped<DateTime?>? dateFrom,
      Wrapped<DateTime?>? dateTo}) {
    return TransactionFilterRequestDto(
        accountId: (accountId != null ? accountId.value : this.accountId),
        transactionType: (transactionType != null
            ? transactionType.value
            : this.transactionType),
        dateFrom: (dateFrom != null ? dateFrom.value : this.dateFrom),
        dateTo: (dateTo != null ? dateTo.value : this.dateTo));
  }
}

@JsonSerializable(explicitToJson: true)
class Details {
  const Details({
    this.principalPaid,
    this.feePaid,
    this.interestPaid,
  });

  factory Details.fromJson(Map<String, dynamic> json) =>
      _$DetailsFromJson(json);

  static const toJsonFactory = _$DetailsToJson;
  Map<String, dynamic> toJson() => _$DetailsToJson(this);

  @JsonKey(name: 'principalPaid', includeIfNull: false)
  final double? principalPaid;
  @JsonKey(name: 'feePaid', includeIfNull: false)
  final double? feePaid;
  @JsonKey(name: 'interestPaid', includeIfNull: false)
  final double? interestPaid;
  static const fromJsonFactory = _$DetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $DetailsExtension on Details {
  Details copyWith(
      {double? principalPaid, double? feePaid, double? interestPaid}) {
    return Details(
        principalPaid: principalPaid ?? this.principalPaid,
        feePaid: feePaid ?? this.feePaid,
        interestPaid: interestPaid ?? this.interestPaid);
  }

  Details copyWithWrapped(
      {Wrapped<double?>? principalPaid,
      Wrapped<double?>? feePaid,
      Wrapped<double?>? interestPaid}) {
    return Details(
        principalPaid:
            (principalPaid != null ? principalPaid.value : this.principalPaid),
        feePaid: (feePaid != null ? feePaid.value : this.feePaid),
        interestPaid:
            (interestPaid != null ? interestPaid.value : this.interestPaid));
  }
}

@JsonSerializable(explicitToJson: true)
class PaginatedResponseTransactionResponse {
  const PaginatedResponseTransactionResponse({
    this.size,
    this.totalPages,
    this.currentPage,
    this.content,
  });

  factory PaginatedResponseTransactionResponse.fromJson(
          Map<String, dynamic> json) =>
      _$PaginatedResponseTransactionResponseFromJson(json);

  static const toJsonFactory = _$PaginatedResponseTransactionResponseToJson;
  Map<String, dynamic> toJson() =>
      _$PaginatedResponseTransactionResponseToJson(this);

  @JsonKey(name: 'size', includeIfNull: false)
  final int? size;
  @JsonKey(name: 'totalPages', includeIfNull: false)
  final int? totalPages;
  @JsonKey(name: 'currentPage', includeIfNull: false)
  final int? currentPage;
  @JsonKey(
      name: 'content',
      includeIfNull: false,
      defaultValue: <TransactionResponse>[])
  final List<TransactionResponse>? content;
  static const fromJsonFactory = _$PaginatedResponseTransactionResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $PaginatedResponseTransactionResponseExtension
    on PaginatedResponseTransactionResponse {
  PaginatedResponseTransactionResponse copyWith(
      {int? size,
      int? totalPages,
      int? currentPage,
      List<TransactionResponse>? content}) {
    return PaginatedResponseTransactionResponse(
        size: size ?? this.size,
        totalPages: totalPages ?? this.totalPages,
        currentPage: currentPage ?? this.currentPage,
        content: content ?? this.content);
  }

  PaginatedResponseTransactionResponse copyWithWrapped(
      {Wrapped<int?>? size,
      Wrapped<int?>? totalPages,
      Wrapped<int?>? currentPage,
      Wrapped<List<TransactionResponse>?>? content}) {
    return PaginatedResponseTransactionResponse(
        size: (size != null ? size.value : this.size),
        totalPages: (totalPages != null ? totalPages.value : this.totalPages),
        currentPage:
            (currentPage != null ? currentPage.value : this.currentPage),
        content: (content != null ? content.value : this.content));
  }
}

@JsonSerializable(explicitToJson: true)
class TransactionResponse {
  const TransactionResponse({
    required this.accountId,
    required this.id,
    required this.transactionIdentifier,
    this.referenceNumber,
    required this.transactionDateTime,
    required this.amount,
    this.totalBalance,
    this.availableBalance,
    this.currency,
    this.transactionType,
    this.transactionSubType,
    this.transactionStatus,
    this.transactionMode,
    this.details,
  });

  factory TransactionResponse.fromJson(Map<String, dynamic> json) =>
      _$TransactionResponseFromJson(json);

  static const toJsonFactory = _$TransactionResponseToJson;
  Map<String, dynamic> toJson() => _$TransactionResponseToJson(this);

  @JsonKey(name: 'AccountId', includeIfNull: false)
  final String accountId;
  @JsonKey(name: 'Id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'TransactionIdentifier', includeIfNull: false)
  final String transactionIdentifier;
  @JsonKey(name: 'ReferenceNumber', includeIfNull: false)
  final String? referenceNumber;
  @JsonKey(name: 'TransactionDateTime', includeIfNull: false)
  final DateTime transactionDateTime;
  @JsonKey(name: 'Amount', includeIfNull: false)
  final double amount;
  @JsonKey(name: 'TotalBalance', includeIfNull: false)
  final double? totalBalance;
  @JsonKey(name: 'AvailableBalance', includeIfNull: false)
  final double? availableBalance;
  @JsonKey(
    name: 'Currency',
    includeIfNull: false,
    toJson: currencyNullableToJson,
    fromJson: currencyNullableFromJson,
  )
  final enums.Currency? currency;
  @JsonKey(
    name: 'TransactionType',
    includeIfNull: false,
    toJson: transactionResponseTransactionTypeNullableToJson,
    fromJson: transactionResponseTransactionTypeNullableFromJson,
  )
  final enums.TransactionResponseTransactionType? transactionType;
  @JsonKey(
    name: 'TransactionSubType',
    includeIfNull: false,
    toJson: transactionResponseTransactionSubTypeNullableToJson,
    fromJson: transactionResponseTransactionSubTypeNullableFromJson,
  )
  final enums.TransactionResponseTransactionSubType? transactionSubType;
  @JsonKey(
    name: 'TransactionStatus',
    includeIfNull: false,
    toJson: transactionResponseTransactionStatusNullableToJson,
    fromJson: transactionResponseTransactionStatusNullableFromJson,
  )
  final enums.TransactionResponseTransactionStatus? transactionStatus;
  @JsonKey(
    name: 'TransactionMode',
    includeIfNull: false,
    toJson: transactionResponseTransactionModeNullableToJson,
    fromJson: transactionResponseTransactionModeNullableFromJson,
  )
  final enums.TransactionResponseTransactionMode? transactionMode;
  @JsonKey(name: 'Details', includeIfNull: false)
  final Details? details;
  static const fromJsonFactory = _$TransactionResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $TransactionResponseExtension on TransactionResponse {
  TransactionResponse copyWith(
      {String? accountId,
      String? id,
      String? transactionIdentifier,
      String? referenceNumber,
      DateTime? transactionDateTime,
      double? amount,
      double? totalBalance,
      double? availableBalance,
      enums.Currency? currency,
      enums.TransactionResponseTransactionType? transactionType,
      enums.TransactionResponseTransactionSubType? transactionSubType,
      enums.TransactionResponseTransactionStatus? transactionStatus,
      enums.TransactionResponseTransactionMode? transactionMode,
      Details? details}) {
    return TransactionResponse(
        accountId: accountId ?? this.accountId,
        id: id ?? this.id,
        transactionIdentifier:
            transactionIdentifier ?? this.transactionIdentifier,
        referenceNumber: referenceNumber ?? this.referenceNumber,
        transactionDateTime: transactionDateTime ?? this.transactionDateTime,
        amount: amount ?? this.amount,
        totalBalance: totalBalance ?? this.totalBalance,
        availableBalance: availableBalance ?? this.availableBalance,
        currency: currency ?? this.currency,
        transactionType: transactionType ?? this.transactionType,
        transactionSubType: transactionSubType ?? this.transactionSubType,
        transactionStatus: transactionStatus ?? this.transactionStatus,
        transactionMode: transactionMode ?? this.transactionMode,
        details: details ?? this.details);
  }

  TransactionResponse copyWithWrapped(
      {Wrapped<String>? accountId,
      Wrapped<String>? id,
      Wrapped<String>? transactionIdentifier,
      Wrapped<String?>? referenceNumber,
      Wrapped<DateTime>? transactionDateTime,
      Wrapped<double>? amount,
      Wrapped<double?>? totalBalance,
      Wrapped<double?>? availableBalance,
      Wrapped<enums.Currency?>? currency,
      Wrapped<enums.TransactionResponseTransactionType?>? transactionType,
      Wrapped<enums.TransactionResponseTransactionSubType?>? transactionSubType,
      Wrapped<enums.TransactionResponseTransactionStatus?>? transactionStatus,
      Wrapped<enums.TransactionResponseTransactionMode?>? transactionMode,
      Wrapped<Details?>? details}) {
    return TransactionResponse(
        accountId: (accountId != null ? accountId.value : this.accountId),
        id: (id != null ? id.value : this.id),
        transactionIdentifier: (transactionIdentifier != null
            ? transactionIdentifier.value
            : this.transactionIdentifier),
        referenceNumber: (referenceNumber != null
            ? referenceNumber.value
            : this.referenceNumber),
        transactionDateTime: (transactionDateTime != null
            ? transactionDateTime.value
            : this.transactionDateTime),
        amount: (amount != null ? amount.value : this.amount),
        totalBalance:
            (totalBalance != null ? totalBalance.value : this.totalBalance),
        availableBalance: (availableBalance != null
            ? availableBalance.value
            : this.availableBalance),
        currency: (currency != null ? currency.value : this.currency),
        transactionType: (transactionType != null
            ? transactionType.value
            : this.transactionType),
        transactionSubType: (transactionSubType != null
            ? transactionSubType.value
            : this.transactionSubType),
        transactionStatus: (transactionStatus != null
            ? transactionStatus.value
            : this.transactionStatus),
        transactionMode: (transactionMode != null
            ? transactionMode.value
            : this.transactionMode),
        details: (details != null ? details.value : this.details));
  }
}

@JsonSerializable(explicitToJson: true)
class LoanRepaymentCommand {
  const LoanRepaymentCommand({
    required this.depositAccountId,
    required this.loanAccountId,
    required this.amount,
  });

  factory LoanRepaymentCommand.fromJson(Map<String, dynamic> json) =>
      _$LoanRepaymentCommandFromJson(json);

  static const toJsonFactory = _$LoanRepaymentCommandToJson;
  Map<String, dynamic> toJson() => _$LoanRepaymentCommandToJson(this);

  @JsonKey(name: 'depositAccountId', includeIfNull: false)
  final String depositAccountId;
  @JsonKey(name: 'loanAccountId', includeIfNull: false)
  final String loanAccountId;
  @JsonKey(name: 'amount', includeIfNull: false)
  final Money amount;
  static const fromJsonFactory = _$LoanRepaymentCommandFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $LoanRepaymentCommandExtension on LoanRepaymentCommand {
  LoanRepaymentCommand copyWith(
      {String? depositAccountId, String? loanAccountId, Money? amount}) {
    return LoanRepaymentCommand(
        depositAccountId: depositAccountId ?? this.depositAccountId,
        loanAccountId: loanAccountId ?? this.loanAccountId,
        amount: amount ?? this.amount);
  }

  LoanRepaymentCommand copyWithWrapped(
      {Wrapped<String>? depositAccountId,
      Wrapped<String>? loanAccountId,
      Wrapped<Money>? amount}) {
    return LoanRepaymentCommand(
        depositAccountId: (depositAccountId != null
            ? depositAccountId.value
            : this.depositAccountId),
        loanAccountId:
            (loanAccountId != null ? loanAccountId.value : this.loanAccountId),
        amount: (amount != null ? amount.value : this.amount));
  }
}

@JsonSerializable(explicitToJson: true)
class UpdatePaymentSettingsRequest {
  const UpdatePaymentSettingsRequest({
    required this.percentage,
  });

  factory UpdatePaymentSettingsRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdatePaymentSettingsRequestFromJson(json);

  static const toJsonFactory = _$UpdatePaymentSettingsRequestToJson;
  Map<String, dynamic> toJson() => _$UpdatePaymentSettingsRequestToJson(this);

  @JsonKey(name: 'percentage', includeIfNull: false)
  final int percentage;
  static const fromJsonFactory = _$UpdatePaymentSettingsRequestFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UpdatePaymentSettingsRequestExtension
    on UpdatePaymentSettingsRequest {
  UpdatePaymentSettingsRequest copyWith({int? percentage}) {
    return UpdatePaymentSettingsRequest(
        percentage: percentage ?? this.percentage);
  }

  UpdatePaymentSettingsRequest copyWithWrapped({Wrapped<int>? percentage}) {
    return UpdatePaymentSettingsRequest(
        percentage: (percentage != null ? percentage.value : this.percentage));
  }
}

@JsonSerializable(explicitToJson: true)
class Account {
  const Account({
    required this.id,
    required this.customerId,
    required this.name,
    this.lendingApplicationId,
    required this.currency,
    this.notes,
    required this.loanAmount,
    required this.balances,
    required this.paymentSettings,
    required this.createdAt,
    required this.productType,
    required this.mambuState,
    this.mambuSubState,
    required this.feePercentage,
    this.loanExpiryPeriod,
    this.loanPeriod,
    this.annualFee,
    this.cashWithdrawalAllowance,
    this.firstRepaymentDate,
    this.minLimit,
    this.repaymentFeeParameters,
    this.lateFeeParameters,
    this.nextInstallmentDetails,
    this.feeFreePeriodUntil,
    this.closeable,
    this.active,
  });

  factory Account.fromJson(Map<String, dynamic> json) =>
      _$AccountFromJson(json);

  static const toJsonFactory = _$AccountToJson;
  Map<String, dynamic> toJson() => _$AccountToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'customerId', includeIfNull: false)
  final String customerId;
  @JsonKey(name: 'name', includeIfNull: false)
  final String name;
  @JsonKey(name: 'lendingApplicationId', includeIfNull: false)
  final String? lendingApplicationId;
  @JsonKey(
    name: 'currency',
    includeIfNull: false,
    toJson: currencyToJson,
    fromJson: currencyFromJson,
  )
  final enums.Currency currency;
  @JsonKey(name: 'notes', includeIfNull: false)
  final String? notes;
  @JsonKey(name: 'loanAmount', includeIfNull: false)
  final double loanAmount;
  @JsonKey(name: 'balances', includeIfNull: false)
  final Balances balances;
  @JsonKey(name: 'paymentSettings', includeIfNull: false)
  final PaymentSettings paymentSettings;
  @JsonKey(name: 'createdAt', includeIfNull: false)
  final DateTime createdAt;
  @JsonKey(
    name: 'productType',
    includeIfNull: false,
    toJson: productTypeToJson,
    fromJson: productTypeFromJson,
  )
  final enums.ProductType productType;
  @JsonKey(name: 'mambuState', includeIfNull: false)
  final String mambuState;
  @JsonKey(name: 'mambuSubState', includeIfNull: false)
  final String? mambuSubState;
  @JsonKey(name: 'feePercentage', includeIfNull: false)
  final double feePercentage;
  @JsonKey(name: 'loanExpiryPeriod', includeIfNull: false)
  final String? loanExpiryPeriod;
  @JsonKey(name: 'loanPeriod', includeIfNull: false)
  final String? loanPeriod;
  @JsonKey(name: 'annualFee', includeIfNull: false)
  final double? annualFee;
  @JsonKey(name: 'cashWithdrawalAllowance', includeIfNull: false)
  final double? cashWithdrawalAllowance;
  @JsonKey(
      name: 'firstRepaymentDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime? firstRepaymentDate;
  @JsonKey(name: 'minLimit', includeIfNull: false)
  final double? minLimit;
  @JsonKey(name: 'repaymentFeeParameters', includeIfNull: false)
  final InstallmentFeeParameters? repaymentFeeParameters;
  @JsonKey(name: 'lateFeeParameters', includeIfNull: false)
  final InstallmentFeeParameters? lateFeeParameters;
  @JsonKey(name: 'nextInstallmentDetails', includeIfNull: false)
  final NextInstallmentDetails? nextInstallmentDetails;
  @JsonKey(
      name: 'feeFreePeriodUntil', includeIfNull: false, toJson: _dateToJson)
  final DateTime? feeFreePeriodUntil;
  @JsonKey(name: 'closeable', includeIfNull: false)
  final bool? closeable;
  @JsonKey(name: 'active', includeIfNull: false)
  final bool? active;
  static const fromJsonFactory = _$AccountFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AccountExtension on Account {
  Account copyWith(
      {String? id,
      String? customerId,
      String? name,
      String? lendingApplicationId,
      enums.Currency? currency,
      String? notes,
      double? loanAmount,
      Balances? balances,
      PaymentSettings? paymentSettings,
      DateTime? createdAt,
      enums.ProductType? productType,
      String? mambuState,
      String? mambuSubState,
      double? feePercentage,
      String? loanExpiryPeriod,
      String? loanPeriod,
      double? annualFee,
      double? cashWithdrawalAllowance,
      DateTime? firstRepaymentDate,
      double? minLimit,
      InstallmentFeeParameters? repaymentFeeParameters,
      InstallmentFeeParameters? lateFeeParameters,
      NextInstallmentDetails? nextInstallmentDetails,
      DateTime? feeFreePeriodUntil,
      bool? closeable,
      bool? active}) {
    return Account(
        id: id ?? this.id,
        customerId: customerId ?? this.customerId,
        name: name ?? this.name,
        lendingApplicationId: lendingApplicationId ?? this.lendingApplicationId,
        currency: currency ?? this.currency,
        notes: notes ?? this.notes,
        loanAmount: loanAmount ?? this.loanAmount,
        balances: balances ?? this.balances,
        paymentSettings: paymentSettings ?? this.paymentSettings,
        createdAt: createdAt ?? this.createdAt,
        productType: productType ?? this.productType,
        mambuState: mambuState ?? this.mambuState,
        mambuSubState: mambuSubState ?? this.mambuSubState,
        feePercentage: feePercentage ?? this.feePercentage,
        loanExpiryPeriod: loanExpiryPeriod ?? this.loanExpiryPeriod,
        loanPeriod: loanPeriod ?? this.loanPeriod,
        annualFee: annualFee ?? this.annualFee,
        cashWithdrawalAllowance:
            cashWithdrawalAllowance ?? this.cashWithdrawalAllowance,
        firstRepaymentDate: firstRepaymentDate ?? this.firstRepaymentDate,
        minLimit: minLimit ?? this.minLimit,
        repaymentFeeParameters:
            repaymentFeeParameters ?? this.repaymentFeeParameters,
        lateFeeParameters: lateFeeParameters ?? this.lateFeeParameters,
        nextInstallmentDetails:
            nextInstallmentDetails ?? this.nextInstallmentDetails,
        feeFreePeriodUntil: feeFreePeriodUntil ?? this.feeFreePeriodUntil,
        closeable: closeable ?? this.closeable,
        active: active ?? this.active);
  }

  Account copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? customerId,
      Wrapped<String>? name,
      Wrapped<String?>? lendingApplicationId,
      Wrapped<enums.Currency>? currency,
      Wrapped<String?>? notes,
      Wrapped<double>? loanAmount,
      Wrapped<Balances>? balances,
      Wrapped<PaymentSettings>? paymentSettings,
      Wrapped<DateTime>? createdAt,
      Wrapped<enums.ProductType>? productType,
      Wrapped<String>? mambuState,
      Wrapped<String?>? mambuSubState,
      Wrapped<double>? feePercentage,
      Wrapped<String?>? loanExpiryPeriod,
      Wrapped<String?>? loanPeriod,
      Wrapped<double?>? annualFee,
      Wrapped<double?>? cashWithdrawalAllowance,
      Wrapped<DateTime?>? firstRepaymentDate,
      Wrapped<double?>? minLimit,
      Wrapped<InstallmentFeeParameters?>? repaymentFeeParameters,
      Wrapped<InstallmentFeeParameters?>? lateFeeParameters,
      Wrapped<NextInstallmentDetails?>? nextInstallmentDetails,
      Wrapped<DateTime?>? feeFreePeriodUntil,
      Wrapped<bool?>? closeable,
      Wrapped<bool?>? active}) {
    return Account(
        id: (id != null ? id.value : this.id),
        customerId: (customerId != null ? customerId.value : this.customerId),
        name: (name != null ? name.value : this.name),
        lendingApplicationId: (lendingApplicationId != null
            ? lendingApplicationId.value
            : this.lendingApplicationId),
        currency: (currency != null ? currency.value : this.currency),
        notes: (notes != null ? notes.value : this.notes),
        loanAmount: (loanAmount != null ? loanAmount.value : this.loanAmount),
        balances: (balances != null ? balances.value : this.balances),
        paymentSettings: (paymentSettings != null
            ? paymentSettings.value
            : this.paymentSettings),
        createdAt: (createdAt != null ? createdAt.value : this.createdAt),
        productType:
            (productType != null ? productType.value : this.productType),
        mambuState: (mambuState != null ? mambuState.value : this.mambuState),
        mambuSubState:
            (mambuSubState != null ? mambuSubState.value : this.mambuSubState),
        feePercentage:
            (feePercentage != null ? feePercentage.value : this.feePercentage),
        loanExpiryPeriod: (loanExpiryPeriod != null
            ? loanExpiryPeriod.value
            : this.loanExpiryPeriod),
        loanPeriod: (loanPeriod != null ? loanPeriod.value : this.loanPeriod),
        annualFee: (annualFee != null ? annualFee.value : this.annualFee),
        cashWithdrawalAllowance: (cashWithdrawalAllowance != null
            ? cashWithdrawalAllowance.value
            : this.cashWithdrawalAllowance),
        firstRepaymentDate: (firstRepaymentDate != null
            ? firstRepaymentDate.value
            : this.firstRepaymentDate),
        minLimit: (minLimit != null ? minLimit.value : this.minLimit),
        repaymentFeeParameters: (repaymentFeeParameters != null
            ? repaymentFeeParameters.value
            : this.repaymentFeeParameters),
        lateFeeParameters: (lateFeeParameters != null
            ? lateFeeParameters.value
            : this.lateFeeParameters),
        nextInstallmentDetails: (nextInstallmentDetails != null
            ? nextInstallmentDetails.value
            : this.nextInstallmentDetails),
        feeFreePeriodUntil: (feeFreePeriodUntil != null
            ? feeFreePeriodUntil.value
            : this.feeFreePeriodUntil),
        closeable: (closeable != null ? closeable.value : this.closeable),
        active: (active != null ? active.value : this.active));
  }
}

@JsonSerializable(explicitToJson: true)
class NextInstallmentDetails {
  const NextInstallmentDetails({
    required this.dueDate,
    required this.amount,
  });

  factory NextInstallmentDetails.fromJson(Map<String, dynamic> json) =>
      _$NextInstallmentDetailsFromJson(json);

  static const toJsonFactory = _$NextInstallmentDetailsToJson;
  Map<String, dynamic> toJson() => _$NextInstallmentDetailsToJson(this);

  @JsonKey(name: 'dueDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime dueDate;
  @JsonKey(name: 'amount', includeIfNull: false)
  final InstallmentAmount amount;
  static const fromJsonFactory = _$NextInstallmentDetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $NextInstallmentDetailsExtension on NextInstallmentDetails {
  NextInstallmentDetails copyWith(
      {DateTime? dueDate, InstallmentAmount? amount}) {
    return NextInstallmentDetails(
        dueDate: dueDate ?? this.dueDate, amount: amount ?? this.amount);
  }

  NextInstallmentDetails copyWithWrapped(
      {Wrapped<DateTime>? dueDate, Wrapped<InstallmentAmount>? amount}) {
    return NextInstallmentDetails(
        dueDate: (dueDate != null ? dueDate.value : this.dueDate),
        amount: (amount != null ? amount.value : this.amount));
  }
}

@JsonSerializable(explicitToJson: true)
class PaymentSettings {
  const PaymentSettings({
    required this.percentage,
  });

  factory PaymentSettings.fromJson(Map<String, dynamic> json) =>
      _$PaymentSettingsFromJson(json);

  static const toJsonFactory = _$PaymentSettingsToJson;
  Map<String, dynamic> toJson() => _$PaymentSettingsToJson(this);

  @JsonKey(name: 'percentage', includeIfNull: false)
  final double percentage;
  static const fromJsonFactory = _$PaymentSettingsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $PaymentSettingsExtension on PaymentSettings {
  PaymentSettings copyWith({double? percentage}) {
    return PaymentSettings(percentage: percentage ?? this.percentage);
  }

  PaymentSettings copyWithWrapped({Wrapped<double>? percentage}) {
    return PaymentSettings(
        percentage: (percentage != null ? percentage.value : this.percentage));
  }
}

@JsonSerializable(explicitToJson: true)
class UpdateLoanAmountRequest {
  const UpdateLoanAmountRequest({
    this.loanAmount,
  });

  factory UpdateLoanAmountRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateLoanAmountRequestFromJson(json);

  static const toJsonFactory = _$UpdateLoanAmountRequestToJson;
  Map<String, dynamic> toJson() => _$UpdateLoanAmountRequestToJson(this);

  @JsonKey(name: 'loanAmount', includeIfNull: false)
  final double? loanAmount;
  static const fromJsonFactory = _$UpdateLoanAmountRequestFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UpdateLoanAmountRequestExtension on UpdateLoanAmountRequest {
  UpdateLoanAmountRequest copyWith({double? loanAmount}) {
    return UpdateLoanAmountRequest(loanAmount: loanAmount ?? this.loanAmount);
  }

  UpdateLoanAmountRequest copyWithWrapped({Wrapped<double?>? loanAmount}) {
    return UpdateLoanAmountRequest(
        loanAmount: (loanAmount != null ? loanAmount.value : this.loanAmount));
  }
}

@JsonSerializable(explicitToJson: true)
class ProductCodeStaticDetailsDto {
  const ProductCodeStaticDetailsDto({
    this.cashbackPercentage,
    this.minFDAmount,
    this.limitFromFDPercent,
  });

  factory ProductCodeStaticDetailsDto.fromJson(Map<String, dynamic> json) =>
      _$ProductCodeStaticDetailsDtoFromJson(json);

  static const toJsonFactory = _$ProductCodeStaticDetailsDtoToJson;
  Map<String, dynamic> toJson() => _$ProductCodeStaticDetailsDtoToJson(this);

  @JsonKey(name: 'cashbackPercentage', includeIfNull: false)
  final double? cashbackPercentage;
  @JsonKey(name: 'minFDAmount', includeIfNull: false)
  final Money? minFDAmount;
  @JsonKey(name: 'limitFromFDPercent', includeIfNull: false)
  final double? limitFromFDPercent;
  static const fromJsonFactory = _$ProductCodeStaticDetailsDtoFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ProductCodeStaticDetailsDtoExtension on ProductCodeStaticDetailsDto {
  ProductCodeStaticDetailsDto copyWith(
      {double? cashbackPercentage,
      Money? minFDAmount,
      double? limitFromFDPercent}) {
    return ProductCodeStaticDetailsDto(
        cashbackPercentage: cashbackPercentage ?? this.cashbackPercentage,
        minFDAmount: minFDAmount ?? this.minFDAmount,
        limitFromFDPercent: limitFromFDPercent ?? this.limitFromFDPercent);
  }

  ProductCodeStaticDetailsDto copyWithWrapped(
      {Wrapped<double?>? cashbackPercentage,
      Wrapped<Money?>? minFDAmount,
      Wrapped<double?>? limitFromFDPercent}) {
    return ProductCodeStaticDetailsDto(
        cashbackPercentage: (cashbackPercentage != null
            ? cashbackPercentage.value
            : this.cashbackPercentage),
        minFDAmount:
            (minFDAmount != null ? minFDAmount.value : this.minFDAmount),
        limitFromFDPercent: (limitFromFDPercent != null
            ? limitFromFDPercent.value
            : this.limitFromFDPercent));
  }
}

@JsonSerializable(explicitToJson: true)
class BusinessDetails {
  const BusinessDetails({
    this.businessId,
    this.individualId,
    this.userName,
    this.email,
  });

  factory BusinessDetails.fromJson(Map<String, dynamic> json) =>
      _$BusinessDetailsFromJson(json);

  static const toJsonFactory = _$BusinessDetailsToJson;
  Map<String, dynamic> toJson() => _$BusinessDetailsToJson(this);

  @JsonKey(name: 'businessId', includeIfNull: false)
  final String? businessId;
  @JsonKey(name: 'individualId', includeIfNull: false)
  final String? individualId;
  @JsonKey(name: 'userName', includeIfNull: false)
  final String? userName;
  @JsonKey(name: 'email', includeIfNull: false)
  final String? email;
  static const fromJsonFactory = _$BusinessDetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $BusinessDetailsExtension on BusinessDetails {
  BusinessDetails copyWith(
      {String? businessId,
      String? individualId,
      String? userName,
      String? email}) {
    return BusinessDetails(
        businessId: businessId ?? this.businessId,
        individualId: individualId ?? this.individualId,
        userName: userName ?? this.userName,
        email: email ?? this.email);
  }

  BusinessDetails copyWithWrapped(
      {Wrapped<String?>? businessId,
      Wrapped<String?>? individualId,
      Wrapped<String?>? userName,
      Wrapped<String?>? email}) {
    return BusinessDetails(
        businessId: (businessId != null ? businessId.value : this.businessId),
        individualId:
            (individualId != null ? individualId.value : this.individualId),
        userName: (userName != null ? userName.value : this.userName),
        email: (email != null ? email.value : this.email));
  }
}

@JsonSerializable(explicitToJson: true)
class StatementDto {
  const StatementDto({
    required this.statementId,
    required this.month,
    required this.year,
  });

  factory StatementDto.fromJson(Map<String, dynamic> json) =>
      _$StatementDtoFromJson(json);

  static const toJsonFactory = _$StatementDtoToJson;
  Map<String, dynamic> toJson() => _$StatementDtoToJson(this);

  @JsonKey(name: 'statementId', includeIfNull: false)
  final String statementId;
  @JsonKey(name: 'month', includeIfNull: false)
  final int month;
  @JsonKey(name: 'year', includeIfNull: false)
  final int year;
  static const fromJsonFactory = _$StatementDtoFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $StatementDtoExtension on StatementDto {
  StatementDto copyWith({String? statementId, int? month, int? year}) {
    return StatementDto(
        statementId: statementId ?? this.statementId,
        month: month ?? this.month,
        year: year ?? this.year);
  }

  StatementDto copyWithWrapped(
      {Wrapped<String>? statementId, Wrapped<int>? month, Wrapped<int>? year}) {
    return StatementDto(
        statementId:
            (statementId != null ? statementId.value : this.statementId),
        month: (month != null ? month.value : this.month),
        year: (year != null ? year.value : this.year));
  }
}

@JsonSerializable(explicitToJson: true)
class ReferralValidationResponse {
  const ReferralValidationResponse({
    required this.valid,
    required this.anchorBusinessName,
    this.anchorBusinessId,
    this.productType,
  });

  factory ReferralValidationResponse.fromJson(Map<String, dynamic> json) =>
      _$ReferralValidationResponseFromJson(json);

  static const toJsonFactory = _$ReferralValidationResponseToJson;
  Map<String, dynamic> toJson() => _$ReferralValidationResponseToJson(this);

  @JsonKey(name: 'valid', includeIfNull: false)
  final bool valid;
  @JsonKey(name: 'anchorBusinessName', includeIfNull: false)
  final String anchorBusinessName;
  @JsonKey(name: 'anchorBusinessId', includeIfNull: false)
  final String? anchorBusinessId;
  @JsonKey(
    name: 'productType',
    includeIfNull: false,
    toJson: productTypeNullableToJson,
    fromJson: productTypeNullableFromJson,
  )
  final enums.ProductType? productType;
  static const fromJsonFactory = _$ReferralValidationResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ReferralValidationResponseExtension on ReferralValidationResponse {
  ReferralValidationResponse copyWith(
      {bool? valid,
      String? anchorBusinessName,
      String? anchorBusinessId,
      enums.ProductType? productType}) {
    return ReferralValidationResponse(
        valid: valid ?? this.valid,
        anchorBusinessName: anchorBusinessName ?? this.anchorBusinessName,
        anchorBusinessId: anchorBusinessId ?? this.anchorBusinessId,
        productType: productType ?? this.productType);
  }

  ReferralValidationResponse copyWithWrapped(
      {Wrapped<bool>? valid,
      Wrapped<String>? anchorBusinessName,
      Wrapped<String?>? anchorBusinessId,
      Wrapped<enums.ProductType?>? productType}) {
    return ReferralValidationResponse(
        valid: (valid != null ? valid.value : this.valid),
        anchorBusinessName: (anchorBusinessName != null
            ? anchorBusinessName.value
            : this.anchorBusinessName),
        anchorBusinessId: (anchorBusinessId != null
            ? anchorBusinessId.value
            : this.anchorBusinessId),
        productType:
            (productType != null ? productType.value : this.productType));
  }
}

@JsonSerializable(explicitToJson: true)
class Referral {
  const Referral({
    this.referralCode,
    this.supplierId,
    this.anchorId,
    this.anchorBusinessName,
    this.productType,
    this.status,
    this.multiUserRequestId,
    this.domainId,
    this.jocataApplicationId,
    this.tncUrls,
    this.pricingTriggered,
  });

  factory Referral.fromJson(Map<String, dynamic> json) =>
      _$ReferralFromJson(json);

  static const toJsonFactory = _$ReferralToJson;
  Map<String, dynamic> toJson() => _$ReferralToJson(this);

  @JsonKey(name: 'referralCode', includeIfNull: false)
  final String? referralCode;
  @JsonKey(name: 'supplierId', includeIfNull: false)
  final String? supplierId;
  @JsonKey(name: 'anchorId', includeIfNull: false)
  final String? anchorId;
  @JsonKey(name: 'anchorBusinessName', includeIfNull: false)
  final String? anchorBusinessName;
  @JsonKey(
    name: 'productType',
    includeIfNull: false,
    toJson: productTypeNullableToJson,
    fromJson: productTypeNullableFromJson,
  )
  final enums.ProductType? productType;
  @JsonKey(
    name: 'status',
    includeIfNull: false,
    toJson: optInStatusNullableToJson,
    fromJson: optInStatusNullableFromJson,
  )
  final enums.OptInStatus? status;
  @JsonKey(name: 'multiUserRequestId', includeIfNull: false)
  final String? multiUserRequestId;
  @JsonKey(name: 'domainId', includeIfNull: false)
  final String? domainId;
  @JsonKey(name: 'jocataApplicationId', includeIfNull: false)
  final String? jocataApplicationId;
  @JsonKey(name: 'tncUrls', includeIfNull: false)
  final LocalizedDocumentLink? tncUrls;
  @JsonKey(name: 'pricingTriggered', includeIfNull: false)
  final bool? pricingTriggered;
  static const fromJsonFactory = _$ReferralFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ReferralExtension on Referral {
  Referral copyWith(
      {String? referralCode,
      String? supplierId,
      String? anchorId,
      String? anchorBusinessName,
      enums.ProductType? productType,
      enums.OptInStatus? status,
      String? multiUserRequestId,
      String? domainId,
      String? jocataApplicationId,
      LocalizedDocumentLink? tncUrls,
      bool? pricingTriggered}) {
    return Referral(
        referralCode: referralCode ?? this.referralCode,
        supplierId: supplierId ?? this.supplierId,
        anchorId: anchorId ?? this.anchorId,
        anchorBusinessName: anchorBusinessName ?? this.anchorBusinessName,
        productType: productType ?? this.productType,
        status: status ?? this.status,
        multiUserRequestId: multiUserRequestId ?? this.multiUserRequestId,
        domainId: domainId ?? this.domainId,
        jocataApplicationId: jocataApplicationId ?? this.jocataApplicationId,
        tncUrls: tncUrls ?? this.tncUrls,
        pricingTriggered: pricingTriggered ?? this.pricingTriggered);
  }

  Referral copyWithWrapped(
      {Wrapped<String?>? referralCode,
      Wrapped<String?>? supplierId,
      Wrapped<String?>? anchorId,
      Wrapped<String?>? anchorBusinessName,
      Wrapped<enums.ProductType?>? productType,
      Wrapped<enums.OptInStatus?>? status,
      Wrapped<String?>? multiUserRequestId,
      Wrapped<String?>? domainId,
      Wrapped<String?>? jocataApplicationId,
      Wrapped<LocalizedDocumentLink?>? tncUrls,
      Wrapped<bool?>? pricingTriggered}) {
    return Referral(
        referralCode:
            (referralCode != null ? referralCode.value : this.referralCode),
        supplierId: (supplierId != null ? supplierId.value : this.supplierId),
        anchorId: (anchorId != null ? anchorId.value : this.anchorId),
        anchorBusinessName: (anchorBusinessName != null
            ? anchorBusinessName.value
            : this.anchorBusinessName),
        productType:
            (productType != null ? productType.value : this.productType),
        status: (status != null ? status.value : this.status),
        multiUserRequestId: (multiUserRequestId != null
            ? multiUserRequestId.value
            : this.multiUserRequestId),
        domainId: (domainId != null ? domainId.value : this.domainId),
        jocataApplicationId: (jocataApplicationId != null
            ? jocataApplicationId.value
            : this.jocataApplicationId),
        tncUrls: (tncUrls != null ? tncUrls.value : this.tncUrls),
        pricingTriggered: (pricingTriggered != null
            ? pricingTriggered.value
            : this.pricingTriggered));
  }
}

@JsonSerializable(explicitToJson: true)
class Invoice {
  const Invoice({
    this.invoiceId,
    this.invoiceNumber,
    this.status,
    this.requestDate,
    this.dueDate,
    this.invoiceAmount,
    this.interest,
    this.amountToSupplier,
    this.currency,
    this.supplierName,
    this.preparer,
  });

  factory Invoice.fromJson(Map<String, dynamic> json) =>
      _$InvoiceFromJson(json);

  static const toJsonFactory = _$InvoiceToJson;
  Map<String, dynamic> toJson() => _$InvoiceToJson(this);

  @JsonKey(name: 'invoiceId', includeIfNull: false)
  final String? invoiceId;
  @JsonKey(name: 'invoiceNumber', includeIfNull: false)
  final String? invoiceNumber;
  @JsonKey(name: 'status', includeIfNull: false)
  final String? status;
  @JsonKey(name: 'requestDate', includeIfNull: false)
  final String? requestDate;
  @JsonKey(name: 'dueDate', includeIfNull: false)
  final String? dueDate;
  @JsonKey(name: 'invoiceAmount', includeIfNull: false)
  final String? invoiceAmount;
  @JsonKey(name: 'interest', includeIfNull: false)
  final String? interest;
  @JsonKey(name: 'amountToSupplier', includeIfNull: false)
  final String? amountToSupplier;
  @JsonKey(name: 'currency', includeIfNull: false)
  final String? currency;
  @JsonKey(name: 'supplierName', includeIfNull: false)
  final String? supplierName;
  @JsonKey(name: 'preparer', includeIfNull: false)
  final String? preparer;
  static const fromJsonFactory = _$InvoiceFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $InvoiceExtension on Invoice {
  Invoice copyWith(
      {String? invoiceId,
      String? invoiceNumber,
      String? status,
      String? requestDate,
      String? dueDate,
      String? invoiceAmount,
      String? interest,
      String? amountToSupplier,
      String? currency,
      String? supplierName,
      String? preparer}) {
    return Invoice(
        invoiceId: invoiceId ?? this.invoiceId,
        invoiceNumber: invoiceNumber ?? this.invoiceNumber,
        status: status ?? this.status,
        requestDate: requestDate ?? this.requestDate,
        dueDate: dueDate ?? this.dueDate,
        invoiceAmount: invoiceAmount ?? this.invoiceAmount,
        interest: interest ?? this.interest,
        amountToSupplier: amountToSupplier ?? this.amountToSupplier,
        currency: currency ?? this.currency,
        supplierName: supplierName ?? this.supplierName,
        preparer: preparer ?? this.preparer);
  }

  Invoice copyWithWrapped(
      {Wrapped<String?>? invoiceId,
      Wrapped<String?>? invoiceNumber,
      Wrapped<String?>? status,
      Wrapped<String?>? requestDate,
      Wrapped<String?>? dueDate,
      Wrapped<String?>? invoiceAmount,
      Wrapped<String?>? interest,
      Wrapped<String?>? amountToSupplier,
      Wrapped<String?>? currency,
      Wrapped<String?>? supplierName,
      Wrapped<String?>? preparer}) {
    return Invoice(
        invoiceId: (invoiceId != null ? invoiceId.value : this.invoiceId),
        invoiceNumber:
            (invoiceNumber != null ? invoiceNumber.value : this.invoiceNumber),
        status: (status != null ? status.value : this.status),
        requestDate:
            (requestDate != null ? requestDate.value : this.requestDate),
        dueDate: (dueDate != null ? dueDate.value : this.dueDate),
        invoiceAmount:
            (invoiceAmount != null ? invoiceAmount.value : this.invoiceAmount),
        interest: (interest != null ? interest.value : this.interest),
        amountToSupplier: (amountToSupplier != null
            ? amountToSupplier.value
            : this.amountToSupplier),
        currency: (currency != null ? currency.value : this.currency),
        supplierName:
            (supplierName != null ? supplierName.value : this.supplierName),
        preparer: (preparer != null ? preparer.value : this.preparer));
  }
}

@JsonSerializable(explicitToJson: true)
class JocataViewInvoicesResponse {
  const JocataViewInvoicesResponse({
    this.invoicesCount,
    this.totalInvoiceAmount,
    this.totalDiscount,
    this.currency,
    this.invoices,
  });

  factory JocataViewInvoicesResponse.fromJson(Map<String, dynamic> json) =>
      _$JocataViewInvoicesResponseFromJson(json);

  static const toJsonFactory = _$JocataViewInvoicesResponseToJson;
  Map<String, dynamic> toJson() => _$JocataViewInvoicesResponseToJson(this);

  @JsonKey(name: 'invoicesCount', includeIfNull: false)
  final int? invoicesCount;
  @JsonKey(name: 'totalInvoiceAmount', includeIfNull: false)
  final String? totalInvoiceAmount;
  @JsonKey(name: 'totalDiscount', includeIfNull: false)
  final String? totalDiscount;
  @JsonKey(name: 'currency', includeIfNull: false)
  final String? currency;
  @JsonKey(name: 'invoices', includeIfNull: false, defaultValue: <Invoice>[])
  final List<Invoice>? invoices;
  static const fromJsonFactory = _$JocataViewInvoicesResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $JocataViewInvoicesResponseExtension on JocataViewInvoicesResponse {
  JocataViewInvoicesResponse copyWith(
      {int? invoicesCount,
      String? totalInvoiceAmount,
      String? totalDiscount,
      String? currency,
      List<Invoice>? invoices}) {
    return JocataViewInvoicesResponse(
        invoicesCount: invoicesCount ?? this.invoicesCount,
        totalInvoiceAmount: totalInvoiceAmount ?? this.totalInvoiceAmount,
        totalDiscount: totalDiscount ?? this.totalDiscount,
        currency: currency ?? this.currency,
        invoices: invoices ?? this.invoices);
  }

  JocataViewInvoicesResponse copyWithWrapped(
      {Wrapped<int?>? invoicesCount,
      Wrapped<String?>? totalInvoiceAmount,
      Wrapped<String?>? totalDiscount,
      Wrapped<String?>? currency,
      Wrapped<List<Invoice>?>? invoices}) {
    return JocataViewInvoicesResponse(
        invoicesCount:
            (invoicesCount != null ? invoicesCount.value : this.invoicesCount),
        totalInvoiceAmount: (totalInvoiceAmount != null
            ? totalInvoiceAmount.value
            : this.totalInvoiceAmount),
        totalDiscount:
            (totalDiscount != null ? totalDiscount.value : this.totalDiscount),
        currency: (currency != null ? currency.value : this.currency),
        invoices: (invoices != null ? invoices.value : this.invoices));
  }
}

@JsonSerializable(explicitToJson: true)
class InvoicesRequestSummary {
  const InvoicesRequestSummary({
    this.invoicesCount,
    this.totalInvoiceAmount,
    this.totalDiscount,
    this.currency,
  });

  factory InvoicesRequestSummary.fromJson(Map<String, dynamic> json) =>
      _$InvoicesRequestSummaryFromJson(json);

  static const toJsonFactory = _$InvoicesRequestSummaryToJson;
  Map<String, dynamic> toJson() => _$InvoicesRequestSummaryToJson(this);

  @JsonKey(name: 'invoicesCount', includeIfNull: false)
  final int? invoicesCount;
  @JsonKey(name: 'totalInvoiceAmount', includeIfNull: false)
  final double? totalInvoiceAmount;
  @JsonKey(name: 'totalDiscount', includeIfNull: false)
  final double? totalDiscount;
  @JsonKey(name: 'currency', includeIfNull: false)
  final String? currency;
  static const fromJsonFactory = _$InvoicesRequestSummaryFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $InvoicesRequestSummaryExtension on InvoicesRequestSummary {
  InvoicesRequestSummary copyWith(
      {int? invoicesCount,
      double? totalInvoiceAmount,
      double? totalDiscount,
      String? currency}) {
    return InvoicesRequestSummary(
        invoicesCount: invoicesCount ?? this.invoicesCount,
        totalInvoiceAmount: totalInvoiceAmount ?? this.totalInvoiceAmount,
        totalDiscount: totalDiscount ?? this.totalDiscount,
        currency: currency ?? this.currency);
  }

  InvoicesRequestSummary copyWithWrapped(
      {Wrapped<int?>? invoicesCount,
      Wrapped<double?>? totalInvoiceAmount,
      Wrapped<double?>? totalDiscount,
      Wrapped<String?>? currency}) {
    return InvoicesRequestSummary(
        invoicesCount:
            (invoicesCount != null ? invoicesCount.value : this.invoicesCount),
        totalInvoiceAmount: (totalInvoiceAmount != null
            ? totalInvoiceAmount.value
            : this.totalInvoiceAmount),
        totalDiscount:
            (totalDiscount != null ? totalDiscount.value : this.totalDiscount),
        currency: (currency != null ? currency.value : this.currency));
  }
}

@JsonSerializable(explicitToJson: true)
class JocataInvoiceSummaryResponse {
  const JocataInvoiceSummaryResponse({
    this.response,
  });

  factory JocataInvoiceSummaryResponse.fromJson(Map<String, dynamic> json) =>
      _$JocataInvoiceSummaryResponseFromJson(json);

  static const toJsonFactory = _$JocataInvoiceSummaryResponseToJson;
  Map<String, dynamic> toJson() => _$JocataInvoiceSummaryResponseToJson(this);

  @JsonKey(
      name: 'response',
      includeIfNull: false,
      defaultValue: <InvoicesRequestSummary>[])
  final List<InvoicesRequestSummary>? response;
  static const fromJsonFactory = _$JocataInvoiceSummaryResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $JocataInvoiceSummaryResponseExtension
    on JocataInvoiceSummaryResponse {
  JocataInvoiceSummaryResponse copyWith(
      {List<InvoicesRequestSummary>? response}) {
    return JocataInvoiceSummaryResponse(response: response ?? this.response);
  }

  JocataInvoiceSummaryResponse copyWithWrapped(
      {Wrapped<List<InvoicesRequestSummary>?>? response}) {
    return JocataInvoiceSummaryResponse(
        response: (response != null ? response.value : this.response));
  }
}

@JsonSerializable(explicitToJson: true)
class SalesChannelResponse {
  const SalesChannelResponse({
    required this.code,
    required this.description,
  });

  factory SalesChannelResponse.fromJson(Map<String, dynamic> json) =>
      _$SalesChannelResponseFromJson(json);

  static const toJsonFactory = _$SalesChannelResponseToJson;
  Map<String, dynamic> toJson() => _$SalesChannelResponseToJson(this);

  @JsonKey(
    name: 'code',
    includeIfNull: false,
    toJson: salesChannelResponseCodeToJson,
    fromJson: salesChannelResponseCodeFromJson,
  )
  final enums.SalesChannelResponseCode code;
  @JsonKey(name: 'description', includeIfNull: false)
  final String description;
  static const fromJsonFactory = _$SalesChannelResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $SalesChannelResponseExtension on SalesChannelResponse {
  SalesChannelResponse copyWith(
      {enums.SalesChannelResponseCode? code, String? description}) {
    return SalesChannelResponse(
        code: code ?? this.code, description: description ?? this.description);
  }

  SalesChannelResponse copyWithWrapped(
      {Wrapped<enums.SalesChannelResponseCode>? code,
      Wrapped<String>? description}) {
    return SalesChannelResponse(
        code: (code != null ? code.value : this.code),
        description:
            (description != null ? description.value : this.description));
  }
}

@JsonSerializable(explicitToJson: true)
class AmountRange {
  const AmountRange({
    required this.min,
    required this.max,
  });

  factory AmountRange.fromJson(Map<String, dynamic> json) =>
      _$AmountRangeFromJson(json);

  static const toJsonFactory = _$AmountRangeToJson;
  Map<String, dynamic> toJson() => _$AmountRangeToJson(this);

  @JsonKey(name: 'min', includeIfNull: false)
  final Money min;
  @JsonKey(name: 'max', includeIfNull: false)
  final Money max;
  static const fromJsonFactory = _$AmountRangeFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AmountRangeExtension on AmountRange {
  AmountRange copyWith({Money? min, Money? max}) {
    return AmountRange(min: min ?? this.min, max: max ?? this.max);
  }

  AmountRange copyWithWrapped({Wrapped<Money>? min, Wrapped<Money>? max}) {
    return AmountRange(
        min: (min != null ? min.value : this.min),
        max: (max != null ? max.value : this.max));
  }
}

@JsonSerializable(explicitToJson: true)
class InstallmentInterval {
  const InstallmentInterval({
    this.interval,
    this.count,
  });

  factory InstallmentInterval.fromJson(Map<String, dynamic> json) =>
      _$InstallmentIntervalFromJson(json);

  static const toJsonFactory = _$InstallmentIntervalToJson;
  Map<String, dynamic> toJson() => _$InstallmentIntervalToJson(this);

  @JsonKey(
    name: 'interval',
    includeIfNull: false,
    toJson: installmentIntervalIntervalNullableToJson,
    fromJson: installmentIntervalIntervalNullableFromJson,
  )
  final enums.InstallmentIntervalInterval? interval;
  @JsonKey(name: 'count', includeIfNull: false)
  final int? count;
  static const fromJsonFactory = _$InstallmentIntervalFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $InstallmentIntervalExtension on InstallmentInterval {
  InstallmentInterval copyWith(
      {enums.InstallmentIntervalInterval? interval, int? count}) {
    return InstallmentInterval(
        interval: interval ?? this.interval, count: count ?? this.count);
  }

  InstallmentInterval copyWithWrapped(
      {Wrapped<enums.InstallmentIntervalInterval?>? interval,
      Wrapped<int?>? count}) {
    return InstallmentInterval(
        interval: (interval != null ? interval.value : this.interval),
        count: (count != null ? count.value : this.count));
  }
}

@JsonSerializable(explicitToJson: true)
class InterestRange {
  const InterestRange({
    required this.min,
    required this.max,
  });

  factory InterestRange.fromJson(Map<String, dynamic> json) =>
      _$InterestRangeFromJson(json);

  static const toJsonFactory = _$InterestRangeToJson;
  Map<String, dynamic> toJson() => _$InterestRangeToJson(this);

  @JsonKey(name: 'min', includeIfNull: false)
  final int min;
  @JsonKey(name: 'max', includeIfNull: false)
  final int max;
  static const fromJsonFactory = _$InterestRangeFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $InterestRangeExtension on InterestRange {
  InterestRange copyWith({int? min, int? max}) {
    return InterestRange(min: min ?? this.min, max: max ?? this.max);
  }

  InterestRange copyWithWrapped({Wrapped<int>? min, Wrapped<int>? max}) {
    return InterestRange(
        min: (min != null ? min.value : this.min),
        max: (max != null ? max.value : this.max));
  }
}

@JsonSerializable(explicitToJson: true)
class LocalTime {
  const LocalTime({
    this.hour,
    this.minute,
    this.second,
    this.nano,
  });

  factory LocalTime.fromJson(Map<String, dynamic> json) =>
      _$LocalTimeFromJson(json);

  static const toJsonFactory = _$LocalTimeToJson;
  Map<String, dynamic> toJson() => _$LocalTimeToJson(this);

  @JsonKey(name: 'hour', includeIfNull: false)
  final int? hour;
  @JsonKey(name: 'minute', includeIfNull: false)
  final int? minute;
  @JsonKey(name: 'second', includeIfNull: false)
  final int? second;
  @JsonKey(name: 'nano', includeIfNull: false)
  final int? nano;
  static const fromJsonFactory = _$LocalTimeFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $LocalTimeExtension on LocalTime {
  LocalTime copyWith({int? hour, int? minute, int? second, int? nano}) {
    return LocalTime(
        hour: hour ?? this.hour,
        minute: minute ?? this.minute,
        second: second ?? this.second,
        nano: nano ?? this.nano);
  }

  LocalTime copyWithWrapped(
      {Wrapped<int?>? hour,
      Wrapped<int?>? minute,
      Wrapped<int?>? second,
      Wrapped<int?>? nano}) {
    return LocalTime(
        hour: (hour != null ? hour.value : this.hour),
        minute: (minute != null ? minute.value : this.minute),
        second: (second != null ? second.value : this.second),
        nano: (nano != null ? nano.value : this.nano));
  }
}

@JsonSerializable(explicitToJson: true)
class StaticDetails {
  const StaticDetails({
    required this.feeFreePeriod,
    required this.cashbackPercentage,
    required this.fileTypes,
    required this.maxSize,
    required this.revolvingFeePercentage,
    required this.minimumPrincipalRepaymentPercentage,
    required this.lateFee,
    required this.minimumCreditLimitAmountOnApproval,
    required this.vatTemplateUrl,
    this.auditedFinancialStatementTemplateUrl,
    this.interestRange,
    this.securedInterestRange,
    this.amountRange,
    this.securedAmountRange,
    this.processingFee,
    this.feeInterestPerDay,
    this.lateFeeGracePeriod,
    this.dueDays,
    this.maxLimitPercentage,
    this.productType,
    this.installmentInterval,
    this.minimumDisbursementAmount,
    this.minimumRepaymentAmount,
    this.autoPaymentTime,
    this.creditAutoPaymentTime,
  });

  factory StaticDetails.fromJson(Map<String, dynamic> json) =>
      _$StaticDetailsFromJson(json);

  static const toJsonFactory = _$StaticDetailsToJson;
  Map<String, dynamic> toJson() => _$StaticDetailsToJson(this);

  @JsonKey(name: 'feeFreePeriod', includeIfNull: false)
  final int feeFreePeriod;
  @JsonKey(name: 'cashbackPercentage', includeIfNull: false)
  final double cashbackPercentage;
  @JsonKey(name: 'fileTypes', includeIfNull: false, defaultValue: <String>[])
  final List<String> fileTypes;
  @JsonKey(name: 'maxSize', includeIfNull: false)
  final int maxSize;
  @JsonKey(name: 'revolvingFeePercentage', includeIfNull: false)
  final double revolvingFeePercentage;
  @JsonKey(name: 'minimumPrincipalRepaymentPercentage', includeIfNull: false)
  final double minimumPrincipalRepaymentPercentage;
  @JsonKey(name: 'lateFee', includeIfNull: false)
  final Money lateFee;
  @JsonKey(name: 'minimumCreditLimitAmountOnApproval', includeIfNull: false)
  final Money minimumCreditLimitAmountOnApproval;
  @JsonKey(name: 'vatTemplateUrl', includeIfNull: false)
  final String vatTemplateUrl;
  @JsonKey(name: 'auditedFinancialStatementTemplateUrl', includeIfNull: false)
  final String? auditedFinancialStatementTemplateUrl;
  @JsonKey(name: 'interestRange', includeIfNull: false)
  final InterestRange? interestRange;
  @JsonKey(name: 'securedInterestRange', includeIfNull: false)
  final InterestRange? securedInterestRange;
  @JsonKey(name: 'amountRange', includeIfNull: false)
  final AmountRange? amountRange;
  @JsonKey(name: 'securedAmountRange', includeIfNull: false)
  final AmountRange? securedAmountRange;
  @JsonKey(name: 'processingFee', includeIfNull: false)
  final Money? processingFee;
  @JsonKey(name: 'feeInterestPerDay', includeIfNull: false)
  final double? feeInterestPerDay;
  @JsonKey(name: 'lateFeeGracePeriod', includeIfNull: false)
  final int? lateFeeGracePeriod;
  @JsonKey(name: 'dueDays', includeIfNull: false)
  final int? dueDays;
  @JsonKey(name: 'maxLimitPercentage', includeIfNull: false)
  final double? maxLimitPercentage;
  @JsonKey(
    name: 'productType',
    includeIfNull: false,
    toJson: productTypeNullableToJson,
    fromJson: productTypeNullableFromJson,
  )
  final enums.ProductType? productType;
  @JsonKey(name: 'installmentInterval', includeIfNull: false)
  final InstallmentInterval? installmentInterval;
  @JsonKey(name: 'minimumDisbursementAmount', includeIfNull: false)
  final Money? minimumDisbursementAmount;
  @JsonKey(name: 'minimumRepaymentAmount', includeIfNull: false)
  final Money? minimumRepaymentAmount;
  @JsonKey(name: 'autoPaymentTime', includeIfNull: false)
  final LocalTime? autoPaymentTime;
  @JsonKey(name: 'creditAutoPaymentTime', includeIfNull: false)
  final String? creditAutoPaymentTime;
  static const fromJsonFactory = _$StaticDetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $StaticDetailsExtension on StaticDetails {
  StaticDetails copyWith(
      {int? feeFreePeriod,
      double? cashbackPercentage,
      List<String>? fileTypes,
      int? maxSize,
      double? revolvingFeePercentage,
      double? minimumPrincipalRepaymentPercentage,
      Money? lateFee,
      Money? minimumCreditLimitAmountOnApproval,
      String? vatTemplateUrl,
      String? auditedFinancialStatementTemplateUrl,
      InterestRange? interestRange,
      InterestRange? securedInterestRange,
      AmountRange? amountRange,
      AmountRange? securedAmountRange,
      Money? processingFee,
      double? feeInterestPerDay,
      int? lateFeeGracePeriod,
      int? dueDays,
      double? maxLimitPercentage,
      enums.ProductType? productType,
      InstallmentInterval? installmentInterval,
      Money? minimumDisbursementAmount,
      Money? minimumRepaymentAmount,
      LocalTime? autoPaymentTime,
      String? creditAutoPaymentTime}) {
    return StaticDetails(
        feeFreePeriod: feeFreePeriod ?? this.feeFreePeriod,
        cashbackPercentage: cashbackPercentage ?? this.cashbackPercentage,
        fileTypes: fileTypes ?? this.fileTypes,
        maxSize: maxSize ?? this.maxSize,
        revolvingFeePercentage:
            revolvingFeePercentage ?? this.revolvingFeePercentage,
        minimumPrincipalRepaymentPercentage:
            minimumPrincipalRepaymentPercentage ??
                this.minimumPrincipalRepaymentPercentage,
        lateFee: lateFee ?? this.lateFee,
        minimumCreditLimitAmountOnApproval:
            minimumCreditLimitAmountOnApproval ??
                this.minimumCreditLimitAmountOnApproval,
        vatTemplateUrl: vatTemplateUrl ?? this.vatTemplateUrl,
        auditedFinancialStatementTemplateUrl:
            auditedFinancialStatementTemplateUrl ??
                this.auditedFinancialStatementTemplateUrl,
        interestRange: interestRange ?? this.interestRange,
        securedInterestRange: securedInterestRange ?? this.securedInterestRange,
        amountRange: amountRange ?? this.amountRange,
        securedAmountRange: securedAmountRange ?? this.securedAmountRange,
        processingFee: processingFee ?? this.processingFee,
        feeInterestPerDay: feeInterestPerDay ?? this.feeInterestPerDay,
        lateFeeGracePeriod: lateFeeGracePeriod ?? this.lateFeeGracePeriod,
        dueDays: dueDays ?? this.dueDays,
        maxLimitPercentage: maxLimitPercentage ?? this.maxLimitPercentage,
        productType: productType ?? this.productType,
        installmentInterval: installmentInterval ?? this.installmentInterval,
        minimumDisbursementAmount:
            minimumDisbursementAmount ?? this.minimumDisbursementAmount,
        minimumRepaymentAmount:
            minimumRepaymentAmount ?? this.minimumRepaymentAmount,
        autoPaymentTime: autoPaymentTime ?? this.autoPaymentTime,
        creditAutoPaymentTime:
            creditAutoPaymentTime ?? this.creditAutoPaymentTime);
  }

  StaticDetails copyWithWrapped(
      {Wrapped<int>? feeFreePeriod,
      Wrapped<double>? cashbackPercentage,
      Wrapped<List<String>>? fileTypes,
      Wrapped<int>? maxSize,
      Wrapped<double>? revolvingFeePercentage,
      Wrapped<double>? minimumPrincipalRepaymentPercentage,
      Wrapped<Money>? lateFee,
      Wrapped<Money>? minimumCreditLimitAmountOnApproval,
      Wrapped<String>? vatTemplateUrl,
      Wrapped<String?>? auditedFinancialStatementTemplateUrl,
      Wrapped<InterestRange?>? interestRange,
      Wrapped<InterestRange?>? securedInterestRange,
      Wrapped<AmountRange?>? amountRange,
      Wrapped<AmountRange?>? securedAmountRange,
      Wrapped<Money?>? processingFee,
      Wrapped<double?>? feeInterestPerDay,
      Wrapped<int?>? lateFeeGracePeriod,
      Wrapped<int?>? dueDays,
      Wrapped<double?>? maxLimitPercentage,
      Wrapped<enums.ProductType?>? productType,
      Wrapped<InstallmentInterval?>? installmentInterval,
      Wrapped<Money?>? minimumDisbursementAmount,
      Wrapped<Money?>? minimumRepaymentAmount,
      Wrapped<LocalTime?>? autoPaymentTime,
      Wrapped<String?>? creditAutoPaymentTime}) {
    return StaticDetails(
        feeFreePeriod:
            (feeFreePeriod != null ? feeFreePeriod.value : this.feeFreePeriod),
        cashbackPercentage: (cashbackPercentage != null
            ? cashbackPercentage.value
            : this.cashbackPercentage),
        fileTypes: (fileTypes != null ? fileTypes.value : this.fileTypes),
        maxSize: (maxSize != null ? maxSize.value : this.maxSize),
        revolvingFeePercentage: (revolvingFeePercentage != null
            ? revolvingFeePercentage.value
            : this.revolvingFeePercentage),
        minimumPrincipalRepaymentPercentage: (minimumPrincipalRepaymentPercentage != null
            ? minimumPrincipalRepaymentPercentage.value
            : this.minimumPrincipalRepaymentPercentage),
        lateFee: (lateFee != null ? lateFee.value : this.lateFee),
        minimumCreditLimitAmountOnApproval: (minimumCreditLimitAmountOnApproval != null
            ? minimumCreditLimitAmountOnApproval.value
            : this.minimumCreditLimitAmountOnApproval),
        vatTemplateUrl: (vatTemplateUrl != null
            ? vatTemplateUrl.value
            : this.vatTemplateUrl),
        auditedFinancialStatementTemplateUrl: (auditedFinancialStatementTemplateUrl != null
            ? auditedFinancialStatementTemplateUrl.value
            : this.auditedFinancialStatementTemplateUrl),
        interestRange:
            (interestRange != null ? interestRange.value : this.interestRange),
        securedInterestRange: (securedInterestRange != null
            ? securedInterestRange.value
            : this.securedInterestRange),
        amountRange:
            (amountRange != null ? amountRange.value : this.amountRange),
        securedAmountRange: (securedAmountRange != null
            ? securedAmountRange.value
            : this.securedAmountRange),
        processingFee:
            (processingFee != null ? processingFee.value : this.processingFee),
        feeInterestPerDay: (feeInterestPerDay != null
            ? feeInterestPerDay.value
            : this.feeInterestPerDay),
        lateFeeGracePeriod: (lateFeeGracePeriod != null
            ? lateFeeGracePeriod.value
            : this.lateFeeGracePeriod),
        dueDays: (dueDays != null ? dueDays.value : this.dueDays),
        maxLimitPercentage: (maxLimitPercentage != null
            ? maxLimitPercentage.value
            : this.maxLimitPercentage),
        productType:
            (productType != null ? productType.value : this.productType),
        installmentInterval: (installmentInterval != null
            ? installmentInterval.value
            : this.installmentInterval),
        minimumDisbursementAmount: (minimumDisbursementAmount != null
            ? minimumDisbursementAmount.value
            : this.minimumDisbursementAmount),
        minimumRepaymentAmount: (minimumRepaymentAmount != null ? minimumRepaymentAmount.value : this.minimumRepaymentAmount),
        autoPaymentTime: (autoPaymentTime != null ? autoPaymentTime.value : this.autoPaymentTime),
        creditAutoPaymentTime: (creditAutoPaymentTime != null ? creditAutoPaymentTime.value : this.creditAutoPaymentTime));
  }
}

@JsonSerializable(explicitToJson: true)
class NonRegisteredVatReason {
  const NonRegisteredVatReason({
    this.type,
    this.en,
    this.ar,
  });

  factory NonRegisteredVatReason.fromJson(Map<String, dynamic> json) =>
      _$NonRegisteredVatReasonFromJson(json);

  static const toJsonFactory = _$NonRegisteredVatReasonToJson;
  Map<String, dynamic> toJson() => _$NonRegisteredVatReasonToJson(this);

  @JsonKey(
    name: 'type',
    includeIfNull: false,
    toJson: nonRegisteredVatReasonTypeNullableToJson,
    fromJson: nonRegisteredVatReasonTypeNullableFromJson,
  )
  final enums.NonRegisteredVatReasonType? type;
  @JsonKey(name: 'en', includeIfNull: false)
  final String? en;
  @JsonKey(name: 'ar', includeIfNull: false)
  final String? ar;
  static const fromJsonFactory = _$NonRegisteredVatReasonFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $NonRegisteredVatReasonExtension on NonRegisteredVatReason {
  NonRegisteredVatReason copyWith(
      {enums.NonRegisteredVatReasonType? type, String? en, String? ar}) {
    return NonRegisteredVatReason(
        type: type ?? this.type, en: en ?? this.en, ar: ar ?? this.ar);
  }

  NonRegisteredVatReason copyWithWrapped(
      {Wrapped<enums.NonRegisteredVatReasonType?>? type,
      Wrapped<String?>? en,
      Wrapped<String?>? ar}) {
    return NonRegisteredVatReason(
        type: (type != null ? type.value : this.type),
        en: (en != null ? en.value : this.en),
        ar: (ar != null ? ar.value : this.ar));
  }
}

@JsonSerializable(explicitToJson: true)
class EmployeeCountResponse {
  const EmployeeCountResponse({
    required this.code,
    required this.description,
  });

  factory EmployeeCountResponse.fromJson(Map<String, dynamic> json) =>
      _$EmployeeCountResponseFromJson(json);

  static const toJsonFactory = _$EmployeeCountResponseToJson;
  Map<String, dynamic> toJson() => _$EmployeeCountResponseToJson(this);

  @JsonKey(
    name: 'code',
    includeIfNull: false,
    toJson: employeeCountResponseCodeToJson,
    fromJson: employeeCountResponseCodeFromJson,
  )
  final enums.EmployeeCountResponseCode code;
  @JsonKey(name: 'description', includeIfNull: false)
  final String description;
  static const fromJsonFactory = _$EmployeeCountResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EmployeeCountResponseExtension on EmployeeCountResponse {
  EmployeeCountResponse copyWith(
      {enums.EmployeeCountResponseCode? code, String? description}) {
    return EmployeeCountResponse(
        code: code ?? this.code, description: description ?? this.description);
  }

  EmployeeCountResponse copyWithWrapped(
      {Wrapped<enums.EmployeeCountResponseCode>? code,
      Wrapped<String>? description}) {
    return EmployeeCountResponse(
        code: (code != null ? code.value : this.code),
        description:
            (description != null ? description.value : this.description));
  }
}

@JsonSerializable(explicitToJson: true)
class GetOffersResponse {
  const GetOffersResponse({
    this.nonEligibilityDetails,
    this.items,
    this.total,
  });

  factory GetOffersResponse.fromJson(Map<String, dynamic> json) =>
      _$GetOffersResponseFromJson(json);

  static const toJsonFactory = _$GetOffersResponseToJson;
  Map<String, dynamic> toJson() => _$GetOffersResponseToJson(this);

  @JsonKey(name: 'nonEligibilityDetails', includeIfNull: false)
  final NonEligibilityDetails? nonEligibilityDetails;
  @JsonKey(
      name: 'items', includeIfNull: false, defaultValue: <PreQualifiedOffer>[])
  final List<PreQualifiedOffer>? items;
  @JsonKey(name: 'total', includeIfNull: false)
  final int? total;
  static const fromJsonFactory = _$GetOffersResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $GetOffersResponseExtension on GetOffersResponse {
  GetOffersResponse copyWith(
      {NonEligibilityDetails? nonEligibilityDetails,
      List<PreQualifiedOffer>? items,
      int? total}) {
    return GetOffersResponse(
        nonEligibilityDetails:
            nonEligibilityDetails ?? this.nonEligibilityDetails,
        items: items ?? this.items,
        total: total ?? this.total);
  }

  GetOffersResponse copyWithWrapped(
      {Wrapped<NonEligibilityDetails?>? nonEligibilityDetails,
      Wrapped<List<PreQualifiedOffer>?>? items,
      Wrapped<int?>? total}) {
    return GetOffersResponse(
        nonEligibilityDetails: (nonEligibilityDetails != null
            ? nonEligibilityDetails.value
            : this.nonEligibilityDetails),
        items: (items != null ? items.value : this.items),
        total: (total != null ? total.value : this.total));
  }
}

@JsonSerializable(explicitToJson: true)
class NonEligibilityDetails {
  const NonEligibilityDetails({
    required this.description,
    required this.subDescription,
    required this.isUpdateDocument,
    required this.reason,
  });

  factory NonEligibilityDetails.fromJson(Map<String, dynamic> json) =>
      _$NonEligibilityDetailsFromJson(json);

  static const toJsonFactory = _$NonEligibilityDetailsToJson;
  Map<String, dynamic> toJson() => _$NonEligibilityDetailsToJson(this);

  @JsonKey(name: 'description', includeIfNull: false)
  final String description;
  @JsonKey(name: 'subDescription', includeIfNull: false)
  final String subDescription;
  @JsonKey(name: 'isUpdateDocument', includeIfNull: false)
  final bool isUpdateDocument;
  @JsonKey(
    name: 'reason',
    includeIfNull: false,
    toJson: nonEligibilityDetailsReasonToJson,
    fromJson: nonEligibilityDetailsReasonFromJson,
  )
  final enums.NonEligibilityDetailsReason reason;
  static const fromJsonFactory = _$NonEligibilityDetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $NonEligibilityDetailsExtension on NonEligibilityDetails {
  NonEligibilityDetails copyWith(
      {String? description,
      String? subDescription,
      bool? isUpdateDocument,
      enums.NonEligibilityDetailsReason? reason}) {
    return NonEligibilityDetails(
        description: description ?? this.description,
        subDescription: subDescription ?? this.subDescription,
        isUpdateDocument: isUpdateDocument ?? this.isUpdateDocument,
        reason: reason ?? this.reason);
  }

  NonEligibilityDetails copyWithWrapped(
      {Wrapped<String>? description,
      Wrapped<String>? subDescription,
      Wrapped<bool>? isUpdateDocument,
      Wrapped<enums.NonEligibilityDetailsReason>? reason}) {
    return NonEligibilityDetails(
        description:
            (description != null ? description.value : this.description),
        subDescription: (subDescription != null
            ? subDescription.value
            : this.subDescription),
        isUpdateDocument: (isUpdateDocument != null
            ? isUpdateDocument.value
            : this.isUpdateDocument),
        reason: (reason != null ? reason.value : this.reason));
  }
}

@JsonSerializable(explicitToJson: true)
class PreQualifiedOffer {
  const PreQualifiedOffer({
    required this.money,
    required this.productType,
  });

  factory PreQualifiedOffer.fromJson(Map<String, dynamic> json) =>
      _$PreQualifiedOfferFromJson(json);

  static const toJsonFactory = _$PreQualifiedOfferToJson;
  Map<String, dynamic> toJson() => _$PreQualifiedOfferToJson(this);

  @JsonKey(name: 'money', includeIfNull: false)
  final Money money;
  @JsonKey(
    name: 'productType',
    includeIfNull: false,
    toJson: productTypeToJson,
    fromJson: productTypeFromJson,
  )
  final enums.ProductType productType;
  static const fromJsonFactory = _$PreQualifiedOfferFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $PreQualifiedOfferExtension on PreQualifiedOffer {
  PreQualifiedOffer copyWith({Money? money, enums.ProductType? productType}) {
    return PreQualifiedOffer(
        money: money ?? this.money,
        productType: productType ?? this.productType);
  }

  PreQualifiedOffer copyWithWrapped(
      {Wrapped<Money>? money, Wrapped<enums.ProductType>? productType}) {
    return PreQualifiedOffer(
        money: (money != null ? money.value : this.money),
        productType:
            (productType != null ? productType.value : this.productType));
  }
}

@JsonSerializable(explicitToJson: true)
class MultiUserApprovalsInfoDto {
  const MultiUserApprovalsInfoDto({
    this.name,
    this.status,
    this.timestamp,
  });

  factory MultiUserApprovalsInfoDto.fromJson(Map<String, dynamic> json) =>
      _$MultiUserApprovalsInfoDtoFromJson(json);

  static const toJsonFactory = _$MultiUserApprovalsInfoDtoToJson;
  Map<String, dynamic> toJson() => _$MultiUserApprovalsInfoDtoToJson(this);

  @JsonKey(name: 'name', includeIfNull: false)
  final String? name;
  @JsonKey(
    name: 'status',
    includeIfNull: false,
    toJson: multiUserApprovalsInfoDtoStatusNullableToJson,
    fromJson: multiUserApprovalsInfoDtoStatusNullableFromJson,
  )
  final enums.MultiUserApprovalsInfoDtoStatus? status;
  @JsonKey(name: 'timestamp', includeIfNull: false)
  final String? timestamp;
  static const fromJsonFactory = _$MultiUserApprovalsInfoDtoFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MultiUserApprovalsInfoDtoExtension on MultiUserApprovalsInfoDto {
  MultiUserApprovalsInfoDto copyWith(
      {String? name,
      enums.MultiUserApprovalsInfoDtoStatus? status,
      String? timestamp}) {
    return MultiUserApprovalsInfoDto(
        name: name ?? this.name,
        status: status ?? this.status,
        timestamp: timestamp ?? this.timestamp);
  }

  MultiUserApprovalsInfoDto copyWithWrapped(
      {Wrapped<String?>? name,
      Wrapped<enums.MultiUserApprovalsInfoDtoStatus?>? status,
      Wrapped<String?>? timestamp}) {
    return MultiUserApprovalsInfoDto(
        name: (name != null ? name.value : this.name),
        status: (status != null ? status.value : this.status),
        timestamp: (timestamp != null ? timestamp.value : this.timestamp));
  }
}

@JsonSerializable(explicitToJson: true)
class GetRequestsParams {
  const GetRequestsParams({
    this.page,
    this.size,
    this.id,
    this.sort,
    this.domainTypes,
    this.fromDate,
    this.toDate,
    this.status,
  });

  factory GetRequestsParams.fromJson(Map<String, dynamic> json) =>
      _$GetRequestsParamsFromJson(json);

  static const toJsonFactory = _$GetRequestsParamsToJson;
  Map<String, dynamic> toJson() => _$GetRequestsParamsToJson(this);

  @JsonKey(name: 'page', includeIfNull: false)
  final int? page;
  @JsonKey(name: 'size', includeIfNull: false)
  final int? size;
  @JsonKey(name: 'id', includeIfNull: false)
  final String? id;
  @JsonKey(name: 'sort', includeIfNull: false)
  final String? sort;
  @JsonKey(
    name: 'domainTypes',
    includeIfNull: false,
    toJson: getRequestsParamsDomainTypesListToJson,
    fromJson: getRequestsParamsDomainTypesListFromJson,
  )
  final List<enums.GetRequestsParamsDomainTypes>? domainTypes;
  @JsonKey(name: 'fromDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime? fromDate;
  @JsonKey(name: 'toDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime? toDate;
  @JsonKey(
    name: 'status',
    includeIfNull: false,
    toJson: getRequestsParamsStatusListToJson,
    fromJson: getRequestsParamsStatusListFromJson,
  )
  final List<enums.GetRequestsParamsStatus>? status;
  static const fromJsonFactory = _$GetRequestsParamsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $GetRequestsParamsExtension on GetRequestsParams {
  GetRequestsParams copyWith(
      {int? page,
      int? size,
      String? id,
      String? sort,
      List<enums.GetRequestsParamsDomainTypes>? domainTypes,
      DateTime? fromDate,
      DateTime? toDate,
      List<enums.GetRequestsParamsStatus>? status}) {
    return GetRequestsParams(
        page: page ?? this.page,
        size: size ?? this.size,
        id: id ?? this.id,
        sort: sort ?? this.sort,
        domainTypes: domainTypes ?? this.domainTypes,
        fromDate: fromDate ?? this.fromDate,
        toDate: toDate ?? this.toDate,
        status: status ?? this.status);
  }

  GetRequestsParams copyWithWrapped(
      {Wrapped<int?>? page,
      Wrapped<int?>? size,
      Wrapped<String?>? id,
      Wrapped<String?>? sort,
      Wrapped<List<enums.GetRequestsParamsDomainTypes>?>? domainTypes,
      Wrapped<DateTime?>? fromDate,
      Wrapped<DateTime?>? toDate,
      Wrapped<List<enums.GetRequestsParamsStatus>?>? status}) {
    return GetRequestsParams(
        page: (page != null ? page.value : this.page),
        size: (size != null ? size.value : this.size),
        id: (id != null ? id.value : this.id),
        sort: (sort != null ? sort.value : this.sort),
        domainTypes:
            (domainTypes != null ? domainTypes.value : this.domainTypes),
        fromDate: (fromDate != null ? fromDate.value : this.fromDate),
        toDate: (toDate != null ? toDate.value : this.toDate),
        status: (status != null ? status.value : this.status));
  }
}

@JsonSerializable(explicitToJson: true)
class GetMultiUserRequestsResponse {
  const GetMultiUserRequestsResponse({
    this.totalNumberRecords,
    this.totalNumberPages,
    this.requests,
  });

  factory GetMultiUserRequestsResponse.fromJson(Map<String, dynamic> json) =>
      _$GetMultiUserRequestsResponseFromJson(json);

  static const toJsonFactory = _$GetMultiUserRequestsResponseToJson;
  Map<String, dynamic> toJson() => _$GetMultiUserRequestsResponseToJson(this);

  @JsonKey(name: 'totalNumberRecords', includeIfNull: false)
  final int? totalNumberRecords;
  @JsonKey(name: 'totalNumberPages', includeIfNull: false)
  final int? totalNumberPages;
  @JsonKey(
      name: 'requests',
      includeIfNull: false,
      defaultValue: <MultiUserResponseDTO>[])
  final List<MultiUserResponseDTO>? requests;
  static const fromJsonFactory = _$GetMultiUserRequestsResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $GetMultiUserRequestsResponseExtension
    on GetMultiUserRequestsResponse {
  GetMultiUserRequestsResponse copyWith(
      {int? totalNumberRecords,
      int? totalNumberPages,
      List<MultiUserResponseDTO>? requests}) {
    return GetMultiUserRequestsResponse(
        totalNumberRecords: totalNumberRecords ?? this.totalNumberRecords,
        totalNumberPages: totalNumberPages ?? this.totalNumberPages,
        requests: requests ?? this.requests);
  }

  GetMultiUserRequestsResponse copyWithWrapped(
      {Wrapped<int?>? totalNumberRecords,
      Wrapped<int?>? totalNumberPages,
      Wrapped<List<MultiUserResponseDTO>?>? requests}) {
    return GetMultiUserRequestsResponse(
        totalNumberRecords: (totalNumberRecords != null
            ? totalNumberRecords.value
            : this.totalNumberRecords),
        totalNumberPages: (totalNumberPages != null
            ? totalNumberPages.value
            : this.totalNumberPages),
        requests: (requests != null ? requests.value : this.requests));
  }
}

@JsonSerializable(explicitToJson: true)
class JsonNode {
  const JsonNode();

  factory JsonNode.fromJson(Map<String, dynamic> json) =>
      _$JsonNodeFromJson(json);

  static const toJsonFactory = _$JsonNodeToJson;
  Map<String, dynamic> toJson() => _$JsonNodeToJson(this);

  static const fromJsonFactory = _$JsonNodeFromJson;

  @override
  String toString() => jsonEncode(this);
}

@JsonSerializable(explicitToJson: true)
class BankDetails {
  const BankDetails({
    this.bankName,
    this.bankCode,
    this.bic,
  });

  factory BankDetails.fromJson(Map<String, dynamic> json) =>
      _$BankDetailsFromJson(json);

  static const toJsonFactory = _$BankDetailsToJson;
  Map<String, dynamic> toJson() => _$BankDetailsToJson(this);

  @JsonKey(name: 'bankName', includeIfNull: false)
  final String? bankName;
  @JsonKey(name: 'bankCode', includeIfNull: false)
  final String? bankCode;
  @JsonKey(name: 'bic', includeIfNull: false)
  final String? bic;
  static const fromJsonFactory = _$BankDetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $BankDetailsExtension on BankDetails {
  BankDetails copyWith({String? bankName, String? bankCode, String? bic}) {
    return BankDetails(
        bankName: bankName ?? this.bankName,
        bankCode: bankCode ?? this.bankCode,
        bic: bic ?? this.bic);
  }

  BankDetails copyWithWrapped(
      {Wrapped<String?>? bankName,
      Wrapped<String?>? bankCode,
      Wrapped<String?>? bic}) {
    return BankDetails(
        bankName: (bankName != null ? bankName.value : this.bankName),
        bankCode: (bankCode != null ? bankCode.value : this.bankCode),
        bic: (bic != null ? bic.value : this.bic));
  }
}

@JsonSerializable(explicitToJson: true)
class CreditArrangement {
  const CreditArrangement({
    this.accounts,
  });

  factory CreditArrangement.fromJson(Map<String, dynamic> json) =>
      _$CreditArrangementFromJson(json);

  static const toJsonFactory = _$CreditArrangementToJson;
  Map<String, dynamic> toJson() => _$CreditArrangementToJson(this);

  @JsonKey(name: 'accounts', includeIfNull: false, defaultValue: <Account>[])
  final List<Account>? accounts;
  static const fromJsonFactory = _$CreditArrangementFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CreditArrangementExtension on CreditArrangement {
  CreditArrangement copyWith({List<Account>? accounts}) {
    return CreditArrangement(accounts: accounts ?? this.accounts);
  }

  CreditArrangement copyWithWrapped({Wrapped<List<Account>?>? accounts}) {
    return CreditArrangement(
        accounts: (accounts != null ? accounts.value : this.accounts));
  }
}

@JsonSerializable(explicitToJson: true)
class EasyCashLimit {
  const EasyCashLimit({
    required this.easyCashPercentageLimit,
    required this.easyCashInterestRate,
    required this.easyCashAvailableLimit,
    required this.easyCashLimit,
    required this.dueDate,
    required this.creditArrangement,
  });

  factory EasyCashLimit.fromJson(Map<String, dynamic> json) =>
      _$EasyCashLimitFromJson(json);

  static const toJsonFactory = _$EasyCashLimitToJson;
  Map<String, dynamic> toJson() => _$EasyCashLimitToJson(this);

  @JsonKey(name: 'easyCashPercentageLimit', includeIfNull: false)
  final double easyCashPercentageLimit;
  @JsonKey(name: 'easyCashInterestRate', includeIfNull: false)
  final double easyCashInterestRate;
  @JsonKey(name: 'easyCashAvailableLimit', includeIfNull: false)
  final Money easyCashAvailableLimit;
  @JsonKey(name: 'easyCashLimit', includeIfNull: false)
  final Money easyCashLimit;
  @JsonKey(name: 'dueDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime dueDate;
  @JsonKey(name: 'creditArrangement', includeIfNull: false)
  final CreditArrangement creditArrangement;
  static const fromJsonFactory = _$EasyCashLimitFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EasyCashLimitExtension on EasyCashLimit {
  EasyCashLimit copyWith(
      {double? easyCashPercentageLimit,
      double? easyCashInterestRate,
      Money? easyCashAvailableLimit,
      Money? easyCashLimit,
      DateTime? dueDate,
      CreditArrangement? creditArrangement}) {
    return EasyCashLimit(
        easyCashPercentageLimit:
            easyCashPercentageLimit ?? this.easyCashPercentageLimit,
        easyCashInterestRate: easyCashInterestRate ?? this.easyCashInterestRate,
        easyCashAvailableLimit:
            easyCashAvailableLimit ?? this.easyCashAvailableLimit,
        easyCashLimit: easyCashLimit ?? this.easyCashLimit,
        dueDate: dueDate ?? this.dueDate,
        creditArrangement: creditArrangement ?? this.creditArrangement);
  }

  EasyCashLimit copyWithWrapped(
      {Wrapped<double>? easyCashPercentageLimit,
      Wrapped<double>? easyCashInterestRate,
      Wrapped<Money>? easyCashAvailableLimit,
      Wrapped<Money>? easyCashLimit,
      Wrapped<DateTime>? dueDate,
      Wrapped<CreditArrangement>? creditArrangement}) {
    return EasyCashLimit(
        easyCashPercentageLimit: (easyCashPercentageLimit != null
            ? easyCashPercentageLimit.value
            : this.easyCashPercentageLimit),
        easyCashInterestRate: (easyCashInterestRate != null
            ? easyCashInterestRate.value
            : this.easyCashInterestRate),
        easyCashAvailableLimit: (easyCashAvailableLimit != null
            ? easyCashAvailableLimit.value
            : this.easyCashAvailableLimit),
        easyCashLimit:
            (easyCashLimit != null ? easyCashLimit.value : this.easyCashLimit),
        dueDate: (dueDate != null ? dueDate.value : this.dueDate),
        creditArrangement: (creditArrangement != null
            ? creditArrangement.value
            : this.creditArrangement));
  }
}

@JsonSerializable(explicitToJson: true)
class DownloadResponse {
  const DownloadResponse({
    this.data,
  });

  factory DownloadResponse.fromJson(Map<String, dynamic> json) =>
      _$DownloadResponseFromJson(json);

  static const toJsonFactory = _$DownloadResponseToJson;
  Map<String, dynamic> toJson() => _$DownloadResponseToJson(this);

  @JsonKey(name: 'data', includeIfNull: false)
  final String? data;
  static const fromJsonFactory = _$DownloadResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $DownloadResponseExtension on DownloadResponse {
  DownloadResponse copyWith({String? data}) {
    return DownloadResponse(data: data ?? this.data);
  }

  DownloadResponse copyWithWrapped({Wrapped<String?>? data}) {
    return DownloadResponse(data: (data != null ? data.value : this.data));
  }
}

@JsonSerializable(explicitToJson: true)
class ApplicationListResponse {
  const ApplicationListResponse({
    this.data,
  });

  factory ApplicationListResponse.fromJson(Map<String, dynamic> json) =>
      _$ApplicationListResponseFromJson(json);

  static const toJsonFactory = _$ApplicationListResponseToJson;
  Map<String, dynamic> toJson() => _$ApplicationListResponseToJson(this);

  @JsonKey(name: 'data', includeIfNull: false, defaultValue: <Application>[])
  final List<Application>? data;
  static const fromJsonFactory = _$ApplicationListResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ApplicationListResponseExtension on ApplicationListResponse {
  ApplicationListResponse copyWith({List<Application>? data}) {
    return ApplicationListResponse(data: data ?? this.data);
  }

  ApplicationListResponse copyWithWrapped({Wrapped<List<Application>?>? data}) {
    return ApplicationListResponse(
        data: (data != null ? data.value : this.data));
  }
}

@JsonSerializable(explicitToJson: true)
class DocumentListResponse {
  const DocumentListResponse({
    this.size,
    this.items,
  });

  factory DocumentListResponse.fromJson(Map<String, dynamic> json) =>
      _$DocumentListResponseFromJson(json);

  static const toJsonFactory = _$DocumentListResponseToJson;
  Map<String, dynamic> toJson() => _$DocumentListResponseToJson(this);

  @JsonKey(name: 'size', includeIfNull: false)
  final int? size;
  @JsonKey(
      name: 'items',
      includeIfNull: false,
      defaultValue: <DocumentMetadataResponse>[])
  final List<DocumentMetadataResponse>? items;
  static const fromJsonFactory = _$DocumentListResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $DocumentListResponseExtension on DocumentListResponse {
  DocumentListResponse copyWith(
      {int? size, List<DocumentMetadataResponse>? items}) {
    return DocumentListResponse(
        size: size ?? this.size, items: items ?? this.items);
  }

  DocumentListResponse copyWithWrapped(
      {Wrapped<int?>? size, Wrapped<List<DocumentMetadataResponse>?>? items}) {
    return DocumentListResponse(
        size: (size != null ? size.value : this.size),
        items: (items != null ? items.value : this.items));
  }
}

@JsonSerializable(explicitToJson: true)
class GetCreditAgreementResponse {
  const GetCreditAgreementResponse({
    this.data,
  });

  factory GetCreditAgreementResponse.fromJson(Map<String, dynamic> json) =>
      _$GetCreditAgreementResponseFromJson(json);

  static const toJsonFactory = _$GetCreditAgreementResponseToJson;
  Map<String, dynamic> toJson() => _$GetCreditAgreementResponseToJson(this);

  @JsonKey(name: 'data', includeIfNull: false)
  final String? data;
  static const fromJsonFactory = _$GetCreditAgreementResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $GetCreditAgreementResponseExtension on GetCreditAgreementResponse {
  GetCreditAgreementResponse copyWith({String? data}) {
    return GetCreditAgreementResponse(data: data ?? this.data);
  }

  GetCreditAgreementResponse copyWithWrapped({Wrapped<String?>? data}) {
    return GetCreditAgreementResponse(
        data: (data != null ? data.value : this.data));
  }
}

@JsonSerializable(explicitToJson: true)
class RepaymentSchedulePdfResponse {
  const RepaymentSchedulePdfResponse({
    this.pdfString,
  });

  factory RepaymentSchedulePdfResponse.fromJson(Map<String, dynamic> json) =>
      _$RepaymentSchedulePdfResponseFromJson(json);

  static const toJsonFactory = _$RepaymentSchedulePdfResponseToJson;
  Map<String, dynamic> toJson() => _$RepaymentSchedulePdfResponseToJson(this);

  @JsonKey(name: 'pdfString', includeIfNull: false)
  final String? pdfString;
  static const fromJsonFactory = _$RepaymentSchedulePdfResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $RepaymentSchedulePdfResponseExtension
    on RepaymentSchedulePdfResponse {
  RepaymentSchedulePdfResponse copyWith({String? pdfString}) {
    return RepaymentSchedulePdfResponse(pdfString: pdfString ?? this.pdfString);
  }

  RepaymentSchedulePdfResponse copyWithWrapped({Wrapped<String?>? pdfString}) {
    return RepaymentSchedulePdfResponse(
        pdfString: (pdfString != null ? pdfString.value : this.pdfString));
  }
}

@JsonSerializable(explicitToJson: true)
class AutoPaymentInfo {
  const AutoPaymentInfo({
    required this.amount,
    required this.fee,
    required this.totalOutstandingBalance,
    required this.linkedAccountsOutstanding,
    this.feeFreeDate,
    required this.fullRepaymentDoneInCurrentCycle,
    required this.currency,
    required this.paymentDate,
    required this.minimumRepaymentAmount,
    this.delinquentAccountDetails,
    this.amountPaidInCurrentCycle,
    this.preferences,
    this.feesPerDay,
    this.customerId,
    this.totalFeeBalance,
    this.schedule,
  });

  factory AutoPaymentInfo.fromJson(Map<String, dynamic> json) =>
      _$AutoPaymentInfoFromJson(json);

  static const toJsonFactory = _$AutoPaymentInfoToJson;
  Map<String, dynamic> toJson() => _$AutoPaymentInfoToJson(this);

  @JsonKey(name: 'amount', includeIfNull: false)
  final double amount;
  @JsonKey(name: 'fee', includeIfNull: false)
  final double fee;
  @JsonKey(name: 'totalOutstandingBalance', includeIfNull: false)
  final double totalOutstandingBalance;
  @JsonKey(name: 'linkedAccountsOutstanding', includeIfNull: false)
  final double linkedAccountsOutstanding;
  @JsonKey(name: 'feeFreeDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime? feeFreeDate;
  @JsonKey(name: 'fullRepaymentDoneInCurrentCycle', includeIfNull: false)
  final bool fullRepaymentDoneInCurrentCycle;
  @JsonKey(
    name: 'currency',
    includeIfNull: false,
    toJson: currencyToJson,
    fromJson: currencyFromJson,
  )
  final enums.Currency currency;
  @JsonKey(name: 'paymentDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime paymentDate;
  @JsonKey(name: 'minimumRepaymentAmount', includeIfNull: false)
  final double minimumRepaymentAmount;
  @JsonKey(name: 'delinquentAccountDetails', includeIfNull: false)
  final DelinquentAccountDetails? delinquentAccountDetails;
  @JsonKey(name: 'amountPaidInCurrentCycle', includeIfNull: false)
  final double? amountPaidInCurrentCycle;
  @JsonKey(name: 'preferences', includeIfNull: false)
  final PreferencesV2? preferences;
  @JsonKey(name: 'feesPerDay', includeIfNull: false)
  final double? feesPerDay;
  @JsonKey(name: 'customerId', includeIfNull: false)
  final String? customerId;
  @JsonKey(name: 'totalFeeBalance', includeIfNull: false)
  final double? totalFeeBalance;
  @JsonKey(name: 'schedule', includeIfNull: false)
  final Schedule? schedule;
  static const fromJsonFactory = _$AutoPaymentInfoFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AutoPaymentInfoExtension on AutoPaymentInfo {
  AutoPaymentInfo copyWith(
      {double? amount,
      double? fee,
      double? totalOutstandingBalance,
      double? linkedAccountsOutstanding,
      DateTime? feeFreeDate,
      bool? fullRepaymentDoneInCurrentCycle,
      enums.Currency? currency,
      DateTime? paymentDate,
      double? minimumRepaymentAmount,
      DelinquentAccountDetails? delinquentAccountDetails,
      double? amountPaidInCurrentCycle,
      PreferencesV2? preferences,
      double? feesPerDay,
      String? customerId,
      double? totalFeeBalance,
      Schedule? schedule}) {
    return AutoPaymentInfo(
        amount: amount ?? this.amount,
        fee: fee ?? this.fee,
        totalOutstandingBalance:
            totalOutstandingBalance ?? this.totalOutstandingBalance,
        linkedAccountsOutstanding:
            linkedAccountsOutstanding ?? this.linkedAccountsOutstanding,
        feeFreeDate: feeFreeDate ?? this.feeFreeDate,
        fullRepaymentDoneInCurrentCycle: fullRepaymentDoneInCurrentCycle ??
            this.fullRepaymentDoneInCurrentCycle,
        currency: currency ?? this.currency,
        paymentDate: paymentDate ?? this.paymentDate,
        minimumRepaymentAmount:
            minimumRepaymentAmount ?? this.minimumRepaymentAmount,
        delinquentAccountDetails:
            delinquentAccountDetails ?? this.delinquentAccountDetails,
        amountPaidInCurrentCycle:
            amountPaidInCurrentCycle ?? this.amountPaidInCurrentCycle,
        preferences: preferences ?? this.preferences,
        feesPerDay: feesPerDay ?? this.feesPerDay,
        customerId: customerId ?? this.customerId,
        totalFeeBalance: totalFeeBalance ?? this.totalFeeBalance,
        schedule: schedule ?? this.schedule);
  }

  AutoPaymentInfo copyWithWrapped(
      {Wrapped<double>? amount,
      Wrapped<double>? fee,
      Wrapped<double>? totalOutstandingBalance,
      Wrapped<double>? linkedAccountsOutstanding,
      Wrapped<DateTime?>? feeFreeDate,
      Wrapped<bool>? fullRepaymentDoneInCurrentCycle,
      Wrapped<enums.Currency>? currency,
      Wrapped<DateTime>? paymentDate,
      Wrapped<double>? minimumRepaymentAmount,
      Wrapped<DelinquentAccountDetails?>? delinquentAccountDetails,
      Wrapped<double?>? amountPaidInCurrentCycle,
      Wrapped<PreferencesV2?>? preferences,
      Wrapped<double?>? feesPerDay,
      Wrapped<String?>? customerId,
      Wrapped<double?>? totalFeeBalance,
      Wrapped<Schedule?>? schedule}) {
    return AutoPaymentInfo(
        amount: (amount != null ? amount.value : this.amount),
        fee: (fee != null ? fee.value : this.fee),
        totalOutstandingBalance: (totalOutstandingBalance != null
            ? totalOutstandingBalance.value
            : this.totalOutstandingBalance),
        linkedAccountsOutstanding: (linkedAccountsOutstanding != null
            ? linkedAccountsOutstanding.value
            : this.linkedAccountsOutstanding),
        feeFreeDate:
            (feeFreeDate != null ? feeFreeDate.value : this.feeFreeDate),
        fullRepaymentDoneInCurrentCycle:
            (fullRepaymentDoneInCurrentCycle != null
                ? fullRepaymentDoneInCurrentCycle.value
                : this.fullRepaymentDoneInCurrentCycle),
        currency: (currency != null ? currency.value : this.currency),
        paymentDate:
            (paymentDate != null ? paymentDate.value : this.paymentDate),
        minimumRepaymentAmount: (minimumRepaymentAmount != null
            ? minimumRepaymentAmount.value
            : this.minimumRepaymentAmount),
        delinquentAccountDetails: (delinquentAccountDetails != null
            ? delinquentAccountDetails.value
            : this.delinquentAccountDetails),
        amountPaidInCurrentCycle: (amountPaidInCurrentCycle != null
            ? amountPaidInCurrentCycle.value
            : this.amountPaidInCurrentCycle),
        preferences:
            (preferences != null ? preferences.value : this.preferences),
        feesPerDay: (feesPerDay != null ? feesPerDay.value : this.feesPerDay),
        customerId: (customerId != null ? customerId.value : this.customerId),
        totalFeeBalance: (totalFeeBalance != null
            ? totalFeeBalance.value
            : this.totalFeeBalance),
        schedule: (schedule != null ? schedule.value : this.schedule));
  }
}

@JsonSerializable(explicitToJson: true)
class DelinquentAccountDetails {
  const DelinquentAccountDetails({
    this.minimumRepaymentAmount,
    this.locked,
    this.daysPastDue,
    required this.dueDate,
    this.latePaymentFee,
    this.minimumRepaymentAmountExcLateFee,
  });

  factory DelinquentAccountDetails.fromJson(Map<String, dynamic> json) =>
      _$DelinquentAccountDetailsFromJson(json);

  static const toJsonFactory = _$DelinquentAccountDetailsToJson;
  Map<String, dynamic> toJson() => _$DelinquentAccountDetailsToJson(this);

  @JsonKey(name: 'minimumRepaymentAmount', includeIfNull: false)
  final double? minimumRepaymentAmount;
  @JsonKey(name: 'locked', includeIfNull: false)
  final bool? locked;
  @JsonKey(name: 'daysPastDue', includeIfNull: false)
  final int? daysPastDue;
  @JsonKey(name: 'dueDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime dueDate;
  @JsonKey(name: 'latePaymentFee', includeIfNull: false)
  final double? latePaymentFee;
  @JsonKey(name: 'minimumRepaymentAmountExcLateFee', includeIfNull: false)
  final double? minimumRepaymentAmountExcLateFee;
  static const fromJsonFactory = _$DelinquentAccountDetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $DelinquentAccountDetailsExtension on DelinquentAccountDetails {
  DelinquentAccountDetails copyWith(
      {double? minimumRepaymentAmount,
      bool? locked,
      int? daysPastDue,
      DateTime? dueDate,
      double? latePaymentFee,
      double? minimumRepaymentAmountExcLateFee}) {
    return DelinquentAccountDetails(
        minimumRepaymentAmount:
            minimumRepaymentAmount ?? this.minimumRepaymentAmount,
        locked: locked ?? this.locked,
        daysPastDue: daysPastDue ?? this.daysPastDue,
        dueDate: dueDate ?? this.dueDate,
        latePaymentFee: latePaymentFee ?? this.latePaymentFee,
        minimumRepaymentAmountExcLateFee: minimumRepaymentAmountExcLateFee ??
            this.minimumRepaymentAmountExcLateFee);
  }

  DelinquentAccountDetails copyWithWrapped(
      {Wrapped<double?>? minimumRepaymentAmount,
      Wrapped<bool?>? locked,
      Wrapped<int?>? daysPastDue,
      Wrapped<DateTime>? dueDate,
      Wrapped<double?>? latePaymentFee,
      Wrapped<double?>? minimumRepaymentAmountExcLateFee}) {
    return DelinquentAccountDetails(
        minimumRepaymentAmount: (minimumRepaymentAmount != null
            ? minimumRepaymentAmount.value
            : this.minimumRepaymentAmount),
        locked: (locked != null ? locked.value : this.locked),
        daysPastDue:
            (daysPastDue != null ? daysPastDue.value : this.daysPastDue),
        dueDate: (dueDate != null ? dueDate.value : this.dueDate),
        latePaymentFee: (latePaymentFee != null
            ? latePaymentFee.value
            : this.latePaymentFee),
        minimumRepaymentAmountExcLateFee:
            (minimumRepaymentAmountExcLateFee != null
                ? minimumRepaymentAmountExcLateFee.value
                : this.minimumRepaymentAmountExcLateFee));
  }
}

@JsonSerializable(explicitToJson: true)
class CreditLimitDetailsDto {
  const CreditLimitDetailsDto({
    this.limitDetails,
    this.inProgressRequest,
  });

  factory CreditLimitDetailsDto.fromJson(Map<String, dynamic> json) =>
      _$CreditLimitDetailsDtoFromJson(json);

  static const toJsonFactory = _$CreditLimitDetailsDtoToJson;
  Map<String, dynamic> toJson() => _$CreditLimitDetailsDtoToJson(this);

  @JsonKey(name: 'limitDetails', includeIfNull: false)
  final LimitDetails? limitDetails;
  @JsonKey(name: 'inProgressRequest', includeIfNull: false)
  final InProgressRequest? inProgressRequest;
  static const fromJsonFactory = _$CreditLimitDetailsDtoFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CreditLimitDetailsDtoExtension on CreditLimitDetailsDto {
  CreditLimitDetailsDto copyWith(
      {LimitDetails? limitDetails, InProgressRequest? inProgressRequest}) {
    return CreditLimitDetailsDto(
        limitDetails: limitDetails ?? this.limitDetails,
        inProgressRequest: inProgressRequest ?? this.inProgressRequest);
  }

  CreditLimitDetailsDto copyWithWrapped(
      {Wrapped<LimitDetails?>? limitDetails,
      Wrapped<InProgressRequest?>? inProgressRequest}) {
    return CreditLimitDetailsDto(
        limitDetails:
            (limitDetails != null ? limitDetails.value : this.limitDetails),
        inProgressRequest: (inProgressRequest != null
            ? inProgressRequest.value
            : this.inProgressRequest));
  }
}

@JsonSerializable(explicitToJson: true)
class InProgressRequest {
  const InProgressRequest({
    required this.id,
    required this.money,
    required this.date,
  });

  factory InProgressRequest.fromJson(Map<String, dynamic> json) =>
      _$InProgressRequestFromJson(json);

  static const toJsonFactory = _$InProgressRequestToJson;
  Map<String, dynamic> toJson() => _$InProgressRequestToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'money', includeIfNull: false)
  final Money money;
  @JsonKey(name: 'date', includeIfNull: false)
  final DateTime date;
  static const fromJsonFactory = _$InProgressRequestFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $InProgressRequestExtension on InProgressRequest {
  InProgressRequest copyWith({String? id, Money? money, DateTime? date}) {
    return InProgressRequest(
        id: id ?? this.id, money: money ?? this.money, date: date ?? this.date);
  }

  InProgressRequest copyWithWrapped(
      {Wrapped<String>? id, Wrapped<Money>? money, Wrapped<DateTime>? date}) {
    return InProgressRequest(
        id: (id != null ? id.value : this.id),
        money: (money != null ? money.value : this.money),
        date: (date != null ? date.value : this.date));
  }
}

@JsonSerializable(explicitToJson: true)
class LimitDetails {
  const LimitDetails({
    required this.minLimit,
    required this.maxLimit,
    required this.allowIncreaseLimit,
    required this.allowReduceLimit,
  });

  factory LimitDetails.fromJson(Map<String, dynamic> json) =>
      _$LimitDetailsFromJson(json);

  static const toJsonFactory = _$LimitDetailsToJson;
  Map<String, dynamic> toJson() => _$LimitDetailsToJson(this);

  @JsonKey(name: 'minLimit', includeIfNull: false)
  final Money minLimit;
  @JsonKey(name: 'maxLimit', includeIfNull: false)
  final Money maxLimit;
  @JsonKey(name: 'allowIncreaseLimit', includeIfNull: false)
  final bool allowIncreaseLimit;
  @JsonKey(name: 'allowReduceLimit', includeIfNull: false)
  final bool allowReduceLimit;
  static const fromJsonFactory = _$LimitDetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $LimitDetailsExtension on LimitDetails {
  LimitDetails copyWith(
      {Money? minLimit,
      Money? maxLimit,
      bool? allowIncreaseLimit,
      bool? allowReduceLimit}) {
    return LimitDetails(
        minLimit: minLimit ?? this.minLimit,
        maxLimit: maxLimit ?? this.maxLimit,
        allowIncreaseLimit: allowIncreaseLimit ?? this.allowIncreaseLimit,
        allowReduceLimit: allowReduceLimit ?? this.allowReduceLimit);
  }

  LimitDetails copyWithWrapped(
      {Wrapped<Money>? minLimit,
      Wrapped<Money>? maxLimit,
      Wrapped<bool>? allowIncreaseLimit,
      Wrapped<bool>? allowReduceLimit}) {
    return LimitDetails(
        minLimit: (minLimit != null ? minLimit.value : this.minLimit),
        maxLimit: (maxLimit != null ? maxLimit.value : this.maxLimit),
        allowIncreaseLimit: (allowIncreaseLimit != null
            ? allowIncreaseLimit.value
            : this.allowIncreaseLimit),
        allowReduceLimit: (allowReduceLimit != null
            ? allowReduceLimit.value
            : this.allowReduceLimit));
  }
}

@JsonSerializable(explicitToJson: true)
class ApiV1ApplicationsApplicationIdDocumentsPost$RequestBody {
  const ApiV1ApplicationsApplicationIdDocumentsPost$RequestBody({
    required this.file,
  });

  factory ApiV1ApplicationsApplicationIdDocumentsPost$RequestBody.fromJson(
          Map<String, dynamic> json) =>
      _$ApiV1ApplicationsApplicationIdDocumentsPost$RequestBodyFromJson(json);

  static const toJsonFactory =
      _$ApiV1ApplicationsApplicationIdDocumentsPost$RequestBodyToJson;
  Map<String, dynamic> toJson() =>
      _$ApiV1ApplicationsApplicationIdDocumentsPost$RequestBodyToJson(this);

  @JsonKey(name: 'file', includeIfNull: false)
  final String file;
  static const fromJsonFactory =
      _$ApiV1ApplicationsApplicationIdDocumentsPost$RequestBodyFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ApiV1ApplicationsApplicationIdDocumentsPost$RequestBodyExtension
    on ApiV1ApplicationsApplicationIdDocumentsPost$RequestBody {
  ApiV1ApplicationsApplicationIdDocumentsPost$RequestBody copyWith(
      {String? file}) {
    return ApiV1ApplicationsApplicationIdDocumentsPost$RequestBody(
        file: file ?? this.file);
  }

  ApiV1ApplicationsApplicationIdDocumentsPost$RequestBody copyWithWrapped(
      {Wrapped<String>? file}) {
    return ApiV1ApplicationsApplicationIdDocumentsPost$RequestBody(
        file: (file != null ? file.value : this.file));
  }
}

String? failedEventsDocumentTypeNullableToJson(
    enums.FailedEventsDocumentType? failedEventsDocumentType) {
  return failedEventsDocumentType?.value;
}

String? failedEventsDocumentTypeToJson(
    enums.FailedEventsDocumentType failedEventsDocumentType) {
  return failedEventsDocumentType.value;
}

enums.FailedEventsDocumentType failedEventsDocumentTypeFromJson(
  Object? failedEventsDocumentType, [
  enums.FailedEventsDocumentType? defaultValue,
]) {
  return enums.FailedEventsDocumentType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          failedEventsDocumentType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.FailedEventsDocumentType.swaggerGeneratedUnknown;
}

enums.FailedEventsDocumentType? failedEventsDocumentTypeNullableFromJson(
  Object? failedEventsDocumentType, [
  enums.FailedEventsDocumentType? defaultValue,
]) {
  if (failedEventsDocumentType == null) {
    return null;
  }
  return enums.FailedEventsDocumentType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          failedEventsDocumentType.toString().toLowerCase()) ??
      defaultValue;
}

String failedEventsDocumentTypeExplodedListToJson(
    List<enums.FailedEventsDocumentType>? failedEventsDocumentType) {
  return failedEventsDocumentType?.map((e) => e.value!).join(',') ?? '';
}

List<String> failedEventsDocumentTypeListToJson(
    List<enums.FailedEventsDocumentType>? failedEventsDocumentType) {
  if (failedEventsDocumentType == null) {
    return [];
  }

  return failedEventsDocumentType.map((e) => e.value!).toList();
}

List<enums.FailedEventsDocumentType> failedEventsDocumentTypeListFromJson(
  List? failedEventsDocumentType, [
  List<enums.FailedEventsDocumentType>? defaultValue,
]) {
  if (failedEventsDocumentType == null) {
    return defaultValue ?? [];
  }

  return failedEventsDocumentType
      .map((e) => failedEventsDocumentTypeFromJson(e.toString()))
      .toList();
}

List<enums.FailedEventsDocumentType>?
    failedEventsDocumentTypeNullableListFromJson(
  List? failedEventsDocumentType, [
  List<enums.FailedEventsDocumentType>? defaultValue,
]) {
  if (failedEventsDocumentType == null) {
    return defaultValue;
  }

  return failedEventsDocumentType
      .map((e) => failedEventsDocumentTypeFromJson(e.toString()))
      .toList();
}

String? failedEventsDocumentSeverityNullableToJson(
    enums.FailedEventsDocumentSeverity? failedEventsDocumentSeverity) {
  return failedEventsDocumentSeverity?.value;
}

String? failedEventsDocumentSeverityToJson(
    enums.FailedEventsDocumentSeverity failedEventsDocumentSeverity) {
  return failedEventsDocumentSeverity.value;
}

enums.FailedEventsDocumentSeverity failedEventsDocumentSeverityFromJson(
  Object? failedEventsDocumentSeverity, [
  enums.FailedEventsDocumentSeverity? defaultValue,
]) {
  return enums.FailedEventsDocumentSeverity.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          failedEventsDocumentSeverity?.toString().toLowerCase()) ??
      defaultValue ??
      enums.FailedEventsDocumentSeverity.swaggerGeneratedUnknown;
}

enums.FailedEventsDocumentSeverity?
    failedEventsDocumentSeverityNullableFromJson(
  Object? failedEventsDocumentSeverity, [
  enums.FailedEventsDocumentSeverity? defaultValue,
]) {
  if (failedEventsDocumentSeverity == null) {
    return null;
  }
  return enums.FailedEventsDocumentSeverity.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          failedEventsDocumentSeverity.toString().toLowerCase()) ??
      defaultValue;
}

String failedEventsDocumentSeverityExplodedListToJson(
    List<enums.FailedEventsDocumentSeverity>? failedEventsDocumentSeverity) {
  return failedEventsDocumentSeverity?.map((e) => e.value!).join(',') ?? '';
}

List<String> failedEventsDocumentSeverityListToJson(
    List<enums.FailedEventsDocumentSeverity>? failedEventsDocumentSeverity) {
  if (failedEventsDocumentSeverity == null) {
    return [];
  }

  return failedEventsDocumentSeverity.map((e) => e.value!).toList();
}

List<enums.FailedEventsDocumentSeverity>
    failedEventsDocumentSeverityListFromJson(
  List? failedEventsDocumentSeverity, [
  List<enums.FailedEventsDocumentSeverity>? defaultValue,
]) {
  if (failedEventsDocumentSeverity == null) {
    return defaultValue ?? [];
  }

  return failedEventsDocumentSeverity
      .map((e) => failedEventsDocumentSeverityFromJson(e.toString()))
      .toList();
}

List<enums.FailedEventsDocumentSeverity>?
    failedEventsDocumentSeverityNullableListFromJson(
  List? failedEventsDocumentSeverity, [
  List<enums.FailedEventsDocumentSeverity>? defaultValue,
]) {
  if (failedEventsDocumentSeverity == null) {
    return defaultValue;
  }

  return failedEventsDocumentSeverity
      .map((e) => failedEventsDocumentSeverityFromJson(e.toString()))
      .toList();
}

String? addressAddressTypeNullableToJson(
    enums.AddressAddressType? addressAddressType) {
  return addressAddressType?.value;
}

String? addressAddressTypeToJson(enums.AddressAddressType addressAddressType) {
  return addressAddressType.value;
}

enums.AddressAddressType addressAddressTypeFromJson(
  Object? addressAddressType, [
  enums.AddressAddressType? defaultValue,
]) {
  return enums.AddressAddressType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          addressAddressType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.AddressAddressType.swaggerGeneratedUnknown;
}

enums.AddressAddressType? addressAddressTypeNullableFromJson(
  Object? addressAddressType, [
  enums.AddressAddressType? defaultValue,
]) {
  if (addressAddressType == null) {
    return null;
  }
  return enums.AddressAddressType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          addressAddressType.toString().toLowerCase()) ??
      defaultValue;
}

String addressAddressTypeExplodedListToJson(
    List<enums.AddressAddressType>? addressAddressType) {
  return addressAddressType?.map((e) => e.value!).join(',') ?? '';
}

List<String> addressAddressTypeListToJson(
    List<enums.AddressAddressType>? addressAddressType) {
  if (addressAddressType == null) {
    return [];
  }

  return addressAddressType.map((e) => e.value!).toList();
}

List<enums.AddressAddressType> addressAddressTypeListFromJson(
  List? addressAddressType, [
  List<enums.AddressAddressType>? defaultValue,
]) {
  if (addressAddressType == null) {
    return defaultValue ?? [];
  }

  return addressAddressType
      .map((e) => addressAddressTypeFromJson(e.toString()))
      .toList();
}

List<enums.AddressAddressType>? addressAddressTypeNullableListFromJson(
  List? addressAddressType, [
  List<enums.AddressAddressType>? defaultValue,
]) {
  if (addressAddressType == null) {
    return defaultValue;
  }

  return addressAddressType
      .map((e) => addressAddressTypeFromJson(e.toString()))
      .toList();
}

String? applicationInputDataRequestVatReportingMethodNullableToJson(
    enums.ApplicationInputDataRequestVatReportingMethod?
        applicationInputDataRequestVatReportingMethod) {
  return applicationInputDataRequestVatReportingMethod?.value;
}

String? applicationInputDataRequestVatReportingMethodToJson(
    enums.ApplicationInputDataRequestVatReportingMethod
        applicationInputDataRequestVatReportingMethod) {
  return applicationInputDataRequestVatReportingMethod.value;
}

enums.ApplicationInputDataRequestVatReportingMethod
    applicationInputDataRequestVatReportingMethodFromJson(
  Object? applicationInputDataRequestVatReportingMethod, [
  enums.ApplicationInputDataRequestVatReportingMethod? defaultValue,
]) {
  return enums.ApplicationInputDataRequestVatReportingMethod.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              applicationInputDataRequestVatReportingMethod
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.ApplicationInputDataRequestVatReportingMethod
          .swaggerGeneratedUnknown;
}

enums.ApplicationInputDataRequestVatReportingMethod?
    applicationInputDataRequestVatReportingMethodNullableFromJson(
  Object? applicationInputDataRequestVatReportingMethod, [
  enums.ApplicationInputDataRequestVatReportingMethod? defaultValue,
]) {
  if (applicationInputDataRequestVatReportingMethod == null) {
    return null;
  }
  return enums.ApplicationInputDataRequestVatReportingMethod.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              applicationInputDataRequestVatReportingMethod
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String applicationInputDataRequestVatReportingMethodExplodedListToJson(
    List<enums.ApplicationInputDataRequestVatReportingMethod>?
        applicationInputDataRequestVatReportingMethod) {
  return applicationInputDataRequestVatReportingMethod
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> applicationInputDataRequestVatReportingMethodListToJson(
    List<enums.ApplicationInputDataRequestVatReportingMethod>?
        applicationInputDataRequestVatReportingMethod) {
  if (applicationInputDataRequestVatReportingMethod == null) {
    return [];
  }

  return applicationInputDataRequestVatReportingMethod
      .map((e) => e.value!)
      .toList();
}

List<enums.ApplicationInputDataRequestVatReportingMethod>
    applicationInputDataRequestVatReportingMethodListFromJson(
  List? applicationInputDataRequestVatReportingMethod, [
  List<enums.ApplicationInputDataRequestVatReportingMethod>? defaultValue,
]) {
  if (applicationInputDataRequestVatReportingMethod == null) {
    return defaultValue ?? [];
  }

  return applicationInputDataRequestVatReportingMethod
      .map((e) =>
          applicationInputDataRequestVatReportingMethodFromJson(e.toString()))
      .toList();
}

List<enums.ApplicationInputDataRequestVatReportingMethod>?
    applicationInputDataRequestVatReportingMethodNullableListFromJson(
  List? applicationInputDataRequestVatReportingMethod, [
  List<enums.ApplicationInputDataRequestVatReportingMethod>? defaultValue,
]) {
  if (applicationInputDataRequestVatReportingMethod == null) {
    return defaultValue;
  }

  return applicationInputDataRequestVatReportingMethod
      .map((e) =>
          applicationInputDataRequestVatReportingMethodFromJson(e.toString()))
      .toList();
}

String? applicationInputDataRequestNonRegisteredVatReasonTypeNullableToJson(
    enums.ApplicationInputDataRequestNonRegisteredVatReasonType?
        applicationInputDataRequestNonRegisteredVatReasonType) {
  return applicationInputDataRequestNonRegisteredVatReasonType?.value;
}

String? applicationInputDataRequestNonRegisteredVatReasonTypeToJson(
    enums.ApplicationInputDataRequestNonRegisteredVatReasonType
        applicationInputDataRequestNonRegisteredVatReasonType) {
  return applicationInputDataRequestNonRegisteredVatReasonType.value;
}

enums.ApplicationInputDataRequestNonRegisteredVatReasonType
    applicationInputDataRequestNonRegisteredVatReasonTypeFromJson(
  Object? applicationInputDataRequestNonRegisteredVatReasonType, [
  enums.ApplicationInputDataRequestNonRegisteredVatReasonType? defaultValue,
]) {
  return enums.ApplicationInputDataRequestNonRegisteredVatReasonType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              applicationInputDataRequestNonRegisteredVatReasonType
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.ApplicationInputDataRequestNonRegisteredVatReasonType
          .swaggerGeneratedUnknown;
}

enums.ApplicationInputDataRequestNonRegisteredVatReasonType?
    applicationInputDataRequestNonRegisteredVatReasonTypeNullableFromJson(
  Object? applicationInputDataRequestNonRegisteredVatReasonType, [
  enums.ApplicationInputDataRequestNonRegisteredVatReasonType? defaultValue,
]) {
  if (applicationInputDataRequestNonRegisteredVatReasonType == null) {
    return null;
  }
  return enums.ApplicationInputDataRequestNonRegisteredVatReasonType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              applicationInputDataRequestNonRegisteredVatReasonType
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String applicationInputDataRequestNonRegisteredVatReasonTypeExplodedListToJson(
    List<enums.ApplicationInputDataRequestNonRegisteredVatReasonType>?
        applicationInputDataRequestNonRegisteredVatReasonType) {
  return applicationInputDataRequestNonRegisteredVatReasonType
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> applicationInputDataRequestNonRegisteredVatReasonTypeListToJson(
    List<enums.ApplicationInputDataRequestNonRegisteredVatReasonType>?
        applicationInputDataRequestNonRegisteredVatReasonType) {
  if (applicationInputDataRequestNonRegisteredVatReasonType == null) {
    return [];
  }

  return applicationInputDataRequestNonRegisteredVatReasonType
      .map((e) => e.value!)
      .toList();
}

List<enums.ApplicationInputDataRequestNonRegisteredVatReasonType>
    applicationInputDataRequestNonRegisteredVatReasonTypeListFromJson(
  List? applicationInputDataRequestNonRegisteredVatReasonType, [
  List<enums.ApplicationInputDataRequestNonRegisteredVatReasonType>?
      defaultValue,
]) {
  if (applicationInputDataRequestNonRegisteredVatReasonType == null) {
    return defaultValue ?? [];
  }

  return applicationInputDataRequestNonRegisteredVatReasonType
      .map((e) => applicationInputDataRequestNonRegisteredVatReasonTypeFromJson(
          e.toString()))
      .toList();
}

List<enums.ApplicationInputDataRequestNonRegisteredVatReasonType>?
    applicationInputDataRequestNonRegisteredVatReasonTypeNullableListFromJson(
  List? applicationInputDataRequestNonRegisteredVatReasonType, [
  List<enums.ApplicationInputDataRequestNonRegisteredVatReasonType>?
      defaultValue,
]) {
  if (applicationInputDataRequestNonRegisteredVatReasonType == null) {
    return defaultValue;
  }

  return applicationInputDataRequestNonRegisteredVatReasonType
      .map((e) => applicationInputDataRequestNonRegisteredVatReasonTypeFromJson(
          e.toString()))
      .toList();
}

String? applicationStatusNullableToJson(
    enums.ApplicationStatus? applicationStatus) {
  return applicationStatus?.value;
}

String? applicationStatusToJson(enums.ApplicationStatus applicationStatus) {
  return applicationStatus.value;
}

enums.ApplicationStatus applicationStatusFromJson(
  Object? applicationStatus, [
  enums.ApplicationStatus? defaultValue,
]) {
  return enums.ApplicationStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          applicationStatus?.toString().toLowerCase()) ??
      defaultValue ??
      enums.ApplicationStatus.swaggerGeneratedUnknown;
}

enums.ApplicationStatus? applicationStatusNullableFromJson(
  Object? applicationStatus, [
  enums.ApplicationStatus? defaultValue,
]) {
  if (applicationStatus == null) {
    return null;
  }
  return enums.ApplicationStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          applicationStatus.toString().toLowerCase()) ??
      defaultValue;
}

String applicationStatusExplodedListToJson(
    List<enums.ApplicationStatus>? applicationStatus) {
  return applicationStatus?.map((e) => e.value!).join(',') ?? '';
}

List<String> applicationStatusListToJson(
    List<enums.ApplicationStatus>? applicationStatus) {
  if (applicationStatus == null) {
    return [];
  }

  return applicationStatus.map((e) => e.value!).toList();
}

List<enums.ApplicationStatus> applicationStatusListFromJson(
  List? applicationStatus, [
  List<enums.ApplicationStatus>? defaultValue,
]) {
  if (applicationStatus == null) {
    return defaultValue ?? [];
  }

  return applicationStatus
      .map((e) => applicationStatusFromJson(e.toString()))
      .toList();
}

List<enums.ApplicationStatus>? applicationStatusNullableListFromJson(
  List? applicationStatus, [
  List<enums.ApplicationStatus>? defaultValue,
]) {
  if (applicationStatus == null) {
    return defaultValue;
  }

  return applicationStatus
      .map((e) => applicationStatusFromJson(e.toString()))
      .toList();
}

String? applicationUiStatusNullableToJson(
    enums.ApplicationUiStatus? applicationUiStatus) {
  return applicationUiStatus?.value;
}

String? applicationUiStatusToJson(
    enums.ApplicationUiStatus applicationUiStatus) {
  return applicationUiStatus.value;
}

enums.ApplicationUiStatus applicationUiStatusFromJson(
  Object? applicationUiStatus, [
  enums.ApplicationUiStatus? defaultValue,
]) {
  return enums.ApplicationUiStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          applicationUiStatus?.toString().toLowerCase()) ??
      defaultValue ??
      enums.ApplicationUiStatus.swaggerGeneratedUnknown;
}

enums.ApplicationUiStatus? applicationUiStatusNullableFromJson(
  Object? applicationUiStatus, [
  enums.ApplicationUiStatus? defaultValue,
]) {
  if (applicationUiStatus == null) {
    return null;
  }
  return enums.ApplicationUiStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          applicationUiStatus.toString().toLowerCase()) ??
      defaultValue;
}

String applicationUiStatusExplodedListToJson(
    List<enums.ApplicationUiStatus>? applicationUiStatus) {
  return applicationUiStatus?.map((e) => e.value!).join(',') ?? '';
}

List<String> applicationUiStatusListToJson(
    List<enums.ApplicationUiStatus>? applicationUiStatus) {
  if (applicationUiStatus == null) {
    return [];
  }

  return applicationUiStatus.map((e) => e.value!).toList();
}

List<enums.ApplicationUiStatus> applicationUiStatusListFromJson(
  List? applicationUiStatus, [
  List<enums.ApplicationUiStatus>? defaultValue,
]) {
  if (applicationUiStatus == null) {
    return defaultValue ?? [];
  }

  return applicationUiStatus
      .map((e) => applicationUiStatusFromJson(e.toString()))
      .toList();
}

List<enums.ApplicationUiStatus>? applicationUiStatusNullableListFromJson(
  List? applicationUiStatus, [
  List<enums.ApplicationUiStatus>? defaultValue,
]) {
  if (applicationUiStatus == null) {
    return defaultValue;
  }

  return applicationUiStatus
      .map((e) => applicationUiStatusFromJson(e.toString()))
      .toList();
}

String? creditDecisionResultCreditDecisionStatusNullableToJson(
    enums.CreditDecisionResultCreditDecisionStatus?
        creditDecisionResultCreditDecisionStatus) {
  return creditDecisionResultCreditDecisionStatus?.value;
}

String? creditDecisionResultCreditDecisionStatusToJson(
    enums.CreditDecisionResultCreditDecisionStatus
        creditDecisionResultCreditDecisionStatus) {
  return creditDecisionResultCreditDecisionStatus.value;
}

enums.CreditDecisionResultCreditDecisionStatus
    creditDecisionResultCreditDecisionStatusFromJson(
  Object? creditDecisionResultCreditDecisionStatus, [
  enums.CreditDecisionResultCreditDecisionStatus? defaultValue,
]) {
  return enums.CreditDecisionResultCreditDecisionStatus.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              creditDecisionResultCreditDecisionStatus
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.CreditDecisionResultCreditDecisionStatus.swaggerGeneratedUnknown;
}

enums.CreditDecisionResultCreditDecisionStatus?
    creditDecisionResultCreditDecisionStatusNullableFromJson(
  Object? creditDecisionResultCreditDecisionStatus, [
  enums.CreditDecisionResultCreditDecisionStatus? defaultValue,
]) {
  if (creditDecisionResultCreditDecisionStatus == null) {
    return null;
  }
  return enums.CreditDecisionResultCreditDecisionStatus.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              creditDecisionResultCreditDecisionStatus
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String creditDecisionResultCreditDecisionStatusExplodedListToJson(
    List<enums.CreditDecisionResultCreditDecisionStatus>?
        creditDecisionResultCreditDecisionStatus) {
  return creditDecisionResultCreditDecisionStatus
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> creditDecisionResultCreditDecisionStatusListToJson(
    List<enums.CreditDecisionResultCreditDecisionStatus>?
        creditDecisionResultCreditDecisionStatus) {
  if (creditDecisionResultCreditDecisionStatus == null) {
    return [];
  }

  return creditDecisionResultCreditDecisionStatus.map((e) => e.value!).toList();
}

List<enums.CreditDecisionResultCreditDecisionStatus>
    creditDecisionResultCreditDecisionStatusListFromJson(
  List? creditDecisionResultCreditDecisionStatus, [
  List<enums.CreditDecisionResultCreditDecisionStatus>? defaultValue,
]) {
  if (creditDecisionResultCreditDecisionStatus == null) {
    return defaultValue ?? [];
  }

  return creditDecisionResultCreditDecisionStatus
      .map(
          (e) => creditDecisionResultCreditDecisionStatusFromJson(e.toString()))
      .toList();
}

List<enums.CreditDecisionResultCreditDecisionStatus>?
    creditDecisionResultCreditDecisionStatusNullableListFromJson(
  List? creditDecisionResultCreditDecisionStatus, [
  List<enums.CreditDecisionResultCreditDecisionStatus>? defaultValue,
]) {
  if (creditDecisionResultCreditDecisionStatus == null) {
    return defaultValue;
  }

  return creditDecisionResultCreditDecisionStatus
      .map(
          (e) => creditDecisionResultCreditDecisionStatusFromJson(e.toString()))
      .toList();
}

String? creditDecisionResultV2CreditDecisionStatusNullableToJson(
    enums.CreditDecisionResultV2CreditDecisionStatus?
        creditDecisionResultV2CreditDecisionStatus) {
  return creditDecisionResultV2CreditDecisionStatus?.value;
}

String? creditDecisionResultV2CreditDecisionStatusToJson(
    enums.CreditDecisionResultV2CreditDecisionStatus
        creditDecisionResultV2CreditDecisionStatus) {
  return creditDecisionResultV2CreditDecisionStatus.value;
}

enums.CreditDecisionResultV2CreditDecisionStatus
    creditDecisionResultV2CreditDecisionStatusFromJson(
  Object? creditDecisionResultV2CreditDecisionStatus, [
  enums.CreditDecisionResultV2CreditDecisionStatus? defaultValue,
]) {
  return enums.CreditDecisionResultV2CreditDecisionStatus.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              creditDecisionResultV2CreditDecisionStatus
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.CreditDecisionResultV2CreditDecisionStatus.swaggerGeneratedUnknown;
}

enums.CreditDecisionResultV2CreditDecisionStatus?
    creditDecisionResultV2CreditDecisionStatusNullableFromJson(
  Object? creditDecisionResultV2CreditDecisionStatus, [
  enums.CreditDecisionResultV2CreditDecisionStatus? defaultValue,
]) {
  if (creditDecisionResultV2CreditDecisionStatus == null) {
    return null;
  }
  return enums.CreditDecisionResultV2CreditDecisionStatus.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              creditDecisionResultV2CreditDecisionStatus
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String creditDecisionResultV2CreditDecisionStatusExplodedListToJson(
    List<enums.CreditDecisionResultV2CreditDecisionStatus>?
        creditDecisionResultV2CreditDecisionStatus) {
  return creditDecisionResultV2CreditDecisionStatus
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> creditDecisionResultV2CreditDecisionStatusListToJson(
    List<enums.CreditDecisionResultV2CreditDecisionStatus>?
        creditDecisionResultV2CreditDecisionStatus) {
  if (creditDecisionResultV2CreditDecisionStatus == null) {
    return [];
  }

  return creditDecisionResultV2CreditDecisionStatus
      .map((e) => e.value!)
      .toList();
}

List<enums.CreditDecisionResultV2CreditDecisionStatus>
    creditDecisionResultV2CreditDecisionStatusListFromJson(
  List? creditDecisionResultV2CreditDecisionStatus, [
  List<enums.CreditDecisionResultV2CreditDecisionStatus>? defaultValue,
]) {
  if (creditDecisionResultV2CreditDecisionStatus == null) {
    return defaultValue ?? [];
  }

  return creditDecisionResultV2CreditDecisionStatus
      .map((e) =>
          creditDecisionResultV2CreditDecisionStatusFromJson(e.toString()))
      .toList();
}

List<enums.CreditDecisionResultV2CreditDecisionStatus>?
    creditDecisionResultV2CreditDecisionStatusNullableListFromJson(
  List? creditDecisionResultV2CreditDecisionStatus, [
  List<enums.CreditDecisionResultV2CreditDecisionStatus>? defaultValue,
]) {
  if (creditDecisionResultV2CreditDecisionStatus == null) {
    return defaultValue;
  }

  return creditDecisionResultV2CreditDecisionStatus
      .map((e) =>
          creditDecisionResultV2CreditDecisionStatusFromJson(e.toString()))
      .toList();
}

String? creditDecisionResultV2CreditAcceptanceStatusNullableToJson(
    enums.CreditDecisionResultV2CreditAcceptanceStatus?
        creditDecisionResultV2CreditAcceptanceStatus) {
  return creditDecisionResultV2CreditAcceptanceStatus?.value;
}

String? creditDecisionResultV2CreditAcceptanceStatusToJson(
    enums.CreditDecisionResultV2CreditAcceptanceStatus
        creditDecisionResultV2CreditAcceptanceStatus) {
  return creditDecisionResultV2CreditAcceptanceStatus.value;
}

enums.CreditDecisionResultV2CreditAcceptanceStatus
    creditDecisionResultV2CreditAcceptanceStatusFromJson(
  Object? creditDecisionResultV2CreditAcceptanceStatus, [
  enums.CreditDecisionResultV2CreditAcceptanceStatus? defaultValue,
]) {
  return enums.CreditDecisionResultV2CreditAcceptanceStatus.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              creditDecisionResultV2CreditAcceptanceStatus
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums
          .CreditDecisionResultV2CreditAcceptanceStatus.swaggerGeneratedUnknown;
}

enums.CreditDecisionResultV2CreditAcceptanceStatus?
    creditDecisionResultV2CreditAcceptanceStatusNullableFromJson(
  Object? creditDecisionResultV2CreditAcceptanceStatus, [
  enums.CreditDecisionResultV2CreditAcceptanceStatus? defaultValue,
]) {
  if (creditDecisionResultV2CreditAcceptanceStatus == null) {
    return null;
  }
  return enums.CreditDecisionResultV2CreditAcceptanceStatus.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              creditDecisionResultV2CreditAcceptanceStatus
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String creditDecisionResultV2CreditAcceptanceStatusExplodedListToJson(
    List<enums.CreditDecisionResultV2CreditAcceptanceStatus>?
        creditDecisionResultV2CreditAcceptanceStatus) {
  return creditDecisionResultV2CreditAcceptanceStatus
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> creditDecisionResultV2CreditAcceptanceStatusListToJson(
    List<enums.CreditDecisionResultV2CreditAcceptanceStatus>?
        creditDecisionResultV2CreditAcceptanceStatus) {
  if (creditDecisionResultV2CreditAcceptanceStatus == null) {
    return [];
  }

  return creditDecisionResultV2CreditAcceptanceStatus
      .map((e) => e.value!)
      .toList();
}

List<enums.CreditDecisionResultV2CreditAcceptanceStatus>
    creditDecisionResultV2CreditAcceptanceStatusListFromJson(
  List? creditDecisionResultV2CreditAcceptanceStatus, [
  List<enums.CreditDecisionResultV2CreditAcceptanceStatus>? defaultValue,
]) {
  if (creditDecisionResultV2CreditAcceptanceStatus == null) {
    return defaultValue ?? [];
  }

  return creditDecisionResultV2CreditAcceptanceStatus
      .map((e) =>
          creditDecisionResultV2CreditAcceptanceStatusFromJson(e.toString()))
      .toList();
}

List<enums.CreditDecisionResultV2CreditAcceptanceStatus>?
    creditDecisionResultV2CreditAcceptanceStatusNullableListFromJson(
  List? creditDecisionResultV2CreditAcceptanceStatus, [
  List<enums.CreditDecisionResultV2CreditAcceptanceStatus>? defaultValue,
]) {
  if (creditDecisionResultV2CreditAcceptanceStatus == null) {
    return defaultValue;
  }

  return creditDecisionResultV2CreditAcceptanceStatus
      .map((e) =>
          creditDecisionResultV2CreditAcceptanceStatusFromJson(e.toString()))
      .toList();
}

String? creditDecisionResultV2VerificationStatusNullableToJson(
    enums.CreditDecisionResultV2VerificationStatus?
        creditDecisionResultV2VerificationStatus) {
  return creditDecisionResultV2VerificationStatus?.value;
}

String? creditDecisionResultV2VerificationStatusToJson(
    enums.CreditDecisionResultV2VerificationStatus
        creditDecisionResultV2VerificationStatus) {
  return creditDecisionResultV2VerificationStatus.value;
}

enums.CreditDecisionResultV2VerificationStatus
    creditDecisionResultV2VerificationStatusFromJson(
  Object? creditDecisionResultV2VerificationStatus, [
  enums.CreditDecisionResultV2VerificationStatus? defaultValue,
]) {
  return enums.CreditDecisionResultV2VerificationStatus.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              creditDecisionResultV2VerificationStatus
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.CreditDecisionResultV2VerificationStatus.swaggerGeneratedUnknown;
}

enums.CreditDecisionResultV2VerificationStatus?
    creditDecisionResultV2VerificationStatusNullableFromJson(
  Object? creditDecisionResultV2VerificationStatus, [
  enums.CreditDecisionResultV2VerificationStatus? defaultValue,
]) {
  if (creditDecisionResultV2VerificationStatus == null) {
    return null;
  }
  return enums.CreditDecisionResultV2VerificationStatus.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              creditDecisionResultV2VerificationStatus
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String creditDecisionResultV2VerificationStatusExplodedListToJson(
    List<enums.CreditDecisionResultV2VerificationStatus>?
        creditDecisionResultV2VerificationStatus) {
  return creditDecisionResultV2VerificationStatus
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> creditDecisionResultV2VerificationStatusListToJson(
    List<enums.CreditDecisionResultV2VerificationStatus>?
        creditDecisionResultV2VerificationStatus) {
  if (creditDecisionResultV2VerificationStatus == null) {
    return [];
  }

  return creditDecisionResultV2VerificationStatus.map((e) => e.value!).toList();
}

List<enums.CreditDecisionResultV2VerificationStatus>
    creditDecisionResultV2VerificationStatusListFromJson(
  List? creditDecisionResultV2VerificationStatus, [
  List<enums.CreditDecisionResultV2VerificationStatus>? defaultValue,
]) {
  if (creditDecisionResultV2VerificationStatus == null) {
    return defaultValue ?? [];
  }

  return creditDecisionResultV2VerificationStatus
      .map(
          (e) => creditDecisionResultV2VerificationStatusFromJson(e.toString()))
      .toList();
}

List<enums.CreditDecisionResultV2VerificationStatus>?
    creditDecisionResultV2VerificationStatusNullableListFromJson(
  List? creditDecisionResultV2VerificationStatus, [
  List<enums.CreditDecisionResultV2VerificationStatus>? defaultValue,
]) {
  if (creditDecisionResultV2VerificationStatus == null) {
    return defaultValue;
  }

  return creditDecisionResultV2VerificationStatus
      .map(
          (e) => creditDecisionResultV2VerificationStatusFromJson(e.toString()))
      .toList();
}

String? creditDecisionResultsCreditDecisionStatusNullableToJson(
    enums.CreditDecisionResultsCreditDecisionStatus?
        creditDecisionResultsCreditDecisionStatus) {
  return creditDecisionResultsCreditDecisionStatus?.value;
}

String? creditDecisionResultsCreditDecisionStatusToJson(
    enums.CreditDecisionResultsCreditDecisionStatus
        creditDecisionResultsCreditDecisionStatus) {
  return creditDecisionResultsCreditDecisionStatus.value;
}

enums.CreditDecisionResultsCreditDecisionStatus
    creditDecisionResultsCreditDecisionStatusFromJson(
  Object? creditDecisionResultsCreditDecisionStatus, [
  enums.CreditDecisionResultsCreditDecisionStatus? defaultValue,
]) {
  return enums.CreditDecisionResultsCreditDecisionStatus.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              creditDecisionResultsCreditDecisionStatus
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.CreditDecisionResultsCreditDecisionStatus.swaggerGeneratedUnknown;
}

enums.CreditDecisionResultsCreditDecisionStatus?
    creditDecisionResultsCreditDecisionStatusNullableFromJson(
  Object? creditDecisionResultsCreditDecisionStatus, [
  enums.CreditDecisionResultsCreditDecisionStatus? defaultValue,
]) {
  if (creditDecisionResultsCreditDecisionStatus == null) {
    return null;
  }
  return enums.CreditDecisionResultsCreditDecisionStatus.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              creditDecisionResultsCreditDecisionStatus
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String creditDecisionResultsCreditDecisionStatusExplodedListToJson(
    List<enums.CreditDecisionResultsCreditDecisionStatus>?
        creditDecisionResultsCreditDecisionStatus) {
  return creditDecisionResultsCreditDecisionStatus
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> creditDecisionResultsCreditDecisionStatusListToJson(
    List<enums.CreditDecisionResultsCreditDecisionStatus>?
        creditDecisionResultsCreditDecisionStatus) {
  if (creditDecisionResultsCreditDecisionStatus == null) {
    return [];
  }

  return creditDecisionResultsCreditDecisionStatus
      .map((e) => e.value!)
      .toList();
}

List<enums.CreditDecisionResultsCreditDecisionStatus>
    creditDecisionResultsCreditDecisionStatusListFromJson(
  List? creditDecisionResultsCreditDecisionStatus, [
  List<enums.CreditDecisionResultsCreditDecisionStatus>? defaultValue,
]) {
  if (creditDecisionResultsCreditDecisionStatus == null) {
    return defaultValue ?? [];
  }

  return creditDecisionResultsCreditDecisionStatus
      .map((e) =>
          creditDecisionResultsCreditDecisionStatusFromJson(e.toString()))
      .toList();
}

List<enums.CreditDecisionResultsCreditDecisionStatus>?
    creditDecisionResultsCreditDecisionStatusNullableListFromJson(
  List? creditDecisionResultsCreditDecisionStatus, [
  List<enums.CreditDecisionResultsCreditDecisionStatus>? defaultValue,
]) {
  if (creditDecisionResultsCreditDecisionStatus == null) {
    return defaultValue;
  }

  return creditDecisionResultsCreditDecisionStatus
      .map((e) =>
          creditDecisionResultsCreditDecisionStatusFromJson(e.toString()))
      .toList();
}

String? currencyNullableToJson(enums.Currency? currency) {
  return currency?.value;
}

String? currencyToJson(enums.Currency currency) {
  return currency.value;
}

enums.Currency currencyFromJson(
  Object? currency, [
  enums.Currency? defaultValue,
]) {
  return enums.Currency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          currency?.toString().toLowerCase()) ??
      defaultValue ??
      enums.Currency.swaggerGeneratedUnknown;
}

enums.Currency? currencyNullableFromJson(
  Object? currency, [
  enums.Currency? defaultValue,
]) {
  if (currency == null) {
    return null;
  }
  return enums.Currency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          currency.toString().toLowerCase()) ??
      defaultValue;
}

String currencyExplodedListToJson(List<enums.Currency>? currency) {
  return currency?.map((e) => e.value!).join(',') ?? '';
}

List<String> currencyListToJson(List<enums.Currency>? currency) {
  if (currency == null) {
    return [];
  }

  return currency.map((e) => e.value!).toList();
}

List<enums.Currency> currencyListFromJson(
  List? currency, [
  List<enums.Currency>? defaultValue,
]) {
  if (currency == null) {
    return defaultValue ?? [];
  }

  return currency.map((e) => currencyFromJson(e.toString())).toList();
}

List<enums.Currency>? currencyNullableListFromJson(
  List? currency, [
  List<enums.Currency>? defaultValue,
]) {
  if (currency == null) {
    return defaultValue;
  }

  return currency.map((e) => currencyFromJson(e.toString())).toList();
}

String? documentTypeNullableToJson(enums.DocumentType? documentType) {
  return documentType?.value;
}

String? documentTypeToJson(enums.DocumentType documentType) {
  return documentType.value;
}

enums.DocumentType documentTypeFromJson(
  Object? documentType, [
  enums.DocumentType? defaultValue,
]) {
  return enums.DocumentType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          documentType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.DocumentType.swaggerGeneratedUnknown;
}

enums.DocumentType? documentTypeNullableFromJson(
  Object? documentType, [
  enums.DocumentType? defaultValue,
]) {
  if (documentType == null) {
    return null;
  }
  return enums.DocumentType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          documentType.toString().toLowerCase()) ??
      defaultValue;
}

String documentTypeExplodedListToJson(List<enums.DocumentType>? documentType) {
  return documentType?.map((e) => e.value!).join(',') ?? '';
}

List<String> documentTypeListToJson(List<enums.DocumentType>? documentType) {
  if (documentType == null) {
    return [];
  }

  return documentType.map((e) => e.value!).toList();
}

List<enums.DocumentType> documentTypeListFromJson(
  List? documentType, [
  List<enums.DocumentType>? defaultValue,
]) {
  if (documentType == null) {
    return defaultValue ?? [];
  }

  return documentType.map((e) => documentTypeFromJson(e.toString())).toList();
}

List<enums.DocumentType>? documentTypeNullableListFromJson(
  List? documentType, [
  List<enums.DocumentType>? defaultValue,
]) {
  if (documentType == null) {
    return defaultValue;
  }

  return documentType.map((e) => documentTypeFromJson(e.toString())).toList();
}

String? fixedDepositInfoLoanProductCodeNullableToJson(
    enums.FixedDepositInfoLoanProductCode? fixedDepositInfoLoanProductCode) {
  return fixedDepositInfoLoanProductCode?.value;
}

String? fixedDepositInfoLoanProductCodeToJson(
    enums.FixedDepositInfoLoanProductCode fixedDepositInfoLoanProductCode) {
  return fixedDepositInfoLoanProductCode.value;
}

enums.FixedDepositInfoLoanProductCode fixedDepositInfoLoanProductCodeFromJson(
  Object? fixedDepositInfoLoanProductCode, [
  enums.FixedDepositInfoLoanProductCode? defaultValue,
]) {
  return enums.FixedDepositInfoLoanProductCode.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          fixedDepositInfoLoanProductCode?.toString().toLowerCase()) ??
      defaultValue ??
      enums.FixedDepositInfoLoanProductCode.swaggerGeneratedUnknown;
}

enums.FixedDepositInfoLoanProductCode?
    fixedDepositInfoLoanProductCodeNullableFromJson(
  Object? fixedDepositInfoLoanProductCode, [
  enums.FixedDepositInfoLoanProductCode? defaultValue,
]) {
  if (fixedDepositInfoLoanProductCode == null) {
    return null;
  }
  return enums.FixedDepositInfoLoanProductCode.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          fixedDepositInfoLoanProductCode.toString().toLowerCase()) ??
      defaultValue;
}

String fixedDepositInfoLoanProductCodeExplodedListToJson(
    List<enums.FixedDepositInfoLoanProductCode>?
        fixedDepositInfoLoanProductCode) {
  return fixedDepositInfoLoanProductCode?.map((e) => e.value!).join(',') ?? '';
}

List<String> fixedDepositInfoLoanProductCodeListToJson(
    List<enums.FixedDepositInfoLoanProductCode>?
        fixedDepositInfoLoanProductCode) {
  if (fixedDepositInfoLoanProductCode == null) {
    return [];
  }

  return fixedDepositInfoLoanProductCode.map((e) => e.value!).toList();
}

List<enums.FixedDepositInfoLoanProductCode>
    fixedDepositInfoLoanProductCodeListFromJson(
  List? fixedDepositInfoLoanProductCode, [
  List<enums.FixedDepositInfoLoanProductCode>? defaultValue,
]) {
  if (fixedDepositInfoLoanProductCode == null) {
    return defaultValue ?? [];
  }

  return fixedDepositInfoLoanProductCode
      .map((e) => fixedDepositInfoLoanProductCodeFromJson(e.toString()))
      .toList();
}

List<enums.FixedDepositInfoLoanProductCode>?
    fixedDepositInfoLoanProductCodeNullableListFromJson(
  List? fixedDepositInfoLoanProductCode, [
  List<enums.FixedDepositInfoLoanProductCode>? defaultValue,
]) {
  if (fixedDepositInfoLoanProductCode == null) {
    return defaultValue;
  }

  return fixedDepositInfoLoanProductCode
      .map((e) => fixedDepositInfoLoanProductCodeFromJson(e.toString()))
      .toList();
}

String? loanSecurityProductDetailsProductCodeNullableToJson(
    enums.LoanSecurityProductDetailsProductCode?
        loanSecurityProductDetailsProductCode) {
  return loanSecurityProductDetailsProductCode?.value;
}

String? loanSecurityProductDetailsProductCodeToJson(
    enums.LoanSecurityProductDetailsProductCode
        loanSecurityProductDetailsProductCode) {
  return loanSecurityProductDetailsProductCode.value;
}

enums.LoanSecurityProductDetailsProductCode
    loanSecurityProductDetailsProductCodeFromJson(
  Object? loanSecurityProductDetailsProductCode, [
  enums.LoanSecurityProductDetailsProductCode? defaultValue,
]) {
  return enums.LoanSecurityProductDetailsProductCode.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              loanSecurityProductDetailsProductCode
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.LoanSecurityProductDetailsProductCode.swaggerGeneratedUnknown;
}

enums.LoanSecurityProductDetailsProductCode?
    loanSecurityProductDetailsProductCodeNullableFromJson(
  Object? loanSecurityProductDetailsProductCode, [
  enums.LoanSecurityProductDetailsProductCode? defaultValue,
]) {
  if (loanSecurityProductDetailsProductCode == null) {
    return null;
  }
  return enums.LoanSecurityProductDetailsProductCode.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              loanSecurityProductDetailsProductCode.toString().toLowerCase()) ??
      defaultValue;
}

String loanSecurityProductDetailsProductCodeExplodedListToJson(
    List<enums.LoanSecurityProductDetailsProductCode>?
        loanSecurityProductDetailsProductCode) {
  return loanSecurityProductDetailsProductCode
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> loanSecurityProductDetailsProductCodeListToJson(
    List<enums.LoanSecurityProductDetailsProductCode>?
        loanSecurityProductDetailsProductCode) {
  if (loanSecurityProductDetailsProductCode == null) {
    return [];
  }

  return loanSecurityProductDetailsProductCode.map((e) => e.value!).toList();
}

List<enums.LoanSecurityProductDetailsProductCode>
    loanSecurityProductDetailsProductCodeListFromJson(
  List? loanSecurityProductDetailsProductCode, [
  List<enums.LoanSecurityProductDetailsProductCode>? defaultValue,
]) {
  if (loanSecurityProductDetailsProductCode == null) {
    return defaultValue ?? [];
  }

  return loanSecurityProductDetailsProductCode
      .map((e) => loanSecurityProductDetailsProductCodeFromJson(e.toString()))
      .toList();
}

List<enums.LoanSecurityProductDetailsProductCode>?
    loanSecurityProductDetailsProductCodeNullableListFromJson(
  List? loanSecurityProductDetailsProductCode, [
  List<enums.LoanSecurityProductDetailsProductCode>? defaultValue,
]) {
  if (loanSecurityProductDetailsProductCode == null) {
    return defaultValue;
  }

  return loanSecurityProductDetailsProductCode
      .map((e) => loanSecurityProductDetailsProductCodeFromJson(e.toString()))
      .toList();
}

String? productApplicationAccountCreationStatusNullableToJson(
    enums.ProductApplicationAccountCreationStatus?
        productApplicationAccountCreationStatus) {
  return productApplicationAccountCreationStatus?.value;
}

String? productApplicationAccountCreationStatusToJson(
    enums.ProductApplicationAccountCreationStatus
        productApplicationAccountCreationStatus) {
  return productApplicationAccountCreationStatus.value;
}

enums.ProductApplicationAccountCreationStatus
    productApplicationAccountCreationStatusFromJson(
  Object? productApplicationAccountCreationStatus, [
  enums.ProductApplicationAccountCreationStatus? defaultValue,
]) {
  return enums.ProductApplicationAccountCreationStatus.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              productApplicationAccountCreationStatus
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.ProductApplicationAccountCreationStatus.swaggerGeneratedUnknown;
}

enums.ProductApplicationAccountCreationStatus?
    productApplicationAccountCreationStatusNullableFromJson(
  Object? productApplicationAccountCreationStatus, [
  enums.ProductApplicationAccountCreationStatus? defaultValue,
]) {
  if (productApplicationAccountCreationStatus == null) {
    return null;
  }
  return enums.ProductApplicationAccountCreationStatus.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              productApplicationAccountCreationStatus
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String productApplicationAccountCreationStatusExplodedListToJson(
    List<enums.ProductApplicationAccountCreationStatus>?
        productApplicationAccountCreationStatus) {
  return productApplicationAccountCreationStatus
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> productApplicationAccountCreationStatusListToJson(
    List<enums.ProductApplicationAccountCreationStatus>?
        productApplicationAccountCreationStatus) {
  if (productApplicationAccountCreationStatus == null) {
    return [];
  }

  return productApplicationAccountCreationStatus.map((e) => e.value!).toList();
}

List<enums.ProductApplicationAccountCreationStatus>
    productApplicationAccountCreationStatusListFromJson(
  List? productApplicationAccountCreationStatus, [
  List<enums.ProductApplicationAccountCreationStatus>? defaultValue,
]) {
  if (productApplicationAccountCreationStatus == null) {
    return defaultValue ?? [];
  }

  return productApplicationAccountCreationStatus
      .map((e) => productApplicationAccountCreationStatusFromJson(e.toString()))
      .toList();
}

List<enums.ProductApplicationAccountCreationStatus>?
    productApplicationAccountCreationStatusNullableListFromJson(
  List? productApplicationAccountCreationStatus, [
  List<enums.ProductApplicationAccountCreationStatus>? defaultValue,
]) {
  if (productApplicationAccountCreationStatus == null) {
    return defaultValue;
  }

  return productApplicationAccountCreationStatus
      .map((e) => productApplicationAccountCreationStatusFromJson(e.toString()))
      .toList();
}

String? productTypeNullableToJson(enums.ProductType? productType) {
  return productType?.value;
}

String? productTypeToJson(enums.ProductType productType) {
  return productType.value;
}

enums.ProductType productTypeFromJson(
  Object? productType, [
  enums.ProductType? defaultValue,
]) {
  return enums.ProductType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          productType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.ProductType.swaggerGeneratedUnknown;
}

enums.ProductType? productTypeNullableFromJson(
  Object? productType, [
  enums.ProductType? defaultValue,
]) {
  if (productType == null) {
    return null;
  }
  return enums.ProductType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          productType.toString().toLowerCase()) ??
      defaultValue;
}

String productTypeExplodedListToJson(List<enums.ProductType>? productType) {
  return productType?.map((e) => e.value!).join(',') ?? '';
}

List<String> productTypeListToJson(List<enums.ProductType>? productType) {
  if (productType == null) {
    return [];
  }

  return productType.map((e) => e.value!).toList();
}

List<enums.ProductType> productTypeListFromJson(
  List? productType, [
  List<enums.ProductType>? defaultValue,
]) {
  if (productType == null) {
    return defaultValue ?? [];
  }

  return productType.map((e) => productTypeFromJson(e.toString())).toList();
}

List<enums.ProductType>? productTypeNullableListFromJson(
  List? productType, [
  List<enums.ProductType>? defaultValue,
]) {
  if (productType == null) {
    return defaultValue;
  }

  return productType.map((e) => productTypeFromJson(e.toString())).toList();
}

String? applicationInputDataRequestDTOVatReportingMethodNullableToJson(
    enums.ApplicationInputDataRequestDTOVatReportingMethod?
        applicationInputDataRequestDTOVatReportingMethod) {
  return applicationInputDataRequestDTOVatReportingMethod?.value;
}

String? applicationInputDataRequestDTOVatReportingMethodToJson(
    enums.ApplicationInputDataRequestDTOVatReportingMethod
        applicationInputDataRequestDTOVatReportingMethod) {
  return applicationInputDataRequestDTOVatReportingMethod.value;
}

enums.ApplicationInputDataRequestDTOVatReportingMethod
    applicationInputDataRequestDTOVatReportingMethodFromJson(
  Object? applicationInputDataRequestDTOVatReportingMethod, [
  enums.ApplicationInputDataRequestDTOVatReportingMethod? defaultValue,
]) {
  return enums.ApplicationInputDataRequestDTOVatReportingMethod.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              applicationInputDataRequestDTOVatReportingMethod
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.ApplicationInputDataRequestDTOVatReportingMethod
          .swaggerGeneratedUnknown;
}

enums.ApplicationInputDataRequestDTOVatReportingMethod?
    applicationInputDataRequestDTOVatReportingMethodNullableFromJson(
  Object? applicationInputDataRequestDTOVatReportingMethod, [
  enums.ApplicationInputDataRequestDTOVatReportingMethod? defaultValue,
]) {
  if (applicationInputDataRequestDTOVatReportingMethod == null) {
    return null;
  }
  return enums.ApplicationInputDataRequestDTOVatReportingMethod.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              applicationInputDataRequestDTOVatReportingMethod
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String applicationInputDataRequestDTOVatReportingMethodExplodedListToJson(
    List<enums.ApplicationInputDataRequestDTOVatReportingMethod>?
        applicationInputDataRequestDTOVatReportingMethod) {
  return applicationInputDataRequestDTOVatReportingMethod
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> applicationInputDataRequestDTOVatReportingMethodListToJson(
    List<enums.ApplicationInputDataRequestDTOVatReportingMethod>?
        applicationInputDataRequestDTOVatReportingMethod) {
  if (applicationInputDataRequestDTOVatReportingMethod == null) {
    return [];
  }

  return applicationInputDataRequestDTOVatReportingMethod
      .map((e) => e.value!)
      .toList();
}

List<enums.ApplicationInputDataRequestDTOVatReportingMethod>
    applicationInputDataRequestDTOVatReportingMethodListFromJson(
  List? applicationInputDataRequestDTOVatReportingMethod, [
  List<enums.ApplicationInputDataRequestDTOVatReportingMethod>? defaultValue,
]) {
  if (applicationInputDataRequestDTOVatReportingMethod == null) {
    return defaultValue ?? [];
  }

  return applicationInputDataRequestDTOVatReportingMethod
      .map((e) => applicationInputDataRequestDTOVatReportingMethodFromJson(
          e.toString()))
      .toList();
}

List<enums.ApplicationInputDataRequestDTOVatReportingMethod>?
    applicationInputDataRequestDTOVatReportingMethodNullableListFromJson(
  List? applicationInputDataRequestDTOVatReportingMethod, [
  List<enums.ApplicationInputDataRequestDTOVatReportingMethod>? defaultValue,
]) {
  if (applicationInputDataRequestDTOVatReportingMethod == null) {
    return defaultValue;
  }

  return applicationInputDataRequestDTOVatReportingMethod
      .map((e) => applicationInputDataRequestDTOVatReportingMethodFromJson(
          e.toString()))
      .toList();
}

String? applicationInputDataRequestDTONonRegisteredVatReasonTypeNullableToJson(
    enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType?
        applicationInputDataRequestDTONonRegisteredVatReasonType) {
  return applicationInputDataRequestDTONonRegisteredVatReasonType?.value;
}

String? applicationInputDataRequestDTONonRegisteredVatReasonTypeToJson(
    enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType
        applicationInputDataRequestDTONonRegisteredVatReasonType) {
  return applicationInputDataRequestDTONonRegisteredVatReasonType.value;
}

enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType
    applicationInputDataRequestDTONonRegisteredVatReasonTypeFromJson(
  Object? applicationInputDataRequestDTONonRegisteredVatReasonType, [
  enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType? defaultValue,
]) {
  return enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              applicationInputDataRequestDTONonRegisteredVatReasonType
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType
          .swaggerGeneratedUnknown;
}

enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType?
    applicationInputDataRequestDTONonRegisteredVatReasonTypeNullableFromJson(
  Object? applicationInputDataRequestDTONonRegisteredVatReasonType, [
  enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType? defaultValue,
]) {
  if (applicationInputDataRequestDTONonRegisteredVatReasonType == null) {
    return null;
  }
  return enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              applicationInputDataRequestDTONonRegisteredVatReasonType
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String
    applicationInputDataRequestDTONonRegisteredVatReasonTypeExplodedListToJson(
        List<enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType>?
            applicationInputDataRequestDTONonRegisteredVatReasonType) {
  return applicationInputDataRequestDTONonRegisteredVatReasonType
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> applicationInputDataRequestDTONonRegisteredVatReasonTypeListToJson(
    List<enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType>?
        applicationInputDataRequestDTONonRegisteredVatReasonType) {
  if (applicationInputDataRequestDTONonRegisteredVatReasonType == null) {
    return [];
  }

  return applicationInputDataRequestDTONonRegisteredVatReasonType
      .map((e) => e.value!)
      .toList();
}

List<enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType>
    applicationInputDataRequestDTONonRegisteredVatReasonTypeListFromJson(
  List? applicationInputDataRequestDTONonRegisteredVatReasonType, [
  List<enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType>?
      defaultValue,
]) {
  if (applicationInputDataRequestDTONonRegisteredVatReasonType == null) {
    return defaultValue ?? [];
  }

  return applicationInputDataRequestDTONonRegisteredVatReasonType
      .map((e) =>
          applicationInputDataRequestDTONonRegisteredVatReasonTypeFromJson(
              e.toString()))
      .toList();
}

List<enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType>?
    applicationInputDataRequestDTONonRegisteredVatReasonTypeNullableListFromJson(
  List? applicationInputDataRequestDTONonRegisteredVatReasonType, [
  List<enums.ApplicationInputDataRequestDTONonRegisteredVatReasonType>?
      defaultValue,
]) {
  if (applicationInputDataRequestDTONonRegisteredVatReasonType == null) {
    return defaultValue;
  }

  return applicationInputDataRequestDTONonRegisteredVatReasonType
      .map((e) =>
          applicationInputDataRequestDTONonRegisteredVatReasonTypeFromJson(
              e.toString()))
      .toList();
}

String? loanInstallmentEvaluateCommandLoanProductCodeNullableToJson(
    enums.LoanInstallmentEvaluateCommandLoanProductCode?
        loanInstallmentEvaluateCommandLoanProductCode) {
  return loanInstallmentEvaluateCommandLoanProductCode?.value;
}

String? loanInstallmentEvaluateCommandLoanProductCodeToJson(
    enums.LoanInstallmentEvaluateCommandLoanProductCode
        loanInstallmentEvaluateCommandLoanProductCode) {
  return loanInstallmentEvaluateCommandLoanProductCode.value;
}

enums.LoanInstallmentEvaluateCommandLoanProductCode
    loanInstallmentEvaluateCommandLoanProductCodeFromJson(
  Object? loanInstallmentEvaluateCommandLoanProductCode, [
  enums.LoanInstallmentEvaluateCommandLoanProductCode? defaultValue,
]) {
  return enums.LoanInstallmentEvaluateCommandLoanProductCode.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              loanInstallmentEvaluateCommandLoanProductCode
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.LoanInstallmentEvaluateCommandLoanProductCode
          .swaggerGeneratedUnknown;
}

enums.LoanInstallmentEvaluateCommandLoanProductCode?
    loanInstallmentEvaluateCommandLoanProductCodeNullableFromJson(
  Object? loanInstallmentEvaluateCommandLoanProductCode, [
  enums.LoanInstallmentEvaluateCommandLoanProductCode? defaultValue,
]) {
  if (loanInstallmentEvaluateCommandLoanProductCode == null) {
    return null;
  }
  return enums.LoanInstallmentEvaluateCommandLoanProductCode.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              loanInstallmentEvaluateCommandLoanProductCode
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String loanInstallmentEvaluateCommandLoanProductCodeExplodedListToJson(
    List<enums.LoanInstallmentEvaluateCommandLoanProductCode>?
        loanInstallmentEvaluateCommandLoanProductCode) {
  return loanInstallmentEvaluateCommandLoanProductCode
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> loanInstallmentEvaluateCommandLoanProductCodeListToJson(
    List<enums.LoanInstallmentEvaluateCommandLoanProductCode>?
        loanInstallmentEvaluateCommandLoanProductCode) {
  if (loanInstallmentEvaluateCommandLoanProductCode == null) {
    return [];
  }

  return loanInstallmentEvaluateCommandLoanProductCode
      .map((e) => e.value!)
      .toList();
}

List<enums.LoanInstallmentEvaluateCommandLoanProductCode>
    loanInstallmentEvaluateCommandLoanProductCodeListFromJson(
  List? loanInstallmentEvaluateCommandLoanProductCode, [
  List<enums.LoanInstallmentEvaluateCommandLoanProductCode>? defaultValue,
]) {
  if (loanInstallmentEvaluateCommandLoanProductCode == null) {
    return defaultValue ?? [];
  }

  return loanInstallmentEvaluateCommandLoanProductCode
      .map((e) =>
          loanInstallmentEvaluateCommandLoanProductCodeFromJson(e.toString()))
      .toList();
}

List<enums.LoanInstallmentEvaluateCommandLoanProductCode>?
    loanInstallmentEvaluateCommandLoanProductCodeNullableListFromJson(
  List? loanInstallmentEvaluateCommandLoanProductCode, [
  List<enums.LoanInstallmentEvaluateCommandLoanProductCode>? defaultValue,
]) {
  if (loanInstallmentEvaluateCommandLoanProductCode == null) {
    return defaultValue;
  }

  return loanInstallmentEvaluateCommandLoanProductCode
      .map((e) =>
          loanInstallmentEvaluateCommandLoanProductCodeFromJson(e.toString()))
      .toList();
}

String? loanInstallmentEvaluateCommandEvaluationTransactionTypeNullableToJson(
    enums.LoanInstallmentEvaluateCommandEvaluationTransactionType?
        loanInstallmentEvaluateCommandEvaluationTransactionType) {
  return loanInstallmentEvaluateCommandEvaluationTransactionType?.value;
}

String? loanInstallmentEvaluateCommandEvaluationTransactionTypeToJson(
    enums.LoanInstallmentEvaluateCommandEvaluationTransactionType
        loanInstallmentEvaluateCommandEvaluationTransactionType) {
  return loanInstallmentEvaluateCommandEvaluationTransactionType.value;
}

enums.LoanInstallmentEvaluateCommandEvaluationTransactionType
    loanInstallmentEvaluateCommandEvaluationTransactionTypeFromJson(
  Object? loanInstallmentEvaluateCommandEvaluationTransactionType, [
  enums.LoanInstallmentEvaluateCommandEvaluationTransactionType? defaultValue,
]) {
  return enums.LoanInstallmentEvaluateCommandEvaluationTransactionType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              loanInstallmentEvaluateCommandEvaluationTransactionType
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.LoanInstallmentEvaluateCommandEvaluationTransactionType
          .swaggerGeneratedUnknown;
}

enums.LoanInstallmentEvaluateCommandEvaluationTransactionType?
    loanInstallmentEvaluateCommandEvaluationTransactionTypeNullableFromJson(
  Object? loanInstallmentEvaluateCommandEvaluationTransactionType, [
  enums.LoanInstallmentEvaluateCommandEvaluationTransactionType? defaultValue,
]) {
  if (loanInstallmentEvaluateCommandEvaluationTransactionType == null) {
    return null;
  }
  return enums.LoanInstallmentEvaluateCommandEvaluationTransactionType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              loanInstallmentEvaluateCommandEvaluationTransactionType
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String
    loanInstallmentEvaluateCommandEvaluationTransactionTypeExplodedListToJson(
        List<enums.LoanInstallmentEvaluateCommandEvaluationTransactionType>?
            loanInstallmentEvaluateCommandEvaluationTransactionType) {
  return loanInstallmentEvaluateCommandEvaluationTransactionType
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> loanInstallmentEvaluateCommandEvaluationTransactionTypeListToJson(
    List<enums.LoanInstallmentEvaluateCommandEvaluationTransactionType>?
        loanInstallmentEvaluateCommandEvaluationTransactionType) {
  if (loanInstallmentEvaluateCommandEvaluationTransactionType == null) {
    return [];
  }

  return loanInstallmentEvaluateCommandEvaluationTransactionType
      .map((e) => e.value!)
      .toList();
}

List<enums.LoanInstallmentEvaluateCommandEvaluationTransactionType>
    loanInstallmentEvaluateCommandEvaluationTransactionTypeListFromJson(
  List? loanInstallmentEvaluateCommandEvaluationTransactionType, [
  List<enums.LoanInstallmentEvaluateCommandEvaluationTransactionType>?
      defaultValue,
]) {
  if (loanInstallmentEvaluateCommandEvaluationTransactionType == null) {
    return defaultValue ?? [];
  }

  return loanInstallmentEvaluateCommandEvaluationTransactionType
      .map((e) =>
          loanInstallmentEvaluateCommandEvaluationTransactionTypeFromJson(
              e.toString()))
      .toList();
}

List<enums.LoanInstallmentEvaluateCommandEvaluationTransactionType>?
    loanInstallmentEvaluateCommandEvaluationTransactionTypeNullableListFromJson(
  List? loanInstallmentEvaluateCommandEvaluationTransactionType, [
  List<enums.LoanInstallmentEvaluateCommandEvaluationTransactionType>?
      defaultValue,
]) {
  if (loanInstallmentEvaluateCommandEvaluationTransactionType == null) {
    return defaultValue;
  }

  return loanInstallmentEvaluateCommandEvaluationTransactionType
      .map((e) =>
          loanInstallmentEvaluateCommandEvaluationTransactionTypeFromJson(
              e.toString()))
      .toList();
}

String? loanInstallmentEvaluateCommandPreviewScheduleStrategyNullableToJson(
    enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy?
        loanInstallmentEvaluateCommandPreviewScheduleStrategy) {
  return loanInstallmentEvaluateCommandPreviewScheduleStrategy?.value;
}

String? loanInstallmentEvaluateCommandPreviewScheduleStrategyToJson(
    enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy
        loanInstallmentEvaluateCommandPreviewScheduleStrategy) {
  return loanInstallmentEvaluateCommandPreviewScheduleStrategy.value;
}

enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy
    loanInstallmentEvaluateCommandPreviewScheduleStrategyFromJson(
  Object? loanInstallmentEvaluateCommandPreviewScheduleStrategy, [
  enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy? defaultValue,
]) {
  return enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              loanInstallmentEvaluateCommandPreviewScheduleStrategy
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy
          .swaggerGeneratedUnknown;
}

enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy?
    loanInstallmentEvaluateCommandPreviewScheduleStrategyNullableFromJson(
  Object? loanInstallmentEvaluateCommandPreviewScheduleStrategy, [
  enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy? defaultValue,
]) {
  if (loanInstallmentEvaluateCommandPreviewScheduleStrategy == null) {
    return null;
  }
  return enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              loanInstallmentEvaluateCommandPreviewScheduleStrategy
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String loanInstallmentEvaluateCommandPreviewScheduleStrategyExplodedListToJson(
    List<enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy>?
        loanInstallmentEvaluateCommandPreviewScheduleStrategy) {
  return loanInstallmentEvaluateCommandPreviewScheduleStrategy
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> loanInstallmentEvaluateCommandPreviewScheduleStrategyListToJson(
    List<enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy>?
        loanInstallmentEvaluateCommandPreviewScheduleStrategy) {
  if (loanInstallmentEvaluateCommandPreviewScheduleStrategy == null) {
    return [];
  }

  return loanInstallmentEvaluateCommandPreviewScheduleStrategy
      .map((e) => e.value!)
      .toList();
}

List<enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy>
    loanInstallmentEvaluateCommandPreviewScheduleStrategyListFromJson(
  List? loanInstallmentEvaluateCommandPreviewScheduleStrategy, [
  List<enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy>?
      defaultValue,
]) {
  if (loanInstallmentEvaluateCommandPreviewScheduleStrategy == null) {
    return defaultValue ?? [];
  }

  return loanInstallmentEvaluateCommandPreviewScheduleStrategy
      .map((e) => loanInstallmentEvaluateCommandPreviewScheduleStrategyFromJson(
          e.toString()))
      .toList();
}

List<enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy>?
    loanInstallmentEvaluateCommandPreviewScheduleStrategyNullableListFromJson(
  List? loanInstallmentEvaluateCommandPreviewScheduleStrategy, [
  List<enums.LoanInstallmentEvaluateCommandPreviewScheduleStrategy>?
      defaultValue,
]) {
  if (loanInstallmentEvaluateCommandPreviewScheduleStrategy == null) {
    return defaultValue;
  }

  return loanInstallmentEvaluateCommandPreviewScheduleStrategy
      .map((e) => loanInstallmentEvaluateCommandPreviewScheduleStrategyFromJson(
          e.toString()))
      .toList();
}

String? nextInstallmentIntervalNullableToJson(
    enums.NextInstallmentInterval? nextInstallmentInterval) {
  return nextInstallmentInterval?.value;
}

String? nextInstallmentIntervalToJson(
    enums.NextInstallmentInterval nextInstallmentInterval) {
  return nextInstallmentInterval.value;
}

enums.NextInstallmentInterval nextInstallmentIntervalFromJson(
  Object? nextInstallmentInterval, [
  enums.NextInstallmentInterval? defaultValue,
]) {
  return enums.NextInstallmentInterval.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          nextInstallmentInterval?.toString().toLowerCase()) ??
      defaultValue ??
      enums.NextInstallmentInterval.swaggerGeneratedUnknown;
}

enums.NextInstallmentInterval? nextInstallmentIntervalNullableFromJson(
  Object? nextInstallmentInterval, [
  enums.NextInstallmentInterval? defaultValue,
]) {
  if (nextInstallmentInterval == null) {
    return null;
  }
  return enums.NextInstallmentInterval.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          nextInstallmentInterval.toString().toLowerCase()) ??
      defaultValue;
}

String nextInstallmentIntervalExplodedListToJson(
    List<enums.NextInstallmentInterval>? nextInstallmentInterval) {
  return nextInstallmentInterval?.map((e) => e.value!).join(',') ?? '';
}

List<String> nextInstallmentIntervalListToJson(
    List<enums.NextInstallmentInterval>? nextInstallmentInterval) {
  if (nextInstallmentInterval == null) {
    return [];
  }

  return nextInstallmentInterval.map((e) => e.value!).toList();
}

List<enums.NextInstallmentInterval> nextInstallmentIntervalListFromJson(
  List? nextInstallmentInterval, [
  List<enums.NextInstallmentInterval>? defaultValue,
]) {
  if (nextInstallmentInterval == null) {
    return defaultValue ?? [];
  }

  return nextInstallmentInterval
      .map((e) => nextInstallmentIntervalFromJson(e.toString()))
      .toList();
}

List<enums.NextInstallmentInterval>?
    nextInstallmentIntervalNullableListFromJson(
  List? nextInstallmentInterval, [
  List<enums.NextInstallmentInterval>? defaultValue,
]) {
  if (nextInstallmentInterval == null) {
    return defaultValue;
  }

  return nextInstallmentInterval
      .map((e) => nextInstallmentIntervalFromJson(e.toString()))
      .toList();
}

String? loanInstallmentDisbursementV2ProductSubTypeNullableToJson(
    enums.LoanInstallmentDisbursementV2ProductSubType?
        loanInstallmentDisbursementV2ProductSubType) {
  return loanInstallmentDisbursementV2ProductSubType?.value;
}

String? loanInstallmentDisbursementV2ProductSubTypeToJson(
    enums.LoanInstallmentDisbursementV2ProductSubType
        loanInstallmentDisbursementV2ProductSubType) {
  return loanInstallmentDisbursementV2ProductSubType.value;
}

enums.LoanInstallmentDisbursementV2ProductSubType
    loanInstallmentDisbursementV2ProductSubTypeFromJson(
  Object? loanInstallmentDisbursementV2ProductSubType, [
  enums.LoanInstallmentDisbursementV2ProductSubType? defaultValue,
]) {
  return enums.LoanInstallmentDisbursementV2ProductSubType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              loanInstallmentDisbursementV2ProductSubType
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.LoanInstallmentDisbursementV2ProductSubType.swaggerGeneratedUnknown;
}

enums.LoanInstallmentDisbursementV2ProductSubType?
    loanInstallmentDisbursementV2ProductSubTypeNullableFromJson(
  Object? loanInstallmentDisbursementV2ProductSubType, [
  enums.LoanInstallmentDisbursementV2ProductSubType? defaultValue,
]) {
  if (loanInstallmentDisbursementV2ProductSubType == null) {
    return null;
  }
  return enums.LoanInstallmentDisbursementV2ProductSubType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              loanInstallmentDisbursementV2ProductSubType
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String loanInstallmentDisbursementV2ProductSubTypeExplodedListToJson(
    List<enums.LoanInstallmentDisbursementV2ProductSubType>?
        loanInstallmentDisbursementV2ProductSubType) {
  return loanInstallmentDisbursementV2ProductSubType
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> loanInstallmentDisbursementV2ProductSubTypeListToJson(
    List<enums.LoanInstallmentDisbursementV2ProductSubType>?
        loanInstallmentDisbursementV2ProductSubType) {
  if (loanInstallmentDisbursementV2ProductSubType == null) {
    return [];
  }

  return loanInstallmentDisbursementV2ProductSubType
      .map((e) => e.value!)
      .toList();
}

List<enums.LoanInstallmentDisbursementV2ProductSubType>
    loanInstallmentDisbursementV2ProductSubTypeListFromJson(
  List? loanInstallmentDisbursementV2ProductSubType, [
  List<enums.LoanInstallmentDisbursementV2ProductSubType>? defaultValue,
]) {
  if (loanInstallmentDisbursementV2ProductSubType == null) {
    return defaultValue ?? [];
  }

  return loanInstallmentDisbursementV2ProductSubType
      .map((e) =>
          loanInstallmentDisbursementV2ProductSubTypeFromJson(e.toString()))
      .toList();
}

List<enums.LoanInstallmentDisbursementV2ProductSubType>?
    loanInstallmentDisbursementV2ProductSubTypeNullableListFromJson(
  List? loanInstallmentDisbursementV2ProductSubType, [
  List<enums.LoanInstallmentDisbursementV2ProductSubType>? defaultValue,
]) {
  if (loanInstallmentDisbursementV2ProductSubType == null) {
    return defaultValue;
  }

  return loanInstallmentDisbursementV2ProductSubType
      .map((e) =>
          loanInstallmentDisbursementV2ProductSubTypeFromJson(e.toString()))
      .toList();
}

String? eligibleCreditLimitRequestDtoProductCodeNullableToJson(
    enums.EligibleCreditLimitRequestDtoProductCode?
        eligibleCreditLimitRequestDtoProductCode) {
  return eligibleCreditLimitRequestDtoProductCode?.value;
}

String? eligibleCreditLimitRequestDtoProductCodeToJson(
    enums.EligibleCreditLimitRequestDtoProductCode
        eligibleCreditLimitRequestDtoProductCode) {
  return eligibleCreditLimitRequestDtoProductCode.value;
}

enums.EligibleCreditLimitRequestDtoProductCode
    eligibleCreditLimitRequestDtoProductCodeFromJson(
  Object? eligibleCreditLimitRequestDtoProductCode, [
  enums.EligibleCreditLimitRequestDtoProductCode? defaultValue,
]) {
  return enums.EligibleCreditLimitRequestDtoProductCode.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              eligibleCreditLimitRequestDtoProductCode
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.EligibleCreditLimitRequestDtoProductCode.swaggerGeneratedUnknown;
}

enums.EligibleCreditLimitRequestDtoProductCode?
    eligibleCreditLimitRequestDtoProductCodeNullableFromJson(
  Object? eligibleCreditLimitRequestDtoProductCode, [
  enums.EligibleCreditLimitRequestDtoProductCode? defaultValue,
]) {
  if (eligibleCreditLimitRequestDtoProductCode == null) {
    return null;
  }
  return enums.EligibleCreditLimitRequestDtoProductCode.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              eligibleCreditLimitRequestDtoProductCode
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String eligibleCreditLimitRequestDtoProductCodeExplodedListToJson(
    List<enums.EligibleCreditLimitRequestDtoProductCode>?
        eligibleCreditLimitRequestDtoProductCode) {
  return eligibleCreditLimitRequestDtoProductCode
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> eligibleCreditLimitRequestDtoProductCodeListToJson(
    List<enums.EligibleCreditLimitRequestDtoProductCode>?
        eligibleCreditLimitRequestDtoProductCode) {
  if (eligibleCreditLimitRequestDtoProductCode == null) {
    return [];
  }

  return eligibleCreditLimitRequestDtoProductCode.map((e) => e.value!).toList();
}

List<enums.EligibleCreditLimitRequestDtoProductCode>
    eligibleCreditLimitRequestDtoProductCodeListFromJson(
  List? eligibleCreditLimitRequestDtoProductCode, [
  List<enums.EligibleCreditLimitRequestDtoProductCode>? defaultValue,
]) {
  if (eligibleCreditLimitRequestDtoProductCode == null) {
    return defaultValue ?? [];
  }

  return eligibleCreditLimitRequestDtoProductCode
      .map(
          (e) => eligibleCreditLimitRequestDtoProductCodeFromJson(e.toString()))
      .toList();
}

List<enums.EligibleCreditLimitRequestDtoProductCode>?
    eligibleCreditLimitRequestDtoProductCodeNullableListFromJson(
  List? eligibleCreditLimitRequestDtoProductCode, [
  List<enums.EligibleCreditLimitRequestDtoProductCode>? defaultValue,
]) {
  if (eligibleCreditLimitRequestDtoProductCode == null) {
    return defaultValue;
  }

  return eligibleCreditLimitRequestDtoProductCode
      .map(
          (e) => eligibleCreditLimitRequestDtoProductCodeFromJson(e.toString()))
      .toList();
}

String? multiUserCreateRequestDTODomainTypeNullableToJson(
    enums.MultiUserCreateRequestDTODomainType?
        multiUserCreateRequestDTODomainType) {
  return multiUserCreateRequestDTODomainType?.value;
}

String? multiUserCreateRequestDTODomainTypeToJson(
    enums.MultiUserCreateRequestDTODomainType
        multiUserCreateRequestDTODomainType) {
  return multiUserCreateRequestDTODomainType.value;
}

enums.MultiUserCreateRequestDTODomainType
    multiUserCreateRequestDTODomainTypeFromJson(
  Object? multiUserCreateRequestDTODomainType, [
  enums.MultiUserCreateRequestDTODomainType? defaultValue,
]) {
  return enums.MultiUserCreateRequestDTODomainType.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              multiUserCreateRequestDTODomainType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.MultiUserCreateRequestDTODomainType.swaggerGeneratedUnknown;
}

enums.MultiUserCreateRequestDTODomainType?
    multiUserCreateRequestDTODomainTypeNullableFromJson(
  Object? multiUserCreateRequestDTODomainType, [
  enums.MultiUserCreateRequestDTODomainType? defaultValue,
]) {
  if (multiUserCreateRequestDTODomainType == null) {
    return null;
  }
  return enums.MultiUserCreateRequestDTODomainType.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              multiUserCreateRequestDTODomainType.toString().toLowerCase()) ??
      defaultValue;
}

String multiUserCreateRequestDTODomainTypeExplodedListToJson(
    List<enums.MultiUserCreateRequestDTODomainType>?
        multiUserCreateRequestDTODomainType) {
  return multiUserCreateRequestDTODomainType?.map((e) => e.value!).join(',') ??
      '';
}

List<String> multiUserCreateRequestDTODomainTypeListToJson(
    List<enums.MultiUserCreateRequestDTODomainType>?
        multiUserCreateRequestDTODomainType) {
  if (multiUserCreateRequestDTODomainType == null) {
    return [];
  }

  return multiUserCreateRequestDTODomainType.map((e) => e.value!).toList();
}

List<enums.MultiUserCreateRequestDTODomainType>
    multiUserCreateRequestDTODomainTypeListFromJson(
  List? multiUserCreateRequestDTODomainType, [
  List<enums.MultiUserCreateRequestDTODomainType>? defaultValue,
]) {
  if (multiUserCreateRequestDTODomainType == null) {
    return defaultValue ?? [];
  }

  return multiUserCreateRequestDTODomainType
      .map((e) => multiUserCreateRequestDTODomainTypeFromJson(e.toString()))
      .toList();
}

List<enums.MultiUserCreateRequestDTODomainType>?
    multiUserCreateRequestDTODomainTypeNullableListFromJson(
  List? multiUserCreateRequestDTODomainType, [
  List<enums.MultiUserCreateRequestDTODomainType>? defaultValue,
]) {
  if (multiUserCreateRequestDTODomainType == null) {
    return defaultValue;
  }

  return multiUserCreateRequestDTODomainType
      .map((e) => multiUserCreateRequestDTODomainTypeFromJson(e.toString()))
      .toList();
}

String? multiUserResponseDTODomainTypeNullableToJson(
    enums.MultiUserResponseDTODomainType? multiUserResponseDTODomainType) {
  return multiUserResponseDTODomainType?.value;
}

String? multiUserResponseDTODomainTypeToJson(
    enums.MultiUserResponseDTODomainType multiUserResponseDTODomainType) {
  return multiUserResponseDTODomainType.value;
}

enums.MultiUserResponseDTODomainType multiUserResponseDTODomainTypeFromJson(
  Object? multiUserResponseDTODomainType, [
  enums.MultiUserResponseDTODomainType? defaultValue,
]) {
  return enums.MultiUserResponseDTODomainType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          multiUserResponseDTODomainType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.MultiUserResponseDTODomainType.swaggerGeneratedUnknown;
}

enums.MultiUserResponseDTODomainType?
    multiUserResponseDTODomainTypeNullableFromJson(
  Object? multiUserResponseDTODomainType, [
  enums.MultiUserResponseDTODomainType? defaultValue,
]) {
  if (multiUserResponseDTODomainType == null) {
    return null;
  }
  return enums.MultiUserResponseDTODomainType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          multiUserResponseDTODomainType.toString().toLowerCase()) ??
      defaultValue;
}

String multiUserResponseDTODomainTypeExplodedListToJson(
    List<enums.MultiUserResponseDTODomainType>?
        multiUserResponseDTODomainType) {
  return multiUserResponseDTODomainType?.map((e) => e.value!).join(',') ?? '';
}

List<String> multiUserResponseDTODomainTypeListToJson(
    List<enums.MultiUserResponseDTODomainType>?
        multiUserResponseDTODomainType) {
  if (multiUserResponseDTODomainType == null) {
    return [];
  }

  return multiUserResponseDTODomainType.map((e) => e.value!).toList();
}

List<enums.MultiUserResponseDTODomainType>
    multiUserResponseDTODomainTypeListFromJson(
  List? multiUserResponseDTODomainType, [
  List<enums.MultiUserResponseDTODomainType>? defaultValue,
]) {
  if (multiUserResponseDTODomainType == null) {
    return defaultValue ?? [];
  }

  return multiUserResponseDTODomainType
      .map((e) => multiUserResponseDTODomainTypeFromJson(e.toString()))
      .toList();
}

List<enums.MultiUserResponseDTODomainType>?
    multiUserResponseDTODomainTypeNullableListFromJson(
  List? multiUserResponseDTODomainType, [
  List<enums.MultiUserResponseDTODomainType>? defaultValue,
]) {
  if (multiUserResponseDTODomainType == null) {
    return defaultValue;
  }

  return multiUserResponseDTODomainType
      .map((e) => multiUserResponseDTODomainTypeFromJson(e.toString()))
      .toList();
}

String? multiUserResponseDTOStatusNullableToJson(
    enums.MultiUserResponseDTOStatus? multiUserResponseDTOStatus) {
  return multiUserResponseDTOStatus?.value;
}

String? multiUserResponseDTOStatusToJson(
    enums.MultiUserResponseDTOStatus multiUserResponseDTOStatus) {
  return multiUserResponseDTOStatus.value;
}

enums.MultiUserResponseDTOStatus multiUserResponseDTOStatusFromJson(
  Object? multiUserResponseDTOStatus, [
  enums.MultiUserResponseDTOStatus? defaultValue,
]) {
  return enums.MultiUserResponseDTOStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          multiUserResponseDTOStatus?.toString().toLowerCase()) ??
      defaultValue ??
      enums.MultiUserResponseDTOStatus.swaggerGeneratedUnknown;
}

enums.MultiUserResponseDTOStatus? multiUserResponseDTOStatusNullableFromJson(
  Object? multiUserResponseDTOStatus, [
  enums.MultiUserResponseDTOStatus? defaultValue,
]) {
  if (multiUserResponseDTOStatus == null) {
    return null;
  }
  return enums.MultiUserResponseDTOStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          multiUserResponseDTOStatus.toString().toLowerCase()) ??
      defaultValue;
}

String multiUserResponseDTOStatusExplodedListToJson(
    List<enums.MultiUserResponseDTOStatus>? multiUserResponseDTOStatus) {
  return multiUserResponseDTOStatus?.map((e) => e.value!).join(',') ?? '';
}

List<String> multiUserResponseDTOStatusListToJson(
    List<enums.MultiUserResponseDTOStatus>? multiUserResponseDTOStatus) {
  if (multiUserResponseDTOStatus == null) {
    return [];
  }

  return multiUserResponseDTOStatus.map((e) => e.value!).toList();
}

List<enums.MultiUserResponseDTOStatus> multiUserResponseDTOStatusListFromJson(
  List? multiUserResponseDTOStatus, [
  List<enums.MultiUserResponseDTOStatus>? defaultValue,
]) {
  if (multiUserResponseDTOStatus == null) {
    return defaultValue ?? [];
  }

  return multiUserResponseDTOStatus
      .map((e) => multiUserResponseDTOStatusFromJson(e.toString()))
      .toList();
}

List<enums.MultiUserResponseDTOStatus>?
    multiUserResponseDTOStatusNullableListFromJson(
  List? multiUserResponseDTOStatus, [
  List<enums.MultiUserResponseDTOStatus>? defaultValue,
]) {
  if (multiUserResponseDTOStatus == null) {
    return defaultValue;
  }

  return multiUserResponseDTOStatus
      .map((e) => multiUserResponseDTOStatusFromJson(e.toString()))
      .toList();
}

String? transactionFilterRequestDtoTransactionTypeNullableToJson(
    enums.TransactionFilterRequestDtoTransactionType?
        transactionFilterRequestDtoTransactionType) {
  return transactionFilterRequestDtoTransactionType?.value;
}

String? transactionFilterRequestDtoTransactionTypeToJson(
    enums.TransactionFilterRequestDtoTransactionType
        transactionFilterRequestDtoTransactionType) {
  return transactionFilterRequestDtoTransactionType.value;
}

enums.TransactionFilterRequestDtoTransactionType
    transactionFilterRequestDtoTransactionTypeFromJson(
  Object? transactionFilterRequestDtoTransactionType, [
  enums.TransactionFilterRequestDtoTransactionType? defaultValue,
]) {
  return enums.TransactionFilterRequestDtoTransactionType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              transactionFilterRequestDtoTransactionType
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.TransactionFilterRequestDtoTransactionType.swaggerGeneratedUnknown;
}

enums.TransactionFilterRequestDtoTransactionType?
    transactionFilterRequestDtoTransactionTypeNullableFromJson(
  Object? transactionFilterRequestDtoTransactionType, [
  enums.TransactionFilterRequestDtoTransactionType? defaultValue,
]) {
  if (transactionFilterRequestDtoTransactionType == null) {
    return null;
  }
  return enums.TransactionFilterRequestDtoTransactionType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              transactionFilterRequestDtoTransactionType
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String transactionFilterRequestDtoTransactionTypeExplodedListToJson(
    List<enums.TransactionFilterRequestDtoTransactionType>?
        transactionFilterRequestDtoTransactionType) {
  return transactionFilterRequestDtoTransactionType
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> transactionFilterRequestDtoTransactionTypeListToJson(
    List<enums.TransactionFilterRequestDtoTransactionType>?
        transactionFilterRequestDtoTransactionType) {
  if (transactionFilterRequestDtoTransactionType == null) {
    return [];
  }

  return transactionFilterRequestDtoTransactionType
      .map((e) => e.value!)
      .toList();
}

List<enums.TransactionFilterRequestDtoTransactionType>
    transactionFilterRequestDtoTransactionTypeListFromJson(
  List? transactionFilterRequestDtoTransactionType, [
  List<enums.TransactionFilterRequestDtoTransactionType>? defaultValue,
]) {
  if (transactionFilterRequestDtoTransactionType == null) {
    return defaultValue ?? [];
  }

  return transactionFilterRequestDtoTransactionType
      .map((e) =>
          transactionFilterRequestDtoTransactionTypeFromJson(e.toString()))
      .toList();
}

List<enums.TransactionFilterRequestDtoTransactionType>?
    transactionFilterRequestDtoTransactionTypeNullableListFromJson(
  List? transactionFilterRequestDtoTransactionType, [
  List<enums.TransactionFilterRequestDtoTransactionType>? defaultValue,
]) {
  if (transactionFilterRequestDtoTransactionType == null) {
    return defaultValue;
  }

  return transactionFilterRequestDtoTransactionType
      .map((e) =>
          transactionFilterRequestDtoTransactionTypeFromJson(e.toString()))
      .toList();
}

String? transactionResponseTransactionTypeNullableToJson(
    enums.TransactionResponseTransactionType?
        transactionResponseTransactionType) {
  return transactionResponseTransactionType?.value;
}

String? transactionResponseTransactionTypeToJson(
    enums.TransactionResponseTransactionType
        transactionResponseTransactionType) {
  return transactionResponseTransactionType.value;
}

enums.TransactionResponseTransactionType
    transactionResponseTransactionTypeFromJson(
  Object? transactionResponseTransactionType, [
  enums.TransactionResponseTransactionType? defaultValue,
]) {
  return enums.TransactionResponseTransactionType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          transactionResponseTransactionType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.TransactionResponseTransactionType.swaggerGeneratedUnknown;
}

enums.TransactionResponseTransactionType?
    transactionResponseTransactionTypeNullableFromJson(
  Object? transactionResponseTransactionType, [
  enums.TransactionResponseTransactionType? defaultValue,
]) {
  if (transactionResponseTransactionType == null) {
    return null;
  }
  return enums.TransactionResponseTransactionType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          transactionResponseTransactionType.toString().toLowerCase()) ??
      defaultValue;
}

String transactionResponseTransactionTypeExplodedListToJson(
    List<enums.TransactionResponseTransactionType>?
        transactionResponseTransactionType) {
  return transactionResponseTransactionType?.map((e) => e.value!).join(',') ??
      '';
}

List<String> transactionResponseTransactionTypeListToJson(
    List<enums.TransactionResponseTransactionType>?
        transactionResponseTransactionType) {
  if (transactionResponseTransactionType == null) {
    return [];
  }

  return transactionResponseTransactionType.map((e) => e.value!).toList();
}

List<enums.TransactionResponseTransactionType>
    transactionResponseTransactionTypeListFromJson(
  List? transactionResponseTransactionType, [
  List<enums.TransactionResponseTransactionType>? defaultValue,
]) {
  if (transactionResponseTransactionType == null) {
    return defaultValue ?? [];
  }

  return transactionResponseTransactionType
      .map((e) => transactionResponseTransactionTypeFromJson(e.toString()))
      .toList();
}

List<enums.TransactionResponseTransactionType>?
    transactionResponseTransactionTypeNullableListFromJson(
  List? transactionResponseTransactionType, [
  List<enums.TransactionResponseTransactionType>? defaultValue,
]) {
  if (transactionResponseTransactionType == null) {
    return defaultValue;
  }

  return transactionResponseTransactionType
      .map((e) => transactionResponseTransactionTypeFromJson(e.toString()))
      .toList();
}

String? transactionResponseTransactionSubTypeNullableToJson(
    enums.TransactionResponseTransactionSubType?
        transactionResponseTransactionSubType) {
  return transactionResponseTransactionSubType?.value;
}

String? transactionResponseTransactionSubTypeToJson(
    enums.TransactionResponseTransactionSubType
        transactionResponseTransactionSubType) {
  return transactionResponseTransactionSubType.value;
}

enums.TransactionResponseTransactionSubType
    transactionResponseTransactionSubTypeFromJson(
  Object? transactionResponseTransactionSubType, [
  enums.TransactionResponseTransactionSubType? defaultValue,
]) {
  return enums.TransactionResponseTransactionSubType.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              transactionResponseTransactionSubType
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.TransactionResponseTransactionSubType.swaggerGeneratedUnknown;
}

enums.TransactionResponseTransactionSubType?
    transactionResponseTransactionSubTypeNullableFromJson(
  Object? transactionResponseTransactionSubType, [
  enums.TransactionResponseTransactionSubType? defaultValue,
]) {
  if (transactionResponseTransactionSubType == null) {
    return null;
  }
  return enums.TransactionResponseTransactionSubType.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              transactionResponseTransactionSubType.toString().toLowerCase()) ??
      defaultValue;
}

String transactionResponseTransactionSubTypeExplodedListToJson(
    List<enums.TransactionResponseTransactionSubType>?
        transactionResponseTransactionSubType) {
  return transactionResponseTransactionSubType
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> transactionResponseTransactionSubTypeListToJson(
    List<enums.TransactionResponseTransactionSubType>?
        transactionResponseTransactionSubType) {
  if (transactionResponseTransactionSubType == null) {
    return [];
  }

  return transactionResponseTransactionSubType.map((e) => e.value!).toList();
}

List<enums.TransactionResponseTransactionSubType>
    transactionResponseTransactionSubTypeListFromJson(
  List? transactionResponseTransactionSubType, [
  List<enums.TransactionResponseTransactionSubType>? defaultValue,
]) {
  if (transactionResponseTransactionSubType == null) {
    return defaultValue ?? [];
  }

  return transactionResponseTransactionSubType
      .map((e) => transactionResponseTransactionSubTypeFromJson(e.toString()))
      .toList();
}

List<enums.TransactionResponseTransactionSubType>?
    transactionResponseTransactionSubTypeNullableListFromJson(
  List? transactionResponseTransactionSubType, [
  List<enums.TransactionResponseTransactionSubType>? defaultValue,
]) {
  if (transactionResponseTransactionSubType == null) {
    return defaultValue;
  }

  return transactionResponseTransactionSubType
      .map((e) => transactionResponseTransactionSubTypeFromJson(e.toString()))
      .toList();
}

String? transactionResponseTransactionStatusNullableToJson(
    enums.TransactionResponseTransactionStatus?
        transactionResponseTransactionStatus) {
  return transactionResponseTransactionStatus?.value;
}

String? transactionResponseTransactionStatusToJson(
    enums.TransactionResponseTransactionStatus
        transactionResponseTransactionStatus) {
  return transactionResponseTransactionStatus.value;
}

enums.TransactionResponseTransactionStatus
    transactionResponseTransactionStatusFromJson(
  Object? transactionResponseTransactionStatus, [
  enums.TransactionResponseTransactionStatus? defaultValue,
]) {
  return enums.TransactionResponseTransactionStatus.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              transactionResponseTransactionStatus?.toString().toLowerCase()) ??
      defaultValue ??
      enums.TransactionResponseTransactionStatus.swaggerGeneratedUnknown;
}

enums.TransactionResponseTransactionStatus?
    transactionResponseTransactionStatusNullableFromJson(
  Object? transactionResponseTransactionStatus, [
  enums.TransactionResponseTransactionStatus? defaultValue,
]) {
  if (transactionResponseTransactionStatus == null) {
    return null;
  }
  return enums.TransactionResponseTransactionStatus.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              transactionResponseTransactionStatus.toString().toLowerCase()) ??
      defaultValue;
}

String transactionResponseTransactionStatusExplodedListToJson(
    List<enums.TransactionResponseTransactionStatus>?
        transactionResponseTransactionStatus) {
  return transactionResponseTransactionStatus?.map((e) => e.value!).join(',') ??
      '';
}

List<String> transactionResponseTransactionStatusListToJson(
    List<enums.TransactionResponseTransactionStatus>?
        transactionResponseTransactionStatus) {
  if (transactionResponseTransactionStatus == null) {
    return [];
  }

  return transactionResponseTransactionStatus.map((e) => e.value!).toList();
}

List<enums.TransactionResponseTransactionStatus>
    transactionResponseTransactionStatusListFromJson(
  List? transactionResponseTransactionStatus, [
  List<enums.TransactionResponseTransactionStatus>? defaultValue,
]) {
  if (transactionResponseTransactionStatus == null) {
    return defaultValue ?? [];
  }

  return transactionResponseTransactionStatus
      .map((e) => transactionResponseTransactionStatusFromJson(e.toString()))
      .toList();
}

List<enums.TransactionResponseTransactionStatus>?
    transactionResponseTransactionStatusNullableListFromJson(
  List? transactionResponseTransactionStatus, [
  List<enums.TransactionResponseTransactionStatus>? defaultValue,
]) {
  if (transactionResponseTransactionStatus == null) {
    return defaultValue;
  }

  return transactionResponseTransactionStatus
      .map((e) => transactionResponseTransactionStatusFromJson(e.toString()))
      .toList();
}

String? transactionResponseTransactionModeNullableToJson(
    enums.TransactionResponseTransactionMode?
        transactionResponseTransactionMode) {
  return transactionResponseTransactionMode?.value;
}

String? transactionResponseTransactionModeToJson(
    enums.TransactionResponseTransactionMode
        transactionResponseTransactionMode) {
  return transactionResponseTransactionMode.value;
}

enums.TransactionResponseTransactionMode
    transactionResponseTransactionModeFromJson(
  Object? transactionResponseTransactionMode, [
  enums.TransactionResponseTransactionMode? defaultValue,
]) {
  return enums.TransactionResponseTransactionMode.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          transactionResponseTransactionMode?.toString().toLowerCase()) ??
      defaultValue ??
      enums.TransactionResponseTransactionMode.swaggerGeneratedUnknown;
}

enums.TransactionResponseTransactionMode?
    transactionResponseTransactionModeNullableFromJson(
  Object? transactionResponseTransactionMode, [
  enums.TransactionResponseTransactionMode? defaultValue,
]) {
  if (transactionResponseTransactionMode == null) {
    return null;
  }
  return enums.TransactionResponseTransactionMode.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          transactionResponseTransactionMode.toString().toLowerCase()) ??
      defaultValue;
}

String transactionResponseTransactionModeExplodedListToJson(
    List<enums.TransactionResponseTransactionMode>?
        transactionResponseTransactionMode) {
  return transactionResponseTransactionMode?.map((e) => e.value!).join(',') ??
      '';
}

List<String> transactionResponseTransactionModeListToJson(
    List<enums.TransactionResponseTransactionMode>?
        transactionResponseTransactionMode) {
  if (transactionResponseTransactionMode == null) {
    return [];
  }

  return transactionResponseTransactionMode.map((e) => e.value!).toList();
}

List<enums.TransactionResponseTransactionMode>
    transactionResponseTransactionModeListFromJson(
  List? transactionResponseTransactionMode, [
  List<enums.TransactionResponseTransactionMode>? defaultValue,
]) {
  if (transactionResponseTransactionMode == null) {
    return defaultValue ?? [];
  }

  return transactionResponseTransactionMode
      .map((e) => transactionResponseTransactionModeFromJson(e.toString()))
      .toList();
}

List<enums.TransactionResponseTransactionMode>?
    transactionResponseTransactionModeNullableListFromJson(
  List? transactionResponseTransactionMode, [
  List<enums.TransactionResponseTransactionMode>? defaultValue,
]) {
  if (transactionResponseTransactionMode == null) {
    return defaultValue;
  }

  return transactionResponseTransactionMode
      .map((e) => transactionResponseTransactionModeFromJson(e.toString()))
      .toList();
}

String? statusFilterNullableToJson(enums.StatusFilter? statusFilter) {
  return statusFilter?.value;
}

String? statusFilterToJson(enums.StatusFilter statusFilter) {
  return statusFilter.value;
}

enums.StatusFilter statusFilterFromJson(
  Object? statusFilter, [
  enums.StatusFilter? defaultValue,
]) {
  return enums.StatusFilter.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          statusFilter?.toString().toLowerCase()) ??
      defaultValue ??
      enums.StatusFilter.swaggerGeneratedUnknown;
}

enums.StatusFilter? statusFilterNullableFromJson(
  Object? statusFilter, [
  enums.StatusFilter? defaultValue,
]) {
  if (statusFilter == null) {
    return null;
  }
  return enums.StatusFilter.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          statusFilter.toString().toLowerCase()) ??
      defaultValue;
}

String statusFilterExplodedListToJson(List<enums.StatusFilter>? statusFilter) {
  return statusFilter?.map((e) => e.value!).join(',') ?? '';
}

List<String> statusFilterListToJson(List<enums.StatusFilter>? statusFilter) {
  if (statusFilter == null) {
    return [];
  }

  return statusFilter.map((e) => e.value!).toList();
}

List<enums.StatusFilter> statusFilterListFromJson(
  List? statusFilter, [
  List<enums.StatusFilter>? defaultValue,
]) {
  if (statusFilter == null) {
    return defaultValue ?? [];
  }

  return statusFilter.map((e) => statusFilterFromJson(e.toString())).toList();
}

List<enums.StatusFilter>? statusFilterNullableListFromJson(
  List? statusFilter, [
  List<enums.StatusFilter>? defaultValue,
]) {
  if (statusFilter == null) {
    return defaultValue;
  }

  return statusFilter.map((e) => statusFilterFromJson(e.toString())).toList();
}

String? optInStatusNullableToJson(enums.OptInStatus? optInStatus) {
  return optInStatus?.value;
}

String? optInStatusToJson(enums.OptInStatus optInStatus) {
  return optInStatus.value;
}

enums.OptInStatus optInStatusFromJson(
  Object? optInStatus, [
  enums.OptInStatus? defaultValue,
]) {
  return enums.OptInStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          optInStatus?.toString().toLowerCase()) ??
      defaultValue ??
      enums.OptInStatus.swaggerGeneratedUnknown;
}

enums.OptInStatus? optInStatusNullableFromJson(
  Object? optInStatus, [
  enums.OptInStatus? defaultValue,
]) {
  if (optInStatus == null) {
    return null;
  }
  return enums.OptInStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          optInStatus.toString().toLowerCase()) ??
      defaultValue;
}

String optInStatusExplodedListToJson(List<enums.OptInStatus>? optInStatus) {
  return optInStatus?.map((e) => e.value!).join(',') ?? '';
}

List<String> optInStatusListToJson(List<enums.OptInStatus>? optInStatus) {
  if (optInStatus == null) {
    return [];
  }

  return optInStatus.map((e) => e.value!).toList();
}

List<enums.OptInStatus> optInStatusListFromJson(
  List? optInStatus, [
  List<enums.OptInStatus>? defaultValue,
]) {
  if (optInStatus == null) {
    return defaultValue ?? [];
  }

  return optInStatus.map((e) => optInStatusFromJson(e.toString())).toList();
}

List<enums.OptInStatus>? optInStatusNullableListFromJson(
  List? optInStatus, [
  List<enums.OptInStatus>? defaultValue,
]) {
  if (optInStatus == null) {
    return defaultValue;
  }

  return optInStatus.map((e) => optInStatusFromJson(e.toString())).toList();
}

String? salesChannelResponseCodeNullableToJson(
    enums.SalesChannelResponseCode? salesChannelResponseCode) {
  return salesChannelResponseCode?.value;
}

String? salesChannelResponseCodeToJson(
    enums.SalesChannelResponseCode salesChannelResponseCode) {
  return salesChannelResponseCode.value;
}

enums.SalesChannelResponseCode salesChannelResponseCodeFromJson(
  Object? salesChannelResponseCode, [
  enums.SalesChannelResponseCode? defaultValue,
]) {
  return enums.SalesChannelResponseCode.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          salesChannelResponseCode?.toString().toLowerCase()) ??
      defaultValue ??
      enums.SalesChannelResponseCode.swaggerGeneratedUnknown;
}

enums.SalesChannelResponseCode? salesChannelResponseCodeNullableFromJson(
  Object? salesChannelResponseCode, [
  enums.SalesChannelResponseCode? defaultValue,
]) {
  if (salesChannelResponseCode == null) {
    return null;
  }
  return enums.SalesChannelResponseCode.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          salesChannelResponseCode.toString().toLowerCase()) ??
      defaultValue;
}

String salesChannelResponseCodeExplodedListToJson(
    List<enums.SalesChannelResponseCode>? salesChannelResponseCode) {
  return salesChannelResponseCode?.map((e) => e.value!).join(',') ?? '';
}

List<String> salesChannelResponseCodeListToJson(
    List<enums.SalesChannelResponseCode>? salesChannelResponseCode) {
  if (salesChannelResponseCode == null) {
    return [];
  }

  return salesChannelResponseCode.map((e) => e.value!).toList();
}

List<enums.SalesChannelResponseCode> salesChannelResponseCodeListFromJson(
  List? salesChannelResponseCode, [
  List<enums.SalesChannelResponseCode>? defaultValue,
]) {
  if (salesChannelResponseCode == null) {
    return defaultValue ?? [];
  }

  return salesChannelResponseCode
      .map((e) => salesChannelResponseCodeFromJson(e.toString()))
      .toList();
}

List<enums.SalesChannelResponseCode>?
    salesChannelResponseCodeNullableListFromJson(
  List? salesChannelResponseCode, [
  List<enums.SalesChannelResponseCode>? defaultValue,
]) {
  if (salesChannelResponseCode == null) {
    return defaultValue;
  }

  return salesChannelResponseCode
      .map((e) => salesChannelResponseCodeFromJson(e.toString()))
      .toList();
}

String? installmentIntervalIntervalNullableToJson(
    enums.InstallmentIntervalInterval? installmentIntervalInterval) {
  return installmentIntervalInterval?.value;
}

String? installmentIntervalIntervalToJson(
    enums.InstallmentIntervalInterval installmentIntervalInterval) {
  return installmentIntervalInterval.value;
}

enums.InstallmentIntervalInterval installmentIntervalIntervalFromJson(
  Object? installmentIntervalInterval, [
  enums.InstallmentIntervalInterval? defaultValue,
]) {
  return enums.InstallmentIntervalInterval.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          installmentIntervalInterval?.toString().toLowerCase()) ??
      defaultValue ??
      enums.InstallmentIntervalInterval.swaggerGeneratedUnknown;
}

enums.InstallmentIntervalInterval? installmentIntervalIntervalNullableFromJson(
  Object? installmentIntervalInterval, [
  enums.InstallmentIntervalInterval? defaultValue,
]) {
  if (installmentIntervalInterval == null) {
    return null;
  }
  return enums.InstallmentIntervalInterval.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          installmentIntervalInterval.toString().toLowerCase()) ??
      defaultValue;
}

String installmentIntervalIntervalExplodedListToJson(
    List<enums.InstallmentIntervalInterval>? installmentIntervalInterval) {
  return installmentIntervalInterval?.map((e) => e.value!).join(',') ?? '';
}

List<String> installmentIntervalIntervalListToJson(
    List<enums.InstallmentIntervalInterval>? installmentIntervalInterval) {
  if (installmentIntervalInterval == null) {
    return [];
  }

  return installmentIntervalInterval.map((e) => e.value!).toList();
}

List<enums.InstallmentIntervalInterval> installmentIntervalIntervalListFromJson(
  List? installmentIntervalInterval, [
  List<enums.InstallmentIntervalInterval>? defaultValue,
]) {
  if (installmentIntervalInterval == null) {
    return defaultValue ?? [];
  }

  return installmentIntervalInterval
      .map((e) => installmentIntervalIntervalFromJson(e.toString()))
      .toList();
}

List<enums.InstallmentIntervalInterval>?
    installmentIntervalIntervalNullableListFromJson(
  List? installmentIntervalInterval, [
  List<enums.InstallmentIntervalInterval>? defaultValue,
]) {
  if (installmentIntervalInterval == null) {
    return defaultValue;
  }

  return installmentIntervalInterval
      .map((e) => installmentIntervalIntervalFromJson(e.toString()))
      .toList();
}

String? nonRegisteredVatReasonTypeNullableToJson(
    enums.NonRegisteredVatReasonType? nonRegisteredVatReasonType) {
  return nonRegisteredVatReasonType?.value;
}

String? nonRegisteredVatReasonTypeToJson(
    enums.NonRegisteredVatReasonType nonRegisteredVatReasonType) {
  return nonRegisteredVatReasonType.value;
}

enums.NonRegisteredVatReasonType nonRegisteredVatReasonTypeFromJson(
  Object? nonRegisteredVatReasonType, [
  enums.NonRegisteredVatReasonType? defaultValue,
]) {
  return enums.NonRegisteredVatReasonType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          nonRegisteredVatReasonType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.NonRegisteredVatReasonType.swaggerGeneratedUnknown;
}

enums.NonRegisteredVatReasonType? nonRegisteredVatReasonTypeNullableFromJson(
  Object? nonRegisteredVatReasonType, [
  enums.NonRegisteredVatReasonType? defaultValue,
]) {
  if (nonRegisteredVatReasonType == null) {
    return null;
  }
  return enums.NonRegisteredVatReasonType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          nonRegisteredVatReasonType.toString().toLowerCase()) ??
      defaultValue;
}

String nonRegisteredVatReasonTypeExplodedListToJson(
    List<enums.NonRegisteredVatReasonType>? nonRegisteredVatReasonType) {
  return nonRegisteredVatReasonType?.map((e) => e.value!).join(',') ?? '';
}

List<String> nonRegisteredVatReasonTypeListToJson(
    List<enums.NonRegisteredVatReasonType>? nonRegisteredVatReasonType) {
  if (nonRegisteredVatReasonType == null) {
    return [];
  }

  return nonRegisteredVatReasonType.map((e) => e.value!).toList();
}

List<enums.NonRegisteredVatReasonType> nonRegisteredVatReasonTypeListFromJson(
  List? nonRegisteredVatReasonType, [
  List<enums.NonRegisteredVatReasonType>? defaultValue,
]) {
  if (nonRegisteredVatReasonType == null) {
    return defaultValue ?? [];
  }

  return nonRegisteredVatReasonType
      .map((e) => nonRegisteredVatReasonTypeFromJson(e.toString()))
      .toList();
}

List<enums.NonRegisteredVatReasonType>?
    nonRegisteredVatReasonTypeNullableListFromJson(
  List? nonRegisteredVatReasonType, [
  List<enums.NonRegisteredVatReasonType>? defaultValue,
]) {
  if (nonRegisteredVatReasonType == null) {
    return defaultValue;
  }

  return nonRegisteredVatReasonType
      .map((e) => nonRegisteredVatReasonTypeFromJson(e.toString()))
      .toList();
}

String? employeeCountResponseCodeNullableToJson(
    enums.EmployeeCountResponseCode? employeeCountResponseCode) {
  return employeeCountResponseCode?.value;
}

String? employeeCountResponseCodeToJson(
    enums.EmployeeCountResponseCode employeeCountResponseCode) {
  return employeeCountResponseCode.value;
}

enums.EmployeeCountResponseCode employeeCountResponseCodeFromJson(
  Object? employeeCountResponseCode, [
  enums.EmployeeCountResponseCode? defaultValue,
]) {
  return enums.EmployeeCountResponseCode.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          employeeCountResponseCode?.toString().toLowerCase()) ??
      defaultValue ??
      enums.EmployeeCountResponseCode.swaggerGeneratedUnknown;
}

enums.EmployeeCountResponseCode? employeeCountResponseCodeNullableFromJson(
  Object? employeeCountResponseCode, [
  enums.EmployeeCountResponseCode? defaultValue,
]) {
  if (employeeCountResponseCode == null) {
    return null;
  }
  return enums.EmployeeCountResponseCode.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          employeeCountResponseCode.toString().toLowerCase()) ??
      defaultValue;
}

String employeeCountResponseCodeExplodedListToJson(
    List<enums.EmployeeCountResponseCode>? employeeCountResponseCode) {
  return employeeCountResponseCode?.map((e) => e.value!).join(',') ?? '';
}

List<String> employeeCountResponseCodeListToJson(
    List<enums.EmployeeCountResponseCode>? employeeCountResponseCode) {
  if (employeeCountResponseCode == null) {
    return [];
  }

  return employeeCountResponseCode.map((e) => e.value!).toList();
}

List<enums.EmployeeCountResponseCode> employeeCountResponseCodeListFromJson(
  List? employeeCountResponseCode, [
  List<enums.EmployeeCountResponseCode>? defaultValue,
]) {
  if (employeeCountResponseCode == null) {
    return defaultValue ?? [];
  }

  return employeeCountResponseCode
      .map((e) => employeeCountResponseCodeFromJson(e.toString()))
      .toList();
}

List<enums.EmployeeCountResponseCode>?
    employeeCountResponseCodeNullableListFromJson(
  List? employeeCountResponseCode, [
  List<enums.EmployeeCountResponseCode>? defaultValue,
]) {
  if (employeeCountResponseCode == null) {
    return defaultValue;
  }

  return employeeCountResponseCode
      .map((e) => employeeCountResponseCodeFromJson(e.toString()))
      .toList();
}

String? nonEligibilityDetailsReasonNullableToJson(
    enums.NonEligibilityDetailsReason? nonEligibilityDetailsReason) {
  return nonEligibilityDetailsReason?.value;
}

String? nonEligibilityDetailsReasonToJson(
    enums.NonEligibilityDetailsReason nonEligibilityDetailsReason) {
  return nonEligibilityDetailsReason.value;
}

enums.NonEligibilityDetailsReason nonEligibilityDetailsReasonFromJson(
  Object? nonEligibilityDetailsReason, [
  enums.NonEligibilityDetailsReason? defaultValue,
]) {
  return enums.NonEligibilityDetailsReason.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          nonEligibilityDetailsReason?.toString().toLowerCase()) ??
      defaultValue ??
      enums.NonEligibilityDetailsReason.swaggerGeneratedUnknown;
}

enums.NonEligibilityDetailsReason? nonEligibilityDetailsReasonNullableFromJson(
  Object? nonEligibilityDetailsReason, [
  enums.NonEligibilityDetailsReason? defaultValue,
]) {
  if (nonEligibilityDetailsReason == null) {
    return null;
  }
  return enums.NonEligibilityDetailsReason.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          nonEligibilityDetailsReason.toString().toLowerCase()) ??
      defaultValue;
}

String nonEligibilityDetailsReasonExplodedListToJson(
    List<enums.NonEligibilityDetailsReason>? nonEligibilityDetailsReason) {
  return nonEligibilityDetailsReason?.map((e) => e.value!).join(',') ?? '';
}

List<String> nonEligibilityDetailsReasonListToJson(
    List<enums.NonEligibilityDetailsReason>? nonEligibilityDetailsReason) {
  if (nonEligibilityDetailsReason == null) {
    return [];
  }

  return nonEligibilityDetailsReason.map((e) => e.value!).toList();
}

List<enums.NonEligibilityDetailsReason> nonEligibilityDetailsReasonListFromJson(
  List? nonEligibilityDetailsReason, [
  List<enums.NonEligibilityDetailsReason>? defaultValue,
]) {
  if (nonEligibilityDetailsReason == null) {
    return defaultValue ?? [];
  }

  return nonEligibilityDetailsReason
      .map((e) => nonEligibilityDetailsReasonFromJson(e.toString()))
      .toList();
}

List<enums.NonEligibilityDetailsReason>?
    nonEligibilityDetailsReasonNullableListFromJson(
  List? nonEligibilityDetailsReason, [
  List<enums.NonEligibilityDetailsReason>? defaultValue,
]) {
  if (nonEligibilityDetailsReason == null) {
    return defaultValue;
  }

  return nonEligibilityDetailsReason
      .map((e) => nonEligibilityDetailsReasonFromJson(e.toString()))
      .toList();
}

String? multiUserApprovalsInfoDtoStatusNullableToJson(
    enums.MultiUserApprovalsInfoDtoStatus? multiUserApprovalsInfoDtoStatus) {
  return multiUserApprovalsInfoDtoStatus?.value;
}

String? multiUserApprovalsInfoDtoStatusToJson(
    enums.MultiUserApprovalsInfoDtoStatus multiUserApprovalsInfoDtoStatus) {
  return multiUserApprovalsInfoDtoStatus.value;
}

enums.MultiUserApprovalsInfoDtoStatus multiUserApprovalsInfoDtoStatusFromJson(
  Object? multiUserApprovalsInfoDtoStatus, [
  enums.MultiUserApprovalsInfoDtoStatus? defaultValue,
]) {
  return enums.MultiUserApprovalsInfoDtoStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          multiUserApprovalsInfoDtoStatus?.toString().toLowerCase()) ??
      defaultValue ??
      enums.MultiUserApprovalsInfoDtoStatus.swaggerGeneratedUnknown;
}

enums.MultiUserApprovalsInfoDtoStatus?
    multiUserApprovalsInfoDtoStatusNullableFromJson(
  Object? multiUserApprovalsInfoDtoStatus, [
  enums.MultiUserApprovalsInfoDtoStatus? defaultValue,
]) {
  if (multiUserApprovalsInfoDtoStatus == null) {
    return null;
  }
  return enums.MultiUserApprovalsInfoDtoStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          multiUserApprovalsInfoDtoStatus.toString().toLowerCase()) ??
      defaultValue;
}

String multiUserApprovalsInfoDtoStatusExplodedListToJson(
    List<enums.MultiUserApprovalsInfoDtoStatus>?
        multiUserApprovalsInfoDtoStatus) {
  return multiUserApprovalsInfoDtoStatus?.map((e) => e.value!).join(',') ?? '';
}

List<String> multiUserApprovalsInfoDtoStatusListToJson(
    List<enums.MultiUserApprovalsInfoDtoStatus>?
        multiUserApprovalsInfoDtoStatus) {
  if (multiUserApprovalsInfoDtoStatus == null) {
    return [];
  }

  return multiUserApprovalsInfoDtoStatus.map((e) => e.value!).toList();
}

List<enums.MultiUserApprovalsInfoDtoStatus>
    multiUserApprovalsInfoDtoStatusListFromJson(
  List? multiUserApprovalsInfoDtoStatus, [
  List<enums.MultiUserApprovalsInfoDtoStatus>? defaultValue,
]) {
  if (multiUserApprovalsInfoDtoStatus == null) {
    return defaultValue ?? [];
  }

  return multiUserApprovalsInfoDtoStatus
      .map((e) => multiUserApprovalsInfoDtoStatusFromJson(e.toString()))
      .toList();
}

List<enums.MultiUserApprovalsInfoDtoStatus>?
    multiUserApprovalsInfoDtoStatusNullableListFromJson(
  List? multiUserApprovalsInfoDtoStatus, [
  List<enums.MultiUserApprovalsInfoDtoStatus>? defaultValue,
]) {
  if (multiUserApprovalsInfoDtoStatus == null) {
    return defaultValue;
  }

  return multiUserApprovalsInfoDtoStatus
      .map((e) => multiUserApprovalsInfoDtoStatusFromJson(e.toString()))
      .toList();
}

String? getRequestsParamsDomainTypesNullableToJson(
    enums.GetRequestsParamsDomainTypes? getRequestsParamsDomainTypes) {
  return getRequestsParamsDomainTypes?.value;
}

String? getRequestsParamsDomainTypesToJson(
    enums.GetRequestsParamsDomainTypes getRequestsParamsDomainTypes) {
  return getRequestsParamsDomainTypes.value;
}

enums.GetRequestsParamsDomainTypes getRequestsParamsDomainTypesFromJson(
  Object? getRequestsParamsDomainTypes, [
  enums.GetRequestsParamsDomainTypes? defaultValue,
]) {
  return enums.GetRequestsParamsDomainTypes.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          getRequestsParamsDomainTypes?.toString().toLowerCase()) ??
      defaultValue ??
      enums.GetRequestsParamsDomainTypes.swaggerGeneratedUnknown;
}

enums.GetRequestsParamsDomainTypes?
    getRequestsParamsDomainTypesNullableFromJson(
  Object? getRequestsParamsDomainTypes, [
  enums.GetRequestsParamsDomainTypes? defaultValue,
]) {
  if (getRequestsParamsDomainTypes == null) {
    return null;
  }
  return enums.GetRequestsParamsDomainTypes.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          getRequestsParamsDomainTypes.toString().toLowerCase()) ??
      defaultValue;
}

String getRequestsParamsDomainTypesExplodedListToJson(
    List<enums.GetRequestsParamsDomainTypes>? getRequestsParamsDomainTypes) {
  return getRequestsParamsDomainTypes?.map((e) => e.value!).join(',') ?? '';
}

List<String> getRequestsParamsDomainTypesListToJson(
    List<enums.GetRequestsParamsDomainTypes>? getRequestsParamsDomainTypes) {
  if (getRequestsParamsDomainTypes == null) {
    return [];
  }

  return getRequestsParamsDomainTypes.map((e) => e.value!).toList();
}

List<enums.GetRequestsParamsDomainTypes>
    getRequestsParamsDomainTypesListFromJson(
  List? getRequestsParamsDomainTypes, [
  List<enums.GetRequestsParamsDomainTypes>? defaultValue,
]) {
  if (getRequestsParamsDomainTypes == null) {
    return defaultValue ?? [];
  }

  return getRequestsParamsDomainTypes
      .map((e) => getRequestsParamsDomainTypesFromJson(e.toString()))
      .toList();
}

List<enums.GetRequestsParamsDomainTypes>?
    getRequestsParamsDomainTypesNullableListFromJson(
  List? getRequestsParamsDomainTypes, [
  List<enums.GetRequestsParamsDomainTypes>? defaultValue,
]) {
  if (getRequestsParamsDomainTypes == null) {
    return defaultValue;
  }

  return getRequestsParamsDomainTypes
      .map((e) => getRequestsParamsDomainTypesFromJson(e.toString()))
      .toList();
}

String? getRequestsParamsStatusNullableToJson(
    enums.GetRequestsParamsStatus? getRequestsParamsStatus) {
  return getRequestsParamsStatus?.value;
}

String? getRequestsParamsStatusToJson(
    enums.GetRequestsParamsStatus getRequestsParamsStatus) {
  return getRequestsParamsStatus.value;
}

enums.GetRequestsParamsStatus getRequestsParamsStatusFromJson(
  Object? getRequestsParamsStatus, [
  enums.GetRequestsParamsStatus? defaultValue,
]) {
  return enums.GetRequestsParamsStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          getRequestsParamsStatus?.toString().toLowerCase()) ??
      defaultValue ??
      enums.GetRequestsParamsStatus.swaggerGeneratedUnknown;
}

enums.GetRequestsParamsStatus? getRequestsParamsStatusNullableFromJson(
  Object? getRequestsParamsStatus, [
  enums.GetRequestsParamsStatus? defaultValue,
]) {
  if (getRequestsParamsStatus == null) {
    return null;
  }
  return enums.GetRequestsParamsStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          getRequestsParamsStatus.toString().toLowerCase()) ??
      defaultValue;
}

String getRequestsParamsStatusExplodedListToJson(
    List<enums.GetRequestsParamsStatus>? getRequestsParamsStatus) {
  return getRequestsParamsStatus?.map((e) => e.value!).join(',') ?? '';
}

List<String> getRequestsParamsStatusListToJson(
    List<enums.GetRequestsParamsStatus>? getRequestsParamsStatus) {
  if (getRequestsParamsStatus == null) {
    return [];
  }

  return getRequestsParamsStatus.map((e) => e.value!).toList();
}

List<enums.GetRequestsParamsStatus> getRequestsParamsStatusListFromJson(
  List? getRequestsParamsStatus, [
  List<enums.GetRequestsParamsStatus>? defaultValue,
]) {
  if (getRequestsParamsStatus == null) {
    return defaultValue ?? [];
  }

  return getRequestsParamsStatus
      .map((e) => getRequestsParamsStatusFromJson(e.toString()))
      .toList();
}

List<enums.GetRequestsParamsStatus>?
    getRequestsParamsStatusNullableListFromJson(
  List? getRequestsParamsStatus, [
  List<enums.GetRequestsParamsStatus>? defaultValue,
]) {
  if (getRequestsParamsStatus == null) {
    return defaultValue;
  }

  return getRequestsParamsStatus
      .map((e) => getRequestsParamsStatusFromJson(e.toString()))
      .toList();
}

String? apiV1LendingFailedEventsGetSeverityNullableToJson(
    enums.ApiV1LendingFailedEventsGetSeverity?
        apiV1LendingFailedEventsGetSeverity) {
  return apiV1LendingFailedEventsGetSeverity?.value;
}

String? apiV1LendingFailedEventsGetSeverityToJson(
    enums.ApiV1LendingFailedEventsGetSeverity
        apiV1LendingFailedEventsGetSeverity) {
  return apiV1LendingFailedEventsGetSeverity.value;
}

enums.ApiV1LendingFailedEventsGetSeverity
    apiV1LendingFailedEventsGetSeverityFromJson(
  Object? apiV1LendingFailedEventsGetSeverity, [
  enums.ApiV1LendingFailedEventsGetSeverity? defaultValue,
]) {
  return enums.ApiV1LendingFailedEventsGetSeverity.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              apiV1LendingFailedEventsGetSeverity?.toString().toLowerCase()) ??
      defaultValue ??
      enums.ApiV1LendingFailedEventsGetSeverity.swaggerGeneratedUnknown;
}

enums.ApiV1LendingFailedEventsGetSeverity?
    apiV1LendingFailedEventsGetSeverityNullableFromJson(
  Object? apiV1LendingFailedEventsGetSeverity, [
  enums.ApiV1LendingFailedEventsGetSeverity? defaultValue,
]) {
  if (apiV1LendingFailedEventsGetSeverity == null) {
    return null;
  }
  return enums.ApiV1LendingFailedEventsGetSeverity.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              apiV1LendingFailedEventsGetSeverity.toString().toLowerCase()) ??
      defaultValue;
}

String apiV1LendingFailedEventsGetSeverityExplodedListToJson(
    List<enums.ApiV1LendingFailedEventsGetSeverity>?
        apiV1LendingFailedEventsGetSeverity) {
  return apiV1LendingFailedEventsGetSeverity?.map((e) => e.value!).join(',') ??
      '';
}

List<String> apiV1LendingFailedEventsGetSeverityListToJson(
    List<enums.ApiV1LendingFailedEventsGetSeverity>?
        apiV1LendingFailedEventsGetSeverity) {
  if (apiV1LendingFailedEventsGetSeverity == null) {
    return [];
  }

  return apiV1LendingFailedEventsGetSeverity.map((e) => e.value!).toList();
}

List<enums.ApiV1LendingFailedEventsGetSeverity>
    apiV1LendingFailedEventsGetSeverityListFromJson(
  List? apiV1LendingFailedEventsGetSeverity, [
  List<enums.ApiV1LendingFailedEventsGetSeverity>? defaultValue,
]) {
  if (apiV1LendingFailedEventsGetSeverity == null) {
    return defaultValue ?? [];
  }

  return apiV1LendingFailedEventsGetSeverity
      .map((e) => apiV1LendingFailedEventsGetSeverityFromJson(e.toString()))
      .toList();
}

List<enums.ApiV1LendingFailedEventsGetSeverity>?
    apiV1LendingFailedEventsGetSeverityNullableListFromJson(
  List? apiV1LendingFailedEventsGetSeverity, [
  List<enums.ApiV1LendingFailedEventsGetSeverity>? defaultValue,
]) {
  if (apiV1LendingFailedEventsGetSeverity == null) {
    return defaultValue;
  }

  return apiV1LendingFailedEventsGetSeverity
      .map((e) => apiV1LendingFailedEventsGetSeverityFromJson(e.toString()))
      .toList();
}

String?
    apiV1ApplicationResendEmailApplicationIdPostNotificationTypeNullableToJson(
        enums.ApiV1ApplicationResendEmailApplicationIdPostNotificationType?
            apiV1ApplicationResendEmailApplicationIdPostNotificationType) {
  return apiV1ApplicationResendEmailApplicationIdPostNotificationType?.value;
}

String? apiV1ApplicationResendEmailApplicationIdPostNotificationTypeToJson(
    enums.ApiV1ApplicationResendEmailApplicationIdPostNotificationType
        apiV1ApplicationResendEmailApplicationIdPostNotificationType) {
  return apiV1ApplicationResendEmailApplicationIdPostNotificationType.value;
}

enums.ApiV1ApplicationResendEmailApplicationIdPostNotificationType
    apiV1ApplicationResendEmailApplicationIdPostNotificationTypeFromJson(
  Object? apiV1ApplicationResendEmailApplicationIdPostNotificationType, [
  enums.ApiV1ApplicationResendEmailApplicationIdPostNotificationType?
      defaultValue,
]) {
  return enums
          .ApiV1ApplicationResendEmailApplicationIdPostNotificationType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              apiV1ApplicationResendEmailApplicationIdPostNotificationType
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.ApiV1ApplicationResendEmailApplicationIdPostNotificationType
          .swaggerGeneratedUnknown;
}

enums.ApiV1ApplicationResendEmailApplicationIdPostNotificationType?
    apiV1ApplicationResendEmailApplicationIdPostNotificationTypeNullableFromJson(
  Object? apiV1ApplicationResendEmailApplicationIdPostNotificationType, [
  enums.ApiV1ApplicationResendEmailApplicationIdPostNotificationType?
      defaultValue,
]) {
  if (apiV1ApplicationResendEmailApplicationIdPostNotificationType == null) {
    return null;
  }
  return enums
          .ApiV1ApplicationResendEmailApplicationIdPostNotificationType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              apiV1ApplicationResendEmailApplicationIdPostNotificationType
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String
    apiV1ApplicationResendEmailApplicationIdPostNotificationTypeExplodedListToJson(
        List<
                enums
                .ApiV1ApplicationResendEmailApplicationIdPostNotificationType>?
            apiV1ApplicationResendEmailApplicationIdPostNotificationType) {
  return apiV1ApplicationResendEmailApplicationIdPostNotificationType
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String>
    apiV1ApplicationResendEmailApplicationIdPostNotificationTypeListToJson(
        List<
                enums
                .ApiV1ApplicationResendEmailApplicationIdPostNotificationType>?
            apiV1ApplicationResendEmailApplicationIdPostNotificationType) {
  if (apiV1ApplicationResendEmailApplicationIdPostNotificationType == null) {
    return [];
  }

  return apiV1ApplicationResendEmailApplicationIdPostNotificationType
      .map((e) => e.value!)
      .toList();
}

List<enums.ApiV1ApplicationResendEmailApplicationIdPostNotificationType>
    apiV1ApplicationResendEmailApplicationIdPostNotificationTypeListFromJson(
  List? apiV1ApplicationResendEmailApplicationIdPostNotificationType, [
  List<enums.ApiV1ApplicationResendEmailApplicationIdPostNotificationType>?
      defaultValue,
]) {
  if (apiV1ApplicationResendEmailApplicationIdPostNotificationType == null) {
    return defaultValue ?? [];
  }

  return apiV1ApplicationResendEmailApplicationIdPostNotificationType
      .map((e) =>
          apiV1ApplicationResendEmailApplicationIdPostNotificationTypeFromJson(
              e.toString()))
      .toList();
}

List<enums.ApiV1ApplicationResendEmailApplicationIdPostNotificationType>?
    apiV1ApplicationResendEmailApplicationIdPostNotificationTypeNullableListFromJson(
  List? apiV1ApplicationResendEmailApplicationIdPostNotificationType, [
  List<enums.ApiV1ApplicationResendEmailApplicationIdPostNotificationType>?
      defaultValue,
]) {
  if (apiV1ApplicationResendEmailApplicationIdPostNotificationType == null) {
    return defaultValue;
  }

  return apiV1ApplicationResendEmailApplicationIdPostNotificationType
      .map((e) =>
          apiV1ApplicationResendEmailApplicationIdPostNotificationTypeFromJson(
              e.toString()))
      .toList();
}

String? apiV2StaticProductGetProductCodeNullableToJson(
    enums.ApiV2StaticProductGetProductCode? apiV2StaticProductGetProductCode) {
  return apiV2StaticProductGetProductCode?.value;
}

String? apiV2StaticProductGetProductCodeToJson(
    enums.ApiV2StaticProductGetProductCode apiV2StaticProductGetProductCode) {
  return apiV2StaticProductGetProductCode.value;
}

enums.ApiV2StaticProductGetProductCode apiV2StaticProductGetProductCodeFromJson(
  Object? apiV2StaticProductGetProductCode, [
  enums.ApiV2StaticProductGetProductCode? defaultValue,
]) {
  return enums.ApiV2StaticProductGetProductCode.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          apiV2StaticProductGetProductCode?.toString().toLowerCase()) ??
      defaultValue ??
      enums.ApiV2StaticProductGetProductCode.swaggerGeneratedUnknown;
}

enums.ApiV2StaticProductGetProductCode?
    apiV2StaticProductGetProductCodeNullableFromJson(
  Object? apiV2StaticProductGetProductCode, [
  enums.ApiV2StaticProductGetProductCode? defaultValue,
]) {
  if (apiV2StaticProductGetProductCode == null) {
    return null;
  }
  return enums.ApiV2StaticProductGetProductCode.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          apiV2StaticProductGetProductCode.toString().toLowerCase()) ??
      defaultValue;
}

String apiV2StaticProductGetProductCodeExplodedListToJson(
    List<enums.ApiV2StaticProductGetProductCode>?
        apiV2StaticProductGetProductCode) {
  return apiV2StaticProductGetProductCode?.map((e) => e.value!).join(',') ?? '';
}

List<String> apiV2StaticProductGetProductCodeListToJson(
    List<enums.ApiV2StaticProductGetProductCode>?
        apiV2StaticProductGetProductCode) {
  if (apiV2StaticProductGetProductCode == null) {
    return [];
  }

  return apiV2StaticProductGetProductCode.map((e) => e.value!).toList();
}

List<enums.ApiV2StaticProductGetProductCode>
    apiV2StaticProductGetProductCodeListFromJson(
  List? apiV2StaticProductGetProductCode, [
  List<enums.ApiV2StaticProductGetProductCode>? defaultValue,
]) {
  if (apiV2StaticProductGetProductCode == null) {
    return defaultValue ?? [];
  }

  return apiV2StaticProductGetProductCode
      .map((e) => apiV2StaticProductGetProductCodeFromJson(e.toString()))
      .toList();
}

List<enums.ApiV2StaticProductGetProductCode>?
    apiV2StaticProductGetProductCodeNullableListFromJson(
  List? apiV2StaticProductGetProductCode, [
  List<enums.ApiV2StaticProductGetProductCode>? defaultValue,
]) {
  if (apiV2StaticProductGetProductCode == null) {
    return defaultValue;
  }

  return apiV2StaticProductGetProductCode
      .map((e) => apiV2StaticProductGetProductCodeFromJson(e.toString()))
      .toList();
}

// ignore: unused_element
String? _dateToJson(DateTime? date) {
  if (date == null) {
    return null;
  }

  final year = date.year.toString();
  final month = date.month < 10 ? '0${date.month}' : date.month.toString();
  final day = date.day < 10 ? '0${date.day}' : date.day.toString();

  return '$year-$month-$day';
}

class Wrapped<T> {
  final T value;
  const Wrapped.value(this.value);
}
