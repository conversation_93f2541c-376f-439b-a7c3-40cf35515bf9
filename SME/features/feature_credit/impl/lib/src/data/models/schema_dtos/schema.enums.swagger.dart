import 'package:json_annotation/json_annotation.dart';
import 'package:collection/collection.dart';

enum FailedEventsDocumentType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('REST')
  rest('REST'),
  @<PERSON>sonValue('KAFKA')
  kafka('KAFKA'),
  @JsonValue('CRON')
  cron('CRON');

  final String? value;

  const FailedEventsDocumentType(this.value);
}

enum FailedEventsDocumentSeverity {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('MINOR')
  minor('MINOR'),
  @JsonValue('MAJOR')
  major('MAJOR'),
  @JsonValue('CRITICAL')
  critical('CRITICAL');

  final String? value;

  const FailedEventsDocumentSeverity(this.value);
}

enum AddressAddressType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('OPERATING_ADDRESS')
  operatingAddress('OPERATING_ADDRESS'),
  @JsonValue('REGISTERED_ADDRESS')
  registeredAddress('REGISTERED_ADDRESS'),
  @JsonValue('ALTERNATE_ADDRESS')
  alternateAddress('ALTERNATE_ADDRESS');

  final String? value;

  const AddressAddressType(this.value);
}

enum ApplicationInputDataRequestVatReportingMethod {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('MONTHLY')
  monthly('MONTHLY'),
  @JsonValue('QUARTERLY')
  quarterly('QUARTERLY'),
  @JsonValue('ANNUAL')
  annual('ANNUAL'),
  @JsonValue('NO_REPORTING')
  noReporting('NO_REPORTING');

  final String? value;

  const ApplicationInputDataRequestVatReportingMethod(this.value);
}

enum ApplicationInputDataRequestNonRegisteredVatReasonType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('BUSINESS_TAX_LESS_THAN_THRESHOLD_LIMIT')
  businessTaxLessThanThresholdLimit('BUSINESS_TAX_LESS_THAN_THRESHOLD_LIMIT'),
  @JsonValue('BUSINESS_TAX_NOT_IN_UAE')
  businessTaxNotInUae('BUSINESS_TAX_NOT_IN_UAE'),
  @JsonValue('BUSINESS_HIGH_SEA_SALES')
  businessHighSeaSales('BUSINESS_HIGH_SEA_SALES'),
  @JsonValue('BUSINESS_VAT_EXCEPTION_FROM_FTA')
  businessVatExceptionFromFta('BUSINESS_VAT_EXCEPTION_FROM_FTA'),
  @JsonValue('BUSINESS_EXEMPTED_ACTIVITIES')
  businessExemptedActivities('BUSINESS_EXEMPTED_ACTIVITIES'),
  @JsonValue('OTHER')
  other('OTHER');

  final String? value;

  const ApplicationInputDataRequestNonRegisteredVatReasonType(this.value);
}

enum ApplicationStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('IN_PROGRESS')
  inProgress('IN_PROGRESS'),
  @JsonValue('IN_REVIEW')
  inReview('IN_REVIEW'),
  @JsonValue('COMPLETED')
  completed('COMPLETED'),
  @JsonValue('REJECTED')
  rejected('REJECTED'),
  @JsonValue('CANCELLED')
  cancelled('CANCELLED'),
  @JsonValue('EXPIRED')
  expired('EXPIRED');

  final String? value;

  const ApplicationStatus(this.value);
}

enum ApplicationUiStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('ANNUAL_TURNOVER_INPUT')
  annualTurnoverInput('ANNUAL_TURNOVER_INPUT'),
  @JsonValue('VAT_REPORTING_METHOD_INPUT')
  vatReportingMethodInput('VAT_REPORTING_METHOD_INPUT'),
  @JsonValue('VAT_STATEMENTS_UPLOAD')
  vatStatementsUpload('VAT_STATEMENTS_UPLOAD'),
  @JsonValue('VAT_NON_REGISTRATION_REASON_INPUT')
  vatNonRegistrationReasonInput('VAT_NON_REGISTRATION_REASON_INPUT'),
  @JsonValue('BANK_ACCOUNTS_INPUT')
  bankAccountsInput('BANK_ACCOUNTS_INPUT'),
  @JsonValue('RECAP_SCREEN')
  recapScreen('RECAP_SCREEN'),
  @JsonValue('CREDIT_DECISION_IN_REVIEW')
  creditDecisionInReview('CREDIT_DECISION_IN_REVIEW'),
  @JsonValue('MONTHLY_REPAYMENT_PERCENTAGE_INPUT')
  monthlyRepaymentPercentageInput('MONTHLY_REPAYMENT_PERCENTAGE_INPUT'),
  @JsonValue('MONTHLY_REPAYMENT_DAY_INPUT')
  monthlyRepaymentDayInput('MONTHLY_REPAYMENT_DAY_INPUT'),
  @JsonValue('BORROW_AGREEMENT_SIGNING')
  borrowAgreementSigning('BORROW_AGREEMENT_SIGNING'),
  @JsonValue('APPLICATION_COMPLETED')
  applicationCompleted('APPLICATION_COMPLETED'),
  @JsonValue('SELECTED_AMOUNT_INPUT')
  selectedAmountInput('SELECTED_AMOUNT_INPUT'),
  @JsonValue('AUDITED_FINANCIAL_STATEMENT_UPLOAD')
  auditedFinancialStatementUpload('AUDITED_FINANCIAL_STATEMENT_UPLOAD'),
  @JsonValue('CONFIRM_AUTODEBIT')
  confirmAutodebit('CONFIRM_AUTODEBIT'),
  @JsonValue('LOAN_TERM_AMOUNT_SELECTION')
  loanTermAmountSelection('LOAN_TERM_AMOUNT_SELECTION'),
  @JsonValue('REPAYMENT_PLAN')
  repaymentPlan('REPAYMENT_PLAN'),
  @JsonValue('REFERRAL_CODE_INPUT')
  referralCodeInput('REFERRAL_CODE_INPUT'),
  @JsonValue('SECURED_LOAN_INPUT')
  securedLoanInput('SECURED_LOAN_INPUT'),
  @JsonValue('REQUESTED_AMOUNT_INPUT')
  requestedAmountInput('REQUESTED_AMOUNT_INPUT'),
  @JsonValue('EMPLOYEE_COUNT_RANGE_INPUT')
  employeeCountRangeInput('EMPLOYEE_COUNT_RANGE_INPUT'),
  @JsonValue('SERVICE_CHANNELS_INPUT')
  serviceChannelsInput('SERVICE_CHANNELS_INPUT'),
  @JsonValue('BUSINESS_MODEL_DESCRIPTION_INPUT')
  businessModelDescriptionInput('BUSINESS_MODEL_DESCRIPTION_INPUT'),
  @JsonValue('FINANCIAL_ACTIVITY_UPLOAD')
  financialActivityUpload('FINANCIAL_ACTIVITY_UPLOAD'),
  @JsonValue('TRADING_ADDRESS_INPUT')
  tradingAddressInput('TRADING_ADDRESS_INPUT'),
  @JsonValue('PROOF_OF_ADDRESS_UPLOAD')
  proofOfAddressUpload('PROOF_OF_ADDRESS_UPLOAD');

  final String? value;

  const ApplicationUiStatus(this.value);
}

enum CreditDecisionResultCreditDecisionStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('NOT_STARTED')
  notStarted('NOT_STARTED'),
  @JsonValue('IN_PROGRESS')
  inProgress('IN_PROGRESS'),
  @JsonValue('REJECTED')
  rejected('REJECTED'),
  @JsonValue('APPROVED')
  approved('APPROVED');

  final String? value;

  const CreditDecisionResultCreditDecisionStatus(this.value);
}

enum CreditDecisionResultV2CreditDecisionStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('NOT_STARTED')
  notStarted('NOT_STARTED'),
  @JsonValue('IN_PROGRESS')
  inProgress('IN_PROGRESS'),
  @JsonValue('REJECTED')
  rejected('REJECTED'),
  @JsonValue('APPROVED')
  approved('APPROVED');

  final String? value;

  const CreditDecisionResultV2CreditDecisionStatus(this.value);
}

enum CreditDecisionResultV2CreditAcceptanceStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('NOT_STARTED')
  notStarted('NOT_STARTED'),
  @JsonValue('IN_PROGRESS')
  inProgress('IN_PROGRESS'),
  @JsonValue('COMPLETED')
  completed('COMPLETED');

  final String? value;

  const CreditDecisionResultV2CreditAcceptanceStatus(this.value);
}

enum CreditDecisionResultV2VerificationStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('REQUIRED')
  required('REQUIRED'),
  @JsonValue('NOT_REQUIRED')
  notRequired('NOT_REQUIRED'),
  @JsonValue('APPROVED')
  approved('APPROVED'),
  @JsonValue('REJECTED')
  rejected('REJECTED');

  final String? value;

  const CreditDecisionResultV2VerificationStatus(this.value);
}

enum CreditDecisionResultsCreditDecisionStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('NOT_STARTED')
  notStarted('NOT_STARTED'),
  @JsonValue('IN_PROGRESS')
  inProgress('IN_PROGRESS'),
  @JsonValue('REJECTED')
  rejected('REJECTED'),
  @JsonValue('APPROVED')
  approved('APPROVED');

  final String? value;

  const CreditDecisionResultsCreditDecisionStatus(this.value);
}

enum Currency {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('AED')
  aed('AED'),
  @JsonValue('USD')
  usd('USD'),
  @JsonValue('EUR')
  eur('EUR');

  final String? value;

  const Currency(this.value);
}

enum DocumentType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('VAT_STATEMENT')
  vatStatement('VAT_STATEMENT'),
  @JsonValue('KEY_FACT_STATEMENT')
  keyFactStatement('KEY_FACT_STATEMENT'),
  @JsonValue('CREDIT_AGREEMENT')
  creditAgreement('CREDIT_AGREEMENT'),
  @JsonValue('TERMS_AND_CONDITIONS')
  termsAndConditions('TERMS_AND_CONDITIONS'),
  @JsonValue('BANK_STATEMENT')
  bankStatement('BANK_STATEMENT'),
  @JsonValue('AUDITED_FINANCIAL_STATEMENT')
  auditedFinancialStatement('AUDITED_FINANCIAL_STATEMENT'),
  @JsonValue('BORROWING_POWER_PROOF')
  borrowingPowerProof('BORROWING_POWER_PROOF'),
  @JsonValue('LENDING_KEY_FACT_STATEMENT_POS_SME')
  lendingKeyFactStatementPosSme('LENDING_KEY_FACT_STATEMENT_POS_SME'),
  @JsonValue('LENDING_TERMS_AND_CONDITIONS_POS_SME')
  lendingTermsAndConditionsPosSme('LENDING_TERMS_AND_CONDITIONS_POS_SME'),
  @JsonValue('LENDING_KEY_FACT_STATEMENT_BL')
  lendingKeyFactStatementBl('LENDING_KEY_FACT_STATEMENT_BL'),
  @JsonValue('LENDING_TERMS_AND_CONDITIONS_BL')
  lendingTermsAndConditionsBl('LENDING_TERMS_AND_CONDITIONS_BL');

  final String? value;

  const DocumentType(this.value);
}

enum FixedDepositInfoLoanProductCode {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('PL')
  pl('PL'),
  @JsonValue('PL_CP')
  plCp('PL_CP'),
  @JsonValue('PL_AUTO')
  plAuto('PL_AUTO'),
  @JsonValue('PL_PORTFOLIO')
  plPortfolio('PL_PORTFOLIO'),
  @JsonValue('EC')
  ec('EC'),
  @JsonValue('CC')
  cc('CC'),
  @JsonValue('SME_CC_FD')
  smeCcFd('SME_CC_FD'),
  @JsonValue('BL')
  bl('BL'),
  @JsonValue('SME_EC')
  smeEc('SME_EC'),
  @JsonValue('SME_CC')
  smeCc('SME_CC'),
  @JsonValue('POS')
  pos('POS'),
  @JsonValue('IPO')
  ipo('IPO'),
  @JsonValue('SCF')
  scf('SCF'),
  @JsonValue('CF')
  cf('CF'),
  @JsonValue('MT')
  mt('MT');

  final String? value;

  const FixedDepositInfoLoanProductCode(this.value);
}

enum LoanSecurityProductDetailsProductCode {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('PL')
  pl('PL'),
  @JsonValue('PL_CP')
  plCp('PL_CP'),
  @JsonValue('PL_AUTO')
  plAuto('PL_AUTO'),
  @JsonValue('PL_PORTFOLIO')
  plPortfolio('PL_PORTFOLIO'),
  @JsonValue('EC')
  ec('EC'),
  @JsonValue('CC')
  cc('CC'),
  @JsonValue('SME_CC_FD')
  smeCcFd('SME_CC_FD'),
  @JsonValue('BL')
  bl('BL'),
  @JsonValue('SME_EC')
  smeEc('SME_EC'),
  @JsonValue('SME_CC')
  smeCc('SME_CC'),
  @JsonValue('POS')
  pos('POS'),
  @JsonValue('IPO')
  ipo('IPO'),
  @JsonValue('SCF')
  scf('SCF'),
  @JsonValue('CF')
  cf('CF'),
  @JsonValue('MT')
  mt('MT');

  final String? value;

  const LoanSecurityProductDetailsProductCode(this.value);
}

enum ProductApplicationAccountCreationStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('NOT_STARTED')
  notStarted('NOT_STARTED'),
  @JsonValue('IN_PROGRESS')
  inProgress('IN_PROGRESS'),
  @JsonValue('COMPLETED')
  completed('COMPLETED');

  final String? value;

  const ProductApplicationAccountCreationStatus(this.value);
}

enum ProductType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('SME_RECEIVABLE_FINANCE')
  smeReceivableFinance('SME_RECEIVABLE_FINANCE'),
  @JsonValue('SME_CREDIT_CARD')
  smeCreditCard('SME_CREDIT_CARD'),
  @JsonValue('SME_EASY_CASH')
  smeEasyCash('SME_EASY_CASH'),
  @JsonValue('SCF')
  scf('SCF'),
  @JsonValue('CHANNEL_FINANCE')
  channelFinance('CHANNEL_FINANCE'),
  @JsonValue('CREDIT')
  credit('CREDIT'),
  @JsonValue('IPO')
  ipo('IPO'),
  @JsonValue('EASY_CASH')
  easyCash('EASY_CASH'),
  @JsonValue('BUSINESS_LOAN')
  businessLoan('BUSINESS_LOAN');

  final String? value;

  const ProductType(this.value);
}

enum ApplicationInputDataRequestDTOVatReportingMethod {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('MONTHLY')
  monthly('MONTHLY'),
  @JsonValue('QUARTERLY')
  quarterly('QUARTERLY'),
  @JsonValue('ANNUAL')
  annual('ANNUAL'),
  @JsonValue('NO_REPORTING')
  noReporting('NO_REPORTING');

  final String? value;

  const ApplicationInputDataRequestDTOVatReportingMethod(this.value);
}

enum ApplicationInputDataRequestDTONonRegisteredVatReasonType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('BUSINESS_TAX_LESS_THAN_THRESHOLD_LIMIT')
  businessTaxLessThanThresholdLimit('BUSINESS_TAX_LESS_THAN_THRESHOLD_LIMIT'),
  @JsonValue('BUSINESS_TAX_NOT_IN_UAE')
  businessTaxNotInUae('BUSINESS_TAX_NOT_IN_UAE'),
  @JsonValue('BUSINESS_HIGH_SEA_SALES')
  businessHighSeaSales('BUSINESS_HIGH_SEA_SALES'),
  @JsonValue('BUSINESS_VAT_EXCEPTION_FROM_FTA')
  businessVatExceptionFromFta('BUSINESS_VAT_EXCEPTION_FROM_FTA'),
  @JsonValue('BUSINESS_EXEMPTED_ACTIVITIES')
  businessExemptedActivities('BUSINESS_EXEMPTED_ACTIVITIES'),
  @JsonValue('OTHER')
  other('OTHER');

  final String? value;

  const ApplicationInputDataRequestDTONonRegisteredVatReasonType(this.value);
}

enum LoanInstallmentEvaluateCommandLoanProductCode {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('PL')
  pl('PL'),
  @JsonValue('PL_CP')
  plCp('PL_CP'),
  @JsonValue('PL_AUTO')
  plAuto('PL_AUTO'),
  @JsonValue('PL_PORTFOLIO')
  plPortfolio('PL_PORTFOLIO'),
  @JsonValue('EC')
  ec('EC'),
  @JsonValue('CC')
  cc('CC'),
  @JsonValue('SME_CC_FD')
  smeCcFd('SME_CC_FD'),
  @JsonValue('BL')
  bl('BL'),
  @JsonValue('SME_EC')
  smeEc('SME_EC'),
  @JsonValue('SME_CC')
  smeCc('SME_CC'),
  @JsonValue('POS')
  pos('POS'),
  @JsonValue('IPO')
  ipo('IPO'),
  @JsonValue('SCF')
  scf('SCF'),
  @JsonValue('CF')
  cf('CF'),
  @JsonValue('MT')
  mt('MT');

  final String? value;

  const LoanInstallmentEvaluateCommandLoanProductCode(this.value);
}

enum LoanInstallmentEvaluateCommandEvaluationTransactionType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DISBURSEMENT')
  disbursement('DISBURSEMENT'),
  @JsonValue('REPAYMENT')
  repayment('REPAYMENT');

  final String? value;

  const LoanInstallmentEvaluateCommandEvaluationTransactionType(this.value);
}

enum LoanInstallmentEvaluateCommandPreviewScheduleStrategy {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('INCLUDE_PREVIOUS_WITHDRAWALS')
  includePreviousWithdrawals('INCLUDE_PREVIOUS_WITHDRAWALS'),
  @JsonValue('CURRENT_AMOUNT')
  currentAmount('CURRENT_AMOUNT');

  final String? value;

  const LoanInstallmentEvaluateCommandPreviewScheduleStrategy(this.value);
}

enum NextInstallmentInterval {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DAILY')
  daily('DAILY'),
  @JsonValue('WEEKLY')
  weekly('WEEKLY'),
  @JsonValue('MONTHLY')
  monthly('MONTHLY'),
  @JsonValue('YEARLY')
  yearly('YEARLY');

  final String? value;

  const NextInstallmentInterval(this.value);
}

enum LoanInstallmentDisbursementV2ProductSubType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('CARD_PURCHASE')
  cardPurchase('CARD_PURCHASE');

  final String? value;

  const LoanInstallmentDisbursementV2ProductSubType(this.value);
}

enum EligibleCreditLimitRequestDtoProductCode {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('PL')
  pl('PL'),
  @JsonValue('PL_CP')
  plCp('PL_CP'),
  @JsonValue('PL_AUTO')
  plAuto('PL_AUTO'),
  @JsonValue('PL_PORTFOLIO')
  plPortfolio('PL_PORTFOLIO'),
  @JsonValue('EC')
  ec('EC'),
  @JsonValue('CC')
  cc('CC'),
  @JsonValue('SME_CC_FD')
  smeCcFd('SME_CC_FD'),
  @JsonValue('BL')
  bl('BL'),
  @JsonValue('SME_EC')
  smeEc('SME_EC'),
  @JsonValue('SME_CC')
  smeCc('SME_CC'),
  @JsonValue('POS')
  pos('POS'),
  @JsonValue('IPO')
  ipo('IPO'),
  @JsonValue('SCF')
  scf('SCF'),
  @JsonValue('CF')
  cf('CF'),
  @JsonValue('MT')
  mt('MT');

  final String? value;

  const EligibleCreditLimitRequestDtoProductCode(this.value);
}

enum MultiUserCreateRequestDTODomainType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('SCF_SUPPLIER_INVITE')
  scfSupplierInvite('SCF_SUPPLIER_INVITE'),
  @JsonValue('SCF_INVOICE_UPLOAD')
  scfInvoiceUpload('SCF_INVOICE_UPLOAD'),
  @JsonValue('SCF_INVOICE_FINANCING_ACCEPTANCE')
  scfInvoiceFinancingAcceptance('SCF_INVOICE_FINANCING_ACCEPTANCE'),
  @JsonValue('SCF_INVOICE_REPAYMENT_INITIATE')
  scfInvoiceRepaymentInitiate('SCF_INVOICE_REPAYMENT_INITIATE'),
  @JsonValue('SCF_INVOICE_REPAYMENT_DEFERRED')
  scfInvoiceRepaymentDeferred('SCF_INVOICE_REPAYMENT_DEFERRED');

  final String? value;

  const MultiUserCreateRequestDTODomainType(this.value);
}

enum MultiUserResponseDTODomainType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('SCF_SUPPLIER_INVITE')
  scfSupplierInvite('SCF_SUPPLIER_INVITE'),
  @JsonValue('SCF_INVOICE_UPLOAD')
  scfInvoiceUpload('SCF_INVOICE_UPLOAD'),
  @JsonValue('SCF_INVOICE_REPAYMENT_INITIATE')
  scfInvoiceRepaymentInitiate('SCF_INVOICE_REPAYMENT_INITIATE'),
  @JsonValue('SCF_INVOICE_FINANCING_ACCEPTANCE')
  scfInvoiceFinancingAcceptance('SCF_INVOICE_FINANCING_ACCEPTANCE'),
  @JsonValue('SCF_INVOICE_REPAYMENT_DEFERRED')
  scfInvoiceRepaymentDeferred('SCF_INVOICE_REPAYMENT_DEFERRED'),
  @JsonValue('CREDIT_CONTRACT')
  creditContract('CREDIT_CONTRACT');

  final String? value;

  const MultiUserResponseDTODomainType(this.value);
}

enum MultiUserResponseDTOStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DRAFT')
  draft('DRAFT'),
  @JsonValue('ACTIVE')
  active('ACTIVE'),
  @JsonValue('REJECTED')
  rejected('REJECTED'),
  @JsonValue('APPROVED')
  approved('APPROVED'),
  @JsonValue('ACTION_REQUIRED')
  actionRequired('ACTION_REQUIRED'),
  @JsonValue('COMPLETED')
  completed('COMPLETED'),
  @JsonValue('FAILED')
  failed('FAILED'),
  @JsonValue('IN_REVIEW')
  inReview('IN_REVIEW'),
  @JsonValue('PENDING')
  pending('PENDING');

  final String? value;

  const MultiUserResponseDTOStatus(this.value);
}

enum TransactionFilterRequestDtoTransactionType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DISBURSEMENT')
  disbursement('DISBURSEMENT'),
  @JsonValue('REPAYMENT')
  repayment('REPAYMENT'),
  @JsonValue('FEE_APPLIED')
  feeApplied('FEE_APPLIED'),
  @JsonValue('REVOLVING_FEE')
  revolvingFee('REVOLVING_FEE'),
  @JsonValue('LATE_FEE')
  lateFee('LATE_FEE'),
  @JsonValue('INTEREST')
  interest('INTEREST'),
  @JsonValue('REVERSAL')
  reversal('REVERSAL');

  final String? value;

  const TransactionFilterRequestDtoTransactionType(this.value);
}

enum TransactionResponseTransactionType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('EASY_CASH')
  easyCash('EASY_CASH'),
  @JsonValue('CREDIT_CARD')
  creditCard('CREDIT_CARD'),
  @JsonValue('SME_RECEIVABLE_FINANCE')
  smeReceivableFinance('SME_RECEIVABLE_FINANCE'),
  @JsonValue('BUSINESS_LOAN')
  businessLoan('BUSINESS_LOAN'),
  @JsonValue('UNKNOWN')
  unknown('UNKNOWN');

  final String? value;

  const TransactionResponseTransactionType(this.value);
}

enum TransactionResponseTransactionSubType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('LENDING_DISBURSEMENT')
  lendingDisbursement('LENDING_DISBURSEMENT'),
  @JsonValue('LENDING_REPAYMENT')
  lendingRepayment('LENDING_REPAYMENT'),
  @JsonValue('LENDING_REVOLVING_FEE')
  lendingRevolvingFee('LENDING_REVOLVING_FEE'),
  @JsonValue('LENDING_LATE_FEE')
  lendingLateFee('LENDING_LATE_FEE'),
  @JsonValue('LENDING_INTEREST')
  lendingInterest('LENDING_INTEREST'),
  @JsonValue('LENDING_FEE_APPLIED')
  lendingFeeApplied('LENDING_FEE_APPLIED');

  final String? value;

  const TransactionResponseTransactionSubType(this.value);
}

enum TransactionResponseTransactionStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('FAILED')
  failed('FAILED'),
  @JsonValue('COMPLETED')
  completed('COMPLETED'),
  @JsonValue('PENDING')
  pending('PENDING');

  final String? value;

  const TransactionResponseTransactionStatus(this.value);
}

enum TransactionResponseTransactionMode {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DEPOSIT')
  deposit('DEPOSIT'),
  @JsonValue('WITHDRAWAL')
  withdrawal('WITHDRAWAL');

  final String? value;

  const TransactionResponseTransactionMode(this.value);
}

enum StatusFilter {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('ALL')
  all('ALL'),
  @JsonValue('PENDING')
  pending('PENDING'),
  @JsonValue('COMPLETED')
  completed('COMPLETED');

  final String? value;

  const StatusFilter(this.value);
}

enum OptInStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('PENDING')
  pending('PENDING'),
  @JsonValue('COMPLETED')
  completed('COMPLETED'),
  @JsonValue('REJECTED')
  rejected('REJECTED'),
  @JsonValue('CANCELLED')
  cancelled('CANCELLED');

  final String? value;

  const OptInStatus(this.value);
}

enum SalesChannelResponseCode {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('WEBSITE')
  website('WEBSITE'),
  @JsonValue('SOCIAL_MEDIA')
  socialMedia('SOCIAL_MEDIA'),
  @JsonValue('AMAZON_NOON')
  amazonNoon('AMAZON_NOON'),
  @JsonValue('PHYSICAL_STORE')
  physicalStore('PHYSICAL_STORE'),
  @JsonValue('OTHER')
  other('OTHER');

  final String? value;

  const SalesChannelResponseCode(this.value);
}

enum InstallmentIntervalInterval {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DAILY')
  daily('DAILY'),
  @JsonValue('WEEKLY')
  weekly('WEEKLY'),
  @JsonValue('MONTHLY')
  monthly('MONTHLY'),
  @JsonValue('YEARLY')
  yearly('YEARLY');

  final String? value;

  const InstallmentIntervalInterval(this.value);
}

enum NonRegisteredVatReasonType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('BUSINESS_TAX_LESS_THAN_THRESHOLD_LIMIT')
  businessTaxLessThanThresholdLimit('BUSINESS_TAX_LESS_THAN_THRESHOLD_LIMIT'),
  @JsonValue('BUSINESS_TAX_NOT_IN_UAE')
  businessTaxNotInUae('BUSINESS_TAX_NOT_IN_UAE'),
  @JsonValue('BUSINESS_HIGH_SEA_SALES')
  businessHighSeaSales('BUSINESS_HIGH_SEA_SALES'),
  @JsonValue('BUSINESS_VAT_EXCEPTION_FROM_FTA')
  businessVatExceptionFromFta('BUSINESS_VAT_EXCEPTION_FROM_FTA'),
  @JsonValue('BUSINESS_EXEMPTED_ACTIVITIES')
  businessExemptedActivities('BUSINESS_EXEMPTED_ACTIVITIES'),
  @JsonValue('OTHER')
  other('OTHER');

  final String? value;

  const NonRegisteredVatReasonType(this.value);
}

enum EmployeeCountResponseCode {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('LESS_THAN_3')
  lessThan3('LESS_THAN_3'),
  @JsonValue('FROM_3_TO_10')
  from3To10('FROM_3_TO_10'),
  @JsonValue('FROM_10_TO_25')
  from10To25('FROM_10_TO_25'),
  @JsonValue('FROM_25_TO_50')
  from25To50('FROM_25_TO_50'),
  @JsonValue('MORE_THAN_50')
  moreThan50('MORE_THAN_50');

  final String? value;

  const EmployeeCountResponseCode(this.value);
}

enum NonEligibilityDetailsReason {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DOCUMENT_EXPIRED')
  documentExpired('DOCUMENT_EXPIRED'),
  @JsonValue('DOCUMENT_EXPIRING_SOON')
  documentExpiringSoon('DOCUMENT_EXPIRING_SOON'),
  @JsonValue('LOB_BELOW_THRESHOLD')
  lobBelowThreshold('LOB_BELOW_THRESHOLD'),
  @JsonValue('OTHER')
  other('OTHER');

  final String? value;

  const NonEligibilityDetailsReason(this.value);
}

enum MultiUserApprovalsInfoDtoStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DRAFT')
  draft('DRAFT'),
  @JsonValue('ACTIVE')
  active('ACTIVE'),
  @JsonValue('REJECTED')
  rejected('REJECTED'),
  @JsonValue('APPROVED')
  approved('APPROVED'),
  @JsonValue('ACTION_REQUIRED')
  actionRequired('ACTION_REQUIRED'),
  @JsonValue('COMPLETED')
  completed('COMPLETED'),
  @JsonValue('FAILED')
  failed('FAILED'),
  @JsonValue('IN_REVIEW')
  inReview('IN_REVIEW'),
  @JsonValue('PENDING')
  pending('PENDING');

  final String? value;

  const MultiUserApprovalsInfoDtoStatus(this.value);
}

enum GetRequestsParamsDomainTypes {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('SCF_SUPPLIER_INVITE')
  scfSupplierInvite('SCF_SUPPLIER_INVITE'),
  @JsonValue('SCF_INVOICE_UPLOAD')
  scfInvoiceUpload('SCF_INVOICE_UPLOAD'),
  @JsonValue('SCF_INVOICE_FINANCING_ACCEPTANCE')
  scfInvoiceFinancingAcceptance('SCF_INVOICE_FINANCING_ACCEPTANCE'),
  @JsonValue('SCF_INVOICE_REPAYMENT_INITIATE')
  scfInvoiceRepaymentInitiate('SCF_INVOICE_REPAYMENT_INITIATE'),
  @JsonValue('SCF_INVOICE_REPAYMENT_DEFERRED')
  scfInvoiceRepaymentDeferred('SCF_INVOICE_REPAYMENT_DEFERRED');

  final String? value;

  const GetRequestsParamsDomainTypes(this.value);
}

enum GetRequestsParamsStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DRAFT')
  draft('DRAFT'),
  @JsonValue('ACTIVE')
  active('ACTIVE'),
  @JsonValue('REJECTED')
  rejected('REJECTED'),
  @JsonValue('APPROVED')
  approved('APPROVED'),
  @JsonValue('ACTION_REQUIRED')
  actionRequired('ACTION_REQUIRED'),
  @JsonValue('COMPLETED')
  completed('COMPLETED'),
  @JsonValue('FAILED')
  failed('FAILED'),
  @JsonValue('IN_REVIEW')
  inReview('IN_REVIEW'),
  @JsonValue('PENDING')
  pending('PENDING');

  final String? value;

  const GetRequestsParamsStatus(this.value);
}

enum ApiV1LendingFailedEventsGetSeverity {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('MINOR')
  minor('MINOR'),
  @JsonValue('MAJOR')
  major('MAJOR'),
  @JsonValue('CRITICAL')
  critical('CRITICAL');

  final String? value;

  const ApiV1LendingFailedEventsGetSeverity(this.value);
}

enum ApiV1ApplicationResendEmailApplicationIdPostNotificationType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('HYPOTHECATION_VERIFY')
  hypothecationVerify('HYPOTHECATION_VERIFY');

  final String? value;

  const ApiV1ApplicationResendEmailApplicationIdPostNotificationType(
      this.value);
}

enum ApiV2StaticProductGetProductCode {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('PL')
  pl('PL'),
  @JsonValue('PL_CP')
  plCp('PL_CP'),
  @JsonValue('PL_AUTO')
  plAuto('PL_AUTO'),
  @JsonValue('PL_PORTFOLIO')
  plPortfolio('PL_PORTFOLIO'),
  @JsonValue('EC')
  ec('EC'),
  @JsonValue('CC')
  cc('CC'),
  @JsonValue('SME_CC_FD')
  smeCcFd('SME_CC_FD'),
  @JsonValue('BL')
  bl('BL'),
  @JsonValue('SME_EC')
  smeEc('SME_EC'),
  @JsonValue('SME_CC')
  smeCc('SME_CC'),
  @JsonValue('POS')
  pos('POS'),
  @JsonValue('IPO')
  ipo('IPO'),
  @JsonValue('SCF')
  scf('SCF'),
  @JsonValue('CF')
  cf('CF'),
  @JsonValue('MT')
  mt('MT');

  final String? value;

  const ApiV2StaticProductGetProductCode(this.value);
}
