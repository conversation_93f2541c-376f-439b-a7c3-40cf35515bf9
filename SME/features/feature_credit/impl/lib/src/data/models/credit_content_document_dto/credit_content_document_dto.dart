import 'package:freezed_annotation/freezed_annotation.dart';

part 'credit_content_document_dto.freezed.dart';
part 'credit_content_document_dto.g.dart';

enum CreditContentDocumentDtoType {
  @JsonValue('LENDING_TERMS_AND_CONDITIONS_SME')
  termsAndConditions,
  @JsonValue('LENDING_KEY_FACT_STATEMENT_SME')
  keyFactsStatement,
  @JsonValue('LENDING_TERMS_AND_CONDITIONS_POS_SME')
  posTermsAndConditions,
  @JsonValue('LENDING_KEY_FACT_STATEMENT_POS_SME')
  posFactsStatement,
  @JsonValue('CF_TERMS_AND_CONDITIONS')
  channelFinanceAgreement,
  @JsonValue('LENDING_KEY_FACT_STATEMENT_BL')
  businessLoanKeyFactsStatement,
  @JsonValue('LENDING_TERMS_AND_CONDITIONS_BL')
  businessLoanTermsAndConditions,
}

@freezed
class CreditContentDocumentDto with _$CreditContentDocumentDto {
  factory CreditContentDocumentDto({
    required CreditContentDocumentDtoType type,
    required UrlsDto urls,
  }) = _CreditContentDocumentDto;

  factory CreditContentDocumentDto.fromJson(Map<String, dynamic> json) =>
      _$CreditContentDocumentDtoFromJson(json);
}

@freezed
class UrlsDto with _$UrlsDto {
  factory UrlsDto({
    required String en,
    required String ar,
  }) = _UrlsDto;

  factory UrlsDto.fromJson(Map<String, dynamic> json) =>
      _$UrlsDtoFromJson(json);
}
