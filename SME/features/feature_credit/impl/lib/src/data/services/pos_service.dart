import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_credit_impl/src/data/models/schema_dtos/schema.swagger.dart';

typedef _Json = Map<String, dynamic>;

abstract class _Endpoints {
  static String get _prefix => '/lending/sme/api/v1';

  static String loanAccount({String? id}) {
    if (id == null) {
      return '$_prefix/loan-account';
    }

    return '$_prefix/loan-account/$id';
  }

  static String evaluateInstallments(String accountId) =>
      '${loanAccount()}/evaluation/$accountId/installments';

  static String disbursement(String accountId) =>
      '${loanAccount(id: accountId)}/disbursement';
}

abstract class PosService {
  Future<Schedule> evaluateInstallments({
    required String accountId,
    required LoanInstallmentEvaluateCommand body,
  });

  Future<void> makeDisbursement({
    required String accountId,
    required LoanInstallmentDisbursement body,
    required String idempotencyKey,
  });
}

class PosServiceImpl extends RestApiService implements PosService {
  final IRestApiClient _restApiClient;
  static const idempotencyKeyHeader = 'Idempotency-Key';

  PosServiceImpl({
    required IRestApiClient restApiClient,
  }) : _restApiClient = restApiClient;

  @override
  Future<Schedule> evaluateInstallments({
    required String accountId,
    required LoanInstallmentEvaluateCommand body,
  }) {
    return execute<Schedule, HttpRequestException>(
      _restApiClient.execute<_Json>(
        RestApiRequest(
          _Endpoints.evaluateInstallments(accountId),
          method: HttpRequestMethod.post,
          body: body.toJson(),
        ),
      ),
      (json) => Schedule.fromJson(json as _Json),
    );
  }

  @override
  Future<void> makeDisbursement({
    required String accountId,
    required LoanInstallmentDisbursement body,
    required String idempotencyKey,
  }) {
    return execute<void, HttpRequestException>(
      _restApiClient.execute<_Json>(
        RestApiRequest(
          _Endpoints.disbursement(accountId),
          method: HttpRequestMethod.post,
          body: body.toJson(),
          headers: {
            idempotencyKeyHeader: idempotencyKey,
          },
        ),
      ),
      (_) {},
    );
  }
}
