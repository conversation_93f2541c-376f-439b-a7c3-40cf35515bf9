{"openapi": "3.1.0", "info": {"title": "Lending SME Public API", "description": "The SME Orchestrator Service handles interaction\nbetween frontend and backend services for SME product.\n", "version": "1.0.0"}, "servers": [{"url": "https://app-lending-bank-sme-orchestrator-service-java-ms.aks.pre.neobank-internal.ae", "description": "Generated server url"}], "paths": {"/api/v2/loan-accounts/update-preferences": {"put": {"tags": ["sme-orchestrator-service"], "operationId": "put-api-v2-loan-accounts-update-preferences", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePreferencesRequestV2"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/LoanAccount"}}}}}}}, "/api/v1/loan-accounts/update-preferences": {"put": {"tags": ["sme-orchestrator-service"], "operationId": "put-api-v1-loan-accounts-update-preferences", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePreferencesRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/LoanAccount"}}}}}}}, "/api/v1/lending/failed-events": {"get": {"tags": ["failed-events-controller"], "operationId": "get-api-v1-lending-failed-events", "parameters": [{"name": "resolved", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "severity", "in": "query", "required": false, "schema": {"type": "string", "enum": ["MINOR", "MAJOR", "CRITICAL"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FailedEventsDocument"}}}}}}}, "put": {"tags": ["failed-events-controller"], "operationId": "put-api-v1-lending-failed-events", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FailedEventUpdateDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FailedEventsDocument"}}}}}}}}, "/api/v1/applications/{applicationId}": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-applications-param", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Application"}}}}}}, "put": {"tags": ["sme-orchestrator-service"], "operationId": "put-api-v1-applications-param", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateApplicationCommand"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Application"}}}}}}}, "/api/v1/applications/{applicationId}/input-data": {"put": {"tags": ["sme-orchestrator-service"], "operationId": "put-api-v1-applications-param-input-data", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationInputDataRequestDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Application"}}}}}}}, "/api/v2/loan-account/evaluation/installments": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v2-loan-account-evaluation-installments", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoanInstallmentEvaluateCommand"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Schedule"}}}}}}}, "/api/v2/loan-account/disbursement": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v2-loan-account-disbursement", "parameters": [{"name": "Idempotency-Key", "in": "header", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoanInstallmentDisbursementV2"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v2/credit-limit": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v2-credit-limit", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EligibleCreditLimitRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/EligibleCreditLimitResponseDto"}}}}}}}, "/api/v1/transaction-statements": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-transaction-statements", "parameters": [{"name": "lang", "in": "query", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatementFilter"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/v1/supplier/referrals": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-supplier-referrals", "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/StatusFilter", "default": "COMPLETED"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Referral"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetail"}}}}}}, "post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-supplier-referrals", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReferralRequestDTO"}}}, "required": true}, "responses": {"401": {"description": "Unauthorized"}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetail"}}}}, "200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubmitReferralResponse"}}}}, "400": {"description": "Invalid input", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetail"}}}}}}}, "/api/v1/submit/applications/{applicationId}": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-submit-applications-param", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Application"}}}}}}}, "/api/v1/submit/applications/{applicationId}/credit-agreement": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-submit-applications-param-credit-agreement", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgreementInputDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/sign/applications/{applicationId}/credit-agreement": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-sign-applications-param-credit-agreement", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "x-2fa-id", "in": "header", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgreementInputDto"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Application"}}}}}}}, "/api/v1/retry/failed-events": {"post": {"tags": ["failed-events-controller"], "operationId": "post-api-v1-retry-failed-events", "responses": {"200": {"description": "OK"}}}}, "/api/v1/retry/failed-events/{id}": {"post": {"tags": ["failed-events-controller"], "operationId": "post-api-v1-retry-failed-events-param", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/resubmit/applications/{applicationId}": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-resubmit-applications-param", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResubmittedApplicationDetails"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Application"}}}}}}}, "/api/v1/multi-user/requests": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-multi-user-requests", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MultiUserCreateRequestDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MultiUserResponseDTO"}}}}}}}, "/api/v1/multi-user/requests/{multiuserRequestId}/reject": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-multi-user-requests-param-reject", "parameters": [{"name": "multiuserRequestId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MultiUserRejectRequestDTO"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/multi-user/requests/{multiuserRequestId}/approve": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-multi-user-requests-param-approve", "parameters": [{"name": "multiuserRequestId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MultiUserApproveRequestDTO"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/loan-account/{loanId}/disbursement": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-loan-account-param-disbursement", "parameters": [{"name": "Idempotency-Key", "in": "header", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "loanId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoanInstallmentDisbursement"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/loan-account/evaluation/{loanId}/installments": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-loan-account-evaluation-param-installments", "parameters": [{"name": "loanId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoanInstallmentEvaluateCommand"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Schedule"}}}}}}}, "/api/v1/internal/scf/opt-in": {"post": {"tags": ["sample-controller"], "operationId": "post-api-v1-internal-scf-opt-in", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalReferralRequestDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SubmitReferralResponse"}}}}}}}, "/api/v1/internal/replay/multi-user": {"post": {"tags": ["sample-controller"], "operationId": "post-api-v1-internal-replay-multi-user", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MultiUserEventData"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/internal/evict/cache": {"post": {"tags": ["sample-controller"], "operationId": "post-api-v1-internal-evict-cache", "parameters": [{"name": "cacheName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "key", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/easy-cash/repayment": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-easy-cash-repayment", "parameters": [{"name": "Idempotency-Key", "in": "header", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EasyCashRepayment"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/easy-cash/evaluation/repayment": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-easy-cash-evaluation-repayment", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EasyCashEvaluateRepayment"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/EasyCashRepaymentEvaluation"}}}}}}}, "/api/v1/easy-cash/evaluation/disbursement": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-easy-cash-evaluation-disbursement", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EasyCashEvaluateDisbursement"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/EasyCashDisbursementEvaluation"}}}}}}}, "/api/v1/easy-cash/disbursement": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-easy-cash-disbursement", "parameters": [{"name": "Idempotency-Key", "in": "header", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EasyCashDisbursement"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/cancel/applications/{applicationId}": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-cancel-applications-param", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Application"}}}}}}}, "/api/v1/applications": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-applications", "parameters": [{"name": "productType", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ProductType"}}, {"name": "status", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ApplicationStatus"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ApplicationListResponse"}}}}}}, "post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-applications", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateApplicationCommand"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Application"}}}}}}}, "/api/v1/applications/{applicationId}/documents": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-applications-param-documents", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DocumentListResponse"}}}}}}, "post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-applications-param-documents", "parameters": [{"name": "X-Document-Type", "in": "header", "required": true, "schema": {"$ref": "#/components/schemas/DocumentType"}}, {"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "password", "in": "query", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}, "required": ["file"]}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DocumentMetadataResponse"}}}}}}}, "/api/v1/applications/{applicationId}/credit-decision-rejection:acknowledge": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-applications-param-credit-decision-rejection:acknowledge", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Application"}}}}}}}, "/api/v1/application/resend-email/{applicationId}": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-application-resend-email-param", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "notificationType", "in": "query", "required": true, "schema": {"type": "string", "enum": ["HYPOTHECATION_VERIFY"]}}, {"name": "productType", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ProductType", "default": "SME_RECEIVABLE_FINANCE"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/accounts/search/transactions": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-accounts-search-transactions", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionFilterRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PaginatedResponseTransactionResponse"}}}}}}}, "/api/v1/accounts/repayment": {"post": {"tags": ["sme-orchestrator-service"], "operationId": "post-api-v1-accounts-repayment", "parameters": [{"name": "Idempotency-Key", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoanRepaymentCommand"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/accounts/{loanAccountId}/close": {"patch": {"tags": ["sme-orchestrator-service"], "operationId": "patch-api-v1-accounts-param-close", "parameters": [{"name": "loanAccountId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/accounts/{id}/payment-settings": {"patch": {"tags": ["sme-orchestrator-service"], "operationId": "patch-api-v1-accounts-param-payment-settings", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentSettingsRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Account"}}}}}}}, "/api/v1/accounts/{id}/loan-amount": {"patch": {"tags": ["sme-orchestrator-service"], "operationId": "patch-api-v1-accounts-param-loan-amount", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLoanAmountRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Account"}}}}}}}, "/api/v2/static/product": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v2-static-product", "parameters": [{"name": "productCode", "in": "query", "required": true, "schema": {"type": "string", "enum": ["PL", "PL_CP", "PL_AUTO", "PL_PORTFOLIO", "EC", "CC", "SME_CC_FD", "BL", "SME_EC", "SME_CC", "POS", "IPO", "SCF", "CF", "MT"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductCodeStaticDetailsDto"}}}}}}}, "/api/v1/whoami": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-whoami", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BusinessDetails"}}}}}}}, "/api/v1/transaction-statements/{id}": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-transaction-statements-param", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/v1/transaction-statements/{accountId}/statements": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-transaction-statements-param-statements", "parameters": [{"name": "accountId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StatementDto"}}}}}}}}, "/api/v1/supplier/{domainId}": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-supplier-param", "parameters": [{"name": "domainId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReferralValidationResponse"}}}}}}}, "/api/v1/supplier/referrals/{referralCode}": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-supplier-referrals-param", "parameters": [{"name": "referralCode", "in": "path", "required": true, "schema": {"type": "string", "pattern": "^[a-zA-Z0-9]{10}$"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReferralValidationResponse"}}}}, "400": {"description": "Invalid input", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetail"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetail"}}}}}}}, "/api/v1/supplier/invoices": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-supplier-invoices", "parameters": [{"name": "multiUserRequestId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JocataViewInvoicesResponse"}}}}, "400": {"description": "Invalid input", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetail"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetail"}}}}}}}, "/api/v1/supplier/invoices/summary": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-supplier-invoices-summary", "parameters": [{"name": "requestIds", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JocataInvoiceSummaryResponse"}}}}, "400": {"description": "Invalid input", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetail"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetail"}}}}}}}, "/api/v1/static/sales-channels": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-static-sales-channels", "parameters": [{"name": "accept-language", "in": "header", "required": false, "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SalesChannelResponse"}}}}}}}}, "/api/v1/static/products": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-static-products", "parameters": [{"name": "productTypes", "in": "query", "description": "List of product types", "required": true, "schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductType"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StaticDetails"}}}}}}}}, "/api/v1/static/product": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-static-product", "parameters": [{"name": "productType", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ProductType"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StaticDetails"}}}}}}}, "/api/v1/static/non-registered-vat-reasons": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-static-non-registered-vat-reasons", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NonRegisteredVatReason"}}}}}}}}, "/api/v1/static/employee-counts": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-static-employee-counts", "parameters": [{"name": "accept-language", "in": "header", "required": false, "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EmployeeCountResponse"}}}}}}}}, "/api/v1/offer": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-offer", "parameters": [{"name": "productType", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/ProductType"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/GetOffersResponse"}}}}}}}, "/api/v1/multi-user/requests/{requestId}/{individualId}/approvals": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-multi-user-requests-param-param-approvals", "parameters": [{"name": "requestId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "individualId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MultiUserApprovalsInfoDto"}}}}}}}}, "/api/v1/multi-user/requests/{individualId}": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-multi-user-requests-param", "parameters": [{"name": "individualId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "params", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/GetRequestsParams"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/GetMultiUserRequestsResponse"}}}}}}}, "/api/v1/multi-user/requests/approver-requests/{individualId}": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-multi-user-requests-approver-requests-param", "parameters": [{"name": "individualId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/JsonNode"}}}}}}}, "/api/v1/lending/failed-events/{id}": {"get": {"tags": ["failed-events-controller"], "operationId": "get-api-v1-lending-failed-events-param", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FailedEventsDocument"}}}}}}}, "/api/v1/lending/bank-details/{iban}": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-lending-bank-details-param", "parameters": [{"name": "iban", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BankDetails"}}}}}}}, "/api/v1/easy-cash/{creditCardLoanAccountId}/limit": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-easy-cash-param-limit", "parameters": [{"name": "creditCardLoanAccountId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/EasyCashLimit"}}}}}}}, "/api/v1/download/applications/{applicationId}/documents/{documentId}": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-download-applications-param-documents-param", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "documentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DownloadResponse"}}}}}}}, "/api/v1/applications/{applicationId}/documents/{documentId}": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-applications-param-documents-param", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "documentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DocumentMetadataResponse"}}}}}}, "delete": {"tags": ["sme-orchestrator-service"], "operationId": "delete-api-v1-applications-param-documents-param", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "documentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/applications/{applicationId}/credit-agreement": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-applications-param-credit-agreement", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "lang", "in": "query", "required": false, "schema": {"type": "string", "default": "EN"}}, {"name": "product", "in": "query", "description": "Product type", "required": false, "schema": {"$ref": "#/components/schemas/ProductType"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/GetCreditAgreementResponse"}}}}}}}, "/api/v1/accounts": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-accounts", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Account"}}}}}}}}, "/api/v1/accounts/{loanAccountId}": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-accounts-param", "parameters": [{"name": "loanAccountId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "isCalculateMinLimit", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Account"}}}}}}}, "/api/v1/accounts/{loanAccountId}/loan-amortization-schedule-pdf": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-accounts-param-loan-amortization-schedule-pdf", "parameters": [{"name": "loanAccountId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RepaymentSchedulePdfResponse"}}}}}}}, "/api/v1/accounts/{loanAccountId}/current-auto-payment": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-accounts-param-current-auto-payment", "parameters": [{"name": "loanAccountId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AutoPaymentInfo"}}}}}}}, "/api/v1/accounts/{id}/credit/limit": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-accounts-param-credit-limit", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CreditLimitDetailsDto"}}}}}}}, "/api/v1/accounts/transactions": {"get": {"tags": ["sme-orchestrator-service"], "operationId": "get-api-v1-accounts-transactions", "parameters": [{"name": "filter", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/TransactionFilterRequestDto"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PaginatedResponseTransactionResponse"}}}}}}}, "/api/v1/applications/{applicationId}/preferences/reduced-credit-card-limit": {"delete": {"tags": ["sme-orchestrator-service"], "operationId": "delete-api-v1-applications-param-preferences-reduced-credit-card-limit", "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "productType", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ProductType"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Application"}}}}}}}}, "components": {"schemas": {"PreferencesV2": {"type": "object", "properties": {"settlementAccountId": {"type": "string"}, "disableAutoPayFromSavings": {"type": "boolean"}}}, "UpdatePreferencesRequestV2": {"type": "object", "properties": {"loanAccountId": {"type": "string"}, "preferences": {"$ref": "#/components/schemas/PreferencesV2"}}, "required": ["loanAccountId", "preferences"]}, "LoanAccount": {"type": "object", "properties": {"accountId": {"type": "string"}, "name": {"type": "string"}, "loanAmount": {"type": "number"}, "customerId": {"type": "string"}, "monthlyRepaymentDay": {"type": "integer", "format": "int32"}, "preferences": {"$ref": "#/components/schemas/PreferencesV2"}, "isActive": {"type": "boolean"}}}, "Preferences": {"type": "object", "properties": {"disableAutoPayFromSavings": {"type": "boolean"}}, "required": ["disableAutoPayFromSavings"]}, "UpdatePreferencesRequest": {"type": "object", "properties": {"loanAccountId": {"type": "string"}, "preferences": {"$ref": "#/components/schemas/Preferences"}}, "required": ["loanAccountId", "preferences"]}, "FailedEventUpdateDto": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}}, "resolved": {"type": "boolean"}}}, "FailedEventsDocument": {"type": "object", "properties": {"id": {"type": "string"}, "applicationId": {"type": "string"}, "customerId": {"type": "string"}, "correlationId": {"type": "string"}, "cloudEventId": {"type": "string"}, "exception": {"type": "string"}, "requestURL": {"type": "string"}, "request": {"type": "string"}, "type": {"type": "string", "enum": ["REST", "KAFKA", "CRON"]}, "numberOfRetries": {"type": "integer", "format": "int32"}, "topic": {"type": "string"}, "severity": {"type": "string", "enum": ["MINOR", "MAJOR", "CRITICAL"]}, "resolved": {"type": "boolean"}, "retryable": {"$ref": "#/components/schemas/Retryable"}}}, "Retryable": {"type": "object", "properties": {"isRetryable": {"type": "boolean"}, "isCloudEvent": {"type": "boolean"}}}, "UpdateApplicationCommand": {"type": "object"}, "Address": {"type": "object", "properties": {"addressType": {"type": "string", "enum": ["OPERATING_ADDRESS", "REGISTERED_ADDRESS", "ALTERNATE_ADDRESS"]}, "buildingName": {"type": "string"}, "street": {"type": "string"}, "city": {"type": "string"}, "emirate": {"type": "string"}, "country": {"type": "string"}, "unitNumber": {"type": "string"}, "poBox": {"type": "string"}}, "required": ["addressType", "buildingName", "city", "country", "emirate", "street", "unitNumber"]}, "Application": {"type": "object", "properties": {"id": {"type": "string"}, "businessId": {"type": "string"}, "externalReferenceId": {"type": "string"}, "companyName": {"type": "string"}, "applicantId": {"type": "string"}, "referralCode": {"type": "string"}, "status": {"$ref": "#/components/schemas/ApplicationStatus"}, "uiStatus": {"$ref": "#/components/schemas/ApplicationUiStatus"}, "productType": {"$ref": "#/components/schemas/ProductType"}, "entryProductType": {"$ref": "#/components/schemas/ProductType"}, "creditDecisionResult": {"$ref": "#/components/schemas/CreditDecisionResult"}, "creditDecisionResults": {"$ref": "#/components/schemas/CreditDecisionResults"}, "inputData": {"$ref": "#/components/schemas/ApplicationInputDataRequest"}, "onboardingFlowControls": {"$ref": "#/components/schemas/ApplicationOnboardingFlowControls"}, "documents": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentMetadataResponse"}}, "productApplications": {"type": "array", "items": {"$ref": "#/components/schemas/ProductApplication"}}, "resubmittedApplicationDetails": {"$ref": "#/components/schemas/ResubmittedApplicationDetails"}, "secureLoanEligibilityDetails": {"$ref": "#/components/schemas/SecureLoanEligibilityDetails"}, "createdAt": {"type": "string", "format": "date-time"}}}, "ApplicationInputDataRequest": {"type": "object", "properties": {"requestedMoney": {"$ref": "#/components/schemas/Money"}, "annualTurnover": {"$ref": "#/components/schemas/Money"}, "selectedAmount": {"$ref": "#/components/schemas/Money"}, "vatReportingMethod": {"type": "string", "enum": ["MONTHLY", "QUARTERLY", "ANNUAL", "NO_REPORTING"]}, "ibans": {"type": "array", "items": {"$ref": "#/components/schemas/Iban"}}, "wioIbans": {"type": "array", "items": {"$ref": "#/components/schemas/Iban"}}, "monthlyPaymentPercentage": {"type": "integer", "format": "int32"}, "isAutopayFromSavingSpace": {"type": "boolean"}, "monthlyRepaymentDay": {"type": "integer", "format": "int32"}, "uiStatus": {"$ref": "#/components/schemas/ApplicationUiStatus"}, "nonRegisteredVatReasonType": {"type": "string", "enum": ["BUSINESS_TAX_LESS_THAN_THRESHOLD_LIMIT", "BUSINESS_TAX_NOT_IN_UAE", "BUSINESS_HIGH_SEA_SALES", "BUSINESS_VAT_EXCEPTION_FROM_FTA", "BUSINESS_EXEMPTED_ACTIVITIES", "OTHER"]}, "nonRegisteredVatReasonText": {"type": "string"}, "referralCode": {"type": "string"}, "securedLoan": {"type": "boolean"}, "serviceChannels": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}, "employeeCountRange": {"type": "string"}, "businessModelDescription": {"type": "string"}, "missingProofOfAddressReason": {"type": "string"}, "missingFinancialStatementReason": {"type": "string"}, "missingVatReason": {"type": "string"}, "addresses": {"type": "array", "items": {"$ref": "#/components/schemas/Address"}, "uniqueItems": true}, "productPreferences": {"type": "array", "items": {"$ref": "#/components/schemas/ProductPreferences"}}}}, "ApplicationOnboardingFlowControls": {"type": "object", "properties": {"shouldUploadFinancialDocument": {"type": "boolean"}, "shouldUploadBorrowingPowerVerificationDocument": {"type": "boolean"}, "shouldCollectMonthlyPaymentDate": {"type": "boolean"}, "shouldCollectRequestedAmount": {"type": "boolean"}}}, "ApplicationStatus": {"type": "string", "enum": ["IN_PROGRESS", "IN_REVIEW", "COMPLETED", "REJECTED", "CANCELLED", "EXPIRED"]}, "ApplicationUiStatus": {"type": "string", "enum": ["ANNUAL_TURNOVER_INPUT", "VAT_REPORTING_METHOD_INPUT", "VAT_STATEMENTS_UPLOAD", "VAT_NON_REGISTRATION_REASON_INPUT", "BANK_ACCOUNTS_INPUT", "RECAP_SCREEN", "CREDIT_DECISION_IN_REVIEW", "MONTHLY_REPAYMENT_PERCENTAGE_INPUT", "MONTHLY_REPAYMENT_DAY_INPUT", "BORROW_AGREEMENT_SIGNING", "APPLICATION_COMPLETED", "SELECTED_AMOUNT_INPUT", "AUDITED_FINANCIAL_STATEMENT_UPLOAD", "CONFIRM_AUTODEBIT", "LOAN_TERM_AMOUNT_SELECTION", "REPAYMENT_PLAN", "REFERRAL_CODE_INPUT", "SECURED_LOAN_INPUT", "REQUESTED_AMOUNT_INPUT", "EMPLOYEE_COUNT_RANGE_INPUT", "SERVICE_CHANNELS_INPUT", "BUSINESS_MODEL_DESCRIPTION_INPUT", "FINANCIAL_ACTIVITY_UPLOAD", "TRADING_ADDRESS_INPUT", "PROOF_OF_ADDRESS_UPLOAD"]}, "CreditDecisionResult": {"type": "object", "properties": {"creditDecisionStatus": {"type": "string", "enum": ["NOT_STARTED", "IN_PROGRESS", "REJECTED", "APPROVED"]}, "approvedAmount": {"type": "number"}, "interestRate": {"type": "number"}, "annualFee": {"type": "number"}, "cashWithdrawalAllowance": {"type": "number"}}}, "CreditDecisionResultV2": {"type": "object", "properties": {"creditDecisionStatus": {"type": "string", "enum": ["NOT_STARTED", "IN_PROGRESS", "REJECTED", "APPROVED"]}, "approvedAmount": {"type": "number"}, "availableAmount": {"type": "number"}, "productType": {"$ref": "#/components/schemas/ProductType"}, "creditAcceptanceStatus": {"type": "string", "enum": ["NOT_STARTED", "IN_PROGRESS", "COMPLETED"]}, "productTerms": {"$ref": "#/components/schemas/ProductTerms"}, "verificationStatus": {"type": "string", "enum": ["REQUIRED", "NOT_REQUIRED", "APPROVED", "REJECTED"]}, "partnerName": {"type": "string"}, "isAutoAcceptOffer": {"type": "boolean"}}, "required": ["productType", "verificationStatus"]}, "CreditDecisionResults": {"type": "object", "properties": {"creditDecisionStatus": {"type": "string", "enum": ["NOT_STARTED", "IN_PROGRESS", "REJECTED", "APPROVED"]}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/CreditDecisionResultV2"}}}}, "Currency": {"type": "string", "enum": ["AED", "USD", "EUR"]}, "DocumentMetadataResponse": {"type": "object", "properties": {"id": {"type": "string"}, "filename": {"type": "string"}, "documentType": {"$ref": "#/components/schemas/DocumentType"}, "contentType": {"type": "string"}, "url": {"type": "string"}, "uploadedAt": {"type": "string", "format": "date-time"}, "urls": {"$ref": "#/components/schemas/LocalizedDocumentLink"}}, "required": ["contentType", "documentType", "filename", "id"]}, "DocumentType": {"type": "string", "enum": ["VAT_STATEMENT", "KEY_FACT_STATEMENT", "CREDIT_AGREEMENT", "TERMS_AND_CONDITIONS", "BANK_STATEMENT", "AUDITED_FINANCIAL_STATEMENT", "BORROWING_POWER_PROOF", "LENDING_KEY_FACT_STATEMENT_POS_SME", "LENDING_TERMS_AND_CONDITIONS_POS_SME", "LENDING_KEY_FACT_STATEMENT_BL", "LENDING_TERMS_AND_CONDITIONS_BL"]}, "FixedDepositInfo": {"type": "object", "properties": {"money": {"$ref": "#/components/schemas/Money"}, "loanProductCode": {"type": "string", "enum": ["PL", "PL_CP", "PL_AUTO", "PL_PORTFOLIO", "EC", "CC", "SME_CC_FD", "BL", "SME_EC", "SME_CC", "POS", "IPO", "SCF", "CF", "MT"]}, "depositAccountId": {"type": "string"}}, "required": ["depositAccountId", "loanProductCode", "money"]}, "Iban": {"type": "object", "properties": {"iban": {"type": "string"}, "name": {"type": "string", "pattern": "[a-zA-Z0-9 ]*"}}}, "InstallmentFeeParameters": {"type": "object", "properties": {"minimumFee": {"type": "number", "format": "double"}, "fixedFee": {"type": "number", "format": "double"}, "principalPercentage": {"type": "number", "format": "double"}}}, "LoanSecurityProductDetails": {"type": "object", "properties": {"productCode": {"type": "string", "enum": ["PL", "PL_CP", "PL_AUTO", "PL_PORTFOLIO", "EC", "CC", "SME_CC_FD", "BL", "SME_EC", "SME_CC", "POS", "IPO", "SCF", "CF", "MT"]}}}, "LocalizedDocumentLink": {"type": "object", "properties": {"en": {"type": "string"}, "ar": {"type": "string"}}, "required": ["ar", "en"]}, "Money": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"$ref": "#/components/schemas/Currency"}}}, "ProductApplication": {"type": "object", "properties": {"productType": {"$ref": "#/components/schemas/ProductType"}, "accountCreationStatus": {"type": "string", "enum": ["NOT_STARTED", "IN_PROGRESS", "COMPLETED"]}, "accountId": {"type": "string"}}}, "ProductPreferences": {"type": "object", "properties": {"isAutopayFromSavingSpace": {"type": "boolean"}, "monthlyRepaymentDay": {"type": "integer", "format": "int32"}, "monthlyPaymentPercentage": {"type": "integer", "format": "int32"}, "selectedAmount": {"$ref": "#/components/schemas/Money"}, "reducedCreditCardLimit": {"$ref": "#/components/schemas/Money"}, "loanPeriod": {"type": "string", "example": "P6M"}, "productType": {"$ref": "#/components/schemas/ProductType"}, "settlementAccountId": {"type": "string"}, "uiStatus": {"$ref": "#/components/schemas/ApplicationUiStatus"}}}, "ProductTerms": {"type": "object", "properties": {"interestRate": {"type": "number"}, "annualFee": {"type": "number"}, "lateFee": {"type": "number"}, "cashWithdrawalAllowance": {"type": "number"}, "dpdPeriod": {"type": "string", "example": "P90D"}, "loanExpiryPeriod": {"type": "string", "example": "P360D"}, "loanPeriod": {"type": "string", "example": "P90D"}, "issuanceFeeParameters": {"$ref": "#/components/schemas/InstallmentFeeParameters"}, "repaymentFeeParameters": {"$ref": "#/components/schemas/InstallmentFeeParameters"}}}, "ProductType": {"type": "string", "enum": ["SME_RECEIVABLE_FINANCE", "SME_CREDIT_CARD", "SME_EASY_CASH", "SCF", "CHANNEL_FINANCE", "CREDIT", "IPO", "EASY_CASH", "BUSINESS_LOAN"]}, "ResubmittedApplicationDetails": {"type": "object", "properties": {"fixedDepositInfo": {"$ref": "#/components/schemas/FixedDepositInfo"}}}, "SecureLoanEligibilityDetails": {"type": "object", "properties": {"securityProductDetails": {"type": "array", "items": {"$ref": "#/components/schemas/LoanSecurityProductDetails"}}}}, "ApplicationInputDataRequestDTO": {"type": "object", "properties": {"requestedMoney": {"$ref": "#/components/schemas/Money"}, "annualTurnover": {"$ref": "#/components/schemas/Money"}, "selectedAmount": {"$ref": "#/components/schemas/Money"}, "vatReportingMethod": {"type": "string", "enum": ["MONTHLY", "QUARTERLY", "ANNUAL", "NO_REPORTING"]}, "ibans": {"type": "array", "items": {"$ref": "#/components/schemas/Iban"}}, "wioIbans": {"type": "array", "items": {"$ref": "#/components/schemas/Iban"}}, "monthlyPaymentPercentage": {"type": "integer", "format": "int32", "maximum": 100, "minimum": 5}, "isAutopayFromSavingSpace": {"type": "boolean"}, "monthlyRepaymentDay": {"type": "integer", "format": "int32", "maximum": 28, "minimum": 1}, "uiStatus": {"$ref": "#/components/schemas/ApplicationUiStatus"}, "nonRegisteredVatReasonType": {"type": "string", "enum": ["BUSINESS_TAX_LESS_THAN_THRESHOLD_LIMIT", "BUSINESS_TAX_NOT_IN_UAE", "BUSINESS_HIGH_SEA_SALES", "BUSINESS_VAT_EXCEPTION_FROM_FTA", "BUSINESS_EXEMPTED_ACTIVITIES", "OTHER"]}, "nonRegisteredVatReasonText": {"type": "string"}, "referralCode": {"type": "string"}, "securedLoan": {"type": "boolean"}, "serviceChannels": {"type": "array", "items": {"type": "string"}}, "employeeCountRange": {"type": "string"}, "businessModelDescription": {"type": "string"}, "missingProofOfAddressReason": {"type": "string"}, "missingFinancialStatementReason": {"type": "string"}, "missingVatReason": {"type": "string"}, "addresses": {"type": "array", "items": {"$ref": "#/components/schemas/Address"}}, "productPreferences": {"type": "array", "items": {"$ref": "#/components/schemas/ProductPreferences"}}}}, "LoanInstallmentEvaluateCommand": {"type": "object", "properties": {"amount": {"$ref": "#/components/schemas/Money"}, "productType": {"$ref": "#/components/schemas/ProductType"}, "loanProductCode": {"type": "string", "enum": ["PL", "PL_CP", "PL_AUTO", "PL_PORTFOLIO", "EC", "CC", "SME_CC_FD", "BL", "SME_EC", "SME_CC", "POS", "IPO", "SCF", "CF", "MT"]}, "evaluationTransactionType": {"type": "string", "enum": ["DISBURSEMENT", "REPAYMENT"]}, "previewScheduleStrategy": {"type": "string", "enum": ["INCLUDE_PREVIOUS_WITHDRAWALS", "CURRENT_AMOUNT"]}, "loanPeriod": {"type": "string", "example": "P12M"}, "applicationId": {"type": "string"}, "loanAccountId": {"type": "string"}, "firstRepaymentDate": {"type": "string", "format": "date"}}, "required": ["amount", "productType"]}, "ApplicableFees": {"type": "object", "properties": {"earlyRepaymentFee": {"$ref": "#/components/schemas/EarlyRepaymentFee"}, "loanIssuanceFee": {"$ref": "#/components/schemas/LoanIssuanceFee"}}}, "EarlyRepaymentFee": {"type": "object", "properties": {"txnId": {"type": "string"}, "amount": {"type": "number"}, "currency": {"$ref": "#/components/schemas/Currency"}}}, "Installment": {"type": "object", "properties": {"dueDate": {"type": "string", "format": "date"}, "overDue": {"$ref": "#/components/schemas/Money"}, "amount": {"$ref": "#/components/schemas/InstallmentAmount"}, "installmentAmount": {"$ref": "#/components/schemas/InstallmentAmount"}, "totalInstallmentAmount": {"$ref": "#/components/schemas/InstallmentAmount"}, "totalInstallmentAmountDue": {"$ref": "#/components/schemas/InstallmentAmount"}, "totalInstallmentAmountPaid": {"$ref": "#/components/schemas/InstallmentAmount"}, "totalScheduledInterestDue": {"$ref": "#/components/schemas/Money"}, "totalPenaltyInterest": {"$ref": "#/components/schemas/Money"}, "isPast": {"type": "boolean"}, "paid": {"type": "boolean"}, "upcomingInstallment": {"type": "boolean"}}, "required": ["amount", "dueDate"]}, "InstallmentAmount": {"type": "object", "properties": {"fee": {"type": "number"}, "principal": {"type": "number"}, "interest": {"type": "number"}}, "required": ["fee", "interest", "principal"]}, "LoanIssuanceFee": {"type": "object", "properties": {"txnId": {"type": "string"}, "amount": {"type": "number"}, "currency": {"$ref": "#/components/schemas/Currency"}}}, "NextInstallment": {"type": "object", "properties": {"interval": {"type": "string", "enum": ["DAILY", "WEEKLY", "MONTHLY", "YEARLY"]}, "installment": {"$ref": "#/components/schemas/Installment"}}, "required": ["installment", "interval"]}, "RepaymentDetails": {"type": "object", "properties": {"principalPaid": {"type": "number"}, "interestPaid": {"type": "number"}, "feesPaid": {"type": "number"}}}, "Schedule": {"type": "object", "properties": {"overDue": {"type": "number"}, "missedPaymentCount": {"type": "integer", "format": "int32"}, "loanAmount": {"$ref": "#/components/schemas/InstallmentAmount"}, "currency": {"$ref": "#/components/schemas/Currency"}, "nextInstallment": {"$ref": "#/components/schemas/NextInstallment"}, "installmentSummary": {"type": "array", "items": {"$ref": "#/components/schemas/Installment"}}, "applicableFees": {"$ref": "#/components/schemas/ApplicableFees"}, "repaymentDetails": {"$ref": "#/components/schemas/RepaymentDetails"}, "interestRate": {"type": "number"}, "currentInstallmentDue": {"$ref": "#/components/schemas/InstallmentAmount"}}}, "LoanInstallmentDisbursementV2": {"type": "object", "properties": {"amount": {"$ref": "#/components/schemas/Money"}, "transactionId": {"type": "string"}, "productSubType": {"type": "string", "enum": ["CARD_PURCHASE"]}, "loanPeriod": {"type": "string", "example": "P12M"}, "firstRepaymentDate": {"type": "string", "format": "date"}}, "required": ["amount"]}, "EligibleCreditLimitRequestDto": {"type": "object", "properties": {"productCode": {"type": "string", "enum": ["PL", "PL_CP", "PL_AUTO", "PL_PORTFOLIO", "EC", "CC", "SME_CC_FD", "BL", "SME_EC", "SME_CC", "POS", "IPO", "SCF", "CF", "MT"]}, "amount": {"$ref": "#/components/schemas/Money"}}}, "CreditCardLimitDetails": {"type": "object", "properties": {"maxLimit": {"$ref": "#/components/schemas/Money"}}, "required": ["maxLimit"]}, "EligibleCreditLimitResponseDto": {"type": "object", "properties": {"creditCardLimit": {"$ref": "#/components/schemas/CreditCardLimitDetails"}}}, "StatementFilter": {"type": "object", "properties": {"accountId": {"type": "string"}, "dateFrom": {"type": "string", "format": "date"}, "dateTo": {"type": "string", "format": "date"}}, "required": ["accountId", "dateFrom", "dateTo"]}, "ReferralRequestDTO": {"type": "object", "properties": {"referralCode": {"type": "string", "pattern": "^[a-zA-Z0-9]{10}$"}, "termsAndConditions": {"$ref": "#/components/schemas/LocalizedDocumentLink"}}, "required": ["referralCode", "termsAndConditions"]}, "ProblemDetail": {"type": "object", "properties": {"type": {"type": "string", "format": "uri"}, "title": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "detail": {"type": "string"}, "instance": {"type": "string", "format": "uri"}, "properties": {"type": "object", "additionalProperties": {"type": "object"}}}}, "SubmitReferralResponse": {"type": "object", "properties": {"referralCode": {"type": "string"}, "anchorBusinessName": {"type": "string"}, "multiUserRequestId": {"type": "string"}, "domainId": {"type": "string"}}, "required": ["anchorBusinessName", "referralCode"]}, "AgreementInputDto": {"type": "object", "properties": {"productType": {"$ref": "#/components/schemas/ProductType"}}}, "MultiUserCreateRequestDTO": {"type": "object", "properties": {"individualId": {"type": "string"}, "email": {"type": "string"}, "domainType": {"type": "string", "enum": ["SCF_SUPPLIER_INVITE", "SCF_INVOICE_UPLOAD", "SCF_INVOICE_FINANCING_ACCEPTANCE", "SCF_INVOICE_REPAYMENT_INITIATE", "SCF_INVOICE_REPAYMENT_DEFERRED"]}, "domainId": {"type": "string"}, "scfRequestSummary": {"$ref": "#/components/schemas/ScfRequestSummary"}}, "required": ["domainId", "domainType", "email", "individualId", "scfRequestSummary"]}, "ScfRequestSummary": {"type": "object", "properties": {"description": {"type": "string"}, "amount": {"type": "number"}, "currency": {"$ref": "#/components/schemas/Currency"}}, "required": ["description"]}, "ApproverDTO": {"type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}}}, "ApproverDetailsDTO": {"type": "object", "properties": {"approver": {"$ref": "#/components/schemas/ApproverDTO"}, "status": {"type": "string"}, "note": {"type": "string"}}}, "MultiUserResponseDTO": {"type": "object", "properties": {"domainType": {"type": "string", "enum": ["SCF_SUPPLIER_INVITE", "SCF_INVOICE_UPLOAD", "SCF_INVOICE_REPAYMENT_INITIATE", "SCF_INVOICE_FINANCING_ACCEPTANCE", "SCF_INVOICE_REPAYMENT_DEFERRED", "CREDIT_CONTRACT"]}, "id": {"type": "string"}, "status": {"type": "string", "enum": ["DRAFT", "ACTIVE", "REJECTED", "APPROVED", "ACTION_REQUIRED", "COMPLETED", "FAILED", "IN_REVIEW", "PENDING"]}, "rejectionNotes": {"type": "string"}, "domainId": {"type": "string"}, "authorId": {"type": "string"}, "creationFlow": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "twoFaRequired": {"type": "boolean"}, "approvers": {"type": "array", "items": {"$ref": "#/components/schemas/ApproverDTO"}}, "approvals": {"type": "array", "items": {"$ref": "#/components/schemas/ApproverDetailsDTO"}}, "scfSupplierInviteSummary": {"$ref": "#/components/schemas/ScfRequestSummary"}, "scfInvoiceUploadSummary": {"$ref": "#/components/schemas/ScfRequestSummary"}, "scfInvoiceRepaymentInitiateSummary": {"$ref": "#/components/schemas/ScfRequestSummary"}}}, "MultiUserRejectRequestDTO": {"type": "object", "properties": {"individualId": {"type": "string"}, "note": {"type": "string"}}, "required": ["individualId"]}, "MultiUserApproveRequestDTO": {"type": "object", "properties": {"individualId": {"type": "string"}}, "required": ["individualId"]}, "LoanInstallmentDisbursement": {"type": "object", "properties": {"amount": {"$ref": "#/components/schemas/Money"}}, "required": ["amount"]}, "InternalReferralRequestDTO": {"type": "object", "properties": {"referralCode": {"type": "string", "pattern": "^[a-zA-Z0-9]{10}$"}, "individualId": {"type": "string"}, "termsAndConditions": {"$ref": "#/components/schemas/LocalizedDocumentLink"}}, "required": ["individualId", "referralCode", "termsAndConditions"]}, "Data": {"type": "object", "properties": {"RequestType": {"type": "string"}, "RequestId": {"type": "string"}, "DomainId": {"type": "string"}, "AuthorId": {"type": "string"}, "BusinessId": {"type": "string"}, "DomainType": {"type": "string"}}}, "Header": {"type": "object", "properties": {"SchemaVersion": {"type": "string"}, "EventId": {"type": "string"}, "Environment": {"type": "string"}, "EventType": {"type": "string"}, "EventName": {"type": "string"}, "CorrelationId": {"type": "string"}, "SourceName": {"type": "string"}, "SourceType": {"type": "string"}, "Channel": {"type": "string"}, "Tenant": {"type": "string"}, "SessionId": {"type": "string"}, "Product": {"type": "string"}, "EventTime_UTC": {"type": "string"}, "SourceTime_UTC": {"type": "string"}}}, "MultiUserEventData": {"type": "object", "properties": {"Header": {"$ref": "#/components/schemas/Header"}, "Payload": {"$ref": "#/components/schemas/Payload"}}}, "Payload": {"type": "object", "properties": {"Data": {"$ref": "#/components/schemas/Data"}}}, "EasyCashRepayment": {"type": "object", "properties": {"easyCashLoanId": {"type": "string"}, "depositAccountId": {"type": "string"}, "amount": {"$ref": "#/components/schemas/Money"}}, "required": ["amount", "depositAccountId", "easyCashLoanId"]}, "EasyCashEvaluateRepayment": {"type": "object", "properties": {"easyCashLoanId": {"type": "string"}, "depositAccountId": {"type": "string"}, "amount": {"$ref": "#/components/schemas/Money"}}, "required": ["amount", "depositAccountId", "easyCashLoanId"]}, "Balances": {"type": "object", "properties": {"availableAmount": {"type": "number"}, "holdAmount": {"type": "number"}, "feesBalance": {"type": "number"}, "principalBalance": {"type": "number"}, "interestBalance": {"type": "number"}, "estimatedFee": {"type": "number"}}, "required": ["availableAmount", "feesBalance", "holdAmount", "principalBalance"]}, "EasyCashRepaymentEvaluation": {"type": "object", "properties": {"feePerDay": {"$ref": "#/components/schemas/Money"}, "totalFees": {"$ref": "#/components/schemas/Money"}, "feeSaved": {"$ref": "#/components/schemas/Money"}, "currency": {"$ref": "#/components/schemas/Currency"}, "balances": {"$ref": "#/components/schemas/Balances"}}, "required": ["feePerDay", "feeSaved", "totalFees"]}, "EasyCashEvaluateDisbursement": {"type": "object", "properties": {"dueDate": {"type": "string", "format": "date"}, "amount": {"$ref": "#/components/schemas/Money"}, "creditCardLoanAccountId": {"type": "string"}}, "required": ["amount", "creditCardLoanAccountId", "dueDate"]}, "EasyCashDisbursementEvaluation": {"type": "object", "properties": {"feePerDay": {"$ref": "#/components/schemas/Money"}, "totalFees": {"$ref": "#/components/schemas/Money"}, "finalTotalFees": {"$ref": "#/components/schemas/Money"}, "currentFeePerDay": {"$ref": "#/components/schemas/Money"}, "currentEstimatedTotalFees": {"$ref": "#/components/schemas/Money"}}, "required": ["currentEstimatedTotalFees", "currentFeePerDay", "feePerDay", "finalTotalFees", "totalFees"]}, "EasyCashDisbursement": {"type": "object", "properties": {"creditCardLoanId": {"type": "string"}, "amount": {"$ref": "#/components/schemas/Money"}, "currentAccountId": {"type": "string"}, "preferences": {"$ref": "#/components/schemas/Preferences"}}, "required": ["amount", "creditCardLoanId", "preferences"]}, "CreateApplicationCommand": {"type": "object", "properties": {"productType": {"$ref": "#/components/schemas/ProductType"}, "referralCode": {"type": "string", "pattern": "^[a-zA-Z0-9]{10}$"}, "externalReferenceId": {"type": "string"}, "termsAndConditions": {"$ref": "#/components/schemas/LocalizedDocumentLink"}, "keyFactStatement": {"$ref": "#/components/schemas/LocalizedDocumentLink"}}, "required": ["productType"]}, "TransactionFilterRequestDto": {"type": "object", "properties": {"accountId": {"type": "string", "pattern": "[0-9]{1,10}"}, "transactionType": {"type": "string", "enum": ["DISBURSEMENT", "REPAYMENT", "FEE_APPLIED", "REVOLVING_FEE", "LATE_FEE", "INTEREST", "REVERSAL"]}, "dateFrom": {"type": "string", "format": "date"}, "dateTo": {"type": "string", "format": "date"}}, "required": ["accountId"]}, "Details": {"type": "object", "properties": {"principalPaid": {"type": "number"}, "feePaid": {"type": "number"}, "interestPaid": {"type": "number"}}}, "PaginatedResponseTransactionResponse": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "currentPage": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionResponse"}}}}, "TransactionResponse": {"type": "object", "properties": {"AccountId": {"type": "string"}, "Id": {"type": "string"}, "TransactionIdentifier": {"type": "string"}, "ReferenceNumber": {"type": "string"}, "TransactionDateTime": {"type": "string", "format": "date-time"}, "Amount": {"type": "number"}, "TotalBalance": {"type": "number"}, "AvailableBalance": {"type": "number"}, "Currency": {"$ref": "#/components/schemas/Currency"}, "TransactionType": {"type": "string", "enum": ["EASY_CASH", "CREDIT_CARD", "SME_RECEIVABLE_FINANCE", "BUSINESS_LOAN", "UNKNOWN"]}, "TransactionSubType": {"type": "string", "enum": ["LENDING_DISBURSEMENT", "LENDING_REPAYMENT", "LENDING_REVOLVING_FEE", "LENDING_LATE_FEE", "LENDING_INTEREST", "LENDING_FEE_APPLIED"]}, "TransactionStatus": {"type": "string", "enum": ["FAILED", "COMPLETED", "PENDING"]}, "TransactionMode": {"type": "string", "enum": ["DEPOSIT", "WITHDRAWAL"]}, "Details": {"$ref": "#/components/schemas/Details"}}, "required": ["AccountId", "Amount", "Id", "TransactionDateTime", "TransactionIdentifier"]}, "LoanRepaymentCommand": {"type": "object", "properties": {"depositAccountId": {"type": "string"}, "loanAccountId": {"type": "string"}, "amount": {"$ref": "#/components/schemas/Money"}}, "required": ["amount", "depositAccountId", "loanAccountId"]}, "UpdatePaymentSettingsRequest": {"type": "object", "properties": {"percentage": {"type": "integer", "format": "int32", "maximum": 100, "minimum": 5}}, "required": ["percentage"]}, "Account": {"type": "object", "properties": {"id": {"type": "string"}, "customerId": {"type": "string"}, "name": {"type": "string"}, "lendingApplicationId": {"type": "string"}, "currency": {"$ref": "#/components/schemas/Currency"}, "notes": {"type": "string"}, "loanAmount": {"type": "number"}, "balances": {"$ref": "#/components/schemas/Balances"}, "paymentSettings": {"$ref": "#/components/schemas/PaymentSettings"}, "createdAt": {"type": "string", "format": "date-time"}, "productType": {"$ref": "#/components/schemas/ProductType"}, "mambuState": {"type": "string"}, "mambuSubState": {"type": "string"}, "feePercentage": {"type": "number", "format": "double"}, "loanExpiryPeriod": {"type": "string", "example": "P360D"}, "loanPeriod": {"type": "string", "example": "P90D"}, "annualFee": {"type": "number", "format": "double"}, "cashWithdrawalAllowance": {"type": "number", "format": "double"}, "firstRepaymentDate": {"type": "string", "format": "date"}, "minLimit": {"type": "number"}, "repaymentFeeParameters": {"$ref": "#/components/schemas/InstallmentFeeParameters"}, "lateFeeParameters": {"$ref": "#/components/schemas/InstallmentFeeParameters"}, "nextInstallmentDetails": {"$ref": "#/components/schemas/NextInstallmentDetails"}, "feeFreePeriodUntil": {"type": "string", "format": "date"}, "closeable": {"type": "boolean"}, "active": {"type": "boolean"}}, "required": ["balances", "createdAt", "currency", "customerId", "feePercentage", "id", "loanAmount", "mambuState", "name", "paymentSettings", "productType"]}, "NextInstallmentDetails": {"type": "object", "properties": {"dueDate": {"type": "string", "format": "date"}, "amount": {"$ref": "#/components/schemas/InstallmentAmount"}}, "required": ["amount", "dueDate"]}, "PaymentSettings": {"type": "object", "properties": {"percentage": {"type": "number"}}, "required": ["percentage"]}, "UpdateLoanAmountRequest": {"type": "object", "properties": {"loanAmount": {"type": "number"}}}, "ProductCodeStaticDetailsDto": {"type": "object", "properties": {"cashbackPercentage": {"type": "number", "format": "double"}, "minFDAmount": {"$ref": "#/components/schemas/Money"}, "limitFromFDPercent": {"type": "number", "format": "double"}}}, "BusinessDetails": {"type": "object", "properties": {"businessId": {"type": "string"}, "individualId": {"type": "string"}, "userName": {"type": "string"}, "email": {"type": "string"}}}, "StatementDto": {"type": "object", "properties": {"statementId": {"type": "string"}, "month": {"type": "integer", "format": "int32"}, "year": {"type": "integer", "format": "int32"}}, "required": ["month", "statementId", "year"]}, "ReferralValidationResponse": {"type": "object", "properties": {"valid": {"type": "boolean"}, "anchorBusinessName": {"type": "string"}, "anchorBusinessId": {"type": "string"}, "productType": {"$ref": "#/components/schemas/ProductType"}}, "required": ["anchorBusinessName", "valid"]}, "StatusFilter": {"type": "string", "enum": ["ALL", "PENDING", "COMPLETED"]}, "OptInStatus": {"type": "string", "enum": ["PENDING", "COMPLETED", "REJECTED", "CANCELLED"]}, "Referral": {"type": "object", "properties": {"referralCode": {"type": "string"}, "supplierId": {"type": "string"}, "anchorId": {"type": "string"}, "anchorBusinessName": {"type": "string"}, "productType": {"$ref": "#/components/schemas/ProductType"}, "status": {"$ref": "#/components/schemas/OptInStatus"}, "multiUserRequestId": {"type": "string"}, "domainId": {"type": "string"}, "jocataApplicationId": {"type": "string"}, "tncUrls": {"$ref": "#/components/schemas/LocalizedDocumentLink"}, "pricingTriggered": {"type": "boolean"}}}, "Invoice": {"type": "object", "properties": {"invoiceId": {"type": "string"}, "invoiceNumber": {"type": "string"}, "status": {"type": "string"}, "requestDate": {"type": "string"}, "dueDate": {"type": "string"}, "invoiceAmount": {"type": "string"}, "interest": {"type": "string"}, "amountToSupplier": {"type": "string"}, "currency": {"type": "string"}, "supplierName": {"type": "string"}, "preparer": {"type": "string"}}}, "JocataViewInvoicesResponse": {"type": "object", "properties": {"invoicesCount": {"type": "integer", "format": "int32"}, "totalInvoiceAmount": {"type": "string"}, "totalDiscount": {"type": "string"}, "currency": {"type": "string"}, "invoices": {"type": "array", "items": {"$ref": "#/components/schemas/Invoice"}}}}, "InvoicesRequestSummary": {"type": "object", "properties": {"invoicesCount": {"type": "integer", "format": "int32"}, "totalInvoiceAmount": {"type": "number"}, "totalDiscount": {"type": "number"}, "currency": {"type": "string"}}}, "JocataInvoiceSummaryResponse": {"type": "object", "properties": {"response": {"type": "array", "items": {"$ref": "#/components/schemas/InvoicesRequestSummary"}}}}, "SalesChannelResponse": {"type": "object", "properties": {"code": {"type": "string", "enum": ["WEBSITE", "SOCIAL_MEDIA", "AMAZON_NOON", "PHYSICAL_STORE", "OTHER"]}, "description": {"type": "string"}}, "required": ["code", "description"]}, "AmountRange": {"type": "object", "properties": {"min": {"$ref": "#/components/schemas/Money"}, "max": {"$ref": "#/components/schemas/Money"}}, "required": ["max", "min"]}, "InstallmentInterval": {"type": "object", "properties": {"interval": {"type": "string", "enum": ["DAILY", "WEEKLY", "MONTHLY", "YEARLY"]}, "count": {"type": "integer", "format": "int32"}}}, "InterestRange": {"type": "object", "properties": {"min": {"type": "integer", "format": "int32"}, "max": {"type": "integer", "format": "int32"}}, "required": ["max", "min"]}, "LocalTime": {"type": "object", "properties": {"hour": {"type": "integer", "format": "int32"}, "minute": {"type": "integer", "format": "int32"}, "second": {"type": "integer", "format": "int32"}, "nano": {"type": "integer", "format": "int32"}}}, "StaticDetails": {"type": "object", "properties": {"feeFreePeriod": {"type": "integer", "format": "int32"}, "cashbackPercentage": {"type": "number", "format": "double"}, "fileTypes": {"type": "array", "items": {"type": "string"}}, "maxSize": {"type": "integer", "format": "int32"}, "revolvingFeePercentage": {"type": "number", "format": "double"}, "minimumPrincipalRepaymentPercentage": {"type": "number", "format": "double"}, "lateFee": {"$ref": "#/components/schemas/Money"}, "minimumCreditLimitAmountOnApproval": {"$ref": "#/components/schemas/Money"}, "vatTemplateUrl": {"type": "string"}, "auditedFinancialStatementTemplateUrl": {"type": "string"}, "interestRange": {"$ref": "#/components/schemas/InterestRange"}, "securedInterestRange": {"$ref": "#/components/schemas/InterestRange"}, "amountRange": {"$ref": "#/components/schemas/AmountRange"}, "securedAmountRange": {"$ref": "#/components/schemas/AmountRange"}, "processingFee": {"$ref": "#/components/schemas/Money"}, "feeInterestPerDay": {"type": "number"}, "lateFeeGracePeriod": {"type": "integer", "format": "int32"}, "dueDays": {"type": "integer", "format": "int32"}, "maxLimitPercentage": {"type": "number"}, "productType": {"$ref": "#/components/schemas/ProductType"}, "installmentInterval": {"$ref": "#/components/schemas/InstallmentInterval"}, "minimumDisbursementAmount": {"$ref": "#/components/schemas/Money"}, "minimumRepaymentAmount": {"$ref": "#/components/schemas/Money"}, "autoPaymentTime": {"$ref": "#/components/schemas/LocalTime"}, "creditAutoPaymentTime": {"type": "string", "example": "21:00:00"}}, "required": ["cashbackPercentage", "feeFreePeriod", "fileTypes", "lateFee", "maxSize", "minimumCreditLimitAmountOnApproval", "minimumPrincipalRepaymentPercentage", "revolvingFeePercentage", "vatTemplateUrl"]}, "NonRegisteredVatReason": {"type": "object", "properties": {"type": {"type": "string", "enum": ["BUSINESS_TAX_LESS_THAN_THRESHOLD_LIMIT", "BUSINESS_TAX_NOT_IN_UAE", "BUSINESS_HIGH_SEA_SALES", "BUSINESS_VAT_EXCEPTION_FROM_FTA", "BUSINESS_EXEMPTED_ACTIVITIES", "OTHER"]}, "en": {"type": "string"}, "ar": {"type": "string"}}}, "EmployeeCountResponse": {"type": "object", "properties": {"code": {"type": "string", "enum": ["LESS_THAN_3", "FROM_3_TO_10", "FROM_10_TO_25", "FROM_25_TO_50", "MORE_THAN_50"]}, "description": {"type": "string"}}, "required": ["code", "description"]}, "GetOffersResponse": {"type": "object", "properties": {"nonEligibilityDetails": {"$ref": "#/components/schemas/NonEligibilityDetails"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/PreQualifiedOffer"}}, "total": {"type": "integer", "format": "int32"}}}, "NonEligibilityDetails": {"type": "object", "properties": {"description": {"type": "string"}, "subDescription": {"type": "string"}, "isUpdateDocument": {"type": "boolean"}, "reason": {"type": "string", "enum": ["DOCUMENT_EXPIRED", "DOCUMENT_EXPIRING_SOON", "LOB_BELOW_THRESHOLD", "OTHER"]}}, "required": ["description", "isUpdateDocument", "reason", "subDescription"]}, "PreQualifiedOffer": {"type": "object", "properties": {"money": {"$ref": "#/components/schemas/Money"}, "productType": {"$ref": "#/components/schemas/ProductType"}}, "required": ["money", "productType"]}, "MultiUserApprovalsInfoDto": {"type": "object", "properties": {"name": {"type": "string"}, "status": {"type": "string", "enum": ["DRAFT", "ACTIVE", "REJECTED", "APPROVED", "ACTION_REQUIRED", "COMPLETED", "FAILED", "IN_REVIEW", "PENDING"]}, "timestamp": {"type": "string"}}}, "GetRequestsParams": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "id": {"type": "string"}, "sort": {"type": "string"}, "domainTypes": {"type": "array", "items": {"type": "string", "enum": ["SCF_SUPPLIER_INVITE", "SCF_INVOICE_UPLOAD", "SCF_INVOICE_FINANCING_ACCEPTANCE", "SCF_INVOICE_REPAYMENT_INITIATE", "SCF_INVOICE_REPAYMENT_DEFERRED"]}}, "fromDate": {"type": "string", "format": "date"}, "toDate": {"type": "string", "format": "date"}, "status": {"type": "array", "items": {"type": "string", "enum": ["DRAFT", "ACTIVE", "REJECTED", "APPROVED", "ACTION_REQUIRED", "COMPLETED", "FAILED", "IN_REVIEW", "PENDING"]}}}}, "GetMultiUserRequestsResponse": {"type": "object", "properties": {"totalNumberRecords": {"type": "integer", "format": "int32"}, "totalNumberPages": {"type": "integer", "format": "int32"}, "requests": {"type": "array", "items": {"$ref": "#/components/schemas/MultiUserResponseDTO"}}}}, "JsonNode": {"type": "object"}, "BankDetails": {"type": "object", "properties": {"bankName": {"type": "string"}, "bankCode": {"type": "string"}, "bic": {"type": "string"}}}, "CreditArrangement": {"type": "object", "properties": {"accounts": {"type": "array", "items": {"$ref": "#/components/schemas/Account"}}}}, "EasyCashLimit": {"type": "object", "properties": {"easyCashPercentageLimit": {"type": "number"}, "easyCashInterestRate": {"type": "number"}, "easyCashAvailableLimit": {"$ref": "#/components/schemas/Money"}, "easyCashLimit": {"$ref": "#/components/schemas/Money"}, "dueDate": {"type": "string", "format": "date"}, "creditArrangement": {"$ref": "#/components/schemas/CreditArrangement"}}, "required": ["creditArrangement", "dueDate", "easyCashAvailableLimit", "easyCashInterestRate", "easyCashLimit", "easyCashPercentageLimit"]}, "DownloadResponse": {"type": "object", "properties": {"data": {"type": "string"}}}, "ApplicationListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Application"}}}}, "DocumentListResponse": {"type": "object", "properties": {"size": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentMetadataResponse"}}}}, "GetCreditAgreementResponse": {"type": "object", "properties": {"data": {"type": "string"}}}, "RepaymentSchedulePdfResponse": {"type": "object", "properties": {"pdfString": {"type": "string"}}}, "AutoPaymentInfo": {"type": "object", "properties": {"amount": {"type": "number"}, "fee": {"type": "number"}, "totalOutstandingBalance": {"type": "number"}, "linkedAccountsOutstanding": {"type": "number"}, "feeFreeDate": {"type": "string", "format": "date"}, "fullRepaymentDoneInCurrentCycle": {"type": "boolean"}, "currency": {"$ref": "#/components/schemas/Currency"}, "paymentDate": {"type": "string", "format": "date"}, "minimumRepaymentAmount": {"type": "number"}, "delinquentAccountDetails": {"$ref": "#/components/schemas/DelinquentAccountDetails"}, "amountPaidInCurrentCycle": {"type": "number"}, "preferences": {"$ref": "#/components/schemas/PreferencesV2"}, "feesPerDay": {"type": "number"}, "customerId": {"type": "string"}, "totalFeeBalance": {"type": "number"}, "schedule": {"$ref": "#/components/schemas/Schedule"}}, "required": ["amount", "currency", "fee", "fullRepaymentDoneInCurrentCycle", "linkedAccountsOutstanding", "minimumRepaymentAmount", "paymentDate", "totalOutstandingBalance"]}, "DelinquentAccountDetails": {"type": "object", "properties": {"minimumRepaymentAmount": {"type": "number"}, "locked": {"type": "boolean"}, "daysPastDue": {"type": "integer", "format": "int64"}, "dueDate": {"type": "string", "format": "date"}, "latePaymentFee": {"type": "number"}, "minimumRepaymentAmountExcLateFee": {"type": "number"}}, "required": ["dueDate"]}, "CreditLimitDetailsDto": {"type": "object", "properties": {"limitDetails": {"$ref": "#/components/schemas/LimitDetails"}, "inProgressRequest": {"$ref": "#/components/schemas/InProgressRequest"}}}, "InProgressRequest": {"type": "object", "properties": {"id": {"type": "string"}, "money": {"$ref": "#/components/schemas/Money"}, "date": {"type": "string", "format": "date-time"}}, "required": ["date", "id", "money"]}, "LimitDetails": {"type": "object", "properties": {"minLimit": {"$ref": "#/components/schemas/Money"}, "maxLimit": {"$ref": "#/components/schemas/Money"}, "allowIncreaseLimit": {"type": "boolean"}, "allowReduceLimit": {"type": "boolean"}}, "required": ["allowIncreaseLimit", "allowReduceLimit", "maxLimit", "minLimit"]}}, "securitySchemes": {"jwt": {"type": "http", "description": "Wio SME", "scheme": "bearer"}}}}