import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_credit_impl/src/data/models/schema_dtos/schema.swagger.dart'
    as dto;
import 'package:wio_feature_credit_impl/src/mappers/easy_cash_mapper.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

import '../mocks.dart';
import '../stub_data.dart';

void main() {
  late EasyCashMapper mapper;
  late MockErrorReporter mockErrorReporter;
  late MockCommonCreditMapper mockCommonCreditMapper;
  late MockCreditAccountMapper mockCreditAccountMapper;

  setUp(() {
    mockErrorReporter = MockErrorReporter();
    mockCommonCreditMapper = MockCommonCreditMapper();
    mockCreditAccountMapper = MockCreditAccountMapper();

    mapper = EasyCashMapper(
      errorReporter: mockErrorReporter,
      commonCreditMapper: mockCommonCreditMapper,
      creditAccountMapper: mockCreditAccountMapper,
    );
  });

  group('EasyCashMapper Tests', () {
    test('mapDisbursementEvaluation should map correctly', () {
      const money = dto.Money(amount: 50, currency: dto.Currency.aed);
      const dtoEvaluation = dto.EasyCashDisbursementEvaluation(
        feePerDay: money,
        totalFees: money,
        finalTotalFees: money,
        currentEstimatedTotalFees: money,
        currentFeePerDay: money,
      );

      when(() => mockCommonCreditMapper.mapToMoney(money))
          .thenReturn(Money.fromIntWithCurrency(50, Currency.aed));

      final result = mapper.mapDisbursementEvaluation(dtoEvaluation);

      expect(result.feePerDay, Money.fromIntWithCurrency(50, Currency.aed));
      expect(result.totalFees, Money.fromIntWithCurrency(50, Currency.aed));
      expect(
        result.finalTotalFees,
        Money.fromIntWithCurrency(50, Currency.aed),
      );
      expect(result.upcomingFee, Money.fromIntWithCurrency(50, Currency.aed));
    });

    test('mapRepaymentEvaluation should map correctly', () {
      const moneyDto = dto.Money(amount: 50, currency: dto.Currency.aed);
      final moneyDomain = Money.fromNumWithCurrency(50, Currency.aed);
      const dtoEvaluation = dto.EasyCashRepaymentEvaluation(
        feePerDay: moneyDto,
        totalFees: moneyDto,
        feeSaved: moneyDto,
        balances: dto.Balances(
          availableAmount: 50,
          holdAmount: 50,
          feesBalance: 50,
          principalBalance: 50,
          interestBalance: 50,
        ),
      );

      when(() => mockCommonCreditMapper.mapToMoney(moneyDto))
          .thenReturn(moneyDomain);

      final result = mapper.mapRepaymentEvaluation(dtoEvaluation);

      expect(result.feePerDay, moneyDomain);
      expect(result.totalFees, moneyDomain);
      expect(result.feeSaved, moneyDomain);
      expect(
        result.creditAccountBalances,
        Balances(
          availableAmount: moneyDomain,
          holdAmount: moneyDomain,
          feesBalance: moneyDomain,
          principalBalance: moneyDomain,
          interestBalance: moneyDomain,
        ),
      );
    });

    test('mapEasyCashLimit should map correctly', () {
      const money = dto.Money(amount: 50, currency: dto.Currency.aed);
      final swaggerAccount = CreditImplStubs.creditAccountSwagger;
      final dtoLimit = dto.EasyCashLimit(
        easyCashPercentageLimit: 80,
        easyCashInterestRate: 3.5,
        easyCashAvailableLimit: money,
        easyCashLimit: money,
        dueDate: DateTime(2024, 12, 31),
        creditArrangement: dto.CreditArrangement(accounts: [swaggerAccount]),
      );

      when(
        () => mockCommonCreditMapper.mapToLoanProductIdentifier(
          swaggerAccount.productType,
        ),
      ).thenReturn(LoanProductIdentifier.smeCreditCard);

      when(() => mockCommonCreditMapper.mapToMoney(money))
          .thenReturn(Money.fromNumWithCurrency(50, Currency.aed));

      when(
        () => mockCreditAccountMapper.mapToDomainAccount(
          swaggerAccount,
          LoanProductIdentifier.smeCreditCard,
        ),
      ).thenReturn(CreditImplStubs.creditAccount);

      final result = mapper.mapEasyCashLimit(dtoLimit);

      expect(result.easyCashPercentageLimit, 80);
      expect(result.easyCashInterestRate, 3.5);
      expect(
        result.easyCashAvailableLimit,
        Money.fromNumWithCurrency(50, Currency.aed),
      );
      expect(result.easyCashLimit, Money.fromNumWithCurrency(50, Currency.aed));
      expect(result.dueDate, DateTime(2024, 12, 31));
      expect(result.creditArrangement.accounts, isNotEmpty);
      expect(
        result.creditArrangement.accounts?.first,
        CreditImplStubs.creditAccount,
      );
    });

    test('mapToEasyCashProductInfo should handle null gracefully', () {
      const money = dto.Money(amount: 50, currency: dto.Currency.aed);
      const dtoInfo = dto.StaticDetails(
        feeFreePeriod: 30,
        cashbackPercentage: 5.0,
        lateFee: money,
        feeInterestPerDay: 0.05,
        dueDays: 15,
        lateFeeGracePeriod: 5,
        fileTypes: [],
        maxSize: 256,
        revolvingFeePercentage: 5,
        minimumPrincipalRepaymentPercentage: 5,
        minimumCreditLimitAmountOnApproval: money,
        vatTemplateUrl: 'vatTemplateUrl',
      );

      when(() => mockCommonCreditMapper.mapToMoney(money))
          .thenReturn(Money.fromIntWithCurrency(50, Currency.aed));

      final result = mapper.mapToEasyCashProductInfo(dtoInfo);

      expect(result.feeFreePeriod, 30);
      expect(result.cashbackPercentage, 5.0);
      expect(result.lateFee, Money.fromIntWithCurrency(50, Currency.aed));
      expect(result.feeInterestPerDay, 0.05);
      expect(result.dueDays, 15);
      expect(result.lateFeeGracePeriod, 5);
    });

    test('Error handling works correctly', () {
      const money = dto.Money(amount: 50, currency: dto.Currency.aed);
      const dtoEvaluation = dto.EasyCashDisbursementEvaluation(
        feePerDay: money,
        totalFees: money,
        finalTotalFees: money,
        currentEstimatedTotalFees: money,
        currentFeePerDay: money,
      );

      when(() => mockCommonCreditMapper.mapToMoney(money))
          .thenThrow(Exception('exception'));

      expect(
        () => mapper.mapDisbursementEvaluation(dtoEvaluation),
        throwsException,
      );
    });
  });
}
