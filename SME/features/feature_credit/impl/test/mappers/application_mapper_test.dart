import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_credit_api/credit_api.dart';
import 'package:wio_feature_credit_impl/src/data/models/schema_dtos/schema.swagger.dart'
    as swagger;
import 'package:wio_feature_credit_impl/src/mappers/common_credit_mapper.dart';
import 'package:wio_feature_credit_impl/src/mappers/credit_application_mapper.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

import '../mocks.dart';
import '../stub_data.dart';

void main() {
  late CreditApplicationMapper mapper;
  late ErrorReporter errorReporter;
  late CommonCreditMapper commonCreditMapper;

  setUp(() {
    commonCreditMapper = MockCommonCreditMapper();
    errorReporter = MockErrorReporter();
    mapper = CreditApplicationMapperImpl(
      errorReporter: errorReporter,
      commonCreditMapper: commonCreditMapper,
    );

    registerFallbacks();
  });

  group('mapToDomainApplication tests', () {
    test(' => should return a CreditApplication', () {
      // arrange
      const expected = CreditImplStubs.creditApplicationDomain;

      // act
      final actual =
          mapper.mapToDomainApplication(CreditImplStubs.applicationDto);

      // assert
      expect(actual, expected);
    });

    test(' => should return a CreditApplication with CreditDescisionResults',
        () {
      // arrange
      final dto = CreditImplStubs.applicationDto.copyWith(
        uiStatus: swagger.ApplicationUiStatus.confirmAutodebit,
        creditDecisionResults: const swagger.CreditDecisionResults(
          creditDecisionStatus:
              swagger.CreditDecisionResultsCreditDecisionStatus.approved,
          results: [
            swagger.CreditDecisionResultV2(
              productType: swagger.ProductType.smeReceivableFinance,
              verificationStatus:
                  swagger.CreditDecisionResultV2VerificationStatus.required,
              approvedAmount: 100,
              creditAcceptanceStatus: swagger
                  .CreditDecisionResultV2CreditAcceptanceStatus.completed,
            ),
          ],
        ),
      );
      final expected = CreditImplStubs.creditApplicationDomain.copyWith(
        stage: const ConfirmAutodebit(),
        creditDecisions: CreditDecisions(
          status: CreditDecisionStatus.approved,
          decisions: [
            CreditDecision(
              approvedAmount: Money.fromNumWithCurrency(100, Currency.aed),
              productType: SmeCreditProductType.pos,
              verificationStatus: VerificationStatus.required,
              creditAcceptanceStatus: CreditAcceptanceStatus.completed,
            ),
          ],
        ),
      );

      // act
      final actual = mapper.mapToDomainApplication(dto);

      // assert
      expect(actual, expected);
    });
  });

  group('_mapToCreditApplicationStage', () {
    <swagger.ApplicationUiStatus, CreditApplicationStage>{
      swagger.ApplicationUiStatus.vatReportingMethodInput:
          const VatStage(vatSubStage: VatSubStage.vatReportingIntervalInput),
      swagger.ApplicationUiStatus.vatStatementsUpload:
          const VatStage(vatSubStage: VatSubStage.vatStatementsUpload),
      swagger.ApplicationUiStatus.vatNonRegistrationReasonInput: const VatStage(
        vatSubStage: VatSubStage.vatNonRegistrationReasonInput,
      ),
      swagger.ApplicationUiStatus.annualTurnoverInput:
          const AnnualTurnoverInput(),
      swagger.ApplicationUiStatus.bankAccountsInput: const BankAccountsInput(),
      swagger.ApplicationUiStatus.creditDecisionInReview:
          const CreditDecisionInReview(),
      swagger.ApplicationUiStatus.monthlyRepaymentPercentageInput:
          const MonthlyRepaymentPercentageInput(),
      swagger.ApplicationUiStatus.monthlyRepaymentDayInput:
          const MonthlyRepaymentDayInput(),
      swagger.ApplicationUiStatus.borrowAgreementSigning:
          const BorrowAgreementSigning(),
      swagger.ApplicationUiStatus.applicationCompleted:
          const ApplicationCompleted(),
    }.forEach((key, value) {
      test('should return $value', () {
        // arrange
        final expected = CreditImplStubs.creditApplicationDomain
            .copyWith(stage: value)
            .stage;

        // act
        final actual = mapper
            .mapToDomainApplication(
              CreditImplStubs.applicationDto.copyWith(uiStatus: key),
            )
            .stage;

        // assert
        expect(actual, expected);
      });
    });
  });

  group('documentMapperTest', () {
    test('map when documents is null', () {
      //   arrange
      final expected = <CreditDocument>[];

      final applicationDto = CreditImplStubs.applicationDto.copyWith();

      //   act
      final actual = mapper.mapToDomainApplication(applicationDto).documents;
      //   assert
      expect(actual, expected);
    });

    test('map when documents is non null and non empty', () {
      //   arrange
      final expected = <CreditDocument>[
        CreditDocument(
          id: 'id1',
          documentType: CreditDocumentType.vatStatement,
          contentType: 'application/pdf',
          fileName: 'file1',
          uploadedAt: DateTime(2023),
        ),
        CreditDocument(
          id: 'id2',
          documentType: CreditDocumentType.vatStatement,
          contentType: 'application/pdf',
          fileName: 'file2',
          uploadedAt: DateTime(2023),
        ),
      ];

      final applicationDto = CreditImplStubs.applicationDto.copyWith(
        documents: [
          swagger.DocumentMetadataResponse(
            id: 'id1',
            documentType: swagger.DocumentType.vatStatement,
            contentType: 'application/pdf',
            uploadedAt: DateTime(2023),
            filename: 'file1',
          ),
          swagger.DocumentMetadataResponse(
            id: 'id2',
            filename: 'file2',
            documentType: swagger.DocumentType.vatStatement,
            contentType: 'application/pdf',
            uploadedAt: DateTime(2023),
          ),
        ],
      );

      //   act
      final actual = mapper.mapToDomainApplication(applicationDto).documents;
      //   assert
      expect(actual, expected);
    });
  });

  group('_mapToCreditDecision tests', () {
    test('should return not started when null', () {
      // arrange
      final expected = CreditImplStubs.creditApplicationDomain
          .copyWith(
            creditDecisionResult: const CreditDecisionResult.notStarted(),
          )
          .creditDecisionResult;

      // act
      final actual = mapper
          .mapToDomainApplication(
            CreditImplStubs.applicationDto,
          )
          .creditDecisionResult;

      // assert
      expect(actual, expected);
    });

    test('should return not started when credit decision status is null', () {
      // arrange
      final expected = CreditImplStubs.creditApplicationDomain
          .copyWith(
            creditDecisionResult: const CreditDecisionResult.notStarted(),
          )
          .creditDecisionResult;

      // act
      final actual = mapper
          .mapToDomainApplication(
            CreditImplStubs.applicationDto.copyWith(
              creditDecisionResult: const swagger.CreditDecisionResult(),
            ),
          )
          .creditDecisionResult;

      // assert
      expect(actual, expected);
    });

    test(
      'should return not started when credit decision status is not started',
      () {
        // arrange
        final expected = CreditImplStubs.creditApplicationDomain
            .copyWith(
              creditDecisionResult: const CreditDecisionResult.notStarted(),
            )
            .creditDecisionResult;

        // act
        final actual = mapper
            .mapToDomainApplication(
              CreditImplStubs.applicationDto.copyWith(
                creditDecisionResult: const swagger.CreditDecisionResult(
                  creditDecisionStatus: swagger
                      .CreditDecisionResultCreditDecisionStatus.notStarted,
                ),
              ),
            )
            .creditDecisionResult;

        // assert
        expect(actual, expected);
      },
    );

    test('should return a approved credit decision', () {
      // arrange
      final expected = CreditImplStubs.creditApplicationDomain
          .copyWith(
            creditDecisionResult: CreditDecisionResult.approved(
              approvedAmount: Money.fromNumWithCurrency(1234, Currency.aed),
              interestRate: 1.1,
            ),
          )
          .creditDecisionResult;

      // act
      final actual = mapper
          .mapToDomainApplication(
            CreditImplStubs.applicationDto.copyWith(
              creditDecisionResult: const swagger.CreditDecisionResult(
                approvedAmount: 1234,
                interestRate: 1.1,
                creditDecisionStatus:
                    swagger.CreditDecisionResultCreditDecisionStatus.approved,
              ),
            ),
          )
          .creditDecisionResult;

      // assert
      expect(actual, expected);
    });

    test('should return a rejected credit decision', () {
      // arrange
      final expected = CreditImplStubs.creditApplicationDomain
          .copyWith(creditDecisionResult: const CreditDecisionResult.rejected())
          .creditDecisionResult;

      // act
      final actual = mapper
          .mapToDomainApplication(
            CreditImplStubs.applicationDto.copyWith(
              creditDecisionResult: const swagger.CreditDecisionResult(
                creditDecisionStatus:
                    swagger.CreditDecisionResultCreditDecisionStatus.rejected,
              ),
            ),
          )
          .creditDecisionResult;

      // assert
      expect(actual, expected);
    });

    test('should return a in progress credit decision', () {
      // arrange
      final expected = CreditImplStubs.creditApplicationDomain
          .copyWith(
            creditDecisionResult: const CreditDecisionResult.inProgress(),
          )
          .creditDecisionResult;

      // act
      final actual = mapper
          .mapToDomainApplication(
            CreditImplStubs.applicationDto.copyWith(
              creditDecisionResult: const swagger.CreditDecisionResult(
                creditDecisionStatus:
                    swagger.CreditDecisionResultCreditDecisionStatus.inProgress,
              ),
            ),
          )
          .creditDecisionResult;

      // assert
      expect(actual, expected);
    });
  });

  group('_mapToApplicationInputData test', () {
    test('No null fields', () {
      //   arrange
      final expected = CreditApplicationInputData(
        annualTurnover: Money.fromNumWithCurrency(1234, Currency.aed),
        vatReportingInterval: VatReportingInterval.annually,
        ibans: ['123', '456'].map((ibanStr) => Iban(iban: ibanStr)).toList(),
        monthlyPaymentPercentage: 75,
        monthlyRepaymentDay: 1,
      );

      when(() => commonCreditMapper.mapToMoney(any()))
          .thenReturn(Money.fromNumWithCurrency(1234, Currency.aed));

      //   act
      final actual = mapper
          .mapToDomainApplication(
            CreditImplStubs.applicationDto.copyWith(
              inputData: swagger.ApplicationInputDataRequest(
                annualTurnover: const swagger.Money(
                  amount: 1234,
                  currency: swagger.Currency.aed,
                ),
                vatReportingMethod: swagger
                    .ApplicationInputDataRequestVatReportingMethod.annual,
                ibans: [
                  '123',
                  '456',
                ].map((iban) => swagger.Iban(iban: iban)).toList(),
                monthlyPaymentPercentage: 75,
                monthlyRepaymentDay: 1,
              ),
            ),
          )
          .inputData;

      //   assert
      expect(actual, expected);
    });
  });

  group('_mapToCreditApplicationStatus test', () {
    <swagger.ApplicationStatus, CreditApplicationStatus>{
      swagger.ApplicationStatus.inProgress: CreditApplicationStatus.inProgress,
      swagger.ApplicationStatus.inReview: CreditApplicationStatus.inReview,
      swagger.ApplicationStatus.completed: CreditApplicationStatus.completed,
      swagger.ApplicationStatus.rejected: CreditApplicationStatus.rejected,
      swagger.ApplicationStatus.cancelled: CreditApplicationStatus.cancelled,
    }.forEach((key, value) {
      test('should return $value', () {
        //   arrange
        final expected = value;

        //   act
        final actual = mapper
            .mapToDomainApplication(
              CreditImplStubs.applicationDto.copyWith(status: key),
            )
            .status;

        //   assert
        expect(actual, expected);
      });
    });

    test('No null fields', () {
      //   arrange
      final expected = CreditApplicationInputData(
        annualTurnover: Money.fromNumWithCurrency(1234, Currency.aed),
        vatReportingInterval: VatReportingInterval.annually,
        ibans: ['123', '456'].map((ibanStr) => Iban(iban: ibanStr)).toList(),
        monthlyPaymentPercentage: 75,
      );

      when(() => commonCreditMapper.mapToMoney(any()))
          .thenReturn(Money.fromNumWithCurrency(1234, Currency.aed));

      //   act
      final actual = mapper
          .mapToDomainApplication(
            CreditImplStubs.applicationDto.copyWith(
              inputData: swagger.ApplicationInputDataRequest(
                annualTurnover: const swagger.Money(
                  amount: 1234,
                  currency: swagger.Currency.aed,
                ),
                vatReportingMethod: swagger
                    .ApplicationInputDataRequestVatReportingMethod.annual,
                ibans: [
                  '123',
                  '456',
                ].map((iban) => swagger.Iban(iban: iban)).toList(),
                monthlyPaymentPercentage: 75,
              ),
            ),
          )
          .inputData;

      //   assert
      expect(actual, expected);
    });
  });

  group('mapToProductTypeDto tests =>', () {
    test('should return a ProductType', () {
      // arrange
      const expected = swagger.ProductType.smeCreditCard;

      // act
      final actual =
          mapper.mapToProductTypeDto(SmeCreditProductType.creditCard);

      // assert
      expect(actual, expected);
    });
  });

  group('mapToApplicationInputData test =>', () {
    test('No null fields', () {
      //   arrange
      final expected = swagger.ApplicationInputDataRequest(
        annualTurnover: const swagger.Money(
          amount: 1000000,
          currency: swagger.Currency.aed,
        ),
        vatReportingMethod:
            swagger.ApplicationInputDataRequestVatReportingMethod.annual,
        ibans: [
          '123',
          '456',
        ].map((iban) => swagger.Iban(iban: iban)).toList(),
        monthlyPaymentPercentage: 75,
        monthlyRepaymentDay: 3,
        uiStatus: swagger.ApplicationUiStatus.vatStatementsUpload,
        isAutopayFromSavingSpace: true,
      );

      //   act
      final actual = mapper.mapToApplicationInputDataFromDomain(
        updatedApplication: CreditApplicationInputData(
          annualTurnover: Money.fromNumWithCurrency(1000000, Currency.aed),
          vatReportingInterval: VatReportingInterval.annually,
          ibans: ['123', '456'].map((ibanStr) => Iban(iban: ibanStr)).toList(),
          monthlyPaymentPercentage: 75,
          isAutopayFromSavingsSpaceEnabled: true,
          monthlyRepaymentDay: 3,
        ),
        nextStage: const VatStage(vatSubStage: VatSubStage.vatStatementsUpload),
      );
      //   assert

      expect(actual.annualTurnover?.amount, expected.annualTurnover?.amount);
      expect(
        actual.annualTurnover?.currency,
        expected.annualTurnover?.currency,
      );
      expect(actual.vatReportingMethod, expected.vatReportingMethod);
      expect(
        actual.monthlyPaymentPercentage,
        expected.monthlyPaymentPercentage,
      );
      expect(actual.monthlyRepaymentDay, expected.monthlyRepaymentDay);
      expect(actual.uiStatus, expected.uiStatus);
    });
  });

  group('mapToDocumentTypeDto tests =>', () {
    final testCases = {
      CreditDocumentType.creditAgreement: swagger.DocumentType.creditAgreement,
      CreditDocumentType.vatStatement: swagger.DocumentType.vatStatement,
      CreditDocumentType.keyFactStatement:
          swagger.DocumentType.keyFactStatement,
    };

    for (final entry in testCases.entries) {
      test('should return ${entry.value}', () {
        // arrange
        final expected = entry.value;

        // act
        final actual = mapper.mapToDocumentTypeDto(entry.key);

        // assert
        expect(actual, expected);
      });
    }
  });

  group('mapToDocumentDomain tests =>', () {
    test('should return a CreditDocument', () {
      // arrange
      final expected = CreditImplStubs.creditDocument;

      // act
      final actual = mapper.mapToDocumentDomain(
        CreditImplStubs.documentUploadResponseDto,
      );

      // assert
      expect(actual, expected);
    });
  });

  group('mapToFileData test =>', () {
    test('should return a FileData', () {
      //   arrange
      final expected = FileData(0);

      //   act
      final actual =
          mapper.mapToFileData(const swagger.GetCreditAgreementResponse());

      //   assert
      expect(actual, expected);
    });
  });

  group('mapToGetApplicationsStatus test', () {
    final tests = <CreditApplicationStatus, swagger.ApplicationStatus>{
      CreditApplicationStatus.inProgress: swagger.ApplicationStatus.inProgress,
      CreditApplicationStatus.inReview: swagger.ApplicationStatus.inReview,
      CreditApplicationStatus.completed: swagger.ApplicationStatus.completed,
      CreditApplicationStatus.rejected: swagger.ApplicationStatus.rejected,
      CreditApplicationStatus.cancelled: swagger.ApplicationStatus.cancelled,
      CreditApplicationStatus.expired: swagger.ApplicationStatus.expired,
    };

    for (final testCase in tests.entries) {
      final input = testCase.key;
      final expected = testCase.value;
      test('should return a $expected when domain value is $input', () {
        // act
        final actual = mapper.mapToGetApplicationsStatus(input);

        // assert
        expect(actual, expected);
      });
    }
  });

  group('_mapToCreditDocumentType tests', () {
    final tests = <swagger.DocumentType, CreditDocumentType>{
      swagger.DocumentType.creditAgreement: CreditDocumentType.creditAgreement,
      swagger.DocumentType.vatStatement: CreditDocumentType.vatStatement,
      swagger.DocumentType.keyFactStatement:
          CreditDocumentType.keyFactStatement,
      swagger.DocumentType.lendingTermsAndConditionsBl:
          CreditDocumentType.businessLoanTermsAndConditions,
      swagger.DocumentType.lendingKeyFactStatementBl:
          CreditDocumentType.businessLoanKeyFactStatement,
    };

    for (final testCase in tests.entries) {
      final input = testCase.key;

      test('should return a CreditDocument with type as ${testCase.value}', () {
        // arrange
        final expected = CreditImplStubs.creditDocument
            .copyWith(documentType: testCase.value);

        // act
        final actual = mapper.mapToDocumentDomain(
          CreditImplStubs.documentUploadResponseDto.copyWith(
            documentType: input,
          ),
        );

        // assert
        expect(actual.documentType, expected.documentType);
      });
    }

    test('should throw an exception when unknown value is passed', () {
      // act && assert
      expect(
        () => mapper.mapToDocumentDomain(
          CreditImplStubs.documentUploadResponseDto.copyWith(
            documentType: swagger.DocumentType.swaggerGeneratedUnknown,
          ),
        ),
        throwsA(isA<Exception>()),
      );
    });
  });

  group(
    'test product info mapping',
    () {
      test(
        'should return product info',
        () {
          final expected = CreditImplStubs.creditProductInfo;
          const swaggerStub = CreditImplStubs.creditProductInfoSwagger;

          when(() => commonCreditMapper.mapToMoney(swaggerStub.lateFee))
              .thenReturn(expected.lateFee);
          when(
            () => commonCreditMapper.mapToMoney(
              swaggerStub.minimumCreditLimitAmountOnApproval,
            ),
          ).thenReturn(expected.minimumCreditLimitAmountOnApproval);
          when(
            () => commonCreditMapper.mapToMoney(
              swaggerStub.amountRange!.min,
            ),
          ).thenReturn(expected.amountRange!.minimum);
          when(
            () => commonCreditMapper.mapToMoney(
              swaggerStub.amountRange!.max,
            ),
          ).thenReturn(expected.amountRange!.maximum);

          final actual = mapper.mapToProductInfoDomain(
            swaggerStub,
            SmeCreditProductType.creditCard,
          );

          expect(actual, expected);
        },
      );

      test(
        'should return pos product info',
        () {
          final expected = CreditImplStubs.posProductInfo;
          const swaggerStub = CreditImplStubs.posProductInfoSwagger;

          when(() => commonCreditMapper.mapToMoney(swaggerStub.lateFee))
              .thenReturn(expected.lateFee);

          when(() => commonCreditMapper.mapToMoney(swaggerStub.processingFee!))
              .thenReturn(
            expected.map(
              pos: (value) => value.processingFee,
              creditCard: (value) => Money.fromNumWithCurrency(0, Currency.aed),
            ),
          );

          when(
            () => commonCreditMapper.mapToMoney(
              swaggerStub.minimumCreditLimitAmountOnApproval,
            ),
          ).thenReturn(expected.minimumCreditLimitAmountOnApproval);

          final actual = mapper.mapToProductInfoDomain(
            swaggerStub,
            SmeCreditProductType.pos,
          );

          expect(actual, expected);
        },
      );

      test(
        'should return product info with minimumDisbursementAmount',
        () {
          final expected = CreditImplStubs.creditProductInfo.copyWith(
            minimumDisbursementAmount:
                Money.fromNumWithCurrency(10, Currency.aed),
          );
          final swaggerStub = CreditImplStubs.creditProductInfoSwagger.copyWith(
            minimumDisbursementAmount: const swagger.Money(
              amount: 10,
              currency: swagger.Currency.aed,
            ),
          );

          when(() => commonCreditMapper.mapToMoney(swaggerStub.lateFee))
              .thenReturn(expected.lateFee);
          when(
            () => commonCreditMapper.mapToMoney(
              swaggerStub.minimumCreditLimitAmountOnApproval,
            ),
          ).thenReturn(expected.minimumCreditLimitAmountOnApproval);
          when(
            () => commonCreditMapper.mapToMoney(
              swaggerStub.minimumDisbursementAmount!,
            ),
          ).thenReturn(expected.minimumDisbursementAmount!);
          when(
            () => commonCreditMapper.mapToMoney(
              swaggerStub.amountRange!.min,
            ),
          ).thenReturn(expected.amountRange!.minimum);

          when(
            () => commonCreditMapper.mapToMoney(
              swaggerStub.amountRange!.max,
            ),
          ).thenReturn(expected.amountRange!.maximum);

          final actual = mapper.mapToProductInfoDomain(
            swaggerStub,
            SmeCreditProductType.creditCard,
          );

          expect(actual, expected);
        },
      );
    },
  );

  test('mapToLocalizedDocumentLinkDto test', () {
    // arrange
    const links = LocalizedDocumentLinks(enUrl: 'en', arUrl: 'ar');
    const expected = swagger.LocalizedDocumentLink(
      en: 'en',
      ar: 'ar',
    );

    // act
    final actual = mapper.mapToLocalizedDocumentLinkDto(links);

    // assert
    expect(actual.en, expected.en);
    expect(actual.ar, expected.ar);
  });

  test('map to product preferences', () {
    // arrange
    final dto = CreditImplStubs.applicationDto.copyWith(
      uiStatus: swagger.ApplicationUiStatus.confirmAutodebit,
      inputData: const swagger.ApplicationInputDataRequest(
        productPreferences: [
          swagger.ProductPreferences(
            isAutopayFromSavingSpace: true,
            settlementAccountId: 'id',
          ),
        ],
      ),
    );
    final expected = CreditImplStubs.creditApplicationDomain.copyWith(
      stage: const ConfirmAutodebit(),
      inputData: const CreditApplicationInputData(
        productPreferences: [
          ProductPreferences(
            isAutopayFromSavingSpacesEnabled: true,
            settlementAccountId: 'id',
          ),
        ],
      ),
    );

    // act
    final result = mapper.mapToDomainApplication(dto);

    // assert
    expect(
      result.inputData.productPreferences,
      expected.inputData.productPreferences,
    );
  });

  test('map to vat non registration reason', () {
    // arrange
    const dto = [
      swagger.NonRegisteredVatReason(
        ar: 'ar',
        en: 'en',
        type: swagger.NonRegisteredVatReasonType.businessExemptedActivities,
      ),
    ];
    const expected = [
      VatNonRegistrationReasonDetails(
        type: VatNonRegistrationReason.businessExemptedActivities,
        en: 'en',
        ar: 'ar',
      ),
    ];

    // act
    final result = mapper.mapToReasonDescription(dto);

    // assert
    expect(result, expected);
  });

  group('mapToCreditOffer tests =>', () {
    test('should return a CreditOffer', () {
      // arrange
      final expected = CreditImplStubs.creditCardOffer;

      when(() => commonCreditMapper.mapToMoney(any()))
          .thenReturn(Money.fromNumWithCurrency(1000000, Currency.aed));

      // act
      final actual = mapper.mapToCreditOffer(
        const swagger.PreQualifiedOffer(
          money: swagger.Money(
            amount: 1000000,
            currency: swagger.Currency.aed,
          ),
          productType: swagger.ProductType.smeCreditCard,
        ),
        SmeCreditProductType.creditCard,
      );

      // assert
      expect(actual, expected);
    });
  });

  group('mapToCreditIneligibilityDetails tests =>', () {
    test('should return null when dto is null', () {
      // act
      final actual = mapper.mapToCreditIneligibilityDetails(null);

      // assert
      expect(actual, isNull);
    });

    test('should return CreditIneligibilityDetails when dto is provided', () {
      // arrange
      const expected = CreditIneligibiltyDetails(
        title: 'description',
        description: 'subDescription',
        isUpdateDocument: true,
        reason: CreditIneligibiltyReason.documentExpired,
      );

      // act
      final actual = mapper.mapToCreditIneligibilityDetails(
        const swagger.NonEligibilityDetails(
          description: 'description',
          subDescription: 'subDescription',
          isUpdateDocument: true,
          reason: swagger.NonEligibilityDetailsReason.documentExpired,
        ),
      );

      // assert
      expect(actual, expected);
    });

    test('should return null when reason mapping fails', () {
      // act
      final actual = mapper.mapToCreditIneligibilityDetails(
        const swagger.NonEligibilityDetails(
          description: 'description',
          subDescription: 'subDescription',
          isUpdateDocument: true,
          reason: swagger.NonEligibilityDetailsReason.swaggerGeneratedUnknown,
        ),
      );

      // assert
      expect(actual, isNull);
    });
  });
}
