import 'package:bloc_test/bloc_test.dart';
import 'package:common_feature_fx_api/navigation/fx_account_selection_bottom_sheet_config.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_fx_ui/src/bottom_sheet/account_selection/fx_account_selection_cubit.dart';
import 'package:wio_feature_fx_ui/src/bottom_sheet/account_selection/fx_account_selection_state.dart';

import '../test_entities.dart';

void main() {
  late FXAccountSelectionCubit fxAccountSelectionCubit;
  late MockNavigationProvider mockNavigationProvider;

  setUp(() {
    mockNavigationProvider = MockNavigationProvider();
    fxAccountSelectionCubit = FXAccountSelectionCubit(
      navigationProvider: mockNavigationProvider,
    );
  });

  tearDown(() {
    fxAccountSelectionCubit.close();
  });

  group('FXAccountSelectionCubit', () {
    test('initial state should be idle', () {
      expect(
        fxAccountSelectionCubit.state,
        equals(const FXAccountSelectionState.idle()),
      );
    });

    blocTest<FXAccountSelectionCubit, FXAccountSelectionState>(
      'initialize emits idle state with accounts'
      ' and selectedAccountId when valid',
      build: () => fxAccountSelectionCubit,
      act: (cubit) => cubit.initialize(
        accounts: [
          TestEntities.randAccount(currency: Currency.aed, id: 'aed'),
          TestEntities.randAccount(currency: Currency.usd, id: 'usd'),
        ],
        selectedAccountId: 'aed',
      ),
      expect: () => [
        FXAccountSelectionState.idle(
          accounts: [
            TestEntities.randAccount(currency: Currency.aed, id: 'aed'),
            TestEntities.randAccount(currency: Currency.usd, id: 'usd'),
          ],
          selectedAccountId: 'aed',
        ),
      ],
    );

    blocTest<FXAccountSelectionCubit, FXAccountSelectionState>(
      'initialize emits idle state with null '
      'selectedAccountId if not found in accounts',
      build: () => fxAccountSelectionCubit,
      act: (cubit) => cubit.initialize(
        accounts: [
          TestEntities.randAccount(currency: Currency.aed),
          TestEntities.randAccount(currency: Currency.usd),
        ],
        selectedAccountId: '3',
      ),
      expect: () => [
        FXAccountSelectionState.idle(
          accounts: [
            TestEntities.randAccount(currency: Currency.aed),
            TestEntities.randAccount(currency: Currency.usd),
          ],
        ),
      ],
    );

    blocTest<FXAccountSelectionCubit, FXAccountSelectionState>(
      'selectAccount calls goBack with FXAccountSelectionResult'
      ' containing selected account',
      build: () => fxAccountSelectionCubit,
      act: (cubit) => cubit.selectAccount(
        TestEntities.randAccount(currency: Currency.aed),
      ),
      verify: (_) {
        verify(
          () => mockNavigationProvider.goBack(
            FXAccountSelectionResult(
              TestEntities.randAccount(currency: Currency.aed),
            ),
          ),
        ).called(1);
      },
    );
  });
}
