import 'package:mocktail/mocktail.dart';
import 'package:sme_graphql_api/graphql_api.dart' as sme_graphql;
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_cash_api/feature_cash_api.dart';
import 'package:wio_feature_cash_impl/src/data/repositories/lulu_mapper.dart';

// Mocking the necessary GraphQL data classes
class MockActiveCashBeneficiary extends Mock
    implements
        sme_graphql
        .GetAuthorizedCashBeneficiaries$Query$ActiveCashBeneficiary {}

class MockCashBeneficiaryResponse extends Mock
    implements
        sme_graphql.AddCashBeneficiary$Mutation$CashBeneficiaryResponse {}

class MockCashDepositBranch extends Mock
    implements sme_graphql.GetLuLuDepositLocations$Query$CashDepositBranch {}

class MockEstimation extends Mock
    implements sme_graphql.GetEstimateCharge$Query$Estimation {}

void main() {
  late LuluMapper mapper;

  setUp(() {
    mapper = const LuluMapper();
  });

  test('mapToAuthorizedBeneficiary correctly maps data', () {
    final mockBeneficiary = MockActiveCashBeneficiary();
    when(() => mockBeneficiary.beneficiaryId).thenReturn('123');
    when(() => mockBeneficiary.beneficiaryName).thenReturn('John Doe');
    when(() => mockBeneficiary.governmentId).thenReturn('gov123');
    when(() => mockBeneficiary.autoCreated).thenReturn(true);

    final result = mapper.mapToAuthorizedBeneficiary(mockBeneficiary);

    expect(result.beneficiaryId, '123');
    expect(result.beneficiaryName, 'John Doe');
    expect(result.governmentId, 'gov123');
    expect(result.isAutoCreated, true);
  });

  test('mapToCashBeneficiaryInputDto correctly maps data', () {
    const input = CashBeneficiaryInput(
      beneficiaryName: 'Jane Doe',
      governmentId: 'gov456',
      accountId: 'acc789',
    );

    final result = mapper.mapToCashBeneficiaryInputDto(input);

    expect(result.type, sme_graphql.CashBeneficiaryType.depositor);
    expect(result.beneficiaryName, 'Jane Doe');
    expect(result.governmentId, 'gov456');
    expect(result.accountId, 'acc789');
  });

  test('mapToCashBeneficiaryRecord correctly maps data', () {
    final mockResponse = MockCashBeneficiaryResponse();
    when(() => mockResponse.beneficiaryId).thenReturn('321');
    when(() => mockResponse.beneficiaryName).thenReturn('Alice Smith');

    final result = mapper.mapToCashBeneficiaryRecord(mockResponse);

    expect(result.beneficiaryId, '321');
    expect(result.beneficiaryName, 'Alice Smith');
  });

  test('mapToLuLuBranch correctly maps data', () {
    final mockBranch = MockCashDepositBranch();
    when(() => mockBranch.branchName).thenReturn('Main Branch');
    when(() => mockBranch.branchId).thenReturn('branch321');
    when(() => mockBranch.branchAddress).thenReturn('321 Main St');
    when(() => mockBranch.active).thenReturn(true);
    when(() => mockBranch.branchLocation).thenReturn('Location X');
    when(() => mockBranch.coordinatesXLocation).thenReturn(24.4539);
    when(() => mockBranch.coordinatesYLocation).thenReturn(54.3773);

    final result = mapper.mapToLuLuBranch(mockBranch);

    expect(result.branchName, 'Main Branch');
    expect(result.branchId, 'branch321');
    expect(result.branchAddress, '321 Main St');
    expect(result.isActive, true);
    expect(result.branchLocation, 'Location X');
    expect(result.latitude, 24.4539);
    expect(result.longitude, 54.3773);
  });

  test('mapToEstimationFee correctly maps data or returns default', () {
    final mockEstimation = MockEstimation();
    when(() => mockEstimation.id).thenReturn('est321');
    when(() => mockEstimation.amount).thenReturn(100.0);

    final result = mapper.mapToEstimationFee(mockEstimation);

    expect(result.id, 'est321');
    expect(result.amount.toString(), '100.00');
    expect(result.amount.currency, Currency.aed);

    // Test default case
    final resultDefault = mapper.mapToEstimationFee(null);
    expect(resultDefault.id, '');
    expect(resultDefault.amount.toString(), '0.00');
    expect(resultDefault.amount.currency, Currency.aed);
  });
}
