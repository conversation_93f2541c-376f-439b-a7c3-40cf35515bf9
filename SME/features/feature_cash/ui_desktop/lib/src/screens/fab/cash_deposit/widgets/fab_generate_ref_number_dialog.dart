import 'package:flutter/material.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_feature_cash_api/feature_cash_api.dart';
import 'package:wio_feature_cash_ui_desktop/feature_cash_ui_desktop.dart';
import 'package:wio_feature_cash_ui_desktop/src/screens/fab/cash_deposit/cubit/fab_deposit_cubit.dart';
import 'package:wio_feature_cash_ui_desktop/src/screens/fab/cash_deposit/cubit/fab_deposit_state.dart';

class FabGenerateRefNumberDialog extends StatelessWidget {
  final FabDepositCubit cubit;
  final CashBeneficiary beneficiary;

  const FabGenerateRefNumberDialog({
    Key? key,
    required this.cubit,
    required this.beneficiary,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final localizations = CashUiLocalizations.of(context);

    return WillPopScope(
      onWillPop: () async => false,
      child: BlocBuilder<FabDepositCubit, FabDepositState>(
        bloc: cubit,
        builder: (context, state) {
          final isLoading = state.maybeMap(
            orElse: () => false,
            generatingReferenceNumber: (_) => true,
          );

          return ModalDialog(
            header: ModalHeader(
              prefixIcon: CompanyIconPointer.close,
              onPrefixIconClick: Navigator.of(context).pop,
            ),
            body: ModalBody(
              titleCompanyRichTextModel: CompanyRichTextModel(
                text: localizations
                    .fabCashDepositorRefDialogTitle(beneficiary.name),
                highlightedTextModels: [
                  HighlightedTextModel(beneficiary.name),
                ],
              ),
              subTitleCompanyRichTextModel: CompanyRichTextModel(
                text: localizations.fabCashDepositorRefDialogSubtitle,
                normalStyle: CompanyTextStylePointer.b1,
                normalTextColor: CompanyColorPointer.secondary3,
              ),
            ),
            footer: ModalFooter(
              leadingWidget: CtaButton.l(
                ctaButtonVariant: CtaButtonVariant.primaryType,
                onTap: () => cubit.generateReferenceNumber(beneficiary),
                title: localizations.fabCashDepositorRefDialogCtaTitle
                    .toUpperCase(),
                isLoading: isLoading,
              ),
            ),
          );
        },
      ),
    );
  }
}
