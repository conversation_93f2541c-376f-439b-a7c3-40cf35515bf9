name: wio_feature_cash_ui
description: A new package for handle mobile cash ui.
version: 0.0.1
publish_to: none
environment: 
  sdk: '>=3.6.0 <4.0.0'
  flutter: 3.27.3
dependencies: 
  di: 
    path: ../../../../core/di
  domain: 
    path: ../../../../core/domain
  flutter: 
    sdk: flutter
  flutter_bloc: 9.0.0
  freezed_annotation: 2.4.4
  logging_api: 
    path: ../../../../core/logging/api
  ui: 
    path: ../../../../core/ui
  ui_kit_legacy_core: 
    path: ../../../../ui_kit_legacy/ui_kit_core
  ui_kit_legacy_mobile: 
    path: ../../../../ui_kit_legacy/ui_kit_mobile
  widget_library: 
    path: ../../../app-mobile-core/widgets/app-mobile-widgets/src
  wio_core_navigation_api: 
    path: ../../../../core/navigation/api
  wio_core_navigation_ui: 
    path: ../../../../core/navigation/ui
  wio_feature_cash_api: 
    path: ../api
  wio_feature_cash_impl: 
    path: ../impl
  wio_feature_error_domain_api: 
    path: ../../../../common/features/feature_error/api
  wio_feature_status_view_api: 
    path: ../../../../common/features/feature_status_view/api
  wio_sme_error_handler_api: 
    path: ../../../common/feature_error_handler/api
  wio_sme_error_handler_ui: 
    path: ../../../common/feature_error_handler/ui
dev_dependencies: 
  bloc_test: 10.0.0
  build_runner: 2.4.14
  core_lints: 
    path: ../../../../tooling/core_lints
  flutter_lints: 4.0.0
  flutter_test: 
    sdk: flutter
  freezed: 2.5.7
  mocktail: 1.0.4
  tests: 
    path: ../../../../core/tests/impl
  tests_ui: 
    path: ../../../../core/tests/ui
flutter: 
  uses-material-design: true
