import 'package:currency_feature_impl/src/data/dto/currency_dto.dart';
import 'package:currency_feature_impl/src/data/dto/payment_currencies_dto.dart';
import 'package:currency_feature_impl/src/data/request/get_all_currencies_request_builder.dart';
import 'package:currency_feature_impl/src/data/request/get_popular_currencies_request_builder.dart';
import 'package:data/graph_ql_network_manager/network_manager/graph_ql_retwork_manager_facade.dart';
import 'package:sme_configuration/configuration.dart';

abstract class CurrencyDataSource {
  Future<List<CurrencyDto>> fetchPopularCurrencies();

  Future<List<CurrencyDto>> fetchAllCurrencies();

  Future<PaymentCurrenciesDto> getPaymentCurrencies();
}

class CurrencyDataSourceImpl implements CurrencyDataSource {
  final IGraphQLNetworkManagerFacade _networkManager;

  CurrencyDataSourceImpl(this._networkManager);

  @override
  Future<List<CurrencyDto>> fetchAllCurrencies() async {
    const requestBuilder = AllCurrenciesRequestBuilder();
    final response = await _networkManager.execute(
      envKey: ApiEnvironmentKey.defaultEnv,
      request: requestBuilder.buildRequest(),
    );
    return response.map(
      (json) => _getCurrencyList(json['allCurrencies']['Currencies']),
    );
  }

  @override
  Future<List<CurrencyDto>> fetchPopularCurrencies() async {
    const requestBuilder = PopularCurrenciesRequestBuilder();
    final response = await _networkManager.execute(
      envKey: ApiEnvironmentKey.defaultEnv,
      request: requestBuilder.buildRequest(),
    );
    return response.map(
      (json) => _getCurrencyList(json['preferredCurrencies']['Currencies']),
    );
  }

  List<CurrencyDto> _getCurrencyList(dynamic rawData) => List<CurrencyDto>.from(
        rawData.map(
          // ignore: unnecessary_lambdas
          (dto) => CurrencyDto.fromJson(dto),
        ),
      );

  @override
  Future<PaymentCurrenciesDto> getPaymentCurrencies() async {
    final result = await Future.wait(
      [
        fetchAllCurrencies(),
        fetchPopularCurrencies(),
      ],
    );
    return PaymentCurrenciesDto(
      allCurrencies: result.first..retainWhere((i) => i.hasLocalCurrency),
      popularCurrencies: result.last..retainWhere((i) => i.hasLocalCurrency),
    );
  }
}
