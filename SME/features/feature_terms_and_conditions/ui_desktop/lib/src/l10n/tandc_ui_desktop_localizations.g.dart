// GENERATED CODE
//
// After the template files .arb have been changed,
// generate this class by the command in the terminal:
// flutter pub run lokalise_flutter_sdk:gen-lok-l10n
//
// Please see https://pub.dev/packages/lokalise_flutter_sdk

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes
// ignore_for_file: depend_on_referenced_packages

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart';
import 'package:lokalise_flutter_sdk/lokalise_flutter_sdk.dart';
import 'intl/messages_all.dart';

class TermsAndConditionsUiLocalizations {
  TermsAndConditionsUiLocalizations._internal();

  static const LocalizationsDelegate<TermsAndConditionsUiLocalizations>
      delegate = _AppLocalizationDelegate();

  static const List<Locale> supportedLocales = [
    Locale.fromSubtags(languageCode: 'en'),
    Locale.fromSubtags(languageCode: 'ar')
  ];

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  static final Map<String, List<String>> _metadata = {
    'consentArabic': [],
    'consentEnglish': [],
    'privacyPolicyHighlightedWord2': [],
    'termsAndConditions': []
  };

  static Future<TermsAndConditionsUiLocalizations> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    Lokalise.instance.metadata = _metadata;

    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = TermsAndConditionsUiLocalizations._internal();
      return instance;
    });
  }

  static TermsAndConditionsUiLocalizations of(BuildContext context) {
    final instance = Localizations.of<TermsAndConditionsUiLocalizations>(
        context, TermsAndConditionsUiLocalizations);
    assert(instance != null,
        'No instance of TermsAndConditionsUiLocalizations present in the widget tree. Did you add TermsAndConditionsUiLocalizations.delegate in localizationsDelegates?');
    return instance!;
  }

  /// `Arabic`
  String get consentArabic {
    return Intl.message(
      'Arabic',
      name: 'consentArabic',
      desc: '',
      args: [],
    );
  }

  /// `English`
  String get consentEnglish {
    return Intl.message(
      'English',
      name: 'consentEnglish',
      desc: '',
      args: [],
    );
  }

  /// `Privacy Policy`
  String get privacyPolicyHighlightedWord2 {
    return Intl.message(
      'Privacy Policy',
      name: 'privacyPolicyHighlightedWord2',
      desc: '',
      args: [],
    );
  }

  /// `Terms & Conditions`
  String get termsAndConditions {
    return Intl.message(
      'Terms & Conditions',
      name: 'termsAndConditions',
      desc: '',
      args: [],
    );
  }
}

class _AppLocalizationDelegate
    extends LocalizationsDelegate<TermsAndConditionsUiLocalizations> {
  const _AppLocalizationDelegate();

  @override
  bool isSupported(Locale locale) =>
      TermsAndConditionsUiLocalizations.supportedLocales.any(
          (supportedLocale) =>
              supportedLocale.languageCode == locale.languageCode);

  @override
  Future<TermsAndConditionsUiLocalizations> load(Locale locale) =>
      TermsAndConditionsUiLocalizations.load(locale);

  @override
  bool shouldReload(_AppLocalizationDelegate old) => false;
}
