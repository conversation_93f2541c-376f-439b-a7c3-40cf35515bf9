import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui/extensions/locale_extension.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_feature_invitation_api/domain/models/terms_and_conditions.dart';
import 'package:wio_feature_terms_and_conditions_api/model/agreement_type.dart';
import 'package:wio_feature_terms_and_conditions_ui_desktop/feature_terms_and_conditions_ui_desktop.dart';
import 'package:wio_feature_terms_and_conditions_ui_desktop/src/screens/terms_and_conditions/terms_and_conditions_cubit.dart';
import 'package:wio_feature_terms_and_conditions_ui_desktop/src/screens/terms_and_conditions/terms_and_conditions_page.dart';
import 'package:wio_feature_terms_and_conditions_ui_desktop/src/screens/terms_and_conditions/terms_and_conditions_state.dart';

class TermsAndConditionsPageV2 extends StatelessWidget {
  final AgreementType agreementType;
  final TermsAndConditions? muTermsAndConditions;
  final bool? hasHorizontalPadding;

  const TermsAndConditionsPageV2({
    required this.agreementType,
    this.hasHorizontalPadding,
    this.muTermsAndConditions,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => DependencyProvider.get<TermsAndConditionsCubit>()
        ..initialize(
          agreementType: agreementType,
          muTermsAndConditions: muTermsAndConditions,
        ),
      child: TermsAndConditionsPageV2Content(
        agreementType: agreementType,
        hasHorizontalPadding: hasHorizontalPadding,
      ),
    );
  }
}

class TermsAndConditionsPageV2Content extends StatefulWidget {
  final AgreementType agreementType;
  final bool? hasHorizontalPadding;

  const TermsAndConditionsPageV2Content({
    required this.agreementType,
    this.hasHorizontalPadding,
    super.key,
  });

  @override
  State<TermsAndConditionsPageV2Content> createState() =>
      _TermsAndConditionsPageV2ContentState();
}

class _TermsAndConditionsPageV2ContentState
    extends State<TermsAndConditionsPageV2Content> {
  bool isAr = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    isAr = context.isArabic;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = TermsAndConditionsUiLocalizations.of(context);
    final style = context.textStyling;
    final colors = context.colorStyling;
    final hasHorizontalPadding = widget.hasHorizontalPadding ?? false;

    return BlocBuilder<TermsAndConditionsCubit, TermsAndConditionsState>(
      builder: (context, state) {
        return state.map(
          generalTandC: (loadedState) => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 34.0),
                child: Text(
                  widget.agreementType == AgreementType.termsAndConditions
                      ? l10n.termsAndConditions
                      : l10n.privacyPolicyHighlightedWord2,
                  style: style.h3medium.copyWith(color: colors.primary1),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(bottom: 34.0),
                child: Row(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(right: 12.0),
                        child: CtaButton.s(
                          title: l10n.consentEnglish,
                          ctaButtonVariant: isAr
                              ? CtaButtonVariant.lightBackgroundType
                              : CtaButtonVariant.primaryType,
                          onTap: () async => _onTapEnglish(),
                        ),
                      ),
                    ),
                    Expanded(
                      child: CtaButton.s(
                        title: l10n.consentArabic,
                        ctaButtonVariant: isAr
                            ? CtaButtonVariant.primaryType
                            : CtaButtonVariant.lightBackgroundType,
                        onTap: () async => _onTapArabic(),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  padding: hasHorizontalPadding
                      ? const EdgeInsets.symmetric(horizontal: 8)
                      : null,
                  color: hasHorizontalPadding ? colors.surface9 : null,
                  child: TermsAndConditionsLoadedView(
                    engUrl:
                        widget.agreementType == AgreementType.termsAndConditions
                            ? loadedState.tAndCEn
                            : loadedState.privacyEn,
                    arabicUrl:
                        widget.agreementType == AgreementType.termsAndConditions
                            ? loadedState.tAndCAr
                            : loadedState.privacyAr,
                    agreementType: widget.agreementType,
                    isAr: isAr,
                  ),
                ),
              ),
            ],
          ),
          loading: (_) => const Center(child: LazyLoadSpinner()),
          multiuserTandC: (loadedState) => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 34.0),
                child: Text(
                  widget.agreementType == AgreementType.termsAndConditions
                      ? l10n.termsAndConditions
                      : l10n.privacyPolicyHighlightedWord2,
                  style: style.h3medium.copyWith(color: colors.primary1),
                ),
              ),
              Expanded(
                child: Container(
                  padding: hasHorizontalPadding
                      ? const EdgeInsets.symmetric(horizontal: 8)
                      : null,
                  color: hasHorizontalPadding ? colors.surface9 : null,
                  child: TermsAndConditionsLoadedView(
                    engUrl: loadedState.muTermsAndConditions.enUrl,
                    arabicUrl: loadedState.muTermsAndConditions.arUrl,
                    agreementType: AgreementType.termsAndConditions,
                    isMultiuser: true,
                    isAr: context.isArabic,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _onTapEnglish() {
    _toggleLanguage(newIsArabic: false);
    context
        .read<TermsAndConditionsCubit>()
        .reload(agreementType: widget.agreementType);
  }

  void _onTapArabic() {
    _toggleLanguage(newIsArabic: true);
    context
        .read<TermsAndConditionsCubit>()
        .reload(agreementType: widget.agreementType);
  }

  void _toggleLanguage({required bool newIsArabic}) {
    if (isAr == newIsArabic) return;
    setState(() {
      isAr = newIsArabic;
    });
  }
}
