import 'package:wio_feature_terms_and_conditions_api/data/terms_and_conditions_repository.dart';
import 'package:wio_feature_terms_and_conditions_api/model/agreement_model.dart';
import 'package:wio_feature_terms_and_conditions_impl/src/data/terms_and_conditions_data_source.dart';
import 'package:wio_feature_terms_and_conditions_impl/src/data/terms_and_conditions_mapper.dart';

class TermsAndConditionsRepositoryImpl implements TermsAndConditionsRepository {
  final TermsAndConditionsDataSource _dataSource;
  final TermsAndConditionsMapper _mapper;

  TermsAndConditionsRepositoryImpl({
    required TermsAndConditionsDataSource dataSource,
    required TermsAndConditionsMapper mapper,
  })  : _dataSource = dataSource,
        _mapper = mapper;

  @override
  Future<Agreement> getAgreements() async {
    return _mapper.mapToAgreement(await _dataSource.getAgreements());
  }
}
