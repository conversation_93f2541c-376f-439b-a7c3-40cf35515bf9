import 'package:di/di.dart';
import 'package:logging_api/logging.dart';
import 'package:ui/l10n/common_localizations.g.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_eligibility_criteria_api/domain/interactor/eligibility_criteria_interactor.dart';
import 'package:wio_feature_eligibility_criteria_api/navigation/eligibility_criteria_feature_navigation_config.dart';
import 'package:wio_feature_eligibility_criteria_ui/src/navigation/eligibility_criteria_router.dart';
import 'package:wio_feature_eligibility_criteria_ui/src/page/eligibility_criteria_cubit.dart';

class EligibilityCriteriaFeatureDependencyModuleResolver {
  static void register() {
    DependencyProvider.registerLazySingleton(EligibilityCriteriaRouter.new);

    DependencyProvider.registerLazySingleton<NavigationRouter>(
      () => DependencyProvider.get<EligibilityCriteriaRouter>(),
      instanceName: EligibilityCriteriaFeatureNavigationConfig.name,
    );

    DependencyProvider.registerFactory<EligibilityCriteriaCubit>(() =>
        EligibilityCriteriaCubit(
          interactor: DependencyProvider.get<EligibilityCriteriaInteractor>(),
          localizations: DependencyProvider.get<CommonLocalizations>(),
          logger: DependencyProvider.get<Logger>(),
        ));
  }
}
