const multiplePayrolls = '''
{
	"data": [
		{
			"id": "0786c624-84db-42a6-8849-bfda2735fc4d",
			"title": "12345676543210221028153000",
			"amount": 2862.13,
			"recordsNumber": 10,
			"statusDate": "2022-12-06T00:00:00+0000",
			"uploadedDate": "2022-12-06T00:00:00+0000",
			"fee": 30.00,
			"status": "processing",
			"filename": "myfile"
		},
		{
			"id": "77bc5502-9af3-4a86-8b47-0561c4bdc9e4",
			"title": "12345676543210221019153000",
			"amount": 45234.13,
			"recordsNumber": 100,
			"statusDate": "2022-12-08T00:00:00+0000",
			"uploadedDate": "2022-12-08T00:00:00+0000",
			"fee": 30.00,
			"status": "transferred",
			"filename": "myfile"
		},
		{
			"id": "723aab62-d86d-4d01-8b5e-0653afadc847",
			"title": "12345676543210221019153000",
			"amount": 324213.13,
			"recordsNumber": 90,
			"statusDate": "2022-11-29T00:00:00+0000",
			"uploadedDate": "2022-11-29T00:00:00+0000",
			"fee": 30.00,
			"status": "reversed",
			"filename": "myfile"
		}
	]
}
''';

const onePayroll = '''
{
	"data": {
		"id": "0786c624-84db-42a6-8849-bfda2735fc4d",
		"title": "12345676543210221028153000",
		"amount": 2862.13,
		"recordsNumber": 10,
		"statusDate": "2022-12-06T00:00:00+0000",
		"uploadedDate": "2022-12-06T00:00:00+0000",
		"fee": 30.00,
		"status": "processing",
		"filename": "myfile"
	}
}
''';

const employerResponse = '''
{
	"data": {
		"individualId": "b70ac00b-dd11-4bc1-afbc-89dd18f13447",
		"businessId": "470e9c7b-2799-4732-8993-4cbb841417d4",
		"employerId": "*************",
		"companyName": "Wio Bank If SME"
	}
}
''';

const fileProcessingInfo = '''
{
  "data": {
    "id": "string",
    "payrollId": "string"
  }
}
''';

const base64FileData =
    'RURSLDAwMTAyMDE1NDEyOTcwLDgwODUxMDEwMSxBRTAwWFhYWFhYWFhYWFhYWFhYN'
    'zIyOCwyMDIwLTEwLTAxLDIwMjAtMTAtMzAsMzAsOTAwLjAwLDAuMDAsMApFRFIsMD'
    'AxMDIwMTYxMTI5NzEsODA4NTEwMTAxLEFFMDBYWFhYWFhYWFhYWFhYWFgyMTA4LDI'
    'wMjAtMTAtMDEsMjAyMC0xMC0zMCwzMCw5MDAuMDAsMC4wMCwwCkVEUiwwMDEwMjAx'
    'NjExMjk3Miw4MDg1MTAxMDEsQUUwMFhYWFhYWFhYWFhYWFhYWDYwNTgsMjAyMC0xM'
    'C0wMSwyMDIwLTEwLTMwLDMwLDYwMC4wMCwwLjAwLDAKU0NSLDAwMDAwMDAwMDA1MT'
    'gsMDAyNTEwMTAxLDIwMjAtMTAtMTUsMTcwOCwxMDIwMjAsMywyNDAwLjAwLEFFRCx'
    'TQUxBUlkgRk9SIEF1Zy0xNw==';

const employeeData = '''
{
    "data": {
        "payrollId": "payrollId",
        "employees": [
            {
                "index": 0,
                "employeeId": "id",
                "routingCodeOfBank": "bankRoutingCode",
                "employeeAccount": "iban",
                "payStartDate": "2023-02-05",
                "payEndDate": "2023-02-06",
                "daysInPeriod": 3,
                "incomeFixed": 123,
                "incomeVariable": 12,
                "daysOnLeave": 1,
                "reversed": false
            }
        ]
    }
}''';
