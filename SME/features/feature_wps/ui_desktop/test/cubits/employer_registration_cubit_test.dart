import 'dart:ui';

import 'package:account_feature_api/account_feature_api.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart' hide MockLogger;
import 'package:ui/ota_lokalisation/ota_lokalization.dart';
import 'package:wio_feature_wps_api/feature_wps_api.dart';
import 'package:wio_feature_wps_ui_desktop/locale/wps_ui_desktop_localizations.g.dart';
import 'package:wio_feature_wps_ui_desktop/src/screens/new_salary_transfer/views/stages/employer_registration/cubit/employer_registration_cubit.dart';

import '../mocks.dart';
import '../stub_data.dart';

typedef _Cubit = EmployerRegistrationCubit;
typedef _State = EmployerRegistrationState;

void main() {
  late EmployerRegistrationCubit cubit;
  late MockPayrollAnalytics payrollAnalytics;
  late MockPayrollInteractor interactor;
  late WpsUiLocalizations localizations;
  late MockAccountInteractor accountInteractor;
  late MockNotificationService notificationService;
  late MockLogger logger;

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    localizations = await WpsUiLocalizations.load(const Locale('en'));
  });

  setUp(() {
    payrollAnalytics = MockPayrollAnalytics();
    interactor = MockPayrollInteractor();
    accountInteractor = MockAccountInteractor();
    notificationService = MockNotificationService();
    logger = MockLogger();

    cubit = EmployerRegistrationCubit(
      payrollAnalytics: payrollAnalytics,
      interactor: interactor,
      localizations: localizations,
      accountInteractor: accountInteractor,
      notificationService: notificationService,
      logger: logger,
    );
  });

  group('Init Cubit Test', () {
    blocTest<_Cubit, _State>(
      'Init bloc with active account',
      build: () => cubit,
      setUp: () {
        when(() => accountInteractor.getAccount())
            .justAnswerAsync(StubData.getAccountData());
      },
      act: (cubit) => cubit.init(),
      expect: () {
        final expected = EmployerRegistrationState.loaded(
          account: StubData.getAccountData(),
        );

        return [expected];
      },
    );

    blocTest<_Cubit, _State>(
      'Init bloc with restricted debit account',
      build: () => cubit,
      setUp: () {
        when(() => accountInteractor.getAccount()).justAnswerAsync(
          StubData.getAccountData(subState: AccountSubState.restrictedDebit),
        );
      },
      act: (cubit) => cubit.init(),
      expect: () {
        final expected = EmployerRegistrationState.loaded(
          account: StubData.getAccountData(
            subState: AccountSubState.restrictedDebit,
          ),
          errorMessage: localizations.wpsAccountRestrictedState,
        );

        return [expected];
      },
    );

    blocTest<_Cubit, _State>(
      'Init bloc with error',
      build: () => cubit,
      setUp: () {
        when(() => accountInteractor.getAccount()).thenThrow(Exception());
      },
      act: (cubit) => cubit.init(),
      expect: () => [const EmployerRegistrationState.error()],
    );
  });

  group('Onboard Employer Test', () {
    blocTest<_Cubit, _State>(
      'Onboard employer with no errors',
      build: () => cubit,
      setUp: () {
        when(() => interactor.onboardEmployer(any()))
            .justAnswerAsync(StubData.getEmployer());
      },
      seed: () =>
          EmployerRegistrationState.loaded(account: StubData.getAccountData()),
      act: (cubit) => cubit.onboardEmployer('employerId'),
      expect: () {
        final expected = EmployerRegistrationState.loaded(
          account: StubData.getAccountData(),
          employer: StubData.getEmployer(),
        );

        return [expected];
      },
    );

    blocTest<_Cubit, _State>(
      'Onboard employer thats already onboarded',
      build: () => cubit,
      setUp: () {
        when(() => interactor.onboardEmployer(any()))
            .thenThrow(EmployerAlreadyOnboardedException(''));
        when(() => interactor.getEmployer())
            .justAnswerAsync(StubData.getEmployer());
      },
      seed: () =>
          EmployerRegistrationState.loaded(account: StubData.getAccountData()),
      act: (cubit) => cubit.onboardEmployer('employerId'),
      expect: () {
        final expected = EmployerRegistrationState.loaded(
          account: StubData.getAccountData(),
          employer: StubData.getEmployer(),
        );

        return [expected];
      },
    );

    blocTest<_Cubit, _State>(
      'Try to onboard employer whose id has already been onboarded',
      build: () => cubit,
      setUp: () {
        when(() => interactor.onboardEmployer(any()))
            .thenThrow(EmployerIdAlreadyExistsException(''));
      },
      seed: () =>
          EmployerRegistrationState.loaded(account: StubData.getAccountData()),
      act: (cubit) => cubit.onboardEmployer('employerId'),
      expect: () {
        final expected = EmployerRegistrationState.loaded(
          account: StubData.getAccountData(),
          errorMessage: localizations.wpsEmployerIdAlreadyExists,
        );

        return [expected];
      },
    );

    blocTest<_Cubit, _State>(
      'Any expection during employer onboard',
      build: () => cubit,
      setUp: () {
        when(() => interactor.onboardEmployer(any())).thenThrow(Exception());
      },
      seed: () =>
          EmployerRegistrationState.loaded(account: StubData.getAccountData()),
      act: (cubit) => cubit.onboardEmployer('employerId'),
      expect: () => <Object>[],
    );
  });
}
