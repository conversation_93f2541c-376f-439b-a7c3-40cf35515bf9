import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_feature_wps_ui_desktop/feature_wps_ui_desktop.dart';
import 'package:wio_feature_wps_ui_desktop/src/dialogs/close_flow_dialog/close_dialog_flow_cubit.dart';
import 'package:wio_feature_wps_ui_desktop/src/dialogs/close_flow_dialog/close_dialog_flow_state.dart';

class CloseFlowDialog extends StatelessWidget {
  final String screenStep;

  const CloseFlowDialog({required this.screenStep, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PageBlocProvider<CloseFlowDialogCubit, CloseFlowDialogState>(
      createBloc: () => DependencyProvider.get<CloseFlowDialogCubit>()
        ..init(
          screenStep,
        ),
      child: const _DialogContent(),
    );
  }
}

class _DialogContent extends StatelessWidget {
  const _DialogContent({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final localizations = WpsUiLocalizations.of(context);
    final bloc = BlocProvider.of<CloseFlowDialogCubit>(context);

    return ModalDialog(
      body: ModalBody(
        titleCompanyRichTextModel: CompanyRichTextModel(
          text: localizations.wpsCloseDialogTitle,
          highlightedTextModels: [
            HighlightedTextModel(
              localizations.wpsCloseDialogHighlightedText,
            ),
          ],
        ),
      ),
      footer: ModalFooter(
        leadingWidget: CtaButton.l(
          ctaButtonVariant: CtaButtonVariant.primaryType,
          onTap: () async => bloc.closeFlow(),
          title: localizations.wpsCloseDialogConfirmButtonText,
        ),
        trailingWidget: Padding(
          padding: const EdgeInsets.only(top: 8),
          child: TextLinkButton(
            TextLinkButtonModel(
              text: localizations.wpsCloseDialogCancelText.toUpperCase(),
              type: TextLinkButtonType.alternative2,
            ),
            onClick: bloc.closeDialog,
          ),
        ),
      ),
    );
  }
}
