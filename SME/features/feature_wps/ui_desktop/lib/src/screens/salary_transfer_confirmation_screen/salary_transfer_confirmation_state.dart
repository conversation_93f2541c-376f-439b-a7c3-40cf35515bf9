import 'package:account_feature_api/account_feature_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_mu_payment_requests_api/feature_mu_payment_requests_api.dart';
import 'package:wio_feature_wps_api/feature_wps_api.dart';

part 'salary_transfer_confirmation_state.freezed.dart';

@freezed
class SalaryTransferConfirmationState with _$SalaryTransferConfirmationState {
  const factory SalaryTransferConfirmationState.loading() =
      SalaryTransferConfirmationLoadingState;

  const factory SalaryTransferConfirmationState.loaded({
    required Account account,
    required Payroll payroll,
    MuPayrollRequest? muRequest,
  }) = SalaryTransferConfirmationLoadedState;
}
