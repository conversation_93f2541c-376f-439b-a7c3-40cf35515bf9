import 'dart:async';

import 'package:account_feature_api/account_feature_api.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:logging_api/logging.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_mu_payment_requests_api/feature_mu_payment_requests_api.dart';
import 'package:wio_feature_wps_api/feature_wps_api.dart';
import 'package:wio_feature_wps_ui_desktop/locale/wps_ui_desktop_localizations.g.dart';
import 'package:wio_feature_wps_ui_desktop/src/analytics/payroll_analytics.dart';
import 'package:wio_feature_wps_ui_desktop/src/navigation/salary_transfer_confirmation_screen_navigation_config.dart';
import 'package:wio_feature_wps_ui_desktop/src/navigation/save_as_draft_dialog_navigation_config.dart';
import 'package:wio_feature_wps_ui_desktop/src/screens/new_salary_transfer/views/stages/employer_registration/employer_registration.dart';
import 'package:wio_feature_wps_ui_desktop/src/screens/new_salary_transfer/views/stages/file_upload/file_upload.dart';
import 'package:wio_sme_status_api/wio_sme_status_api.dart';

part 'new_salary_transfer_cubit.freezed.dart';

part 'new_salary_transfer_state.dart';

class NewSalaryTransferCubit extends BaseCubit<NewSalaryTransferState> {
  final AccountInteractor _accountInteractor;
  final PayrollInteractor _interactor;
  final Logger _logger;
  final NavigationProvider _navigationProvider;
  final PayrollAnalytics _payrollAnalytics;
  final WpsUiLocalizations _l10n;

  NewSalaryTransferCubit({
    required AccountInteractor accountInteractor,
    required PayrollInteractor interactor,
    required Logger logger,
    required NavigationProvider navigationProvider,
    required PayrollAnalytics payrollAnalytics,
    required WpsUiLocalizations l10n,
  })  : _accountInteractor = accountInteractor,
        _interactor = interactor,
        _logger = logger,
        _navigationProvider = navigationProvider,
        _payrollAnalytics = payrollAnalytics,
        _l10n = l10n,
        super(const NewSalaryTransferState.loading(pageNumber: 0));

  Future<void> init({String? payrollId}) async {
    try {
      final employer = await _interactor.getEmployer().disposeBy(this);

      emit(
        NewSalaryTransferState.fileUpload(
          employer: employer,
          payrollId: payrollId,
          pageNumber: state.pageNumber,
        ),
      );
    } on EmployerNotFoundException catch (e, stackTrace) {
      _logger.info(
        'Employer was not found. Employer onboarding required',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        NewSalaryTransferState.employerRegistration(
          pageNumber: state.pageNumber,
        ),
      );
    } on Object catch (e, stackTrace) {
      unawaited(_navigateToErrorScreen(() => init(payrollId: payrollId)));
      _logger.error(
        'Could not load new salary transfer page',
        error: e,
        stackTrace: stackTrace,
      );
      emit(NewSalaryTransferState.error(pageNumber: state.pageNumber));
    }
  }

  void employerOnboarded(Employer employer) {
    state.mapOrNull(
      employerRegistration: (_) => emit(
        NewSalaryTransferState.fileUpload(
          employer: employer,
          pageNumber: state.pageNumber,
        ),
      ),
    );
  }

  void switchToApproverSelection(MuPayrollRequest muPayrollRequest) {
    _payrollAnalytics.viewMuSelectPreparerPageView();
    emit(
      NewSalaryTransferState.approverSelection(
        muRequest: muPayrollRequest,
        pageNumber: state.pageNumber,
      ),
    );
  }

  Future<void> goToConfirmationScreen(String payrollId) async {
    await _navigationProvider.push(
      SalaryTransferConfirmationScreenNavigationConfig(
        payrollId: payrollId,
      ),
    );
  }

  Future<void> goToErrorCorrection(File file) async {
    final employer = await _getEmployer();
    if (employer == null) return;
    emit(
      NewSalaryTransferState.errorCorrection(
        employer: employer,
        file: file,
        pageNumber: state.pageNumber,
      ),
    );
  }

  Future<void> goToCorrectedFileView(File file) async {
    _payrollAnalytics.trackFileErrorsCorrectedDialog();
    final accountBalance = await _getAccountBalance();
    final employer = await _getEmployer();
    if (employer == null) return;
    emit(
      NewSalaryTransferState.fileRecordsOverview(
        employer: employer,
        file: file,
        pageNumber: state.pageNumber,
        accountBalance: accountBalance,
      ),
    );
  }

  Future<void> proceedToCorrectedFileUpload(
    File file, {
    bool isFromCorrectionScreen = false,
  }) async {
    _payrollAnalytics.clickSubmitFromCorrectionView();
    final employer = await _getEmployer();
    if (employer == null) return;
    emit(
      NewSalaryTransferState.fileUpload(
        employer: employer,
        file: file,
        fileName: file.fileName,
        fileData: file.generateFile(),
        pageNumber: state.pageNumber,
      ),
    );
  }

  void handleClose({String? payrollId}) {
    final screenStep = _getScreenStep();
    if (payrollId == null) {
      _handleCloseWithoutDialogAnalytics(screenStep);
      _navigationProvider.goBack();
    } else {
      _navigationProvider.showDialog(
        SaveAsDraftDialogFlowNavigationConfig.withPayrollId(
          payrollId: payrollId,
          analyticsPageScreen: screenStep,
        ),
      );
    }
  }

  void handleFatalStageError() {
    emit(NewSalaryTransferState.error(pageNumber: state.pageNumber));
  }

  void pageChanged(int? pageNumber) {
    emit(state.copyWith(pageNumber: pageNumber ?? 0));
  }

  Future<Employer?> _getEmployer() async {
    try {
      final employer = state.employer;
      if (employer != null) return employer;

      return await _interactor.getEmployer().disposeBy(this);
    } on Object catch (e, st) {
      unawaited(_navigateToErrorScreen(() => _getEmployer()));
      _logger.error(
        'Could not load get employer details',
        error: e,
        stackTrace: st,
      );
      emit(NewSalaryTransferState.error(pageNumber: state.pageNumber));

      return null;
    }
  }

  String _getScreenStep() {
    switch (state.pageNumber) {
      case 1:
        return EmployerRegistration.employerRegistrationStep;
      case 2:
        return FileUpload.fileUploadStep;
    }

    return '';
  }

  Future<Money?> _getAccountBalance() async {
    Account? account;
    try {
      account =
          await _accountInteractor.getAccountFor(Currency.aed).disposeBy(this);
    } on AccountNotFound catch (e, st) {
      _logger.error('Account balance not found', error: e, stackTrace: st);
    } on Object catch (e, st) {
      _logger.error('getAccountBalanceOf failed', error: e, stackTrace: st);
    }

    return account?.balanceMoney;
  }

  void _handleCloseWithoutDialogAnalytics(String screenStep) {
    switch (screenStep) {
      case EmployerRegistration.employerRegistrationStep:
        _payrollAnalytics.closeEmployerWpsRegistrationWithoutDialog();
      case FileUpload.fileUploadStep:
        _payrollAnalytics.closeFileUploadWpsWithoutDialog();
    }
  }

  Future<void> _navigateToErrorScreen(VoidCallback ctaCallback) =>
      _navigationProvider.removeStackAndPush(
        ErrorScreenNavigationConfig.legacy(
          model: ErrorScreenModel(
            errorTitle: _l10n.uhOh,
            errorSubtitle: _l10n.smthWentWrong,
            contactLinkText: _l10n.consentContactSupport,
            confirmCtaText: _l10n.retryButton,
          ),
          onCtaAction: ctaCallback,
        ),
      );

  @override
  String toString() => 'NewSalaryTransferCubit{}';
}
