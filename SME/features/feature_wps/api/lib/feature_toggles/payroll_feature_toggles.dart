import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';

class PayrollFeatureToggles {
  /// determines if WPS is enabled
  static const isWpsEnabled = FeatureToggleKey(
    key: 'enable_wpsFileUploadFlow_release_20221103',
    defaultValue: false,
  );

  /// determines if WPS Save as Draft option should be enabled
  static const enableSaveAsDraftDialog = FeatureToggleKey<bool>(
    key: 'smeWebEnableWpsFileSaveAsDraft_release_20230125',
    defaultValue: false,
  );

  /// determines if the WPS Salary Transfer Flow Refactored is enabled
  static const enableWpsRefactoredCreationFlow = FeatureToggleKey<bool>(
    key: 'enable_wps_refactored_flow_20240507',
    defaultValue: false,
  );
}
