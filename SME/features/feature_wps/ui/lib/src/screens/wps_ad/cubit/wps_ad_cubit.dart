import 'package:logging_api/logging.dart';
import 'package:ui/cubit/base_cubit.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_share_api/feature_share_api.dart';
import 'package:wio_feature_share_api/models/open_file/domain_open_result.dart';
import 'package:wio_feature_wps_api/navigation/wps_tutorial_navigation_config.dart';
import 'package:wio_feature_wps_ui/locale/wps_ui_localizations.g.dart';
import 'package:wio_feature_wps_ui/src/bottom_sheets/utils/file_path_helper.dart';
import 'package:wio_feature_wps_ui/src/screens/wps_ad/analytics/wps_ad_analytics.dart';
import 'package:wio_feature_wps_ui/src/screens/wps_ad/cubit/wps_ad_state.dart';
import 'package:wio_feature_wps_ui/src/screens/wps_ad/wps_assets.dart';

class WpsAdCubit extends BaseCubit<WpsAdState> {
  // TODO(amitrica): to be changed with api integration in https://wiobank.atlassian.net/browse/MU-1345
  static const wpsTutorialUrl =
      'https://videos.ctfassets.net/l65m9bcr2nac/2EZSu0APgdOKdOmMW9HXoO/2ea5e8a854403f570618ce59eb83bef7/WPS___How_to_Upload_file.mp4';
  final FilePathHelper _filePathHelper;
  final Logger _logger;
  final NavigationProvider _navigationProvider;
  final ShareProvider _shareProvider;
  final ToastMessageProvider _toastMessageProvider;
  final WpsAdAnalytics _wpsAdAnalytics;
  final WpsUiLocalizations _wpsUiLocalizations;

  WpsAdCubit({
    required FilePathHelper filePathHelper,
    required Logger logger,
    required NavigationProvider navigationProvider,
    required ShareProvider shareProvider,
    required ToastMessageProvider toastMessageProvider,
    required WpsAdAnalytics wpsAdAnalytics,
    required WpsUiLocalizations wpsUiLocalizations,
  })  : _filePathHelper = filePathHelper,
        _logger = logger,
        _navigationProvider = navigationProvider,
        _shareProvider = shareProvider,
        _toastMessageProvider = toastMessageProvider,
        _wpsAdAnalytics = wpsAdAnalytics,
        _wpsUiLocalizations = wpsUiLocalizations,
        super(const WpsAdState());

  Future<void> openWpsGuide({required String languageCode}) async {
    try {
      final assetPath = WpsAssets.wpsGuideAsset(languageCode);
      final isAr = languageCode == 'ar';
      final path = await _filePathHelper.getPathFromAsset(assetPath);
      _wpsAdAnalytics.openWpsGuide();
      final openResult = await _shareProvider.openFile(path);
      final resultType = openResult.resultType;
      if (resultType == DomainResultType.noAppToOpen) {
        _wpsAdAnalytics.shareWpsGuide();
        await _shareProvider.shareFile(
          [path],
          subject: isAr ? WpsAssets.wpsGuideAr : WpsAssets.wpsGuide,
        );
        _wpsAdAnalytics.shareWpsGuideOpened();

        return;
      }

      if (resultType != DomainResultType.done) {
        throw UnsupportedError('Unexpected result type of $resultType');
      }
      _wpsAdAnalytics.openedWpsGuide();
    } on Object catch (e, st) {
      const ext = WpsAssets.wpsGuideExtension;
      _showOpenFileErrorToast(
        _wpsUiLocalizations.somethingHappenedWhenOpening(ext.toUpperCase()),
      );
      _logger.error(
        'Something happened during openFile $ext',
        error: e,
        stackTrace: st,
      );
    }
  }

  Future<void> shareTemplate({
    required String languageCode,
  }) async {
    try {
      final assetPath = WpsAssets.templateAsset(languageCode);
      final assetName = WpsAssets.templateName(languageCode);

      final path = await _filePathHelper.getPathFromAsset(assetPath);
      _wpsAdAnalytics.shareWpsTemplate();
      await _shareProvider.shareFile(
        [path],
        subject: assetName,
      );
      _wpsAdAnalytics.shareWpsTemplateOpened();
    } on Object catch (e, st) {
      const ext = WpsAssets.templateExtension;
      _showOpenFileErrorToast(
        _wpsUiLocalizations.somethingHappenedWhenSaving(ext.toUpperCase()),
      );
      _logger.error(
        'Something happened during saving the file template $ext',
        error: e,
        stackTrace: st,
      );
    }
  }

  Future<void> openTutorial() async {
    _wpsAdAnalytics.clickWpsHowToVideo();
    await _navigationProvider
        .push(const WpsTutorialScreenNavigationConfig(uri: wpsTutorialUrl));
  }

  void _showOpenFileErrorToast(String error) {
    _toastMessageProvider.showRetailMobileThemedToastMessage(
      NotificationToastMessageConfiguration.error(error),
    );
  }

  void onCloseButtonPressed() => _navigationProvider.goBack();

  @override
  String toString() => 'WpsAdCubit{}';
}
