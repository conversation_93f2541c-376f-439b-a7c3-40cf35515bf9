{"format": "graph-model", "generatedBy": "2.0.0", "convertedBy": "TensorFlow.js Converter v2.0.0", "userDefinedMetadata": {"signature": {"inputs": {"preprocessed_images_pixels:0": {"name": "preprocessed_images_pixels:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "-1"}, {"size": "-1"}, {"size": "3"}]}}}, "outputs": {"Identity_6:0": {"name": "Identity_6:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "3"}]}}, "Identity_1:0": {"name": "Identity_1:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "1"}]}}, "Identity_2:0": {"name": "Identity_2:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "1"}]}}, "Identity_4:0": {"name": "Identity_4:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "1"}]}}, "Identity_5:0": {"name": "Identity_5:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "1"}]}}, "Identity:0": {"name": "Identity:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "2"}]}}, "Identity_7:0": {"name": "Identity_7:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "3"}]}}, "Identity_3:0": {"name": "Identity_3:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "1"}]}}}}}, "modelTopology": {"node": [{"name": "StatefulPartitionedCall/model_1/smile/MatMul/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3200"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/smile/add/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/object/MatMul/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3200"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/object/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/light_lighting/MatMul/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3200"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/light_lighting/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/light_exposure/MatMul/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3200"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/light_exposure/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/light_background/MatMul/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3200"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/light_background/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/motion_blur/MatMul/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3200"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/motion_blur/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/eyes_left/MatMul/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3200"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/eyes_left/add/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d_4/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/dense_8/MatMul/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}, {"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/dense_8/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense_9/MatMul/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense_9/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/multiply_4/ExpandDims/dim", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model_1/multiply_4/ExpandDims_1/dim", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d_3/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/dense_6/MatMul/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense_6/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense_7/MatMul/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense_7/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/multiply_3/ExpandDims/dim", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model_1/multiply_3/ExpandDims_1/dim", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d_2/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/dense_4/MatMul/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense_4/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense_5/MatMul/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/dense_5/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/multiply_2/ExpandDims/dim", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model_1/multiply_2/ExpandDims_1/dim", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d_1/Mean/reduction_indices", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model_1/dense_2/MatMul/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense_2/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/dense_3/MatMul/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/dense_3/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/multiply_1/ExpandDims/dim", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model_1/multiply_1/ExpandDims_1/dim", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/dense/MatMul/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}, {"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/dense/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense_1/MatMul/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense_1/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/multiply/ExpandDims/dim", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model_1/multiply/ExpandDims_1/dim", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model_1/white_norm/Const", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model_1/conv5/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/conv5/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/flatten/strided_slice/stack", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/flatten/strided_slice/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/flatten/strided_slice/stack_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model_1/flatten/Reshape/shape/1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model_1/eyes_right/MatMul/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3200"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/eyes_right/add/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "preprocessed_images_pixels", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "-1"}, {"size": "-1"}, {"size": "-1"}, {"size": "3"}]}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/conv2d/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/conv2d/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/conv2/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/conv2/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/conv3/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model_1/conv3/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/conv4/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/conv4/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/white_norm/sub", "op": "Sub", "input": ["preprocessed_images_pixels", "StatefulPartitionedCall/model_1/white_norm/Const"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/activation/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model_1/white_norm/sub", "StatefulPartitionedCall/model_1/conv2d/Conv2D_weights", "StatefulPartitionedCall/model_1/conv2d/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0.0}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/model_1/average_pooling2d/AvgPool", "op": "AvgPool", "input": ["StatefulPartitionedCall/model_1/activation/Relu"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model_1/average_pooling2d/AvgPool", "StatefulPartitionedCall/model_1/global_average_pooling2d/Mean/reduction_indices"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense/Relu", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/model_1/global_average_pooling2d/Mean", "StatefulPartitionedCall/model_1/dense/MatMul/ReadVariableOp", "StatefulPartitionedCall/model_1/dense/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "epsilon": {"f": 0.0}, "transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "transpose_a": {"b": false}}}, {"name": "StatefulPartitionedCall/model_1/dense_1/BiasAdd", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/model_1/dense/Relu", "StatefulPartitionedCall/model_1/dense_1/MatMul/ReadVariableOp", "StatefulPartitionedCall/model_1/dense_1/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/model_1/dense_1/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/dense_1/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/multiply/ExpandDims", "op": "ExpandDims", "input": ["StatefulPartitionedCall/model_1/dense_1/Sigmoid", "StatefulPartitionedCall/model_1/multiply/ExpandDims/dim"], "attr": {"T": {"type": "DT_FLOAT"}, "Tdim": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model_1/multiply/ExpandDims_1", "op": "ExpandDims", "input": ["StatefulPartitionedCall/model_1/multiply/ExpandDims", "StatefulPartitionedCall/model_1/multiply/ExpandDims_1/dim"], "attr": {"Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/multiply/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/multiply/ExpandDims_1", "StatefulPartitionedCall/model_1/average_pooling2d/AvgPool"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/activation_1/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model_1/multiply/mul", "StatefulPartitionedCall/model_1/conv2/Conv2D_weights", "StatefulPartitionedCall/model_1/conv2/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model_1/average_pooling2d_1/AvgPool", "op": "AvgPool", "input": ["StatefulPartitionedCall/model_1/activation_1/Relu"], "attr": {"ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d_1/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model_1/average_pooling2d_1/AvgPool", "StatefulPartitionedCall/model_1/global_average_pooling2d_1/Mean/reduction_indices"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense_2/Relu", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/model_1/global_average_pooling2d_1/Mean", "StatefulPartitionedCall/model_1/dense_2/MatMul/ReadVariableOp", "StatefulPartitionedCall/model_1/dense_2/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "epsilon": {"f": 0.0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}}}, {"name": "StatefulPartitionedCall/model_1/dense_3/BiasAdd", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/model_1/dense_2/Relu", "StatefulPartitionedCall/model_1/dense_3/MatMul/ReadVariableOp", "StatefulPartitionedCall/model_1/dense_3/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "transpose_b": {"b": false}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense_3/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/dense_3/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/multiply_1/ExpandDims", "op": "ExpandDims", "input": ["StatefulPartitionedCall/model_1/dense_3/Sigmoid", "StatefulPartitionedCall/model_1/multiply_1/ExpandDims/dim"], "attr": {"T": {"type": "DT_FLOAT"}, "Tdim": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model_1/multiply_1/ExpandDims_1", "op": "ExpandDims", "input": ["StatefulPartitionedCall/model_1/multiply_1/ExpandDims", "StatefulPartitionedCall/model_1/multiply_1/ExpandDims_1/dim"], "attr": {"Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/multiply_1/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/multiply_1/ExpandDims_1", "StatefulPartitionedCall/model_1/average_pooling2d_1/AvgPool"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/activation_2/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model_1/multiply_1/mul", "StatefulPartitionedCall/model_1/conv3/Conv2D_weights", "StatefulPartitionedCall/model_1/conv3/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "epsilon": {"f": 0.0}}}, {"name": "StatefulPartitionedCall/model_1/average_pooling2d_2/AvgPool", "op": "AvgPool", "input": ["StatefulPartitionedCall/model_1/activation_2/Relu"], "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d_2/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model_1/average_pooling2d_2/AvgPool", "StatefulPartitionedCall/model_1/global_average_pooling2d_2/Mean/reduction_indices"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "keep_dims": {"b": false}}}, {"name": "StatefulPartitionedCall/model_1/dense_4/Relu", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/model_1/global_average_pooling2d_2/Mean", "StatefulPartitionedCall/model_1/dense_4/MatMul/ReadVariableOp", "StatefulPartitionedCall/model_1/dense_4/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/model_1/dense_5/BiasAdd", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/model_1/dense_4/Relu", "StatefulPartitionedCall/model_1/dense_5/MatMul/ReadVariableOp", "StatefulPartitionedCall/model_1/dense_5/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "transpose_b": {"b": false}, "epsilon": {"f": 0.0}}}, {"name": "StatefulPartitionedCall/model_1/dense_5/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/dense_5/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/multiply_2/ExpandDims", "op": "ExpandDims", "input": ["StatefulPartitionedCall/model_1/dense_5/Sigmoid", "StatefulPartitionedCall/model_1/multiply_2/ExpandDims/dim"], "attr": {"T": {"type": "DT_FLOAT"}, "Tdim": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model_1/multiply_2/ExpandDims_1", "op": "ExpandDims", "input": ["StatefulPartitionedCall/model_1/multiply_2/ExpandDims", "StatefulPartitionedCall/model_1/multiply_2/ExpandDims_1/dim"], "attr": {"T": {"type": "DT_FLOAT"}, "Tdim": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model_1/multiply_2/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/multiply_2/ExpandDims_1", "StatefulPartitionedCall/model_1/average_pooling2d_2/AvgPool"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/activation_3/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model_1/multiply_2/mul", "StatefulPartitionedCall/model_1/conv4/Conv2D_weights", "StatefulPartitionedCall/model_1/conv4/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d_3/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model_1/activation_3/Relu", "StatefulPartitionedCall/model_1/global_average_pooling2d_3/Mean/reduction_indices"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense_6/Relu", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/model_1/global_average_pooling2d_3/Mean", "StatefulPartitionedCall/model_1/dense_6/MatMul/ReadVariableOp", "StatefulPartitionedCall/model_1/dense_6/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_b": {"b": false}, "transpose_a": {"b": false}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0.0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/model_1/dense_7/BiasAdd", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/model_1/dense_6/Relu", "StatefulPartitionedCall/model_1/dense_7/MatMul/ReadVariableOp", "StatefulPartitionedCall/model_1/dense_7/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "epsilon": {"f": 0.0}, "transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/model_1/dense_7/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/dense_7/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/multiply_3/ExpandDims", "op": "ExpandDims", "input": ["StatefulPartitionedCall/model_1/dense_7/Sigmoid", "StatefulPartitionedCall/model_1/multiply_3/ExpandDims/dim"], "attr": {"Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/multiply_3/ExpandDims_1", "op": "ExpandDims", "input": ["StatefulPartitionedCall/model_1/multiply_3/ExpandDims", "StatefulPartitionedCall/model_1/multiply_3/ExpandDims_1/dim"], "attr": {"T": {"type": "DT_FLOAT"}, "Tdim": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model_1/multiply_3/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/multiply_3/ExpandDims_1", "StatefulPartitionedCall/model_1/activation_3/Relu"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/conv5/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model_1/multiply_3/mul", "StatefulPartitionedCall/model_1/conv5/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model_1/conv5/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0.0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d_4/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model_1/conv5/BiasAdd", "StatefulPartitionedCall/model_1/global_average_pooling2d_4/Mean/reduction_indices"], "attr": {"keep_dims": {"b": false}, "Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense_8/Relu", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/model_1/global_average_pooling2d_4/Mean", "StatefulPartitionedCall/model_1/dense_8/MatMul/ReadVariableOp", "StatefulPartitionedCall/model_1/dense_8/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0.0}, "transpose_a": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/dense_9/BiasAdd", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/model_1/dense_8/Relu", "StatefulPartitionedCall/model_1/dense_9/MatMul/ReadVariableOp", "StatefulPartitionedCall/model_1/dense_9/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_b": {"b": false}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}, "transpose_a": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/model_1/dense_9/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/dense_9/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/multiply_4/ExpandDims", "op": "ExpandDims", "input": ["StatefulPartitionedCall/model_1/dense_9/Sigmoid", "StatefulPartitionedCall/model_1/multiply_4/ExpandDims/dim"], "attr": {"Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/multiply_4/ExpandDims_1", "op": "ExpandDims", "input": ["StatefulPartitionedCall/model_1/multiply_4/ExpandDims", "StatefulPartitionedCall/model_1/multiply_4/ExpandDims_1/dim"], "attr": {"Tdim": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/multiply_4/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/multiply_4/ExpandDims_1", "StatefulPartitionedCall/model_1/conv5/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/flatten/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/multiply_4/mul"], "attr": {"out_type": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/flatten/strided_slice", "op": "StridedSlice", "input": ["StatefulPartitionedCall/model_1/flatten/Shape", "StatefulPartitionedCall/model_1/flatten/strided_slice/stack", "StatefulPartitionedCall/model_1/flatten/strided_slice/stack_1", "StatefulPartitionedCall/model_1/flatten/strided_slice/stack_2"], "attr": {"shrink_axis_mask": {"i": "1"}, "Index": {"type": "DT_INT32"}, "end_mask": {"i": "0"}, "begin_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "T": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model_1/flatten/Reshape/shape", "op": "Pack", "input": ["StatefulPartitionedCall/model_1/flatten/strided_slice", "StatefulPartitionedCall/model_1/flatten/Reshape/shape/1"], "attr": {"axis": {"i": "0"}, "T": {"type": "DT_INT32"}, "N": {"i": "2"}}}, {"name": "StatefulPartitionedCall/model_1/flatten/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/model_1/multiply_4/mul", "StatefulPartitionedCall/model_1/flatten/Reshape/shape"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model_1/smile/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/model_1/flatten/Reshape", "StatefulPartitionedCall/model_1/smile/MatMul/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_b": {"b": false}, "transpose_a": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/object/BiasAdd", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/model_1/flatten/Reshape", "StatefulPartitionedCall/model_1/object/MatMul/ReadVariableOp", "StatefulPartitionedCall/model_1/object/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_b": {"b": false}, "transpose_a": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/light_lighting/BiasAdd", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/model_1/flatten/Reshape", "StatefulPartitionedCall/model_1/light_lighting/MatMul/ReadVariableOp", "StatefulPartitionedCall/model_1/light_lighting/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/model_1/eyes_right/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/model_1/flatten/Reshape", "StatefulPartitionedCall/model_1/eyes_right/MatMul/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "T": {"type": "DT_FLOAT"}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/model_1/light_exposure/BiasAdd", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/model_1/flatten/Reshape", "StatefulPartitionedCall/model_1/light_exposure/MatMul/ReadVariableOp", "StatefulPartitionedCall/model_1/light_exposure/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/model_1/light_background/BiasAdd", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/model_1/flatten/Reshape", "StatefulPartitionedCall/model_1/light_background/MatMul/ReadVariableOp", "StatefulPartitionedCall/model_1/light_background/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "transpose_b": {"b": false}, "num_args": {"i": "1"}, "transpose_a": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/model_1/motion_blur/BiasAdd", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/model_1/flatten/Reshape", "StatefulPartitionedCall/model_1/motion_blur/MatMul/ReadVariableOp", "StatefulPartitionedCall/model_1/motion_blur/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "epsilon": {"f": 0.0}, "transpose_b": {"b": false}, "transpose_a": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/model_1/eyes_left/MatMul", "op": "Mat<PERSON>ul", "input": ["StatefulPartitionedCall/model_1/flatten/Reshape", "StatefulPartitionedCall/model_1/eyes_left/MatMul/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "transpose_a": {"b": false}, "transpose_b": {"b": false}}}, {"name": "StatefulPartitionedCall/model_1/smile/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model_1/smile/MatMul", "StatefulPartitionedCall/model_1/smile/add/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/object/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/object/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/light_lighting/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/light_lighting/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/eyes_right/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model_1/eyes_right/MatMul", "StatefulPartitionedCall/model_1/eyes_right/add/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/light_exposure/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/light_exposure/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/light_background/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model_1/light_background/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity_5", "op": "Identity", "input": ["StatefulPartitionedCall/model_1/motion_blur/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model_1/eyes_left/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model_1/eyes_left/MatMul", "StatefulPartitionedCall/model_1/eyes_left/add/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity", "op": "Identity", "input": ["StatefulPartitionedCall/model_1/smile/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity_1", "op": "Identity", "input": ["StatefulPartitionedCall/model_1/object/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity_2", "op": "Identity", "input": ["StatefulPartitionedCall/model_1/light_lighting/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity_7", "op": "Identity", "input": ["StatefulPartitionedCall/model_1/eyes_right/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity_3", "op": "Identity", "input": ["StatefulPartitionedCall/model_1/light_exposure/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity_4", "op": "Identity", "input": ["StatefulPartitionedCall/model_1/light_background/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity_6", "op": "Identity", "input": ["StatefulPartitionedCall/model_1/eyes_left/add"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {"producer": 987}}, "weightsManifest": [{"paths": ["group1-shard1of1.dat"], "weights": [{"name": "StatefulPartitionedCall/model_1/smile/MatMul/ReadVariableOp", "shape": [3200, 1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/smile/add/ReadVariableOp", "shape": [2], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/object/MatMul/ReadVariableOp", "shape": [3200, 1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/object/BiasAdd/ReadVariableOp", "shape": [1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/light_lighting/MatMul/ReadVariableOp", "shape": [3200, 1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/light_lighting/BiasAdd/ReadVariableOp", "shape": [1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/light_exposure/MatMul/ReadVariableOp", "shape": [3200, 1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/light_exposure/BiasAdd/ReadVariableOp", "shape": [1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/light_background/MatMul/ReadVariableOp", "shape": [3200, 1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/light_background/BiasAdd/ReadVariableOp", "shape": [1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/motion_blur/MatMul/ReadVariableOp", "shape": [3200, 1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/motion_blur/BiasAdd/ReadVariableOp", "shape": [1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/eyes_left/MatMul/ReadVariableOp", "shape": [3200, 1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/eyes_left/add/ReadVariableOp", "shape": [3], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d_4/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/dense_8/MatMul/ReadVariableOp", "shape": [32, 16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/dense_8/BiasAdd/ReadVariableOp", "shape": [16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/dense_9/MatMul/ReadVariableOp", "shape": [16, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/dense_9/BiasAdd/ReadVariableOp", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/multiply_4/ExpandDims/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/multiply_4/ExpandDims_1/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d_3/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/dense_6/MatMul/ReadVariableOp", "shape": [32, 16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/dense_6/BiasAdd/ReadVariableOp", "shape": [16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/dense_7/MatMul/ReadVariableOp", "shape": [16, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/dense_7/BiasAdd/ReadVariableOp", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/multiply_3/ExpandDims/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/multiply_3/ExpandDims_1/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d_2/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/dense_4/MatMul/ReadVariableOp", "shape": [32, 16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/dense_4/BiasAdd/ReadVariableOp", "shape": [16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/dense_5/MatMul/ReadVariableOp", "shape": [16, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/dense_5/BiasAdd/ReadVariableOp", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/multiply_2/ExpandDims/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/multiply_2/ExpandDims_1/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d_1/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/dense_2/MatMul/ReadVariableOp", "shape": [32, 16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/dense_2/BiasAdd/ReadVariableOp", "shape": [16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/dense_3/MatMul/ReadVariableOp", "shape": [16, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/dense_3/BiasAdd/ReadVariableOp", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/multiply_1/ExpandDims/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/multiply_1/ExpandDims_1/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/global_average_pooling2d/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/dense/MatMul/ReadVariableOp", "shape": [32, 16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/dense/BiasAdd/ReadVariableOp", "shape": [16], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/dense_1/MatMul/ReadVariableOp", "shape": [16, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/dense_1/BiasAdd/ReadVariableOp", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/multiply/ExpandDims/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/multiply/ExpandDims_1/dim", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/white_norm/Const", "shape": [], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv5/Conv2D/ReadVariableOp", "shape": [1, 1, 32, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv5/BiasAdd/ReadVariableOp", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/flatten/strided_slice/stack", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/flatten/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/flatten/strided_slice/stack_2", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/flatten/Reshape/shape/1", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model_1/eyes_right/MatMul/ReadVariableOp", "shape": [3200, 1], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/eyes_right/add/ReadVariableOp", "shape": [3], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv2d/Conv2D_weights", "shape": [3, 3, 3, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv2d/Conv2D_bn_offset", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv2/Conv2D_weights", "shape": [3, 3, 32, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv2/Conv2D_bn_offset", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv3/Conv2D_weights", "shape": [3, 3, 32, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv3/Conv2D_bn_offset", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv4/Conv2D_weights", "shape": [3, 3, 32, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/model_1/conv4/Conv2D_bn_offset", "shape": [32], "dtype": "float32"}]}]}