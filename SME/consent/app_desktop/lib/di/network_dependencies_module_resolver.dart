import 'package:consent_desktop/interceptors/two_fa_interceptor.dart';
import 'package:data/auth_manager/auth_manager.dart';
import 'package:data/graph_ql_network_manager/graph_ql_network_manager.dart'
    hide Headers;
import 'package:di/di.dart';
import 'package:dio/dio.dart';
import 'package:feature_2fa_api/feature_2fa_api.dart';
import 'package:logging_api/logging.dart';
import 'package:sme_configuration/configuration.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/rest_network_manager.dart' hide Headers;
import 'package:wio_sme_reporting_impl/index.dart';

class NetworkDependenciesModuleResolver {
  NetworkDependenciesModuleResolver._();

  static void register() {
    DependencyProvider.registerLazySingleton<ApiEnvironmentMapFactory>(
      () => ApiEnvironmentMapFactoryWeb(
        envProvider: DependencyProvider.get<EnvProvider>(),
        platformInfo: DependencyProvider.get<PlatformInfo>(),
      ),
    );

    DependencyProvider.registerLazySingleton<IGraphQLNetworkManagerFactory>(
      GraphQLNetworkManagerFactory.new,
    );

    DependencyProvider.registerLazySingleton<IAuthManager>(
      () => EphemeralAuthManager(
        analyticsConfigurator: DependencyProvider.get<AnalyticsConfigurator>(),
      ),
    );

    DependencyProvider.registerLazySingleton<IGraphQLNetworkManagerFacade>(
      () => GraphQLNetworkManagerFacade(
        factory: DependencyProvider.get<IGraphQLNetworkManagerFactory>(),
        authManager: DependencyProvider.get<IAuthManager>(),
      ),
    );

    _register2FaNetworkDependencies();
  }

  static void _register2FaNetworkDependencies() {
    DependencyProvider.registerLazySingleton<TwoFaDioInterceptor>(
      () => TwoFaDioInterceptor(
        dio: DependencyProvider.get<Dio>(),
        identityExceptionMapper:
            DependencyProvider.get<IdentityExceptionMapper>(),
        twoFaUIParamBridge: DependencyProvider.get<TwoFaUIParamBridge>(),
      ),
    );

    DependencyProvider.registerLazySingleton<Dio>(() => Dio());

    DependencyProvider.registerLazySingleton<IRestApiClient>(() {
      final dio = DependencyProvider.get<Dio>();
      dio.interceptors
        ..removeImplyContentTypeInterceptor()
        ..addAll([
          const DioDefaultContentTypeInterceptor(
            defaultContentType: Headers.jsonContentType,
          ),
          DependencyProvider.get<TwoFaDioInterceptor>(),
        ]);

      return DioClient(dio: dio);
    });
  }

  static Future<void> initialize(AppEnvironment environment) async {
    const isTwoFaEnabled = true;
    const apiEnvironmentMap = {
      AppEnvironment.dev: ApiEnvironment.dev,
      AppEnvironment.pre: ApiEnvironment.pre,
      AppEnvironment.prod: ApiEnvironment.prod,
    };

    final graphQlNetworkManager =
        DependencyProvider.get<IGraphQLNetworkManagerFacade>();
    final restManager = DependencyProvider.get<IRestApiClient>();
    final apiEnvironment = ArgumentError.checkNotNull(
      apiEnvironmentMap[environment],
    );

    final configFactory = DependencyProvider.get<ApiEnvironmentMapFactory>();
    final environments = await configFactory.makeGraphQLConfigMap(
      apiEnvironment: apiEnvironment,
      twoFaEnabled: isTwoFaEnabled,
    );
    final restConfig = await configFactory.makeRestApiConfig(
      apiEnvironment: apiEnvironment,
      twoFaEnabled: isTwoFaEnabled,
    );

    graphQlNetworkManager.initialize(
      environments: environments,
      cacheStrategyType: GraphQLCacheType.network,
      links: _buildGraphQlInterceptors(
        isDebugEnabled: environment.isDebug,
      ),
    );

    restManager.initialize(
      config: restConfig,
      interceptors: _buildRestApiInterceptors(
        isDebugEnabled: environment.isDebug,
      ),
    );
  }

  static List<InterceptorLink> _buildGraphQlInterceptors({
    required bool isDebugEnabled,
  }) {
    final logger = DependencyProvider.get<Logger>();
    final trackerFactory = DependencyProvider.get<AnalyticsTrackerFactory>();

    return [
      CorrelationIdInterceptor(),
      LoggingInterceptorLink(
        logger: logger,
        doLogResponseBody: isDebugEnabled,
        doLogRequestVariables: isDebugEnabled,
        loggingFilterConfig: const LoggingFilterConfig.logAll(),
      ),
      AnalyticsNetworkErrorInterceptorLink(
        tracker: trackerFactory.get('network'),
      ),
    ];
  }

  static List<RestApiInterceptor> _buildRestApiInterceptors({
    required bool isDebugEnabled,
  }) {
    final logger = DependencyProvider.get<Logger>();
    final trackerFactory = DependencyProvider.get<AnalyticsTrackerFactory>();

    return [
      UniqueRequestHeaderInterceptor.correlationIdInterceptor(),
      SmeRestAuthorizationHeaderInterceptor(
        authManager: DependencyProvider.get<IAuthManager>(),
      ),
      LoggingInterceptor(
        logger: logger,
        doSendRequestBody: isDebugEnabled,
        doSendResponseBody: isDebugEnabled,
        doFilterHeaders: !isDebugEnabled,
      ),
      AnalyticsNetworkErrorRestApiInterceptor(
        tracker: trackerFactory.get('network'),
      ),
    ];
  }
}
