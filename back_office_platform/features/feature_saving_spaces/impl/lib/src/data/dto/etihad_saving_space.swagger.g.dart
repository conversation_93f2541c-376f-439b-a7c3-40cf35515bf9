// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'etihad_saving_space.swagger.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Details _$DetailsFromJson(Map<String, dynamic> json) => Details(
      field: json['Field'] as String?,
      $Value: json['Value'],
    );

Map<String, dynamic> _$DetailsToJson(Details instance) => <String, dynamic>{
      if (instance.field case final value?) 'Field': value,
      if (instance.$Value case final value?) 'Value': value,
    };

ApiError _$ApiErrorFromJson(Map<String, dynamic> json) => ApiError(
      id: json['id'] as String?,
      code: json['code'] as String?,
      message: json['message'] as String?,
      errorFormat: json['errorFormat'] as String?,
      context: json['context'] as Map<String, dynamic>?,
      details: (json['details'] as List<dynamic>?)
              ?.map((e) => ApiErrorDetail.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$ApiErrorToJson(ApiError instance) => <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.code case final value?) 'code': value,
      if (instance.message case final value?) 'message': value,
      if (instance.errorFormat case final value?) 'errorFormat': value,
      if (instance.context case final value?) 'context': value,
      if (instance.details?.map((e) => e.toJson()).toList() case final value?)
        'details': value,
    };

ApiErrorDetail _$ApiErrorDetailFromJson(Map<String, dynamic> json) =>
    ApiErrorDetail(
      detailCode: json['detailCode'] as String?,
      description: json['description'] as String?,
      errorFormat: json['errorFormat'] as String?,
      context: json['context'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ApiErrorDetailToJson(ApiErrorDetail instance) =>
    <String, dynamic>{
      if (instance.detailCode case final value?) 'detailCode': value,
      if (instance.description case final value?) 'description': value,
      if (instance.errorFormat case final value?) 'errorFormat': value,
      if (instance.context case final value?) 'context': value,
    };

FixedTermEtihadDepositClosureInfoResponse
    _$FixedTermEtihadDepositClosureInfoResponseFromJson(
            Map<String, dynamic> json) =>
        FixedTermEtihadDepositClosureInfoResponse(
          tenor: (json['Tenor'] as num).toInt(),
          incompletePeriodInMonths:
              (json['IncompletePeriodInMonths'] as num).toInt(),
          penaltyRate: (json['PenaltyRate'] as num).toDouble(),
          totalMiles: (json['TotalMiles'] as num).toDouble(),
          penaltyAmount: (json['PenaltyAmount'] as num).toDouble(),
          currency: fixedTermEtihadDepositClosureInfoResponseCurrencyFromJson(
              json['Currency']),
          maturityDate: DateTime.parse(json['MaturityDate'] as String),
          startingDate: DateTime.parse(json['StartingDate'] as String),
        );

Map<String, dynamic> _$FixedTermEtihadDepositClosureInfoResponseToJson(
        FixedTermEtihadDepositClosureInfoResponse instance) =>
    <String, dynamic>{
      'Tenor': instance.tenor,
      'IncompletePeriodInMonths': instance.incompletePeriodInMonths,
      'PenaltyRate': instance.penaltyRate,
      'TotalMiles': instance.totalMiles,
      'PenaltyAmount': instance.penaltyAmount,
      if (fixedTermEtihadDepositClosureInfoResponseCurrencyToJson(
              instance.currency)
          case final value?)
        'Currency': value,
      if (_dateToJson(instance.maturityDate) case final value?)
        'MaturityDate': value,
      if (_dateToJson(instance.startingDate) case final value?)
        'StartingDate': value,
    };
