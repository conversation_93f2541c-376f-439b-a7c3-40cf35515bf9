import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_error_domain_api/models/api_exception.dart';
import 'package:wio_feature_saving_space_impl/src/data/dto/etihad_saving_space.swagger.dart';
import 'package:wio_feature_saving_space_impl/src/data/dto/saving_space_recurring_rule.swagger.dart'
    as recurring_rule;
import 'package:wio_feature_saving_space_impl/src/data/dto/saving_space_schema.swagger.dart';
import 'package:wio_feature_saving_space_impl/src/data/service/exception/saving_space_exception_dto.dart';
import 'package:wio_feature_saving_space_impl/src/data/service/saving_space_service.dart';

typedef _Json = Map<String, Object?>;

class ServiceConstants {
  static const _baseUrl = '/account-details/api/v1';
  static const _getSavingSpaces = '$_baseUrl/accounts';

  static String savingAccountDetails(String accountId) =>
      '$_baseUrl/accounts/$accountId';

  static const savingAccountRecurringDetails =
      '/rules-core/api/v1/saving-spaces';

  static String etihadSavingSpaceClosureInfo(
    String accountId,
    String customerId,
  ) =>
      '/retail-accounts/api/v1/internal/fixed-term-etihad-deposits/$accountId/close/$customerId';

  static const accountType = 'type';
  static const spaceId = 'accountId';
  static const customerId = 'customerIdentifier';
  static const clientKey = 'BACKOFFICE';
  static const savingSpaceIds = 'savingSpaceIds';
}

class SavingSpaceServiceImpl extends RestApiService
    implements SavingSpaceService {
  final IRestApiClient _restApiClient;

  SavingSpaceServiceImpl({
    required IRestApiClient restApiClient,
  }) : _restApiClient = restApiClient;

  @override
  Future<AccountsDetailsListResponse> getAllSavingSpaces({
    required String customerId,
  }) {
    return execute<AccountsDetailsListResponse, HttpRequestException>(
      _restApiClient.execute<Object?>(
        RestApiRequest(
          ServiceConstants._getSavingSpaces,
          method: HttpRequestMethod.get,
          queryParameters: {
            ServiceConstants.customerId: customerId,
          },
          headers: {
            'x-client-id': ServiceConstants.clientKey,
          },
        ),
      ),
      (json) => AccountsDetailsListResponse.fromJson(
        json as _Json,
      ),
      errorJsonParser: _parseException,
    );
  } //InternalAccountDetails

  @override
  Future<AccountDetailsResponse> getSavingSpaceDetails({
    required String spaceId,
  }) {
    return execute<AccountDetailsResponse, HttpRequestException>(
      _restApiClient.execute<Object?>(
        RestApiRequest(
          ServiceConstants.savingAccountDetails(spaceId),
          method: HttpRequestMethod.get,
          headers: {
            'x-client-id': ServiceConstants.clientKey,
          },
        ),
      ),
      (json) => AccountDetailsResponse.fromJson(
        json as _Json,
      ),
      errorJsonParser: _parseException,
    );
  }

  @override
  Future<recurring_rule.SavingSpacesRulesDetailsResponse>
      getSavingSpaceRecurringDetails({
    required String spaceId,
  }) {
    return execute<recurring_rule.SavingSpacesRulesDetailsResponse,
        HttpRequestException>(
      _restApiClient.execute<Object?>(
        RestApiRequest(
          ServiceConstants.savingAccountRecurringDetails,
          queryParameters: {
            ServiceConstants.savingSpaceIds: spaceId,
          },
          method: HttpRequestMethod.get,
          headers: {
            'x-client-id': ServiceConstants.clientKey,
          },
        ),
      ),
      (json) => recurring_rule.SavingSpacesRulesDetailsResponse.fromJson(
        json as _Json,
      ),
      errorJsonParser: _parseException,
    );
  }

  HttpRequestException _parseException(Object? data, {String? correlationId}) {
    final json = data as _Json;
    return _mapToApiException(json);
  }

  SavingSpaceExceptionDto _mapToApiException(Map<String, Object?> error) {
    final apiError = ErrorMessage.fromJson(error);

    final exception = ApiException<Object?>(
      id: apiError.id ?? 'UNAVAILABLE',
      code: apiError.code ?? 'ERROR_NOT_SPECIFIED_IN_RESPONSE',
      message: apiError.description,
      errorFormat: apiError.additionalInfo,
      context: apiError.context,
    );

    return SavingSpaceExceptionDto(content: exception);
  }

  @override
  Future<FixedTermEtihadDepositClosureInfoResponse>
      getEtihadDepositClosureInfo({
    required String accountId,
    required String customerId,
  }) {
    return execute<FixedTermEtihadDepositClosureInfoResponse,
        HttpRequestException>(
      _restApiClient.execute<Object?>(
        RestApiRequest(
          ServiceConstants.etihadSavingSpaceClosureInfo(accountId, customerId),
          method: HttpRequestMethod.get,
          headers: {
            'x-client-id': ServiceConstants.clientKey,
          },
        ),
      ),
      (json) => FixedTermEtihadDepositClosureInfoResponse.fromJson(
        json as _Json,
      ),
      errorJsonParser: _parseException,
    );
  }
}
