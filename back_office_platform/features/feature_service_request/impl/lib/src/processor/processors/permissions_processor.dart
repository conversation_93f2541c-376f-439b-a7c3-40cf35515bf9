import 'package:wio_feature_permissions_api/permissions_api.dart';
import 'package:wio_feature_service_request_api/service_request_api.dart';
import 'package:wio_feature_service_request_impl/src/processor/processor.dart';
import 'package:wio_feature_service_request_impl/src/processor/service_request_chained_processor.dart';

class FormPermissionsProcessor implements Processor<SrForms> {
  final Set<String> _permissionFilterExceptions;
  final ServiceRequestInteractor _serviceRequestInteractor;
  final PermissionsResolver _permissionsResolver;

  FormPermissionsProcessor({
    required ServiceRequestInteractor serviceRequestInteractor,
    required PermissionsResolver permissionsResolver,
  })  : _serviceRequestInteractor = serviceRequestInteractor,
        _permissionsResolver = permissionsResolver,
        _permissionFilterExceptions = {
          'CREDIT_RESTRICTION',
          'DEBIT_RESTRICTION',
          'LIFT_CREDIT_RESTRICTION',
          'LIFT_DEBIT_RESTRICTION',
        };

  @override
  SrForms process(SrForms input) {
    final processOptionsMap = _serviceRequestInteractor.getFormConfigOptions();

    final filteredAndUpdatedConfigs = <FormConfig>[];

    for (final formConfig in input) {
      /// Check if we need to skip permission checking for this form
      if (_permissionFilterExceptions
          .any((item) => item == formConfig.processName)) {
        filteredAndUpdatedConfigs.add(formConfig);
        continue;
      }

      /// Get the process name from the form config
      final formProcessNameKey = _getProcessNameKey(formConfig);

      final matchingOption = processOptionsMap[formProcessNameKey];

      if (matchingOption == null) {
        continue;
      }

      var hasPermission = false;

      if (matchingOption.makerGroups.isNotEmpty) {
        hasPermission = matchingOption.makerGroups.any(
          (permission) => _permissionsResolver.resolve(permission),
        );
      }

      /// If the user does not have permission, skip the form
      if (!hasPermission) {
        continue;
      }

      filteredAndUpdatedConfigs.add(formConfig);
    }

    return filteredAndUpdatedConfigs;
  }

  String _getProcessNameKey(FormConfig formConfig) {
    return formConfig.processName;
  }
}
