import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_app_common_api/domain/app_context_interactor.dart';
import 'package:wio_feature_common_error_handler_api/common_error_handler_api.dart';
import 'package:wio_feature_customer_idv_api/customer_idv_api.dart';
import 'package:wio_feature_service_request_api/domain/model/form_options.dart';
import 'package:wio_feature_service_request_api/domain/model/service_request_creation_model.dart';
import 'package:wio_feature_service_request_api/domain/model/service_request_creation_response.dart';
import 'package:wio_feature_service_request_api/service_request_api.dart';
import 'package:wio_feature_service_request_impl/src/data/service_request_mapper.dart';
import 'package:wio_feature_service_request_impl/src/service/sr_service.dart';

class ServiceRequestRepositoryImpl implements ServiceRequestRepository {
  final CustomerProvider _customerProvider;
  final ServiceRequestMapper _mapper;
  final SrService _service;
  final AppContextInteractor _appContextInteractor;

  // Form configs cache
  final _formConfigsCache = <String, FormOption>{};

  ServiceRequestRepositoryImpl({
    required CustomerProvider customerProvider,
    required SrService service,
    required ServiceRequestMapper mapper,
    required AppContextInteractor appContextInteractor,
  })  : _customerProvider = customerProvider,
        _service = service,
        _mapper = mapper,
        _appContextInteractor = appContextInteractor;

  @override
  Future<ServiceRequestCreationResponse> createServiceRequest(
    ServiceRequestCreationModel serviceRequestCreationModel,
  ) async {
    final customer = _getCurrentCustomer();

    final appContext = _appContextInteractor.getAppContext();

    final requestDto = _mapper.toCreateServiceRequestDtoModel(
      domain: serviceRequestCreationModel,
      customer: customer,
      appContext: appContext,
    );

    try {
      final response = await _service.createServiceRequest(requestDto);
      return _mapper.mapToCreationRequestResponse(response);
    } on Object catch (error, st) {
      if (error is RestApiException && error.statusCode == 409) {
        final isErrorDataAMap = error.response?.data != null &&
            error.response!.data! is Map<String, dynamic>;
        final errorDataMap = isErrorDataAMap
            ? error.response!.data as Map<String, dynamic>
            : null;

        final bool hasDescription;

        if (!isErrorDataAMap) {
          hasDescription = false;
        } else {
          hasDescription = errorDataMap!.containsKey('description');
        }

        final errorDesc =
            hasDescription ? errorDataMap!['description'] as String : null;
        throw CommonError.message(
          errorMessage: errorDesc ?? '',
          identifier: const ErrorIdentifier.none(),
          errorCode: 'FE_RESOURCE_DUPLICATE_ERROR',
          stackTrace: st,
          payload: 'Case cannot be created',
        );
      } else {
        rethrow;
      }
    }
  }

  CurrentCustomer _getCurrentCustomer() {
    final customer = _customerProvider.getCurrentCustomerDetails();
    if (customer == null) {
      throw CommonError(
        identifier: const ErrorIdentifier.none(),
        errorCode: 'FE_CURRENT_CUSTOMER_IS_NULL',
        stackTrace: StackTrace.current,
        payload: 'A null object was returned from '
            'CustomerProvider.getCurrentCustomerDetails(). '
            'At this stage, current customer should not be null',
      );
    }
    return customer;
  }

  @override
  Future<ServiceRequestCreationResponse> createSyncServiceRequest({
    required Map<String, Object> payload,
    required ServiceRequestType type,
  }) async {
    final customer = _getCurrentCustomer();

    final requestDto = _mapper.syncSrCreationPayloadDto(
      payload: payload,
      customer: customer,
      srType: type,
    );

    final endpoint = _mapper.parseEndpoint(type);

    final response = await _service.createSyncSr(
      dto: requestDto,
      endpoint: endpoint,
    );

    return _mapper.mapToCreationRequestResponse(response);
  }

  @override
  Map<String, FormOption> getFormConfigOptions() {
    return _formConfigsCache;
  }

  @override
  Future<void> getFormConfigs() async {
    final dto = await _service.getSrOptions();

    final formOptions = _mapper.mapToOptions(dto);

    _formConfigsCache.addAll(formOptions);
  }
}
