import 'package:wio_feature_service_request_api/domain/model/export.dart';
import 'package:wio_feature_service_request_api/service_request_api.dart';
import 'package:wio_feature_service_request_ui_desktop/src/form_configs/department_generic_sr_configs/base_department_generic_sr_form_config.dart';
import 'package:wio_feature_service_request_ui_desktop/src/form_configs/department_generic_sr_configs/fall_back_generic_sr_form_config.dart';

class DepartmentGenericSrHandler implements ServiceRequestFormHandler {
  final ServiceRequestInteractor _serviceRequestInteractor;

  DepartmentGenericSrHandler({
    required ServiceRequestInteractor serviceRequestInteractor,
  }) : _serviceRequestInteractor = serviceRequestInteractor;

  @override
  List<FormConfig>? getFormConfigs(Object? serviceRequestEntity) {
    if (serviceRequestEntity is! DepartmentGenericSrEntity) {
      return null;
    }
    final formOptions = _serviceRequestInteractor.getFormConfigOptions();

    if (formOptions.isEmpty) {
      return [];
    }

    final genericSrFormOptions =
        formOptions.values.where((option) => option.srType == 'Generic');

    final derivedSrFormConfigs = genericSrFormOptions
        .map(
          (option) => _OverriddenFormConfig(
            formConfig: BaseDepartmentGenericSrFormConfig(),
            updatedTitle: option.title,
            updatedProcessName: option.processName,
          ),
        )
        .toList();

    return [
      ...derivedSrFormConfigs,
      FallbackGenericSrFormConfig(),
    ];
  }

  @override
  String toString() => 'DepartmentGenericSrHandler';
}

class _OverriddenFormConfig implements FormConfig {
  final FormConfig formConfig;
  final String updatedTitle;
  final String updatedProcessName;

  const _OverriddenFormConfig({
    required this.formConfig,
    required this.updatedTitle,
    required this.updatedProcessName,
  });

  @override
  String get processName => updatedProcessName;

  @override
  String get title => updatedTitle;

  @override
  List<Disclaimers> alertField() => formConfig.alertField();

  @override
  bool get attachmentRequired => formConfig.attachmentRequired;

  @override
  List<EntityField> entityFields(Object entity) {
    return formConfig.entityFields(entity);
  }

  @override
  Map<FormFieldId, FormField> fields(Object? entity) {
    return formConfig.fields(entity);
  }

  @override
  bool get hideNotes => formConfig.hideNotes;

  @override
  bool get notesRequired => formConfig.notesRequired;

  @override
  Future<Payload?> preCaseCreationCallback(Payload payload) async =>
      formConfig.preCaseCreationCallback(payload);

  @override
  bool get hasPriority => formConfig.hasPriority;

  @override
  bool get hideAttachment => formConfig.hideAttachment;
}
