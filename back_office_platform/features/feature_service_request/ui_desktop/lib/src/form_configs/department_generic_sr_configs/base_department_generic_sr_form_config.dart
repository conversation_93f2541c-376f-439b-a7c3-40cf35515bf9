import 'package:wio_feature_service_request_api/service_request_api.dart';

class BaseDepartmentGenericSrFormConfig implements FormConfig {
  @override
  String get processName => 'NEEDS_DECORATION_IN_HANDLER';

  @override
  String get title => 'Generic SR';

  @override
  List<Disclaimers> alertField() => [];

  @override
  bool get attachmentRequired => false;

  @override
  List<EntityField> entityFields(Object entity) => [];

  @override
  Map<FormFieldId, FormField> fields(Object? entity) => {};

  @override
  bool get hideNotes => false;

  @override
  bool get notesRequired => true;

  @override
  Future<Payload?> preCaseCreationCallback(Payload payload) async {
    return payload;
  }

  @override
  bool get hideAttachment => false;

  @override
  bool get hasPriority => true;
}
