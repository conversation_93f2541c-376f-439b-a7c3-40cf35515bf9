// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'service_request_form_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ServiceRequestFormState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(
            BackOfficeCasePriority? priority,
            String notes,
            List<ServiceRequestAttachment> attachments,
            List<Transaction> selectedTransactions,
            Object? serviceRequestEntity)
        ready,
    required TResult Function(
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            BackOfficeCasePriority? priority,
            String notes)
        processing,
    required TResult Function(
            BackOfficeCasePriority priority,
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            String notes)
        completed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(
            BackOfficeCasePriority? priority,
            String notes,
            List<ServiceRequestAttachment> attachments,
            List<Transaction> selectedTransactions,
            Object? serviceRequestEntity)?
        ready,
    TResult? Function(
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            BackOfficeCasePriority? priority,
            String notes)?
        processing,
    TResult? Function(
            BackOfficeCasePriority priority,
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            String notes)?
        completed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(
            BackOfficeCasePriority? priority,
            String notes,
            List<ServiceRequestAttachment> attachments,
            List<Transaction> selectedTransactions,
            Object? serviceRequestEntity)?
        ready,
    TResult Function(
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            BackOfficeCasePriority? priority,
            String notes)?
        processing,
    TResult Function(
            BackOfficeCasePriority priority,
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            String notes)?
        completed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ServiceRequestFormInitialState value) initial,
    required TResult Function(_ServiceRequestFormReadyState value) ready,
    required TResult Function(_ServiceRequestFormProcessingState value)
        processing,
    required TResult Function(_ServiceRequestFormCompletedState value)
        completed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ServiceRequestFormInitialState value)? initial,
    TResult? Function(_ServiceRequestFormReadyState value)? ready,
    TResult? Function(_ServiceRequestFormProcessingState value)? processing,
    TResult? Function(_ServiceRequestFormCompletedState value)? completed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ServiceRequestFormInitialState value)? initial,
    TResult Function(_ServiceRequestFormReadyState value)? ready,
    TResult Function(_ServiceRequestFormProcessingState value)? processing,
    TResult Function(_ServiceRequestFormCompletedState value)? completed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ServiceRequestFormStateCopyWith<$Res> {
  factory $ServiceRequestFormStateCopyWith(ServiceRequestFormState value,
          $Res Function(ServiceRequestFormState) then) =
      _$ServiceRequestFormStateCopyWithImpl<$Res, ServiceRequestFormState>;
}

/// @nodoc
class _$ServiceRequestFormStateCopyWithImpl<$Res,
        $Val extends ServiceRequestFormState>
    implements $ServiceRequestFormStateCopyWith<$Res> {
  _$ServiceRequestFormStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ServiceRequestFormState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$ServiceRequestFormInitialStateImplCopyWith<$Res> {
  factory _$$ServiceRequestFormInitialStateImplCopyWith(
          _$ServiceRequestFormInitialStateImpl value,
          $Res Function(_$ServiceRequestFormInitialStateImpl) then) =
      __$$ServiceRequestFormInitialStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ServiceRequestFormInitialStateImplCopyWithImpl<$Res>
    extends _$ServiceRequestFormStateCopyWithImpl<$Res,
        _$ServiceRequestFormInitialStateImpl>
    implements _$$ServiceRequestFormInitialStateImplCopyWith<$Res> {
  __$$ServiceRequestFormInitialStateImplCopyWithImpl(
      _$ServiceRequestFormInitialStateImpl _value,
      $Res Function(_$ServiceRequestFormInitialStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ServiceRequestFormState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ServiceRequestFormInitialStateImpl
    extends _ServiceRequestFormInitialState {
  const _$ServiceRequestFormInitialStateImpl() : super._();

  @override
  String toString() {
    return 'ServiceRequestFormState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ServiceRequestFormInitialStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(
            BackOfficeCasePriority? priority,
            String notes,
            List<ServiceRequestAttachment> attachments,
            List<Transaction> selectedTransactions,
            Object? serviceRequestEntity)
        ready,
    required TResult Function(
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            BackOfficeCasePriority? priority,
            String notes)
        processing,
    required TResult Function(
            BackOfficeCasePriority priority,
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            String notes)
        completed,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(
            BackOfficeCasePriority? priority,
            String notes,
            List<ServiceRequestAttachment> attachments,
            List<Transaction> selectedTransactions,
            Object? serviceRequestEntity)?
        ready,
    TResult? Function(
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            BackOfficeCasePriority? priority,
            String notes)?
        processing,
    TResult? Function(
            BackOfficeCasePriority priority,
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            String notes)?
        completed,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(
            BackOfficeCasePriority? priority,
            String notes,
            List<ServiceRequestAttachment> attachments,
            List<Transaction> selectedTransactions,
            Object? serviceRequestEntity)?
        ready,
    TResult Function(
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            BackOfficeCasePriority? priority,
            String notes)?
        processing,
    TResult Function(
            BackOfficeCasePriority priority,
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            String notes)?
        completed,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ServiceRequestFormInitialState value) initial,
    required TResult Function(_ServiceRequestFormReadyState value) ready,
    required TResult Function(_ServiceRequestFormProcessingState value)
        processing,
    required TResult Function(_ServiceRequestFormCompletedState value)
        completed,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ServiceRequestFormInitialState value)? initial,
    TResult? Function(_ServiceRequestFormReadyState value)? ready,
    TResult? Function(_ServiceRequestFormProcessingState value)? processing,
    TResult? Function(_ServiceRequestFormCompletedState value)? completed,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ServiceRequestFormInitialState value)? initial,
    TResult Function(_ServiceRequestFormReadyState value)? ready,
    TResult Function(_ServiceRequestFormProcessingState value)? processing,
    TResult Function(_ServiceRequestFormCompletedState value)? completed,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _ServiceRequestFormInitialState extends ServiceRequestFormState {
  const factory _ServiceRequestFormInitialState() =
      _$ServiceRequestFormInitialStateImpl;
  const _ServiceRequestFormInitialState._() : super._();
}

/// @nodoc
abstract class _$$ServiceRequestFormReadyStateImplCopyWith<$Res> {
  factory _$$ServiceRequestFormReadyStateImplCopyWith(
          _$ServiceRequestFormReadyStateImpl value,
          $Res Function(_$ServiceRequestFormReadyStateImpl) then) =
      __$$ServiceRequestFormReadyStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {BackOfficeCasePriority? priority,
      String notes,
      List<ServiceRequestAttachment> attachments,
      List<Transaction> selectedTransactions,
      Object? serviceRequestEntity});
}

/// @nodoc
class __$$ServiceRequestFormReadyStateImplCopyWithImpl<$Res>
    extends _$ServiceRequestFormStateCopyWithImpl<$Res,
        _$ServiceRequestFormReadyStateImpl>
    implements _$$ServiceRequestFormReadyStateImplCopyWith<$Res> {
  __$$ServiceRequestFormReadyStateImplCopyWithImpl(
      _$ServiceRequestFormReadyStateImpl _value,
      $Res Function(_$ServiceRequestFormReadyStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ServiceRequestFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? priority = freezed,
    Object? notes = null,
    Object? attachments = null,
    Object? selectedTransactions = null,
    Object? serviceRequestEntity = freezed,
  }) {
    return _then(_$ServiceRequestFormReadyStateImpl(
      priority: freezed == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as BackOfficeCasePriority?,
      notes: null == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String,
      attachments: null == attachments
          ? _value._attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<ServiceRequestAttachment>,
      selectedTransactions: null == selectedTransactions
          ? _value._selectedTransactions
          : selectedTransactions // ignore: cast_nullable_to_non_nullable
              as List<Transaction>,
      serviceRequestEntity: freezed == serviceRequestEntity
          ? _value.serviceRequestEntity
          : serviceRequestEntity,
    ));
  }
}

/// @nodoc

class _$ServiceRequestFormReadyStateImpl extends _ServiceRequestFormReadyState {
  const _$ServiceRequestFormReadyStateImpl(
      {this.priority,
      this.notes = '',
      final List<ServiceRequestAttachment> attachments = const [],
      final List<Transaction> selectedTransactions = const [],
      this.serviceRequestEntity})
      : _attachments = attachments,
        _selectedTransactions = selectedTransactions,
        super._();

  @override
  final BackOfficeCasePriority? priority;
  @override
  @JsonKey()
  final String notes;
  final List<ServiceRequestAttachment> _attachments;
  @override
  @JsonKey()
  List<ServiceRequestAttachment> get attachments {
    if (_attachments is EqualUnmodifiableListView) return _attachments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_attachments);
  }

  final List<Transaction> _selectedTransactions;
  @override
  @JsonKey()
  List<Transaction> get selectedTransactions {
    if (_selectedTransactions is EqualUnmodifiableListView)
      return _selectedTransactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedTransactions);
  }

  @override
  final Object? serviceRequestEntity;

  @override
  String toString() {
    return 'ServiceRequestFormState.ready(priority: $priority, notes: $notes, attachments: $attachments, selectedTransactions: $selectedTransactions, serviceRequestEntity: $serviceRequestEntity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ServiceRequestFormReadyStateImpl &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            const DeepCollectionEquality()
                .equals(other._attachments, _attachments) &&
            const DeepCollectionEquality()
                .equals(other._selectedTransactions, _selectedTransactions) &&
            const DeepCollectionEquality()
                .equals(other.serviceRequestEntity, serviceRequestEntity));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      priority,
      notes,
      const DeepCollectionEquality().hash(_attachments),
      const DeepCollectionEquality().hash(_selectedTransactions),
      const DeepCollectionEquality().hash(serviceRequestEntity));

  /// Create a copy of ServiceRequestFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ServiceRequestFormReadyStateImplCopyWith<
          _$ServiceRequestFormReadyStateImpl>
      get copyWith => __$$ServiceRequestFormReadyStateImplCopyWithImpl<
          _$ServiceRequestFormReadyStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(
            BackOfficeCasePriority? priority,
            String notes,
            List<ServiceRequestAttachment> attachments,
            List<Transaction> selectedTransactions,
            Object? serviceRequestEntity)
        ready,
    required TResult Function(
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            BackOfficeCasePriority? priority,
            String notes)
        processing,
    required TResult Function(
            BackOfficeCasePriority priority,
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            String notes)
        completed,
  }) {
    return ready(priority, notes, attachments, selectedTransactions,
        serviceRequestEntity);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(
            BackOfficeCasePriority? priority,
            String notes,
            List<ServiceRequestAttachment> attachments,
            List<Transaction> selectedTransactions,
            Object? serviceRequestEntity)?
        ready,
    TResult? Function(
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            BackOfficeCasePriority? priority,
            String notes)?
        processing,
    TResult? Function(
            BackOfficeCasePriority priority,
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            String notes)?
        completed,
  }) {
    return ready?.call(priority, notes, attachments, selectedTransactions,
        serviceRequestEntity);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(
            BackOfficeCasePriority? priority,
            String notes,
            List<ServiceRequestAttachment> attachments,
            List<Transaction> selectedTransactions,
            Object? serviceRequestEntity)?
        ready,
    TResult Function(
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            BackOfficeCasePriority? priority,
            String notes)?
        processing,
    TResult Function(
            BackOfficeCasePriority priority,
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            String notes)?
        completed,
    required TResult orElse(),
  }) {
    if (ready != null) {
      return ready(priority, notes, attachments, selectedTransactions,
          serviceRequestEntity);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ServiceRequestFormInitialState value) initial,
    required TResult Function(_ServiceRequestFormReadyState value) ready,
    required TResult Function(_ServiceRequestFormProcessingState value)
        processing,
    required TResult Function(_ServiceRequestFormCompletedState value)
        completed,
  }) {
    return ready(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ServiceRequestFormInitialState value)? initial,
    TResult? Function(_ServiceRequestFormReadyState value)? ready,
    TResult? Function(_ServiceRequestFormProcessingState value)? processing,
    TResult? Function(_ServiceRequestFormCompletedState value)? completed,
  }) {
    return ready?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ServiceRequestFormInitialState value)? initial,
    TResult Function(_ServiceRequestFormReadyState value)? ready,
    TResult Function(_ServiceRequestFormProcessingState value)? processing,
    TResult Function(_ServiceRequestFormCompletedState value)? completed,
    required TResult orElse(),
  }) {
    if (ready != null) {
      return ready(this);
    }
    return orElse();
  }
}

abstract class _ServiceRequestFormReadyState extends ServiceRequestFormState {
  const factory _ServiceRequestFormReadyState(
      {final BackOfficeCasePriority? priority,
      final String notes,
      final List<ServiceRequestAttachment> attachments,
      final List<Transaction> selectedTransactions,
      final Object? serviceRequestEntity}) = _$ServiceRequestFormReadyStateImpl;
  const _ServiceRequestFormReadyState._() : super._();

  BackOfficeCasePriority? get priority;
  String get notes;
  List<ServiceRequestAttachment> get attachments;
  List<Transaction> get selectedTransactions;
  Object? get serviceRequestEntity;

  /// Create a copy of ServiceRequestFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ServiceRequestFormReadyStateImplCopyWith<
          _$ServiceRequestFormReadyStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ServiceRequestFormProcessingStateImplCopyWith<$Res> {
  factory _$$ServiceRequestFormProcessingStateImplCopyWith(
          _$ServiceRequestFormProcessingStateImpl value,
          $Res Function(_$ServiceRequestFormProcessingStateImpl) then) =
      __$$ServiceRequestFormProcessingStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<ServiceRequestAttachment> attachments,
      Object? serviceRequestEntity,
      List<Transaction> selectedTransactions,
      BackOfficeCasePriority? priority,
      String notes});
}

/// @nodoc
class __$$ServiceRequestFormProcessingStateImplCopyWithImpl<$Res>
    extends _$ServiceRequestFormStateCopyWithImpl<$Res,
        _$ServiceRequestFormProcessingStateImpl>
    implements _$$ServiceRequestFormProcessingStateImplCopyWith<$Res> {
  __$$ServiceRequestFormProcessingStateImplCopyWithImpl(
      _$ServiceRequestFormProcessingStateImpl _value,
      $Res Function(_$ServiceRequestFormProcessingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ServiceRequestFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? attachments = null,
    Object? serviceRequestEntity = freezed,
    Object? selectedTransactions = null,
    Object? priority = freezed,
    Object? notes = null,
  }) {
    return _then(_$ServiceRequestFormProcessingStateImpl(
      attachments: null == attachments
          ? _value._attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<ServiceRequestAttachment>,
      serviceRequestEntity: freezed == serviceRequestEntity
          ? _value.serviceRequestEntity
          : serviceRequestEntity,
      selectedTransactions: null == selectedTransactions
          ? _value._selectedTransactions
          : selectedTransactions // ignore: cast_nullable_to_non_nullable
              as List<Transaction>,
      priority: freezed == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as BackOfficeCasePriority?,
      notes: null == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ServiceRequestFormProcessingStateImpl
    extends _ServiceRequestFormProcessingState {
  const _$ServiceRequestFormProcessingStateImpl(
      {required final List<ServiceRequestAttachment> attachments,
      required this.serviceRequestEntity,
      final List<Transaction> selectedTransactions = const [],
      this.priority,
      this.notes = ''})
      : _attachments = attachments,
        _selectedTransactions = selectedTransactions,
        super._();

  final List<ServiceRequestAttachment> _attachments;
  @override
  List<ServiceRequestAttachment> get attachments {
    if (_attachments is EqualUnmodifiableListView) return _attachments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_attachments);
  }

  @override
  final Object? serviceRequestEntity;
  final List<Transaction> _selectedTransactions;
  @override
  @JsonKey()
  List<Transaction> get selectedTransactions {
    if (_selectedTransactions is EqualUnmodifiableListView)
      return _selectedTransactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedTransactions);
  }

  @override
  final BackOfficeCasePriority? priority;
  @override
  @JsonKey()
  final String notes;

  @override
  String toString() {
    return 'ServiceRequestFormState.processing(attachments: $attachments, serviceRequestEntity: $serviceRequestEntity, selectedTransactions: $selectedTransactions, priority: $priority, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ServiceRequestFormProcessingStateImpl &&
            const DeepCollectionEquality()
                .equals(other._attachments, _attachments) &&
            const DeepCollectionEquality()
                .equals(other.serviceRequestEntity, serviceRequestEntity) &&
            const DeepCollectionEquality()
                .equals(other._selectedTransactions, _selectedTransactions) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_attachments),
      const DeepCollectionEquality().hash(serviceRequestEntity),
      const DeepCollectionEquality().hash(_selectedTransactions),
      priority,
      notes);

  /// Create a copy of ServiceRequestFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ServiceRequestFormProcessingStateImplCopyWith<
          _$ServiceRequestFormProcessingStateImpl>
      get copyWith => __$$ServiceRequestFormProcessingStateImplCopyWithImpl<
          _$ServiceRequestFormProcessingStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(
            BackOfficeCasePriority? priority,
            String notes,
            List<ServiceRequestAttachment> attachments,
            List<Transaction> selectedTransactions,
            Object? serviceRequestEntity)
        ready,
    required TResult Function(
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            BackOfficeCasePriority? priority,
            String notes)
        processing,
    required TResult Function(
            BackOfficeCasePriority priority,
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            String notes)
        completed,
  }) {
    return processing(attachments, serviceRequestEntity, selectedTransactions,
        priority, notes);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(
            BackOfficeCasePriority? priority,
            String notes,
            List<ServiceRequestAttachment> attachments,
            List<Transaction> selectedTransactions,
            Object? serviceRequestEntity)?
        ready,
    TResult? Function(
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            BackOfficeCasePriority? priority,
            String notes)?
        processing,
    TResult? Function(
            BackOfficeCasePriority priority,
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            String notes)?
        completed,
  }) {
    return processing?.call(attachments, serviceRequestEntity,
        selectedTransactions, priority, notes);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(
            BackOfficeCasePriority? priority,
            String notes,
            List<ServiceRequestAttachment> attachments,
            List<Transaction> selectedTransactions,
            Object? serviceRequestEntity)?
        ready,
    TResult Function(
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            BackOfficeCasePriority? priority,
            String notes)?
        processing,
    TResult Function(
            BackOfficeCasePriority priority,
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            String notes)?
        completed,
    required TResult orElse(),
  }) {
    if (processing != null) {
      return processing(attachments, serviceRequestEntity, selectedTransactions,
          priority, notes);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ServiceRequestFormInitialState value) initial,
    required TResult Function(_ServiceRequestFormReadyState value) ready,
    required TResult Function(_ServiceRequestFormProcessingState value)
        processing,
    required TResult Function(_ServiceRequestFormCompletedState value)
        completed,
  }) {
    return processing(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ServiceRequestFormInitialState value)? initial,
    TResult? Function(_ServiceRequestFormReadyState value)? ready,
    TResult? Function(_ServiceRequestFormProcessingState value)? processing,
    TResult? Function(_ServiceRequestFormCompletedState value)? completed,
  }) {
    return processing?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ServiceRequestFormInitialState value)? initial,
    TResult Function(_ServiceRequestFormReadyState value)? ready,
    TResult Function(_ServiceRequestFormProcessingState value)? processing,
    TResult Function(_ServiceRequestFormCompletedState value)? completed,
    required TResult orElse(),
  }) {
    if (processing != null) {
      return processing(this);
    }
    return orElse();
  }
}

abstract class _ServiceRequestFormProcessingState
    extends ServiceRequestFormState {
  const factory _ServiceRequestFormProcessingState(
      {required final List<ServiceRequestAttachment> attachments,
      required final Object? serviceRequestEntity,
      final List<Transaction> selectedTransactions,
      final BackOfficeCasePriority? priority,
      final String notes}) = _$ServiceRequestFormProcessingStateImpl;
  const _ServiceRequestFormProcessingState._() : super._();

  List<ServiceRequestAttachment> get attachments;
  Object? get serviceRequestEntity;
  List<Transaction> get selectedTransactions;
  BackOfficeCasePriority? get priority;
  String get notes;

  /// Create a copy of ServiceRequestFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ServiceRequestFormProcessingStateImplCopyWith<
          _$ServiceRequestFormProcessingStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ServiceRequestFormCompletedStateImplCopyWith<$Res> {
  factory _$$ServiceRequestFormCompletedStateImplCopyWith(
          _$ServiceRequestFormCompletedStateImpl value,
          $Res Function(_$ServiceRequestFormCompletedStateImpl) then) =
      __$$ServiceRequestFormCompletedStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {BackOfficeCasePriority priority,
      List<ServiceRequestAttachment> attachments,
      Object? serviceRequestEntity,
      List<Transaction> selectedTransactions,
      String notes});
}

/// @nodoc
class __$$ServiceRequestFormCompletedStateImplCopyWithImpl<$Res>
    extends _$ServiceRequestFormStateCopyWithImpl<$Res,
        _$ServiceRequestFormCompletedStateImpl>
    implements _$$ServiceRequestFormCompletedStateImplCopyWith<$Res> {
  __$$ServiceRequestFormCompletedStateImplCopyWithImpl(
      _$ServiceRequestFormCompletedStateImpl _value,
      $Res Function(_$ServiceRequestFormCompletedStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ServiceRequestFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? priority = null,
    Object? attachments = null,
    Object? serviceRequestEntity = freezed,
    Object? selectedTransactions = null,
    Object? notes = null,
  }) {
    return _then(_$ServiceRequestFormCompletedStateImpl(
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as BackOfficeCasePriority,
      attachments: null == attachments
          ? _value._attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<ServiceRequestAttachment>,
      serviceRequestEntity: freezed == serviceRequestEntity
          ? _value.serviceRequestEntity
          : serviceRequestEntity,
      selectedTransactions: null == selectedTransactions
          ? _value._selectedTransactions
          : selectedTransactions // ignore: cast_nullable_to_non_nullable
              as List<Transaction>,
      notes: null == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ServiceRequestFormCompletedStateImpl
    extends _ServiceRequestFormCompletedState {
  const _$ServiceRequestFormCompletedStateImpl(
      {required this.priority,
      required final List<ServiceRequestAttachment> attachments,
      required this.serviceRequestEntity,
      final List<Transaction> selectedTransactions = const [],
      this.notes = ''})
      : _attachments = attachments,
        _selectedTransactions = selectedTransactions,
        super._();

  @override
  final BackOfficeCasePriority priority;
  final List<ServiceRequestAttachment> _attachments;
  @override
  List<ServiceRequestAttachment> get attachments {
    if (_attachments is EqualUnmodifiableListView) return _attachments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_attachments);
  }

  @override
  final Object? serviceRequestEntity;
  final List<Transaction> _selectedTransactions;
  @override
  @JsonKey()
  List<Transaction> get selectedTransactions {
    if (_selectedTransactions is EqualUnmodifiableListView)
      return _selectedTransactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedTransactions);
  }

  @override
  @JsonKey()
  final String notes;

  @override
  String toString() {
    return 'ServiceRequestFormState.completed(priority: $priority, attachments: $attachments, serviceRequestEntity: $serviceRequestEntity, selectedTransactions: $selectedTransactions, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ServiceRequestFormCompletedStateImpl &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            const DeepCollectionEquality()
                .equals(other._attachments, _attachments) &&
            const DeepCollectionEquality()
                .equals(other.serviceRequestEntity, serviceRequestEntity) &&
            const DeepCollectionEquality()
                .equals(other._selectedTransactions, _selectedTransactions) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      priority,
      const DeepCollectionEquality().hash(_attachments),
      const DeepCollectionEquality().hash(serviceRequestEntity),
      const DeepCollectionEquality().hash(_selectedTransactions),
      notes);

  /// Create a copy of ServiceRequestFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ServiceRequestFormCompletedStateImplCopyWith<
          _$ServiceRequestFormCompletedStateImpl>
      get copyWith => __$$ServiceRequestFormCompletedStateImplCopyWithImpl<
          _$ServiceRequestFormCompletedStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(
            BackOfficeCasePriority? priority,
            String notes,
            List<ServiceRequestAttachment> attachments,
            List<Transaction> selectedTransactions,
            Object? serviceRequestEntity)
        ready,
    required TResult Function(
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            BackOfficeCasePriority? priority,
            String notes)
        processing,
    required TResult Function(
            BackOfficeCasePriority priority,
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            String notes)
        completed,
  }) {
    return completed(priority, attachments, serviceRequestEntity,
        selectedTransactions, notes);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(
            BackOfficeCasePriority? priority,
            String notes,
            List<ServiceRequestAttachment> attachments,
            List<Transaction> selectedTransactions,
            Object? serviceRequestEntity)?
        ready,
    TResult? Function(
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            BackOfficeCasePriority? priority,
            String notes)?
        processing,
    TResult? Function(
            BackOfficeCasePriority priority,
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            String notes)?
        completed,
  }) {
    return completed?.call(priority, attachments, serviceRequestEntity,
        selectedTransactions, notes);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(
            BackOfficeCasePriority? priority,
            String notes,
            List<ServiceRequestAttachment> attachments,
            List<Transaction> selectedTransactions,
            Object? serviceRequestEntity)?
        ready,
    TResult Function(
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            BackOfficeCasePriority? priority,
            String notes)?
        processing,
    TResult Function(
            BackOfficeCasePriority priority,
            List<ServiceRequestAttachment> attachments,
            Object? serviceRequestEntity,
            List<Transaction> selectedTransactions,
            String notes)?
        completed,
    required TResult orElse(),
  }) {
    if (completed != null) {
      return completed(priority, attachments, serviceRequestEntity,
          selectedTransactions, notes);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ServiceRequestFormInitialState value) initial,
    required TResult Function(_ServiceRequestFormReadyState value) ready,
    required TResult Function(_ServiceRequestFormProcessingState value)
        processing,
    required TResult Function(_ServiceRequestFormCompletedState value)
        completed,
  }) {
    return completed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ServiceRequestFormInitialState value)? initial,
    TResult? Function(_ServiceRequestFormReadyState value)? ready,
    TResult? Function(_ServiceRequestFormProcessingState value)? processing,
    TResult? Function(_ServiceRequestFormCompletedState value)? completed,
  }) {
    return completed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ServiceRequestFormInitialState value)? initial,
    TResult Function(_ServiceRequestFormReadyState value)? ready,
    TResult Function(_ServiceRequestFormProcessingState value)? processing,
    TResult Function(_ServiceRequestFormCompletedState value)? completed,
    required TResult orElse(),
  }) {
    if (completed != null) {
      return completed(this);
    }
    return orElse();
  }
}

abstract class _ServiceRequestFormCompletedState
    extends ServiceRequestFormState {
  const factory _ServiceRequestFormCompletedState(
      {required final BackOfficeCasePriority priority,
      required final List<ServiceRequestAttachment> attachments,
      required final Object? serviceRequestEntity,
      final List<Transaction> selectedTransactions,
      final String notes}) = _$ServiceRequestFormCompletedStateImpl;
  const _ServiceRequestFormCompletedState._() : super._();

  BackOfficeCasePriority get priority;
  List<ServiceRequestAttachment> get attachments;
  Object? get serviceRequestEntity;
  List<Transaction> get selectedTransactions;
  String get notes;

  /// Create a copy of ServiceRequestFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ServiceRequestFormCompletedStateImplCopyWith<
          _$ServiceRequestFormCompletedStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
