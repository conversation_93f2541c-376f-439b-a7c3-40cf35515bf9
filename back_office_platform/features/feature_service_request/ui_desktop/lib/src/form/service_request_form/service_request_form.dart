import 'package:di/di.dart';
import 'package:flutter/material.dart' hide <PERSON><PERSON><PERSON>, TextField, Banner;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_app_common_ui_desktop/feature_app_common_desktop_ui.dart';
import 'package:wio_feature_cases_api/tasks_api.dart';
import 'package:wio_feature_service_request_api/service_request_api.dart';
import 'package:wio_feature_service_request_api/service_request_form/config/fields/pre_fetch_dropdown_field.dart';
import 'package:wio_feature_service_request_ui_desktop/src/form/service_request_form/cubit/service_request_form_cubit.dart';
import 'package:wio_feature_service_request_ui_desktop/src/form/service_request_form/widgets/file_picker.dart';
import 'package:wio_feature_service_request_ui_desktop/src/form/service_request_form/widgets/form_fields/dropdown_sr_field.dart';
import 'package:wio_feature_service_request_ui_desktop/src/form_configs/sr_flow_state.dart';
import 'package:wio_feature_transactions_api/transactions_api.dart';
import 'package:wio_feature_transactions_ui_desktop/feature_transactions_desktop_ui.dart';

class ServiceRequestForm extends StatelessWidget {
  final FormConfig formConfig;
  final ValueSetter<TransactionPickerStage> onTransactionDropdownPressed;
  final Object? serviceRequestEntity;

  const ServiceRequestForm({
    required this.formConfig,
    required this.serviceRequestEntity,
    required this.onTransactionDropdownPressed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => DependencyProvider.get<ServiceRequestFormCubit>()
        ..init(serviceRequestEntity),
      child: _Content(
        formConfig: formConfig,
        serviceRequestEntity: serviceRequestEntity,
        onTransactionDropdownPressed: onTransactionDropdownPressed,
      ),
    );
  }
}

class _Content extends StatefulWidget {
  final FormConfig formConfig;

  final ValueSetter<TransactionPickerStage> onTransactionDropdownPressed;
  final Object? serviceRequestEntity;

  const _Content({
    required this.formConfig,
    required this.serviceRequestEntity,
    required this.onTransactionDropdownPressed,
  });

  @override
  State<_Content> createState() => _ContentState();
}

class _ContentState extends State<_Content> {
  final _controllers = <FormFieldId, TextEditingController>{};
  bool canCreate = false;
  final priorityDropdownFocusNode = FocusNode();

  @override
  void initState() {
    final inputFields = widget.formConfig.fields(widget.serviceRequestEntity);

    for (final field in inputFields.entries) {
      _controllers[field.key] = TextEditingController();
    }

    canCreate = _canCreate();

    super.initState();
  }

  @override
  void didUpdateWidget(covariant _Content oldWidget) {
    super.didUpdateWidget(oldWidget);
    canCreate = _canCreate();
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }

    priorityDropdownFocusNode.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = context.watch<ServiceRequestFormCubit>().state;
    final serviceRequestEntity = widget.serviceRequestEntity;
    final entityFields = serviceRequestEntity != null
        ? widget.formConfig.entityFields(serviceRequestEntity)
        : <EntityField>[];
    final alertFields = widget.formConfig.alertField()
      ..sort((a, b) => a.type.order);
    final fields = widget.formConfig.fields(serviceRequestEntity);

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: ConstrainedBox(
          constraints: const BoxConstraints(
            maxHeight: 530.0,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 12.0),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      if (alertFields.isNotEmpty)
                        for (final alert in alertFields) ...[
                          Banner(
                            BannerModel.simple(
                              text: alert.label,
                              showIcon: false,
                              backgroundColor: getDisclaimerTypeBg(alert.type),
                              borderColor: getDisclaimerTypeBorder(alert.type),
                            ),
                          ),
                          const SizedBox(height: 16.0),
                        ],
                      if (entityFields.isNotEmpty) ...[
                        _LinkedEntityDetails(fields: entityFields),
                        const SizedBox(height: 16.0),
                      ],
                      for (final entry in fields.entries)
                        if (entry.value is FreeTextField)
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: InputField(
                              isEnabled: !state.isProcessing,
                              readOnly: entry.value.isReadOnly,
                              model: InputFieldModel(
                                label: entry.value.isReadOnly
                                    ? entry.value.label
                                    : null,
                                hint: entry.value.label,
                                size: InputFieldSize.small,
                                theme: InputFieldTheme.light,
                                initialValue:
                                    (entry.value as FreeTextField).initialValue,
                              ),
                              maxLines: (entry.value as FreeTextField).maxLines,
                              onFieldSubmitted: (_) {},
                              onInputChanged: (value) =>
                                  _updateTextValue(entry.key, value),
                            ),
                          )
                        else if (entry.value is TextField)
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: InputField(
                              isEnabled: !state.isProcessing,
                              model: InputFieldModel(
                                hint: entry.value.label,
                                size: InputFieldSize.small,
                                theme: InputFieldTheme.light,
                              ),
                              onFieldSubmitted: (_) {},
                              onInputChanged: (value) =>
                                  _updateTextValue(entry.key, value),
                            ),
                          )
                        else if (entry.value is DateField)
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: DatePickerWidget(
                              date: DateTime.tryParse(
                                _controllers[entry.key]?.text ?? '',
                              ),
                              hint: entry.value.label,
                              onDateSelect: (dateTime) => _updateTextValue(
                                entry.key,
                                dateTime?.toIso8601String() ?? '',
                              ),
                            ),
                          )
                        else if (entry.value is DropdownField ||
                            entry.value is PreFetchDropdownField)
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: DropdownSrField(
                              field: entry.value,
                              onChanged: (value) =>
                                  _updateTextValue(entry.key, value ?? ''),
                            ),
                          )
                        else if (entry.value is TransactionsDropdownField) ...[
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: _TransactionsDropdownField(
                              hint: entry.value.label,
                              label: entry.value.label,
                              onTap: () => widget.onTransactionDropdownPressed(
                                TransactionPickerStage(
                                  selectedTransactions:
                                      state.selectedTransactions,
                                  onTransactionsSelected: (transactions) {
                                    context
                                        .read<ServiceRequestFormCubit>()
                                        .updateSelectedTransactions(
                                          transactions,
                                        );
                                  },
                                  context:
                                      (entry.value as TransactionsDropdownField)
                                          .context,
                                  accountIds:
                                      (entry.value as TransactionsDropdownField)
                                          .accountIds,
                                  detailsPayload:
                                      (entry.value as TransactionsDropdownField)
                                          .detailsPayload,
                                ),
                              ),
                              value: _getSelectedTransactionsText(
                                state.selectedTransactions.length,
                              ),
                            ),
                          ),
                          if (state.selectedTransactions.isNotEmpty)
                            ...state.selectedTransactions.map(
                              (transactions) => _TransactionTile(
                                transaction: transactions,
                              ),
                            ),
                        ],
                      const SizedBox(height: 4.0),
                      if (widget.formConfig.hasPriority) ...[
                        DropdownButtonFormField(
                          focusNode: priorityDropdownFocusNode,
                          decoration: InputDecoration(
                            labelText: state.selectedPriority == null
                                ? 'Select Priority'
                                : 'Priority',
                          ),
                          items: [
                            for (final priority
                                in BackOfficeCasePriority.values)
                              DropdownMenuItem(
                                value: priority,
                                child: Label(
                                  model: LabelModel(
                                    text: priority.displayString,
                                    color: CompanyColorPointer.primary3,
                                  ),
                                ),
                              ),
                          ],
                          onChanged:
                              state.isProcessing ? null : _onSelectPriority,
                        ),
                        const SizedBox(height: 4.0),
                      ],
                      if (!widget.formConfig.hideNotes) ...[
                        InputField(
                          isEnabled: !state.isProcessing,
                          model: InputFieldModel(
                            hint: !widget.formConfig.notesRequired
                                ? 'Notes (optional)'
                                : 'Notes',
                            size: InputFieldSize.small,
                            theme: InputFieldTheme.light,
                          ),
                          maxLines: 6,
                          onInputChanged:
                              state.isProcessing ? null : _onNotesChanged,
                          onFieldSubmitted: (_) {},
                        ),
                        const SizedBox(height: 16.0),
                      ],
                      if (!widget.formConfig.hideAttachment)
                        const FilePicker(fileAttachment: FileAttachment()),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16.0),
              SizedBox(
                width: double.infinity,
                child: Button(
                  model: ButtonModel(
                    title: 'Submit',
                    loading: state.isProcessing,
                  ),
                  onPressed: !canCreate ? null : _createServiceRequest,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getSelectedTransactionsText(int length) => switch (length) {
        0 => '',
        1 => '1 Transaction selected',
        _ => '$length Transactions selected',
      };

  void _onNotesChanged(String notes) {
    context.read<ServiceRequestFormCubit>().updateNotes(notes);

    setState(() {
      canCreate = _canCreate();
    });
  }

  void _onSelectPriority(BackOfficeCasePriority? selectedPriority) {
    if (selectedPriority == null) return;

    context.read<ServiceRequestFormCubit>().onSelectPriority(selectedPriority);

    setState(() {
      canCreate = _canCreate();
    });

    priorityDropdownFocusNode.unfocus();
  }

  void _updateTextValue(String controllerKey, String value) {
    _controllers[controllerKey]?.text = value;
    setState(() {
      canCreate = _canCreate();
    });
  }

  bool _canCreate() {
    final state = context.read<ServiceRequestFormCubit>().state;

    if (state.selectedPriority == null) {
      if (widget.formConfig.hasPriority) return false;
      context
          .read<ServiceRequestFormCubit>()
          .onSelectPriority(BackOfficeCasePriority.high);
    }

    if (state.selectedPriority == null && widget.formConfig.hasPriority) {
      return false;
    }

    if (widget.formConfig.notesRequired && state.srNotes.isEmpty) {
      return false;
    }

    final fields = widget.formConfig.fields(widget.serviceRequestEntity);

    for (final controllerEntry in _controllers.entries) {
      final controller = controllerEntry.value;
      final fieldKey = controllerEntry.key;

      final field = fields[fieldKey];

      if (field == null) continue;

      final fieldRequired = field.required;

      if (!fieldRequired) continue;

      if (field is TransactionsDropdownField &&
          state.selectedTransactions.isNotEmpty) {
        continue;
      }

      if (controller.text.isEmpty) {
        return false;
      }

      if (field is FreeTextField) {
        final regex = field.validationRegex;
        if (regex != null && !regex.hasMatch(controller.text)) {
          return false;
        }
      }
    }

    if (widget.formConfig.attachmentRequired && !state.hasAttachments) {
      return false;
    }

    return true;
  }

  Future<void> _createServiceRequest() async {
    final state = context.read<ServiceRequestFormCubit>().state;
    final cubit = context.read<ServiceRequestFormCubit>();
    final serviceRequestEntity = widget.serviceRequestEntity;

    final entityFields = serviceRequestEntity != null
        ? widget.formConfig.entityFields(serviceRequestEntity)
        : <EntityField>[];

    final formPayload = <String, Object>{
      for (final field in entityFields) field.payloadFieldName: field.value,
      for (final field
          in widget.formConfig.fields(serviceRequestEntity).entries)
        field.key: field.value is TransactionsDropdownField
            ? state.selectedTransactions
            : _controllers[field.key]?.text ?? '',
    };

    cubit.updateStateToProcessing();

    final effectivePayload =
        await widget.formConfig.preCaseCreationCallback(formPayload);

    cubit.updateStateToReady();

    if (!mounted) return;

    if (effectivePayload == null) return;

    final updatedState = context.read<ServiceRequestFormCubit>().state;

    if (updatedState.isProcessing) return;

    final processNameFromPayload = effectivePayload['PROCESS_NAME'] as String?;

    if (effectivePayload.containsKey('PROCESS_NAME')) {
      effectivePayload.remove('PROCESS_NAME');
    }

    await cubit.createServiceRequest(
      effectivePayload,
      processNameFromPayload ?? widget.formConfig.processName,
    );
  }

  CompanyColorPointer getDisclaimerTypeBg(DisclaimersType type) {
    return switch (type) {
      DisclaimersType.warning => CompanyColorPointer.surface20,
      DisclaimersType.positive => CompanyColorPointer.surface8,
      DisclaimersType.negative => CompanyColorPointer.surface18,
    };
  }

  CompanyColorPointer getDisclaimerTypeBorder(DisclaimersType type) {
    return switch (type) {
      DisclaimersType.warning => CompanyColorPointer.secondary12,
      DisclaimersType.positive => CompanyColorPointer.secondary7,
      DisclaimersType.negative => CompanyColorPointer.secondary7,
    };
  }
}

class _LinkedEntityDetails extends StatelessWidget {
  final List<EntityField> fields;

  const _LinkedEntityDetails({required this.fields});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Label(
          model: LabelModel(
            text: 'Linked Details',
            color: CompanyColorPointer.secondary4,
            textStyle: CompanyTextStylePointer.b4,
          ),
        ),
        DecoratedBox(
          decoration: BoxDecoration(
            color: CompanyColorPointer.surface7.colorOf(context),
            borderRadius: const BorderRadius.all(Radius.circular(8.0)),
          ),
          child: DetailsContainer(
            detailLayouts: List.generate(
              (fields.length / 2).ceil(),
              (index) {
                final firstField = fields[index * 2];
                final secondField = fields.length > index * 2 + 1
                    ? fields[index * 2 + 1]
                    : null;

                final secondFieldLabel = secondField?.label ?? '';
                final secondFieldValue = secondField?.value ?? '-';

                final secondDetail = secondField != null
                    ? DetailModel.simple(
                        label: secondFieldLabel,
                        value: secondFieldValue,
                      )
                    : null;

                return DetailLayoutType.doubleDetail(
                  firstDetail: DetailModel.simple(
                    label: firstField.label,
                    value: firstField.value,
                  ),
                  secondDetail: secondDetail,
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}

extension on BackOfficeCasePriority {
  String get displayString {
    switch (this) {
      case BackOfficeCasePriority.low:
        return 'Low';
      case BackOfficeCasePriority.medium:
        return 'Medium';
      case BackOfficeCasePriority.high:
        return 'High';
      case BackOfficeCasePriority.urgent:
        return 'Urgent';
    }
  }
}

class _TransactionsDropdownField extends StatefulWidget {
  final String label;
  final String hint;
  final String value;
  final VoidCallback onTap;

  const _TransactionsDropdownField({
    required this.hint,
    required this.label,
    required this.onTap,
    required this.value,
  });

  @override
  State<_TransactionsDropdownField> createState() =>
      __TransactionsDropdownFieldState();
}

class __TransactionsDropdownFieldState
    extends State<_TransactionsDropdownField> {
  TextEditingController? _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value);
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant _TransactionsDropdownField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _controller?.text = widget.value;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: AbsorbPointer(
        child: InputField(
          model: InputFieldModel(
            hint: widget.hint,
            label: widget.label,
            size: InputFieldSize.small,
            theme: InputFieldTheme.light,
            rightIcon: const GraphicAssetPointer.icon(
              CompanyIconPointer.chevron_down_mini,
            ),
          ),
          onFieldSubmitted: (_) {},
          controller: _controller,
          readOnly: true,
        ),
      ),
    );
  }
}

class _TransactionTile extends StatelessWidget {
  final Transaction transaction;

  const _TransactionTile({required this.transaction});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.only(bottom: 16.0),
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: CompanyColorPointer.surface7.colorOf(context),
          borderRadius: const BorderRadius.all(Radius.circular(12.0)),
        ),
        child: ExpansionTile(
          tilePadding: const EdgeInsetsDirectional.symmetric(horizontal: 16),
          childrenPadding: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
          trailing: const CompanyIcon(
            CompanyIconModel(
              icon: GraphicAssetPointer.icon(
                CompanyIconPointer.chevron_down_mini,
              ),
              size: CompanyIconSize.large,
            ),
          ),
          title: Label(
            model: LabelModel(
              text: 'Transaction: ${transaction.transactionIdentifier}',
              color: CompanyColorPointer.primary3,
              textStyle: CompanyTextStylePointer.b2medium,
            ),
          ),
          backgroundColor: CompanyColorPointer.surface7.colorOf(context),
          children: [
            DetailsContainer(
              detailLayouts: [
                DetailLayoutType.doubleDetail(
                  firstDetail: getAmountDetailsModal(transaction),
                  secondDetail: getTransactionStatusDetailsModal(transaction),
                ),
                ...getCommonDetailLayouts(transaction),
                ...getDomainDetailLayouts(transaction),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
