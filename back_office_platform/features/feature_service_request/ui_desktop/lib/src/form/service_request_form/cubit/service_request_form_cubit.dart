import 'package:file_picker/file_picker.dart';
import 'package:logging_api/logging.dart';
import 'package:ui/cubit/base_cubit.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:uuid/uuid.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_cases_api/models/enums/backoffice_case_priority.dart';
import 'package:wio_feature_common_error_handler_api/common_error_handler_api.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_files_api/files_api.dart';
import 'package:wio_feature_service_request_api/domain/model/export.dart';
import 'package:wio_feature_service_request_api/domain/model/service_request_creation_model.dart';
import 'package:wio_feature_service_request_api/service_request_api.dart';
import 'package:wio_feature_service_request_ui_desktop/src/form/service_request_form/cubit/service_request_form_state.dart';
import 'package:wio_feature_transactions_api/transactions_api.dart';

class ServiceRequestFormCubit extends BaseCubit<ServiceRequestFormState> {
  final Uuid _uuid;
  final ServiceRequestInteractor _interactor;
  final ToastMessageProvider _toastMessageProvider;
  final CommonErrorHandler _commonErrorHandler;
  final Logger _logger;
  final FilesInteractor _filesInteractor;
  final FilePicker _filePicker;
  final NavigationProvider _navigationProvider;

  ServiceRequestFormCubit({
    required ServiceRequestInteractor interactor,
    required ToastMessageProvider toastMessageProvider,
    required CommonErrorHandler commonErrorHandler,
    required Logger logger,
    required FilesInteractor filesInteractor,
    required FilePicker filePicker,
    required NavigationProvider navigationProvider,
    required Uuid uuid,
  })  : _interactor = interactor,
        _toastMessageProvider = toastMessageProvider,
        _commonErrorHandler = commonErrorHandler,
        _logger = logger,
        _filesInteractor = filesInteractor,
        _filePicker = filePicker,
        _navigationProvider = navigationProvider,
        _uuid = uuid,
        super(const ServiceRequestFormState.initial());

  void init(Object? serviceRequestEntity) {
    emit(
      ServiceRequestFormState.ready(
        serviceRequestEntity: serviceRequestEntity,
      ),
    );
  }

  void onSelectPriority(BackOfficeCasePriority selectedPriority) {
    state.mapOrNull(
      ready: (readyState) {
        emit(readyState.copyWith(priority: selectedPriority));
      },
    );
  }

  void updateNotes(String notes) {
    state.mapOrNull(
      ready: (readyState) {
        emit(readyState.copyWith(notes: notes));
      },
    );
  }

  Future<void> selectFile({
    required List<String> allowedExtensions,
    required bool allowMultiple,
  }) async {
    await state.mapOrNull(
      ready: (readyState) async {
        try {
          _logger
            ..debug('Selecting files. Allowed extensions: $allowedExtensions')
            ..debug('Can user select multiple files: $allowMultiple');

          final results = await _filePicker.pickFiles(
            type: FileType.custom,
            allowedExtensions: allowedExtensions,
            allowMultiple: allowMultiple,
          );

          if (results == null || results.files.isEmpty) {
            _logger.debug('No files selected');

            _showWarningToastMessage('No files selected');

            return;
          }

          for (final file in results.files) {
            if (!allowedExtensions.contains(file.extension)) {
              _showWarningToastMessage('Selected file type is not allowed. '
                  'Allowed types: ${allowedExtensions.join(', ')}');

              return;
            }
          }

          emit(readyState.toProcessing());

          for (final file in results.files) {
            if (file.bytes != null) {
              final uploadedFile = await _filesInteractor.uploadFile(
                fileData: file.bytes!,
                fileName: file.name,
                documentType: 'case_attachment',
              );

              state.mapOrNull(
                processing: (processingState) => emit(
                  processingState.copyWith(
                    attachments: [
                      ServiceRequestAttachment(
                        filePath: uploadedFile,
                        fileName: file.name,
                        id: _uuid.v4(),
                      ),
                      ...processingState.attachments,
                    ],
                  ),
                ),
              );
            }
          }

          state.mapOrNull(
            processing: (processingState) => emit(
              readyState.copyWith(attachments: processingState.attachments),
            ),
          );
        } on Object catch (e, stackTrace) {
          _commonErrorHandler.handleError(
            error: e,
            stackTrace: stackTrace,
          );

          emit(readyState);
        }
      },
    );
  }

  void removeFile(String attachmentId) {
    state.mapOrNull(
      ready: (readyState) {
        emit(readyState.toProcessing());

        emit(
          readyState.copyWith(
            attachments: readyState.attachments
                .where((attachment) => attachment.id != attachmentId)
                .toList(),
          ),
        );
      },
    );
  }

  Future<void> createServiceRequest(
    Map<String, Object> payload,
    String processName,
  ) async {
    await state.mapOrNull(
      ready: (readyState) async {
        try {
          if (readyState.priority == null) {
            _showWarningToastMessage('Priority must be selected');
          }

          emit(readyState.toProcessing());

          final createdCase = await _interactor.createServiceRequest(
            ServiceRequestCreationModel(
              actionType: processName,
              attachments: readyState.attachments,
              payload: payload,
              notes: readyState.notes,
            ),
          );

          emit(state.toCompleted());

          _toastMessageProvider.showRetailMobileThemedToastMessage(
            NotificationToastMessageConfiguration.success(
              'Successfully created service request! '
              'Case Number: ${createdCase.caseNumber}',
            ),
          );

          if (_navigationProvider.canPop()) {
            _navigationProvider.goBack();
          }
        } on Object catch (e, stackTrace) {
          emit(readyState);

          _commonErrorHandler.handleError(
            error: e,
            stackTrace: stackTrace,
          );
        }
      },
    );
  }

  void updateStateToProcessing() => emit(state.toProcessing());

  void updateStateToReady() => state.mapOrNull(
        processing: (processing) => emit(
          ServiceRequestFormState.ready(
            attachments: processing.attachments,
            notes: processing.notes,
            priority: processing.priority,
            selectedTransactions: processing.selectedTransactions,
            serviceRequestEntity: processing.serviceRequestEntity,
          ),
        ),
      );

  void updateSelectedTransactions(List<Transaction> selectedTransactions) =>
      state.mapOrNull(
        ready: (ready) => emit(
          ready.copyWith(
            selectedTransactions: selectedTransactions,
          ),
        ),
      );

  void _showWarningToastMessage(String message) {
    _toastMessageProvider.showRetailMobileThemedToastMessage(
      NotificationToastMessageConfiguration.warning(message),
    );
  }

  @override
  String toString() => 'ServiceRequestFormCubit';
}
