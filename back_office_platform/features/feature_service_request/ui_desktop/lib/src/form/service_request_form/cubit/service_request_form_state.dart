import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_cases_api/tasks_api.dart';
import 'package:wio_feature_service_request_api/domain/model/export.dart';
import 'package:wio_feature_transactions_api/transactions_api.dart';

part 'service_request_form_state.freezed.dart';

@freezed
class ServiceRequestFormState with _$ServiceRequestFormState {
  const factory ServiceRequestFormState.initial() =
      _ServiceRequestFormInitialState;

  const factory ServiceRequestFormState.ready({
    BackOfficeCasePriority? priority,
    @Default('') String notes,
    @Default([]) List<ServiceRequestAttachment> attachments,
    @Default([]) List<Transaction> selectedTransactions,
    Object? serviceRequestEntity,
  }) = _ServiceRequestFormReadyState;

  const factory ServiceRequestFormState.processing({
    required List<ServiceRequestAttachment> attachments,
    required Object? serviceRequestEntity,
    @Default([]) List<Transaction> selectedTransactions,
    BackOfficeCasePriority? priority,
    @Default('') String notes,
  }) = _ServiceRequestFormProcessingState;

  const factory ServiceRequestFormState.completed({
    required BackOfficeCasePriority priority,
    required List<ServiceRequestAttachment> attachments,
    required Object? serviceRequestEntity,
    @Default([]) List<Transaction> selectedTransactions,
    @Default('') String notes,
  }) = _ServiceRequestFormCompletedState;

  const ServiceRequestFormState._();

  bool get isProcessing => maybeMap(
        processing: (_) => true,
        orElse: () => false,
      );

  List<ServiceRequestAttachment> get srAttachments => maybeMap(
        ready: (readyState) => readyState.attachments,
        processing: (processingState) => processingState.attachments,
        completed: (completedState) => completedState.attachments,
        orElse: () => [],
      );

  ServiceRequestFormState toCompleted() => maybeMap(
        processing: (processingState) {
          final priority = processingState.priority;

          if (priority == null) {
            throw Exception(
              'Cannot convert to completed state without priority',
            );
          }

          return ServiceRequestFormState.completed(
            priority: priority,
            notes: processingState.notes,
            attachments: processingState.attachments,
            serviceRequestEntity: processingState.serviceRequestEntity,
            selectedTransactions: processingState.selectedTransactions,
          );
        },
        orElse: () => throw Exception('Cannot convert to processing state'),
      );

  ServiceRequestFormState toProcessing() => maybeMap(
        ready: (readyState) {
          final priority = readyState.priority;

          return ServiceRequestFormState.processing(
            priority: priority,
            notes: readyState.notes,
            attachments: readyState.attachments,
            serviceRequestEntity: readyState.serviceRequestEntity,
            selectedTransactions: readyState.selectedTransactions,
          );
        },
        orElse: () => throw Exception('Cannot convert to processing state'),
      );

  BackOfficeCasePriority? get selectedPriority => maybeMap(
        ready: (readyState) => readyState.priority,
        processing: (processingState) => processingState.priority,
        completed: (completedState) => completedState.priority,
        orElse: () => null,
      );

  bool get isCompleted => maybeMap(
        completed: (_) => true,
        orElse: () => false,
      );

  bool get hasAttachments => srAttachments.isNotEmpty;

  String get srNotes => maybeMap(
        ready: (readyState) => readyState.notes,
        processing: (processingState) => processingState.notes,
        completed: (completedState) => completedState.notes,
        orElse: () => '',
      );

  List<Transaction> get selectedTransactions => maybeMap(
        ready: (readyState) => readyState.selectedTransactions,
        processing: (processingState) => processingState.selectedTransactions,
        completed: (completedState) => completedState.selectedTransactions,
        orElse: () => [],
      );
}
