{"openapi": "3.0.3", "info": {"title": "Api Documentation", "description": "Api Documentation", "termsOfService": "urn:tos", "contact": {}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}, "version": "1.0"}, "servers": [{"url": "http://localhost:80", "description": "Inferred Url"}], "tags": [{"name": "invoice-dashboard-controller", "description": "Invoice Dashboard Controller"}], "paths": {"/api/v1/dashboard/invoices": {"get": {"tags": ["invoice-dashboard-controller"], "summary": "getInvoices", "operationId": "getInvoicesUsingGET", "parameters": [{"name": "businessId", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "currency", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "dateFrom", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date"}}, {"name": "dateTo", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "format": "date"}}, {"name": "limit", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "offset", "in": "query", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "searchText", "in": "query", "required": false, "style": "form", "schema": {"type": "string"}}, {"name": "sortingField", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["AMOUNT", "CREATED_DATE", "DUE_DATE", "INVOICE_NUMBER"]}}, {"name": "sortingOrder", "in": "query", "required": false, "style": "form", "schema": {"type": "string", "enum": ["ASC", "DESC"]}}, {"name": "statuses", "in": "query", "required": false, "style": "pipeDelimited", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/GetFullInvoicesPaginatedResponse"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v1/dashboard/invoices/pdf/{invoiceId}": {"get": {"tags": ["invoice-dashboard-controller"], "summary": "getByInvoiceIdAndBusinessId", "operationId": "getByInvoiceIdAndBusinessIdUsingGET", "parameters": [{"name": "invoiceId", "in": "path", "description": "invoiceId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "byte"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v1/dashboard/invoices/{invoiceId}": {"get": {"tags": ["invoice-dashboard-controller"], "summary": "getInvoice", "operationId": "getInvoiceUsingGET", "parameters": [{"name": "invoiceId", "in": "path", "description": "invoiceId", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/GetInvoiceResponse"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}}, "components": {"schemas": {"Address": {"title": "Address", "type": "object", "properties": {"Address": {"type": "string"}, "Attention": {"type": "string"}, "City": {"type": "string"}, "Country": {"type": "string"}, "Fax": {"type": "string"}, "Phone": {"type": "string"}, "State": {"type": "string"}, "Street": {"type": "string"}, "Street2": {"type": "string"}, "Zip": {"type": "string"}}}, "ContactPersonsDetail": {"title": "ContactPersonsDetail", "type": "object", "properties": {"ContactPersonId": {"type": "string"}, "Email": {"type": "string"}, "FirstName": {"type": "string"}, "IsPrimaryContact": {"type": "boolean"}, "LastName": {"type": "string"}, "Mobile": {"type": "string"}, "Phone": {"type": "string"}, "PhotoUrl": {"type": "string"}}}, "Download": {"title": "Download", "type": "object", "properties": {"File": {"type": "string"}, "Type": {"type": "string"}, "blob": {"type": "boolean"}}}, "GetFullInvoicesPaginatedResponse": {"title": "GetFullInvoicesPaginatedResponse", "type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Invoice"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "GetInvoiceResponse": {"title": "GetInvoiceResponse", "type": "object", "properties": {"BusinessId": {"type": "string"}, "Invoice": {"$ref": "#/components/schemas/Invoice"}, "OrganizationId": {"type": "string"}}}, "Invoice": {"title": "Invoice", "type": "object", "properties": {"AchPaymentInitiated": {"type": "boolean"}, "AchSupported": {"type": "boolean"}, "Adjustment": {"type": "integer", "format": "int32"}, "AdjustmentDescription": {"type": "string"}, "AllowPartialPayments": {"type": "boolean"}, "ApproverId": {"type": "string"}, "AttachmentName": {"type": "string"}, "Balance": {"type": "number", "format": "double"}, "BcyAdjustment": {"type": "integer", "format": "int32"}, "BcyDiscountTotal": {"type": "integer", "format": "int32"}, "BcyShippingCharge": {"type": "integer", "format": "int32"}, "BcySubTotal": {"type": "integer", "format": "int32"}, "BcyTaxTotal": {"type": "integer", "format": "int32"}, "BcyTotal": {"type": "integer", "format": "int32"}, "BillingAddress": {"$ref": "#/components/schemas/Address"}, "CanSendInMail": {"type": "boolean"}, "CanSendInvoiceSms": {"type": "boolean"}, "ClientViewedTime": {"type": "string"}, "ColorCode": {"type": "string"}, "ContactCategory": {"type": "string"}, "ContactPersons": {"type": "array", "items": {"type": "string"}}, "ContactPersonsDetails": {"type": "array", "items": {"$ref": "#/components/schemas/ContactPersonsDetail"}}, "CreatedById": {"type": "string"}, "CreatedDate": {"type": "string"}, "CreatedTime": {"type": "string"}, "CreditsApplied": {"type": "integer", "format": "int32"}, "CurrencyCode": {"type": "string"}, "CurrencyId": {"type": "string"}, "CurrencySymbol": {"type": "string"}, "CurrentSubStatus": {"type": "string"}, "CurrentSubStatusId": {"type": "string"}, "CustomerDefaultBillingAddress": {"$ref": "#/components/schemas/Address"}, "CustomerId": {"type": "string"}, "CustomerName": {"type": "string"}, "Date": {"type": "string"}, "Discount": {"type": "integer", "format": "int32"}, "DiscountAppliedOnAmount": {"type": "integer", "format": "int32"}, "DiscountPercent": {"type": "integer", "format": "int32"}, "DiscountTotal": {"type": "integer", "format": "int32"}, "DiscountType": {"type": "string"}, "Download": {"$ref": "#/components/schemas/Download"}, "DueDate": {"type": "string"}, "EcommOperatorId": {"type": "string"}, "EcommOperatorName": {"type": "string"}, "Email": {"type": "string"}, "EstimateId": {"type": "string"}, "ExchangeRate": {"type": "integer", "format": "int32"}, "FiledInVatReturnId": {"type": "string"}, "FiledInVatReturnName": {"type": "string"}, "FiledInVatReturnType": {"type": "string"}, "FreshPaymentLink": {"type": "string"}, "GccVatTreatment": {"type": "string"}, "IncludesPackageTrackingInfo": {"type": "boolean"}, "InprocessTransactionPresent": {"type": "boolean"}, "InvoiceId": {"type": "string"}, "InvoiceNumber": {"type": "string"}, "InvoiceUrl": {"type": "string"}, "IsAutobillEnabled": {"type": "boolean"}, "IsBackorder": {"type": "string"}, "IsClientReviewSettingsEnabled": {"type": "boolean"}, "IsDiscountBeforeTax": {"type": "boolean"}, "IsEmailed": {"type": "boolean"}, "IsInclusiveTax": {"type": "boolean"}, "IsReverseChargeApplied": {"type": "boolean"}, "IsTaxable": {"type": "boolean"}, "IsViewedByClient": {"type": "boolean"}, "IsViewedInMail": {"type": "boolean"}, "LastModifiedById": {"type": "string"}, "LastModifiedTime": {"type": "string"}, "LastPaymentDate": {"type": "string"}, "LastReminderSentDate": {"type": "string"}, "LineItems": {"type": "array", "items": {"$ref": "#/components/schemas/LineItem"}}, "MailFirstViewedTime": {"type": "string"}, "MailLastViewedTime": {"type": "string"}, "MerchantId": {"type": "string"}, "MerchantName": {"type": "string"}, "NextReminderDateFormatted": {"type": "string"}, "Notes": {"type": "string"}, "Orientation": {"type": "string"}, "PageHeight": {"type": "string"}, "PageWidth": {"type": "string"}, "PaymentDiscount": {"type": "integer", "format": "int32"}, "PaymentExpectedDate": {"type": "string"}, "PaymentMade": {"type": "integer", "format": "int32"}, "PaymentOptions": {"$ref": "#/components/schemas/PaymentOptions"}, "PaymentReminderEnabled": {"type": "boolean"}, "PaymentRequestLinks": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRequestLink"}}, "PaymentStatus": {"type": "string"}, "PaymentTerms": {"type": "integer", "format": "int32"}, "PaymentTermsLabel": {"type": "string"}, "PlaceOfSupply": {"type": "string"}, "PricePrecision": {"type": "integer", "format": "int32"}, "ReaderOfflinePaymentInitiated": {"type": "boolean"}, "Reason": {"type": "string"}, "RecurringInvoiceId": {"type": "string"}, "ReferenceNumber": {"type": "string"}, "RemindersSent": {"type": "integer", "format": "int32"}, "RoundoffValue": {"type": "integer", "format": "int32"}, "SalesChannel": {"type": "string"}, "SalesorderId": {"type": "string"}, "SalesorderNumber": {"type": "string"}, "SalespersonId": {"type": "string"}, "SalespersonName": {"type": "string"}, "ScheduleTime": {"type": "string"}, "ShippingAddress": {"$ref": "#/components/schemas/Address"}, "ShippingCharge": {"type": "integer", "format": "int32"}, "Status": {"type": "string"}, "StopReminderUntilPaymentExpectedDate": {"type": "boolean"}, "StripeInd": {"type": "boolean"}, "SubTotal": {"type": "integer", "format": "int32"}, "SubTotalInclusiveOfTax": {"type": "integer", "format": "int32"}, "SubjectContent": {"type": "string"}, "SubmittedBy": {"type": "string"}, "SubmittedDate": {"type": "string"}, "SubmitterId": {"type": "string"}, "TaxAmountWithheld": {"type": "integer", "format": "int32"}, "TaxRegNo": {"type": "string"}, "TaxRounding": {"type": "string"}, "TaxTotal": {"type": "integer", "format": "int32"}, "TaxTreatment": {"type": "string"}, "Taxes": {"type": "array", "items": {"$ref": "#/components/schemas/Tax"}}, "TemplateId": {"type": "string"}, "TemplateName": {"type": "string"}, "TemplateType": {"type": "string"}, "Terms": {"type": "string"}, "Total": {"type": "number", "format": "double"}, "TransactionRoundingType": {"type": "string"}, "Type": {"type": "string"}, "UnusedRetainerPayments": {"type": "integer", "format": "int32"}, "WriteOffAmount": {"type": "number", "format": "double"}, "ZcrmPotentialId": {"type": "string"}, "ZcrmPotentialName": {"type": "string"}}}, "LineItem": {"title": "LineItem", "type": "object", "properties": {"AccountId": {"type": "string"}, "AccountName": {"type": "string"}, "BcyRate": {"type": "integer", "format": "int32"}, "BillId": {"type": "string"}, "BillItemId": {"type": "string"}, "CostAmount": {"type": "integer", "format": "int32"}, "Description": {"type": "string"}, "Discount": {"type": "number", "format": "double"}, "DiscountAmount": {"type": "integer", "format": "int32"}, "Discounts": {"type": "array", "items": {"type": "object"}}, "Documents": {"type": "array", "items": {"type": "object"}}, "ExpenseId": {"type": "string"}, "ExpenseReceiptName": {"type": "string"}, "HeaderId": {"type": "string"}, "HeaderName": {"type": "string"}, "ImageDocumentId": {"type": "string"}, "ImageName": {"type": "string"}, "ItemCustomFields": {"type": "array", "items": {"type": "object"}}, "ItemId": {"type": "integer", "format": "int64"}, "ItemOrder": {"type": "integer", "format": "int32"}, "ItemTotal": {"type": "integer", "format": "int32"}, "ItemType": {"type": "string"}, "LineItemId": {"type": "integer", "format": "int64"}, "Name": {"type": "string"}, "PricebookId": {"type": "string"}, "ProductType": {"type": "string"}, "ProjectId": {"type": "string"}, "PurchaseRate": {"type": "string"}, "Quantity": {"type": "number", "format": "double"}, "Rate": {"type": "number", "format": "double"}, "SalesorderItemId": {"type": "string"}, "Tags": {"type": "array", "items": {"type": "object"}}, "TaxId": {"type": "string"}, "TaxName": {"type": "string"}, "TaxPercentage": {"type": "string"}, "TaxTreatmentCode": {"type": "string"}, "TaxType": {"type": "string"}, "TimeEntryIds": {"type": "array", "items": {"type": "object"}}, "Unit": {"type": "string"}}}, "Pagination": {"title": "Pagination", "type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}}}, "PaymentOptions": {"title": "PaymentOptions", "type": "object", "properties": {"PaymentGateways": {"type": "array", "items": {"type": "object"}}}}, "PaymentRequestLink": {"title": "PaymentRequestLink", "type": "object", "properties": {"PaymentRequestCreationTime": {"type": "string", "example": "yyyy-MM-dd HH:mm:ss"}, "PaymentRequestExpiryTime": {"type": "string", "example": "yyyy-MM-dd HH:mm:ss"}, "PaymentRequestLink": {"type": "string"}, "PaymentRequestLinkId": {"type": "string"}, "PaymentRequestLinkStatus": {"type": "string"}}}, "Tax": {"title": "Tax", "type": "object", "properties": {"TaxAmount": {"type": "integer", "format": "int32"}, "TaxName": {"type": "string"}}}}}}