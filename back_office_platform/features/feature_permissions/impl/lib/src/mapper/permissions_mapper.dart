import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_permissions_impl/src/data/dtos/spec.swagger.dart';

abstract class PermissionsMapper {
  Set<String> mapPermissions(List<UserGroupDto> groups);
}

class PermissionsMapperImpl implements PermissionsMapper {
  final ErrorReporter _errorReporter;

  const PermissionsMapperImpl({
    required ErrorReporter errorReporter,
  }) : _errorReporter = errorReporter;

  @override
  Set<String> mapPermissions(List<UserGroupDto> groups) {
    return _errorReporter.executeWithReport(() {
      return groups.map((group) => group.name).whereType<String>().toSet();
    });
  }
}
