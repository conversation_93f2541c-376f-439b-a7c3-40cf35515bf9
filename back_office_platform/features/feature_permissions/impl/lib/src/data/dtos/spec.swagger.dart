// ignore_for_file: type=lint

import 'package:json_annotation/json_annotation.dart';
import 'package:json_annotation/json_annotation.dart' as json;
import 'package:collection/collection.dart';
import 'dart:convert';

part 'spec.swagger.g.dart';

@JsonSerializable(explicitToJson: true)
class UserAuthorizationDto {
  const UserAuthorizationDto({
    this.userId,
    this.userName,
    this.userSurname,
    this.userEmail,
    this.userGroups,
  });

  factory UserAuthorizationDto.fromJson(Map<String, dynamic> json) =>
      _$UserAuthorizationDtoFromJson(json);

  static const toJsonFactory = _$UserAuthorizationDtoToJson;
  Map<String, dynamic> toJson() => _$UserAuthorizationDtoToJson(this);

  @JsonKey(name: 'userId', includeIfNull: false)
  final String? userId;
  @JsonKey(name: 'userName', includeIfNull: false)
  final String? userName;
  @Json<PERSON>ey(name: 'userSurname', includeIfNull: false)
  final String? userSurname;
  @JsonKey(name: 'userEmail', includeIfNull: false)
  final String? userEmail;
  @JsonKey(
      name: 'userGroups', includeIfNull: false, defaultValue: <UserGroupDto>[])
  final List<UserGroupDto>? userGroups;
  static const fromJsonFactory = _$UserAuthorizationDtoFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UserAuthorizationDtoExtension on UserAuthorizationDto {
  UserAuthorizationDto copyWith(
      {String? userId,
      String? userName,
      String? userSurname,
      String? userEmail,
      List<UserGroupDto>? userGroups}) {
    return UserAuthorizationDto(
        userId: userId ?? this.userId,
        userName: userName ?? this.userName,
        userSurname: userSurname ?? this.userSurname,
        userEmail: userEmail ?? this.userEmail,
        userGroups: userGroups ?? this.userGroups);
  }

  UserAuthorizationDto copyWithWrapped(
      {Wrapped<String?>? userId,
      Wrapped<String?>? userName,
      Wrapped<String?>? userSurname,
      Wrapped<String?>? userEmail,
      Wrapped<List<UserGroupDto>?>? userGroups}) {
    return UserAuthorizationDto(
        userId: (userId != null ? userId.value : this.userId),
        userName: (userName != null ? userName.value : this.userName),
        userSurname:
            (userSurname != null ? userSurname.value : this.userSurname),
        userEmail: (userEmail != null ? userEmail.value : this.userEmail),
        userGroups: (userGroups != null ? userGroups.value : this.userGroups));
  }
}

@JsonSerializable(explicitToJson: true)
class UserGroupDto {
  const UserGroupDto({
    this.id,
    this.name,
  });

  factory UserGroupDto.fromJson(Map<String, dynamic> json) =>
      _$UserGroupDtoFromJson(json);

  static const toJsonFactory = _$UserGroupDtoToJson;
  Map<String, dynamic> toJson() => _$UserGroupDtoToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String? id;
  @JsonKey(name: 'name', includeIfNull: false)
  final String? name;
  static const fromJsonFactory = _$UserGroupDtoFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UserGroupDtoExtension on UserGroupDto {
  UserGroupDto copyWith({String? id, String? name}) {
    return UserGroupDto(id: id ?? this.id, name: name ?? this.name);
  }

  UserGroupDto copyWithWrapped({Wrapped<String?>? id, Wrapped<String?>? name}) {
    return UserGroupDto(
        id: (id != null ? id.value : this.id),
        name: (name != null ? name.value : this.name));
  }
}

// ignore: unused_element
String? _dateToJson(DateTime? date) {
  if (date == null) {
    return null;
  }

  final year = date.year.toString();
  final month = date.month < 10 ? '0${date.month}' : date.month.toString();
  final day = date.day < 10 ? '0${date.day}' : date.day.toString();

  return '$year-$month-$day';
}

class Wrapped<T> {
  final T value;
  const Wrapped.value(this.value);
}
