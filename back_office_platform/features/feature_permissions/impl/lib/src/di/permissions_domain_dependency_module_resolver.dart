import 'package:di/di.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_app_common_api/data/app_context_repository.dart';
import 'package:wio_feature_customer_idv_api/customer_idv_api.dart';
import 'package:wio_feature_permissions_api/permissions_api.dart';
import 'package:wio_feature_permissions_impl/src/data/permissions_repository.dart';
import 'package:wio_feature_permissions_impl/src/domain/permissions_interactor.dart';
import 'package:wio_feature_permissions_impl/src/mapper/permissions_mapper.dart';
import 'package:wio_feature_permissions_impl/src/permissions_resolver_impl.dart';
import 'package:wio_feature_permissions_impl/src/permissions_service.dart';

class PermissionsDomainDependencyModuleResolver {
  static void register() {
    DependencyProvider.registerLazySingleton<PermissionsRepository>(() {
      return PermissionsRepositoryImpl(
        service: DependencyProvider.get<PermissionsService>(),
        mapper: DependencyProvider.get<PermissionsMapper>(),
        customerIdvRepository: DependencyProvider.get<CustomerIdvRepository>(),
        appContextRepository: DependencyProvider.get<AppContextRepository>(),
      );
    });

    DependencyProvider.registerLazySingleton<PermissionsResolver>(
      () => PermissionsResolverImpl(
        permissionsRepository: DependencyProvider.get<PermissionsRepository>(),
      ),
    );

    DependencyProvider.registerLazySingleton<PermissionsMapper>(() {
      return PermissionsMapperImpl(
        errorReporter: DependencyProvider.get<ErrorReporter>(),
      );
    });

    DependencyProvider.registerLazySingleton<PermissionsInteractor>(() {
      return PermissionsInteractorImpl(
        repository: DependencyProvider.get<PermissionsRepository>(),
      );
    });

    DependencyProvider.registerLazySingleton<PermissionsService>(
      () => PermissionsServiceImpl(
        restApiClient: DependencyProvider.get<IRestApiClient>(),
      ),
    );
  }
}
