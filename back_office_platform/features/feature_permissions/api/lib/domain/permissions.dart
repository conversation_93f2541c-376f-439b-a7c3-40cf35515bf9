/// Permissions for the back office platform.
class Permissions {
  /// The user can view transactions.
  static const String viewTransactions = 'TRANSACTIONS_READ';

  /// The user can view amount hold creation entrypoint.
  static const String viewAmountHold = 'BACKOFFICE_PERMISSION_AMOUNT_HOLD_VIEW';

  /// The user can view amount unhold entrypoint.
  static const String viewAmountUnHold =
      'BACKOFFICE_PERMISSION_AMOUNT_UN_HOLD_VIEW';

  /// The user can view debit restriction SR
  static const String viewDebitRestriction =
      'BACKOFFICE_DEBIT_RESTRICTION_BY_CUSTOMER_CARE_MAKER';

  /// The user can view retail details.
  static const String viewRetailDetails =
      'BACKOFFICE_PERMISSION_AGENT_RETAIL_VIEW';

  /// The user can view invest customer details.
  static const String viewInvestDetails =
      'BACKOFFICE_PERMISSION_AGENT_BROKER_VIEW';

  /// The user can view customer details standalone.
  static const String viewCustomerDetailsStandalone =
      'BACKOFFICE_PERMISSION_STAND_ALONE_PROFILE_VIEW';

  /// The user can dial customer.
  static const String dialCustomer =
      'BACKOFFICE_PERMISSIONS_DIAL_CUSTOMER_VIEW';

  /// The user can view employee salary transfer file details
  static const String viewEmployeeSalary = 'BACKOFFICE_SALARY_VIEW';
}
