import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_disputes_api/disputes_api.dart';

part 'dispute_transactions_state.freezed.dart';

@Freezed(
  when: FreezedWhenOptions.none,
  toJson: false,
  fromJson: false,
)
class DisputeTransactionsState with _$DisputeTransactionsState {
  const factory DisputeTransactionsState.loading() =
      DisputeTransactionsLoadingState;

  const factory DisputeTransactionsState.empty() =
      DisputeTransactionsEmptyState;

  const factory DisputeTransactionsState.loaded({
    required DisputeInfo disputeInformations,
  }) = DisputeTransactionsLoadedState;
}
