import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_cases_api/tasks_api.dart';

/// Navigation configuration for the ticket details screen
class CaseDetailNavigationConfig extends ScreenNavigationConfig {
  /// ScreenId
  static const screenId = 'case_history_list';
  final BackOfficeCase? backOfficeCase;
  final String caseId;

  /// Constructor
  const CaseDetailNavigationConfig({
    required this.caseId,
    this.backOfficeCase,
  }) : super(
          id: screenId,
          feature: TasksFeatureNavigationConfig.name,
        );

  @override
  String toString() => 'CaseDetailNavigationConfig';
}
