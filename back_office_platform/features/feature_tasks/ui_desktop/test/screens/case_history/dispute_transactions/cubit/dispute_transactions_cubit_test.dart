import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:wio_feature_disputes_api/feature_toggles/disputes_feature_toggles.dart';
import 'package:wio_feature_tasks_ui_desktop/src/navigation/configs/transactions_info_dialog_navigation_config.dart';
import 'package:wio_feature_tasks_ui_desktop/src/screens/case_history/dispute_transactions/cubit/dispute_transactions_cubit.dart';
import 'package:wio_feature_tasks_ui_desktop/src/screens/case_history/dispute_transactions/cubit/dispute_transactions_state.dart';

import '../../../../mock.dart';

void main() {
  late MockDisputesInteractor disputesInteractor;
  late MockCommonErrorHandler commonErrorHandler;
  late MockFeatureToggleProvider featureToggleProvider;
  late MockNavigationProvider navigationProvider;
  late DisputeTransactionsCubit cubit;

  setUp(() {
    disputesInteractor = MockDisputesInteractor();
    commonErrorHandler = MockCommonErrorHandler();
    featureToggleProvider = MockFeatureToggleProvider();
    navigationProvider = MockNavigationProvider();

    cubit = DisputeTransactionsCubit(
      interactor: disputesInteractor,
      commonErrorHandler: commonErrorHandler,
      featureToggleProvider: featureToggleProvider,
      navigationProvider: navigationProvider,
    );

    registerFallbackValue(
      TransactionsInfoDialogNavigationConfig(
        transaction: FakeTransaction(),
      ),
    );
  });

  group('initialize', () {
    const disputeId = 'test-dispute-id';
    final disputeInfo = FakeDisputeInfo();

    test('should do nothing when feature flag is disabled', () async {
      when(
        () => featureToggleProvider
            .get(DisputesFeatureToggles.isCardDisputesFeatureEnabled),
      ).thenReturn(false);

      await cubit.initialize(disputeId: disputeId);

      verifyNever(
        () => disputesInteractor.getDisputeById(disputeId: disputeId),
      );
      expect(cubit.state, const DisputeTransactionsState.empty());
    });

    test('should do nothing when disputeId is null', () async {
      when(
        () => featureToggleProvider
            .get(DisputesFeatureToggles.isCardDisputesFeatureEnabled),
      ).thenReturn(true);

      await cubit.initialize();

      verifyNever(
        () => disputesInteractor.getDisputeById(
          disputeId: any(named: 'disputeId'),
        ),
      );
      expect(cubit.state, const DisputeTransactionsState.empty());
    });

    blocTest<DisputeTransactionsCubit, DisputeTransactionsState>(
      'should load dispute information when feature flag is enabled',
      setUp: () {
        when(
          () => featureToggleProvider
              .get(DisputesFeatureToggles.isCardDisputesFeatureEnabled),
        ).thenReturn(true);
        when(
          () => disputesInteractor.getDisputeById(disputeId: disputeId),
        ).thenAnswer((_) async => disputeInfo);
      },
      build: () => cubit,
      act: (cubit) => cubit.initialize(disputeId: disputeId),
      expect: () => [
        const DisputeTransactionsState.loading(),
        DisputeTransactionsState.loaded(disputeInformations: disputeInfo),
      ],
      verify: (_) =>
          verify(() => disputesInteractor.getDisputeById(disputeId: disputeId))
              .called(1),
    );

    blocTest<DisputeTransactionsCubit, DisputeTransactionsState>(
      'should handle errors during initialization',
      setUp: () {
        when(
          () => featureToggleProvider
              .get(DisputesFeatureToggles.isCardDisputesFeatureEnabled),
        ).thenReturn(true);
        when(
          () => disputesInteractor.getDisputeById(disputeId: disputeId),
        ).thenThrow(Exception('Test error'));
      },
      build: () => cubit,
      act: (cubit) => cubit.initialize(disputeId: disputeId),
      expect: () => [
        const DisputeTransactionsState.loading(),
        const DisputeTransactionsState.empty(),
      ],
      verify: (_) => verify(
        () => commonErrorHandler.handleError(
          error: any(named: 'error'),
          stackTrace: any(named: 'stackTrace'),
        ),
      ).called(1),
    );
  });

  group('openTransactionsDialog', () {
    test('should open dialog with correct configuration', () async {
      final transaction = FakeTransaction();

      when(() => navigationProvider.showDialog<void>(any()))
          .thenAnswer((_) async {});

      await cubit.openTransactionsDialog(transaction: transaction);

      verify(
        () => navigationProvider.showDialog<void>(
          any(
            that: predicate(
              (config) =>
                  config is TransactionsInfoDialogNavigationConfig &&
                  config.transaction == transaction,
            ),
          ),
        ),
      ).called(1);
    });
  });
}
