// ignore_for_file: public_member_api_docs

import 'package:wio_feature_onboarding_api/domain/model/timeline/document_file.dart';
import 'package:wio_feature_onboarding_api/domain/model/timeline/enums.dart';

/// class to hold all info related to a business document
class BusinessDocument {
  final String documentId;
  final String businessId;
  final String? version;
  final String documentType;
  final List<DocumentFile> documentFiles;
  final DateTime? issueDate;
  final DateTime? expiryDate;
  final DocumentStatus documentStatus;
  final DateTime createdAt;
  final DateTime? lastModifiedAt;

  const BusinessDocument({
    required this.documentId,
    required this.businessId,
    required this.version,
    required this.documentType,
    required this.documentFiles,
    required this.issueDate,
    required this.expiryDate,
    required this.documentStatus,
    required this.createdAt,
    required this.lastModifiedAt,
  });
}
