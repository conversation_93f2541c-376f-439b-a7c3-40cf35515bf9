// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'partner_info_dialog_navigation_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PartnerInfoDialogNavigationConfig {}

/// @nodoc
abstract class $PartnerInfoDialogNavigationConfigCopyWith<$Res> {
  factory $PartnerInfoDialogNavigationConfigCopyWith(
          PartnerInfoDialogNavigationConfig value,
          $Res Function(PartnerInfoDialogNavigationConfig) then) =
      _$PartnerInfoDialogNavigationConfigCopyWithImpl<$Res,
          PartnerInfoDialogNavigationConfig>;
}

/// @nodoc
class _$PartnerInfoDialogNavigationConfigCopyWithImpl<$Res,
        $Val extends PartnerInfoDialogNavigationConfig>
    implements $PartnerInfoDialogNavigationConfigCopyWith<$Res> {
  _$PartnerInfoDialogNavigationConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PartnerInfoDialogNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$PartnerInfoDialogNavigationConfigImplCopyWith<$Res> {
  factory _$$PartnerInfoDialogNavigationConfigImplCopyWith(
          _$PartnerInfoDialogNavigationConfigImpl value,
          $Res Function(_$PartnerInfoDialogNavigationConfigImpl) then) =
      __$$PartnerInfoDialogNavigationConfigImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PartnerInfoDialogNavigationConfigImplCopyWithImpl<$Res>
    extends _$PartnerInfoDialogNavigationConfigCopyWithImpl<$Res,
        _$PartnerInfoDialogNavigationConfigImpl>
    implements _$$PartnerInfoDialogNavigationConfigImplCopyWith<$Res> {
  __$$PartnerInfoDialogNavigationConfigImplCopyWithImpl(
      _$PartnerInfoDialogNavigationConfigImpl _value,
      $Res Function(_$PartnerInfoDialogNavigationConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of PartnerInfoDialogNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PartnerInfoDialogNavigationConfigImpl
    extends _PartnerInfoDialogNavigationConfig {
  const _$PartnerInfoDialogNavigationConfigImpl() : super._();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PartnerInfoDialogNavigationConfigImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;
}

abstract class _PartnerInfoDialogNavigationConfig
    extends PartnerInfoDialogNavigationConfig {
  const factory _PartnerInfoDialogNavigationConfig() =
      _$PartnerInfoDialogNavigationConfigImpl;
  const _PartnerInfoDialogNavigationConfig._() : super._();
}
