import 'package:json_annotation/json_annotation.dart';
import 'package:collection/collection.dart';

enum BackofficeCaseCreateRequestDtoProduct {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('SME')
  sme('SME'),
  @JsonValue('RETAIL')
  retail('RETAIL');

  final String? value;

  const BackofficeCaseCreateRequestDtoProduct(this.value);
}

enum BackofficeCaseDtoProduct {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('SME')
  sme('SME'),
  @JsonValue('RETAIL')
  retail('RETAIL');

  final String? value;

  const BackofficeCaseDtoProduct(this.value);
}
