import 'package:mocktail/mocktail.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_accounts_api/accounts_feature_api.dart';
import 'package:wio_feature_accounts_impl/src/data/accounts_data_mapper.dart';
import 'package:wio_feature_accounts_impl/src/data/service/accounts_service.dart';

class MockRestApiClient extends Mock implements IRestApiClient {}

class MockAccountsService extends Mock implements AccountsService {}

class MockAccountsDataMapper extends Mock implements AccountsDataMapper {}

class MockAccountsRepository extends Mock implements AccountsRepository {}
