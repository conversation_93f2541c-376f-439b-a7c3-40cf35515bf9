import 'package:di/di.dart';
import 'package:logging_api/logging.dart';
import 'package:uuid/uuid.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_accounts_api/accounts_feature_api.dart';
import 'package:wio_feature_accounts_ui_desktop/src/form_configs/accounts_form_configs_handlers.dart';
import 'package:wio_feature_accounts_ui_desktop/src/navigation/accounts_router.dart';
import 'package:wio_feature_accounts_ui_desktop/src/screens/account_details/account_details_cubit.dart';
import 'package:wio_feature_accounts_ui_desktop/src/screens/account_header/accounts_header_cubit.dart';
import 'package:wio_feature_accounts_ui_desktop/src/screens/account_list/accounts_list_cubit.dart';
import 'package:wio_feature_accounts_ui_desktop/src/utils/html_anchor_element.dart';
import 'package:wio_feature_auth_api/auth_api.dart';
import 'package:wio_feature_common_error_handler_api/handler/common_error_handler.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_customer_idv_api/providers/customer_provider.dart';
import 'package:wio_feature_service_request_api/service_request_api.dart';

class AccountsFeatureDependencyModuleResolver {
  static void register() {
    _registerNavigation();
    _registerHtmlAnchorElement();
    _registerAccountsListDashboard();
    _registerAccountsHeaderDashboard();
    _registerAccountDetails();
    _registerAccountsFormConfig();
  }

  static void _registerAccountsFormConfig() {
    final handlers = [
      AccountFormConfigsHandler(),
    ];

    for (final handler in handlers) {
      DependencyProvider.get<ServiceRequestFormRegister>().register(handler);
    }
  }

  static void _registerHtmlAnchorElement() {
    DependencyProvider.registerFactory(HtmlAnchorElement.new);
  }

  static void _registerNavigation() {
    DependencyProvider.registerLazySingleton<AccountsRouter>(() {
      return const AccountsRouter();
    });

    DependencyProvider.registerLazySingleton<NavigationRouter>(
      () => DependencyProvider.get<AccountsRouter>(),
      instanceName: AccountsFeatureNavigationConfig.name,
    );
  }

  static void _registerAccountsListDashboard() {
    DependencyProvider.registerFactoryWithParams<AccountsListCubit, int, void>(
      (
        accountsLimitToShowInSinglePageOfTable,
        _,
      ) =>
          AccountsListCubit(
        accountsLimitToShowInSinglePageOfTable:
            accountsLimitToShowInSinglePageOfTable,
        accountsInteractor: DependencyProvider.get<AccountsInteractor>(),
        logger: DependencyProvider.get<Logger>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        customerProvider: DependencyProvider.get<CustomerProvider>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );
  }

  static void _registerAccountsHeaderDashboard() {
    DependencyProvider.registerFactory<AccountsHeaderCubit>(
      () => AccountsHeaderCubit(
        logger: DependencyProvider.get<Logger>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        customerProvider: DependencyProvider.get<CustomerProvider>(),
      ),
    );
  }

  static void _registerAccountDetails() {
    DependencyProvider.registerFactory<AccountDetailsCubit>(
      () => AccountDetailsCubit(
        accountsInteractor: DependencyProvider.get<AccountsInteractor>(),
        customerProvider: DependencyProvider.get<CustomerProvider>(),
        serviceRequestInteractor:
            DependencyProvider.get<ServiceRequestInteractor>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        authInteractor: DependencyProvider.get<AuthInteractor>(),
        logger: DependencyProvider.get<Logger>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        uuid: const Uuid(),
        serviceRequestFormRegister:
            DependencyProvider.get<ServiceRequestFormRegister>(),
      ),
    );
  }
}
