import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_accounts_api/accounts_feature_api.dart';
import 'package:wio_feature_accounts_api/domain/models/account_info.dart';
import 'package:wio_feature_accounts_ui_desktop/src/screens/ui_mapper/accounts_filter_request_payload.dart';

part 'accounts_list_state.freezed.dart';

@freezed
class AccountsListState with _$AccountsListState {
  const factory AccountsListState.loading({
    required int currentPageNumber,
    required AccountsFilterRequestPayload filterPayload,
  }) = AccountsListLoadingState;

  const factory AccountsListState.loaded({
    required int currentPageNumber,
    required List<AccountInfo> accounts,
    required AccountsFilterRequestPayload filterPayload,
    required int totalPageCount,
  }) = AccountsListLoadedState;

  const factory AccountsListState.error({
    required int currentPageNumber,
    required AccountsFilterRequestPayload filterPayload,
  }) = AccountsListErrorState;
}

extension AccountsListStateExt on AccountsListState {
  bool get showFilters => false; // Initially filter is not supported.
}
