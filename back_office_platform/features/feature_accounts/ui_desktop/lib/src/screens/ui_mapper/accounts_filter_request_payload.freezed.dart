// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'accounts_filter_request_payload.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AccountsFilterRequestPayload {
  AccountsListInput? get accountsListInput =>
      throw _privateConstructorUsedError;
  List<AccountStatus>? get accountStatusFilter =>
      throw _privateConstructorUsedError;
  bool? get shouldShowEmptyTable => throw _privateConstructorUsedError;

  /// Create a copy of AccountsFilterRequestPayload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountsFilterRequestPayloadCopyWith<AccountsFilterRequestPayload>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountsFilterRequestPayloadCopyWith<$Res> {
  factory $AccountsFilterRequestPayloadCopyWith(
          AccountsFilterRequestPayload value,
          $Res Function(AccountsFilterRequestPayload) then) =
      _$AccountsFilterRequestPayloadCopyWithImpl<$Res,
          AccountsFilterRequestPayload>;
  @useResult
  $Res call(
      {AccountsListInput? accountsListInput,
      List<AccountStatus>? accountStatusFilter,
      bool? shouldShowEmptyTable});
}

/// @nodoc
class _$AccountsFilterRequestPayloadCopyWithImpl<$Res,
        $Val extends AccountsFilterRequestPayload>
    implements $AccountsFilterRequestPayloadCopyWith<$Res> {
  _$AccountsFilterRequestPayloadCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountsFilterRequestPayload
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountsListInput = freezed,
    Object? accountStatusFilter = freezed,
    Object? shouldShowEmptyTable = freezed,
  }) {
    return _then(_value.copyWith(
      accountsListInput: freezed == accountsListInput
          ? _value.accountsListInput
          : accountsListInput // ignore: cast_nullable_to_non_nullable
              as AccountsListInput?,
      accountStatusFilter: freezed == accountStatusFilter
          ? _value.accountStatusFilter
          : accountStatusFilter // ignore: cast_nullable_to_non_nullable
              as List<AccountStatus>?,
      shouldShowEmptyTable: freezed == shouldShowEmptyTable
          ? _value.shouldShowEmptyTable
          : shouldShowEmptyTable // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AccountsFilterRequestPayloadImplCopyWith<$Res>
    implements $AccountsFilterRequestPayloadCopyWith<$Res> {
  factory _$$AccountsFilterRequestPayloadImplCopyWith(
          _$AccountsFilterRequestPayloadImpl value,
          $Res Function(_$AccountsFilterRequestPayloadImpl) then) =
      __$$AccountsFilterRequestPayloadImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AccountsListInput? accountsListInput,
      List<AccountStatus>? accountStatusFilter,
      bool? shouldShowEmptyTable});
}

/// @nodoc
class __$$AccountsFilterRequestPayloadImplCopyWithImpl<$Res>
    extends _$AccountsFilterRequestPayloadCopyWithImpl<$Res,
        _$AccountsFilterRequestPayloadImpl>
    implements _$$AccountsFilterRequestPayloadImplCopyWith<$Res> {
  __$$AccountsFilterRequestPayloadImplCopyWithImpl(
      _$AccountsFilterRequestPayloadImpl _value,
      $Res Function(_$AccountsFilterRequestPayloadImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountsFilterRequestPayload
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountsListInput = freezed,
    Object? accountStatusFilter = freezed,
    Object? shouldShowEmptyTable = freezed,
  }) {
    return _then(_$AccountsFilterRequestPayloadImpl(
      accountsListInput: freezed == accountsListInput
          ? _value.accountsListInput
          : accountsListInput // ignore: cast_nullable_to_non_nullable
              as AccountsListInput?,
      accountStatusFilter: freezed == accountStatusFilter
          ? _value._accountStatusFilter
          : accountStatusFilter // ignore: cast_nullable_to_non_nullable
              as List<AccountStatus>?,
      shouldShowEmptyTable: freezed == shouldShowEmptyTable
          ? _value.shouldShowEmptyTable
          : shouldShowEmptyTable // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$AccountsFilterRequestPayloadImpl
    implements _AccountsFilterRequestPayload {
  const _$AccountsFilterRequestPayloadImpl(
      {this.accountsListInput,
      final List<AccountStatus>? accountStatusFilter,
      this.shouldShowEmptyTable = true})
      : _accountStatusFilter = accountStatusFilter;

  @override
  final AccountsListInput? accountsListInput;
  final List<AccountStatus>? _accountStatusFilter;
  @override
  List<AccountStatus>? get accountStatusFilter {
    final value = _accountStatusFilter;
    if (value == null) return null;
    if (_accountStatusFilter is EqualUnmodifiableListView)
      return _accountStatusFilter;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey()
  final bool? shouldShowEmptyTable;

  @override
  String toString() {
    return 'AccountsFilterRequestPayload(accountsListInput: $accountsListInput, accountStatusFilter: $accountStatusFilter, shouldShowEmptyTable: $shouldShowEmptyTable)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountsFilterRequestPayloadImpl &&
            (identical(other.accountsListInput, accountsListInput) ||
                other.accountsListInput == accountsListInput) &&
            const DeepCollectionEquality()
                .equals(other._accountStatusFilter, _accountStatusFilter) &&
            (identical(other.shouldShowEmptyTable, shouldShowEmptyTable) ||
                other.shouldShowEmptyTable == shouldShowEmptyTable));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      accountsListInput,
      const DeepCollectionEquality().hash(_accountStatusFilter),
      shouldShowEmptyTable);

  /// Create a copy of AccountsFilterRequestPayload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountsFilterRequestPayloadImplCopyWith<
          _$AccountsFilterRequestPayloadImpl>
      get copyWith => __$$AccountsFilterRequestPayloadImplCopyWithImpl<
          _$AccountsFilterRequestPayloadImpl>(this, _$identity);
}

abstract class _AccountsFilterRequestPayload
    implements AccountsFilterRequestPayload {
  const factory _AccountsFilterRequestPayload(
      {final AccountsListInput? accountsListInput,
      final List<AccountStatus>? accountStatusFilter,
      final bool? shouldShowEmptyTable}) = _$AccountsFilterRequestPayloadImpl;

  @override
  AccountsListInput? get accountsListInput;
  @override
  List<AccountStatus>? get accountStatusFilter;
  @override
  bool? get shouldShowEmptyTable;

  /// Create a copy of AccountsFilterRequestPayload
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountsFilterRequestPayloadImplCopyWith<
          _$AccountsFilterRequestPayloadImpl>
      get copyWith => throw _privateConstructorUsedError;
}
