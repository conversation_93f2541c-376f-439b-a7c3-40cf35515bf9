// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_details_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AccountsDetailsState {
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountsListInitialState value) initial,
    required TResult Function(AccountsDetailsLoadingState value) loading,
    required TResult Function(AccountsDetailsLoadedState value) loaded,
    required TResult Function(AccountsDetailsErrorState value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountsListInitialState value)? initial,
    TResult? Function(AccountsDetailsLoadingState value)? loading,
    TResult? Function(AccountsDetailsLoadedState value)? loaded,
    TResult? Function(AccountsDetailsErrorState value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountsListInitialState value)? initial,
    TResult Function(AccountsDetailsLoadingState value)? loading,
    TResult Function(AccountsDetailsLoadedState value)? loaded,
    TResult Function(AccountsDetailsErrorState value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountsDetailsStateCopyWith<$Res> {
  factory $AccountsDetailsStateCopyWith(AccountsDetailsState value,
          $Res Function(AccountsDetailsState) then) =
      _$AccountsDetailsStateCopyWithImpl<$Res, AccountsDetailsState>;
}

/// @nodoc
class _$AccountsDetailsStateCopyWithImpl<$Res,
        $Val extends AccountsDetailsState>
    implements $AccountsDetailsStateCopyWith<$Res> {
  _$AccountsDetailsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountsDetailsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$AccountsListInitialStateImplCopyWith<$Res> {
  factory _$$AccountsListInitialStateImplCopyWith(
          _$AccountsListInitialStateImpl value,
          $Res Function(_$AccountsListInitialStateImpl) then) =
      __$$AccountsListInitialStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AccountsListInitialStateImplCopyWithImpl<$Res>
    extends _$AccountsDetailsStateCopyWithImpl<$Res,
        _$AccountsListInitialStateImpl>
    implements _$$AccountsListInitialStateImplCopyWith<$Res> {
  __$$AccountsListInitialStateImplCopyWithImpl(
      _$AccountsListInitialStateImpl _value,
      $Res Function(_$AccountsListInitialStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountsDetailsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$AccountsListInitialStateImpl implements AccountsListInitialState {
  const _$AccountsListInitialStateImpl();

  @override
  String toString() {
    return 'AccountsDetailsState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountsListInitialStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountsListInitialState value) initial,
    required TResult Function(AccountsDetailsLoadingState value) loading,
    required TResult Function(AccountsDetailsLoadedState value) loaded,
    required TResult Function(AccountsDetailsErrorState value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountsListInitialState value)? initial,
    TResult? Function(AccountsDetailsLoadingState value)? loading,
    TResult? Function(AccountsDetailsLoadedState value)? loaded,
    TResult? Function(AccountsDetailsErrorState value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountsListInitialState value)? initial,
    TResult Function(AccountsDetailsLoadingState value)? loading,
    TResult Function(AccountsDetailsLoadedState value)? loaded,
    TResult Function(AccountsDetailsErrorState value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class AccountsListInitialState implements AccountsDetailsState {
  const factory AccountsListInitialState() = _$AccountsListInitialStateImpl;
}

/// @nodoc
abstract class _$$AccountsDetailsLoadingStateImplCopyWith<$Res> {
  factory _$$AccountsDetailsLoadingStateImplCopyWith(
          _$AccountsDetailsLoadingStateImpl value,
          $Res Function(_$AccountsDetailsLoadingStateImpl) then) =
      __$$AccountsDetailsLoadingStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AccountsDetailsLoadingStateImplCopyWithImpl<$Res>
    extends _$AccountsDetailsStateCopyWithImpl<$Res,
        _$AccountsDetailsLoadingStateImpl>
    implements _$$AccountsDetailsLoadingStateImplCopyWith<$Res> {
  __$$AccountsDetailsLoadingStateImplCopyWithImpl(
      _$AccountsDetailsLoadingStateImpl _value,
      $Res Function(_$AccountsDetailsLoadingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountsDetailsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$AccountsDetailsLoadingStateImpl implements AccountsDetailsLoadingState {
  const _$AccountsDetailsLoadingStateImpl();

  @override
  String toString() {
    return 'AccountsDetailsState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountsDetailsLoadingStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountsListInitialState value) initial,
    required TResult Function(AccountsDetailsLoadingState value) loading,
    required TResult Function(AccountsDetailsLoadedState value) loaded,
    required TResult Function(AccountsDetailsErrorState value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountsListInitialState value)? initial,
    TResult? Function(AccountsDetailsLoadingState value)? loading,
    TResult? Function(AccountsDetailsLoadedState value)? loaded,
    TResult? Function(AccountsDetailsErrorState value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountsListInitialState value)? initial,
    TResult Function(AccountsDetailsLoadingState value)? loading,
    TResult Function(AccountsDetailsLoadedState value)? loaded,
    TResult Function(AccountsDetailsErrorState value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class AccountsDetailsLoadingState implements AccountsDetailsState {
  const factory AccountsDetailsLoadingState() =
      _$AccountsDetailsLoadingStateImpl;
}

/// @nodoc
abstract class _$$AccountsDetailsLoadedStateImplCopyWith<$Res> {
  factory _$$AccountsDetailsLoadedStateImplCopyWith(
          _$AccountsDetailsLoadedStateImpl value,
          $Res Function(_$AccountsDetailsLoadedStateImpl) then) =
      __$$AccountsDetailsLoadedStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {AccountInfo accountInfo,
      User agentInfo,
      List<FormConfig> srConfigs,
      List<ReasonInfo> reasonInfoList,
      List<AmountHoldInfo>? amountHoldInfoList});

  $AccountInfoCopyWith<$Res> get accountInfo;
}

/// @nodoc
class __$$AccountsDetailsLoadedStateImplCopyWithImpl<$Res>
    extends _$AccountsDetailsStateCopyWithImpl<$Res,
        _$AccountsDetailsLoadedStateImpl>
    implements _$$AccountsDetailsLoadedStateImplCopyWith<$Res> {
  __$$AccountsDetailsLoadedStateImplCopyWithImpl(
      _$AccountsDetailsLoadedStateImpl _value,
      $Res Function(_$AccountsDetailsLoadedStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountsDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountInfo = null,
    Object? agentInfo = null,
    Object? srConfigs = null,
    Object? reasonInfoList = null,
    Object? amountHoldInfoList = freezed,
  }) {
    return _then(_$AccountsDetailsLoadedStateImpl(
      accountInfo: null == accountInfo
          ? _value.accountInfo
          : accountInfo // ignore: cast_nullable_to_non_nullable
              as AccountInfo,
      agentInfo: null == agentInfo
          ? _value.agentInfo
          : agentInfo // ignore: cast_nullable_to_non_nullable
              as User,
      srConfigs: null == srConfigs
          ? _value._srConfigs
          : srConfigs // ignore: cast_nullable_to_non_nullable
              as List<FormConfig>,
      reasonInfoList: null == reasonInfoList
          ? _value._reasonInfoList
          : reasonInfoList // ignore: cast_nullable_to_non_nullable
              as List<ReasonInfo>,
      amountHoldInfoList: freezed == amountHoldInfoList
          ? _value._amountHoldInfoList
          : amountHoldInfoList // ignore: cast_nullable_to_non_nullable
              as List<AmountHoldInfo>?,
    ));
  }

  /// Create a copy of AccountsDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountInfoCopyWith<$Res> get accountInfo {
    return $AccountInfoCopyWith<$Res>(_value.accountInfo, (value) {
      return _then(_value.copyWith(accountInfo: value));
    });
  }
}

/// @nodoc

class _$AccountsDetailsLoadedStateImpl implements AccountsDetailsLoadedState {
  const _$AccountsDetailsLoadedStateImpl(
      {required this.accountInfo,
      required this.agentInfo,
      required final List<FormConfig> srConfigs,
      final List<ReasonInfo> reasonInfoList = const <ReasonInfo>[],
      final List<AmountHoldInfo>? amountHoldInfoList})
      : _srConfigs = srConfigs,
        _reasonInfoList = reasonInfoList,
        _amountHoldInfoList = amountHoldInfoList;

  @override
  final AccountInfo accountInfo;
  @override
  final User agentInfo;
  final List<FormConfig> _srConfigs;
  @override
  List<FormConfig> get srConfigs {
    if (_srConfigs is EqualUnmodifiableListView) return _srConfigs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_srConfigs);
  }

  final List<ReasonInfo> _reasonInfoList;
  @override
  @JsonKey()
  List<ReasonInfo> get reasonInfoList {
    if (_reasonInfoList is EqualUnmodifiableListView) return _reasonInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reasonInfoList);
  }

  final List<AmountHoldInfo>? _amountHoldInfoList;
  @override
  List<AmountHoldInfo>? get amountHoldInfoList {
    final value = _amountHoldInfoList;
    if (value == null) return null;
    if (_amountHoldInfoList is EqualUnmodifiableListView)
      return _amountHoldInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'AccountsDetailsState.loaded(accountInfo: $accountInfo, agentInfo: $agentInfo, srConfigs: $srConfigs, reasonInfoList: $reasonInfoList, amountHoldInfoList: $amountHoldInfoList)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountsDetailsLoadedStateImpl &&
            (identical(other.accountInfo, accountInfo) ||
                other.accountInfo == accountInfo) &&
            (identical(other.agentInfo, agentInfo) ||
                other.agentInfo == agentInfo) &&
            const DeepCollectionEquality()
                .equals(other._srConfigs, _srConfigs) &&
            const DeepCollectionEquality()
                .equals(other._reasonInfoList, _reasonInfoList) &&
            const DeepCollectionEquality()
                .equals(other._amountHoldInfoList, _amountHoldInfoList));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      accountInfo,
      agentInfo,
      const DeepCollectionEquality().hash(_srConfigs),
      const DeepCollectionEquality().hash(_reasonInfoList),
      const DeepCollectionEquality().hash(_amountHoldInfoList));

  /// Create a copy of AccountsDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountsDetailsLoadedStateImplCopyWith<_$AccountsDetailsLoadedStateImpl>
      get copyWith => __$$AccountsDetailsLoadedStateImplCopyWithImpl<
          _$AccountsDetailsLoadedStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountsListInitialState value) initial,
    required TResult Function(AccountsDetailsLoadingState value) loading,
    required TResult Function(AccountsDetailsLoadedState value) loaded,
    required TResult Function(AccountsDetailsErrorState value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountsListInitialState value)? initial,
    TResult? Function(AccountsDetailsLoadingState value)? loading,
    TResult? Function(AccountsDetailsLoadedState value)? loaded,
    TResult? Function(AccountsDetailsErrorState value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountsListInitialState value)? initial,
    TResult Function(AccountsDetailsLoadingState value)? loading,
    TResult Function(AccountsDetailsLoadedState value)? loaded,
    TResult Function(AccountsDetailsErrorState value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class AccountsDetailsLoadedState implements AccountsDetailsState {
  const factory AccountsDetailsLoadedState(
          {required final AccountInfo accountInfo,
          required final User agentInfo,
          required final List<FormConfig> srConfigs,
          final List<ReasonInfo> reasonInfoList,
          final List<AmountHoldInfo>? amountHoldInfoList}) =
      _$AccountsDetailsLoadedStateImpl;

  AccountInfo get accountInfo;
  User get agentInfo;
  List<FormConfig> get srConfigs;
  List<ReasonInfo> get reasonInfoList;
  List<AmountHoldInfo>? get amountHoldInfoList;

  /// Create a copy of AccountsDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountsDetailsLoadedStateImplCopyWith<_$AccountsDetailsLoadedStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AccountsDetailsErrorStateImplCopyWith<$Res> {
  factory _$$AccountsDetailsErrorStateImplCopyWith(
          _$AccountsDetailsErrorStateImpl value,
          $Res Function(_$AccountsDetailsErrorStateImpl) then) =
      __$$AccountsDetailsErrorStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String accountId});
}

/// @nodoc
class __$$AccountsDetailsErrorStateImplCopyWithImpl<$Res>
    extends _$AccountsDetailsStateCopyWithImpl<$Res,
        _$AccountsDetailsErrorStateImpl>
    implements _$$AccountsDetailsErrorStateImplCopyWith<$Res> {
  __$$AccountsDetailsErrorStateImplCopyWithImpl(
      _$AccountsDetailsErrorStateImpl _value,
      $Res Function(_$AccountsDetailsErrorStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountsDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
  }) {
    return _then(_$AccountsDetailsErrorStateImpl(
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AccountsDetailsErrorStateImpl implements AccountsDetailsErrorState {
  const _$AccountsDetailsErrorStateImpl({required this.accountId});

  @override
  final String accountId;

  @override
  String toString() {
    return 'AccountsDetailsState.error(accountId: $accountId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountsDetailsErrorStateImpl &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, accountId);

  /// Create a copy of AccountsDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountsDetailsErrorStateImplCopyWith<_$AccountsDetailsErrorStateImpl>
      get copyWith => __$$AccountsDetailsErrorStateImplCopyWithImpl<
          _$AccountsDetailsErrorStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountsListInitialState value) initial,
    required TResult Function(AccountsDetailsLoadingState value) loading,
    required TResult Function(AccountsDetailsLoadedState value) loaded,
    required TResult Function(AccountsDetailsErrorState value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountsListInitialState value)? initial,
    TResult? Function(AccountsDetailsLoadingState value)? loading,
    TResult? Function(AccountsDetailsLoadedState value)? loaded,
    TResult? Function(AccountsDetailsErrorState value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountsListInitialState value)? initial,
    TResult Function(AccountsDetailsLoadingState value)? loading,
    TResult Function(AccountsDetailsLoadedState value)? loaded,
    TResult Function(AccountsDetailsErrorState value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class AccountsDetailsErrorState implements AccountsDetailsState {
  const factory AccountsDetailsErrorState({required final String accountId}) =
      _$AccountsDetailsErrorStateImpl;

  String get accountId;

  /// Create a copy of AccountsDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountsDetailsErrorStateImplCopyWith<_$AccountsDetailsErrorStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
