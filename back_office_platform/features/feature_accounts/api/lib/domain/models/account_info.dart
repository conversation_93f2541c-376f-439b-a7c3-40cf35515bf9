import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_accounts_api/domain/models/account_enums.dart';
import 'package:wio_feature_accounts_api/domain/models/balance_info.dart';

part 'account_info.freezed.dart';

/// Model to represent account details.
@freezed
class AccountInfo with _$AccountInfo {
  /// Constructor
  const factory AccountInfo({
    /// Account id of account
    required String accountId,

    /// Tells about account current state
    required AccountStatus accountStatus,

    /// Tells about account sub state when active
    required AccountSubState subState,

    /// Tells product type
    required ProductType productType,

    /// Tells about account who is holding the account
    required AccountHolderType accountHolderType,

    /// Tells about account type
    required AccountType accountType,

    /// User balance details
    required BalanceInfo balanceInfo,

    /// Account name
    required String accountName,

    /// Tells if user has NPSS as payment method
    required bool isNPSSEnabled,

    /// Account currency
    required Currency currency,

    /// Maximum amount of money that can be withdrawn from this account
    Money? maxWithdrawalAmount,

    /// Date for created account
    DateTime? accountCreationDate,

    /// Bic
    String? bic,

    /// Business ID of user
    String? businessId,

    /// Account closing date
    DateTime? closedDate,

    /// Account locked date
    DateTime? lockedDate,

    /// Account locked reason
    String? lockedReason,

    /// Nick name set by user
    String? accountNickName,

    /// IBAN number
    String? iBan,

    /// Date for which account activated
    DateTime? activationDate,

    /// Account notes if any
    String? notes,
  }) = _AccountInfo;
}
