{"openapi": "3.0.1", "info": {"title": "Lending Application Service APIs", "description": "The Lending Application Service acts as an adapter\nbetween lending and other platforms.", "version": "1.0.0"}, "servers": [{"url": "http://app-lending-bank-lending-application-service-java-ms.aks.pre.neobank-internal.ae", "description": "Generated server url"}], "paths": {"/api/v1/sme/{businessId}/applications": {"get": {"tags": ["lending-application-service"], "operationId": "get-api-v1-sme-param-applications", "parameters": [{"name": "businessId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerApplicationDetailsDTO"}}}}}}}}, "/api/v1/customers/{customerId}/applications": {"get": {"tags": ["lending-application-service"], "operationId": "get-api-v1-customers-param-applications", "parameters": [{"name": "customerId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerApplicationDetailsDTO"}}}}}}}}}, "components": {"schemas": {"AccountBalances": {"type": "object", "properties": {"paid": {"type": "number"}, "due": {"type": "number"}, "balance": {"type": "number"}}}, "Amount": {"type": "object", "properties": {"due": {"type": "number"}, "expected": {"type": "number"}, "paid": {"type": "number"}}}, "AutoPaymentInfo": {"type": "object", "properties": {"repaymentPrincipal": {"type": "number"}, "repaymentFee": {"type": "number"}, "repaymentDate": {"type": "string", "format": "date"}, "toleranceDate": {"type": "string", "format": "date"}, "rollOverFeeDue": {"type": "number"}}}, "BaseLoanAccountDetailsDTO": {"type": "object", "properties": {"accountId": {"type": "string"}, "lendingApplicationId": {"type": "string"}, "accountState": {"type": "string"}, "accountSubState": {"type": "string"}, "accountHolderName": {"type": "string"}, "totalAccountLimit": {"type": "number"}, "consumedCredit": {"type": "number"}, "availableLimit": {"type": "number"}, "holdingBalance": {"type": "number"}, "currency": {"type": "string"}, "agreementSignedDate": {"type": "string", "format": "date-time"}, "approvedLoanAmount": {"type": "number"}, "interestRate": {"type": "number"}, "installments": {"type": "array", "items": {"$ref": "#/components/schemas/Installment"}}, "creationDate": {"type": "string", "format": "date-time"}, "productType": {"type": "string", "enum": ["CREDIT_CARD", "EASY_CASH", "IPO", "SME_CREDIT_CARD", "SME_EASY_CASH", "SCF", "SME_RECEIVABLE_FINANCE", "CREDIT", "CHANNEL_FINANCE"]}, "state": {"type": "string"}, "subState": {"type": "string"}, "paymentPercentage": {"type": "number"}, "type": {"type": "string"}, "autoPaymentInfo": {"$ref": "#/components/schemas/AutoPaymentInfo"}, "principal": {"$ref": "#/components/schemas/AccountBalances"}, "fee": {"$ref": "#/components/schemas/AccountBalances"}, "interest": {"$ref": "#/components/schemas/AccountBalances"}}}, "CustomerApplicationDetailsDTO": {"type": "object", "properties": {"lendingApplicationId": {"type": "string"}, "customerName": {"type": "string"}, "status": {"type": "string", "enum": ["PREQUALIFIED_OFFER_AVAILABLE", "NOT_ELIGIBLE_FOR_LENDING", "APPLICATION_STARTED", "APPLICATION_SUBMITTED", "UNDERWRITING_IN_REVIEW", "APPROVED_AND_TAKEN", "APPROVED_AND_PENDING", "APPROVED_AND_EXPIRED", "REJECTED", "CLOSED", "LOCKED", "EXPIRED"]}, "lendingStatus": {"type": "string", "enum": ["IN_PROGRESS", "IN_REVIEW", "COMPLETED", "REJECTED", "CANCELLED", "EXPIRED", "APPLICATION_CREATED", "CLOSED", "KYC_UPDATED", "AECB_UPDATED", "STATEMENT_UPDATED", "DES_UPDATED_WITHOUT_UAEFTS", "DES_UPDATED_WITH_UAEFTS", "APPROVED", "PENDING", "ACCOUNT_CREATION_STARTED", "ACCOUNT_CREATED"]}, "riskScore": {"type": "string"}, "underWriterRemarks": {"type": "string"}, "applicationStartDate": {"type": "string", "format": "date-time"}, "applicationSubmitDate": {"type": "string", "format": "date-time"}, "underWriterRemarksDate": {"type": "string", "format": "date-time"}, "lendingProduct": {"type": "string", "enum": ["CREDIT_CARD", "EASY_CASH", "IPO", "SME_CREDIT_CARD", "SME_EASY_CASH", "SCF", "SME_RECEIVABLE_FINANCE", "CREDIT", "CHANNEL_FINANCE"]}, "approvedCreditLimit": {"type": "number"}, "customerAcceptedCreditLimit": {"type": "number"}, "rollOverFeePercentage": {"type": "number"}, "currency": {"type": "string", "enum": ["AED", "USD"]}, "accountDetails": {"$ref": "#/components/schemas/BaseLoanAccountDetailsDTO"}}}, "Installment": {"type": "object", "properties": {"dueDate": {"type": "string", "format": "date-time"}, "principal": {"$ref": "#/components/schemas/Amount"}, "state": {"type": "string"}, "fee": {"$ref": "#/components/schemas/Amount"}, "currency": {"type": "string", "enum": ["AED", "USD"]}}}}}}