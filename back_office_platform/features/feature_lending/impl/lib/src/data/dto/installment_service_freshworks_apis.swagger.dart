// ignore_for_file: type=lint

import 'package:json_annotation/json_annotation.dart';
import 'package:json_annotation/json_annotation.dart' as json;
import 'package:collection/collection.dart';
import 'dart:convert';

import 'installment_service_freshworks_apis.enums.swagger.dart' as enums;
export 'installment_service_freshworks_apis.enums.swagger.dart';

part 'installment_service_freshworks_apis.swagger.g.dart';

@JsonSerializable(explicitToJson: true)
class AccountDetails {
  const AccountDetails({
    this.id,
    this.accountId,
    this.creditArrangementId,
    this.name,
    this.loanAmount,
    this.currency,
    this.type,
    this.createdAt,
    this.lastModifiedAt,
    this.createdBy,
    this.lastModifiedBy,
    this.settlementAccountId,
    this.monthlyRepaymentDate,
    this.paymentPercentage,
    this.settlementAccountKey,
    this.customerId,
    this.isActive,
  });

  factory AccountDetails.fromJson(Map<String, dynamic> json) =>
      _$AccountDetailsFromJson(json);

  static const toJsonFactory = _$AccountDetailsToJson;
  Map<String, dynamic> toJson() => _$AccountDetailsToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String? id;
  @JsonKey(name: 'accountId', includeIfNull: false)
  final String? accountId;
  @JsonKey(name: 'creditArrangementId', includeIfNull: false)
  final String? creditArrangementId;
  @JsonKey(name: 'name', includeIfNull: false)
  final String? name;
  @JsonKey(name: 'loanAmount', includeIfNull: false)
  final double? loanAmount;
  @JsonKey(
    name: 'currency',
    includeIfNull: false,
    toJson: accountDetailsCurrencyNullableToJson,
    fromJson: accountDetailsCurrencyNullableFromJson,
  )
  final enums.AccountDetailsCurrency? currency;
  @JsonKey(
    name: 'type',
    includeIfNull: false,
    toJson: accountDetailsTypeNullableToJson,
    fromJson: accountDetailsTypeNullableFromJson,
  )
  final enums.AccountDetailsType? type;
  @JsonKey(name: 'createdAt', includeIfNull: false)
  final DateTime? createdAt;
  @JsonKey(name: 'lastModifiedAt', includeIfNull: false)
  final DateTime? lastModifiedAt;
  @JsonKey(name: 'createdBy', includeIfNull: false)
  final String? createdBy;
  @JsonKey(name: 'lastModifiedBy', includeIfNull: false)
  final String? lastModifiedBy;
  @JsonKey(name: 'settlementAccountId', includeIfNull: false)
  final String? settlementAccountId;
  @JsonKey(name: 'monthlyRepaymentDate', includeIfNull: false)
  final int? monthlyRepaymentDate;
  @JsonKey(name: 'paymentPercentage', includeIfNull: false)
  final double? paymentPercentage;
  @JsonKey(name: 'settlementAccountKey', includeIfNull: false)
  final String? settlementAccountKey;
  @JsonKey(name: 'customerId', includeIfNull: false)
  final String? customerId;
  @JsonKey(name: 'isActive', includeIfNull: false)
  final bool? isActive;
  static const fromJsonFactory = _$AccountDetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AccountDetailsExtension on AccountDetails {
  AccountDetails copyWith(
      {String? id,
      String? accountId,
      String? creditArrangementId,
      String? name,
      double? loanAmount,
      enums.AccountDetailsCurrency? currency,
      enums.AccountDetailsType? type,
      DateTime? createdAt,
      DateTime? lastModifiedAt,
      String? createdBy,
      String? lastModifiedBy,
      String? settlementAccountId,
      int? monthlyRepaymentDate,
      double? paymentPercentage,
      String? settlementAccountKey,
      String? customerId,
      bool? isActive}) {
    return AccountDetails(
        id: id ?? this.id,
        accountId: accountId ?? this.accountId,
        creditArrangementId: creditArrangementId ?? this.creditArrangementId,
        name: name ?? this.name,
        loanAmount: loanAmount ?? this.loanAmount,
        currency: currency ?? this.currency,
        type: type ?? this.type,
        createdAt: createdAt ?? this.createdAt,
        lastModifiedAt: lastModifiedAt ?? this.lastModifiedAt,
        createdBy: createdBy ?? this.createdBy,
        lastModifiedBy: lastModifiedBy ?? this.lastModifiedBy,
        settlementAccountId: settlementAccountId ?? this.settlementAccountId,
        monthlyRepaymentDate: monthlyRepaymentDate ?? this.monthlyRepaymentDate,
        paymentPercentage: paymentPercentage ?? this.paymentPercentage,
        settlementAccountKey: settlementAccountKey ?? this.settlementAccountKey,
        customerId: customerId ?? this.customerId,
        isActive: isActive ?? this.isActive);
  }

  AccountDetails copyWithWrapped(
      {Wrapped<String?>? id,
      Wrapped<String?>? accountId,
      Wrapped<String?>? creditArrangementId,
      Wrapped<String?>? name,
      Wrapped<double?>? loanAmount,
      Wrapped<enums.AccountDetailsCurrency?>? currency,
      Wrapped<enums.AccountDetailsType?>? type,
      Wrapped<DateTime?>? createdAt,
      Wrapped<DateTime?>? lastModifiedAt,
      Wrapped<String?>? createdBy,
      Wrapped<String?>? lastModifiedBy,
      Wrapped<String?>? settlementAccountId,
      Wrapped<int?>? monthlyRepaymentDate,
      Wrapped<double?>? paymentPercentage,
      Wrapped<String?>? settlementAccountKey,
      Wrapped<String?>? customerId,
      Wrapped<bool?>? isActive}) {
    return AccountDetails(
        id: (id != null ? id.value : this.id),
        accountId: (accountId != null ? accountId.value : this.accountId),
        creditArrangementId: (creditArrangementId != null
            ? creditArrangementId.value
            : this.creditArrangementId),
        name: (name != null ? name.value : this.name),
        loanAmount: (loanAmount != null ? loanAmount.value : this.loanAmount),
        currency: (currency != null ? currency.value : this.currency),
        type: (type != null ? type.value : this.type),
        createdAt: (createdAt != null ? createdAt.value : this.createdAt),
        lastModifiedAt: (lastModifiedAt != null
            ? lastModifiedAt.value
            : this.lastModifiedAt),
        createdBy: (createdBy != null ? createdBy.value : this.createdBy),
        lastModifiedBy: (lastModifiedBy != null
            ? lastModifiedBy.value
            : this.lastModifiedBy),
        settlementAccountId: (settlementAccountId != null
            ? settlementAccountId.value
            : this.settlementAccountId),
        monthlyRepaymentDate: (monthlyRepaymentDate != null
            ? monthlyRepaymentDate.value
            : this.monthlyRepaymentDate),
        paymentPercentage: (paymentPercentage != null
            ? paymentPercentage.value
            : this.paymentPercentage),
        settlementAccountKey: (settlementAccountKey != null
            ? settlementAccountKey.value
            : this.settlementAccountKey),
        customerId: (customerId != null ? customerId.value : this.customerId),
        isActive: (isActive != null ? isActive.value : this.isActive));
  }
}

@JsonSerializable(explicitToJson: true)
class UpdateLoanAmountCommand {
  const UpdateLoanAmountCommand({
    required this.loanAmount,
  });

  factory UpdateLoanAmountCommand.fromJson(Map<String, dynamic> json) =>
      _$UpdateLoanAmountCommandFromJson(json);

  static const toJsonFactory = _$UpdateLoanAmountCommandToJson;
  Map<String, dynamic> toJson() => _$UpdateLoanAmountCommandToJson(this);

  @JsonKey(name: 'loanAmount', includeIfNull: false)
  final double loanAmount;
  static const fromJsonFactory = _$UpdateLoanAmountCommandFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UpdateLoanAmountCommandExtension on UpdateLoanAmountCommand {
  UpdateLoanAmountCommand copyWith({double? loanAmount}) {
    return UpdateLoanAmountCommand(loanAmount: loanAmount ?? this.loanAmount);
  }

  UpdateLoanAmountCommand copyWithWrapped({Wrapped<double>? loanAmount}) {
    return UpdateLoanAmountCommand(
        loanAmount: (loanAmount != null ? loanAmount.value : this.loanAmount));
  }
}

@JsonSerializable(explicitToJson: true)
class CreditArrangementLimits {
  const CreditArrangementLimits({
    required this.globalLimit,
    required this.approvedAmount,
    this.riskGrade,
    required this.customerId,
  });

  factory CreditArrangementLimits.fromJson(Map<String, dynamic> json) =>
      _$CreditArrangementLimitsFromJson(json);

  static const toJsonFactory = _$CreditArrangementLimitsToJson;
  Map<String, dynamic> toJson() => _$CreditArrangementLimitsToJson(this);

  @JsonKey(name: 'globalLimit', includeIfNull: false)
  final double globalLimit;
  @JsonKey(name: 'approvedAmount', includeIfNull: false)
  final double approvedAmount;
  @JsonKey(name: 'riskGrade', includeIfNull: false)
  final String? riskGrade;
  @JsonKey(name: 'customerId', includeIfNull: false)
  final String customerId;
  static const fromJsonFactory = _$CreditArrangementLimitsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CreditArrangementLimitsExtension on CreditArrangementLimits {
  CreditArrangementLimits copyWith(
      {double? globalLimit,
      double? approvedAmount,
      String? riskGrade,
      String? customerId}) {
    return CreditArrangementLimits(
        globalLimit: globalLimit ?? this.globalLimit,
        approvedAmount: approvedAmount ?? this.approvedAmount,
        riskGrade: riskGrade ?? this.riskGrade,
        customerId: customerId ?? this.customerId);
  }

  CreditArrangementLimits copyWithWrapped(
      {Wrapped<double>? globalLimit,
      Wrapped<double>? approvedAmount,
      Wrapped<String?>? riskGrade,
      Wrapped<String>? customerId}) {
    return CreditArrangementLimits(
        globalLimit:
            (globalLimit != null ? globalLimit.value : this.globalLimit),
        approvedAmount: (approvedAmount != null
            ? approvedAmount.value
            : this.approvedAmount),
        riskGrade: (riskGrade != null ? riskGrade.value : this.riskGrade),
        customerId: (customerId != null ? customerId.value : this.customerId));
  }
}

@JsonSerializable(explicitToJson: true)
class LoanAccountDetailsDTO {
  const LoanAccountDetailsDTO({
    this.id,
    this.applicationId,
    this.name,
    this.creationDate,
    this.currency,
    this.notes,
    this.holderKey,
    this.branchKey,
    this.productKey,
    this.productType,
    this.lendingProductSubType,
    this.securityType,
    this.state,
    this.subState,
    this.rawMambuResponse,
    this.loanAmount,
    this.paymentSettings,
    this.paymentSchedule,
    this.interestSettings,
    this.firstRepaymentDate,
    this.type,
    this.encodedKey,
    this.accountHolderType,
    this.customerId,
    this.principleBalance,
    this.interestBalance,
    this.feeBalance,
    this.holdBalance,
    this.interestDue,
    this.minLimit,
    this.estimatedFee,
  });

  factory LoanAccountDetailsDTO.fromJson(Map<String, dynamic> json) =>
      _$LoanAccountDetailsDTOFromJson(json);

  static const toJsonFactory = _$LoanAccountDetailsDTOToJson;
  Map<String, dynamic> toJson() => _$LoanAccountDetailsDTOToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String? id;
  @JsonKey(name: 'applicationId', includeIfNull: false)
  final String? applicationId;
  @JsonKey(name: 'name', includeIfNull: false)
  final String? name;
  @JsonKey(name: 'creationDate', includeIfNull: false)
  final DateTime? creationDate;
  @JsonKey(
    name: 'currency',
    includeIfNull: false,
    toJson: loanAccountDetailsDTOCurrencyNullableToJson,
    fromJson: loanAccountDetailsDTOCurrencyNullableFromJson,
  )
  final enums.LoanAccountDetailsDTOCurrency? currency;
  @JsonKey(name: 'notes', includeIfNull: false)
  final String? notes;
  @JsonKey(name: 'holderKey', includeIfNull: false)
  final String? holderKey;
  @JsonKey(name: 'branchKey', includeIfNull: false)
  final String? branchKey;
  @JsonKey(name: 'productKey', includeIfNull: false)
  final String? productKey;
  @JsonKey(
    name: 'productType',
    includeIfNull: false,
    toJson: loanAccountDetailsDTOProductTypeNullableToJson,
    fromJson: loanAccountDetailsDTOProductTypeNullableFromJson,
  )
  final enums.LoanAccountDetailsDTOProductType? productType;
  @JsonKey(
    name: 'lendingProductSubType',
    includeIfNull: false,
    toJson: loanAccountDetailsDTOLendingProductSubTypeNullableToJson,
    fromJson: loanAccountDetailsDTOLendingProductSubTypeNullableFromJson,
  )
  final enums.LoanAccountDetailsDTOLendingProductSubType? lendingProductSubType;
  @JsonKey(
    name: 'securityType',
    includeIfNull: false,
    toJson: securityTypeNullableToJson,
    fromJson: securityTypeNullableFromJson,
  )
  final enums.SecurityType? securityType;
  @JsonKey(name: 'state', includeIfNull: false)
  final String? state;
  @JsonKey(name: 'subState', includeIfNull: false)
  final String? subState;
  @JsonKey(name: 'rawMambuResponse', includeIfNull: false)
  final Object? rawMambuResponse;
  @JsonKey(name: 'loanAmount', includeIfNull: false)
  final double? loanAmount;
  @JsonKey(name: 'paymentSettings', includeIfNull: false)
  final PaymentSettings? paymentSettings;
  @JsonKey(name: 'paymentSchedule', includeIfNull: false)
  final ScheduleSettings? paymentSchedule;
  @JsonKey(name: 'interestSettings', includeIfNull: false)
  final InterestSettings? interestSettings;
  @JsonKey(
      name: 'firstRepaymentDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime? firstRepaymentDate;
  @JsonKey(
    name: 'type',
    includeIfNull: false,
    toJson: loanAccountDetailsDTOTypeNullableToJson,
    fromJson: loanAccountDetailsDTOTypeNullableFromJson,
  )
  final enums.LoanAccountDetailsDTOType? type;
  @JsonKey(name: 'encodedKey', includeIfNull: false)
  final String? encodedKey;
  @JsonKey(name: 'accountHolderType', includeIfNull: false)
  final String? accountHolderType;
  @JsonKey(name: 'customerId', includeIfNull: false)
  final String? customerId;
  @JsonKey(name: 'principleBalance', includeIfNull: false)
  final double? principleBalance;
  @JsonKey(name: 'interestBalance', includeIfNull: false)
  final double? interestBalance;
  @JsonKey(name: 'feeBalance', includeIfNull: false)
  final double? feeBalance;
  @JsonKey(name: 'holdBalance', includeIfNull: false)
  final double? holdBalance;
  @JsonKey(name: 'interestDue', includeIfNull: false)
  final double? interestDue;
  @JsonKey(name: 'minLimit', includeIfNull: false)
  final double? minLimit;
  @JsonKey(name: 'estimatedFee', includeIfNull: false)
  final double? estimatedFee;
  static const fromJsonFactory = _$LoanAccountDetailsDTOFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $LoanAccountDetailsDTOExtension on LoanAccountDetailsDTO {
  LoanAccountDetailsDTO copyWith(
      {String? id,
      String? applicationId,
      String? name,
      DateTime? creationDate,
      enums.LoanAccountDetailsDTOCurrency? currency,
      String? notes,
      String? holderKey,
      String? branchKey,
      String? productKey,
      enums.LoanAccountDetailsDTOProductType? productType,
      enums.LoanAccountDetailsDTOLendingProductSubType? lendingProductSubType,
      enums.SecurityType? securityType,
      String? state,
      String? subState,
      Object? rawMambuResponse,
      double? loanAmount,
      PaymentSettings? paymentSettings,
      ScheduleSettings? paymentSchedule,
      InterestSettings? interestSettings,
      DateTime? firstRepaymentDate,
      enums.LoanAccountDetailsDTOType? type,
      String? encodedKey,
      String? accountHolderType,
      String? customerId,
      double? principleBalance,
      double? interestBalance,
      double? feeBalance,
      double? holdBalance,
      double? interestDue,
      double? minLimit,
      double? estimatedFee}) {
    return LoanAccountDetailsDTO(
        id: id ?? this.id,
        applicationId: applicationId ?? this.applicationId,
        name: name ?? this.name,
        creationDate: creationDate ?? this.creationDate,
        currency: currency ?? this.currency,
        notes: notes ?? this.notes,
        holderKey: holderKey ?? this.holderKey,
        branchKey: branchKey ?? this.branchKey,
        productKey: productKey ?? this.productKey,
        productType: productType ?? this.productType,
        lendingProductSubType:
            lendingProductSubType ?? this.lendingProductSubType,
        securityType: securityType ?? this.securityType,
        state: state ?? this.state,
        subState: subState ?? this.subState,
        rawMambuResponse: rawMambuResponse ?? this.rawMambuResponse,
        loanAmount: loanAmount ?? this.loanAmount,
        paymentSettings: paymentSettings ?? this.paymentSettings,
        paymentSchedule: paymentSchedule ?? this.paymentSchedule,
        interestSettings: interestSettings ?? this.interestSettings,
        firstRepaymentDate: firstRepaymentDate ?? this.firstRepaymentDate,
        type: type ?? this.type,
        encodedKey: encodedKey ?? this.encodedKey,
        accountHolderType: accountHolderType ?? this.accountHolderType,
        customerId: customerId ?? this.customerId,
        principleBalance: principleBalance ?? this.principleBalance,
        interestBalance: interestBalance ?? this.interestBalance,
        feeBalance: feeBalance ?? this.feeBalance,
        holdBalance: holdBalance ?? this.holdBalance,
        interestDue: interestDue ?? this.interestDue,
        minLimit: minLimit ?? this.minLimit,
        estimatedFee: estimatedFee ?? this.estimatedFee);
  }

  LoanAccountDetailsDTO copyWithWrapped(
      {Wrapped<String?>? id,
      Wrapped<String?>? applicationId,
      Wrapped<String?>? name,
      Wrapped<DateTime?>? creationDate,
      Wrapped<enums.LoanAccountDetailsDTOCurrency?>? currency,
      Wrapped<String?>? notes,
      Wrapped<String?>? holderKey,
      Wrapped<String?>? branchKey,
      Wrapped<String?>? productKey,
      Wrapped<enums.LoanAccountDetailsDTOProductType?>? productType,
      Wrapped<enums.LoanAccountDetailsDTOLendingProductSubType?>?
          lendingProductSubType,
      Wrapped<enums.SecurityType?>? securityType,
      Wrapped<String?>? state,
      Wrapped<String?>? subState,
      Wrapped<Object?>? rawMambuResponse,
      Wrapped<double?>? loanAmount,
      Wrapped<PaymentSettings?>? paymentSettings,
      Wrapped<ScheduleSettings?>? paymentSchedule,
      Wrapped<InterestSettings?>? interestSettings,
      Wrapped<DateTime?>? firstRepaymentDate,
      Wrapped<enums.LoanAccountDetailsDTOType?>? type,
      Wrapped<String?>? encodedKey,
      Wrapped<String?>? accountHolderType,
      Wrapped<String?>? customerId,
      Wrapped<double?>? principleBalance,
      Wrapped<double?>? interestBalance,
      Wrapped<double?>? feeBalance,
      Wrapped<double?>? holdBalance,
      Wrapped<double?>? interestDue,
      Wrapped<double?>? minLimit,
      Wrapped<double?>? estimatedFee}) {
    return LoanAccountDetailsDTO(
        id: (id != null ? id.value : this.id),
        applicationId:
            (applicationId != null ? applicationId.value : this.applicationId),
        name: (name != null ? name.value : this.name),
        creationDate:
            (creationDate != null ? creationDate.value : this.creationDate),
        currency: (currency != null ? currency.value : this.currency),
        notes: (notes != null ? notes.value : this.notes),
        holderKey: (holderKey != null ? holderKey.value : this.holderKey),
        branchKey: (branchKey != null ? branchKey.value : this.branchKey),
        productKey: (productKey != null ? productKey.value : this.productKey),
        productType:
            (productType != null ? productType.value : this.productType),
        lendingProductSubType: (lendingProductSubType != null
            ? lendingProductSubType.value
            : this.lendingProductSubType),
        securityType:
            (securityType != null ? securityType.value : this.securityType),
        state: (state != null ? state.value : this.state),
        subState: (subState != null ? subState.value : this.subState),
        rawMambuResponse: (rawMambuResponse != null
            ? rawMambuResponse.value
            : this.rawMambuResponse),
        loanAmount: (loanAmount != null ? loanAmount.value : this.loanAmount),
        paymentSettings: (paymentSettings != null
            ? paymentSettings.value
            : this.paymentSettings),
        paymentSchedule: (paymentSchedule != null
            ? paymentSchedule.value
            : this.paymentSchedule),
        interestSettings: (interestSettings != null
            ? interestSettings.value
            : this.interestSettings),
        firstRepaymentDate: (firstRepaymentDate != null
            ? firstRepaymentDate.value
            : this.firstRepaymentDate),
        type: (type != null ? type.value : this.type),
        encodedKey: (encodedKey != null ? encodedKey.value : this.encodedKey),
        accountHolderType: (accountHolderType != null
            ? accountHolderType.value
            : this.accountHolderType),
        customerId: (customerId != null ? customerId.value : this.customerId),
        principleBalance: (principleBalance != null
            ? principleBalance.value
            : this.principleBalance),
        interestBalance: (interestBalance != null
            ? interestBalance.value
            : this.interestBalance),
        feeBalance: (feeBalance != null ? feeBalance.value : this.feeBalance),
        holdBalance:
            (holdBalance != null ? holdBalance.value : this.holdBalance),
        interestDue:
            (interestDue != null ? interestDue.value : this.interestDue),
        minLimit: (minLimit != null ? minLimit.value : this.minLimit),
        estimatedFee:
            (estimatedFee != null ? estimatedFee.value : this.estimatedFee));
  }
}

@JsonSerializable(explicitToJson: true)
class LoanAccountDetailsResponse {
  const LoanAccountDetailsResponse({
    this.loanAccountDetailsDTOS,
    this.creditSummary,
  });

  factory LoanAccountDetailsResponse.fromJson(Map<String, dynamic> json) =>
      _$LoanAccountDetailsResponseFromJson(json);

  static const toJsonFactory = _$LoanAccountDetailsResponseToJson;
  Map<String, dynamic> toJson() => _$LoanAccountDetailsResponseToJson(this);

  @JsonKey(
      name: 'loanAccountDetailsDTOS',
      includeIfNull: false,
      defaultValue: <LoanAccountDetailsDTO>[])
  final List<LoanAccountDetailsDTO>? loanAccountDetailsDTOS;
  @JsonKey(name: 'creditSummary', includeIfNull: false)
  final CreditSummary? creditSummary;
  static const fromJsonFactory = _$LoanAccountDetailsResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $LoanAccountDetailsResponseExtension on LoanAccountDetailsResponse {
  LoanAccountDetailsResponse copyWith(
      {List<LoanAccountDetailsDTO>? loanAccountDetailsDTOS,
      CreditSummary? creditSummary}) {
    return LoanAccountDetailsResponse(
        loanAccountDetailsDTOS:
            loanAccountDetailsDTOS ?? this.loanAccountDetailsDTOS,
        creditSummary: creditSummary ?? this.creditSummary);
  }

  LoanAccountDetailsResponse copyWithWrapped(
      {Wrapped<List<LoanAccountDetailsDTO>?>? loanAccountDetailsDTOS,
      Wrapped<CreditSummary?>? creditSummary}) {
    return LoanAccountDetailsResponse(
        loanAccountDetailsDTOS: (loanAccountDetailsDTOS != null
            ? loanAccountDetailsDTOS.value
            : this.loanAccountDetailsDTOS),
        creditSummary:
            (creditSummary != null ? creditSummary.value : this.creditSummary));
  }
}

@JsonSerializable(explicitToJson: true)
class PaymentSettings {
  const PaymentSettings({
    this.percentage,
  });

  factory PaymentSettings.fromJson(Map<String, dynamic> json) =>
      _$PaymentSettingsFromJson(json);

  static const toJsonFactory = _$PaymentSettingsToJson;
  Map<String, dynamic> toJson() => _$PaymentSettingsToJson(this);

  @JsonKey(name: 'percentage', includeIfNull: false)
  final double? percentage;
  static const fromJsonFactory = _$PaymentSettingsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $PaymentSettingsExtension on PaymentSettings {
  PaymentSettings copyWith({double? percentage}) {
    return PaymentSettings(percentage: percentage ?? this.percentage);
  }

  PaymentSettings copyWithWrapped({Wrapped<double?>? percentage}) {
    return PaymentSettings(
        percentage: (percentage != null ? percentage.value : this.percentage));
  }
}

@JsonSerializable(explicitToJson: true)
class ScheduleSettings {
  const ScheduleSettings({
    this.interval,
    this.numberOfPreviewedInstalments,
    this.firstRepaymentDate,
    this.repaymentPeriodCount,
    this.repaymentPeriodUnit,
  });

  factory ScheduleSettings.fromJson(Map<String, dynamic> json) =>
      _$ScheduleSettingsFromJson(json);

  static const toJsonFactory = _$ScheduleSettingsToJson;
  Map<String, dynamic> toJson() => _$ScheduleSettingsToJson(this);

  @JsonKey(name: 'interval', includeIfNull: false)
  final Interval? interval;
  @JsonKey(name: 'numberOfPreviewedInstalments', includeIfNull: false)
  final int? numberOfPreviewedInstalments;
  @JsonKey(
      name: 'firstRepaymentDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime? firstRepaymentDate;
  @JsonKey(name: 'repaymentPeriodCount', includeIfNull: false)
  final int? repaymentPeriodCount;
  @JsonKey(
    name: 'repaymentPeriodUnit',
    includeIfNull: false,
    toJson: scheduleSettingsRepaymentPeriodUnitNullableToJson,
    fromJson: scheduleSettingsRepaymentPeriodUnitNullableFromJson,
  )
  final enums.ScheduleSettingsRepaymentPeriodUnit? repaymentPeriodUnit;
  static const fromJsonFactory = _$ScheduleSettingsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ScheduleSettingsExtension on ScheduleSettings {
  ScheduleSettings copyWith(
      {Interval? interval,
      int? numberOfPreviewedInstalments,
      DateTime? firstRepaymentDate,
      int? repaymentPeriodCount,
      enums.ScheduleSettingsRepaymentPeriodUnit? repaymentPeriodUnit}) {
    return ScheduleSettings(
        interval: interval ?? this.interval,
        numberOfPreviewedInstalments:
            numberOfPreviewedInstalments ?? this.numberOfPreviewedInstalments,
        firstRepaymentDate: firstRepaymentDate ?? this.firstRepaymentDate,
        repaymentPeriodCount: repaymentPeriodCount ?? this.repaymentPeriodCount,
        repaymentPeriodUnit: repaymentPeriodUnit ?? this.repaymentPeriodUnit);
  }

  ScheduleSettings copyWithWrapped(
      {Wrapped<Interval?>? interval,
      Wrapped<int?>? numberOfPreviewedInstalments,
      Wrapped<DateTime?>? firstRepaymentDate,
      Wrapped<int?>? repaymentPeriodCount,
      Wrapped<enums.ScheduleSettingsRepaymentPeriodUnit?>?
          repaymentPeriodUnit}) {
    return ScheduleSettings(
        interval: (interval != null ? interval.value : this.interval),
        numberOfPreviewedInstalments: (numberOfPreviewedInstalments != null
            ? numberOfPreviewedInstalments.value
            : this.numberOfPreviewedInstalments),
        firstRepaymentDate: (firstRepaymentDate != null
            ? firstRepaymentDate.value
            : this.firstRepaymentDate),
        repaymentPeriodCount: (repaymentPeriodCount != null
            ? repaymentPeriodCount.value
            : this.repaymentPeriodCount),
        repaymentPeriodUnit: (repaymentPeriodUnit != null
            ? repaymentPeriodUnit.value
            : this.repaymentPeriodUnit));
  }
}

@JsonSerializable(explicitToJson: true)
class InterestSettings {
  const InterestSettings({
    this.interestRateSource,
    this.interestRate,
    this.interestType,
  });

  factory InterestSettings.fromJson(Map<String, dynamic> json) =>
      _$InterestSettingsFromJson(json);

  static const toJsonFactory = _$InterestSettingsToJson;
  Map<String, dynamic> toJson() => _$InterestSettingsToJson(this);

  @JsonKey(
    name: 'interestRateSource',
    includeIfNull: false,
    toJson: interestSettingsInterestRateSourceNullableToJson,
    fromJson: interestSettingsInterestRateSourceNullableFromJson,
  )
  final enums.InterestSettingsInterestRateSource? interestRateSource;
  @JsonKey(name: 'interestRate', includeIfNull: false)
  final double? interestRate;
  @JsonKey(
    name: 'interestType',
    includeIfNull: false,
    toJson: interestSettingsInterestTypeNullableToJson,
    fromJson: interestSettingsInterestTypeNullableFromJson,
  )
  final enums.InterestSettingsInterestType? interestType;
  static const fromJsonFactory = _$InterestSettingsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $InterestSettingsExtension on InterestSettings {
  InterestSettings copyWith(
      {enums.InterestSettingsInterestRateSource? interestRateSource,
      double? interestRate,
      enums.InterestSettingsInterestType? interestType}) {
    return InterestSettings(
        interestRateSource: interestRateSource ?? this.interestRateSource,
        interestRate: interestRate ?? this.interestRate,
        interestType: interestType ?? this.interestType);
  }

  InterestSettings copyWithWrapped(
      {Wrapped<enums.InterestSettingsInterestRateSource?>? interestRateSource,
      Wrapped<double?>? interestRate,
      Wrapped<enums.InterestSettingsInterestType?>? interestType}) {
    return InterestSettings(
        interestRateSource: (interestRateSource != null
            ? interestRateSource.value
            : this.interestRateSource),
        interestRate:
            (interestRate != null ? interestRate.value : this.interestRate),
        interestType:
            (interestType != null ? interestType.value : this.interestType));
  }
}

@JsonSerializable(explicitToJson: true)
class Interval {
  const Interval({
    this.count,
    this.unit,
  });

  factory Interval.fromJson(Map<String, dynamic> json) =>
      _$IntervalFromJson(json);

  static const toJsonFactory = _$IntervalToJson;
  Map<String, dynamic> toJson() => _$IntervalToJson(this);

  @JsonKey(name: 'count', includeIfNull: false)
  final int? count;
  @JsonKey(
    name: 'unit',
    includeIfNull: false,
    toJson: intervalUnitNullableToJson,
    fromJson: intervalUnitNullableFromJson,
  )
  final enums.IntervalUnit? unit;
  static const fromJsonFactory = _$IntervalFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $IntervalExtension on Interval {
  Interval copyWith({int? count, enums.IntervalUnit? unit}) {
    return Interval(count: count ?? this.count, unit: unit ?? this.unit);
  }

  Interval copyWithWrapped(
      {Wrapped<int?>? count, Wrapped<enums.IntervalUnit?>? unit}) {
    return Interval(
        count: (count != null ? count.value : this.count),
        unit: (unit != null ? unit.value : this.unit));
  }
}

@JsonSerializable(explicitToJson: true)
class CreditSummary {
  const CreditSummary({
    this.availableToSpend,
    this.wioCreditAmount,
    this.totalCreditAmount,
    this.creditCardLoanAmount,
    this.easyCashLoanAmount,
    this.cardInstallmentLoanAmount,
    this.personalLoanAmount,
    this.businessLoanAmount,
  });

  factory CreditSummary.fromJson(Map<String, dynamic> json) =>
      _$CreditSummaryFromJson(json);

  static const toJsonFactory = _$CreditSummaryToJson;
  Map<String, dynamic> toJson() => _$CreditSummaryToJson(this);

  @JsonKey(name: 'availableToSpend', includeIfNull: false)
  final double? availableToSpend;
  @JsonKey(name: 'wioCreditAmount', includeIfNull: false)
  final double? wioCreditAmount;
  @JsonKey(name: 'totalCreditAmount', includeIfNull: false)
  final double? totalCreditAmount;
  @JsonKey(name: 'creditCardLoanAmount', includeIfNull: false)
  final double? creditCardLoanAmount;
  @JsonKey(name: 'easyCashLoanAmount', includeIfNull: false)
  final double? easyCashLoanAmount;
  @JsonKey(name: 'cardInstallmentLoanAmount', includeIfNull: false)
  final double? cardInstallmentLoanAmount;
  @JsonKey(name: 'personalLoanAmount', includeIfNull: false)
  final double? personalLoanAmount;
  @JsonKey(name: 'businessLoanAmount', includeIfNull: false)
  final double? businessLoanAmount;
  static const fromJsonFactory = _$CreditSummaryFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CreditSummaryExtension on CreditSummary {
  CreditSummary copyWith(
      {double? availableToSpend,
      double? wioCreditAmount,
      double? totalCreditAmount,
      double? creditCardLoanAmount,
      double? easyCashLoanAmount,
      double? cardInstallmentLoanAmount,
      double? personalLoanAmount,
      double? businessLoanAmount}) {
    return CreditSummary(
        availableToSpend: availableToSpend ?? this.availableToSpend,
        wioCreditAmount: wioCreditAmount ?? this.wioCreditAmount,
        totalCreditAmount: totalCreditAmount ?? this.totalCreditAmount,
        creditCardLoanAmount: creditCardLoanAmount ?? this.creditCardLoanAmount,
        easyCashLoanAmount: easyCashLoanAmount ?? this.easyCashLoanAmount,
        cardInstallmentLoanAmount:
            cardInstallmentLoanAmount ?? this.cardInstallmentLoanAmount,
        personalLoanAmount: personalLoanAmount ?? this.personalLoanAmount,
        businessLoanAmount: businessLoanAmount ?? this.businessLoanAmount);
  }

  CreditSummary copyWithWrapped(
      {Wrapped<double?>? availableToSpend,
      Wrapped<double?>? wioCreditAmount,
      Wrapped<double?>? totalCreditAmount,
      Wrapped<double?>? creditCardLoanAmount,
      Wrapped<double?>? easyCashLoanAmount,
      Wrapped<double?>? cardInstallmentLoanAmount,
      Wrapped<double?>? personalLoanAmount,
      Wrapped<double?>? businessLoanAmount}) {
    return CreditSummary(
        availableToSpend: (availableToSpend != null
            ? availableToSpend.value
            : this.availableToSpend),
        wioCreditAmount: (wioCreditAmount != null
            ? wioCreditAmount.value
            : this.wioCreditAmount),
        totalCreditAmount: (totalCreditAmount != null
            ? totalCreditAmount.value
            : this.totalCreditAmount),
        creditCardLoanAmount: (creditCardLoanAmount != null
            ? creditCardLoanAmount.value
            : this.creditCardLoanAmount),
        easyCashLoanAmount: (easyCashLoanAmount != null
            ? easyCashLoanAmount.value
            : this.easyCashLoanAmount),
        cardInstallmentLoanAmount: (cardInstallmentLoanAmount != null
            ? cardInstallmentLoanAmount.value
            : this.cardInstallmentLoanAmount),
        personalLoanAmount: (personalLoanAmount != null
            ? personalLoanAmount.value
            : this.personalLoanAmount),
        businessLoanAmount: (businessLoanAmount != null
            ? businessLoanAmount.value
            : this.businessLoanAmount));
  }
}

@JsonSerializable(explicitToJson: true)
class EasyCashLimitDetailsDTO {
  const EasyCashLimitDetailsDTO({
    this.easyCashPercentageLimit,
    this.easyCashInterestRate,
    this.easyCashAvailableLimit,
    this.easyCashLimit,
    this.creditCardLimit,
    this.totalLimit,
    this.dueDate,
    this.creditArrangement,
  });

  factory EasyCashLimitDetailsDTO.fromJson(Map<String, dynamic> json) =>
      _$EasyCashLimitDetailsDTOFromJson(json);

  static const toJsonFactory = _$EasyCashLimitDetailsDTOToJson;
  Map<String, dynamic> toJson() => _$EasyCashLimitDetailsDTOToJson(this);

  @JsonKey(name: 'easyCashPercentageLimit', includeIfNull: false)
  final double? easyCashPercentageLimit;
  @JsonKey(name: 'easyCashInterestRate', includeIfNull: false)
  final double? easyCashInterestRate;
  @JsonKey(name: 'easyCashAvailableLimit', includeIfNull: false)
  final Money? easyCashAvailableLimit;
  @JsonKey(name: 'easyCashLimit', includeIfNull: false)
  final Money? easyCashLimit;
  @JsonKey(name: 'creditCardLimit', includeIfNull: false)
  final Money? creditCardLimit;
  @JsonKey(name: 'totalLimit', includeIfNull: false)
  final Money? totalLimit;
  @JsonKey(name: 'dueDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime? dueDate;
  @JsonKey(name: 'creditArrangement', includeIfNull: false)
  final CreditArrangement? creditArrangement;
  static const fromJsonFactory = _$EasyCashLimitDetailsDTOFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EasyCashLimitDetailsDTOExtension on EasyCashLimitDetailsDTO {
  EasyCashLimitDetailsDTO copyWith(
      {double? easyCashPercentageLimit,
      double? easyCashInterestRate,
      Money? easyCashAvailableLimit,
      Money? easyCashLimit,
      Money? creditCardLimit,
      Money? totalLimit,
      DateTime? dueDate,
      CreditArrangement? creditArrangement}) {
    return EasyCashLimitDetailsDTO(
        easyCashPercentageLimit:
            easyCashPercentageLimit ?? this.easyCashPercentageLimit,
        easyCashInterestRate: easyCashInterestRate ?? this.easyCashInterestRate,
        easyCashAvailableLimit:
            easyCashAvailableLimit ?? this.easyCashAvailableLimit,
        easyCashLimit: easyCashLimit ?? this.easyCashLimit,
        creditCardLimit: creditCardLimit ?? this.creditCardLimit,
        totalLimit: totalLimit ?? this.totalLimit,
        dueDate: dueDate ?? this.dueDate,
        creditArrangement: creditArrangement ?? this.creditArrangement);
  }

  EasyCashLimitDetailsDTO copyWithWrapped(
      {Wrapped<double?>? easyCashPercentageLimit,
      Wrapped<double?>? easyCashInterestRate,
      Wrapped<Money?>? easyCashAvailableLimit,
      Wrapped<Money?>? easyCashLimit,
      Wrapped<Money?>? creditCardLimit,
      Wrapped<Money?>? totalLimit,
      Wrapped<DateTime?>? dueDate,
      Wrapped<CreditArrangement?>? creditArrangement}) {
    return EasyCashLimitDetailsDTO(
        easyCashPercentageLimit: (easyCashPercentageLimit != null
            ? easyCashPercentageLimit.value
            : this.easyCashPercentageLimit),
        easyCashInterestRate: (easyCashInterestRate != null
            ? easyCashInterestRate.value
            : this.easyCashInterestRate),
        easyCashAvailableLimit: (easyCashAvailableLimit != null
            ? easyCashAvailableLimit.value
            : this.easyCashAvailableLimit),
        easyCashLimit:
            (easyCashLimit != null ? easyCashLimit.value : this.easyCashLimit),
        creditCardLimit: (creditCardLimit != null
            ? creditCardLimit.value
            : this.creditCardLimit),
        totalLimit: (totalLimit != null ? totalLimit.value : this.totalLimit),
        dueDate: (dueDate != null ? dueDate.value : this.dueDate),
        creditArrangement: (creditArrangement != null
            ? creditArrangement.value
            : this.creditArrangement));
  }
}

@JsonSerializable(explicitToJson: true)
class Money {
  const Money({
    this.amount,
    this.currency,
  });

  factory Money.fromJson(Map<String, dynamic> json) => _$MoneyFromJson(json);

  static const toJsonFactory = _$MoneyToJson;
  Map<String, dynamic> toJson() => _$MoneyToJson(this);

  @JsonKey(name: 'amount', includeIfNull: false)
  final double? amount;
  @JsonKey(
    name: 'currency',
    includeIfNull: false,
    toJson: moneyCurrencyNullableToJson,
    fromJson: moneyCurrencyNullableFromJson,
  )
  final enums.MoneyCurrency? currency;
  static const fromJsonFactory = _$MoneyFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MoneyExtension on Money {
  Money copyWith({double? amount, enums.MoneyCurrency? currency}) {
    return Money(
        amount: amount ?? this.amount, currency: currency ?? this.currency);
  }

  Money copyWithWrapped(
      {Wrapped<double?>? amount, Wrapped<enums.MoneyCurrency?>? currency}) {
    return Money(
        amount: (amount != null ? amount.value : this.amount),
        currency: (currency != null ? currency.value : this.currency));
  }
}

@JsonSerializable(explicitToJson: true)
class CreditArrangement {
  const CreditArrangement({
    this.id,
    this.amount,
    this.availableCreditAmount,
    this.consumedCreditAmount,
    this.currency,
    this.accounts,
  });

  factory CreditArrangement.fromJson(Map<String, dynamic> json) =>
      _$CreditArrangementFromJson(json);

  static const toJsonFactory = _$CreditArrangementToJson;
  Map<String, dynamic> toJson() => _$CreditArrangementToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String? id;
  @JsonKey(name: 'amount', includeIfNull: false)
  final double? amount;
  @JsonKey(name: 'availableCreditAmount', includeIfNull: false)
  final double? availableCreditAmount;
  @JsonKey(name: 'consumedCreditAmount', includeIfNull: false)
  final double? consumedCreditAmount;
  @JsonKey(
    name: 'currency',
    includeIfNull: false,
    toJson: creditArrangementCurrencyNullableToJson,
    fromJson: creditArrangementCurrencyNullableFromJson,
  )
  final enums.CreditArrangementCurrency? currency;
  @JsonKey(
      name: 'accounts',
      includeIfNull: false,
      defaultValue: <LoanAccountDetails>[])
  final List<LoanAccountDetails>? accounts;
  static const fromJsonFactory = _$CreditArrangementFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CreditArrangementExtension on CreditArrangement {
  CreditArrangement copyWith(
      {String? id,
      double? amount,
      double? availableCreditAmount,
      double? consumedCreditAmount,
      enums.CreditArrangementCurrency? currency,
      List<LoanAccountDetails>? accounts}) {
    return CreditArrangement(
        id: id ?? this.id,
        amount: amount ?? this.amount,
        availableCreditAmount:
            availableCreditAmount ?? this.availableCreditAmount,
        consumedCreditAmount: consumedCreditAmount ?? this.consumedCreditAmount,
        currency: currency ?? this.currency,
        accounts: accounts ?? this.accounts);
  }

  CreditArrangement copyWithWrapped(
      {Wrapped<String?>? id,
      Wrapped<double?>? amount,
      Wrapped<double?>? availableCreditAmount,
      Wrapped<double?>? consumedCreditAmount,
      Wrapped<enums.CreditArrangementCurrency?>? currency,
      Wrapped<List<LoanAccountDetails>?>? accounts}) {
    return CreditArrangement(
        id: (id != null ? id.value : this.id),
        amount: (amount != null ? amount.value : this.amount),
        availableCreditAmount: (availableCreditAmount != null
            ? availableCreditAmount.value
            : this.availableCreditAmount),
        consumedCreditAmount: (consumedCreditAmount != null
            ? consumedCreditAmount.value
            : this.consumedCreditAmount),
        currency: (currency != null ? currency.value : this.currency),
        accounts: (accounts != null ? accounts.value : this.accounts));
  }
}

@JsonSerializable(explicitToJson: true)
class LoanAccountDetails {
  const LoanAccountDetails({
    this.id,
    this.name,
    this.creationDate,
    this.currency,
    this.notes,
    this.holderKey,
    this.branchKey,
    this.state,
    this.subState,
    this.productKey,
    this.productType,
    this.lendingProductSubType,
    this.rawMambuResponse,
    this.loanAmount,
    this.paymentSettings,
    this.paymentSchedule,
    this.interestSettings,
    this.firstRepaymentDate,
    this.type,
    this.encodedKey,
    this.accountHolderType,
    this.principleBalance,
    this.interestBalance,
    this.feeBalance,
    this.holdBalance,
    this.interestDue,
    this.minLimit,
    this.estimatedFee,
  });

  factory LoanAccountDetails.fromJson(Map<String, dynamic> json) =>
      _$LoanAccountDetailsFromJson(json);

  static const toJsonFactory = _$LoanAccountDetailsToJson;
  Map<String, dynamic> toJson() => _$LoanAccountDetailsToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String? id;
  @JsonKey(name: 'name', includeIfNull: false)
  final String? name;
  @JsonKey(name: 'creationDate', includeIfNull: false)
  final DateTime? creationDate;
  @JsonKey(
    name: 'currency',
    includeIfNull: false,
    toJson: loanAccountDetailsCurrencyNullableToJson,
    fromJson: loanAccountDetailsCurrencyNullableFromJson,
  )
  final enums.LoanAccountDetailsCurrency? currency;
  @JsonKey(name: 'notes', includeIfNull: false)
  final String? notes;
  @JsonKey(name: 'holderKey', includeIfNull: false)
  final String? holderKey;
  @JsonKey(name: 'branchKey', includeIfNull: false)
  final String? branchKey;
  @JsonKey(name: 'state', includeIfNull: false)
  final String? state;
  @JsonKey(name: 'subState', includeIfNull: false)
  final String? subState;
  @JsonKey(name: 'productKey', includeIfNull: false)
  final String? productKey;
  @JsonKey(
    name: 'productType',
    includeIfNull: false,
    toJson: loanAccountDetailsProductTypeNullableToJson,
    fromJson: loanAccountDetailsProductTypeNullableFromJson,
  )
  final enums.LoanAccountDetailsProductType? productType;
  @JsonKey(
    name: 'lendingProductSubType',
    includeIfNull: false,
    toJson: loanAccountDetailsLendingProductSubTypeNullableToJson,
    fromJson: loanAccountDetailsLendingProductSubTypeNullableFromJson,
  )
  final enums.LoanAccountDetailsLendingProductSubType? lendingProductSubType;
  @JsonKey(name: 'rawMambuResponse', includeIfNull: false)
  final Object? rawMambuResponse;
  @JsonKey(name: 'loanAmount', includeIfNull: false)
  final double? loanAmount;
  @JsonKey(name: 'paymentSettings', includeIfNull: false)
  final PaymentSettings? paymentSettings;
  @JsonKey(name: 'paymentSchedule', includeIfNull: false)
  final ScheduleSettings? paymentSchedule;
  @JsonKey(name: 'interestSettings', includeIfNull: false)
  final InterestSettings? interestSettings;
  @JsonKey(
      name: 'firstRepaymentDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime? firstRepaymentDate;
  @JsonKey(
    name: 'type',
    includeIfNull: false,
    toJson: loanAccountDetailsTypeNullableToJson,
    fromJson: loanAccountDetailsTypeNullableFromJson,
  )
  final enums.LoanAccountDetailsType? type;
  @JsonKey(name: 'encodedKey', includeIfNull: false)
  final String? encodedKey;
  @JsonKey(name: 'accountHolderType', includeIfNull: false)
  final String? accountHolderType;
  @JsonKey(name: 'principleBalance', includeIfNull: false)
  final double? principleBalance;
  @JsonKey(name: 'interestBalance', includeIfNull: false)
  final double? interestBalance;
  @JsonKey(name: 'feeBalance', includeIfNull: false)
  final double? feeBalance;
  @JsonKey(name: 'holdBalance', includeIfNull: false)
  final double? holdBalance;
  @JsonKey(name: 'interestDue', includeIfNull: false)
  final double? interestDue;
  @JsonKey(name: 'minLimit', includeIfNull: false)
  final double? minLimit;
  @JsonKey(name: 'estimatedFee', includeIfNull: false)
  final double? estimatedFee;
  static const fromJsonFactory = _$LoanAccountDetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $LoanAccountDetailsExtension on LoanAccountDetails {
  LoanAccountDetails copyWith(
      {String? id,
      String? name,
      DateTime? creationDate,
      enums.LoanAccountDetailsCurrency? currency,
      String? notes,
      String? holderKey,
      String? branchKey,
      String? state,
      String? subState,
      String? productKey,
      enums.LoanAccountDetailsProductType? productType,
      enums.LoanAccountDetailsLendingProductSubType? lendingProductSubType,
      Object? rawMambuResponse,
      double? loanAmount,
      PaymentSettings? paymentSettings,
      ScheduleSettings? paymentSchedule,
      InterestSettings? interestSettings,
      DateTime? firstRepaymentDate,
      enums.LoanAccountDetailsType? type,
      String? encodedKey,
      String? accountHolderType,
      double? principleBalance,
      double? interestBalance,
      double? feeBalance,
      double? holdBalance,
      double? interestDue,
      double? minLimit,
      double? estimatedFee}) {
    return LoanAccountDetails(
        id: id ?? this.id,
        name: name ?? this.name,
        creationDate: creationDate ?? this.creationDate,
        currency: currency ?? this.currency,
        notes: notes ?? this.notes,
        holderKey: holderKey ?? this.holderKey,
        branchKey: branchKey ?? this.branchKey,
        state: state ?? this.state,
        subState: subState ?? this.subState,
        productKey: productKey ?? this.productKey,
        productType: productType ?? this.productType,
        lendingProductSubType:
            lendingProductSubType ?? this.lendingProductSubType,
        rawMambuResponse: rawMambuResponse ?? this.rawMambuResponse,
        loanAmount: loanAmount ?? this.loanAmount,
        paymentSettings: paymentSettings ?? this.paymentSettings,
        paymentSchedule: paymentSchedule ?? this.paymentSchedule,
        interestSettings: interestSettings ?? this.interestSettings,
        firstRepaymentDate: firstRepaymentDate ?? this.firstRepaymentDate,
        type: type ?? this.type,
        encodedKey: encodedKey ?? this.encodedKey,
        accountHolderType: accountHolderType ?? this.accountHolderType,
        principleBalance: principleBalance ?? this.principleBalance,
        interestBalance: interestBalance ?? this.interestBalance,
        feeBalance: feeBalance ?? this.feeBalance,
        holdBalance: holdBalance ?? this.holdBalance,
        interestDue: interestDue ?? this.interestDue,
        minLimit: minLimit ?? this.minLimit,
        estimatedFee: estimatedFee ?? this.estimatedFee);
  }

  LoanAccountDetails copyWithWrapped(
      {Wrapped<String?>? id,
      Wrapped<String?>? name,
      Wrapped<DateTime?>? creationDate,
      Wrapped<enums.LoanAccountDetailsCurrency?>? currency,
      Wrapped<String?>? notes,
      Wrapped<String?>? holderKey,
      Wrapped<String?>? branchKey,
      Wrapped<String?>? state,
      Wrapped<String?>? subState,
      Wrapped<String?>? productKey,
      Wrapped<enums.LoanAccountDetailsProductType?>? productType,
      Wrapped<enums.LoanAccountDetailsLendingProductSubType?>?
          lendingProductSubType,
      Wrapped<Object?>? rawMambuResponse,
      Wrapped<double?>? loanAmount,
      Wrapped<PaymentSettings?>? paymentSettings,
      Wrapped<ScheduleSettings?>? paymentSchedule,
      Wrapped<InterestSettings?>? interestSettings,
      Wrapped<DateTime?>? firstRepaymentDate,
      Wrapped<enums.LoanAccountDetailsType?>? type,
      Wrapped<String?>? encodedKey,
      Wrapped<String?>? accountHolderType,
      Wrapped<double?>? principleBalance,
      Wrapped<double?>? interestBalance,
      Wrapped<double?>? feeBalance,
      Wrapped<double?>? holdBalance,
      Wrapped<double?>? interestDue,
      Wrapped<double?>? minLimit,
      Wrapped<double?>? estimatedFee}) {
    return LoanAccountDetails(
        id: (id != null ? id.value : this.id),
        name: (name != null ? name.value : this.name),
        creationDate:
            (creationDate != null ? creationDate.value : this.creationDate),
        currency: (currency != null ? currency.value : this.currency),
        notes: (notes != null ? notes.value : this.notes),
        holderKey: (holderKey != null ? holderKey.value : this.holderKey),
        branchKey: (branchKey != null ? branchKey.value : this.branchKey),
        state: (state != null ? state.value : this.state),
        subState: (subState != null ? subState.value : this.subState),
        productKey: (productKey != null ? productKey.value : this.productKey),
        productType:
            (productType != null ? productType.value : this.productType),
        lendingProductSubType: (lendingProductSubType != null
            ? lendingProductSubType.value
            : this.lendingProductSubType),
        rawMambuResponse: (rawMambuResponse != null
            ? rawMambuResponse.value
            : this.rawMambuResponse),
        loanAmount: (loanAmount != null ? loanAmount.value : this.loanAmount),
        paymentSettings: (paymentSettings != null
            ? paymentSettings.value
            : this.paymentSettings),
        paymentSchedule: (paymentSchedule != null
            ? paymentSchedule.value
            : this.paymentSchedule),
        interestSettings: (interestSettings != null
            ? interestSettings.value
            : this.interestSettings),
        firstRepaymentDate: (firstRepaymentDate != null
            ? firstRepaymentDate.value
            : this.firstRepaymentDate),
        type: (type != null ? type.value : this.type),
        encodedKey: (encodedKey != null ? encodedKey.value : this.encodedKey),
        accountHolderType: (accountHolderType != null
            ? accountHolderType.value
            : this.accountHolderType),
        principleBalance: (principleBalance != null
            ? principleBalance.value
            : this.principleBalance),
        interestBalance: (interestBalance != null
            ? interestBalance.value
            : this.interestBalance),
        feeBalance: (feeBalance != null ? feeBalance.value : this.feeBalance),
        holdBalance:
            (holdBalance != null ? holdBalance.value : this.holdBalance),
        interestDue:
            (interestDue != null ? interestDue.value : this.interestDue),
        minLimit: (minLimit != null ? minLimit.value : this.minLimit),
        estimatedFee:
            (estimatedFee != null ? estimatedFee.value : this.estimatedFee));
  }
}

String? accountDetailsCurrencyNullableToJson(
    enums.AccountDetailsCurrency? accountDetailsCurrency) {
  return accountDetailsCurrency?.value;
}

String? accountDetailsCurrencyToJson(
    enums.AccountDetailsCurrency accountDetailsCurrency) {
  return accountDetailsCurrency.value;
}

enums.AccountDetailsCurrency accountDetailsCurrencyFromJson(
  Object? accountDetailsCurrency, [
  enums.AccountDetailsCurrency? defaultValue,
]) {
  return enums.AccountDetailsCurrency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          accountDetailsCurrency?.toString().toLowerCase()) ??
      defaultValue ??
      enums.AccountDetailsCurrency.swaggerGeneratedUnknown;
}

enums.AccountDetailsCurrency? accountDetailsCurrencyNullableFromJson(
  Object? accountDetailsCurrency, [
  enums.AccountDetailsCurrency? defaultValue,
]) {
  if (accountDetailsCurrency == null) {
    return null;
  }
  return enums.AccountDetailsCurrency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          accountDetailsCurrency.toString().toLowerCase()) ??
      defaultValue;
}

String accountDetailsCurrencyExplodedListToJson(
    List<enums.AccountDetailsCurrency>? accountDetailsCurrency) {
  return accountDetailsCurrency?.map((e) => e.value!).join(',') ?? '';
}

List<String> accountDetailsCurrencyListToJson(
    List<enums.AccountDetailsCurrency>? accountDetailsCurrency) {
  if (accountDetailsCurrency == null) {
    return [];
  }

  return accountDetailsCurrency.map((e) => e.value!).toList();
}

List<enums.AccountDetailsCurrency> accountDetailsCurrencyListFromJson(
  List? accountDetailsCurrency, [
  List<enums.AccountDetailsCurrency>? defaultValue,
]) {
  if (accountDetailsCurrency == null) {
    return defaultValue ?? [];
  }

  return accountDetailsCurrency
      .map((e) => accountDetailsCurrencyFromJson(e.toString()))
      .toList();
}

List<enums.AccountDetailsCurrency>? accountDetailsCurrencyNullableListFromJson(
  List? accountDetailsCurrency, [
  List<enums.AccountDetailsCurrency>? defaultValue,
]) {
  if (accountDetailsCurrency == null) {
    return defaultValue;
  }

  return accountDetailsCurrency
      .map((e) => accountDetailsCurrencyFromJson(e.toString()))
      .toList();
}

String? accountDetailsTypeNullableToJson(
    enums.AccountDetailsType? accountDetailsType) {
  return accountDetailsType?.value;
}

String? accountDetailsTypeToJson(enums.AccountDetailsType accountDetailsType) {
  return accountDetailsType.value;
}

enums.AccountDetailsType accountDetailsTypeFromJson(
  Object? accountDetailsType, [
  enums.AccountDetailsType? defaultValue,
]) {
  return enums.AccountDetailsType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          accountDetailsType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.AccountDetailsType.swaggerGeneratedUnknown;
}

enums.AccountDetailsType? accountDetailsTypeNullableFromJson(
  Object? accountDetailsType, [
  enums.AccountDetailsType? defaultValue,
]) {
  if (accountDetailsType == null) {
    return null;
  }
  return enums.AccountDetailsType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          accountDetailsType.toString().toLowerCase()) ??
      defaultValue;
}

String accountDetailsTypeExplodedListToJson(
    List<enums.AccountDetailsType>? accountDetailsType) {
  return accountDetailsType?.map((e) => e.value!).join(',') ?? '';
}

List<String> accountDetailsTypeListToJson(
    List<enums.AccountDetailsType>? accountDetailsType) {
  if (accountDetailsType == null) {
    return [];
  }

  return accountDetailsType.map((e) => e.value!).toList();
}

List<enums.AccountDetailsType> accountDetailsTypeListFromJson(
  List? accountDetailsType, [
  List<enums.AccountDetailsType>? defaultValue,
]) {
  if (accountDetailsType == null) {
    return defaultValue ?? [];
  }

  return accountDetailsType
      .map((e) => accountDetailsTypeFromJson(e.toString()))
      .toList();
}

List<enums.AccountDetailsType>? accountDetailsTypeNullableListFromJson(
  List? accountDetailsType, [
  List<enums.AccountDetailsType>? defaultValue,
]) {
  if (accountDetailsType == null) {
    return defaultValue;
  }

  return accountDetailsType
      .map((e) => accountDetailsTypeFromJson(e.toString()))
      .toList();
}

String? loanAccountDetailsDTOCurrencyNullableToJson(
    enums.LoanAccountDetailsDTOCurrency? loanAccountDetailsDTOCurrency) {
  return loanAccountDetailsDTOCurrency?.value;
}

String? loanAccountDetailsDTOCurrencyToJson(
    enums.LoanAccountDetailsDTOCurrency loanAccountDetailsDTOCurrency) {
  return loanAccountDetailsDTOCurrency.value;
}

enums.LoanAccountDetailsDTOCurrency loanAccountDetailsDTOCurrencyFromJson(
  Object? loanAccountDetailsDTOCurrency, [
  enums.LoanAccountDetailsDTOCurrency? defaultValue,
]) {
  return enums.LoanAccountDetailsDTOCurrency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsDTOCurrency?.toString().toLowerCase()) ??
      defaultValue ??
      enums.LoanAccountDetailsDTOCurrency.swaggerGeneratedUnknown;
}

enums.LoanAccountDetailsDTOCurrency?
    loanAccountDetailsDTOCurrencyNullableFromJson(
  Object? loanAccountDetailsDTOCurrency, [
  enums.LoanAccountDetailsDTOCurrency? defaultValue,
]) {
  if (loanAccountDetailsDTOCurrency == null) {
    return null;
  }
  return enums.LoanAccountDetailsDTOCurrency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsDTOCurrency.toString().toLowerCase()) ??
      defaultValue;
}

String loanAccountDetailsDTOCurrencyExplodedListToJson(
    List<enums.LoanAccountDetailsDTOCurrency>? loanAccountDetailsDTOCurrency) {
  return loanAccountDetailsDTOCurrency?.map((e) => e.value!).join(',') ?? '';
}

List<String> loanAccountDetailsDTOCurrencyListToJson(
    List<enums.LoanAccountDetailsDTOCurrency>? loanAccountDetailsDTOCurrency) {
  if (loanAccountDetailsDTOCurrency == null) {
    return [];
  }

  return loanAccountDetailsDTOCurrency.map((e) => e.value!).toList();
}

List<enums.LoanAccountDetailsDTOCurrency>
    loanAccountDetailsDTOCurrencyListFromJson(
  List? loanAccountDetailsDTOCurrency, [
  List<enums.LoanAccountDetailsDTOCurrency>? defaultValue,
]) {
  if (loanAccountDetailsDTOCurrency == null) {
    return defaultValue ?? [];
  }

  return loanAccountDetailsDTOCurrency
      .map((e) => loanAccountDetailsDTOCurrencyFromJson(e.toString()))
      .toList();
}

List<enums.LoanAccountDetailsDTOCurrency>?
    loanAccountDetailsDTOCurrencyNullableListFromJson(
  List? loanAccountDetailsDTOCurrency, [
  List<enums.LoanAccountDetailsDTOCurrency>? defaultValue,
]) {
  if (loanAccountDetailsDTOCurrency == null) {
    return defaultValue;
  }

  return loanAccountDetailsDTOCurrency
      .map((e) => loanAccountDetailsDTOCurrencyFromJson(e.toString()))
      .toList();
}

String? loanAccountDetailsDTOProductTypeNullableToJson(
    enums.LoanAccountDetailsDTOProductType? loanAccountDetailsDTOProductType) {
  return loanAccountDetailsDTOProductType?.value;
}

String? loanAccountDetailsDTOProductTypeToJson(
    enums.LoanAccountDetailsDTOProductType loanAccountDetailsDTOProductType) {
  return loanAccountDetailsDTOProductType.value;
}

enums.LoanAccountDetailsDTOProductType loanAccountDetailsDTOProductTypeFromJson(
  Object? loanAccountDetailsDTOProductType, [
  enums.LoanAccountDetailsDTOProductType? defaultValue,
]) {
  return enums.LoanAccountDetailsDTOProductType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsDTOProductType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.LoanAccountDetailsDTOProductType.swaggerGeneratedUnknown;
}

enums.LoanAccountDetailsDTOProductType?
    loanAccountDetailsDTOProductTypeNullableFromJson(
  Object? loanAccountDetailsDTOProductType, [
  enums.LoanAccountDetailsDTOProductType? defaultValue,
]) {
  if (loanAccountDetailsDTOProductType == null) {
    return null;
  }
  return enums.LoanAccountDetailsDTOProductType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsDTOProductType.toString().toLowerCase()) ??
      defaultValue;
}

String loanAccountDetailsDTOProductTypeExplodedListToJson(
    List<enums.LoanAccountDetailsDTOProductType>?
        loanAccountDetailsDTOProductType) {
  return loanAccountDetailsDTOProductType?.map((e) => e.value!).join(',') ?? '';
}

List<String> loanAccountDetailsDTOProductTypeListToJson(
    List<enums.LoanAccountDetailsDTOProductType>?
        loanAccountDetailsDTOProductType) {
  if (loanAccountDetailsDTOProductType == null) {
    return [];
  }

  return loanAccountDetailsDTOProductType.map((e) => e.value!).toList();
}

List<enums.LoanAccountDetailsDTOProductType>
    loanAccountDetailsDTOProductTypeListFromJson(
  List? loanAccountDetailsDTOProductType, [
  List<enums.LoanAccountDetailsDTOProductType>? defaultValue,
]) {
  if (loanAccountDetailsDTOProductType == null) {
    return defaultValue ?? [];
  }

  return loanAccountDetailsDTOProductType
      .map((e) => loanAccountDetailsDTOProductTypeFromJson(e.toString()))
      .toList();
}

List<enums.LoanAccountDetailsDTOProductType>?
    loanAccountDetailsDTOProductTypeNullableListFromJson(
  List? loanAccountDetailsDTOProductType, [
  List<enums.LoanAccountDetailsDTOProductType>? defaultValue,
]) {
  if (loanAccountDetailsDTOProductType == null) {
    return defaultValue;
  }

  return loanAccountDetailsDTOProductType
      .map((e) => loanAccountDetailsDTOProductTypeFromJson(e.toString()))
      .toList();
}

String? loanAccountDetailsDTOLendingProductSubTypeNullableToJson(
    enums.LoanAccountDetailsDTOLendingProductSubType?
        loanAccountDetailsDTOLendingProductSubType) {
  return loanAccountDetailsDTOLendingProductSubType?.value;
}

String? loanAccountDetailsDTOLendingProductSubTypeToJson(
    enums.LoanAccountDetailsDTOLendingProductSubType
        loanAccountDetailsDTOLendingProductSubType) {
  return loanAccountDetailsDTOLendingProductSubType.value;
}

enums.LoanAccountDetailsDTOLendingProductSubType
    loanAccountDetailsDTOLendingProductSubTypeFromJson(
  Object? loanAccountDetailsDTOLendingProductSubType, [
  enums.LoanAccountDetailsDTOLendingProductSubType? defaultValue,
]) {
  return enums.LoanAccountDetailsDTOLendingProductSubType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              loanAccountDetailsDTOLendingProductSubType
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.LoanAccountDetailsDTOLendingProductSubType.swaggerGeneratedUnknown;
}

enums.LoanAccountDetailsDTOLendingProductSubType?
    loanAccountDetailsDTOLendingProductSubTypeNullableFromJson(
  Object? loanAccountDetailsDTOLendingProductSubType, [
  enums.LoanAccountDetailsDTOLendingProductSubType? defaultValue,
]) {
  if (loanAccountDetailsDTOLendingProductSubType == null) {
    return null;
  }
  return enums.LoanAccountDetailsDTOLendingProductSubType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              loanAccountDetailsDTOLendingProductSubType
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String loanAccountDetailsDTOLendingProductSubTypeExplodedListToJson(
    List<enums.LoanAccountDetailsDTOLendingProductSubType>?
        loanAccountDetailsDTOLendingProductSubType) {
  return loanAccountDetailsDTOLendingProductSubType
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> loanAccountDetailsDTOLendingProductSubTypeListToJson(
    List<enums.LoanAccountDetailsDTOLendingProductSubType>?
        loanAccountDetailsDTOLendingProductSubType) {
  if (loanAccountDetailsDTOLendingProductSubType == null) {
    return [];
  }

  return loanAccountDetailsDTOLendingProductSubType
      .map((e) => e.value!)
      .toList();
}

List<enums.LoanAccountDetailsDTOLendingProductSubType>
    loanAccountDetailsDTOLendingProductSubTypeListFromJson(
  List? loanAccountDetailsDTOLendingProductSubType, [
  List<enums.LoanAccountDetailsDTOLendingProductSubType>? defaultValue,
]) {
  if (loanAccountDetailsDTOLendingProductSubType == null) {
    return defaultValue ?? [];
  }

  return loanAccountDetailsDTOLendingProductSubType
      .map((e) =>
          loanAccountDetailsDTOLendingProductSubTypeFromJson(e.toString()))
      .toList();
}

List<enums.LoanAccountDetailsDTOLendingProductSubType>?
    loanAccountDetailsDTOLendingProductSubTypeNullableListFromJson(
  List? loanAccountDetailsDTOLendingProductSubType, [
  List<enums.LoanAccountDetailsDTOLendingProductSubType>? defaultValue,
]) {
  if (loanAccountDetailsDTOLendingProductSubType == null) {
    return defaultValue;
  }

  return loanAccountDetailsDTOLendingProductSubType
      .map((e) =>
          loanAccountDetailsDTOLendingProductSubTypeFromJson(e.toString()))
      .toList();
}

String? loanAccountDetailsDTOTypeNullableToJson(
    enums.LoanAccountDetailsDTOType? loanAccountDetailsDTOType) {
  return loanAccountDetailsDTOType?.value;
}

String? loanAccountDetailsDTOTypeToJson(
    enums.LoanAccountDetailsDTOType loanAccountDetailsDTOType) {
  return loanAccountDetailsDTOType.value;
}

enums.LoanAccountDetailsDTOType loanAccountDetailsDTOTypeFromJson(
  Object? loanAccountDetailsDTOType, [
  enums.LoanAccountDetailsDTOType? defaultValue,
]) {
  return enums.LoanAccountDetailsDTOType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsDTOType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.LoanAccountDetailsDTOType.swaggerGeneratedUnknown;
}

enums.LoanAccountDetailsDTOType? loanAccountDetailsDTOTypeNullableFromJson(
  Object? loanAccountDetailsDTOType, [
  enums.LoanAccountDetailsDTOType? defaultValue,
]) {
  if (loanAccountDetailsDTOType == null) {
    return null;
  }
  return enums.LoanAccountDetailsDTOType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsDTOType.toString().toLowerCase()) ??
      defaultValue;
}

String loanAccountDetailsDTOTypeExplodedListToJson(
    List<enums.LoanAccountDetailsDTOType>? loanAccountDetailsDTOType) {
  return loanAccountDetailsDTOType?.map((e) => e.value!).join(',') ?? '';
}

List<String> loanAccountDetailsDTOTypeListToJson(
    List<enums.LoanAccountDetailsDTOType>? loanAccountDetailsDTOType) {
  if (loanAccountDetailsDTOType == null) {
    return [];
  }

  return loanAccountDetailsDTOType.map((e) => e.value!).toList();
}

List<enums.LoanAccountDetailsDTOType> loanAccountDetailsDTOTypeListFromJson(
  List? loanAccountDetailsDTOType, [
  List<enums.LoanAccountDetailsDTOType>? defaultValue,
]) {
  if (loanAccountDetailsDTOType == null) {
    return defaultValue ?? [];
  }

  return loanAccountDetailsDTOType
      .map((e) => loanAccountDetailsDTOTypeFromJson(e.toString()))
      .toList();
}

List<enums.LoanAccountDetailsDTOType>?
    loanAccountDetailsDTOTypeNullableListFromJson(
  List? loanAccountDetailsDTOType, [
  List<enums.LoanAccountDetailsDTOType>? defaultValue,
]) {
  if (loanAccountDetailsDTOType == null) {
    return defaultValue;
  }

  return loanAccountDetailsDTOType
      .map((e) => loanAccountDetailsDTOTypeFromJson(e.toString()))
      .toList();
}

String? scheduleSettingsRepaymentPeriodUnitNullableToJson(
    enums.ScheduleSettingsRepaymentPeriodUnit?
        scheduleSettingsRepaymentPeriodUnit) {
  return scheduleSettingsRepaymentPeriodUnit?.value;
}

String? scheduleSettingsRepaymentPeriodUnitToJson(
    enums.ScheduleSettingsRepaymentPeriodUnit
        scheduleSettingsRepaymentPeriodUnit) {
  return scheduleSettingsRepaymentPeriodUnit.value;
}

enums.ScheduleSettingsRepaymentPeriodUnit
    scheduleSettingsRepaymentPeriodUnitFromJson(
  Object? scheduleSettingsRepaymentPeriodUnit, [
  enums.ScheduleSettingsRepaymentPeriodUnit? defaultValue,
]) {
  return enums.ScheduleSettingsRepaymentPeriodUnit.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              scheduleSettingsRepaymentPeriodUnit?.toString().toLowerCase()) ??
      defaultValue ??
      enums.ScheduleSettingsRepaymentPeriodUnit.swaggerGeneratedUnknown;
}

enums.ScheduleSettingsRepaymentPeriodUnit?
    scheduleSettingsRepaymentPeriodUnitNullableFromJson(
  Object? scheduleSettingsRepaymentPeriodUnit, [
  enums.ScheduleSettingsRepaymentPeriodUnit? defaultValue,
]) {
  if (scheduleSettingsRepaymentPeriodUnit == null) {
    return null;
  }
  return enums.ScheduleSettingsRepaymentPeriodUnit.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              scheduleSettingsRepaymentPeriodUnit.toString().toLowerCase()) ??
      defaultValue;
}

String scheduleSettingsRepaymentPeriodUnitExplodedListToJson(
    List<enums.ScheduleSettingsRepaymentPeriodUnit>?
        scheduleSettingsRepaymentPeriodUnit) {
  return scheduleSettingsRepaymentPeriodUnit?.map((e) => e.value!).join(',') ??
      '';
}

List<String> scheduleSettingsRepaymentPeriodUnitListToJson(
    List<enums.ScheduleSettingsRepaymentPeriodUnit>?
        scheduleSettingsRepaymentPeriodUnit) {
  if (scheduleSettingsRepaymentPeriodUnit == null) {
    return [];
  }

  return scheduleSettingsRepaymentPeriodUnit.map((e) => e.value!).toList();
}

List<enums.ScheduleSettingsRepaymentPeriodUnit>
    scheduleSettingsRepaymentPeriodUnitListFromJson(
  List? scheduleSettingsRepaymentPeriodUnit, [
  List<enums.ScheduleSettingsRepaymentPeriodUnit>? defaultValue,
]) {
  if (scheduleSettingsRepaymentPeriodUnit == null) {
    return defaultValue ?? [];
  }

  return scheduleSettingsRepaymentPeriodUnit
      .map((e) => scheduleSettingsRepaymentPeriodUnitFromJson(e.toString()))
      .toList();
}

List<enums.ScheduleSettingsRepaymentPeriodUnit>?
    scheduleSettingsRepaymentPeriodUnitNullableListFromJson(
  List? scheduleSettingsRepaymentPeriodUnit, [
  List<enums.ScheduleSettingsRepaymentPeriodUnit>? defaultValue,
]) {
  if (scheduleSettingsRepaymentPeriodUnit == null) {
    return defaultValue;
  }

  return scheduleSettingsRepaymentPeriodUnit
      .map((e) => scheduleSettingsRepaymentPeriodUnitFromJson(e.toString()))
      .toList();
}

String? interestSettingsInterestRateSourceNullableToJson(
    enums.InterestSettingsInterestRateSource?
        interestSettingsInterestRateSource) {
  return interestSettingsInterestRateSource?.value;
}

String? interestSettingsInterestRateSourceToJson(
    enums.InterestSettingsInterestRateSource
        interestSettingsInterestRateSource) {
  return interestSettingsInterestRateSource.value;
}

enums.InterestSettingsInterestRateSource
    interestSettingsInterestRateSourceFromJson(
  Object? interestSettingsInterestRateSource, [
  enums.InterestSettingsInterestRateSource? defaultValue,
]) {
  return enums.InterestSettingsInterestRateSource.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          interestSettingsInterestRateSource?.toString().toLowerCase()) ??
      defaultValue ??
      enums.InterestSettingsInterestRateSource.swaggerGeneratedUnknown;
}

enums.InterestSettingsInterestRateSource?
    interestSettingsInterestRateSourceNullableFromJson(
  Object? interestSettingsInterestRateSource, [
  enums.InterestSettingsInterestRateSource? defaultValue,
]) {
  if (interestSettingsInterestRateSource == null) {
    return null;
  }
  return enums.InterestSettingsInterestRateSource.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          interestSettingsInterestRateSource.toString().toLowerCase()) ??
      defaultValue;
}

String interestSettingsInterestRateSourceExplodedListToJson(
    List<enums.InterestSettingsInterestRateSource>?
        interestSettingsInterestRateSource) {
  return interestSettingsInterestRateSource?.map((e) => e.value!).join(',') ??
      '';
}

List<String> interestSettingsInterestRateSourceListToJson(
    List<enums.InterestSettingsInterestRateSource>?
        interestSettingsInterestRateSource) {
  if (interestSettingsInterestRateSource == null) {
    return [];
  }

  return interestSettingsInterestRateSource.map((e) => e.value!).toList();
}

List<enums.InterestSettingsInterestRateSource>
    interestSettingsInterestRateSourceListFromJson(
  List? interestSettingsInterestRateSource, [
  List<enums.InterestSettingsInterestRateSource>? defaultValue,
]) {
  if (interestSettingsInterestRateSource == null) {
    return defaultValue ?? [];
  }

  return interestSettingsInterestRateSource
      .map((e) => interestSettingsInterestRateSourceFromJson(e.toString()))
      .toList();
}

List<enums.InterestSettingsInterestRateSource>?
    interestSettingsInterestRateSourceNullableListFromJson(
  List? interestSettingsInterestRateSource, [
  List<enums.InterestSettingsInterestRateSource>? defaultValue,
]) {
  if (interestSettingsInterestRateSource == null) {
    return defaultValue;
  }

  return interestSettingsInterestRateSource
      .map((e) => interestSettingsInterestRateSourceFromJson(e.toString()))
      .toList();
}

String? interestSettingsInterestTypeNullableToJson(
    enums.InterestSettingsInterestType? interestSettingsInterestType) {
  return interestSettingsInterestType?.value;
}

String? interestSettingsInterestTypeToJson(
    enums.InterestSettingsInterestType interestSettingsInterestType) {
  return interestSettingsInterestType.value;
}

enums.InterestSettingsInterestType interestSettingsInterestTypeFromJson(
  Object? interestSettingsInterestType, [
  enums.InterestSettingsInterestType? defaultValue,
]) {
  return enums.InterestSettingsInterestType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          interestSettingsInterestType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.InterestSettingsInterestType.swaggerGeneratedUnknown;
}

enums.InterestSettingsInterestType?
    interestSettingsInterestTypeNullableFromJson(
  Object? interestSettingsInterestType, [
  enums.InterestSettingsInterestType? defaultValue,
]) {
  if (interestSettingsInterestType == null) {
    return null;
  }
  return enums.InterestSettingsInterestType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          interestSettingsInterestType.toString().toLowerCase()) ??
      defaultValue;
}

String interestSettingsInterestTypeExplodedListToJson(
    List<enums.InterestSettingsInterestType>? interestSettingsInterestType) {
  return interestSettingsInterestType?.map((e) => e.value!).join(',') ?? '';
}

List<String> interestSettingsInterestTypeListToJson(
    List<enums.InterestSettingsInterestType>? interestSettingsInterestType) {
  if (interestSettingsInterestType == null) {
    return [];
  }

  return interestSettingsInterestType.map((e) => e.value!).toList();
}

List<enums.InterestSettingsInterestType>
    interestSettingsInterestTypeListFromJson(
  List? interestSettingsInterestType, [
  List<enums.InterestSettingsInterestType>? defaultValue,
]) {
  if (interestSettingsInterestType == null) {
    return defaultValue ?? [];
  }

  return interestSettingsInterestType
      .map((e) => interestSettingsInterestTypeFromJson(e.toString()))
      .toList();
}

List<enums.InterestSettingsInterestType>?
    interestSettingsInterestTypeNullableListFromJson(
  List? interestSettingsInterestType, [
  List<enums.InterestSettingsInterestType>? defaultValue,
]) {
  if (interestSettingsInterestType == null) {
    return defaultValue;
  }

  return interestSettingsInterestType
      .map((e) => interestSettingsInterestTypeFromJson(e.toString()))
      .toList();
}

String? intervalUnitNullableToJson(enums.IntervalUnit? intervalUnit) {
  return intervalUnit?.value;
}

String? intervalUnitToJson(enums.IntervalUnit intervalUnit) {
  return intervalUnit.value;
}

enums.IntervalUnit intervalUnitFromJson(
  Object? intervalUnit, [
  enums.IntervalUnit? defaultValue,
]) {
  return enums.IntervalUnit.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          intervalUnit?.toString().toLowerCase()) ??
      defaultValue ??
      enums.IntervalUnit.swaggerGeneratedUnknown;
}

enums.IntervalUnit? intervalUnitNullableFromJson(
  Object? intervalUnit, [
  enums.IntervalUnit? defaultValue,
]) {
  if (intervalUnit == null) {
    return null;
  }
  return enums.IntervalUnit.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          intervalUnit.toString().toLowerCase()) ??
      defaultValue;
}

String intervalUnitExplodedListToJson(List<enums.IntervalUnit>? intervalUnit) {
  return intervalUnit?.map((e) => e.value!).join(',') ?? '';
}

List<String> intervalUnitListToJson(List<enums.IntervalUnit>? intervalUnit) {
  if (intervalUnit == null) {
    return [];
  }

  return intervalUnit.map((e) => e.value!).toList();
}

List<enums.IntervalUnit> intervalUnitListFromJson(
  List? intervalUnit, [
  List<enums.IntervalUnit>? defaultValue,
]) {
  if (intervalUnit == null) {
    return defaultValue ?? [];
  }

  return intervalUnit.map((e) => intervalUnitFromJson(e.toString())).toList();
}

List<enums.IntervalUnit>? intervalUnitNullableListFromJson(
  List? intervalUnit, [
  List<enums.IntervalUnit>? defaultValue,
]) {
  if (intervalUnit == null) {
    return defaultValue;
  }

  return intervalUnit.map((e) => intervalUnitFromJson(e.toString())).toList();
}

String? moneyCurrencyNullableToJson(enums.MoneyCurrency? moneyCurrency) {
  return moneyCurrency?.value;
}

String? moneyCurrencyToJson(enums.MoneyCurrency moneyCurrency) {
  return moneyCurrency.value;
}

enums.MoneyCurrency moneyCurrencyFromJson(
  Object? moneyCurrency, [
  enums.MoneyCurrency? defaultValue,
]) {
  return enums.MoneyCurrency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          moneyCurrency?.toString().toLowerCase()) ??
      defaultValue ??
      enums.MoneyCurrency.swaggerGeneratedUnknown;
}

enums.MoneyCurrency? moneyCurrencyNullableFromJson(
  Object? moneyCurrency, [
  enums.MoneyCurrency? defaultValue,
]) {
  if (moneyCurrency == null) {
    return null;
  }
  return enums.MoneyCurrency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          moneyCurrency.toString().toLowerCase()) ??
      defaultValue;
}

String moneyCurrencyExplodedListToJson(
    List<enums.MoneyCurrency>? moneyCurrency) {
  return moneyCurrency?.map((e) => e.value!).join(',') ?? '';
}

List<String> moneyCurrencyListToJson(List<enums.MoneyCurrency>? moneyCurrency) {
  if (moneyCurrency == null) {
    return [];
  }

  return moneyCurrency.map((e) => e.value!).toList();
}

List<enums.MoneyCurrency> moneyCurrencyListFromJson(
  List? moneyCurrency, [
  List<enums.MoneyCurrency>? defaultValue,
]) {
  if (moneyCurrency == null) {
    return defaultValue ?? [];
  }

  return moneyCurrency.map((e) => moneyCurrencyFromJson(e.toString())).toList();
}

List<enums.MoneyCurrency>? moneyCurrencyNullableListFromJson(
  List? moneyCurrency, [
  List<enums.MoneyCurrency>? defaultValue,
]) {
  if (moneyCurrency == null) {
    return defaultValue;
  }

  return moneyCurrency.map((e) => moneyCurrencyFromJson(e.toString())).toList();
}

String? creditArrangementCurrencyNullableToJson(
    enums.CreditArrangementCurrency? creditArrangementCurrency) {
  return creditArrangementCurrency?.value;
}

String? creditArrangementCurrencyToJson(
    enums.CreditArrangementCurrency creditArrangementCurrency) {
  return creditArrangementCurrency.value;
}

enums.CreditArrangementCurrency creditArrangementCurrencyFromJson(
  Object? creditArrangementCurrency, [
  enums.CreditArrangementCurrency? defaultValue,
]) {
  return enums.CreditArrangementCurrency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          creditArrangementCurrency?.toString().toLowerCase()) ??
      defaultValue ??
      enums.CreditArrangementCurrency.swaggerGeneratedUnknown;
}

enums.CreditArrangementCurrency? creditArrangementCurrencyNullableFromJson(
  Object? creditArrangementCurrency, [
  enums.CreditArrangementCurrency? defaultValue,
]) {
  if (creditArrangementCurrency == null) {
    return null;
  }
  return enums.CreditArrangementCurrency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          creditArrangementCurrency.toString().toLowerCase()) ??
      defaultValue;
}

String creditArrangementCurrencyExplodedListToJson(
    List<enums.CreditArrangementCurrency>? creditArrangementCurrency) {
  return creditArrangementCurrency?.map((e) => e.value!).join(',') ?? '';
}

List<String> creditArrangementCurrencyListToJson(
    List<enums.CreditArrangementCurrency>? creditArrangementCurrency) {
  if (creditArrangementCurrency == null) {
    return [];
  }

  return creditArrangementCurrency.map((e) => e.value!).toList();
}

List<enums.CreditArrangementCurrency> creditArrangementCurrencyListFromJson(
  List? creditArrangementCurrency, [
  List<enums.CreditArrangementCurrency>? defaultValue,
]) {
  if (creditArrangementCurrency == null) {
    return defaultValue ?? [];
  }

  return creditArrangementCurrency
      .map((e) => creditArrangementCurrencyFromJson(e.toString()))
      .toList();
}

List<enums.CreditArrangementCurrency>?
    creditArrangementCurrencyNullableListFromJson(
  List? creditArrangementCurrency, [
  List<enums.CreditArrangementCurrency>? defaultValue,
]) {
  if (creditArrangementCurrency == null) {
    return defaultValue;
  }

  return creditArrangementCurrency
      .map((e) => creditArrangementCurrencyFromJson(e.toString()))
      .toList();
}

String? securityTypeNullableToJson(enums.SecurityType? securityType) {
  return securityType?.value;
}

String? securityTypeToJson(enums.SecurityType securityType) {
  return securityType.value;
}

enums.SecurityType securityTypeFromJson(
  Object? securityType, [
  enums.SecurityType? defaultValue,
]) {
  return enums.SecurityType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          securityType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.SecurityType.swaggerGeneratedUnknown;
}

enums.SecurityType? securityTypeNullableFromJson(
  Object? securityType, [
  enums.SecurityType? defaultValue,
]) {
  if (securityType == null) {
    return null;
  }
  return enums.SecurityType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          securityType.toString().toLowerCase()) ??
      defaultValue;
}

String securityTypeExplodedListToJson(List<enums.SecurityType>? securityType) {
  return securityType?.map((e) => e.value!).join(',') ?? '';
}

List<String> securityTypeListToJson(List<enums.SecurityType>? securityType) {
  if (securityType == null) {
    return [];
  }

  return securityType.map((e) => e.value!).toList();
}

List<enums.SecurityType> securityTypeListFromJson(
  List? securityType, [
  List<enums.SecurityType>? defaultValue,
]) {
  if (securityType == null) {
    return defaultValue ?? [];
  }

  return securityType.map((e) => securityTypeFromJson(e.toString())).toList();
}

List<enums.SecurityType>? securityTypeNullableListFromJson(
  List? securityType, [
  List<enums.SecurityType>? defaultValue,
]) {
  if (securityType == null) {
    return defaultValue;
  }

  return securityType.map((e) => securityTypeFromJson(e.toString())).toList();
}

String? loanAccountDetailsCurrencyNullableToJson(
    enums.LoanAccountDetailsCurrency? loanAccountDetailsCurrency) {
  return loanAccountDetailsCurrency?.value;
}

String? loanAccountDetailsCurrencyToJson(
    enums.LoanAccountDetailsCurrency loanAccountDetailsCurrency) {
  return loanAccountDetailsCurrency.value;
}

enums.LoanAccountDetailsCurrency loanAccountDetailsCurrencyFromJson(
  Object? loanAccountDetailsCurrency, [
  enums.LoanAccountDetailsCurrency? defaultValue,
]) {
  return enums.LoanAccountDetailsCurrency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsCurrency?.toString().toLowerCase()) ??
      defaultValue ??
      enums.LoanAccountDetailsCurrency.swaggerGeneratedUnknown;
}

enums.LoanAccountDetailsCurrency? loanAccountDetailsCurrencyNullableFromJson(
  Object? loanAccountDetailsCurrency, [
  enums.LoanAccountDetailsCurrency? defaultValue,
]) {
  if (loanAccountDetailsCurrency == null) {
    return null;
  }
  return enums.LoanAccountDetailsCurrency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsCurrency.toString().toLowerCase()) ??
      defaultValue;
}

String loanAccountDetailsCurrencyExplodedListToJson(
    List<enums.LoanAccountDetailsCurrency>? loanAccountDetailsCurrency) {
  return loanAccountDetailsCurrency?.map((e) => e.value!).join(',') ?? '';
}

List<String> loanAccountDetailsCurrencyListToJson(
    List<enums.LoanAccountDetailsCurrency>? loanAccountDetailsCurrency) {
  if (loanAccountDetailsCurrency == null) {
    return [];
  }

  return loanAccountDetailsCurrency.map((e) => e.value!).toList();
}

List<enums.LoanAccountDetailsCurrency> loanAccountDetailsCurrencyListFromJson(
  List? loanAccountDetailsCurrency, [
  List<enums.LoanAccountDetailsCurrency>? defaultValue,
]) {
  if (loanAccountDetailsCurrency == null) {
    return defaultValue ?? [];
  }

  return loanAccountDetailsCurrency
      .map((e) => loanAccountDetailsCurrencyFromJson(e.toString()))
      .toList();
}

List<enums.LoanAccountDetailsCurrency>?
    loanAccountDetailsCurrencyNullableListFromJson(
  List? loanAccountDetailsCurrency, [
  List<enums.LoanAccountDetailsCurrency>? defaultValue,
]) {
  if (loanAccountDetailsCurrency == null) {
    return defaultValue;
  }

  return loanAccountDetailsCurrency
      .map((e) => loanAccountDetailsCurrencyFromJson(e.toString()))
      .toList();
}

String? loanAccountDetailsProductTypeNullableToJson(
    enums.LoanAccountDetailsProductType? loanAccountDetailsProductType) {
  return loanAccountDetailsProductType?.value;
}

String? loanAccountDetailsProductTypeToJson(
    enums.LoanAccountDetailsProductType loanAccountDetailsProductType) {
  return loanAccountDetailsProductType.value;
}

enums.LoanAccountDetailsProductType loanAccountDetailsProductTypeFromJson(
  Object? loanAccountDetailsProductType, [
  enums.LoanAccountDetailsProductType? defaultValue,
]) {
  return enums.LoanAccountDetailsProductType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsProductType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.LoanAccountDetailsProductType.swaggerGeneratedUnknown;
}

enums.LoanAccountDetailsProductType?
    loanAccountDetailsProductTypeNullableFromJson(
  Object? loanAccountDetailsProductType, [
  enums.LoanAccountDetailsProductType? defaultValue,
]) {
  if (loanAccountDetailsProductType == null) {
    return null;
  }
  return enums.LoanAccountDetailsProductType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsProductType.toString().toLowerCase()) ??
      defaultValue;
}

String loanAccountDetailsProductTypeExplodedListToJson(
    List<enums.LoanAccountDetailsProductType>? loanAccountDetailsProductType) {
  return loanAccountDetailsProductType?.map((e) => e.value!).join(',') ?? '';
}

List<String> loanAccountDetailsProductTypeListToJson(
    List<enums.LoanAccountDetailsProductType>? loanAccountDetailsProductType) {
  if (loanAccountDetailsProductType == null) {
    return [];
  }

  return loanAccountDetailsProductType.map((e) => e.value!).toList();
}

List<enums.LoanAccountDetailsProductType>
    loanAccountDetailsProductTypeListFromJson(
  List? loanAccountDetailsProductType, [
  List<enums.LoanAccountDetailsProductType>? defaultValue,
]) {
  if (loanAccountDetailsProductType == null) {
    return defaultValue ?? [];
  }

  return loanAccountDetailsProductType
      .map((e) => loanAccountDetailsProductTypeFromJson(e.toString()))
      .toList();
}

List<enums.LoanAccountDetailsProductType>?
    loanAccountDetailsProductTypeNullableListFromJson(
  List? loanAccountDetailsProductType, [
  List<enums.LoanAccountDetailsProductType>? defaultValue,
]) {
  if (loanAccountDetailsProductType == null) {
    return defaultValue;
  }

  return loanAccountDetailsProductType
      .map((e) => loanAccountDetailsProductTypeFromJson(e.toString()))
      .toList();
}

String? loanAccountDetailsLendingProductSubTypeNullableToJson(
    enums.LoanAccountDetailsLendingProductSubType?
        loanAccountDetailsLendingProductSubType) {
  return loanAccountDetailsLendingProductSubType?.value;
}

String? loanAccountDetailsLendingProductSubTypeToJson(
    enums.LoanAccountDetailsLendingProductSubType
        loanAccountDetailsLendingProductSubType) {
  return loanAccountDetailsLendingProductSubType.value;
}

enums.LoanAccountDetailsLendingProductSubType
    loanAccountDetailsLendingProductSubTypeFromJson(
  Object? loanAccountDetailsLendingProductSubType, [
  enums.LoanAccountDetailsLendingProductSubType? defaultValue,
]) {
  return enums.LoanAccountDetailsLendingProductSubType.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              loanAccountDetailsLendingProductSubType
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.LoanAccountDetailsLendingProductSubType.swaggerGeneratedUnknown;
}

enums.LoanAccountDetailsLendingProductSubType?
    loanAccountDetailsLendingProductSubTypeNullableFromJson(
  Object? loanAccountDetailsLendingProductSubType, [
  enums.LoanAccountDetailsLendingProductSubType? defaultValue,
]) {
  if (loanAccountDetailsLendingProductSubType == null) {
    return null;
  }
  return enums.LoanAccountDetailsLendingProductSubType.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              loanAccountDetailsLendingProductSubType
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String loanAccountDetailsLendingProductSubTypeExplodedListToJson(
    List<enums.LoanAccountDetailsLendingProductSubType>?
        loanAccountDetailsLendingProductSubType) {
  return loanAccountDetailsLendingProductSubType
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> loanAccountDetailsLendingProductSubTypeListToJson(
    List<enums.LoanAccountDetailsLendingProductSubType>?
        loanAccountDetailsLendingProductSubType) {
  if (loanAccountDetailsLendingProductSubType == null) {
    return [];
  }

  return loanAccountDetailsLendingProductSubType.map((e) => e.value!).toList();
}

List<enums.LoanAccountDetailsLendingProductSubType>
    loanAccountDetailsLendingProductSubTypeListFromJson(
  List? loanAccountDetailsLendingProductSubType, [
  List<enums.LoanAccountDetailsLendingProductSubType>? defaultValue,
]) {
  if (loanAccountDetailsLendingProductSubType == null) {
    return defaultValue ?? [];
  }

  return loanAccountDetailsLendingProductSubType
      .map((e) => loanAccountDetailsLendingProductSubTypeFromJson(e.toString()))
      .toList();
}

List<enums.LoanAccountDetailsLendingProductSubType>?
    loanAccountDetailsLendingProductSubTypeNullableListFromJson(
  List? loanAccountDetailsLendingProductSubType, [
  List<enums.LoanAccountDetailsLendingProductSubType>? defaultValue,
]) {
  if (loanAccountDetailsLendingProductSubType == null) {
    return defaultValue;
  }

  return loanAccountDetailsLendingProductSubType
      .map((e) => loanAccountDetailsLendingProductSubTypeFromJson(e.toString()))
      .toList();
}

String? loanAccountDetailsTypeNullableToJson(
    enums.LoanAccountDetailsType? loanAccountDetailsType) {
  return loanAccountDetailsType?.value;
}

String? loanAccountDetailsTypeToJson(
    enums.LoanAccountDetailsType loanAccountDetailsType) {
  return loanAccountDetailsType.value;
}

enums.LoanAccountDetailsType loanAccountDetailsTypeFromJson(
  Object? loanAccountDetailsType, [
  enums.LoanAccountDetailsType? defaultValue,
]) {
  return enums.LoanAccountDetailsType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.LoanAccountDetailsType.swaggerGeneratedUnknown;
}

enums.LoanAccountDetailsType? loanAccountDetailsTypeNullableFromJson(
  Object? loanAccountDetailsType, [
  enums.LoanAccountDetailsType? defaultValue,
]) {
  if (loanAccountDetailsType == null) {
    return null;
  }
  return enums.LoanAccountDetailsType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsType.toString().toLowerCase()) ??
      defaultValue;
}

String loanAccountDetailsTypeExplodedListToJson(
    List<enums.LoanAccountDetailsType>? loanAccountDetailsType) {
  return loanAccountDetailsType?.map((e) => e.value!).join(',') ?? '';
}

List<String> loanAccountDetailsTypeListToJson(
    List<enums.LoanAccountDetailsType>? loanAccountDetailsType) {
  if (loanAccountDetailsType == null) {
    return [];
  }

  return loanAccountDetailsType.map((e) => e.value!).toList();
}

List<enums.LoanAccountDetailsType> loanAccountDetailsTypeListFromJson(
  List? loanAccountDetailsType, [
  List<enums.LoanAccountDetailsType>? defaultValue,
]) {
  if (loanAccountDetailsType == null) {
    return defaultValue ?? [];
  }

  return loanAccountDetailsType
      .map((e) => loanAccountDetailsTypeFromJson(e.toString()))
      .toList();
}

List<enums.LoanAccountDetailsType>? loanAccountDetailsTypeNullableListFromJson(
  List? loanAccountDetailsType, [
  List<enums.LoanAccountDetailsType>? defaultValue,
]) {
  if (loanAccountDetailsType == null) {
    return defaultValue;
  }

  return loanAccountDetailsType
      .map((e) => loanAccountDetailsTypeFromJson(e.toString()))
      .toList();
}

String? apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerTypeNullableToJson(
    enums.ApiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType?
        apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType) {
  return apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType?.value;
}

String? apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerTypeToJson(
    enums.ApiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType
        apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType) {
  return apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType.value;
}

enums.ApiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType
    apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerTypeFromJson(
  Object? apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType, [
  enums.ApiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType? defaultValue,
]) {
  return enums.ApiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.ApiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType
          .swaggerGeneratedUnknown;
}

enums.ApiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType?
    apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerTypeNullableFromJson(
  Object? apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType, [
  enums.ApiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType? defaultValue,
]) {
  if (apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType == null) {
    return null;
  }
  return enums.ApiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String
    apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerTypeExplodedListToJson(
        List<enums.ApiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType>?
            apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType) {
  return apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerTypeListToJson(
    List<enums.ApiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType>?
        apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType) {
  if (apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType == null) {
    return [];
  }

  return apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType
      .map((e) => e.value!)
      .toList();
}

List<enums.ApiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType>
    apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerTypeListFromJson(
  List? apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType, [
  List<enums.ApiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType>?
      defaultValue,
]) {
  if (apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType == null) {
    return defaultValue ?? [];
  }

  return apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType
      .map((e) =>
          apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerTypeFromJson(
              e.toString()))
      .toList();
}

List<enums.ApiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType>?
    apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerTypeNullableListFromJson(
  List? apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType, [
  List<enums.ApiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType>?
      defaultValue,
]) {
  if (apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType == null) {
    return defaultValue;
  }

  return apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerType
      .map((e) =>
          apiV1EasyCashCustomerIdLoanAccountIdLimitGetCustomerTypeFromJson(
              e.toString()))
      .toList();
}

// ignore: unused_element
String? _dateToJson(DateTime? date) {
  if (date == null) {
    return null;
  }

  final year = date.year.toString();
  final month = date.month < 10 ? '0${date.month}' : date.month.toString();
  final day = date.day < 10 ? '0${date.day}' : date.day.toString();

  return '$year-$month-$day';
}

class Wrapped<T> {
  final T value;
  const Wrapped.value(this.value);
}
