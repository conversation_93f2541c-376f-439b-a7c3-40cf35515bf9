// ignore_for_file: type=lint

import 'package:json_annotation/json_annotation.dart';
import 'package:json_annotation/json_annotation.dart' as json;
import 'package:collection/collection.dart';
import 'dart:convert';

import 'installment_service_schema.enums.swagger.dart' as enums;
export 'installment_service_schema.enums.swagger.dart';

part 'installment_service_schema.swagger.g.dart';

@JsonSerializable(explicitToJson: true)
class AccountDetails {
  const AccountDetails({
    this.id,
    this.accountId,
    this.creditArrangementId,
    this.name,
    this.loanAmount,
    this.currency,
    this.type,
    this.createdAt,
    this.lastModifiedAt,
    this.createdBy,
    this.lastModifiedBy,
    this.settlementAccountId,
    this.monthlyRepaymentDate,
    this.paymentPercentage,
    this.settlementAccountKey,
    this.customerId,
    this.isActive,
  });

  factory AccountDetails.fromJson(Map<String, dynamic> json) =>
      _$AccountDetailsFromJson(json);

  static const toJsonFactory = _$AccountDetailsToJson;
  Map<String, dynamic> toJson() => _$AccountDetailsToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String? id;
  @JsonKey(name: 'accountId', includeIfNull: false)
  final String? accountId;
  @JsonKey(name: 'creditArrangementId', includeIfNull: false)
  final String? creditArrangementId;
  @JsonKey(name: 'name', includeIfNull: false)
  final String? name;
  @JsonKey(name: 'loanAmount', includeIfNull: false)
  final double? loanAmount;
  @JsonKey(
    name: 'currency',
    includeIfNull: false,
    toJson: accountDetailsCurrencyNullableToJson,
    fromJson: accountDetailsCurrencyNullableFromJson,
  )
  final enums.AccountDetailsCurrency? currency;
  @JsonKey(
    name: 'type',
    includeIfNull: false,
    toJson: accountDetailsTypeNullableToJson,
    fromJson: accountDetailsTypeNullableFromJson,
  )
  final enums.AccountDetailsType? type;
  @JsonKey(name: 'createdAt', includeIfNull: false)
  final DateTime? createdAt;
  @JsonKey(name: 'lastModifiedAt', includeIfNull: false)
  final DateTime? lastModifiedAt;
  @JsonKey(name: 'createdBy', includeIfNull: false)
  final String? createdBy;
  @JsonKey(name: 'lastModifiedBy', includeIfNull: false)
  final String? lastModifiedBy;
  @JsonKey(name: 'settlementAccountId', includeIfNull: false)
  final String? settlementAccountId;
  @JsonKey(name: 'monthlyRepaymentDate', includeIfNull: false)
  final int? monthlyRepaymentDate;
  @JsonKey(name: 'paymentPercentage', includeIfNull: false)
  final double? paymentPercentage;
  @JsonKey(name: 'settlementAccountKey', includeIfNull: false)
  final String? settlementAccountKey;
  @JsonKey(name: 'customerId', includeIfNull: false)
  final String? customerId;
  @JsonKey(name: 'isActive', includeIfNull: false)
  final bool? isActive;
  static const fromJsonFactory = _$AccountDetailsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AccountDetailsExtension on AccountDetails {
  AccountDetails copyWith(
      {String? id,
      String? accountId,
      String? creditArrangementId,
      String? name,
      double? loanAmount,
      enums.AccountDetailsCurrency? currency,
      enums.AccountDetailsType? type,
      DateTime? createdAt,
      DateTime? lastModifiedAt,
      String? createdBy,
      String? lastModifiedBy,
      String? settlementAccountId,
      int? monthlyRepaymentDate,
      double? paymentPercentage,
      String? settlementAccountKey,
      String? customerId,
      bool? isActive}) {
    return AccountDetails(
        id: id ?? this.id,
        accountId: accountId ?? this.accountId,
        creditArrangementId: creditArrangementId ?? this.creditArrangementId,
        name: name ?? this.name,
        loanAmount: loanAmount ?? this.loanAmount,
        currency: currency ?? this.currency,
        type: type ?? this.type,
        createdAt: createdAt ?? this.createdAt,
        lastModifiedAt: lastModifiedAt ?? this.lastModifiedAt,
        createdBy: createdBy ?? this.createdBy,
        lastModifiedBy: lastModifiedBy ?? this.lastModifiedBy,
        settlementAccountId: settlementAccountId ?? this.settlementAccountId,
        monthlyRepaymentDate: monthlyRepaymentDate ?? this.monthlyRepaymentDate,
        paymentPercentage: paymentPercentage ?? this.paymentPercentage,
        settlementAccountKey: settlementAccountKey ?? this.settlementAccountKey,
        customerId: customerId ?? this.customerId,
        isActive: isActive ?? this.isActive);
  }

  AccountDetails copyWithWrapped(
      {Wrapped<String?>? id,
      Wrapped<String?>? accountId,
      Wrapped<String?>? creditArrangementId,
      Wrapped<String?>? name,
      Wrapped<double?>? loanAmount,
      Wrapped<enums.AccountDetailsCurrency?>? currency,
      Wrapped<enums.AccountDetailsType?>? type,
      Wrapped<DateTime?>? createdAt,
      Wrapped<DateTime?>? lastModifiedAt,
      Wrapped<String?>? createdBy,
      Wrapped<String?>? lastModifiedBy,
      Wrapped<String?>? settlementAccountId,
      Wrapped<int?>? monthlyRepaymentDate,
      Wrapped<double?>? paymentPercentage,
      Wrapped<String?>? settlementAccountKey,
      Wrapped<String?>? customerId,
      Wrapped<bool?>? isActive}) {
    return AccountDetails(
        id: (id != null ? id.value : this.id),
        accountId: (accountId != null ? accountId.value : this.accountId),
        creditArrangementId: (creditArrangementId != null
            ? creditArrangementId.value
            : this.creditArrangementId),
        name: (name != null ? name.value : this.name),
        loanAmount: (loanAmount != null ? loanAmount.value : this.loanAmount),
        currency: (currency != null ? currency.value : this.currency),
        type: (type != null ? type.value : this.type),
        createdAt: (createdAt != null ? createdAt.value : this.createdAt),
        lastModifiedAt: (lastModifiedAt != null
            ? lastModifiedAt.value
            : this.lastModifiedAt),
        createdBy: (createdBy != null ? createdBy.value : this.createdBy),
        lastModifiedBy: (lastModifiedBy != null
            ? lastModifiedBy.value
            : this.lastModifiedBy),
        settlementAccountId: (settlementAccountId != null
            ? settlementAccountId.value
            : this.settlementAccountId),
        monthlyRepaymentDate: (monthlyRepaymentDate != null
            ? monthlyRepaymentDate.value
            : this.monthlyRepaymentDate),
        paymentPercentage: (paymentPercentage != null
            ? paymentPercentage.value
            : this.paymentPercentage),
        settlementAccountKey: (settlementAccountKey != null
            ? settlementAccountKey.value
            : this.settlementAccountKey),
        customerId: (customerId != null ? customerId.value : this.customerId),
        isActive: (isActive != null ? isActive.value : this.isActive));
  }
}

@JsonSerializable(explicitToJson: true)
class UpdateLoanAmountCommand {
  const UpdateLoanAmountCommand({
    required this.loanAmount,
  });

  factory UpdateLoanAmountCommand.fromJson(Map<String, dynamic> json) =>
      _$UpdateLoanAmountCommandFromJson(json);

  static const toJsonFactory = _$UpdateLoanAmountCommandToJson;
  Map<String, dynamic> toJson() => _$UpdateLoanAmountCommandToJson(this);

  @JsonKey(name: 'loanAmount', includeIfNull: false)
  final double loanAmount;
  static const fromJsonFactory = _$UpdateLoanAmountCommandFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UpdateLoanAmountCommandExtension on UpdateLoanAmountCommand {
  UpdateLoanAmountCommand copyWith({double? loanAmount}) {
    return UpdateLoanAmountCommand(loanAmount: loanAmount ?? this.loanAmount);
  }

  UpdateLoanAmountCommand copyWithWrapped({Wrapped<double>? loanAmount}) {
    return UpdateLoanAmountCommand(
        loanAmount: (loanAmount != null ? loanAmount.value : this.loanAmount));
  }
}

@JsonSerializable(explicitToJson: true)
class CreditArrangementLimits {
  const CreditArrangementLimits({
    required this.globalLimit,
    required this.approvedAmount,
    this.riskGrade,
    required this.customerId,
  });

  factory CreditArrangementLimits.fromJson(Map<String, dynamic> json) =>
      _$CreditArrangementLimitsFromJson(json);

  static const toJsonFactory = _$CreditArrangementLimitsToJson;
  Map<String, dynamic> toJson() => _$CreditArrangementLimitsToJson(this);

  @JsonKey(name: 'globalLimit', includeIfNull: false)
  final double globalLimit;
  @JsonKey(name: 'approvedAmount', includeIfNull: false)
  final double approvedAmount;
  @JsonKey(name: 'riskGrade', includeIfNull: false)
  final String? riskGrade;
  @JsonKey(name: 'customerId', includeIfNull: false)
  final String customerId;
  static const fromJsonFactory = _$CreditArrangementLimitsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CreditArrangementLimitsExtension on CreditArrangementLimits {
  CreditArrangementLimits copyWith(
      {double? globalLimit,
      double? approvedAmount,
      String? riskGrade,
      String? customerId}) {
    return CreditArrangementLimits(
        globalLimit: globalLimit ?? this.globalLimit,
        approvedAmount: approvedAmount ?? this.approvedAmount,
        riskGrade: riskGrade ?? this.riskGrade,
        customerId: customerId ?? this.customerId);
  }

  CreditArrangementLimits copyWithWrapped(
      {Wrapped<double>? globalLimit,
      Wrapped<double>? approvedAmount,
      Wrapped<String?>? riskGrade,
      Wrapped<String>? customerId}) {
    return CreditArrangementLimits(
        globalLimit:
            (globalLimit != null ? globalLimit.value : this.globalLimit),
        approvedAmount: (approvedAmount != null
            ? approvedAmount.value
            : this.approvedAmount),
        riskGrade: (riskGrade != null ? riskGrade.value : this.riskGrade),
        customerId: (customerId != null ? customerId.value : this.customerId));
  }
}

@JsonSerializable(explicitToJson: true)
class LoanAccountDetailsDTO {
  const LoanAccountDetailsDTO({
    this.id,
    this.applicationId,
    this.name,
    this.creationDate,
    this.currency,
    this.notes,
    this.holderKey,
    this.branchKey,
    this.productKey,
    this.productType,
    this.state,
    this.subState,
    this.rawMambuResponse,
    this.loanAmount,
    this.paymentSettings,
    this.paymentSchedule,
    this.interestSettings,
    this.firstRepaymentDate,
    this.type,
    this.encodedKey,
    this.accountHolderType,
    this.customerId,
    this.principleBalance,
    this.interestBalance,
    this.feeBalance,
    this.holdBalance,
    this.interestDue,
    this.minLimit,
  });

  factory LoanAccountDetailsDTO.fromJson(Map<String, dynamic> json) =>
      _$LoanAccountDetailsDTOFromJson(json);

  static const toJsonFactory = _$LoanAccountDetailsDTOToJson;
  Map<String, dynamic> toJson() => _$LoanAccountDetailsDTOToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String? id;
  @JsonKey(name: 'applicationId', includeIfNull: false)
  final String? applicationId;
  @JsonKey(name: 'name', includeIfNull: false)
  final String? name;
  @JsonKey(name: 'creationDate', includeIfNull: false)
  final DateTime? creationDate;
  @JsonKey(
    name: 'currency',
    includeIfNull: false,
    toJson: loanAccountDetailsDTOCurrencyNullableToJson,
    fromJson: loanAccountDetailsDTOCurrencyNullableFromJson,
  )
  final enums.LoanAccountDetailsDTOCurrency? currency;
  @JsonKey(name: 'notes', includeIfNull: false)
  final String? notes;
  @JsonKey(name: 'holderKey', includeIfNull: false)
  final String? holderKey;
  @JsonKey(name: 'branchKey', includeIfNull: false)
  final String? branchKey;
  @JsonKey(name: 'productKey', includeIfNull: false)
  final String? productKey;
  @JsonKey(
    name: 'productType',
    includeIfNull: false,
    toJson: loanAccountDetailsDTOProductTypeNullableToJson,
    fromJson: loanAccountDetailsDTOProductTypeNullableFromJson,
  )
  final enums.LoanAccountDetailsDTOProductType? productType;
  @JsonKey(name: 'state', includeIfNull: false)
  final String? state;
  @JsonKey(name: 'subState', includeIfNull: false)
  final String? subState;
  @JsonKey(name: 'rawMambuResponse', includeIfNull: false)
  final Object? rawMambuResponse;
  @JsonKey(name: 'loanAmount', includeIfNull: false)
  final double? loanAmount;
  @JsonKey(name: 'paymentSettings', includeIfNull: false)
  final PaymentSettings? paymentSettings;
  @JsonKey(name: 'paymentSchedule', includeIfNull: false)
  final ScheduleSettings? paymentSchedule;
  @JsonKey(name: 'interestSettings', includeIfNull: false)
  final InterestSettings? interestSettings;
  @JsonKey(
      name: 'firstRepaymentDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime? firstRepaymentDate;
  @JsonKey(
    name: 'type',
    includeIfNull: false,
    toJson: loanAccountDetailsDTOTypeNullableToJson,
    fromJson: loanAccountDetailsDTOTypeNullableFromJson,
  )
  final enums.LoanAccountDetailsDTOType? type;
  @JsonKey(name: 'encodedKey', includeIfNull: false)
  final String? encodedKey;
  @JsonKey(name: 'accountHolderType', includeIfNull: false)
  final String? accountHolderType;
  @JsonKey(name: 'customerId', includeIfNull: false)
  final String? customerId;
  @JsonKey(name: 'principleBalance', includeIfNull: false)
  final double? principleBalance;
  @JsonKey(name: 'interestBalance', includeIfNull: false)
  final double? interestBalance;
  @JsonKey(name: 'feeBalance', includeIfNull: false)
  final double? feeBalance;
  @JsonKey(name: 'holdBalance', includeIfNull: false)
  final double? holdBalance;
  @JsonKey(name: 'interestDue', includeIfNull: false)
  final double? interestDue;
  @JsonKey(name: 'minLimit', includeIfNull: false)
  final double? minLimit;
  static const fromJsonFactory = _$LoanAccountDetailsDTOFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $LoanAccountDetailsDTOExtension on LoanAccountDetailsDTO {
  LoanAccountDetailsDTO copyWith(
      {String? id,
      String? applicationId,
      String? name,
      DateTime? creationDate,
      enums.LoanAccountDetailsDTOCurrency? currency,
      String? notes,
      String? holderKey,
      String? branchKey,
      String? productKey,
      enums.LoanAccountDetailsDTOProductType? productType,
      String? state,
      String? subState,
      Object? rawMambuResponse,
      double? loanAmount,
      PaymentSettings? paymentSettings,
      ScheduleSettings? paymentSchedule,
      InterestSettings? interestSettings,
      DateTime? firstRepaymentDate,
      enums.LoanAccountDetailsDTOType? type,
      String? encodedKey,
      String? accountHolderType,
      String? customerId,
      double? principleBalance,
      double? interestBalance,
      double? feeBalance,
      double? holdBalance,
      double? interestDue,
      double? minLimit}) {
    return LoanAccountDetailsDTO(
        id: id ?? this.id,
        applicationId: applicationId ?? this.applicationId,
        name: name ?? this.name,
        creationDate: creationDate ?? this.creationDate,
        currency: currency ?? this.currency,
        notes: notes ?? this.notes,
        holderKey: holderKey ?? this.holderKey,
        branchKey: branchKey ?? this.branchKey,
        productKey: productKey ?? this.productKey,
        productType: productType ?? this.productType,
        state: state ?? this.state,
        subState: subState ?? this.subState,
        rawMambuResponse: rawMambuResponse ?? this.rawMambuResponse,
        loanAmount: loanAmount ?? this.loanAmount,
        paymentSettings: paymentSettings ?? this.paymentSettings,
        paymentSchedule: paymentSchedule ?? this.paymentSchedule,
        interestSettings: interestSettings ?? this.interestSettings,
        firstRepaymentDate: firstRepaymentDate ?? this.firstRepaymentDate,
        type: type ?? this.type,
        encodedKey: encodedKey ?? this.encodedKey,
        accountHolderType: accountHolderType ?? this.accountHolderType,
        customerId: customerId ?? this.customerId,
        principleBalance: principleBalance ?? this.principleBalance,
        interestBalance: interestBalance ?? this.interestBalance,
        feeBalance: feeBalance ?? this.feeBalance,
        holdBalance: holdBalance ?? this.holdBalance,
        interestDue: interestDue ?? this.interestDue,
        minLimit: minLimit ?? this.minLimit);
  }

  LoanAccountDetailsDTO copyWithWrapped(
      {Wrapped<String?>? id,
      Wrapped<String?>? applicationId,
      Wrapped<String?>? name,
      Wrapped<DateTime?>? creationDate,
      Wrapped<enums.LoanAccountDetailsDTOCurrency?>? currency,
      Wrapped<String?>? notes,
      Wrapped<String?>? holderKey,
      Wrapped<String?>? branchKey,
      Wrapped<String?>? productKey,
      Wrapped<enums.LoanAccountDetailsDTOProductType?>? productType,
      Wrapped<String?>? state,
      Wrapped<String?>? subState,
      Wrapped<Object?>? rawMambuResponse,
      Wrapped<double?>? loanAmount,
      Wrapped<PaymentSettings?>? paymentSettings,
      Wrapped<ScheduleSettings?>? paymentSchedule,
      Wrapped<InterestSettings?>? interestSettings,
      Wrapped<DateTime?>? firstRepaymentDate,
      Wrapped<enums.LoanAccountDetailsDTOType?>? type,
      Wrapped<String?>? encodedKey,
      Wrapped<String?>? accountHolderType,
      Wrapped<String?>? customerId,
      Wrapped<double?>? principleBalance,
      Wrapped<double?>? interestBalance,
      Wrapped<double?>? feeBalance,
      Wrapped<double?>? holdBalance,
      Wrapped<double?>? interestDue,
      Wrapped<double?>? minLimit}) {
    return LoanAccountDetailsDTO(
        id: (id != null ? id.value : this.id),
        applicationId:
            (applicationId != null ? applicationId.value : this.applicationId),
        name: (name != null ? name.value : this.name),
        creationDate:
            (creationDate != null ? creationDate.value : this.creationDate),
        currency: (currency != null ? currency.value : this.currency),
        notes: (notes != null ? notes.value : this.notes),
        holderKey: (holderKey != null ? holderKey.value : this.holderKey),
        branchKey: (branchKey != null ? branchKey.value : this.branchKey),
        productKey: (productKey != null ? productKey.value : this.productKey),
        productType:
            (productType != null ? productType.value : this.productType),
        state: (state != null ? state.value : this.state),
        subState: (subState != null ? subState.value : this.subState),
        rawMambuResponse: (rawMambuResponse != null
            ? rawMambuResponse.value
            : this.rawMambuResponse),
        loanAmount: (loanAmount != null ? loanAmount.value : this.loanAmount),
        paymentSettings: (paymentSettings != null
            ? paymentSettings.value
            : this.paymentSettings),
        paymentSchedule: (paymentSchedule != null
            ? paymentSchedule.value
            : this.paymentSchedule),
        interestSettings: (interestSettings != null
            ? interestSettings.value
            : this.interestSettings),
        firstRepaymentDate: (firstRepaymentDate != null
            ? firstRepaymentDate.value
            : this.firstRepaymentDate),
        type: (type != null ? type.value : this.type),
        encodedKey: (encodedKey != null ? encodedKey.value : this.encodedKey),
        accountHolderType: (accountHolderType != null
            ? accountHolderType.value
            : this.accountHolderType),
        customerId: (customerId != null ? customerId.value : this.customerId),
        principleBalance: (principleBalance != null
            ? principleBalance.value
            : this.principleBalance),
        interestBalance: (interestBalance != null
            ? interestBalance.value
            : this.interestBalance),
        feeBalance: (feeBalance != null ? feeBalance.value : this.feeBalance),
        holdBalance:
            (holdBalance != null ? holdBalance.value : this.holdBalance),
        interestDue:
            (interestDue != null ? interestDue.value : this.interestDue),
        minLimit: (minLimit != null ? minLimit.value : this.minLimit));
  }
}

@JsonSerializable(explicitToJson: true)
class LoanAccountDetailsResponse {
  const LoanAccountDetailsResponse({
    this.loanAccountDetailsDTOS,
    this.creditSummary,
  });

  factory LoanAccountDetailsResponse.fromJson(Map<String, dynamic> json) =>
      _$LoanAccountDetailsResponseFromJson(json);

  static const toJsonFactory = _$LoanAccountDetailsResponseToJson;
  Map<String, dynamic> toJson() => _$LoanAccountDetailsResponseToJson(this);

  @JsonKey(
      name: 'loanAccountDetailsDTOS',
      includeIfNull: false,
      defaultValue: <LoanAccountDetailsDTO>[])
  final List<LoanAccountDetailsDTO>? loanAccountDetailsDTOS;
  @JsonKey(name: 'creditSummary', includeIfNull: false)
  final CreditSummary? creditSummary;
  static const fromJsonFactory = _$LoanAccountDetailsResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $LoanAccountDetailsResponseExtension on LoanAccountDetailsResponse {
  LoanAccountDetailsResponse copyWith(
      {List<LoanAccountDetailsDTO>? loanAccountDetailsDTOS,
      CreditSummary? creditSummary}) {
    return LoanAccountDetailsResponse(
        loanAccountDetailsDTOS:
            loanAccountDetailsDTOS ?? this.loanAccountDetailsDTOS,
        creditSummary: creditSummary ?? this.creditSummary);
  }

  LoanAccountDetailsResponse copyWithWrapped(
      {Wrapped<List<LoanAccountDetailsDTO>?>? loanAccountDetailsDTOS,
      Wrapped<CreditSummary?>? creditSummary}) {
    return LoanAccountDetailsResponse(
        loanAccountDetailsDTOS: (loanAccountDetailsDTOS != null
            ? loanAccountDetailsDTOS.value
            : this.loanAccountDetailsDTOS),
        creditSummary:
            (creditSummary != null ? creditSummary.value : this.creditSummary));
  }
}

@JsonSerializable(explicitToJson: true)
class PaymentSettings {
  const PaymentSettings({
    this.percentage,
  });

  factory PaymentSettings.fromJson(Map<String, dynamic> json) =>
      _$PaymentSettingsFromJson(json);

  static const toJsonFactory = _$PaymentSettingsToJson;
  Map<String, dynamic> toJson() => _$PaymentSettingsToJson(this);

  @JsonKey(name: 'percentage', includeIfNull: false)
  final double? percentage;
  static const fromJsonFactory = _$PaymentSettingsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $PaymentSettingsExtension on PaymentSettings {
  PaymentSettings copyWith({double? percentage}) {
    return PaymentSettings(percentage: percentage ?? this.percentage);
  }

  PaymentSettings copyWithWrapped({Wrapped<double?>? percentage}) {
    return PaymentSettings(
        percentage: (percentage != null ? percentage.value : this.percentage));
  }
}

@JsonSerializable(explicitToJson: true)
class ScheduleSettings {
  const ScheduleSettings({
    this.interval,
    this.numberOfPreviewedInstalments,
    this.firstRepaymentDate,
    this.repaymentPeriodCount,
    this.repaymentPeriodUnit,
  });

  factory ScheduleSettings.fromJson(Map<String, dynamic> json) =>
      _$ScheduleSettingsFromJson(json);

  static const toJsonFactory = _$ScheduleSettingsToJson;
  Map<String, dynamic> toJson() => _$ScheduleSettingsToJson(this);

  @JsonKey(name: 'interval', includeIfNull: false)
  final Interval? interval;
  @JsonKey(name: 'numberOfPreviewedInstalments', includeIfNull: false)
  final int? numberOfPreviewedInstalments;
  @JsonKey(
      name: 'firstRepaymentDate', includeIfNull: false, toJson: _dateToJson)
  final DateTime? firstRepaymentDate;
  @JsonKey(name: 'repaymentPeriodCount', includeIfNull: false)
  final int? repaymentPeriodCount;
  @JsonKey(
    name: 'repaymentPeriodUnit',
    includeIfNull: false,
    toJson: scheduleSettingsRepaymentPeriodUnitNullableToJson,
    fromJson: scheduleSettingsRepaymentPeriodUnitNullableFromJson,
  )
  final enums.ScheduleSettingsRepaymentPeriodUnit? repaymentPeriodUnit;
  static const fromJsonFactory = _$ScheduleSettingsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ScheduleSettingsExtension on ScheduleSettings {
  ScheduleSettings copyWith(
      {Interval? interval,
      int? numberOfPreviewedInstalments,
      DateTime? firstRepaymentDate,
      int? repaymentPeriodCount,
      enums.ScheduleSettingsRepaymentPeriodUnit? repaymentPeriodUnit}) {
    return ScheduleSettings(
        interval: interval ?? this.interval,
        numberOfPreviewedInstalments:
            numberOfPreviewedInstalments ?? this.numberOfPreviewedInstalments,
        firstRepaymentDate: firstRepaymentDate ?? this.firstRepaymentDate,
        repaymentPeriodCount: repaymentPeriodCount ?? this.repaymentPeriodCount,
        repaymentPeriodUnit: repaymentPeriodUnit ?? this.repaymentPeriodUnit);
  }

  ScheduleSettings copyWithWrapped(
      {Wrapped<Interval?>? interval,
      Wrapped<int?>? numberOfPreviewedInstalments,
      Wrapped<DateTime?>? firstRepaymentDate,
      Wrapped<int?>? repaymentPeriodCount,
      Wrapped<enums.ScheduleSettingsRepaymentPeriodUnit?>?
          repaymentPeriodUnit}) {
    return ScheduleSettings(
        interval: (interval != null ? interval.value : this.interval),
        numberOfPreviewedInstalments: (numberOfPreviewedInstalments != null
            ? numberOfPreviewedInstalments.value
            : this.numberOfPreviewedInstalments),
        firstRepaymentDate: (firstRepaymentDate != null
            ? firstRepaymentDate.value
            : this.firstRepaymentDate),
        repaymentPeriodCount: (repaymentPeriodCount != null
            ? repaymentPeriodCount.value
            : this.repaymentPeriodCount),
        repaymentPeriodUnit: (repaymentPeriodUnit != null
            ? repaymentPeriodUnit.value
            : this.repaymentPeriodUnit));
  }
}

@JsonSerializable(explicitToJson: true)
class InterestSettings {
  const InterestSettings({
    this.interestRateSource,
    this.interestRate,
    this.interestType,
  });

  factory InterestSettings.fromJson(Map<String, dynamic> json) =>
      _$InterestSettingsFromJson(json);

  static const toJsonFactory = _$InterestSettingsToJson;
  Map<String, dynamic> toJson() => _$InterestSettingsToJson(this);

  @JsonKey(
    name: 'interestRateSource',
    includeIfNull: false,
    toJson: interestSettingsInterestRateSourceNullableToJson,
    fromJson: interestSettingsInterestRateSourceNullableFromJson,
  )
  final enums.InterestSettingsInterestRateSource? interestRateSource;
  @JsonKey(name: 'interestRate', includeIfNull: false)
  final double? interestRate;
  @JsonKey(
    name: 'interestType',
    includeIfNull: false,
    toJson: interestSettingsInterestTypeNullableToJson,
    fromJson: interestSettingsInterestTypeNullableFromJson,
  )
  final enums.InterestSettingsInterestType? interestType;
  static const fromJsonFactory = _$InterestSettingsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $InterestSettingsExtension on InterestSettings {
  InterestSettings copyWith(
      {enums.InterestSettingsInterestRateSource? interestRateSource,
      double? interestRate,
      enums.InterestSettingsInterestType? interestType}) {
    return InterestSettings(
        interestRateSource: interestRateSource ?? this.interestRateSource,
        interestRate: interestRate ?? this.interestRate,
        interestType: interestType ?? this.interestType);
  }

  InterestSettings copyWithWrapped(
      {Wrapped<enums.InterestSettingsInterestRateSource?>? interestRateSource,
      Wrapped<double?>? interestRate,
      Wrapped<enums.InterestSettingsInterestType?>? interestType}) {
    return InterestSettings(
        interestRateSource: (interestRateSource != null
            ? interestRateSource.value
            : this.interestRateSource),
        interestRate:
            (interestRate != null ? interestRate.value : this.interestRate),
        interestType:
            (interestType != null ? interestType.value : this.interestType));
  }
}

@JsonSerializable(explicitToJson: true)
class Interval {
  const Interval({
    this.count,
    this.unit,
  });

  factory Interval.fromJson(Map<String, dynamic> json) =>
      _$IntervalFromJson(json);

  static const toJsonFactory = _$IntervalToJson;
  Map<String, dynamic> toJson() => _$IntervalToJson(this);

  @JsonKey(name: 'count', includeIfNull: false)
  final int? count;
  @JsonKey(
    name: 'unit',
    includeIfNull: false,
    toJson: intervalUnitNullableToJson,
    fromJson: intervalUnitNullableFromJson,
  )
  final enums.IntervalUnit? unit;
  static const fromJsonFactory = _$IntervalFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $IntervalExtension on Interval {
  Interval copyWith({int? count, enums.IntervalUnit? unit}) {
    return Interval(count: count ?? this.count, unit: unit ?? this.unit);
  }

  Interval copyWithWrapped(
      {Wrapped<int?>? count, Wrapped<enums.IntervalUnit?>? unit}) {
    return Interval(
        count: (count != null ? count.value : this.count),
        unit: (unit != null ? unit.value : this.unit));
  }
}

@JsonSerializable(explicitToJson: true)
class CreditSummary {
  const CreditSummary({
    this.availableToSpend,
    this.wioCreditAmount,
    this.totalCreditAmount,
    this.creditCardLoanAmount,
    this.easyCashLoanAmount,
  });

  factory CreditSummary.fromJson(Map<String, dynamic> json) =>
      _$CreditSummaryFromJson(json);

  static const toJsonFactory = _$CreditSummaryToJson;
  Map<String, dynamic> toJson() => _$CreditSummaryToJson(this);

  @JsonKey(name: 'availableToSpend', includeIfNull: false)
  final double? availableToSpend;
  @JsonKey(name: 'wioCreditAmount', includeIfNull: false)
  final double? wioCreditAmount;
  @JsonKey(name: 'totalCreditAmount', includeIfNull: false)
  final double? totalCreditAmount;
  @JsonKey(name: 'creditCardLoanAmount', includeIfNull: false)
  final double? creditCardLoanAmount;
  @JsonKey(name: 'easyCashLoanAmount', includeIfNull: false)
  final double? easyCashLoanAmount;
  static const fromJsonFactory = _$CreditSummaryFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CreditSummaryExtension on CreditSummary {
  CreditSummary copyWith(
      {double? availableToSpend,
      double? wioCreditAmount,
      double? totalCreditAmount,
      double? creditCardLoanAmount,
      double? easyCashLoanAmount}) {
    return CreditSummary(
        availableToSpend: availableToSpend ?? this.availableToSpend,
        wioCreditAmount: wioCreditAmount ?? this.wioCreditAmount,
        totalCreditAmount: totalCreditAmount ?? this.totalCreditAmount,
        creditCardLoanAmount: creditCardLoanAmount ?? this.creditCardLoanAmount,
        easyCashLoanAmount: easyCashLoanAmount ?? this.easyCashLoanAmount);
  }

  CreditSummary copyWithWrapped(
      {Wrapped<double?>? availableToSpend,
      Wrapped<double?>? wioCreditAmount,
      Wrapped<double?>? totalCreditAmount,
      Wrapped<double?>? creditCardLoanAmount,
      Wrapped<double?>? easyCashLoanAmount}) {
    return CreditSummary(
        availableToSpend: (availableToSpend != null
            ? availableToSpend.value
            : this.availableToSpend),
        wioCreditAmount: (wioCreditAmount != null
            ? wioCreditAmount.value
            : this.wioCreditAmount),
        totalCreditAmount: (totalCreditAmount != null
            ? totalCreditAmount.value
            : this.totalCreditAmount),
        creditCardLoanAmount: (creditCardLoanAmount != null
            ? creditCardLoanAmount.value
            : this.creditCardLoanAmount),
        easyCashLoanAmount: (easyCashLoanAmount != null
            ? easyCashLoanAmount.value
            : this.easyCashLoanAmount));
  }
}

String? accountDetailsCurrencyNullableToJson(
    enums.AccountDetailsCurrency? accountDetailsCurrency) {
  return accountDetailsCurrency?.value;
}

String? accountDetailsCurrencyToJson(
    enums.AccountDetailsCurrency accountDetailsCurrency) {
  return accountDetailsCurrency.value;
}

enums.AccountDetailsCurrency accountDetailsCurrencyFromJson(
  Object? accountDetailsCurrency, [
  enums.AccountDetailsCurrency? defaultValue,
]) {
  return enums.AccountDetailsCurrency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          accountDetailsCurrency?.toString().toLowerCase()) ??
      defaultValue ??
      enums.AccountDetailsCurrency.swaggerGeneratedUnknown;
}

enums.AccountDetailsCurrency? accountDetailsCurrencyNullableFromJson(
  Object? accountDetailsCurrency, [
  enums.AccountDetailsCurrency? defaultValue,
]) {
  if (accountDetailsCurrency == null) {
    return null;
  }
  return enums.AccountDetailsCurrency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          accountDetailsCurrency.toString().toLowerCase()) ??
      defaultValue;
}

String accountDetailsCurrencyExplodedListToJson(
    List<enums.AccountDetailsCurrency>? accountDetailsCurrency) {
  return accountDetailsCurrency?.map((e) => e.value!).join(',') ?? '';
}

List<String> accountDetailsCurrencyListToJson(
    List<enums.AccountDetailsCurrency>? accountDetailsCurrency) {
  if (accountDetailsCurrency == null) {
    return [];
  }

  return accountDetailsCurrency.map((e) => e.value!).toList();
}

List<enums.AccountDetailsCurrency> accountDetailsCurrencyListFromJson(
  List? accountDetailsCurrency, [
  List<enums.AccountDetailsCurrency>? defaultValue,
]) {
  if (accountDetailsCurrency == null) {
    return defaultValue ?? [];
  }

  return accountDetailsCurrency
      .map((e) => accountDetailsCurrencyFromJson(e.toString()))
      .toList();
}

List<enums.AccountDetailsCurrency>? accountDetailsCurrencyNullableListFromJson(
  List? accountDetailsCurrency, [
  List<enums.AccountDetailsCurrency>? defaultValue,
]) {
  if (accountDetailsCurrency == null) {
    return defaultValue;
  }

  return accountDetailsCurrency
      .map((e) => accountDetailsCurrencyFromJson(e.toString()))
      .toList();
}

String? accountDetailsTypeNullableToJson(
    enums.AccountDetailsType? accountDetailsType) {
  return accountDetailsType?.value;
}

String? accountDetailsTypeToJson(enums.AccountDetailsType accountDetailsType) {
  return accountDetailsType.value;
}

enums.AccountDetailsType accountDetailsTypeFromJson(
  Object? accountDetailsType, [
  enums.AccountDetailsType? defaultValue,
]) {
  return enums.AccountDetailsType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          accountDetailsType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.AccountDetailsType.swaggerGeneratedUnknown;
}

enums.AccountDetailsType? accountDetailsTypeNullableFromJson(
  Object? accountDetailsType, [
  enums.AccountDetailsType? defaultValue,
]) {
  if (accountDetailsType == null) {
    return null;
  }
  return enums.AccountDetailsType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          accountDetailsType.toString().toLowerCase()) ??
      defaultValue;
}

String accountDetailsTypeExplodedListToJson(
    List<enums.AccountDetailsType>? accountDetailsType) {
  return accountDetailsType?.map((e) => e.value!).join(',') ?? '';
}

List<String> accountDetailsTypeListToJson(
    List<enums.AccountDetailsType>? accountDetailsType) {
  if (accountDetailsType == null) {
    return [];
  }

  return accountDetailsType.map((e) => e.value!).toList();
}

List<enums.AccountDetailsType> accountDetailsTypeListFromJson(
  List? accountDetailsType, [
  List<enums.AccountDetailsType>? defaultValue,
]) {
  if (accountDetailsType == null) {
    return defaultValue ?? [];
  }

  return accountDetailsType
      .map((e) => accountDetailsTypeFromJson(e.toString()))
      .toList();
}

List<enums.AccountDetailsType>? accountDetailsTypeNullableListFromJson(
  List? accountDetailsType, [
  List<enums.AccountDetailsType>? defaultValue,
]) {
  if (accountDetailsType == null) {
    return defaultValue;
  }

  return accountDetailsType
      .map((e) => accountDetailsTypeFromJson(e.toString()))
      .toList();
}

String? loanAccountDetailsDTOCurrencyNullableToJson(
    enums.LoanAccountDetailsDTOCurrency? loanAccountDetailsDTOCurrency) {
  return loanAccountDetailsDTOCurrency?.value;
}

String? loanAccountDetailsDTOCurrencyToJson(
    enums.LoanAccountDetailsDTOCurrency loanAccountDetailsDTOCurrency) {
  return loanAccountDetailsDTOCurrency.value;
}

enums.LoanAccountDetailsDTOCurrency loanAccountDetailsDTOCurrencyFromJson(
  Object? loanAccountDetailsDTOCurrency, [
  enums.LoanAccountDetailsDTOCurrency? defaultValue,
]) {
  return enums.LoanAccountDetailsDTOCurrency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsDTOCurrency?.toString().toLowerCase()) ??
      defaultValue ??
      enums.LoanAccountDetailsDTOCurrency.swaggerGeneratedUnknown;
}

enums.LoanAccountDetailsDTOCurrency?
    loanAccountDetailsDTOCurrencyNullableFromJson(
  Object? loanAccountDetailsDTOCurrency, [
  enums.LoanAccountDetailsDTOCurrency? defaultValue,
]) {
  if (loanAccountDetailsDTOCurrency == null) {
    return null;
  }
  return enums.LoanAccountDetailsDTOCurrency.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsDTOCurrency.toString().toLowerCase()) ??
      defaultValue;
}

String loanAccountDetailsDTOCurrencyExplodedListToJson(
    List<enums.LoanAccountDetailsDTOCurrency>? loanAccountDetailsDTOCurrency) {
  return loanAccountDetailsDTOCurrency?.map((e) => e.value!).join(',') ?? '';
}

List<String> loanAccountDetailsDTOCurrencyListToJson(
    List<enums.LoanAccountDetailsDTOCurrency>? loanAccountDetailsDTOCurrency) {
  if (loanAccountDetailsDTOCurrency == null) {
    return [];
  }

  return loanAccountDetailsDTOCurrency.map((e) => e.value!).toList();
}

List<enums.LoanAccountDetailsDTOCurrency>
    loanAccountDetailsDTOCurrencyListFromJson(
  List? loanAccountDetailsDTOCurrency, [
  List<enums.LoanAccountDetailsDTOCurrency>? defaultValue,
]) {
  if (loanAccountDetailsDTOCurrency == null) {
    return defaultValue ?? [];
  }

  return loanAccountDetailsDTOCurrency
      .map((e) => loanAccountDetailsDTOCurrencyFromJson(e.toString()))
      .toList();
}

List<enums.LoanAccountDetailsDTOCurrency>?
    loanAccountDetailsDTOCurrencyNullableListFromJson(
  List? loanAccountDetailsDTOCurrency, [
  List<enums.LoanAccountDetailsDTOCurrency>? defaultValue,
]) {
  if (loanAccountDetailsDTOCurrency == null) {
    return defaultValue;
  }

  return loanAccountDetailsDTOCurrency
      .map((e) => loanAccountDetailsDTOCurrencyFromJson(e.toString()))
      .toList();
}

String? loanAccountDetailsDTOProductTypeNullableToJson(
    enums.LoanAccountDetailsDTOProductType? loanAccountDetailsDTOProductType) {
  return loanAccountDetailsDTOProductType?.value;
}

String? loanAccountDetailsDTOProductTypeToJson(
    enums.LoanAccountDetailsDTOProductType loanAccountDetailsDTOProductType) {
  return loanAccountDetailsDTOProductType.value;
}

enums.LoanAccountDetailsDTOProductType loanAccountDetailsDTOProductTypeFromJson(
  Object? loanAccountDetailsDTOProductType, [
  enums.LoanAccountDetailsDTOProductType? defaultValue,
]) {
  return enums.LoanAccountDetailsDTOProductType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsDTOProductType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.LoanAccountDetailsDTOProductType.swaggerGeneratedUnknown;
}

enums.LoanAccountDetailsDTOProductType?
    loanAccountDetailsDTOProductTypeNullableFromJson(
  Object? loanAccountDetailsDTOProductType, [
  enums.LoanAccountDetailsDTOProductType? defaultValue,
]) {
  if (loanAccountDetailsDTOProductType == null) {
    return null;
  }
  return enums.LoanAccountDetailsDTOProductType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsDTOProductType.toString().toLowerCase()) ??
      defaultValue;
}

String loanAccountDetailsDTOProductTypeExplodedListToJson(
    List<enums.LoanAccountDetailsDTOProductType>?
        loanAccountDetailsDTOProductType) {
  return loanAccountDetailsDTOProductType?.map((e) => e.value!).join(',') ?? '';
}

List<String> loanAccountDetailsDTOProductTypeListToJson(
    List<enums.LoanAccountDetailsDTOProductType>?
        loanAccountDetailsDTOProductType) {
  if (loanAccountDetailsDTOProductType == null) {
    return [];
  }

  return loanAccountDetailsDTOProductType.map((e) => e.value!).toList();
}

List<enums.LoanAccountDetailsDTOProductType>
    loanAccountDetailsDTOProductTypeListFromJson(
  List? loanAccountDetailsDTOProductType, [
  List<enums.LoanAccountDetailsDTOProductType>? defaultValue,
]) {
  if (loanAccountDetailsDTOProductType == null) {
    return defaultValue ?? [];
  }

  return loanAccountDetailsDTOProductType
      .map((e) => loanAccountDetailsDTOProductTypeFromJson(e.toString()))
      .toList();
}

List<enums.LoanAccountDetailsDTOProductType>?
    loanAccountDetailsDTOProductTypeNullableListFromJson(
  List? loanAccountDetailsDTOProductType, [
  List<enums.LoanAccountDetailsDTOProductType>? defaultValue,
]) {
  if (loanAccountDetailsDTOProductType == null) {
    return defaultValue;
  }

  return loanAccountDetailsDTOProductType
      .map((e) => loanAccountDetailsDTOProductTypeFromJson(e.toString()))
      .toList();
}

String? loanAccountDetailsDTOTypeNullableToJson(
    enums.LoanAccountDetailsDTOType? loanAccountDetailsDTOType) {
  return loanAccountDetailsDTOType?.value;
}

String? loanAccountDetailsDTOTypeToJson(
    enums.LoanAccountDetailsDTOType loanAccountDetailsDTOType) {
  return loanAccountDetailsDTOType.value;
}

enums.LoanAccountDetailsDTOType loanAccountDetailsDTOTypeFromJson(
  Object? loanAccountDetailsDTOType, [
  enums.LoanAccountDetailsDTOType? defaultValue,
]) {
  return enums.LoanAccountDetailsDTOType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsDTOType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.LoanAccountDetailsDTOType.swaggerGeneratedUnknown;
}

enums.LoanAccountDetailsDTOType? loanAccountDetailsDTOTypeNullableFromJson(
  Object? loanAccountDetailsDTOType, [
  enums.LoanAccountDetailsDTOType? defaultValue,
]) {
  if (loanAccountDetailsDTOType == null) {
    return null;
  }
  return enums.LoanAccountDetailsDTOType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          loanAccountDetailsDTOType.toString().toLowerCase()) ??
      defaultValue;
}

String loanAccountDetailsDTOTypeExplodedListToJson(
    List<enums.LoanAccountDetailsDTOType>? loanAccountDetailsDTOType) {
  return loanAccountDetailsDTOType?.map((e) => e.value!).join(',') ?? '';
}

List<String> loanAccountDetailsDTOTypeListToJson(
    List<enums.LoanAccountDetailsDTOType>? loanAccountDetailsDTOType) {
  if (loanAccountDetailsDTOType == null) {
    return [];
  }

  return loanAccountDetailsDTOType.map((e) => e.value!).toList();
}

List<enums.LoanAccountDetailsDTOType> loanAccountDetailsDTOTypeListFromJson(
  List? loanAccountDetailsDTOType, [
  List<enums.LoanAccountDetailsDTOType>? defaultValue,
]) {
  if (loanAccountDetailsDTOType == null) {
    return defaultValue ?? [];
  }

  return loanAccountDetailsDTOType
      .map((e) => loanAccountDetailsDTOTypeFromJson(e.toString()))
      .toList();
}

List<enums.LoanAccountDetailsDTOType>?
    loanAccountDetailsDTOTypeNullableListFromJson(
  List? loanAccountDetailsDTOType, [
  List<enums.LoanAccountDetailsDTOType>? defaultValue,
]) {
  if (loanAccountDetailsDTOType == null) {
    return defaultValue;
  }

  return loanAccountDetailsDTOType
      .map((e) => loanAccountDetailsDTOTypeFromJson(e.toString()))
      .toList();
}

String? scheduleSettingsRepaymentPeriodUnitNullableToJson(
    enums.ScheduleSettingsRepaymentPeriodUnit?
        scheduleSettingsRepaymentPeriodUnit) {
  return scheduleSettingsRepaymentPeriodUnit?.value;
}

String? scheduleSettingsRepaymentPeriodUnitToJson(
    enums.ScheduleSettingsRepaymentPeriodUnit
        scheduleSettingsRepaymentPeriodUnit) {
  return scheduleSettingsRepaymentPeriodUnit.value;
}

enums.ScheduleSettingsRepaymentPeriodUnit
    scheduleSettingsRepaymentPeriodUnitFromJson(
  Object? scheduleSettingsRepaymentPeriodUnit, [
  enums.ScheduleSettingsRepaymentPeriodUnit? defaultValue,
]) {
  return enums.ScheduleSettingsRepaymentPeriodUnit.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              scheduleSettingsRepaymentPeriodUnit?.toString().toLowerCase()) ??
      defaultValue ??
      enums.ScheduleSettingsRepaymentPeriodUnit.swaggerGeneratedUnknown;
}

enums.ScheduleSettingsRepaymentPeriodUnit?
    scheduleSettingsRepaymentPeriodUnitNullableFromJson(
  Object? scheduleSettingsRepaymentPeriodUnit, [
  enums.ScheduleSettingsRepaymentPeriodUnit? defaultValue,
]) {
  if (scheduleSettingsRepaymentPeriodUnit == null) {
    return null;
  }
  return enums.ScheduleSettingsRepaymentPeriodUnit.values.firstWhereOrNull(
          (e) =>
              e.value.toString().toLowerCase() ==
              scheduleSettingsRepaymentPeriodUnit.toString().toLowerCase()) ??
      defaultValue;
}

String scheduleSettingsRepaymentPeriodUnitExplodedListToJson(
    List<enums.ScheduleSettingsRepaymentPeriodUnit>?
        scheduleSettingsRepaymentPeriodUnit) {
  return scheduleSettingsRepaymentPeriodUnit?.map((e) => e.value!).join(',') ??
      '';
}

List<String> scheduleSettingsRepaymentPeriodUnitListToJson(
    List<enums.ScheduleSettingsRepaymentPeriodUnit>?
        scheduleSettingsRepaymentPeriodUnit) {
  if (scheduleSettingsRepaymentPeriodUnit == null) {
    return [];
  }

  return scheduleSettingsRepaymentPeriodUnit.map((e) => e.value!).toList();
}

List<enums.ScheduleSettingsRepaymentPeriodUnit>
    scheduleSettingsRepaymentPeriodUnitListFromJson(
  List? scheduleSettingsRepaymentPeriodUnit, [
  List<enums.ScheduleSettingsRepaymentPeriodUnit>? defaultValue,
]) {
  if (scheduleSettingsRepaymentPeriodUnit == null) {
    return defaultValue ?? [];
  }

  return scheduleSettingsRepaymentPeriodUnit
      .map((e) => scheduleSettingsRepaymentPeriodUnitFromJson(e.toString()))
      .toList();
}

List<enums.ScheduleSettingsRepaymentPeriodUnit>?
    scheduleSettingsRepaymentPeriodUnitNullableListFromJson(
  List? scheduleSettingsRepaymentPeriodUnit, [
  List<enums.ScheduleSettingsRepaymentPeriodUnit>? defaultValue,
]) {
  if (scheduleSettingsRepaymentPeriodUnit == null) {
    return defaultValue;
  }

  return scheduleSettingsRepaymentPeriodUnit
      .map((e) => scheduleSettingsRepaymentPeriodUnitFromJson(e.toString()))
      .toList();
}

String? interestSettingsInterestRateSourceNullableToJson(
    enums.InterestSettingsInterestRateSource?
        interestSettingsInterestRateSource) {
  return interestSettingsInterestRateSource?.value;
}

String? interestSettingsInterestRateSourceToJson(
    enums.InterestSettingsInterestRateSource
        interestSettingsInterestRateSource) {
  return interestSettingsInterestRateSource.value;
}

enums.InterestSettingsInterestRateSource
    interestSettingsInterestRateSourceFromJson(
  Object? interestSettingsInterestRateSource, [
  enums.InterestSettingsInterestRateSource? defaultValue,
]) {
  return enums.InterestSettingsInterestRateSource.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          interestSettingsInterestRateSource?.toString().toLowerCase()) ??
      defaultValue ??
      enums.InterestSettingsInterestRateSource.swaggerGeneratedUnknown;
}

enums.InterestSettingsInterestRateSource?
    interestSettingsInterestRateSourceNullableFromJson(
  Object? interestSettingsInterestRateSource, [
  enums.InterestSettingsInterestRateSource? defaultValue,
]) {
  if (interestSettingsInterestRateSource == null) {
    return null;
  }
  return enums.InterestSettingsInterestRateSource.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          interestSettingsInterestRateSource.toString().toLowerCase()) ??
      defaultValue;
}

String interestSettingsInterestRateSourceExplodedListToJson(
    List<enums.InterestSettingsInterestRateSource>?
        interestSettingsInterestRateSource) {
  return interestSettingsInterestRateSource?.map((e) => e.value!).join(',') ??
      '';
}

List<String> interestSettingsInterestRateSourceListToJson(
    List<enums.InterestSettingsInterestRateSource>?
        interestSettingsInterestRateSource) {
  if (interestSettingsInterestRateSource == null) {
    return [];
  }

  return interestSettingsInterestRateSource.map((e) => e.value!).toList();
}

List<enums.InterestSettingsInterestRateSource>
    interestSettingsInterestRateSourceListFromJson(
  List? interestSettingsInterestRateSource, [
  List<enums.InterestSettingsInterestRateSource>? defaultValue,
]) {
  if (interestSettingsInterestRateSource == null) {
    return defaultValue ?? [];
  }

  return interestSettingsInterestRateSource
      .map((e) => interestSettingsInterestRateSourceFromJson(e.toString()))
      .toList();
}

List<enums.InterestSettingsInterestRateSource>?
    interestSettingsInterestRateSourceNullableListFromJson(
  List? interestSettingsInterestRateSource, [
  List<enums.InterestSettingsInterestRateSource>? defaultValue,
]) {
  if (interestSettingsInterestRateSource == null) {
    return defaultValue;
  }

  return interestSettingsInterestRateSource
      .map((e) => interestSettingsInterestRateSourceFromJson(e.toString()))
      .toList();
}

String? interestSettingsInterestTypeNullableToJson(
    enums.InterestSettingsInterestType? interestSettingsInterestType) {
  return interestSettingsInterestType?.value;
}

String? interestSettingsInterestTypeToJson(
    enums.InterestSettingsInterestType interestSettingsInterestType) {
  return interestSettingsInterestType.value;
}

enums.InterestSettingsInterestType interestSettingsInterestTypeFromJson(
  Object? interestSettingsInterestType, [
  enums.InterestSettingsInterestType? defaultValue,
]) {
  return enums.InterestSettingsInterestType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          interestSettingsInterestType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.InterestSettingsInterestType.swaggerGeneratedUnknown;
}

enums.InterestSettingsInterestType?
    interestSettingsInterestTypeNullableFromJson(
  Object? interestSettingsInterestType, [
  enums.InterestSettingsInterestType? defaultValue,
]) {
  if (interestSettingsInterestType == null) {
    return null;
  }
  return enums.InterestSettingsInterestType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          interestSettingsInterestType.toString().toLowerCase()) ??
      defaultValue;
}

String interestSettingsInterestTypeExplodedListToJson(
    List<enums.InterestSettingsInterestType>? interestSettingsInterestType) {
  return interestSettingsInterestType?.map((e) => e.value!).join(',') ?? '';
}

List<String> interestSettingsInterestTypeListToJson(
    List<enums.InterestSettingsInterestType>? interestSettingsInterestType) {
  if (interestSettingsInterestType == null) {
    return [];
  }

  return interestSettingsInterestType.map((e) => e.value!).toList();
}

List<enums.InterestSettingsInterestType>
    interestSettingsInterestTypeListFromJson(
  List? interestSettingsInterestType, [
  List<enums.InterestSettingsInterestType>? defaultValue,
]) {
  if (interestSettingsInterestType == null) {
    return defaultValue ?? [];
  }

  return interestSettingsInterestType
      .map((e) => interestSettingsInterestTypeFromJson(e.toString()))
      .toList();
}

List<enums.InterestSettingsInterestType>?
    interestSettingsInterestTypeNullableListFromJson(
  List? interestSettingsInterestType, [
  List<enums.InterestSettingsInterestType>? defaultValue,
]) {
  if (interestSettingsInterestType == null) {
    return defaultValue;
  }

  return interestSettingsInterestType
      .map((e) => interestSettingsInterestTypeFromJson(e.toString()))
      .toList();
}

String? intervalUnitNullableToJson(enums.IntervalUnit? intervalUnit) {
  return intervalUnit?.value;
}

String? intervalUnitToJson(enums.IntervalUnit intervalUnit) {
  return intervalUnit.value;
}

enums.IntervalUnit intervalUnitFromJson(
  Object? intervalUnit, [
  enums.IntervalUnit? defaultValue,
]) {
  return enums.IntervalUnit.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          intervalUnit?.toString().toLowerCase()) ??
      defaultValue ??
      enums.IntervalUnit.swaggerGeneratedUnknown;
}

enums.IntervalUnit? intervalUnitNullableFromJson(
  Object? intervalUnit, [
  enums.IntervalUnit? defaultValue,
]) {
  if (intervalUnit == null) {
    return null;
  }
  return enums.IntervalUnit.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          intervalUnit.toString().toLowerCase()) ??
      defaultValue;
}

String intervalUnitExplodedListToJson(List<enums.IntervalUnit>? intervalUnit) {
  return intervalUnit?.map((e) => e.value!).join(',') ?? '';
}

List<String> intervalUnitListToJson(List<enums.IntervalUnit>? intervalUnit) {
  if (intervalUnit == null) {
    return [];
  }

  return intervalUnit.map((e) => e.value!).toList();
}

List<enums.IntervalUnit> intervalUnitListFromJson(
  List? intervalUnit, [
  List<enums.IntervalUnit>? defaultValue,
]) {
  if (intervalUnit == null) {
    return defaultValue ?? [];
  }

  return intervalUnit.map((e) => intervalUnitFromJson(e.toString())).toList();
}

List<enums.IntervalUnit>? intervalUnitNullableListFromJson(
  List? intervalUnit, [
  List<enums.IntervalUnit>? defaultValue,
]) {
  if (intervalUnit == null) {
    return defaultValue;
  }

  return intervalUnit.map((e) => intervalUnitFromJson(e.toString())).toList();
}

// ignore: unused_element
String? _dateToJson(DateTime? date) {
  if (date == null) {
    return null;
  }

  final year = date.year.toString();
  final month = date.month < 10 ? '0${date.month}' : date.month.toString();
  final day = date.day < 10 ? '0${date.day}' : date.day.toString();

  return '$year-$month-$day';
}

class Wrapped<T> {
  final T value;
  const Wrapped.value(this.value);
}
