import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/credit/wio_credit_content_widget.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/easycash/easy_cash_content_widget.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/lending/lending_cubit.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/lending/widget/list_details_item.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/lending/widget/locked_credit_account_alert/locked_credit_account_alert.dart';

const emptyPlaceholder = '-';

class LendingDetailsView extends StatelessWidget {
  final LoanAccountDetails loanAccountDetails;
  final List<CreditApplication>? creditApplicationList;
  final LoanRepayment? loanRepayment;
  final LoanAccount? loanAccount;
  final bool isCreditStatementButtonLoading;

  const LendingDetailsView({
    required this.loanAccountDetails,
    required this.isCreditStatementButtonLoading,
    this.creditApplicationList,
    this.loanRepayment,
    this.loanAccount,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<LendingCubit>();
    final wioCreditAccount =
        loanAccountDetails.loanAccountList.firstWhereOrNull(
      (account) =>
          account.productType == LendingProductType.creditCard ||
          account.productType == LendingProductType.smeCreditCard,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        LockedCreditAccountAlert(
          loanAccounts: loanAccountDetails.loanAccountList,
          repaymentDetails: loanRepayment,
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            const Label(
              model: LabelModel(
                text: 'Credit',
                textStyle: CompanyTextStylePointer.h4,
                color: CompanyColorPointer.primary3,
              ),
            ),
            const Spacer(),
            if (wioCreditAccount != null)
              Button(
                model: ButtonModel(
                  title: 'Credit statement',
                  negative: true,
                  size: ButtonSize.xSmall,
                  theme: ButtonModelTheme.sme,
                  loading: isCreditStatementButtonLoading,
                ),
                onPressed: () => cubit.openCreditStatementDialog(
                  accountId: wioCreditAccount.id,
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        _CreditLimitDistributionWithBar(
          loanAccountDetails: loanAccountDetails,
        ),
        const SizedBox(height: 12),
        _Content(
          loanAccountList: loanAccountDetails.loanAccountList,
        ),
      ],
    );
  }
}

class _Content extends StatefulWidget {
  final List<LoanAccount> loanAccountList;

  const _Content({
    required this.loanAccountList,
  });

  @override
  State<_Content> createState() => _ContentState();
}

class _ContentState extends State<_Content> {
  final List<String> tabNames = [];
  LoanAccount? wioCreditAccount;
  LoanAccount? easyCashAccount;
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    if (widget.loanAccountList.isNotEmpty) {
      wioCreditAccount = widget.loanAccountList.firstWhereOrNull(
        (element) =>
            element.productType == LendingProductType.creditCard ||
            element.productType == LendingProductType.smeCreditCard,
      );
      easyCashAccount = widget.loanAccountList.firstWhereOrNull(
        (element) =>
            element.productType == LendingProductType.easyCash ||
            element.productType == LendingProductType.smeEasyCash,
      );

      if (wioCreditAccount != null) {
        tabNames.add('Wio Credit');
      }
      if (easyCashAccount != null) {
        tabNames.add('Easycash');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.loanAccountList.isNotEmpty
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Tabs(
                TabsModel(
                  tabNames: tabNames,
                  variant: TabsVariant.smallConnected,
                  selectedIndex: _selectedIndex,
                  inactiveTextColor: CompanyColorPointer.surface4,
                  activeBackgroundColor: CompanyColorPointer.primary3,
                  activeTextColor: CompanyColorPointer.surface2,
                ),
                onTabPressed: (index) {
                  setState(() {
                    _selectedIndex = index;
                  });
                },
              ),
              const SizedBox(
                height: 12,
              ),
              if (tabNames[_selectedIndex] == 'Wio Credit' &&
                  wioCreditAccount != null)
                WioCreditContent(loanAccount: wioCreditAccount!)
              else if (tabNames[_selectedIndex] == 'Easycash' &&
                  easyCashAccount != null)
                EasyCashContent(
                  easyCashAccount: easyCashAccount!,
                  wioCreditAccount: wioCreditAccount,
                ),
            ],
          )
        : const SizedBox.shrink();
  }
}

class _CreditLimitDistributionWithBar extends StatelessWidget {
  final LoanAccountDetails loanAccountDetails;

  const _CreditLimitDistributionWithBar({
    required this.loanAccountDetails,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = context.colorStyling;

    final easyCashAccount = loanAccountDetails.loanAccountList.firstWhereOrNull(
      (loanAccount) => [
        LendingProductType.smeEasyCash,
        LendingProductType.easyCash,
      ].contains(loanAccount.productType),
    );

    final creditCardAccount =
        loanAccountDetails.loanAccountList.firstWhereOrNull(
      (loanAccount) => [
        LendingProductType.creditCard,
        LendingProductType.smeCreditCard,
      ].contains(loanAccount.productType),
    );

    final principalBalance = creditCardAccount?.principalBalance ??
        Money.fromNumWithCurrency(0, Currency.aed);
    final holdBalance = creditCardAccount?.holdBalance ??
        Money.fromNumWithCurrency(0, Currency.aed);

    final installmentsAmount =
        loanAccountDetails.creditSummary.creditCardLoanAmount;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface2,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ListDetailsItem(
                title: 'Tot. limit',
                subtitle: loanAccountDetails.creditSummary.totalCreditAmount
                    .withoutCodeFormat(),
                containerPaddingHorizontal: 0,
                containerPaddingVertical: 0,
              ),
              const Spacer(flex: 3),
              SizedBox(
                height: 48,
                child: VerticalDivider(
                  thickness: 2,
                  color: CompanyColorPointer.border6.colorOf(context),
                  width: 0,
                ),
              ),
              const Spacer(),
              ListDetailsItem(
                title: 'Wio Credit Spent',
                subtitle: (principalBalance + holdBalance).withoutCodeFormat(),
                statusDot:
                    const StatusDot(statusType: StatusDotType.defaultType),
                containerPaddingHorizontal: 0,
                containerPaddingVertical: 0,
              ),
              if (easyCashAccount != null) ...[
                const Spacer(flex: 2),
                ListDetailsItem(
                  title: 'E. Cash Borrowed',
                  subtitle:
                      easyCashAccount.principalBalance?.withoutCodeFormat() ??
                          '-',
                  statusDot:
                      const StatusDot(statusType: StatusDotType.inactive),
                  containerPaddingHorizontal: 0,
                  containerPaddingVertical: 0,
                ),
              ],
              if (installmentsAmount != null) ...[
                const Spacer(flex: 2),
                ListDetailsItem(
                  title: 'Installments bal.',
                  subtitle: installmentsAmount.withoutCodeFormat(),
                  statusDot:
                      const StatusDot(statusType: StatusDotType.inactive),
                  containerPaddingHorizontal: 0,
                  containerPaddingVertical: 0,
                ),
              ],
              const Spacer(flex: 2),
              ListDetailsItem(
                title: 'Avl. spend',
                subtitle: loanAccountDetails.creditSummary.availableToSpend
                    .withoutCodeFormat(),
                statusDot: const StatusDot(statusType: StatusDotType.inactive),
                containerPaddingHorizontal: 0,
                containerPaddingVertical: 0,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
