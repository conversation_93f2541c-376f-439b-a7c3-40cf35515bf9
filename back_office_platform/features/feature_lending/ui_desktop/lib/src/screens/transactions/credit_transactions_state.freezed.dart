// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'credit_transactions_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CreditTransactionsState {
  int get currentPageNumber => throw _privateConstructorUsedError;
  CreditTransactionsFilterRequestPayload get filterPayload =>
      throw _privateConstructorUsedError;
  CreditSortingCriteria? get sortingCriteria =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            int currentPageNumber,
            List<CreditTransaction> transactions,
            int totalPageCount,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)
        loaded,
    required TResult Function(
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)
        loading,
    required TResult Function(
            String errorMessage,
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)
        error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            int currentPageNumber,
            List<CreditTransaction> transactions,
            int totalPageCount,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loaded,
    TResult? Function(
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loading,
    TResult? Function(
            String errorMessage,
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            int currentPageNumber,
            List<CreditTransaction> transactions,
            int totalPageCount,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loaded,
    TResult Function(
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loading,
    TResult Function(
            String errorMessage,
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CreditTransactionsStateLoaded value) loaded,
    required TResult Function(_CreditTransactionsStateLoading value) loading,
    required TResult Function(_CreditTransactionsStateError value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CreditTransactionsStateLoaded value)? loaded,
    TResult? Function(_CreditTransactionsStateLoading value)? loading,
    TResult? Function(_CreditTransactionsStateError value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CreditTransactionsStateLoaded value)? loaded,
    TResult Function(_CreditTransactionsStateLoading value)? loading,
    TResult Function(_CreditTransactionsStateError value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of CreditTransactionsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreditTransactionsStateCopyWith<CreditTransactionsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreditTransactionsStateCopyWith<$Res> {
  factory $CreditTransactionsStateCopyWith(CreditTransactionsState value,
          $Res Function(CreditTransactionsState) then) =
      _$CreditTransactionsStateCopyWithImpl<$Res, CreditTransactionsState>;
  @useResult
  $Res call(
      {int currentPageNumber,
      CreditTransactionsFilterRequestPayload filterPayload,
      CreditSortingCriteria? sortingCriteria});

  $CreditTransactionsFilterRequestPayloadCopyWith<$Res> get filterPayload;
}

/// @nodoc
class _$CreditTransactionsStateCopyWithImpl<$Res,
        $Val extends CreditTransactionsState>
    implements $CreditTransactionsStateCopyWith<$Res> {
  _$CreditTransactionsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreditTransactionsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentPageNumber = null,
    Object? filterPayload = null,
    Object? sortingCriteria = freezed,
  }) {
    return _then(_value.copyWith(
      currentPageNumber: null == currentPageNumber
          ? _value.currentPageNumber
          : currentPageNumber // ignore: cast_nullable_to_non_nullable
              as int,
      filterPayload: null == filterPayload
          ? _value.filterPayload
          : filterPayload // ignore: cast_nullable_to_non_nullable
              as CreditTransactionsFilterRequestPayload,
      sortingCriteria: freezed == sortingCriteria
          ? _value.sortingCriteria
          : sortingCriteria // ignore: cast_nullable_to_non_nullable
              as CreditSortingCriteria?,
    ) as $Val);
  }

  /// Create a copy of CreditTransactionsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CreditTransactionsFilterRequestPayloadCopyWith<$Res> get filterPayload {
    return $CreditTransactionsFilterRequestPayloadCopyWith<$Res>(
        _value.filterPayload, (value) {
      return _then(_value.copyWith(filterPayload: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CreditTransactionsStateLoadedImplCopyWith<$Res>
    implements $CreditTransactionsStateCopyWith<$Res> {
  factory _$$CreditTransactionsStateLoadedImplCopyWith(
          _$CreditTransactionsStateLoadedImpl value,
          $Res Function(_$CreditTransactionsStateLoadedImpl) then) =
      __$$CreditTransactionsStateLoadedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int currentPageNumber,
      List<CreditTransaction> transactions,
      int totalPageCount,
      CreditTransactionsFilterRequestPayload filterPayload,
      CreditSortingCriteria? sortingCriteria});

  @override
  $CreditTransactionsFilterRequestPayloadCopyWith<$Res> get filterPayload;
}

/// @nodoc
class __$$CreditTransactionsStateLoadedImplCopyWithImpl<$Res>
    extends _$CreditTransactionsStateCopyWithImpl<$Res,
        _$CreditTransactionsStateLoadedImpl>
    implements _$$CreditTransactionsStateLoadedImplCopyWith<$Res> {
  __$$CreditTransactionsStateLoadedImplCopyWithImpl(
      _$CreditTransactionsStateLoadedImpl _value,
      $Res Function(_$CreditTransactionsStateLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditTransactionsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentPageNumber = null,
    Object? transactions = null,
    Object? totalPageCount = null,
    Object? filterPayload = null,
    Object? sortingCriteria = freezed,
  }) {
    return _then(_$CreditTransactionsStateLoadedImpl(
      currentPageNumber: null == currentPageNumber
          ? _value.currentPageNumber
          : currentPageNumber // ignore: cast_nullable_to_non_nullable
              as int,
      transactions: null == transactions
          ? _value._transactions
          : transactions // ignore: cast_nullable_to_non_nullable
              as List<CreditTransaction>,
      totalPageCount: null == totalPageCount
          ? _value.totalPageCount
          : totalPageCount // ignore: cast_nullable_to_non_nullable
              as int,
      filterPayload: null == filterPayload
          ? _value.filterPayload
          : filterPayload // ignore: cast_nullable_to_non_nullable
              as CreditTransactionsFilterRequestPayload,
      sortingCriteria: freezed == sortingCriteria
          ? _value.sortingCriteria
          : sortingCriteria // ignore: cast_nullable_to_non_nullable
              as CreditSortingCriteria?,
    ));
  }
}

/// @nodoc

class _$CreditTransactionsStateLoadedImpl
    implements CreditTransactionsStateLoaded {
  const _$CreditTransactionsStateLoadedImpl(
      {required this.currentPageNumber,
      required final List<CreditTransaction> transactions,
      required this.totalPageCount,
      required this.filterPayload,
      this.sortingCriteria})
      : _transactions = transactions;

  @override
  final int currentPageNumber;
  final List<CreditTransaction> _transactions;
  @override
  List<CreditTransaction> get transactions {
    if (_transactions is EqualUnmodifiableListView) return _transactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transactions);
  }

  @override
  final int totalPageCount;
  @override
  final CreditTransactionsFilterRequestPayload filterPayload;
  @override
  final CreditSortingCriteria? sortingCriteria;

  @override
  String toString() {
    return 'CreditTransactionsState.loaded(currentPageNumber: $currentPageNumber, transactions: $transactions, totalPageCount: $totalPageCount, filterPayload: $filterPayload, sortingCriteria: $sortingCriteria)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreditTransactionsStateLoadedImpl &&
            (identical(other.currentPageNumber, currentPageNumber) ||
                other.currentPageNumber == currentPageNumber) &&
            const DeepCollectionEquality()
                .equals(other._transactions, _transactions) &&
            (identical(other.totalPageCount, totalPageCount) ||
                other.totalPageCount == totalPageCount) &&
            (identical(other.filterPayload, filterPayload) ||
                other.filterPayload == filterPayload) &&
            (identical(other.sortingCriteria, sortingCriteria) ||
                other.sortingCriteria == sortingCriteria));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentPageNumber,
      const DeepCollectionEquality().hash(_transactions),
      totalPageCount,
      filterPayload,
      sortingCriteria);

  /// Create a copy of CreditTransactionsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreditTransactionsStateLoadedImplCopyWith<
          _$CreditTransactionsStateLoadedImpl>
      get copyWith => __$$CreditTransactionsStateLoadedImplCopyWithImpl<
          _$CreditTransactionsStateLoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            int currentPageNumber,
            List<CreditTransaction> transactions,
            int totalPageCount,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)
        loaded,
    required TResult Function(
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)
        loading,
    required TResult Function(
            String errorMessage,
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)
        error,
  }) {
    return loaded(currentPageNumber, transactions, totalPageCount,
        filterPayload, sortingCriteria);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            int currentPageNumber,
            List<CreditTransaction> transactions,
            int totalPageCount,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loaded,
    TResult? Function(
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loading,
    TResult? Function(
            String errorMessage,
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        error,
  }) {
    return loaded?.call(currentPageNumber, transactions, totalPageCount,
        filterPayload, sortingCriteria);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            int currentPageNumber,
            List<CreditTransaction> transactions,
            int totalPageCount,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loaded,
    TResult Function(
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loading,
    TResult Function(
            String errorMessage,
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(currentPageNumber, transactions, totalPageCount,
          filterPayload, sortingCriteria);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CreditTransactionsStateLoaded value) loaded,
    required TResult Function(_CreditTransactionsStateLoading value) loading,
    required TResult Function(_CreditTransactionsStateError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CreditTransactionsStateLoaded value)? loaded,
    TResult? Function(_CreditTransactionsStateLoading value)? loading,
    TResult? Function(_CreditTransactionsStateError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CreditTransactionsStateLoaded value)? loaded,
    TResult Function(_CreditTransactionsStateLoading value)? loading,
    TResult Function(_CreditTransactionsStateError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class CreditTransactionsStateLoaded
    implements CreditTransactionsState {
  const factory CreditTransactionsStateLoaded(
          {required final int currentPageNumber,
          required final List<CreditTransaction> transactions,
          required final int totalPageCount,
          required final CreditTransactionsFilterRequestPayload filterPayload,
          final CreditSortingCriteria? sortingCriteria}) =
      _$CreditTransactionsStateLoadedImpl;

  @override
  int get currentPageNumber;
  List<CreditTransaction> get transactions;
  int get totalPageCount;
  @override
  CreditTransactionsFilterRequestPayload get filterPayload;
  @override
  CreditSortingCriteria? get sortingCriteria;

  /// Create a copy of CreditTransactionsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreditTransactionsStateLoadedImplCopyWith<
          _$CreditTransactionsStateLoadedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CreditTransactionsStateLoadingImplCopyWith<$Res>
    implements $CreditTransactionsStateCopyWith<$Res> {
  factory _$$CreditTransactionsStateLoadingImplCopyWith(
          _$CreditTransactionsStateLoadingImpl value,
          $Res Function(_$CreditTransactionsStateLoadingImpl) then) =
      __$$CreditTransactionsStateLoadingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int currentPageNumber,
      CreditTransactionsFilterRequestPayload filterPayload,
      CreditSortingCriteria? sortingCriteria});

  @override
  $CreditTransactionsFilterRequestPayloadCopyWith<$Res> get filterPayload;
}

/// @nodoc
class __$$CreditTransactionsStateLoadingImplCopyWithImpl<$Res>
    extends _$CreditTransactionsStateCopyWithImpl<$Res,
        _$CreditTransactionsStateLoadingImpl>
    implements _$$CreditTransactionsStateLoadingImplCopyWith<$Res> {
  __$$CreditTransactionsStateLoadingImplCopyWithImpl(
      _$CreditTransactionsStateLoadingImpl _value,
      $Res Function(_$CreditTransactionsStateLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditTransactionsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentPageNumber = null,
    Object? filterPayload = null,
    Object? sortingCriteria = freezed,
  }) {
    return _then(_$CreditTransactionsStateLoadingImpl(
      currentPageNumber: null == currentPageNumber
          ? _value.currentPageNumber
          : currentPageNumber // ignore: cast_nullable_to_non_nullable
              as int,
      filterPayload: null == filterPayload
          ? _value.filterPayload
          : filterPayload // ignore: cast_nullable_to_non_nullable
              as CreditTransactionsFilterRequestPayload,
      sortingCriteria: freezed == sortingCriteria
          ? _value.sortingCriteria
          : sortingCriteria // ignore: cast_nullable_to_non_nullable
              as CreditSortingCriteria?,
    ));
  }
}

/// @nodoc

class _$CreditTransactionsStateLoadingImpl
    implements _CreditTransactionsStateLoading {
  const _$CreditTransactionsStateLoadingImpl(
      {required this.currentPageNumber,
      required this.filterPayload,
      this.sortingCriteria});

  @override
  final int currentPageNumber;
  @override
  final CreditTransactionsFilterRequestPayload filterPayload;
  @override
  final CreditSortingCriteria? sortingCriteria;

  @override
  String toString() {
    return 'CreditTransactionsState.loading(currentPageNumber: $currentPageNumber, filterPayload: $filterPayload, sortingCriteria: $sortingCriteria)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreditTransactionsStateLoadingImpl &&
            (identical(other.currentPageNumber, currentPageNumber) ||
                other.currentPageNumber == currentPageNumber) &&
            (identical(other.filterPayload, filterPayload) ||
                other.filterPayload == filterPayload) &&
            (identical(other.sortingCriteria, sortingCriteria) ||
                other.sortingCriteria == sortingCriteria));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, currentPageNumber, filterPayload, sortingCriteria);

  /// Create a copy of CreditTransactionsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreditTransactionsStateLoadingImplCopyWith<
          _$CreditTransactionsStateLoadingImpl>
      get copyWith => __$$CreditTransactionsStateLoadingImplCopyWithImpl<
          _$CreditTransactionsStateLoadingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            int currentPageNumber,
            List<CreditTransaction> transactions,
            int totalPageCount,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)
        loaded,
    required TResult Function(
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)
        loading,
    required TResult Function(
            String errorMessage,
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)
        error,
  }) {
    return loading(currentPageNumber, filterPayload, sortingCriteria);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            int currentPageNumber,
            List<CreditTransaction> transactions,
            int totalPageCount,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loaded,
    TResult? Function(
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loading,
    TResult? Function(
            String errorMessage,
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        error,
  }) {
    return loading?.call(currentPageNumber, filterPayload, sortingCriteria);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            int currentPageNumber,
            List<CreditTransaction> transactions,
            int totalPageCount,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loaded,
    TResult Function(
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loading,
    TResult Function(
            String errorMessage,
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(currentPageNumber, filterPayload, sortingCriteria);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CreditTransactionsStateLoaded value) loaded,
    required TResult Function(_CreditTransactionsStateLoading value) loading,
    required TResult Function(_CreditTransactionsStateError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CreditTransactionsStateLoaded value)? loaded,
    TResult? Function(_CreditTransactionsStateLoading value)? loading,
    TResult? Function(_CreditTransactionsStateError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CreditTransactionsStateLoaded value)? loaded,
    TResult Function(_CreditTransactionsStateLoading value)? loading,
    TResult Function(_CreditTransactionsStateError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _CreditTransactionsStateLoading
    implements CreditTransactionsState {
  const factory _CreditTransactionsStateLoading(
          {required final int currentPageNumber,
          required final CreditTransactionsFilterRequestPayload filterPayload,
          final CreditSortingCriteria? sortingCriteria}) =
      _$CreditTransactionsStateLoadingImpl;

  @override
  int get currentPageNumber;
  @override
  CreditTransactionsFilterRequestPayload get filterPayload;
  @override
  CreditSortingCriteria? get sortingCriteria;

  /// Create a copy of CreditTransactionsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreditTransactionsStateLoadingImplCopyWith<
          _$CreditTransactionsStateLoadingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CreditTransactionsStateErrorImplCopyWith<$Res>
    implements $CreditTransactionsStateCopyWith<$Res> {
  factory _$$CreditTransactionsStateErrorImplCopyWith(
          _$CreditTransactionsStateErrorImpl value,
          $Res Function(_$CreditTransactionsStateErrorImpl) then) =
      __$$CreditTransactionsStateErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String errorMessage,
      int currentPageNumber,
      CreditTransactionsFilterRequestPayload filterPayload,
      CreditSortingCriteria? sortingCriteria});

  @override
  $CreditTransactionsFilterRequestPayloadCopyWith<$Res> get filterPayload;
}

/// @nodoc
class __$$CreditTransactionsStateErrorImplCopyWithImpl<$Res>
    extends _$CreditTransactionsStateCopyWithImpl<$Res,
        _$CreditTransactionsStateErrorImpl>
    implements _$$CreditTransactionsStateErrorImplCopyWith<$Res> {
  __$$CreditTransactionsStateErrorImplCopyWithImpl(
      _$CreditTransactionsStateErrorImpl _value,
      $Res Function(_$CreditTransactionsStateErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditTransactionsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? errorMessage = null,
    Object? currentPageNumber = null,
    Object? filterPayload = null,
    Object? sortingCriteria = freezed,
  }) {
    return _then(_$CreditTransactionsStateErrorImpl(
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      currentPageNumber: null == currentPageNumber
          ? _value.currentPageNumber
          : currentPageNumber // ignore: cast_nullable_to_non_nullable
              as int,
      filterPayload: null == filterPayload
          ? _value.filterPayload
          : filterPayload // ignore: cast_nullable_to_non_nullable
              as CreditTransactionsFilterRequestPayload,
      sortingCriteria: freezed == sortingCriteria
          ? _value.sortingCriteria
          : sortingCriteria // ignore: cast_nullable_to_non_nullable
              as CreditSortingCriteria?,
    ));
  }
}

/// @nodoc

class _$CreditTransactionsStateErrorImpl
    implements _CreditTransactionsStateError {
  const _$CreditTransactionsStateErrorImpl(
      {required this.errorMessage,
      required this.currentPageNumber,
      required this.filterPayload,
      this.sortingCriteria});

  @override
  final String errorMessage;
  @override
  final int currentPageNumber;
  @override
  final CreditTransactionsFilterRequestPayload filterPayload;
  @override
  final CreditSortingCriteria? sortingCriteria;

  @override
  String toString() {
    return 'CreditTransactionsState.error(errorMessage: $errorMessage, currentPageNumber: $currentPageNumber, filterPayload: $filterPayload, sortingCriteria: $sortingCriteria)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreditTransactionsStateErrorImpl &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.currentPageNumber, currentPageNumber) ||
                other.currentPageNumber == currentPageNumber) &&
            (identical(other.filterPayload, filterPayload) ||
                other.filterPayload == filterPayload) &&
            (identical(other.sortingCriteria, sortingCriteria) ||
                other.sortingCriteria == sortingCriteria));
  }

  @override
  int get hashCode => Object.hash(runtimeType, errorMessage, currentPageNumber,
      filterPayload, sortingCriteria);

  /// Create a copy of CreditTransactionsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreditTransactionsStateErrorImplCopyWith<
          _$CreditTransactionsStateErrorImpl>
      get copyWith => __$$CreditTransactionsStateErrorImplCopyWithImpl<
          _$CreditTransactionsStateErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            int currentPageNumber,
            List<CreditTransaction> transactions,
            int totalPageCount,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)
        loaded,
    required TResult Function(
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)
        loading,
    required TResult Function(
            String errorMessage,
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)
        error,
  }) {
    return error(
        errorMessage, currentPageNumber, filterPayload, sortingCriteria);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            int currentPageNumber,
            List<CreditTransaction> transactions,
            int totalPageCount,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loaded,
    TResult? Function(
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loading,
    TResult? Function(
            String errorMessage,
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        error,
  }) {
    return error?.call(
        errorMessage, currentPageNumber, filterPayload, sortingCriteria);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            int currentPageNumber,
            List<CreditTransaction> transactions,
            int totalPageCount,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loaded,
    TResult Function(
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        loading,
    TResult Function(
            String errorMessage,
            int currentPageNumber,
            CreditTransactionsFilterRequestPayload filterPayload,
            CreditSortingCriteria? sortingCriteria)?
        error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(
          errorMessage, currentPageNumber, filterPayload, sortingCriteria);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CreditTransactionsStateLoaded value) loaded,
    required TResult Function(_CreditTransactionsStateLoading value) loading,
    required TResult Function(_CreditTransactionsStateError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CreditTransactionsStateLoaded value)? loaded,
    TResult? Function(_CreditTransactionsStateLoading value)? loading,
    TResult? Function(_CreditTransactionsStateError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CreditTransactionsStateLoaded value)? loaded,
    TResult Function(_CreditTransactionsStateLoading value)? loading,
    TResult Function(_CreditTransactionsStateError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _CreditTransactionsStateError
    implements CreditTransactionsState {
  const factory _CreditTransactionsStateError(
          {required final String errorMessage,
          required final int currentPageNumber,
          required final CreditTransactionsFilterRequestPayload filterPayload,
          final CreditSortingCriteria? sortingCriteria}) =
      _$CreditTransactionsStateErrorImpl;

  String get errorMessage;
  @override
  int get currentPageNumber;
  @override
  CreditTransactionsFilterRequestPayload get filterPayload;
  @override
  CreditSortingCriteria? get sortingCriteria;

  /// Create a copy of CreditTransactionsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreditTransactionsStateErrorImplCopyWith<
          _$CreditTransactionsStateErrorImpl>
      get copyWith => throw _privateConstructorUsedError;
}
