import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_app_common_ui_desktop/feature_app_common_desktop_ui.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui_desktop/src/common/data.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/installments/installments_dashboard/widgets/section_container.dart';

enum _Tab {
  payments,
  upcoming,
}

class InstallmentsPaymentTracker extends StatefulWidget {
  final Data<CurrentAutoPayment> autoPaymentDetails;
  final InstallmentAccount account;

  const InstallmentsPaymentTracker({
    required this.autoPaymentDetails,
    required this.account,
    super.key,
  });

  @override
  State<InstallmentsPaymentTracker> createState() =>
      _InstallmentsPaymentTrackerState();
}

class _InstallmentsPaymentTrackerState
    extends State<InstallmentsPaymentTracker> {
  final _orderTabs = [
    _Tab.payments,
  ];

  var _selectedTab = _Tab.payments;

  @override
  void initState() {
    super.initState();
    final isInstallmentActive = widget.account.isActive;
    if (isInstallmentActive != null && isInstallmentActive) {
      _orderTabs.add(_Tab.upcoming);
    }

    _selectedTab = _orderTabs.first;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UnderlineTabBar(
          model: UnderlineTabsModel(
            selectedIndex: _orderTabs.indexWhere((tab) => tab == _selectedTab),
            tabItems: _orderTabs
                .map(
                  (tab) => UnderlineTabItem(
                    label: tab.tabName,
                  ),
                )
                .toList(),
            activeTextColor: CompanyColorPointer.primary3,
          ),
          onTabPressed: (selectedIndex) => setState(() {
            _selectedTab = _orderTabs[selectedIndex];
          }),
        ),
        SectionContainer(
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 200),
            key: ValueKey(_selectedTab),
            child: switch (_selectedTab) {
              _Tab.payments => _PaidInstallmentsView(
                  repaymentData: widget.autoPaymentDetails,
                  account: widget.account,
                ),
              _Tab.upcoming => _UpcomingPaymentsView(
                  autoPaymentData: widget.autoPaymentDetails,
                ),
            },
          ),
        ),
      ],
    );
  }
}

class _PaidInstallmentsView extends StatelessWidget {
  final Data<CurrentAutoPayment> repaymentData;
  final InstallmentAccount account;

  const _PaidInstallmentsView({
    required this.repaymentData,
    required this.account,
  });

  @override
  Widget build(BuildContext context) {
    const unavailableError = SizedBox(
      height: 500,
      width: double.infinity,
      child: Center(
        child: GenericError(
          GenericErrorModel(
            title: 'Paid installments tracker is unavailable for this '
                'installment at this time',
            subtitle: '',
          ),
        ),
      ),
    );

    return repaymentData.map(
      fetching: (_) => const _CommonSpinner(),
      available: (data) {
        final dueAmount = data.data.totalDueAmountIncludingOverdue;

        final hasOverdueInstallments =
            data.data.delinquentAccountDetails != null;

        final nextScheduledInstallmentAmount =
            data.data.schedule?.nextInstallment?.installment?.amount;
        return CompanyTracker(
          CompanyTrackerModel(
            steps: [
              CompanyTrackerStepModel.fromWidget(
                state: CompanyTrackerStateEnum.passed,
                stepWidget: _PaymentsHistory(
                  repaymentDetails: data.data,
                ),
              ),
              if (account.dueDate != null)
                CompanyTrackerStepModel.fromWidget(
                  state: CompanyTrackerStateEnum.pending,
                  stepWidget: Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _UpcomingPaymentSummaryStep(
                          dueDate: account.dueDate,
                          dueAmount: dueAmount,
                          principalDueAmount:
                              data.data.totalPrincipalDueAmountIncludingOverdue,
                          interestDueAmount:
                              data.data.totalInterestDueAmountIncludingOverdue,
                          feeDueAmount:
                              data.data.totalFeeDueAmountIncludingOverdue,
                          hasOverdueInstallments: hasOverdueInstallments,
                          scheduledPrincipalDueAmount:
                              nextScheduledInstallmentAmount?.principal,
                          scheduledInterestDueAmount:
                              nextScheduledInstallmentAmount?.interest,
                          scheduledFeeDueAmount:
                              nextScheduledInstallmentAmount?.fee,
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        );
      },
      error: (_) => unavailableError,
    );
  }
}

class _PaymentsHistory extends StatefulWidget {
  final CurrentAutoPayment repaymentDetails;

  const _PaymentsHistory({
    required this.repaymentDetails,
  });

  @override
  State<_PaymentsHistory> createState() => _PaymentsHistoryState();
}

class _PaymentsHistoryState extends State<_PaymentsHistory> {
  var _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final allInstallments =
        (widget.repaymentDetails.schedule?.installmentSummary ?? [])
            .where((item) => item.dueDate != null)
            .toList();

    // Show all past installments, not just paid ones
    final pastInstallments = allInstallments
        .where(
          (item) =>
              item.dueDate != null && item.dueDate!.isBefore(DateTime.now()),
        )
        .toList();

    final lastPayment =
        pastInstallments.isNotEmpty ? pastInstallments.last : null;
    final totalAmountPaid = lastPayment?.totalInstallmentAmountPaid;
    final totalPrincipalPaid = totalAmountPaid?.principal;
    final totalFeePaid = totalAmountPaid?.fee;
    final totalInterestPaid = totalAmountPaid?.interest;
    final values = [
      totalPrincipalPaid,
      totalFeePaid,
      totalInterestPaid,
    ];
    final nonNullValues = values.whereType<Money>();
    final totalDueLastPayment = nonNullValues.isEmpty
        ? '-'
        : nonNullValues
            .reduce((value, element) => value + element)
            .orMoneyNotAvailable;

    final hasOverduePayments =
        widget.repaymentDetails.delinquentAccountDetails != null;

    return Theme(
      data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
      child: SectionContainer(
        backgroundColor: CompanyColorPointer.secondary6,
        padding: const EdgeInsets.symmetric(
          horizontal: 8.0,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ExpansionTile(
              onExpansionChanged: (isOpen) => setState(() {
                _isExpanded = isOpen;
              }),
              tilePadding: EdgeInsets.zero,
              title: const Label(
                model: LabelModel(
                  text: 'Installments Paid',
                  color: CompanyColorPointer.primary3,
                  textStyle: CompanyTextStylePointer.b2,
                ),
              ),
              children: pastInstallments.isEmpty
                  ? [
                      const Label(
                        model: LabelModel(
                          text: 'No installments paid yet',
                        ),
                      ),
                      const SizedBox(height: 16.0),
                    ]
                  : [
                      CompanyTracker(
                        CompanyTrackerModel(
                          steps: pastInstallments.mapIndexed(
                            (index, item) {
                              final principalAmount =
                                  item.installmentAmount?.principal;
                              final interestAmount =
                                  item.installmentAmount?.interest;
                              final feeAmount = item.installmentAmount?.fee;
                              final amounts = [
                                principalAmount,
                                interestAmount,
                                feeAmount,
                              ];
                              final nonNullAmounts = amounts.whereType<Money>();
                              final totalAmount = nonNullAmounts.isEmpty
                                  ? null
                                  : nonNullAmounts.reduce(
                                      (value, element) => value + element,
                                    );
                              final missed = item.isPast != null &&
                                  item.isPast! &&
                                  item.paid == false;
                              final paid = item.isPast != null &&
                                  item.isPast! &&
                                  item.paid != null &&
                                  item.paid!;
                              final tagModel = missed
                                  ? const CompanyLabelModel.v2(
                                      text: 'Installment Missed',
                                      icon: CompanyLabelIcon.leading(
                                        GraphicAssetPointer.icon(
                                          CompanyIconPointer.failure,
                                        ),
                                      ),
                                      status: CompanyLabelStatus.negative,
                                      boxed: true,
                                    )
                                  : paid
                                      ? const CompanyLabelModel.v2(
                                          text: 'Installment Paid',
                                          icon: CompanyLabelIcon.leading(
                                            GraphicAssetPointer.icon(
                                              CompanyIconPointer.circle_success,
                                            ),
                                          ),
                                          status: CompanyLabelStatus.positive,
                                          boxed: true,
                                        )
                                      : null;
                              return CompanyTrackerStepModel.fromWidget(
                                stepNumberFormattingStyle:
                                    CompanyTrackerStepNumberFormattingType
                                        .normal,
                                state: paid
                                    ? CompanyTrackerStateEnum.passed
                                    : missed
                                        ? CompanyTrackerStateEnum.cancel
                                        : CompanyTrackerStateEnum.pending,
                                stepWidget: _UpcomingPaymentDetail(
                                  principalAmount: principalAmount,
                                  interestAmount: interestAmount,
                                  feeAmount: feeAmount,
                                  totalAmount: totalAmount,
                                  dueDate: item.dueDate,
                                  tagModel: tagModel,
                                ),
                              );
                            },
                          ).toList(),
                        ),
                      ),
                    ],
            ),
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 250),
              child: _isExpanded || lastPayment == null
                  ? const SizedBox.shrink()
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Label(
                          model: LabelModel(
                            text: lastPayment.dueDate == null
                                ? ''
                                : lastPayment
                                    .dueDate!.formattedDateOrNotAvailable,
                            color: CompanyColorPointer.secondary4,
                            textStyle: CompanyTextStylePointer.b4,
                          ),
                        ),
                        const SizedBox(height: 4.0),
                        Label(
                          model: LabelModel(
                            text: 'Last installment paid: $totalDueLastPayment',
                            color: CompanyColorPointer.primary3,
                            textStyle: CompanyTextStylePointer.b3,
                          ),
                        ),
                        const SizedBox(height: 8),
                        if (hasOverduePayments)
                          const CompanyLabel(
                            CompanyLabelModel.v2(
                              text: 'Missed',
                              status: CompanyLabelStatus.negative,
                              boxed: true,
                            ),
                          )
                        else
                          const CompanyLabel(
                            CompanyLabelModel.v2(
                              text: 'Paid',
                              status: CompanyLabelStatus.positive,
                              boxed: true,
                            ),
                          ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: _BoxLabelValue(
                                label: 'Principal',
                                value: totalPrincipalPaid.orMoneyNotAvailable,
                              ),
                            ),
                            const SizedBox(width: 8.0),
                            Expanded(
                              child: _BoxLabelValue(
                                label: 'Interest',
                                value: totalInterestPaid.orMoneyNotAvailable,
                              ),
                            ),
                            if (totalFeePaid == null ||
                                totalFeePaid.isZero) ...[
                              const SizedBox(width: 8.0),
                              Expanded(
                                child: _BoxLabelValue(
                                  label: 'Fee',
                                  value: totalFeePaid.orMoneyNotAvailable,
                                ),
                              ),
                            ],
                          ],
                        ),
                        const SizedBox(height: 12.0),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

class _BoxLabelValue extends StatelessWidget {
  final String label;
  final String value;

  const _BoxLabelValue({
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: CompanyColorPointer.background3.colorOf(context),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Label(
              model: LabelModel(
                text: label,
                color: CompanyColorPointer.secondary4,
                textStyle: CompanyTextStylePointer.b3,
              ),
            ),
            const SizedBox(height: 4),
            Label(
              model: LabelModel(
                text: value,
                color: CompanyColorPointer.primary3,
                textStyle: CompanyTextStylePointer.b3,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _UpcomingPaymentSummaryStep extends StatelessWidget {
  final DateTime? dueDate;
  final Money? dueAmount;
  final Money? principalDueAmount;
  final Money? interestDueAmount;
  final Money? feeDueAmount;
  final Money? scheduledPrincipalDueAmount;
  final Money? scheduledInterestDueAmount;
  final Money? scheduledFeeDueAmount;
  final bool? hasOverdueInstallments;

  const _UpcomingPaymentSummaryStep({
    required this.dueDate,
    required this.dueAmount,
    required this.principalDueAmount,
    required this.interestDueAmount,
    required this.feeDueAmount,
    required this.scheduledPrincipalDueAmount,
    required this.scheduledInterestDueAmount,
    required this.scheduledFeeDueAmount,
    this.hasOverdueInstallments,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Label(
          model: LabelModel(
            text: dueDate.formattedDateOrNotAvailable,
            color: CompanyColorPointer.secondary4,
            textStyle: CompanyTextStylePointer.b4,
          ),
        ),
        const SizedBox(height: 4.0),
        Label(
          model: LabelModel(
            text: 'Total Due - ${dueAmount.orMoneyNotAvailable}',
            color: CompanyColorPointer.primary3,
            textStyle: CompanyTextStylePointer.b3,
          ),
        ),
        _ScheduleMoneyItem(
          label: 'Principal',
          amount: principalDueAmount,
        ),
        _ScheduleMoneyItem(
          label: 'Interest',
          amount: interestDueAmount,
        ),
        if (!feeDueAmount.isNullOrZero)
          _ScheduleMoneyItem(
            label: 'Fee',
            amount: feeDueAmount,
          ),
        if (hasOverdueInstallments != null && hasOverdueInstallments!) ...[
          _ScheduleMoneyItem(
            label: 'Overdue Principal Amount',
            amount: principalDueAmount! - scheduledPrincipalDueAmount!,
          ),
          _ScheduleMoneyItem(
            label: 'Overdue Interest Amount',
            amount: interestDueAmount! - scheduledInterestDueAmount!,
          ),
          if (!scheduledFeeDueAmount.isNullOrZero && !feeDueAmount.isNullOrZero)
            _ScheduleMoneyItem(
              label: 'Overdue Fee Amount',
              amount: feeDueAmount! - scheduledFeeDueAmount!,
            ),
        ],
        if (dueDate != null)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: SizedBox(
              height: 24.0,
              child: CompanyLabel(
                CompanyLabelModel.v2(
                  text: hasOverdueInstallments == null ||
                          hasOverdueInstallments == false
                      ? dueDate!.getRelativeDateLabel()
                      : 'Overdue',
                  icon:
                      hasOverdueInstallments != null && hasOverdueInstallments!
                          ? const CompanyLabelIcon.leading(
                              GraphicAssetPointer.icon(
                                CompanyIconPointer.time,
                              ),
                            )
                          : null,
                  status: hasOverdueInstallments == null ||
                          hasOverdueInstallments == false
                      ? CompanyLabelStatus.partial
                      : CompanyLabelStatus.negative,
                  boxed: true,
                ),
              ),
            ),
          ),
        const SizedBox(height: 16.0),
      ],
    );
  }
}

class _CommonSpinner extends StatelessWidget {
  const _CommonSpinner();

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 300,
      width: double.infinity,
      child: Center(
        child: Spinner(
          model: SpinnerModel(
            type: SpinnerType.primary,
          ),
        ),
      ),
    );
  }
}

extension on _Tab {
  String get tabName {
    return switch (this) {
      _Tab.payments => 'Installment payments',
      _Tab.upcoming => 'Upcoming',
    };
  }
}

class _UpcomingPaymentsView extends StatelessWidget {
  final Data<CurrentAutoPayment> autoPaymentData;

  const _UpcomingPaymentsView({
    required this.autoPaymentData,
  });

  @override
  Widget build(BuildContext context) {
    const unavailableError = SizedBox(
      height: 500,
      width: double.infinity,
      child: Center(
        child: GenericError(
          GenericErrorModel(
            title: 'Upcoming payment tracker is unavailable for this '
                'installment at this time',
            subtitle: '',
          ),
        ),
      ),
    );

    return autoPaymentData.map(
      fetching: (_) => const _CommonSpinner(),
      available: (data) {
        final effectiveScheduleDetails = data.data.schedule;

        if (effectiveScheduleDetails == null) {
          return unavailableError;
        }

        final schedule = effectiveScheduleDetails.installmentSummary;

        if (schedule == null || schedule.isEmpty) {
          return unavailableError;
        }

        return CompanyTracker(
          CompanyTrackerModel(
            steps: schedule
                .where(
              (scheduleItem) =>
                  scheduleItem.dueDate != null &&
                  scheduleItem.dueDate!.isAfter(DateTime.now()),
            )
                .mapIndexed(
              (index, scheduleItem) {
                final lastPaidIndex = schedule
                    .lastIndexWhere((item) => item.paid != null && item.paid!);

                final missedInstallments = schedule
                    .where(
                      (item) =>
                          item.isPast != null &&
                          item.isPast! &&
                          item.paid == false &&
                          (lastPaidIndex == -1 ||
                              schedule.indexOf(item) > lastPaidIndex),
                    )
                    .toList();

                var principalAmount = scheduleItem.installmentAmount?.principal;
                var interestAmount = scheduleItem.installmentAmount?.interest;
                var feeAmount = scheduleItem.installmentAmount?.fee;

                if (index == 0 && missedInstallments.isNotEmpty) {
                  principalAmount =
                      data.data.totalPrincipalDueAmountIncludingOverdue;
                  interestAmount =
                      data.data.totalInterestDueAmountIncludingOverdue;
                  feeAmount = data.data.totalFeeDueAmountIncludingOverdue;
                }
                final amounts = [
                  principalAmount,
                  interestAmount,
                  feeAmount,
                ];
                final nonNullAmounts = amounts.whereType<Money>();
                final totalAmount = nonNullAmounts.isEmpty
                    ? null
                    : nonNullAmounts
                        .reduce((value, element) => value + element);

                final tagModel = index != 0
                    ? null
                    : missedInstallments.isNotEmpty
                        ? const CompanyLabelModel.v2(
                            text: 'Due incl. missed',
                            icon: CompanyLabelIcon.leading(
                              GraphicAssetPointer.icon(
                                CompanyIconPointer.time,
                              ),
                            ),
                            status: CompanyLabelStatus.partial,
                            boxed: true,
                          )
                        : CompanyLabelModel.v2(
                            text: scheduleItem.dueDate!.getRelativeDateLabel(),
                            icon: const CompanyLabelIcon.leading(
                              GraphicAssetPointer.icon(
                                CompanyIconPointer.time,
                              ),
                            ),
                            status: CompanyLabelStatus.partial,
                            boxed: true,
                          );

                final nextScheduledInstallmentAmount =
                    data.data.schedule?.nextInstallment?.installment?.amount;

                return CompanyTrackerStepModel.fromWidget(
                  state: index == 0
                      ? CompanyTrackerStateEnum.pending
                      : CompanyTrackerStateEnum.upComing,
                  stepWidget: _UpcomingPaymentDetail(
                    principalAmount: principalAmount,
                    interestAmount: interestAmount,
                    feeAmount: feeAmount,
                    totalAmount: totalAmount,
                    dueDate: scheduleItem.dueDate,
                    tagModel: tagModel,
                    hasOverdueInstallments: missedInstallments.isNotEmpty ||
                        (index == 0 && missedInstallments.isNotEmpty),
                    scheduledPrincipalDueAmount:
                        nextScheduledInstallmentAmount?.principal,
                    scheduledInterestDueAmount:
                        nextScheduledInstallmentAmount?.interest,
                    scheduledFeeDueAmount: nextScheduledInstallmentAmount?.fee,
                  ),
                );
              },
            ).toList(),
          ),
        );
      },
      error: (_) => unavailableError,
    );
  }
}

class _UpcomingPaymentDetail extends StatelessWidget {
  final Money? totalAmount;
  final Money? principalAmount;
  final Money? interestAmount;
  final Money? feeAmount;
  final DateTime? dueDate;
  final CompanyLabelModel? tagModel;
  final bool? hasOverdueInstallments;
  final Money? scheduledPrincipalDueAmount;
  final Money? scheduledInterestDueAmount;
  final Money? scheduledFeeDueAmount;

  const _UpcomingPaymentDetail({
    required this.totalAmount,
    required this.principalAmount,
    required this.interestAmount,
    required this.feeAmount,
    required this.dueDate,
    this.tagModel,
    this.hasOverdueInstallments,
    this.scheduledPrincipalDueAmount,
    this.scheduledInterestDueAmount,
    this.scheduledFeeDueAmount,
  });

  Money? get _totalAmount {
    final values = [
      principalAmount,
      interestAmount,
      feeAmount,
    ];

    final nonNullValues = values.whereType<Money>();
    if (nonNullValues.isEmpty) return null;
    final currency = nonNullValues.first.currency;
    var total = Money.fromNumWithCurrency(0, currency);
    for (final amount in nonNullValues) {
      total += amount;
    }
    return total;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Label(
          model: LabelModel(
            text: dueDate == null ? '' : dueDate!.formattedDateOrNotAvailable,
            color: CompanyColorPointer.secondary4,
            textStyle: CompanyTextStylePointer.b4,
          ),
        ),
        const SizedBox(height: 4.0),
        Label(
          model: LabelModel(
            text: 'Total Due - ${_totalAmount?.orMoneyNotAvailable ?? '-'}',
            color: CompanyColorPointer.primary3,
            textStyle: CompanyTextStylePointer.b3,
          ),
        ),
        _ScheduleMoneyItem(
          label: 'Principal',
          amount: principalAmount,
        ),
        _ScheduleMoneyItem(
          label: 'Interest',
          amount: interestAmount,
        ),
        if (!(feeAmount).isNullOrZero)
          _ScheduleMoneyItem(
            label: 'Fee',
            amount: feeAmount,
          ),
        if (hasOverdueInstallments != null && hasOverdueInstallments!) ...[
          _ScheduleMoneyItem(
            label: 'Overdue Principal Amount',
            amount: principalAmount! - scheduledPrincipalDueAmount!,
          ),
          _ScheduleMoneyItem(
            label: 'Overdue Interest Amount',
            amount: interestAmount! - scheduledInterestDueAmount!,
          ),
          if (!scheduledFeeDueAmount.isNullOrZero && !feeAmount.isNullOrZero)
            _ScheduleMoneyItem(
              label: 'Overdue Fee Amount',
              amount: feeAmount! - scheduledFeeDueAmount!,
            ),
        ],
        if (tagModel != null)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: SizedBox(
              height: 24.0,
              child: CompanyLabel(tagModel!),
            ),
          ),
        const SizedBox(height: 16.0),
      ],
    );
  }
}

extension on DateTime {
  String getRelativeDateLabel() {
    if (_isDueToday(this)) {
      return 'Due today';
    } else if (_isDueTomorrow(this)) {
      return 'Due tomorrow';
    } else {
      final daysToDueDate = _calculateDaysToDueDate(this);
      return 'Due in $daysToDueDate days';
    }
  }

  int _calculateDaysToDueDate(DateTime dueDate) {
    final currentTime = DateTime.now();
    final now = DateTime(currentTime.year, currentTime.month, currentTime.day);
    return dueDate.difference(now).inDays;
  }

  bool _isDueToday(DateTime dueDate) {
    return dueDate.isToday();
  }

  bool _isDueTomorrow(DateTime dueDate) {
    return dueDate.isTomorrow();
  }
}

extension on Money? {
  bool get isNullOrZero {
    return this == null || this!.isZero;
  }
}

class _ScheduleMoneyItem extends StatelessWidget {
  final String label;
  final Money? amount;

  const _ScheduleMoneyItem({
    required this.label,
    required this.amount,
  });

  @override
  Widget build(BuildContext context) {
    return Label(
      model: LabelModel(
        text: '$label - ${amount?.orMoneyNotAvailable ?? '-'}',
        color: CompanyColorPointer.secondary4,
        textStyle: CompanyTextStylePointer.b4,
      ),
    );
  }
}
