import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_app_common_ui_desktop/feature_app_common_desktop_ui.dart';
import 'package:wio_feature_lending_api/domain/model/installment_account.dart';
import 'package:wio_feature_lending_api/domain/model/loan_repayment.dart';
import 'package:wio_feature_lending_ui_desktop/src/common/data.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/installments/installments_dashboard/widgets/section_container.dart';

class InstallmentAccountDetails extends StatelessWidget {
  final InstallmentAccount account;
  final Data<LoanRepayment> repaymentDetails;

  const InstallmentAccountDetails({
    required this.account,
    required this.repaymentDetails,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final transaction = account.transaction;
    final vendor = transaction?.merchantName;
    final transactionAmount = transaction?.amount;
    final paymentDetails = account.paymentDetails;
    final totalPayments = paymentDetails?.totalInstallments;

    final repaymentSchedule = repaymentDetails.dataOrNull?.repaymentSchedule;
    final schedule = repaymentSchedule?.items;

    return SectionContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DetailsContainer(
            enableCopyAll: false,
            detailLayouts: [
              DetailLayoutType.doubleDetail(
                firstDetail: DetailModel.simple(
                  label: 'Vendor',
                  value: vendor.orNotAvailable,
                ),
                secondDetail: DetailModel.simple(
                  label: 'Total credit amount',
                  value: transactionAmount.orMoneyNotAvailable,
                ),
              ),
              DetailLayoutType.doubleDetail(
                firstDetail: DetailModel.simple(
                  label: 'Activation Date',
                  value: repaymentSchedule?.specDetails?.startDate
                          .formattedDateOrNotAvailable.orNotAvailable ??
                      '-',
                  isLoading: repaymentDetails.isFetching,
                ),
                secondDetail: DetailModel.simple(
                  label: 'End Date',
                  value: schedule == null || schedule.isEmpty
                      ? '-'
                      : schedule.last.date.formattedDateOrNotAvailable,
                  isLoading: repaymentDetails.isFetching,
                ),
              ),
              DetailLayoutType.doubleDetail(
                firstDetail: const DetailModel.simple(
                  label: 'Loan Type',
                  value: 'Installments',
                ),
                secondDetail: DetailModel.simple(
                  label: 'Tenure',
                  value: totalPayments == null ? '-' : '$totalPayments months',
                ),
              ),
            ],
          ),
          if (paymentDetails != null)
            SectionContainer(
              padding: EdgeInsets.zero,
              backgroundColor: CompanyColorPointer.surface7,
              child: _InstallmentPaymentDetails(
                paymentDetails: paymentDetails,
                repaymentDetails: repaymentDetails,
              ),
            ),
        ],
      ),
    );
  }
}

class _InstallmentPaymentDetails extends StatelessWidget {
  final InstallmentPaymentDetails paymentDetails;
  final Data<LoanRepayment> repaymentDetails;

  const _InstallmentPaymentDetails({
    required this.paymentDetails,
    required this.repaymentDetails,
  });

  @override
  Widget build(BuildContext context) {
    final installmentsPaid = paymentDetails.installmentsPaid;
    final totalInstallments = paymentDetails.totalInstallments;

    int? unpaidInstallments;

    if (installmentsPaid != null && totalInstallments != null) {
      unpaidInstallments = totalInstallments - installmentsPaid;
    }

    final daysPastDue = repaymentDetails.mapOrNull(
      available: (available) =>
          available.data.delinquentLoanRepayment?.daysPastDue,
    );

    final minimumPayment = repaymentDetails.mapOrNull(
      available: (available) =>
          available.data.delinquentLoanRepayment?.minimumRepaymentAmount,
    );

    return AnimatedSize(
      duration: const Duration(milliseconds: 500),
      child: DetailsContainer(
        key: ValueKey(
          repaymentDetails.dataOrNull?.delinquentLoanRepayment != null,
        ),
        enableCopyAll: false,
        detailLayouts: [
          if (!repaymentDetails.isFetching ||
              repaymentDetails.dataOrNull?.delinquentLoanRepayment != null)
            DetailLayoutType.doubleDetail(
              firstDetail: DetailModel.tag(
                label: 'Days Past Due',
                value: daysPastDue == null
                    ? '-'
                    : '${daysPastDue.toString()} days',
                isLoading: repaymentDetails.isFetching,
                tag: DetailTagType.negative,
              ),
              secondDetail: DetailModel.simple(
                label: 'Minimum Payment',
                value: minimumPayment.orMoneyNotAvailable,
                isLoading: repaymentDetails.isFetching,
              ),
            ),
          DetailLayoutType.doubleDetail(
            firstDetail: DetailModel.simple(
              label: 'Paid Installments',
              value:
                  installmentsPaid == null ? '-' : installmentsPaid.toString(),
            ),
            secondDetail: DetailModel.simple(
              label: 'Unpaid Installments',
              value: unpaidInstallments == null
                  ? '-'
                  : unpaidInstallments.toString(),
            ),
          ),
          DetailLayoutType.doubleDetail(
            firstDetail: DetailModel.simple(
              label: 'Principal Paid',
              value: _principalPaid().orMoneyNotAvailable,
              isLoading: repaymentDetails.isFetching,
            ),
            secondDetail: DetailModel.simple(
              label: 'Total Outstanding (Prin. + Int. + Fee)',
              value: (repaymentDetails.dataOrNull?.totalOutstandingBalance)
                  .orMoneyNotAvailable,
              isLoading: repaymentDetails.isFetching,
            ),
          ),
          DetailLayoutType.doubleDetail(
            firstDetail: DetailModel.simple(
              label: 'Interest Paid',
              value: _interestPaid().orMoneyNotAvailable,
              isLoading: repaymentDetails.isFetching,
            ),
            secondDetail: DetailModel.simple(
              label: 'Monthly Payment',
              value: (paymentDetails.installmentAmount?.totalAmount)
                  .orMoneyNotAvailable,
              isLoading: repaymentDetails.isFetching,
            ),
          ),
        ],
      ),
    );
  }

  Money? _interestPaid() {
    final repaymentSchedule =
        repaymentDetails.dataOrNull?.repaymentSchedule?.items ?? [];

    final currency =
        paymentDetails.installmentAmount?.principal?.currency ?? Currency.aed;

    final total = repaymentSchedule.where((item) {
      final date = item.date;
      return date != null && (date.isBefore(DateTime.now()) || date.isToday());
    }).fold(
      Money.fromNumWithCurrency(0, currency),
      (previous, element) => element.interestPaidAmount == null
          ? previous
          : previous + element.interestPaidAmount!,
    );

    return total;
  }

  Money? _principalPaid() {
    final repaymentSchedule =
        repaymentDetails.dataOrNull?.repaymentSchedule?.items ?? [];

    final currency =
        paymentDetails.installmentAmount?.principal?.currency ?? Currency.aed;

    final total = repaymentSchedule.where((item) {
      final date = item.date;
      return date != null && (date.isBefore(DateTime.now()) || date.isToday());
    }).fold(
      Money.fromNumWithCurrency(0, currency),
      (previous, element) => element.principalPaidAmount == null
          ? previous
          : previous + element.principalPaidAmount!,
    );

    return total;
  }
}
