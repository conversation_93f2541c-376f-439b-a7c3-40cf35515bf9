import 'package:di/di.dart';
import 'package:flutter/material.dart' hide Banner;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui_desktop/src/common/data.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/installments/installment_details/cubit/installment_details_cubit.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/installments/installment_details/cubit/installment_details_state.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/installments/installment_details/widgets/installment_account_details.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/installments/installment_details/widgets/installments_payment_tracker.dart';
import 'package:wio_feature_navigation_rail_ui_desktop/feature_navigation_rail_desktop_ui.dart';

class InstallmentDetailsPage extends StatelessWidget {
  final String accountId;

  const InstallmentDetailsPage({
    required this.accountId,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) =>
          DependencyProvider.get<InstallmentDetailsCubit>()..init(accountId),
      child: const BackOfficeContentPage(
        content: SingleChildScrollView(
          child: _Content(),
        ),
      ),
    );
  }
}

class _Content extends StatelessWidget {
  const _Content();

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<InstallmentDetailsCubit>();

    return BlocBuilder<InstallmentDetailsCubit, InstallmentDetailsState>(
      builder: (_, state) => AnimatedSwitcher(
        duration: const Duration(milliseconds: 200),
        child: state.map(
          loading: (_) => SizedBox(
            height: MediaQuery.of(context).size.height,
            child: Center(
              child: Spinner(model: SpinnerModel(type: SpinnerType.primary)),
            ),
          ),
          ready: (readyState) => _LoadedView(
            account: readyState.account,
            repaymentDetails: readyState.repaymentDetails,
            autoPaymentDetails: readyState.autoPaymentDetails,
          ),
          error: (errorState) => Center(
            child: GenericError(
              const GenericErrorModel(
                title: 'Sorry something went wrong',
                subtitle: 'Unable to show installment details at this time',
                buttonLabel: 'Retry',
              ),
              onPressed: () => cubit.init(errorState.accountId),
            ),
          ),
        ),
      ),
    );
  }
}

enum _Tabs {
  details,
  tracker,
}

class _LoadedView extends StatefulWidget {
  final InstallmentAccount account;
  final Data<LoanRepayment> repaymentDetails;
  final Data<CurrentAutoPayment> autoPaymentDetails;

  const _LoadedView({
    required this.account,
    required this.repaymentDetails,
    required this.autoPaymentDetails,
  });

  @override
  State<_LoadedView> createState() => _LoadedViewState();
}

class _LoadedViewState extends State<_LoadedView> {
  static const _orderedTabs = [
    _Tabs.details,
    _Tabs.tracker,
  ];
  var _selectedTab = _orderedTabs.first;

  @override
  Widget build(BuildContext context) {
    final dueDate = widget.account.dueDate;
    final dateFormat = DateFormat('dd/MM/yyyy');

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _BackButton(),
            const SizedBox(height: 12.0),
            _DelinquencyDetails(autoPaymentDetails: widget.autoPaymentDetails),
            const SizedBox(height: 12.0),
            Row(
              children: [
                const Label(
                  model: LabelModel(
                    text: 'Installments',
                    color: CompanyColorPointer.primary3,
                    textStyle: CompanyTextStylePointer.h4,
                  ),
                ),
                const SizedBox(width: 4),
                if (widget.account.isActive != null)
                  CompanyLabel(
                    CompanyLabelModel.v2(
                      text: widget.account.isActive! ? 'Active' : 'Completed',
                      status: widget.account.isActive!
                          ? CompanyLabelStatus.positive
                          : CompanyLabelStatus.tertiary,
                      boxed: true,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12.0),
            if (dueDate != null)
              Banner(
                BannerModel.simple(
                  text: 'Next payment due on ${dateFormat.format(dueDate)}',
                  backgroundColor: CompanyColorPointer.surface20,
                  borderColor: CompanyColorPointer.secondary12,
                  showIcon: false,
                ),
              ),
            const SizedBox(height: 12),
            Tabs(
              TabsModel(
                tabNames: _orderedTabs
                    .map((tabItem) => tabItem.displayString)
                    .toList(),
                variant: TabsVariant.smallConnected,
                activeTextColor: CompanyColorPointer.surface2,
                inactiveTextColor: CompanyColorPointer.surface4,
                activeBackgroundColor: CompanyColorPointer.primary3,
                selectedIndex:
                    _orderedTabs.indexWhere((tab) => tab == _selectedTab),
              ),
              onTabPressed: (idx) => setState(() {
                _selectedTab = _orderedTabs[idx];
              }),
            ),
            const SizedBox(height: 12),
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 500),
              child: switch (_selectedTab) {
                _Tabs.details => InstallmentAccountDetails(
                    account: widget.account,
                    repaymentDetails: widget.repaymentDetails,
                  ),
                _Tabs.tracker => InstallmentsPaymentTracker(
                    account: widget.account,
                    autoPaymentDetails: widget.autoPaymentDetails,
                  ),
              },
            ),
          ],
        ),
      ),
    );
  }
}

extension on _Tabs {
  String get displayString {
    return switch (this) {
      _Tabs.details => 'Details',
      _Tabs.tracker => 'Tracker',
    };
  }
}

class _DelinquencyDetails extends StatelessWidget {
  final Data<CurrentAutoPayment> autoPaymentDetails;
  const _DelinquencyDetails({
    required this.autoPaymentDetails,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 500),
      child: autoPaymentDetails.map(
        available: (available) {
          final delinquentAccountDetails =
              available.data.delinquentAccountDetails;

          if (delinquentAccountDetails == null) {
            return const SizedBox.shrink();
          }

          final minimumRepaymentAmount =
              available.data.minimumRepaymentAmountForDelinquentAccount;

          var minimumRepaymentAmountString = '';

          if (minimumRepaymentAmount != null) {
            minimumRepaymentAmountString = 'Minimum repayment amount is'
                ' ${minimumRepaymentAmount.toCodeOnRightFormat()}';
          }

          return Banner(
            BannerModel.simple(
              text: 'Installment is in delinquency. '
                  '$minimumRepaymentAmountString',
              borderColor: CompanyColorPointer.secondary7,
              showIcon: false,
            ),
          );
        },
        error: (error) => const SizedBox.shrink(),
        fetching: (fetching) => const CompanyShimmer(
          model: CompanyShimmerModel(),
          child: Banner(
            BannerModel.simple(
              text: '',
            ),
          ),
        ),
      ),
    );
  }
}

class _BackButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) => InkWell(
        onTap: Navigator.of(context).pop,
        child: const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CompanyIcon(
              CompanyIconModel(
                icon: GraphicAssetPointer.icon(
                  CompanyIconPointer.chevron_left,
                ),
                size: CompanyIconSize.xSmall,
              ),
            ),
            Label(
              model: LabelModel(
                text: 'Back',
                textStyle: CompanyTextStylePointer.b3,
                color: CompanyColorPointer.primary3,
              ),
            ),
          ],
        ),
      );
}
