import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_lending_api/domain/model/installment_account.dart';
import 'package:wio_feature_lending_ui_desktop/src/common/data.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/installments/installments_dashboard/widgets/next_upcoming_installment_breakdown.dart';

class AutopaySection extends StatelessWidget {
  final Data<List<InstallmentAccount>> installmentAccounts;

  const AutopaySection({
    required this.installmentAccounts,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 500),
      child: installmentAccounts.map(
        fetching: (_) => CompanyShimmer(
          model: const CompanyShimmerModel(),
          child: _Content(
            installmentAccounts: [
              InstallmentAccount(
                paymentDetails: InstallmentPaymentDetails(
                  installmentDueDate: DateTime.now(),
                ),
              ),
            ],
          ),
        ),
        available: (data) => _Content(installmentAccounts: data.data),
        error: (_) => const SizedBox.shrink(),
      ),
    );
  }
}

class _Content extends StatelessWidget {
  final List<InstallmentAccount> installmentAccounts;

  const _Content({
    required this.installmentAccounts,
  });

  @override
  Widget build(BuildContext context) {
    if (installmentAccounts.isEmpty) {
      return const SizedBox.shrink();
    }

    final accountsGroupedByDueDate = <DateTime, List<InstallmentAccount>>{};

    for (final account in installmentAccounts) {
      final dueDate = account.dueDate;

      if (dueDate != null) {
        if (accountsGroupedByDueDate.containsKey(dueDate)) {
          accountsGroupedByDueDate[dueDate]!.add(account);
        } else {
          accountsGroupedByDueDate[dueDate] = [account];
        }
      }
    }

    final mostRecentDueDate =
        accountsGroupedByDueDate.keys.reduce((a, b) => a.isBefore(b) ? a : b);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: NextUpcomingInstallmentBreakdown(
        dueDate: mostRecentDueDate,
        installmentAccounts: accountsGroupedByDueDate[mostRecentDueDate]!,
      ),
    );
  }
}
