import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:ui/page/base_page.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_app_common_ui_desktop/feature_app_common_desktop_ui.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/credit/credit_cubit.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/credit/credit_state.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/installments/installments_dashboard/widgets/next_upcoming_installment_breakdown.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/installments/installments_dashboard/widgets/section_container.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/lending/lending_utils.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/lending/widget/list_details_item.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/lending/widget/modal/fee_details_widget.dart';
import 'package:wio_feature_transactions_api/transactions_api.dart'
    as transaction;
import 'package:wio_feature_transactions_ui_desktop/feature_transactions_desktop_ui.dart';

const emptyPlaceholder = '-';

class WioCreditContent extends BasePage<CreditState, CreditCubit> {
  final LoanAccount loanAccount;

  const WioCreditContent({
    required this.loanAccount,
    super.key,
  });

  @override
  CreditCubit createBloc() => DependencyProvider.get<CreditCubit>()
    ..initialize(
      accountId: loanAccount.id,
    );

  @override
  Widget buildPage(
    BuildContext context,
    CreditCubit bloc,
    CreditState state,
  ) {
    final colorScheme = context.colorStyling;
    return state.maybeMap(
      loaded: (it) => Column(
        children: [
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: colorScheme.surface2,
              borderRadius: BorderRadius.circular(8),
            ),
            child: AdditionalDetailLine(
              model: AdditionalDetailLineModel.doubleBox(
                firstDetailBoxModel: DetailBoxModel.text(
                  title: 'Creation date',
                  subtitle: loanAccount.creationDate.formatDateToString() ??
                      emptyPlaceholder,
                ),
                secondDetailBoxModel: DetailBoxModel.companyLabel(
                  title: 'Status',
                  companyLabelModel: CompanyLabelModel(
                    text: LendingUtils.mapToAccountState(loanAccount.state),
                    backgroundColor: CompanyColorPointer.surface9,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          _CreditLimitSection(
            loanAccount: loanAccount,
            loanRepayment: it.loanRepayment,
            summary: it.loanAccounts.creditSummary,
          ),
          const SizedBox(height: 12),
          if (it.loanRepayment.preferences?.disableAutoPayFromSavings !=
              null) ...[
            _AutopaySettings(
              isPaymentFromSavingsSpaceEnabled:
                  !it.loanRepayment.preferences!.disableAutoPayFromSavings!,
            ),
            const SizedBox(height: 12),
          ],
          _AutoPaySection(
            loanAccount: loanAccount,
            loanRepayment: it.loanRepayment,
            installmentAccounts: it.installmentAccounts,
          ),
          const SizedBox(height: 8),
          _CustomerDetailsSection(
            loanAccount: loanAccount,
            customerId: it.loanRepayment.customerId,
          ),
          const SizedBox(height: 12),
          _WioCreditPaymentHistorySection(loanAccount.id),
        ],
      ),
      orElse: () => const SizedBox.shrink(),
    );
  }
}

class _AutopaySettings extends StatelessWidget {
  final bool isPaymentFromSavingsSpaceEnabled;
  const _AutopaySettings({
    required this.isPaymentFromSavingsSpaceEnabled,
  });

  @override
  Widget build(BuildContext context) {
    final settingTabs = <String>[
      'Disabled',
      'Enabled',
    ];

    return SectionContainer(
      padding: EdgeInsets.zero,
      child: AdditionalDetailLine(
        model: AdditionalDetailLineModel.switcher(
          detailSwitchBoxModel: DetailSwitchBoxModel(
            enabled: isPaymentFromSavingsSpaceEnabled,
            title: 'Autopay from savings space',
            tabsList: settingTabs,
            defaultTab: isPaymentFromSavingsSpaceEnabled
                ? settingTabs.last
                : settingTabs.first,
            inactiveTextColor: CompanyColorPointer.surface4,
            activeBackgroundColor: CompanyColorPointer.primary3,
            activeTextColor: CompanyColorPointer.surface2,
          ),
        ),
      ),
    );
  }
}

class _WioCreditPaymentHistorySection extends StatefulWidget {
  final String accountId;

  const _WioCreditPaymentHistorySection(this.accountId);

  @override
  State<_WioCreditPaymentHistorySection> createState() =>
      _WioCreditPaymentHistorySectionState();
}

class _WioCreditPaymentHistorySectionState
    extends State<_WioCreditPaymentHistorySection> {
  late ScrollController _scrollController;

  @override
  void initState() {
    _scrollController = ScrollController();
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  List<TransactionTableColumnProperties> getColumnProperties() {
    final columnProperties = <TransactionTableColumnProperties>[
      inOutColumn,
      dateColumn,
      paymentAmount,
      outStandingBalanceColumn,
      statusColumn,
    ];
    return columnProperties;
  }

  final paymentAmount = TransactionTableColumnProperties(
    cellBuilder: (context, transaction) {
      return CompanyCell(
        model: CompanyCellModel.text(
          text: transaction.amount.formattedTitle,
        ),
        onCellPressed: () => showTransactionDetailsModal(
          context: context,
          transaction: transaction,
        ),
      );
    },
    headerText: 'Payment amount',
    sortField: transaction.SortField.amount,
    flex: 14,
    columnWidth: 131,
  );

  final outStandingBalanceColumn = TransactionTableColumnProperties(
    cellBuilder: (context, transaction) {
      return CompanyCell(
        model: CompanyCellModel.text(
          text: transaction.availableBalance?.formattedTitle ?? '-',
        ),
        onCellPressed: () => showTransactionDetailsModal(
          context: context,
          transaction: transaction,
        ),
      );
    },
    headerText: 'Outstanding',
    sortField: transaction.SortField.availableBalance,
    flex: 14,
    columnWidth: 124,
  );

  @override
  Widget build(BuildContext context) {
    return TransactionsTable(
      tableModel: const CompanyTableModel(
        tableName: 'Wio Credit Payments',
        items: ['Wio Credit Payments'],
      ),
      filterPayload: transaction.TransactionsFilterRequestPayload(
        accountIds: [
          widget.accountId,
        ],
      ),
      transactionContextType: TransactionContextType.lending,
      showFilter: true,
      columnProperties: getColumnProperties(),
    );
  }
}

class _CustomerDetailsSection extends StatelessWidget {
  final LoanAccount loanAccount;
  final String? customerId;

  const _CustomerDetailsSection({
    required this.loanAccount,
    this.customerId,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = context.colorStyling;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface2,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          AdditionalDetailLine(
            model: AdditionalDetailLineModel.doubleBox(
              firstDetailBoxModel: DetailBoxModel.text(
                title: 'Customer ID',
                subtitle: customerId ?? emptyPlaceholder,
              ),
              secondDetailBoxModel: DetailBoxModel.text(
                title: 'Account ID',
                subtitle: loanAccount.id,
              ),
            ),
          ),
          AdditionalDetailLine(
            model: AdditionalDetailLineModel.doubleBox(
              firstDetailBoxModel: DetailBoxModel.companyLabel(
                title: 'Status',
                companyLabelModel: CompanyLabelModel.v2(
                  text: LendingUtils.mapToAccountState(loanAccount.state),
                  status: CompanyLabelStatus.positive,
                  boxed: true,
                ),
              ),
              secondDetailBoxModel: const DetailBoxModel.text(
                title: '',
                subtitle: '',
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _CreditLimitSection extends StatelessWidget {
  final LoanAccount loanAccount;
  final LoanRepayment loanRepayment;
  final CreditSummary summary;

  const _CreditLimitSection({
    required this.loanAccount,
    required this.loanRepayment,
    required this.summary,
  });

  @override
  Widget build(BuildContext context) {
    final principalBalance = loanAccount.principalBalance ??
        Money.fromNumWithCurrency(0, Currency.aed);
    final holdBalance =
        loanAccount.holdBalance ?? Money.fromNumWithCurrency(0, Currency.aed);

    final colorScheme = context.colorStyling;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface2,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ListDetailsItem(
            title: 'Credit limit',
            subtitle: loanAccount.loanAmount.toCodeOnRightFormat(),
            containerPaddingHorizontal: 0,
          ),
          ListDetailsItem(
            title: 'Outstanding amount',
            subtitle: (principalBalance + holdBalance).withoutCodeFormat(),
            statusDot: const StatusDot(
              statusType: StatusDotType.defaultType,
            ),
            containerPaddingHorizontal: 0,
          ),
          const SizedBox(width: 24),
          ListDetailsItem(
            title: 'Remaining',
            subtitle: summary.availableToSpend.toCodeOnRightFormat(),
            statusDot: const StatusDot(statusType: StatusDotType.inactive),
            containerPaddingHorizontal: 0,
          ),
        ],
      ),
    );
  }
}

class _AutoPaySection extends StatelessWidget {
  final LoanAccount loanAccount;
  final LoanRepayment loanRepayment;
  final List<InstallmentAccount> installmentAccounts;

  const _AutoPaySection({
    required this.loanAccount,
    required this.loanRepayment,
    required this.installmentAccounts,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = context.colorStyling;
    final autoPayPercentage = loanAccount.paymentSettings?.percentage;
    final daysLeft =
        '${loanRepayment.feeFreeDate.daysBetween(DateTime.now())} Days left';

    final repaymentDate =
        loanRepayment.repaymentDate.formatDateToStringWithMonth();

    final totalInstallmentAmount = installmentAccounts.totalAmountDueUpcoming;
    final totalLoanAccountDueAmount = loanRepayment.totalDueAmount;
    final totalOverallAmount =
        totalLoanAccountDueAmount + totalInstallmentAmount;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface2,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: CompanyRichText(
                  CompanyRichTextModel(
                    text:
                        'Next autopay on ${repaymentDate ?? emptyPlaceholder}',
                    normalStyle: CompanyTextStylePointer.b2,
                    textAlign: TextAlign.start,
                    accentStyle: CompanyTextStylePointer.b2medium,
                    normalTextColor: CompanyColorPointer.primary3,
                    accentTextColor: CompanyColorPointer.primary1,
                    highlightedTextModels: [
                      HighlightedTextModel(
                        repaymentDate ?? emptyPlaceholder,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Button(
                    onPressed: () async {
                      await FeeDetailsModal.show<void>(
                        context: context,
                        loanRepayment: loanRepayment,
                        loanAccount: loanAccount,
                      );
                    },
                    model: const ButtonModel(
                      title: 'Fee details',
                      type: ButtonType.secondary,
                      theme: ButtonModelTheme.sme,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const DividerWithSpacing(),
          SingleLineDetail(
            title: 'Total Credit Amount',
            value: totalOverallAmount.orMoneyNotAvailable,
          ),
          if (loanAccount.feeBalance != null) ...[
            const SizedBox(height: 4),
            SingleLineDetail(
              title: 'Total Fees Applied',
              value: loanAccount.feeBalance.orMoneyNotAvailable,
            ),
          ],
          const SizedBox(height: 16),
          if (installmentAccounts.activeInstallments.isNotEmpty) ...[
            _LabeledSection(
              label: 'Installments',
              child: NextUpcomingInstallmentBreakdown(
                installmentAccounts: installmentAccounts,
                backgroundColor: CompanyColorPointer.surface7,
                padding: EdgeInsets.zero,
              ),
            ),
            const DividerWithSpacing(),
          ],
          _LabeledSection(
            label: 'Wio Credit',
            child: _WioCreditAutopayBreakdown(
              loanAccount: loanAccount,
              loanRepayment: loanRepayment,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _DetailItem(
                title: 'Auto pay percentage',
                value: '${autoPayPercentage?.toString() ?? emptyPlaceholder}% '
                    'of outstanding amount',
              ),
              _DetailItem(
                title: 'Free fee period until',
                value: _getFreeFeePeriodAndDays(loanRepayment.feeFreeDate),
                labelModel: CompanyLabelModel.v2(
                  text: '$daysLeft left',
                  status: CompanyLabelStatus.partial,
                  boxed: true,
                ),
              ),
              const SizedBox.shrink(),
            ],
          ),
        ],
      ),
    );
  }

  String _getFreeFeePeriodAndDays(DateTime? toDate) {
    return '${toDate.formatDateToString()}';
  }
}

class _DetailItem extends StatelessWidget {
  final String title;
  final String value;
  final CompanyLabelModel? labelModel;

  const _DetailItem({
    required this.title,
    required this.value,
    this.labelModel,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Label(
          model: LabelModel(
            text: title,
            textStyle: CompanyTextStylePointer.b3,
            color: CompanyColorPointer.secondary4,
          ),
        ),
        const SizedBox(height: 4),
        Label(
          model: LabelModel(
            text: value,
            textStyle: CompanyTextStylePointer.b2,
            color: CompanyColorPointer.primary3,
          ),
        ),
        if (labelModel != null) ...[
          const SizedBox(height: 4),
          CompanyLabel(labelModel!),
        ],
      ],
    );
  }
}

class _WioCreditAutopayBreakdown extends StatelessWidget {
  final LoanAccount loanAccount;
  final LoanRepayment loanRepayment;
  const _WioCreditAutopayBreakdown({
    required this.loanAccount,
    required this.loanRepayment,
  });

  @override
  Widget build(BuildContext context) {
    return SectionContainer(
      backgroundColor: CompanyColorPointer.surface7,
      padding: EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          PaymentPart(
            label: 'Total Due',
            amount: loanRepayment.totalDueAmount,
          ),
          Operator.equal(),
          PaymentPart(
            label: 'Principal',
            amount: loanRepayment.repaymentPrincipal,
          ),
          Operator.plus(),
          PaymentPart(
            label: 'Fee',
            amount: loanRepayment.repaymentFee,
          ),
        ],
      ),
    );
  }
}

extension on LoanRepayment {
  Money get totalDueAmount {
    return repaymentPrincipal + repaymentFee;
  }
}

class _LabeledSection extends StatelessWidget {
  final String label;
  final Widget child;
  const _LabeledSection({
    required this.label,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Label(
          model: LabelModel(
            text: label,
            textStyle: CompanyTextStylePointer.b3,
            color: CompanyColorPointer.secondary4,
          ),
        ),
        const SizedBox(height: 4),
        child,
      ],
    );
  }
}

class DividerWithSpacing extends StatelessWidget {
  const DividerWithSpacing({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = context.colorStyling;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const SizedBox(height: 16),
        Divider(
          color: colorScheme.secondary6,
          height: 1,
        ),
        const SizedBox(height: 16),
      ],
    );
  }
}

class SingleLineDetail extends StatelessWidget {
  final String title;
  final String value;
  const SingleLineDetail({
    required this.title,
    required this.value,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return CompanyRichText(
      CompanyRichTextModel(
        text: '$title: $value',
        highlightedTextModels: [
          HighlightedTextModel(
            value,
          ),
        ],
        normalStyle: CompanyTextStylePointer.b2,
        normalTextColor: CompanyColorPointer.secondary5,
        accentStyle: CompanyTextStylePointer.b2medium,
        accentTextColor: CompanyColorPointer.primary3,
      ),
    );
  }
}
