// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'credit_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CreditState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            LoanRepayment loanRepayment,
            LoanAccountDetails loanAccounts,
            List<InstallmentAccount> installmentAccounts,
            List<CreditApplication>? creditApplicationList)
        loaded,
    required TResult Function() loading,
    required TResult Function() error,
    required TResult Function() empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            LoanRepayment loanRepayment,
            LoanAccountDetails loanAccounts,
            List<InstallmentAccount> installmentAccounts,
            List<CreditApplication>? creditApplicationList)?
        loaded,
    TResult? Function()? loading,
    TResult? Function()? error,
    TResult? Function()? empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            LoanRepayment loanRepayment,
            LoanAccountDetails loanAccounts,
            List<InstallmentAccount> installmentAccounts,
            List<CreditApplication>? creditApplicationList)?
        loaded,
    TResult Function()? loading,
    TResult Function()? error,
    TResult Function()? empty,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CreditStateLoaded value) loaded,
    required TResult Function(CreditLoadingState value) loading,
    required TResult Function(CreditStateError value) error,
    required TResult Function(CreditStateEmpty value) empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CreditStateLoaded value)? loaded,
    TResult? Function(CreditLoadingState value)? loading,
    TResult? Function(CreditStateError value)? error,
    TResult? Function(CreditStateEmpty value)? empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CreditStateLoaded value)? loaded,
    TResult Function(CreditLoadingState value)? loading,
    TResult Function(CreditStateError value)? error,
    TResult Function(CreditStateEmpty value)? empty,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreditStateCopyWith<$Res> {
  factory $CreditStateCopyWith(
          CreditState value, $Res Function(CreditState) then) =
      _$CreditStateCopyWithImpl<$Res, CreditState>;
}

/// @nodoc
class _$CreditStateCopyWithImpl<$Res, $Val extends CreditState>
    implements $CreditStateCopyWith<$Res> {
  _$CreditStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreditState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$CreditStateLoadedImplCopyWith<$Res> {
  factory _$$CreditStateLoadedImplCopyWith(_$CreditStateLoadedImpl value,
          $Res Function(_$CreditStateLoadedImpl) then) =
      __$$CreditStateLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {LoanRepayment loanRepayment,
      LoanAccountDetails loanAccounts,
      List<InstallmentAccount> installmentAccounts,
      List<CreditApplication>? creditApplicationList});

  $LoanRepaymentCopyWith<$Res> get loanRepayment;
  $LoanAccountDetailsCopyWith<$Res> get loanAccounts;
}

/// @nodoc
class __$$CreditStateLoadedImplCopyWithImpl<$Res>
    extends _$CreditStateCopyWithImpl<$Res, _$CreditStateLoadedImpl>
    implements _$$CreditStateLoadedImplCopyWith<$Res> {
  __$$CreditStateLoadedImplCopyWithImpl(_$CreditStateLoadedImpl _value,
      $Res Function(_$CreditStateLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loanRepayment = null,
    Object? loanAccounts = null,
    Object? installmentAccounts = null,
    Object? creditApplicationList = freezed,
  }) {
    return _then(_$CreditStateLoadedImpl(
      loanRepayment: null == loanRepayment
          ? _value.loanRepayment
          : loanRepayment // ignore: cast_nullable_to_non_nullable
              as LoanRepayment,
      loanAccounts: null == loanAccounts
          ? _value.loanAccounts
          : loanAccounts // ignore: cast_nullable_to_non_nullable
              as LoanAccountDetails,
      installmentAccounts: null == installmentAccounts
          ? _value._installmentAccounts
          : installmentAccounts // ignore: cast_nullable_to_non_nullable
              as List<InstallmentAccount>,
      creditApplicationList: freezed == creditApplicationList
          ? _value._creditApplicationList
          : creditApplicationList // ignore: cast_nullable_to_non_nullable
              as List<CreditApplication>?,
    ));
  }

  /// Create a copy of CreditState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LoanRepaymentCopyWith<$Res> get loanRepayment {
    return $LoanRepaymentCopyWith<$Res>(_value.loanRepayment, (value) {
      return _then(_value.copyWith(loanRepayment: value));
    });
  }

  /// Create a copy of CreditState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LoanAccountDetailsCopyWith<$Res> get loanAccounts {
    return $LoanAccountDetailsCopyWith<$Res>(_value.loanAccounts, (value) {
      return _then(_value.copyWith(loanAccounts: value));
    });
  }
}

/// @nodoc

class _$CreditStateLoadedImpl implements CreditStateLoaded {
  const _$CreditStateLoadedImpl(
      {required this.loanRepayment,
      required this.loanAccounts,
      required final List<InstallmentAccount> installmentAccounts,
      final List<CreditApplication>? creditApplicationList})
      : _installmentAccounts = installmentAccounts,
        _creditApplicationList = creditApplicationList;

  @override
  final LoanRepayment loanRepayment;
  @override
  final LoanAccountDetails loanAccounts;
  final List<InstallmentAccount> _installmentAccounts;
  @override
  List<InstallmentAccount> get installmentAccounts {
    if (_installmentAccounts is EqualUnmodifiableListView)
      return _installmentAccounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_installmentAccounts);
  }

  final List<CreditApplication>? _creditApplicationList;
  @override
  List<CreditApplication>? get creditApplicationList {
    final value = _creditApplicationList;
    if (value == null) return null;
    if (_creditApplicationList is EqualUnmodifiableListView)
      return _creditApplicationList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CreditState.loaded(loanRepayment: $loanRepayment, loanAccounts: $loanAccounts, installmentAccounts: $installmentAccounts, creditApplicationList: $creditApplicationList)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreditStateLoadedImpl &&
            (identical(other.loanRepayment, loanRepayment) ||
                other.loanRepayment == loanRepayment) &&
            (identical(other.loanAccounts, loanAccounts) ||
                other.loanAccounts == loanAccounts) &&
            const DeepCollectionEquality()
                .equals(other._installmentAccounts, _installmentAccounts) &&
            const DeepCollectionEquality()
                .equals(other._creditApplicationList, _creditApplicationList));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      loanRepayment,
      loanAccounts,
      const DeepCollectionEquality().hash(_installmentAccounts),
      const DeepCollectionEquality().hash(_creditApplicationList));

  /// Create a copy of CreditState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreditStateLoadedImplCopyWith<_$CreditStateLoadedImpl> get copyWith =>
      __$$CreditStateLoadedImplCopyWithImpl<_$CreditStateLoadedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            LoanRepayment loanRepayment,
            LoanAccountDetails loanAccounts,
            List<InstallmentAccount> installmentAccounts,
            List<CreditApplication>? creditApplicationList)
        loaded,
    required TResult Function() loading,
    required TResult Function() error,
    required TResult Function() empty,
  }) {
    return loaded(loanRepayment, loanAccounts, installmentAccounts,
        creditApplicationList);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            LoanRepayment loanRepayment,
            LoanAccountDetails loanAccounts,
            List<InstallmentAccount> installmentAccounts,
            List<CreditApplication>? creditApplicationList)?
        loaded,
    TResult? Function()? loading,
    TResult? Function()? error,
    TResult? Function()? empty,
  }) {
    return loaded?.call(loanRepayment, loanAccounts, installmentAccounts,
        creditApplicationList);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            LoanRepayment loanRepayment,
            LoanAccountDetails loanAccounts,
            List<InstallmentAccount> installmentAccounts,
            List<CreditApplication>? creditApplicationList)?
        loaded,
    TResult Function()? loading,
    TResult Function()? error,
    TResult Function()? empty,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(loanRepayment, loanAccounts, installmentAccounts,
          creditApplicationList);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CreditStateLoaded value) loaded,
    required TResult Function(CreditLoadingState value) loading,
    required TResult Function(CreditStateError value) error,
    required TResult Function(CreditStateEmpty value) empty,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CreditStateLoaded value)? loaded,
    TResult? Function(CreditLoadingState value)? loading,
    TResult? Function(CreditStateError value)? error,
    TResult? Function(CreditStateEmpty value)? empty,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CreditStateLoaded value)? loaded,
    TResult Function(CreditLoadingState value)? loading,
    TResult Function(CreditStateError value)? error,
    TResult Function(CreditStateEmpty value)? empty,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class CreditStateLoaded implements CreditState {
  const factory CreditStateLoaded(
          {required final LoanRepayment loanRepayment,
          required final LoanAccountDetails loanAccounts,
          required final List<InstallmentAccount> installmentAccounts,
          final List<CreditApplication>? creditApplicationList}) =
      _$CreditStateLoadedImpl;

  LoanRepayment get loanRepayment;
  LoanAccountDetails get loanAccounts;
  List<InstallmentAccount> get installmentAccounts;
  List<CreditApplication>? get creditApplicationList;

  /// Create a copy of CreditState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreditStateLoadedImplCopyWith<_$CreditStateLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CreditLoadingStateImplCopyWith<$Res> {
  factory _$$CreditLoadingStateImplCopyWith(_$CreditLoadingStateImpl value,
          $Res Function(_$CreditLoadingStateImpl) then) =
      __$$CreditLoadingStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CreditLoadingStateImplCopyWithImpl<$Res>
    extends _$CreditStateCopyWithImpl<$Res, _$CreditLoadingStateImpl>
    implements _$$CreditLoadingStateImplCopyWith<$Res> {
  __$$CreditLoadingStateImplCopyWithImpl(_$CreditLoadingStateImpl _value,
      $Res Function(_$CreditLoadingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CreditLoadingStateImpl implements CreditLoadingState {
  const _$CreditLoadingStateImpl();

  @override
  String toString() {
    return 'CreditState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CreditLoadingStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            LoanRepayment loanRepayment,
            LoanAccountDetails loanAccounts,
            List<InstallmentAccount> installmentAccounts,
            List<CreditApplication>? creditApplicationList)
        loaded,
    required TResult Function() loading,
    required TResult Function() error,
    required TResult Function() empty,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            LoanRepayment loanRepayment,
            LoanAccountDetails loanAccounts,
            List<InstallmentAccount> installmentAccounts,
            List<CreditApplication>? creditApplicationList)?
        loaded,
    TResult? Function()? loading,
    TResult? Function()? error,
    TResult? Function()? empty,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            LoanRepayment loanRepayment,
            LoanAccountDetails loanAccounts,
            List<InstallmentAccount> installmentAccounts,
            List<CreditApplication>? creditApplicationList)?
        loaded,
    TResult Function()? loading,
    TResult Function()? error,
    TResult Function()? empty,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CreditStateLoaded value) loaded,
    required TResult Function(CreditLoadingState value) loading,
    required TResult Function(CreditStateError value) error,
    required TResult Function(CreditStateEmpty value) empty,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CreditStateLoaded value)? loaded,
    TResult? Function(CreditLoadingState value)? loading,
    TResult? Function(CreditStateError value)? error,
    TResult? Function(CreditStateEmpty value)? empty,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CreditStateLoaded value)? loaded,
    TResult Function(CreditLoadingState value)? loading,
    TResult Function(CreditStateError value)? error,
    TResult Function(CreditStateEmpty value)? empty,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class CreditLoadingState implements CreditState {
  const factory CreditLoadingState() = _$CreditLoadingStateImpl;
}

/// @nodoc
abstract class _$$CreditStateErrorImplCopyWith<$Res> {
  factory _$$CreditStateErrorImplCopyWith(_$CreditStateErrorImpl value,
          $Res Function(_$CreditStateErrorImpl) then) =
      __$$CreditStateErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CreditStateErrorImplCopyWithImpl<$Res>
    extends _$CreditStateCopyWithImpl<$Res, _$CreditStateErrorImpl>
    implements _$$CreditStateErrorImplCopyWith<$Res> {
  __$$CreditStateErrorImplCopyWithImpl(_$CreditStateErrorImpl _value,
      $Res Function(_$CreditStateErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CreditStateErrorImpl implements CreditStateError {
  const _$CreditStateErrorImpl();

  @override
  String toString() {
    return 'CreditState.error()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CreditStateErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            LoanRepayment loanRepayment,
            LoanAccountDetails loanAccounts,
            List<InstallmentAccount> installmentAccounts,
            List<CreditApplication>? creditApplicationList)
        loaded,
    required TResult Function() loading,
    required TResult Function() error,
    required TResult Function() empty,
  }) {
    return error();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            LoanRepayment loanRepayment,
            LoanAccountDetails loanAccounts,
            List<InstallmentAccount> installmentAccounts,
            List<CreditApplication>? creditApplicationList)?
        loaded,
    TResult? Function()? loading,
    TResult? Function()? error,
    TResult? Function()? empty,
  }) {
    return error?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            LoanRepayment loanRepayment,
            LoanAccountDetails loanAccounts,
            List<InstallmentAccount> installmentAccounts,
            List<CreditApplication>? creditApplicationList)?
        loaded,
    TResult Function()? loading,
    TResult Function()? error,
    TResult Function()? empty,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CreditStateLoaded value) loaded,
    required TResult Function(CreditLoadingState value) loading,
    required TResult Function(CreditStateError value) error,
    required TResult Function(CreditStateEmpty value) empty,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CreditStateLoaded value)? loaded,
    TResult? Function(CreditLoadingState value)? loading,
    TResult? Function(CreditStateError value)? error,
    TResult? Function(CreditStateEmpty value)? empty,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CreditStateLoaded value)? loaded,
    TResult Function(CreditLoadingState value)? loading,
    TResult Function(CreditStateError value)? error,
    TResult Function(CreditStateEmpty value)? empty,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class CreditStateError implements CreditState {
  const factory CreditStateError() = _$CreditStateErrorImpl;
}

/// @nodoc
abstract class _$$CreditStateEmptyImplCopyWith<$Res> {
  factory _$$CreditStateEmptyImplCopyWith(_$CreditStateEmptyImpl value,
          $Res Function(_$CreditStateEmptyImpl) then) =
      __$$CreditStateEmptyImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CreditStateEmptyImplCopyWithImpl<$Res>
    extends _$CreditStateCopyWithImpl<$Res, _$CreditStateEmptyImpl>
    implements _$$CreditStateEmptyImplCopyWith<$Res> {
  __$$CreditStateEmptyImplCopyWithImpl(_$CreditStateEmptyImpl _value,
      $Res Function(_$CreditStateEmptyImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CreditStateEmptyImpl implements CreditStateEmpty {
  const _$CreditStateEmptyImpl();

  @override
  String toString() {
    return 'CreditState.empty()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CreditStateEmptyImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            LoanRepayment loanRepayment,
            LoanAccountDetails loanAccounts,
            List<InstallmentAccount> installmentAccounts,
            List<CreditApplication>? creditApplicationList)
        loaded,
    required TResult Function() loading,
    required TResult Function() error,
    required TResult Function() empty,
  }) {
    return empty();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            LoanRepayment loanRepayment,
            LoanAccountDetails loanAccounts,
            List<InstallmentAccount> installmentAccounts,
            List<CreditApplication>? creditApplicationList)?
        loaded,
    TResult? Function()? loading,
    TResult? Function()? error,
    TResult? Function()? empty,
  }) {
    return empty?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            LoanRepayment loanRepayment,
            LoanAccountDetails loanAccounts,
            List<InstallmentAccount> installmentAccounts,
            List<CreditApplication>? creditApplicationList)?
        loaded,
    TResult Function()? loading,
    TResult Function()? error,
    TResult Function()? empty,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CreditStateLoaded value) loaded,
    required TResult Function(CreditLoadingState value) loading,
    required TResult Function(CreditStateError value) error,
    required TResult Function(CreditStateEmpty value) empty,
  }) {
    return empty(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CreditStateLoaded value)? loaded,
    TResult? Function(CreditLoadingState value)? loading,
    TResult? Function(CreditStateError value)? error,
    TResult? Function(CreditStateEmpty value)? empty,
  }) {
    return empty?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CreditStateLoaded value)? loaded,
    TResult Function(CreditLoadingState value)? loading,
    TResult Function(CreditStateError value)? error,
    TResult Function(CreditStateEmpty value)? empty,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty(this);
    }
    return orElse();
  }
}

abstract class CreditStateEmpty implements CreditState {
  const factory CreditStateEmpty() = _$CreditStateEmptyImpl;
}
