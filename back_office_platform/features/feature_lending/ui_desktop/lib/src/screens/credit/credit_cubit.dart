import 'package:logging_api/logging.dart';
import 'package:ui/ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_common_error_handler_api/handler/common_error_handler.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui_desktop/src/screens/credit/credit_state.dart';

class CreditCubit extends BaseCubit<CreditState> {
  final NavigationProvider _navigationProvider;
  final Logger _logger;
  final LendingInteractor _lendingInteractor;
  final CommonErrorHandler _commonErrorHandler;

  CreditCubit({
    required NavigationProvider navigationProvider,
    required LendingInteractor lendingInteractor,
    required Logger logger,
    required CommonErrorHandler commonErrorHandler,
  })  : _navigationProvider = navigationProvider,
        _logger = logger,
        _commonErrorHandler = commonErrorHandler,
        _lendingInteractor = lendingInteractor,
        super(const CreditState.empty());

  Future<void> initialize({required String accountId}) async {
    try {
      safeEmit(const CreditState.loading());

      final (loanRepayment, loanAccounts, installmentAccounts) = await (
        _lendingInteractor.getLoanRepaymentDetails(accountId),
        _lendingInteractor.getAllLoanAccountDetails(),
        _lendingInteractor.getInstallmentAccounts(),
      ).wait;

      safeEmit(
        CreditState.loaded(
          loanRepayment: loanRepayment,
          loanAccounts: loanAccounts,
          installmentAccounts: installmentAccounts,
        ),
      );
    } on Object catch (e, s) {
      safeEmit(const CreditState.error());
      _logger.error(
        'Failed to fetch loan repayment details',
        error: e,
        stackTrace: s,
      );
      _commonErrorHandler.handleError(error: e, stackTrace: s);
    }
  }

  void onBackPress() => _navigationProvider.goBack();

  @override
  String toString() => 'LendingCubit';
}
