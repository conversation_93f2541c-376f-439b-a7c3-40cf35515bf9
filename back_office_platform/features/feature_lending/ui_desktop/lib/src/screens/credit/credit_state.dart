import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_lending_api/lending_api.dart';

part 'credit_state.freezed.dart';

@freezed
class CreditState with _$CreditState {
  const factory CreditState.loaded({
    required LoanRepayment loanRepayment,
    required LoanAccountDetails loanAccounts,
    required List<InstallmentAccount> installmentAccounts,
    List<CreditApplication>? creditApplicationList,
  }) = CreditStateLoaded;

  const factory CreditState.loading() = CreditLoadingState;

  const factory CreditState.error() = CreditStateError;

  const factory CreditState.empty() = CreditStateEmpty;
}
