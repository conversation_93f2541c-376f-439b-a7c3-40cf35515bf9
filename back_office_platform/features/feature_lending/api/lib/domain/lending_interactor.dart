import 'dart:typed_data';

import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_lending_api/domain/model/eligible_transaction.dart';
import 'package:wio_feature_lending_api/lending_api.dart';

/// Exposes the API to get lending information.
abstract class LendingInteractor {
  /// Returns all lending application information for a users
  /// using customer/business id
  ///
  /// [CreditApplication] - credit application information
  Future<List<CreditApplication>> getCreditLendingInfo();

  /// Returns all loan repayment details
  ///
  /// [LoanRepayment] - lending information
  Future<LoanRepayment> getLoanRepaymentDetails(String accountId);

  /// Returns status of loan amount updating
  ///
  /// [UpdateLoanAmountInput] - update loan Amount input params
  /// for updating loan amount
  Future<bool> updateLoanAmount(UpdateLoanAmountInput updateLoanAmountInput);

  /// Returns status that loan limit updated
  ///
  /// [CreditArrangementLimitsInput] - credit arrangement limits
  /// input for updating loan limit
  Future<bool> updateLoanLimits(
    CreditArrangementLimitsInput creditArrangementLimitsInput,
  );

  /// Returns loan account details
  ///
  /// [LoanAccount] - loan Account Details for loanAccountId
  Future<LoanAccount> getLoanAccountDetails(String loanAccountId);

  /// Returns all loan account details
  ///
  /// [LoanAccountDetails] - loan account details contains
  /// [CreditSummary] and List of [LoanAccount]
  Future<LoanAccountDetails> getAllLoanAccountDetails({
    bool includeInactive = false,
  });

  /// Returns easycash loan account transaction
  ///
  /// [CreditTransactionResponse] - Transaction response
  Future<CreditTransactionResponse> getLendingTransactions({
    required CreditTransactionsFilterRequestPayload filterRequestPayload,
    int pageNumber = 0,
    int pageSize = 20,
  });

  /// Returns credit application tracker info
  ///
  /// [ApplicationTracker] - application tracker based on customer
  Future<ApplicationTracker?> getApplicationTrackerInfo();

  /// Returns credit account monthly statments list with statment ID
  Future<StatementsDetails> getCreditStatements({required String accountId});

  /// Returns base 64 encoded pdf data
  Future<Uint8List> getCreditStatement({required String statementId});

  /// get available to borrow easy cash
  Future<Money?> getEasyCashAvailableLimit({
    required String loanAccountId,
  });

  /// Will return a list of installment accounts based on [productCategory] and
  /// if completed transactions should be included specified
  /// by [includeCompleted]
  Future<List<InstallmentAccount>> getInstallmentAccounts({
    InstallmentProductCategory productCategory =
        InstallmentProductCategory.split,
    bool includeCompleted = false,
    bool forceRefresh = false,
  });

  /// Returns a installment account by the given accountId
  Future<InstallmentAccount?> getInstallmentAccountById({
    required String accountId,
    bool forceRefresh = false,
  });

  /// Returns all transaction for the given credit account.
  ///
  /// It is not recommended to use this for accounts other than installments,
  /// where the transactions are limited
  Future<CreditTransactionResponse> getAllTransactionsForLendingAccount({
    required String accountId,
  });

  /// Returns current auto payment info for a loan account
  Future<CurrentAutoPayment> getCurrentAutoPayment({
    required String loanAccountId,
  });

  /// Returns a list of transactions eligible for installment payment plans
  Future<EligibleTransactions> getEligibleForInstallment({
    required InstallmentProductSubtype productSubType,
    bool forceRefresh = false,
  });
}
