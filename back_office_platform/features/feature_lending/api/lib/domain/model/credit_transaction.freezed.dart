// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'credit_transaction.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CreditTransaction {
  /// Random unique identifier of a [Transaction] for the record in db
  String get id => throw _privateConstructorUsedError;
  String get accountId => throw _privateConstructorUsedError;
  CreditTransactionType get transactionType =>
      throw _privateConstructorUsedError;

  /// Unique identifier of a [Transaction]
  String get transactionIdentifier => throw _privateConstructorUsedError;
  CreditTransactionStatus get status => throw _privateConstructorUsedError;
  Money get amount => throw _privateConstructorUsedError;
  CreditTransactionMode get transactionMode =>
      throw _privateConstructorUsedError;
  String? get referenceNumber => throw _privateConstructorUsedError;
  CreditTransactionSubType? get transactionSubType =>
      throw _privateConstructorUsedError;
  DateTime? get transactionDateTime => throw _privateConstructorUsedError;
  Money? get availableBalance => throw _privateConstructorUsedError;
  Money? get totalBalance => throw _privateConstructorUsedError;

  /// Create a copy of CreditTransaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreditTransactionCopyWith<CreditTransaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreditTransactionCopyWith<$Res> {
  factory $CreditTransactionCopyWith(
          CreditTransaction value, $Res Function(CreditTransaction) then) =
      _$CreditTransactionCopyWithImpl<$Res, CreditTransaction>;
  @useResult
  $Res call(
      {String id,
      String accountId,
      CreditTransactionType transactionType,
      String transactionIdentifier,
      CreditTransactionStatus status,
      Money amount,
      CreditTransactionMode transactionMode,
      String? referenceNumber,
      CreditTransactionSubType? transactionSubType,
      DateTime? transactionDateTime,
      Money? availableBalance,
      Money? totalBalance});
}

/// @nodoc
class _$CreditTransactionCopyWithImpl<$Res, $Val extends CreditTransaction>
    implements $CreditTransactionCopyWith<$Res> {
  _$CreditTransactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreditTransaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? accountId = null,
    Object? transactionType = null,
    Object? transactionIdentifier = null,
    Object? status = null,
    Object? amount = null,
    Object? transactionMode = null,
    Object? referenceNumber = freezed,
    Object? transactionSubType = freezed,
    Object? transactionDateTime = freezed,
    Object? availableBalance = freezed,
    Object? totalBalance = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      transactionType: null == transactionType
          ? _value.transactionType
          : transactionType // ignore: cast_nullable_to_non_nullable
              as CreditTransactionType,
      transactionIdentifier: null == transactionIdentifier
          ? _value.transactionIdentifier
          : transactionIdentifier // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as CreditTransactionStatus,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Money,
      transactionMode: null == transactionMode
          ? _value.transactionMode
          : transactionMode // ignore: cast_nullable_to_non_nullable
              as CreditTransactionMode,
      referenceNumber: freezed == referenceNumber
          ? _value.referenceNumber
          : referenceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionSubType: freezed == transactionSubType
          ? _value.transactionSubType
          : transactionSubType // ignore: cast_nullable_to_non_nullable
              as CreditTransactionSubType?,
      transactionDateTime: freezed == transactionDateTime
          ? _value.transactionDateTime
          : transactionDateTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      availableBalance: freezed == availableBalance
          ? _value.availableBalance
          : availableBalance // ignore: cast_nullable_to_non_nullable
              as Money?,
      totalBalance: freezed == totalBalance
          ? _value.totalBalance
          : totalBalance // ignore: cast_nullable_to_non_nullable
              as Money?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreditTransactionImplCopyWith<$Res>
    implements $CreditTransactionCopyWith<$Res> {
  factory _$$CreditTransactionImplCopyWith(_$CreditTransactionImpl value,
          $Res Function(_$CreditTransactionImpl) then) =
      __$$CreditTransactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String accountId,
      CreditTransactionType transactionType,
      String transactionIdentifier,
      CreditTransactionStatus status,
      Money amount,
      CreditTransactionMode transactionMode,
      String? referenceNumber,
      CreditTransactionSubType? transactionSubType,
      DateTime? transactionDateTime,
      Money? availableBalance,
      Money? totalBalance});
}

/// @nodoc
class __$$CreditTransactionImplCopyWithImpl<$Res>
    extends _$CreditTransactionCopyWithImpl<$Res, _$CreditTransactionImpl>
    implements _$$CreditTransactionImplCopyWith<$Res> {
  __$$CreditTransactionImplCopyWithImpl(_$CreditTransactionImpl _value,
      $Res Function(_$CreditTransactionImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditTransaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? accountId = null,
    Object? transactionType = null,
    Object? transactionIdentifier = null,
    Object? status = null,
    Object? amount = null,
    Object? transactionMode = null,
    Object? referenceNumber = freezed,
    Object? transactionSubType = freezed,
    Object? transactionDateTime = freezed,
    Object? availableBalance = freezed,
    Object? totalBalance = freezed,
  }) {
    return _then(_$CreditTransactionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      transactionType: null == transactionType
          ? _value.transactionType
          : transactionType // ignore: cast_nullable_to_non_nullable
              as CreditTransactionType,
      transactionIdentifier: null == transactionIdentifier
          ? _value.transactionIdentifier
          : transactionIdentifier // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as CreditTransactionStatus,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Money,
      transactionMode: null == transactionMode
          ? _value.transactionMode
          : transactionMode // ignore: cast_nullable_to_non_nullable
              as CreditTransactionMode,
      referenceNumber: freezed == referenceNumber
          ? _value.referenceNumber
          : referenceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionSubType: freezed == transactionSubType
          ? _value.transactionSubType
          : transactionSubType // ignore: cast_nullable_to_non_nullable
              as CreditTransactionSubType?,
      transactionDateTime: freezed == transactionDateTime
          ? _value.transactionDateTime
          : transactionDateTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      availableBalance: freezed == availableBalance
          ? _value.availableBalance
          : availableBalance // ignore: cast_nullable_to_non_nullable
              as Money?,
      totalBalance: freezed == totalBalance
          ? _value.totalBalance
          : totalBalance // ignore: cast_nullable_to_non_nullable
              as Money?,
    ));
  }
}

/// @nodoc

class _$CreditTransactionImpl implements _CreditTransaction {
  const _$CreditTransactionImpl(
      {required this.id,
      required this.accountId,
      required this.transactionType,
      required this.transactionIdentifier,
      required this.status,
      required this.amount,
      required this.transactionMode,
      this.referenceNumber,
      this.transactionSubType,
      this.transactionDateTime,
      this.availableBalance,
      this.totalBalance});

  /// Random unique identifier of a [Transaction] for the record in db
  @override
  final String id;
  @override
  final String accountId;
  @override
  final CreditTransactionType transactionType;

  /// Unique identifier of a [Transaction]
  @override
  final String transactionIdentifier;
  @override
  final CreditTransactionStatus status;
  @override
  final Money amount;
  @override
  final CreditTransactionMode transactionMode;
  @override
  final String? referenceNumber;
  @override
  final CreditTransactionSubType? transactionSubType;
  @override
  final DateTime? transactionDateTime;
  @override
  final Money? availableBalance;
  @override
  final Money? totalBalance;

  @override
  String toString() {
    return 'CreditTransaction(id: $id, accountId: $accountId, transactionType: $transactionType, transactionIdentifier: $transactionIdentifier, status: $status, amount: $amount, transactionMode: $transactionMode, referenceNumber: $referenceNumber, transactionSubType: $transactionSubType, transactionDateTime: $transactionDateTime, availableBalance: $availableBalance, totalBalance: $totalBalance)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreditTransactionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.transactionType, transactionType) ||
                other.transactionType == transactionType) &&
            (identical(other.transactionIdentifier, transactionIdentifier) ||
                other.transactionIdentifier == transactionIdentifier) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.transactionMode, transactionMode) ||
                other.transactionMode == transactionMode) &&
            (identical(other.referenceNumber, referenceNumber) ||
                other.referenceNumber == referenceNumber) &&
            (identical(other.transactionSubType, transactionSubType) ||
                other.transactionSubType == transactionSubType) &&
            (identical(other.transactionDateTime, transactionDateTime) ||
                other.transactionDateTime == transactionDateTime) &&
            (identical(other.availableBalance, availableBalance) ||
                other.availableBalance == availableBalance) &&
            (identical(other.totalBalance, totalBalance) ||
                other.totalBalance == totalBalance));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      accountId,
      transactionType,
      transactionIdentifier,
      status,
      amount,
      transactionMode,
      referenceNumber,
      transactionSubType,
      transactionDateTime,
      availableBalance,
      totalBalance);

  /// Create a copy of CreditTransaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreditTransactionImplCopyWith<_$CreditTransactionImpl> get copyWith =>
      __$$CreditTransactionImplCopyWithImpl<_$CreditTransactionImpl>(
          this, _$identity);
}

abstract class _CreditTransaction implements CreditTransaction {
  const factory _CreditTransaction(
      {required final String id,
      required final String accountId,
      required final CreditTransactionType transactionType,
      required final String transactionIdentifier,
      required final CreditTransactionStatus status,
      required final Money amount,
      required final CreditTransactionMode transactionMode,
      final String? referenceNumber,
      final CreditTransactionSubType? transactionSubType,
      final DateTime? transactionDateTime,
      final Money? availableBalance,
      final Money? totalBalance}) = _$CreditTransactionImpl;

  /// Random unique identifier of a [Transaction] for the record in db
  @override
  String get id;
  @override
  String get accountId;
  @override
  CreditTransactionType get transactionType;

  /// Unique identifier of a [Transaction]
  @override
  String get transactionIdentifier;
  @override
  CreditTransactionStatus get status;
  @override
  Money get amount;
  @override
  CreditTransactionMode get transactionMode;
  @override
  String? get referenceNumber;
  @override
  CreditTransactionSubType? get transactionSubType;
  @override
  DateTime? get transactionDateTime;
  @override
  Money? get availableBalance;
  @override
  Money? get totalBalance;

  /// Create a copy of CreditTransaction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreditTransactionImplCopyWith<_$CreditTransactionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
