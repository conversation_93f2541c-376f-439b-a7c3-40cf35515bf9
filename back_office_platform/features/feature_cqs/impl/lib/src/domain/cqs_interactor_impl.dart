import 'package:wio_feature_cqs_api/cqs_api.dart';

class CqsInteractorImpl implements CqsInteractor {
  final CqsRepository _repository;

  CqsInteractorImpl({
    required CqsRepository repository,
  }) : _repository = repository;

  @override
  Future<QuestionnaireListInfo> getQuestionnaire({
    required int page,
    required int size,
  }) =>
      _repository.getQuestionnaire(page: page, size: size);

  @override
  Future<UserQuestionnaireFlowInfo> getUserQuestionnaireResponse({
    required String questionnaireFlowInstanceId,
  }) =>
      _repository.getUserQuestionnaireResponse(
        questionnaireFlowInstanceId: questionnaireFlowInstanceId,
      );
}
