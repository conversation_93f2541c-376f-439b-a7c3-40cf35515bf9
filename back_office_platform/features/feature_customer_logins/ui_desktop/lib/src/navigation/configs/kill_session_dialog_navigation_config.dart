import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_customer_logins_api/customer_logins_api.dart';

part 'kill_session_dialog_navigation_config.freezed.dart';

@freezed
class KillSessionDialogNavigationConfig
    with _$KillSessionDialogNavigationConfig
    implements DialogNavigationConfig<bool?> {
  const factory KillSessionDialogNavigationConfig({
    required String requester,
    required LoginDevicesInfo loginDevicesInfo,
  }) = _KillSessionDialogNavigationConfig;

  const KillSessionDialogNavigationConfig._();

  @override
  String get feature => CustomerLoginsFeatureNavigationConfig.name;

  @override
  String toString() => 'KillSessionDialogNavigationConfig{}';
}
