// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivery_schema.swagger.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ErrorMessage _$ErrorMessageFromJson(Map<String, dynamic> json) => ErrorMessage(
      id: json['Id'] as String?,
      code: json['Code'] as String?,
      description: json['Description'] as String?,
      path: json['Path'] as String?,
      additionalInfo: json['AdditionalInfo'] as String?,
      context: json['Context'],
    );

Map<String, dynamic> _$ErrorMessageToJson(ErrorMessage instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'Id': value,
      if (instance.code case final value?) 'Code': value,
      if (instance.description case final value?) 'Description': value,
      if (instance.path case final value?) 'Path': value,
      if (instance.additionalInfo case final value?) 'AdditionalInfo': value,
      if (instance.context case final value?) 'Context': value,
    };

DeliveryTrackerResponse _$DeliveryTrackerResponseFromJson(
        Map<String, dynamic> json) =>
    DeliveryTrackerResponse(
      deliveryReference: json['DeliveryReference'] as String,
      status: deliveryTrackerResponseStatusFromJson(json['Status']),
      externalReference: json['ExternalReference'] as String?,
      externalReferenceType:
          deliveryTrackerResponseExternalReferenceTypeNullableFromJson(
              json['ExternalReferenceType']),
      customerId: json['CustomerId'] as String,
      expectedDeliveryDate:
          DateTime.parse(json['ExpectedDeliveryDate'] as String),
      product: deliveryTrackerResponseProductNullableFromJson(json['Product']),
      waybillNumber: json['WaybillNumber'] as String?,
    );

Map<String, dynamic> _$DeliveryTrackerResponseToJson(
        DeliveryTrackerResponse instance) =>
    <String, dynamic>{
      'DeliveryReference': instance.deliveryReference,
      if (deliveryTrackerResponseStatusToJson(instance.status)
          case final value?)
        'Status': value,
      if (instance.externalReference case final value?)
        'ExternalReference': value,
      if (deliveryTrackerResponseExternalReferenceTypeNullableToJson(
              instance.externalReferenceType)
          case final value?)
        'ExternalReferenceType': value,
      'CustomerId': instance.customerId,
      if (_dateToJson(instance.expectedDeliveryDate) case final value?)
        'ExpectedDeliveryDate': value,
      if (deliveryTrackerResponseProductNullableToJson(instance.product)
          case final value?)
        'Product': value,
      if (instance.waybillNumber case final value?) 'WaybillNumber': value,
    };
