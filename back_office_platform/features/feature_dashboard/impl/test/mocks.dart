import 'package:mocktail/mocktail.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_dashboard_api/dashboard_api.dart';
import 'package:wio_feature_dashboard_impl/src/data/models/schema_dtos/delivery_schema.swagger.dart';
import 'package:wio_feature_dashboard_impl/src/mapper/delivery_mapper.dart';
import 'package:wio_feature_dashboard_impl/src/services/delivery_service.dart';

class MockRestApiClient extends Mock implements IRestApiClient {}

class FakeRestApiRequest extends Fake implements RestApiRequest {}

class MockErrorReporter extends Mock implements ErrorReporter {}

class MockDeliveryService extends Mock implements DeliveryService {}

class MockDeliveryMapper extends Mock implements DeliveryMapper {}

class MockDeliveryRepository extends Mock implements DeliveryRepository {}

class FakeHttpException extends Fake implements HttpRequestException {
  @override
  String toString() {
    return 'FakeHttpException';
  }
}

void registerFallBackValues() {
  registerFallbackValue(FakeRestApiRequest());
}

final date = DateTime(2024);

final deliveryTrackerDto = DeliveryTrackerResponse(
  customerId: 'testCustomerId',
  deliveryReference: 'testDeliveryRef',
  expectedDeliveryDate: date,
  externalReference: 'extRef',
  waybillNumber: 'waybill123',
  externalReferenceType: DeliveryTrackerResponseExternalReferenceType.card,
  status: DeliveryTrackerResponseStatus.delivered,
  product: DeliveryTrackerResponseProduct.retail,
);

final deliveryTracker = DeliveryTracker(
  customerId: 'testCustomerId',
  deliveryReference: 'testDeliveryRef',
  expectedDeliveryDate: date,
  externalReference: 'extRef',
  waybillNumber: 'waybill123',
  externalReferenceType: ExternalReferenceType.card,
  status: DeliveryStatus.delivered,
  product: DeliveryProduct.retail,
);

const deliveryTrackerResponseJson = {
  'DeliveryReference': 'ref1',
  'Status': 'INITIATED',
  'ExternalReference': 'string',
  'ExternalReferenceType': 'CARD',
  'CustomerId': 'string',
  'ExpectedDeliveryDate': '2024-06-20',
  'Product': 'INTERNAL',
  'WaybillNumber': 'string',
};

const deliveryTrackersResponseJson = [
  {
    'DeliveryReference': 'ref1',
    'Status': 'INITIATED',
    'ExternalReference': 'string',
    'ExternalReferenceType': 'CARD',
    'CustomerId': 'string',
    'ExpectedDeliveryDate': '2024-06-20',
    'Product': 'INTERNAL',
    'WaybillNumber': 'string',
  },
  {
    'DeliveryReference': 'ref2',
    'Status': 'INITIATED',
    'ExternalReference': 'string',
    'ExternalReferenceType': 'CARD',
    'CustomerId': 'string',
    'ExpectedDeliveryDate': '2024-06-20',
    'Product': 'INTERNAL',
    'WaybillNumber': 'string',
  },
  {
    'DeliveryReference': 'ref3',
    'Status': 'INITIATED',
    'ExternalReference': 'string',
    'ExternalReferenceType': 'CARD',
    'CustomerId': 'string',
    'ExpectedDeliveryDate': '2024-06-20',
    'Product': 'INTERNAL',
    'WaybillNumber': 'string',
  },
  {
    'DeliveryReference': 'ref4',
    'Status': 'INITIATED',
    'ExternalReference': 'string',
    'ExternalReferenceType': 'CARD',
    'CustomerId': 'string',
    'ExpectedDeliveryDate': '2024-06-20',
    'Product': 'INTERNAL',
    'WaybillNumber': 'string',
  }
];
