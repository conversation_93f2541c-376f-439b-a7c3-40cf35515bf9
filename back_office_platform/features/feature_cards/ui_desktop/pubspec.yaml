name: wio_feature_cards_ui_desktop
version: 0.0.1
publish_to: none
environment:
  flutter: 3.27.3
  sdk: '>=3.6.0 <4.0.0'
  
dependencies:
  collection: 1.19.0
  common_feature_toggle_api:
    path: ../../../../common/tools/feature_toggle/api
  di:
    path: ../../../../core/di
  flutter:
    sdk: flutter
  flutter_bloc: 9.0.0
  freezed_annotation: 2.4.4
  logging_api:
    path: ../../../../core/logging/api
  ui:
    path: ../../../../core/ui
  ui_kit_legacy_core:
    path: ../../../../ui_kit_legacy/ui_kit_core
  ui_kit_legacy_mobile:
    path: ../../../../ui_kit_legacy/ui_kit_mobile
  wio_app_core_api:
    path: ../../../../core/app_core/api
  wio_core_navigation_api:
    path: ../../../../core/navigation/api
  wio_core_navigation_ui:
    path: ../../../../core/navigation/ui
  wio_feature_accounts_api:
    path: ../../../features/feature_accounts/api
  wio_feature_app_common_api:
    path: ../../../common/feature_app_common/api
  wio_feature_app_common_ui_desktop:
    path: ../../../common/feature_app_common/ui_desktop
  wio_feature_cards_api:
    path: ../api
  wio_feature_common_error_handler_api:
    path: ../../../common/feature_common_error_handler/api
  wio_feature_common_toast_message_api:
    path: ../../../../common/tools/toast_message/api
  wio_feature_customer_idv_api:
    path: ../../feature_customer_idv/api
  wio_feature_disputes_api: 
    path: ../../feature_disputes/api
  wio_feature_lending_api:
    path: ../../feature_lending/api
  wio_feature_navigation_rail_ui_desktop:
    path: ../../feature_navigation_rail/ui_desktop
  wio_feature_permissions_api:
    path: ../../feature_permissions/api
  wio_feature_service_request_api:
    path: ../../feature_service_request/api
  wio_feature_service_request_ui_desktop:
    path: ../../feature_service_request/ui_desktop
  wio_feature_transactions_api:
    path: ../../feature_transactions/api
  wio_feature_transactions_ui_desktop:
    path: ../../feature_transactions/ui_desktop
dev_dependencies:
  bloc_test: 10.0.0
  build_runner: 2.4.14
  core_lints:
    path: ../../../../tooling/core_lints
  flutter_lints: 4.0.0
  flutter_test:
    sdk: flutter
  freezed: 2.5.7
  mocktail: 1.0.4
  test: 1.25.8
  tests:
    path: ../../../../core/tests/impl
flutter:
  uses-material-design: true