import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_cards_api/navigation/cards_feature_navigation_config.dart';

part 'card_details_navigation_config.freezed.dart';

/// Entry point to the card details page.
@freezed
class CardDetailsNavigationConfig extends ScreenNavigationConfig
    with _$CardDetailsNavigationConfig {
  static const screenName = 'card_details';

  const factory CardDetailsNavigationConfig({
    required int cardToken,
  }) = _CardDetailsNavigationConfig;

  const CardDetailsNavigationConfig._()
      : super(id: screenName, feature: CardsFeatureNavigationConfig.name);

  @override
  String toString() {
    return screenName;
  }
}
