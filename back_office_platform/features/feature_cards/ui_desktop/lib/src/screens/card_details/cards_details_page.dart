import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui/page/base_page.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_cards_ui_desktop/src/screens/card_details/cubit/card_details_cubit.dart';
import 'package:wio_feature_cards_ui_desktop/src/screens/card_details/cubit/card_details_state.dart';
import 'package:wio_feature_cards_ui_desktop/src/screens/card_details/widget/card_details_loading_view.dart';
import 'package:wio_feature_cards_ui_desktop/src/screens/card_details/widget/card_details_view.dart';
import 'package:wio_feature_navigation_rail_ui_desktop/feature_navigation_rail_desktop_ui.dart';

class CardDetailsPage extends BasePage<CardDetailsState, CardDetailsCubit> {
  final String cardToken;

  const CardDetailsPage({
    required this.cardToken,
    super.key,
  });

  @override
  void initBloc(CardDetailsCubit bloc) {
    super.initBloc(bloc);
    bloc.init(cardToken: cardToken);
  }

  @override
  Widget buildPage(
    BuildContext context,
    CardDetailsCubit bloc,
    CardDetailsState state,
  ) {
    return BackOfficeContentPage(
      content: Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: _CardDetailsBody(),
          ),
        ),
      ),
    );
  }

  @override
  CardDetailsCubit createBloc() => DependencyProvider.get<CardDetailsCubit>();
}

class _CardDetailsBody extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final cubit = context.read<CardDetailsCubit>();
    final newState = context.watch<CardDetailsCubit>().state;

    return newState.map(
      loaded: (it) => CardDetailsView(
        cardInfo: it.cardInfo,
        reasonInfoList: it.reasonInfoList,
        accountType: it.accountType,
        accountIds: [
          ...it.currentAccountIds,
          ...it.creditAccountIds,
        ],
        isBusinessCustomer: it.isBusinessCustomer,
      ),
      loading: (it) => const CardDetailsLoadingView(),
      error: (it) => Center(
        child: GenericError(
          const GenericErrorModel(
            title: 'Could not fetch card details',
            subtitle: 'Please retry!',
            buttonLabel: 'Retry',
          ),
          onPressed: () {
            cubit.init(cardToken: it.cardToken);
          },
        ),
      ),
    );
  }
}
