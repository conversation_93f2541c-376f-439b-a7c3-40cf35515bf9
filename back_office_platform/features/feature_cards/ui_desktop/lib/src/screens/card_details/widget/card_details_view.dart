import 'package:collection/collection.dart';
import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_accounts_api/domain/models/account_enums.dart';
import 'package:wio_feature_accounts_api/domain/models/reason_info.dart';
import 'package:wio_feature_cards_api/card_feature_api.dart';
import 'package:wio_feature_cards_ui_desktop/src/screens/card_details/cubit/card_details_cubit.dart';
import 'package:wio_feature_cards_ui_desktop/src/screens/card_details/details_content_widget.dart';
import 'package:wio_feature_cards_ui_desktop/src/screens/card_details/transactions_content_widget.dart';
import 'package:wio_feature_cards_ui_desktop/src/screens/card_details/widget/card_status_dialog.dart';
import 'package:wio_feature_cards_ui_desktop/src/screens/card_details/widget/overflow_menu_widget.dart';
import 'package:wio_feature_cards_ui_desktop/src/screens/card_replacement/card_replacement_widget.dart';
import 'package:wio_feature_cards_ui_desktop/src/screens/common/extensions.dart';
import 'package:wio_feature_permissions_api/permissions_api.dart';
import 'package:wio_feature_service_request_api/service_request_api.dart';
import 'package:wio_feature_service_request_ui_desktop/feature_service_request_desktop_ui.dart';

class CardDetailsView extends StatelessWidget {
  final CardInfo cardInfo;
  final AccountType? accountType;
  final List<ReasonInfo> reasonInfoList;
  final List<String> accountIds;
  final bool isBusinessCustomer;

  const CardDetailsView({
    required this.cardInfo,
    required this.reasonInfoList,
    required this.accountIds,
    required this.isBusinessCustomer,
    this.accountType,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<CardDetailsCubit>();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 10.0),
          child: InkWell(
            customBorder: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            onTap: cubit.onBackPress,
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CompanyIcon(
                  CompanyIconModel(
                    icon: GraphicAssetPointer.icon(
                      CompanyIconPointer.chevron_left,
                    ),
                    size: CompanyIconSize.xSmall,
                  ),
                ),
                Label(
                  model: LabelModel(
                    text: 'Back',
                    textStyle: CompanyTextStylePointer.b3,
                    color: CompanyColorPointer.primary3,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 8.0),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                children: [
                  CompanyIcon(
                    CompanyIconModel(
                      icon: cardInfo.getIcon(),
                      size: CompanyIconSize.medium,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Label(
                    model: LabelModel(
                      text: cardInfo.cardName,
                      textStyle: CompanyTextStylePointer.h4,
                      color: CompanyColorPointer.primary3,
                    ),
                  ),
                ],
              ),
            ),
            _BlockCard(
              cardInfo: cardInfo,
              reasonInfoList: reasonInfoList,
              accountIds: accountIds,
              isBusinessCustomer: isBusinessCustomer,
            ),
          ],
        ),
        const SizedBox(
          height: 12,
        ),
        _Content(
          cardInfo: cardInfo,
          accountType: accountType,
          accountIds: accountIds,
        ),
      ],
    );
  }
}

enum CardDetailsOverFlowEnum {
  physicalCardReplacement,
}

class _BlockCard extends StatefulWidget {
  final CardInfo cardInfo;
  final List<ReasonInfo> reasonInfoList;
  final List<String> accountIds;
  final bool isBusinessCustomer;

  const _BlockCard({
    required this.cardInfo,
    required this.reasonInfoList,
    required this.accountIds,
    required this.isBusinessCustomer,
  });

  @override
  State<_BlockCard> createState() => _BlockCardState();
}

class _BlockCardState extends State<_BlockCard> {
  var _formConfigs = <FormConfig>[];
  late FormConfig? _reportFraudFormConfig;
  late CardFraudFormParams fraudFromServiceEntity;

  @override
  void initState() {
    super.initState();
    _formConfigs = DependencyProvider.get<ServiceRequestFormRegister>()
            .tryHandle(widget.cardInfo) ??
        [];
    fraudFromServiceEntity = CardFraudFormParams(
      cardInfo: widget.cardInfo,
      accountIds: widget.accountIds,
    );
    final reportFraudConfigs =
        DependencyProvider.get<ServiceRequestFormRegister>()
                .tryHandle(fraudFromServiceEntity) ??
            [];
    _reportFraudFormConfig =
        reportFraudConfigs.isNotEmpty ? reportFraudConfigs.first : null;
  }

  @override
  Widget build(BuildContext context) {
    final isCardActiveOrUnFreezed =
        widget.cardInfo.status == CardStatus.unFreezed ||
            widget.cardInfo.status == CardStatus.active;
    final formItems = _formConfigs.map(
      (config) => OverflowMenuItem(
        label: config.title,
        value: config.processName,
      ),
    );

    final overflowItems = [...formItems];

    return Row(
      children: [
        if (_reportFraudFormConfig != null && !widget.isBusinessCustomer)
          Button(
            model: const ButtonModel(
              title: 'Report fraud',
              size: ButtonSize.xSmall,
              styleOverride: ButtonStyleOverride(
                active: ButtonStateColorScheme(
                  foreground: CompanyColorPointer.surface7,
                  background: CompanyColorPointer.secondary13,
                ),
              ),
            ),
            onPressed: () {
              showDialog<void>(
                context: context,
                builder: (context) => SrModal(
                  formConfig: _reportFraudFormConfig,
                  serviceRequestEntity: fraudFromServiceEntity,
                ),
              );
            },
          ),
        const SizedBox(width: 8),
        if (isCardActiveOrUnFreezed)
          Button(
            model: ButtonModel(
              title: 'Block card',
              size: ButtonSize.xSmall,
              styleOverride: const ButtonStyleOverride(
                active: ButtonStateColorScheme(
                  foreground: CompanyColorPointer.surface7,
                  background: CompanyColorPointer.secondary13,
                ),
              ),
              graphicAssetPointer: CompanyIconPointer.security.toGraphicAsset(),
              iconSize: CompanyIconSize.small,
            ),
            onPressed: () {
              _showBlockCardDialogAndPop(
                context: context,
                cardInfo: widget.cardInfo,
                cardStatusType: CardStatusType.block,
                reasonInfoList: widget.reasonInfoList,
              );
            },
          ),
        if (widget.cardInfo.status == CardStatus.blockedBySupportTeam)
          Button(
            model: ButtonModel(
              title: 'Unblock card',
              size: ButtonSize.xSmall,
              styleOverride: const ButtonStyleOverride(
                active: ButtonStateColorScheme(
                  foreground: CompanyColorPointer.surface7,
                  background: CompanyColorPointer.secondary13,
                ),
              ),
              graphicAssetPointer: CompanyIconPointer.security.toGraphicAsset(),
              iconSize: CompanyIconSize.small,
            ),
            onPressed: () {
              _showBlockCardDialogAndPop(
                context: context,
                cardInfo: widget.cardInfo,
                cardStatusType: CardStatusType.unblock,
                reasonInfoList: widget.reasonInfoList,
              );
            },
          ),
        const SizedBox(width: 8),
        if (isCardActiveOrUnFreezed || _formConfigs.isNotEmpty)
          OverflowMenuWidget(
            items: overflowItems,
            onItemClicked: (item) {
              final cardOverflowItem = CardDetailsOverFlowEnum.values
                  .firstWhereOrNull((enumItem) => enumItem.name == item.value);
              final formConfigItem = _formConfigs.firstWhereOrNull(
                (config) => config.processName == item.value,
              );

              if (cardOverflowItem == null && formConfigItem == null) return;

              if (cardOverflowItem != null) {
                _onClick(context, cardOverflowItem);
                return;
              }

              Navigator.of(context).pop();

              showDialog<void>(
                context: context,
                builder: (context) => SrModal(
                  formConfig: formConfigItem,
                  serviceRequestEntity: widget.cardInfo,
                ),
              );
            },
          ),
      ],
    );
  }

  void _onClick(BuildContext context, CardDetailsOverFlowEnum overflow) {
    if (overflow == CardDetailsOverFlowEnum.physicalCardReplacement) {
      CardReplacementView.show<void>(
        context: context,
        cardInfo: widget.cardInfo,
        reasonInfo: widget.reasonInfoList,
      );
    }
  }

  Future<void> _showBlockCardDialogAndPop({
    required BuildContext context,
    required CardInfo cardInfo,
    required CardStatusType cardStatusType,
    required List<ReasonInfo> reasonInfoList,
  }) async {
    final cubit = context.read<CardDetailsCubit>();

    await CompanyModal.showModal<void>(
      context: context,
      routeSettings:
          const RouteSettings(arguments: 'block_card_dialog_and_pop'),
      builder: (_) => CardStatusDialog(
        cardInfo: cardInfo,
        reasonInfoList: reasonInfoList,
        statusType: cardStatusType,
        onUpdateCardStatus: (token, statusType, reasonInfo) {
          cubit.updateCardStatus(
            cardNumber: cardInfo.maskedPan,
            cardToken: token,
            reasonInfo: reasonInfo,
            cardStatusType: statusType,
          );
        },
      ),
    );
  }
}

class _Content extends StatefulWidget {
  final CardInfo cardInfo;
  final AccountType? accountType;
  final List<String> accountIds;

  const _Content({
    required this.cardInfo,
    required this.accountIds,
    this.accountType,
  });

  @override
  State<_Content> createState() => _ContentState();
}

class _ContentState extends State<_Content> {
  final List<String> tabNames = ['Details'];
  int _selectedIndex = 0;

  @override
  void initState() {
    final canReadTransactions = context
        .read<PermissionsResolver>()
        .resolve(Permissions.viewTransactions);

    if (canReadTransactions) {
      tabNames.add('Transactions');
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Tabs(
          TabsModel(
            tabNames: tabNames,
            variant: TabsVariant.smallConnected,
            selectedIndex: _selectedIndex,
            inactiveTextColor: CompanyColorPointer.surface4,
            activeBackgroundColor: CompanyColorPointer.primary3,
            activeTextColor: CompanyColorPointer.surface2,
          ),
          onTabPressed: (index) {
            setState(() {
              _selectedIndex = index;
            });
          },
        ),
        const SizedBox(
          height: 16,
        ),
        if (_selectedIndex == 0)
          DetailsContent(
            cardInfo: widget.cardInfo,
            accountType: widget.accountType,
          )
        else
          TransactionsContent(
            cardToken: widget.cardInfo.token.toString(),
            accountsIds: widget.accountIds,
          ),
      ],
    );
  }
}
