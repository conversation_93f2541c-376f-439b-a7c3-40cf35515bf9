// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'card_details_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CardDetailsState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            CardInfo cardInfo,
            List<String> currentAccountIds,
            List<String> creditAccountIds,
            bool isBusinessCustomer,
            bool isCardReplacementEnabled,
            AccountType? accountType,
            List<ReasonInfo> reasonInfoList)
        loaded,
    required TResult Function() loading,
    required TResult Function(String cardToken) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            CardInfo cardInfo,
            List<String> currentAccountIds,
            List<String> creditAccountIds,
            bool isBusinessCustomer,
            bool isCardReplacementEnabled,
            AccountType? accountType,
            List<ReasonInfo> reasonInfoList)?
        loaded,
    TResult? Function()? loading,
    TResult? Function(String cardToken)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            CardInfo cardInfo,
            List<String> currentAccountIds,
            List<String> creditAccountIds,
            bool isBusinessCustomer,
            bool isCardReplacementEnabled,
            AccountType? accountType,
            List<ReasonInfo> reasonInfoList)?
        loaded,
    TResult Function()? loading,
    TResult Function(String cardToken)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CardDetailsLoadedState value) loaded,
    required TResult Function(CardDetailsLoadingState value) loading,
    required TResult Function(CardDetailsStateError value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CardDetailsLoadedState value)? loaded,
    TResult? Function(CardDetailsLoadingState value)? loading,
    TResult? Function(CardDetailsStateError value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CardDetailsLoadedState value)? loaded,
    TResult Function(CardDetailsLoadingState value)? loading,
    TResult Function(CardDetailsStateError value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CardDetailsStateCopyWith<$Res> {
  factory $CardDetailsStateCopyWith(
          CardDetailsState value, $Res Function(CardDetailsState) then) =
      _$CardDetailsStateCopyWithImpl<$Res, CardDetailsState>;
}

/// @nodoc
class _$CardDetailsStateCopyWithImpl<$Res, $Val extends CardDetailsState>
    implements $CardDetailsStateCopyWith<$Res> {
  _$CardDetailsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CardDetailsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$CardDetailsLoadedStateImplCopyWith<$Res> {
  factory _$$CardDetailsLoadedStateImplCopyWith(
          _$CardDetailsLoadedStateImpl value,
          $Res Function(_$CardDetailsLoadedStateImpl) then) =
      __$$CardDetailsLoadedStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {CardInfo cardInfo,
      List<String> currentAccountIds,
      List<String> creditAccountIds,
      bool isBusinessCustomer,
      bool isCardReplacementEnabled,
      AccountType? accountType,
      List<ReasonInfo> reasonInfoList});

  $CardInfoCopyWith<$Res> get cardInfo;
}

/// @nodoc
class __$$CardDetailsLoadedStateImplCopyWithImpl<$Res>
    extends _$CardDetailsStateCopyWithImpl<$Res, _$CardDetailsLoadedStateImpl>
    implements _$$CardDetailsLoadedStateImplCopyWith<$Res> {
  __$$CardDetailsLoadedStateImplCopyWithImpl(
      _$CardDetailsLoadedStateImpl _value,
      $Res Function(_$CardDetailsLoadedStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CardDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardInfo = null,
    Object? currentAccountIds = null,
    Object? creditAccountIds = null,
    Object? isBusinessCustomer = null,
    Object? isCardReplacementEnabled = null,
    Object? accountType = freezed,
    Object? reasonInfoList = null,
  }) {
    return _then(_$CardDetailsLoadedStateImpl(
      cardInfo: null == cardInfo
          ? _value.cardInfo
          : cardInfo // ignore: cast_nullable_to_non_nullable
              as CardInfo,
      currentAccountIds: null == currentAccountIds
          ? _value._currentAccountIds
          : currentAccountIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      creditAccountIds: null == creditAccountIds
          ? _value._creditAccountIds
          : creditAccountIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isBusinessCustomer: null == isBusinessCustomer
          ? _value.isBusinessCustomer
          : isBusinessCustomer // ignore: cast_nullable_to_non_nullable
              as bool,
      isCardReplacementEnabled: null == isCardReplacementEnabled
          ? _value.isCardReplacementEnabled
          : isCardReplacementEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      accountType: freezed == accountType
          ? _value.accountType
          : accountType // ignore: cast_nullable_to_non_nullable
              as AccountType?,
      reasonInfoList: null == reasonInfoList
          ? _value._reasonInfoList
          : reasonInfoList // ignore: cast_nullable_to_non_nullable
              as List<ReasonInfo>,
    ));
  }

  /// Create a copy of CardDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CardInfoCopyWith<$Res> get cardInfo {
    return $CardInfoCopyWith<$Res>(_value.cardInfo, (value) {
      return _then(_value.copyWith(cardInfo: value));
    });
  }
}

/// @nodoc

class _$CardDetailsLoadedStateImpl implements CardDetailsLoadedState {
  const _$CardDetailsLoadedStateImpl(
      {required this.cardInfo,
      required final List<String> currentAccountIds,
      required final List<String> creditAccountIds,
      required this.isBusinessCustomer,
      this.isCardReplacementEnabled = false,
      this.accountType,
      final List<ReasonInfo> reasonInfoList = const <ReasonInfo>[]})
      : _currentAccountIds = currentAccountIds,
        _creditAccountIds = creditAccountIds,
        _reasonInfoList = reasonInfoList;

  @override
  final CardInfo cardInfo;
  final List<String> _currentAccountIds;
  @override
  List<String> get currentAccountIds {
    if (_currentAccountIds is EqualUnmodifiableListView)
      return _currentAccountIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_currentAccountIds);
  }

  final List<String> _creditAccountIds;
  @override
  List<String> get creditAccountIds {
    if (_creditAccountIds is EqualUnmodifiableListView)
      return _creditAccountIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_creditAccountIds);
  }

  @override
  final bool isBusinessCustomer;
  @override
  @JsonKey()
  final bool isCardReplacementEnabled;
  @override
  final AccountType? accountType;
  final List<ReasonInfo> _reasonInfoList;
  @override
  @JsonKey()
  List<ReasonInfo> get reasonInfoList {
    if (_reasonInfoList is EqualUnmodifiableListView) return _reasonInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reasonInfoList);
  }

  @override
  String toString() {
    return 'CardDetailsState.loaded(cardInfo: $cardInfo, currentAccountIds: $currentAccountIds, creditAccountIds: $creditAccountIds, isBusinessCustomer: $isBusinessCustomer, isCardReplacementEnabled: $isCardReplacementEnabled, accountType: $accountType, reasonInfoList: $reasonInfoList)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CardDetailsLoadedStateImpl &&
            (identical(other.cardInfo, cardInfo) ||
                other.cardInfo == cardInfo) &&
            const DeepCollectionEquality()
                .equals(other._currentAccountIds, _currentAccountIds) &&
            const DeepCollectionEquality()
                .equals(other._creditAccountIds, _creditAccountIds) &&
            (identical(other.isBusinessCustomer, isBusinessCustomer) ||
                other.isBusinessCustomer == isBusinessCustomer) &&
            (identical(
                    other.isCardReplacementEnabled, isCardReplacementEnabled) ||
                other.isCardReplacementEnabled == isCardReplacementEnabled) &&
            (identical(other.accountType, accountType) ||
                other.accountType == accountType) &&
            const DeepCollectionEquality()
                .equals(other._reasonInfoList, _reasonInfoList));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      cardInfo,
      const DeepCollectionEquality().hash(_currentAccountIds),
      const DeepCollectionEquality().hash(_creditAccountIds),
      isBusinessCustomer,
      isCardReplacementEnabled,
      accountType,
      const DeepCollectionEquality().hash(_reasonInfoList));

  /// Create a copy of CardDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CardDetailsLoadedStateImplCopyWith<_$CardDetailsLoadedStateImpl>
      get copyWith => __$$CardDetailsLoadedStateImplCopyWithImpl<
          _$CardDetailsLoadedStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            CardInfo cardInfo,
            List<String> currentAccountIds,
            List<String> creditAccountIds,
            bool isBusinessCustomer,
            bool isCardReplacementEnabled,
            AccountType? accountType,
            List<ReasonInfo> reasonInfoList)
        loaded,
    required TResult Function() loading,
    required TResult Function(String cardToken) error,
  }) {
    return loaded(
        cardInfo,
        currentAccountIds,
        creditAccountIds,
        isBusinessCustomer,
        isCardReplacementEnabled,
        accountType,
        reasonInfoList);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            CardInfo cardInfo,
            List<String> currentAccountIds,
            List<String> creditAccountIds,
            bool isBusinessCustomer,
            bool isCardReplacementEnabled,
            AccountType? accountType,
            List<ReasonInfo> reasonInfoList)?
        loaded,
    TResult? Function()? loading,
    TResult? Function(String cardToken)? error,
  }) {
    return loaded?.call(
        cardInfo,
        currentAccountIds,
        creditAccountIds,
        isBusinessCustomer,
        isCardReplacementEnabled,
        accountType,
        reasonInfoList);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            CardInfo cardInfo,
            List<String> currentAccountIds,
            List<String> creditAccountIds,
            bool isBusinessCustomer,
            bool isCardReplacementEnabled,
            AccountType? accountType,
            List<ReasonInfo> reasonInfoList)?
        loaded,
    TResult Function()? loading,
    TResult Function(String cardToken)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(
          cardInfo,
          currentAccountIds,
          creditAccountIds,
          isBusinessCustomer,
          isCardReplacementEnabled,
          accountType,
          reasonInfoList);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CardDetailsLoadedState value) loaded,
    required TResult Function(CardDetailsLoadingState value) loading,
    required TResult Function(CardDetailsStateError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CardDetailsLoadedState value)? loaded,
    TResult? Function(CardDetailsLoadingState value)? loading,
    TResult? Function(CardDetailsStateError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CardDetailsLoadedState value)? loaded,
    TResult Function(CardDetailsLoadingState value)? loading,
    TResult Function(CardDetailsStateError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class CardDetailsLoadedState implements CardDetailsState {
  const factory CardDetailsLoadedState(
      {required final CardInfo cardInfo,
      required final List<String> currentAccountIds,
      required final List<String> creditAccountIds,
      required final bool isBusinessCustomer,
      final bool isCardReplacementEnabled,
      final AccountType? accountType,
      final List<ReasonInfo> reasonInfoList}) = _$CardDetailsLoadedStateImpl;

  CardInfo get cardInfo;
  List<String> get currentAccountIds;
  List<String> get creditAccountIds;
  bool get isBusinessCustomer;
  bool get isCardReplacementEnabled;
  AccountType? get accountType;
  List<ReasonInfo> get reasonInfoList;

  /// Create a copy of CardDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CardDetailsLoadedStateImplCopyWith<_$CardDetailsLoadedStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CardDetailsLoadingStateImplCopyWith<$Res> {
  factory _$$CardDetailsLoadingStateImplCopyWith(
          _$CardDetailsLoadingStateImpl value,
          $Res Function(_$CardDetailsLoadingStateImpl) then) =
      __$$CardDetailsLoadingStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CardDetailsLoadingStateImplCopyWithImpl<$Res>
    extends _$CardDetailsStateCopyWithImpl<$Res, _$CardDetailsLoadingStateImpl>
    implements _$$CardDetailsLoadingStateImplCopyWith<$Res> {
  __$$CardDetailsLoadingStateImplCopyWithImpl(
      _$CardDetailsLoadingStateImpl _value,
      $Res Function(_$CardDetailsLoadingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CardDetailsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CardDetailsLoadingStateImpl implements CardDetailsLoadingState {
  const _$CardDetailsLoadingStateImpl();

  @override
  String toString() {
    return 'CardDetailsState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CardDetailsLoadingStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            CardInfo cardInfo,
            List<String> currentAccountIds,
            List<String> creditAccountIds,
            bool isBusinessCustomer,
            bool isCardReplacementEnabled,
            AccountType? accountType,
            List<ReasonInfo> reasonInfoList)
        loaded,
    required TResult Function() loading,
    required TResult Function(String cardToken) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            CardInfo cardInfo,
            List<String> currentAccountIds,
            List<String> creditAccountIds,
            bool isBusinessCustomer,
            bool isCardReplacementEnabled,
            AccountType? accountType,
            List<ReasonInfo> reasonInfoList)?
        loaded,
    TResult? Function()? loading,
    TResult? Function(String cardToken)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            CardInfo cardInfo,
            List<String> currentAccountIds,
            List<String> creditAccountIds,
            bool isBusinessCustomer,
            bool isCardReplacementEnabled,
            AccountType? accountType,
            List<ReasonInfo> reasonInfoList)?
        loaded,
    TResult Function()? loading,
    TResult Function(String cardToken)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CardDetailsLoadedState value) loaded,
    required TResult Function(CardDetailsLoadingState value) loading,
    required TResult Function(CardDetailsStateError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CardDetailsLoadedState value)? loaded,
    TResult? Function(CardDetailsLoadingState value)? loading,
    TResult? Function(CardDetailsStateError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CardDetailsLoadedState value)? loaded,
    TResult Function(CardDetailsLoadingState value)? loading,
    TResult Function(CardDetailsStateError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class CardDetailsLoadingState implements CardDetailsState {
  const factory CardDetailsLoadingState() = _$CardDetailsLoadingStateImpl;
}

/// @nodoc
abstract class _$$CardDetailsStateErrorImplCopyWith<$Res> {
  factory _$$CardDetailsStateErrorImplCopyWith(
          _$CardDetailsStateErrorImpl value,
          $Res Function(_$CardDetailsStateErrorImpl) then) =
      __$$CardDetailsStateErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String cardToken});
}

/// @nodoc
class __$$CardDetailsStateErrorImplCopyWithImpl<$Res>
    extends _$CardDetailsStateCopyWithImpl<$Res, _$CardDetailsStateErrorImpl>
    implements _$$CardDetailsStateErrorImplCopyWith<$Res> {
  __$$CardDetailsStateErrorImplCopyWithImpl(_$CardDetailsStateErrorImpl _value,
      $Res Function(_$CardDetailsStateErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of CardDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardToken = null,
  }) {
    return _then(_$CardDetailsStateErrorImpl(
      cardToken: null == cardToken
          ? _value.cardToken
          : cardToken // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CardDetailsStateErrorImpl implements CardDetailsStateError {
  const _$CardDetailsStateErrorImpl({required this.cardToken});

  @override
  final String cardToken;

  @override
  String toString() {
    return 'CardDetailsState.error(cardToken: $cardToken)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CardDetailsStateErrorImpl &&
            (identical(other.cardToken, cardToken) ||
                other.cardToken == cardToken));
  }

  @override
  int get hashCode => Object.hash(runtimeType, cardToken);

  /// Create a copy of CardDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CardDetailsStateErrorImplCopyWith<_$CardDetailsStateErrorImpl>
      get copyWith => __$$CardDetailsStateErrorImplCopyWithImpl<
          _$CardDetailsStateErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            CardInfo cardInfo,
            List<String> currentAccountIds,
            List<String> creditAccountIds,
            bool isBusinessCustomer,
            bool isCardReplacementEnabled,
            AccountType? accountType,
            List<ReasonInfo> reasonInfoList)
        loaded,
    required TResult Function() loading,
    required TResult Function(String cardToken) error,
  }) {
    return error(cardToken);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            CardInfo cardInfo,
            List<String> currentAccountIds,
            List<String> creditAccountIds,
            bool isBusinessCustomer,
            bool isCardReplacementEnabled,
            AccountType? accountType,
            List<ReasonInfo> reasonInfoList)?
        loaded,
    TResult? Function()? loading,
    TResult? Function(String cardToken)? error,
  }) {
    return error?.call(cardToken);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            CardInfo cardInfo,
            List<String> currentAccountIds,
            List<String> creditAccountIds,
            bool isBusinessCustomer,
            bool isCardReplacementEnabled,
            AccountType? accountType,
            List<ReasonInfo> reasonInfoList)?
        loaded,
    TResult Function()? loading,
    TResult Function(String cardToken)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(cardToken);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CardDetailsLoadedState value) loaded,
    required TResult Function(CardDetailsLoadingState value) loading,
    required TResult Function(CardDetailsStateError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CardDetailsLoadedState value)? loaded,
    TResult? Function(CardDetailsLoadingState value)? loading,
    TResult? Function(CardDetailsStateError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CardDetailsLoadedState value)? loaded,
    TResult Function(CardDetailsLoadingState value)? loading,
    TResult Function(CardDetailsStateError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class CardDetailsStateError implements CardDetailsState {
  const factory CardDetailsStateError({required final String cardToken}) =
      _$CardDetailsStateErrorImpl;

  String get cardToken;

  /// Create a copy of CardDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CardDetailsStateErrorImplCopyWith<_$CardDetailsStateErrorImpl>
      get copyWith => throw _privateConstructorUsedError;
}
