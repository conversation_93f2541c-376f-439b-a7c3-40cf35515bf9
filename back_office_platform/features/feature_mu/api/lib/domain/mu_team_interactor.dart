import 'package:wio_feature_mu_api/mu_api.dart';

/// The tasks interactor can be used to perform business users from multiuser.
///
/// Multi User Service allows to get user info related to Multi user flow.
abstract class MuTeamInteractor {
  /// Returns all active users and most invitations.
  ///
  /// Excludes rejected and inactive users, cancelled or unknown invitations.
  Future<List<TeamMemberInput>> getTeamMembers();

  Future<List<MuRequest>> getMuRequests({
    required int pageNumber,
    required int pageSize,
    MuRequestFilterParameters? params,
    MuRequestSortInfo? sortInfo,
  });

  Future<FlowOverrideInfoAuthorityType?> getAuthorityType();
}
