import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_mu_api/mu_api.dart';

part 'mu_request.freezed.dart';

@freezed
class MuRequest with _$MuRequest {
  const factory MuRequest.local({
    /// [muDomain] contains fields pertaining to the request details. For
    /// instance requestId and authorId, list of approvers etc
    required MuDomain muDomain,
    required MuLocalTransfer? localTransfer,
  }) = MuLocalTransferRequest;

  const factory MuRequest.payroll({
    required MuDomain muDomain,
    required MuPayrollSummary? payrollSummary,
  }) = MuPayrollRequest;

  const factory MuRequest.fx({
    required MuDomain muDomain,
    required MuFxSummary? fxSummary,
  }) = MuFxRequest;

  const factory MuRequest.accountCreation({
    required MuDomain muDomain,
    required MuAccountCreationSummary? muAccountCreationSummary,
  }) = MuFxAccountCreationRequest;

  const factory MuRequest.accountClosure({
    required MuDomain muDomain,
    required MuAccountClosureSummary? muAccountClosureSummary,
  }) = MuFxAccountClosureRequest;

  const factory MuRequest.savingSpaces({
    required MuDomain muDomain,
    required MuSavingSpaceAccountSummary? muSavingSpaceAccountSummary,
  }) = MuSavingSpaceAccountRequest;

  const factory MuRequest.internal({
    required MuDomain muDomain,
    required MuInternalTransferSummary? muInternalTransferSummary,
  }) = MuInternalTransferRequest;

  const factory MuRequest.international({
    required MuDomain muDomain,
    required MuInternationalTransfer? muInternationalTransfer,
  }) = MuInternationalTransferRequest;

  const factory MuRequest.creditContract({
    required MuDomain muDomain,
    required MuCreditContractSummary? muCreditContractSummary,
  }) = MuCreditContractRequest;

  const factory MuRequest.currentAccountInternalTransfer({
    required MuDomain muDomain,
    required MuCurrentAccountInternalTransferSummary?
        muCurrentAccountInternalTransferSummary,
  }) = MuCurrentAccountInternalTransferRequest;

  const factory MuRequest.fixedTermDepositAccountUpdate({
    required MuDomain muDomain,
    required MuFixedTermDepositAccountUpdateSummary?
        muFixedTermDepositAccountUpdateSummary,
  }) = MuFixedTermDepositAccountUpdateRequest;

  const factory MuRequest.fixedTermDepositClosing({
    required MuDomain muDomain,
    required MuFixedTermDepositClosingSummary? muFixedTermDepositClosingSummary,
  }) = MuFixedTermDepositClosingRequest;

  const factory MuRequest.fixedTermDepositCreationFromSs({
    required MuDomain muDomain,
    required MuFixedTermDepositCreationFromSsSummary?
        muFixedTermDepositCreationFromSsSummary,
  }) = MuFixedTermDepositCreationFromSsRequest;

  const factory MuRequest.fixedTermDepositPartialWithdrawal({
    required MuDomain muDomain,
    required MuFixedTermDepositPartialWithdrawalSummary?
        muFixedTermDepositPartialWithdrawalSummary,
  }) = MuFixedTermDepositPartialWithdrawalRequest;

  const factory MuRequest.fixedTermDepositeCreation({
    required MuDomain muDomain,
    required MuFixedTermDepositeCreationSummary?
        muFixedTermDepositeCreationSummary,
  }) = MuFixedTermDepositeCreationRequest;

  const factory MuRequest.multiSignatureCardCreation({
    required MuDomain muDomain,
    required MuMultiSignatureCardCreationSummary?
        muMultiSignatureCardCreationSummary,
  }) = MuMultiSignatureCardCreationRequest;

  const factory MuRequest.scfInvoiceFinancingAcceptance({
    required MuDomain muDomain,
    required MuScfInvoiceFinancingAcceptanceSummary?
        muScfInvoiceFinancingAcceptanceSummary,
  }) = MuScfInvoiceFinancingAcceptanceRequest;

  const factory MuRequest.scfInvoiceRepaymentDeferred({
    required MuDomain muDomain,
    required MuScfInvoiceRepaymentDeferredSummary? summary,
  }) = MuScfInvoiceRepaymentDeferredRequest;

  const factory MuRequest.scfInvoiceRepaymentInitiate({
    required MuDomain muDomain,
    required MuScfInvoiceRepaymentInitiateSummary?
        muScfInvoiceRepaymentInitiateSummary,
  }) = MuScfInvoiceRepaymentInitiateRequest;

  const factory MuRequest.scfInvoiceUpload({
    required MuDomain muDomain,
    required MuScfInvoiceUploadSummary? muScfInvoiceUploadSummary,
  }) = MuScfInvoiceUploadRequest;

  const factory MuRequest.scfSupplierInvite({
    required MuDomain muDomain,
    required MuScfSupplierInviteSummary? muScfSupplierInviteSummary,
  }) = MuScfSupplierInviteRequest;

  const factory MuRequest.virtualCardCreation({
    required MuDomain muDomain,
    required MuVirtualCardCreationSummary? muVirtualCardCreationSummary,
  }) = MuVirtualCardCreationRequest;
}
