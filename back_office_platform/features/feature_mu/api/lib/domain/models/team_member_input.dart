import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_mu_api/mu_api.dart';

part 'team_member_input.freezed.dart';

@freezed
// TODO(amitrica): revisit contract and its domain models https://wiobank.atlassian.net/browse/MU-1820
/// Multiuser data of an user
class TeamMemberInput with _$TeamMemberInput {
  /// Multiuser data of an user
  const factory TeamMemberInput({
    @Default('') String businessId,
    @Default('') String individualId,
    @Default('') String fullName,
    @Default(<String>[]) List<String> assignedGroups,
    @Default(<String>[]) List<String> assignedUserGroupForDisplay,
    @Default('') String email,
    @Default('') String invitationId,
    @Default(MuUserStatus.empty) MuUserStatus muUserStatus,
    @Default(MuInvitationStatus.notAssigned)
    MuInvitationStatus muInvitationStatus,
    @Default(false) bool canBeDeleted,
    DateTime? invitationCreationDate,
  }) = _TeamMemberInput;
}
