import 'package:flutter/material.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_navigation_rail_ui_desktop/feature_navigation_rail_desktop_ui.dart';

class TicketNavigationActionBanner extends StatelessWidget {
  const TicketNavigationActionBanner({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<BackOfficeNavigationRailCubit>();
    final state = context.watch<BackOfficeNavigationRailCubit>().state;
    final backOfficeCase = state.backOfficeCase;

    if (backOfficeCase == null) {
      return const SizedBox.shrink();
    }

    return Align(
      alignment: AlignmentDirectional.bottomCenter,
      child: Material(
        child: InkWell(
          onTap: () => cubit.navigateToTicketDetails(),
          child: DecoratedBox(
            decoration: BoxDecoration(
              color: context.colorStyling.background1,
            ),
            child: SizedBox(
              height: 80.0,
              child: DecoratedBox(
                decoration: const BoxDecoration(
                  color: Colors.white,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      const CompanyIcon(
                        CompanyIconModel(
                          icon: GraphicAssetPointer.icon(
                            CompanyIconPointer.chevron_left,
                          ),
                          size: CompanyIconSize.large,
                        ),
                      ),
                      const SizedBox(width: 12.0),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Label(
                              model: LabelModel(
                                text: 'Case ${backOfficeCase.caseNumber}',
                                textStyle: CompanyTextStylePointer.b4,
                                color: CompanyColorPointer.secondary3,
                              ),
                            ),
                            Label(
                              model: LabelModel(
                                maxLines: 1,
                                overflow: LabelTextOverflow.ellipsis,
                                text: backOfficeCase.caseTitle,
                                textStyle: CompanyTextStylePointer.b3medium,
                                color: CompanyColorPointer.primary3,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
