// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'invest_market_onboarding_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$InvestMarketOnboardingState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<OnboardingFeatureStatus> onboardingStatuses)
        loaded,
    required TResult Function() error,
    required TResult Function() loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<OnboardingFeatureStatus> onboardingStatuses)? loaded,
    TResult? Function()? error,
    TResult? Function()? loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<OnboardingFeatureStatus> onboardingStatuses)? loaded,
    TResult Function()? error,
    TResult Function()? loading,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InvestMarketOnboardingLoadedState value) loaded,
    required TResult Function(_InvestMarketOnboardingErrorState value) error,
    required TResult Function(_InvestMarketOnboardingLoadingState value)
        loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(InvestMarketOnboardingLoadedState value)? loaded,
    TResult? Function(_InvestMarketOnboardingErrorState value)? error,
    TResult? Function(_InvestMarketOnboardingLoadingState value)? loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InvestMarketOnboardingLoadedState value)? loaded,
    TResult Function(_InvestMarketOnboardingErrorState value)? error,
    TResult Function(_InvestMarketOnboardingLoadingState value)? loading,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InvestMarketOnboardingStateCopyWith<$Res> {
  factory $InvestMarketOnboardingStateCopyWith(
          InvestMarketOnboardingState value,
          $Res Function(InvestMarketOnboardingState) then) =
      _$InvestMarketOnboardingStateCopyWithImpl<$Res,
          InvestMarketOnboardingState>;
}

/// @nodoc
class _$InvestMarketOnboardingStateCopyWithImpl<$Res,
        $Val extends InvestMarketOnboardingState>
    implements $InvestMarketOnboardingStateCopyWith<$Res> {
  _$InvestMarketOnboardingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$InvestMarketOnboardingLoadedStateImplCopyWith<$Res> {
  factory _$$InvestMarketOnboardingLoadedStateImplCopyWith(
          _$InvestMarketOnboardingLoadedStateImpl value,
          $Res Function(_$InvestMarketOnboardingLoadedStateImpl) then) =
      __$$InvestMarketOnboardingLoadedStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<OnboardingFeatureStatus> onboardingStatuses});
}

/// @nodoc
class __$$InvestMarketOnboardingLoadedStateImplCopyWithImpl<$Res>
    extends _$InvestMarketOnboardingStateCopyWithImpl<$Res,
        _$InvestMarketOnboardingLoadedStateImpl>
    implements _$$InvestMarketOnboardingLoadedStateImplCopyWith<$Res> {
  __$$InvestMarketOnboardingLoadedStateImplCopyWithImpl(
      _$InvestMarketOnboardingLoadedStateImpl _value,
      $Res Function(_$InvestMarketOnboardingLoadedStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? onboardingStatuses = null,
  }) {
    return _then(_$InvestMarketOnboardingLoadedStateImpl(
      onboardingStatuses: null == onboardingStatuses
          ? _value._onboardingStatuses
          : onboardingStatuses // ignore: cast_nullable_to_non_nullable
              as List<OnboardingFeatureStatus>,
    ));
  }
}

/// @nodoc

class _$InvestMarketOnboardingLoadedStateImpl
    implements InvestMarketOnboardingLoadedState {
  const _$InvestMarketOnboardingLoadedStateImpl(
      {required final List<OnboardingFeatureStatus> onboardingStatuses})
      : _onboardingStatuses = onboardingStatuses;

  final List<OnboardingFeatureStatus> _onboardingStatuses;
  @override
  List<OnboardingFeatureStatus> get onboardingStatuses {
    if (_onboardingStatuses is EqualUnmodifiableListView)
      return _onboardingStatuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_onboardingStatuses);
  }

  @override
  String toString() {
    return 'InvestMarketOnboardingState.loaded(onboardingStatuses: $onboardingStatuses)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvestMarketOnboardingLoadedStateImpl &&
            const DeepCollectionEquality()
                .equals(other._onboardingStatuses, _onboardingStatuses));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_onboardingStatuses));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InvestMarketOnboardingLoadedStateImplCopyWith<
          _$InvestMarketOnboardingLoadedStateImpl>
      get copyWith => __$$InvestMarketOnboardingLoadedStateImplCopyWithImpl<
          _$InvestMarketOnboardingLoadedStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<OnboardingFeatureStatus> onboardingStatuses)
        loaded,
    required TResult Function() error,
    required TResult Function() loading,
  }) {
    return loaded(onboardingStatuses);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<OnboardingFeatureStatus> onboardingStatuses)? loaded,
    TResult? Function()? error,
    TResult? Function()? loading,
  }) {
    return loaded?.call(onboardingStatuses);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<OnboardingFeatureStatus> onboardingStatuses)? loaded,
    TResult Function()? error,
    TResult Function()? loading,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(onboardingStatuses);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InvestMarketOnboardingLoadedState value) loaded,
    required TResult Function(_InvestMarketOnboardingErrorState value) error,
    required TResult Function(_InvestMarketOnboardingLoadingState value)
        loading,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(InvestMarketOnboardingLoadedState value)? loaded,
    TResult? Function(_InvestMarketOnboardingErrorState value)? error,
    TResult? Function(_InvestMarketOnboardingLoadingState value)? loading,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InvestMarketOnboardingLoadedState value)? loaded,
    TResult Function(_InvestMarketOnboardingErrorState value)? error,
    TResult Function(_InvestMarketOnboardingLoadingState value)? loading,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class InvestMarketOnboardingLoadedState
    implements InvestMarketOnboardingState {
  const factory InvestMarketOnboardingLoadedState(
          {required final List<OnboardingFeatureStatus> onboardingStatuses}) =
      _$InvestMarketOnboardingLoadedStateImpl;

  List<OnboardingFeatureStatus> get onboardingStatuses;
  @JsonKey(ignore: true)
  _$$InvestMarketOnboardingLoadedStateImplCopyWith<
          _$InvestMarketOnboardingLoadedStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InvestMarketOnboardingErrorStateImplCopyWith<$Res> {
  factory _$$InvestMarketOnboardingErrorStateImplCopyWith(
          _$InvestMarketOnboardingErrorStateImpl value,
          $Res Function(_$InvestMarketOnboardingErrorStateImpl) then) =
      __$$InvestMarketOnboardingErrorStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InvestMarketOnboardingErrorStateImplCopyWithImpl<$Res>
    extends _$InvestMarketOnboardingStateCopyWithImpl<$Res,
        _$InvestMarketOnboardingErrorStateImpl>
    implements _$$InvestMarketOnboardingErrorStateImplCopyWith<$Res> {
  __$$InvestMarketOnboardingErrorStateImplCopyWithImpl(
      _$InvestMarketOnboardingErrorStateImpl _value,
      $Res Function(_$InvestMarketOnboardingErrorStateImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$InvestMarketOnboardingErrorStateImpl
    implements _InvestMarketOnboardingErrorState {
  const _$InvestMarketOnboardingErrorStateImpl();

  @override
  String toString() {
    return 'InvestMarketOnboardingState.error()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvestMarketOnboardingErrorStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<OnboardingFeatureStatus> onboardingStatuses)
        loaded,
    required TResult Function() error,
    required TResult Function() loading,
  }) {
    return error();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<OnboardingFeatureStatus> onboardingStatuses)? loaded,
    TResult? Function()? error,
    TResult? Function()? loading,
  }) {
    return error?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<OnboardingFeatureStatus> onboardingStatuses)? loaded,
    TResult Function()? error,
    TResult Function()? loading,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InvestMarketOnboardingLoadedState value) loaded,
    required TResult Function(_InvestMarketOnboardingErrorState value) error,
    required TResult Function(_InvestMarketOnboardingLoadingState value)
        loading,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(InvestMarketOnboardingLoadedState value)? loaded,
    TResult? Function(_InvestMarketOnboardingErrorState value)? error,
    TResult? Function(_InvestMarketOnboardingLoadingState value)? loading,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InvestMarketOnboardingLoadedState value)? loaded,
    TResult Function(_InvestMarketOnboardingErrorState value)? error,
    TResult Function(_InvestMarketOnboardingLoadingState value)? loading,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _InvestMarketOnboardingErrorState
    implements InvestMarketOnboardingState {
  const factory _InvestMarketOnboardingErrorState() =
      _$InvestMarketOnboardingErrorStateImpl;
}

/// @nodoc
abstract class _$$InvestMarketOnboardingLoadingStateImplCopyWith<$Res> {
  factory _$$InvestMarketOnboardingLoadingStateImplCopyWith(
          _$InvestMarketOnboardingLoadingStateImpl value,
          $Res Function(_$InvestMarketOnboardingLoadingStateImpl) then) =
      __$$InvestMarketOnboardingLoadingStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InvestMarketOnboardingLoadingStateImplCopyWithImpl<$Res>
    extends _$InvestMarketOnboardingStateCopyWithImpl<$Res,
        _$InvestMarketOnboardingLoadingStateImpl>
    implements _$$InvestMarketOnboardingLoadingStateImplCopyWith<$Res> {
  __$$InvestMarketOnboardingLoadingStateImplCopyWithImpl(
      _$InvestMarketOnboardingLoadingStateImpl _value,
      $Res Function(_$InvestMarketOnboardingLoadingStateImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$InvestMarketOnboardingLoadingStateImpl
    implements _InvestMarketOnboardingLoadingState {
  const _$InvestMarketOnboardingLoadingStateImpl();

  @override
  String toString() {
    return 'InvestMarketOnboardingState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvestMarketOnboardingLoadingStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<OnboardingFeatureStatus> onboardingStatuses)
        loaded,
    required TResult Function() error,
    required TResult Function() loading,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<OnboardingFeatureStatus> onboardingStatuses)? loaded,
    TResult? Function()? error,
    TResult? Function()? loading,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<OnboardingFeatureStatus> onboardingStatuses)? loaded,
    TResult Function()? error,
    TResult Function()? loading,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(InvestMarketOnboardingLoadedState value) loaded,
    required TResult Function(_InvestMarketOnboardingErrorState value) error,
    required TResult Function(_InvestMarketOnboardingLoadingState value)
        loading,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(InvestMarketOnboardingLoadedState value)? loaded,
    TResult? Function(_InvestMarketOnboardingErrorState value)? error,
    TResult? Function(_InvestMarketOnboardingLoadingState value)? loading,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(InvestMarketOnboardingLoadedState value)? loaded,
    TResult Function(_InvestMarketOnboardingErrorState value)? error,
    TResult Function(_InvestMarketOnboardingLoadingState value)? loading,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _InvestMarketOnboardingLoadingState
    implements InvestMarketOnboardingState {
  const factory _InvestMarketOnboardingLoadingState() =
      _$InvestMarketOnboardingLoadingStateImpl;
}
