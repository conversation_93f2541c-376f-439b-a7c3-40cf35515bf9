import 'dart:convert';

import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_invest_customer_impl/src/data/models/schema_dtos/schema.swagger.dart';
import 'package:wio_feature_invest_customer_impl/src/services/invest_customer_service.dart';

import '../mocks.dart';

typedef _Json = Map<String, dynamic>;
void main() {
  late InvestCustomerServiceImpl investService;
  late MockRestApiClient mockRestApiClient;

  setUpAll(() => registerFallBackValues());

  setUp(() {
    mockRestApiClient = MockRestApiClient();
    investService = InvestCustomerServiceImpl(restApiClient: mockRestApiClient);
  });

  group('InvestServiceImpl', () {
    test('getInvestCustomerDetails should return UserResponse on success',
        () async {
      // Arrange
      final responseJson = jsonDecode(investCustomerDetailsJsonString) as _Json;

      // Mock the response from the API client
      when(() => mockRestApiClient.execute<_Json>(any())).thenAnswer(
        (_) async => RestApiResponse(data: responseJson),
      );

      // Act
      final result = await investService.getInvestCustomerDetails('customerId');

      // Assert
      expect(result, isA<UserResponse>());
      expect(result.addressInfo?.postalCode, '0');
      expect(
        result.investmentExperience,
        UserResponseInvestmentExperience.yrs12,
      );
    });

    test('getInvestCustomerDetails should throw exception on error', () async {
      // Arrange
      when(() => mockRestApiClient.execute<_Json>(any()))
          .thenThrow(FakeHttpException());

      // Act & Assert
      expect(
        () async => investService.getInvestCustomerDetails('customerId'),
        throwsA(isA<HttpRequestException>()),
      );
    });

    test(
        '''getCustomerInvestInfo should return CustomerSupportCustomerInformationResponse on success''',
        () async {
      // Arrange

      final responseJson = jsonDecode(customerInvestInfoJsonString) as _Json;

      // Mock the response from the API client
      when(() => mockRestApiClient.execute<_Json>(any())).thenAnswer(
        (_) async => RestApiResponse(data: responseJson),
      );

      // Act
      final result = await investService.getCustomerInvestInfo('customerId');

      // Assert
      expect(result, isA<CustomerSupportCustomerInformationResponse>());
      expect(
        result.data.onboardingFeatureStatuses?.first.partnerAccountNumber,
        'DQJN000001',
      );
      expect(
        result.data.customerInformation?.ninNumbers.first.exchange,
        NinExchange.adx,
      );
      expect(
        result.data.features?.suitabilityAssessment,
        CustomerInformation$FeaturesSuitabilityAssessment.done,
      );
    });

    test('getCustomerInvestInfo should throw exception on error', () async {
      // Arrange

      when(() => mockRestApiClient.execute<_Json>(any()))
          .thenThrow(FakeHttpException());

      // Act & Assert
      expect(
        () async => investService.getCustomerInvestInfo('customerId'),
        throwsA(isA<HttpRequestException>()),
      );
    });
  });
}
