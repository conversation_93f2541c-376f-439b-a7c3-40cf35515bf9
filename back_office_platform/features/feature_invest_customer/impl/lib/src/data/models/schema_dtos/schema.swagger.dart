// ignore_for_file: type=lint

import 'package:json_annotation/json_annotation.dart';
import 'package:json_annotation/json_annotation.dart' as json;
import 'package:collection/collection.dart';
import 'dart:convert';

import 'schema.enums.swagger.dart' as enums;
export 'schema.enums.swagger.dart';

part 'schema.swagger.g.dart';

@JsonSerializable(explicitToJson: true)
class OnboardingQuestionResponse {
  const OnboardingQuestionResponse({
    required this.screen,
    required this.question,
  });

  factory OnboardingQuestionResponse.fromJson(Map<String, dynamic> json) =>
      _$OnboardingQuestionResponseFromJson(json);

  static const toJsonFactory = _$OnboardingQuestionResponseToJson;
  Map<String, dynamic> toJson() => _$OnboardingQuestionResponseToJson(this);

  @JsonKey(name: 'screen', includeIfNull: false)
  final Screen screen;
  @JsonKey(name: 'question', includeIfNull: false)
  final Question question;
  static const fromJsonFactory = _$OnboardingQuestionResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $OnboardingQuestionResponseExtension on OnboardingQuestionResponse {
  OnboardingQuestionResponse copyWith({Screen? screen, Question? question}) {
    return OnboardingQuestionResponse(
        screen: screen ?? this.screen, question: question ?? this.question);
  }

  OnboardingQuestionResponse copyWithWrapped(
      {Wrapped<Screen>? screen, Wrapped<Question>? question}) {
    return OnboardingQuestionResponse(
        screen: (screen != null ? screen.value : this.screen),
        question: (question != null ? question.value : this.question));
  }
}

@JsonSerializable(explicitToJson: true)
class Screen {
  const Screen({
    required this.actions,
    required this.progress,
  });

  factory Screen.fromJson(Map<String, dynamic> json) => _$ScreenFromJson(json);

  static const toJsonFactory = _$ScreenToJson;
  Map<String, dynamic> toJson() => _$ScreenToJson(this);

  @JsonKey(name: 'actions', includeIfNull: false)
  final Actions actions;
  @JsonKey(name: 'progress', includeIfNull: false)
  final Progress progress;
  static const fromJsonFactory = _$ScreenFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ScreenExtension on Screen {
  Screen copyWith({Actions? actions, Progress? progress}) {
    return Screen(
        actions: actions ?? this.actions, progress: progress ?? this.progress);
  }

  Screen copyWithWrapped(
      {Wrapped<Actions>? actions, Wrapped<Progress>? progress}) {
    return Screen(
        actions: (actions != null ? actions.value : this.actions),
        progress: (progress != null ? progress.value : this.progress));
  }
}

@JsonSerializable(explicitToJson: true)
class Actions {
  const Actions({
    required this.back,
    required this.isCompleted,
  });

  factory Actions.fromJson(Map<String, dynamic> json) =>
      _$ActionsFromJson(json);

  static const toJsonFactory = _$ActionsToJson;
  Map<String, dynamic> toJson() => _$ActionsToJson(this);

  @JsonKey(name: 'back', includeIfNull: false)
  final bool back;
  @JsonKey(name: 'isCompleted', includeIfNull: false)
  final bool isCompleted;
  static const fromJsonFactory = _$ActionsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ActionsExtension on Actions {
  Actions copyWith({bool? back, bool? isCompleted}) {
    return Actions(
        back: back ?? this.back, isCompleted: isCompleted ?? this.isCompleted);
  }

  Actions copyWithWrapped({Wrapped<bool>? back, Wrapped<bool>? isCompleted}) {
    return Actions(
        back: (back != null ? back.value : this.back),
        isCompleted:
            (isCompleted != null ? isCompleted.value : this.isCompleted));
  }
}

@JsonSerializable(explicitToJson: true)
class Progress {
  const Progress({
    required this.answered,
    required this.total,
  });

  factory Progress.fromJson(Map<String, dynamic> json) =>
      _$ProgressFromJson(json);

  static const toJsonFactory = _$ProgressToJson;
  Map<String, dynamic> toJson() => _$ProgressToJson(this);

  @JsonKey(name: 'answered', includeIfNull: false)
  final int answered;
  @JsonKey(name: 'total', includeIfNull: false)
  final int total;
  static const fromJsonFactory = _$ProgressFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $ProgressExtension on Progress {
  Progress copyWith({int? answered, int? total}) {
    return Progress(
        answered: answered ?? this.answered, total: total ?? this.total);
  }

  Progress copyWithWrapped({Wrapped<int>? answered, Wrapped<int>? total}) {
    return Progress(
        answered: (answered != null ? answered.value : this.answered),
        total: (total != null ? total.value : this.total));
  }
}

@JsonSerializable(explicitToJson: true)
class Question {
  const Question({
    required this.id,
    required this.type,
  });

  factory Question.fromJson(Map<String, dynamic> json) =>
      _$QuestionFromJson(json);

  static const toJsonFactory = _$QuestionToJson;
  Map<String, dynamic> toJson() => _$QuestionToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(
    name: 'type',
    includeIfNull: false,
    toJson: questionTypeToJson,
    fromJson: questionTypeFromJson,
  )
  final enums.QuestionType type;
  static const fromJsonFactory = _$QuestionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $QuestionExtension on Question {
  Question copyWith({String? id, enums.QuestionType? type}) {
    return Question(id: id ?? this.id, type: type ?? this.type);
  }

  Question copyWithWrapped(
      {Wrapped<String>? id, Wrapped<enums.QuestionType>? type}) {
    return Question(
        id: (id != null ? id.value : this.id),
        type: (type != null ? type.value : this.type));
  }
}

@JsonSerializable(explicitToJson: true)
class SelectQuestion {
  const SelectQuestion({
    required this.id,
    required this.type,
    this.answer,
    required this.content,
    this.text,
  });

  factory SelectQuestion.fromJson(Map<String, dynamic> json) =>
      _$SelectQuestionFromJson(json);

  static const toJsonFactory = _$SelectQuestionToJson;
  Map<String, dynamic> toJson() => _$SelectQuestionToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'answer', includeIfNull: false)
  final SelectAnswer? answer;
  @JsonKey(name: 'content', includeIfNull: false)
  final SelectContent content;
  @JsonKey(name: 'text', includeIfNull: false)
  final String? text;
  static const fromJsonFactory = _$SelectQuestionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $SelectQuestionExtension on SelectQuestion {
  SelectQuestion copyWith(
      {String? id,
      String? type,
      SelectAnswer? answer,
      SelectContent? content,
      String? text}) {
    return SelectQuestion(
        id: id ?? this.id,
        type: type ?? this.type,
        answer: answer ?? this.answer,
        content: content ?? this.content,
        text: text ?? this.text);
  }

  SelectQuestion copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? type,
      Wrapped<SelectAnswer?>? answer,
      Wrapped<SelectContent>? content,
      Wrapped<String?>? text}) {
    return SelectQuestion(
        id: (id != null ? id.value : this.id),
        type: (type != null ? type.value : this.type),
        answer: (answer != null ? answer.value : this.answer),
        content: (content != null ? content.value : this.content),
        text: (text != null ? text.value : this.text));
  }
}

@JsonSerializable(explicitToJson: true)
class SelectWithSearchQuestion {
  const SelectWithSearchQuestion({
    required this.id,
    required this.type,
    this.answer,
    required this.content,
  });

  factory SelectWithSearchQuestion.fromJson(Map<String, dynamic> json) =>
      _$SelectWithSearchQuestionFromJson(json);

  static const toJsonFactory = _$SelectWithSearchQuestionToJson;
  Map<String, dynamic> toJson() => _$SelectWithSearchQuestionToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'answer', includeIfNull: false)
  final SelectAnswer? answer;
  @JsonKey(name: 'content', includeIfNull: false)
  final SelectWithSearchContent content;
  static const fromJsonFactory = _$SelectWithSearchQuestionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $SelectWithSearchQuestionExtension on SelectWithSearchQuestion {
  SelectWithSearchQuestion copyWith(
      {String? id,
      String? type,
      SelectAnswer? answer,
      SelectWithSearchContent? content}) {
    return SelectWithSearchQuestion(
        id: id ?? this.id,
        type: type ?? this.type,
        answer: answer ?? this.answer,
        content: content ?? this.content);
  }

  SelectWithSearchQuestion copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? type,
      Wrapped<SelectAnswer?>? answer,
      Wrapped<SelectWithSearchContent>? content}) {
    return SelectWithSearchQuestion(
        id: (id != null ? id.value : this.id),
        type: (type != null ? type.value : this.type),
        answer: (answer != null ? answer.value : this.answer),
        content: (content != null ? content.value : this.content));
  }
}

@JsonSerializable(explicitToJson: true)
class InputQuestion {
  const InputQuestion({
    required this.id,
    required this.type,
    this.answer,
    required this.content,
  });

  factory InputQuestion.fromJson(Map<String, dynamic> json) =>
      _$InputQuestionFromJson(json);

  static const toJsonFactory = _$InputQuestionToJson;
  Map<String, dynamic> toJson() => _$InputQuestionToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'answer', includeIfNull: false)
  final InputAnswer? answer;
  @JsonKey(name: 'content', includeIfNull: false)
  final InputContent content;
  static const fromJsonFactory = _$InputQuestionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $InputQuestionExtension on InputQuestion {
  InputQuestion copyWith(
      {String? id, String? type, InputAnswer? answer, InputContent? content}) {
    return InputQuestion(
        id: id ?? this.id,
        type: type ?? this.type,
        answer: answer ?? this.answer,
        content: content ?? this.content);
  }

  InputQuestion copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? type,
      Wrapped<InputAnswer?>? answer,
      Wrapped<InputContent>? content}) {
    return InputQuestion(
        id: (id != null ? id.value : this.id),
        type: (type != null ? type.value : this.type),
        answer: (answer != null ? answer.value : this.answer),
        content: (content != null ? content.value : this.content));
  }
}

@JsonSerializable(explicitToJson: true)
class AddressQuestion {
  const AddressQuestion({
    required this.id,
    required this.type,
    this.answer,
    required this.content,
  });

  factory AddressQuestion.fromJson(Map<String, dynamic> json) =>
      _$AddressQuestionFromJson(json);

  static const toJsonFactory = _$AddressQuestionToJson;
  Map<String, dynamic> toJson() => _$AddressQuestionToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'answer', includeIfNull: false)
  final AddressAnswer? answer;
  @JsonKey(name: 'content', includeIfNull: false)
  final AddressContent content;
  static const fromJsonFactory = _$AddressQuestionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AddressQuestionExtension on AddressQuestion {
  AddressQuestion copyWith(
      {String? id,
      String? type,
      AddressAnswer? answer,
      AddressContent? content}) {
    return AddressQuestion(
        id: id ?? this.id,
        type: type ?? this.type,
        answer: answer ?? this.answer,
        content: content ?? this.content);
  }

  AddressQuestion copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? type,
      Wrapped<AddressAnswer?>? answer,
      Wrapped<AddressContent>? content}) {
    return AddressQuestion(
        id: (id != null ? id.value : this.id),
        type: (type != null ? type.value : this.type),
        answer: (answer != null ? answer.value : this.answer),
        content: (content != null ? content.value : this.content));
  }
}

@JsonSerializable(explicitToJson: true)
class TermsAndConditionsQuestion {
  const TermsAndConditionsQuestion({
    required this.id,
    required this.type,
    this.answer,
    required this.content,
  });

  factory TermsAndConditionsQuestion.fromJson(Map<String, dynamic> json) =>
      _$TermsAndConditionsQuestionFromJson(json);

  static const toJsonFactory = _$TermsAndConditionsQuestionToJson;
  Map<String, dynamic> toJson() => _$TermsAndConditionsQuestionToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'answer', includeIfNull: false)
  final TermsAndConditionsAnswer? answer;
  @JsonKey(name: 'content', includeIfNull: false)
  final TermsAndConditionsContent content;
  static const fromJsonFactory = _$TermsAndConditionsQuestionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $TermsAndConditionsQuestionExtension on TermsAndConditionsQuestion {
  TermsAndConditionsQuestion copyWith(
      {String? id,
      String? type,
      TermsAndConditionsAnswer? answer,
      TermsAndConditionsContent? content}) {
    return TermsAndConditionsQuestion(
        id: id ?? this.id,
        type: type ?? this.type,
        answer: answer ?? this.answer,
        content: content ?? this.content);
  }

  TermsAndConditionsQuestion copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? type,
      Wrapped<TermsAndConditionsAnswer?>? answer,
      Wrapped<TermsAndConditionsContent>? content}) {
    return TermsAndConditionsQuestion(
        id: (id != null ? id.value : this.id),
        type: (type != null ? type.value : this.type),
        answer: (answer != null ? answer.value : this.answer),
        content: (content != null ? content.value : this.content));
  }
}

@JsonSerializable(explicitToJson: true)
class EmptyQuestion {
  const EmptyQuestion({
    required this.id,
    required this.type,
  });

  factory EmptyQuestion.fromJson(Map<String, dynamic> json) =>
      _$EmptyQuestionFromJson(json);

  static const toJsonFactory = _$EmptyQuestionToJson;
  Map<String, dynamic> toJson() => _$EmptyQuestionToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  static const fromJsonFactory = _$EmptyQuestionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EmptyQuestionExtension on EmptyQuestion {
  EmptyQuestion copyWith({String? id, String? type}) {
    return EmptyQuestion(id: id ?? this.id, type: type ?? this.type);
  }

  EmptyQuestion copyWithWrapped({Wrapped<String>? id, Wrapped<String>? type}) {
    return EmptyQuestion(
        id: (id != null ? id.value : this.id),
        type: (type != null ? type.value : this.type));
  }
}

@JsonSerializable(explicitToJson: true)
class MultiSelectQuestion {
  const MultiSelectQuestion({
    required this.id,
    required this.type,
    this.answer,
    required this.content,
    this.text,
  });

  factory MultiSelectQuestion.fromJson(Map<String, dynamic> json) =>
      _$MultiSelectQuestionFromJson(json);

  static const toJsonFactory = _$MultiSelectQuestionToJson;
  Map<String, dynamic> toJson() => _$MultiSelectQuestionToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'answer', includeIfNull: false)
  final MultiSelectAnswer? answer;
  @JsonKey(name: 'content', includeIfNull: false)
  final MultiSelectContent content;
  @JsonKey(name: 'text', includeIfNull: false)
  final String? text;
  static const fromJsonFactory = _$MultiSelectQuestionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MultiSelectQuestionExtension on MultiSelectQuestion {
  MultiSelectQuestion copyWith(
      {String? id,
      String? type,
      MultiSelectAnswer? answer,
      MultiSelectContent? content,
      String? text}) {
    return MultiSelectQuestion(
        id: id ?? this.id,
        type: type ?? this.type,
        answer: answer ?? this.answer,
        content: content ?? this.content,
        text: text ?? this.text);
  }

  MultiSelectQuestion copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? type,
      Wrapped<MultiSelectAnswer?>? answer,
      Wrapped<MultiSelectContent>? content,
      Wrapped<String?>? text}) {
    return MultiSelectQuestion(
        id: (id != null ? id.value : this.id),
        type: (type != null ? type.value : this.type),
        answer: (answer != null ? answer.value : this.answer),
        content: (content != null ? content.value : this.content),
        text: (text != null ? text.value : this.text));
  }
}

@JsonSerializable(explicitToJson: true)
class MultiSectionsQuestion {
  const MultiSectionsQuestion({
    required this.id,
    required this.type,
    this.answer,
    required this.content,
  });

  factory MultiSectionsQuestion.fromJson(Map<String, dynamic> json) =>
      _$MultiSectionsQuestionFromJson(json);

  static const toJsonFactory = _$MultiSectionsQuestionToJson;
  Map<String, dynamic> toJson() => _$MultiSectionsQuestionToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'answer', includeIfNull: false)
  final MultiSectionsAnswer? answer;
  @JsonKey(name: 'content', includeIfNull: false)
  final MultiSectionsContent content;
  static const fromJsonFactory = _$MultiSectionsQuestionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MultiSectionsQuestionExtension on MultiSectionsQuestion {
  MultiSectionsQuestion copyWith(
      {String? id,
      String? type,
      MultiSectionsAnswer? answer,
      MultiSectionsContent? content}) {
    return MultiSectionsQuestion(
        id: id ?? this.id,
        type: type ?? this.type,
        answer: answer ?? this.answer,
        content: content ?? this.content);
  }

  MultiSectionsQuestion copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? type,
      Wrapped<MultiSectionsAnswer?>? answer,
      Wrapped<MultiSectionsContent>? content}) {
    return MultiSectionsQuestion(
        id: (id != null ? id.value : this.id),
        type: (type != null ? type.value : this.type),
        answer: (answer != null ? answer.value : this.answer),
        content: (content != null ? content.value : this.content));
  }
}

@JsonSerializable(explicitToJson: true)
class UploadDocumentsQuestion {
  const UploadDocumentsQuestion({
    required this.id,
    required this.type,
    this.answer,
    required this.content,
  });

  factory UploadDocumentsQuestion.fromJson(Map<String, dynamic> json) =>
      _$UploadDocumentsQuestionFromJson(json);

  static const toJsonFactory = _$UploadDocumentsQuestionToJson;
  Map<String, dynamic> toJson() => _$UploadDocumentsQuestionToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'answer', includeIfNull: false)
  final UploadDocumentsAnswer? answer;
  @JsonKey(name: 'content', includeIfNull: false)
  final UploadDocumentsContent content;
  static const fromJsonFactory = _$UploadDocumentsQuestionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UploadDocumentsQuestionExtension on UploadDocumentsQuestion {
  UploadDocumentsQuestion copyWith(
      {String? id,
      String? type,
      UploadDocumentsAnswer? answer,
      UploadDocumentsContent? content}) {
    return UploadDocumentsQuestion(
        id: id ?? this.id,
        type: type ?? this.type,
        answer: answer ?? this.answer,
        content: content ?? this.content);
  }

  UploadDocumentsQuestion copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? type,
      Wrapped<UploadDocumentsAnswer?>? answer,
      Wrapped<UploadDocumentsContent>? content}) {
    return UploadDocumentsQuestion(
        id: (id != null ? id.value : this.id),
        type: (type != null ? type.value : this.type),
        answer: (answer != null ? answer.value : this.answer),
        content: (content != null ? content.value : this.content));
  }
}

@JsonSerializable(explicitToJson: true)
class SelectContent {
  const SelectContent({
    required this.headerText,
    required this.text,
    this.isPopUp,
    this.buttonText,
    required this.options,
  });

  factory SelectContent.fromJson(Map<String, dynamic> json) =>
      _$SelectContentFromJson(json);

  static const toJsonFactory = _$SelectContentToJson;
  Map<String, dynamic> toJson() => _$SelectContentToJson(this);

  @JsonKey(name: 'headerText', includeIfNull: false)
  final String headerText;
  @JsonKey(name: 'text', includeIfNull: false)
  final String text;
  @JsonKey(name: 'isPopUp', includeIfNull: false)
  final bool? isPopUp;
  @JsonKey(name: 'buttonText', includeIfNull: false)
  final String? buttonText;
  @JsonKey(name: 'options', includeIfNull: false)
  final List<Option> options;
  static const fromJsonFactory = _$SelectContentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $SelectContentExtension on SelectContent {
  SelectContent copyWith(
      {String? headerText,
      String? text,
      bool? isPopUp,
      String? buttonText,
      List<Option>? options}) {
    return SelectContent(
        headerText: headerText ?? this.headerText,
        text: text ?? this.text,
        isPopUp: isPopUp ?? this.isPopUp,
        buttonText: buttonText ?? this.buttonText,
        options: options ?? this.options);
  }

  SelectContent copyWithWrapped(
      {Wrapped<String>? headerText,
      Wrapped<String>? text,
      Wrapped<bool?>? isPopUp,
      Wrapped<String?>? buttonText,
      Wrapped<List<Option>>? options}) {
    return SelectContent(
        headerText: (headerText != null ? headerText.value : this.headerText),
        text: (text != null ? text.value : this.text),
        isPopUp: (isPopUp != null ? isPopUp.value : this.isPopUp),
        buttonText: (buttonText != null ? buttonText.value : this.buttonText),
        options: (options != null ? options.value : this.options));
  }
}

@JsonSerializable(explicitToJson: true)
class SelectWithSearchContent {
  const SelectWithSearchContent({
    required this.headerText,
    required this.text,
    required this.hint,
    required this.options,
  });

  factory SelectWithSearchContent.fromJson(Map<String, dynamic> json) =>
      _$SelectWithSearchContentFromJson(json);

  static const toJsonFactory = _$SelectWithSearchContentToJson;
  Map<String, dynamic> toJson() => _$SelectWithSearchContentToJson(this);

  @JsonKey(name: 'headerText', includeIfNull: false)
  final String headerText;
  @JsonKey(name: 'text', includeIfNull: false)
  final String text;
  @JsonKey(name: 'hint', includeIfNull: false)
  final String hint;
  @JsonKey(name: 'options', includeIfNull: false)
  final List<Option> options;
  static const fromJsonFactory = _$SelectWithSearchContentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $SelectWithSearchContentExtension on SelectWithSearchContent {
  SelectWithSearchContent copyWith(
      {String? headerText, String? text, String? hint, List<Option>? options}) {
    return SelectWithSearchContent(
        headerText: headerText ?? this.headerText,
        text: text ?? this.text,
        hint: hint ?? this.hint,
        options: options ?? this.options);
  }

  SelectWithSearchContent copyWithWrapped(
      {Wrapped<String>? headerText,
      Wrapped<String>? text,
      Wrapped<String>? hint,
      Wrapped<List<Option>>? options}) {
    return SelectWithSearchContent(
        headerText: (headerText != null ? headerText.value : this.headerText),
        text: (text != null ? text.value : this.text),
        hint: (hint != null ? hint.value : this.hint),
        options: (options != null ? options.value : this.options));
  }
}

@JsonSerializable(explicitToJson: true)
class InputContent {
  const InputContent({
    required this.headerText,
    required this.buttonText,
    required this.text,
    required this.options,
  });

  factory InputContent.fromJson(Map<String, dynamic> json) =>
      _$InputContentFromJson(json);

  static const toJsonFactory = _$InputContentToJson;
  Map<String, dynamic> toJson() => _$InputContentToJson(this);

  @JsonKey(name: 'headerText', includeIfNull: false)
  final String headerText;
  @JsonKey(name: 'buttonText', includeIfNull: false)
  final String buttonText;
  @JsonKey(name: 'text', includeIfNull: false)
  final String text;
  @JsonKey(name: 'options', includeIfNull: false)
  final List<Option> options;
  static const fromJsonFactory = _$InputContentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $InputContentExtension on InputContent {
  InputContent copyWith(
      {String? headerText,
      String? buttonText,
      String? text,
      List<Option>? options}) {
    return InputContent(
        headerText: headerText ?? this.headerText,
        buttonText: buttonText ?? this.buttonText,
        text: text ?? this.text,
        options: options ?? this.options);
  }

  InputContent copyWithWrapped(
      {Wrapped<String>? headerText,
      Wrapped<String>? buttonText,
      Wrapped<String>? text,
      Wrapped<List<Option>>? options}) {
    return InputContent(
        headerText: (headerText != null ? headerText.value : this.headerText),
        buttonText: (buttonText != null ? buttonText.value : this.buttonText),
        text: (text != null ? text.value : this.text),
        options: (options != null ? options.value : this.options));
  }
}

@JsonSerializable(explicitToJson: true)
class AddressContent {
  const AddressContent({
    required this.headerText,
    required this.text,
    required this.hint,
    required this.cityByCountry,
    required this.options,
  });

  factory AddressContent.fromJson(Map<String, dynamic> json) =>
      _$AddressContentFromJson(json);

  static const toJsonFactory = _$AddressContentToJson;
  Map<String, dynamic> toJson() => _$AddressContentToJson(this);

  @JsonKey(name: 'headerText', includeIfNull: false)
  final String headerText;
  @JsonKey(name: 'text', includeIfNull: false)
  final String text;
  @JsonKey(name: 'hint', includeIfNull: false)
  final String hint;
  @JsonKey(name: 'cityByCountry', includeIfNull: false)
  final List<CityByCountry> cityByCountry;
  @JsonKey(name: 'options', includeIfNull: false)
  final List<Option> options;
  static const fromJsonFactory = _$AddressContentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AddressContentExtension on AddressContent {
  AddressContent copyWith(
      {String? headerText,
      String? text,
      String? hint,
      List<CityByCountry>? cityByCountry,
      List<Option>? options}) {
    return AddressContent(
        headerText: headerText ?? this.headerText,
        text: text ?? this.text,
        hint: hint ?? this.hint,
        cityByCountry: cityByCountry ?? this.cityByCountry,
        options: options ?? this.options);
  }

  AddressContent copyWithWrapped(
      {Wrapped<String>? headerText,
      Wrapped<String>? text,
      Wrapped<String>? hint,
      Wrapped<List<CityByCountry>>? cityByCountry,
      Wrapped<List<Option>>? options}) {
    return AddressContent(
        headerText: (headerText != null ? headerText.value : this.headerText),
        text: (text != null ? text.value : this.text),
        hint: (hint != null ? hint.value : this.hint),
        cityByCountry:
            (cityByCountry != null ? cityByCountry.value : this.cityByCountry),
        options: (options != null ? options.value : this.options));
  }
}

@JsonSerializable(explicitToJson: true)
class MultiSelectContent {
  const MultiSelectContent({
    required this.headerText,
    required this.text,
    this.isPopUp,
    required this.buttonText,
    required this.options,
  });

  factory MultiSelectContent.fromJson(Map<String, dynamic> json) =>
      _$MultiSelectContentFromJson(json);

  static const toJsonFactory = _$MultiSelectContentToJson;
  Map<String, dynamic> toJson() => _$MultiSelectContentToJson(this);

  @JsonKey(name: 'headerText', includeIfNull: false)
  final String headerText;
  @JsonKey(name: 'text', includeIfNull: false)
  final String text;
  @JsonKey(name: 'isPopUp', includeIfNull: false)
  final bool? isPopUp;
  @JsonKey(name: 'buttonText', includeIfNull: false)
  final String buttonText;
  @JsonKey(name: 'options', includeIfNull: false)
  final List<Option> options;
  static const fromJsonFactory = _$MultiSelectContentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MultiSelectContentExtension on MultiSelectContent {
  MultiSelectContent copyWith(
      {String? headerText,
      String? text,
      bool? isPopUp,
      String? buttonText,
      List<Option>? options}) {
    return MultiSelectContent(
        headerText: headerText ?? this.headerText,
        text: text ?? this.text,
        isPopUp: isPopUp ?? this.isPopUp,
        buttonText: buttonText ?? this.buttonText,
        options: options ?? this.options);
  }

  MultiSelectContent copyWithWrapped(
      {Wrapped<String>? headerText,
      Wrapped<String>? text,
      Wrapped<bool?>? isPopUp,
      Wrapped<String>? buttonText,
      Wrapped<List<Option>>? options}) {
    return MultiSelectContent(
        headerText: (headerText != null ? headerText.value : this.headerText),
        text: (text != null ? text.value : this.text),
        isPopUp: (isPopUp != null ? isPopUp.value : this.isPopUp),
        buttonText: (buttonText != null ? buttonText.value : this.buttonText),
        options: (options != null ? options.value : this.options));
  }
}

@JsonSerializable(explicitToJson: true)
class MultiSectionsContent {
  const MultiSectionsContent({
    required this.headerText,
    required this.text,
    this.textLinks,
    this.checkBoxTexts,
    required this.sectionsContent,
    required this.buttonText,
    this.secondaryButtonText,
  });

  factory MultiSectionsContent.fromJson(Map<String, dynamic> json) =>
      _$MultiSectionsContentFromJson(json);

  static const toJsonFactory = _$MultiSectionsContentToJson;
  Map<String, dynamic> toJson() => _$MultiSectionsContentToJson(this);

  @JsonKey(name: 'headerText', includeIfNull: false)
  final String headerText;
  @JsonKey(name: 'text', includeIfNull: false)
  final String text;
  @JsonKey(name: 'textLinks', includeIfNull: false, defaultValue: <TextLink>[])
  final List<TextLink>? textLinks;
  @JsonKey(
      name: 'checkBoxTexts', includeIfNull: false, defaultValue: <String>[])
  final List<String>? checkBoxTexts;
  @JsonKey(
      name: 'sectionsContent',
      includeIfNull: false,
      defaultValue: <SectionContent>[])
  final List<SectionContent> sectionsContent;
  @JsonKey(name: 'buttonText', includeIfNull: false)
  final String buttonText;
  @JsonKey(name: 'secondaryButtonText', includeIfNull: false)
  final String? secondaryButtonText;
  static const fromJsonFactory = _$MultiSectionsContentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MultiSectionsContentExtension on MultiSectionsContent {
  MultiSectionsContent copyWith(
      {String? headerText,
      String? text,
      List<TextLink>? textLinks,
      List<String>? checkBoxTexts,
      List<SectionContent>? sectionsContent,
      String? buttonText,
      String? secondaryButtonText}) {
    return MultiSectionsContent(
        headerText: headerText ?? this.headerText,
        text: text ?? this.text,
        textLinks: textLinks ?? this.textLinks,
        checkBoxTexts: checkBoxTexts ?? this.checkBoxTexts,
        sectionsContent: sectionsContent ?? this.sectionsContent,
        buttonText: buttonText ?? this.buttonText,
        secondaryButtonText: secondaryButtonText ?? this.secondaryButtonText);
  }

  MultiSectionsContent copyWithWrapped(
      {Wrapped<String>? headerText,
      Wrapped<String>? text,
      Wrapped<List<TextLink>?>? textLinks,
      Wrapped<List<String>?>? checkBoxTexts,
      Wrapped<List<SectionContent>>? sectionsContent,
      Wrapped<String>? buttonText,
      Wrapped<String?>? secondaryButtonText}) {
    return MultiSectionsContent(
        headerText: (headerText != null ? headerText.value : this.headerText),
        text: (text != null ? text.value : this.text),
        textLinks: (textLinks != null ? textLinks.value : this.textLinks),
        checkBoxTexts:
            (checkBoxTexts != null ? checkBoxTexts.value : this.checkBoxTexts),
        sectionsContent: (sectionsContent != null
            ? sectionsContent.value
            : this.sectionsContent),
        buttonText: (buttonText != null ? buttonText.value : this.buttonText),
        secondaryButtonText: (secondaryButtonText != null
            ? secondaryButtonText.value
            : this.secondaryButtonText));
  }
}

@JsonSerializable(explicitToJson: true)
class UploadDocumentsContent {
  const UploadDocumentsContent({
    required this.categoryType,
    required this.buttonText,
    required this.headerText,
    required this.description,
    required this.documents,
  });

  factory UploadDocumentsContent.fromJson(Map<String, dynamic> json) =>
      _$UploadDocumentsContentFromJson(json);

  static const toJsonFactory = _$UploadDocumentsContentToJson;
  Map<String, dynamic> toJson() => _$UploadDocumentsContentToJson(this);

  @JsonKey(name: 'categoryType', includeIfNull: false)
  final String categoryType;
  @JsonKey(name: 'buttonText', includeIfNull: false)
  final String buttonText;
  @JsonKey(name: 'headerText', includeIfNull: false)
  final String headerText;
  @JsonKey(name: 'description', includeIfNull: false)
  final String description;
  @JsonKey(
      name: 'documents',
      includeIfNull: false,
      defaultValue: <UploadableDocument>[])
  final List<UploadableDocument> documents;
  static const fromJsonFactory = _$UploadDocumentsContentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UploadDocumentsContentExtension on UploadDocumentsContent {
  UploadDocumentsContent copyWith(
      {String? categoryType,
      String? buttonText,
      String? headerText,
      String? description,
      List<UploadableDocument>? documents}) {
    return UploadDocumentsContent(
        categoryType: categoryType ?? this.categoryType,
        buttonText: buttonText ?? this.buttonText,
        headerText: headerText ?? this.headerText,
        description: description ?? this.description,
        documents: documents ?? this.documents);
  }

  UploadDocumentsContent copyWithWrapped(
      {Wrapped<String>? categoryType,
      Wrapped<String>? buttonText,
      Wrapped<String>? headerText,
      Wrapped<String>? description,
      Wrapped<List<UploadableDocument>>? documents}) {
    return UploadDocumentsContent(
        categoryType:
            (categoryType != null ? categoryType.value : this.categoryType),
        buttonText: (buttonText != null ? buttonText.value : this.buttonText),
        headerText: (headerText != null ? headerText.value : this.headerText),
        description:
            (description != null ? description.value : this.description),
        documents: (documents != null ? documents.value : this.documents));
  }
}

@JsonSerializable(explicitToJson: true)
class UploadableDocument {
  const UploadableDocument({
    required this.name,
    required this.type,
    required this.validations,
  });

  factory UploadableDocument.fromJson(Map<String, dynamic> json) =>
      _$UploadableDocumentFromJson(json);

  static const toJsonFactory = _$UploadableDocumentToJson;
  Map<String, dynamic> toJson() => _$UploadableDocumentToJson(this);

  @JsonKey(name: 'name', includeIfNull: false)
  final String name;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'validations', includeIfNull: false)
  final UploadableDocumentValidations validations;
  static const fromJsonFactory = _$UploadableDocumentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UploadableDocumentExtension on UploadableDocument {
  UploadableDocument copyWith(
      {String? name,
      String? type,
      UploadableDocumentValidations? validations}) {
    return UploadableDocument(
        name: name ?? this.name,
        type: type ?? this.type,
        validations: validations ?? this.validations);
  }

  UploadableDocument copyWithWrapped(
      {Wrapped<String>? name,
      Wrapped<String>? type,
      Wrapped<UploadableDocumentValidations>? validations}) {
    return UploadableDocument(
        name: (name != null ? name.value : this.name),
        type: (type != null ? type.value : this.type),
        validations:
            (validations != null ? validations.value : this.validations));
  }
}

@JsonSerializable(explicitToJson: true)
class UploadableDocumentValidations {
  const UploadableDocumentValidations({
    required this.minFilesAmount,
    required this.maxFilesAmount,
    required this.maxFileSize,
    required this.contentTypes,
  });

  factory UploadableDocumentValidations.fromJson(Map<String, dynamic> json) =>
      _$UploadableDocumentValidationsFromJson(json);

  static const toJsonFactory = _$UploadableDocumentValidationsToJson;
  Map<String, dynamic> toJson() => _$UploadableDocumentValidationsToJson(this);

  @JsonKey(name: 'minFilesAmount', includeIfNull: false)
  final int minFilesAmount;
  @JsonKey(name: 'maxFilesAmount', includeIfNull: false)
  final int maxFilesAmount;
  @JsonKey(name: 'maxFileSize', includeIfNull: false)
  final int maxFileSize;
  @JsonKey(name: 'contentTypes', includeIfNull: false, defaultValue: <String>[])
  final List<String> contentTypes;
  static const fromJsonFactory = _$UploadableDocumentValidationsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UploadableDocumentValidationsExtension
    on UploadableDocumentValidations {
  UploadableDocumentValidations copyWith(
      {int? minFilesAmount,
      int? maxFilesAmount,
      int? maxFileSize,
      List<String>? contentTypes}) {
    return UploadableDocumentValidations(
        minFilesAmount: minFilesAmount ?? this.minFilesAmount,
        maxFilesAmount: maxFilesAmount ?? this.maxFilesAmount,
        maxFileSize: maxFileSize ?? this.maxFileSize,
        contentTypes: contentTypes ?? this.contentTypes);
  }

  UploadableDocumentValidations copyWithWrapped(
      {Wrapped<int>? minFilesAmount,
      Wrapped<int>? maxFilesAmount,
      Wrapped<int>? maxFileSize,
      Wrapped<List<String>>? contentTypes}) {
    return UploadableDocumentValidations(
        minFilesAmount: (minFilesAmount != null
            ? minFilesAmount.value
            : this.minFilesAmount),
        maxFilesAmount: (maxFilesAmount != null
            ? maxFilesAmount.value
            : this.maxFilesAmount),
        maxFileSize:
            (maxFileSize != null ? maxFileSize.value : this.maxFileSize),
        contentTypes:
            (contentTypes != null ? contentTypes.value : this.contentTypes));
  }
}

@JsonSerializable(explicitToJson: true)
class UploadDocumentsMismatch {
  const UploadDocumentsMismatch({
    required this.name,
    required this.headerText,
    required this.explanation,
    required this.icon,
  });

  factory UploadDocumentsMismatch.fromJson(Map<String, dynamic> json) =>
      _$UploadDocumentsMismatchFromJson(json);

  static const toJsonFactory = _$UploadDocumentsMismatchToJson;
  Map<String, dynamic> toJson() => _$UploadDocumentsMismatchToJson(this);

  @JsonKey(name: 'name', includeIfNull: false)
  final String name;
  @JsonKey(name: 'headerText', includeIfNull: false)
  final String headerText;
  @JsonKey(name: 'explanation', includeIfNull: false)
  final String explanation;
  @JsonKey(name: 'icon', includeIfNull: false)
  final String icon;
  static const fromJsonFactory = _$UploadDocumentsMismatchFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UploadDocumentsMismatchExtension on UploadDocumentsMismatch {
  UploadDocumentsMismatch copyWith(
      {String? name, String? headerText, String? explanation, String? icon}) {
    return UploadDocumentsMismatch(
        name: name ?? this.name,
        headerText: headerText ?? this.headerText,
        explanation: explanation ?? this.explanation,
        icon: icon ?? this.icon);
  }

  UploadDocumentsMismatch copyWithWrapped(
      {Wrapped<String>? name,
      Wrapped<String>? headerText,
      Wrapped<String>? explanation,
      Wrapped<String>? icon}) {
    return UploadDocumentsMismatch(
        name: (name != null ? name.value : this.name),
        headerText: (headerText != null ? headerText.value : this.headerText),
        explanation:
            (explanation != null ? explanation.value : this.explanation),
        icon: (icon != null ? icon.value : this.icon));
  }
}

@JsonSerializable(explicitToJson: true)
class TextLink {
  const TextLink({
    required this.highlightedText,
    required this.bottomSheetContent,
  });

  factory TextLink.fromJson(Map<String, dynamic> json) =>
      _$TextLinkFromJson(json);

  static const toJsonFactory = _$TextLinkToJson;
  Map<String, dynamic> toJson() => _$TextLinkToJson(this);

  @JsonKey(name: 'highlightedText', includeIfNull: false)
  final String highlightedText;
  @JsonKey(name: 'bottomSheetContent', includeIfNull: false)
  final BottomSheetContent bottomSheetContent;
  static const fromJsonFactory = _$TextLinkFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $TextLinkExtension on TextLink {
  TextLink copyWith(
      {String? highlightedText, BottomSheetContent? bottomSheetContent}) {
    return TextLink(
        highlightedText: highlightedText ?? this.highlightedText,
        bottomSheetContent: bottomSheetContent ?? this.bottomSheetContent);
  }

  TextLink copyWithWrapped(
      {Wrapped<String>? highlightedText,
      Wrapped<BottomSheetContent>? bottomSheetContent}) {
    return TextLink(
        highlightedText: (highlightedText != null
            ? highlightedText.value
            : this.highlightedText),
        bottomSheetContent: (bottomSheetContent != null
            ? bottomSheetContent.value
            : this.bottomSheetContent));
  }
}

@JsonSerializable(explicitToJson: true)
class BottomSheetContent {
  const BottomSheetContent({
    this.headerText,
    this.text,
  });

  factory BottomSheetContent.fromJson(Map<String, dynamic> json) =>
      _$BottomSheetContentFromJson(json);

  static const toJsonFactory = _$BottomSheetContentToJson;
  Map<String, dynamic> toJson() => _$BottomSheetContentToJson(this);

  @JsonKey(name: 'headerText', includeIfNull: false)
  final String? headerText;
  @JsonKey(name: 'text', includeIfNull: false)
  final String? text;
  static const fromJsonFactory = _$BottomSheetContentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $BottomSheetContentExtension on BottomSheetContent {
  BottomSheetContent copyWith({String? headerText, String? text}) {
    return BottomSheetContent(
        headerText: headerText ?? this.headerText, text: text ?? this.text);
  }

  BottomSheetContent copyWithWrapped(
      {Wrapped<String?>? headerText, Wrapped<String?>? text}) {
    return BottomSheetContent(
        headerText: (headerText != null ? headerText.value : this.headerText),
        text: (text != null ? text.value : this.text));
  }
}

@JsonSerializable(explicitToJson: true)
class SectionContent {
  const SectionContent({
    required this.sectionId,
    required this.type,
    required this.label,
    required this.content,
    required this.conditions,
  });

  factory SectionContent.fromJson(Map<String, dynamic> json) =>
      _$SectionContentFromJson(json);

  static const toJsonFactory = _$SectionContentToJson;
  Map<String, dynamic> toJson() => _$SectionContentToJson(this);

  @JsonKey(name: 'sectionId', includeIfNull: false)
  final String sectionId;
  @JsonKey(
    name: 'type',
    includeIfNull: false,
    toJson: questionTypeToJson,
    fromJson: questionTypeFromJson,
  )
  final enums.QuestionType type;
  @JsonKey(name: 'label', includeIfNull: false)
  final String label;
  @JsonKey(name: 'content', includeIfNull: false)
  final Object content;
  @JsonKey(
      name: 'conditions',
      includeIfNull: false,
      defaultValue: <SectionCondition>[])
  final List<SectionCondition> conditions;
  static const fromJsonFactory = _$SectionContentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $SectionContentExtension on SectionContent {
  SectionContent copyWith(
      {String? sectionId,
      enums.QuestionType? type,
      String? label,
      Object? content,
      List<SectionCondition>? conditions}) {
    return SectionContent(
        sectionId: sectionId ?? this.sectionId,
        type: type ?? this.type,
        label: label ?? this.label,
        content: content ?? this.content,
        conditions: conditions ?? this.conditions);
  }

  SectionContent copyWithWrapped(
      {Wrapped<String>? sectionId,
      Wrapped<enums.QuestionType>? type,
      Wrapped<String>? label,
      Wrapped<Object>? content,
      Wrapped<List<SectionCondition>>? conditions}) {
    return SectionContent(
        sectionId: (sectionId != null ? sectionId.value : this.sectionId),
        type: (type != null ? type.value : this.type),
        label: (label != null ? label.value : this.label),
        content: (content != null ? content.value : this.content),
        conditions: (conditions != null ? conditions.value : this.conditions));
  }
}

@JsonSerializable(explicitToJson: true)
class SectionCondition {
  const SectionCondition({
    required this.type,
  });

  factory SectionCondition.fromJson(Map<String, dynamic> json) =>
      _$SectionConditionFromJson(json);

  static const toJsonFactory = _$SectionConditionToJson;
  Map<String, dynamic> toJson() => _$SectionConditionToJson(this);

  @JsonKey(
    name: 'type',
    includeIfNull: false,
    toJson: sectionConditionTypeToJson,
    fromJson: sectionConditionTypeFromJson,
  )
  final enums.SectionConditionType type;
  static const fromJsonFactory = _$SectionConditionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $SectionConditionExtension on SectionCondition {
  SectionCondition copyWith({enums.SectionConditionType? type}) {
    return SectionCondition(type: type ?? this.type);
  }

  SectionCondition copyWithWrapped(
      {Wrapped<enums.SectionConditionType>? type}) {
    return SectionCondition(type: (type != null ? type.value : this.type));
  }
}

@JsonSerializable(explicitToJson: true)
class NextSectionCondition {
  const NextSectionCondition({
    required this.type,
    required this.nextSectionId,
    required this.answer,
  });

  factory NextSectionCondition.fromJson(Map<String, dynamic> json) =>
      _$NextSectionConditionFromJson(json);

  static const toJsonFactory = _$NextSectionConditionToJson;
  Map<String, dynamic> toJson() => _$NextSectionConditionToJson(this);

  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'nextSectionId', includeIfNull: false)
  final String nextSectionId;
  @JsonKey(name: 'answer', includeIfNull: false)
  final Answer answer;
  static const fromJsonFactory = _$NextSectionConditionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $NextSectionConditionExtension on NextSectionCondition {
  NextSectionCondition copyWith(
      {String? type, String? nextSectionId, Answer? answer}) {
    return NextSectionCondition(
        type: type ?? this.type,
        nextSectionId: nextSectionId ?? this.nextSectionId,
        answer: answer ?? this.answer);
  }

  NextSectionCondition copyWithWrapped(
      {Wrapped<String>? type,
      Wrapped<String>? nextSectionId,
      Wrapped<Answer>? answer}) {
    return NextSectionCondition(
        type: (type != null ? type.value : this.type),
        nextSectionId:
            (nextSectionId != null ? nextSectionId.value : this.nextSectionId),
        answer: (answer != null ? answer.value : this.answer));
  }
}

typedef CityByCountries = List<CityByCountry>;

@JsonSerializable(explicitToJson: true)
class CityByCountry {
  const CityByCountry({
    required this.id,
    required this.countryName,
    required this.cities,
  });

  factory CityByCountry.fromJson(Map<String, dynamic> json) =>
      _$CityByCountryFromJson(json);

  static const toJsonFactory = _$CityByCountryToJson;
  Map<String, dynamic> toJson() => _$CityByCountryToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'countryName', includeIfNull: false)
  final String countryName;
  @JsonKey(name: 'cities', includeIfNull: false)
  final List<City> cities;
  static const fromJsonFactory = _$CityByCountryFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CityByCountryExtension on CityByCountry {
  CityByCountry copyWith(
      {String? id, String? countryName, List<City>? cities}) {
    return CityByCountry(
        id: id ?? this.id,
        countryName: countryName ?? this.countryName,
        cities: cities ?? this.cities);
  }

  CityByCountry copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? countryName,
      Wrapped<List<City>>? cities}) {
    return CityByCountry(
        id: (id != null ? id.value : this.id),
        countryName:
            (countryName != null ? countryName.value : this.countryName),
        cities: (cities != null ? cities.value : this.cities));
  }
}

typedef Cities = List<City>;

@JsonSerializable(explicitToJson: true)
class City {
  const City({
    required this.id,
    required this.name,
  });

  factory City.fromJson(Map<String, dynamic> json) => _$CityFromJson(json);

  static const toJsonFactory = _$CityToJson;
  Map<String, dynamic> toJson() => _$CityToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'name', includeIfNull: false)
  final String name;
  static const fromJsonFactory = _$CityFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CityExtension on City {
  City copyWith({String? id, String? name}) {
    return City(id: id ?? this.id, name: name ?? this.name);
  }

  City copyWithWrapped({Wrapped<String>? id, Wrapped<String>? name}) {
    return City(
        id: (id != null ? id.value : this.id),
        name: (name != null ? name.value : this.name));
  }
}

@JsonSerializable(explicitToJson: true)
class TermsAndConditionsContent {
  const TermsAndConditionsContent({
    required this.headerText,
    required this.text,
    required this.documents,
    required this.sections,
    required this.statements,
    required this.riskDisclosure,
    required this.termsConditions,
    required this.buttonText,
  });

  factory TermsAndConditionsContent.fromJson(Map<String, dynamic> json) =>
      _$TermsAndConditionsContentFromJson(json);

  static const toJsonFactory = _$TermsAndConditionsContentToJson;
  Map<String, dynamic> toJson() => _$TermsAndConditionsContentToJson(this);

  @JsonKey(name: 'headerText', includeIfNull: false)
  final String headerText;
  @JsonKey(name: 'text', includeIfNull: false)
  final String text;
  @JsonKey(name: 'documents', includeIfNull: false)
  final List<TermsAndConditions> documents;
  @JsonKey(name: 'sections', includeIfNull: false)
  final List<TermsAndConditionsSection> sections;
  @JsonKey(name: 'statements', includeIfNull: false)
  final List<Statement> statements;
  @JsonKey(name: 'riskDisclosure', includeIfNull: false)
  final Agreement riskDisclosure;
  @JsonKey(name: 'termsConditions', includeIfNull: false)
  final Agreement termsConditions;
  @JsonKey(name: 'buttonText', includeIfNull: false)
  final String buttonText;
  static const fromJsonFactory = _$TermsAndConditionsContentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $TermsAndConditionsContentExtension on TermsAndConditionsContent {
  TermsAndConditionsContent copyWith(
      {String? headerText,
      String? text,
      List<TermsAndConditions>? documents,
      List<TermsAndConditionsSection>? sections,
      List<Statement>? statements,
      Agreement? riskDisclosure,
      Agreement? termsConditions,
      String? buttonText}) {
    return TermsAndConditionsContent(
        headerText: headerText ?? this.headerText,
        text: text ?? this.text,
        documents: documents ?? this.documents,
        sections: sections ?? this.sections,
        statements: statements ?? this.statements,
        riskDisclosure: riskDisclosure ?? this.riskDisclosure,
        termsConditions: termsConditions ?? this.termsConditions,
        buttonText: buttonText ?? this.buttonText);
  }

  TermsAndConditionsContent copyWithWrapped(
      {Wrapped<String>? headerText,
      Wrapped<String>? text,
      Wrapped<List<TermsAndConditions>>? documents,
      Wrapped<List<TermsAndConditionsSection>>? sections,
      Wrapped<List<Statement>>? statements,
      Wrapped<Agreement>? riskDisclosure,
      Wrapped<Agreement>? termsConditions,
      Wrapped<String>? buttonText}) {
    return TermsAndConditionsContent(
        headerText: (headerText != null ? headerText.value : this.headerText),
        text: (text != null ? text.value : this.text),
        documents: (documents != null ? documents.value : this.documents),
        sections: (sections != null ? sections.value : this.sections),
        statements: (statements != null ? statements.value : this.statements),
        riskDisclosure: (riskDisclosure != null
            ? riskDisclosure.value
            : this.riskDisclosure),
        termsConditions: (termsConditions != null
            ? termsConditions.value
            : this.termsConditions),
        buttonText: (buttonText != null ? buttonText.value : this.buttonText));
  }
}

typedef TermsAndConditionsSections = List<TermsAndConditionsSection>;

@JsonSerializable(explicitToJson: true)
class TermsAndConditionsSection {
  const TermsAndConditionsSection({
    this.name,
    this.acceptance,
    required this.group,
    required this.documents,
  });

  factory TermsAndConditionsSection.fromJson(Map<String, dynamic> json) =>
      _$TermsAndConditionsSectionFromJson(json);

  static const toJsonFactory = _$TermsAndConditionsSectionToJson;
  Map<String, dynamic> toJson() => _$TermsAndConditionsSectionToJson(this);

  @JsonKey(name: 'name', includeIfNull: false)
  final String? name;
  @JsonKey(name: 'acceptance', includeIfNull: false)
  final Acceptance? acceptance;
  @JsonKey(
    name: 'group',
    includeIfNull: false,
    toJson: termsAndConditionsGroupToJson,
    fromJson: termsAndConditionsGroupFromJson,
  )
  final enums.TermsAndConditionsGroup group;
  @JsonKey(name: 'documents', includeIfNull: false)
  final List<TermsAndConditions> documents;
  static const fromJsonFactory = _$TermsAndConditionsSectionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $TermsAndConditionsSectionExtension on TermsAndConditionsSection {
  TermsAndConditionsSection copyWith(
      {String? name,
      Acceptance? acceptance,
      enums.TermsAndConditionsGroup? group,
      List<TermsAndConditions>? documents}) {
    return TermsAndConditionsSection(
        name: name ?? this.name,
        acceptance: acceptance ?? this.acceptance,
        group: group ?? this.group,
        documents: documents ?? this.documents);
  }

  TermsAndConditionsSection copyWithWrapped(
      {Wrapped<String?>? name,
      Wrapped<Acceptance?>? acceptance,
      Wrapped<enums.TermsAndConditionsGroup>? group,
      Wrapped<List<TermsAndConditions>>? documents}) {
    return TermsAndConditionsSection(
        name: (name != null ? name.value : this.name),
        acceptance: (acceptance != null ? acceptance.value : this.acceptance),
        group: (group != null ? group.value : this.group),
        documents: (documents != null ? documents.value : this.documents));
  }
}

@JsonSerializable(explicitToJson: true)
class Acceptance {
  const Acceptance({
    required this.text,
    required this.defaultAccepted,
  });

  factory Acceptance.fromJson(Map<String, dynamic> json) =>
      _$AcceptanceFromJson(json);

  static const toJsonFactory = _$AcceptanceToJson;
  Map<String, dynamic> toJson() => _$AcceptanceToJson(this);

  @JsonKey(name: 'text', includeIfNull: false)
  final String text;
  @JsonKey(name: 'defaultAccepted', includeIfNull: false)
  final bool defaultAccepted;
  static const fromJsonFactory = _$AcceptanceFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AcceptanceExtension on Acceptance {
  Acceptance copyWith({String? text, bool? defaultAccepted}) {
    return Acceptance(
        text: text ?? this.text,
        defaultAccepted: defaultAccepted ?? this.defaultAccepted);
  }

  Acceptance copyWithWrapped(
      {Wrapped<String>? text, Wrapped<bool>? defaultAccepted}) {
    return Acceptance(
        text: (text != null ? text.value : this.text),
        defaultAccepted: (defaultAccepted != null
            ? defaultAccepted.value
            : this.defaultAccepted));
  }
}

typedef TermsAndConditionsList = List<TermsAndConditions>;

@JsonSerializable(explicitToJson: true)
class TermsAndConditions {
  const TermsAndConditions({
    required this.name,
    this.required,
    required this.type,
    required this.version,
    required this.documents,
  });

  factory TermsAndConditions.fromJson(Map<String, dynamic> json) =>
      _$TermsAndConditionsFromJson(json);

  static const toJsonFactory = _$TermsAndConditionsToJson;
  Map<String, dynamic> toJson() => _$TermsAndConditionsToJson(this);

  @JsonKey(name: 'name', includeIfNull: false)
  final String name;
  @JsonKey(name: 'required', includeIfNull: false)
  final bool? required;
  @JsonKey(
    name: 'type',
    includeIfNull: false,
    toJson: termsAndConditionsTypeToJson,
    fromJson: termsAndConditionsTypeFromJson,
  )
  final enums.TermsAndConditionsType type;
  @JsonKey(name: 'version', includeIfNull: false)
  final String version;
  @JsonKey(
      name: 'documents',
      includeIfNull: false,
      defaultValue: <TranslatedTermsAndConditionsDocument>[])
  final List<TranslatedTermsAndConditionsDocument> documents;
  static const fromJsonFactory = _$TermsAndConditionsFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $TermsAndConditionsExtension on TermsAndConditions {
  TermsAndConditions copyWith(
      {String? name,
      bool? required,
      enums.TermsAndConditionsType? type,
      String? version,
      List<TranslatedTermsAndConditionsDocument>? documents}) {
    return TermsAndConditions(
        name: name ?? this.name,
        required: required ?? this.required,
        type: type ?? this.type,
        version: version ?? this.version,
        documents: documents ?? this.documents);
  }

  TermsAndConditions copyWithWrapped(
      {Wrapped<String>? name,
      Wrapped<bool?>? required,
      Wrapped<enums.TermsAndConditionsType>? type,
      Wrapped<String>? version,
      Wrapped<List<TranslatedTermsAndConditionsDocument>>? documents}) {
    return TermsAndConditions(
        name: (name != null ? name.value : this.name),
        required: (required != null ? required.value : this.required),
        type: (type != null ? type.value : this.type),
        version: (version != null ? version.value : this.version),
        documents: (documents != null ? documents.value : this.documents));
  }
}

typedef Statements = List<Statement>;

@JsonSerializable(explicitToJson: true)
class Statement {
  const Statement({
    required this.text,
    required this.icon,
  });

  factory Statement.fromJson(Map<String, dynamic> json) =>
      _$StatementFromJson(json);

  static const toJsonFactory = _$StatementToJson;
  Map<String, dynamic> toJson() => _$StatementToJson(this);

  @JsonKey(name: 'text', includeIfNull: false)
  final String text;
  @JsonKey(
    name: 'icon',
    includeIfNull: false,
    toJson: statementIconToJson,
    fromJson: statementIconFromJson,
  )
  final enums.StatementIcon icon;
  static const fromJsonFactory = _$StatementFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $StatementExtension on Statement {
  Statement copyWith({String? text, enums.StatementIcon? icon}) {
    return Statement(text: text ?? this.text, icon: icon ?? this.icon);
  }

  Statement copyWithWrapped(
      {Wrapped<String>? text, Wrapped<enums.StatementIcon>? icon}) {
    return Statement(
        text: (text != null ? text.value : this.text),
        icon: (icon != null ? icon.value : this.icon));
  }
}

@JsonSerializable(explicitToJson: true)
class Agreement {
  const Agreement({
    required this.text,
    required this.highlightedText,
    required this.buttonText,
    required this.documents,
  });

  factory Agreement.fromJson(Map<String, dynamic> json) =>
      _$AgreementFromJson(json);

  static const toJsonFactory = _$AgreementToJson;
  Map<String, dynamic> toJson() => _$AgreementToJson(this);

  @JsonKey(name: 'text', includeIfNull: false)
  final String text;
  @JsonKey(name: 'highlightedText', includeIfNull: false)
  final String highlightedText;
  @JsonKey(name: 'buttonText', includeIfNull: false)
  final String buttonText;
  @JsonKey(name: 'documents', includeIfNull: false)
  final List<DocumentWithLanguage> documents;
  static const fromJsonFactory = _$AgreementFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AgreementExtension on Agreement {
  Agreement copyWith(
      {String? text,
      String? highlightedText,
      String? buttonText,
      List<DocumentWithLanguage>? documents}) {
    return Agreement(
        text: text ?? this.text,
        highlightedText: highlightedText ?? this.highlightedText,
        buttonText: buttonText ?? this.buttonText,
        documents: documents ?? this.documents);
  }

  Agreement copyWithWrapped(
      {Wrapped<String>? text,
      Wrapped<String>? highlightedText,
      Wrapped<String>? buttonText,
      Wrapped<List<DocumentWithLanguage>>? documents}) {
    return Agreement(
        text: (text != null ? text.value : this.text),
        highlightedText: (highlightedText != null
            ? highlightedText.value
            : this.highlightedText),
        buttonText: (buttonText != null ? buttonText.value : this.buttonText),
        documents: (documents != null ? documents.value : this.documents));
  }
}

@JsonSerializable(explicitToJson: true)
class TranslatedTermsAndConditionsDocument {
  const TranslatedTermsAndConditionsDocument({
    required this.name,
    required this.language,
    required this.url,
  });

  factory TranslatedTermsAndConditionsDocument.fromJson(
          Map<String, dynamic> json) =>
      _$TranslatedTermsAndConditionsDocumentFromJson(json);

  static const toJsonFactory = _$TranslatedTermsAndConditionsDocumentToJson;
  Map<String, dynamic> toJson() =>
      _$TranslatedTermsAndConditionsDocumentToJson(this);

  @JsonKey(name: 'name', includeIfNull: false)
  final String name;
  @JsonKey(name: 'language', includeIfNull: false)
  final String language;
  @JsonKey(name: 'url', includeIfNull: false)
  final String url;
  static const fromJsonFactory = _$TranslatedTermsAndConditionsDocumentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $TranslatedTermsAndConditionsDocumentExtension
    on TranslatedTermsAndConditionsDocument {
  TranslatedTermsAndConditionsDocument copyWith(
      {String? name, String? language, String? url}) {
    return TranslatedTermsAndConditionsDocument(
        name: name ?? this.name,
        language: language ?? this.language,
        url: url ?? this.url);
  }

  TranslatedTermsAndConditionsDocument copyWithWrapped(
      {Wrapped<String>? name,
      Wrapped<String>? language,
      Wrapped<String>? url}) {
    return TranslatedTermsAndConditionsDocument(
        name: (name != null ? name.value : this.name),
        language: (language != null ? language.value : this.language),
        url: (url != null ? url.value : this.url));
  }
}

typedef DocumentsWithLanguage = List<DocumentWithLanguage>;

@JsonSerializable(explicitToJson: true)
class DocumentWithLanguage {
  const DocumentWithLanguage({
    required this.language,
    required this.documents,
  });

  factory DocumentWithLanguage.fromJson(Map<String, dynamic> json) =>
      _$DocumentWithLanguageFromJson(json);

  static const toJsonFactory = _$DocumentWithLanguageToJson;
  Map<String, dynamic> toJson() => _$DocumentWithLanguageToJson(this);

  @JsonKey(name: 'language', includeIfNull: false)
  final String language;
  @JsonKey(name: 'documents', includeIfNull: false, defaultValue: <Document>[])
  final List<Document> documents;
  static const fromJsonFactory = _$DocumentWithLanguageFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $DocumentWithLanguageExtension on DocumentWithLanguage {
  DocumentWithLanguage copyWith({String? language, List<Document>? documents}) {
    return DocumentWithLanguage(
        language: language ?? this.language,
        documents: documents ?? this.documents);
  }

  DocumentWithLanguage copyWithWrapped(
      {Wrapped<String>? language, Wrapped<List<Document>>? documents}) {
    return DocumentWithLanguage(
        language: (language != null ? language.value : this.language),
        documents: (documents != null ? documents.value : this.documents));
  }
}

@JsonSerializable(explicitToJson: true)
class Document {
  const Document({
    required this.name,
    required this.url,
  });

  factory Document.fromJson(Map<String, dynamic> json) =>
      _$DocumentFromJson(json);

  static const toJsonFactory = _$DocumentToJson;
  Map<String, dynamic> toJson() => _$DocumentToJson(this);

  @JsonKey(name: 'name', includeIfNull: false)
  final String name;
  @JsonKey(name: 'url', includeIfNull: false)
  final String url;
  static const fromJsonFactory = _$DocumentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $DocumentExtension on Document {
  Document copyWith({String? name, String? url}) {
    return Document(name: name ?? this.name, url: url ?? this.url);
  }

  Document copyWithWrapped({Wrapped<String>? name, Wrapped<String>? url}) {
    return Document(
        name: (name != null ? name.value : this.name),
        url: (url != null ? url.value : this.url));
  }
}

@JsonSerializable(explicitToJson: true)
class SelectAnswer {
  const SelectAnswer({
    required this.type,
    required this.$value,
  });

  factory SelectAnswer.fromJson(Map<String, dynamic> json) =>
      _$SelectAnswerFromJson(json);

  static const toJsonFactory = _$SelectAnswerToJson;
  Map<String, dynamic> toJson() => _$SelectAnswerToJson(this);

  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'value', includeIfNull: false)
  final String $value;
  static const fromJsonFactory = _$SelectAnswerFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $SelectAnswerExtension on SelectAnswer {
  SelectAnswer copyWith({String? type, String? $value}) {
    return SelectAnswer(type: type ?? this.type, $value: $value ?? this.$value);
  }

  SelectAnswer copyWithWrapped(
      {Wrapped<String>? type, Wrapped<String>? $value}) {
    return SelectAnswer(
        type: (type != null ? type.value : this.type),
        $value: ($value != null ? $value.value : this.$value));
  }
}

@JsonSerializable(explicitToJson: true)
class MultiSelectAnswer {
  const MultiSelectAnswer({
    required this.type,
    required this.values,
  });

  factory MultiSelectAnswer.fromJson(Map<String, dynamic> json) =>
      _$MultiSelectAnswerFromJson(json);

  static const toJsonFactory = _$MultiSelectAnswerToJson;
  Map<String, dynamic> toJson() => _$MultiSelectAnswerToJson(this);

  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'values', includeIfNull: false, defaultValue: <String>[])
  final List<String> values;
  static const fromJsonFactory = _$MultiSelectAnswerFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MultiSelectAnswerExtension on MultiSelectAnswer {
  MultiSelectAnswer copyWith({String? type, List<String>? values}) {
    return MultiSelectAnswer(
        type: type ?? this.type, values: values ?? this.values);
  }

  MultiSelectAnswer copyWithWrapped(
      {Wrapped<String>? type, Wrapped<List<String>>? values}) {
    return MultiSelectAnswer(
        type: (type != null ? type.value : this.type),
        values: (values != null ? values.value : this.values));
  }
}

@JsonSerializable(explicitToJson: true)
class InputAnswer {
  const InputAnswer({
    required this.type,
    required this.data,
  });

  factory InputAnswer.fromJson(Map<String, dynamic> json) =>
      _$InputAnswerFromJson(json);

  static const toJsonFactory = _$InputAnswerToJson;
  Map<String, dynamic> toJson() => _$InputAnswerToJson(this);

  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(
      name: 'data', includeIfNull: false, defaultValue: <AnswerWithOption>[])
  final List<AnswerWithOption> data;
  static const fromJsonFactory = _$InputAnswerFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $InputAnswerExtension on InputAnswer {
  InputAnswer copyWith({String? type, List<AnswerWithOption>? data}) {
    return InputAnswer(type: type ?? this.type, data: data ?? this.data);
  }

  InputAnswer copyWithWrapped(
      {Wrapped<String>? type, Wrapped<List<AnswerWithOption>>? data}) {
    return InputAnswer(
        type: (type != null ? type.value : this.type),
        data: (data != null ? data.value : this.data));
  }
}

@JsonSerializable(explicitToJson: true)
class AnswerWithOption {
  const AnswerWithOption({
    required this.optionId,
    required this.$value,
  });

  factory AnswerWithOption.fromJson(Map<String, dynamic> json) =>
      _$AnswerWithOptionFromJson(json);

  static const toJsonFactory = _$AnswerWithOptionToJson;
  Map<String, dynamic> toJson() => _$AnswerWithOptionToJson(this);

  @JsonKey(name: 'optionId', includeIfNull: false)
  final String optionId;
  @JsonKey(name: 'value', includeIfNull: false)
  final String $value;
  static const fromJsonFactory = _$AnswerWithOptionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AnswerWithOptionExtension on AnswerWithOption {
  AnswerWithOption copyWith({String? optionId, String? $value}) {
    return AnswerWithOption(
        optionId: optionId ?? this.optionId, $value: $value ?? this.$value);
  }

  AnswerWithOption copyWithWrapped(
      {Wrapped<String>? optionId, Wrapped<String>? $value}) {
    return AnswerWithOption(
        optionId: (optionId != null ? optionId.value : this.optionId),
        $value: ($value != null ? $value.value : this.$value));
  }
}

@JsonSerializable(explicitToJson: true)
class AddressAnswer {
  const AddressAnswer({
    required this.type,
    required this.country,
    required this.city,
    required this.addressStreet,
    required this.apartmentNumber,
    this.buildingName,
  });

  factory AddressAnswer.fromJson(Map<String, dynamic> json) =>
      _$AddressAnswerFromJson(json);

  static const toJsonFactory = _$AddressAnswerToJson;
  Map<String, dynamic> toJson() => _$AddressAnswerToJson(this);

  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'country', includeIfNull: false)
  final String country;
  @JsonKey(name: 'city', includeIfNull: false)
  final String city;
  @JsonKey(name: 'addressStreet', includeIfNull: false)
  final String addressStreet;
  @JsonKey(name: 'apartmentNumber', includeIfNull: false)
  final String apartmentNumber;
  @JsonKey(name: 'buildingName', includeIfNull: false)
  final String? buildingName;
  static const fromJsonFactory = _$AddressAnswerFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AddressAnswerExtension on AddressAnswer {
  AddressAnswer copyWith(
      {String? type,
      String? country,
      String? city,
      String? addressStreet,
      String? apartmentNumber,
      String? buildingName}) {
    return AddressAnswer(
        type: type ?? this.type,
        country: country ?? this.country,
        city: city ?? this.city,
        addressStreet: addressStreet ?? this.addressStreet,
        apartmentNumber: apartmentNumber ?? this.apartmentNumber,
        buildingName: buildingName ?? this.buildingName);
  }

  AddressAnswer copyWithWrapped(
      {Wrapped<String>? type,
      Wrapped<String>? country,
      Wrapped<String>? city,
      Wrapped<String>? addressStreet,
      Wrapped<String>? apartmentNumber,
      Wrapped<String?>? buildingName}) {
    return AddressAnswer(
        type: (type != null ? type.value : this.type),
        country: (country != null ? country.value : this.country),
        city: (city != null ? city.value : this.city),
        addressStreet:
            (addressStreet != null ? addressStreet.value : this.addressStreet),
        apartmentNumber: (apartmentNumber != null
            ? apartmentNumber.value
            : this.apartmentNumber),
        buildingName:
            (buildingName != null ? buildingName.value : this.buildingName));
  }
}

@JsonSerializable(explicitToJson: true)
class TermsAndConditionsAnswer {
  const TermsAndConditionsAnswer({
    required this.type,
    required this.agree,
    required this.timestamp,
    this.acceptedDocuments,
  });

  factory TermsAndConditionsAnswer.fromJson(Map<String, dynamic> json) =>
      _$TermsAndConditionsAnswerFromJson(json);

  static const toJsonFactory = _$TermsAndConditionsAnswerToJson;
  Map<String, dynamic> toJson() => _$TermsAndConditionsAnswerToJson(this);

  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'agree', includeIfNull: false)
  final bool agree;
  @JsonKey(name: 'timestamp', includeIfNull: false)
  final DateTime timestamp;
  @JsonKey(
      name: 'acceptedDocuments',
      includeIfNull: false,
      defaultValue: <AcceptedDocument>[])
  final List<AcceptedDocument>? acceptedDocuments;
  static const fromJsonFactory = _$TermsAndConditionsAnswerFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $TermsAndConditionsAnswerExtension on TermsAndConditionsAnswer {
  TermsAndConditionsAnswer copyWith(
      {String? type,
      bool? agree,
      DateTime? timestamp,
      List<AcceptedDocument>? acceptedDocuments}) {
    return TermsAndConditionsAnswer(
        type: type ?? this.type,
        agree: agree ?? this.agree,
        timestamp: timestamp ?? this.timestamp,
        acceptedDocuments: acceptedDocuments ?? this.acceptedDocuments);
  }

  TermsAndConditionsAnswer copyWithWrapped(
      {Wrapped<String>? type,
      Wrapped<bool>? agree,
      Wrapped<DateTime>? timestamp,
      Wrapped<List<AcceptedDocument>?>? acceptedDocuments}) {
    return TermsAndConditionsAnswer(
        type: (type != null ? type.value : this.type),
        agree: (agree != null ? agree.value : this.agree),
        timestamp: (timestamp != null ? timestamp.value : this.timestamp),
        acceptedDocuments: (acceptedDocuments != null
            ? acceptedDocuments.value
            : this.acceptedDocuments));
  }
}

@JsonSerializable(explicitToJson: true)
class AcceptedDocument {
  const AcceptedDocument({
    required this.type,
    required this.version,
    required this.accepted,
  });

  factory AcceptedDocument.fromJson(Map<String, dynamic> json) =>
      _$AcceptedDocumentFromJson(json);

  static const toJsonFactory = _$AcceptedDocumentToJson;
  Map<String, dynamic> toJson() => _$AcceptedDocumentToJson(this);

  @JsonKey(
    name: 'type',
    includeIfNull: false,
    toJson: termsAndConditionsTypeToJson,
    fromJson: termsAndConditionsTypeFromJson,
  )
  final enums.TermsAndConditionsType type;
  @JsonKey(name: 'version', includeIfNull: false)
  final String version;
  @JsonKey(name: 'accepted', includeIfNull: false)
  final bool accepted;
  static const fromJsonFactory = _$AcceptedDocumentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AcceptedDocumentExtension on AcceptedDocument {
  AcceptedDocument copyWith(
      {enums.TermsAndConditionsType? type, String? version, bool? accepted}) {
    return AcceptedDocument(
        type: type ?? this.type,
        version: version ?? this.version,
        accepted: accepted ?? this.accepted);
  }

  AcceptedDocument copyWithWrapped(
      {Wrapped<enums.TermsAndConditionsType>? type,
      Wrapped<String>? version,
      Wrapped<bool>? accepted}) {
    return AcceptedDocument(
        type: (type != null ? type.value : this.type),
        version: (version != null ? version.value : this.version),
        accepted: (accepted != null ? accepted.value : this.accepted));
  }
}

@JsonSerializable(explicitToJson: true)
class MultiSectionsAnswer {
  const MultiSectionsAnswer({
    required this.type,
    required this.answers,
  });

  factory MultiSectionsAnswer.fromJson(Map<String, dynamic> json) =>
      _$MultiSectionsAnswerFromJson(json);

  static const toJsonFactory = _$MultiSectionsAnswerToJson;
  Map<String, dynamic> toJson() => _$MultiSectionsAnswerToJson(this);

  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(
      name: 'answers', includeIfNull: false, defaultValue: <SectionAnswer>[])
  final List<SectionAnswer> answers;
  static const fromJsonFactory = _$MultiSectionsAnswerFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MultiSectionsAnswerExtension on MultiSectionsAnswer {
  MultiSectionsAnswer copyWith({String? type, List<SectionAnswer>? answers}) {
    return MultiSectionsAnswer(
        type: type ?? this.type, answers: answers ?? this.answers);
  }

  MultiSectionsAnswer copyWithWrapped(
      {Wrapped<String>? type, Wrapped<List<SectionAnswer>>? answers}) {
    return MultiSectionsAnswer(
        type: (type != null ? type.value : this.type),
        answers: (answers != null ? answers.value : this.answers));
  }
}

@JsonSerializable(explicitToJson: true)
class UploadDocumentsAnswer {
  const UploadDocumentsAnswer({
    required this.categoryType,
    required this.documents,
    required this.type,
  });

  factory UploadDocumentsAnswer.fromJson(Map<String, dynamic> json) =>
      _$UploadDocumentsAnswerFromJson(json);

  static const toJsonFactory = _$UploadDocumentsAnswerToJson;
  Map<String, dynamic> toJson() => _$UploadDocumentsAnswerToJson(this);

  @JsonKey(name: 'categoryType', includeIfNull: false)
  final String categoryType;
  @JsonKey(
      name: 'documents',
      includeIfNull: false,
      defaultValue: <UploadedDocument>[])
  final List<UploadedDocument> documents;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  static const fromJsonFactory = _$UploadDocumentsAnswerFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UploadDocumentsAnswerExtension on UploadDocumentsAnswer {
  UploadDocumentsAnswer copyWith(
      {String? categoryType, List<UploadedDocument>? documents, String? type}) {
    return UploadDocumentsAnswer(
        categoryType: categoryType ?? this.categoryType,
        documents: documents ?? this.documents,
        type: type ?? this.type);
  }

  UploadDocumentsAnswer copyWithWrapped(
      {Wrapped<String>? categoryType,
      Wrapped<List<UploadedDocument>>? documents,
      Wrapped<String>? type}) {
    return UploadDocumentsAnswer(
        categoryType:
            (categoryType != null ? categoryType.value : this.categoryType),
        documents: (documents != null ? documents.value : this.documents),
        type: (type != null ? type.value : this.type));
  }
}

@JsonSerializable(explicitToJson: true)
class UploadedDocument {
  const UploadedDocument({
    required this.documentType,
    required this.documentId,
    required this.fileNames,
  });

  factory UploadedDocument.fromJson(Map<String, dynamic> json) =>
      _$UploadedDocumentFromJson(json);

  static const toJsonFactory = _$UploadedDocumentToJson;
  Map<String, dynamic> toJson() => _$UploadedDocumentToJson(this);

  @JsonKey(name: 'documentType', includeIfNull: false)
  final String documentType;
  @JsonKey(name: 'documentId', includeIfNull: false)
  final String documentId;
  @JsonKey(name: 'fileNames', includeIfNull: false, defaultValue: <String>[])
  final List<String> fileNames;
  static const fromJsonFactory = _$UploadedDocumentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UploadedDocumentExtension on UploadedDocument {
  UploadedDocument copyWith(
      {String? documentType, String? documentId, List<String>? fileNames}) {
    return UploadedDocument(
        documentType: documentType ?? this.documentType,
        documentId: documentId ?? this.documentId,
        fileNames: fileNames ?? this.fileNames);
  }

  UploadedDocument copyWithWrapped(
      {Wrapped<String>? documentType,
      Wrapped<String>? documentId,
      Wrapped<List<String>>? fileNames}) {
    return UploadedDocument(
        documentType:
            (documentType != null ? documentType.value : this.documentType),
        documentId: (documentId != null ? documentId.value : this.documentId),
        fileNames: (fileNames != null ? fileNames.value : this.fileNames));
  }
}

@JsonSerializable(explicitToJson: true)
class SectionAnswer {
  const SectionAnswer({
    required this.sectionId,
    required this.$value,
  });

  factory SectionAnswer.fromJson(Map<String, dynamic> json) =>
      _$SectionAnswerFromJson(json);

  static const toJsonFactory = _$SectionAnswerToJson;
  Map<String, dynamic> toJson() => _$SectionAnswerToJson(this);

  @JsonKey(name: 'sectionId', includeIfNull: false)
  final String sectionId;
  @JsonKey(name: 'value', includeIfNull: false)
  final Answer $value;
  static const fromJsonFactory = _$SectionAnswerFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $SectionAnswerExtension on SectionAnswer {
  SectionAnswer copyWith({String? sectionId, Answer? $value}) {
    return SectionAnswer(
        sectionId: sectionId ?? this.sectionId, $value: $value ?? this.$value);
  }

  SectionAnswer copyWithWrapped(
      {Wrapped<String>? sectionId, Wrapped<Answer>? $value}) {
    return SectionAnswer(
        sectionId: (sectionId != null ? sectionId.value : this.sectionId),
        $value: ($value != null ? $value.value : this.$value));
  }
}

typedef Options = List<Option>;

@JsonSerializable(explicitToJson: true)
class Option {
  const Option({
    required this.type,
  });

  factory Option.fromJson(Map<String, dynamic> json) => _$OptionFromJson(json);

  static const toJsonFactory = _$OptionToJson;
  Map<String, dynamic> toJson() => _$OptionToJson(this);

  @JsonKey(
    name: 'type',
    includeIfNull: false,
    toJson: optionTypeToJson,
    fromJson: optionTypeFromJson,
  )
  final enums.OptionType type;
  static const fromJsonFactory = _$OptionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $OptionExtension on Option {
  Option copyWith({enums.OptionType? type}) {
    return Option(type: type ?? this.type);
  }

  Option copyWithWrapped({Wrapped<enums.OptionType>? type}) {
    return Option(type: (type != null ? type.value : this.type));
  }
}

@JsonSerializable(explicitToJson: true)
class TextOption {
  const TextOption({
    required this.id,
    required this.type,
    required this.content,
  });

  factory TextOption.fromJson(Map<String, dynamic> json) =>
      _$TextOptionFromJson(json);

  static const toJsonFactory = _$TextOptionToJson;
  Map<String, dynamic> toJson() => _$TextOptionToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'content', includeIfNull: false)
  final TextOption$Content content;
  static const fromJsonFactory = _$TextOptionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $TextOptionExtension on TextOption {
  TextOption copyWith({String? id, String? type, TextOption$Content? content}) {
    return TextOption(
        id: id ?? this.id,
        type: type ?? this.type,
        content: content ?? this.content);
  }

  TextOption copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? type,
      Wrapped<TextOption$Content>? content}) {
    return TextOption(
        id: (id != null ? id.value : this.id),
        type: (type != null ? type.value : this.type),
        content: (content != null ? content.value : this.content));
  }
}

@JsonSerializable(explicitToJson: true)
class TextWithHeaderOption {
  const TextWithHeaderOption({
    required this.id,
    required this.type,
    required this.content,
  });

  factory TextWithHeaderOption.fromJson(Map<String, dynamic> json) =>
      _$TextWithHeaderOptionFromJson(json);

  static const toJsonFactory = _$TextWithHeaderOptionToJson;
  Map<String, dynamic> toJson() => _$TextWithHeaderOptionToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'content', includeIfNull: false)
  final TextWithHeaderOption$Content content;
  static const fromJsonFactory = _$TextWithHeaderOptionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $TextWithHeaderOptionExtension on TextWithHeaderOption {
  TextWithHeaderOption copyWith(
      {String? id, String? type, TextWithHeaderOption$Content? content}) {
    return TextWithHeaderOption(
        id: id ?? this.id,
        type: type ?? this.type,
        content: content ?? this.content);
  }

  TextWithHeaderOption copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? type,
      Wrapped<TextWithHeaderOption$Content>? content}) {
    return TextWithHeaderOption(
        id: (id != null ? id.value : this.id),
        type: (type != null ? type.value : this.type),
        content: (content != null ? content.value : this.content));
  }
}

@JsonSerializable(explicitToJson: true)
class EnterTextOption {
  const EnterTextOption({
    required this.id,
    required this.type,
    required this.content,
    required this.validations,
  });

  factory EnterTextOption.fromJson(Map<String, dynamic> json) =>
      _$EnterTextOptionFromJson(json);

  static const toJsonFactory = _$EnterTextOptionToJson;
  Map<String, dynamic> toJson() => _$EnterTextOptionToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'content', includeIfNull: false)
  final OptionContent content;
  @JsonKey(
      name: 'validations',
      includeIfNull: false,
      defaultValue: <EnterTextValidation>[])
  final List<EnterTextValidation> validations;
  static const fromJsonFactory = _$EnterTextOptionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EnterTextOptionExtension on EnterTextOption {
  EnterTextOption copyWith(
      {String? id,
      String? type,
      OptionContent? content,
      List<EnterTextValidation>? validations}) {
    return EnterTextOption(
        id: id ?? this.id,
        type: type ?? this.type,
        content: content ?? this.content,
        validations: validations ?? this.validations);
  }

  EnterTextOption copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? type,
      Wrapped<OptionContent>? content,
      Wrapped<List<EnterTextValidation>>? validations}) {
    return EnterTextOption(
        id: (id != null ? id.value : this.id),
        type: (type != null ? type.value : this.type),
        content: (content != null ? content.value : this.content),
        validations:
            (validations != null ? validations.value : this.validations));
  }
}

@JsonSerializable(explicitToJson: true)
class OptionContent {
  const OptionContent({
    required this.text,
    required this.hint,
    required this.additionalInfo,
  });

  factory OptionContent.fromJson(Map<String, dynamic> json) =>
      _$OptionContentFromJson(json);

  static const toJsonFactory = _$OptionContentToJson;
  Map<String, dynamic> toJson() => _$OptionContentToJson(this);

  @JsonKey(name: 'text', includeIfNull: false)
  final String text;
  @JsonKey(name: 'hint', includeIfNull: false)
  final String hint;
  @JsonKey(name: 'additionalInfo', includeIfNull: false)
  final String additionalInfo;
  static const fromJsonFactory = _$OptionContentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $OptionContentExtension on OptionContent {
  OptionContent copyWith({String? text, String? hint, String? additionalInfo}) {
    return OptionContent(
        text: text ?? this.text,
        hint: hint ?? this.hint,
        additionalInfo: additionalInfo ?? this.additionalInfo);
  }

  OptionContent copyWithWrapped(
      {Wrapped<String>? text,
      Wrapped<String>? hint,
      Wrapped<String>? additionalInfo}) {
    return OptionContent(
        text: (text != null ? text.value : this.text),
        hint: (hint != null ? hint.value : this.hint),
        additionalInfo: (additionalInfo != null
            ? additionalInfo.value
            : this.additionalInfo));
  }
}

@JsonSerializable(explicitToJson: true)
class EnterTextValidation {
  const EnterTextValidation({
    required this.type,
    this.errorText,
  });

  factory EnterTextValidation.fromJson(Map<String, dynamic> json) =>
      _$EnterTextValidationFromJson(json);

  static const toJsonFactory = _$EnterTextValidationToJson;
  Map<String, dynamic> toJson() => _$EnterTextValidationToJson(this);

  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'errorText', includeIfNull: false)
  final String? errorText;
  static const fromJsonFactory = _$EnterTextValidationFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EnterTextValidationExtension on EnterTextValidation {
  EnterTextValidation copyWith({String? type, String? errorText}) {
    return EnterTextValidation(
        type: type ?? this.type, errorText: errorText ?? this.errorText);
  }

  EnterTextValidation copyWithWrapped(
      {Wrapped<String>? type, Wrapped<String?>? errorText}) {
    return EnterTextValidation(
        type: (type != null ? type.value : this.type),
        errorText: (errorText != null ? errorText.value : this.errorText));
  }
}

@JsonSerializable(explicitToJson: true)
class MinLengthValidation {
  const MinLengthValidation({
    required this.$value,
    required this.type,
  });

  factory MinLengthValidation.fromJson(Map<String, dynamic> json) =>
      _$MinLengthValidationFromJson(json);

  static const toJsonFactory = _$MinLengthValidationToJson;
  Map<String, dynamic> toJson() => _$MinLengthValidationToJson(this);

  @JsonKey(name: 'value', includeIfNull: false)
  final int $value;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  static const fromJsonFactory = _$MinLengthValidationFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MinLengthValidationExtension on MinLengthValidation {
  MinLengthValidation copyWith({int? $value, String? type}) {
    return MinLengthValidation(
        $value: $value ?? this.$value, type: type ?? this.type);
  }

  MinLengthValidation copyWithWrapped(
      {Wrapped<int>? $value, Wrapped<String>? type}) {
    return MinLengthValidation(
        $value: ($value != null ? $value.value : this.$value),
        type: (type != null ? type.value : this.type));
  }
}

@JsonSerializable(explicitToJson: true)
class MaxLengthValidation {
  const MaxLengthValidation({
    required this.$value,
    required this.type,
  });

  factory MaxLengthValidation.fromJson(Map<String, dynamic> json) =>
      _$MaxLengthValidationFromJson(json);

  static const toJsonFactory = _$MaxLengthValidationToJson;
  Map<String, dynamic> toJson() => _$MaxLengthValidationToJson(this);

  @JsonKey(name: 'value', includeIfNull: false)
  final int $value;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  static const fromJsonFactory = _$MaxLengthValidationFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MaxLengthValidationExtension on MaxLengthValidation {
  MaxLengthValidation copyWith({int? $value, String? type}) {
    return MaxLengthValidation(
        $value: $value ?? this.$value, type: type ?? this.type);
  }

  MaxLengthValidation copyWithWrapped(
      {Wrapped<int>? $value, Wrapped<String>? type}) {
    return MaxLengthValidation(
        $value: ($value != null ? $value.value : this.$value),
        type: (type != null ? type.value : this.type));
  }
}

@JsonSerializable(explicitToJson: true)
class InputTypeValidation {
  const InputTypeValidation({
    required this.inputType,
    required this.type,
  });

  factory InputTypeValidation.fromJson(Map<String, dynamic> json) =>
      _$InputTypeValidationFromJson(json);

  static const toJsonFactory = _$InputTypeValidationToJson;
  Map<String, dynamic> toJson() => _$InputTypeValidationToJson(this);

  @JsonKey(
    name: 'inputType',
    includeIfNull: false,
    toJson: inputTypeToJson,
    fromJson: inputTypeFromJson,
  )
  final enums.InputType inputType;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  static const fromJsonFactory = _$InputTypeValidationFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $InputTypeValidationExtension on InputTypeValidation {
  InputTypeValidation copyWith({enums.InputType? inputType, String? type}) {
    return InputTypeValidation(
        inputType: inputType ?? this.inputType, type: type ?? this.type);
  }

  InputTypeValidation copyWithWrapped(
      {Wrapped<enums.InputType>? inputType, Wrapped<String>? type}) {
    return InputTypeValidation(
        inputType: (inputType != null ? inputType.value : this.inputType),
        type: (type != null ? type.value : this.type));
  }
}

@JsonSerializable(explicitToJson: true)
class NotAllowedCharsValidation {
  const NotAllowedCharsValidation({
    required this.chars,
    required this.type,
  });

  factory NotAllowedCharsValidation.fromJson(Map<String, dynamic> json) =>
      _$NotAllowedCharsValidationFromJson(json);

  static const toJsonFactory = _$NotAllowedCharsValidationToJson;
  Map<String, dynamic> toJson() => _$NotAllowedCharsValidationToJson(this);

  @JsonKey(name: 'chars', includeIfNull: false, defaultValue: <String>[])
  final List<String> chars;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  static const fromJsonFactory = _$NotAllowedCharsValidationFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $NotAllowedCharsValidationExtension on NotAllowedCharsValidation {
  NotAllowedCharsValidation copyWith({List<String>? chars, String? type}) {
    return NotAllowedCharsValidation(
        chars: chars ?? this.chars, type: type ?? this.type);
  }

  NotAllowedCharsValidation copyWithWrapped(
      {Wrapped<List<String>>? chars, Wrapped<String>? type}) {
    return NotAllowedCharsValidation(
        chars: (chars != null ? chars.value : this.chars),
        type: (type != null ? type.value : this.type));
  }
}

@JsonSerializable(explicitToJson: true)
class EnterNumberValidation {
  const EnterNumberValidation({
    required this.type,
    required this.errorText,
  });

  factory EnterNumberValidation.fromJson(Map<String, dynamic> json) =>
      _$EnterNumberValidationFromJson(json);

  static const toJsonFactory = _$EnterNumberValidationToJson;
  Map<String, dynamic> toJson() => _$EnterNumberValidationToJson(this);

  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'errorText', includeIfNull: false)
  final String errorText;
  static const fromJsonFactory = _$EnterNumberValidationFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EnterNumberValidationExtension on EnterNumberValidation {
  EnterNumberValidation copyWith({String? type, String? errorText}) {
    return EnterNumberValidation(
        type: type ?? this.type, errorText: errorText ?? this.errorText);
  }

  EnterNumberValidation copyWithWrapped(
      {Wrapped<String>? type, Wrapped<String>? errorText}) {
    return EnterNumberValidation(
        type: (type != null ? type.value : this.type),
        errorText: (errorText != null ? errorText.value : this.errorText));
  }
}

@JsonSerializable(explicitToJson: true)
class MinValueValidation {
  const MinValueValidation({
    required this.$value,
    required this.type,
  });

  factory MinValueValidation.fromJson(Map<String, dynamic> json) =>
      _$MinValueValidationFromJson(json);

  static const toJsonFactory = _$MinValueValidationToJson;
  Map<String, dynamic> toJson() => _$MinValueValidationToJson(this);

  @JsonKey(name: 'value', includeIfNull: false)
  final double $value;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  static const fromJsonFactory = _$MinValueValidationFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MinValueValidationExtension on MinValueValidation {
  MinValueValidation copyWith({double? $value, String? type}) {
    return MinValueValidation(
        $value: $value ?? this.$value, type: type ?? this.type);
  }

  MinValueValidation copyWithWrapped(
      {Wrapped<double>? $value, Wrapped<String>? type}) {
    return MinValueValidation(
        $value: ($value != null ? $value.value : this.$value),
        type: (type != null ? type.value : this.type));
  }
}

@JsonSerializable(explicitToJson: true)
class MaxValueValidation {
  const MaxValueValidation({
    required this.$value,
    required this.type,
  });

  factory MaxValueValidation.fromJson(Map<String, dynamic> json) =>
      _$MaxValueValidationFromJson(json);

  static const toJsonFactory = _$MaxValueValidationToJson;
  Map<String, dynamic> toJson() => _$MaxValueValidationToJson(this);

  @JsonKey(name: 'value', includeIfNull: false)
  final double $value;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  static const fromJsonFactory = _$MaxValueValidationFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $MaxValueValidationExtension on MaxValueValidation {
  MaxValueValidation copyWith({double? $value, String? type}) {
    return MaxValueValidation(
        $value: $value ?? this.$value, type: type ?? this.type);
  }

  MaxValueValidation copyWithWrapped(
      {Wrapped<double>? $value, Wrapped<String>? type}) {
    return MaxValueValidation(
        $value: ($value != null ? $value.value : this.$value),
        type: (type != null ? type.value : this.type));
  }
}

@JsonSerializable(explicitToJson: true)
class TextWithPopupOption {
  const TextWithPopupOption({
    required this.id,
    required this.type,
    required this.content,
  });

  factory TextWithPopupOption.fromJson(Map<String, dynamic> json) =>
      _$TextWithPopupOptionFromJson(json);

  static const toJsonFactory = _$TextWithPopupOptionToJson;
  Map<String, dynamic> toJson() => _$TextWithPopupOptionToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'content', includeIfNull: false)
  final TextWithPopupOption$Content content;
  static const fromJsonFactory = _$TextWithPopupOptionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $TextWithPopupOptionExtension on TextWithPopupOption {
  TextWithPopupOption copyWith(
      {String? id, String? type, TextWithPopupOption$Content? content}) {
    return TextWithPopupOption(
        id: id ?? this.id,
        type: type ?? this.type,
        content: content ?? this.content);
  }

  TextWithPopupOption copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? type,
      Wrapped<TextWithPopupOption$Content>? content}) {
    return TextWithPopupOption(
        id: (id != null ? id.value : this.id),
        type: (type != null ? type.value : this.type),
        content: (content != null ? content.value : this.content));
  }
}

@JsonSerializable(explicitToJson: true)
class EnterNumberOption {
  const EnterNumberOption({
    required this.id,
    required this.type,
    required this.content,
    required this.validations,
  });

  factory EnterNumberOption.fromJson(Map<String, dynamic> json) =>
      _$EnterNumberOptionFromJson(json);

  static const toJsonFactory = _$EnterNumberOptionToJson;
  Map<String, dynamic> toJson() => _$EnterNumberOptionToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  @JsonKey(name: 'content', includeIfNull: false)
  final OptionContent content;
  @JsonKey(
      name: 'validations',
      includeIfNull: false,
      defaultValue: <EnterNumberValidation>[])
  final List<EnterNumberValidation> validations;
  static const fromJsonFactory = _$EnterNumberOptionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EnterNumberOptionExtension on EnterNumberOption {
  EnterNumberOption copyWith(
      {String? id,
      String? type,
      OptionContent? content,
      List<EnterNumberValidation>? validations}) {
    return EnterNumberOption(
        id: id ?? this.id,
        type: type ?? this.type,
        content: content ?? this.content,
        validations: validations ?? this.validations);
  }

  EnterNumberOption copyWithWrapped(
      {Wrapped<String>? id,
      Wrapped<String>? type,
      Wrapped<OptionContent>? content,
      Wrapped<List<EnterNumberValidation>>? validations}) {
    return EnterNumberOption(
        id: (id != null ? id.value : this.id),
        type: (type != null ? type.value : this.type),
        content: (content != null ? content.value : this.content),
        validations:
            (validations != null ? validations.value : this.validations));
  }
}

@JsonSerializable(explicitToJson: true)
class PopupOption {
  const PopupOption({
    required this.text,
    required this.headerText,
    required this.buttonText,
  });

  factory PopupOption.fromJson(Map<String, dynamic> json) =>
      _$PopupOptionFromJson(json);

  static const toJsonFactory = _$PopupOptionToJson;
  Map<String, dynamic> toJson() => _$PopupOptionToJson(this);

  @JsonKey(name: 'text', includeIfNull: false)
  final String text;
  @JsonKey(name: 'headerText', includeIfNull: false)
  final String headerText;
  @JsonKey(name: 'buttonText', includeIfNull: false)
  final String buttonText;
  static const fromJsonFactory = _$PopupOptionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $PopupOptionExtension on PopupOption {
  PopupOption copyWith({String? text, String? headerText, String? buttonText}) {
    return PopupOption(
        text: text ?? this.text,
        headerText: headerText ?? this.headerText,
        buttonText: buttonText ?? this.buttonText);
  }

  PopupOption copyWithWrapped(
      {Wrapped<String>? text,
      Wrapped<String>? headerText,
      Wrapped<String>? buttonText}) {
    return PopupOption(
        text: (text != null ? text.value : this.text),
        headerText: (headerText != null ? headerText.value : this.headerText),
        buttonText: (buttonText != null ? buttonText.value : this.buttonText));
  }
}

@JsonSerializable(explicitToJson: true)
class OnboardingNextQuestionRequest {
  const OnboardingNextQuestionRequest({
    required this.productId,
    this.feature,
    this.answer,
  });

  factory OnboardingNextQuestionRequest.fromJson(Map<String, dynamic> json) =>
      _$OnboardingNextQuestionRequestFromJson(json);

  static const toJsonFactory = _$OnboardingNextQuestionRequestToJson;
  Map<String, dynamic> toJson() => _$OnboardingNextQuestionRequestToJson(this);

  @JsonKey(name: 'productId', includeIfNull: false)
  final String productId;
  @JsonKey(
    name: 'feature',
    includeIfNull: false,
    toJson: brokerFeatureNullableToJson,
    fromJson: brokerFeatureNullableFromJson,
  )
  final enums.BrokerFeature? feature;
  @JsonKey(name: 'answer', includeIfNull: false)
  final OnboardingAnswer? answer;
  static const fromJsonFactory = _$OnboardingNextQuestionRequestFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $OnboardingNextQuestionRequestExtension
    on OnboardingNextQuestionRequest {
  OnboardingNextQuestionRequest copyWith(
      {String? productId,
      enums.BrokerFeature? feature,
      OnboardingAnswer? answer}) {
    return OnboardingNextQuestionRequest(
        productId: productId ?? this.productId,
        feature: feature ?? this.feature,
        answer: answer ?? this.answer);
  }

  OnboardingNextQuestionRequest copyWithWrapped(
      {Wrapped<String>? productId,
      Wrapped<enums.BrokerFeature?>? feature,
      Wrapped<OnboardingAnswer?>? answer}) {
    return OnboardingNextQuestionRequest(
        productId: (productId != null ? productId.value : this.productId),
        feature: (feature != null ? feature.value : this.feature),
        answer: (answer != null ? answer.value : this.answer));
  }
}

@JsonSerializable(explicitToJson: true)
class OnboardingAnswer {
  const OnboardingAnswer({
    required this.questionId,
    required this.answer,
  });

  factory OnboardingAnswer.fromJson(Map<String, dynamic> json) =>
      _$OnboardingAnswerFromJson(json);

  static const toJsonFactory = _$OnboardingAnswerToJson;
  Map<String, dynamic> toJson() => _$OnboardingAnswerToJson(this);

  @JsonKey(name: 'questionId', includeIfNull: false)
  final String questionId;
  @JsonKey(name: 'answer', includeIfNull: false)
  final Answer answer;
  static const fromJsonFactory = _$OnboardingAnswerFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $OnboardingAnswerExtension on OnboardingAnswer {
  OnboardingAnswer copyWith({String? questionId, Answer? answer}) {
    return OnboardingAnswer(
        questionId: questionId ?? this.questionId,
        answer: answer ?? this.answer);
  }

  OnboardingAnswer copyWithWrapped(
      {Wrapped<String>? questionId, Wrapped<Answer>? answer}) {
    return OnboardingAnswer(
        questionId: (questionId != null ? questionId.value : this.questionId),
        answer: (answer != null ? answer.value : this.answer));
  }
}

@JsonSerializable(explicitToJson: true)
class Answer {
  const Answer({
    required this.type,
  });

  factory Answer.fromJson(Map<String, dynamic> json) => _$AnswerFromJson(json);

  static const toJsonFactory = _$AnswerToJson;
  Map<String, dynamic> toJson() => _$AnswerToJson(this);

  @JsonKey(name: 'type', includeIfNull: false)
  final String type;
  static const fromJsonFactory = _$AnswerFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AnswerExtension on Answer {
  Answer copyWith({String? type}) {
    return Answer(type: type ?? this.type);
  }

  Answer copyWithWrapped({Wrapped<String>? type}) {
    return Answer(type: (type != null ? type.value : this.type));
  }
}

@JsonSerializable(explicitToJson: true)
class OnboardingPreviousQuestionRequest {
  const OnboardingPreviousQuestionRequest({
    required this.productId,
    required this.questionId,
    this.feature,
  });

  factory OnboardingPreviousQuestionRequest.fromJson(
          Map<String, dynamic> json) =>
      _$OnboardingPreviousQuestionRequestFromJson(json);

  static const toJsonFactory = _$OnboardingPreviousQuestionRequestToJson;
  Map<String, dynamic> toJson() =>
      _$OnboardingPreviousQuestionRequestToJson(this);

  @JsonKey(name: 'productId', includeIfNull: false)
  final String productId;
  @JsonKey(name: 'questionId', includeIfNull: false)
  final String questionId;
  @JsonKey(
    name: 'feature',
    includeIfNull: false,
    toJson: brokerFeatureNullableToJson,
    fromJson: brokerFeatureNullableFromJson,
  )
  final enums.BrokerFeature? feature;
  static const fromJsonFactory = _$OnboardingPreviousQuestionRequestFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $OnboardingPreviousQuestionRequestExtension
    on OnboardingPreviousQuestionRequest {
  OnboardingPreviousQuestionRequest copyWith(
      {String? productId, String? questionId, enums.BrokerFeature? feature}) {
    return OnboardingPreviousQuestionRequest(
        productId: productId ?? this.productId,
        questionId: questionId ?? this.questionId,
        feature: feature ?? this.feature);
  }

  OnboardingPreviousQuestionRequest copyWithWrapped(
      {Wrapped<String>? productId,
      Wrapped<String>? questionId,
      Wrapped<enums.BrokerFeature?>? feature}) {
    return OnboardingPreviousQuestionRequest(
        productId: (productId != null ? productId.value : this.productId),
        questionId: (questionId != null ? questionId.value : this.questionId),
        feature: (feature != null ? feature.value : this.feature));
  }
}

@JsonSerializable(explicitToJson: true)
class UserResponse {
  const UserResponse({
    this.addressInfo,
    this.employmentInfo,
    this.investmentObjectives,
    this.investmentExperience,
    required this.kycStatus,
    this.politicallyExposedNames,
    this.partnerAccountNumber,
  });

  factory UserResponse.fromJson(Map<String, dynamic> json) =>
      _$UserResponseFromJson(json);

  static const toJsonFactory = _$UserResponseToJson;
  Map<String, dynamic> toJson() => _$UserResponseToJson(this);

  @JsonKey(name: 'addressInfo', includeIfNull: false)
  final AddressInfo? addressInfo;
  @JsonKey(name: 'employmentInfo', includeIfNull: false)
  final EmploymentInfo? employmentInfo;
  @JsonKey(
    name: 'investmentObjectives',
    includeIfNull: false,
    toJson: userResponseInvestmentObjectivesNullableToJson,
    fromJson: userResponseInvestmentObjectivesNullableFromJson,
  )
  final enums.UserResponseInvestmentObjectives? investmentObjectives;
  @JsonKey(
    name: 'investmentExperience',
    includeIfNull: false,
    toJson: userResponseInvestmentExperienceNullableToJson,
    fromJson: userResponseInvestmentExperienceNullableFromJson,
  )
  final enums.UserResponseInvestmentExperience? investmentExperience;
  @JsonKey(
    name: 'kycStatus',
    includeIfNull: false,
    toJson: userResponseKycStatusToJson,
    fromJson: userResponseKycStatusFromJson,
  )
  final enums.UserResponseKycStatus kycStatus;
  @JsonKey(name: 'politicallyExposedNames', includeIfNull: false)
  final String? politicallyExposedNames;
  @JsonKey(name: 'partnerAccountNumber', includeIfNull: false)
  final String? partnerAccountNumber;
  static const fromJsonFactory = _$UserResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UserResponseExtension on UserResponse {
  UserResponse copyWith(
      {AddressInfo? addressInfo,
      EmploymentInfo? employmentInfo,
      enums.UserResponseInvestmentObjectives? investmentObjectives,
      enums.UserResponseInvestmentExperience? investmentExperience,
      enums.UserResponseKycStatus? kycStatus,
      String? politicallyExposedNames,
      String? partnerAccountNumber}) {
    return UserResponse(
        addressInfo: addressInfo ?? this.addressInfo,
        employmentInfo: employmentInfo ?? this.employmentInfo,
        investmentObjectives: investmentObjectives ?? this.investmentObjectives,
        investmentExperience: investmentExperience ?? this.investmentExperience,
        kycStatus: kycStatus ?? this.kycStatus,
        politicallyExposedNames:
            politicallyExposedNames ?? this.politicallyExposedNames,
        partnerAccountNumber:
            partnerAccountNumber ?? this.partnerAccountNumber);
  }

  UserResponse copyWithWrapped(
      {Wrapped<AddressInfo?>? addressInfo,
      Wrapped<EmploymentInfo?>? employmentInfo,
      Wrapped<enums.UserResponseInvestmentObjectives?>? investmentObjectives,
      Wrapped<enums.UserResponseInvestmentExperience?>? investmentExperience,
      Wrapped<enums.UserResponseKycStatus>? kycStatus,
      Wrapped<String?>? politicallyExposedNames,
      Wrapped<String?>? partnerAccountNumber}) {
    return UserResponse(
        addressInfo:
            (addressInfo != null ? addressInfo.value : this.addressInfo),
        employmentInfo: (employmentInfo != null
            ? employmentInfo.value
            : this.employmentInfo),
        investmentObjectives: (investmentObjectives != null
            ? investmentObjectives.value
            : this.investmentObjectives),
        investmentExperience: (investmentExperience != null
            ? investmentExperience.value
            : this.investmentExperience),
        kycStatus: (kycStatus != null ? kycStatus.value : this.kycStatus),
        politicallyExposedNames: (politicallyExposedNames != null
            ? politicallyExposedNames.value
            : this.politicallyExposedNames),
        partnerAccountNumber: (partnerAccountNumber != null
            ? partnerAccountNumber.value
            : this.partnerAccountNumber));
  }
}

@JsonSerializable(explicitToJson: true)
class AddressInfo {
  const AddressInfo({
    required this.addressStreet,
    this.apartmentNumber,
    required this.province,
    required this.postalCode,
    required this.country,
  });

  factory AddressInfo.fromJson(Map<String, dynamic> json) =>
      _$AddressInfoFromJson(json);

  static const toJsonFactory = _$AddressInfoToJson;
  Map<String, dynamic> toJson() => _$AddressInfoToJson(this);

  @JsonKey(name: 'addressStreet', includeIfNull: false)
  final String addressStreet;
  @JsonKey(name: 'apartmentNumber', includeIfNull: false)
  final String? apartmentNumber;
  @JsonKey(name: 'province', includeIfNull: false)
  final String province;
  @JsonKey(name: 'postalCode', includeIfNull: false)
  final String postalCode;
  @JsonKey(name: 'country', includeIfNull: false)
  final String country;
  static const fromJsonFactory = _$AddressInfoFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AddressInfoExtension on AddressInfo {
  AddressInfo copyWith(
      {String? addressStreet,
      String? apartmentNumber,
      String? province,
      String? postalCode,
      String? country}) {
    return AddressInfo(
        addressStreet: addressStreet ?? this.addressStreet,
        apartmentNumber: apartmentNumber ?? this.apartmentNumber,
        province: province ?? this.province,
        postalCode: postalCode ?? this.postalCode,
        country: country ?? this.country);
  }

  AddressInfo copyWithWrapped(
      {Wrapped<String>? addressStreet,
      Wrapped<String?>? apartmentNumber,
      Wrapped<String>? province,
      Wrapped<String>? postalCode,
      Wrapped<String>? country}) {
    return AddressInfo(
        addressStreet:
            (addressStreet != null ? addressStreet.value : this.addressStreet),
        apartmentNumber: (apartmentNumber != null
            ? apartmentNumber.value
            : this.apartmentNumber),
        province: (province != null ? province.value : this.province),
        postalCode: (postalCode != null ? postalCode.value : this.postalCode),
        country: (country != null ? country.value : this.country));
  }
}

@JsonSerializable(explicitToJson: true)
class EmploymentInfo {
  const EmploymentInfo({
    required this.education,
    this.employmentType,
    this.previousPosition,
    this.currentPosition,
    required this.worksAsBroker,
    this.directorOf,
  });

  factory EmploymentInfo.fromJson(Map<String, dynamic> json) =>
      _$EmploymentInfoFromJson(json);

  static const toJsonFactory = _$EmploymentInfoToJson;
  Map<String, dynamic> toJson() => _$EmploymentInfoToJson(this);

  @JsonKey(
    name: 'education',
    includeIfNull: false,
    toJson: employmentInfoEducationToJson,
    fromJson: employmentInfoEducationFromJson,
  )
  final enums.EmploymentInfoEducation education;
  @JsonKey(
    name: 'employmentType',
    includeIfNull: false,
    toJson: employmentInfoEmploymentTypeNullableToJson,
    fromJson: employmentInfoEmploymentTypeNullableFromJson,
  )
  final enums.EmploymentInfoEmploymentType? employmentType;
  @JsonKey(
    name: 'previousPosition',
    includeIfNull: false,
    toJson: employmentPositionNullableToJson,
    fromJson: employmentPositionNullableFromJson,
  )
  final enums.EmploymentPosition? previousPosition;
  @JsonKey(
    name: 'currentPosition',
    includeIfNull: false,
    toJson: employmentPositionNullableToJson,
    fromJson: employmentPositionNullableFromJson,
  )
  final enums.EmploymentPosition? currentPosition;
  @JsonKey(name: 'worksAsBroker', includeIfNull: false)
  final bool worksAsBroker;
  @JsonKey(name: 'directorOf', includeIfNull: false)
  final String? directorOf;
  static const fromJsonFactory = _$EmploymentInfoFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $EmploymentInfoExtension on EmploymentInfo {
  EmploymentInfo copyWith(
      {enums.EmploymentInfoEducation? education,
      enums.EmploymentInfoEmploymentType? employmentType,
      enums.EmploymentPosition? previousPosition,
      enums.EmploymentPosition? currentPosition,
      bool? worksAsBroker,
      String? directorOf}) {
    return EmploymentInfo(
        education: education ?? this.education,
        employmentType: employmentType ?? this.employmentType,
        previousPosition: previousPosition ?? this.previousPosition,
        currentPosition: currentPosition ?? this.currentPosition,
        worksAsBroker: worksAsBroker ?? this.worksAsBroker,
        directorOf: directorOf ?? this.directorOf);
  }

  EmploymentInfo copyWithWrapped(
      {Wrapped<enums.EmploymentInfoEducation>? education,
      Wrapped<enums.EmploymentInfoEmploymentType?>? employmentType,
      Wrapped<enums.EmploymentPosition?>? previousPosition,
      Wrapped<enums.EmploymentPosition?>? currentPosition,
      Wrapped<bool>? worksAsBroker,
      Wrapped<String?>? directorOf}) {
    return EmploymentInfo(
        education: (education != null ? education.value : this.education),
        employmentType: (employmentType != null
            ? employmentType.value
            : this.employmentType),
        previousPosition: (previousPosition != null
            ? previousPosition.value
            : this.previousPosition),
        currentPosition: (currentPosition != null
            ? currentPosition.value
            : this.currentPosition),
        worksAsBroker:
            (worksAsBroker != null ? worksAsBroker.value : this.worksAsBroker),
        directorOf: (directorOf != null ? directorOf.value : this.directorOf));
  }
}

@JsonSerializable(explicitToJson: true)
class OnboardingInformationResponse {
  const OnboardingInformationResponse({
    required this.data,
  });

  factory OnboardingInformationResponse.fromJson(Map<String, dynamic> json) =>
      _$OnboardingInformationResponseFromJson(json);

  static const toJsonFactory = _$OnboardingInformationResponseToJson;
  Map<String, dynamic> toJson() => _$OnboardingInformationResponseToJson(this);

  @JsonKey(name: 'data', includeIfNull: false)
  final OnboardingInformation data;
  static const fromJsonFactory = _$OnboardingInformationResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $OnboardingInformationResponseExtension
    on OnboardingInformationResponse {
  OnboardingInformationResponse copyWith({OnboardingInformation? data}) {
    return OnboardingInformationResponse(data: data ?? this.data);
  }

  OnboardingInformationResponse copyWithWrapped(
      {Wrapped<OnboardingInformation>? data}) {
    return OnboardingInformationResponse(
        data: (data != null ? data.value : this.data));
  }
}

@JsonSerializable(explicitToJson: true)
class OnboardingInformation {
  const OnboardingInformation({
    required this.productId,
    required this.brokerFeature,
    required this.customerId,
    required this.answeredQuestions,
    required this.onboardedAt,
  });

  factory OnboardingInformation.fromJson(Map<String, dynamic> json) =>
      _$OnboardingInformationFromJson(json);

  static const toJsonFactory = _$OnboardingInformationToJson;
  Map<String, dynamic> toJson() => _$OnboardingInformationToJson(this);

  @JsonKey(name: 'productId', includeIfNull: false)
  final String productId;
  @JsonKey(
    name: 'brokerFeature',
    includeIfNull: false,
    toJson: brokerFeatureToJson,
    fromJson: brokerFeatureFromJson,
  )
  final enums.BrokerFeature brokerFeature;
  @JsonKey(name: 'customerId', includeIfNull: false)
  final String customerId;
  @JsonKey(
      name: 'answeredQuestions',
      includeIfNull: false,
      defaultValue: <AnsweredQuestion>[])
  final List<AnsweredQuestion> answeredQuestions;
  @JsonKey(name: 'onboardedAt', includeIfNull: false)
  final DateTime onboardedAt;
  static const fromJsonFactory = _$OnboardingInformationFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $OnboardingInformationExtension on OnboardingInformation {
  OnboardingInformation copyWith(
      {String? productId,
      enums.BrokerFeature? brokerFeature,
      String? customerId,
      List<AnsweredQuestion>? answeredQuestions,
      DateTime? onboardedAt}) {
    return OnboardingInformation(
        productId: productId ?? this.productId,
        brokerFeature: brokerFeature ?? this.brokerFeature,
        customerId: customerId ?? this.customerId,
        answeredQuestions: answeredQuestions ?? this.answeredQuestions,
        onboardedAt: onboardedAt ?? this.onboardedAt);
  }

  OnboardingInformation copyWithWrapped(
      {Wrapped<String>? productId,
      Wrapped<enums.BrokerFeature>? brokerFeature,
      Wrapped<String>? customerId,
      Wrapped<List<AnsweredQuestion>>? answeredQuestions,
      Wrapped<DateTime>? onboardedAt}) {
    return OnboardingInformation(
        productId: (productId != null ? productId.value : this.productId),
        brokerFeature:
            (brokerFeature != null ? brokerFeature.value : this.brokerFeature),
        customerId: (customerId != null ? customerId.value : this.customerId),
        answeredQuestions: (answeredQuestions != null
            ? answeredQuestions.value
            : this.answeredQuestions),
        onboardedAt:
            (onboardedAt != null ? onboardedAt.value : this.onboardedAt));
  }
}

@JsonSerializable(explicitToJson: true)
class AnsweredQuestion {
  const AnsweredQuestion({
    required this.questionId,
    required this.answer,
  });

  factory AnsweredQuestion.fromJson(Map<String, dynamic> json) =>
      _$AnsweredQuestionFromJson(json);

  static const toJsonFactory = _$AnsweredQuestionToJson;
  Map<String, dynamic> toJson() => _$AnsweredQuestionToJson(this);

  @JsonKey(name: 'questionId', includeIfNull: false)
  final String questionId;
  @JsonKey(name: 'answer', includeIfNull: false)
  final String answer;
  static const fromJsonFactory = _$AnsweredQuestionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $AnsweredQuestionExtension on AnsweredQuestion {
  AnsweredQuestion copyWith({String? questionId, String? answer}) {
    return AnsweredQuestion(
        questionId: questionId ?? this.questionId,
        answer: answer ?? this.answer);
  }

  AnsweredQuestion copyWithWrapped(
      {Wrapped<String>? questionId, Wrapped<String>? answer}) {
    return AnsweredQuestion(
        questionId: (questionId != null ? questionId.value : this.questionId),
        answer: (answer != null ? answer.value : this.answer));
  }
}

@JsonSerializable(explicitToJson: true)
class UploadDocumentsRequest {
  const UploadDocumentsRequest({
    required this.productId,
    required this.feature,
    required this.categoryType,
    required this.documentType,
    required this.files,
  });

  factory UploadDocumentsRequest.fromJson(Map<String, dynamic> json) =>
      _$UploadDocumentsRequestFromJson(json);

  static const toJsonFactory = _$UploadDocumentsRequestToJson;
  Map<String, dynamic> toJson() => _$UploadDocumentsRequestToJson(this);

  @JsonKey(name: 'productId', includeIfNull: false)
  final String productId;
  @JsonKey(
    name: 'feature',
    includeIfNull: false,
    toJson: brokerFeatureToJson,
    fromJson: brokerFeatureFromJson,
  )
  final enums.BrokerFeature feature;
  @JsonKey(name: 'categoryType', includeIfNull: false)
  final String categoryType;
  @JsonKey(name: 'documentType', includeIfNull: false)
  final String documentType;
  @JsonKey(name: 'files', includeIfNull: false, defaultValue: <String>[])
  final List<String> files;
  static const fromJsonFactory = _$UploadDocumentsRequestFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UploadDocumentsRequestExtension on UploadDocumentsRequest {
  UploadDocumentsRequest copyWith(
      {String? productId,
      enums.BrokerFeature? feature,
      String? categoryType,
      String? documentType,
      List<String>? files}) {
    return UploadDocumentsRequest(
        productId: productId ?? this.productId,
        feature: feature ?? this.feature,
        categoryType: categoryType ?? this.categoryType,
        documentType: documentType ?? this.documentType,
        files: files ?? this.files);
  }

  UploadDocumentsRequest copyWithWrapped(
      {Wrapped<String>? productId,
      Wrapped<enums.BrokerFeature>? feature,
      Wrapped<String>? categoryType,
      Wrapped<String>? documentType,
      Wrapped<List<String>>? files}) {
    return UploadDocumentsRequest(
        productId: (productId != null ? productId.value : this.productId),
        feature: (feature != null ? feature.value : this.feature),
        categoryType:
            (categoryType != null ? categoryType.value : this.categoryType),
        documentType:
            (documentType != null ? documentType.value : this.documentType),
        files: (files != null ? files.value : this.files));
  }
}

@JsonSerializable(explicitToJson: true)
class FileData {
  const FileData({
    required this.fileId,
    required this.contentType,
    required this.data,
  });

  factory FileData.fromJson(Map<String, dynamic> json) =>
      _$FileDataFromJson(json);

  static const toJsonFactory = _$FileDataToJson;
  Map<String, dynamic> toJson() => _$FileDataToJson(this);

  @JsonKey(name: 'fileId', includeIfNull: false)
  final String fileId;
  @JsonKey(name: 'contentType', includeIfNull: false)
  final String contentType;
  @JsonKey(name: 'data', includeIfNull: false)
  final String data;
  static const fromJsonFactory = _$FileDataFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $FileDataExtension on FileData {
  FileData copyWith({String? fileId, String? contentType, String? data}) {
    return FileData(
        fileId: fileId ?? this.fileId,
        contentType: contentType ?? this.contentType,
        data: data ?? this.data);
  }

  FileData copyWithWrapped(
      {Wrapped<String>? fileId,
      Wrapped<String>? contentType,
      Wrapped<String>? data}) {
    return FileData(
        fileId: (fileId != null ? fileId.value : this.fileId),
        contentType:
            (contentType != null ? contentType.value : this.contentType),
        data: (data != null ? data.value : this.data));
  }
}

@JsonSerializable(explicitToJson: true)
class UploadDocumentsResponse {
  const UploadDocumentsResponse({
    required this.data,
  });

  factory UploadDocumentsResponse.fromJson(Map<String, dynamic> json) =>
      _$UploadDocumentsResponseFromJson(json);

  static const toJsonFactory = _$UploadDocumentsResponseToJson;
  Map<String, dynamic> toJson() => _$UploadDocumentsResponseToJson(this);

  @JsonKey(name: 'data', includeIfNull: false)
  final UploadedDocument data;
  static const fromJsonFactory = _$UploadDocumentsResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $UploadDocumentsResponseExtension on UploadDocumentsResponse {
  UploadDocumentsResponse copyWith({UploadedDocument? data}) {
    return UploadDocumentsResponse(data: data ?? this.data);
  }

  UploadDocumentsResponse copyWithWrapped({Wrapped<UploadedDocument>? data}) {
    return UploadDocumentsResponse(
        data: (data != null ? data.value : this.data));
  }
}

@JsonSerializable(explicitToJson: true)
class DocumentResponse {
  const DocumentResponse({
    required this.data,
  });

  factory DocumentResponse.fromJson(Map<String, dynamic> json) =>
      _$DocumentResponseFromJson(json);

  static const toJsonFactory = _$DocumentResponseToJson;
  Map<String, dynamic> toJson() => _$DocumentResponseToJson(this);

  @JsonKey(name: 'data', includeIfNull: false)
  final KycAssetDocument data;
  static const fromJsonFactory = _$DocumentResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $DocumentResponseExtension on DocumentResponse {
  DocumentResponse copyWith({KycAssetDocument? data}) {
    return DocumentResponse(data: data ?? this.data);
  }

  DocumentResponse copyWithWrapped({Wrapped<KycAssetDocument>? data}) {
    return DocumentResponse(data: (data != null ? data.value : this.data));
  }
}

@JsonSerializable(explicitToJson: true)
class KycAssetDocument {
  const KycAssetDocument({
    required this.customerId,
    required this.documentId,
    required this.documentType,
    required this.filesData,
  });

  factory KycAssetDocument.fromJson(Map<String, dynamic> json) =>
      _$KycAssetDocumentFromJson(json);

  static const toJsonFactory = _$KycAssetDocumentToJson;
  Map<String, dynamic> toJson() => _$KycAssetDocumentToJson(this);

  @JsonKey(name: 'customerId', includeIfNull: false)
  final String customerId;
  @JsonKey(name: 'documentId', includeIfNull: false)
  final String documentId;
  @JsonKey(name: 'documentType', includeIfNull: false)
  final String documentType;
  @JsonKey(name: 'filesData', includeIfNull: false, defaultValue: <FileData>[])
  final List<FileData> filesData;
  static const fromJsonFactory = _$KycAssetDocumentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $KycAssetDocumentExtension on KycAssetDocument {
  KycAssetDocument copyWith(
      {String? customerId,
      String? documentId,
      String? documentType,
      List<FileData>? filesData}) {
    return KycAssetDocument(
        customerId: customerId ?? this.customerId,
        documentId: documentId ?? this.documentId,
        documentType: documentType ?? this.documentType,
        filesData: filesData ?? this.filesData);
  }

  KycAssetDocument copyWithWrapped(
      {Wrapped<String>? customerId,
      Wrapped<String>? documentId,
      Wrapped<String>? documentType,
      Wrapped<List<FileData>>? filesData}) {
    return KycAssetDocument(
        customerId: (customerId != null ? customerId.value : this.customerId),
        documentId: (documentId != null ? documentId.value : this.documentId),
        documentType:
            (documentType != null ? documentType.value : this.documentType),
        filesData: (filesData != null ? filesData.value : this.filesData));
  }
}

@JsonSerializable(explicitToJson: true)
class CustomerSupportCustomerInformationResponse {
  const CustomerSupportCustomerInformationResponse({
    required this.data,
  });

  factory CustomerSupportCustomerInformationResponse.fromJson(
          Map<String, dynamic> json) =>
      _$CustomerSupportCustomerInformationResponseFromJson(json);

  static const toJsonFactory =
      _$CustomerSupportCustomerInformationResponseToJson;
  Map<String, dynamic> toJson() =>
      _$CustomerSupportCustomerInformationResponseToJson(this);

  @JsonKey(name: 'data', includeIfNull: false)
  final CustomerInformation data;
  static const fromJsonFactory =
      _$CustomerSupportCustomerInformationResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CustomerSupportCustomerInformationResponseExtension
    on CustomerSupportCustomerInformationResponse {
  CustomerSupportCustomerInformationResponse copyWith(
      {CustomerInformation? data}) {
    return CustomerSupportCustomerInformationResponse(data: data ?? this.data);
  }

  CustomerSupportCustomerInformationResponse copyWithWrapped(
      {Wrapped<CustomerInformation>? data}) {
    return CustomerSupportCustomerInformationResponse(
        data: (data != null ? data.value : this.data));
  }
}

@JsonSerializable(explicitToJson: true)
class CustomerInformation {
  const CustomerInformation({
    this.onboardingFeatureStatuses,
    this.customerInformation,
    this.features,
  });

  factory CustomerInformation.fromJson(Map<String, dynamic> json) =>
      _$CustomerInformationFromJson(json);

  static const toJsonFactory = _$CustomerInformationToJson;
  Map<String, dynamic> toJson() => _$CustomerInformationToJson(this);

  @JsonKey(
      name: 'onboardingFeatureStatuses',
      includeIfNull: false,
      defaultValue: <FeatureStatus>[])
  final List<FeatureStatus>? onboardingFeatureStatuses;
  @JsonKey(name: 'customerInformation', includeIfNull: false)
  final CustomerInformation$CustomerInformation? customerInformation;
  @JsonKey(name: 'features', includeIfNull: false)
  final CustomerInformation$Features? features;
  static const fromJsonFactory = _$CustomerInformationFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CustomerInformationExtension on CustomerInformation {
  CustomerInformation copyWith(
      {List<FeatureStatus>? onboardingFeatureStatuses,
      CustomerInformation$CustomerInformation? customerInformation,
      CustomerInformation$Features? features}) {
    return CustomerInformation(
        onboardingFeatureStatuses:
            onboardingFeatureStatuses ?? this.onboardingFeatureStatuses,
        customerInformation: customerInformation ?? this.customerInformation,
        features: features ?? this.features);
  }

  CustomerInformation copyWithWrapped(
      {Wrapped<List<FeatureStatus>?>? onboardingFeatureStatuses,
      Wrapped<CustomerInformation$CustomerInformation?>? customerInformation,
      Wrapped<CustomerInformation$Features?>? features}) {
    return CustomerInformation(
        onboardingFeatureStatuses: (onboardingFeatureStatuses != null
            ? onboardingFeatureStatuses.value
            : this.onboardingFeatureStatuses),
        customerInformation: (customerInformation != null
            ? customerInformation.value
            : this.customerInformation),
        features: (features != null ? features.value : this.features));
  }
}

@JsonSerializable(explicitToJson: true)
class Nin {
  const Nin({
    required this.exchange,
    required this.number,
  });

  factory Nin.fromJson(Map<String, dynamic> json) => _$NinFromJson(json);

  static const toJsonFactory = _$NinToJson;
  Map<String, dynamic> toJson() => _$NinToJson(this);

  @JsonKey(
    name: 'exchange',
    includeIfNull: false,
    toJson: ninExchangeToJson,
    fromJson: ninExchangeFromJson,
  )
  final enums.NinExchange exchange;
  @JsonKey(name: 'number', includeIfNull: false)
  final String number;
  static const fromJsonFactory = _$NinFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $NinExtension on Nin {
  Nin copyWith({enums.NinExchange? exchange, String? number}) {
    return Nin(
        exchange: exchange ?? this.exchange, number: number ?? this.number);
  }

  Nin copyWithWrapped(
      {Wrapped<enums.NinExchange>? exchange, Wrapped<String>? number}) {
    return Nin(
        exchange: (exchange != null ? exchange.value : this.exchange),
        number: (number != null ? number.value : this.number));
  }
}

@JsonSerializable(explicitToJson: true)
class FeatureStatus {
  const FeatureStatus({
    required this.productName,
    required this.partnerAccountNumber,
    required this.status,
  });

  factory FeatureStatus.fromJson(Map<String, dynamic> json) =>
      _$FeatureStatusFromJson(json);

  static const toJsonFactory = _$FeatureStatusToJson;
  Map<String, dynamic> toJson() => _$FeatureStatusToJson(this);

  @JsonKey(
    name: 'productName',
    includeIfNull: false,
    toJson: featureStatusProductNameToJson,
    fromJson: featureStatusProductNameFromJson,
  )
  final enums.FeatureStatusProductName productName;
  @JsonKey(name: 'partnerAccountNumber', includeIfNull: false)
  final String partnerAccountNumber;
  @JsonKey(
    name: 'status',
    includeIfNull: false,
    toJson: featureStatusStatusToJson,
    fromJson: featureStatusStatusFromJson,
  )
  final enums.FeatureStatusStatus status;
  static const fromJsonFactory = _$FeatureStatusFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $FeatureStatusExtension on FeatureStatus {
  FeatureStatus copyWith(
      {enums.FeatureStatusProductName? productName,
      String? partnerAccountNumber,
      enums.FeatureStatusStatus? status}) {
    return FeatureStatus(
        productName: productName ?? this.productName,
        partnerAccountNumber: partnerAccountNumber ?? this.partnerAccountNumber,
        status: status ?? this.status);
  }

  FeatureStatus copyWithWrapped(
      {Wrapped<enums.FeatureStatusProductName>? productName,
      Wrapped<String>? partnerAccountNumber,
      Wrapped<enums.FeatureStatusStatus>? status}) {
    return FeatureStatus(
        productName:
            (productName != null ? productName.value : this.productName),
        partnerAccountNumber: (partnerAccountNumber != null
            ? partnerAccountNumber.value
            : this.partnerAccountNumber),
        status: (status != null ? status.value : this.status));
  }
}

@JsonSerializable(explicitToJson: true)
class CustomerSupportPortfolioInformationResponse {
  const CustomerSupportPortfolioInformationResponse({
    required this.data,
  });

  factory CustomerSupportPortfolioInformationResponse.fromJson(
          Map<String, dynamic> json) =>
      _$CustomerSupportPortfolioInformationResponseFromJson(json);

  static const toJsonFactory =
      _$CustomerSupportPortfolioInformationResponseToJson;
  Map<String, dynamic> toJson() =>
      _$CustomerSupportPortfolioInformationResponseToJson(this);

  @JsonKey(name: 'data', includeIfNull: false)
  final CustomerSupportPortfolioInformationResponse$Data data;
  static const fromJsonFactory =
      _$CustomerSupportPortfolioInformationResponseFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CustomerSupportPortfolioInformationResponseExtension
    on CustomerSupportPortfolioInformationResponse {
  CustomerSupportPortfolioInformationResponse copyWith(
      {CustomerSupportPortfolioInformationResponse$Data? data}) {
    return CustomerSupportPortfolioInformationResponse(data: data ?? this.data);
  }

  CustomerSupportPortfolioInformationResponse copyWithWrapped(
      {Wrapped<CustomerSupportPortfolioInformationResponse$Data>? data}) {
    return CustomerSupportPortfolioInformationResponse(
        data: (data != null ? data.value : this.data));
  }
}

@JsonSerializable(explicitToJson: true)
class PortfolioPosition {
  const PortfolioPosition({
    required this.symbol,
    required this.logo,
    required this.averagePrice,
    required this.costBasis,
    required this.quantity,
    required this.marketValue,
    required this.allTimeAbsoluteGainLoss,
    required this.allTimeRelativeGainLoss,
  });

  factory PortfolioPosition.fromJson(Map<String, dynamic> json) =>
      _$PortfolioPositionFromJson(json);

  static const toJsonFactory = _$PortfolioPositionToJson;
  Map<String, dynamic> toJson() => _$PortfolioPositionToJson(this);

  @JsonKey(name: 'symbol', includeIfNull: false)
  final String symbol;
  @JsonKey(name: 'logo', includeIfNull: false)
  final String logo;
  @JsonKey(name: 'averagePrice', includeIfNull: false)
  final List<NumberValue> averagePrice;
  @JsonKey(name: 'costBasis', includeIfNull: false)
  final List<NumberValue> costBasis;
  @JsonKey(name: 'quantity', includeIfNull: false)
  final NumberValue quantity;
  @JsonKey(name: 'marketValue', includeIfNull: false)
  final List<NumberValue> marketValue;
  @JsonKey(
      name: 'allTimeAbsoluteGainLoss',
      includeIfNull: false,
      defaultValue: <NumberValue>[])
  final List<NumberValue> allTimeAbsoluteGainLoss;
  @JsonKey(
      name: 'allTimeRelativeGainLoss',
      includeIfNull: false,
      defaultValue: <NumberValue>[])
  final List<NumberValue> allTimeRelativeGainLoss;
  static const fromJsonFactory = _$PortfolioPositionFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $PortfolioPositionExtension on PortfolioPosition {
  PortfolioPosition copyWith(
      {String? symbol,
      String? logo,
      List<NumberValue>? averagePrice,
      List<NumberValue>? costBasis,
      NumberValue? quantity,
      List<NumberValue>? marketValue,
      List<NumberValue>? allTimeAbsoluteGainLoss,
      List<NumberValue>? allTimeRelativeGainLoss}) {
    return PortfolioPosition(
        symbol: symbol ?? this.symbol,
        logo: logo ?? this.logo,
        averagePrice: averagePrice ?? this.averagePrice,
        costBasis: costBasis ?? this.costBasis,
        quantity: quantity ?? this.quantity,
        marketValue: marketValue ?? this.marketValue,
        allTimeAbsoluteGainLoss:
            allTimeAbsoluteGainLoss ?? this.allTimeAbsoluteGainLoss,
        allTimeRelativeGainLoss:
            allTimeRelativeGainLoss ?? this.allTimeRelativeGainLoss);
  }

  PortfolioPosition copyWithWrapped(
      {Wrapped<String>? symbol,
      Wrapped<String>? logo,
      Wrapped<List<NumberValue>>? averagePrice,
      Wrapped<List<NumberValue>>? costBasis,
      Wrapped<NumberValue>? quantity,
      Wrapped<List<NumberValue>>? marketValue,
      Wrapped<List<NumberValue>>? allTimeAbsoluteGainLoss,
      Wrapped<List<NumberValue>>? allTimeRelativeGainLoss}) {
    return PortfolioPosition(
        symbol: (symbol != null ? symbol.value : this.symbol),
        logo: (logo != null ? logo.value : this.logo),
        averagePrice:
            (averagePrice != null ? averagePrice.value : this.averagePrice),
        costBasis: (costBasis != null ? costBasis.value : this.costBasis),
        quantity: (quantity != null ? quantity.value : this.quantity),
        marketValue:
            (marketValue != null ? marketValue.value : this.marketValue),
        allTimeAbsoluteGainLoss: (allTimeAbsoluteGainLoss != null
            ? allTimeAbsoluteGainLoss.value
            : this.allTimeAbsoluteGainLoss),
        allTimeRelativeGainLoss: (allTimeRelativeGainLoss != null
            ? allTimeRelativeGainLoss.value
            : this.allTimeRelativeGainLoss));
  }
}

typedef MoneyArray = List<NumberValue>;

@JsonSerializable(explicitToJson: true)
class NumberValue {
  const NumberValue({
    required this.type,
    this.currency,
    required this.$value,
    this.sign,
  });

  factory NumberValue.fromJson(Map<String, dynamic> json) =>
      _$NumberValueFromJson(json);

  static const toJsonFactory = _$NumberValueToJson;
  Map<String, dynamic> toJson() => _$NumberValueToJson(this);

  @JsonKey(
    name: 'type',
    includeIfNull: false,
    toJson: numberValueTypeToJson,
    fromJson: numberValueTypeFromJson,
  )
  final enums.NumberValueType type;
  @JsonKey(name: 'currency', includeIfNull: false)
  final String? currency;
  @JsonKey(name: 'value', includeIfNull: false)
  final String $value;
  @JsonKey(
    name: 'sign',
    includeIfNull: false,
    toJson: numberValueSignNullableToJson,
    fromJson: numberValueSignNullableFromJson,
  )
  final enums.NumberValueSign? sign;
  static const fromJsonFactory = _$NumberValueFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $NumberValueExtension on NumberValue {
  NumberValue copyWith(
      {enums.NumberValueType? type,
      String? currency,
      String? $value,
      enums.NumberValueSign? sign}) {
    return NumberValue(
        type: type ?? this.type,
        currency: currency ?? this.currency,
        $value: $value ?? this.$value,
        sign: sign ?? this.sign);
  }

  NumberValue copyWithWrapped(
      {Wrapped<enums.NumberValueType>? type,
      Wrapped<String?>? currency,
      Wrapped<String>? $value,
      Wrapped<enums.NumberValueSign?>? sign}) {
    return NumberValue(
        type: (type != null ? type.value : this.type),
        currency: (currency != null ? currency.value : this.currency),
        $value: ($value != null ? $value.value : this.$value),
        sign: (sign != null ? sign.value : this.sign));
  }
}

@JsonSerializable(explicitToJson: true)
class TextOption$Content {
  const TextOption$Content({
    required this.text,
  });

  factory TextOption$Content.fromJson(Map<String, dynamic> json) =>
      _$TextOption$ContentFromJson(json);

  static const toJsonFactory = _$TextOption$ContentToJson;
  Map<String, dynamic> toJson() => _$TextOption$ContentToJson(this);

  @JsonKey(name: 'text', includeIfNull: false)
  final String text;
  static const fromJsonFactory = _$TextOption$ContentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $TextOption$ContentExtension on TextOption$Content {
  TextOption$Content copyWith({String? text}) {
    return TextOption$Content(text: text ?? this.text);
  }

  TextOption$Content copyWithWrapped({Wrapped<String>? text}) {
    return TextOption$Content(text: (text != null ? text.value : this.text));
  }
}

@JsonSerializable(explicitToJson: true)
class TextWithHeaderOption$Content {
  const TextWithHeaderOption$Content({
    required this.text,
    required this.headerText,
  });

  factory TextWithHeaderOption$Content.fromJson(Map<String, dynamic> json) =>
      _$TextWithHeaderOption$ContentFromJson(json);

  static const toJsonFactory = _$TextWithHeaderOption$ContentToJson;
  Map<String, dynamic> toJson() => _$TextWithHeaderOption$ContentToJson(this);

  @JsonKey(name: 'text', includeIfNull: false)
  final String text;
  @JsonKey(name: 'headerText', includeIfNull: false)
  final String headerText;
  static const fromJsonFactory = _$TextWithHeaderOption$ContentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $TextWithHeaderOption$ContentExtension
    on TextWithHeaderOption$Content {
  TextWithHeaderOption$Content copyWith({String? text, String? headerText}) {
    return TextWithHeaderOption$Content(
        text: text ?? this.text, headerText: headerText ?? this.headerText);
  }

  TextWithHeaderOption$Content copyWithWrapped(
      {Wrapped<String>? text, Wrapped<String>? headerText}) {
    return TextWithHeaderOption$Content(
        text: (text != null ? text.value : this.text),
        headerText: (headerText != null ? headerText.value : this.headerText));
  }
}

@JsonSerializable(explicitToJson: true)
class TextWithPopupOption$Content {
  const TextWithPopupOption$Content({
    required this.text,
    required this.popup,
  });

  factory TextWithPopupOption$Content.fromJson(Map<String, dynamic> json) =>
      _$TextWithPopupOption$ContentFromJson(json);

  static const toJsonFactory = _$TextWithPopupOption$ContentToJson;
  Map<String, dynamic> toJson() => _$TextWithPopupOption$ContentToJson(this);

  @JsonKey(name: 'text', includeIfNull: false)
  final String text;
  @JsonKey(name: 'popup', includeIfNull: false)
  final PopupOption popup;
  static const fromJsonFactory = _$TextWithPopupOption$ContentFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $TextWithPopupOption$ContentExtension on TextWithPopupOption$Content {
  TextWithPopupOption$Content copyWith({String? text, PopupOption? popup}) {
    return TextWithPopupOption$Content(
        text: text ?? this.text, popup: popup ?? this.popup);
  }

  TextWithPopupOption$Content copyWithWrapped(
      {Wrapped<String>? text, Wrapped<PopupOption>? popup}) {
    return TextWithPopupOption$Content(
        text: (text != null ? text.value : this.text),
        popup: (popup != null ? popup.value : this.popup));
  }
}

@JsonSerializable(explicitToJson: true)
class CustomerInformation$CustomerInformation {
  const CustomerInformation$CustomerInformation({
    required this.customerId,
    required this.email,
    required this.phoneNumber,
    required this.riskProfile,
    required this.screeningResult,
    required this.screeningCategories,
    required this.ninNumbers,
    required this.address,
  });

  factory CustomerInformation$CustomerInformation.fromJson(
          Map<String, dynamic> json) =>
      _$CustomerInformation$CustomerInformationFromJson(json);

  static const toJsonFactory = _$CustomerInformation$CustomerInformationToJson;
  Map<String, dynamic> toJson() =>
      _$CustomerInformation$CustomerInformationToJson(this);

  @JsonKey(name: 'customerId', includeIfNull: false)
  final String customerId;
  @JsonKey(name: 'email', includeIfNull: false)
  final String email;
  @JsonKey(name: 'phoneNumber', includeIfNull: false)
  final String phoneNumber;
  @JsonKey(
    name: 'riskProfile',
    includeIfNull: false,
    toJson: customerInformation$CustomerInformationRiskProfileToJson,
    fromJson: customerInformation$CustomerInformationRiskProfileFromJson,
  )
  final enums.CustomerInformation$CustomerInformationRiskProfile riskProfile;
  @JsonKey(
    name: 'screeningResult',
    includeIfNull: false,
    toJson: customerInformation$CustomerInformationScreeningResultToJson,
    fromJson: customerInformation$CustomerInformationScreeningResultFromJson,
  )
  final enums.CustomerInformation$CustomerInformationScreeningResult
      screeningResult;
  @JsonKey(
    name: 'screeningCategories',
    includeIfNull: false,
    toJson:
        customerInformation$CustomerInformationScreeningCategoriesListToJson,
    fromJson:
        customerInformation$CustomerInformationScreeningCategoriesListFromJson,
  )
  final List<enums.CustomerInformation$CustomerInformationScreeningCategories>
      screeningCategories;
  @JsonKey(name: 'ninNumbers', includeIfNull: false, defaultValue: <Nin>[])
  final List<Nin> ninNumbers;
  @JsonKey(name: 'address', includeIfNull: false)
  final AddressInfo address;
  static const fromJsonFactory =
      _$CustomerInformation$CustomerInformationFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CustomerInformation$CustomerInformationExtension
    on CustomerInformation$CustomerInformation {
  CustomerInformation$CustomerInformation copyWith(
      {String? customerId,
      String? email,
      String? phoneNumber,
      enums.CustomerInformation$CustomerInformationRiskProfile? riskProfile,
      enums.CustomerInformation$CustomerInformationScreeningResult?
          screeningResult,
      List<enums.CustomerInformation$CustomerInformationScreeningCategories>?
          screeningCategories,
      List<Nin>? ninNumbers,
      AddressInfo? address}) {
    return CustomerInformation$CustomerInformation(
        customerId: customerId ?? this.customerId,
        email: email ?? this.email,
        phoneNumber: phoneNumber ?? this.phoneNumber,
        riskProfile: riskProfile ?? this.riskProfile,
        screeningResult: screeningResult ?? this.screeningResult,
        screeningCategories: screeningCategories ?? this.screeningCategories,
        ninNumbers: ninNumbers ?? this.ninNumbers,
        address: address ?? this.address);
  }

  CustomerInformation$CustomerInformation copyWithWrapped(
      {Wrapped<String>? customerId,
      Wrapped<String>? email,
      Wrapped<String>? phoneNumber,
      Wrapped<enums.CustomerInformation$CustomerInformationRiskProfile>?
          riskProfile,
      Wrapped<enums.CustomerInformation$CustomerInformationScreeningResult>?
          screeningResult,
      Wrapped<
              List<
                  enums
                  .CustomerInformation$CustomerInformationScreeningCategories>>?
          screeningCategories,
      Wrapped<List<Nin>>? ninNumbers,
      Wrapped<AddressInfo>? address}) {
    return CustomerInformation$CustomerInformation(
        customerId: (customerId != null ? customerId.value : this.customerId),
        email: (email != null ? email.value : this.email),
        phoneNumber:
            (phoneNumber != null ? phoneNumber.value : this.phoneNumber),
        riskProfile:
            (riskProfile != null ? riskProfile.value : this.riskProfile),
        screeningResult: (screeningResult != null
            ? screeningResult.value
            : this.screeningResult),
        screeningCategories: (screeningCategories != null
            ? screeningCategories.value
            : this.screeningCategories),
        ninNumbers: (ninNumbers != null ? ninNumbers.value : this.ninNumbers),
        address: (address != null ? address.value : this.address));
  }
}

@JsonSerializable(explicitToJson: true)
class CustomerInformation$Features {
  const CustomerInformation$Features({
    required this.securitiesLending,
    required this.suitabilityAssessment,
    required this.suitabilityAssessmentAnswer,
  });

  factory CustomerInformation$Features.fromJson(Map<String, dynamic> json) =>
      _$CustomerInformation$FeaturesFromJson(json);

  static const toJsonFactory = _$CustomerInformation$FeaturesToJson;
  Map<String, dynamic> toJson() => _$CustomerInformation$FeaturesToJson(this);

  @JsonKey(
    name: 'securitiesLending',
    includeIfNull: false,
    toJson: customerInformation$FeaturesSecuritiesLendingToJson,
    fromJson: customerInformation$FeaturesSecuritiesLendingFromJson,
  )
  final enums.CustomerInformation$FeaturesSecuritiesLending securitiesLending;
  @JsonKey(
    name: 'suitabilityAssessment',
    includeIfNull: false,
    toJson: customerInformation$FeaturesSuitabilityAssessmentToJson,
    fromJson: customerInformation$FeaturesSuitabilityAssessmentFromJson,
  )
  final enums.CustomerInformation$FeaturesSuitabilityAssessment
      suitabilityAssessment;
  @JsonKey(
    name: 'suitabilityAssessmentAnswer',
    includeIfNull: false,
    toJson: customerInformation$FeaturesSuitabilityAssessmentAnswerToJson,
    fromJson: customerInformation$FeaturesSuitabilityAssessmentAnswerFromJson,
  )
  final enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer
      suitabilityAssessmentAnswer;
  static const fromJsonFactory = _$CustomerInformation$FeaturesFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CustomerInformation$FeaturesExtension
    on CustomerInformation$Features {
  CustomerInformation$Features copyWith(
      {enums.CustomerInformation$FeaturesSecuritiesLending? securitiesLending,
      enums.CustomerInformation$FeaturesSuitabilityAssessment?
          suitabilityAssessment,
      enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer?
          suitabilityAssessmentAnswer}) {
    return CustomerInformation$Features(
        securitiesLending: securitiesLending ?? this.securitiesLending,
        suitabilityAssessment:
            suitabilityAssessment ?? this.suitabilityAssessment,
        suitabilityAssessmentAnswer:
            suitabilityAssessmentAnswer ?? this.suitabilityAssessmentAnswer);
  }

  CustomerInformation$Features copyWithWrapped(
      {Wrapped<enums.CustomerInformation$FeaturesSecuritiesLending>?
          securitiesLending,
      Wrapped<enums.CustomerInformation$FeaturesSuitabilityAssessment>?
          suitabilityAssessment,
      Wrapped<enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer>?
          suitabilityAssessmentAnswer}) {
    return CustomerInformation$Features(
        securitiesLending: (securitiesLending != null
            ? securitiesLending.value
            : this.securitiesLending),
        suitabilityAssessment: (suitabilityAssessment != null
            ? suitabilityAssessment.value
            : this.suitabilityAssessment),
        suitabilityAssessmentAnswer: (suitabilityAssessmentAnswer != null
            ? suitabilityAssessmentAnswer.value
            : this.suitabilityAssessmentAnswer));
  }
}

@JsonSerializable(explicitToJson: true)
class CustomerSupportPortfolioInformationResponse$Data {
  const CustomerSupportPortfolioInformationResponse$Data({
    this.portfolioView,
  });

  factory CustomerSupportPortfolioInformationResponse$Data.fromJson(
          Map<String, dynamic> json) =>
      _$CustomerSupportPortfolioInformationResponse$DataFromJson(json);

  static const toJsonFactory =
      _$CustomerSupportPortfolioInformationResponse$DataToJson;
  Map<String, dynamic> toJson() =>
      _$CustomerSupportPortfolioInformationResponse$DataToJson(this);

  @JsonKey(name: 'portfolioView', includeIfNull: false)
  final List<
          CustomerSupportPortfolioInformationResponse$Data$PortfolioView$Item>?
      portfolioView;
  static const fromJsonFactory =
      _$CustomerSupportPortfolioInformationResponse$DataFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CustomerSupportPortfolioInformationResponse$DataExtension
    on CustomerSupportPortfolioInformationResponse$Data {
  CustomerSupportPortfolioInformationResponse$Data copyWith(
      {List<CustomerSupportPortfolioInformationResponse$Data$PortfolioView$Item>?
          portfolioView}) {
    return CustomerSupportPortfolioInformationResponse$Data(
        portfolioView: portfolioView ?? this.portfolioView);
  }

  CustomerSupportPortfolioInformationResponse$Data copyWithWrapped(
      {Wrapped<
              List<
                  CustomerSupportPortfolioInformationResponse$Data$PortfolioView$Item>?>?
          portfolioView}) {
    return CustomerSupportPortfolioInformationResponse$Data(
        portfolioView:
            (portfolioView != null ? portfolioView.value : this.portfolioView));
  }
}

@JsonSerializable(explicitToJson: true)
class CustomerSupportPortfolioInformationResponse$Data$PortfolioView$Item {
  const CustomerSupportPortfolioInformationResponse$Data$PortfolioView$Item({
    required this.portfolioId,
    required this.portfolioPositions,
  });

  factory CustomerSupportPortfolioInformationResponse$Data$PortfolioView$Item.fromJson(
          Map<String, dynamic> json) =>
      _$CustomerSupportPortfolioInformationResponse$Data$PortfolioView$ItemFromJson(
          json);

  static const toJsonFactory =
      _$CustomerSupportPortfolioInformationResponse$Data$PortfolioView$ItemToJson;
  Map<String, dynamic> toJson() =>
      _$CustomerSupportPortfolioInformationResponse$Data$PortfolioView$ItemToJson(
          this);

  @JsonKey(name: 'portfolioId', includeIfNull: false)
  final String portfolioId;
  @JsonKey(
      name: 'portfolioPositions',
      includeIfNull: false,
      defaultValue: <Object>[])
  final List<Object> portfolioPositions;
  static const fromJsonFactory =
      _$CustomerSupportPortfolioInformationResponse$Data$PortfolioView$ItemFromJson;

  @override
  String toString() => jsonEncode(this);
}

extension $CustomerSupportPortfolioInformationResponse$Data$PortfolioView$ItemExtension
    on CustomerSupportPortfolioInformationResponse$Data$PortfolioView$Item {
  CustomerSupportPortfolioInformationResponse$Data$PortfolioView$Item copyWith(
      {String? portfolioId, List<Object>? portfolioPositions}) {
    return CustomerSupportPortfolioInformationResponse$Data$PortfolioView$Item(
        portfolioId: portfolioId ?? this.portfolioId,
        portfolioPositions: portfolioPositions ?? this.portfolioPositions);
  }

  CustomerSupportPortfolioInformationResponse$Data$PortfolioView$Item
      copyWithWrapped(
          {Wrapped<String>? portfolioId,
          Wrapped<List<Object>>? portfolioPositions}) {
    return CustomerSupportPortfolioInformationResponse$Data$PortfolioView$Item(
        portfolioId:
            (portfolioId != null ? portfolioId.value : this.portfolioId),
        portfolioPositions: (portfolioPositions != null
            ? portfolioPositions.value
            : this.portfolioPositions));
  }
}

String? questionTypeNullableToJson(enums.QuestionType? questionType) {
  return questionType?.value;
}

String? questionTypeToJson(enums.QuestionType questionType) {
  return questionType.value;
}

enums.QuestionType questionTypeFromJson(
  Object? questionType, [
  enums.QuestionType? defaultValue,
]) {
  return enums.QuestionType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          questionType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.QuestionType.swaggerGeneratedUnknown;
}

enums.QuestionType? questionTypeNullableFromJson(
  Object? questionType, [
  enums.QuestionType? defaultValue,
]) {
  if (questionType == null) {
    return null;
  }
  return enums.QuestionType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          questionType.toString().toLowerCase()) ??
      defaultValue;
}

String questionTypeExplodedListToJson(List<enums.QuestionType>? questionType) {
  return questionType?.map((e) => e.value!).join(',') ?? '';
}

List<String> questionTypeListToJson(List<enums.QuestionType>? questionType) {
  if (questionType == null) {
    return [];
  }

  return questionType.map((e) => e.value!).toList();
}

List<enums.QuestionType> questionTypeListFromJson(
  List? questionType, [
  List<enums.QuestionType>? defaultValue,
]) {
  if (questionType == null) {
    return defaultValue ?? [];
  }

  return questionType.map((e) => questionTypeFromJson(e.toString())).toList();
}

List<enums.QuestionType>? questionTypeNullableListFromJson(
  List? questionType, [
  List<enums.QuestionType>? defaultValue,
]) {
  if (questionType == null) {
    return defaultValue;
  }

  return questionType.map((e) => questionTypeFromJson(e.toString())).toList();
}

String? sectionConditionTypeNullableToJson(
    enums.SectionConditionType? sectionConditionType) {
  return sectionConditionType?.value;
}

String? sectionConditionTypeToJson(
    enums.SectionConditionType sectionConditionType) {
  return sectionConditionType.value;
}

enums.SectionConditionType sectionConditionTypeFromJson(
  Object? sectionConditionType, [
  enums.SectionConditionType? defaultValue,
]) {
  return enums.SectionConditionType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          sectionConditionType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.SectionConditionType.swaggerGeneratedUnknown;
}

enums.SectionConditionType? sectionConditionTypeNullableFromJson(
  Object? sectionConditionType, [
  enums.SectionConditionType? defaultValue,
]) {
  if (sectionConditionType == null) {
    return null;
  }
  return enums.SectionConditionType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          sectionConditionType.toString().toLowerCase()) ??
      defaultValue;
}

String sectionConditionTypeExplodedListToJson(
    List<enums.SectionConditionType>? sectionConditionType) {
  return sectionConditionType?.map((e) => e.value!).join(',') ?? '';
}

List<String> sectionConditionTypeListToJson(
    List<enums.SectionConditionType>? sectionConditionType) {
  if (sectionConditionType == null) {
    return [];
  }

  return sectionConditionType.map((e) => e.value!).toList();
}

List<enums.SectionConditionType> sectionConditionTypeListFromJson(
  List? sectionConditionType, [
  List<enums.SectionConditionType>? defaultValue,
]) {
  if (sectionConditionType == null) {
    return defaultValue ?? [];
  }

  return sectionConditionType
      .map((e) => sectionConditionTypeFromJson(e.toString()))
      .toList();
}

List<enums.SectionConditionType>? sectionConditionTypeNullableListFromJson(
  List? sectionConditionType, [
  List<enums.SectionConditionType>? defaultValue,
]) {
  if (sectionConditionType == null) {
    return defaultValue;
  }

  return sectionConditionType
      .map((e) => sectionConditionTypeFromJson(e.toString()))
      .toList();
}

String? termsAndConditionsGroupNullableToJson(
    enums.TermsAndConditionsGroup? termsAndConditionsGroup) {
  return termsAndConditionsGroup?.value;
}

String? termsAndConditionsGroupToJson(
    enums.TermsAndConditionsGroup termsAndConditionsGroup) {
  return termsAndConditionsGroup.value;
}

enums.TermsAndConditionsGroup termsAndConditionsGroupFromJson(
  Object? termsAndConditionsGroup, [
  enums.TermsAndConditionsGroup? defaultValue,
]) {
  return enums.TermsAndConditionsGroup.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          termsAndConditionsGroup?.toString().toLowerCase()) ??
      defaultValue ??
      enums.TermsAndConditionsGroup.swaggerGeneratedUnknown;
}

enums.TermsAndConditionsGroup? termsAndConditionsGroupNullableFromJson(
  Object? termsAndConditionsGroup, [
  enums.TermsAndConditionsGroup? defaultValue,
]) {
  if (termsAndConditionsGroup == null) {
    return null;
  }
  return enums.TermsAndConditionsGroup.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          termsAndConditionsGroup.toString().toLowerCase()) ??
      defaultValue;
}

String termsAndConditionsGroupExplodedListToJson(
    List<enums.TermsAndConditionsGroup>? termsAndConditionsGroup) {
  return termsAndConditionsGroup?.map((e) => e.value!).join(',') ?? '';
}

List<String> termsAndConditionsGroupListToJson(
    List<enums.TermsAndConditionsGroup>? termsAndConditionsGroup) {
  if (termsAndConditionsGroup == null) {
    return [];
  }

  return termsAndConditionsGroup.map((e) => e.value!).toList();
}

List<enums.TermsAndConditionsGroup> termsAndConditionsGroupListFromJson(
  List? termsAndConditionsGroup, [
  List<enums.TermsAndConditionsGroup>? defaultValue,
]) {
  if (termsAndConditionsGroup == null) {
    return defaultValue ?? [];
  }

  return termsAndConditionsGroup
      .map((e) => termsAndConditionsGroupFromJson(e.toString()))
      .toList();
}

List<enums.TermsAndConditionsGroup>?
    termsAndConditionsGroupNullableListFromJson(
  List? termsAndConditionsGroup, [
  List<enums.TermsAndConditionsGroup>? defaultValue,
]) {
  if (termsAndConditionsGroup == null) {
    return defaultValue;
  }

  return termsAndConditionsGroup
      .map((e) => termsAndConditionsGroupFromJson(e.toString()))
      .toList();
}

String? statementIconNullableToJson(enums.StatementIcon? statementIcon) {
  return statementIcon?.value;
}

String? statementIconToJson(enums.StatementIcon statementIcon) {
  return statementIcon.value;
}

enums.StatementIcon statementIconFromJson(
  Object? statementIcon, [
  enums.StatementIcon? defaultValue,
]) {
  return enums.StatementIcon.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          statementIcon?.toString().toLowerCase()) ??
      defaultValue ??
      enums.StatementIcon.swaggerGeneratedUnknown;
}

enums.StatementIcon? statementIconNullableFromJson(
  Object? statementIcon, [
  enums.StatementIcon? defaultValue,
]) {
  if (statementIcon == null) {
    return null;
  }
  return enums.StatementIcon.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          statementIcon.toString().toLowerCase()) ??
      defaultValue;
}

String statementIconExplodedListToJson(
    List<enums.StatementIcon>? statementIcon) {
  return statementIcon?.map((e) => e.value!).join(',') ?? '';
}

List<String> statementIconListToJson(List<enums.StatementIcon>? statementIcon) {
  if (statementIcon == null) {
    return [];
  }

  return statementIcon.map((e) => e.value!).toList();
}

List<enums.StatementIcon> statementIconListFromJson(
  List? statementIcon, [
  List<enums.StatementIcon>? defaultValue,
]) {
  if (statementIcon == null) {
    return defaultValue ?? [];
  }

  return statementIcon.map((e) => statementIconFromJson(e.toString())).toList();
}

List<enums.StatementIcon>? statementIconNullableListFromJson(
  List? statementIcon, [
  List<enums.StatementIcon>? defaultValue,
]) {
  if (statementIcon == null) {
    return defaultValue;
  }

  return statementIcon.map((e) => statementIconFromJson(e.toString())).toList();
}

String? termsAndConditionsTypeNullableToJson(
    enums.TermsAndConditionsType? termsAndConditionsType) {
  return termsAndConditionsType?.value;
}

String? termsAndConditionsTypeToJson(
    enums.TermsAndConditionsType termsAndConditionsType) {
  return termsAndConditionsType.value;
}

enums.TermsAndConditionsType termsAndConditionsTypeFromJson(
  Object? termsAndConditionsType, [
  enums.TermsAndConditionsType? defaultValue,
]) {
  return enums.TermsAndConditionsType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          termsAndConditionsType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.TermsAndConditionsType.swaggerGeneratedUnknown;
}

enums.TermsAndConditionsType? termsAndConditionsTypeNullableFromJson(
  Object? termsAndConditionsType, [
  enums.TermsAndConditionsType? defaultValue,
]) {
  if (termsAndConditionsType == null) {
    return null;
  }
  return enums.TermsAndConditionsType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          termsAndConditionsType.toString().toLowerCase()) ??
      defaultValue;
}

String termsAndConditionsTypeExplodedListToJson(
    List<enums.TermsAndConditionsType>? termsAndConditionsType) {
  return termsAndConditionsType?.map((e) => e.value!).join(',') ?? '';
}

List<String> termsAndConditionsTypeListToJson(
    List<enums.TermsAndConditionsType>? termsAndConditionsType) {
  if (termsAndConditionsType == null) {
    return [];
  }

  return termsAndConditionsType.map((e) => e.value!).toList();
}

List<enums.TermsAndConditionsType> termsAndConditionsTypeListFromJson(
  List? termsAndConditionsType, [
  List<enums.TermsAndConditionsType>? defaultValue,
]) {
  if (termsAndConditionsType == null) {
    return defaultValue ?? [];
  }

  return termsAndConditionsType
      .map((e) => termsAndConditionsTypeFromJson(e.toString()))
      .toList();
}

List<enums.TermsAndConditionsType>? termsAndConditionsTypeNullableListFromJson(
  List? termsAndConditionsType, [
  List<enums.TermsAndConditionsType>? defaultValue,
]) {
  if (termsAndConditionsType == null) {
    return defaultValue;
  }

  return termsAndConditionsType
      .map((e) => termsAndConditionsTypeFromJson(e.toString()))
      .toList();
}

String? optionTypeNullableToJson(enums.OptionType? optionType) {
  return optionType?.value;
}

String? optionTypeToJson(enums.OptionType optionType) {
  return optionType.value;
}

enums.OptionType optionTypeFromJson(
  Object? optionType, [
  enums.OptionType? defaultValue,
]) {
  return enums.OptionType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          optionType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.OptionType.swaggerGeneratedUnknown;
}

enums.OptionType? optionTypeNullableFromJson(
  Object? optionType, [
  enums.OptionType? defaultValue,
]) {
  if (optionType == null) {
    return null;
  }
  return enums.OptionType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          optionType.toString().toLowerCase()) ??
      defaultValue;
}

String optionTypeExplodedListToJson(List<enums.OptionType>? optionType) {
  return optionType?.map((e) => e.value!).join(',') ?? '';
}

List<String> optionTypeListToJson(List<enums.OptionType>? optionType) {
  if (optionType == null) {
    return [];
  }

  return optionType.map((e) => e.value!).toList();
}

List<enums.OptionType> optionTypeListFromJson(
  List? optionType, [
  List<enums.OptionType>? defaultValue,
]) {
  if (optionType == null) {
    return defaultValue ?? [];
  }

  return optionType.map((e) => optionTypeFromJson(e.toString())).toList();
}

List<enums.OptionType>? optionTypeNullableListFromJson(
  List? optionType, [
  List<enums.OptionType>? defaultValue,
]) {
  if (optionType == null) {
    return defaultValue;
  }

  return optionType.map((e) => optionTypeFromJson(e.toString())).toList();
}

String? inputTypeNullableToJson(enums.InputType? inputType) {
  return inputType?.value;
}

String? inputTypeToJson(enums.InputType inputType) {
  return inputType.value;
}

enums.InputType inputTypeFromJson(
  Object? inputType, [
  enums.InputType? defaultValue,
]) {
  return enums.InputType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          inputType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.InputType.swaggerGeneratedUnknown;
}

enums.InputType? inputTypeNullableFromJson(
  Object? inputType, [
  enums.InputType? defaultValue,
]) {
  if (inputType == null) {
    return null;
  }
  return enums.InputType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          inputType.toString().toLowerCase()) ??
      defaultValue;
}

String inputTypeExplodedListToJson(List<enums.InputType>? inputType) {
  return inputType?.map((e) => e.value!).join(',') ?? '';
}

List<String> inputTypeListToJson(List<enums.InputType>? inputType) {
  if (inputType == null) {
    return [];
  }

  return inputType.map((e) => e.value!).toList();
}

List<enums.InputType> inputTypeListFromJson(
  List? inputType, [
  List<enums.InputType>? defaultValue,
]) {
  if (inputType == null) {
    return defaultValue ?? [];
  }

  return inputType.map((e) => inputTypeFromJson(e.toString())).toList();
}

List<enums.InputType>? inputTypeNullableListFromJson(
  List? inputType, [
  List<enums.InputType>? defaultValue,
]) {
  if (inputType == null) {
    return defaultValue;
  }

  return inputType.map((e) => inputTypeFromJson(e.toString())).toList();
}

String? validationTypeNullableToJson(enums.ValidationType? validationType) {
  return validationType?.value;
}

String? validationTypeToJson(enums.ValidationType validationType) {
  return validationType.value;
}

enums.ValidationType validationTypeFromJson(
  Object? validationType, [
  enums.ValidationType? defaultValue,
]) {
  return enums.ValidationType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          validationType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.ValidationType.swaggerGeneratedUnknown;
}

enums.ValidationType? validationTypeNullableFromJson(
  Object? validationType, [
  enums.ValidationType? defaultValue,
]) {
  if (validationType == null) {
    return null;
  }
  return enums.ValidationType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          validationType.toString().toLowerCase()) ??
      defaultValue;
}

String validationTypeExplodedListToJson(
    List<enums.ValidationType>? validationType) {
  return validationType?.map((e) => e.value!).join(',') ?? '';
}

List<String> validationTypeListToJson(
    List<enums.ValidationType>? validationType) {
  if (validationType == null) {
    return [];
  }

  return validationType.map((e) => e.value!).toList();
}

List<enums.ValidationType> validationTypeListFromJson(
  List? validationType, [
  List<enums.ValidationType>? defaultValue,
]) {
  if (validationType == null) {
    return defaultValue ?? [];
  }

  return validationType
      .map((e) => validationTypeFromJson(e.toString()))
      .toList();
}

List<enums.ValidationType>? validationTypeNullableListFromJson(
  List? validationType, [
  List<enums.ValidationType>? defaultValue,
]) {
  if (validationType == null) {
    return defaultValue;
  }

  return validationType
      .map((e) => validationTypeFromJson(e.toString()))
      .toList();
}

String? answerTypeNullableToJson(enums.AnswerType? answerType) {
  return answerType?.value;
}

String? answerTypeToJson(enums.AnswerType answerType) {
  return answerType.value;
}

enums.AnswerType answerTypeFromJson(
  Object? answerType, [
  enums.AnswerType? defaultValue,
]) {
  return enums.AnswerType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          answerType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.AnswerType.swaggerGeneratedUnknown;
}

enums.AnswerType? answerTypeNullableFromJson(
  Object? answerType, [
  enums.AnswerType? defaultValue,
]) {
  if (answerType == null) {
    return null;
  }
  return enums.AnswerType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          answerType.toString().toLowerCase()) ??
      defaultValue;
}

String answerTypeExplodedListToJson(List<enums.AnswerType>? answerType) {
  return answerType?.map((e) => e.value!).join(',') ?? '';
}

List<String> answerTypeListToJson(List<enums.AnswerType>? answerType) {
  if (answerType == null) {
    return [];
  }

  return answerType.map((e) => e.value!).toList();
}

List<enums.AnswerType> answerTypeListFromJson(
  List? answerType, [
  List<enums.AnswerType>? defaultValue,
]) {
  if (answerType == null) {
    return defaultValue ?? [];
  }

  return answerType.map((e) => answerTypeFromJson(e.toString())).toList();
}

List<enums.AnswerType>? answerTypeNullableListFromJson(
  List? answerType, [
  List<enums.AnswerType>? defaultValue,
]) {
  if (answerType == null) {
    return defaultValue;
  }

  return answerType.map((e) => answerTypeFromJson(e.toString())).toList();
}

String? userResponseInvestmentObjectivesNullableToJson(
    enums.UserResponseInvestmentObjectives? userResponseInvestmentObjectives) {
  return userResponseInvestmentObjectives?.value;
}

String? userResponseInvestmentObjectivesToJson(
    enums.UserResponseInvestmentObjectives userResponseInvestmentObjectives) {
  return userResponseInvestmentObjectives.value;
}

enums.UserResponseInvestmentObjectives userResponseInvestmentObjectivesFromJson(
  Object? userResponseInvestmentObjectives, [
  enums.UserResponseInvestmentObjectives? defaultValue,
]) {
  return enums.UserResponseInvestmentObjectives.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          userResponseInvestmentObjectives?.toString().toLowerCase()) ??
      defaultValue ??
      enums.UserResponseInvestmentObjectives.swaggerGeneratedUnknown;
}

enums.UserResponseInvestmentObjectives?
    userResponseInvestmentObjectivesNullableFromJson(
  Object? userResponseInvestmentObjectives, [
  enums.UserResponseInvestmentObjectives? defaultValue,
]) {
  if (userResponseInvestmentObjectives == null) {
    return null;
  }
  return enums.UserResponseInvestmentObjectives.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          userResponseInvestmentObjectives.toString().toLowerCase()) ??
      defaultValue;
}

String userResponseInvestmentObjectivesExplodedListToJson(
    List<enums.UserResponseInvestmentObjectives>?
        userResponseInvestmentObjectives) {
  return userResponseInvestmentObjectives?.map((e) => e.value!).join(',') ?? '';
}

List<String> userResponseInvestmentObjectivesListToJson(
    List<enums.UserResponseInvestmentObjectives>?
        userResponseInvestmentObjectives) {
  if (userResponseInvestmentObjectives == null) {
    return [];
  }

  return userResponseInvestmentObjectives.map((e) => e.value!).toList();
}

List<enums.UserResponseInvestmentObjectives>
    userResponseInvestmentObjectivesListFromJson(
  List? userResponseInvestmentObjectives, [
  List<enums.UserResponseInvestmentObjectives>? defaultValue,
]) {
  if (userResponseInvestmentObjectives == null) {
    return defaultValue ?? [];
  }

  return userResponseInvestmentObjectives
      .map((e) => userResponseInvestmentObjectivesFromJson(e.toString()))
      .toList();
}

List<enums.UserResponseInvestmentObjectives>?
    userResponseInvestmentObjectivesNullableListFromJson(
  List? userResponseInvestmentObjectives, [
  List<enums.UserResponseInvestmentObjectives>? defaultValue,
]) {
  if (userResponseInvestmentObjectives == null) {
    return defaultValue;
  }

  return userResponseInvestmentObjectives
      .map((e) => userResponseInvestmentObjectivesFromJson(e.toString()))
      .toList();
}

String? userResponseInvestmentExperienceNullableToJson(
    enums.UserResponseInvestmentExperience? userResponseInvestmentExperience) {
  return userResponseInvestmentExperience?.value;
}

String? userResponseInvestmentExperienceToJson(
    enums.UserResponseInvestmentExperience userResponseInvestmentExperience) {
  return userResponseInvestmentExperience.value;
}

enums.UserResponseInvestmentExperience userResponseInvestmentExperienceFromJson(
  Object? userResponseInvestmentExperience, [
  enums.UserResponseInvestmentExperience? defaultValue,
]) {
  return enums.UserResponseInvestmentExperience.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          userResponseInvestmentExperience?.toString().toLowerCase()) ??
      defaultValue ??
      enums.UserResponseInvestmentExperience.swaggerGeneratedUnknown;
}

enums.UserResponseInvestmentExperience?
    userResponseInvestmentExperienceNullableFromJson(
  Object? userResponseInvestmentExperience, [
  enums.UserResponseInvestmentExperience? defaultValue,
]) {
  if (userResponseInvestmentExperience == null) {
    return null;
  }
  return enums.UserResponseInvestmentExperience.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          userResponseInvestmentExperience.toString().toLowerCase()) ??
      defaultValue;
}

String userResponseInvestmentExperienceExplodedListToJson(
    List<enums.UserResponseInvestmentExperience>?
        userResponseInvestmentExperience) {
  return userResponseInvestmentExperience?.map((e) => e.value!).join(',') ?? '';
}

List<String> userResponseInvestmentExperienceListToJson(
    List<enums.UserResponseInvestmentExperience>?
        userResponseInvestmentExperience) {
  if (userResponseInvestmentExperience == null) {
    return [];
  }

  return userResponseInvestmentExperience.map((e) => e.value!).toList();
}

List<enums.UserResponseInvestmentExperience>
    userResponseInvestmentExperienceListFromJson(
  List? userResponseInvestmentExperience, [
  List<enums.UserResponseInvestmentExperience>? defaultValue,
]) {
  if (userResponseInvestmentExperience == null) {
    return defaultValue ?? [];
  }

  return userResponseInvestmentExperience
      .map((e) => userResponseInvestmentExperienceFromJson(e.toString()))
      .toList();
}

List<enums.UserResponseInvestmentExperience>?
    userResponseInvestmentExperienceNullableListFromJson(
  List? userResponseInvestmentExperience, [
  List<enums.UserResponseInvestmentExperience>? defaultValue,
]) {
  if (userResponseInvestmentExperience == null) {
    return defaultValue;
  }

  return userResponseInvestmentExperience
      .map((e) => userResponseInvestmentExperienceFromJson(e.toString()))
      .toList();
}

String? userResponseKycStatusNullableToJson(
    enums.UserResponseKycStatus? userResponseKycStatus) {
  return userResponseKycStatus?.value;
}

String? userResponseKycStatusToJson(
    enums.UserResponseKycStatus userResponseKycStatus) {
  return userResponseKycStatus.value;
}

enums.UserResponseKycStatus userResponseKycStatusFromJson(
  Object? userResponseKycStatus, [
  enums.UserResponseKycStatus? defaultValue,
]) {
  return enums.UserResponseKycStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          userResponseKycStatus?.toString().toLowerCase()) ??
      defaultValue ??
      enums.UserResponseKycStatus.swaggerGeneratedUnknown;
}

enums.UserResponseKycStatus? userResponseKycStatusNullableFromJson(
  Object? userResponseKycStatus, [
  enums.UserResponseKycStatus? defaultValue,
]) {
  if (userResponseKycStatus == null) {
    return null;
  }
  return enums.UserResponseKycStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          userResponseKycStatus.toString().toLowerCase()) ??
      defaultValue;
}

String userResponseKycStatusExplodedListToJson(
    List<enums.UserResponseKycStatus>? userResponseKycStatus) {
  return userResponseKycStatus?.map((e) => e.value!).join(',') ?? '';
}

List<String> userResponseKycStatusListToJson(
    List<enums.UserResponseKycStatus>? userResponseKycStatus) {
  if (userResponseKycStatus == null) {
    return [];
  }

  return userResponseKycStatus.map((e) => e.value!).toList();
}

List<enums.UserResponseKycStatus> userResponseKycStatusListFromJson(
  List? userResponseKycStatus, [
  List<enums.UserResponseKycStatus>? defaultValue,
]) {
  if (userResponseKycStatus == null) {
    return defaultValue ?? [];
  }

  return userResponseKycStatus
      .map((e) => userResponseKycStatusFromJson(e.toString()))
      .toList();
}

List<enums.UserResponseKycStatus>? userResponseKycStatusNullableListFromJson(
  List? userResponseKycStatus, [
  List<enums.UserResponseKycStatus>? defaultValue,
]) {
  if (userResponseKycStatus == null) {
    return defaultValue;
  }

  return userResponseKycStatus
      .map((e) => userResponseKycStatusFromJson(e.toString()))
      .toList();
}

String? employmentInfoEducationNullableToJson(
    enums.EmploymentInfoEducation? employmentInfoEducation) {
  return employmentInfoEducation?.value;
}

String? employmentInfoEducationToJson(
    enums.EmploymentInfoEducation employmentInfoEducation) {
  return employmentInfoEducation.value;
}

enums.EmploymentInfoEducation employmentInfoEducationFromJson(
  Object? employmentInfoEducation, [
  enums.EmploymentInfoEducation? defaultValue,
]) {
  return enums.EmploymentInfoEducation.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          employmentInfoEducation?.toString().toLowerCase()) ??
      defaultValue ??
      enums.EmploymentInfoEducation.swaggerGeneratedUnknown;
}

enums.EmploymentInfoEducation? employmentInfoEducationNullableFromJson(
  Object? employmentInfoEducation, [
  enums.EmploymentInfoEducation? defaultValue,
]) {
  if (employmentInfoEducation == null) {
    return null;
  }
  return enums.EmploymentInfoEducation.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          employmentInfoEducation.toString().toLowerCase()) ??
      defaultValue;
}

String employmentInfoEducationExplodedListToJson(
    List<enums.EmploymentInfoEducation>? employmentInfoEducation) {
  return employmentInfoEducation?.map((e) => e.value!).join(',') ?? '';
}

List<String> employmentInfoEducationListToJson(
    List<enums.EmploymentInfoEducation>? employmentInfoEducation) {
  if (employmentInfoEducation == null) {
    return [];
  }

  return employmentInfoEducation.map((e) => e.value!).toList();
}

List<enums.EmploymentInfoEducation> employmentInfoEducationListFromJson(
  List? employmentInfoEducation, [
  List<enums.EmploymentInfoEducation>? defaultValue,
]) {
  if (employmentInfoEducation == null) {
    return defaultValue ?? [];
  }

  return employmentInfoEducation
      .map((e) => employmentInfoEducationFromJson(e.toString()))
      .toList();
}

List<enums.EmploymentInfoEducation>?
    employmentInfoEducationNullableListFromJson(
  List? employmentInfoEducation, [
  List<enums.EmploymentInfoEducation>? defaultValue,
]) {
  if (employmentInfoEducation == null) {
    return defaultValue;
  }

  return employmentInfoEducation
      .map((e) => employmentInfoEducationFromJson(e.toString()))
      .toList();
}

String? employmentInfoEmploymentTypeNullableToJson(
    enums.EmploymentInfoEmploymentType? employmentInfoEmploymentType) {
  return employmentInfoEmploymentType?.value;
}

String? employmentInfoEmploymentTypeToJson(
    enums.EmploymentInfoEmploymentType employmentInfoEmploymentType) {
  return employmentInfoEmploymentType.value;
}

enums.EmploymentInfoEmploymentType employmentInfoEmploymentTypeFromJson(
  Object? employmentInfoEmploymentType, [
  enums.EmploymentInfoEmploymentType? defaultValue,
]) {
  return enums.EmploymentInfoEmploymentType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          employmentInfoEmploymentType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.EmploymentInfoEmploymentType.swaggerGeneratedUnknown;
}

enums.EmploymentInfoEmploymentType?
    employmentInfoEmploymentTypeNullableFromJson(
  Object? employmentInfoEmploymentType, [
  enums.EmploymentInfoEmploymentType? defaultValue,
]) {
  if (employmentInfoEmploymentType == null) {
    return null;
  }
  return enums.EmploymentInfoEmploymentType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          employmentInfoEmploymentType.toString().toLowerCase()) ??
      defaultValue;
}

String employmentInfoEmploymentTypeExplodedListToJson(
    List<enums.EmploymentInfoEmploymentType>? employmentInfoEmploymentType) {
  return employmentInfoEmploymentType?.map((e) => e.value!).join(',') ?? '';
}

List<String> employmentInfoEmploymentTypeListToJson(
    List<enums.EmploymentInfoEmploymentType>? employmentInfoEmploymentType) {
  if (employmentInfoEmploymentType == null) {
    return [];
  }

  return employmentInfoEmploymentType.map((e) => e.value!).toList();
}

List<enums.EmploymentInfoEmploymentType>
    employmentInfoEmploymentTypeListFromJson(
  List? employmentInfoEmploymentType, [
  List<enums.EmploymentInfoEmploymentType>? defaultValue,
]) {
  if (employmentInfoEmploymentType == null) {
    return defaultValue ?? [];
  }

  return employmentInfoEmploymentType
      .map((e) => employmentInfoEmploymentTypeFromJson(e.toString()))
      .toList();
}

List<enums.EmploymentInfoEmploymentType>?
    employmentInfoEmploymentTypeNullableListFromJson(
  List? employmentInfoEmploymentType, [
  List<enums.EmploymentInfoEmploymentType>? defaultValue,
]) {
  if (employmentInfoEmploymentType == null) {
    return defaultValue;
  }

  return employmentInfoEmploymentType
      .map((e) => employmentInfoEmploymentTypeFromJson(e.toString()))
      .toList();
}

String? employmentPositionNullableToJson(
    enums.EmploymentPosition? employmentPosition) {
  return employmentPosition?.value;
}

String? employmentPositionToJson(enums.EmploymentPosition employmentPosition) {
  return employmentPosition.value;
}

enums.EmploymentPosition employmentPositionFromJson(
  Object? employmentPosition, [
  enums.EmploymentPosition? defaultValue,
]) {
  return enums.EmploymentPosition.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          employmentPosition?.toString().toLowerCase()) ??
      defaultValue ??
      enums.EmploymentPosition.swaggerGeneratedUnknown;
}

enums.EmploymentPosition? employmentPositionNullableFromJson(
  Object? employmentPosition, [
  enums.EmploymentPosition? defaultValue,
]) {
  if (employmentPosition == null) {
    return null;
  }
  return enums.EmploymentPosition.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          employmentPosition.toString().toLowerCase()) ??
      defaultValue;
}

String employmentPositionExplodedListToJson(
    List<enums.EmploymentPosition>? employmentPosition) {
  return employmentPosition?.map((e) => e.value!).join(',') ?? '';
}

List<String> employmentPositionListToJson(
    List<enums.EmploymentPosition>? employmentPosition) {
  if (employmentPosition == null) {
    return [];
  }

  return employmentPosition.map((e) => e.value!).toList();
}

List<enums.EmploymentPosition> employmentPositionListFromJson(
  List? employmentPosition, [
  List<enums.EmploymentPosition>? defaultValue,
]) {
  if (employmentPosition == null) {
    return defaultValue ?? [];
  }

  return employmentPosition
      .map((e) => employmentPositionFromJson(e.toString()))
      .toList();
}

List<enums.EmploymentPosition>? employmentPositionNullableListFromJson(
  List? employmentPosition, [
  List<enums.EmploymentPosition>? defaultValue,
]) {
  if (employmentPosition == null) {
    return defaultValue;
  }

  return employmentPosition
      .map((e) => employmentPositionFromJson(e.toString()))
      .toList();
}

String? brokerFeatureNullableToJson(enums.BrokerFeature? brokerFeature) {
  return brokerFeature?.value;
}

String? brokerFeatureToJson(enums.BrokerFeature brokerFeature) {
  return brokerFeature.value;
}

enums.BrokerFeature brokerFeatureFromJson(
  Object? brokerFeature, [
  enums.BrokerFeature? defaultValue,
]) {
  return enums.BrokerFeature.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          brokerFeature?.toString().toLowerCase()) ??
      defaultValue ??
      enums.BrokerFeature.swaggerGeneratedUnknown;
}

enums.BrokerFeature? brokerFeatureNullableFromJson(
  Object? brokerFeature, [
  enums.BrokerFeature? defaultValue,
]) {
  if (brokerFeature == null) {
    return null;
  }
  return enums.BrokerFeature.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          brokerFeature.toString().toLowerCase()) ??
      defaultValue;
}

String brokerFeatureExplodedListToJson(
    List<enums.BrokerFeature>? brokerFeature) {
  return brokerFeature?.map((e) => e.value!).join(',') ?? '';
}

List<String> brokerFeatureListToJson(List<enums.BrokerFeature>? brokerFeature) {
  if (brokerFeature == null) {
    return [];
  }

  return brokerFeature.map((e) => e.value!).toList();
}

List<enums.BrokerFeature> brokerFeatureListFromJson(
  List? brokerFeature, [
  List<enums.BrokerFeature>? defaultValue,
]) {
  if (brokerFeature == null) {
    return defaultValue ?? [];
  }

  return brokerFeature.map((e) => brokerFeatureFromJson(e.toString())).toList();
}

List<enums.BrokerFeature>? brokerFeatureNullableListFromJson(
  List? brokerFeature, [
  List<enums.BrokerFeature>? defaultValue,
]) {
  if (brokerFeature == null) {
    return defaultValue;
  }

  return brokerFeature.map((e) => brokerFeatureFromJson(e.toString())).toList();
}

String? customerInformation$CustomerInformationRiskProfileNullableToJson(
    enums.CustomerInformation$CustomerInformationRiskProfile?
        customerInformation$CustomerInformationRiskProfile) {
  return customerInformation$CustomerInformationRiskProfile?.value;
}

String? customerInformation$CustomerInformationRiskProfileToJson(
    enums.CustomerInformation$CustomerInformationRiskProfile
        customerInformation$CustomerInformationRiskProfile) {
  return customerInformation$CustomerInformationRiskProfile.value;
}

enums.CustomerInformation$CustomerInformationRiskProfile
    customerInformation$CustomerInformationRiskProfileFromJson(
  Object? customerInformation$CustomerInformationRiskProfile, [
  enums.CustomerInformation$CustomerInformationRiskProfile? defaultValue,
]) {
  return enums.CustomerInformation$CustomerInformationRiskProfile.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              customerInformation$CustomerInformationRiskProfile
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.CustomerInformation$CustomerInformationRiskProfile
          .swaggerGeneratedUnknown;
}

enums.CustomerInformation$CustomerInformationRiskProfile?
    customerInformation$CustomerInformationRiskProfileNullableFromJson(
  Object? customerInformation$CustomerInformationRiskProfile, [
  enums.CustomerInformation$CustomerInformationRiskProfile? defaultValue,
]) {
  if (customerInformation$CustomerInformationRiskProfile == null) {
    return null;
  }
  return enums.CustomerInformation$CustomerInformationRiskProfile.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              customerInformation$CustomerInformationRiskProfile
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String customerInformation$CustomerInformationRiskProfileExplodedListToJson(
    List<enums.CustomerInformation$CustomerInformationRiskProfile>?
        customerInformation$CustomerInformationRiskProfile) {
  return customerInformation$CustomerInformationRiskProfile
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> customerInformation$CustomerInformationRiskProfileListToJson(
    List<enums.CustomerInformation$CustomerInformationRiskProfile>?
        customerInformation$CustomerInformationRiskProfile) {
  if (customerInformation$CustomerInformationRiskProfile == null) {
    return [];
  }

  return customerInformation$CustomerInformationRiskProfile
      .map((e) => e.value!)
      .toList();
}

List<enums.CustomerInformation$CustomerInformationRiskProfile>
    customerInformation$CustomerInformationRiskProfileListFromJson(
  List? customerInformation$CustomerInformationRiskProfile, [
  List<enums.CustomerInformation$CustomerInformationRiskProfile>? defaultValue,
]) {
  if (customerInformation$CustomerInformationRiskProfile == null) {
    return defaultValue ?? [];
  }

  return customerInformation$CustomerInformationRiskProfile
      .map((e) => customerInformation$CustomerInformationRiskProfileFromJson(
          e.toString()))
      .toList();
}

List<enums.CustomerInformation$CustomerInformationRiskProfile>?
    customerInformation$CustomerInformationRiskProfileNullableListFromJson(
  List? customerInformation$CustomerInformationRiskProfile, [
  List<enums.CustomerInformation$CustomerInformationRiskProfile>? defaultValue,
]) {
  if (customerInformation$CustomerInformationRiskProfile == null) {
    return defaultValue;
  }

  return customerInformation$CustomerInformationRiskProfile
      .map((e) => customerInformation$CustomerInformationRiskProfileFromJson(
          e.toString()))
      .toList();
}

String? customerInformation$CustomerInformationScreeningResultNullableToJson(
    enums.CustomerInformation$CustomerInformationScreeningResult?
        customerInformation$CustomerInformationScreeningResult) {
  return customerInformation$CustomerInformationScreeningResult?.value;
}

String? customerInformation$CustomerInformationScreeningResultToJson(
    enums.CustomerInformation$CustomerInformationScreeningResult
        customerInformation$CustomerInformationScreeningResult) {
  return customerInformation$CustomerInformationScreeningResult.value;
}

enums.CustomerInformation$CustomerInformationScreeningResult
    customerInformation$CustomerInformationScreeningResultFromJson(
  Object? customerInformation$CustomerInformationScreeningResult, [
  enums.CustomerInformation$CustomerInformationScreeningResult? defaultValue,
]) {
  return enums.CustomerInformation$CustomerInformationScreeningResult.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              customerInformation$CustomerInformationScreeningResult
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.CustomerInformation$CustomerInformationScreeningResult
          .swaggerGeneratedUnknown;
}

enums.CustomerInformation$CustomerInformationScreeningResult?
    customerInformation$CustomerInformationScreeningResultNullableFromJson(
  Object? customerInformation$CustomerInformationScreeningResult, [
  enums.CustomerInformation$CustomerInformationScreeningResult? defaultValue,
]) {
  if (customerInformation$CustomerInformationScreeningResult == null) {
    return null;
  }
  return enums.CustomerInformation$CustomerInformationScreeningResult.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              customerInformation$CustomerInformationScreeningResult
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String customerInformation$CustomerInformationScreeningResultExplodedListToJson(
    List<enums.CustomerInformation$CustomerInformationScreeningResult>?
        customerInformation$CustomerInformationScreeningResult) {
  return customerInformation$CustomerInformationScreeningResult
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> customerInformation$CustomerInformationScreeningResultListToJson(
    List<enums.CustomerInformation$CustomerInformationScreeningResult>?
        customerInformation$CustomerInformationScreeningResult) {
  if (customerInformation$CustomerInformationScreeningResult == null) {
    return [];
  }

  return customerInformation$CustomerInformationScreeningResult
      .map((e) => e.value!)
      .toList();
}

List<enums.CustomerInformation$CustomerInformationScreeningResult>
    customerInformation$CustomerInformationScreeningResultListFromJson(
  List? customerInformation$CustomerInformationScreeningResult, [
  List<enums.CustomerInformation$CustomerInformationScreeningResult>?
      defaultValue,
]) {
  if (customerInformation$CustomerInformationScreeningResult == null) {
    return defaultValue ?? [];
  }

  return customerInformation$CustomerInformationScreeningResult
      .map((e) =>
          customerInformation$CustomerInformationScreeningResultFromJson(
              e.toString()))
      .toList();
}

List<enums.CustomerInformation$CustomerInformationScreeningResult>?
    customerInformation$CustomerInformationScreeningResultNullableListFromJson(
  List? customerInformation$CustomerInformationScreeningResult, [
  List<enums.CustomerInformation$CustomerInformationScreeningResult>?
      defaultValue,
]) {
  if (customerInformation$CustomerInformationScreeningResult == null) {
    return defaultValue;
  }

  return customerInformation$CustomerInformationScreeningResult
      .map((e) =>
          customerInformation$CustomerInformationScreeningResultFromJson(
              e.toString()))
      .toList();
}

String?
    customerInformation$CustomerInformationScreeningCategoriesNullableToJson(
        enums.CustomerInformation$CustomerInformationScreeningCategories?
            customerInformation$CustomerInformationScreeningCategories) {
  return customerInformation$CustomerInformationScreeningCategories?.value;
}

String? customerInformation$CustomerInformationScreeningCategoriesToJson(
    enums.CustomerInformation$CustomerInformationScreeningCategories
        customerInformation$CustomerInformationScreeningCategories) {
  return customerInformation$CustomerInformationScreeningCategories.value;
}

enums.CustomerInformation$CustomerInformationScreeningCategories
    customerInformation$CustomerInformationScreeningCategoriesFromJson(
  Object? customerInformation$CustomerInformationScreeningCategories, [
  enums.CustomerInformation$CustomerInformationScreeningCategories?
      defaultValue,
]) {
  return enums.CustomerInformation$CustomerInformationScreeningCategories.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              customerInformation$CustomerInformationScreeningCategories
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.CustomerInformation$CustomerInformationScreeningCategories
          .swaggerGeneratedUnknown;
}

enums.CustomerInformation$CustomerInformationScreeningCategories?
    customerInformation$CustomerInformationScreeningCategoriesNullableFromJson(
  Object? customerInformation$CustomerInformationScreeningCategories, [
  enums.CustomerInformation$CustomerInformationScreeningCategories?
      defaultValue,
]) {
  if (customerInformation$CustomerInformationScreeningCategories == null) {
    return null;
  }
  return enums.CustomerInformation$CustomerInformationScreeningCategories.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              customerInformation$CustomerInformationScreeningCategories
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String
    customerInformation$CustomerInformationScreeningCategoriesExplodedListToJson(
        List<enums.CustomerInformation$CustomerInformationScreeningCategories>?
            customerInformation$CustomerInformationScreeningCategories) {
  return customerInformation$CustomerInformationScreeningCategories
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String>
    customerInformation$CustomerInformationScreeningCategoriesListToJson(
        List<enums.CustomerInformation$CustomerInformationScreeningCategories>?
            customerInformation$CustomerInformationScreeningCategories) {
  if (customerInformation$CustomerInformationScreeningCategories == null) {
    return [];
  }

  return customerInformation$CustomerInformationScreeningCategories
      .map((e) => e.value!)
      .toList();
}

List<enums.CustomerInformation$CustomerInformationScreeningCategories>
    customerInformation$CustomerInformationScreeningCategoriesListFromJson(
  List? customerInformation$CustomerInformationScreeningCategories, [
  List<enums.CustomerInformation$CustomerInformationScreeningCategories>?
      defaultValue,
]) {
  if (customerInformation$CustomerInformationScreeningCategories == null) {
    return defaultValue ?? [];
  }

  return customerInformation$CustomerInformationScreeningCategories
      .map((e) =>
          customerInformation$CustomerInformationScreeningCategoriesFromJson(
              e.toString()))
      .toList();
}

List<enums.CustomerInformation$CustomerInformationScreeningCategories>?
    customerInformation$CustomerInformationScreeningCategoriesNullableListFromJson(
  List? customerInformation$CustomerInformationScreeningCategories, [
  List<enums.CustomerInformation$CustomerInformationScreeningCategories>?
      defaultValue,
]) {
  if (customerInformation$CustomerInformationScreeningCategories == null) {
    return defaultValue;
  }

  return customerInformation$CustomerInformationScreeningCategories
      .map((e) =>
          customerInformation$CustomerInformationScreeningCategoriesFromJson(
              e.toString()))
      .toList();
}

String? customerInformation$FeaturesSecuritiesLendingNullableToJson(
    enums.CustomerInformation$FeaturesSecuritiesLending?
        customerInformation$FeaturesSecuritiesLending) {
  return customerInformation$FeaturesSecuritiesLending?.value;
}

String? customerInformation$FeaturesSecuritiesLendingToJson(
    enums.CustomerInformation$FeaturesSecuritiesLending
        customerInformation$FeaturesSecuritiesLending) {
  return customerInformation$FeaturesSecuritiesLending.value;
}

enums.CustomerInformation$FeaturesSecuritiesLending
    customerInformation$FeaturesSecuritiesLendingFromJson(
  Object? customerInformation$FeaturesSecuritiesLending, [
  enums.CustomerInformation$FeaturesSecuritiesLending? defaultValue,
]) {
  return enums.CustomerInformation$FeaturesSecuritiesLending.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              customerInformation$FeaturesSecuritiesLending
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.CustomerInformation$FeaturesSecuritiesLending
          .swaggerGeneratedUnknown;
}

enums.CustomerInformation$FeaturesSecuritiesLending?
    customerInformation$FeaturesSecuritiesLendingNullableFromJson(
  Object? customerInformation$FeaturesSecuritiesLending, [
  enums.CustomerInformation$FeaturesSecuritiesLending? defaultValue,
]) {
  if (customerInformation$FeaturesSecuritiesLending == null) {
    return null;
  }
  return enums.CustomerInformation$FeaturesSecuritiesLending.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              customerInformation$FeaturesSecuritiesLending
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String customerInformation$FeaturesSecuritiesLendingExplodedListToJson(
    List<enums.CustomerInformation$FeaturesSecuritiesLending>?
        customerInformation$FeaturesSecuritiesLending) {
  return customerInformation$FeaturesSecuritiesLending
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> customerInformation$FeaturesSecuritiesLendingListToJson(
    List<enums.CustomerInformation$FeaturesSecuritiesLending>?
        customerInformation$FeaturesSecuritiesLending) {
  if (customerInformation$FeaturesSecuritiesLending == null) {
    return [];
  }

  return customerInformation$FeaturesSecuritiesLending
      .map((e) => e.value!)
      .toList();
}

List<enums.CustomerInformation$FeaturesSecuritiesLending>
    customerInformation$FeaturesSecuritiesLendingListFromJson(
  List? customerInformation$FeaturesSecuritiesLending, [
  List<enums.CustomerInformation$FeaturesSecuritiesLending>? defaultValue,
]) {
  if (customerInformation$FeaturesSecuritiesLending == null) {
    return defaultValue ?? [];
  }

  return customerInformation$FeaturesSecuritiesLending
      .map((e) =>
          customerInformation$FeaturesSecuritiesLendingFromJson(e.toString()))
      .toList();
}

List<enums.CustomerInformation$FeaturesSecuritiesLending>?
    customerInformation$FeaturesSecuritiesLendingNullableListFromJson(
  List? customerInformation$FeaturesSecuritiesLending, [
  List<enums.CustomerInformation$FeaturesSecuritiesLending>? defaultValue,
]) {
  if (customerInformation$FeaturesSecuritiesLending == null) {
    return defaultValue;
  }

  return customerInformation$FeaturesSecuritiesLending
      .map((e) =>
          customerInformation$FeaturesSecuritiesLendingFromJson(e.toString()))
      .toList();
}

String? customerInformation$FeaturesSuitabilityAssessmentNullableToJson(
    enums.CustomerInformation$FeaturesSuitabilityAssessment?
        customerInformation$FeaturesSuitabilityAssessment) {
  return customerInformation$FeaturesSuitabilityAssessment?.value;
}

String? customerInformation$FeaturesSuitabilityAssessmentToJson(
    enums.CustomerInformation$FeaturesSuitabilityAssessment
        customerInformation$FeaturesSuitabilityAssessment) {
  return customerInformation$FeaturesSuitabilityAssessment.value;
}

enums.CustomerInformation$FeaturesSuitabilityAssessment
    customerInformation$FeaturesSuitabilityAssessmentFromJson(
  Object? customerInformation$FeaturesSuitabilityAssessment, [
  enums.CustomerInformation$FeaturesSuitabilityAssessment? defaultValue,
]) {
  return enums.CustomerInformation$FeaturesSuitabilityAssessment.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              customerInformation$FeaturesSuitabilityAssessment
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.CustomerInformation$FeaturesSuitabilityAssessment
          .swaggerGeneratedUnknown;
}

enums.CustomerInformation$FeaturesSuitabilityAssessment?
    customerInformation$FeaturesSuitabilityAssessmentNullableFromJson(
  Object? customerInformation$FeaturesSuitabilityAssessment, [
  enums.CustomerInformation$FeaturesSuitabilityAssessment? defaultValue,
]) {
  if (customerInformation$FeaturesSuitabilityAssessment == null) {
    return null;
  }
  return enums.CustomerInformation$FeaturesSuitabilityAssessment.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              customerInformation$FeaturesSuitabilityAssessment
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String customerInformation$FeaturesSuitabilityAssessmentExplodedListToJson(
    List<enums.CustomerInformation$FeaturesSuitabilityAssessment>?
        customerInformation$FeaturesSuitabilityAssessment) {
  return customerInformation$FeaturesSuitabilityAssessment
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> customerInformation$FeaturesSuitabilityAssessmentListToJson(
    List<enums.CustomerInformation$FeaturesSuitabilityAssessment>?
        customerInformation$FeaturesSuitabilityAssessment) {
  if (customerInformation$FeaturesSuitabilityAssessment == null) {
    return [];
  }

  return customerInformation$FeaturesSuitabilityAssessment
      .map((e) => e.value!)
      .toList();
}

List<enums.CustomerInformation$FeaturesSuitabilityAssessment>
    customerInformation$FeaturesSuitabilityAssessmentListFromJson(
  List? customerInformation$FeaturesSuitabilityAssessment, [
  List<enums.CustomerInformation$FeaturesSuitabilityAssessment>? defaultValue,
]) {
  if (customerInformation$FeaturesSuitabilityAssessment == null) {
    return defaultValue ?? [];
  }

  return customerInformation$FeaturesSuitabilityAssessment
      .map((e) => customerInformation$FeaturesSuitabilityAssessmentFromJson(
          e.toString()))
      .toList();
}

List<enums.CustomerInformation$FeaturesSuitabilityAssessment>?
    customerInformation$FeaturesSuitabilityAssessmentNullableListFromJson(
  List? customerInformation$FeaturesSuitabilityAssessment, [
  List<enums.CustomerInformation$FeaturesSuitabilityAssessment>? defaultValue,
]) {
  if (customerInformation$FeaturesSuitabilityAssessment == null) {
    return defaultValue;
  }

  return customerInformation$FeaturesSuitabilityAssessment
      .map((e) => customerInformation$FeaturesSuitabilityAssessmentFromJson(
          e.toString()))
      .toList();
}

String? customerInformation$FeaturesSuitabilityAssessmentAnswerNullableToJson(
    enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer?
        customerInformation$FeaturesSuitabilityAssessmentAnswer) {
  return customerInformation$FeaturesSuitabilityAssessmentAnswer?.value;
}

String? customerInformation$FeaturesSuitabilityAssessmentAnswerToJson(
    enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer
        customerInformation$FeaturesSuitabilityAssessmentAnswer) {
  return customerInformation$FeaturesSuitabilityAssessmentAnswer.value;
}

enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer
    customerInformation$FeaturesSuitabilityAssessmentAnswerFromJson(
  Object? customerInformation$FeaturesSuitabilityAssessmentAnswer, [
  enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer? defaultValue,
]) {
  return enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              customerInformation$FeaturesSuitabilityAssessmentAnswer
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer
          .swaggerGeneratedUnknown;
}

enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer?
    customerInformation$FeaturesSuitabilityAssessmentAnswerNullableFromJson(
  Object? customerInformation$FeaturesSuitabilityAssessmentAnswer, [
  enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer? defaultValue,
]) {
  if (customerInformation$FeaturesSuitabilityAssessmentAnswer == null) {
    return null;
  }
  return enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              customerInformation$FeaturesSuitabilityAssessmentAnswer
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String
    customerInformation$FeaturesSuitabilityAssessmentAnswerExplodedListToJson(
        List<enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer>?
            customerInformation$FeaturesSuitabilityAssessmentAnswer) {
  return customerInformation$FeaturesSuitabilityAssessmentAnswer
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> customerInformation$FeaturesSuitabilityAssessmentAnswerListToJson(
    List<enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer>?
        customerInformation$FeaturesSuitabilityAssessmentAnswer) {
  if (customerInformation$FeaturesSuitabilityAssessmentAnswer == null) {
    return [];
  }

  return customerInformation$FeaturesSuitabilityAssessmentAnswer
      .map((e) => e.value!)
      .toList();
}

List<enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer>
    customerInformation$FeaturesSuitabilityAssessmentAnswerListFromJson(
  List? customerInformation$FeaturesSuitabilityAssessmentAnswer, [
  List<enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer>?
      defaultValue,
]) {
  if (customerInformation$FeaturesSuitabilityAssessmentAnswer == null) {
    return defaultValue ?? [];
  }

  return customerInformation$FeaturesSuitabilityAssessmentAnswer
      .map((e) =>
          customerInformation$FeaturesSuitabilityAssessmentAnswerFromJson(
              e.toString()))
      .toList();
}

List<enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer>?
    customerInformation$FeaturesSuitabilityAssessmentAnswerNullableListFromJson(
  List? customerInformation$FeaturesSuitabilityAssessmentAnswer, [
  List<enums.CustomerInformation$FeaturesSuitabilityAssessmentAnswer>?
      defaultValue,
]) {
  if (customerInformation$FeaturesSuitabilityAssessmentAnswer == null) {
    return defaultValue;
  }

  return customerInformation$FeaturesSuitabilityAssessmentAnswer
      .map((e) =>
          customerInformation$FeaturesSuitabilityAssessmentAnswerFromJson(
              e.toString()))
      .toList();
}

String? ninExchangeNullableToJson(enums.NinExchange? ninExchange) {
  return ninExchange?.value;
}

String? ninExchangeToJson(enums.NinExchange ninExchange) {
  return ninExchange.value;
}

enums.NinExchange ninExchangeFromJson(
  Object? ninExchange, [
  enums.NinExchange? defaultValue,
]) {
  return enums.NinExchange.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          ninExchange?.toString().toLowerCase()) ??
      defaultValue ??
      enums.NinExchange.swaggerGeneratedUnknown;
}

enums.NinExchange? ninExchangeNullableFromJson(
  Object? ninExchange, [
  enums.NinExchange? defaultValue,
]) {
  if (ninExchange == null) {
    return null;
  }
  return enums.NinExchange.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          ninExchange.toString().toLowerCase()) ??
      defaultValue;
}

String ninExchangeExplodedListToJson(List<enums.NinExchange>? ninExchange) {
  return ninExchange?.map((e) => e.value!).join(',') ?? '';
}

List<String> ninExchangeListToJson(List<enums.NinExchange>? ninExchange) {
  if (ninExchange == null) {
    return [];
  }

  return ninExchange.map((e) => e.value!).toList();
}

List<enums.NinExchange> ninExchangeListFromJson(
  List? ninExchange, [
  List<enums.NinExchange>? defaultValue,
]) {
  if (ninExchange == null) {
    return defaultValue ?? [];
  }

  return ninExchange.map((e) => ninExchangeFromJson(e.toString())).toList();
}

List<enums.NinExchange>? ninExchangeNullableListFromJson(
  List? ninExchange, [
  List<enums.NinExchange>? defaultValue,
]) {
  if (ninExchange == null) {
    return defaultValue;
  }

  return ninExchange.map((e) => ninExchangeFromJson(e.toString())).toList();
}

String? featureStatusProductNameNullableToJson(
    enums.FeatureStatusProductName? featureStatusProductName) {
  return featureStatusProductName?.value;
}

String? featureStatusProductNameToJson(
    enums.FeatureStatusProductName featureStatusProductName) {
  return featureStatusProductName.value;
}

enums.FeatureStatusProductName featureStatusProductNameFromJson(
  Object? featureStatusProductName, [
  enums.FeatureStatusProductName? defaultValue,
]) {
  return enums.FeatureStatusProductName.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          featureStatusProductName?.toString().toLowerCase()) ??
      defaultValue ??
      enums.FeatureStatusProductName.swaggerGeneratedUnknown;
}

enums.FeatureStatusProductName? featureStatusProductNameNullableFromJson(
  Object? featureStatusProductName, [
  enums.FeatureStatusProductName? defaultValue,
]) {
  if (featureStatusProductName == null) {
    return null;
  }
  return enums.FeatureStatusProductName.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          featureStatusProductName.toString().toLowerCase()) ??
      defaultValue;
}

String featureStatusProductNameExplodedListToJson(
    List<enums.FeatureStatusProductName>? featureStatusProductName) {
  return featureStatusProductName?.map((e) => e.value!).join(',') ?? '';
}

List<String> featureStatusProductNameListToJson(
    List<enums.FeatureStatusProductName>? featureStatusProductName) {
  if (featureStatusProductName == null) {
    return [];
  }

  return featureStatusProductName.map((e) => e.value!).toList();
}

List<enums.FeatureStatusProductName> featureStatusProductNameListFromJson(
  List? featureStatusProductName, [
  List<enums.FeatureStatusProductName>? defaultValue,
]) {
  if (featureStatusProductName == null) {
    return defaultValue ?? [];
  }

  return featureStatusProductName
      .map((e) => featureStatusProductNameFromJson(e.toString()))
      .toList();
}

List<enums.FeatureStatusProductName>?
    featureStatusProductNameNullableListFromJson(
  List? featureStatusProductName, [
  List<enums.FeatureStatusProductName>? defaultValue,
]) {
  if (featureStatusProductName == null) {
    return defaultValue;
  }

  return featureStatusProductName
      .map((e) => featureStatusProductNameFromJson(e.toString()))
      .toList();
}

String? featureStatusStatusNullableToJson(
    enums.FeatureStatusStatus? featureStatusStatus) {
  return featureStatusStatus?.value;
}

String? featureStatusStatusToJson(
    enums.FeatureStatusStatus featureStatusStatus) {
  return featureStatusStatus.value;
}

enums.FeatureStatusStatus featureStatusStatusFromJson(
  Object? featureStatusStatus, [
  enums.FeatureStatusStatus? defaultValue,
]) {
  return enums.FeatureStatusStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          featureStatusStatus?.toString().toLowerCase()) ??
      defaultValue ??
      enums.FeatureStatusStatus.swaggerGeneratedUnknown;
}

enums.FeatureStatusStatus? featureStatusStatusNullableFromJson(
  Object? featureStatusStatus, [
  enums.FeatureStatusStatus? defaultValue,
]) {
  if (featureStatusStatus == null) {
    return null;
  }
  return enums.FeatureStatusStatus.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          featureStatusStatus.toString().toLowerCase()) ??
      defaultValue;
}

String featureStatusStatusExplodedListToJson(
    List<enums.FeatureStatusStatus>? featureStatusStatus) {
  return featureStatusStatus?.map((e) => e.value!).join(',') ?? '';
}

List<String> featureStatusStatusListToJson(
    List<enums.FeatureStatusStatus>? featureStatusStatus) {
  if (featureStatusStatus == null) {
    return [];
  }

  return featureStatusStatus.map((e) => e.value!).toList();
}

List<enums.FeatureStatusStatus> featureStatusStatusListFromJson(
  List? featureStatusStatus, [
  List<enums.FeatureStatusStatus>? defaultValue,
]) {
  if (featureStatusStatus == null) {
    return defaultValue ?? [];
  }

  return featureStatusStatus
      .map((e) => featureStatusStatusFromJson(e.toString()))
      .toList();
}

List<enums.FeatureStatusStatus>? featureStatusStatusNullableListFromJson(
  List? featureStatusStatus, [
  List<enums.FeatureStatusStatus>? defaultValue,
]) {
  if (featureStatusStatus == null) {
    return defaultValue;
  }

  return featureStatusStatus
      .map((e) => featureStatusStatusFromJson(e.toString()))
      .toList();
}

String? numberValueTypeNullableToJson(enums.NumberValueType? numberValueType) {
  return numberValueType?.value;
}

String? numberValueTypeToJson(enums.NumberValueType numberValueType) {
  return numberValueType.value;
}

enums.NumberValueType numberValueTypeFromJson(
  Object? numberValueType, [
  enums.NumberValueType? defaultValue,
]) {
  return enums.NumberValueType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          numberValueType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.NumberValueType.swaggerGeneratedUnknown;
}

enums.NumberValueType? numberValueTypeNullableFromJson(
  Object? numberValueType, [
  enums.NumberValueType? defaultValue,
]) {
  if (numberValueType == null) {
    return null;
  }
  return enums.NumberValueType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          numberValueType.toString().toLowerCase()) ??
      defaultValue;
}

String numberValueTypeExplodedListToJson(
    List<enums.NumberValueType>? numberValueType) {
  return numberValueType?.map((e) => e.value!).join(',') ?? '';
}

List<String> numberValueTypeListToJson(
    List<enums.NumberValueType>? numberValueType) {
  if (numberValueType == null) {
    return [];
  }

  return numberValueType.map((e) => e.value!).toList();
}

List<enums.NumberValueType> numberValueTypeListFromJson(
  List? numberValueType, [
  List<enums.NumberValueType>? defaultValue,
]) {
  if (numberValueType == null) {
    return defaultValue ?? [];
  }

  return numberValueType
      .map((e) => numberValueTypeFromJson(e.toString()))
      .toList();
}

List<enums.NumberValueType>? numberValueTypeNullableListFromJson(
  List? numberValueType, [
  List<enums.NumberValueType>? defaultValue,
]) {
  if (numberValueType == null) {
    return defaultValue;
  }

  return numberValueType
      .map((e) => numberValueTypeFromJson(e.toString()))
      .toList();
}

String? numberValueSignNullableToJson(enums.NumberValueSign? numberValueSign) {
  return numberValueSign?.value;
}

String? numberValueSignToJson(enums.NumberValueSign numberValueSign) {
  return numberValueSign.value;
}

enums.NumberValueSign numberValueSignFromJson(
  Object? numberValueSign, [
  enums.NumberValueSign? defaultValue,
]) {
  return enums.NumberValueSign.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          numberValueSign?.toString().toLowerCase()) ??
      defaultValue ??
      enums.NumberValueSign.swaggerGeneratedUnknown;
}

enums.NumberValueSign? numberValueSignNullableFromJson(
  Object? numberValueSign, [
  enums.NumberValueSign? defaultValue,
]) {
  if (numberValueSign == null) {
    return null;
  }
  return enums.NumberValueSign.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          numberValueSign.toString().toLowerCase()) ??
      defaultValue;
}

String numberValueSignExplodedListToJson(
    List<enums.NumberValueSign>? numberValueSign) {
  return numberValueSign?.map((e) => e.value!).join(',') ?? '';
}

List<String> numberValueSignListToJson(
    List<enums.NumberValueSign>? numberValueSign) {
  if (numberValueSign == null) {
    return [];
  }

  return numberValueSign.map((e) => e.value!).toList();
}

List<enums.NumberValueSign> numberValueSignListFromJson(
  List? numberValueSign, [
  List<enums.NumberValueSign>? defaultValue,
]) {
  if (numberValueSign == null) {
    return defaultValue ?? [];
  }

  return numberValueSign
      .map((e) => numberValueSignFromJson(e.toString()))
      .toList();
}

List<enums.NumberValueSign>? numberValueSignNullableListFromJson(
  List? numberValueSign, [
  List<enums.NumberValueSign>? defaultValue,
]) {
  if (numberValueSign == null) {
    return defaultValue;
  }

  return numberValueSign
      .map((e) => numberValueSignFromJson(e.toString()))
      .toList();
}

// ignore: unused_element
String? _dateToJson(DateTime? date) {
  if (date == null) {
    return null;
  }

  final year = date.year.toString();
  final month = date.month < 10 ? '0${date.month}' : date.month.toString();
  final day = date.day < 10 ? '0${date.day}' : date.day.toString();

  return '$year-$month-$day';
}

class Wrapped<T> {
  final T value;
  const Wrapped.value(this.value);
}
