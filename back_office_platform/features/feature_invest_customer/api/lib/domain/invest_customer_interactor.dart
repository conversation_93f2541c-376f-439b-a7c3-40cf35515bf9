import 'package:wio_feature_invest_customer_api/invest_customer_api.dart';

/// Defines the methods that the InvestCustomerInteractor class would implement
abstract interface class InvestCustomerInteractor {
  /// Gets customer details of the invest user
  Future<InvestCustomerDetails> getInvestCustomerDetails(String customerId);

  /// Gets the investor information of the customer
  Future<CustomerInvestInformation> getCustomerInvestInfo(
    String customerId,
  );
}
