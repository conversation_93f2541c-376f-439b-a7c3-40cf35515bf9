import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_common_error_handler_api/common_error_handler_api.dart';
import 'package:wio_feature_wps_api/domain/models/payroll_employee.dart';
import 'package:wio_feature_wps_api/domain/models/payroll_file.dart';
import 'package:wio_feature_wps_api/domain/models/payroll_filter_request_payload.dart';
import 'package:wio_feature_wps_api/domain/models/payroll_nak_info.dart';
import 'package:wio_feature_wps_api/domain/models/payroll_validation_failures.dart';
import 'package:wio_feature_wps_api/domain/models/reversed_payroll_employee.dart';
import 'package:wio_feature_wps_impl/src/data/models/schema_dtos/payroll_filter_request_dto.dart';
import 'package:wio_feature_wps_impl/src/data/models/schema_dtos/payroll_schema.swagger.dart'
    as payroll_swagger;
import 'package:wio_feature_wps_impl/src/data/payroll_files_enum_mappers.dart';

abstract interface class PayrollFilesMapper {
  PayrollFile mapToPayrollFileModel(
    payroll_swagger.FileDetailsCrmDto fileDetailsDto,
  );

  PayrollNakInfo mapToPayrollNakInfo(payroll_swagger.NakError nakInfo);

  PayrollValidationFailures mapToPayrollValidationFailures(
    payroll_swagger.SalaryValidationFailure validationFailure,
  );

  PayrollEmployee mapToPayrollEmployee(
    payroll_swagger.EmployeeDetailDto employeeDetailDto,
  );

  PayrollFilterRequestDto mapToPayrollFilterRequestDto(
    PayrollFilterRequestPayload filterPayload,
  );

  CommonError mapToCommonError(RestApiException<Object?> error);
}

class PayrollFilesMapperImpl implements PayrollFilesMapper {
  final ErrorReporter _errorReporter;
  final PayrollFilesEnumMapper _enumMapper;

  PayrollFilesMapperImpl({
    required ErrorReporter errorReporter,
    required PayrollFilesEnumMapper enumMapper,
  })  : _errorReporter = errorReporter,
        _enumMapper = enumMapper;

  @override
  PayrollFile mapToPayrollFileModel(
    payroll_swagger.FileDetailsCrmDto fileDetailsDto,
  ) {
    return _errorReporter
        .executeWithReport(() => _mapToPayrollFileModel(fileDetailsDto));
  }

  @override
  PayrollValidationFailures mapToPayrollValidationFailures(
    payroll_swagger.SalaryValidationFailure validationFailure,
  ) {
    return _errorReporter.executeWithReport(
      () => _mapToPayrollValidationFailure(validationFailure),
    );
  }

  @override
  PayrollNakInfo mapToPayrollNakInfo(
    payroll_swagger.NakError nakInfo,
  ) {
    return _errorReporter
        .executeWithReport(() => _mapToPayrollNakInfo(nakInfo));
  }

  @override
  PayrollEmployee mapToPayrollEmployee(
    payroll_swagger.EmployeeDetailDto employeeDetailDto,
  ) {
    return _errorReporter.executeWithReport(
      () => _mapToPayrollEmployee(employeeDetailDto),
    );
  }

  PayrollFile _mapToPayrollFileModel(
    payroll_swagger.FileDetailsCrmDto fileDetailsDto,
  ) {
    final validationFailures = fileDetailsDto.validationFailures ??
        <payroll_swagger.SalaryValidationFailure>[];
    final nakInfos = fileDetailsDto.nakInfo ?? <payroll_swagger.NakError>[];
    final fee = fileDetailsDto.fee == null
        ? null
        : Money.fromNumWithCurrency(
            fileDetailsDto.fee!,
            Currency.aed,
          );
    final amount = fileDetailsDto.amount == null
        ? null
        : Money.fromNumWithCurrency(
            fileDetailsDto.amount!,
            Currency.aed,
          );

    return PayrollFile(
      fee: fee,
      amount: amount,
      id: fileDetailsDto.id,
      title: fileDetailsDto.title,
      employerId: fileDetailsDto.employerId,
      businessId: fileDetailsDto.businessId,
      statusDate: fileDetailsDto.statusDate,
      uploadDate: fileDetailsDto.uploadedDate,
      recordsNumber: fileDetailsDto.recordsNumber,
      channelReferenceNumber: fileDetailsDto.channelReferenceNumber,
      nakInfo: [
        for (final nakInfo in nakInfos) mapToPayrollNakInfo(nakInfo),
      ],
      validationFailures: [
        for (final validationFailure in validationFailures)
          mapToPayrollValidationFailures(validationFailure),
      ],
      status: _enumMapper.mapToPayrollFileStatus(fileDetailsDto.status),
      unityFileCreationDate: fileDetailsDto.unityFileCreationDate,
    );
  }

  PayrollValidationFailures _mapToPayrollValidationFailure(
    payroll_swagger.SalaryValidationFailure validationFailure,
  ) {
    return PayrollValidationFailures(
      lineNumber: validationFailure.lineNumber,
      failureCode: validationFailure.failureCode,
      failureReason: validationFailure.failureReason,
    );
  }

  PayrollNakInfo _mapToPayrollNakInfo(
    payroll_swagger.NakError nakError,
  ) {
    return PayrollNakInfo(
      lineNumber: nakError.lineNumber,
      failureCode: nakError.failureCode,
      failureReason: nakError.failureReason,
    );
  }

  PayrollEmployee _mapToPayrollEmployee(
    payroll_swagger.EmployeeDetailDto employeeDetailDto,
  ) {
    final reversalInfo = employeeDetailDto.reversalInfo != null
        ? _mapToReversedPayrollEmployee(employeeDetailDto.reversalInfo!)
        : null;

    final incomeFixed = employeeDetailDto.incomeFixed == null
        ? null
        : Money.fromNumWithCurrency(
            employeeDetailDto.incomeFixed!,
            Currency.aed,
          );

    final incomeVariable = employeeDetailDto.incomeVariable == null
        ? null
        : Money.fromNumWithCurrency(
            employeeDetailDto.incomeVariable!,
            Currency.aed,
          );

    return PayrollEmployee(
      incomeFixed: incomeFixed,
      reversalInfo: reversalInfo,
      incomeVariable: incomeVariable,
      index: employeeDetailDto.index,
      reversed: employeeDetailDto.reversed,
      employeeId: employeeDetailDto.employeeId,
      payEndDate: employeeDetailDto.payEndDate,
      daysOnLeave: employeeDetailDto.daysOnLeave,
      employeeEID: employeeDetailDto.employeeEID,
      payStartDate: employeeDetailDto.payStartDate,
      employeeName: employeeDetailDto.employeeName,
      daysInPeriod: employeeDetailDto.daysInPeriod,
      employeeAccount: employeeDetailDto.employeeAccount,
      routingCodeOfBank: employeeDetailDto.routingCodeOfBank,
    );
  }

  ReversedPayrollEmployee _mapToReversedPayrollEmployee(
    payroll_swagger.TransactionReversedEmployee transactionReversedEmployee,
  ) {
    return ReversedPayrollEmployee(
      employeeId: transactionReversedEmployee.employeeId,
      transAmount: transactionReversedEmployee.transAmount,
      employeeIBAN: transactionReversedEmployee.employeeIBAN,
      reasonOfReturn: transactionReversedEmployee.reasonOfReturn,
    );
  }

  @override
  CommonError mapToCommonError(RestApiException<Object?> error) {
    return _errorReporter.executeWithReport(
      () {
        final domainError = payroll_swagger.ErrorModel.fromJsonFactory(
          error.response?.data as Map<String, dynamic>,
        );

        return CommonError(
          identifier: error.correlationId != null
              ? ErrorIdentifier.correlationId(error.correlationId!)
              : const ErrorIdentifier.none(),
          errorCode: domainError.code ??
              _getErrorCode(error.statusCode ?? error.response?.data) ??
              'FE_NO_ERROR_CODE_PROVIDED',
          stackTrace: StackTrace.current,
          payload: error,
        );
      },
    );
  }

  String? _getErrorCode(Object? data) {
    if (data is int) {
      return data.toString();
    }

    if (data is! Map<String, dynamic>) {
      return null;
    }

    final errorStatus = data['code'] ?? data['status'];

    if (errorStatus is num || errorStatus is String) {
      return errorStatus.toString();
    }

    return null;
  }

  @override
  PayrollFilterRequestDto mapToPayrollFilterRequestDto(
    PayrollFilterRequestPayload filterPayload,
  ) {
    final statuses = filterPayload.statuses
        ?.map(
          (status) => _enumMapper.mapToPayrollFileStatusDto(status),
        )
        .toList();
    return PayrollFilterRequestDto(
      customerId: filterPayload.businessId,
      statuses: statuses,
    );
  }
}
