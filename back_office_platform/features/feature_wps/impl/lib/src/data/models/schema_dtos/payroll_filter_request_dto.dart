import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_wps_impl/src/data/models/schema_dtos/payroll_schema.enums.swagger.dart';

part 'payroll_filter_request_dto.freezed.dart';

/// Dto class to hold all info related to payrolls list filter
@freezed
class PayrollFilterRequestDto with _$PayrollFilterRequestDto {
  /// Constructor to create a [PayrollFilterRequestDto]
  const factory PayrollFilterRequestDto({
    String? customerId,
    List<FileDetailsCrmDtoStatus>? statuses,
  }) = _PayrollFilterRequestDto;
}
