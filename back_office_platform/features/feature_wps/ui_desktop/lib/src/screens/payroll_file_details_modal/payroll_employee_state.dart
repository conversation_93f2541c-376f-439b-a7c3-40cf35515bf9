import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_wps_api/domain/models/payroll_employee.dart';

part 'payroll_employee_state.freezed.dart';

@freezed
class PayrollEmployeeState with _$PayrollEmployeeState {
  const factory PayrollEmployeeState.loaded({
    required List<PayrollEmployee> payrollEmployees,
  }) = _PayrollEmployeeStateLoaded;

  const factory PayrollEmployeeState.loading() = _PayrollEmployeeStateLoading;

  const factory PayrollEmployeeState.error({
    required String errorMessage,
  }) = _PayrollEmployeeStateError;
}
