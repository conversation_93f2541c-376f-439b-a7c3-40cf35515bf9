import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_wps_api/domain/models/payroll_file.dart';
import 'package:wio_feature_wps_api/domain/models/payroll_filter_request_payload.dart';

part 'wps_state.freezed.dart';

@freezed
class WpsState with _$WpsState {
  const factory WpsState.loaded({
    required int limit,
    required int totalPages,
    required int currentPageNumber,
    required List<PayrollFile> payrollFiles,
    required PayrollFilterRequestPayload filterRequestPayload,
  }) = _WpsStateLoaded;

  const factory WpsState.loading({
    required int limit,
    required int currentPageNumber,
    required PayrollFilterRequestPayload filterRequestPayload,
  }) = _WpsStateLoading;

  const factory WpsState.error({
    required int limit,
    required int currentPageNumber,
    required PayrollFilterRequestPayload filterRequestPayload,
  }) = _WpsStateError;
}
