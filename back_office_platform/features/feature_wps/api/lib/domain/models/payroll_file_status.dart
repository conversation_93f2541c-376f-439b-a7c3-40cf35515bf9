/// Enum for payroll file status that represents various status of payroll files
/// uploaded.
enum PayrollFileStatus {
  /// Represents invalid payroll file
  invalid,

  /// Represents payroll file in pending approval state
  pendingApproval,

  /// Represents approved payroll file
  approved,

  /// Represents transferred payroll file
  transferred,

  /// Represents payroll file in processing state
  processing,

  /// Represents payroll file in uploaded state
  uploaded,

  /// Represents payroll file in reversed state
  reversed,

  /// Represents payroll file in failed upload state
  failed,

  /// Represents payroll file which is partially reversed
  partiallyReversed,

  /// Represents payroll file which is rejected
  rejected,

  /// Unknown payroll file status type
  unknown,
}
