name: wio_feature_subscription_ui_desktop
version: 0.0.1
publish_to: none
environment: 
  flutter: 3.27.3
  sdk: '>=3.6.0 <4.0.0'
dependencies: 
  collection: 1.19.0
  di: 
    path: ../../../../core/di
  file_picker: 8.3.5
  flutter: 
    sdk: flutter
  flutter_bloc: 9.0.0
  freezed_annotation: 2.4.4
  intl: 0.19.0
  logging_api: 
    path: ../../../../core/logging/api
  ui: 
    path: ../../../../core/ui
  ui_kit_legacy_core: 
    path: ../../../../ui_kit_legacy/ui_kit_core
  ui_kit_legacy_mobile: 
    path: ../../../../ui_kit_legacy/ui_kit_mobile
  wio_app_core_api:
    path: ../../../../core/app_core/api
  wio_core_navigation_api: 
    path: ../../../../core/navigation/api
  wio_core_navigation_ui: 
    path: ../../../../core/navigation/ui
  wio_feature_common_error_handler_api: 
    path: ../../../common/feature_common_error_handler/api
  wio_feature_common_toast_message_api: 
    path: ../../../../common/tools/toast_message/api
  wio_feature_files_api: 
    path: ../../../common/feature_files/api
  wio_feature_subscription_api: 
    path: ../api
dev_dependencies: 
  bloc_test: 10.0.0
  build_runner: 2.4.14
  core_lints: 
    path: ../../../../tooling/core_lints
  flutter_lints: 4.0.0
  flutter_test: 
    sdk: flutter
  freezed: 2.5.7
  mocktail: 1.0.4
  tests: 
    path: ../../../../core/tests/impl
flutter: 
  uses-material-design: true
