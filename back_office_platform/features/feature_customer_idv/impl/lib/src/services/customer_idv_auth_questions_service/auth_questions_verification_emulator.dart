import 'package:wio_feature_customer_idv_api/customer_idv_api.dart';
import 'package:wio_feature_customer_idv_impl/src/openapi/dtos/auth_questions_dto.dart';
import 'package:wio_feature_customer_idv_impl/src/services/customer_idv_auth_questions_service/auth_questions_emulator_mapper.dart';
import 'package:wio_feature_customer_idv_impl/src/services/customer_idv_auth_questions_service/internal_models.dart';

/// [AuthQuestionsVerificationEmulator] will emulate a BE service that will
/// provide the authentication questions and verify the answers
abstract class AuthQuestionsVerificationEmulator {
  /// Get the authentication questions based input provided for the customer
  Future<AuthenticationQuestions> getAuthQuestions(AuthQuestionsDto input);

  /// Verify the answers provided by the customer for a question set
  /// with questionSetId as [questionSetId] for question with [questionId] with
  /// answerId as [answerId]
  Future<AuthenticationQuestionAnswerCorrectness> verifyAuthAnswers({
    required String questionSetId,
    required String questionId,
    required String answerId,
  });
}

class AuthQuestionsVerificationEmulatorImpl
    implements AuthQuestionsVerificationEmulator {
  AuthenticationQuestionsInternal? _currentQuestions;
  final AuthQuestionEmulatorMapper _authQuestionEmulatorMapper;

  AuthQuestionsVerificationEmulatorImpl({
    required AuthQuestionEmulatorMapper authQuestionEmulatorMapper,
  }) : _authQuestionEmulatorMapper = authQuestionEmulatorMapper;

  @override
  Future<AuthenticationQuestions> getAuthQuestions(
    AuthQuestionsDto questions,
  ) async {
    final response = _authQuestionEmulatorMapper.mapToAuthQuestions(questions);

    _currentQuestions = response.internalQuestions;

    return response.uiQuestions;
  }

  @override
  Future<AuthenticationQuestionAnswerCorrectness> verifyAuthAnswers({
    required String questionSetId,
    required String questionId,
    required String answerId,
  }) async {
    final internalQuestions = _currentQuestions;
    if (internalQuestions == null) {
      throw Exception('No questions available to verify');
    }

    final question = internalQuestions.questions[questionId];

    if (question == null) {
      throw Exception(
        'Invalid question id. No such question available to verify',
      );
    }

    final answer = question.answers[answerId];

    if (answer == null) {
      throw Exception(
        'Invalid answer id. No such answer available to verify',
      );
    }

    if (answer.isCorrect) {
      return AuthenticationQuestionAnswerCorrectness.correct;
    }

    return AuthenticationQuestionAnswerCorrectness.incorrect;
  }
}
