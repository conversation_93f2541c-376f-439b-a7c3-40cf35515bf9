import 'package:wio_feature_app_common_api/app_common_api.dart' as common;
import 'package:wio_feature_customer_idv_api/customer_idv_api.dart';

class CallCustomerInteractorImpl implements CallCustomerInteractor {
  final CallCustomerRepository _repository;

  const CallCustomerInteractorImpl({
    required CallCustomerRepository repository,
  }) : _repository = repository;

  @override
  Future<void> startCall({
    required String phoneNumber,
    common.ProductType? productType,
    String? individualId,
    String? businessId,
  }) async {
    return _repository.startCall(
      phoneNumber: phoneNumber,
      productType: productType,
      individualId: individualId,
      businessId: businessId,
    );
  }
}
