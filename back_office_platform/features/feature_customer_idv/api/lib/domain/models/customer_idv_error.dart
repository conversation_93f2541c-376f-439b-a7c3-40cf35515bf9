import 'package:wio_app_core_api/rest_network_manager.dart';

/// The type of error that occurred when searching for a customer.
enum CustomerIdvErrorType {
  /// The user should refine their search query.
  refineSearchQuery,
}

/// The error that occurred when searching for a customer.
class CustomerIdvError extends HttpRequestException {
  /// The type of error that occurred when searching for a customer.
  final CustomerIdvErrorType type;

  /// Creates a new [CustomerIdvError].
  CustomerIdvError({required this.type});

  @override
  String toString() => 'CustomerIdvError(type: $type)';
}
