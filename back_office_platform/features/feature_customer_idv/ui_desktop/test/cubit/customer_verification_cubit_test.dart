import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_common_error_handler_api/common_error_handler_api.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_customer_idv_api/customer_idv_api.dart';
import 'package:wio_feature_customer_idv_ui_desktop/src/screens/customer_verification/cubit/customer_verification_cubit.dart';
import 'package:wio_feature_customer_idv_ui_desktop/src/screens/customer_verification/cubit/customer_verification_state.dart';

import '../mock.dart';
import '../stubs.dart';

typedef _Cubit = CustomerVerificationCubit;
typedef _State = CustomerVerificationState;

void main() {
  late _Cubit cubit;
  late CustomerIdvInteractor customerIdvInteractor;
  late CustomerVerificationInteractor customerVerificationInteractor;
  late ToastMessageProvider toastMessageProvider;
  late NavigationProvider navigationProvider;
  late CommonErrorHandler commonErrorHandler;

  setUp(() {
    customerIdvInteractor = MockCustomerIdvInteractor();
    customerVerificationInteractor = MockCustomerVerificationInteractor();
    toastMessageProvider = MockToastMessageProvider();
    navigationProvider = MockNavigationProvider();
    commonErrorHandler = MockCommonErrorHandler();
    cubit = _Cubit(
      customerVerificationInteractor: customerVerificationInteractor,
      customerIdvInteractor: customerIdvInteractor,
      toastMessageProvider: toastMessageProvider,
      navigationProvider: navigationProvider,
      commonErrorHandler: commonErrorHandler,
    );
  });

  setUpAll(() => CustomerIdvUiDesktopTestStubs.registerFallbacks());

  group('init test', () {
    blocTest<_Cubit, _State>(
      'should emit loaded state for retail',
      build: () => cubit,
      setUp: () {
        when(() => customerVerificationInteractor.getAuthQuestions(any()))
            .justAnswerAsync(CustomerIdvUiDesktopTestStubs.authQuestions);
      },
      act: (cubit) => cubit.initialize(
        productType: ProductType.retail,
        individualId: 'individualId',
        conversationId: 'conversationId',
      ),
      expect: () => [
        _State.loaded(
          authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
          conversationId: 'conversationId',
          currentQuestionId: CustomerIdvUiDesktopTestStubs
              .authQuestions.questions.values.first.questionId,
        ),
      ],
    );

    blocTest<_Cubit, _State>(
      'should emit loaded state for SME',
      build: () => cubit,
      setUp: () {
        when(() => customerVerificationInteractor.getAuthQuestions(any()))
            .justAnswerAsync(CustomerIdvUiDesktopTestStubs.authQuestions);
      },
      act: (cubit) => cubit.initialize(
        productType: ProductType.business,
        individualId: 'individualId',
        businessId: 'businessId',
        conversationId: 'conversationId',
      ),
      expect: () => [
        _State.loaded(
          authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
          conversationId: 'conversationId',
          currentQuestionId: CustomerIdvUiDesktopTestStubs
              .authQuestions.questions.values.first.questionId,
        ),
      ],
    );

    blocTest<_Cubit, _State>(
      'should emit error state',
      build: () => cubit,
      setUp: () {
        when(() => customerVerificationInteractor.getAuthQuestions(any()))
            .justThrowAsync(Exception());
      },
      act: (cubit) => cubit.initialize(
        productType: ProductType.business,
        individualId: 'individualId',
        businessId: 'businessId',
        conversationId: 'conversationId',
      ),
      expect: () => [
        const _State.error(
          productType: ProductType.business,
          individualId: 'individualId',
          businessId: 'businessId',
          conversationId: 'conversationId',
        ),
      ],
      verify: (_) {
        verify(
          () => commonErrorHandler.handleError(
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).calledOnce;
      },
    );
  });

  group('retry tests', () {
    blocTest<_Cubit, _State>(
      'should retry from error state',
      build: () => cubit,
      setUp: () {
        when(() => customerVerificationInteractor.getAuthQuestions(any()))
            .justAnswerAsync(CustomerIdvUiDesktopTestStubs.authQuestions);
      },
      seed: () => const _State.error(
        productType: ProductType.business,
        individualId: 'individualId',
        businessId: 'businessId',
        conversationId: 'conversationId',
      ),
      act: (cubit) => cubit.onRetry(),
      expect: () => [
        const _State.initial(),
        _State.loaded(
          authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
          conversationId: 'conversationId',
          currentQuestionId: CustomerIdvUiDesktopTestStubs
              .authQuestions.questions.values.first.questionId,
        ),
      ],
    );
  });

  group('Answer question test', () {
    blocTest<_Cubit, _State>(
      'Correct answer should present next question',
      build: () => cubit,
      setUp: () {
        when(
          () => customerVerificationInteractor.verifyAuthAnswer(
            conversationId: any(named: 'conversationId'),
            questionId: any(named: 'questionId'),
            answerId: any(named: 'answerId'),
          ),
        ).justAnswerAsync(AuthenticationQuestionAnswerCorrectness.correct);
      },
      seed: () => _State.loaded(
        authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
        conversationId: 'conversationId',
        currentQuestionId: CustomerIdvUiDesktopTestStubs
            .authQuestions.questions.values.first.questionId,
      ),
      act: (cubit) => cubit.answerQuestion(
        questionId:
            'd5638fbf787d6ffbbdfa7823b62e84e68a3d08d5bf941f148d31799d775a5987',
        answerId:
            'b595e79becfd3928538dc133eaf606fbb9a9270096c94707a84432df9307004a',
      ),
      expect: () => [
        _State.loaded(
          authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
          conversationId: 'conversationId',
          currentQuestionId: CustomerIdvUiDesktopTestStubs
              .authQuestions.questions.values.first.questionId,
          verifyingAnswer: true,
        ),
        _State.loaded(
          authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
          conversationId: 'conversationId',
          currentQuestionId: CustomerIdvUiDesktopTestStubs
              .authQuestions.questions.values.last.questionId,
          numberCorrectlyAnswered: 1,
        ),
      ],
    );

    blocTest<_Cubit, _State>(
      'verification completed',
      build: () => cubit,
      setUp: () {
        when(
          () => customerVerificationInteractor.verifyAuthAnswer(
            conversationId: any(named: 'conversationId'),
            questionId: any(named: 'questionId'),
            answerId: any(named: 'answerId'),
          ),
        ).justAnswerAsync(AuthenticationQuestionAnswerCorrectness.correct);

        when(
          () => customerIdvInteractor.updateCustomerVerificationMethod(
            conversationId: any(named: 'conversationId'),
            verificationMethod: any(named: 'verificationMethod'),
          ),
        ).justAnswerAsync(
          CustomerIdvUiDesktopTestStubs.verifiedAuthInfo,
        );
      },
      seed: () => _State.loaded(
        authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
        conversationId: 'conversationId',
        currentQuestionId: CustomerIdvUiDesktopTestStubs
            .authQuestions.questions.values.first.questionId,
        numberCorrectlyAnswered: 1,
      ),
      act: (cubit) => cubit.answerQuestion(
        questionId:
            '7310e342c2c3ad6f803d39a136f13bee12d14818abc00e6b0d2676f8f78b65ab',
        answerId:
            '1bb016674a836dbfd57ecd4e1582d7d6303ec62d16ff23ca1b06cca989fa4f6d',
      ),
      expect: () => [
        _State.loaded(
          authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
          conversationId: 'conversationId',
          currentQuestionId: CustomerIdvUiDesktopTestStubs
              .authQuestions.questions.values.first.questionId,
          numberCorrectlyAnswered: 1,
          verifyingAnswer: true,
        ),
        const _State.loaded(
          authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
          conversationId: 'conversationId',
          numberCorrectlyAnswered: 2,
        ),
      ],
    );

    blocTest<_Cubit, _State>(
      'Incorrect answer should present next question',
      build: () => cubit,
      setUp: () {
        when(
          () => customerVerificationInteractor.verifyAuthAnswer(
            conversationId: any(named: 'conversationId'),
            questionId: any(named: 'questionId'),
            answerId: any(named: 'answerId'),
          ),
        ).justAnswerAsync(AuthenticationQuestionAnswerCorrectness.incorrect);
      },
      seed: () => _State.loaded(
        authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
        conversationId: 'conversationId',
        currentQuestionId: CustomerIdvUiDesktopTestStubs
            .authQuestions.questions.values.first.questionId,
      ),
      act: (cubit) => cubit.answerQuestion(
        questionId:
            'd5638fbf787d6ffbbdfa7823b62e84e68a3d08d5bf941f148d31799d775a5987',
        answerId:
            'b595e79becfd3928538dc133eaf606fbb9a9270096c94707a84432df9307004a',
      ),
      expect: () => [
        _State.loaded(
          authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
          conversationId: 'conversationId',
          currentQuestionId: CustomerIdvUiDesktopTestStubs
              .authQuestions.questions.values.first.questionId,
          verifyingAnswer: true,
        ),
        _State.loaded(
          authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
          conversationId: 'conversationId',
          currentQuestionId: CustomerIdvUiDesktopTestStubs
              .authQuestions.questions.values.last.questionId,
        ),
      ],
    );

    blocTest<_Cubit, _State>(
      'Error occured while answering',
      build: () => cubit,
      setUp: () {
        when(
          () => customerVerificationInteractor.verifyAuthAnswer(
            conversationId: any(named: 'conversationId'),
            questionId: any(named: 'questionId'),
            answerId: any(named: 'answerId'),
          ),
        ).justThrowAsync(Exception());
      },
      seed: () => _State.loaded(
        authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
        conversationId: 'conversationId',
        currentQuestionId: CustomerIdvUiDesktopTestStubs
            .authQuestions.questions.values.first.questionId,
      ),
      act: (cubit) => cubit.answerQuestion(
        questionId:
            'd5638fbf787d6ffbbdfa7823b62e84e68a3d08d5bf941f148d31799d775a5987',
        answerId:
            'b595e79becfd3928538dc133eaf606fbb9a9270096c94707a84432df9307004a',
      ),
      expect: () => <_State>[
        _State.loaded(
          authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
          conversationId: 'conversationId',
          currentQuestionId: CustomerIdvUiDesktopTestStubs
              .authQuestions.questions.values.first.questionId,
          verifyingAnswer: true,
        ),
        _State.loaded(
          authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
          conversationId: 'conversationId',
          currentQuestionId: CustomerIdvUiDesktopTestStubs
              .authQuestions.questions.values.first.questionId,
        ),
      ],
      verify: (_) {
        verify(
          () => commonErrorHandler.handleError(
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).calledOnce;
      },
    );
  });

  group('onClickViewDetailsWithoutAuthentication tests', () {
    blocTest<_Cubit, _State>(
      'verification completed',
      build: () => cubit,
      setUp: () {
        when(
          () => customerVerificationInteractor.verifyAuthAnswer(
            conversationId: any(named: 'conversationId'),
            questionId: any(named: 'questionId'),
            answerId: any(named: 'answerId'),
          ),
        ).justAnswerAsync(AuthenticationQuestionAnswerCorrectness.correct);

        when(
          () => customerIdvInteractor.updateCustomerVerificationMethod(
            conversationId: any(named: 'conversationId'),
            verificationMethod: any(named: 'verificationMethod'),
          ),
        ).justAnswerAsync(
          CustomerIdvUiDesktopTestStubs.verifiedAuthInfo,
        );
      },
      seed: () => _State.loaded(
        authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
        conversationId: 'conversationId',
        currentQuestionId: CustomerIdvUiDesktopTestStubs
            .authQuestions.questions.values.first.questionId,
        numberCorrectlyAnswered: 1,
      ),
      act: (cubit) => cubit.onClickViewDetailsWithoutAuthentication(),
      expect: () => <_State>[],
    );
  });

  group('skipQuestion tests', () {
    blocTest<_Cubit, _State>(
      'should move to next question when skipping from the first question',
      build: () => cubit,
      seed: () => _State.loaded(
        authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
        conversationId: 'conversationId',
        currentQuestionId: CustomerIdvUiDesktopTestStubs
            .authQuestions.questions.values.first.questionId,
      ),
      act: (cubit) => cubit.skipQuestion(),
      expect: () => [
        _State.loaded(
          authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
          conversationId: 'conversationId',
          currentQuestionId: CustomerIdvUiDesktopTestStubs
              .authQuestions.questions.values.last.questionId,
        ),
      ],
    );

    blocTest<_Cubit, _State>(
      'should set currentQuestionId to null when '
      'skipping from the last question',
      build: () => cubit,
      seed: () => _State.loaded(
        authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
        conversationId: 'conversationId',
        currentQuestionId: CustomerIdvUiDesktopTestStubs
            .authQuestions.questions.values.last.questionId,
      ),
      act: (cubit) => cubit.skipQuestion(),
      expect: () => [
        const _State.loaded(
          authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
          conversationId: 'conversationId',
        ),
      ],
    );

    blocTest<_Cubit, _State>(
      'should do nothing if currentQuestionId is null',
      build: () => cubit,
      seed: () => const _State.loaded(
        authenticationQuestions: CustomerIdvUiDesktopTestStubs.authQuestions,
        conversationId: 'conversationId',
      ),
      act: (cubit) => cubit.skipQuestion(),
      expect: () => <_State>[],
    );
  });
}
