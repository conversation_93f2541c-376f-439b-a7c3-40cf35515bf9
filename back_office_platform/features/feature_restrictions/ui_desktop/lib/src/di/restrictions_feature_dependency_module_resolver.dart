import 'package:di/di.dart';
import 'package:logging_api/logging.dart';
import 'package:uuid/uuid.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_common_error_handler_api/common_error_handler_api.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_customer_idv_api/customer_idv_api.dart';
import 'package:wio_feature_permissions_api/permissions_api.dart';
import 'package:wio_feature_restrictions_api/restrictions_api.dart';
import 'package:wio_feature_restrictions_ui_desktop/src/form_configs/restrictions_form_configs_handlers.dart';
import 'package:wio_feature_restrictions_ui_desktop/src/navigation/restrictions_router.dart';
import 'package:wio_feature_restrictions_ui_desktop/src/screens/restrictions/restrictions_cubit.dart';
import 'package:wio_feature_service_request_api/service_request_api.dart';

class RestrictionsFeatureDependencyModuleResolver {
  static void register() {
    _registerNavigation();
    _registerCubit();
    _registerFormConfigs();
  }

  static void _registerNavigation() {
    DependencyProvider.registerLazySingleton<RestrictionsRouter>(() {
      return const RestrictionsRouter();
    });

    DependencyProvider.registerLazySingleton<NavigationRouter>(
      () => DependencyProvider.get<RestrictionsRouter>(),
      instanceName: RestrictionsFeatureNavigationConfig.name,
    );
  }

  static void _registerCubit() {
    DependencyProvider.registerFactory<RestrictionsCubit>(
      () => RestrictionsCubit(
        logger: DependencyProvider.get<Logger>(),
        serviceRequestInteractor:
            DependencyProvider.get<ServiceRequestInteractor>(),
        restrictionsInteractor:
            DependencyProvider.get<RestrictionsInteractor>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        permissionsResolver: DependencyProvider.get<PermissionsResolver>(),
        serviceRequestFormRegister:
            DependencyProvider.get<ServiceRequestFormRegister>(),
        uuid: const Uuid(),
      ),
    );
  }

  static void _registerFormConfigs() {
    final handlers = <ServiceRequestFormHandler>[
      RestrictionsFormConfigsHandlers(
        restrictionsInteractor:
            DependencyProvider.get<RestrictionsInteractor>(),
        customerProvider: DependencyProvider.get<CustomerProvider>(),
        permissionsResolver: DependencyProvider.get<PermissionsResolver>(),
        uuid: const Uuid(),
      ),
    ];

    for (final handler in handlers) {
      DependencyProvider.get<ServiceRequestFormRegister>().register(handler);
    }
  }
}
