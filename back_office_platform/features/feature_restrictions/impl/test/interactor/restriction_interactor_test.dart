import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_feature_error_domain_api/models/api_exception.dart';
import 'package:wio_feature_restrictions_api/restrictions_api.dart';
import 'package:wio_feature_restrictions_impl/src/data/service/exception/restriction_exception_dto.dart';
import 'package:wio_feature_restrictions_impl/src/domain/restrictions_interactor_impl.dart';

import '../mocks.dart';
import '../stubs.dart';

void main() {
  late RestrictionsInteractor lendingInteractor;
  late RestrictionsRepository repository;

  const apiError = RestrictionExceptionDTO(
    content: ApiException<Object>(
      id: 'id',
      code: 'code',
    ),
  );

  setUpAll(() {
    registerFallbackValue(StackTrace.current);
    registerFallbackValue(apiError);
    registerFallbackValue(TestEntities.restrictionInput());
  });

  setUp(() {
    repository = MockRestrictionsRepository();
    lendingInteractor = RestrictionsInteractorImpl(repository: repository);
  });

  group('Fetch Restriction', () {
    test('Fetch Reason List Successfully', () async {
      final reasonTypeOption = TestEntities.reasonTypeOptionModel();

      when(() => repository.getReasonType(restriction: Restriction.debit))
          .justAnswerAsync(
        [reasonTypeOption],
      );

      final reasonTypeList =
          await lendingInteractor.getReasonType(restriction: Restriction.debit);

      verify(() => repository.getReasonType(restriction: Restriction.debit));

      expect(reasonTypeList.length, 1);
      final lendingInfo = reasonTypeList.first;
      expect(lendingInfo.title, reasonTypeOption.title);
    });

    test('Apply Restriction Successfully', () async {
      when(() => repository.applyRestriction(input: any(named: 'input')))
          .justAnswerAsync(
        true,
      );

      final applyResponse = await lendingInteractor.applyRestriction(
        input: TestEntities.restrictionInput(),
      );

      verify(
        () =>
            repository.applyRestriction(input: TestEntities.restrictionInput()),
      );

      expect(applyResponse, true);
    });
  });
}
