import 'package:flutter_test/flutter_test.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_app_common_ui_desktop/feature_app_common_desktop_ui.dart';
import 'package:wio_feature_transactions_api/transactions_api.dart';
import 'package:wio_feature_transactions_ui_desktop/src/transaction_details_mapper/mappers/cashback_transaction_details_mapper.dart';

void main() {
  late CashbackTransactionDetailsMapper mapper;

  setUp(() {
    mapper = CashbackTransactionDetailsMapper();
  });

  test('verify getLayout returns null if transaction is not card type', () {
    final transaction = Transaction(
      id: 'id',
      productType: TransactionProductType.retail,
      accountId: 'accountId',
      transactionType: TransactionType.local,
      transactionSubType: TransactionSubType.localInward,
      transactionIdentifier: 'transactionIdentifier',
      status: TransactionStatus.pending,
      amount: Money.fromNumWithCurrency(100, Currency.aed),
      transactionMode: TransactionMode.withdrawal,
      transactionDateTime: DateTime.now(),
    );

    expect(mapper.getLayout(transaction), isNull);
  });

  test('verify getLayout returns entries if transaction is cashback type', () {
    final transaction = Transaction(
      id: 'id',
      productType: TransactionProductType.retail,
      accountId: 'accountId',
      transactionType: TransactionType.cashback,
      transactionSubType: TransactionSubType.lendingRepayment,
      transactionIdentifier: 'transactionIdentifier',
      status: TransactionStatus.pending,
      amount: Money.fromNumWithCurrency(100, Currency.aed),
      transactionMode: TransactionMode.withdrawal,
      transactionDateTime: DateTime.now(),
      transactionCategory: TransactionCategory.cash,
      details:
          const TransactionCardDetails(declinedReason: 'Transaction Declined'),
    );

    expect(mapper.getLayout(transaction), isA<List<DetailLayoutType>>());
  });
}
