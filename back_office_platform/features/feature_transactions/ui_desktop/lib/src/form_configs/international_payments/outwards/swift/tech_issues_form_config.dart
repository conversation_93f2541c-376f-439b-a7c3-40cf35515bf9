import 'package:wio_feature_service_request_api/service_request_api.dart';
import 'package:wio_feature_transactions_ui_desktop/src/form_configs/international_payments/outwards/swift/base_swift_form_config.dart';

class InternationalOutwardsPaymentTechIssuesFormConfig
    extends BaseInternationalOutwardsSwiftPaymentFormConfig {
  @override
  Map<FormFieldId, FormField> fields(Object? entity) => {
        ...super.fields(entity),
        'reason': const FreeTextField(
          label: 'Reason For Tech Issue',
        ),
      };

  @override
  String get processName => 'INT_OUTWARD_TRANSFERS_TECH_ISSUES';

  @override
  String get title => 'International Transfers - Tech issues';
}
