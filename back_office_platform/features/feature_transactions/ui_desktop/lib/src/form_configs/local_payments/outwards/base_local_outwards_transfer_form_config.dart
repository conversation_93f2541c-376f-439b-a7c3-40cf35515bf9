import 'package:wio_feature_service_request_api/service_request_api.dart';
import 'package:wio_feature_transactions_api/transactions_api.dart';
import 'package:wio_feature_transactions_ui_desktop/src/utils/extensions/date_time_extensions.dart';
import 'package:wio_feature_transactions_ui_desktop/src/utils/extensions/transaction_extensions.dart';

class BaseLocalOutwardsTransferFormConfig extends FormConfig {
  @override
  List<EntityField> entityFields(Object? entity) {
    final transaction = entity as Transaction;
    TransactionLocalDetails? details;
    if (transaction.details is TransactionLocalDetails) {
      details = transaction.details as TransactionLocalDetails;
    }

    final transactionDate = transaction.transactionDateTime;

    return [
      EntityField(
        label: 'FTS Reference Number',
        value: transaction.transactionIdentifier,
        payloadFieldName: 'ftsReferenceNumber',
      ),
      EntityField(
        label: 'Transaction ID',
        value: transaction.transactionIdentifier,
        payloadFieldName: 'transactionId',
      ),
      EntityField(
        label: 'Transaction Date',
        value: transactionDate != null ? transactionDate.formattedDate : '-',
        payloadFieldName: 'transactionDate',
      ),
      EntityField(
        label: 'Transfer Fee',
        value: details?.feeData?.total?.toCodeOnRightFormat() ?? '-',
        payloadFieldName: 'transferFee',
      ),
      EntityField(
        label: 'Transaction Amount',
        value: transaction.amount.toCodeOnRightFormat(),
        payloadFieldName: 'transactionAmount',
      ),
      EntityField(
        label: 'Account Number',
        value: transaction.accountId,
        payloadFieldName: 'accountNumber',
      ),
      EntityField(
        label: 'Outward Transaction Status',
        value: transaction.status.displayName,
        payloadFieldName: 'outwardTransactionStatus',
      ),
    ];
  }

  @override
  Map<FormFieldId, FormField> fields(Object? entity) => {
        ...super.fields(entity),
      };

  @override
  String get processName => throw UnimplementedError();

  @override
  String get title => throw UnimplementedError();

  @override
  bool get notesRequired => false;

  @override
  Future<Payload?> preCaseCreationCallback(Payload payload) async => payload;

  @override
  List<Disclaimers> alertField() => [];
}
