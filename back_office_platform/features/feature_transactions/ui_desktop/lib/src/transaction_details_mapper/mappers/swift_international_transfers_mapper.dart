import 'package:wio_feature_app_common_ui_desktop/feature_app_common_desktop_ui.dart';
import 'package:wio_feature_transactions_api/domain/model/model.dart';
import 'package:wio_feature_transactions_api/transactions_api.dart';
import 'package:wio_feature_transactions_ui_desktop/src/transaction_details_mapper/transactions_ui_mapper.dart';

class InternationalSwiftTransferDetailsUiMapper
    implements TransactionsUiMapper {
  @override
  List<DetailLayoutType>? getLayout(Transaction transaction) {
    if ([TransactionSubType.swiftInward, TransactionSubType.swiftOutward]
            .contains(transaction.transactionSubType) ==
        false) {
      return null;
    }

    final details = transaction.details;

    if (details == null) {
      return null;
    }

    final transferDetails = details as TransactionInternationalDetails;

    return [
      DetailLayoutType.doubleDetail(
        firstDetail: DetailModel.simple(
          label: 'Transfer Method',
          value: _getTransferMethod(transaction.transactionSubType) ?? '-',
        ),
        secondDetail: DetailModel.simple(
          label: 'Transfer Type',
          value: transaction.transactionMode == TransactionMode.deposit
              ? 'Inward'
              : 'Outward',
        ),
      ),
      DetailLayoutType.doubleDetail(
        firstDetail: DetailModel.simple(
          label: 'Recipient Account Number',
          value: transferDetails.accountNumber.orNotAvailable,
        ),
        secondDetail: DetailModel.simple(
          label: 'Recipient Bank Name',
          value: transferDetails.bankName.orNotAvailable,
        ),
      ),
      DetailLayoutType.doubleDetail(
        firstDetail: DetailModel.simple(
          label: 'Recipient Country',
          value: transferDetails.targetCountry.orNotAvailable,
        ),
        secondDetail: DetailModel.simple(
          label: 'Recipient Country Code',
          value: transferDetails.targetCountryCode.orNotAvailable,
        ),
      ),
      DetailLayoutType.doubleDetail(
        firstDetail: DetailModel.simple(
          label: 'Recipient Bank Swift Code',
          value: transferDetails.swiftCode.orNotAvailable,
        ),
        secondDetail: DetailModel.simple(
          label: 'Fee Charging Type',
          value: transferDetails.feeChargingType.orNotAvailable,
        ),
      ),
      DetailLayoutType.doubleDetail(
        firstDetail: DetailModel.simple(
          label: 'Recipient Amount',
          value: transferDetails.targetAmount.orMoneyNotAvailable,
        ),
        secondDetail: DetailModel.simple(
          label: 'Total Fees',
          value: transferDetails.feeData == null
              ? '-'
              : transferDetails.feeData!.total.orMoneyNotAvailable,
        ),
      ),
      ..._getFeeDetailLayouts(transferDetails.feeData?.transferFees ?? []),
      DetailLayoutType.doubleDetail(
        firstDetail: DetailModel.simple(
          label: 'Estimated Completed Time',
          value: details.estimatedDateTime.orNotAvailable,
        ),
        secondDetail: DetailModel.simple(
          label: 'Completed Time',
          value: details.completedDateTime.formattedDateWithTimeOrNotAvailable,
        ),
      ),
      DetailLayoutType.doubleDetail(
        firstDetail: DetailModel.simple(
          label: 'Purpose Code',
          value: details.purposeCode.orNotAvailable,
        ),
        secondDetail: DetailModel.simple(
          label: 'Purpose Description',
          value: details.purposeDescription.orNotAvailable,
        ),
      ),
      DetailLayoutType.doubleDetail(
        firstDetail: DetailModel.simple(
          label: 'UETR Number',
          value: details.transferId.orNotAvailable,
        ),
      ),
      DetailLayoutType.fullWidth(
        DetailModel.simple(
          label: 'Sender notes',
          value: transaction.customerNotes.orNotAvailable,
        ),
      ),
    ];
  }

  List<DetailLayoutType> _getFeeDetailLayouts(List<TransferFee> fees) {
    return List.generate((fees.length / 2).ceil(), (index) {
      final firstFeeIndex = index * 2;
      final secondFeeIndex = firstFeeIndex + 1;

      final firstFee = fees[firstFeeIndex];
      final secondFee =
          secondFeeIndex < fees.length ? fees[secondFeeIndex] : null;

      return DetailLayoutType.doubleDetail(
        firstDetail: DetailModel.simple(
          label: '${feeTypeString(firstFee.feeType)} Fee',
          value: firstFee.total.orMoneyNotAvailable,
        ),
        secondDetail: secondFee == null
            ? null
            : DetailModel.simple(
                label: '${feeTypeString(secondFee.feeType)} Fee',
                value: secondFee.total.orMoneyNotAvailable,
              ),
      );
    });
  }

  String feeTypeString(FeeType? feeType) {
    switch (feeType) {
      case FeeType.wio:
        return 'Wio Bank';
      case FeeType.wioCorrespondentBank:
        return 'Correspondent Bank';
      case FeeType.wise:
        return 'Wise';
      default:
        return 'Unknown';
    }
  }

  String? _getTransferMethod(TransactionSubType? subType) {
    if ([TransactionSubType.swiftOutward, TransactionSubType.swiftInward]
        .contains(subType)) {
      return 'SWIFT';
    } else if (TransactionSubType.wiseOutward == subType) {
      return 'Wise';
    } else {
      return null;
    }
  }
}
