import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:ui/ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_common_error_handler_api/common_error_handler_api.dart';
import 'package:wio_feature_disputes_api/disputes_api.dart';
import 'package:wio_feature_disputes_api/feature_toggles/disputes_feature_toggles.dart';
import 'package:wio_feature_tasks_ui_desktop/feature_tasks_desktop_ui.modular.dart';
import 'package:wio_feature_transactions_api/transactions_api.dart';
import 'package:wio_feature_transactions_ui_desktop/src/screens/transaction_detail_modal/dispute_info/cubit/dispute_info_state.dart';

class DisputeInfoCubit extends BaseCubit<DisputeInfoState> {
  final DisputesInteractor _interactor;
  final CommonErrorHandler _commonErrorHandler;
  final FeatureToggleProvider _featureToggleProvider;
  final NavigationProvider _navigationProvider;

  DisputeInfoCubit({
    required DisputesInteractor interactor,
    required CommonErrorHandler commonErrorHandler,
    required FeatureToggleProvider featureToggleProvider,
    required NavigationProvider navigationProvider,
  })  : _interactor = interactor,
        _commonErrorHandler = commonErrorHandler,
        _featureToggleProvider = featureToggleProvider,
        _navigationProvider = navigationProvider,
        super(const DisputeInfoState.empty());

  bool get _isCardDisputesFeatureEnabled => _featureToggleProvider.get(
        DisputesFeatureToggles.isCardDisputesFeatureEnabled,
      );

  Future<void> initialize({required Transaction transaction}) async {
    try {
      if (!_isCardDisputesFeatureEnabled) return;

      String? disputeId;
      final transactionDetails = transaction.details;

      if (transactionDetails is TransactionCardDetails) {
        disputeId = transactionDetails.disputeId;
      }

      if (disputeId == null) {
        return;
      }

      safeEmit(const DisputeInfoState.loading());

      final disputeInformations =
          await _interactor.getDisputeById(disputeId: disputeId);

      safeEmit(
        DisputeInfoState.loaded(
          isActiveDispute: disputeInformations.status.isActiveDispute,
          disputeInfo: disputeInformations,
        ),
      );
    } on Object catch (e, st) {
      _commonErrorHandler.handleError(
        error: e,
        stackTrace: st,
      );
      safeEmit(const DisputeInfoState.empty());
    }
  }

  void navigateToCaseDetail(String caseId) {
    _navigationProvider
      ..goBack()
      ..push(CaseDetailNavigationConfig(caseId: caseId));
  }

  @override
  String toString() => 'DisputeInfoCubit{}';
}
