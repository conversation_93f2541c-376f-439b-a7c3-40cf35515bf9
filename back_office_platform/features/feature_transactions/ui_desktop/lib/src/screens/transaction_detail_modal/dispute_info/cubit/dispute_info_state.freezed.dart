// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dispute_info_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DisputeInfoState {
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DisputeInfoLoadingState value) loading,
    required TResult Function(DisputeInfoEmptyState value) empty,
    required TResult Function(DisputeInfoLoadedState value) loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DisputeInfoLoadingState value)? loading,
    TResult? Function(DisputeInfoEmptyState value)? empty,
    TResult? Function(DisputeInfoLoadedState value)? loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DisputeInfoLoadingState value)? loading,
    TResult Function(DisputeInfoEmptyState value)? empty,
    TResult Function(DisputeInfoLoadedState value)? loaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DisputeInfoStateCopyWith<$Res> {
  factory $DisputeInfoStateCopyWith(
          DisputeInfoState value, $Res Function(DisputeInfoState) then) =
      _$DisputeInfoStateCopyWithImpl<$Res, DisputeInfoState>;
}

/// @nodoc
class _$DisputeInfoStateCopyWithImpl<$Res, $Val extends DisputeInfoState>
    implements $DisputeInfoStateCopyWith<$Res> {
  _$DisputeInfoStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DisputeInfoState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$DisputeInfoLoadingStateImplCopyWith<$Res> {
  factory _$$DisputeInfoLoadingStateImplCopyWith(
          _$DisputeInfoLoadingStateImpl value,
          $Res Function(_$DisputeInfoLoadingStateImpl) then) =
      __$$DisputeInfoLoadingStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DisputeInfoLoadingStateImplCopyWithImpl<$Res>
    extends _$DisputeInfoStateCopyWithImpl<$Res, _$DisputeInfoLoadingStateImpl>
    implements _$$DisputeInfoLoadingStateImplCopyWith<$Res> {
  __$$DisputeInfoLoadingStateImplCopyWithImpl(
      _$DisputeInfoLoadingStateImpl _value,
      $Res Function(_$DisputeInfoLoadingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DisputeInfoState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DisputeInfoLoadingStateImpl implements DisputeInfoLoadingState {
  const _$DisputeInfoLoadingStateImpl();

  @override
  String toString() {
    return 'DisputeInfoState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DisputeInfoLoadingStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DisputeInfoLoadingState value) loading,
    required TResult Function(DisputeInfoEmptyState value) empty,
    required TResult Function(DisputeInfoLoadedState value) loaded,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DisputeInfoLoadingState value)? loading,
    TResult? Function(DisputeInfoEmptyState value)? empty,
    TResult? Function(DisputeInfoLoadedState value)? loaded,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DisputeInfoLoadingState value)? loading,
    TResult Function(DisputeInfoEmptyState value)? empty,
    TResult Function(DisputeInfoLoadedState value)? loaded,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class DisputeInfoLoadingState implements DisputeInfoState {
  const factory DisputeInfoLoadingState() = _$DisputeInfoLoadingStateImpl;
}

/// @nodoc
abstract class _$$DisputeInfoEmptyStateImplCopyWith<$Res> {
  factory _$$DisputeInfoEmptyStateImplCopyWith(
          _$DisputeInfoEmptyStateImpl value,
          $Res Function(_$DisputeInfoEmptyStateImpl) then) =
      __$$DisputeInfoEmptyStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DisputeInfoEmptyStateImplCopyWithImpl<$Res>
    extends _$DisputeInfoStateCopyWithImpl<$Res, _$DisputeInfoEmptyStateImpl>
    implements _$$DisputeInfoEmptyStateImplCopyWith<$Res> {
  __$$DisputeInfoEmptyStateImplCopyWithImpl(_$DisputeInfoEmptyStateImpl _value,
      $Res Function(_$DisputeInfoEmptyStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DisputeInfoState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DisputeInfoEmptyStateImpl implements DisputeInfoEmptyState {
  const _$DisputeInfoEmptyStateImpl();

  @override
  String toString() {
    return 'DisputeInfoState.empty()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DisputeInfoEmptyStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DisputeInfoLoadingState value) loading,
    required TResult Function(DisputeInfoEmptyState value) empty,
    required TResult Function(DisputeInfoLoadedState value) loaded,
  }) {
    return empty(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DisputeInfoLoadingState value)? loading,
    TResult? Function(DisputeInfoEmptyState value)? empty,
    TResult? Function(DisputeInfoLoadedState value)? loaded,
  }) {
    return empty?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DisputeInfoLoadingState value)? loading,
    TResult Function(DisputeInfoEmptyState value)? empty,
    TResult Function(DisputeInfoLoadedState value)? loaded,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty(this);
    }
    return orElse();
  }
}

abstract class DisputeInfoEmptyState implements DisputeInfoState {
  const factory DisputeInfoEmptyState() = _$DisputeInfoEmptyStateImpl;
}

/// @nodoc
abstract class _$$DisputeInfoLoadedStateImplCopyWith<$Res> {
  factory _$$DisputeInfoLoadedStateImplCopyWith(
          _$DisputeInfoLoadedStateImpl value,
          $Res Function(_$DisputeInfoLoadedStateImpl) then) =
      __$$DisputeInfoLoadedStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool isActiveDispute, DisputeInfo disputeInfo});

  $DisputeInfoCopyWith<$Res> get disputeInfo;
}

/// @nodoc
class __$$DisputeInfoLoadedStateImplCopyWithImpl<$Res>
    extends _$DisputeInfoStateCopyWithImpl<$Res, _$DisputeInfoLoadedStateImpl>
    implements _$$DisputeInfoLoadedStateImplCopyWith<$Res> {
  __$$DisputeInfoLoadedStateImplCopyWithImpl(
      _$DisputeInfoLoadedStateImpl _value,
      $Res Function(_$DisputeInfoLoadedStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DisputeInfoState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isActiveDispute = null,
    Object? disputeInfo = null,
  }) {
    return _then(_$DisputeInfoLoadedStateImpl(
      isActiveDispute: null == isActiveDispute
          ? _value.isActiveDispute
          : isActiveDispute // ignore: cast_nullable_to_non_nullable
              as bool,
      disputeInfo: null == disputeInfo
          ? _value.disputeInfo
          : disputeInfo // ignore: cast_nullable_to_non_nullable
              as DisputeInfo,
    ));
  }

  /// Create a copy of DisputeInfoState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DisputeInfoCopyWith<$Res> get disputeInfo {
    return $DisputeInfoCopyWith<$Res>(_value.disputeInfo, (value) {
      return _then(_value.copyWith(disputeInfo: value));
    });
  }
}

/// @nodoc

class _$DisputeInfoLoadedStateImpl implements DisputeInfoLoadedState {
  const _$DisputeInfoLoadedStateImpl(
      {required this.isActiveDispute, required this.disputeInfo});

  @override
  final bool isActiveDispute;
  @override
  final DisputeInfo disputeInfo;

  @override
  String toString() {
    return 'DisputeInfoState.loaded(isActiveDispute: $isActiveDispute, disputeInfo: $disputeInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DisputeInfoLoadedStateImpl &&
            (identical(other.isActiveDispute, isActiveDispute) ||
                other.isActiveDispute == isActiveDispute) &&
            (identical(other.disputeInfo, disputeInfo) ||
                other.disputeInfo == disputeInfo));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isActiveDispute, disputeInfo);

  /// Create a copy of DisputeInfoState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DisputeInfoLoadedStateImplCopyWith<_$DisputeInfoLoadedStateImpl>
      get copyWith => __$$DisputeInfoLoadedStateImplCopyWithImpl<
          _$DisputeInfoLoadedStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DisputeInfoLoadingState value) loading,
    required TResult Function(DisputeInfoEmptyState value) empty,
    required TResult Function(DisputeInfoLoadedState value) loaded,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DisputeInfoLoadingState value)? loading,
    TResult? Function(DisputeInfoEmptyState value)? empty,
    TResult? Function(DisputeInfoLoadedState value)? loaded,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DisputeInfoLoadingState value)? loading,
    TResult Function(DisputeInfoEmptyState value)? empty,
    TResult Function(DisputeInfoLoadedState value)? loaded,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class DisputeInfoLoadedState implements DisputeInfoState {
  const factory DisputeInfoLoadedState(
      {required final bool isActiveDispute,
      required final DisputeInfo disputeInfo}) = _$DisputeInfoLoadedStateImpl;

  bool get isActiveDispute;
  DisputeInfo get disputeInfo;

  /// Create a copy of DisputeInfoState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DisputeInfoLoadedStateImplCopyWith<_$DisputeInfoLoadedStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
