{"openapi": "3.0.1", "info": {"title": "SentNotification API", "version": "1.0", "description": "Customer Servicing Service"}, "servers": [{"url": "http://app-customer-customer-servicing-service-java-ms"}], "paths": {"/api/v1/notifications": {"get": {"tags": ["notification-controller"], "operationId": "getAllByUserId", "parameters": [{"name": "userId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer"}, "example": 0}, {"name": "size", "in": "query", "schema": {"type": "integer"}, "example": 50}], "responses": {"200": {"description": "Ok", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageNotification"}}}}, "400": {"description": "Invalid request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PageNotification"}}}}}}}}, "components": {"schemas": {"Notification": {"type": "object", "properties": {"id": {"type": "string"}, "operationId": {"type": "string"}, "recipient": {"$ref": "#/components/schemas/Recipient"}, "channelTypes": {"type": "array", "items": {"type": "string"}}, "time": {"type": "string", "format": "date-time"}, "templateId": {"type": "string"}, "eventName": {"type": "string"}, "provider": {"type": "string"}, "deliveryStatus": {"type": "string"}, "deliveryTime": {"type": "string", "format": "date-time"}, "deliveryStatusCode": {"type": "integer", "format": "int32"}, "correlationId": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedOn": {"type": "string", "format": "date-time"}}, "description": ""}, "PageNotification": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int64"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "numberOfElements": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"$ref": "#/components/schemas/SortObject"}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "PageableObject": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "paged": {"type": "boolean"}, "unpaged": {"type": "boolean"}, "offset": {"type": "integer", "format": "int64"}, "sort": {"$ref": "#/components/schemas/SortObject"}}}, "Recipient": {"type": "object", "properties": {"userId": {"type": "string"}, "email": {"type": "string"}, "mobile": {"type": "string"}, "templateParams": {"type": "object", "additionalProperties": {"type": "string"}}}, "description": ""}, "SortObject": {"type": "object", "properties": {"sorted": {"type": "boolean"}, "unsorted": {"type": "boolean"}, "empty": {"type": "boolean"}}, "description": ""}}}}