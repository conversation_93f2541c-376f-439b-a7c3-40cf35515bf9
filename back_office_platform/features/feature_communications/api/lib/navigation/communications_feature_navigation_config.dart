import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

/// Navigation configuration for the communication feature
///
/// This can be used to navigate to the root of the communication feature.
class CommunicationsFeatureNavigationConfig extends FeatureNavigationConfig {
  /// A screen navigation config of an internal screen of the feature can be
  /// provided to navigate to the specific screen
  final ScreenNavigationConfig? destination;

  /// Constructor
  const CommunicationsFeatureNavigationConfig({
    this.destination,
  }) : super(name);

  /// Name of feature that will be used in the hash router
  static const name = 'communications_feature';

  @override
  String toString() => 'CommunicationsFeatureNavigationConfig';
}
