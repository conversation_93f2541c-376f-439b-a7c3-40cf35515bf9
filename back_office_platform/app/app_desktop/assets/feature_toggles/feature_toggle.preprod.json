{"feature_toggles": [{"flag": "is_cards_feature_enabled", "value": "TRUE", "category": "Preprod", "label": "Enable/disable cards tab in navigation rail", "type": "boolean"}, {"flag": "is_accounts_feature_enabled", "value": "TRUE", "category": "Preprod", "label": "Enable/disable accounts tab in navigation rail", "type": "boolean"}, {"flag": "is_service_request_feature_enabled_2024_30_05", "value": "FALSE", "category": "Service Request", "label": "Enable/disable service request feature", "type": "boolean"}, {"flag": "is_transactions_feature_enabled_20240522", "value": "FALSE", "category": "Transactions", "label": "Enable/disable transactions tab in navigation rail", "type": "boolean"}, {"flag": "is_transfer_tracker_enabled_20250318", "value": "TRUE", "category": "Transactions", "label": "Enable/disable transfers tab in transaction details", "type": "boolean"}, {"flag": "is_timeline_of_onboarding_feature_enabled_20240611", "value": "FALSE", "category": "Onboarding", "label": "Enable/disable timeline of onboarding feature", "type": "boolean"}, {"flag": "is_deliveries_in_dashboard_enabled_20240620", "value": "FALSE", "category": "Dashboard", "label": "Enable/disable deliveries in the dashboard", "type": "boolean"}, {"flag": "is_entities_of_onboarding_feature_enabled_20240621", "value": "FALSE", "category": "Onboarding", "label": "Enable/disable business of onboarding feature", "type": "boolean"}, {"flag": "is_customer_details_enabled_20240703", "value": "FALSE", "category": "Customer", "label": "Enable/disable customer details feature", "type": "boolean"}, {"flag": "is_documents_of_onboarding_feature_enabled_20240621", "value": "FALSE", "category": "Customer", "label": "Enable/disable customer documents feature", "type": "boolean"}, {"flag": "is_onboarding_comments_enabled_20250501", "value": "FALSE", "category": "Customer", "label": "Enable/disable onboarding comments for customer", "type": "boolean"}, {"flag": "is_subscription_plan_enabled_20240719", "value": "TRUE", "category": "Subscription", "label": "Enable/disable subscription plan info on header", "type": "boolean"}, {"flag": "is_standalone_search_enabled_20241009", "value": "FALSE", "category": "Subscription", "label": "Is customer standalone search enabled", "type": "boolean"}, {"flag": "is_lending_feature_enabled_20241003", "value": "TRUE", "category": "Lending", "label": "Enable/disable lending tab in navigation rail", "type": "boolean"}, {"flag": "is_card_replacement_enabled_20241008", "value": "TRUE", "category": "Cards", "label": "Enable/disable cards replacement in details page", "type": "boolean"}, {"flag": "is_wps_enabled_20241014", "value": "FALSE", "category": "WPS", "label": "Enable/disable WPS feature", "type": "boolean"}, {"flag": "is_invoices_enabled_20250121", "value": "FALSE", "category": "Invoices", "label": "Enable/disable Invoices feature", "type": "boolean"}, {"flag": "enable_tasks_wio360_20241017", "value": "FALSE", "category": "Task Details", "label": "Enable/disable task details feature for Wio360", "type": "boolean"}, {"flag": "is_cash_cheque_feature_enabled_20241224", "value": "TRUE", "category": "Cash & Cheques", "label": "Enable/disable cash and cheques tab in navigation rail", "type": "boolean"}, {"flag": "is_cheque_history_enabled_20250414", "value": "FALSE", "category": "Cash & Cheques", "label": "Enable/disable cheques history", "type": "boolean"}, {"flag": "is_communication_feature_enabled_20250114", "value": "FALSE", "category": "Communication", "label": "Enable/disable communication tab in navigation rail", "type": "boolean"}, {"flag": "is_invest_dashboard_enabled_20250115", "value": "TRUE", "category": "Invest", "label": "Enable/disable invest dashboard in navigation rail", "type": "boolean"}, {"flag": "is_invest_market_onboarding_navigation_rail_enabled_20250115", "value": "TRUE", "category": "Invest", "label": "Enable/disable invest market onboarding in navigation rail", "type": "boolean"}, {"flag": "is_invest_customer_info_navigation_rail_enabled_20250116", "value": "TRUE", "category": "Invest", "label": "Enable/disable invest customer info navigation rail", "type": "boolean"}, {"flag": "is_invest_portfolio_enabled_20250122", "value": "TRUE", "category": "Invest", "label": "Enable/disable invest portfolio", "type": "boolean"}, {"flag": "is_invest_transactions_enabled_20250122", "value": "TRUE", "category": "Invest", "label": "Enable/disable invest transactions", "type": "boolean"}, {"flag": "is_letters_statements_feature_enabled_20250207", "value": "FALSE", "category": "Letters and Statements", "label": "Enable/disable letters and statements", "type": "boolean"}, {"flag": "is_customer_logins_enabled_20250219", "value": "FALSE", "category": "Customer <PERSON>", "label": "Enable/disable customer logins statement", "type": "boolean"}, {"flag": "is_etihad_saving_space_enabled_20250304", "value": "TRUE", "category": "Etihad Saving Space", "label": "Enable/disable etihad saving space", "type": "boolean"}, {"flag": "is_restriction_feature_enabled_20250401", "value": "TRUE", "category": "Restrictions", "label": "Enable/disable restriction feature", "type": "boolean"}, {"flag": "is_cqs_feature_enabled_20250501", "value": "TRUE", "category": "CQS", "label": "Enable/disable cqs feature", "type": "boolean"}, {"flag": "is_disputes_feature_enabled_20250527", "value": "TRUE", "category": "Dispute", "label": "Enable/disable Dispute in transactions modal and sr flow", "type": "boolean"}, {"flag": "is_installments_enabled_20250507", "value": "TRUE", "category": "Lending", "label": "Enable/disable installments feature", "type": "boolean"}, {"flag": "is_locked_credit_account_alert_enabled_20250123", "value": "TRUE", "category": "Lending", "label": "Enable/disable locked credit account alert", "type": "boolean"}]}