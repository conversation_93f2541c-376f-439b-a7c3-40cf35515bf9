import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_auth_api/domain/auth_interactor.dart';

class AuthorizationHeaderInterceptor extends RestApiInterceptor {
  static const authorizationHeader = 'Authorization';
  final AuthInteractor _authInteractor;

  AuthorizationHeaderInterceptor({
    required AuthInteractor authInteractor,
  }) : _authInteractor = authInteractor;

  @override
  Future<RestApiRequest> onRequest(RestApiRequest request) async {
    final headers = Map.of(request.headers);
    final accessToken = await _authInteractor.getAccessToken();
    headers[authorizationHeader] = 'Bearer $accessToken';

    return RestApiRequest(
      request.path,
      method: request.method,
      queryParameters: request.queryParameters,
      body: request.body,
      headers: headers,
      savePath: request.savePath,
    );
  }
}
