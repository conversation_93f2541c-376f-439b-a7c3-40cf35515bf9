import 'dart:async';

import 'package:back_office_platform/di/clearables_dependencies_module_resolver.dart';
import 'package:back_office_platform/di/deferred_localization_source.dart';
import 'package:back_office_platform/di/modules_map.g.dart';
import 'package:back_office_platform/navigation/back_office_navigation_provider.dart';
import 'package:back_office_platform/navigation/back_office_web_navigator_router.dart';
import 'package:di/di.dart';
import 'package:domain/stream/exhaust_stream_executor.dart';
import 'package:feature_session_monitoring_impl/feature_session_monitoring_impl.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:ui/l10n/common_localizations.g.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_accounts_api/accounts_feature_api.dart';
import 'package:wio_feature_accounts_impl/accounts_feature_impl.dart'
    deferred as accounts_impl;
import 'package:wio_feature_accounts_ui_desktop/feature_accounts_ui_desktop.dart'
    deferred as accounts_ui;
import 'package:wio_feature_app_common_impl/feature_app_common_impl.dart'
    deferred as app_common_impl;
import 'package:wio_feature_app_common_ui_desktop/feature_app_common_desktop_ui.dart';
import 'package:wio_feature_auth_api/auth_api.dart';
import 'package:wio_feature_auth_impl/feature_auth_impl.dart'
    deferred as auth_impl;
import 'package:wio_feature_auth_ui_desktop/feature_auth_desktop_ui.dart'
    deferred as auth_ui;
import 'package:wio_feature_backoffice_env_api/backoffice_env_api.dart';
import 'package:wio_feature_cards_api/card_feature_api.dart';
import 'package:wio_feature_cards_api/navigation/cards_feature_navigation_config.dart';
import 'package:wio_feature_cards_impl/cards_feature_impl.dart'
    deferred as cards_impl;
import 'package:wio_feature_cards_ui_desktop/feature_cards_ui_desktop.dart'
    deferred as cards_ui;
import 'package:wio_feature_cases_api/navigation/tasks_feature_navigation_config.dart';
import 'package:wio_feature_cases_impl/feature_tasks_impl.dart'
    deferred as cases_impl;
import 'package:wio_feature_cash_cheques_api/cash_cheques_api.dart';
import 'package:wio_feature_cash_cheques_impl/feature_cash_cheques_impl.dart'
    deferred as cash_cheques_impl;
import 'package:wio_feature_cash_cheques_ui_desktop/feature_cash_cheques_desktop_ui.dart'
    deferred as cash_cheques_ui;
import 'package:wio_feature_common_error_handler_impl/feature_common_error_handler_impl.dart';
import 'package:wio_feature_common_error_handler_ui/feature_common_error_handler_mobile_ui.dart';
import 'package:wio_feature_common_toast_message_ui/feature_toast_message_ui.dart';
import 'package:wio_feature_communications_api/navigation/communications_feature_navigation_config.dart';
import 'package:wio_feature_communications_impl/feature_communications_impl.dart'
    deferred as communications_impl;
import 'package:wio_feature_communications_ui_desktop/feature_communications_desktop_ui.dart'
    deferred as communications_ui;
import 'package:wio_feature_cqs_api/navigation/cqs_feature_navigation_config.dart';
import 'package:wio_feature_cqs_impl/feature_cqs_impl.dart'
    deferred as cqs_impl;
import 'package:wio_feature_cqs_ui_desktop/feature_cqs_desktop_ui.dart'
    deferred as cqs_ui;
import 'package:wio_feature_customer_api/customer_api.modular.dart';
import 'package:wio_feature_customer_idv_api/navigation/customer_idv_feature_navigation_config.dart';
import 'package:wio_feature_customer_idv_impl/feature_customer_idv_impl.dart'
    deferred as customer_idv_impl;
import 'package:wio_feature_customer_idv_ui_desktop/feature_customer_idv_desktop_ui.dart'
    deferred as customer_idv_ui;
import 'package:wio_feature_customer_impl/feature_customer_impl.dart'
    deferred as customer_impl;
import 'package:wio_feature_customer_logins_api/navigation/customer_logins_feature_navigation_config.dart';
import 'package:wio_feature_customer_logins_impl/feature_customer_logins_impl.dart'
    deferred as customer_logins_impl;
import 'package:wio_feature_customer_logins_ui_desktop/feature_customer_logins_desktop_ui.dart'
    deferred as customer_logins_ui;
import 'package:wio_feature_customer_ui_desktop/feature_customer_desktop_ui.dart'
    deferred as customer_ui;
import 'package:wio_feature_dashboard_api/dashboard_api.modular.dart';
import 'package:wio_feature_dashboard_impl/feature_dashboard_impl.dart'
    deferred as dashboard_impl;
import 'package:wio_feature_dashboard_ui_desktop/feature_dashboard_desktop_ui.dart'
    deferred as dashboard_ui;
import 'package:wio_feature_dev_menu_impl/feature_dev_menu_impl.dart';
import 'package:wio_feature_dev_menu_ui_desktop/feature_dev_menu_desktop_ui.dart';
import 'package:wio_feature_disputes_api/disputes_api.dart';
import 'package:wio_feature_disputes_impl/feature_disputes_impl.dart'
    deferred as disputes_impl;
import 'package:wio_feature_disputes_ui_desktop/feature_disputes_desktop_ui.dart'
    deferred as disputes_ui;
import 'package:wio_feature_family_banking_api/family_banking_api.dart';
import 'package:wio_feature_family_banking_impl/feature_family_banking_impl.dart'
    deferred as family_banking_impl;
import 'package:wio_feature_family_banking_ui_desktop/feature_family_banking_desktop_ui.dart'
    deferred as family_banking_ui;
import 'package:wio_feature_files_impl/feature_files_impl.dart'
    deferred as files_impl;
import 'package:wio_feature_invest_customer_api/invest_customer_api.dart';
import 'package:wio_feature_invest_customer_impl/feature_invest_customer_impl.dart'
    deferred as invest_customer_impl;
import 'package:wio_feature_invest_customer_ui_desktop/feature_invest_customer_desktop_ui.dart'
    deferred as invest_customer_ui;
import 'package:wio_feature_invest_dashboard_api/invest_dashboard_api.dart';
import 'package:wio_feature_invest_dashboard_impl/feature_invest_dashboard_impl.dart'
    deferred as invest_dashboard_impl;
import 'package:wio_feature_invest_dashboard_ui_desktop/feature_invest_dashboard_desktop_ui.dart'
    deferred as invest_dashboard_ui;
import 'package:wio_feature_invest_portfolio_api/invest_portfolio_api.dart';
import 'package:wio_feature_invest_portfolio_impl/feature_invest_portfolio_impl.dart'
    deferred as invest_portfolio_impl;
import 'package:wio_feature_invest_portfolio_ui_desktop/feature_invest_portfolio_desktop_ui.dart'
    deferred as invest_portfolio_ui;
import 'package:wio_feature_invoices_api/navigation/invoices_feature_navigation_config.dart';
import 'package:wio_feature_invoices_impl/feature_invoices_impl.dart'
    deferred as invoices_impl;
import 'package:wio_feature_invoices_ui_desktop/feature_invoices_desktop_ui.dart'
    deferred as invoices_ui;
import 'package:wio_feature_lending_api/navigation/lending_feature_navigation_config.dart';
import 'package:wio_feature_lending_impl/feature_lending_impl.dart'
    deferred as lending_impl;
import 'package:wio_feature_lending_ui_desktop/feature_lending_desktop_ui.dart'
    deferred as lending_ui;
import 'package:wio_feature_letters_statements_api/letters_statements_api.dart';
import 'package:wio_feature_letters_statements_impl/feature_letters_statements_impl.dart'
    deferred as letters_statements_impl;
import 'package:wio_feature_letters_statements_ui_desktop/feature_letters_statements_desktop_ui.dart'
    deferred as letters_statements_ui;
import 'package:wio_feature_mu_impl/feature_mu_impl.dart' deferred as mu_impl;
import 'package:wio_feature_mu_ui_desktop/feature_mu_desktop_ui.dart'
    deferred as mu_ui_desktop;
import 'package:wio_feature_navigation_rail_ui_desktop/feature_navigation_rail_desktop_ui.dart';
import 'package:wio_feature_onboarding_api/navigation/onboarding_feature_navigation_config.dart';
import 'package:wio_feature_onboarding_impl/feature_onboarding_impl.dart'
    deferred as onboarding_impl;
import 'package:wio_feature_onboarding_ui_desktop/feature_onboarding_desktop_ui.dart'
    deferred as onboarding_ui;
import 'package:wio_feature_permissions_impl/feature_permissions_impl.dart'
    deferred as permissions_impl;
// ignore: unused_import
import 'package:wio_feature_permissions_ui_desktop/feature_permissions_desktop_ui.dart'
    deferred as permissions_ui;
import 'package:wio_feature_restrictions_api/restrictions_api.dart';
import 'package:wio_feature_restrictions_impl/feature_restrictions_impl.dart'
    deferred as restrictions_impl;
import 'package:wio_feature_restrictions_ui_desktop/feature_restrictions_desktop_ui.dart'
    deferred as restriction_ui;
import 'package:wio_feature_saving_space_impl/saving_space_feature_impl.dart'
    deferred as saving_space_impl;
import 'package:wio_feature_saving_space_ui_desktop/feature_saving_space_ui_desktop.dart'
    deferred as saving_space_ui;
import 'package:wio_feature_saving_spaces_api/navigation/saving_space_feature_navigation_config.dart';
import 'package:wio_feature_service_request_impl/feature_service_request_impl.dart'
    deferred as sr_impl;
import 'package:wio_feature_service_request_ui_desktop/feature_service_request_desktop_ui.dart'
    deferred as sr_ui_desktop;
import 'package:wio_feature_subscription_impl/subscription_feature_impl.dart'
    deferred as subscription_impl;
import 'package:wio_feature_subscription_ui_desktop/feature_subscription_desktop_ui.dart'
    deferred as subscription_ui_desktop;
import 'package:wio_feature_task_case_impl/feature_tasks_cases_impl.dart'
    deferred as tasks_cases_impl;
import 'package:wio_feature_tasks_ui_desktop/feature_tasks_desktop_ui.dart'
    deferred as tasks_ui;
import 'package:wio_feature_transactions_api/transactions_api.modular.dart';
import 'package:wio_feature_transactions_impl/feature_transactions_impl.dart'
    deferred as transactions_impl;
import 'package:wio_feature_transactions_ui_desktop/feature_transactions_desktop_ui.dart'
    deferred as transactions_ui;
import 'package:wio_feature_wps_api/navigation/wps_feature_navigation_config.dart';
import 'package:wio_feature_wps_impl/feature_wps_impl.dart'
    deferred as wps_impl;
import 'package:wio_feature_wps_ui_desktop/feature_wps_desktop_ui.dart'
    deferred as wps_ui;

part 'application_dependencies_module_resolver.module_gen.dart';
part 'nested_module_injection_handler.dart';

class ApplicationDependenciesModuleResolver {
  static Future<void> register(AppEnvironment appEnvironment) async {
    _registerCommon();
    _registerApplication();
    _registerLocalizations();

    await _registerDeeplinksDependencies();

    await _initializeFirebase();

    await _NestedModuleRegistrationHandler.register(
      ModulePackages.wioFeatureAuthUiDesktop,
    );
    _registerModuleRegistrationManagers();
    _registerNavigation();
    await _registerFeatures(appEnvironment);
  }

  static Future<void> _initializeFirebase() async {
    final envProvider = DependencyProvider.get<EnvProvider>();

    final firebaseApp = await Firebase.initializeApp(
      options: FirebaseOptions(
        apiKey: envProvider.get(BackOfficeEnvKeys.fbApiKey),
        appId: envProvider.get(BackOfficeEnvKeys.fbAppId),
        messagingSenderId:
            envProvider.get(BackOfficeEnvKeys.fbMessagingSenderId),
        projectId: envProvider.get(BackOfficeEnvKeys.fbProjectId),
        storageBucket: envProvider.get(BackOfficeEnvKeys.fbStorageBucket),
        authDomain: envProvider.get(BackOfficeEnvKeys.fbAuthDomain),
      ),
    );

    DependencyProvider.registerLazySingleton<FirebaseApp>(
      () => firebaseApp,
    );
  }

  static void _registerCommon() {
    DependencyProvider.registerFactory<ExhaustStreamExecutor>(
      () => ExhaustStreamExecutorImpl(),
    );

    CommonErrorHandlerFeatureDependencyModuleResolver.register();
    CommonErrorHandlerDomainDependencyModuleResolver.register();

    ToastMessageFeatureDependencyModuleResolver.register();

    BackOfficeCommonFeatureDependencyModuleResolver.register();

    ClearableDependenciesModuleResolver.register();

    SessionMonitoringDomainDependencyModuleResolver.register(
      sessionDuration: const Duration(milliseconds: 30 * 60 * 1000),
    );
  }

  static Future<void> _registerDeeplinksDependencies() async {
    DeepLinkDomainDependenciesResolver.register();

    /// Registering deferred dependencies that contain deeplinks
    ///
    /// This should be used only for deferred dependencies that contain
    /// deeplinks otherwise we will end up loading the entire app which will
    /// defeat the purpose of using deferred imports.
    await customer_idv_ui.loadLibrary();
    customer_idv_ui.CustomerIdvFeatureDependencyModuleResolver.register();
    await _NestedModuleRegistrationHandler.register(
      ModulePackages.wioFeatureCustomerIdvUiDesktop,
    );
  }

  static Future<void> _registerFeatures(AppEnvironment appEnvironment) async {
    NavigationRailFeatureDependencyModuleResolver.register();

    await _NestedModuleRegistrationHandler.register(
      ModulePackages.wioFeatureSubscriptionImpl,
    );

    await _NestedModuleRegistrationHandler.register(
      ModulePackages.wioFeatureSubscriptionUiDesktop,
    );

    await _NestedModuleRegistrationHandler.register(
      ModulePackages.wioFeatureServiceRequestImpl,
    );

    await _NestedModuleRegistrationHandler.register(
      ModulePackages.wioFeatureServiceRequestUiDesktop,
    );

    if (appEnvironment.isDebug) {
      await _registerDevMenu();
    }
  }

  static Future<void> _registerDevMenu() async {
    DevMenuDomainDependencyModuleResolver.register();
    DevMenuFeatureDependencyModuleResolver.register();
  }

  static void _registerApplication() {
    DependencyProvider.registerLazySingleton<BuildContext>(
      () => GlobalKeys.navigatorKey.currentContext!,
    );

    DependencyProvider.registerLazySingleton<NavigatorState>(
      () => GlobalKeys.navigatorKey.currentState!,
    );
  }

  static void _registerLocalizations() {
    DependencyProvider.registerLazySingleton(
      () => CommonLocalizations.of(DependencyProvider.get<BuildContext>()),
    );

    DependencyProvider.registerLazySingleton<DeferredLocalizationSource>(
      DeferredLocalizationSource.new,
    );

    DependencyProvider.registerLazySingleton<
        List<LocalizationsDelegate<Object?>>>(
      () => [
        CommonLocalizations.delegate,
      ],
    );
  }

  static void _registerNavigation() {
    final navigationProvider = NavigationProviderImpl(
      navigatorFactory: DependencyProvider.get,
      featureRouterFactory: (feature) =>
          DependencyProvider.get<NavigationRouter>(instanceName: feature),
      buildContextFactory: () => DependencyProvider.get<BuildContext>(),
    );

    DependencyProvider.registerLazySingleton<NavigationProvider>(
      () => BackOfficeNavigationProvider(
        navigationProviderImpl: navigationProvider,
      ),
    );

    DependencyProvider.registerLazySingleton<ApplicationNavigatorRouter>(
      () => BackOfficeWebNavigatorRouter(
        appDeepLinkRepository: DependencyProvider.get<AppDeepLinkRepository>(),
      ),
    );
  }

  static void _registerModuleRegistrationManagers() {
    final managers = _moduleRegistrations;

    for (final featureName in managers.keys) {
      DependencyProvider.registerLazySingleton<WebModuleRegistrationManager>(
        () => managers[featureName]!,
        instanceName: featureName,
      );
    }
  }
}
