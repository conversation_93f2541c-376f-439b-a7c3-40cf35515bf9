// DO NOT EDIT THIS FILE MANUALLY
// IT WAS GENERATED BY module_map_generator.dart
// FROM OUT TOOLING PACKAGE
//
// THIS FILE CREATED MODULE MAP OF FEATURE DEPENDENCIES FOR
// BACKOFFICE FEATURES.

enum ModulePackages {
  wioFeatureAccountsImpl,
  wioFeatureAccountsUiDesktop,
  wioFeatureAuthImpl,
  wioFeatureAuthUiDesktop,
  wioFeatureCardsImpl,
  wioFeatureCardsUiDesktop,
  wioFeatureCaseHistoryImpl,
  wioFeatureCaseHistoryUiDesktop,
  wioFeatureCashChequesImpl,
  wioFeatureCashChequesUiDesktop,
  wioFeatureCommunicationsImpl,
  wioFeatureCommunicationsUiDesktop,
  wioFeatureCqsImpl,
  wioFeatureCqsUiDesktop,
  wioFeatureCustomerIdvImpl,
  wioFeatureCustomerIdvUiDesktop,
  wioFeatureCustomerImpl,
  wioFeatureCustomerLoginsImpl,
  wioFeatureCustomerLoginsUiDesktop,
  wioFeatureCustomerUiDesktop,
  wioFeatureDashboardImpl,
  wioFeatureDashboardUiDesktop,
  wioFeatureDevMenuImpl,
  wioFeatureDevMenuUiDesktop,
  wioFeatureDisputesImpl,
  wioFeatureDisputesUiDesktop,
  wioFeatureInvestCustomerImpl,
  wioFeatureInvestCustomerUiDesktop,
  wioFeatureInvestDashboardImpl,
  wioFeatureInvestDashboardUiDesktop,
  wioFeatureInvestPortfolioImpl,
  wioFeatureInvestPortfolioUiDesktop,
  wioFeatureInvoicesImpl,
  wioFeatureInvoicesUiDesktop,
  wioFeatureLendingImpl,
  wioFeatureLendingUiDesktop,
  wioFeatureLettersStatementsImpl,
  wioFeatureLettersStatementsUiDesktop,
  wioFeatureMuImpl,
  wioFeatureMuUiDesktop,
  wioFeatureNavigationRailUiDesktop,
  wioFeatureOnboardingImpl,
  wioFeatureOnboardingUiDesktop,
  wioFeaturePermissionsImpl,
  wioFeaturePermissionsUiDesktop,
  wioFeatureRestrictionsImpl,
  wioFeatureRestrictionsUiDesktop,
  wioFeatureSavingSpaceImpl,
  wioFeatureSavingSpaceUiDesktop,
  wioFeatureServiceRequestImpl,
  wioFeatureServiceRequestUiDesktop,
  wioFeatureSubscriptionImpl,
  wioFeatureSubscriptionUiDesktop,
  wioFeatureTaskCaseImpl,
  wioFeatureTasksUiDesktop,
  wioFeatureTransactionsImpl,
  wioFeatureTransactionsUiDesktop,
  wioFeatureWpsImpl,
  wioFeatureWpsUiDesktop,
  wioFeatureCommonErrorHandlerImpl,
  wioFeatureErrorDomainImpl,
  commonFeatureToggleImpl,
  wioFeatureAppCommonUiDesktop,
  wioFeatureCommonToastMessageImpl,
  wioFeatureBackofficeEnvImpl,
  commonFeatureToggleUi,
  wioFeatureAppCommonImpl,
  wioFeatureCasesImpl,
  countryFlag,
  wioFeatureSavingSpacesImpl,
  wioFeatureFilesImpl
}

class ModuleDependencies {
  final ModulePackages package;
  final Set<ModulePackages> dependsOn;

  const ModuleDependencies({
    required this.package,
    this.dependsOn = const {},
  });
}

const dependenciesMap = {
  ModuleDependencies(
    package: ModulePackages.wioFeatureAccountsImpl,
    dependsOn: {
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureAccountsUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureAccountsImpl,
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureAuthImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
      ModulePackages.wioFeaturePermissionsImpl,
      ModulePackages.wioFeatureRestrictionsImpl,
      ModulePackages.wioFeatureServiceRequestImpl,
      ModulePackages.wioFeatureServiceRequestUiDesktop,
      ModulePackages.wioFeatureTransactionsImpl,
      ModulePackages.wioFeatureTransactionsUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureAuthImpl,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureBackofficeEnvImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureAuthUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.commonFeatureToggleUi,
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureAuthImpl,
      ModulePackages.wioFeatureCasesImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureDashboardImpl,
      ModulePackages.wioFeaturePermissionsImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCardsImpl,
    dependsOn: {
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCardsUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureAccountsImpl,
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureCardsImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureDisputesImpl,
      ModulePackages.wioFeatureLendingImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
      ModulePackages.wioFeaturePermissionsImpl,
      ModulePackages.wioFeatureServiceRequestImpl,
      ModulePackages.wioFeatureServiceRequestUiDesktop,
      ModulePackages.wioFeatureTransactionsImpl,
      ModulePackages.wioFeatureTransactionsUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCaseHistoryImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCaseHistoryUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureCaseHistoryImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCashChequesImpl,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCashChequesUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureCashChequesImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
      ModulePackages.wioFeatureServiceRequestImpl,
      ModulePackages.wioFeatureServiceRequestUiDesktop,
      ModulePackages.wioFeatureTransactionsImpl,
      ModulePackages.wioFeatureTransactionsUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCommunicationsImpl,
    dependsOn: {
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCommunicationsUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureCommunicationsImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
      ModulePackages.wioFeaturePermissionsImpl,
      ModulePackages.wioFeaturePermissionsUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCqsImpl,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCqsUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureAuthImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureCqsImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
      ModulePackages.wioFeaturePermissionsImpl,
      ModulePackages.wioFeaturePermissionsUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCustomerIdvImpl,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCustomerIdvUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureCasesImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureDashboardImpl,
      ModulePackages.wioFeatureInvestDashboardImpl,
      ModulePackages.wioFeaturePermissionsImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCustomerImpl,
    dependsOn: {
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCustomerLoginsImpl,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureAuthImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCustomerLoginsUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureAuthImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureCustomerLoginsImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
      ModulePackages.wioFeatureServiceRequestImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureCustomerUiDesktop,
    dependsOn: {
      ModulePackages.countryFlag,
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureMuImpl,
      ModulePackages.wioFeatureOnboardingImpl,
      ModulePackages.wioFeaturePermissionsImpl,
      ModulePackages.wioFeaturePermissionsUiDesktop,
      ModulePackages.wioFeatureRestrictionsImpl,
      ModulePackages.wioFeatureServiceRequestImpl,
      ModulePackages.wioFeatureServiceRequestUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureDashboardImpl,
    dependsOn: {
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureDashboardUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureAccountsUiDesktop,
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureCardsUiDesktop,
      ModulePackages.wioFeatureCasesImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCqsImpl,
      ModulePackages.wioFeatureCqsUiDesktop,
      ModulePackages.wioFeatureCustomerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureCustomerLoginsUiDesktop,
      ModulePackages.wioFeatureCustomerUiDesktop,
      ModulePackages.wioFeatureDashboardImpl,
      ModulePackages.wioFeatureMuUiDesktop,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
      ModulePackages.wioFeatureOnboardingUiDesktop,
      ModulePackages.wioFeatureRestrictionsImpl,
      ModulePackages.wioFeatureRestrictionsUiDesktop,
      ModulePackages.wioFeatureServiceRequestImpl,
      ModulePackages.wioFeatureTransactionsUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureDevMenuImpl,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureDevMenuUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureAuthImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureDevMenuImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureDisputesImpl,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureTransactionsImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureDisputesUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureDisputesImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureInvestCustomerImpl,
    dependsOn: {
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureInvestCustomerUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureInvestCustomerImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureInvestDashboardImpl,
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureInvestDashboardUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureCustomerUiDesktop,
      ModulePackages.wioFeatureInvestCustomerImpl,
      ModulePackages.wioFeatureInvestCustomerUiDesktop,
      ModulePackages.wioFeatureInvestDashboardImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureInvestPortfolioImpl,
    dependsOn: {
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureInvestPortfolioUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureInvestPortfolioImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
      ModulePackages.wioFeatureTransactionsImpl,
      ModulePackages.wioFeatureTransactionsUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureInvoicesImpl,
    dependsOn: {
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureInvoicesUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureBackofficeEnvImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureInvoicesImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureLendingImpl,
    dependsOn: {
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
      ModulePackages.wioFeatureTransactionsImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureLendingUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureCustomerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureLendingImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
      ModulePackages.wioFeaturePermissionsImpl,
      ModulePackages.wioFeaturePermissionsUiDesktop,
      ModulePackages.wioFeatureTransactionsImpl,
      ModulePackages.wioFeatureTransactionsUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureLettersStatementsImpl,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureLettersStatementsUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureAccountsImpl,
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureCasesImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureLettersStatementsImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureMuImpl,
    dependsOn: {
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureMuUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureMuImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureNavigationRailUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureAccountsImpl,
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureCardsImpl,
      ModulePackages.wioFeatureCasesImpl,
      ModulePackages.wioFeatureCashChequesImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureCommunicationsImpl,
      ModulePackages.wioFeatureCustomerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureDashboardImpl,
      ModulePackages.wioFeatureInvestCustomerImpl,
      ModulePackages.wioFeatureInvestDashboardImpl,
      ModulePackages.wioFeatureInvestPortfolioImpl,
      ModulePackages.wioFeatureInvoicesImpl,
      ModulePackages.wioFeatureLendingImpl,
      ModulePackages.wioFeatureLettersStatementsImpl,
      ModulePackages.wioFeaturePermissionsImpl,
      ModulePackages.wioFeatureSavingSpacesImpl,
      ModulePackages.wioFeatureServiceRequestImpl,
      ModulePackages.wioFeatureServiceRequestUiDesktop,
      ModulePackages.wioFeatureSubscriptionImpl,
      ModulePackages.wioFeatureTransactionsImpl,
      ModulePackages.wioFeatureWpsImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureOnboardingImpl,
    dependsOn: {
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureOnboardingUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureCasesImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureOnboardingImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeaturePermissionsImpl,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeaturePermissionsUiDesktop,
    dependsOn: {
      ModulePackages.wioFeaturePermissionsImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureRestrictionsImpl,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureCasesImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureRestrictionsUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureAuthImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
      ModulePackages.wioFeaturePermissionsImpl,
      ModulePackages.wioFeaturePermissionsUiDesktop,
      ModulePackages.wioFeatureRestrictionsImpl,
      ModulePackages.wioFeatureServiceRequestImpl,
      ModulePackages.wioFeatureServiceRequestUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureSavingSpaceImpl,
    dependsOn: {
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
      ModulePackages.wioFeatureSavingSpacesImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureSavingSpaceUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
      ModulePackages.wioFeaturePermissionsImpl,
      ModulePackages.wioFeatureSavingSpacesImpl,
      ModulePackages.wioFeatureTransactionsImpl,
      ModulePackages.wioFeatureTransactionsUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureServiceRequestImpl,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureCasesImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeaturePermissionsImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureServiceRequestUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureCasesImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureFilesImpl,
      ModulePackages.wioFeatureServiceRequestImpl,
      ModulePackages.wioFeatureTransactionsImpl,
      ModulePackages.wioFeatureTransactionsUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureSubscriptionImpl,
    dependsOn: {
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureSubscriptionUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureFilesImpl,
      ModulePackages.wioFeatureSubscriptionImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureTaskCaseImpl,
    dependsOn: {
      ModulePackages.wioFeatureCasesImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureTasksUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureAuthImpl,
      ModulePackages.wioFeatureCasesImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureCustomerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureCustomerUiDesktop,
      ModulePackages.wioFeatureDisputesImpl,
      ModulePackages.wioFeatureFilesImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
      ModulePackages.wioFeaturePermissionsImpl,
      ModulePackages.wioFeatureTaskCaseImpl,
      ModulePackages.wioFeatureTransactionsImpl,
      ModulePackages.wioFeatureTransactionsUiDesktop,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureTransactionsImpl,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonImpl,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureErrorDomainImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureTransactionsUiDesktop,
    dependsOn: {
      ModulePackages.commonFeatureToggleImpl,
      ModulePackages.wioFeatureAccountsImpl,
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCommonToastMessageImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureDisputesImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
      ModulePackages.wioFeaturePermissionsImpl,
      ModulePackages.wioFeaturePermissionsUiDesktop,
      ModulePackages.wioFeatureServiceRequestImpl,
      ModulePackages.wioFeatureServiceRequestUiDesktop,
      ModulePackages.wioFeatureTasksUiDesktop,
      ModulePackages.wioFeatureTransactionsImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureWpsImpl,
    dependsOn: {
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
    },
  ),
  ModuleDependencies(
    package: ModulePackages.wioFeatureWpsUiDesktop,
    dependsOn: {
      ModulePackages.wioFeatureAppCommonUiDesktop,
      ModulePackages.wioFeatureCommonErrorHandlerImpl,
      ModulePackages.wioFeatureCustomerIdvImpl,
      ModulePackages.wioFeatureNavigationRailUiDesktop,
      ModulePackages.wioFeatureServiceRequestImpl,
      ModulePackages.wioFeatureServiceRequestUiDesktop,
      ModulePackages.wioFeatureWpsImpl,
    },
  ),
};
