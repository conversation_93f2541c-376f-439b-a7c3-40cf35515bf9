import 'package:flutter/material.dart';

/// Main Base notification for drawer notifications
/// Each drawer should have own notification which extends
/// this class, so that screens will be able to show them.
abstract class DrawerNotification extends Notification {
  final bool isEndDrawer;

  const DrawerNotification({this.isEndDrawer = true});
}

/// Uses [DrawerNotification] to receive notifications
/// about opening drawer from the bottom of the Widget tree.
/// Therefore with method [builder] it will display
/// specific drawer.
class DrawerDisplayer extends StatefulWidget {
  final Widget child;
  final Widget? Function(DrawerNotification) builder;

  /// True to cancel the notification bubbling and false to allow the
  /// notification to continue to be dispatched to further ancestors.
  /// It might be required to be set to false in legacy pages where
  /// BaseDesktopPage is used.
  final bool? shouldCancelNotifications;

  const DrawerDisplayer({
    required this.child,
    required this.builder,
    this.shouldCancelNotifications,
    super.key,
  });

  @override
  State<DrawerDisplayer> createState() => _DrawerDisplayer();
}

class _DrawerDisplayer extends State<DrawerDisplayer> {
  final _scaffoldKey = GlobalKey<ScaffoldState>();
  Widget? _endDrawer;
  Widget? _drawer;

  @override
  Widget build(BuildContext context) {
    return NotificationListener<DrawerNotification>(
      onNotification: (notification) => _showDrawer(
        notification: notification,
        shouldCancelNotifications: widget.shouldCancelNotifications,
      ),
      child: Scaffold(
        key: _scaffoldKey,
        body: widget.child,
        drawer: _drawer,
        endDrawer: _endDrawer,
        onDrawerChanged: _onDrawerChanged,
        onEndDrawerChanged: _onEndDrawerChanged,
      ),
    );
  }

  bool _showDrawer({
    required DrawerNotification notification,
    bool? shouldCancelNotifications,
  }) {
    final drawer = widget.builder(notification);
    if (drawer == null) {
      return shouldCancelNotifications ?? true;
    }

    if (notification.isEndDrawer) {
      setState(() => _endDrawer = drawer);
    } else {
      setState(() => _drawer = drawer);
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (notification.isEndDrawer) {
        _scaffoldKey.currentState!.openEndDrawer();
      } else {
        _scaffoldKey.currentState!.openDrawer();
      }
    });

    return true;
  }

  void _onDrawerChanged(bool isOpen) {
    if (isOpen) {
      return;
    }
    setState(() => _drawer = null);
  }

  void _onEndDrawerChanged(bool isOpen) {
    if (isOpen) {
      return;
    }
    setState(() => _endDrawer = null);
  }
}
