import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_common_error_handler_api/models/common_error/common_error.dart';

part 'identifier.freezed.dart';

/// Defines the types of identifiers that can be used as part of the
/// [CommonError]
///
/// This will be used to identify the type of error that has occurred
@freezed
class ErrorIdentifier with _$ErrorIdentifier {
  /// This can be used when there is no way to identify the error
  const factory ErrorIdentifier.none() = _ErrorNoneIdentifier;

  /// This can be used to identify the error by the correlation id that
  /// corresponds to the network call
  const factory ErrorIdentifier.correlationId(String correlationId) =
      _ErrorUnknownIdentifier;

  /// This can be used to identify the error by the id of the entity for which
  /// the error occurred
  ///
  /// For example if the error occurred while trying to fetch the details of a
  /// user with id 123, then the error identifier can be created as follows:
  /// ErrorIdentifier.entity('123')
  ///
  /// This should be used with discretion so as not to expose sensitive
  /// information. For example, do not use this to identify the error by the
  /// email, phone number, name, card numbers, card details, etc.
  const factory ErrorIdentifier.entity(String entityId) =
      _ErrorEntityIdentifier;

  /// This can use a last resort if the error cannot be identified by any of the
  /// other identifiers
  const factory ErrorIdentifier.custom({
    required String fieldName,
    required String value,
  }) = _ErrorCustomIdentifier;

  const ErrorIdentifier._();

  @override
  String toString() {
    return map(
      none: (none) => 'No error identifier provider',
      correlationId: (correlationid) =>
          'Error correlation id: ${correlationid.correlationId}',
      entity: (entity) => 'Entity identifier: ${entity.entityId}',
      custom: (customIdentifier) => 'Custom identifier: '
          '${customIdentifier.fieldName} - ${customIdentifier.value}',
    );
  }
}
