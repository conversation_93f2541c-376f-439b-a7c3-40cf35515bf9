import 'package:flutter/material.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_sme_mobile_storybook/ui_kit_sme_mobile_storybook.dart';

// ignore_for_file: deprecated_member_use
void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) => CompanyThemeProvider(
        theme: CompanyTheme(
          colorScheme: CompanyColorScheme.smeMobile(),
          textStyles: CompanyTextStyles.smeMobile(),
        ),
        child: MaterialApp(
          home: Storybook(
            stories: SMEMobileStorybook().children(),
          ),
        ),
      );
}
