import 'package:flutter/material.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_mobile/ui_kit_sme_mobile.dart';
import 'package:ui_kit_sme_mobile_storybook/assets.dart';
import 'package:ui_kit_sme_mobile_storybook/widgets/legacy_story.dart';

class PictureTileStories {
  static Story pictureTileStory = LegacyStory(
    section: 'Components',
    name: 'Picture Tile',
    builder: (_, k) => SingleChildScrollView(
      child: Column(
        children: [
          const Text('LTR'),
          PictureTileExample(),
          SizedBox(height: 8),
          const Text('RTL'),
          Directionality(
            textDirection: TextDirection.rtl,
            child: Column(
              children: [
                PictureTileExample(),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

class PictureTileExample extends StatefulWidget {
  @override
  State<PictureTileExample> createState() => _PictureTileExampleState();
}

class _PictureTileExampleState extends State<PictureTileExample> {
  bool value = true;

  @override
  build(BuildContext context) {
    return Column(
      children: [
        PictureTile(PictureTileModel(
          selected: false,
          assetImage: AssetImage(
            Assets.testPicture,
          ),
        )),
        SizedBox(
          height: 8,
        ),
        PictureTile(PictureTileModel(
          selected: true,
          assetImage: AssetImage(
            Assets.testPicture,
          ),
        ))
      ],
    );
  }
}
