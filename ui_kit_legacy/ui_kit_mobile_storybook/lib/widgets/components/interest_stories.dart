import 'package:flutter/material.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:ui_kit_mobile_storybook/widgets/legacy_story.dart';

class InterestStories {
  static Story stories = LegacyStory(
    section: 'Components',
    name: "Interest",
    builder: (_, k) => ListView(
      scrollDirection: Axis.vertical,
      children: [
        const Text('LTR', textAlign: TextAlign.center),
        _InterestExample(),
        const SizedBox(height: 40),
        const Text('RTL', textAlign: TextAlign.center),
        Directionality(
          textDirection: TextDirection.rtl,
          child: _InterestExample(),
        ),
      ],
    ),
  );
}

class _InterestExample extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 24),
      decoration: BoxDecoration(gradient: CompanyGradients.hero()),
      child: const Column(
        children: [
          Interest(
            model: InterestModel(interestRate: '6', subtitle: 'interest'),
          ),
          SizedBox(height: 8),
          Interest(
            model: InterestModel(interestRate: '4', subtitle: 'interest'),
          ),
          SizedBox(height: 8),
          Interest(
            model: InterestModel(interestRate: '^', subtitle: 'interest'),
          ),
        ],
      ),
    );
  }
}
