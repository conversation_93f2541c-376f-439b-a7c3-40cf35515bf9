import 'package:flutter/material.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:ui_kit_mobile_storybook/widgets/legacy_story.dart';
import 'package:wio_app_core_api/money.dart';

class DetailsCardStories {
  static Story story = LegacyStory(
    section: 'Components',
    name: 'Details Card',
    builder: (_, k) => ListView(
      scrollDirection: Axis.vertical,
      children: [
        const Text('Wise details card LTR'),
        const SizedBox(height: 10.0),
        _DetailsCardExample(),
        const SizedBox(height: 10.0),
        const Text('Wise details card RTL'),
        Directionality(
          textDirection: TextDirection.rtl,
          child: _DetailsCardExample(),
        ),
      ],
    ),
  );
}

class _DetailsCardExample extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: 327,
          child: DetailsCard(
            model: DetailsCardModel(
              leading: const DetailsCardLeadingModel.tileWithText(
                title: "Through",
                subtitle: "Wise",
                tileMode: TileModel.image(
                  image: ImageInTileModel(
                    path: 'https://picsum.photos/seed/picsum/100',
                  ),
                  backgroundColor: CompanyColorPointer.surface7,
                  borderRadius: 8.0,
                ),
              ),
              body: DetailsCardBodyModel.dateTimeFeeRate(
                estimatedTimeModel: EstimatedTimeModel.datetime(
                  title: "Estimated time",
                  dateTime: DateTime(2022, 5, 13),
                ),
                fee: FeeModel(
                  money: MoneyModel(
                    title: "Transfer fee",
                    money: Money.fromNumWithCurrency(8.60, Currency.aed),
                    zeroMoneyTitle: 'Free',
                  ),
                ),
                rate: RateModel(
                  title: "Exchange rate",
                  sourceMoney: Money.fromIntWithCurrency(1, Currency.aed),
                  targetMoney: Money.fromIntWithCurrency(100, Currency.usd),
                ),
              ),
              trailing: DetailsCardTrailingModel.amount(
                amount: MoneyModel(
                    title: "You will pay:",
                    money: Money.fromNumWithCurrency(5578.25, Currency.aed),
                    zeroMoneyTitle: 'Free'),
              ),
              background: const CardBackground.color(
                  color: CompanyColorPointer.surface7),
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: 327,
          child: DetailsCard(
            model: DetailsCardModel(
              leading: const DetailsCardLeadingModel.tileWithText(
                title: "Through",
                subtitle: "Wise",
                tileMode: TileModel.image(
                  image: ImageInTileModel(
                    path: 'https://picsum.photos/seed/picsum/100',
                  ),
                  backgroundColor: CompanyColorPointer.surface7,
                  borderRadius: 8.0,
                ),
              ),
              body: DetailsCardBodyModel.dateTimeFeeRate(
                estimatedTimeModel: const EstimatedTimeModel.text(
                    title: "Estimated time", text: "48-72 hours"),
                fee: FeeModel(
                  money: MoneyModel(
                      title: "Transfer fee",
                      money: Money.fromNumWithCurrency(8.60, Currency.aed),
                      zeroMoneyTitle: 'Free'),
                ),
                rate: RateModel(
                  title: "Exchange rate",
                  sourceMoney: Money.fromIntWithCurrency(1, Currency.aed),
                  targetMoney: Money.fromIntWithCurrency(100, Currency.usd),
                ),
              ),
              trailing: DetailsCardTrailingModel.amount(
                amount: MoneyModel(
                    title: "You will pay:",
                    money: Money.fromNumWithCurrency(5578.25, Currency.aed),
                    zeroMoneyTitle: 'Free'),
              ),
              background: const CardBackground.color(
                color: CompanyColorPointer.surface7,
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: 327,
          child: DetailsCard(
            model: DetailsCardModel(
              leading: const DetailsCardLeadingModel.tileWithText(
                title: "Through",
                subtitle: "Wise",
                tileMode: TileModel.image(
                  image: ImageInTileModel(
                    path: 'https://picsum.photos/seed/picsum/100',
                  ),
                  backgroundColor: CompanyColorPointer.surface7,
                  borderRadius: 8.0,
                ),
              ),
              body: DetailsCardBodyModel.dateTimeFeeRate(
                estimatedTimeModel: const EstimatedTimeModel.text(
                    title: "Estimated time", text: "48-72 hours"),
                fee: FeeModel(
                  money: MoneyModel(
                      title: "Transfer fee",
                      money: Money.fromNumWithCurrency(8.60, Currency.aed),
                      zeroMoneyTitle: 'Free'),
                ),
                rate: RateModel(
                  title: "Exchange rate",
                  sourceMoney: Money.fromIntWithCurrency(1, Currency.aed),
                  targetMoney: Money.fromIntWithCurrency(100, Currency.usd),
                ),
              ),
              trailing: DetailsCardTrailingModel.amount(
                amount: MoneyModel(
                    title: "You will pay:",
                    money: Money.fromNumWithCurrency(5578.25, Currency.aed),
                    zeroMoneyTitle: 'Free'),
              ),
              footer: DetailsCardFooterModel.richText(
                model: CompanyRichTextModel(
                  text: 'Wise Terms & Conditions',
                  normalStyle: CompanyTextStylePointer.b4,
                  normalTextColor: CompanyColorPointer.secondary4,
                  accentStyle: CompanyTextStylePointer.b4underline,
                  accentTextColor: CompanyColorPointer.primary1,
                  highlightedTextModels: [
                    HighlightedTextModel(
                      'Terms & Conditions',
                      onTap: () {},
                    ),
                  ],
                ),
              ),
              background: const CardBackground.color(
                color: CompanyColorPointer.surface7,
              ),
            ),
          ),
        )
      ],
    );
  }
}
