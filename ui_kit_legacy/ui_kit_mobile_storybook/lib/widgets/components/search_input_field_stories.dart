import 'package:flutter/widgets.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:ui_kit_mobile_storybook/widgets/legacy_story.dart';

class SearchInputFieldStories {
  static List<Story> get allStories => [searchInputField];

  static Story searchInputField = LegacyStory(
      section: 'Components',
      name: 'Input Field: Search',
      builder: (_, __) {
        return Column(
          children: [
            const Text('LTR'),
            _SearchInputFieldExample(),
            const SizedBox(height: 20),
            const Text('RTL'),
            Directionality(
              textDirection: TextDirection.rtl,
              child: _SearchInputFieldExample(),
            ),
          ],
        );
      });
}

class _SearchInputFieldExample extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SearchField(
      model: const SearchFieldModel(),
      onChanged: (value) {
        debugPrint('Changed: $value');
      },
      onSubmitted: (value) {
        debugPrint('Submitted: $value');
      },
      onCleared: () {
        debugPrint('Cleared');
      },
      onFocusChanged: (isFocused) {
        debugPrint('Focused: $isFocused');
      },
    );
  }
}
