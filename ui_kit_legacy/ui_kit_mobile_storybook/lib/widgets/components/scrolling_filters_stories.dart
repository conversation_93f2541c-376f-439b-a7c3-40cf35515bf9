import 'package:flutter/material.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:ui_kit_mobile_storybook/widgets/legacy_story.dart';

class ScrollingFiltersStories {
  static const String sectionName = 'Components';

  static List<Story> get allStories => [
        scrollingFiltersStory,
      ];

  static Story scrollingFiltersStory = LegacyStory(
    section: sectionName,
    name: 'Filters: Scrolling',
    builder: (_, __) {
      return Column(
        children: const [
          Text('LTR'),
          _ScrollingFiltersExample(),
          SizedBox(height: 20),
          Text('RTL'),
          Directionality(
            textDirection: TextDirection.rtl,
            child: _ScrollingFiltersExample(),
          ),
        ],
      );
    },
  );
}

class _ScrollingFiltersExample extends StatefulWidget {
  const _ScrollingFiltersExample({Key? key}) : super(key: key);

  @override
  _ScrollingFiltersExampleState createState() =>
      _ScrollingFiltersExampleState();
}

class _ScrollingFiltersExampleState extends State<_ScrollingFiltersExample> {
  final contentController = PageController();

  @override
  Widget build(BuildContext context) {
    final colorScheme = CompanyThemeProvider.of(context).colorScheme;
    final titles = [
      'Promising',
      'Daily Movers',
      'Very promising choices',
      'Something else',
    ];

    return Column(
      children: [
        ScrollingFilters(
          model: ScrollingFiltersModel(
            titles: titles,
            horizontalSpacing: 12,
            startPadding: 0,
          ),
          contentController: contentController,
          accentColor: colorScheme.surface1,
        ),
        const SizedBox(
          height: 8,
        ),
        SizedBox(
          height: 100,
          child: PageView(
            controller: contentController,
            children: List.generate(
              titles.length,
              (index) => _getContainer(
                titles[index],
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget _getContainer(String title) {
    return Container(
      color: Colors.blueGrey,
      child: Center(
        child: Text(title),
      ),
    );
  }
}
