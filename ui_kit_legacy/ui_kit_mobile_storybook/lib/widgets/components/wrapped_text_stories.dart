import 'package:flutter/material.dart' hide Banner;
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:ui_kit_mobile_storybook/widgets/legacy_story.dart';

class WrappedTextStories {
  static Story wrappedTexts = LegacyStory(
    section: 'Components',
    name: "WrappedText",
    background: const Color(0XFF411F7C),
    builder: (_, k) => ListView(
      scrollDirection: Axis.vertical,
      children: [
        const Text(
          'Background on this page changed to show of the wrapped text '
          'that has semi-transparent background',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
          ),
        ),
        const SizedBox(height: 60),
        const Text(
          'LTR',
          style: TextStyle(color: Colors.white),
        ),
        _WrappedTextExample(),
        const SizedBox(height: 40),
        const Text(
          'RTL',
          style: TextStyle(color: Colors.white),
        ),
        Directionality(
          textDirection: TextDirection.rtl,
          child: _WrappedTextExample(),
        ),
      ],
    ),
  );
}

class _WrappedTextExample extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: const [
          Text(
            'Base wrapped text',
            style: TextStyle(color: Colors.white),
          ),
          SizedBox(height: 16),
          WrappedText(
            CompanyRichTextModel(
              text: 'Wrapped text check',
              highlightedTextModels: [
                HighlightedTextModel('text'),
              ],
              normalTextColor: CompanyColorPointer.primary2,
              accentTextColor: CompanyColorPointer.secondary10,
            ),
          ),
          SizedBox(height: 36),
          Text(
            'All available horizontal space wrapped text + additional text',
            style: TextStyle(color: Colors.white),
          ),
          SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: WrappedText(
              CompanyRichTextModel(
                text: 'Wrapped text check',
                highlightedTextModels: [
                  HighlightedTextModel('text'),
                ],
                normalTextColor: CompanyColorPointer.primary2,
                accentTextColor: CompanyColorPointer.secondary10,
              ),
              additionalText: 'Additional text',
            ),
          ),
        ],
      ),
    );
  }
}
