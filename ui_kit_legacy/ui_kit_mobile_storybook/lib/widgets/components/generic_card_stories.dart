import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:ui_kit_mobile_storybook/utility/size_measure.dart';
import 'package:ui_kit_mobile_storybook/widgets/legacy_story.dart';

class GenericCardStories {
  static final story = LegacyStory(
    section: 'Components',
    name: 'Generic Card',
    builder: (context, k) => SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Text('LTR direction'),
          ..._getWidgets(context),
          const Text('RTL direction'),
          Directionality(
            textDirection: TextDirection.rtl,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                ..._getWidgets(context),
              ],
            ),
          ),
        ],
      ),
    ),
  );

  static List<Widget> _getWidgets(BuildContext context) {
    final effectiveTextScale =
        MediaQuery.textScalerOf(context).scale(20.0) / 20.0;

    final width = 156.0 * effectiveTextScale;
    final height = width * (4.0 / 3.0);

    return [
      ..._buildScenario(
        title: 'Data',
        withButton: GenericCard(
          GenericCardModel.data(
            graphicAssetPointer:
                CompanyPictogramPointer.metaphors_shop.toGraphicAsset(),
            subtitleModel: const CompanyRichTextModel(
              maxLines: 10,
              text:
                  'You’ve spent 1033,68 AED in grocery this month, 1,6% less than usual',
              textAlign: TextAlign.start,
              highlightedTextModels: [
                HighlightedTextModel(
                  '1033,68 AED',
                ),
                HighlightedTextModel(
                  '1,6% less',
                ),
              ],
              accentTextColor: CompanyColorPointer.surface1,
              normalStyle: CompanyTextStylePointer.b3,
              accentStyle: CompanyTextStylePointer.b3,
            ),
            buttonModel: const ButtonModel(
              title: 'Click',
              size: ButtonSize.small,
            ),
          ),
          onSubmit: () {},
        ),
        withoutButton: GenericCard(
          GenericCardModel.data(
            graphicAssetPointer:
                CompanyPictogramPointer.metaphors_shop.toGraphicAsset(),
            subtitleModel: const CompanyRichTextModel(
              maxLines: 10,
              text:
                  'You’ve spent 1033,68 AED in grocery this month, 1,6% less than usual',
              textAlign: TextAlign.start,
              highlightedTextModels: [
                HighlightedTextModel(
                  '1033,68 AED',
                ),
                HighlightedTextModel(
                  '1,6% less',
                ),
              ],
              accentTextColor: CompanyColorPointer.surface1,
              normalStyle: CompanyTextStylePointer.b3,
              accentStyle: CompanyTextStylePointer.b3,
            ),
          ),
        ),
      ),
      ..._buildScenario(
        title: 'Data 4:3',
        withButton: GenericCard(
          GenericCardModel.data(
            graphicAssetPointer:
                CompanyPictogramPointer.metaphors_shop.toGraphicAsset(),
            subtitleModel: const CompanyRichTextModel(
              maxLines: 10,
              text:
                  'You’ve spent 1033,68 AED in grocery this month, 1,6% less than usual',
              textAlign: TextAlign.start,
              highlightedTextModels: [
                HighlightedTextModel(
                  '1033,68 AED',
                ),
                HighlightedTextModel(
                  '1,6% less',
                ),
              ],
              accentTextColor: CompanyColorPointer.surface1,
              normalStyle: CompanyTextStylePointer.b3,
              accentStyle: CompanyTextStylePointer.b3,
            ),
            buttonModel: const ButtonModel(
              title: 'Click',
              size: ButtonSize.small,
            ),
          ),
          width: width,
          height: height,
          onSubmit: () {},
        ),
        withoutButton: GenericCard(
          GenericCardModel.data(
            graphicAssetPointer:
                CompanyPictogramPointer.metaphors_shop.toGraphicAsset(),
            subtitleModel: const CompanyRichTextModel(
              maxLines: 10,
              text:
                  'You’ve spent 1033,68 AED in grocery this month, 1,6% less than usual',
              textAlign: TextAlign.start,
              highlightedTextModels: [
                HighlightedTextModel(
                  '1033,68 AED',
                ),
                HighlightedTextModel(
                  '1,6% less',
                ),
              ],
              accentTextColor: CompanyColorPointer.surface1,
              normalStyle: CompanyTextStylePointer.b3,
              accentStyle: CompanyTextStylePointer.b3,
            ),
          ),
          width: width,
          height: height,
        ),
      ),
      ..._buildScenario(
        title: 'Tips',
        withButton: GenericCard(
          const GenericCardModel.tips(
            backgroundModel: GenericCardBackgroundModel.color(
              colorPointer: CompanyColorPointer.secondary10,
            ),
            title: 'Saving tip',
            subtitle: 'Set up a new Saving Space and start saving towards it.',
            imageProvider: CompanyImageProvider.asset(
              name: 'assets/test_generic_card_image.png',
            ),
            color: CompanyColorPointer.surface2,
            buttonModel: ButtonModel(
              title: 'Click',
              size: ButtonSize.small,
              negative: true,
            ),
          ),
          onSubmit: () {},
        ),
        withoutButton: GenericCard(
          const GenericCardModel.tips(
            backgroundModel: GenericCardBackgroundModel.color(
              colorPointer: CompanyColorPointer.secondary10,
            ),
            title: 'Saving tip',
            subtitle: 'Set up a new Saving Space and start saving towards it.',
            imageProvider: CompanyImageProvider.asset(
              name: 'assets/test_generic_card_image.png',
            ),
            color: CompanyColorPointer.surface2,
          ),
        ),
      ),
      ..._buildScenario(
        title: 'Tips 4:3',
        withButton: GenericCard(
          const GenericCardModel.tips(
            backgroundModel: GenericCardBackgroundModel.color(
              colorPointer: CompanyColorPointer.secondary10,
            ),
            title: 'Saving tip',
            subtitle: 'Set up a new Saving Space and start saving towards it.',
            imageProvider: CompanyImageProvider.asset(
              name: 'assets/test_generic_card_image.png',
            ),
            color: CompanyColorPointer.surface2,
            buttonModel: ButtonModel(
              title: 'Click',
              size: ButtonSize.small,
              negative: true,
            ),
          ),
          width: width,
          height: height,
          onSubmit: () {},
        ),
        withoutButton: GenericCard(
          const GenericCardModel.tips(
            backgroundModel: GenericCardBackgroundModel.color(
              colorPointer: CompanyColorPointer.secondary10,
            ),
            title: 'Saving tip',
            subtitle: 'Set up a new Saving Space and start saving towards it.',
            imageProvider: CompanyImageProvider.asset(
              name: 'assets/test_generic_card_image.png',
            ),
            color: CompanyColorPointer.surface2,
          ),
          width: width,
          height: height,
        ),
      ),
      ..._buildScenario(
        title: 'Spendings',
        withButton: GenericCard(
          const GenericCardModel.spendings(
            title: 'Your spendings',
            spendingAmount: 13628,
            currency: 'AED',
            subtitleModel: CompanyRichTextModel(
              maxLines: 10,
              textAlign: TextAlign.start,
              text: 'That’s -246.47 AED less compared to past months',
              highlightedTextModels: [
                HighlightedTextModel('-246.47 AED'),
              ],
              accentTextColor: CompanyColorPointer.surface1,
              accentStyle: CompanyTextStylePointer.b3,
            ),
            buttonModel: ButtonModel(
              title: 'Click',
              size: ButtonSize.small,
            ),
          ),
          onSubmit: () {},
        ),
        withoutButton: GenericCard(
          const GenericCardModel.spendings(
            title: 'Your spendings',
            spendingAmount: 129101.1232,
            currency: 'AED',
            subtitleModel: CompanyRichTextModel(
              maxLines: 10,
              text: 'That’s -246.47 AED less compared to past months',
              textAlign: TextAlign.start,
              highlightedTextModels: [
                HighlightedTextModel('-246.47 AED'),
              ],
              accentTextColor: CompanyColorPointer.surface1,
            ),
          ),
        ),
      ),
      ..._buildScenario(
        title: 'Spendings 4:3',
        withButton: GenericCard(
          const GenericCardModel.spendings(
            title: 'Your spendings',
            spendingAmount: 13628,
            currency: 'AED',
            subtitleModel: CompanyRichTextModel(
              maxLines: 10,
              textAlign: TextAlign.start,
              text: 'That’s -246.47 AED less compared to past months',
              highlightedTextModels: [
                HighlightedTextModel('-246.47 AED'),
              ],
              accentTextColor: CompanyColorPointer.surface1,
              accentStyle: CompanyTextStylePointer.b3,
            ),
            buttonModel: ButtonModel(
              title: 'Click',
              size: ButtonSize.small,
            ),
          ),
          width: width,
          height: height,
          onSubmit: () {},
        ),
        withoutButton: GenericCard(
          const GenericCardModel.spendings(
            title: 'Your spendings',
            spendingAmount: 129101.1232,
            currency: 'AED',
            subtitleModel: CompanyRichTextModel(
              maxLines: 10,
              text: 'That’s -246.47 AED less compared to past months',
              textAlign: TextAlign.start,
              highlightedTextModels: [
                HighlightedTextModel('-246.47 AED'),
              ],
              accentTextColor: CompanyColorPointer.surface1,
            ),
          ),
          width: width,
          height: height,
        ),
      ),
    ];
  }

  static List<Widget> _buildScenario({
    required String title,
    required Widget withButton,
    required Widget withoutButton,
  }) {
    return [
      const SizedBox(height: 20.0),
      Text(title),
      const SizedBox(height: 12.0),
      const Text('With button'),
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        child: SizeMeasure(child: withButton),
      ),
      const Text('Without button'),
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        child: SizeMeasure(child: withoutButton),
      ),
    ];
  }
}
