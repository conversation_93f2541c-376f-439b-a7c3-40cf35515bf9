import 'package:flutter/material.dart';
import 'package:otp_text_field/otp_field.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:ui_kit_sme_storybook/widgets/legacy_story.dart';

//Globals
final companyColors = CompanyColorScheme.smeDesktop();
final companyTextStyles = CompanyTextStyles.desktop();

//Stories
class OTPStories {
  static Story otpLTRPage = LegacyStory(
    section: 'Pages',
    name: 'OTP',
    builder: (_, k) => Directionality(
      textDirection: TextDirection.ltr,
      child: otpPage,
    ),
  );

  static Story otpRTLPage = LegacyStory(
    section: 'Pages',
    name: 'OTP RTL',
    builder: (_, k) => Directionality(
      textDirection: TextDirection.rtl,
      child: otpPage,
    ),
  );
}

Widget otpPage = BasicLayout(
  BasicLayoutModel(backgroundColor: CompanyColorPointer.background2),
  body: ColoredBox(
    color: companyColors.background2,
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        SizedBox(
          width: 500,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Enter the code we sent to ****7890',
                style: companyTextStyles.h2,
              ),
              const SizedBox(height: 32),
              SubtleOtp(
                const SubtleOtpModel(),
                onChanged: (value) => {debugPrint(value)},
                otpFieldController: OtpFieldController(),
              ),
              const Text(
                'Resend code in 1:59',
              ),
            ],
          ),
        ),
        const SizedBox(
          width: 500,
          height: 1000,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Positioned.fill(
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: ChevronAnimation(
                    ChevronAnimationModel.var2BottomRight,
                  ),
                ),
              ),
              DesktopPictogram(
                DesktopPictogramModel(
                  icon: CompanyPictogramPointer.objects_phone,
                  size: DesktopPictogramSize.xl,
                  color: CompanyColorPointer.primary2,
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  ),
  header: InternalHeader(
    const InternalHeaderModel(
      variant: InternalHeaderVariant.var3,
      layout: InternalHeaderLayout.logo,
    ),
    const [
      SquareButton(
        icon: CompanyIconPointer.close,
        variant: SquareButtonType.alternativeOutline,
      ),
    ],
    squareButtonArrowOnClick: () async {},
  ),
);
