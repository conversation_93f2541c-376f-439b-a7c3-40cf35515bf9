import 'package:flutter/material.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:ui_kit_sme_storybook/widgets/legacy_story.dart';

Column _generateColumnWithStatesAndWithIconByType(TextLinkButtonType type) {
  return Column(
    children: [
      TextLinkButton(
        TextLinkButtonModel(
          text: 'TextButton',
          type: type,
        ),
        onClick: () => {debugPrint('click button')},
      ),
      SizedBox(height: 15),
      TextLinkButton(
        TextLinkButtonModel(
          text: 'TextButton',
          state: TextLinkButtonState.disabled,
          type: type,
        ),
        onClick: () => {debugPrint('click button')},
      ),
      SizedBox(height: 15),
      TextLinkButton(
        TextLinkButtonModel(
          text: 'Learn More',
          icon: DesktopIconModel(
            icon: CompanyIconPointer.play,
            size: DesktopIconSize.m,
          ),
          type: type,
        ),
        onClick: () => {debugPrint('click button')},
      ),
      SizedBox(height: 15),
      TextLinkButton(
        TextLinkButtonModel(
          text: 'Learn More',
          icon: DesktopIconModel(
            icon: CompanyIconPointer.play,
            size: DesktopIconSize.m,
          ),
          type: type,
          state: TextLinkButtonState.disabled,
        ),
      ),
      SizedBox(height: 15),
      TextLinkButton(
        TextLinkButtonModel(
          text: 'Tight mode button',
          icon: DesktopIconModel(
            icon: CompanyIconPointer.play,
            size: DesktopIconSize.m,
          ),
          type: type,
          isTightMode: true,
        ),
        onClick: () => {debugPrint('click button')},
      ),
      SizedBox(height: 15),
      TextLinkButton(
        TextLinkButtonModel(
          text: 'Left icon tight mode button',
          icon: DesktopIconModel(
            icon: CompanyIconPointer.play,
            size: DesktopIconSize.m,
          ),
          iconDirection: TextLinkIconDirection.left,
          type: type,
          isTightMode: true,
        ),
        onClick: () => {debugPrint('click button')},
      ),
    ],
  );
}

List<Widget> _generateAllTextButtonsTypesColumns() {
  var _columns = <Widget>[];
  for (TextLinkButtonType type in TextLinkButtonType.values) {
    Column _itemColumn = _generateColumnWithStatesAndWithIconByType(type);
    _columns.add(_itemColumn);
    _columns.add(SizedBox(width: 20));
  }

  return _columns;
}

class TextLinkButtonStories {
  static Story differentTextLinkButtonTypes = LegacyStory(
    section: 'Buttons',
    name: 'Text Button',
    builder: (_, k) => Row(
      children: [
        ..._generateAllTextButtonsTypesColumns(),
      ],
    ),
  );
}
