import 'package:flutter/material.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:ui_kit_sme_storybook/widgets/legacy_story.dart';

class ContactsCarouselStories {
  static Story contactsCarousel = LegacyStory(
    section: 'ContactsCarousel',
    name: 'Contacts Carousel Item',
    builder: (_, k) {
      final items = List.generate(
        10,
        (index) => ContactsCarouselItem(
          text: 'Prakash$index Abdurmalnahamaman$index',
          flag: FlagPointer.GB,
          onClick: () => debugPrint('item $index clicked'),
        ),
      );

      return Material(
        child: Padding(
          padding: const EdgeInsets.only(left: 50, top: 50),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Scrollable'),
              Padding(
                padding: const EdgeInsets.fromLTRB(100, 20, 100, 0),
                child: ContactsCarousel.scrollable(items: items),
              ),
              const Text('Regular'),
              Padding(
                padding: const EdgeInsets.fromLTRB(100, 20, 100, 0),
                child: ContactsCarousel(
                  viewAllLabel: 'View all',
                  items: items,
                  onClick: () {},
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
}
