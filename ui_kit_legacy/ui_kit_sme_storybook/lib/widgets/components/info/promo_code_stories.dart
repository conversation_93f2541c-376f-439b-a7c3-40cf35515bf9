import 'package:flutter/widgets.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:ui_kit_sme_storybook/widgets/legacy_story.dart';

class PromoCodeStories {
  static Story story = LegacyStory(
    name: 'PromoCode',
    section: 'Info',
    builder: (context, _) {
      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16.0),
              color: context.colorStyling.background2,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('Var 1'),
                  const SizedBox(height: 24.0),
                  PromoCode(
                    model: PromoCodeModel.var1(
                      title: 'FBC001122',
                      ctaModel: CtaModel(
                        size: CtaSize.s,
                        text: 'COPY NUMBER',
                        style: CtaStyle.alternative,
                        icon: CompanyIconPointer.actions_copy,
                        direction: CtaDirection.reversed,
                        iconColor: CompanyColorPointer.primary1,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 40.0),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Var 2'),
                const SizedBox(height: 24.0),
                PromoCode(
                  model: PromoCodeModel.var2(
                    title: 'FBC001122',
                    ctaModel: CtaModel(
                      size: CtaSize.s,
                      text: 'COPY NUMBER',
                      style: CtaStyle.alternative,
                      icon: CompanyIconPointer.actions_copy,
                      iconColor: CompanyColorPointer.primary1,
                      direction: CtaDirection.reversed,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    },
  );
}
