import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';

class StaticListItemStories {
  static Story staticListItemStory = Story(
    name: 'Static list item Story',
    builder: (_) => const _StaticListItemStoryContent(),
  );
}

class _StaticListItemStoryContent extends StatelessWidget {
  const _StaticListItemStoryContent({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        StaticListItem(
          items: [
            'Item1',
            'Item2',
            'Item3',
          ],
        ),
      ],
    );
  }
}
