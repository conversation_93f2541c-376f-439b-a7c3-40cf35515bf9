import 'package:flutter/material.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';

class BasicTextFieldStories {
  static Story get story => Story(
        name: 'Inputs/Basic text field',
        builder: (_) => const _BasicTextFieldStoryContent(),
      );
}

class _BasicTextFieldStoryContent extends StatelessWidget {
  const _BasicTextFieldStoryContent({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      width: 700.0,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: const [
          _TextField(
            title: 'Simple text field',
            model: BasicTextFieldModel(
              label: 'Label text',
              hintText: 'Hint text',
              infoLabel: 'Maximum 120 Characters',
              suffixes: BasicTextFieldSuffixesModel.list([
                BasicTextFieldSuffix.text(text: 'AED'),
              ]),
            ),
          ),
          _TextField(
            title: 'Simple text field',
            model: BasicTextFieldModel(
              hintText: 'Hint text',
              infoLabel: 'Maximum 120 Characters',
              suffixes: BasicTextFieldSuffixesModel.list([
                BasicTextFieldSuffix.text(text: 'AED'),
              ]),
            ),
          ),
          _TextField(
            title: 'Loading text field',
            model: BasicTextFieldModel(
              label: 'Label text',
              infoLabel: 'Maximum 120 Characters',
              suffixes: BasicTextFieldSuffixesModel.list([
                BasicTextFieldSuffix.spinner(),
                BasicTextFieldSuffix.text(text: 'AED'),
              ]),
            ),
          ),
          _TextField(
            title: 'Error text field',
            model: BasicTextFieldModel(
              label: 'Label text',
              infoLabel: 'Maximum 120 Characters',
              errorMessage: 'Some error message',
              suffixes: BasicTextFieldSuffixesModel.list([
                BasicTextFieldSuffix.spinner(),
                BasicTextFieldSuffix.text(text: 'AED'),
              ]),
            ),
          ),
          _TextField(
            title: 'No auto suffixes',
            model: BasicTextFieldModel(
              label: 'Label text',
              suffixes: BasicTextFieldSuffixesModel.empty(),
            ),
          ),
        ],
      ),
    );
  }
}

class _TextField extends StatelessWidget {
  final BasicTextFieldModel model;
  final String title;

  const _TextField({
    Key? key,
    required this.title,
    required this.model,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(color: Colors.black),
          ),
          const SizedBox(width: 16.0),
          Expanded(
            child: BasicTextField(model),
          ),
        ],
      ),
    );
  }
}
