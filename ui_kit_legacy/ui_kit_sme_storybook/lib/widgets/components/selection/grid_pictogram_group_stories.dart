import 'package:flutter/widgets.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:ui_kit_sme_storybook/widgets/legacy_story.dart';

class GridPictogramGroupStories {
  static Story gridPictogram = LegacyStory(
    section: 'Selection',
    name: 'Grid Pictogram Group',
    builder: (_, k) => SizedBox(
      height: 300,
      width: 300,
      child: GridPictogramGroup(
        GridPictogramGroupModel(
          pictograms: [
            CompanyPictogramPointer.business_data,
            CompanyPictogramPointer.actions_great_idea,
            CompanyPictogramPointer.metaphors_energy,
            CompanyPictogramPointer.metaphors_combine,
            CompanyPictogramPointer.metaphors_nature,
            CompanyPictogramPointer.metaphors_dry,
            CompanyPictogramPointer.metaphors_gift,
            CompanyPictogramPointer.metaphors_cycle,
            CompanyPictogramPointer.nature_water,
            CompanyPictogramPointer.objects_ball,
          ],
          selectionIndex: 0,
        ),
        onChangeSelection: (icon) {},
      ),
    ),
  );
}
