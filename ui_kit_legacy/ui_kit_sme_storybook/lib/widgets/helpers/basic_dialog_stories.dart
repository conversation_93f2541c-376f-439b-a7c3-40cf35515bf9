import 'package:flutter/material.dart';
import 'package:storybook_flutter/storybook_flutter.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:ui_kit_sme_storybook/widgets/legacy_story.dart';

class BasicDialogStories {
  static Story basicDialog = LegacyStory(
    section: 'Helper',
    name: 'Basic Dialog',
    background: const Color(0xFF7171DC),
    wrapperBuilder: (_, __, widget) => Material(
      color: const Color(0xFFF5F5F7),
      child: widget,
    ),
    builder: (context, k) => ListView(
      children: const [
        _BasicDialogExample(),
        SizedBox(height: 8),
        Directionality(
          textDirection: TextDirection.rtl,
          child: _BasicDialogExample(),
        ),
      ],
    ),
  );
}

class _BasicDialogVar1 extends StatelessWidget {
  const _BasicDialogVar1({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 600,
      width: 318,
      child: BasicDialog(
        header: BasicHeader(
          prefixIcon: CompanyIconPointer.close,
          // prefixIcon: null,
          onPrefixIconClick: Navigator.of(context).pop,
        ),
        body: const BasicDialogBody(
          titleCompanyRichTextModel: CompanyRichTextModel(
            text: '''Email address already in use''',
            normalStyle: CompanyTextStylePointer.h3medium,
            maxLines: 1,
          ),
          subTitleCompanyRichTextModel: CompanyRichTextModel(
            text:
                '''An <NAME_EMAIL> already exists. The same email address can’t be used twice for two different accounts. Please sign in to your account.''',
            normalStyle: CompanyTextStylePointer.b1,
            normalTextColor: CompanyColorPointer.secondary9,
            accentStyle: CompanyTextStylePointer.b1medium,
            accentTextColor: CompanyColorPointer.secondary9,
            highlightedTextModels: [
              HighlightedTextModel('<EMAIL>'),
            ],
            maxLines: 3,
          ),
        ),
        footer: BasicDialogFooter(
          leadingWidget: CtaButton.m(
            ctaButtonVariant: CtaButtonVariant.primaryType,
            onTap: () async {},
            title: 'SIGN IN',
          ),
        ),
      ),
    );
  }
}

class _BasicDialogVar2 extends StatelessWidget {
  const _BasicDialogVar2({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 600,
      width: 318,
      child: BasicDialog(
        header: BasicHeader(
          prefixIcon: CompanyIconPointer.close,
          // prefixIcon: null,
          onPrefixIconClick: Navigator.of(context).pop,
        ),
        body: const BasicDialogBody(
          titleCompanyRichTextModel: CompanyRichTextModel(
            text: '''Session will expire in 00:58''',
            normalStyle: CompanyTextStylePointer.h3medium,
            maxLines: 1,
          ),
          subTitleCompanyRichTextModel: CompanyRichTextModel(
            text:
                '''Your session will expire soon. You will need to sign in again to continue. Would you like to extend your session or leave it now?''',
            normalStyle: CompanyTextStylePointer.b1,
            normalTextColor: CompanyColorPointer.secondary9,
            accentStyle: CompanyTextStylePointer.b1medium,
            accentTextColor: CompanyColorPointer.secondary9,
            maxLines: 3,
          ),
        ),
        footer: BasicDialogFooter(
          leadingWidget: CtaButton.m(
            ctaButtonVariant: CtaButtonVariant.primaryType,
            onTap: () async {},
            title: 'Extend session',
          ),
          trailingWidget: CtaButton.m(
            ctaButtonVariant: CtaButtonVariant.negativeType,
            onTap: () async {},
            title: 'Sign out',
          ),
        ),
      ),
    );
  }
}

class _BasicDialogWrapper extends StatelessWidget {
  final Widget child;
  final String title;
  final TextDirection textDirection;

  const _BasicDialogWrapper({
    required this.child,
    required this.title,
    required this.textDirection,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 100,
      child: Center(
        child: TextLinkButton(
          TextLinkButtonModel(
            text: 'Open $title ${textDirection.name}',
          ),
          onClick: () => showDialog<Widget>(
            context: context,
            builder: (context) => Directionality(
              textDirection: textDirection,
              child: child,
            ),
          ),
        ),
      ),
    );
  }
}

class _BasicDialogExample extends StatelessWidget {
  const _BasicDialogExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final currentDirection = Directionality.of(context);

    return Column(
      children: [
        _BasicDialogWrapper(
          title: 'Variation 1',
          textDirection: currentDirection,
          child: const _BasicDialogVar1(),
        ),
        _BasicDialogWrapper(
          title: 'Variation 2',
          textDirection: currentDirection,
          child: const _BasicDialogVar2(),
        ), // const _ModalDialogVar2(),
      ],
    );
  }
}
