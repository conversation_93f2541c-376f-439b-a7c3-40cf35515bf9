import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_sme_desktop/ui_kit_sme_desktop.dart';
import 'package:ui_kit_tests/index.dart';

void main() {
  Iterable<Scenario> getMainListItems() {
    return [
      Scenario(
        'MainListItem with accountName and tag',
        MainListItem(
          model: MainListItemModel(
            id: 'id',
            accountNumber: 'accountNumber',
            accountName: 'accountNamea',
            isDisplayLastColumn: true,
            listHeaderPictogramModel: ListHeaderPictogramModel(
              size: HeaderPictogramSize.xs,
              icon: CompanyPictogramPointer.functions_expand,
              variant: HeaderPictogramVariant.var3,
            ),
          ),
          tag: const Tag(
            text: 'In review',
            type: TagType.mute,
          ),
        ),
      ),
      Scenario(
        'MainListItem with accountName, tag, amount and fixedActions',
        MainListItem(
          model: MainListItemModel(
            id: 'id',
            accountNumber: 'accountNumber',
            accountName: 'accountNamea',
            isDisplayLastColumn: true,
            amount: 'amount',
            subAmount: 'subAmount',
            listHeaderPictogramModel: ListHeaderPictogramModel(
              size: HeaderPictogramSize.xs,
              icon: CompanyPictogramPointer.functions_expand,
              variant: HeaderPictogramVariant.var3,
            ),
          ),
          tag: const Tag(
            text: 'In review',
            type: TagType.mute,
          ),
          fixedActions: [
            OptionsPicker(
              model: OptionsPickerModel(
                toggleDropdownMenuModel: ToggleDropdownMenuModel(
                  options: [
                    const DropdownItemModel(text: 'Download'),
                    const DropdownItemModel(text: 'Send reminder'),
                  ],
                ),
              ),
              onSelectOption: (_) {},
            ),
          ],
        ),
      ),
      Scenario(
        'MainListItem with accountName, tag, amount',
        MainListItem(
          model: MainListItemModel(
            id: 'id',
            accountNumber: 'International payment, [Notes to payee]',
            accountName: 'Hossam Halabi Hossam',
            amount: '150,000,00 AED',
            isDisplayLastColumn: true,
            subAmount: '150,000,00 TRY',
            prefixInAccountName: 'To ',
            listHeaderPictogramModel: ListHeaderPictogramModel(
              size: HeaderPictogramSize.xs,
              icon: CompanyPictogramPointer.functions_expand,
              variant: HeaderPictogramVariant.var3,
            ),
          ),
          tag: const Tag(
            text: 'In review',
            type: TagType.mute,
          ),
        ),
      ),
      Scenario(
        'MainListItem with accountName, tag, amount',
        MainListItem(
          model: MainListItemModel(
            id: 'id',
            accountNumber: 'International payment, [Notes to payee]',
            accountName: 'Hossam Halabi Hossam',
            amount: '150,000,00 AED',
            subAmount: '150,000,00 TRY',
            isDisplayLastColumn: true,
            prefixInAccountName: 'To ',
            listHeaderPictogramModel: ListHeaderPictogramModel(
              size: HeaderPictogramSize.xs,
              icon: CompanyPictogramPointer.functions_expand,
              variant: HeaderPictogramVariant.var3,
            ),
          ),
          tag: const Tag(
            text: 'Action Required',
            type: TagType.active,
          ),
        ),
      ),
      Scenario(
        'MainListItem with accountName, tag, amount, avatarModel',
        MainListItem(
          model: MainListItemModel(
            id: 'id',
            accountNumber: 'International payment, [Notes to payee]',
            accountName: 'Hossam Halabi Hossam',
            amount: '150,000,000,000,00 AED',
            subAmount: '150,000,00 TRY',
            isDisplayLastColumn: true,
            avatarModel: const AvatarModel(
              type: AvatarType.empty,
              variant: AvatarColorVariant.secondary5,
              avatarSize: AvatarSize.s,
              text: 'M',
            ),
            prefixInAccountName: 'To ',
            listHeaderPictogramModel: ListHeaderPictogramModel(
              size: HeaderPictogramSize.xs,
              icon: CompanyPictogramPointer.functions_expand,
              variant: HeaderPictogramVariant.var3,
            ),
          ),
          tag: const Tag(
            text: 'Action Required',
            type: TagType.active,
          ),
        ),
      ),
    ];
  }

  startGoldenRunner(
    'MainListItem LTR',
    stateName: 'LTR',
    widths: [600],
    scenarios: getMainListItems,
    companyColorScheme: CompanyColorScheme.smeDesktop(),
    companyTextStyles: CompanyTextStyles.desktop(),
  );

  startGoldenRunner(
    'MainListItem RTL',
    stateName: 'RTL',
    widths: [600],
    scenarios: () => getMainListItems().map((e) => Scenario(
          e.name,
          Directionality(
            textDirection: TextDirection.rtl,
            child: e.child,
          ),
        )),
    companyColorScheme: CompanyColorScheme.smeDesktop(),
    companyTextStyles: CompanyTextStyles.desktop(),
  );
}
