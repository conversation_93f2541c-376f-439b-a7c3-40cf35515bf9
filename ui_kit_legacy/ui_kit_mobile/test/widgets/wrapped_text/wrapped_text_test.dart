import 'package:flutter/material.dart' hide Banner;
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:ui_kit_tests/index.dart';

void main() {
  Iterable<Scenario> getWrappedTexts() {
    return [
      const <PERSON><PERSON><PERSON>(
        'Base wrapped text',
        WrappedText(
          CompanyRichTextModel(
            text: 'Wrapped text check',
            highlightedTextModels: [
              HighlightedTextModel('text'),
            ],
            normalTextColor: CompanyColorPointer.primary2,
            accentTextColor: CompanyColorPointer.secondary10,
          ),
        ),
      ),
      const Scenario(
        'All available horizontal space wrapped text',
        SizedBox(
          width: double.infinity,
          child: WrappedText(
            CompanyRichTextModel(
              text: 'Wrapped text check',
              highlightedTextModels: [
                HighlightedTextModel('text'),
              ],
              normalTextColor: CompanyColorPointer.primary2,
              accentTextColor: CompanyColorPointer.secondary10,
            ),
          ),
        ),
      ),
      const <PERSON><PERSON><PERSON>(
        'All available horizontal space wrapped text + additional text',
        SizedBox(
          width: double.infinity,
          child: WrappedText(
            CompanyRichTextModel(
              text: 'Wrapped text check',
              highlightedTextModels: [
                HighlightedTextModel('text'),
              ],
              normalTextColor: CompanyColorPointer.primary2,
              accentTextColor: CompanyColorPointer.secondary10,
            ),
            additionalText: 'Additional text',
          ),
        ),
      ),
    ];
  }

  startGoldenRunner(
    'Wrapped Text LTR',
    stateName: 'LTR',
    goldenBuilderTheme: GoldenBuilderTheme.inversed,
    widths: [400],
    scenarios: getWrappedTexts,
    companyColorScheme: CompanyColorScheme.retailMobile(),
    companyTextStyles: CompanyTextStyles.mobile(),
  );

  startGoldenRunner(
    'Wrapped Text RTL',
    stateName: 'RTL',
    goldenBuilderTheme: GoldenBuilderTheme.inversed,
    widths: [400],
    scenarios: () => getWrappedTexts().map(
      (e) => Scenario(
        e.name,
        Directionality(
          textDirection: TextDirection.rtl,
          child: e.child,
        ),
      ),
    ),
    companyColorScheme: CompanyColorScheme.retailMobile(),
    companyTextStyles: CompanyTextStyles.mobile(),
  );
}
