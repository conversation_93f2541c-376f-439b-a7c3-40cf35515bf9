import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:ui_kit_tests/index.dart';

void main() {
  startGoldenRunner(
    'ProgressList LTR',
    stateName: 'LTR',
    widths: [400],
    wrapper: (child) => _WrapWith(TextDirection.ltr, child: child),
    scenarios: () => _getScenarios(),
    companyColorScheme: CompanyColorScheme.retailMobile(),
    companyTextStyles: CompanyTextStyles.mobile(),
  );

  startGoldenRunner(
    'ProgressList RTL',
    stateName: 'RTL',
    widths: [400],
    wrapper: (child) => _WrapWith(TextDirection.rtl, child: child),
    scenarios: () => _getScenarios(),
    companyColorScheme: CompanyColorScheme.retailMobile(),
    companyTextStyles: CompanyTextStyles.mobile(),
  );
}

List<Scenario> _getScenarios() {
  final model = ProgressListWidgetModel(
    icon: TileModel.icon(
      icon: CompanyPictogramPointer.metaphors_spark.toGraphicAsset(),
      iconSize: CompanyIconSize.large,
    ),
    title: const LabelModel(
      text: 'Remaining daily limit:',
      textStyle: CompanyTextStylePointer.b3,
      color: CompanyColorPointer.secondary1,
    ),
    subtitle: const LabelModel(
      text: 'Total daily limit: 750,000.00 AED',
      textStyle: CompanyTextStylePointer.b4,
      color: CompanyColorPointer.secondary4,
    ),
    trailingTitle: const LabelModel(
      text: '2,000.00 AED',
      textStyle: CompanyTextStylePointer.b3medium,
      color: CompanyColorPointer.primary3,
    ),
    trailingSubtitle: const LabelModel(
      text: 'Sutext here',
      textStyle: CompanyTextStylePointer.b4,
      color: CompanyColorPointer.secondary4,
    ),
    progress: const ProgressModel(
      value: 0.8,
      variant: ProgressVariant.var2,
    ),
    description: const CompanyLabelModel.v2(text: 'Description here'),
    faq: CompanyLabelModel.v2(
      icon: CompanyLabelIcon.leading(
        CompanyIconPointer.information.toGraphicAsset(),
      ),
      text: 'FAQ here',
    ),
  );

  final modelwithoutSubtitle = ProgressListWidgetModel(
    icon: TileModel.icon(
      icon: CompanyPictogramPointer.metaphors_spark.toGraphicAsset(),
      iconSize: CompanyIconSize.large,
    ),
    title: const LabelModel(
      text: 'Remaining daily limit:',
      textStyle: CompanyTextStylePointer.b3,
      color: CompanyColorPointer.secondary1,
    ),
    trailingTitle: const LabelModel(
      text: '2,000.00 AED',
      textStyle: CompanyTextStylePointer.b3medium,
      color: CompanyColorPointer.primary3,
    ),
    trailingSubtitle: const LabelModel(
      text: 'Sutext here',
      textStyle: CompanyTextStylePointer.b4,
      color: CompanyColorPointer.secondary4,
    ),
    progress: const ProgressModel(
      value: 0.8,
      variant: ProgressVariant.var2,
    ),
    description: const CompanyLabelModel.v2(text: 'Description here'),
    faq: CompanyLabelModel.v2(
      icon: CompanyLabelIcon.leading(
        CompanyIconPointer.information.toGraphicAsset(),
      ),
      text: 'FAQ here',
    ),
  );

  return [
    Scenario(
      'ProgressList',
      ProgressListWidget(model, onTap: () {}),
    ),
    Scenario(
      'ProgressList with trailing button',
      ProgressListWidget(
        model.copyWith(
          faq: null,
          icon: const TileModel.flag(flag: FlagPointer.AE),
          button: const ButtonModel(title: 'Edit', size: ButtonSize.xSmall),
        ),
        onButtonTap: () {},
      ),
    ),
    Scenario(
      'ProgressList',
      ProgressListWidget(modelwithoutSubtitle, onTap: () {}),
    ),
    Scenario(
      'ProgressList with trailing button',
      ProgressListWidget(
        modelwithoutSubtitle.copyWith(
          faq: null,
          icon: const TileModel.flag(flag: FlagPointer.AE),
          button: const ButtonModel(title: 'Edit', size: ButtonSize.xSmall),
        ),
        onButtonTap: () {},
      ),
    ),
  ];
}

class _WrapWith extends StatelessWidget {
  final TextDirection direction;
  final Widget child;

  const _WrapWith(
    this.direction, {
    required this.child,
  });

  @override
  Widget build(BuildContext context) => Directionality(
        textDirection: direction,
        child: ColoredBox(
          color: CompanyColorScheme.retailMobile().background1,
          child: Padding(padding: const EdgeInsets.all(16), child: child),
        ),
      );
}
