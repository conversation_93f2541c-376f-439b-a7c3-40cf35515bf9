import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:intl/intl.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/money.dart';

part 'details_card.freezed.dart';

const _itemPadding = 8.0;
const _horizontalPadding = 16.0;

typedef SelectCardCallback = ValueSetter<String?>;

@freezed
class DetailsCardModel with _$DetailsCardModel {
  const factory DetailsCardModel({
    required DetailsCardLeadingModel leading,
    required DetailsCardBodyModel body,
    required DetailsCardTrailingModel trailing,
    required CardBackground background,
    DetailsCardFooterModel? footer,
    ListDetailsContainerModel? listDetailsContainerModel,
    ValueChanged<int>? onListDetailsPressed,
    DetailsCardErrorModel? error,
  }) = _DetailsCardModel;
}

@freezed
class DetailsCardLeadingModel with _$DetailsCardLeadingModel {
  const factory DetailsCardLeadingModel.tileWithText({
    required String title,
    required String subtitle,
    required TileModel tileMode,
    DetailsCardToggleModel? toggleModel,
  }) = _DetailsCardLeadingTileWithTextModel;
}

@freezed
class DetailsCardToggleModel with _$DetailsCardToggleModel {
  const factory DetailsCardToggleModel.radioButton({
    required String value,
    String? selectedValue,
  }) = _DetailsCardRadioButtonModel;
}

@freezed
class DetailsCardBodyModel with _$DetailsCardBodyModel {
  const factory DetailsCardBodyModel.dateTimeFeeRate({
    required EstimatedTimeModel estimatedTimeModel,
    required FeeModel fee,
    required RateModel rate,
    @Default(true) bool showRate,
  }) = _DetailsCardDateTimeFeeRateBodyModel;
}

@freezed
class EstimatedTimeModel with _$EstimatedTimeModel {
  const factory EstimatedTimeModel.datetime({
    required String title,
    required DateTime dateTime,
  }) = _EstimatedTimeDateTimeModel;

  const factory EstimatedTimeModel.text({
    required String title,
    required String text,
  }) = _EstimatedTimeTextModel;

  const factory EstimatedTimeModel.companyLabel({
    required String title,
    required CompanyLabelModel labelModel,
  }) = _EstimatedTimeCompanyLabelModel;
}

@freezed
class FeeModel with _$FeeModel {
  const factory FeeModel({
    required MoneyModel money,
    VoidCallback? onDetailsTap,
  }) = _FeeModel;
}

@freezed
class MoneyModel with _$MoneyModel {
  const factory MoneyModel({
    required String title,
    required Money money,
    required String zeroMoneyTitle,
  }) = _MoneyModel;
}

@freezed
class RateModel with _$RateModel {
  const factory RateModel({
    required String title,
    required Money targetMoney,
    required Money sourceMoney,
  }) = _RateModel;
}

@freezed
class DetailsCardTrailingModel with _$DetailsCardTrailingModel {
  const factory DetailsCardTrailingModel.amount({
    required MoneyModel amount,
  }) = _DetailsCardTrailingAmoutModel;
}

@freezed
class DetailsCardFooterModel with _$DetailsCardFooterModel {
  const factory DetailsCardFooterModel.richText({
    required CompanyRichTextModel model,
  }) = _DetailsCardFooterRichTextModel;
}

@freezed
class DetailsCardErrorModel with _$DetailsCardErrorModel {
  const factory DetailsCardErrorModel({
    required String text,
  }) = _DetailsCardErrorModel;
}

/// Figma link: https://www.figma.com/file/egjfzEW9I2FegV8Hh06t3J/Wio---Retail-UI-Library?node-id=10029%3A15673
@immutable
class DetailsCard extends StatelessWidget {
  static const errorMessageKey = ValueKey('errorMessageKey');
  static const freeFeesKey = ValueKey('freeFeesKey');
  static const estimatedTimeKey = ValueKey('estimatedTimeKey');
  static const transferFeesKey = ValueKey('transferFeesKey');
  static const infoIconKey = ValueKey('infoIconKey');
  static const youWillPayKey = ValueKey('youWillPayKey');

  final DetailsCardModel model;
  final SelectCardCallback? onSelect;

  const DetailsCard({
    required this.model,
    this.onSelect,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final footer = model.footer;

    return GestureDetector(
      onTap: () => onSelect?.call(model.leading.toggleModel?.value),
      child: Card(
        elevation: 0.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        color: model.background.mapOrNull(
              color: (value) => value.color.colorOf(context),
            ) ??
            context.colorStyling.surface7,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: _horizontalPadding,
            vertical: 24.0,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Column(
                children: [
                  _LeadingWidget(
                    model: model.leading,
                    onSelect: onSelect,
                  ),
                  const SizedBox(height: 24.0),
                  _BodyWidget(model: model.body),
                  if (model.listDetailsContainerModel != null) ...[
                    const Space.vertical(16.0),
                    ListDetailsContainer(
                      model: model.listDetailsContainerModel!,
                      onValuePressed: model.onListDetailsPressed,
                    ),
                  ],
                ],
              ),
              const Space.vertical(16.0),
              Divider(
                color: context.colorStyling.fromPointer(
                  CompanyColorPointer.secondary6,
                ),
                thickness: 1.0,
                height: 1.0,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const Space.vertical(16.0),
                  _TrailingWidget(model: model.trailing),
                  if (model.error != null) ...[
                    const SizedBox(height: 8),
                    _ErrorWidget(model: model.error!),
                  ],
                  if (footer != null) _FooterWidget(footer),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _FooterWidget extends StatelessWidget {
  final DetailsCardFooterModel model;

  const _FooterWidget(
    this.model,
  );

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: model.map(
        richText: (richTextModel) => CompanyRichText(richTextModel.model),
      ),
    );
  }
}

class _LeadingWidget extends StatelessWidget {
  final DetailsCardLeadingModel model;
  final SelectCardCallback? onSelect;

  const _LeadingWidget({
    required this.model,
    this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    return model.map(
      tileWithText: (model) => _TileWithTextLeadingWidget(
        model: model,
        onSelect: onSelect,
      ),
    );
  }
}

class _TileWithTextLeadingWidget extends StatelessWidget {
  final _DetailsCardLeadingTileWithTextModel model;
  final SelectCardCallback? onSelect;

  const _TileWithTextLeadingWidget({
    required this.model,
    this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    final toggle = model.toggleModel;

    return Row(
      children: [
        Tile(
          model: model.tileMode,
          size: 48.0,
        ),
        const SizedBox(width: 16.0),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Label(
                model: LabelModel(
                  text: model.title,
                  textStyle: CompanyTextStylePointer.b3,
                  color: CompanyColorPointer.secondary4,
                ),
              ),
              Label(
                model: LabelModel(
                  text: model.subtitle,
                  textStyle: CompanyTextStylePointer.h4medium,
                  color: CompanyColorPointer.primary3,
                ),
              ),
            ],
          ),
        ),
        if (toggle != null) ...[
          const SizedBox(width: 12),
          toggle.map(
            radioButton: (model) => CompanyRadio<String>(
              onChanged:
                  onSelect != null ? (value) => onSelect?.call(value) : null,
              toggleable: onSelect != null,
              value: model.value,
              groupValue: model.selectedValue,
            ),
          ),
        ],
      ],
    );
  }
}

class _BodyWidget extends StatelessWidget {
  final DetailsCardBodyModel model;

  const _BodyWidget({
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    return model.map(
      dateTimeFeeRate: (model) => _BodyDateTimeFeeRateWidget(model: model),
    );
  }
}

class _BodyDateTimeFeeRateWidget extends StatelessWidget {
  final _DetailsCardDateTimeFeeRateBodyModel model;

  const _BodyDateTimeFeeRateWidget({
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (model.showRate)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _TitleLabelWidget(title: model.rate.title),
              Label(
                model: LabelModel(
                  text: _formatExchangeRate(),
                  textStyle: CompanyTextStylePointer.b2medium,
                  color: CompanyColorPointer.primary3,
                ),
              ),
            ],
          ),
        const SizedBox(height: _itemPadding),
        model.estimatedTimeModel.map(
          datetime: (model) => Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _TitleLabelWidget(title: model.title),
              Label(
                model: LabelModel(
                  text:
                      DateFormat('d MMMM y').format(model.dateTime).toString(),
                  textStyle: CompanyTextStylePointer.b2medium,
                  color: CompanyColorPointer.primary3,
                ),
              ),
            ],
          ),
          text: (model) => Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _TitleLabelWidget(title: model.title),
              Label(
                key: DetailsCard.estimatedTimeKey,
                model: LabelModel(
                  text: model.text,
                  textStyle: CompanyTextStylePointer.b2medium,
                  color: CompanyColorPointer.primary3,
                ),
              ),
            ],
          ),
          companyLabel: (model) => Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _TitleLabelWidget(title: model.title),
              CompanyLabel(
                model.labelModel.map(
                  (it) => it.copyWith(textAlign: LabelTextAlign.end),
                  v2: (it) => it,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: _itemPadding),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Row(
                children: [
                  Flexible(
                    child: _TitleLabelWidget(title: model.fee.money.title),
                  ),
                  if (model.fee.onDetailsTap != null &&
                      !model.fee.money.money.isZero) ...[
                    const SizedBox(width: _itemPadding),
                    InkWell(
                      onTap: model.fee.onDetailsTap,
                      child: Icon(
                        key: DetailsCard.infoIconKey,
                        CompanyIconFactory.get(CompanyIconPointer.information),
                        color: context.colorStyling
                            .fromPointer(CompanyColorPointer.primary1),
                        size: 20,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (model.fee.money.money.isZero)
              CompanyLabel(
                key: DetailsCard.freeFeesKey,
                CompanyLabelModel(
                  textAlign: LabelTextAlign.right,
                  text: model.fee.money.zeroMoneyTitle,
                  color: CompanyColorPointer.primary2,
                  backgroundColor: CompanyColorPointer.primary3,
                ),
              )
            else
              Label(
                key: DetailsCard.transferFeesKey,
                model: LabelModel(
                  text: model.fee.money.money.toCodeOnRightFormat(),
                  textStyle: CompanyTextStylePointer.b2medium,
                  color: CompanyColorPointer.primary3,
                ),
              ),
          ],
        ),
      ],
    );
  }

  String _formatExchangeRate() {
    final targetAmountExchangeRateHasDecimals =
        model.rate.sourceMoney.decimalPart > BigInt.zero;

    return '${_rateFormat(
      model.rate.targetMoney,
    )} = ${_rateFormat(
      model.rate.sourceMoney,
      longDecimal: targetAmountExchangeRateHasDecimals,
    )}';
  }

  String _rateFormat(
    Money money, {
    bool longDecimal = false,
  }) {
    if (longDecimal) {
      return money.toLongDecimalCodeOnRight();
    } else {
      return money.toIntegerWithCodeOnRightFormat();
    }
  }
}

class _TitleLabelWidget extends StatelessWidget {
  final String title;

  const _TitleLabelWidget({
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Label(
      model: LabelModel(
        text: title,
        textStyle: CompanyTextStylePointer.b2,
        color: CompanyColorPointer.primary3,
        maxLines: 1,
        overflow: LabelTextOverflow.fade,
      ),
    );
  }
}

class _TrailingWidget extends StatelessWidget {
  final DetailsCardTrailingModel model;

  const _TrailingWidget({
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    return model.map(
      amount: (model) => _TotalAmountWidget(model: model),
    );
  }
}

class _TotalAmountWidget extends StatelessWidget {
  final _DetailsCardTrailingAmoutModel model;

  const _TotalAmountWidget({
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Label(
          model: LabelModel(
            text: model.amount.title,
            textStyle: CompanyTextStylePointer.h4,
            color: CompanyColorPointer.primary3,
          ),
        ),
        Label(
          key: DetailsCard.youWillPayKey,
          model: LabelModel(
            text: model.amount.money.toCodeOnRightFormat(),
            textStyle: CompanyTextStylePointer.h4medium,
            color: CompanyColorPointer.primary3,
          ),
        ),
      ],
    );
  }
}

class _ErrorWidget extends StatelessWidget {
  final DetailsCardErrorModel model;

  const _ErrorWidget({
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    return Label(
      key: DetailsCard.errorMessageKey,
      model: LabelModel(
        text: model.text,
        textStyle: CompanyTextStylePointer.b3,
        color: CompanyColorPointer.secondary13,
      ),
    );
  }
}
