// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_exception.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$UserException {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() externalIdentitySop3,
    required TResult Function() externalIdentityVerificationFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? externalIdentitySop3,
    TResult? Function()? externalIdentityVerificationFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? externalIdentitySop3,
    TResult Function()? externalIdentityVerificationFailed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_UserExternalIdentitySop3Exception value)
        externalIdentitySop3,
    required TResult Function(
            _UserExternalIdentityVerificationFailedException value)
        externalIdentityVerificationFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_UserExternalIdentitySop3Exception value)?
        externalIdentitySop3,
    TResult? Function(_UserExternalIdentityVerificationFailedException value)?
        externalIdentityVerificationFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_UserExternalIdentitySop3Exception value)?
        externalIdentitySop3,
    TResult Function(_UserExternalIdentityVerificationFailedException value)?
        externalIdentityVerificationFailed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserExceptionCopyWith<$Res> {
  factory $UserExceptionCopyWith(
          UserException value, $Res Function(UserException) then) =
      _$UserExceptionCopyWithImpl<$Res, UserException>;
}

/// @nodoc
class _$UserExceptionCopyWithImpl<$Res, $Val extends UserException>
    implements $UserExceptionCopyWith<$Res> {
  _$UserExceptionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$UserExternalIdentitySop3ExceptionImplCopyWith<$Res> {
  factory _$$UserExternalIdentitySop3ExceptionImplCopyWith(
          _$UserExternalIdentitySop3ExceptionImpl value,
          $Res Function(_$UserExternalIdentitySop3ExceptionImpl) then) =
      __$$UserExternalIdentitySop3ExceptionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UserExternalIdentitySop3ExceptionImplCopyWithImpl<$Res>
    extends _$UserExceptionCopyWithImpl<$Res,
        _$UserExternalIdentitySop3ExceptionImpl>
    implements _$$UserExternalIdentitySop3ExceptionImplCopyWith<$Res> {
  __$$UserExternalIdentitySop3ExceptionImplCopyWithImpl(
      _$UserExternalIdentitySop3ExceptionImpl _value,
      $Res Function(_$UserExternalIdentitySop3ExceptionImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$UserExternalIdentitySop3ExceptionImpl
    implements _UserExternalIdentitySop3Exception {
  const _$UserExternalIdentitySop3ExceptionImpl();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserExternalIdentitySop3ExceptionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() externalIdentitySop3,
    required TResult Function() externalIdentityVerificationFailed,
  }) {
    return externalIdentitySop3();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? externalIdentitySop3,
    TResult? Function()? externalIdentityVerificationFailed,
  }) {
    return externalIdentitySop3?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? externalIdentitySop3,
    TResult Function()? externalIdentityVerificationFailed,
    required TResult orElse(),
  }) {
    if (externalIdentitySop3 != null) {
      return externalIdentitySop3();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_UserExternalIdentitySop3Exception value)
        externalIdentitySop3,
    required TResult Function(
            _UserExternalIdentityVerificationFailedException value)
        externalIdentityVerificationFailed,
  }) {
    return externalIdentitySop3(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_UserExternalIdentitySop3Exception value)?
        externalIdentitySop3,
    TResult? Function(_UserExternalIdentityVerificationFailedException value)?
        externalIdentityVerificationFailed,
  }) {
    return externalIdentitySop3?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_UserExternalIdentitySop3Exception value)?
        externalIdentitySop3,
    TResult Function(_UserExternalIdentityVerificationFailedException value)?
        externalIdentityVerificationFailed,
    required TResult orElse(),
  }) {
    if (externalIdentitySop3 != null) {
      return externalIdentitySop3(this);
    }
    return orElse();
  }
}

abstract class _UserExternalIdentitySop3Exception implements UserException {
  const factory _UserExternalIdentitySop3Exception() =
      _$UserExternalIdentitySop3ExceptionImpl;
}

/// @nodoc
abstract class _$$UserExternalIdentityVerificationFailedExceptionImplCopyWith<
    $Res> {
  factory _$$UserExternalIdentityVerificationFailedExceptionImplCopyWith(
          _$UserExternalIdentityVerificationFailedExceptionImpl value,
          $Res Function(_$UserExternalIdentityVerificationFailedExceptionImpl)
              then) =
      __$$UserExternalIdentityVerificationFailedExceptionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UserExternalIdentityVerificationFailedExceptionImplCopyWithImpl<$Res>
    extends _$UserExceptionCopyWithImpl<$Res,
        _$UserExternalIdentityVerificationFailedExceptionImpl>
    implements
        _$$UserExternalIdentityVerificationFailedExceptionImplCopyWith<$Res> {
  __$$UserExternalIdentityVerificationFailedExceptionImplCopyWithImpl(
      _$UserExternalIdentityVerificationFailedExceptionImpl _value,
      $Res Function(_$UserExternalIdentityVerificationFailedExceptionImpl)
          _then)
      : super(_value, _then);
}

/// @nodoc

class _$UserExternalIdentityVerificationFailedExceptionImpl
    implements _UserExternalIdentityVerificationFailedException {
  const _$UserExternalIdentityVerificationFailedExceptionImpl();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserExternalIdentityVerificationFailedExceptionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() externalIdentitySop3,
    required TResult Function() externalIdentityVerificationFailed,
  }) {
    return externalIdentityVerificationFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? externalIdentitySop3,
    TResult? Function()? externalIdentityVerificationFailed,
  }) {
    return externalIdentityVerificationFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? externalIdentitySop3,
    TResult Function()? externalIdentityVerificationFailed,
    required TResult orElse(),
  }) {
    if (externalIdentityVerificationFailed != null) {
      return externalIdentityVerificationFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_UserExternalIdentitySop3Exception value)
        externalIdentitySop3,
    required TResult Function(
            _UserExternalIdentityVerificationFailedException value)
        externalIdentityVerificationFailed,
  }) {
    return externalIdentityVerificationFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_UserExternalIdentitySop3Exception value)?
        externalIdentitySop3,
    TResult? Function(_UserExternalIdentityVerificationFailedException value)?
        externalIdentityVerificationFailed,
  }) {
    return externalIdentityVerificationFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_UserExternalIdentitySop3Exception value)?
        externalIdentitySop3,
    TResult Function(_UserExternalIdentityVerificationFailedException value)?
        externalIdentityVerificationFailed,
    required TResult orElse(),
  }) {
    if (externalIdentityVerificationFailed != null) {
      return externalIdentityVerificationFailed(this);
    }
    return orElse();
  }
}

abstract class _UserExternalIdentityVerificationFailedException
    implements UserException {
  const factory _UserExternalIdentityVerificationFailedException() =
      _$UserExternalIdentityVerificationFailedExceptionImpl;
}
