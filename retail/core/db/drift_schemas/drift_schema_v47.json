{"_meta": {"description": "This file contains a serialized version of schema entities for drift.", "version": "1.2.0"}, "options": {"store_date_time_values_as_text": false}, "entities": [{"id": 0, "references": [], "type": "table", "data": {"name": "transactions", "was_declared_in_moor": false, "columns": [{"name": "id", "getter_name": "id", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": 1, "max": 50}}]}, {"name": "product_type", "getter_name": "productType", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "account_id", "getter_name": "accountId", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": 1, "max": 50}}]}, {"name": "transaction_account_type", "getter_name": "transactionAccountType", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "transaction_type", "getter_name": "transactionType", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "transaction_identifier", "getter_name": "transactionIdentifier", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "status", "getter_name": "status", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "reference_number", "getter_name": "referenceNumber", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "transaction_mode", "getter_name": "transactionMode", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "transaction_sub_type", "getter_name": "transactionSubType", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "internal_transaction_status", "getter_name": "internalTransactionStatus", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "transaction_date_time", "getter_name": "transactionDateTime", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "debtor_name", "getter_name": "debtor<PERSON>ame", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "customer_id", "getter_name": "customerId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "core_banking_identifier", "getter_name": "coreBankingIdentifier", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "creditor_name", "getter_name": "creditorName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "description", "getter_name": "description", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "sub_description", "getter_name": "subDescription", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "logo_type", "getter_name": "logoType", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "merchant_name", "getter_name": "merchantName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "transaction_image_key", "getter_name": "transactionImageKey", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "amount_value", "getter_name": "amountValue", "moor_type": "double", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_amount", "getter_name": "localAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "exchange_rate", "getter_name": "exchangeRate", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "available_balance", "getter_name": "availableBalance", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "amount_currency", "getter_name": "amountCurrency", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "execution_date", "getter_name": "executionDate", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "transaction_category", "getter_name": "transactionCategory", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "tpp_external_reference_id", "getter_name": "tppExternalReferenceId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fx_from_amount", "getter_name": "fxFromAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fx_to_amount", "getter_name": "fxToAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fx_from_amount_currency", "getter_name": "fxFromAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fx_to_amount_currency", "getter_name": "fxToAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fx_formatted_exchange_rate", "getter_name": "fxFormattedExchangeRate", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "order_id", "getter_name": "orderId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "portfolio_id", "getter_name": "portfolioId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "instrument_id", "getter_name": "instrumentId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "created_date_time", "getter_name": "createdDateTime", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "order_side", "getter_name": "orderSide", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "instrument_name", "getter_name": "instrumentName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "instrument_symbol", "getter_name": "instrumentSymbol", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "instrument_exchange_id", "getter_name": "instrumentExchangeId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "instrument_image_url", "getter_name": "instrumentImageUrl", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "order_type", "getter_name": "orderType", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "error_code", "getter_name": "errorCode", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "error_message", "getter_name": "errorMessage", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "executed_quantity", "getter_name": "executedQuantity", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_quantity", "getter_name": "estimatedQuantity", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "executed_amount_value", "getter_name": "executedAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "executed_amount_currency", "getter_name": "executedAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "executed_net_amount_value", "getter_name": "executedNetAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "executed_net_amount_currency", "getter_name": "executedNetAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_amount_value", "getter_name": "estimatedAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_amount_currency", "getter_name": "estimatedAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_net_amount_value", "getter_name": "estimatedNetAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_net_amount_currency", "getter_name": "estimatedNetAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "commission_amount_value", "getter_name": "commissionAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "commission_amount_currency", "getter_name": "commissionAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_commission_amount_value", "getter_name": "estimatedCommissionAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_commission_amount_currency", "getter_name": "estimatedCommissionAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "vat_value", "getter_name": "vatValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "vat_currency", "getter_name": "vatCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_vat_value", "getter_name": "estimatedVatValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_vat_currency", "getter_name": "estimatedVatCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "average_price_value", "getter_name": "averagePriceValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "average_price_currency", "getter_name": "averagePriceCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_price_value", "getter_name": "estimatedPriceValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_price_currency", "getter_name": "estimatedPriceCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "executed_total_commission_amount", "getter_name": "executedTotalCommissionAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "executed_total_commission_currency", "getter_name": "executedTotalCommissionCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_total_commision_amount", "getter_name": "estimatedTotalCommissionAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_total_commision_currency", "getter_name": "estimatedTotalCommissionCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "commission_calculation_basis_points", "getter_name": "commissionCalculationBasisPoints", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "commission_calculation_base_amount", "getter_name": "commissionCalculationBaseAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "commission_calculation_min_amount", "getter_name": "commissionCalculationMinAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "commission_calculation_base_amount_currency", "getter_name": "commissionCalculationBaseAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "commission_calculation_min_amount_currency", "getter_name": "commissionCalculationMinAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "instrument_exchange_code", "getter_name": "instrumentExchangeCode", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "recurring_order_template_id", "getter_name": "recurringOrderTemplateId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "recurring_order_account_name", "getter_name": "recurringOrderAccountName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "recurring_order_frequency_type", "getter_name": "recurringOrderFrequencyType", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "recurring_order_frequency_day_of_week", "getter_name": "recurringOrderFrequencyDayOfWeek", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "recurring_order_frequency_day_of_month", "getter_name": "recurringOrderFrequencyDayOfMonth", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "expiration_date", "getter_name": "expirationDate", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "is_payment_proof_ready", "getter_name": "isPaymentProofReady", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"is_payment_proof_ready\" IN (0, 1))", "dialectAwareDefaultConstraints": {"sqlite": "CHECK (\"is_payment_proof_ready\" IN (0, 1))"}, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "is_payment_tracking_available", "getter_name": "isPaymentTrackingAvailable", "moor_type": "bool", "nullable": true, "customConstraints": null, "defaultConstraints": "CHECK (\"is_payment_tracking_available\" IN (0, 1))", "dialectAwareDefaultConstraints": {"sqlite": "CHECK (\"is_payment_tracking_available\" IN (0, 1))"}, "default_dart": "const CustomExpression('0')", "default_client_dart": null, "dsl_features": []}, {"name": "payment_fee_total_amount", "getter_name": "paymentFeeTotalAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "payment_fee_total_currency", "getter_name": "paymentFeeTotalCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "payment_target_country_code", "getter_name": "paymentTargetCountryCode", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "payment_phone_number", "getter_name": "paymentPhoneNumber", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "payment_warning_message", "getter_name": "paymentWarningMessage", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "payment_feetype_description_Wio", "getter_name": "paymentFeeTypeDescriptionWio", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "payment_feetype_amount_Wio", "getter_name": "paymentFeeTypeAmountWio", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "payment_feetype_currency_Wio", "getter_name": "paymentFeeTypeCurrencyWio", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "payment_feetype_description_WioCorrespondentBank", "getter_name": "paymentFeeTypeDescriptionWioCorrespondentBank", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "payment_feetype_amount_WioCorrespondentBank", "getter_name": "paymentFeeTypeAmountWioCorrespondentBank", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "payment_feetype_currency_WioCorrespondentBank", "getter_name": "paymentFeeTypeCurrencyWioCorrespondentBank", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "payment_feetype_description_Wise", "getter_name": "paymentFeeTypeDescriptionWise", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "payment_feetype_amount_Wise", "getter_name": "paymentFeeTypeAmountWise", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "payment_feetype_currency_Wise", "getter_name": "paymentFeeTypeCurrencyWise", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_target_country", "getter_name": "internationalTargetCountry", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_completed_date_time", "getter_name": "internationalCompletedDateTime", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_estimated_date_time", "getter_name": "internationalEstimatedDateTime", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_fee_charging_type", "getter_name": "internationalFeeChargingType", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_swift_code", "getter_name": "internationalSwiftCode", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_account_number", "getter_name": "internationalAccountNumber", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_purpose_description", "getter_name": "internationalPurposeDescription", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_purpose_code", "getter_name": "internationalPurposeCode", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_notes", "getter_name": "internationalNotes", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_bank_name", "getter_name": "internationalBankName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_target_amount", "getter_name": "internationalTargetAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_target_amount_currency", "getter_name": "internationalTargetAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_transfer_fee", "getter_name": "internationalTransferFee", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_transfer_fee_currency", "getter_name": "internationalTransferFeeCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fab_expiration_date", "getter_name": "fabExpirationDate", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fab_bank_name", "getter_name": "fabBankName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fab_total_fee_amount", "getter_name": "fabTotalFeeAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fab_total_fee_amount_currency", "getter_name": "fabTotalFeeAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fab_updated_date", "getter_name": "fabUpdatedDate", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fab_cancellation_reason", "getter_name": "fabCancellationReason", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fab_cheque_number", "getter_name": "fabChequeNumber", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fab_cheque_date", "getter_name": "fabChequeDate", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fab_front_image_url", "getter_name": "fabFrontImageUrl", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_purpose_description", "getter_name": "localPurposeDescription", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_purpose_code", "getter_name": "localPurposeCode", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_note", "getter_name": "localNote", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_target_country", "getter_name": "localTargetCountry", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_completed_date_time", "getter_name": "localCompletedDateTime", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_estimated_date_time", "getter_name": "localEstimatedDateTime", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_account_number", "getter_name": "localAccountNumber", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_swift_code", "getter_name": "localSwiftCode", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_bank_name", "getter_name": "localBankName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_target_amount", "getter_name": "localTargetAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_target_amount_currency", "getter_name": "localTargetAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_transfer_source", "getter_name": "localTransferSource", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_transfer_application_id", "getter_name": "localTransferApplicationId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_recipient_name", "getter_name": "localRecipientName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "npss_expiration_date", "getter_name": "npssExpirationDate", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "npss_notes", "getter_name": "npssNotes", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "npss_rtp_initiation_date", "getter_name": "npssRtpInitiationDate", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "npss_show_in_pending_rtps", "getter_name": "npssShowInPendingRtps", "moor_type": "bool", "nullable": true, "customConstraints": null, "defaultConstraints": "CHECK (\"npss_show_in_pending_rtps\" IN (0, 1))", "dialectAwareDefaultConstraints": {"sqlite": "CHECK (\"npss_show_in_pending_rtps\" IN (0, 1))"}, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "npss_show_rtp_resend_button", "getter_name": "npssShowRtpResendButton", "moor_type": "bool", "nullable": true, "customConstraints": null, "defaultConstraints": "CHECK (\"npss_show_rtp_resend_button\" IN (0, 1))", "dialectAwareDefaultConstraints": {"sqlite": "CHECK (\"npss_show_rtp_resend_button\" IN (0, 1))"}, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "npss_original_transaction_reference", "getter_name": "npssOriginalTransactionReference", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "npss_original_transaction_reason", "getter_name": "npssOriginalTransactionReason", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "npss_original_transaction_deeplink", "getter_name": "npssOriginalTransactionDeeplink", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "token", "getter_name": "token", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acc_number_last_4digits", "getter_name": "accNumberLast4Digits", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "card_masked_pan", "getter_name": "cardMaskedPan", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "merchant_country_code", "getter_name": "merchantCountryCode", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "original_card_transaction_currency", "getter_name": "originalCardTransactionCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "original_card_transaction_amount", "getter_name": "originalCardTransactionAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "earned_cash_back", "getter_name": "earned<PERSON>ashback", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "cash_back_percentage", "getter_name": "cashBackPercentage", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "cash_back_transaction_currency", "getter_name": "cashBackTransactionCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "dividend_tax_amount_value", "getter_name": "dividendTaxAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "dividend_tax_amount_currency", "getter_name": "dividendTaxAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "dividend_amount_value", "getter_name": "dividendAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "dividend_amount_currency", "getter_name": "dividendAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "net_dividend_amount_value", "getter_name": "netDividendAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "net_dividend_amount_currency", "getter_name": "netDividendAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "amount_per_instrument_value", "getter_name": "amountPerInstrumentValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "amount_per_instrument_currency", "getter_name": "amountPerInstrumentCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "instrument_quantity", "getter_name": "instrumentQuantity", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "broker_dividend_tax_type", "getter_name": "brokerDividendTaxType", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "dividend_tax_rate", "getter_name": "dividendTaxRate", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "security_lending_rebate_amount_value", "getter_name": "securityLendingRebateAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "security_lending_rebate_amount_currency", "getter_name": "securityLendingRebateAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "security_lending_rebate_tax_amount_currency", "getter_name": "securityLendingRebateTaxAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "security_lending_rebate_tax_amount_value", "getter_name": "securityLendingRebateTaxAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "security_lending_transferred_amount_currency", "getter_name": "securityLendingTransferredAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "security_lending_transferred_amount_value", "getter_name": "securityLendingTransferredAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "security_lending_month", "getter_name": "securityLendingMonth", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "wealth_management_managed_portfolio_id", "getter_name": "wealthManagementManagedPortfolioId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "wealth_management_product_name", "getter_name": "wealthManagementProductName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "wealth_management_product_category", "getter_name": "wealthManagementProductCategory", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "wealth_management_account_name", "getter_name": "wealthManagementAccountName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "wealth_management_estimated_execution_deadline", "getter_name": "wealthManagementEstimatedExecutionDeadline", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "wealth_management_tracker_steps", "getter_name": "wealthManagementTrackerSteps", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const TransactionTrackerStepConverter()", "dart_type_name": "List<TransactionTrackerStepDto>?"}}, {"name": "acquirer_instrument_name", "getter_name": "acquirerInstrumentName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acquirer_instrument_symbol", "getter_name": "acquirerInstrumentSymbol", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acquirer_instrument_exchange_id", "getter_name": "acquirerInstrumentExchangeId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acquirer_instrument_image_url", "getter_name": "acquirerInstrumentImageUrl", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acquiree_instrument_name", "getter_name": "acquireeInstrumentName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acquiree_instrument_symbol", "getter_name": "acquireeInstrumentSymbol", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acquiree_instrument_exchange_id", "getter_name": "acquireeInstrumentExchangeId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acquiree_instrument_image_url", "getter_name": "acquireeInstrumentImageUrl", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "broker_acquisition_account_amount_value", "getter_name": "brokerAcquisitionAccountAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "broker_acquisition_account_amount_currency", "getter_name": "brokerAcquisitionAccountAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "broker_acquisition_position_delta", "getter_name": "brokerAcquisitionPositionDelta", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_company_name", "getter_name": "ipoCompanyName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_subscription_number", "getter_name": "ipoSubscriptionNumber", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_leverage_value", "getter_name": "ipoLeverageValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_leverage_currency", "getter_name": "ipoLeverageCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_amount_value", "getter_name": "ipoAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_amount_currency", "getter_name": "ipoAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_total_amount_value", "getter_name": "ipoTotalAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_total_amount_currency", "getter_name": "ipoTotalAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_company_logo", "getter_name": "ipoCompanyLogo", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_enhancement_value", "getter_name": "ipoEnhancementValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_enhancement_currency", "getter_name": "ipoEnhancementCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_refund_amount_value", "getter_name": "ipoRefundAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_refund_amount_currency", "getter_name": "ipoRefundAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_leverage_paid_value", "getter_name": "ipoLeveragePaidValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_leverage_paid_currency", "getter_name": "ipoLeveragePaidCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_leverage_fee_percentage", "getter_name": "ipoLeverageFeePercentage", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_leverage_fee_value", "getter_name": "ipoLeverageFeeValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_leverage_fee_currency", "getter_name": "ipoLeverageFeeCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_allocation_fee_percentage", "getter_name": "ipoAllocationFeePercentage", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_alloted_fee_money", "getter_name": "ipoAllotedFeeMoney", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_alloted_fee_money_currency", "getter_name": "ipoAllotedFeeMoneyCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_allocated_money", "getter_name": "ipoAllocatedMoney", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_allocated_money_currency", "getter_name": "ipoAllocatedMoneyCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "dynamic_details", "getter_name": "dynamicDetails", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": "const CustomExpression('\\'\\'')", "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const TransactionDynamicDetailsConverter()", "dart_type_name": "List<DtoTransactionDynamicDetails>"}}, {"name": "linked_transaction_fee_details", "getter_name": "linkedTransactionFeeDetails", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": "const CustomExpression('\\'\\'')", "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const TransactionFeeDetailsConverter()", "dart_type_name": "List<TransactionFeeDetail>"}}, {"name": "cashback_calculations", "getter_name": "cashbackCalculations", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const CashbackCalculationsConverter()", "dart_type_name": "List<CashbackCalculationDetailsDto>?"}}, {"name": "carbon_emission_in_grams", "getter_name": "carbonEmissionInGrams", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "carbon_emission_in_ounces", "getter_name": "carbonEmissionInOunces", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "recurring_transfer_rule_id", "getter_name": "recurringTransferRuleId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["id"]}}]}