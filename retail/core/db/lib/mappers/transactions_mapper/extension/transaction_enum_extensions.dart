import 'package:wio_common_feature_transaction_api/index.dart';

extension TransactionCategoryExtension on TransactionCategory? {
  String get stringValue {
    switch (this) {
      case TransactionCategory.health:
        return 'health';
      case TransactionCategory.shopping:
        return 'shopping';
      case TransactionCategory.travel:
        return 'travel';
      case TransactionCategory.utility:
        return 'utility';
      case TransactionCategory.grocery:
        return 'grocery';
      case TransactionCategory.restaurant:
        return 'restaurant';
      case TransactionCategory.hotel:
        return 'hotel';
      case TransactionCategory.service:
        return 'service';
      case TransactionCategory.entertainment:
        return 'entertainment';
      case TransactionCategory.tax:
        return 'tax';
      case TransactionCategory.rental:
        return 'rental';
      case TransactionCategory.transportation:
        return 'transportation';
      case TransactionCategory.subscription:
        return 'subscription';
      case TransactionCategory.education:
        return 'education';
      case TransactionCategory.general:
        return 'general';
      case TransactionCategory.transfer:
        return 'transfer';
      case TransactionCategory.cash:
        return 'cash';
      case TransactionCategory.unknown:
      case null:
        return 'unknown';
    }
  }
}
