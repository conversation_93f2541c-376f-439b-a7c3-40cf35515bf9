// dart format width=80
// GENERATED CODE, DO NOT EDIT BY HAND.
// ignore_for_file: type=lint
import 'package:drift/drift.dart';

class Transactions extends Table
    with TableInfo<Transactions, TransactionsData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  Transactions(this.attachedDatabase, [this._alias]);
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 50),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  late final GeneratedColumn<String> productType = GeneratedColumn<String>(
      'product_type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  late final GeneratedColumn<String> accountId = GeneratedColumn<String>(
      'account_id', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 50),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  late final GeneratedColumn<String> transactionAccountType =
      GeneratedColumn<String>('transaction_account_type', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> transactionType = GeneratedColumn<String>(
      'transaction_type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  late final GeneratedColumn<String> transactionIdentifier =
      GeneratedColumn<String>('transaction_identifier', aliasedName, false,
          type: DriftSqlType.string, requiredDuringInsert: true);
  late final GeneratedColumn<String> status = GeneratedColumn<String>(
      'status', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  late final GeneratedColumn<String> referenceNumber = GeneratedColumn<String>(
      'reference_number', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> transactionMode = GeneratedColumn<String>(
      'transaction_mode', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  late final GeneratedColumn<String> transactionSubType =
      GeneratedColumn<String>('transaction_sub_type', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> internalTransactionStatus =
      GeneratedColumn<String>('internal_transaction_status', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<DateTime> transactionDateTime =
      GeneratedColumn<DateTime>('transaction_date_time', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  late final GeneratedColumn<String> debtorName = GeneratedColumn<String>(
      'debtor_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> customerId = GeneratedColumn<String>(
      'customer_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> coreBankingIdentifier =
      GeneratedColumn<String>('core_banking_identifier', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> creditorName = GeneratedColumn<String>(
      'creditor_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> description = GeneratedColumn<String>(
      'description', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> subDescription = GeneratedColumn<String>(
      'sub_description', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> logoType = GeneratedColumn<String>(
      'logo_type', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> merchantName = GeneratedColumn<String>(
      'merchant_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> transactionImageKey =
      GeneratedColumn<String>('transaction_image_key', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> amountValue = GeneratedColumn<double>(
      'amount_value', aliasedName, false,
      type: DriftSqlType.double, requiredDuringInsert: true);
  late final GeneratedColumn<double> localAmount = GeneratedColumn<double>(
      'local_amount', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<double> exchangeRate = GeneratedColumn<double>(
      'exchange_rate', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<double> availableBalance = GeneratedColumn<double>(
      'available_balance', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> amountCurrency = GeneratedColumn<String>(
      'amount_currency', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  late final GeneratedColumn<DateTime> executionDate =
      GeneratedColumn<DateTime>('execution_date', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  late final GeneratedColumn<String> transactionCategory =
      GeneratedColumn<String>('transaction_category', aliasedName, false,
          type: DriftSqlType.string, requiredDuringInsert: true);
  late final GeneratedColumn<String> tppExternalReferenceId =
      GeneratedColumn<String>('tpp_external_reference_id', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> fxFromAmount = GeneratedColumn<double>(
      'fx_from_amount', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<double> fxToAmount = GeneratedColumn<double>(
      'fx_to_amount', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> fxFromAmountCurrency =
      GeneratedColumn<String>('fx_from_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> fxToAmountCurrency =
      GeneratedColumn<String>('fx_to_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> fxFormattedExchangeRate =
      GeneratedColumn<String>('fx_formatted_exchange_rate', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> orderId = GeneratedColumn<String>(
      'order_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> portfolioId = GeneratedColumn<String>(
      'portfolio_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> instrumentId = GeneratedColumn<String>(
      'instrument_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<DateTime> createdDateTime =
      GeneratedColumn<DateTime>('created_date_time', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  late final GeneratedColumn<String> orderSide = GeneratedColumn<String>(
      'order_side', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> instrumentName = GeneratedColumn<String>(
      'instrument_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> instrumentSymbol = GeneratedColumn<String>(
      'instrument_symbol', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> instrumentExchangeId =
      GeneratedColumn<String>('instrument_exchange_id', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> instrumentImageUrl =
      GeneratedColumn<String>('instrument_image_url', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> orderType = GeneratedColumn<String>(
      'order_type', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<int> errorCode = GeneratedColumn<int>(
      'error_code', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  late final GeneratedColumn<String> errorMessage = GeneratedColumn<String>(
      'error_message', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> executedQuantity = GeneratedColumn<double>(
      'executed_quantity', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<double> estimatedQuantity =
      GeneratedColumn<double>('estimated_quantity', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<double> executedAmountValue =
      GeneratedColumn<double>('executed_amount_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> executedAmountCurrency =
      GeneratedColumn<String>('executed_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> executedNetAmountValue =
      GeneratedColumn<double>('executed_net_amount_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> executedNetAmountCurrency =
      GeneratedColumn<String>('executed_net_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> estimatedAmountValue =
      GeneratedColumn<double>('estimated_amount_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> estimatedAmountCurrency =
      GeneratedColumn<String>('estimated_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> estimatedNetAmountValue =
      GeneratedColumn<double>('estimated_net_amount_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> estimatedNetAmountCurrency =
      GeneratedColumn<String>(
          'estimated_net_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> commissionAmountValue =
      GeneratedColumn<double>('commission_amount_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> commissionAmountCurrency =
      GeneratedColumn<String>('commission_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> estimatedCommissionAmountValue =
      GeneratedColumn<double>(
          'estimated_commission_amount_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> estimatedCommissionAmountCurrency =
      GeneratedColumn<String>(
          'estimated_commission_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> vatValue = GeneratedColumn<double>(
      'vat_value', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> vatCurrency = GeneratedColumn<String>(
      'vat_currency', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> estimatedVatValue =
      GeneratedColumn<double>('estimated_vat_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> estimatedVatCurrency =
      GeneratedColumn<String>('estimated_vat_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> averagePriceValue =
      GeneratedColumn<double>('average_price_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> averagePriceCurrency =
      GeneratedColumn<String>('average_price_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> estimatedPriceValue =
      GeneratedColumn<double>('estimated_price_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> estimatedPriceCurrency =
      GeneratedColumn<String>('estimated_price_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> executedTotalCommissionAmount =
      GeneratedColumn<double>(
          'executed_total_commission_amount', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> executedTotalCommissionCurrency =
      GeneratedColumn<String>(
          'executed_total_commission_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> estimatedTotalCommissionAmount =
      GeneratedColumn<double>(
          'estimated_total_commision_amount', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> estimatedTotalCommissionCurrency =
      GeneratedColumn<String>(
          'estimated_total_commision_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> commissionCalculationBasisPoints =
      GeneratedColumn<double>(
          'commission_calculation_basis_points', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<double> commissionCalculationBaseAmount =
      GeneratedColumn<double>(
          'commission_calculation_base_amount', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<double> commissionCalculationMinAmount =
      GeneratedColumn<double>(
          'commission_calculation_min_amount', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> commissionCalculationBaseAmountCurrency =
      GeneratedColumn<String>(
          'commission_calculation_base_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> commissionCalculationMinAmountCurrency =
      GeneratedColumn<String>(
          'commission_calculation_min_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> instrumentExchangeCode =
      GeneratedColumn<String>('instrument_exchange_code', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> recurringOrderTemplateId =
      GeneratedColumn<String>('recurring_order_template_id', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> recurringOrderAccountName =
      GeneratedColumn<String>('recurring_order_account_name', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> recurringOrderFrequencyType =
      GeneratedColumn<String>(
          'recurring_order_frequency_type', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> recurringOrderFrequencyDayOfWeek =
      GeneratedColumn<String>(
          'recurring_order_frequency_day_of_week', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<int> recurringOrderFrequencyDayOfMonth =
      GeneratedColumn<int>(
          'recurring_order_frequency_day_of_month', aliasedName, true,
          type: DriftSqlType.int, requiredDuringInsert: false);
  late final GeneratedColumn<DateTime> expirationDate =
      GeneratedColumn<DateTime>('expiration_date', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  late final GeneratedColumn<bool> isPaymentProofReady = GeneratedColumn<bool>(
      'is_payment_proof_ready', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("is_payment_proof_ready" IN (0, 1))'));
  late final GeneratedColumn<bool> isPaymentTrackingAvailable =
      GeneratedColumn<bool>('is_payment_tracking_available', aliasedName, true,
          type: DriftSqlType.bool,
          requiredDuringInsert: false,
          defaultConstraints: GeneratedColumn.constraintIsAlways(
              'CHECK ("is_payment_tracking_available" IN (0, 1))'),
          defaultValue: const CustomExpression('0'));
  late final GeneratedColumn<double> paymentFeeTotalAmount =
      GeneratedColumn<double>('payment_fee_total_amount', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> paymentFeeTotalCurrency =
      GeneratedColumn<String>('payment_fee_total_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> paymentTargetCountryCode =
      GeneratedColumn<String>('payment_target_country_code', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> paymentPhoneNumber =
      GeneratedColumn<String>('payment_phone_number', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> paymentWarningMessage =
      GeneratedColumn<String>('payment_warning_message', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> paymentFeeTypeDescriptionWio =
      GeneratedColumn<String>(
          'payment_feetype_description_Wio', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> paymentFeeTypeAmountWio =
      GeneratedColumn<double>('payment_feetype_amount_Wio', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> paymentFeeTypeCurrencyWio =
      GeneratedColumn<String>('payment_feetype_currency_Wio', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String>
      paymentFeeTypeDescriptionWioCorrespondentBank = GeneratedColumn<String>(
          'payment_feetype_description_WioCorrespondentBank', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> paymentFeeTypeAmountWioCorrespondentBank =
      GeneratedColumn<double>(
          'payment_feetype_amount_WioCorrespondentBank', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String>
      paymentFeeTypeCurrencyWioCorrespondentBank = GeneratedColumn<String>(
          'payment_feetype_currency_WioCorrespondentBank', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> paymentFeeTypeDescriptionWise =
      GeneratedColumn<String>(
          'payment_feetype_description_Wise', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> paymentFeeTypeAmountWise =
      GeneratedColumn<double>('payment_feetype_amount_Wise', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> paymentFeeTypeCurrencyWise =
      GeneratedColumn<String>(
          'payment_feetype_currency_Wise', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> internationalTargetCountry =
      GeneratedColumn<String>('international_target_country', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<DateTime> internationalCompletedDateTime =
      GeneratedColumn<DateTime>(
          'international_completed_date_time', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  late final GeneratedColumn<String> internationalEstimatedDateTime =
      GeneratedColumn<String>(
          'international_estimated_date_time', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> internationalFeeChargingType =
      GeneratedColumn<String>(
          'international_fee_charging_type', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> internationalSwiftCode =
      GeneratedColumn<String>('international_swift_code', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> internationalAccountNumber =
      GeneratedColumn<String>('international_account_number', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> internationalPurposeDescription =
      GeneratedColumn<String>(
          'international_purpose_description', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> internationalPurposeCode =
      GeneratedColumn<String>('international_purpose_code', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> internationalNotes =
      GeneratedColumn<String>('international_notes', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> internationalBankName =
      GeneratedColumn<String>('international_bank_name', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> internationalTargetAmount =
      GeneratedColumn<double>('international_target_amount', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> internationalTargetAmountCurrency =
      GeneratedColumn<String>(
          'international_target_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> internationalTransferFee =
      GeneratedColumn<double>('international_transfer_fee', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> internationalTransferFeeCurrency =
      GeneratedColumn<String>(
          'international_transfer_fee_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<DateTime> fabExpirationDate =
      GeneratedColumn<DateTime>('fab_expiration_date', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  late final GeneratedColumn<String> fabBankName = GeneratedColumn<String>(
      'fab_bank_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> fabTotalFeeAmount =
      GeneratedColumn<double>('fab_total_fee_amount', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> fabTotalFeeAmountCurrency =
      GeneratedColumn<String>(
          'fab_total_fee_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<DateTime> fabUpdatedDate =
      GeneratedColumn<DateTime>('fab_updated_date', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  late final GeneratedColumn<String> fabCancellationReason =
      GeneratedColumn<String>('fab_cancellation_reason', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> fabChequeNumber = GeneratedColumn<String>(
      'fab_cheque_number', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<DateTime> fabChequeDate =
      GeneratedColumn<DateTime>('fab_cheque_date', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  late final GeneratedColumn<String> fabFrontImageUrl = GeneratedColumn<String>(
      'fab_front_image_url', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> localPurposeDescription =
      GeneratedColumn<String>('local_purpose_description', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> localPurposeCode = GeneratedColumn<String>(
      'local_purpose_code', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> localNote = GeneratedColumn<String>(
      'local_note', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> localTargetCountry =
      GeneratedColumn<String>('local_target_country', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<DateTime> localCompletedDateTime =
      GeneratedColumn<DateTime>('local_completed_date_time', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  late final GeneratedColumn<DateTime> localEstimatedDateTime =
      GeneratedColumn<DateTime>('local_estimated_date_time', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  late final GeneratedColumn<String> localAccountNumber =
      GeneratedColumn<String>('local_account_number', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> localSwiftCode = GeneratedColumn<String>(
      'local_swift_code', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> localBankName = GeneratedColumn<String>(
      'local_bank_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> localTargetAmount =
      GeneratedColumn<double>('local_target_amount', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> localTargetAmountCurrency =
      GeneratedColumn<String>('local_target_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> localTransferSource =
      GeneratedColumn<String>('local_transfer_source', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> localTransferApplicationId =
      GeneratedColumn<String>(
          'local_transfer_application_id', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> localRecipientName =
      GeneratedColumn<String>('local_recipient_name', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<DateTime> npssExpirationDate =
      GeneratedColumn<DateTime>('npss_expiration_date', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  late final GeneratedColumn<String> npssNotes = GeneratedColumn<String>(
      'npss_notes', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<DateTime> npssRtpInitiationDate =
      GeneratedColumn<DateTime>('npss_rtp_initiation_date', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  late final GeneratedColumn<bool> npssShowInPendingRtps =
      GeneratedColumn<bool>('npss_show_in_pending_rtps', aliasedName, true,
          type: DriftSqlType.bool,
          requiredDuringInsert: false,
          defaultConstraints: GeneratedColumn.constraintIsAlways(
              'CHECK ("npss_show_in_pending_rtps" IN (0, 1))'));
  late final GeneratedColumn<bool> npssShowRtpResendButton =
      GeneratedColumn<bool>('npss_show_rtp_resend_button', aliasedName, true,
          type: DriftSqlType.bool,
          requiredDuringInsert: false,
          defaultConstraints: GeneratedColumn.constraintIsAlways(
              'CHECK ("npss_show_rtp_resend_button" IN (0, 1))'));
  late final GeneratedColumn<String> npssOriginalTransactionReference =
      GeneratedColumn<String>(
          'npss_original_transaction_reference', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> npssOriginalTransactionReason =
      GeneratedColumn<String>(
          'npss_original_transaction_reason', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> npssOriginalTransactionDeeplink =
      GeneratedColumn<String>(
          'npss_original_transaction_deeplink', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> token = GeneratedColumn<String>(
      'token', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> accNumberLast4Digits =
      GeneratedColumn<String>('acc_number_last_4digits', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> cardMaskedPan = GeneratedColumn<String>(
      'card_masked_pan', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> merchantCountryCode =
      GeneratedColumn<String>('merchant_country_code', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> originalCardTransactionCurrency =
      GeneratedColumn<String>(
          'original_card_transaction_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> originalCardTransactionAmount =
      GeneratedColumn<double>(
          'original_card_transaction_amount', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<double> earnedCashback = GeneratedColumn<double>(
      'earned_cash_back', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<double> cashBackPercentage =
      GeneratedColumn<double>('cash_back_percentage', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> cashBackTransactionCurrency =
      GeneratedColumn<String>(
          'cash_back_transaction_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> dividendTaxAmountValue =
      GeneratedColumn<double>('dividend_tax_amount_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> dividendTaxAmountCurrency =
      GeneratedColumn<String>('dividend_tax_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> dividendAmountValue =
      GeneratedColumn<double>('dividend_amount_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> dividendAmountCurrency =
      GeneratedColumn<String>('dividend_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> netDividendAmountValue =
      GeneratedColumn<double>('net_dividend_amount_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> netDividendAmountCurrency =
      GeneratedColumn<String>('net_dividend_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> amountPerInstrumentValue =
      GeneratedColumn<double>('amount_per_instrument_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> amountPerInstrumentCurrency =
      GeneratedColumn<String>(
          'amount_per_instrument_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> instrumentQuantity =
      GeneratedColumn<double>('instrument_quantity', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> brokerDividendTaxType =
      GeneratedColumn<String>('broker_dividend_tax_type', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> dividendTaxRate = GeneratedColumn<double>(
      'dividend_tax_rate', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<double> securityLendingRebateAmountValue =
      GeneratedColumn<double>(
          'security_lending_rebate_amount_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> securityLendingRebateAmountCurrency =
      GeneratedColumn<String>(
          'security_lending_rebate_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> securityLendingRebateTaxAmountCurrency =
      GeneratedColumn<String>(
          'security_lending_rebate_tax_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> securityLendingRebateTaxAmountValue =
      GeneratedColumn<double>(
          'security_lending_rebate_tax_amount_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> securityLendingTransferredAmountCurrency =
      GeneratedColumn<String>(
          'security_lending_transferred_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> securityLendingTransferredAmountValue =
      GeneratedColumn<double>(
          'security_lending_transferred_amount_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<int> securityLendingMonth = GeneratedColumn<int>(
      'security_lending_month', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  late final GeneratedColumn<String> wealthManagementManagedPortfolioId =
      GeneratedColumn<String>(
          'wealth_management_managed_portfolio_id', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> wealthManagementProductName =
      GeneratedColumn<String>(
          'wealth_management_product_name', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> wealthManagementProductCategory =
      GeneratedColumn<String>(
          'wealth_management_product_category', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> wealthManagementAccountName =
      GeneratedColumn<String>(
          'wealth_management_account_name', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String>
      wealthManagementEstimatedExecutionDeadline = GeneratedColumn<String>(
          'wealth_management_estimated_execution_deadline', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> wealthManagementTrackerSteps =
      GeneratedColumn<String>(
          'wealth_management_tracker_steps', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> acquirerInstrumentName =
      GeneratedColumn<String>('acquirer_instrument_name', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> acquirerInstrumentSymbol =
      GeneratedColumn<String>('acquirer_instrument_symbol', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> acquirerInstrumentExchangeId =
      GeneratedColumn<String>(
          'acquirer_instrument_exchange_id', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> acquirerInstrumentImageUrl =
      GeneratedColumn<String>(
          'acquirer_instrument_image_url', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> acquireeInstrumentName =
      GeneratedColumn<String>('acquiree_instrument_name', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> acquireeInstrumentSymbol =
      GeneratedColumn<String>('acquiree_instrument_symbol', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> acquireeInstrumentExchangeId =
      GeneratedColumn<String>(
          'acquiree_instrument_exchange_id', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> acquireeInstrumentImageUrl =
      GeneratedColumn<String>(
          'acquiree_instrument_image_url', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> brokerAcquisitionAccountAmountValue =
      GeneratedColumn<double>(
          'broker_acquisition_account_amount_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> brokerAcquisitionAccountAmountCurrency =
      GeneratedColumn<String>(
          'broker_acquisition_account_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> brokerAcquisitionPositionDelta =
      GeneratedColumn<double>(
          'broker_acquisition_position_delta', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> ipoCompanyName = GeneratedColumn<String>(
      'ipo_company_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> ipoSubscriptionNumber =
      GeneratedColumn<String>('ipo_subscription_number', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> ipoLeverageValue = GeneratedColumn<double>(
      'ipo_leverage_value', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> ipoLeverageCurrency =
      GeneratedColumn<String>('ipo_leverage_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> ipoAmountValue = GeneratedColumn<double>(
      'ipo_amount_value', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> ipoAmountCurrency =
      GeneratedColumn<String>('ipo_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> ipoTotalAmountValue =
      GeneratedColumn<double>('ipo_total_amount_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> ipoTotalAmountCurrency =
      GeneratedColumn<String>('ipo_total_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> ipoCompanyLogo = GeneratedColumn<String>(
      'ipo_company_logo', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> ipoEnhancementValue =
      GeneratedColumn<double>('ipo_enhancement_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> ipoEnhancementCurrency =
      GeneratedColumn<String>('ipo_enhancement_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> ipoRefundAmountValue =
      GeneratedColumn<double>('ipo_refund_amount_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> ipoRefundAmountCurrency =
      GeneratedColumn<String>('ipo_refund_amount_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> ipoLeveragePaidValue =
      GeneratedColumn<double>('ipo_leverage_paid_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> ipoLeveragePaidCurrency =
      GeneratedColumn<String>('ipo_leverage_paid_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> ipoLeverageFeePercentage =
      GeneratedColumn<double>('ipo_leverage_fee_percentage', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<double> ipoLeverageFeeValue =
      GeneratedColumn<double>('ipo_leverage_fee_value', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> ipoLeverageFeeCurrency =
      GeneratedColumn<String>('ipo_leverage_fee_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> ipoAllocationFeePercentage =
      GeneratedColumn<double>(
          'ipo_allocation_fee_percentage', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<double> ipoAllotedFeeMoney =
      GeneratedColumn<double>('ipo_alloted_fee_money', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> ipoAllotedFeeMoneyCurrency =
      GeneratedColumn<String>(
          'ipo_alloted_fee_money_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> ipoAllocatedMoney =
      GeneratedColumn<double>('ipo_allocated_money', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> ipoAllocatedMoneyCurrency =
      GeneratedColumn<String>('ipo_allocated_money_currency', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<String> dynamicDetails = GeneratedColumn<String>(
      'dynamic_details', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const CustomExpression('\'\''));
  late final GeneratedColumn<String> linkedTransactionFeeDetails =
      GeneratedColumn<String>(
          'linked_transaction_fee_details', aliasedName, false,
          type: DriftSqlType.string,
          requiredDuringInsert: false,
          defaultValue: const CustomExpression('\'\''));
  late final GeneratedColumn<String> cashbackCalculations =
      GeneratedColumn<String>('cashback_calculations', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  late final GeneratedColumn<double> carbonEmissionInGrams =
      GeneratedColumn<double>('carbon_emission_in_grams', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<double> carbonEmissionInOunces =
      GeneratedColumn<double>('carbon_emission_in_ounces', aliasedName, true,
          type: DriftSqlType.double, requiredDuringInsert: false);
  late final GeneratedColumn<String> recurringTransferRuleId =
      GeneratedColumn<String>('recurring_transfer_rule_id', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        productType,
        accountId,
        transactionAccountType,
        transactionType,
        transactionIdentifier,
        status,
        referenceNumber,
        transactionMode,
        transactionSubType,
        internalTransactionStatus,
        transactionDateTime,
        debtorName,
        customerId,
        coreBankingIdentifier,
        creditorName,
        description,
        subDescription,
        logoType,
        merchantName,
        transactionImageKey,
        amountValue,
        localAmount,
        exchangeRate,
        availableBalance,
        amountCurrency,
        executionDate,
        transactionCategory,
        tppExternalReferenceId,
        fxFromAmount,
        fxToAmount,
        fxFromAmountCurrency,
        fxToAmountCurrency,
        fxFormattedExchangeRate,
        orderId,
        portfolioId,
        instrumentId,
        createdDateTime,
        orderSide,
        instrumentName,
        instrumentSymbol,
        instrumentExchangeId,
        instrumentImageUrl,
        orderType,
        errorCode,
        errorMessage,
        executedQuantity,
        estimatedQuantity,
        executedAmountValue,
        executedAmountCurrency,
        executedNetAmountValue,
        executedNetAmountCurrency,
        estimatedAmountValue,
        estimatedAmountCurrency,
        estimatedNetAmountValue,
        estimatedNetAmountCurrency,
        commissionAmountValue,
        commissionAmountCurrency,
        estimatedCommissionAmountValue,
        estimatedCommissionAmountCurrency,
        vatValue,
        vatCurrency,
        estimatedVatValue,
        estimatedVatCurrency,
        averagePriceValue,
        averagePriceCurrency,
        estimatedPriceValue,
        estimatedPriceCurrency,
        executedTotalCommissionAmount,
        executedTotalCommissionCurrency,
        estimatedTotalCommissionAmount,
        estimatedTotalCommissionCurrency,
        commissionCalculationBasisPoints,
        commissionCalculationBaseAmount,
        commissionCalculationMinAmount,
        commissionCalculationBaseAmountCurrency,
        commissionCalculationMinAmountCurrency,
        instrumentExchangeCode,
        recurringOrderTemplateId,
        recurringOrderAccountName,
        recurringOrderFrequencyType,
        recurringOrderFrequencyDayOfWeek,
        recurringOrderFrequencyDayOfMonth,
        expirationDate,
        isPaymentProofReady,
        isPaymentTrackingAvailable,
        paymentFeeTotalAmount,
        paymentFeeTotalCurrency,
        paymentTargetCountryCode,
        paymentPhoneNumber,
        paymentWarningMessage,
        paymentFeeTypeDescriptionWio,
        paymentFeeTypeAmountWio,
        paymentFeeTypeCurrencyWio,
        paymentFeeTypeDescriptionWioCorrespondentBank,
        paymentFeeTypeAmountWioCorrespondentBank,
        paymentFeeTypeCurrencyWioCorrespondentBank,
        paymentFeeTypeDescriptionWise,
        paymentFeeTypeAmountWise,
        paymentFeeTypeCurrencyWise,
        internationalTargetCountry,
        internationalCompletedDateTime,
        internationalEstimatedDateTime,
        internationalFeeChargingType,
        internationalSwiftCode,
        internationalAccountNumber,
        internationalPurposeDescription,
        internationalPurposeCode,
        internationalNotes,
        internationalBankName,
        internationalTargetAmount,
        internationalTargetAmountCurrency,
        internationalTransferFee,
        internationalTransferFeeCurrency,
        fabExpirationDate,
        fabBankName,
        fabTotalFeeAmount,
        fabTotalFeeAmountCurrency,
        fabUpdatedDate,
        fabCancellationReason,
        fabChequeNumber,
        fabChequeDate,
        fabFrontImageUrl,
        localPurposeDescription,
        localPurposeCode,
        localNote,
        localTargetCountry,
        localCompletedDateTime,
        localEstimatedDateTime,
        localAccountNumber,
        localSwiftCode,
        localBankName,
        localTargetAmount,
        localTargetAmountCurrency,
        localTransferSource,
        localTransferApplicationId,
        localRecipientName,
        npssExpirationDate,
        npssNotes,
        npssRtpInitiationDate,
        npssShowInPendingRtps,
        npssShowRtpResendButton,
        npssOriginalTransactionReference,
        npssOriginalTransactionReason,
        npssOriginalTransactionDeeplink,
        token,
        accNumberLast4Digits,
        cardMaskedPan,
        merchantCountryCode,
        originalCardTransactionCurrency,
        originalCardTransactionAmount,
        earnedCashback,
        cashBackPercentage,
        cashBackTransactionCurrency,
        dividendTaxAmountValue,
        dividendTaxAmountCurrency,
        dividendAmountValue,
        dividendAmountCurrency,
        netDividendAmountValue,
        netDividendAmountCurrency,
        amountPerInstrumentValue,
        amountPerInstrumentCurrency,
        instrumentQuantity,
        brokerDividendTaxType,
        dividendTaxRate,
        securityLendingRebateAmountValue,
        securityLendingRebateAmountCurrency,
        securityLendingRebateTaxAmountCurrency,
        securityLendingRebateTaxAmountValue,
        securityLendingTransferredAmountCurrency,
        securityLendingTransferredAmountValue,
        securityLendingMonth,
        wealthManagementManagedPortfolioId,
        wealthManagementProductName,
        wealthManagementProductCategory,
        wealthManagementAccountName,
        wealthManagementEstimatedExecutionDeadline,
        wealthManagementTrackerSteps,
        acquirerInstrumentName,
        acquirerInstrumentSymbol,
        acquirerInstrumentExchangeId,
        acquirerInstrumentImageUrl,
        acquireeInstrumentName,
        acquireeInstrumentSymbol,
        acquireeInstrumentExchangeId,
        acquireeInstrumentImageUrl,
        brokerAcquisitionAccountAmountValue,
        brokerAcquisitionAccountAmountCurrency,
        brokerAcquisitionPositionDelta,
        ipoCompanyName,
        ipoSubscriptionNumber,
        ipoLeverageValue,
        ipoLeverageCurrency,
        ipoAmountValue,
        ipoAmountCurrency,
        ipoTotalAmountValue,
        ipoTotalAmountCurrency,
        ipoCompanyLogo,
        ipoEnhancementValue,
        ipoEnhancementCurrency,
        ipoRefundAmountValue,
        ipoRefundAmountCurrency,
        ipoLeveragePaidValue,
        ipoLeveragePaidCurrency,
        ipoLeverageFeePercentage,
        ipoLeverageFeeValue,
        ipoLeverageFeeCurrency,
        ipoAllocationFeePercentage,
        ipoAllotedFeeMoney,
        ipoAllotedFeeMoneyCurrency,
        ipoAllocatedMoney,
        ipoAllocatedMoneyCurrency,
        dynamicDetails,
        linkedTransactionFeeDetails,
        cashbackCalculations,
        carbonEmissionInGrams,
        carbonEmissionInOunces,
        recurringTransferRuleId
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'transactions';
  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  TransactionsData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return TransactionsData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      productType: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}product_type'])!,
      accountId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}account_id'])!,
      transactionAccountType: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}transaction_account_type']),
      transactionType: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}transaction_type'])!,
      transactionIdentifier: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}transaction_identifier'])!,
      status: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}status'])!,
      referenceNumber: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}reference_number']),
      transactionMode: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}transaction_mode'])!,
      transactionSubType: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}transaction_sub_type']),
      internalTransactionStatus: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}internal_transaction_status']),
      transactionDateTime: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime,
          data['${effectivePrefix}transaction_date_time']),
      debtorName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}debtor_name']),
      customerId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}customer_id']),
      coreBankingIdentifier: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}core_banking_identifier']),
      creditorName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}creditor_name']),
      description: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}description']),
      subDescription: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}sub_description']),
      logoType: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}logo_type']),
      merchantName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}merchant_name']),
      transactionImageKey: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}transaction_image_key']),
      amountValue: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}amount_value'])!,
      localAmount: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}local_amount']),
      exchangeRate: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}exchange_rate']),
      availableBalance: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}available_balance']),
      amountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}amount_currency'])!,
      executionDate: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}execution_date']),
      transactionCategory: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}transaction_category'])!,
      tppExternalReferenceId: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}tpp_external_reference_id']),
      fxFromAmount: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}fx_from_amount']),
      fxToAmount: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}fx_to_amount']),
      fxFromAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}fx_from_amount_currency']),
      fxToAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}fx_to_amount_currency']),
      fxFormattedExchangeRate: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}fx_formatted_exchange_rate']),
      orderId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}order_id']),
      portfolioId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}portfolio_id']),
      instrumentId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}instrument_id']),
      createdDateTime: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}created_date_time']),
      orderSide: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}order_side']),
      instrumentName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}instrument_name']),
      instrumentSymbol: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}instrument_symbol']),
      instrumentExchangeId: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}instrument_exchange_id']),
      instrumentImageUrl: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}instrument_image_url']),
      orderType: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}order_type']),
      errorCode: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}error_code']),
      errorMessage: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}error_message']),
      executedQuantity: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}executed_quantity']),
      estimatedQuantity: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}estimated_quantity']),
      executedAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}executed_amount_value']),
      executedAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}executed_amount_currency']),
      executedNetAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}executed_net_amount_value']),
      executedNetAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}executed_net_amount_currency']),
      estimatedAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}estimated_amount_value']),
      estimatedAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}estimated_amount_currency']),
      estimatedNetAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}estimated_net_amount_value']),
      estimatedNetAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}estimated_net_amount_currency']),
      commissionAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}commission_amount_value']),
      commissionAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}commission_amount_currency']),
      estimatedCommissionAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}estimated_commission_amount_value']),
      estimatedCommissionAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}estimated_commission_amount_currency']),
      vatValue: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}vat_value']),
      vatCurrency: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}vat_currency']),
      estimatedVatValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}estimated_vat_value']),
      estimatedVatCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}estimated_vat_currency']),
      averagePriceValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}average_price_value']),
      averagePriceCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}average_price_currency']),
      estimatedPriceValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}estimated_price_value']),
      estimatedPriceCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}estimated_price_currency']),
      executedTotalCommissionAmount: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}executed_total_commission_amount']),
      executedTotalCommissionCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}executed_total_commission_currency']),
      estimatedTotalCommissionAmount: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}estimated_total_commision_amount']),
      estimatedTotalCommissionCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}estimated_total_commision_currency']),
      commissionCalculationBasisPoints: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}commission_calculation_basis_points']),
      commissionCalculationBaseAmount: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}commission_calculation_base_amount']),
      commissionCalculationMinAmount: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}commission_calculation_min_amount']),
      commissionCalculationBaseAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data[
              '${effectivePrefix}commission_calculation_base_amount_currency']),
      commissionCalculationMinAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}commission_calculation_min_amount_currency']),
      instrumentExchangeCode: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}instrument_exchange_code']),
      recurringOrderTemplateId: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}recurring_order_template_id']),
      recurringOrderAccountName: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}recurring_order_account_name']),
      recurringOrderFrequencyType: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}recurring_order_frequency_type']),
      recurringOrderFrequencyDayOfWeek: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}recurring_order_frequency_day_of_week']),
      recurringOrderFrequencyDayOfMonth: attachedDatabase.typeMapping.read(
          DriftSqlType.int,
          data['${effectivePrefix}recurring_order_frequency_day_of_month']),
      expirationDate: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}expiration_date']),
      isPaymentProofReady: attachedDatabase.typeMapping.read(
          DriftSqlType.bool, data['${effectivePrefix}is_payment_proof_ready'])!,
      isPaymentTrackingAvailable: attachedDatabase.typeMapping.read(
          DriftSqlType.bool,
          data['${effectivePrefix}is_payment_tracking_available']),
      paymentFeeTotalAmount: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}payment_fee_total_amount']),
      paymentFeeTotalCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}payment_fee_total_currency']),
      paymentTargetCountryCode: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}payment_target_country_code']),
      paymentPhoneNumber: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}payment_phone_number']),
      paymentWarningMessage: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}payment_warning_message']),
      paymentFeeTypeDescriptionWio: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}payment_feetype_description_Wio']),
      paymentFeeTypeAmountWio: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}payment_feetype_amount_Wio']),
      paymentFeeTypeCurrencyWio: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}payment_feetype_currency_Wio']),
      paymentFeeTypeDescriptionWioCorrespondentBank:
          attachedDatabase.typeMapping.read(
              DriftSqlType.string,
              data[
                  '${effectivePrefix}payment_feetype_description_WioCorrespondentBank']),
      paymentFeeTypeAmountWioCorrespondentBank: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data[
              '${effectivePrefix}payment_feetype_amount_WioCorrespondentBank']),
      paymentFeeTypeCurrencyWioCorrespondentBank: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data[
              '${effectivePrefix}payment_feetype_currency_WioCorrespondentBank']),
      paymentFeeTypeDescriptionWise: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}payment_feetype_description_Wise']),
      paymentFeeTypeAmountWise: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}payment_feetype_amount_Wise']),
      paymentFeeTypeCurrencyWise: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}payment_feetype_currency_Wise']),
      internationalTargetCountry: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}international_target_country']),
      internationalCompletedDateTime: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime,
          data['${effectivePrefix}international_completed_date_time']),
      internationalEstimatedDateTime: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}international_estimated_date_time']),
      internationalFeeChargingType: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}international_fee_charging_type']),
      internationalSwiftCode: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}international_swift_code']),
      internationalAccountNumber: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}international_account_number']),
      internationalPurposeDescription: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}international_purpose_description']),
      internationalPurposeCode: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}international_purpose_code']),
      internationalNotes: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}international_notes']),
      internationalBankName: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}international_bank_name']),
      internationalTargetAmount: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}international_target_amount']),
      internationalTargetAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}international_target_amount_currency']),
      internationalTransferFee: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}international_transfer_fee']),
      internationalTransferFeeCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}international_transfer_fee_currency']),
      fabExpirationDate: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}fab_expiration_date']),
      fabBankName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}fab_bank_name']),
      fabTotalFeeAmount: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}fab_total_fee_amount']),
      fabTotalFeeAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}fab_total_fee_amount_currency']),
      fabUpdatedDate: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}fab_updated_date']),
      fabCancellationReason: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}fab_cancellation_reason']),
      fabChequeNumber: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}fab_cheque_number']),
      fabChequeDate: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}fab_cheque_date']),
      fabFrontImageUrl: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}fab_front_image_url']),
      localPurposeDescription: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}local_purpose_description']),
      localPurposeCode: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}local_purpose_code']),
      localNote: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}local_note']),
      localTargetCountry: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}local_target_country']),
      localCompletedDateTime: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime,
          data['${effectivePrefix}local_completed_date_time']),
      localEstimatedDateTime: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime,
          data['${effectivePrefix}local_estimated_date_time']),
      localAccountNumber: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}local_account_number']),
      localSwiftCode: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}local_swift_code']),
      localBankName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}local_bank_name']),
      localTargetAmount: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}local_target_amount']),
      localTargetAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}local_target_amount_currency']),
      localTransferSource: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}local_transfer_source']),
      localTransferApplicationId: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}local_transfer_application_id']),
      localRecipientName: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}local_recipient_name']),
      npssExpirationDate: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime,
          data['${effectivePrefix}npss_expiration_date']),
      npssNotes: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}npss_notes']),
      npssRtpInitiationDate: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime,
          data['${effectivePrefix}npss_rtp_initiation_date']),
      npssShowInPendingRtps: attachedDatabase.typeMapping.read(
          DriftSqlType.bool,
          data['${effectivePrefix}npss_show_in_pending_rtps']),
      npssShowRtpResendButton: attachedDatabase.typeMapping.read(
          DriftSqlType.bool,
          data['${effectivePrefix}npss_show_rtp_resend_button']),
      npssOriginalTransactionReference: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}npss_original_transaction_reference']),
      npssOriginalTransactionReason: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}npss_original_transaction_reason']),
      npssOriginalTransactionDeeplink: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}npss_original_transaction_deeplink']),
      token: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}token']),
      accNumberLast4Digits: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}acc_number_last_4digits']),
      cardMaskedPan: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}card_masked_pan']),
      merchantCountryCode: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}merchant_country_code']),
      originalCardTransactionCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}original_card_transaction_currency']),
      originalCardTransactionAmount: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}original_card_transaction_amount']),
      earnedCashback: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}earned_cash_back']),
      cashBackPercentage: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}cash_back_percentage']),
      cashBackTransactionCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}cash_back_transaction_currency']),
      dividendTaxAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}dividend_tax_amount_value']),
      dividendTaxAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}dividend_tax_amount_currency']),
      dividendAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}dividend_amount_value']),
      dividendAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}dividend_amount_currency']),
      netDividendAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}net_dividend_amount_value']),
      netDividendAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}net_dividend_amount_currency']),
      amountPerInstrumentValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}amount_per_instrument_value']),
      amountPerInstrumentCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}amount_per_instrument_currency']),
      instrumentQuantity: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}instrument_quantity']),
      brokerDividendTaxType: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}broker_dividend_tax_type']),
      dividendTaxRate: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}dividend_tax_rate']),
      securityLendingRebateAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}security_lending_rebate_amount_value']),
      securityLendingRebateAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}security_lending_rebate_amount_currency']),
      securityLendingRebateTaxAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data[
              '${effectivePrefix}security_lending_rebate_tax_amount_currency']),
      securityLendingRebateTaxAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}security_lending_rebate_tax_amount_value']),
      securityLendingTransferredAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data[
              '${effectivePrefix}security_lending_transferred_amount_currency']),
      securityLendingTransferredAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}security_lending_transferred_amount_value']),
      securityLendingMonth: attachedDatabase.typeMapping.read(
          DriftSqlType.int, data['${effectivePrefix}security_lending_month']),
      wealthManagementManagedPortfolioId: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}wealth_management_managed_portfolio_id']),
      wealthManagementProductName: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}wealth_management_product_name']),
      wealthManagementProductCategory: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}wealth_management_product_category']),
      wealthManagementAccountName: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}wealth_management_account_name']),
      wealthManagementEstimatedExecutionDeadline: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data[
              '${effectivePrefix}wealth_management_estimated_execution_deadline']),
      wealthManagementTrackerSteps: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}wealth_management_tracker_steps']),
      acquirerInstrumentName: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}acquirer_instrument_name']),
      acquirerInstrumentSymbol: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}acquirer_instrument_symbol']),
      acquirerInstrumentExchangeId: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}acquirer_instrument_exchange_id']),
      acquirerInstrumentImageUrl: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}acquirer_instrument_image_url']),
      acquireeInstrumentName: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}acquiree_instrument_name']),
      acquireeInstrumentSymbol: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}acquiree_instrument_symbol']),
      acquireeInstrumentExchangeId: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}acquiree_instrument_exchange_id']),
      acquireeInstrumentImageUrl: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}acquiree_instrument_image_url']),
      brokerAcquisitionAccountAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}broker_acquisition_account_amount_value']),
      brokerAcquisitionAccountAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}broker_acquisition_account_amount_currency']),
      brokerAcquisitionPositionDelta: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}broker_acquisition_position_delta']),
      ipoCompanyName: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}ipo_company_name']),
      ipoSubscriptionNumber: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}ipo_subscription_number']),
      ipoLeverageValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}ipo_leverage_value']),
      ipoLeverageCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}ipo_leverage_currency']),
      ipoAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}ipo_amount_value']),
      ipoAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}ipo_amount_currency']),
      ipoTotalAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}ipo_total_amount_value']),
      ipoTotalAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}ipo_total_amount_currency']),
      ipoCompanyLogo: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}ipo_company_logo']),
      ipoEnhancementValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}ipo_enhancement_value']),
      ipoEnhancementCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}ipo_enhancement_currency']),
      ipoRefundAmountValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}ipo_refund_amount_value']),
      ipoRefundAmountCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}ipo_refund_amount_currency']),
      ipoLeveragePaidValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}ipo_leverage_paid_value']),
      ipoLeveragePaidCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}ipo_leverage_paid_currency']),
      ipoLeverageFeePercentage: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}ipo_leverage_fee_percentage']),
      ipoLeverageFeeValue: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}ipo_leverage_fee_value']),
      ipoLeverageFeeCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}ipo_leverage_fee_currency']),
      ipoAllocationFeePercentage: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}ipo_allocation_fee_percentage']),
      ipoAllotedFeeMoney: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}ipo_alloted_fee_money']),
      ipoAllotedFeeMoneyCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}ipo_alloted_fee_money_currency']),
      ipoAllocatedMoney: attachedDatabase.typeMapping.read(
          DriftSqlType.double, data['${effectivePrefix}ipo_allocated_money']),
      ipoAllocatedMoneyCurrency: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}ipo_allocated_money_currency']),
      dynamicDetails: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}dynamic_details'])!,
      linkedTransactionFeeDetails: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}linked_transaction_fee_details'])!,
      cashbackCalculations: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}cashback_calculations']),
      carbonEmissionInGrams: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}carbon_emission_in_grams']),
      carbonEmissionInOunces: attachedDatabase.typeMapping.read(
          DriftSqlType.double,
          data['${effectivePrefix}carbon_emission_in_ounces']),
      recurringTransferRuleId: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}recurring_transfer_rule_id']),
    );
  }

  @override
  Transactions createAlias(String alias) {
    return Transactions(attachedDatabase, alias);
  }
}

class TransactionsData extends DataClass
    implements Insertable<TransactionsData> {
  final String id;
  final String productType;
  final String accountId;
  final String? transactionAccountType;
  final String transactionType;
  final String transactionIdentifier;
  final String status;
  final String? referenceNumber;
  final String transactionMode;
  final String? transactionSubType;
  final String? internalTransactionStatus;
  final DateTime? transactionDateTime;
  final String? debtorName;
  final String? customerId;
  final String? coreBankingIdentifier;
  final String? creditorName;
  final String? description;
  final String? subDescription;
  final String? logoType;
  final String? merchantName;
  final String? transactionImageKey;
  final double amountValue;
  final double? localAmount;
  final double? exchangeRate;
  final double? availableBalance;
  final String amountCurrency;
  final DateTime? executionDate;
  final String transactionCategory;
  final String? tppExternalReferenceId;
  final double? fxFromAmount;
  final double? fxToAmount;
  final String? fxFromAmountCurrency;
  final String? fxToAmountCurrency;
  final String? fxFormattedExchangeRate;
  final String? orderId;
  final String? portfolioId;
  final String? instrumentId;
  final DateTime? createdDateTime;
  final String? orderSide;
  final String? instrumentName;
  final String? instrumentSymbol;
  final String? instrumentExchangeId;
  final String? instrumentImageUrl;
  final String? orderType;
  final int? errorCode;
  final String? errorMessage;
  final double? executedQuantity;
  final double? estimatedQuantity;
  final double? executedAmountValue;
  final String? executedAmountCurrency;
  final double? executedNetAmountValue;
  final String? executedNetAmountCurrency;
  final double? estimatedAmountValue;
  final String? estimatedAmountCurrency;
  final double? estimatedNetAmountValue;
  final String? estimatedNetAmountCurrency;
  final double? commissionAmountValue;
  final String? commissionAmountCurrency;
  final double? estimatedCommissionAmountValue;
  final String? estimatedCommissionAmountCurrency;
  final double? vatValue;
  final String? vatCurrency;
  final double? estimatedVatValue;
  final String? estimatedVatCurrency;
  final double? averagePriceValue;
  final String? averagePriceCurrency;
  final double? estimatedPriceValue;
  final String? estimatedPriceCurrency;
  final double? executedTotalCommissionAmount;
  final String? executedTotalCommissionCurrency;
  final double? estimatedTotalCommissionAmount;
  final String? estimatedTotalCommissionCurrency;
  final double? commissionCalculationBasisPoints;
  final double? commissionCalculationBaseAmount;
  final double? commissionCalculationMinAmount;
  final String? commissionCalculationBaseAmountCurrency;
  final String? commissionCalculationMinAmountCurrency;
  final String? instrumentExchangeCode;
  final String? recurringOrderTemplateId;
  final String? recurringOrderAccountName;
  final String? recurringOrderFrequencyType;
  final String? recurringOrderFrequencyDayOfWeek;
  final int? recurringOrderFrequencyDayOfMonth;
  final DateTime? expirationDate;
  final bool isPaymentProofReady;
  final bool? isPaymentTrackingAvailable;
  final double? paymentFeeTotalAmount;
  final String? paymentFeeTotalCurrency;
  final String? paymentTargetCountryCode;
  final String? paymentPhoneNumber;
  final String? paymentWarningMessage;
  final String? paymentFeeTypeDescriptionWio;
  final double? paymentFeeTypeAmountWio;
  final String? paymentFeeTypeCurrencyWio;
  final String? paymentFeeTypeDescriptionWioCorrespondentBank;
  final double? paymentFeeTypeAmountWioCorrespondentBank;
  final String? paymentFeeTypeCurrencyWioCorrespondentBank;
  final String? paymentFeeTypeDescriptionWise;
  final double? paymentFeeTypeAmountWise;
  final String? paymentFeeTypeCurrencyWise;
  final String? internationalTargetCountry;
  final DateTime? internationalCompletedDateTime;
  final String? internationalEstimatedDateTime;
  final String? internationalFeeChargingType;
  final String? internationalSwiftCode;
  final String? internationalAccountNumber;
  final String? internationalPurposeDescription;
  final String? internationalPurposeCode;
  final String? internationalNotes;
  final String? internationalBankName;
  final double? internationalTargetAmount;
  final String? internationalTargetAmountCurrency;
  final double? internationalTransferFee;
  final String? internationalTransferFeeCurrency;
  final DateTime? fabExpirationDate;
  final String? fabBankName;
  final double? fabTotalFeeAmount;
  final String? fabTotalFeeAmountCurrency;
  final DateTime? fabUpdatedDate;
  final String? fabCancellationReason;
  final String? fabChequeNumber;
  final DateTime? fabChequeDate;
  final String? fabFrontImageUrl;
  final String? localPurposeDescription;
  final String? localPurposeCode;
  final String? localNote;
  final String? localTargetCountry;
  final DateTime? localCompletedDateTime;
  final DateTime? localEstimatedDateTime;
  final String? localAccountNumber;
  final String? localSwiftCode;
  final String? localBankName;
  final double? localTargetAmount;
  final String? localTargetAmountCurrency;
  final String? localTransferSource;
  final String? localTransferApplicationId;
  final String? localRecipientName;
  final DateTime? npssExpirationDate;
  final String? npssNotes;
  final DateTime? npssRtpInitiationDate;
  final bool? npssShowInPendingRtps;
  final bool? npssShowRtpResendButton;
  final String? npssOriginalTransactionReference;
  final String? npssOriginalTransactionReason;
  final String? npssOriginalTransactionDeeplink;
  final String? token;
  final String? accNumberLast4Digits;
  final String? cardMaskedPan;
  final String? merchantCountryCode;
  final String? originalCardTransactionCurrency;
  final double? originalCardTransactionAmount;
  final double? earnedCashback;
  final double? cashBackPercentage;
  final String? cashBackTransactionCurrency;
  final double? dividendTaxAmountValue;
  final String? dividendTaxAmountCurrency;
  final double? dividendAmountValue;
  final String? dividendAmountCurrency;
  final double? netDividendAmountValue;
  final String? netDividendAmountCurrency;
  final double? amountPerInstrumentValue;
  final String? amountPerInstrumentCurrency;
  final double? instrumentQuantity;
  final String? brokerDividendTaxType;
  final double? dividendTaxRate;
  final double? securityLendingRebateAmountValue;
  final String? securityLendingRebateAmountCurrency;
  final String? securityLendingRebateTaxAmountCurrency;
  final double? securityLendingRebateTaxAmountValue;
  final String? securityLendingTransferredAmountCurrency;
  final double? securityLendingTransferredAmountValue;
  final int? securityLendingMonth;
  final String? wealthManagementManagedPortfolioId;
  final String? wealthManagementProductName;
  final String? wealthManagementProductCategory;
  final String? wealthManagementAccountName;
  final String? wealthManagementEstimatedExecutionDeadline;
  final String? wealthManagementTrackerSteps;
  final String? acquirerInstrumentName;
  final String? acquirerInstrumentSymbol;
  final String? acquirerInstrumentExchangeId;
  final String? acquirerInstrumentImageUrl;
  final String? acquireeInstrumentName;
  final String? acquireeInstrumentSymbol;
  final String? acquireeInstrumentExchangeId;
  final String? acquireeInstrumentImageUrl;
  final double? brokerAcquisitionAccountAmountValue;
  final String? brokerAcquisitionAccountAmountCurrency;
  final double? brokerAcquisitionPositionDelta;
  final String? ipoCompanyName;
  final String? ipoSubscriptionNumber;
  final double? ipoLeverageValue;
  final String? ipoLeverageCurrency;
  final double? ipoAmountValue;
  final String? ipoAmountCurrency;
  final double? ipoTotalAmountValue;
  final String? ipoTotalAmountCurrency;
  final String? ipoCompanyLogo;
  final double? ipoEnhancementValue;
  final String? ipoEnhancementCurrency;
  final double? ipoRefundAmountValue;
  final String? ipoRefundAmountCurrency;
  final double? ipoLeveragePaidValue;
  final String? ipoLeveragePaidCurrency;
  final double? ipoLeverageFeePercentage;
  final double? ipoLeverageFeeValue;
  final String? ipoLeverageFeeCurrency;
  final double? ipoAllocationFeePercentage;
  final double? ipoAllotedFeeMoney;
  final String? ipoAllotedFeeMoneyCurrency;
  final double? ipoAllocatedMoney;
  final String? ipoAllocatedMoneyCurrency;
  final String dynamicDetails;
  final String linkedTransactionFeeDetails;
  final String? cashbackCalculations;
  final double? carbonEmissionInGrams;
  final double? carbonEmissionInOunces;
  final String? recurringTransferRuleId;
  const TransactionsData(
      {required this.id,
      required this.productType,
      required this.accountId,
      this.transactionAccountType,
      required this.transactionType,
      required this.transactionIdentifier,
      required this.status,
      this.referenceNumber,
      required this.transactionMode,
      this.transactionSubType,
      this.internalTransactionStatus,
      this.transactionDateTime,
      this.debtorName,
      this.customerId,
      this.coreBankingIdentifier,
      this.creditorName,
      this.description,
      this.subDescription,
      this.logoType,
      this.merchantName,
      this.transactionImageKey,
      required this.amountValue,
      this.localAmount,
      this.exchangeRate,
      this.availableBalance,
      required this.amountCurrency,
      this.executionDate,
      required this.transactionCategory,
      this.tppExternalReferenceId,
      this.fxFromAmount,
      this.fxToAmount,
      this.fxFromAmountCurrency,
      this.fxToAmountCurrency,
      this.fxFormattedExchangeRate,
      this.orderId,
      this.portfolioId,
      this.instrumentId,
      this.createdDateTime,
      this.orderSide,
      this.instrumentName,
      this.instrumentSymbol,
      this.instrumentExchangeId,
      this.instrumentImageUrl,
      this.orderType,
      this.errorCode,
      this.errorMessage,
      this.executedQuantity,
      this.estimatedQuantity,
      this.executedAmountValue,
      this.executedAmountCurrency,
      this.executedNetAmountValue,
      this.executedNetAmountCurrency,
      this.estimatedAmountValue,
      this.estimatedAmountCurrency,
      this.estimatedNetAmountValue,
      this.estimatedNetAmountCurrency,
      this.commissionAmountValue,
      this.commissionAmountCurrency,
      this.estimatedCommissionAmountValue,
      this.estimatedCommissionAmountCurrency,
      this.vatValue,
      this.vatCurrency,
      this.estimatedVatValue,
      this.estimatedVatCurrency,
      this.averagePriceValue,
      this.averagePriceCurrency,
      this.estimatedPriceValue,
      this.estimatedPriceCurrency,
      this.executedTotalCommissionAmount,
      this.executedTotalCommissionCurrency,
      this.estimatedTotalCommissionAmount,
      this.estimatedTotalCommissionCurrency,
      this.commissionCalculationBasisPoints,
      this.commissionCalculationBaseAmount,
      this.commissionCalculationMinAmount,
      this.commissionCalculationBaseAmountCurrency,
      this.commissionCalculationMinAmountCurrency,
      this.instrumentExchangeCode,
      this.recurringOrderTemplateId,
      this.recurringOrderAccountName,
      this.recurringOrderFrequencyType,
      this.recurringOrderFrequencyDayOfWeek,
      this.recurringOrderFrequencyDayOfMonth,
      this.expirationDate,
      required this.isPaymentProofReady,
      this.isPaymentTrackingAvailable,
      this.paymentFeeTotalAmount,
      this.paymentFeeTotalCurrency,
      this.paymentTargetCountryCode,
      this.paymentPhoneNumber,
      this.paymentWarningMessage,
      this.paymentFeeTypeDescriptionWio,
      this.paymentFeeTypeAmountWio,
      this.paymentFeeTypeCurrencyWio,
      this.paymentFeeTypeDescriptionWioCorrespondentBank,
      this.paymentFeeTypeAmountWioCorrespondentBank,
      this.paymentFeeTypeCurrencyWioCorrespondentBank,
      this.paymentFeeTypeDescriptionWise,
      this.paymentFeeTypeAmountWise,
      this.paymentFeeTypeCurrencyWise,
      this.internationalTargetCountry,
      this.internationalCompletedDateTime,
      this.internationalEstimatedDateTime,
      this.internationalFeeChargingType,
      this.internationalSwiftCode,
      this.internationalAccountNumber,
      this.internationalPurposeDescription,
      this.internationalPurposeCode,
      this.internationalNotes,
      this.internationalBankName,
      this.internationalTargetAmount,
      this.internationalTargetAmountCurrency,
      this.internationalTransferFee,
      this.internationalTransferFeeCurrency,
      this.fabExpirationDate,
      this.fabBankName,
      this.fabTotalFeeAmount,
      this.fabTotalFeeAmountCurrency,
      this.fabUpdatedDate,
      this.fabCancellationReason,
      this.fabChequeNumber,
      this.fabChequeDate,
      this.fabFrontImageUrl,
      this.localPurposeDescription,
      this.localPurposeCode,
      this.localNote,
      this.localTargetCountry,
      this.localCompletedDateTime,
      this.localEstimatedDateTime,
      this.localAccountNumber,
      this.localSwiftCode,
      this.localBankName,
      this.localTargetAmount,
      this.localTargetAmountCurrency,
      this.localTransferSource,
      this.localTransferApplicationId,
      this.localRecipientName,
      this.npssExpirationDate,
      this.npssNotes,
      this.npssRtpInitiationDate,
      this.npssShowInPendingRtps,
      this.npssShowRtpResendButton,
      this.npssOriginalTransactionReference,
      this.npssOriginalTransactionReason,
      this.npssOriginalTransactionDeeplink,
      this.token,
      this.accNumberLast4Digits,
      this.cardMaskedPan,
      this.merchantCountryCode,
      this.originalCardTransactionCurrency,
      this.originalCardTransactionAmount,
      this.earnedCashback,
      this.cashBackPercentage,
      this.cashBackTransactionCurrency,
      this.dividendTaxAmountValue,
      this.dividendTaxAmountCurrency,
      this.dividendAmountValue,
      this.dividendAmountCurrency,
      this.netDividendAmountValue,
      this.netDividendAmountCurrency,
      this.amountPerInstrumentValue,
      this.amountPerInstrumentCurrency,
      this.instrumentQuantity,
      this.brokerDividendTaxType,
      this.dividendTaxRate,
      this.securityLendingRebateAmountValue,
      this.securityLendingRebateAmountCurrency,
      this.securityLendingRebateTaxAmountCurrency,
      this.securityLendingRebateTaxAmountValue,
      this.securityLendingTransferredAmountCurrency,
      this.securityLendingTransferredAmountValue,
      this.securityLendingMonth,
      this.wealthManagementManagedPortfolioId,
      this.wealthManagementProductName,
      this.wealthManagementProductCategory,
      this.wealthManagementAccountName,
      this.wealthManagementEstimatedExecutionDeadline,
      this.wealthManagementTrackerSteps,
      this.acquirerInstrumentName,
      this.acquirerInstrumentSymbol,
      this.acquirerInstrumentExchangeId,
      this.acquirerInstrumentImageUrl,
      this.acquireeInstrumentName,
      this.acquireeInstrumentSymbol,
      this.acquireeInstrumentExchangeId,
      this.acquireeInstrumentImageUrl,
      this.brokerAcquisitionAccountAmountValue,
      this.brokerAcquisitionAccountAmountCurrency,
      this.brokerAcquisitionPositionDelta,
      this.ipoCompanyName,
      this.ipoSubscriptionNumber,
      this.ipoLeverageValue,
      this.ipoLeverageCurrency,
      this.ipoAmountValue,
      this.ipoAmountCurrency,
      this.ipoTotalAmountValue,
      this.ipoTotalAmountCurrency,
      this.ipoCompanyLogo,
      this.ipoEnhancementValue,
      this.ipoEnhancementCurrency,
      this.ipoRefundAmountValue,
      this.ipoRefundAmountCurrency,
      this.ipoLeveragePaidValue,
      this.ipoLeveragePaidCurrency,
      this.ipoLeverageFeePercentage,
      this.ipoLeverageFeeValue,
      this.ipoLeverageFeeCurrency,
      this.ipoAllocationFeePercentage,
      this.ipoAllotedFeeMoney,
      this.ipoAllotedFeeMoneyCurrency,
      this.ipoAllocatedMoney,
      this.ipoAllocatedMoneyCurrency,
      required this.dynamicDetails,
      required this.linkedTransactionFeeDetails,
      this.cashbackCalculations,
      this.carbonEmissionInGrams,
      this.carbonEmissionInOunces,
      this.recurringTransferRuleId});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['product_type'] = Variable<String>(productType);
    map['account_id'] = Variable<String>(accountId);
    if (!nullToAbsent || transactionAccountType != null) {
      map['transaction_account_type'] =
          Variable<String>(transactionAccountType);
    }
    map['transaction_type'] = Variable<String>(transactionType);
    map['transaction_identifier'] = Variable<String>(transactionIdentifier);
    map['status'] = Variable<String>(status);
    if (!nullToAbsent || referenceNumber != null) {
      map['reference_number'] = Variable<String>(referenceNumber);
    }
    map['transaction_mode'] = Variable<String>(transactionMode);
    if (!nullToAbsent || transactionSubType != null) {
      map['transaction_sub_type'] = Variable<String>(transactionSubType);
    }
    if (!nullToAbsent || internalTransactionStatus != null) {
      map['internal_transaction_status'] =
          Variable<String>(internalTransactionStatus);
    }
    if (!nullToAbsent || transactionDateTime != null) {
      map['transaction_date_time'] = Variable<DateTime>(transactionDateTime);
    }
    if (!nullToAbsent || debtorName != null) {
      map['debtor_name'] = Variable<String>(debtorName);
    }
    if (!nullToAbsent || customerId != null) {
      map['customer_id'] = Variable<String>(customerId);
    }
    if (!nullToAbsent || coreBankingIdentifier != null) {
      map['core_banking_identifier'] = Variable<String>(coreBankingIdentifier);
    }
    if (!nullToAbsent || creditorName != null) {
      map['creditor_name'] = Variable<String>(creditorName);
    }
    if (!nullToAbsent || description != null) {
      map['description'] = Variable<String>(description);
    }
    if (!nullToAbsent || subDescription != null) {
      map['sub_description'] = Variable<String>(subDescription);
    }
    if (!nullToAbsent || logoType != null) {
      map['logo_type'] = Variable<String>(logoType);
    }
    if (!nullToAbsent || merchantName != null) {
      map['merchant_name'] = Variable<String>(merchantName);
    }
    if (!nullToAbsent || transactionImageKey != null) {
      map['transaction_image_key'] = Variable<String>(transactionImageKey);
    }
    map['amount_value'] = Variable<double>(amountValue);
    if (!nullToAbsent || localAmount != null) {
      map['local_amount'] = Variable<double>(localAmount);
    }
    if (!nullToAbsent || exchangeRate != null) {
      map['exchange_rate'] = Variable<double>(exchangeRate);
    }
    if (!nullToAbsent || availableBalance != null) {
      map['available_balance'] = Variable<double>(availableBalance);
    }
    map['amount_currency'] = Variable<String>(amountCurrency);
    if (!nullToAbsent || executionDate != null) {
      map['execution_date'] = Variable<DateTime>(executionDate);
    }
    map['transaction_category'] = Variable<String>(transactionCategory);
    if (!nullToAbsent || tppExternalReferenceId != null) {
      map['tpp_external_reference_id'] =
          Variable<String>(tppExternalReferenceId);
    }
    if (!nullToAbsent || fxFromAmount != null) {
      map['fx_from_amount'] = Variable<double>(fxFromAmount);
    }
    if (!nullToAbsent || fxToAmount != null) {
      map['fx_to_amount'] = Variable<double>(fxToAmount);
    }
    if (!nullToAbsent || fxFromAmountCurrency != null) {
      map['fx_from_amount_currency'] = Variable<String>(fxFromAmountCurrency);
    }
    if (!nullToAbsent || fxToAmountCurrency != null) {
      map['fx_to_amount_currency'] = Variable<String>(fxToAmountCurrency);
    }
    if (!nullToAbsent || fxFormattedExchangeRate != null) {
      map['fx_formatted_exchange_rate'] =
          Variable<String>(fxFormattedExchangeRate);
    }
    if (!nullToAbsent || orderId != null) {
      map['order_id'] = Variable<String>(orderId);
    }
    if (!nullToAbsent || portfolioId != null) {
      map['portfolio_id'] = Variable<String>(portfolioId);
    }
    if (!nullToAbsent || instrumentId != null) {
      map['instrument_id'] = Variable<String>(instrumentId);
    }
    if (!nullToAbsent || createdDateTime != null) {
      map['created_date_time'] = Variable<DateTime>(createdDateTime);
    }
    if (!nullToAbsent || orderSide != null) {
      map['order_side'] = Variable<String>(orderSide);
    }
    if (!nullToAbsent || instrumentName != null) {
      map['instrument_name'] = Variable<String>(instrumentName);
    }
    if (!nullToAbsent || instrumentSymbol != null) {
      map['instrument_symbol'] = Variable<String>(instrumentSymbol);
    }
    if (!nullToAbsent || instrumentExchangeId != null) {
      map['instrument_exchange_id'] = Variable<String>(instrumentExchangeId);
    }
    if (!nullToAbsent || instrumentImageUrl != null) {
      map['instrument_image_url'] = Variable<String>(instrumentImageUrl);
    }
    if (!nullToAbsent || orderType != null) {
      map['order_type'] = Variable<String>(orderType);
    }
    if (!nullToAbsent || errorCode != null) {
      map['error_code'] = Variable<int>(errorCode);
    }
    if (!nullToAbsent || errorMessage != null) {
      map['error_message'] = Variable<String>(errorMessage);
    }
    if (!nullToAbsent || executedQuantity != null) {
      map['executed_quantity'] = Variable<double>(executedQuantity);
    }
    if (!nullToAbsent || estimatedQuantity != null) {
      map['estimated_quantity'] = Variable<double>(estimatedQuantity);
    }
    if (!nullToAbsent || executedAmountValue != null) {
      map['executed_amount_value'] = Variable<double>(executedAmountValue);
    }
    if (!nullToAbsent || executedAmountCurrency != null) {
      map['executed_amount_currency'] =
          Variable<String>(executedAmountCurrency);
    }
    if (!nullToAbsent || executedNetAmountValue != null) {
      map['executed_net_amount_value'] =
          Variable<double>(executedNetAmountValue);
    }
    if (!nullToAbsent || executedNetAmountCurrency != null) {
      map['executed_net_amount_currency'] =
          Variable<String>(executedNetAmountCurrency);
    }
    if (!nullToAbsent || estimatedAmountValue != null) {
      map['estimated_amount_value'] = Variable<double>(estimatedAmountValue);
    }
    if (!nullToAbsent || estimatedAmountCurrency != null) {
      map['estimated_amount_currency'] =
          Variable<String>(estimatedAmountCurrency);
    }
    if (!nullToAbsent || estimatedNetAmountValue != null) {
      map['estimated_net_amount_value'] =
          Variable<double>(estimatedNetAmountValue);
    }
    if (!nullToAbsent || estimatedNetAmountCurrency != null) {
      map['estimated_net_amount_currency'] =
          Variable<String>(estimatedNetAmountCurrency);
    }
    if (!nullToAbsent || commissionAmountValue != null) {
      map['commission_amount_value'] = Variable<double>(commissionAmountValue);
    }
    if (!nullToAbsent || commissionAmountCurrency != null) {
      map['commission_amount_currency'] =
          Variable<String>(commissionAmountCurrency);
    }
    if (!nullToAbsent || estimatedCommissionAmountValue != null) {
      map['estimated_commission_amount_value'] =
          Variable<double>(estimatedCommissionAmountValue);
    }
    if (!nullToAbsent || estimatedCommissionAmountCurrency != null) {
      map['estimated_commission_amount_currency'] =
          Variable<String>(estimatedCommissionAmountCurrency);
    }
    if (!nullToAbsent || vatValue != null) {
      map['vat_value'] = Variable<double>(vatValue);
    }
    if (!nullToAbsent || vatCurrency != null) {
      map['vat_currency'] = Variable<String>(vatCurrency);
    }
    if (!nullToAbsent || estimatedVatValue != null) {
      map['estimated_vat_value'] = Variable<double>(estimatedVatValue);
    }
    if (!nullToAbsent || estimatedVatCurrency != null) {
      map['estimated_vat_currency'] = Variable<String>(estimatedVatCurrency);
    }
    if (!nullToAbsent || averagePriceValue != null) {
      map['average_price_value'] = Variable<double>(averagePriceValue);
    }
    if (!nullToAbsent || averagePriceCurrency != null) {
      map['average_price_currency'] = Variable<String>(averagePriceCurrency);
    }
    if (!nullToAbsent || estimatedPriceValue != null) {
      map['estimated_price_value'] = Variable<double>(estimatedPriceValue);
    }
    if (!nullToAbsent || estimatedPriceCurrency != null) {
      map['estimated_price_currency'] =
          Variable<String>(estimatedPriceCurrency);
    }
    if (!nullToAbsent || executedTotalCommissionAmount != null) {
      map['executed_total_commission_amount'] =
          Variable<double>(executedTotalCommissionAmount);
    }
    if (!nullToAbsent || executedTotalCommissionCurrency != null) {
      map['executed_total_commission_currency'] =
          Variable<String>(executedTotalCommissionCurrency);
    }
    if (!nullToAbsent || estimatedTotalCommissionAmount != null) {
      map['estimated_total_commision_amount'] =
          Variable<double>(estimatedTotalCommissionAmount);
    }
    if (!nullToAbsent || estimatedTotalCommissionCurrency != null) {
      map['estimated_total_commision_currency'] =
          Variable<String>(estimatedTotalCommissionCurrency);
    }
    if (!nullToAbsent || commissionCalculationBasisPoints != null) {
      map['commission_calculation_basis_points'] =
          Variable<double>(commissionCalculationBasisPoints);
    }
    if (!nullToAbsent || commissionCalculationBaseAmount != null) {
      map['commission_calculation_base_amount'] =
          Variable<double>(commissionCalculationBaseAmount);
    }
    if (!nullToAbsent || commissionCalculationMinAmount != null) {
      map['commission_calculation_min_amount'] =
          Variable<double>(commissionCalculationMinAmount);
    }
    if (!nullToAbsent || commissionCalculationBaseAmountCurrency != null) {
      map['commission_calculation_base_amount_currency'] =
          Variable<String>(commissionCalculationBaseAmountCurrency);
    }
    if (!nullToAbsent || commissionCalculationMinAmountCurrency != null) {
      map['commission_calculation_min_amount_currency'] =
          Variable<String>(commissionCalculationMinAmountCurrency);
    }
    if (!nullToAbsent || instrumentExchangeCode != null) {
      map['instrument_exchange_code'] =
          Variable<String>(instrumentExchangeCode);
    }
    if (!nullToAbsent || recurringOrderTemplateId != null) {
      map['recurring_order_template_id'] =
          Variable<String>(recurringOrderTemplateId);
    }
    if (!nullToAbsent || recurringOrderAccountName != null) {
      map['recurring_order_account_name'] =
          Variable<String>(recurringOrderAccountName);
    }
    if (!nullToAbsent || recurringOrderFrequencyType != null) {
      map['recurring_order_frequency_type'] =
          Variable<String>(recurringOrderFrequencyType);
    }
    if (!nullToAbsent || recurringOrderFrequencyDayOfWeek != null) {
      map['recurring_order_frequency_day_of_week'] =
          Variable<String>(recurringOrderFrequencyDayOfWeek);
    }
    if (!nullToAbsent || recurringOrderFrequencyDayOfMonth != null) {
      map['recurring_order_frequency_day_of_month'] =
          Variable<int>(recurringOrderFrequencyDayOfMonth);
    }
    if (!nullToAbsent || expirationDate != null) {
      map['expiration_date'] = Variable<DateTime>(expirationDate);
    }
    map['is_payment_proof_ready'] = Variable<bool>(isPaymentProofReady);
    if (!nullToAbsent || isPaymentTrackingAvailable != null) {
      map['is_payment_tracking_available'] =
          Variable<bool>(isPaymentTrackingAvailable);
    }
    if (!nullToAbsent || paymentFeeTotalAmount != null) {
      map['payment_fee_total_amount'] = Variable<double>(paymentFeeTotalAmount);
    }
    if (!nullToAbsent || paymentFeeTotalCurrency != null) {
      map['payment_fee_total_currency'] =
          Variable<String>(paymentFeeTotalCurrency);
    }
    if (!nullToAbsent || paymentTargetCountryCode != null) {
      map['payment_target_country_code'] =
          Variable<String>(paymentTargetCountryCode);
    }
    if (!nullToAbsent || paymentPhoneNumber != null) {
      map['payment_phone_number'] = Variable<String>(paymentPhoneNumber);
    }
    if (!nullToAbsent || paymentWarningMessage != null) {
      map['payment_warning_message'] = Variable<String>(paymentWarningMessage);
    }
    if (!nullToAbsent || paymentFeeTypeDescriptionWio != null) {
      map['payment_feetype_description_Wio'] =
          Variable<String>(paymentFeeTypeDescriptionWio);
    }
    if (!nullToAbsent || paymentFeeTypeAmountWio != null) {
      map['payment_feetype_amount_Wio'] =
          Variable<double>(paymentFeeTypeAmountWio);
    }
    if (!nullToAbsent || paymentFeeTypeCurrencyWio != null) {
      map['payment_feetype_currency_Wio'] =
          Variable<String>(paymentFeeTypeCurrencyWio);
    }
    if (!nullToAbsent ||
        paymentFeeTypeDescriptionWioCorrespondentBank != null) {
      map['payment_feetype_description_WioCorrespondentBank'] =
          Variable<String>(paymentFeeTypeDescriptionWioCorrespondentBank);
    }
    if (!nullToAbsent || paymentFeeTypeAmountWioCorrespondentBank != null) {
      map['payment_feetype_amount_WioCorrespondentBank'] =
          Variable<double>(paymentFeeTypeAmountWioCorrespondentBank);
    }
    if (!nullToAbsent || paymentFeeTypeCurrencyWioCorrespondentBank != null) {
      map['payment_feetype_currency_WioCorrespondentBank'] =
          Variable<String>(paymentFeeTypeCurrencyWioCorrespondentBank);
    }
    if (!nullToAbsent || paymentFeeTypeDescriptionWise != null) {
      map['payment_feetype_description_Wise'] =
          Variable<String>(paymentFeeTypeDescriptionWise);
    }
    if (!nullToAbsent || paymentFeeTypeAmountWise != null) {
      map['payment_feetype_amount_Wise'] =
          Variable<double>(paymentFeeTypeAmountWise);
    }
    if (!nullToAbsent || paymentFeeTypeCurrencyWise != null) {
      map['payment_feetype_currency_Wise'] =
          Variable<String>(paymentFeeTypeCurrencyWise);
    }
    if (!nullToAbsent || internationalTargetCountry != null) {
      map['international_target_country'] =
          Variable<String>(internationalTargetCountry);
    }
    if (!nullToAbsent || internationalCompletedDateTime != null) {
      map['international_completed_date_time'] =
          Variable<DateTime>(internationalCompletedDateTime);
    }
    if (!nullToAbsent || internationalEstimatedDateTime != null) {
      map['international_estimated_date_time'] =
          Variable<String>(internationalEstimatedDateTime);
    }
    if (!nullToAbsent || internationalFeeChargingType != null) {
      map['international_fee_charging_type'] =
          Variable<String>(internationalFeeChargingType);
    }
    if (!nullToAbsent || internationalSwiftCode != null) {
      map['international_swift_code'] =
          Variable<String>(internationalSwiftCode);
    }
    if (!nullToAbsent || internationalAccountNumber != null) {
      map['international_account_number'] =
          Variable<String>(internationalAccountNumber);
    }
    if (!nullToAbsent || internationalPurposeDescription != null) {
      map['international_purpose_description'] =
          Variable<String>(internationalPurposeDescription);
    }
    if (!nullToAbsent || internationalPurposeCode != null) {
      map['international_purpose_code'] =
          Variable<String>(internationalPurposeCode);
    }
    if (!nullToAbsent || internationalNotes != null) {
      map['international_notes'] = Variable<String>(internationalNotes);
    }
    if (!nullToAbsent || internationalBankName != null) {
      map['international_bank_name'] = Variable<String>(internationalBankName);
    }
    if (!nullToAbsent || internationalTargetAmount != null) {
      map['international_target_amount'] =
          Variable<double>(internationalTargetAmount);
    }
    if (!nullToAbsent || internationalTargetAmountCurrency != null) {
      map['international_target_amount_currency'] =
          Variable<String>(internationalTargetAmountCurrency);
    }
    if (!nullToAbsent || internationalTransferFee != null) {
      map['international_transfer_fee'] =
          Variable<double>(internationalTransferFee);
    }
    if (!nullToAbsent || internationalTransferFeeCurrency != null) {
      map['international_transfer_fee_currency'] =
          Variable<String>(internationalTransferFeeCurrency);
    }
    if (!nullToAbsent || fabExpirationDate != null) {
      map['fab_expiration_date'] = Variable<DateTime>(fabExpirationDate);
    }
    if (!nullToAbsent || fabBankName != null) {
      map['fab_bank_name'] = Variable<String>(fabBankName);
    }
    if (!nullToAbsent || fabTotalFeeAmount != null) {
      map['fab_total_fee_amount'] = Variable<double>(fabTotalFeeAmount);
    }
    if (!nullToAbsent || fabTotalFeeAmountCurrency != null) {
      map['fab_total_fee_amount_currency'] =
          Variable<String>(fabTotalFeeAmountCurrency);
    }
    if (!nullToAbsent || fabUpdatedDate != null) {
      map['fab_updated_date'] = Variable<DateTime>(fabUpdatedDate);
    }
    if (!nullToAbsent || fabCancellationReason != null) {
      map['fab_cancellation_reason'] = Variable<String>(fabCancellationReason);
    }
    if (!nullToAbsent || fabChequeNumber != null) {
      map['fab_cheque_number'] = Variable<String>(fabChequeNumber);
    }
    if (!nullToAbsent || fabChequeDate != null) {
      map['fab_cheque_date'] = Variable<DateTime>(fabChequeDate);
    }
    if (!nullToAbsent || fabFrontImageUrl != null) {
      map['fab_front_image_url'] = Variable<String>(fabFrontImageUrl);
    }
    if (!nullToAbsent || localPurposeDescription != null) {
      map['local_purpose_description'] =
          Variable<String>(localPurposeDescription);
    }
    if (!nullToAbsent || localPurposeCode != null) {
      map['local_purpose_code'] = Variable<String>(localPurposeCode);
    }
    if (!nullToAbsent || localNote != null) {
      map['local_note'] = Variable<String>(localNote);
    }
    if (!nullToAbsent || localTargetCountry != null) {
      map['local_target_country'] = Variable<String>(localTargetCountry);
    }
    if (!nullToAbsent || localCompletedDateTime != null) {
      map['local_completed_date_time'] =
          Variable<DateTime>(localCompletedDateTime);
    }
    if (!nullToAbsent || localEstimatedDateTime != null) {
      map['local_estimated_date_time'] =
          Variable<DateTime>(localEstimatedDateTime);
    }
    if (!nullToAbsent || localAccountNumber != null) {
      map['local_account_number'] = Variable<String>(localAccountNumber);
    }
    if (!nullToAbsent || localSwiftCode != null) {
      map['local_swift_code'] = Variable<String>(localSwiftCode);
    }
    if (!nullToAbsent || localBankName != null) {
      map['local_bank_name'] = Variable<String>(localBankName);
    }
    if (!nullToAbsent || localTargetAmount != null) {
      map['local_target_amount'] = Variable<double>(localTargetAmount);
    }
    if (!nullToAbsent || localTargetAmountCurrency != null) {
      map['local_target_amount_currency'] =
          Variable<String>(localTargetAmountCurrency);
    }
    if (!nullToAbsent || localTransferSource != null) {
      map['local_transfer_source'] = Variable<String>(localTransferSource);
    }
    if (!nullToAbsent || localTransferApplicationId != null) {
      map['local_transfer_application_id'] =
          Variable<String>(localTransferApplicationId);
    }
    if (!nullToAbsent || localRecipientName != null) {
      map['local_recipient_name'] = Variable<String>(localRecipientName);
    }
    if (!nullToAbsent || npssExpirationDate != null) {
      map['npss_expiration_date'] = Variable<DateTime>(npssExpirationDate);
    }
    if (!nullToAbsent || npssNotes != null) {
      map['npss_notes'] = Variable<String>(npssNotes);
    }
    if (!nullToAbsent || npssRtpInitiationDate != null) {
      map['npss_rtp_initiation_date'] =
          Variable<DateTime>(npssRtpInitiationDate);
    }
    if (!nullToAbsent || npssShowInPendingRtps != null) {
      map['npss_show_in_pending_rtps'] = Variable<bool>(npssShowInPendingRtps);
    }
    if (!nullToAbsent || npssShowRtpResendButton != null) {
      map['npss_show_rtp_resend_button'] =
          Variable<bool>(npssShowRtpResendButton);
    }
    if (!nullToAbsent || npssOriginalTransactionReference != null) {
      map['npss_original_transaction_reference'] =
          Variable<String>(npssOriginalTransactionReference);
    }
    if (!nullToAbsent || npssOriginalTransactionReason != null) {
      map['npss_original_transaction_reason'] =
          Variable<String>(npssOriginalTransactionReason);
    }
    if (!nullToAbsent || npssOriginalTransactionDeeplink != null) {
      map['npss_original_transaction_deeplink'] =
          Variable<String>(npssOriginalTransactionDeeplink);
    }
    if (!nullToAbsent || token != null) {
      map['token'] = Variable<String>(token);
    }
    if (!nullToAbsent || accNumberLast4Digits != null) {
      map['acc_number_last_4digits'] = Variable<String>(accNumberLast4Digits);
    }
    if (!nullToAbsent || cardMaskedPan != null) {
      map['card_masked_pan'] = Variable<String>(cardMaskedPan);
    }
    if (!nullToAbsent || merchantCountryCode != null) {
      map['merchant_country_code'] = Variable<String>(merchantCountryCode);
    }
    if (!nullToAbsent || originalCardTransactionCurrency != null) {
      map['original_card_transaction_currency'] =
          Variable<String>(originalCardTransactionCurrency);
    }
    if (!nullToAbsent || originalCardTransactionAmount != null) {
      map['original_card_transaction_amount'] =
          Variable<double>(originalCardTransactionAmount);
    }
    if (!nullToAbsent || earnedCashback != null) {
      map['earned_cash_back'] = Variable<double>(earnedCashback);
    }
    if (!nullToAbsent || cashBackPercentage != null) {
      map['cash_back_percentage'] = Variable<double>(cashBackPercentage);
    }
    if (!nullToAbsent || cashBackTransactionCurrency != null) {
      map['cash_back_transaction_currency'] =
          Variable<String>(cashBackTransactionCurrency);
    }
    if (!nullToAbsent || dividendTaxAmountValue != null) {
      map['dividend_tax_amount_value'] =
          Variable<double>(dividendTaxAmountValue);
    }
    if (!nullToAbsent || dividendTaxAmountCurrency != null) {
      map['dividend_tax_amount_currency'] =
          Variable<String>(dividendTaxAmountCurrency);
    }
    if (!nullToAbsent || dividendAmountValue != null) {
      map['dividend_amount_value'] = Variable<double>(dividendAmountValue);
    }
    if (!nullToAbsent || dividendAmountCurrency != null) {
      map['dividend_amount_currency'] =
          Variable<String>(dividendAmountCurrency);
    }
    if (!nullToAbsent || netDividendAmountValue != null) {
      map['net_dividend_amount_value'] =
          Variable<double>(netDividendAmountValue);
    }
    if (!nullToAbsent || netDividendAmountCurrency != null) {
      map['net_dividend_amount_currency'] =
          Variable<String>(netDividendAmountCurrency);
    }
    if (!nullToAbsent || amountPerInstrumentValue != null) {
      map['amount_per_instrument_value'] =
          Variable<double>(amountPerInstrumentValue);
    }
    if (!nullToAbsent || amountPerInstrumentCurrency != null) {
      map['amount_per_instrument_currency'] =
          Variable<String>(amountPerInstrumentCurrency);
    }
    if (!nullToAbsent || instrumentQuantity != null) {
      map['instrument_quantity'] = Variable<double>(instrumentQuantity);
    }
    if (!nullToAbsent || brokerDividendTaxType != null) {
      map['broker_dividend_tax_type'] = Variable<String>(brokerDividendTaxType);
    }
    if (!nullToAbsent || dividendTaxRate != null) {
      map['dividend_tax_rate'] = Variable<double>(dividendTaxRate);
    }
    if (!nullToAbsent || securityLendingRebateAmountValue != null) {
      map['security_lending_rebate_amount_value'] =
          Variable<double>(securityLendingRebateAmountValue);
    }
    if (!nullToAbsent || securityLendingRebateAmountCurrency != null) {
      map['security_lending_rebate_amount_currency'] =
          Variable<String>(securityLendingRebateAmountCurrency);
    }
    if (!nullToAbsent || securityLendingRebateTaxAmountCurrency != null) {
      map['security_lending_rebate_tax_amount_currency'] =
          Variable<String>(securityLendingRebateTaxAmountCurrency);
    }
    if (!nullToAbsent || securityLendingRebateTaxAmountValue != null) {
      map['security_lending_rebate_tax_amount_value'] =
          Variable<double>(securityLendingRebateTaxAmountValue);
    }
    if (!nullToAbsent || securityLendingTransferredAmountCurrency != null) {
      map['security_lending_transferred_amount_currency'] =
          Variable<String>(securityLendingTransferredAmountCurrency);
    }
    if (!nullToAbsent || securityLendingTransferredAmountValue != null) {
      map['security_lending_transferred_amount_value'] =
          Variable<double>(securityLendingTransferredAmountValue);
    }
    if (!nullToAbsent || securityLendingMonth != null) {
      map['security_lending_month'] = Variable<int>(securityLendingMonth);
    }
    if (!nullToAbsent || wealthManagementManagedPortfolioId != null) {
      map['wealth_management_managed_portfolio_id'] =
          Variable<String>(wealthManagementManagedPortfolioId);
    }
    if (!nullToAbsent || wealthManagementProductName != null) {
      map['wealth_management_product_name'] =
          Variable<String>(wealthManagementProductName);
    }
    if (!nullToAbsent || wealthManagementProductCategory != null) {
      map['wealth_management_product_category'] =
          Variable<String>(wealthManagementProductCategory);
    }
    if (!nullToAbsent || wealthManagementAccountName != null) {
      map['wealth_management_account_name'] =
          Variable<String>(wealthManagementAccountName);
    }
    if (!nullToAbsent || wealthManagementEstimatedExecutionDeadline != null) {
      map['wealth_management_estimated_execution_deadline'] =
          Variable<String>(wealthManagementEstimatedExecutionDeadline);
    }
    if (!nullToAbsent || wealthManagementTrackerSteps != null) {
      map['wealth_management_tracker_steps'] =
          Variable<String>(wealthManagementTrackerSteps);
    }
    if (!nullToAbsent || acquirerInstrumentName != null) {
      map['acquirer_instrument_name'] =
          Variable<String>(acquirerInstrumentName);
    }
    if (!nullToAbsent || acquirerInstrumentSymbol != null) {
      map['acquirer_instrument_symbol'] =
          Variable<String>(acquirerInstrumentSymbol);
    }
    if (!nullToAbsent || acquirerInstrumentExchangeId != null) {
      map['acquirer_instrument_exchange_id'] =
          Variable<String>(acquirerInstrumentExchangeId);
    }
    if (!nullToAbsent || acquirerInstrumentImageUrl != null) {
      map['acquirer_instrument_image_url'] =
          Variable<String>(acquirerInstrumentImageUrl);
    }
    if (!nullToAbsent || acquireeInstrumentName != null) {
      map['acquiree_instrument_name'] =
          Variable<String>(acquireeInstrumentName);
    }
    if (!nullToAbsent || acquireeInstrumentSymbol != null) {
      map['acquiree_instrument_symbol'] =
          Variable<String>(acquireeInstrumentSymbol);
    }
    if (!nullToAbsent || acquireeInstrumentExchangeId != null) {
      map['acquiree_instrument_exchange_id'] =
          Variable<String>(acquireeInstrumentExchangeId);
    }
    if (!nullToAbsent || acquireeInstrumentImageUrl != null) {
      map['acquiree_instrument_image_url'] =
          Variable<String>(acquireeInstrumentImageUrl);
    }
    if (!nullToAbsent || brokerAcquisitionAccountAmountValue != null) {
      map['broker_acquisition_account_amount_value'] =
          Variable<double>(brokerAcquisitionAccountAmountValue);
    }
    if (!nullToAbsent || brokerAcquisitionAccountAmountCurrency != null) {
      map['broker_acquisition_account_amount_currency'] =
          Variable<String>(brokerAcquisitionAccountAmountCurrency);
    }
    if (!nullToAbsent || brokerAcquisitionPositionDelta != null) {
      map['broker_acquisition_position_delta'] =
          Variable<double>(brokerAcquisitionPositionDelta);
    }
    if (!nullToAbsent || ipoCompanyName != null) {
      map['ipo_company_name'] = Variable<String>(ipoCompanyName);
    }
    if (!nullToAbsent || ipoSubscriptionNumber != null) {
      map['ipo_subscription_number'] = Variable<String>(ipoSubscriptionNumber);
    }
    if (!nullToAbsent || ipoLeverageValue != null) {
      map['ipo_leverage_value'] = Variable<double>(ipoLeverageValue);
    }
    if (!nullToAbsent || ipoLeverageCurrency != null) {
      map['ipo_leverage_currency'] = Variable<String>(ipoLeverageCurrency);
    }
    if (!nullToAbsent || ipoAmountValue != null) {
      map['ipo_amount_value'] = Variable<double>(ipoAmountValue);
    }
    if (!nullToAbsent || ipoAmountCurrency != null) {
      map['ipo_amount_currency'] = Variable<String>(ipoAmountCurrency);
    }
    if (!nullToAbsent || ipoTotalAmountValue != null) {
      map['ipo_total_amount_value'] = Variable<double>(ipoTotalAmountValue);
    }
    if (!nullToAbsent || ipoTotalAmountCurrency != null) {
      map['ipo_total_amount_currency'] =
          Variable<String>(ipoTotalAmountCurrency);
    }
    if (!nullToAbsent || ipoCompanyLogo != null) {
      map['ipo_company_logo'] = Variable<String>(ipoCompanyLogo);
    }
    if (!nullToAbsent || ipoEnhancementValue != null) {
      map['ipo_enhancement_value'] = Variable<double>(ipoEnhancementValue);
    }
    if (!nullToAbsent || ipoEnhancementCurrency != null) {
      map['ipo_enhancement_currency'] =
          Variable<String>(ipoEnhancementCurrency);
    }
    if (!nullToAbsent || ipoRefundAmountValue != null) {
      map['ipo_refund_amount_value'] = Variable<double>(ipoRefundAmountValue);
    }
    if (!nullToAbsent || ipoRefundAmountCurrency != null) {
      map['ipo_refund_amount_currency'] =
          Variable<String>(ipoRefundAmountCurrency);
    }
    if (!nullToAbsent || ipoLeveragePaidValue != null) {
      map['ipo_leverage_paid_value'] = Variable<double>(ipoLeveragePaidValue);
    }
    if (!nullToAbsent || ipoLeveragePaidCurrency != null) {
      map['ipo_leverage_paid_currency'] =
          Variable<String>(ipoLeveragePaidCurrency);
    }
    if (!nullToAbsent || ipoLeverageFeePercentage != null) {
      map['ipo_leverage_fee_percentage'] =
          Variable<double>(ipoLeverageFeePercentage);
    }
    if (!nullToAbsent || ipoLeverageFeeValue != null) {
      map['ipo_leverage_fee_value'] = Variable<double>(ipoLeverageFeeValue);
    }
    if (!nullToAbsent || ipoLeverageFeeCurrency != null) {
      map['ipo_leverage_fee_currency'] =
          Variable<String>(ipoLeverageFeeCurrency);
    }
    if (!nullToAbsent || ipoAllocationFeePercentage != null) {
      map['ipo_allocation_fee_percentage'] =
          Variable<double>(ipoAllocationFeePercentage);
    }
    if (!nullToAbsent || ipoAllotedFeeMoney != null) {
      map['ipo_alloted_fee_money'] = Variable<double>(ipoAllotedFeeMoney);
    }
    if (!nullToAbsent || ipoAllotedFeeMoneyCurrency != null) {
      map['ipo_alloted_fee_money_currency'] =
          Variable<String>(ipoAllotedFeeMoneyCurrency);
    }
    if (!nullToAbsent || ipoAllocatedMoney != null) {
      map['ipo_allocated_money'] = Variable<double>(ipoAllocatedMoney);
    }
    if (!nullToAbsent || ipoAllocatedMoneyCurrency != null) {
      map['ipo_allocated_money_currency'] =
          Variable<String>(ipoAllocatedMoneyCurrency);
    }
    map['dynamic_details'] = Variable<String>(dynamicDetails);
    map['linked_transaction_fee_details'] =
        Variable<String>(linkedTransactionFeeDetails);
    if (!nullToAbsent || cashbackCalculations != null) {
      map['cashback_calculations'] = Variable<String>(cashbackCalculations);
    }
    if (!nullToAbsent || carbonEmissionInGrams != null) {
      map['carbon_emission_in_grams'] = Variable<double>(carbonEmissionInGrams);
    }
    if (!nullToAbsent || carbonEmissionInOunces != null) {
      map['carbon_emission_in_ounces'] =
          Variable<double>(carbonEmissionInOunces);
    }
    if (!nullToAbsent || recurringTransferRuleId != null) {
      map['recurring_transfer_rule_id'] =
          Variable<String>(recurringTransferRuleId);
    }
    return map;
  }

  TransactionsCompanion toCompanion(bool nullToAbsent) {
    return TransactionsCompanion(
      id: Value(id),
      productType: Value(productType),
      accountId: Value(accountId),
      transactionAccountType: transactionAccountType == null && nullToAbsent
          ? const Value.absent()
          : Value(transactionAccountType),
      transactionType: Value(transactionType),
      transactionIdentifier: Value(transactionIdentifier),
      status: Value(status),
      referenceNumber: referenceNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(referenceNumber),
      transactionMode: Value(transactionMode),
      transactionSubType: transactionSubType == null && nullToAbsent
          ? const Value.absent()
          : Value(transactionSubType),
      internalTransactionStatus:
          internalTransactionStatus == null && nullToAbsent
              ? const Value.absent()
              : Value(internalTransactionStatus),
      transactionDateTime: transactionDateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(transactionDateTime),
      debtorName: debtorName == null && nullToAbsent
          ? const Value.absent()
          : Value(debtorName),
      customerId: customerId == null && nullToAbsent
          ? const Value.absent()
          : Value(customerId),
      coreBankingIdentifier: coreBankingIdentifier == null && nullToAbsent
          ? const Value.absent()
          : Value(coreBankingIdentifier),
      creditorName: creditorName == null && nullToAbsent
          ? const Value.absent()
          : Value(creditorName),
      description: description == null && nullToAbsent
          ? const Value.absent()
          : Value(description),
      subDescription: subDescription == null && nullToAbsent
          ? const Value.absent()
          : Value(subDescription),
      logoType: logoType == null && nullToAbsent
          ? const Value.absent()
          : Value(logoType),
      merchantName: merchantName == null && nullToAbsent
          ? const Value.absent()
          : Value(merchantName),
      transactionImageKey: transactionImageKey == null && nullToAbsent
          ? const Value.absent()
          : Value(transactionImageKey),
      amountValue: Value(amountValue),
      localAmount: localAmount == null && nullToAbsent
          ? const Value.absent()
          : Value(localAmount),
      exchangeRate: exchangeRate == null && nullToAbsent
          ? const Value.absent()
          : Value(exchangeRate),
      availableBalance: availableBalance == null && nullToAbsent
          ? const Value.absent()
          : Value(availableBalance),
      amountCurrency: Value(amountCurrency),
      executionDate: executionDate == null && nullToAbsent
          ? const Value.absent()
          : Value(executionDate),
      transactionCategory: Value(transactionCategory),
      tppExternalReferenceId: tppExternalReferenceId == null && nullToAbsent
          ? const Value.absent()
          : Value(tppExternalReferenceId),
      fxFromAmount: fxFromAmount == null && nullToAbsent
          ? const Value.absent()
          : Value(fxFromAmount),
      fxToAmount: fxToAmount == null && nullToAbsent
          ? const Value.absent()
          : Value(fxToAmount),
      fxFromAmountCurrency: fxFromAmountCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(fxFromAmountCurrency),
      fxToAmountCurrency: fxToAmountCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(fxToAmountCurrency),
      fxFormattedExchangeRate: fxFormattedExchangeRate == null && nullToAbsent
          ? const Value.absent()
          : Value(fxFormattedExchangeRate),
      orderId: orderId == null && nullToAbsent
          ? const Value.absent()
          : Value(orderId),
      portfolioId: portfolioId == null && nullToAbsent
          ? const Value.absent()
          : Value(portfolioId),
      instrumentId: instrumentId == null && nullToAbsent
          ? const Value.absent()
          : Value(instrumentId),
      createdDateTime: createdDateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(createdDateTime),
      orderSide: orderSide == null && nullToAbsent
          ? const Value.absent()
          : Value(orderSide),
      instrumentName: instrumentName == null && nullToAbsent
          ? const Value.absent()
          : Value(instrumentName),
      instrumentSymbol: instrumentSymbol == null && nullToAbsent
          ? const Value.absent()
          : Value(instrumentSymbol),
      instrumentExchangeId: instrumentExchangeId == null && nullToAbsent
          ? const Value.absent()
          : Value(instrumentExchangeId),
      instrumentImageUrl: instrumentImageUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(instrumentImageUrl),
      orderType: orderType == null && nullToAbsent
          ? const Value.absent()
          : Value(orderType),
      errorCode: errorCode == null && nullToAbsent
          ? const Value.absent()
          : Value(errorCode),
      errorMessage: errorMessage == null && nullToAbsent
          ? const Value.absent()
          : Value(errorMessage),
      executedQuantity: executedQuantity == null && nullToAbsent
          ? const Value.absent()
          : Value(executedQuantity),
      estimatedQuantity: estimatedQuantity == null && nullToAbsent
          ? const Value.absent()
          : Value(estimatedQuantity),
      executedAmountValue: executedAmountValue == null && nullToAbsent
          ? const Value.absent()
          : Value(executedAmountValue),
      executedAmountCurrency: executedAmountCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(executedAmountCurrency),
      executedNetAmountValue: executedNetAmountValue == null && nullToAbsent
          ? const Value.absent()
          : Value(executedNetAmountValue),
      executedNetAmountCurrency:
          executedNetAmountCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(executedNetAmountCurrency),
      estimatedAmountValue: estimatedAmountValue == null && nullToAbsent
          ? const Value.absent()
          : Value(estimatedAmountValue),
      estimatedAmountCurrency: estimatedAmountCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(estimatedAmountCurrency),
      estimatedNetAmountValue: estimatedNetAmountValue == null && nullToAbsent
          ? const Value.absent()
          : Value(estimatedNetAmountValue),
      estimatedNetAmountCurrency:
          estimatedNetAmountCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(estimatedNetAmountCurrency),
      commissionAmountValue: commissionAmountValue == null && nullToAbsent
          ? const Value.absent()
          : Value(commissionAmountValue),
      commissionAmountCurrency: commissionAmountCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(commissionAmountCurrency),
      estimatedCommissionAmountValue:
          estimatedCommissionAmountValue == null && nullToAbsent
              ? const Value.absent()
              : Value(estimatedCommissionAmountValue),
      estimatedCommissionAmountCurrency:
          estimatedCommissionAmountCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(estimatedCommissionAmountCurrency),
      vatValue: vatValue == null && nullToAbsent
          ? const Value.absent()
          : Value(vatValue),
      vatCurrency: vatCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(vatCurrency),
      estimatedVatValue: estimatedVatValue == null && nullToAbsent
          ? const Value.absent()
          : Value(estimatedVatValue),
      estimatedVatCurrency: estimatedVatCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(estimatedVatCurrency),
      averagePriceValue: averagePriceValue == null && nullToAbsent
          ? const Value.absent()
          : Value(averagePriceValue),
      averagePriceCurrency: averagePriceCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(averagePriceCurrency),
      estimatedPriceValue: estimatedPriceValue == null && nullToAbsent
          ? const Value.absent()
          : Value(estimatedPriceValue),
      estimatedPriceCurrency: estimatedPriceCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(estimatedPriceCurrency),
      executedTotalCommissionAmount:
          executedTotalCommissionAmount == null && nullToAbsent
              ? const Value.absent()
              : Value(executedTotalCommissionAmount),
      executedTotalCommissionCurrency:
          executedTotalCommissionCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(executedTotalCommissionCurrency),
      estimatedTotalCommissionAmount:
          estimatedTotalCommissionAmount == null && nullToAbsent
              ? const Value.absent()
              : Value(estimatedTotalCommissionAmount),
      estimatedTotalCommissionCurrency:
          estimatedTotalCommissionCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(estimatedTotalCommissionCurrency),
      commissionCalculationBasisPoints:
          commissionCalculationBasisPoints == null && nullToAbsent
              ? const Value.absent()
              : Value(commissionCalculationBasisPoints),
      commissionCalculationBaseAmount:
          commissionCalculationBaseAmount == null && nullToAbsent
              ? const Value.absent()
              : Value(commissionCalculationBaseAmount),
      commissionCalculationMinAmount:
          commissionCalculationMinAmount == null && nullToAbsent
              ? const Value.absent()
              : Value(commissionCalculationMinAmount),
      commissionCalculationBaseAmountCurrency:
          commissionCalculationBaseAmountCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(commissionCalculationBaseAmountCurrency),
      commissionCalculationMinAmountCurrency:
          commissionCalculationMinAmountCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(commissionCalculationMinAmountCurrency),
      instrumentExchangeCode: instrumentExchangeCode == null && nullToAbsent
          ? const Value.absent()
          : Value(instrumentExchangeCode),
      recurringOrderTemplateId: recurringOrderTemplateId == null && nullToAbsent
          ? const Value.absent()
          : Value(recurringOrderTemplateId),
      recurringOrderAccountName:
          recurringOrderAccountName == null && nullToAbsent
              ? const Value.absent()
              : Value(recurringOrderAccountName),
      recurringOrderFrequencyType:
          recurringOrderFrequencyType == null && nullToAbsent
              ? const Value.absent()
              : Value(recurringOrderFrequencyType),
      recurringOrderFrequencyDayOfWeek:
          recurringOrderFrequencyDayOfWeek == null && nullToAbsent
              ? const Value.absent()
              : Value(recurringOrderFrequencyDayOfWeek),
      recurringOrderFrequencyDayOfMonth:
          recurringOrderFrequencyDayOfMonth == null && nullToAbsent
              ? const Value.absent()
              : Value(recurringOrderFrequencyDayOfMonth),
      expirationDate: expirationDate == null && nullToAbsent
          ? const Value.absent()
          : Value(expirationDate),
      isPaymentProofReady: Value(isPaymentProofReady),
      isPaymentTrackingAvailable:
          isPaymentTrackingAvailable == null && nullToAbsent
              ? const Value.absent()
              : Value(isPaymentTrackingAvailable),
      paymentFeeTotalAmount: paymentFeeTotalAmount == null && nullToAbsent
          ? const Value.absent()
          : Value(paymentFeeTotalAmount),
      paymentFeeTotalCurrency: paymentFeeTotalCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(paymentFeeTotalCurrency),
      paymentTargetCountryCode: paymentTargetCountryCode == null && nullToAbsent
          ? const Value.absent()
          : Value(paymentTargetCountryCode),
      paymentPhoneNumber: paymentPhoneNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(paymentPhoneNumber),
      paymentWarningMessage: paymentWarningMessage == null && nullToAbsent
          ? const Value.absent()
          : Value(paymentWarningMessage),
      paymentFeeTypeDescriptionWio:
          paymentFeeTypeDescriptionWio == null && nullToAbsent
              ? const Value.absent()
              : Value(paymentFeeTypeDescriptionWio),
      paymentFeeTypeAmountWio: paymentFeeTypeAmountWio == null && nullToAbsent
          ? const Value.absent()
          : Value(paymentFeeTypeAmountWio),
      paymentFeeTypeCurrencyWio:
          paymentFeeTypeCurrencyWio == null && nullToAbsent
              ? const Value.absent()
              : Value(paymentFeeTypeCurrencyWio),
      paymentFeeTypeDescriptionWioCorrespondentBank:
          paymentFeeTypeDescriptionWioCorrespondentBank == null && nullToAbsent
              ? const Value.absent()
              : Value(paymentFeeTypeDescriptionWioCorrespondentBank),
      paymentFeeTypeAmountWioCorrespondentBank:
          paymentFeeTypeAmountWioCorrespondentBank == null && nullToAbsent
              ? const Value.absent()
              : Value(paymentFeeTypeAmountWioCorrespondentBank),
      paymentFeeTypeCurrencyWioCorrespondentBank:
          paymentFeeTypeCurrencyWioCorrespondentBank == null && nullToAbsent
              ? const Value.absent()
              : Value(paymentFeeTypeCurrencyWioCorrespondentBank),
      paymentFeeTypeDescriptionWise:
          paymentFeeTypeDescriptionWise == null && nullToAbsent
              ? const Value.absent()
              : Value(paymentFeeTypeDescriptionWise),
      paymentFeeTypeAmountWise: paymentFeeTypeAmountWise == null && nullToAbsent
          ? const Value.absent()
          : Value(paymentFeeTypeAmountWise),
      paymentFeeTypeCurrencyWise:
          paymentFeeTypeCurrencyWise == null && nullToAbsent
              ? const Value.absent()
              : Value(paymentFeeTypeCurrencyWise),
      internationalTargetCountry:
          internationalTargetCountry == null && nullToAbsent
              ? const Value.absent()
              : Value(internationalTargetCountry),
      internationalCompletedDateTime:
          internationalCompletedDateTime == null && nullToAbsent
              ? const Value.absent()
              : Value(internationalCompletedDateTime),
      internationalEstimatedDateTime:
          internationalEstimatedDateTime == null && nullToAbsent
              ? const Value.absent()
              : Value(internationalEstimatedDateTime),
      internationalFeeChargingType:
          internationalFeeChargingType == null && nullToAbsent
              ? const Value.absent()
              : Value(internationalFeeChargingType),
      internationalSwiftCode: internationalSwiftCode == null && nullToAbsent
          ? const Value.absent()
          : Value(internationalSwiftCode),
      internationalAccountNumber:
          internationalAccountNumber == null && nullToAbsent
              ? const Value.absent()
              : Value(internationalAccountNumber),
      internationalPurposeDescription:
          internationalPurposeDescription == null && nullToAbsent
              ? const Value.absent()
              : Value(internationalPurposeDescription),
      internationalPurposeCode: internationalPurposeCode == null && nullToAbsent
          ? const Value.absent()
          : Value(internationalPurposeCode),
      internationalNotes: internationalNotes == null && nullToAbsent
          ? const Value.absent()
          : Value(internationalNotes),
      internationalBankName: internationalBankName == null && nullToAbsent
          ? const Value.absent()
          : Value(internationalBankName),
      internationalTargetAmount:
          internationalTargetAmount == null && nullToAbsent
              ? const Value.absent()
              : Value(internationalTargetAmount),
      internationalTargetAmountCurrency:
          internationalTargetAmountCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(internationalTargetAmountCurrency),
      internationalTransferFee: internationalTransferFee == null && nullToAbsent
          ? const Value.absent()
          : Value(internationalTransferFee),
      internationalTransferFeeCurrency:
          internationalTransferFeeCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(internationalTransferFeeCurrency),
      fabExpirationDate: fabExpirationDate == null && nullToAbsent
          ? const Value.absent()
          : Value(fabExpirationDate),
      fabBankName: fabBankName == null && nullToAbsent
          ? const Value.absent()
          : Value(fabBankName),
      fabTotalFeeAmount: fabTotalFeeAmount == null && nullToAbsent
          ? const Value.absent()
          : Value(fabTotalFeeAmount),
      fabTotalFeeAmountCurrency:
          fabTotalFeeAmountCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(fabTotalFeeAmountCurrency),
      fabUpdatedDate: fabUpdatedDate == null && nullToAbsent
          ? const Value.absent()
          : Value(fabUpdatedDate),
      fabCancellationReason: fabCancellationReason == null && nullToAbsent
          ? const Value.absent()
          : Value(fabCancellationReason),
      fabChequeNumber: fabChequeNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(fabChequeNumber),
      fabChequeDate: fabChequeDate == null && nullToAbsent
          ? const Value.absent()
          : Value(fabChequeDate),
      fabFrontImageUrl: fabFrontImageUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(fabFrontImageUrl),
      localPurposeDescription: localPurposeDescription == null && nullToAbsent
          ? const Value.absent()
          : Value(localPurposeDescription),
      localPurposeCode: localPurposeCode == null && nullToAbsent
          ? const Value.absent()
          : Value(localPurposeCode),
      localNote: localNote == null && nullToAbsent
          ? const Value.absent()
          : Value(localNote),
      localTargetCountry: localTargetCountry == null && nullToAbsent
          ? const Value.absent()
          : Value(localTargetCountry),
      localCompletedDateTime: localCompletedDateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(localCompletedDateTime),
      localEstimatedDateTime: localEstimatedDateTime == null && nullToAbsent
          ? const Value.absent()
          : Value(localEstimatedDateTime),
      localAccountNumber: localAccountNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(localAccountNumber),
      localSwiftCode: localSwiftCode == null && nullToAbsent
          ? const Value.absent()
          : Value(localSwiftCode),
      localBankName: localBankName == null && nullToAbsent
          ? const Value.absent()
          : Value(localBankName),
      localTargetAmount: localTargetAmount == null && nullToAbsent
          ? const Value.absent()
          : Value(localTargetAmount),
      localTargetAmountCurrency:
          localTargetAmountCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(localTargetAmountCurrency),
      localTransferSource: localTransferSource == null && nullToAbsent
          ? const Value.absent()
          : Value(localTransferSource),
      localTransferApplicationId:
          localTransferApplicationId == null && nullToAbsent
              ? const Value.absent()
              : Value(localTransferApplicationId),
      localRecipientName: localRecipientName == null && nullToAbsent
          ? const Value.absent()
          : Value(localRecipientName),
      npssExpirationDate: npssExpirationDate == null && nullToAbsent
          ? const Value.absent()
          : Value(npssExpirationDate),
      npssNotes: npssNotes == null && nullToAbsent
          ? const Value.absent()
          : Value(npssNotes),
      npssRtpInitiationDate: npssRtpInitiationDate == null && nullToAbsent
          ? const Value.absent()
          : Value(npssRtpInitiationDate),
      npssShowInPendingRtps: npssShowInPendingRtps == null && nullToAbsent
          ? const Value.absent()
          : Value(npssShowInPendingRtps),
      npssShowRtpResendButton: npssShowRtpResendButton == null && nullToAbsent
          ? const Value.absent()
          : Value(npssShowRtpResendButton),
      npssOriginalTransactionReference:
          npssOriginalTransactionReference == null && nullToAbsent
              ? const Value.absent()
              : Value(npssOriginalTransactionReference),
      npssOriginalTransactionReason:
          npssOriginalTransactionReason == null && nullToAbsent
              ? const Value.absent()
              : Value(npssOriginalTransactionReason),
      npssOriginalTransactionDeeplink:
          npssOriginalTransactionDeeplink == null && nullToAbsent
              ? const Value.absent()
              : Value(npssOriginalTransactionDeeplink),
      token:
          token == null && nullToAbsent ? const Value.absent() : Value(token),
      accNumberLast4Digits: accNumberLast4Digits == null && nullToAbsent
          ? const Value.absent()
          : Value(accNumberLast4Digits),
      cardMaskedPan: cardMaskedPan == null && nullToAbsent
          ? const Value.absent()
          : Value(cardMaskedPan),
      merchantCountryCode: merchantCountryCode == null && nullToAbsent
          ? const Value.absent()
          : Value(merchantCountryCode),
      originalCardTransactionCurrency:
          originalCardTransactionCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(originalCardTransactionCurrency),
      originalCardTransactionAmount:
          originalCardTransactionAmount == null && nullToAbsent
              ? const Value.absent()
              : Value(originalCardTransactionAmount),
      earnedCashback: earnedCashback == null && nullToAbsent
          ? const Value.absent()
          : Value(earnedCashback),
      cashBackPercentage: cashBackPercentage == null && nullToAbsent
          ? const Value.absent()
          : Value(cashBackPercentage),
      cashBackTransactionCurrency:
          cashBackTransactionCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(cashBackTransactionCurrency),
      dividendTaxAmountValue: dividendTaxAmountValue == null && nullToAbsent
          ? const Value.absent()
          : Value(dividendTaxAmountValue),
      dividendTaxAmountCurrency:
          dividendTaxAmountCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(dividendTaxAmountCurrency),
      dividendAmountValue: dividendAmountValue == null && nullToAbsent
          ? const Value.absent()
          : Value(dividendAmountValue),
      dividendAmountCurrency: dividendAmountCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(dividendAmountCurrency),
      netDividendAmountValue: netDividendAmountValue == null && nullToAbsent
          ? const Value.absent()
          : Value(netDividendAmountValue),
      netDividendAmountCurrency:
          netDividendAmountCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(netDividendAmountCurrency),
      amountPerInstrumentValue: amountPerInstrumentValue == null && nullToAbsent
          ? const Value.absent()
          : Value(amountPerInstrumentValue),
      amountPerInstrumentCurrency:
          amountPerInstrumentCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(amountPerInstrumentCurrency),
      instrumentQuantity: instrumentQuantity == null && nullToAbsent
          ? const Value.absent()
          : Value(instrumentQuantity),
      brokerDividendTaxType: brokerDividendTaxType == null && nullToAbsent
          ? const Value.absent()
          : Value(brokerDividendTaxType),
      dividendTaxRate: dividendTaxRate == null && nullToAbsent
          ? const Value.absent()
          : Value(dividendTaxRate),
      securityLendingRebateAmountValue:
          securityLendingRebateAmountValue == null && nullToAbsent
              ? const Value.absent()
              : Value(securityLendingRebateAmountValue),
      securityLendingRebateAmountCurrency:
          securityLendingRebateAmountCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(securityLendingRebateAmountCurrency),
      securityLendingRebateTaxAmountCurrency:
          securityLendingRebateTaxAmountCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(securityLendingRebateTaxAmountCurrency),
      securityLendingRebateTaxAmountValue:
          securityLendingRebateTaxAmountValue == null && nullToAbsent
              ? const Value.absent()
              : Value(securityLendingRebateTaxAmountValue),
      securityLendingTransferredAmountCurrency:
          securityLendingTransferredAmountCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(securityLendingTransferredAmountCurrency),
      securityLendingTransferredAmountValue:
          securityLendingTransferredAmountValue == null && nullToAbsent
              ? const Value.absent()
              : Value(securityLendingTransferredAmountValue),
      securityLendingMonth: securityLendingMonth == null && nullToAbsent
          ? const Value.absent()
          : Value(securityLendingMonth),
      wealthManagementManagedPortfolioId:
          wealthManagementManagedPortfolioId == null && nullToAbsent
              ? const Value.absent()
              : Value(wealthManagementManagedPortfolioId),
      wealthManagementProductName:
          wealthManagementProductName == null && nullToAbsent
              ? const Value.absent()
              : Value(wealthManagementProductName),
      wealthManagementProductCategory:
          wealthManagementProductCategory == null && nullToAbsent
              ? const Value.absent()
              : Value(wealthManagementProductCategory),
      wealthManagementAccountName:
          wealthManagementAccountName == null && nullToAbsent
              ? const Value.absent()
              : Value(wealthManagementAccountName),
      wealthManagementEstimatedExecutionDeadline:
          wealthManagementEstimatedExecutionDeadline == null && nullToAbsent
              ? const Value.absent()
              : Value(wealthManagementEstimatedExecutionDeadline),
      wealthManagementTrackerSteps:
          wealthManagementTrackerSteps == null && nullToAbsent
              ? const Value.absent()
              : Value(wealthManagementTrackerSteps),
      acquirerInstrumentName: acquirerInstrumentName == null && nullToAbsent
          ? const Value.absent()
          : Value(acquirerInstrumentName),
      acquirerInstrumentSymbol: acquirerInstrumentSymbol == null && nullToAbsent
          ? const Value.absent()
          : Value(acquirerInstrumentSymbol),
      acquirerInstrumentExchangeId:
          acquirerInstrumentExchangeId == null && nullToAbsent
              ? const Value.absent()
              : Value(acquirerInstrumentExchangeId),
      acquirerInstrumentImageUrl:
          acquirerInstrumentImageUrl == null && nullToAbsent
              ? const Value.absent()
              : Value(acquirerInstrumentImageUrl),
      acquireeInstrumentName: acquireeInstrumentName == null && nullToAbsent
          ? const Value.absent()
          : Value(acquireeInstrumentName),
      acquireeInstrumentSymbol: acquireeInstrumentSymbol == null && nullToAbsent
          ? const Value.absent()
          : Value(acquireeInstrumentSymbol),
      acquireeInstrumentExchangeId:
          acquireeInstrumentExchangeId == null && nullToAbsent
              ? const Value.absent()
              : Value(acquireeInstrumentExchangeId),
      acquireeInstrumentImageUrl:
          acquireeInstrumentImageUrl == null && nullToAbsent
              ? const Value.absent()
              : Value(acquireeInstrumentImageUrl),
      brokerAcquisitionAccountAmountValue:
          brokerAcquisitionAccountAmountValue == null && nullToAbsent
              ? const Value.absent()
              : Value(brokerAcquisitionAccountAmountValue),
      brokerAcquisitionAccountAmountCurrency:
          brokerAcquisitionAccountAmountCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(brokerAcquisitionAccountAmountCurrency),
      brokerAcquisitionPositionDelta:
          brokerAcquisitionPositionDelta == null && nullToAbsent
              ? const Value.absent()
              : Value(brokerAcquisitionPositionDelta),
      ipoCompanyName: ipoCompanyName == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoCompanyName),
      ipoSubscriptionNumber: ipoSubscriptionNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoSubscriptionNumber),
      ipoLeverageValue: ipoLeverageValue == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoLeverageValue),
      ipoLeverageCurrency: ipoLeverageCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoLeverageCurrency),
      ipoAmountValue: ipoAmountValue == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoAmountValue),
      ipoAmountCurrency: ipoAmountCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoAmountCurrency),
      ipoTotalAmountValue: ipoTotalAmountValue == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoTotalAmountValue),
      ipoTotalAmountCurrency: ipoTotalAmountCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoTotalAmountCurrency),
      ipoCompanyLogo: ipoCompanyLogo == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoCompanyLogo),
      ipoEnhancementValue: ipoEnhancementValue == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoEnhancementValue),
      ipoEnhancementCurrency: ipoEnhancementCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoEnhancementCurrency),
      ipoRefundAmountValue: ipoRefundAmountValue == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoRefundAmountValue),
      ipoRefundAmountCurrency: ipoRefundAmountCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoRefundAmountCurrency),
      ipoLeveragePaidValue: ipoLeveragePaidValue == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoLeveragePaidValue),
      ipoLeveragePaidCurrency: ipoLeveragePaidCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoLeveragePaidCurrency),
      ipoLeverageFeePercentage: ipoLeverageFeePercentage == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoLeverageFeePercentage),
      ipoLeverageFeeValue: ipoLeverageFeeValue == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoLeverageFeeValue),
      ipoLeverageFeeCurrency: ipoLeverageFeeCurrency == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoLeverageFeeCurrency),
      ipoAllocationFeePercentage:
          ipoAllocationFeePercentage == null && nullToAbsent
              ? const Value.absent()
              : Value(ipoAllocationFeePercentage),
      ipoAllotedFeeMoney: ipoAllotedFeeMoney == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoAllotedFeeMoney),
      ipoAllotedFeeMoneyCurrency:
          ipoAllotedFeeMoneyCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(ipoAllotedFeeMoneyCurrency),
      ipoAllocatedMoney: ipoAllocatedMoney == null && nullToAbsent
          ? const Value.absent()
          : Value(ipoAllocatedMoney),
      ipoAllocatedMoneyCurrency:
          ipoAllocatedMoneyCurrency == null && nullToAbsent
              ? const Value.absent()
              : Value(ipoAllocatedMoneyCurrency),
      dynamicDetails: Value(dynamicDetails),
      linkedTransactionFeeDetails: Value(linkedTransactionFeeDetails),
      cashbackCalculations: cashbackCalculations == null && nullToAbsent
          ? const Value.absent()
          : Value(cashbackCalculations),
      carbonEmissionInGrams: carbonEmissionInGrams == null && nullToAbsent
          ? const Value.absent()
          : Value(carbonEmissionInGrams),
      carbonEmissionInOunces: carbonEmissionInOunces == null && nullToAbsent
          ? const Value.absent()
          : Value(carbonEmissionInOunces),
      recurringTransferRuleId: recurringTransferRuleId == null && nullToAbsent
          ? const Value.absent()
          : Value(recurringTransferRuleId),
    );
  }

  factory TransactionsData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return TransactionsData(
      id: serializer.fromJson<String>(json['id']),
      productType: serializer.fromJson<String>(json['productType']),
      accountId: serializer.fromJson<String>(json['accountId']),
      transactionAccountType:
          serializer.fromJson<String?>(json['transactionAccountType']),
      transactionType: serializer.fromJson<String>(json['transactionType']),
      transactionIdentifier:
          serializer.fromJson<String>(json['transactionIdentifier']),
      status: serializer.fromJson<String>(json['status']),
      referenceNumber: serializer.fromJson<String?>(json['referenceNumber']),
      transactionMode: serializer.fromJson<String>(json['transactionMode']),
      transactionSubType:
          serializer.fromJson<String?>(json['transactionSubType']),
      internalTransactionStatus:
          serializer.fromJson<String?>(json['internalTransactionStatus']),
      transactionDateTime:
          serializer.fromJson<DateTime?>(json['transactionDateTime']),
      debtorName: serializer.fromJson<String?>(json['debtorName']),
      customerId: serializer.fromJson<String?>(json['customerId']),
      coreBankingIdentifier:
          serializer.fromJson<String?>(json['coreBankingIdentifier']),
      creditorName: serializer.fromJson<String?>(json['creditorName']),
      description: serializer.fromJson<String?>(json['description']),
      subDescription: serializer.fromJson<String?>(json['subDescription']),
      logoType: serializer.fromJson<String?>(json['logoType']),
      merchantName: serializer.fromJson<String?>(json['merchantName']),
      transactionImageKey:
          serializer.fromJson<String?>(json['transactionImageKey']),
      amountValue: serializer.fromJson<double>(json['amountValue']),
      localAmount: serializer.fromJson<double?>(json['localAmount']),
      exchangeRate: serializer.fromJson<double?>(json['exchangeRate']),
      availableBalance: serializer.fromJson<double?>(json['availableBalance']),
      amountCurrency: serializer.fromJson<String>(json['amountCurrency']),
      executionDate: serializer.fromJson<DateTime?>(json['executionDate']),
      transactionCategory:
          serializer.fromJson<String>(json['transactionCategory']),
      tppExternalReferenceId:
          serializer.fromJson<String?>(json['tppExternalReferenceId']),
      fxFromAmount: serializer.fromJson<double?>(json['fxFromAmount']),
      fxToAmount: serializer.fromJson<double?>(json['fxToAmount']),
      fxFromAmountCurrency:
          serializer.fromJson<String?>(json['fxFromAmountCurrency']),
      fxToAmountCurrency:
          serializer.fromJson<String?>(json['fxToAmountCurrency']),
      fxFormattedExchangeRate:
          serializer.fromJson<String?>(json['fxFormattedExchangeRate']),
      orderId: serializer.fromJson<String?>(json['orderId']),
      portfolioId: serializer.fromJson<String?>(json['portfolioId']),
      instrumentId: serializer.fromJson<String?>(json['instrumentId']),
      createdDateTime: serializer.fromJson<DateTime?>(json['createdDateTime']),
      orderSide: serializer.fromJson<String?>(json['orderSide']),
      instrumentName: serializer.fromJson<String?>(json['instrumentName']),
      instrumentSymbol: serializer.fromJson<String?>(json['instrumentSymbol']),
      instrumentExchangeId:
          serializer.fromJson<String?>(json['instrumentExchangeId']),
      instrumentImageUrl:
          serializer.fromJson<String?>(json['instrumentImageUrl']),
      orderType: serializer.fromJson<String?>(json['orderType']),
      errorCode: serializer.fromJson<int?>(json['errorCode']),
      errorMessage: serializer.fromJson<String?>(json['errorMessage']),
      executedQuantity: serializer.fromJson<double?>(json['executedQuantity']),
      estimatedQuantity:
          serializer.fromJson<double?>(json['estimatedQuantity']),
      executedAmountValue:
          serializer.fromJson<double?>(json['executedAmountValue']),
      executedAmountCurrency:
          serializer.fromJson<String?>(json['executedAmountCurrency']),
      executedNetAmountValue:
          serializer.fromJson<double?>(json['executedNetAmountValue']),
      executedNetAmountCurrency:
          serializer.fromJson<String?>(json['executedNetAmountCurrency']),
      estimatedAmountValue:
          serializer.fromJson<double?>(json['estimatedAmountValue']),
      estimatedAmountCurrency:
          serializer.fromJson<String?>(json['estimatedAmountCurrency']),
      estimatedNetAmountValue:
          serializer.fromJson<double?>(json['estimatedNetAmountValue']),
      estimatedNetAmountCurrency:
          serializer.fromJson<String?>(json['estimatedNetAmountCurrency']),
      commissionAmountValue:
          serializer.fromJson<double?>(json['commissionAmountValue']),
      commissionAmountCurrency:
          serializer.fromJson<String?>(json['commissionAmountCurrency']),
      estimatedCommissionAmountValue:
          serializer.fromJson<double?>(json['estimatedCommissionAmountValue']),
      estimatedCommissionAmountCurrency: serializer
          .fromJson<String?>(json['estimatedCommissionAmountCurrency']),
      vatValue: serializer.fromJson<double?>(json['vatValue']),
      vatCurrency: serializer.fromJson<String?>(json['vatCurrency']),
      estimatedVatValue:
          serializer.fromJson<double?>(json['estimatedVatValue']),
      estimatedVatCurrency:
          serializer.fromJson<String?>(json['estimatedVatCurrency']),
      averagePriceValue:
          serializer.fromJson<double?>(json['averagePriceValue']),
      averagePriceCurrency:
          serializer.fromJson<String?>(json['averagePriceCurrency']),
      estimatedPriceValue:
          serializer.fromJson<double?>(json['estimatedPriceValue']),
      estimatedPriceCurrency:
          serializer.fromJson<String?>(json['estimatedPriceCurrency']),
      executedTotalCommissionAmount:
          serializer.fromJson<double?>(json['executedTotalCommissionAmount']),
      executedTotalCommissionCurrency:
          serializer.fromJson<String?>(json['executedTotalCommissionCurrency']),
      estimatedTotalCommissionAmount:
          serializer.fromJson<double?>(json['estimatedTotalCommissionAmount']),
      estimatedTotalCommissionCurrency: serializer
          .fromJson<String?>(json['estimatedTotalCommissionCurrency']),
      commissionCalculationBasisPoints: serializer
          .fromJson<double?>(json['commissionCalculationBasisPoints']),
      commissionCalculationBaseAmount:
          serializer.fromJson<double?>(json['commissionCalculationBaseAmount']),
      commissionCalculationMinAmount:
          serializer.fromJson<double?>(json['commissionCalculationMinAmount']),
      commissionCalculationBaseAmountCurrency: serializer
          .fromJson<String?>(json['commissionCalculationBaseAmountCurrency']),
      commissionCalculationMinAmountCurrency: serializer
          .fromJson<String?>(json['commissionCalculationMinAmountCurrency']),
      instrumentExchangeCode:
          serializer.fromJson<String?>(json['instrumentExchangeCode']),
      recurringOrderTemplateId:
          serializer.fromJson<String?>(json['recurringOrderTemplateId']),
      recurringOrderAccountName:
          serializer.fromJson<String?>(json['recurringOrderAccountName']),
      recurringOrderFrequencyType:
          serializer.fromJson<String?>(json['recurringOrderFrequencyType']),
      recurringOrderFrequencyDayOfWeek: serializer
          .fromJson<String?>(json['recurringOrderFrequencyDayOfWeek']),
      recurringOrderFrequencyDayOfMonth:
          serializer.fromJson<int?>(json['recurringOrderFrequencyDayOfMonth']),
      expirationDate: serializer.fromJson<DateTime?>(json['expirationDate']),
      isPaymentProofReady:
          serializer.fromJson<bool>(json['isPaymentProofReady']),
      isPaymentTrackingAvailable:
          serializer.fromJson<bool?>(json['isPaymentTrackingAvailable']),
      paymentFeeTotalAmount:
          serializer.fromJson<double?>(json['paymentFeeTotalAmount']),
      paymentFeeTotalCurrency:
          serializer.fromJson<String?>(json['paymentFeeTotalCurrency']),
      paymentTargetCountryCode:
          serializer.fromJson<String?>(json['paymentTargetCountryCode']),
      paymentPhoneNumber:
          serializer.fromJson<String?>(json['paymentPhoneNumber']),
      paymentWarningMessage:
          serializer.fromJson<String?>(json['paymentWarningMessage']),
      paymentFeeTypeDescriptionWio:
          serializer.fromJson<String?>(json['paymentFeeTypeDescriptionWio']),
      paymentFeeTypeAmountWio:
          serializer.fromJson<double?>(json['paymentFeeTypeAmountWio']),
      paymentFeeTypeCurrencyWio:
          serializer.fromJson<String?>(json['paymentFeeTypeCurrencyWio']),
      paymentFeeTypeDescriptionWioCorrespondentBank:
          serializer.fromJson<String?>(
              json['paymentFeeTypeDescriptionWioCorrespondentBank']),
      paymentFeeTypeAmountWioCorrespondentBank: serializer
          .fromJson<double?>(json['paymentFeeTypeAmountWioCorrespondentBank']),
      paymentFeeTypeCurrencyWioCorrespondentBank: serializer.fromJson<String?>(
          json['paymentFeeTypeCurrencyWioCorrespondentBank']),
      paymentFeeTypeDescriptionWise:
          serializer.fromJson<String?>(json['paymentFeeTypeDescriptionWise']),
      paymentFeeTypeAmountWise:
          serializer.fromJson<double?>(json['paymentFeeTypeAmountWise']),
      paymentFeeTypeCurrencyWise:
          serializer.fromJson<String?>(json['paymentFeeTypeCurrencyWise']),
      internationalTargetCountry:
          serializer.fromJson<String?>(json['internationalTargetCountry']),
      internationalCompletedDateTime: serializer
          .fromJson<DateTime?>(json['internationalCompletedDateTime']),
      internationalEstimatedDateTime:
          serializer.fromJson<String?>(json['internationalEstimatedDateTime']),
      internationalFeeChargingType:
          serializer.fromJson<String?>(json['internationalFeeChargingType']),
      internationalSwiftCode:
          serializer.fromJson<String?>(json['internationalSwiftCode']),
      internationalAccountNumber:
          serializer.fromJson<String?>(json['internationalAccountNumber']),
      internationalPurposeDescription:
          serializer.fromJson<String?>(json['internationalPurposeDescription']),
      internationalPurposeCode:
          serializer.fromJson<String?>(json['internationalPurposeCode']),
      internationalNotes:
          serializer.fromJson<String?>(json['internationalNotes']),
      internationalBankName:
          serializer.fromJson<String?>(json['internationalBankName']),
      internationalTargetAmount:
          serializer.fromJson<double?>(json['internationalTargetAmount']),
      internationalTargetAmountCurrency: serializer
          .fromJson<String?>(json['internationalTargetAmountCurrency']),
      internationalTransferFee:
          serializer.fromJson<double?>(json['internationalTransferFee']),
      internationalTransferFeeCurrency: serializer
          .fromJson<String?>(json['internationalTransferFeeCurrency']),
      fabExpirationDate:
          serializer.fromJson<DateTime?>(json['fabExpirationDate']),
      fabBankName: serializer.fromJson<String?>(json['fabBankName']),
      fabTotalFeeAmount:
          serializer.fromJson<double?>(json['fabTotalFeeAmount']),
      fabTotalFeeAmountCurrency:
          serializer.fromJson<String?>(json['fabTotalFeeAmountCurrency']),
      fabUpdatedDate: serializer.fromJson<DateTime?>(json['fabUpdatedDate']),
      fabCancellationReason:
          serializer.fromJson<String?>(json['fabCancellationReason']),
      fabChequeNumber: serializer.fromJson<String?>(json['fabChequeNumber']),
      fabChequeDate: serializer.fromJson<DateTime?>(json['fabChequeDate']),
      fabFrontImageUrl: serializer.fromJson<String?>(json['fabFrontImageUrl']),
      localPurposeDescription:
          serializer.fromJson<String?>(json['localPurposeDescription']),
      localPurposeCode: serializer.fromJson<String?>(json['localPurposeCode']),
      localNote: serializer.fromJson<String?>(json['localNote']),
      localTargetCountry:
          serializer.fromJson<String?>(json['localTargetCountry']),
      localCompletedDateTime:
          serializer.fromJson<DateTime?>(json['localCompletedDateTime']),
      localEstimatedDateTime:
          serializer.fromJson<DateTime?>(json['localEstimatedDateTime']),
      localAccountNumber:
          serializer.fromJson<String?>(json['localAccountNumber']),
      localSwiftCode: serializer.fromJson<String?>(json['localSwiftCode']),
      localBankName: serializer.fromJson<String?>(json['localBankName']),
      localTargetAmount:
          serializer.fromJson<double?>(json['localTargetAmount']),
      localTargetAmountCurrency:
          serializer.fromJson<String?>(json['localTargetAmountCurrency']),
      localTransferSource:
          serializer.fromJson<String?>(json['localTransferSource']),
      localTransferApplicationId:
          serializer.fromJson<String?>(json['localTransferApplicationId']),
      localRecipientName:
          serializer.fromJson<String?>(json['localRecipientName']),
      npssExpirationDate:
          serializer.fromJson<DateTime?>(json['npssExpirationDate']),
      npssNotes: serializer.fromJson<String?>(json['npssNotes']),
      npssRtpInitiationDate:
          serializer.fromJson<DateTime?>(json['npssRtpInitiationDate']),
      npssShowInPendingRtps:
          serializer.fromJson<bool?>(json['npssShowInPendingRtps']),
      npssShowRtpResendButton:
          serializer.fromJson<bool?>(json['npssShowRtpResendButton']),
      npssOriginalTransactionReference: serializer
          .fromJson<String?>(json['npssOriginalTransactionReference']),
      npssOriginalTransactionReason:
          serializer.fromJson<String?>(json['npssOriginalTransactionReason']),
      npssOriginalTransactionDeeplink:
          serializer.fromJson<String?>(json['npssOriginalTransactionDeeplink']),
      token: serializer.fromJson<String?>(json['token']),
      accNumberLast4Digits:
          serializer.fromJson<String?>(json['accNumberLast4Digits']),
      cardMaskedPan: serializer.fromJson<String?>(json['cardMaskedPan']),
      merchantCountryCode:
          serializer.fromJson<String?>(json['merchantCountryCode']),
      originalCardTransactionCurrency:
          serializer.fromJson<String?>(json['originalCardTransactionCurrency']),
      originalCardTransactionAmount:
          serializer.fromJson<double?>(json['originalCardTransactionAmount']),
      earnedCashback: serializer.fromJson<double?>(json['earnedCashback']),
      cashBackPercentage:
          serializer.fromJson<double?>(json['cashBackPercentage']),
      cashBackTransactionCurrency:
          serializer.fromJson<String?>(json['cashBackTransactionCurrency']),
      dividendTaxAmountValue:
          serializer.fromJson<double?>(json['dividendTaxAmountValue']),
      dividendTaxAmountCurrency:
          serializer.fromJson<String?>(json['dividendTaxAmountCurrency']),
      dividendAmountValue:
          serializer.fromJson<double?>(json['dividendAmountValue']),
      dividendAmountCurrency:
          serializer.fromJson<String?>(json['dividendAmountCurrency']),
      netDividendAmountValue:
          serializer.fromJson<double?>(json['netDividendAmountValue']),
      netDividendAmountCurrency:
          serializer.fromJson<String?>(json['netDividendAmountCurrency']),
      amountPerInstrumentValue:
          serializer.fromJson<double?>(json['amountPerInstrumentValue']),
      amountPerInstrumentCurrency:
          serializer.fromJson<String?>(json['amountPerInstrumentCurrency']),
      instrumentQuantity:
          serializer.fromJson<double?>(json['instrumentQuantity']),
      brokerDividendTaxType:
          serializer.fromJson<String?>(json['brokerDividendTaxType']),
      dividendTaxRate: serializer.fromJson<double?>(json['dividendTaxRate']),
      securityLendingRebateAmountValue: serializer
          .fromJson<double?>(json['securityLendingRebateAmountValue']),
      securityLendingRebateAmountCurrency: serializer
          .fromJson<String?>(json['securityLendingRebateAmountCurrency']),
      securityLendingRebateTaxAmountCurrency: serializer
          .fromJson<String?>(json['securityLendingRebateTaxAmountCurrency']),
      securityLendingRebateTaxAmountValue: serializer
          .fromJson<double?>(json['securityLendingRebateTaxAmountValue']),
      securityLendingTransferredAmountCurrency: serializer
          .fromJson<String?>(json['securityLendingTransferredAmountCurrency']),
      securityLendingTransferredAmountValue: serializer
          .fromJson<double?>(json['securityLendingTransferredAmountValue']),
      securityLendingMonth:
          serializer.fromJson<int?>(json['securityLendingMonth']),
      wealthManagementManagedPortfolioId: serializer
          .fromJson<String?>(json['wealthManagementManagedPortfolioId']),
      wealthManagementProductName:
          serializer.fromJson<String?>(json['wealthManagementProductName']),
      wealthManagementProductCategory:
          serializer.fromJson<String?>(json['wealthManagementProductCategory']),
      wealthManagementAccountName:
          serializer.fromJson<String?>(json['wealthManagementAccountName']),
      wealthManagementEstimatedExecutionDeadline: serializer.fromJson<String?>(
          json['wealthManagementEstimatedExecutionDeadline']),
      wealthManagementTrackerSteps:
          serializer.fromJson<String?>(json['wealthManagementTrackerSteps']),
      acquirerInstrumentName:
          serializer.fromJson<String?>(json['acquirerInstrumentName']),
      acquirerInstrumentSymbol:
          serializer.fromJson<String?>(json['acquirerInstrumentSymbol']),
      acquirerInstrumentExchangeId:
          serializer.fromJson<String?>(json['acquirerInstrumentExchangeId']),
      acquirerInstrumentImageUrl:
          serializer.fromJson<String?>(json['acquirerInstrumentImageUrl']),
      acquireeInstrumentName:
          serializer.fromJson<String?>(json['acquireeInstrumentName']),
      acquireeInstrumentSymbol:
          serializer.fromJson<String?>(json['acquireeInstrumentSymbol']),
      acquireeInstrumentExchangeId:
          serializer.fromJson<String?>(json['acquireeInstrumentExchangeId']),
      acquireeInstrumentImageUrl:
          serializer.fromJson<String?>(json['acquireeInstrumentImageUrl']),
      brokerAcquisitionAccountAmountValue: serializer
          .fromJson<double?>(json['brokerAcquisitionAccountAmountValue']),
      brokerAcquisitionAccountAmountCurrency: serializer
          .fromJson<String?>(json['brokerAcquisitionAccountAmountCurrency']),
      brokerAcquisitionPositionDelta:
          serializer.fromJson<double?>(json['brokerAcquisitionPositionDelta']),
      ipoCompanyName: serializer.fromJson<String?>(json['ipoCompanyName']),
      ipoSubscriptionNumber:
          serializer.fromJson<String?>(json['ipoSubscriptionNumber']),
      ipoLeverageValue: serializer.fromJson<double?>(json['ipoLeverageValue']),
      ipoLeverageCurrency:
          serializer.fromJson<String?>(json['ipoLeverageCurrency']),
      ipoAmountValue: serializer.fromJson<double?>(json['ipoAmountValue']),
      ipoAmountCurrency:
          serializer.fromJson<String?>(json['ipoAmountCurrency']),
      ipoTotalAmountValue:
          serializer.fromJson<double?>(json['ipoTotalAmountValue']),
      ipoTotalAmountCurrency:
          serializer.fromJson<String?>(json['ipoTotalAmountCurrency']),
      ipoCompanyLogo: serializer.fromJson<String?>(json['ipoCompanyLogo']),
      ipoEnhancementValue:
          serializer.fromJson<double?>(json['ipoEnhancementValue']),
      ipoEnhancementCurrency:
          serializer.fromJson<String?>(json['ipoEnhancementCurrency']),
      ipoRefundAmountValue:
          serializer.fromJson<double?>(json['ipoRefundAmountValue']),
      ipoRefundAmountCurrency:
          serializer.fromJson<String?>(json['ipoRefundAmountCurrency']),
      ipoLeveragePaidValue:
          serializer.fromJson<double?>(json['ipoLeveragePaidValue']),
      ipoLeveragePaidCurrency:
          serializer.fromJson<String?>(json['ipoLeveragePaidCurrency']),
      ipoLeverageFeePercentage:
          serializer.fromJson<double?>(json['ipoLeverageFeePercentage']),
      ipoLeverageFeeValue:
          serializer.fromJson<double?>(json['ipoLeverageFeeValue']),
      ipoLeverageFeeCurrency:
          serializer.fromJson<String?>(json['ipoLeverageFeeCurrency']),
      ipoAllocationFeePercentage:
          serializer.fromJson<double?>(json['ipoAllocationFeePercentage']),
      ipoAllotedFeeMoney:
          serializer.fromJson<double?>(json['ipoAllotedFeeMoney']),
      ipoAllotedFeeMoneyCurrency:
          serializer.fromJson<String?>(json['ipoAllotedFeeMoneyCurrency']),
      ipoAllocatedMoney:
          serializer.fromJson<double?>(json['ipoAllocatedMoney']),
      ipoAllocatedMoneyCurrency:
          serializer.fromJson<String?>(json['ipoAllocatedMoneyCurrency']),
      dynamicDetails: serializer.fromJson<String>(json['dynamicDetails']),
      linkedTransactionFeeDetails:
          serializer.fromJson<String>(json['linkedTransactionFeeDetails']),
      cashbackCalculations:
          serializer.fromJson<String?>(json['cashbackCalculations']),
      carbonEmissionInGrams:
          serializer.fromJson<double?>(json['carbonEmissionInGrams']),
      carbonEmissionInOunces:
          serializer.fromJson<double?>(json['carbonEmissionInOunces']),
      recurringTransferRuleId:
          serializer.fromJson<String?>(json['recurringTransferRuleId']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'productType': serializer.toJson<String>(productType),
      'accountId': serializer.toJson<String>(accountId),
      'transactionAccountType':
          serializer.toJson<String?>(transactionAccountType),
      'transactionType': serializer.toJson<String>(transactionType),
      'transactionIdentifier': serializer.toJson<String>(transactionIdentifier),
      'status': serializer.toJson<String>(status),
      'referenceNumber': serializer.toJson<String?>(referenceNumber),
      'transactionMode': serializer.toJson<String>(transactionMode),
      'transactionSubType': serializer.toJson<String?>(transactionSubType),
      'internalTransactionStatus':
          serializer.toJson<String?>(internalTransactionStatus),
      'transactionDateTime': serializer.toJson<DateTime?>(transactionDateTime),
      'debtorName': serializer.toJson<String?>(debtorName),
      'customerId': serializer.toJson<String?>(customerId),
      'coreBankingIdentifier':
          serializer.toJson<String?>(coreBankingIdentifier),
      'creditorName': serializer.toJson<String?>(creditorName),
      'description': serializer.toJson<String?>(description),
      'subDescription': serializer.toJson<String?>(subDescription),
      'logoType': serializer.toJson<String?>(logoType),
      'merchantName': serializer.toJson<String?>(merchantName),
      'transactionImageKey': serializer.toJson<String?>(transactionImageKey),
      'amountValue': serializer.toJson<double>(amountValue),
      'localAmount': serializer.toJson<double?>(localAmount),
      'exchangeRate': serializer.toJson<double?>(exchangeRate),
      'availableBalance': serializer.toJson<double?>(availableBalance),
      'amountCurrency': serializer.toJson<String>(amountCurrency),
      'executionDate': serializer.toJson<DateTime?>(executionDate),
      'transactionCategory': serializer.toJson<String>(transactionCategory),
      'tppExternalReferenceId':
          serializer.toJson<String?>(tppExternalReferenceId),
      'fxFromAmount': serializer.toJson<double?>(fxFromAmount),
      'fxToAmount': serializer.toJson<double?>(fxToAmount),
      'fxFromAmountCurrency': serializer.toJson<String?>(fxFromAmountCurrency),
      'fxToAmountCurrency': serializer.toJson<String?>(fxToAmountCurrency),
      'fxFormattedExchangeRate':
          serializer.toJson<String?>(fxFormattedExchangeRate),
      'orderId': serializer.toJson<String?>(orderId),
      'portfolioId': serializer.toJson<String?>(portfolioId),
      'instrumentId': serializer.toJson<String?>(instrumentId),
      'createdDateTime': serializer.toJson<DateTime?>(createdDateTime),
      'orderSide': serializer.toJson<String?>(orderSide),
      'instrumentName': serializer.toJson<String?>(instrumentName),
      'instrumentSymbol': serializer.toJson<String?>(instrumentSymbol),
      'instrumentExchangeId': serializer.toJson<String?>(instrumentExchangeId),
      'instrumentImageUrl': serializer.toJson<String?>(instrumentImageUrl),
      'orderType': serializer.toJson<String?>(orderType),
      'errorCode': serializer.toJson<int?>(errorCode),
      'errorMessage': serializer.toJson<String?>(errorMessage),
      'executedQuantity': serializer.toJson<double?>(executedQuantity),
      'estimatedQuantity': serializer.toJson<double?>(estimatedQuantity),
      'executedAmountValue': serializer.toJson<double?>(executedAmountValue),
      'executedAmountCurrency':
          serializer.toJson<String?>(executedAmountCurrency),
      'executedNetAmountValue':
          serializer.toJson<double?>(executedNetAmountValue),
      'executedNetAmountCurrency':
          serializer.toJson<String?>(executedNetAmountCurrency),
      'estimatedAmountValue': serializer.toJson<double?>(estimatedAmountValue),
      'estimatedAmountCurrency':
          serializer.toJson<String?>(estimatedAmountCurrency),
      'estimatedNetAmountValue':
          serializer.toJson<double?>(estimatedNetAmountValue),
      'estimatedNetAmountCurrency':
          serializer.toJson<String?>(estimatedNetAmountCurrency),
      'commissionAmountValue':
          serializer.toJson<double?>(commissionAmountValue),
      'commissionAmountCurrency':
          serializer.toJson<String?>(commissionAmountCurrency),
      'estimatedCommissionAmountValue':
          serializer.toJson<double?>(estimatedCommissionAmountValue),
      'estimatedCommissionAmountCurrency':
          serializer.toJson<String?>(estimatedCommissionAmountCurrency),
      'vatValue': serializer.toJson<double?>(vatValue),
      'vatCurrency': serializer.toJson<String?>(vatCurrency),
      'estimatedVatValue': serializer.toJson<double?>(estimatedVatValue),
      'estimatedVatCurrency': serializer.toJson<String?>(estimatedVatCurrency),
      'averagePriceValue': serializer.toJson<double?>(averagePriceValue),
      'averagePriceCurrency': serializer.toJson<String?>(averagePriceCurrency),
      'estimatedPriceValue': serializer.toJson<double?>(estimatedPriceValue),
      'estimatedPriceCurrency':
          serializer.toJson<String?>(estimatedPriceCurrency),
      'executedTotalCommissionAmount':
          serializer.toJson<double?>(executedTotalCommissionAmount),
      'executedTotalCommissionCurrency':
          serializer.toJson<String?>(executedTotalCommissionCurrency),
      'estimatedTotalCommissionAmount':
          serializer.toJson<double?>(estimatedTotalCommissionAmount),
      'estimatedTotalCommissionCurrency':
          serializer.toJson<String?>(estimatedTotalCommissionCurrency),
      'commissionCalculationBasisPoints':
          serializer.toJson<double?>(commissionCalculationBasisPoints),
      'commissionCalculationBaseAmount':
          serializer.toJson<double?>(commissionCalculationBaseAmount),
      'commissionCalculationMinAmount':
          serializer.toJson<double?>(commissionCalculationMinAmount),
      'commissionCalculationBaseAmountCurrency':
          serializer.toJson<String?>(commissionCalculationBaseAmountCurrency),
      'commissionCalculationMinAmountCurrency':
          serializer.toJson<String?>(commissionCalculationMinAmountCurrency),
      'instrumentExchangeCode':
          serializer.toJson<String?>(instrumentExchangeCode),
      'recurringOrderTemplateId':
          serializer.toJson<String?>(recurringOrderTemplateId),
      'recurringOrderAccountName':
          serializer.toJson<String?>(recurringOrderAccountName),
      'recurringOrderFrequencyType':
          serializer.toJson<String?>(recurringOrderFrequencyType),
      'recurringOrderFrequencyDayOfWeek':
          serializer.toJson<String?>(recurringOrderFrequencyDayOfWeek),
      'recurringOrderFrequencyDayOfMonth':
          serializer.toJson<int?>(recurringOrderFrequencyDayOfMonth),
      'expirationDate': serializer.toJson<DateTime?>(expirationDate),
      'isPaymentProofReady': serializer.toJson<bool>(isPaymentProofReady),
      'isPaymentTrackingAvailable':
          serializer.toJson<bool?>(isPaymentTrackingAvailable),
      'paymentFeeTotalAmount':
          serializer.toJson<double?>(paymentFeeTotalAmount),
      'paymentFeeTotalCurrency':
          serializer.toJson<String?>(paymentFeeTotalCurrency),
      'paymentTargetCountryCode':
          serializer.toJson<String?>(paymentTargetCountryCode),
      'paymentPhoneNumber': serializer.toJson<String?>(paymentPhoneNumber),
      'paymentWarningMessage':
          serializer.toJson<String?>(paymentWarningMessage),
      'paymentFeeTypeDescriptionWio':
          serializer.toJson<String?>(paymentFeeTypeDescriptionWio),
      'paymentFeeTypeAmountWio':
          serializer.toJson<double?>(paymentFeeTypeAmountWio),
      'paymentFeeTypeCurrencyWio':
          serializer.toJson<String?>(paymentFeeTypeCurrencyWio),
      'paymentFeeTypeDescriptionWioCorrespondentBank': serializer
          .toJson<String?>(paymentFeeTypeDescriptionWioCorrespondentBank),
      'paymentFeeTypeAmountWioCorrespondentBank':
          serializer.toJson<double?>(paymentFeeTypeAmountWioCorrespondentBank),
      'paymentFeeTypeCurrencyWioCorrespondentBank': serializer
          .toJson<String?>(paymentFeeTypeCurrencyWioCorrespondentBank),
      'paymentFeeTypeDescriptionWise':
          serializer.toJson<String?>(paymentFeeTypeDescriptionWise),
      'paymentFeeTypeAmountWise':
          serializer.toJson<double?>(paymentFeeTypeAmountWise),
      'paymentFeeTypeCurrencyWise':
          serializer.toJson<String?>(paymentFeeTypeCurrencyWise),
      'internationalTargetCountry':
          serializer.toJson<String?>(internationalTargetCountry),
      'internationalCompletedDateTime':
          serializer.toJson<DateTime?>(internationalCompletedDateTime),
      'internationalEstimatedDateTime':
          serializer.toJson<String?>(internationalEstimatedDateTime),
      'internationalFeeChargingType':
          serializer.toJson<String?>(internationalFeeChargingType),
      'internationalSwiftCode':
          serializer.toJson<String?>(internationalSwiftCode),
      'internationalAccountNumber':
          serializer.toJson<String?>(internationalAccountNumber),
      'internationalPurposeDescription':
          serializer.toJson<String?>(internationalPurposeDescription),
      'internationalPurposeCode':
          serializer.toJson<String?>(internationalPurposeCode),
      'internationalNotes': serializer.toJson<String?>(internationalNotes),
      'internationalBankName':
          serializer.toJson<String?>(internationalBankName),
      'internationalTargetAmount':
          serializer.toJson<double?>(internationalTargetAmount),
      'internationalTargetAmountCurrency':
          serializer.toJson<String?>(internationalTargetAmountCurrency),
      'internationalTransferFee':
          serializer.toJson<double?>(internationalTransferFee),
      'internationalTransferFeeCurrency':
          serializer.toJson<String?>(internationalTransferFeeCurrency),
      'fabExpirationDate': serializer.toJson<DateTime?>(fabExpirationDate),
      'fabBankName': serializer.toJson<String?>(fabBankName),
      'fabTotalFeeAmount': serializer.toJson<double?>(fabTotalFeeAmount),
      'fabTotalFeeAmountCurrency':
          serializer.toJson<String?>(fabTotalFeeAmountCurrency),
      'fabUpdatedDate': serializer.toJson<DateTime?>(fabUpdatedDate),
      'fabCancellationReason':
          serializer.toJson<String?>(fabCancellationReason),
      'fabChequeNumber': serializer.toJson<String?>(fabChequeNumber),
      'fabChequeDate': serializer.toJson<DateTime?>(fabChequeDate),
      'fabFrontImageUrl': serializer.toJson<String?>(fabFrontImageUrl),
      'localPurposeDescription':
          serializer.toJson<String?>(localPurposeDescription),
      'localPurposeCode': serializer.toJson<String?>(localPurposeCode),
      'localNote': serializer.toJson<String?>(localNote),
      'localTargetCountry': serializer.toJson<String?>(localTargetCountry),
      'localCompletedDateTime':
          serializer.toJson<DateTime?>(localCompletedDateTime),
      'localEstimatedDateTime':
          serializer.toJson<DateTime?>(localEstimatedDateTime),
      'localAccountNumber': serializer.toJson<String?>(localAccountNumber),
      'localSwiftCode': serializer.toJson<String?>(localSwiftCode),
      'localBankName': serializer.toJson<String?>(localBankName),
      'localTargetAmount': serializer.toJson<double?>(localTargetAmount),
      'localTargetAmountCurrency':
          serializer.toJson<String?>(localTargetAmountCurrency),
      'localTransferSource': serializer.toJson<String?>(localTransferSource),
      'localTransferApplicationId':
          serializer.toJson<String?>(localTransferApplicationId),
      'localRecipientName': serializer.toJson<String?>(localRecipientName),
      'npssExpirationDate': serializer.toJson<DateTime?>(npssExpirationDate),
      'npssNotes': serializer.toJson<String?>(npssNotes),
      'npssRtpInitiationDate':
          serializer.toJson<DateTime?>(npssRtpInitiationDate),
      'npssShowInPendingRtps': serializer.toJson<bool?>(npssShowInPendingRtps),
      'npssShowRtpResendButton':
          serializer.toJson<bool?>(npssShowRtpResendButton),
      'npssOriginalTransactionReference':
          serializer.toJson<String?>(npssOriginalTransactionReference),
      'npssOriginalTransactionReason':
          serializer.toJson<String?>(npssOriginalTransactionReason),
      'npssOriginalTransactionDeeplink':
          serializer.toJson<String?>(npssOriginalTransactionDeeplink),
      'token': serializer.toJson<String?>(token),
      'accNumberLast4Digits': serializer.toJson<String?>(accNumberLast4Digits),
      'cardMaskedPan': serializer.toJson<String?>(cardMaskedPan),
      'merchantCountryCode': serializer.toJson<String?>(merchantCountryCode),
      'originalCardTransactionCurrency':
          serializer.toJson<String?>(originalCardTransactionCurrency),
      'originalCardTransactionAmount':
          serializer.toJson<double?>(originalCardTransactionAmount),
      'earnedCashback': serializer.toJson<double?>(earnedCashback),
      'cashBackPercentage': serializer.toJson<double?>(cashBackPercentage),
      'cashBackTransactionCurrency':
          serializer.toJson<String?>(cashBackTransactionCurrency),
      'dividendTaxAmountValue':
          serializer.toJson<double?>(dividendTaxAmountValue),
      'dividendTaxAmountCurrency':
          serializer.toJson<String?>(dividendTaxAmountCurrency),
      'dividendAmountValue': serializer.toJson<double?>(dividendAmountValue),
      'dividendAmountCurrency':
          serializer.toJson<String?>(dividendAmountCurrency),
      'netDividendAmountValue':
          serializer.toJson<double?>(netDividendAmountValue),
      'netDividendAmountCurrency':
          serializer.toJson<String?>(netDividendAmountCurrency),
      'amountPerInstrumentValue':
          serializer.toJson<double?>(amountPerInstrumentValue),
      'amountPerInstrumentCurrency':
          serializer.toJson<String?>(amountPerInstrumentCurrency),
      'instrumentQuantity': serializer.toJson<double?>(instrumentQuantity),
      'brokerDividendTaxType':
          serializer.toJson<String?>(brokerDividendTaxType),
      'dividendTaxRate': serializer.toJson<double?>(dividendTaxRate),
      'securityLendingRebateAmountValue':
          serializer.toJson<double?>(securityLendingRebateAmountValue),
      'securityLendingRebateAmountCurrency':
          serializer.toJson<String?>(securityLendingRebateAmountCurrency),
      'securityLendingRebateTaxAmountCurrency':
          serializer.toJson<String?>(securityLendingRebateTaxAmountCurrency),
      'securityLendingRebateTaxAmountValue':
          serializer.toJson<double?>(securityLendingRebateTaxAmountValue),
      'securityLendingTransferredAmountCurrency':
          serializer.toJson<String?>(securityLendingTransferredAmountCurrency),
      'securityLendingTransferredAmountValue':
          serializer.toJson<double?>(securityLendingTransferredAmountValue),
      'securityLendingMonth': serializer.toJson<int?>(securityLendingMonth),
      'wealthManagementManagedPortfolioId':
          serializer.toJson<String?>(wealthManagementManagedPortfolioId),
      'wealthManagementProductName':
          serializer.toJson<String?>(wealthManagementProductName),
      'wealthManagementProductCategory':
          serializer.toJson<String?>(wealthManagementProductCategory),
      'wealthManagementAccountName':
          serializer.toJson<String?>(wealthManagementAccountName),
      'wealthManagementEstimatedExecutionDeadline': serializer
          .toJson<String?>(wealthManagementEstimatedExecutionDeadline),
      'wealthManagementTrackerSteps':
          serializer.toJson<String?>(wealthManagementTrackerSteps),
      'acquirerInstrumentName':
          serializer.toJson<String?>(acquirerInstrumentName),
      'acquirerInstrumentSymbol':
          serializer.toJson<String?>(acquirerInstrumentSymbol),
      'acquirerInstrumentExchangeId':
          serializer.toJson<String?>(acquirerInstrumentExchangeId),
      'acquirerInstrumentImageUrl':
          serializer.toJson<String?>(acquirerInstrumentImageUrl),
      'acquireeInstrumentName':
          serializer.toJson<String?>(acquireeInstrumentName),
      'acquireeInstrumentSymbol':
          serializer.toJson<String?>(acquireeInstrumentSymbol),
      'acquireeInstrumentExchangeId':
          serializer.toJson<String?>(acquireeInstrumentExchangeId),
      'acquireeInstrumentImageUrl':
          serializer.toJson<String?>(acquireeInstrumentImageUrl),
      'brokerAcquisitionAccountAmountValue':
          serializer.toJson<double?>(brokerAcquisitionAccountAmountValue),
      'brokerAcquisitionAccountAmountCurrency':
          serializer.toJson<String?>(brokerAcquisitionAccountAmountCurrency),
      'brokerAcquisitionPositionDelta':
          serializer.toJson<double?>(brokerAcquisitionPositionDelta),
      'ipoCompanyName': serializer.toJson<String?>(ipoCompanyName),
      'ipoSubscriptionNumber':
          serializer.toJson<String?>(ipoSubscriptionNumber),
      'ipoLeverageValue': serializer.toJson<double?>(ipoLeverageValue),
      'ipoLeverageCurrency': serializer.toJson<String?>(ipoLeverageCurrency),
      'ipoAmountValue': serializer.toJson<double?>(ipoAmountValue),
      'ipoAmountCurrency': serializer.toJson<String?>(ipoAmountCurrency),
      'ipoTotalAmountValue': serializer.toJson<double?>(ipoTotalAmountValue),
      'ipoTotalAmountCurrency':
          serializer.toJson<String?>(ipoTotalAmountCurrency),
      'ipoCompanyLogo': serializer.toJson<String?>(ipoCompanyLogo),
      'ipoEnhancementValue': serializer.toJson<double?>(ipoEnhancementValue),
      'ipoEnhancementCurrency':
          serializer.toJson<String?>(ipoEnhancementCurrency),
      'ipoRefundAmountValue': serializer.toJson<double?>(ipoRefundAmountValue),
      'ipoRefundAmountCurrency':
          serializer.toJson<String?>(ipoRefundAmountCurrency),
      'ipoLeveragePaidValue': serializer.toJson<double?>(ipoLeveragePaidValue),
      'ipoLeveragePaidCurrency':
          serializer.toJson<String?>(ipoLeveragePaidCurrency),
      'ipoLeverageFeePercentage':
          serializer.toJson<double?>(ipoLeverageFeePercentage),
      'ipoLeverageFeeValue': serializer.toJson<double?>(ipoLeverageFeeValue),
      'ipoLeverageFeeCurrency':
          serializer.toJson<String?>(ipoLeverageFeeCurrency),
      'ipoAllocationFeePercentage':
          serializer.toJson<double?>(ipoAllocationFeePercentage),
      'ipoAllotedFeeMoney': serializer.toJson<double?>(ipoAllotedFeeMoney),
      'ipoAllotedFeeMoneyCurrency':
          serializer.toJson<String?>(ipoAllotedFeeMoneyCurrency),
      'ipoAllocatedMoney': serializer.toJson<double?>(ipoAllocatedMoney),
      'ipoAllocatedMoneyCurrency':
          serializer.toJson<String?>(ipoAllocatedMoneyCurrency),
      'dynamicDetails': serializer.toJson<String>(dynamicDetails),
      'linkedTransactionFeeDetails':
          serializer.toJson<String>(linkedTransactionFeeDetails),
      'cashbackCalculations': serializer.toJson<String?>(cashbackCalculations),
      'carbonEmissionInGrams':
          serializer.toJson<double?>(carbonEmissionInGrams),
      'carbonEmissionInOunces':
          serializer.toJson<double?>(carbonEmissionInOunces),
      'recurringTransferRuleId':
          serializer.toJson<String?>(recurringTransferRuleId),
    };
  }

  TransactionsData copyWith(
          {String? id,
          String? productType,
          String? accountId,
          Value<String?> transactionAccountType = const Value.absent(),
          String? transactionType,
          String? transactionIdentifier,
          String? status,
          Value<String?> referenceNumber = const Value.absent(),
          String? transactionMode,
          Value<String?> transactionSubType = const Value.absent(),
          Value<String?> internalTransactionStatus = const Value.absent(),
          Value<DateTime?> transactionDateTime = const Value.absent(),
          Value<String?> debtorName = const Value.absent(),
          Value<String?> customerId = const Value.absent(),
          Value<String?> coreBankingIdentifier = const Value.absent(),
          Value<String?> creditorName = const Value.absent(),
          Value<String?> description = const Value.absent(),
          Value<String?> subDescription = const Value.absent(),
          Value<String?> logoType = const Value.absent(),
          Value<String?> merchantName = const Value.absent(),
          Value<String?> transactionImageKey = const Value.absent(),
          double? amountValue,
          Value<double?> localAmount = const Value.absent(),
          Value<double?> exchangeRate = const Value.absent(),
          Value<double?> availableBalance = const Value.absent(),
          String? amountCurrency,
          Value<DateTime?> executionDate = const Value.absent(),
          String? transactionCategory,
          Value<String?> tppExternalReferenceId = const Value.absent(),
          Value<double?> fxFromAmount = const Value.absent(),
          Value<double?> fxToAmount = const Value.absent(),
          Value<String?> fxFromAmountCurrency = const Value.absent(),
          Value<String?> fxToAmountCurrency = const Value.absent(),
          Value<String?> fxFormattedExchangeRate = const Value.absent(),
          Value<String?> orderId = const Value.absent(),
          Value<String?> portfolioId = const Value.absent(),
          Value<String?> instrumentId = const Value.absent(),
          Value<DateTime?> createdDateTime = const Value.absent(),
          Value<String?> orderSide = const Value.absent(),
          Value<String?> instrumentName = const Value.absent(),
          Value<String?> instrumentSymbol = const Value.absent(),
          Value<String?> instrumentExchangeId = const Value.absent(),
          Value<String?> instrumentImageUrl = const Value.absent(),
          Value<String?> orderType = const Value.absent(),
          Value<int?> errorCode = const Value.absent(),
          Value<String?> errorMessage = const Value.absent(),
          Value<double?> executedQuantity = const Value.absent(),
          Value<double?> estimatedQuantity = const Value.absent(),
          Value<double?> executedAmountValue = const Value.absent(),
          Value<String?> executedAmountCurrency = const Value.absent(),
          Value<double?> executedNetAmountValue = const Value.absent(),
          Value<String?> executedNetAmountCurrency = const Value.absent(),
          Value<double?> estimatedAmountValue = const Value.absent(),
          Value<String?> estimatedAmountCurrency = const Value.absent(),
          Value<double?> estimatedNetAmountValue = const Value.absent(),
          Value<String?> estimatedNetAmountCurrency = const Value.absent(),
          Value<double?> commissionAmountValue = const Value.absent(),
          Value<String?> commissionAmountCurrency = const Value.absent(),
          Value<double?> estimatedCommissionAmountValue = const Value.absent(),
          Value<String?> estimatedCommissionAmountCurrency =
              const Value.absent(),
          Value<double?> vatValue = const Value.absent(),
          Value<String?> vatCurrency = const Value.absent(),
          Value<double?> estimatedVatValue = const Value.absent(),
          Value<String?> estimatedVatCurrency = const Value.absent(),
          Value<double?> averagePriceValue = const Value.absent(),
          Value<String?> averagePriceCurrency = const Value.absent(),
          Value<double?> estimatedPriceValue = const Value.absent(),
          Value<String?> estimatedPriceCurrency = const Value.absent(),
          Value<double?> executedTotalCommissionAmount = const Value.absent(),
          Value<String?> executedTotalCommissionCurrency = const Value.absent(),
          Value<double?> estimatedTotalCommissionAmount = const Value.absent(),
          Value<String?> estimatedTotalCommissionCurrency =
              const Value.absent(),
          Value<double?> commissionCalculationBasisPoints =
              const Value.absent(),
          Value<double?> commissionCalculationBaseAmount = const Value.absent(),
          Value<double?> commissionCalculationMinAmount = const Value.absent(),
          Value<String?> commissionCalculationBaseAmountCurrency =
              const Value.absent(),
          Value<String?> commissionCalculationMinAmountCurrency =
              const Value.absent(),
          Value<String?> instrumentExchangeCode = const Value.absent(),
          Value<String?> recurringOrderTemplateId = const Value.absent(),
          Value<String?> recurringOrderAccountName = const Value.absent(),
          Value<String?> recurringOrderFrequencyType = const Value.absent(),
          Value<String?> recurringOrderFrequencyDayOfWeek =
              const Value.absent(),
          Value<int?> recurringOrderFrequencyDayOfMonth = const Value.absent(),
          Value<DateTime?> expirationDate = const Value.absent(),
          bool? isPaymentProofReady,
          Value<bool?> isPaymentTrackingAvailable = const Value.absent(),
          Value<double?> paymentFeeTotalAmount = const Value.absent(),
          Value<String?> paymentFeeTotalCurrency = const Value.absent(),
          Value<String?> paymentTargetCountryCode = const Value.absent(),
          Value<String?> paymentPhoneNumber = const Value.absent(),
          Value<String?> paymentWarningMessage = const Value.absent(),
          Value<String?> paymentFeeTypeDescriptionWio = const Value.absent(),
          Value<double?> paymentFeeTypeAmountWio = const Value.absent(),
          Value<String?> paymentFeeTypeCurrencyWio = const Value.absent(),
          Value<String?> paymentFeeTypeDescriptionWioCorrespondentBank =
              const Value.absent(),
          Value<double?> paymentFeeTypeAmountWioCorrespondentBank =
              const Value.absent(),
          Value<String?> paymentFeeTypeCurrencyWioCorrespondentBank =
              const Value.absent(),
          Value<String?> paymentFeeTypeDescriptionWise = const Value.absent(),
          Value<double?> paymentFeeTypeAmountWise = const Value.absent(),
          Value<String?> paymentFeeTypeCurrencyWise = const Value.absent(),
          Value<String?> internationalTargetCountry = const Value.absent(),
          Value<DateTime?> internationalCompletedDateTime =
              const Value.absent(),
          Value<String?> internationalEstimatedDateTime = const Value.absent(),
          Value<String?> internationalFeeChargingType = const Value.absent(),
          Value<String?> internationalSwiftCode = const Value.absent(),
          Value<String?> internationalAccountNumber = const Value.absent(),
          Value<String?> internationalPurposeDescription = const Value.absent(),
          Value<String?> internationalPurposeCode = const Value.absent(),
          Value<String?> internationalNotes = const Value.absent(),
          Value<String?> internationalBankName = const Value.absent(),
          Value<double?> internationalTargetAmount = const Value.absent(),
          Value<String?> internationalTargetAmountCurrency =
              const Value.absent(),
          Value<double?> internationalTransferFee = const Value.absent(),
          Value<String?> internationalTransferFeeCurrency =
              const Value.absent(),
          Value<DateTime?> fabExpirationDate = const Value.absent(),
          Value<String?> fabBankName = const Value.absent(),
          Value<double?> fabTotalFeeAmount = const Value.absent(),
          Value<String?> fabTotalFeeAmountCurrency = const Value.absent(),
          Value<DateTime?> fabUpdatedDate = const Value.absent(),
          Value<String?> fabCancellationReason = const Value.absent(),
          Value<String?> fabChequeNumber = const Value.absent(),
          Value<DateTime?> fabChequeDate = const Value.absent(),
          Value<String?> fabFrontImageUrl = const Value.absent(),
          Value<String?> localPurposeDescription = const Value.absent(),
          Value<String?> localPurposeCode = const Value.absent(),
          Value<String?> localNote = const Value.absent(),
          Value<String?> localTargetCountry = const Value.absent(),
          Value<DateTime?> localCompletedDateTime = const Value.absent(),
          Value<DateTime?> localEstimatedDateTime = const Value.absent(),
          Value<String?> localAccountNumber = const Value.absent(),
          Value<String?> localSwiftCode = const Value.absent(),
          Value<String?> localBankName = const Value.absent(),
          Value<double?> localTargetAmount = const Value.absent(),
          Value<String?> localTargetAmountCurrency = const Value.absent(),
          Value<String?> localTransferSource = const Value.absent(),
          Value<String?> localTransferApplicationId = const Value.absent(),
          Value<String?> localRecipientName = const Value.absent(),
          Value<DateTime?> npssExpirationDate = const Value.absent(),
          Value<String?> npssNotes = const Value.absent(),
          Value<DateTime?> npssRtpInitiationDate = const Value.absent(),
          Value<bool?> npssShowInPendingRtps = const Value.absent(),
          Value<bool?> npssShowRtpResendButton = const Value.absent(),
          Value<String?> npssOriginalTransactionReference =
              const Value.absent(),
          Value<String?> npssOriginalTransactionReason = const Value.absent(),
          Value<String?> npssOriginalTransactionDeeplink = const Value.absent(),
          Value<String?> token = const Value.absent(),
          Value<String?> accNumberLast4Digits = const Value.absent(),
          Value<String?> cardMaskedPan = const Value.absent(),
          Value<String?> merchantCountryCode = const Value.absent(),
          Value<String?> originalCardTransactionCurrency = const Value.absent(),
          Value<double?> originalCardTransactionAmount = const Value.absent(),
          Value<double?> earnedCashback = const Value.absent(),
          Value<double?> cashBackPercentage = const Value.absent(),
          Value<String?> cashBackTransactionCurrency = const Value.absent(),
          Value<double?> dividendTaxAmountValue = const Value.absent(),
          Value<String?> dividendTaxAmountCurrency = const Value.absent(),
          Value<double?> dividendAmountValue = const Value.absent(),
          Value<String?> dividendAmountCurrency = const Value.absent(),
          Value<double?> netDividendAmountValue = const Value.absent(),
          Value<String?> netDividendAmountCurrency = const Value.absent(),
          Value<double?> amountPerInstrumentValue = const Value.absent(),
          Value<String?> amountPerInstrumentCurrency = const Value.absent(),
          Value<double?> instrumentQuantity = const Value.absent(),
          Value<String?> brokerDividendTaxType = const Value.absent(),
          Value<double?> dividendTaxRate = const Value.absent(),
          Value<double?> securityLendingRebateAmountValue =
              const Value.absent(),
          Value<String?> securityLendingRebateAmountCurrency =
              const Value.absent(),
          Value<String?> securityLendingRebateTaxAmountCurrency =
              const Value.absent(),
          Value<double?> securityLendingRebateTaxAmountValue =
              const Value.absent(),
          Value<String?> securityLendingTransferredAmountCurrency =
              const Value.absent(),
          Value<double?> securityLendingTransferredAmountValue =
              const Value.absent(),
          Value<int?> securityLendingMonth = const Value.absent(),
          Value<String?> wealthManagementManagedPortfolioId =
              const Value.absent(),
          Value<String?> wealthManagementProductName = const Value.absent(),
          Value<String?> wealthManagementProductCategory = const Value.absent(),
          Value<String?> wealthManagementAccountName = const Value.absent(),
          Value<String?> wealthManagementEstimatedExecutionDeadline =
              const Value.absent(),
          Value<String?> wealthManagementTrackerSteps = const Value.absent(),
          Value<String?> acquirerInstrumentName = const Value.absent(),
          Value<String?> acquirerInstrumentSymbol = const Value.absent(),
          Value<String?> acquirerInstrumentExchangeId = const Value.absent(),
          Value<String?> acquirerInstrumentImageUrl = const Value.absent(),
          Value<String?> acquireeInstrumentName = const Value.absent(),
          Value<String?> acquireeInstrumentSymbol = const Value.absent(),
          Value<String?> acquireeInstrumentExchangeId = const Value.absent(),
          Value<String?> acquireeInstrumentImageUrl = const Value.absent(),
          Value<double?> brokerAcquisitionAccountAmountValue =
              const Value.absent(),
          Value<String?> brokerAcquisitionAccountAmountCurrency =
              const Value.absent(),
          Value<double?> brokerAcquisitionPositionDelta = const Value.absent(),
          Value<String?> ipoCompanyName = const Value.absent(),
          Value<String?> ipoSubscriptionNumber = const Value.absent(),
          Value<double?> ipoLeverageValue = const Value.absent(),
          Value<String?> ipoLeverageCurrency = const Value.absent(),
          Value<double?> ipoAmountValue = const Value.absent(),
          Value<String?> ipoAmountCurrency = const Value.absent(),
          Value<double?> ipoTotalAmountValue = const Value.absent(),
          Value<String?> ipoTotalAmountCurrency = const Value.absent(),
          Value<String?> ipoCompanyLogo = const Value.absent(),
          Value<double?> ipoEnhancementValue = const Value.absent(),
          Value<String?> ipoEnhancementCurrency = const Value.absent(),
          Value<double?> ipoRefundAmountValue = const Value.absent(),
          Value<String?> ipoRefundAmountCurrency = const Value.absent(),
          Value<double?> ipoLeveragePaidValue = const Value.absent(),
          Value<String?> ipoLeveragePaidCurrency = const Value.absent(),
          Value<double?> ipoLeverageFeePercentage = const Value.absent(),
          Value<double?> ipoLeverageFeeValue = const Value.absent(),
          Value<String?> ipoLeverageFeeCurrency = const Value.absent(),
          Value<double?> ipoAllocationFeePercentage = const Value.absent(),
          Value<double?> ipoAllotedFeeMoney = const Value.absent(),
          Value<String?> ipoAllotedFeeMoneyCurrency = const Value.absent(),
          Value<double?> ipoAllocatedMoney = const Value.absent(),
          Value<String?> ipoAllocatedMoneyCurrency = const Value.absent(),
          String? dynamicDetails,
          String? linkedTransactionFeeDetails,
          Value<String?> cashbackCalculations = const Value.absent(),
          Value<double?> carbonEmissionInGrams = const Value.absent(),
          Value<double?> carbonEmissionInOunces = const Value.absent(),
          Value<String?> recurringTransferRuleId = const Value.absent()}) =>
      TransactionsData(
        id: id ?? this.id,
        productType: productType ?? this.productType,
        accountId: accountId ?? this.accountId,
        transactionAccountType: transactionAccountType.present
            ? transactionAccountType.value
            : this.transactionAccountType,
        transactionType: transactionType ?? this.transactionType,
        transactionIdentifier:
            transactionIdentifier ?? this.transactionIdentifier,
        status: status ?? this.status,
        referenceNumber: referenceNumber.present
            ? referenceNumber.value
            : this.referenceNumber,
        transactionMode: transactionMode ?? this.transactionMode,
        transactionSubType: transactionSubType.present
            ? transactionSubType.value
            : this.transactionSubType,
        internalTransactionStatus: internalTransactionStatus.present
            ? internalTransactionStatus.value
            : this.internalTransactionStatus,
        transactionDateTime: transactionDateTime.present
            ? transactionDateTime.value
            : this.transactionDateTime,
        debtorName: debtorName.present ? debtorName.value : this.debtorName,
        customerId: customerId.present ? customerId.value : this.customerId,
        coreBankingIdentifier: coreBankingIdentifier.present
            ? coreBankingIdentifier.value
            : this.coreBankingIdentifier,
        creditorName:
            creditorName.present ? creditorName.value : this.creditorName,
        description: description.present ? description.value : this.description,
        subDescription:
            subDescription.present ? subDescription.value : this.subDescription,
        logoType: logoType.present ? logoType.value : this.logoType,
        merchantName:
            merchantName.present ? merchantName.value : this.merchantName,
        transactionImageKey: transactionImageKey.present
            ? transactionImageKey.value
            : this.transactionImageKey,
        amountValue: amountValue ?? this.amountValue,
        localAmount: localAmount.present ? localAmount.value : this.localAmount,
        exchangeRate:
            exchangeRate.present ? exchangeRate.value : this.exchangeRate,
        availableBalance: availableBalance.present
            ? availableBalance.value
            : this.availableBalance,
        amountCurrency: amountCurrency ?? this.amountCurrency,
        executionDate:
            executionDate.present ? executionDate.value : this.executionDate,
        transactionCategory: transactionCategory ?? this.transactionCategory,
        tppExternalReferenceId: tppExternalReferenceId.present
            ? tppExternalReferenceId.value
            : this.tppExternalReferenceId,
        fxFromAmount:
            fxFromAmount.present ? fxFromAmount.value : this.fxFromAmount,
        fxToAmount: fxToAmount.present ? fxToAmount.value : this.fxToAmount,
        fxFromAmountCurrency: fxFromAmountCurrency.present
            ? fxFromAmountCurrency.value
            : this.fxFromAmountCurrency,
        fxToAmountCurrency: fxToAmountCurrency.present
            ? fxToAmountCurrency.value
            : this.fxToAmountCurrency,
        fxFormattedExchangeRate: fxFormattedExchangeRate.present
            ? fxFormattedExchangeRate.value
            : this.fxFormattedExchangeRate,
        orderId: orderId.present ? orderId.value : this.orderId,
        portfolioId: portfolioId.present ? portfolioId.value : this.portfolioId,
        instrumentId:
            instrumentId.present ? instrumentId.value : this.instrumentId,
        createdDateTime: createdDateTime.present
            ? createdDateTime.value
            : this.createdDateTime,
        orderSide: orderSide.present ? orderSide.value : this.orderSide,
        instrumentName:
            instrumentName.present ? instrumentName.value : this.instrumentName,
        instrumentSymbol: instrumentSymbol.present
            ? instrumentSymbol.value
            : this.instrumentSymbol,
        instrumentExchangeId: instrumentExchangeId.present
            ? instrumentExchangeId.value
            : this.instrumentExchangeId,
        instrumentImageUrl: instrumentImageUrl.present
            ? instrumentImageUrl.value
            : this.instrumentImageUrl,
        orderType: orderType.present ? orderType.value : this.orderType,
        errorCode: errorCode.present ? errorCode.value : this.errorCode,
        errorMessage:
            errorMessage.present ? errorMessage.value : this.errorMessage,
        executedQuantity: executedQuantity.present
            ? executedQuantity.value
            : this.executedQuantity,
        estimatedQuantity: estimatedQuantity.present
            ? estimatedQuantity.value
            : this.estimatedQuantity,
        executedAmountValue: executedAmountValue.present
            ? executedAmountValue.value
            : this.executedAmountValue,
        executedAmountCurrency: executedAmountCurrency.present
            ? executedAmountCurrency.value
            : this.executedAmountCurrency,
        executedNetAmountValue: executedNetAmountValue.present
            ? executedNetAmountValue.value
            : this.executedNetAmountValue,
        executedNetAmountCurrency: executedNetAmountCurrency.present
            ? executedNetAmountCurrency.value
            : this.executedNetAmountCurrency,
        estimatedAmountValue: estimatedAmountValue.present
            ? estimatedAmountValue.value
            : this.estimatedAmountValue,
        estimatedAmountCurrency: estimatedAmountCurrency.present
            ? estimatedAmountCurrency.value
            : this.estimatedAmountCurrency,
        estimatedNetAmountValue: estimatedNetAmountValue.present
            ? estimatedNetAmountValue.value
            : this.estimatedNetAmountValue,
        estimatedNetAmountCurrency: estimatedNetAmountCurrency.present
            ? estimatedNetAmountCurrency.value
            : this.estimatedNetAmountCurrency,
        commissionAmountValue: commissionAmountValue.present
            ? commissionAmountValue.value
            : this.commissionAmountValue,
        commissionAmountCurrency: commissionAmountCurrency.present
            ? commissionAmountCurrency.value
            : this.commissionAmountCurrency,
        estimatedCommissionAmountValue: estimatedCommissionAmountValue.present
            ? estimatedCommissionAmountValue.value
            : this.estimatedCommissionAmountValue,
        estimatedCommissionAmountCurrency:
            estimatedCommissionAmountCurrency.present
                ? estimatedCommissionAmountCurrency.value
                : this.estimatedCommissionAmountCurrency,
        vatValue: vatValue.present ? vatValue.value : this.vatValue,
        vatCurrency: vatCurrency.present ? vatCurrency.value : this.vatCurrency,
        estimatedVatValue: estimatedVatValue.present
            ? estimatedVatValue.value
            : this.estimatedVatValue,
        estimatedVatCurrency: estimatedVatCurrency.present
            ? estimatedVatCurrency.value
            : this.estimatedVatCurrency,
        averagePriceValue: averagePriceValue.present
            ? averagePriceValue.value
            : this.averagePriceValue,
        averagePriceCurrency: averagePriceCurrency.present
            ? averagePriceCurrency.value
            : this.averagePriceCurrency,
        estimatedPriceValue: estimatedPriceValue.present
            ? estimatedPriceValue.value
            : this.estimatedPriceValue,
        estimatedPriceCurrency: estimatedPriceCurrency.present
            ? estimatedPriceCurrency.value
            : this.estimatedPriceCurrency,
        executedTotalCommissionAmount: executedTotalCommissionAmount.present
            ? executedTotalCommissionAmount.value
            : this.executedTotalCommissionAmount,
        executedTotalCommissionCurrency: executedTotalCommissionCurrency.present
            ? executedTotalCommissionCurrency.value
            : this.executedTotalCommissionCurrency,
        estimatedTotalCommissionAmount: estimatedTotalCommissionAmount.present
            ? estimatedTotalCommissionAmount.value
            : this.estimatedTotalCommissionAmount,
        estimatedTotalCommissionCurrency:
            estimatedTotalCommissionCurrency.present
                ? estimatedTotalCommissionCurrency.value
                : this.estimatedTotalCommissionCurrency,
        commissionCalculationBasisPoints:
            commissionCalculationBasisPoints.present
                ? commissionCalculationBasisPoints.value
                : this.commissionCalculationBasisPoints,
        commissionCalculationBaseAmount: commissionCalculationBaseAmount.present
            ? commissionCalculationBaseAmount.value
            : this.commissionCalculationBaseAmount,
        commissionCalculationMinAmount: commissionCalculationMinAmount.present
            ? commissionCalculationMinAmount.value
            : this.commissionCalculationMinAmount,
        commissionCalculationBaseAmountCurrency:
            commissionCalculationBaseAmountCurrency.present
                ? commissionCalculationBaseAmountCurrency.value
                : this.commissionCalculationBaseAmountCurrency,
        commissionCalculationMinAmountCurrency:
            commissionCalculationMinAmountCurrency.present
                ? commissionCalculationMinAmountCurrency.value
                : this.commissionCalculationMinAmountCurrency,
        instrumentExchangeCode: instrumentExchangeCode.present
            ? instrumentExchangeCode.value
            : this.instrumentExchangeCode,
        recurringOrderTemplateId: recurringOrderTemplateId.present
            ? recurringOrderTemplateId.value
            : this.recurringOrderTemplateId,
        recurringOrderAccountName: recurringOrderAccountName.present
            ? recurringOrderAccountName.value
            : this.recurringOrderAccountName,
        recurringOrderFrequencyType: recurringOrderFrequencyType.present
            ? recurringOrderFrequencyType.value
            : this.recurringOrderFrequencyType,
        recurringOrderFrequencyDayOfWeek:
            recurringOrderFrequencyDayOfWeek.present
                ? recurringOrderFrequencyDayOfWeek.value
                : this.recurringOrderFrequencyDayOfWeek,
        recurringOrderFrequencyDayOfMonth:
            recurringOrderFrequencyDayOfMonth.present
                ? recurringOrderFrequencyDayOfMonth.value
                : this.recurringOrderFrequencyDayOfMonth,
        expirationDate:
            expirationDate.present ? expirationDate.value : this.expirationDate,
        isPaymentProofReady: isPaymentProofReady ?? this.isPaymentProofReady,
        isPaymentTrackingAvailable: isPaymentTrackingAvailable.present
            ? isPaymentTrackingAvailable.value
            : this.isPaymentTrackingAvailable,
        paymentFeeTotalAmount: paymentFeeTotalAmount.present
            ? paymentFeeTotalAmount.value
            : this.paymentFeeTotalAmount,
        paymentFeeTotalCurrency: paymentFeeTotalCurrency.present
            ? paymentFeeTotalCurrency.value
            : this.paymentFeeTotalCurrency,
        paymentTargetCountryCode: paymentTargetCountryCode.present
            ? paymentTargetCountryCode.value
            : this.paymentTargetCountryCode,
        paymentPhoneNumber: paymentPhoneNumber.present
            ? paymentPhoneNumber.value
            : this.paymentPhoneNumber,
        paymentWarningMessage: paymentWarningMessage.present
            ? paymentWarningMessage.value
            : this.paymentWarningMessage,
        paymentFeeTypeDescriptionWio: paymentFeeTypeDescriptionWio.present
            ? paymentFeeTypeDescriptionWio.value
            : this.paymentFeeTypeDescriptionWio,
        paymentFeeTypeAmountWio: paymentFeeTypeAmountWio.present
            ? paymentFeeTypeAmountWio.value
            : this.paymentFeeTypeAmountWio,
        paymentFeeTypeCurrencyWio: paymentFeeTypeCurrencyWio.present
            ? paymentFeeTypeCurrencyWio.value
            : this.paymentFeeTypeCurrencyWio,
        paymentFeeTypeDescriptionWioCorrespondentBank:
            paymentFeeTypeDescriptionWioCorrespondentBank.present
                ? paymentFeeTypeDescriptionWioCorrespondentBank.value
                : this.paymentFeeTypeDescriptionWioCorrespondentBank,
        paymentFeeTypeAmountWioCorrespondentBank:
            paymentFeeTypeAmountWioCorrespondentBank.present
                ? paymentFeeTypeAmountWioCorrespondentBank.value
                : this.paymentFeeTypeAmountWioCorrespondentBank,
        paymentFeeTypeCurrencyWioCorrespondentBank:
            paymentFeeTypeCurrencyWioCorrespondentBank.present
                ? paymentFeeTypeCurrencyWioCorrespondentBank.value
                : this.paymentFeeTypeCurrencyWioCorrespondentBank,
        paymentFeeTypeDescriptionWise: paymentFeeTypeDescriptionWise.present
            ? paymentFeeTypeDescriptionWise.value
            : this.paymentFeeTypeDescriptionWise,
        paymentFeeTypeAmountWise: paymentFeeTypeAmountWise.present
            ? paymentFeeTypeAmountWise.value
            : this.paymentFeeTypeAmountWise,
        paymentFeeTypeCurrencyWise: paymentFeeTypeCurrencyWise.present
            ? paymentFeeTypeCurrencyWise.value
            : this.paymentFeeTypeCurrencyWise,
        internationalTargetCountry: internationalTargetCountry.present
            ? internationalTargetCountry.value
            : this.internationalTargetCountry,
        internationalCompletedDateTime: internationalCompletedDateTime.present
            ? internationalCompletedDateTime.value
            : this.internationalCompletedDateTime,
        internationalEstimatedDateTime: internationalEstimatedDateTime.present
            ? internationalEstimatedDateTime.value
            : this.internationalEstimatedDateTime,
        internationalFeeChargingType: internationalFeeChargingType.present
            ? internationalFeeChargingType.value
            : this.internationalFeeChargingType,
        internationalSwiftCode: internationalSwiftCode.present
            ? internationalSwiftCode.value
            : this.internationalSwiftCode,
        internationalAccountNumber: internationalAccountNumber.present
            ? internationalAccountNumber.value
            : this.internationalAccountNumber,
        internationalPurposeDescription: internationalPurposeDescription.present
            ? internationalPurposeDescription.value
            : this.internationalPurposeDescription,
        internationalPurposeCode: internationalPurposeCode.present
            ? internationalPurposeCode.value
            : this.internationalPurposeCode,
        internationalNotes: internationalNotes.present
            ? internationalNotes.value
            : this.internationalNotes,
        internationalBankName: internationalBankName.present
            ? internationalBankName.value
            : this.internationalBankName,
        internationalTargetAmount: internationalTargetAmount.present
            ? internationalTargetAmount.value
            : this.internationalTargetAmount,
        internationalTargetAmountCurrency:
            internationalTargetAmountCurrency.present
                ? internationalTargetAmountCurrency.value
                : this.internationalTargetAmountCurrency,
        internationalTransferFee: internationalTransferFee.present
            ? internationalTransferFee.value
            : this.internationalTransferFee,
        internationalTransferFeeCurrency:
            internationalTransferFeeCurrency.present
                ? internationalTransferFeeCurrency.value
                : this.internationalTransferFeeCurrency,
        fabExpirationDate: fabExpirationDate.present
            ? fabExpirationDate.value
            : this.fabExpirationDate,
        fabBankName: fabBankName.present ? fabBankName.value : this.fabBankName,
        fabTotalFeeAmount: fabTotalFeeAmount.present
            ? fabTotalFeeAmount.value
            : this.fabTotalFeeAmount,
        fabTotalFeeAmountCurrency: fabTotalFeeAmountCurrency.present
            ? fabTotalFeeAmountCurrency.value
            : this.fabTotalFeeAmountCurrency,
        fabUpdatedDate:
            fabUpdatedDate.present ? fabUpdatedDate.value : this.fabUpdatedDate,
        fabCancellationReason: fabCancellationReason.present
            ? fabCancellationReason.value
            : this.fabCancellationReason,
        fabChequeNumber: fabChequeNumber.present
            ? fabChequeNumber.value
            : this.fabChequeNumber,
        fabChequeDate:
            fabChequeDate.present ? fabChequeDate.value : this.fabChequeDate,
        fabFrontImageUrl: fabFrontImageUrl.present
            ? fabFrontImageUrl.value
            : this.fabFrontImageUrl,
        localPurposeDescription: localPurposeDescription.present
            ? localPurposeDescription.value
            : this.localPurposeDescription,
        localPurposeCode: localPurposeCode.present
            ? localPurposeCode.value
            : this.localPurposeCode,
        localNote: localNote.present ? localNote.value : this.localNote,
        localTargetCountry: localTargetCountry.present
            ? localTargetCountry.value
            : this.localTargetCountry,
        localCompletedDateTime: localCompletedDateTime.present
            ? localCompletedDateTime.value
            : this.localCompletedDateTime,
        localEstimatedDateTime: localEstimatedDateTime.present
            ? localEstimatedDateTime.value
            : this.localEstimatedDateTime,
        localAccountNumber: localAccountNumber.present
            ? localAccountNumber.value
            : this.localAccountNumber,
        localSwiftCode:
            localSwiftCode.present ? localSwiftCode.value : this.localSwiftCode,
        localBankName:
            localBankName.present ? localBankName.value : this.localBankName,
        localTargetAmount: localTargetAmount.present
            ? localTargetAmount.value
            : this.localTargetAmount,
        localTargetAmountCurrency: localTargetAmountCurrency.present
            ? localTargetAmountCurrency.value
            : this.localTargetAmountCurrency,
        localTransferSource: localTransferSource.present
            ? localTransferSource.value
            : this.localTransferSource,
        localTransferApplicationId: localTransferApplicationId.present
            ? localTransferApplicationId.value
            : this.localTransferApplicationId,
        localRecipientName: localRecipientName.present
            ? localRecipientName.value
            : this.localRecipientName,
        npssExpirationDate: npssExpirationDate.present
            ? npssExpirationDate.value
            : this.npssExpirationDate,
        npssNotes: npssNotes.present ? npssNotes.value : this.npssNotes,
        npssRtpInitiationDate: npssRtpInitiationDate.present
            ? npssRtpInitiationDate.value
            : this.npssRtpInitiationDate,
        npssShowInPendingRtps: npssShowInPendingRtps.present
            ? npssShowInPendingRtps.value
            : this.npssShowInPendingRtps,
        npssShowRtpResendButton: npssShowRtpResendButton.present
            ? npssShowRtpResendButton.value
            : this.npssShowRtpResendButton,
        npssOriginalTransactionReference:
            npssOriginalTransactionReference.present
                ? npssOriginalTransactionReference.value
                : this.npssOriginalTransactionReference,
        npssOriginalTransactionReason: npssOriginalTransactionReason.present
            ? npssOriginalTransactionReason.value
            : this.npssOriginalTransactionReason,
        npssOriginalTransactionDeeplink: npssOriginalTransactionDeeplink.present
            ? npssOriginalTransactionDeeplink.value
            : this.npssOriginalTransactionDeeplink,
        token: token.present ? token.value : this.token,
        accNumberLast4Digits: accNumberLast4Digits.present
            ? accNumberLast4Digits.value
            : this.accNumberLast4Digits,
        cardMaskedPan:
            cardMaskedPan.present ? cardMaskedPan.value : this.cardMaskedPan,
        merchantCountryCode: merchantCountryCode.present
            ? merchantCountryCode.value
            : this.merchantCountryCode,
        originalCardTransactionCurrency: originalCardTransactionCurrency.present
            ? originalCardTransactionCurrency.value
            : this.originalCardTransactionCurrency,
        originalCardTransactionAmount: originalCardTransactionAmount.present
            ? originalCardTransactionAmount.value
            : this.originalCardTransactionAmount,
        earnedCashback:
            earnedCashback.present ? earnedCashback.value : this.earnedCashback,
        cashBackPercentage: cashBackPercentage.present
            ? cashBackPercentage.value
            : this.cashBackPercentage,
        cashBackTransactionCurrency: cashBackTransactionCurrency.present
            ? cashBackTransactionCurrency.value
            : this.cashBackTransactionCurrency,
        dividendTaxAmountValue: dividendTaxAmountValue.present
            ? dividendTaxAmountValue.value
            : this.dividendTaxAmountValue,
        dividendTaxAmountCurrency: dividendTaxAmountCurrency.present
            ? dividendTaxAmountCurrency.value
            : this.dividendTaxAmountCurrency,
        dividendAmountValue: dividendAmountValue.present
            ? dividendAmountValue.value
            : this.dividendAmountValue,
        dividendAmountCurrency: dividendAmountCurrency.present
            ? dividendAmountCurrency.value
            : this.dividendAmountCurrency,
        netDividendAmountValue: netDividendAmountValue.present
            ? netDividendAmountValue.value
            : this.netDividendAmountValue,
        netDividendAmountCurrency: netDividendAmountCurrency.present
            ? netDividendAmountCurrency.value
            : this.netDividendAmountCurrency,
        amountPerInstrumentValue: amountPerInstrumentValue.present
            ? amountPerInstrumentValue.value
            : this.amountPerInstrumentValue,
        amountPerInstrumentCurrency: amountPerInstrumentCurrency.present
            ? amountPerInstrumentCurrency.value
            : this.amountPerInstrumentCurrency,
        instrumentQuantity: instrumentQuantity.present
            ? instrumentQuantity.value
            : this.instrumentQuantity,
        brokerDividendTaxType: brokerDividendTaxType.present
            ? brokerDividendTaxType.value
            : this.brokerDividendTaxType,
        dividendTaxRate: dividendTaxRate.present
            ? dividendTaxRate.value
            : this.dividendTaxRate,
        securityLendingRebateAmountValue:
            securityLendingRebateAmountValue.present
                ? securityLendingRebateAmountValue.value
                : this.securityLendingRebateAmountValue,
        securityLendingRebateAmountCurrency:
            securityLendingRebateAmountCurrency.present
                ? securityLendingRebateAmountCurrency.value
                : this.securityLendingRebateAmountCurrency,
        securityLendingRebateTaxAmountCurrency:
            securityLendingRebateTaxAmountCurrency.present
                ? securityLendingRebateTaxAmountCurrency.value
                : this.securityLendingRebateTaxAmountCurrency,
        securityLendingRebateTaxAmountValue:
            securityLendingRebateTaxAmountValue.present
                ? securityLendingRebateTaxAmountValue.value
                : this.securityLendingRebateTaxAmountValue,
        securityLendingTransferredAmountCurrency:
            securityLendingTransferredAmountCurrency.present
                ? securityLendingTransferredAmountCurrency.value
                : this.securityLendingTransferredAmountCurrency,
        securityLendingTransferredAmountValue:
            securityLendingTransferredAmountValue.present
                ? securityLendingTransferredAmountValue.value
                : this.securityLendingTransferredAmountValue,
        securityLendingMonth: securityLendingMonth.present
            ? securityLendingMonth.value
            : this.securityLendingMonth,
        wealthManagementManagedPortfolioId:
            wealthManagementManagedPortfolioId.present
                ? wealthManagementManagedPortfolioId.value
                : this.wealthManagementManagedPortfolioId,
        wealthManagementProductName: wealthManagementProductName.present
            ? wealthManagementProductName.value
            : this.wealthManagementProductName,
        wealthManagementProductCategory: wealthManagementProductCategory.present
            ? wealthManagementProductCategory.value
            : this.wealthManagementProductCategory,
        wealthManagementAccountName: wealthManagementAccountName.present
            ? wealthManagementAccountName.value
            : this.wealthManagementAccountName,
        wealthManagementEstimatedExecutionDeadline:
            wealthManagementEstimatedExecutionDeadline.present
                ? wealthManagementEstimatedExecutionDeadline.value
                : this.wealthManagementEstimatedExecutionDeadline,
        wealthManagementTrackerSteps: wealthManagementTrackerSteps.present
            ? wealthManagementTrackerSteps.value
            : this.wealthManagementTrackerSteps,
        acquirerInstrumentName: acquirerInstrumentName.present
            ? acquirerInstrumentName.value
            : this.acquirerInstrumentName,
        acquirerInstrumentSymbol: acquirerInstrumentSymbol.present
            ? acquirerInstrumentSymbol.value
            : this.acquirerInstrumentSymbol,
        acquirerInstrumentExchangeId: acquirerInstrumentExchangeId.present
            ? acquirerInstrumentExchangeId.value
            : this.acquirerInstrumentExchangeId,
        acquirerInstrumentImageUrl: acquirerInstrumentImageUrl.present
            ? acquirerInstrumentImageUrl.value
            : this.acquirerInstrumentImageUrl,
        acquireeInstrumentName: acquireeInstrumentName.present
            ? acquireeInstrumentName.value
            : this.acquireeInstrumentName,
        acquireeInstrumentSymbol: acquireeInstrumentSymbol.present
            ? acquireeInstrumentSymbol.value
            : this.acquireeInstrumentSymbol,
        acquireeInstrumentExchangeId: acquireeInstrumentExchangeId.present
            ? acquireeInstrumentExchangeId.value
            : this.acquireeInstrumentExchangeId,
        acquireeInstrumentImageUrl: acquireeInstrumentImageUrl.present
            ? acquireeInstrumentImageUrl.value
            : this.acquireeInstrumentImageUrl,
        brokerAcquisitionAccountAmountValue:
            brokerAcquisitionAccountAmountValue.present
                ? brokerAcquisitionAccountAmountValue.value
                : this.brokerAcquisitionAccountAmountValue,
        brokerAcquisitionAccountAmountCurrency:
            brokerAcquisitionAccountAmountCurrency.present
                ? brokerAcquisitionAccountAmountCurrency.value
                : this.brokerAcquisitionAccountAmountCurrency,
        brokerAcquisitionPositionDelta: brokerAcquisitionPositionDelta.present
            ? brokerAcquisitionPositionDelta.value
            : this.brokerAcquisitionPositionDelta,
        ipoCompanyName:
            ipoCompanyName.present ? ipoCompanyName.value : this.ipoCompanyName,
        ipoSubscriptionNumber: ipoSubscriptionNumber.present
            ? ipoSubscriptionNumber.value
            : this.ipoSubscriptionNumber,
        ipoLeverageValue: ipoLeverageValue.present
            ? ipoLeverageValue.value
            : this.ipoLeverageValue,
        ipoLeverageCurrency: ipoLeverageCurrency.present
            ? ipoLeverageCurrency.value
            : this.ipoLeverageCurrency,
        ipoAmountValue:
            ipoAmountValue.present ? ipoAmountValue.value : this.ipoAmountValue,
        ipoAmountCurrency: ipoAmountCurrency.present
            ? ipoAmountCurrency.value
            : this.ipoAmountCurrency,
        ipoTotalAmountValue: ipoTotalAmountValue.present
            ? ipoTotalAmountValue.value
            : this.ipoTotalAmountValue,
        ipoTotalAmountCurrency: ipoTotalAmountCurrency.present
            ? ipoTotalAmountCurrency.value
            : this.ipoTotalAmountCurrency,
        ipoCompanyLogo:
            ipoCompanyLogo.present ? ipoCompanyLogo.value : this.ipoCompanyLogo,
        ipoEnhancementValue: ipoEnhancementValue.present
            ? ipoEnhancementValue.value
            : this.ipoEnhancementValue,
        ipoEnhancementCurrency: ipoEnhancementCurrency.present
            ? ipoEnhancementCurrency.value
            : this.ipoEnhancementCurrency,
        ipoRefundAmountValue: ipoRefundAmountValue.present
            ? ipoRefundAmountValue.value
            : this.ipoRefundAmountValue,
        ipoRefundAmountCurrency: ipoRefundAmountCurrency.present
            ? ipoRefundAmountCurrency.value
            : this.ipoRefundAmountCurrency,
        ipoLeveragePaidValue: ipoLeveragePaidValue.present
            ? ipoLeveragePaidValue.value
            : this.ipoLeveragePaidValue,
        ipoLeveragePaidCurrency: ipoLeveragePaidCurrency.present
            ? ipoLeveragePaidCurrency.value
            : this.ipoLeveragePaidCurrency,
        ipoLeverageFeePercentage: ipoLeverageFeePercentage.present
            ? ipoLeverageFeePercentage.value
            : this.ipoLeverageFeePercentage,
        ipoLeverageFeeValue: ipoLeverageFeeValue.present
            ? ipoLeverageFeeValue.value
            : this.ipoLeverageFeeValue,
        ipoLeverageFeeCurrency: ipoLeverageFeeCurrency.present
            ? ipoLeverageFeeCurrency.value
            : this.ipoLeverageFeeCurrency,
        ipoAllocationFeePercentage: ipoAllocationFeePercentage.present
            ? ipoAllocationFeePercentage.value
            : this.ipoAllocationFeePercentage,
        ipoAllotedFeeMoney: ipoAllotedFeeMoney.present
            ? ipoAllotedFeeMoney.value
            : this.ipoAllotedFeeMoney,
        ipoAllotedFeeMoneyCurrency: ipoAllotedFeeMoneyCurrency.present
            ? ipoAllotedFeeMoneyCurrency.value
            : this.ipoAllotedFeeMoneyCurrency,
        ipoAllocatedMoney: ipoAllocatedMoney.present
            ? ipoAllocatedMoney.value
            : this.ipoAllocatedMoney,
        ipoAllocatedMoneyCurrency: ipoAllocatedMoneyCurrency.present
            ? ipoAllocatedMoneyCurrency.value
            : this.ipoAllocatedMoneyCurrency,
        dynamicDetails: dynamicDetails ?? this.dynamicDetails,
        linkedTransactionFeeDetails:
            linkedTransactionFeeDetails ?? this.linkedTransactionFeeDetails,
        cashbackCalculations: cashbackCalculations.present
            ? cashbackCalculations.value
            : this.cashbackCalculations,
        carbonEmissionInGrams: carbonEmissionInGrams.present
            ? carbonEmissionInGrams.value
            : this.carbonEmissionInGrams,
        carbonEmissionInOunces: carbonEmissionInOunces.present
            ? carbonEmissionInOunces.value
            : this.carbonEmissionInOunces,
        recurringTransferRuleId: recurringTransferRuleId.present
            ? recurringTransferRuleId.value
            : this.recurringTransferRuleId,
      );
  TransactionsData copyWithCompanion(TransactionsCompanion data) {
    return TransactionsData(
      id: data.id.present ? data.id.value : this.id,
      productType:
          data.productType.present ? data.productType.value : this.productType,
      accountId: data.accountId.present ? data.accountId.value : this.accountId,
      transactionAccountType: data.transactionAccountType.present
          ? data.transactionAccountType.value
          : this.transactionAccountType,
      transactionType: data.transactionType.present
          ? data.transactionType.value
          : this.transactionType,
      transactionIdentifier: data.transactionIdentifier.present
          ? data.transactionIdentifier.value
          : this.transactionIdentifier,
      status: data.status.present ? data.status.value : this.status,
      referenceNumber: data.referenceNumber.present
          ? data.referenceNumber.value
          : this.referenceNumber,
      transactionMode: data.transactionMode.present
          ? data.transactionMode.value
          : this.transactionMode,
      transactionSubType: data.transactionSubType.present
          ? data.transactionSubType.value
          : this.transactionSubType,
      internalTransactionStatus: data.internalTransactionStatus.present
          ? data.internalTransactionStatus.value
          : this.internalTransactionStatus,
      transactionDateTime: data.transactionDateTime.present
          ? data.transactionDateTime.value
          : this.transactionDateTime,
      debtorName:
          data.debtorName.present ? data.debtorName.value : this.debtorName,
      customerId:
          data.customerId.present ? data.customerId.value : this.customerId,
      coreBankingIdentifier: data.coreBankingIdentifier.present
          ? data.coreBankingIdentifier.value
          : this.coreBankingIdentifier,
      creditorName: data.creditorName.present
          ? data.creditorName.value
          : this.creditorName,
      description:
          data.description.present ? data.description.value : this.description,
      subDescription: data.subDescription.present
          ? data.subDescription.value
          : this.subDescription,
      logoType: data.logoType.present ? data.logoType.value : this.logoType,
      merchantName: data.merchantName.present
          ? data.merchantName.value
          : this.merchantName,
      transactionImageKey: data.transactionImageKey.present
          ? data.transactionImageKey.value
          : this.transactionImageKey,
      amountValue:
          data.amountValue.present ? data.amountValue.value : this.amountValue,
      localAmount:
          data.localAmount.present ? data.localAmount.value : this.localAmount,
      exchangeRate: data.exchangeRate.present
          ? data.exchangeRate.value
          : this.exchangeRate,
      availableBalance: data.availableBalance.present
          ? data.availableBalance.value
          : this.availableBalance,
      amountCurrency: data.amountCurrency.present
          ? data.amountCurrency.value
          : this.amountCurrency,
      executionDate: data.executionDate.present
          ? data.executionDate.value
          : this.executionDate,
      transactionCategory: data.transactionCategory.present
          ? data.transactionCategory.value
          : this.transactionCategory,
      tppExternalReferenceId: data.tppExternalReferenceId.present
          ? data.tppExternalReferenceId.value
          : this.tppExternalReferenceId,
      fxFromAmount: data.fxFromAmount.present
          ? data.fxFromAmount.value
          : this.fxFromAmount,
      fxToAmount:
          data.fxToAmount.present ? data.fxToAmount.value : this.fxToAmount,
      fxFromAmountCurrency: data.fxFromAmountCurrency.present
          ? data.fxFromAmountCurrency.value
          : this.fxFromAmountCurrency,
      fxToAmountCurrency: data.fxToAmountCurrency.present
          ? data.fxToAmountCurrency.value
          : this.fxToAmountCurrency,
      fxFormattedExchangeRate: data.fxFormattedExchangeRate.present
          ? data.fxFormattedExchangeRate.value
          : this.fxFormattedExchangeRate,
      orderId: data.orderId.present ? data.orderId.value : this.orderId,
      portfolioId:
          data.portfolioId.present ? data.portfolioId.value : this.portfolioId,
      instrumentId: data.instrumentId.present
          ? data.instrumentId.value
          : this.instrumentId,
      createdDateTime: data.createdDateTime.present
          ? data.createdDateTime.value
          : this.createdDateTime,
      orderSide: data.orderSide.present ? data.orderSide.value : this.orderSide,
      instrumentName: data.instrumentName.present
          ? data.instrumentName.value
          : this.instrumentName,
      instrumentSymbol: data.instrumentSymbol.present
          ? data.instrumentSymbol.value
          : this.instrumentSymbol,
      instrumentExchangeId: data.instrumentExchangeId.present
          ? data.instrumentExchangeId.value
          : this.instrumentExchangeId,
      instrumentImageUrl: data.instrumentImageUrl.present
          ? data.instrumentImageUrl.value
          : this.instrumentImageUrl,
      orderType: data.orderType.present ? data.orderType.value : this.orderType,
      errorCode: data.errorCode.present ? data.errorCode.value : this.errorCode,
      errorMessage: data.errorMessage.present
          ? data.errorMessage.value
          : this.errorMessage,
      executedQuantity: data.executedQuantity.present
          ? data.executedQuantity.value
          : this.executedQuantity,
      estimatedQuantity: data.estimatedQuantity.present
          ? data.estimatedQuantity.value
          : this.estimatedQuantity,
      executedAmountValue: data.executedAmountValue.present
          ? data.executedAmountValue.value
          : this.executedAmountValue,
      executedAmountCurrency: data.executedAmountCurrency.present
          ? data.executedAmountCurrency.value
          : this.executedAmountCurrency,
      executedNetAmountValue: data.executedNetAmountValue.present
          ? data.executedNetAmountValue.value
          : this.executedNetAmountValue,
      executedNetAmountCurrency: data.executedNetAmountCurrency.present
          ? data.executedNetAmountCurrency.value
          : this.executedNetAmountCurrency,
      estimatedAmountValue: data.estimatedAmountValue.present
          ? data.estimatedAmountValue.value
          : this.estimatedAmountValue,
      estimatedAmountCurrency: data.estimatedAmountCurrency.present
          ? data.estimatedAmountCurrency.value
          : this.estimatedAmountCurrency,
      estimatedNetAmountValue: data.estimatedNetAmountValue.present
          ? data.estimatedNetAmountValue.value
          : this.estimatedNetAmountValue,
      estimatedNetAmountCurrency: data.estimatedNetAmountCurrency.present
          ? data.estimatedNetAmountCurrency.value
          : this.estimatedNetAmountCurrency,
      commissionAmountValue: data.commissionAmountValue.present
          ? data.commissionAmountValue.value
          : this.commissionAmountValue,
      commissionAmountCurrency: data.commissionAmountCurrency.present
          ? data.commissionAmountCurrency.value
          : this.commissionAmountCurrency,
      estimatedCommissionAmountValue:
          data.estimatedCommissionAmountValue.present
              ? data.estimatedCommissionAmountValue.value
              : this.estimatedCommissionAmountValue,
      estimatedCommissionAmountCurrency:
          data.estimatedCommissionAmountCurrency.present
              ? data.estimatedCommissionAmountCurrency.value
              : this.estimatedCommissionAmountCurrency,
      vatValue: data.vatValue.present ? data.vatValue.value : this.vatValue,
      vatCurrency:
          data.vatCurrency.present ? data.vatCurrency.value : this.vatCurrency,
      estimatedVatValue: data.estimatedVatValue.present
          ? data.estimatedVatValue.value
          : this.estimatedVatValue,
      estimatedVatCurrency: data.estimatedVatCurrency.present
          ? data.estimatedVatCurrency.value
          : this.estimatedVatCurrency,
      averagePriceValue: data.averagePriceValue.present
          ? data.averagePriceValue.value
          : this.averagePriceValue,
      averagePriceCurrency: data.averagePriceCurrency.present
          ? data.averagePriceCurrency.value
          : this.averagePriceCurrency,
      estimatedPriceValue: data.estimatedPriceValue.present
          ? data.estimatedPriceValue.value
          : this.estimatedPriceValue,
      estimatedPriceCurrency: data.estimatedPriceCurrency.present
          ? data.estimatedPriceCurrency.value
          : this.estimatedPriceCurrency,
      executedTotalCommissionAmount: data.executedTotalCommissionAmount.present
          ? data.executedTotalCommissionAmount.value
          : this.executedTotalCommissionAmount,
      executedTotalCommissionCurrency:
          data.executedTotalCommissionCurrency.present
              ? data.executedTotalCommissionCurrency.value
              : this.executedTotalCommissionCurrency,
      estimatedTotalCommissionAmount:
          data.estimatedTotalCommissionAmount.present
              ? data.estimatedTotalCommissionAmount.value
              : this.estimatedTotalCommissionAmount,
      estimatedTotalCommissionCurrency:
          data.estimatedTotalCommissionCurrency.present
              ? data.estimatedTotalCommissionCurrency.value
              : this.estimatedTotalCommissionCurrency,
      commissionCalculationBasisPoints:
          data.commissionCalculationBasisPoints.present
              ? data.commissionCalculationBasisPoints.value
              : this.commissionCalculationBasisPoints,
      commissionCalculationBaseAmount:
          data.commissionCalculationBaseAmount.present
              ? data.commissionCalculationBaseAmount.value
              : this.commissionCalculationBaseAmount,
      commissionCalculationMinAmount:
          data.commissionCalculationMinAmount.present
              ? data.commissionCalculationMinAmount.value
              : this.commissionCalculationMinAmount,
      commissionCalculationBaseAmountCurrency:
          data.commissionCalculationBaseAmountCurrency.present
              ? data.commissionCalculationBaseAmountCurrency.value
              : this.commissionCalculationBaseAmountCurrency,
      commissionCalculationMinAmountCurrency:
          data.commissionCalculationMinAmountCurrency.present
              ? data.commissionCalculationMinAmountCurrency.value
              : this.commissionCalculationMinAmountCurrency,
      instrumentExchangeCode: data.instrumentExchangeCode.present
          ? data.instrumentExchangeCode.value
          : this.instrumentExchangeCode,
      recurringOrderTemplateId: data.recurringOrderTemplateId.present
          ? data.recurringOrderTemplateId.value
          : this.recurringOrderTemplateId,
      recurringOrderAccountName: data.recurringOrderAccountName.present
          ? data.recurringOrderAccountName.value
          : this.recurringOrderAccountName,
      recurringOrderFrequencyType: data.recurringOrderFrequencyType.present
          ? data.recurringOrderFrequencyType.value
          : this.recurringOrderFrequencyType,
      recurringOrderFrequencyDayOfWeek:
          data.recurringOrderFrequencyDayOfWeek.present
              ? data.recurringOrderFrequencyDayOfWeek.value
              : this.recurringOrderFrequencyDayOfWeek,
      recurringOrderFrequencyDayOfMonth:
          data.recurringOrderFrequencyDayOfMonth.present
              ? data.recurringOrderFrequencyDayOfMonth.value
              : this.recurringOrderFrequencyDayOfMonth,
      expirationDate: data.expirationDate.present
          ? data.expirationDate.value
          : this.expirationDate,
      isPaymentProofReady: data.isPaymentProofReady.present
          ? data.isPaymentProofReady.value
          : this.isPaymentProofReady,
      isPaymentTrackingAvailable: data.isPaymentTrackingAvailable.present
          ? data.isPaymentTrackingAvailable.value
          : this.isPaymentTrackingAvailable,
      paymentFeeTotalAmount: data.paymentFeeTotalAmount.present
          ? data.paymentFeeTotalAmount.value
          : this.paymentFeeTotalAmount,
      paymentFeeTotalCurrency: data.paymentFeeTotalCurrency.present
          ? data.paymentFeeTotalCurrency.value
          : this.paymentFeeTotalCurrency,
      paymentTargetCountryCode: data.paymentTargetCountryCode.present
          ? data.paymentTargetCountryCode.value
          : this.paymentTargetCountryCode,
      paymentPhoneNumber: data.paymentPhoneNumber.present
          ? data.paymentPhoneNumber.value
          : this.paymentPhoneNumber,
      paymentWarningMessage: data.paymentWarningMessage.present
          ? data.paymentWarningMessage.value
          : this.paymentWarningMessage,
      paymentFeeTypeDescriptionWio: data.paymentFeeTypeDescriptionWio.present
          ? data.paymentFeeTypeDescriptionWio.value
          : this.paymentFeeTypeDescriptionWio,
      paymentFeeTypeAmountWio: data.paymentFeeTypeAmountWio.present
          ? data.paymentFeeTypeAmountWio.value
          : this.paymentFeeTypeAmountWio,
      paymentFeeTypeCurrencyWio: data.paymentFeeTypeCurrencyWio.present
          ? data.paymentFeeTypeCurrencyWio.value
          : this.paymentFeeTypeCurrencyWio,
      paymentFeeTypeDescriptionWioCorrespondentBank:
          data.paymentFeeTypeDescriptionWioCorrespondentBank.present
              ? data.paymentFeeTypeDescriptionWioCorrespondentBank.value
              : this.paymentFeeTypeDescriptionWioCorrespondentBank,
      paymentFeeTypeAmountWioCorrespondentBank:
          data.paymentFeeTypeAmountWioCorrespondentBank.present
              ? data.paymentFeeTypeAmountWioCorrespondentBank.value
              : this.paymentFeeTypeAmountWioCorrespondentBank,
      paymentFeeTypeCurrencyWioCorrespondentBank:
          data.paymentFeeTypeCurrencyWioCorrespondentBank.present
              ? data.paymentFeeTypeCurrencyWioCorrespondentBank.value
              : this.paymentFeeTypeCurrencyWioCorrespondentBank,
      paymentFeeTypeDescriptionWise: data.paymentFeeTypeDescriptionWise.present
          ? data.paymentFeeTypeDescriptionWise.value
          : this.paymentFeeTypeDescriptionWise,
      paymentFeeTypeAmountWise: data.paymentFeeTypeAmountWise.present
          ? data.paymentFeeTypeAmountWise.value
          : this.paymentFeeTypeAmountWise,
      paymentFeeTypeCurrencyWise: data.paymentFeeTypeCurrencyWise.present
          ? data.paymentFeeTypeCurrencyWise.value
          : this.paymentFeeTypeCurrencyWise,
      internationalTargetCountry: data.internationalTargetCountry.present
          ? data.internationalTargetCountry.value
          : this.internationalTargetCountry,
      internationalCompletedDateTime:
          data.internationalCompletedDateTime.present
              ? data.internationalCompletedDateTime.value
              : this.internationalCompletedDateTime,
      internationalEstimatedDateTime:
          data.internationalEstimatedDateTime.present
              ? data.internationalEstimatedDateTime.value
              : this.internationalEstimatedDateTime,
      internationalFeeChargingType: data.internationalFeeChargingType.present
          ? data.internationalFeeChargingType.value
          : this.internationalFeeChargingType,
      internationalSwiftCode: data.internationalSwiftCode.present
          ? data.internationalSwiftCode.value
          : this.internationalSwiftCode,
      internationalAccountNumber: data.internationalAccountNumber.present
          ? data.internationalAccountNumber.value
          : this.internationalAccountNumber,
      internationalPurposeDescription:
          data.internationalPurposeDescription.present
              ? data.internationalPurposeDescription.value
              : this.internationalPurposeDescription,
      internationalPurposeCode: data.internationalPurposeCode.present
          ? data.internationalPurposeCode.value
          : this.internationalPurposeCode,
      internationalNotes: data.internationalNotes.present
          ? data.internationalNotes.value
          : this.internationalNotes,
      internationalBankName: data.internationalBankName.present
          ? data.internationalBankName.value
          : this.internationalBankName,
      internationalTargetAmount: data.internationalTargetAmount.present
          ? data.internationalTargetAmount.value
          : this.internationalTargetAmount,
      internationalTargetAmountCurrency:
          data.internationalTargetAmountCurrency.present
              ? data.internationalTargetAmountCurrency.value
              : this.internationalTargetAmountCurrency,
      internationalTransferFee: data.internationalTransferFee.present
          ? data.internationalTransferFee.value
          : this.internationalTransferFee,
      internationalTransferFeeCurrency:
          data.internationalTransferFeeCurrency.present
              ? data.internationalTransferFeeCurrency.value
              : this.internationalTransferFeeCurrency,
      fabExpirationDate: data.fabExpirationDate.present
          ? data.fabExpirationDate.value
          : this.fabExpirationDate,
      fabBankName:
          data.fabBankName.present ? data.fabBankName.value : this.fabBankName,
      fabTotalFeeAmount: data.fabTotalFeeAmount.present
          ? data.fabTotalFeeAmount.value
          : this.fabTotalFeeAmount,
      fabTotalFeeAmountCurrency: data.fabTotalFeeAmountCurrency.present
          ? data.fabTotalFeeAmountCurrency.value
          : this.fabTotalFeeAmountCurrency,
      fabUpdatedDate: data.fabUpdatedDate.present
          ? data.fabUpdatedDate.value
          : this.fabUpdatedDate,
      fabCancellationReason: data.fabCancellationReason.present
          ? data.fabCancellationReason.value
          : this.fabCancellationReason,
      fabChequeNumber: data.fabChequeNumber.present
          ? data.fabChequeNumber.value
          : this.fabChequeNumber,
      fabChequeDate: data.fabChequeDate.present
          ? data.fabChequeDate.value
          : this.fabChequeDate,
      fabFrontImageUrl: data.fabFrontImageUrl.present
          ? data.fabFrontImageUrl.value
          : this.fabFrontImageUrl,
      localPurposeDescription: data.localPurposeDescription.present
          ? data.localPurposeDescription.value
          : this.localPurposeDescription,
      localPurposeCode: data.localPurposeCode.present
          ? data.localPurposeCode.value
          : this.localPurposeCode,
      localNote: data.localNote.present ? data.localNote.value : this.localNote,
      localTargetCountry: data.localTargetCountry.present
          ? data.localTargetCountry.value
          : this.localTargetCountry,
      localCompletedDateTime: data.localCompletedDateTime.present
          ? data.localCompletedDateTime.value
          : this.localCompletedDateTime,
      localEstimatedDateTime: data.localEstimatedDateTime.present
          ? data.localEstimatedDateTime.value
          : this.localEstimatedDateTime,
      localAccountNumber: data.localAccountNumber.present
          ? data.localAccountNumber.value
          : this.localAccountNumber,
      localSwiftCode: data.localSwiftCode.present
          ? data.localSwiftCode.value
          : this.localSwiftCode,
      localBankName: data.localBankName.present
          ? data.localBankName.value
          : this.localBankName,
      localTargetAmount: data.localTargetAmount.present
          ? data.localTargetAmount.value
          : this.localTargetAmount,
      localTargetAmountCurrency: data.localTargetAmountCurrency.present
          ? data.localTargetAmountCurrency.value
          : this.localTargetAmountCurrency,
      localTransferSource: data.localTransferSource.present
          ? data.localTransferSource.value
          : this.localTransferSource,
      localTransferApplicationId: data.localTransferApplicationId.present
          ? data.localTransferApplicationId.value
          : this.localTransferApplicationId,
      localRecipientName: data.localRecipientName.present
          ? data.localRecipientName.value
          : this.localRecipientName,
      npssExpirationDate: data.npssExpirationDate.present
          ? data.npssExpirationDate.value
          : this.npssExpirationDate,
      npssNotes: data.npssNotes.present ? data.npssNotes.value : this.npssNotes,
      npssRtpInitiationDate: data.npssRtpInitiationDate.present
          ? data.npssRtpInitiationDate.value
          : this.npssRtpInitiationDate,
      npssShowInPendingRtps: data.npssShowInPendingRtps.present
          ? data.npssShowInPendingRtps.value
          : this.npssShowInPendingRtps,
      npssShowRtpResendButton: data.npssShowRtpResendButton.present
          ? data.npssShowRtpResendButton.value
          : this.npssShowRtpResendButton,
      npssOriginalTransactionReference:
          data.npssOriginalTransactionReference.present
              ? data.npssOriginalTransactionReference.value
              : this.npssOriginalTransactionReference,
      npssOriginalTransactionReason: data.npssOriginalTransactionReason.present
          ? data.npssOriginalTransactionReason.value
          : this.npssOriginalTransactionReason,
      npssOriginalTransactionDeeplink:
          data.npssOriginalTransactionDeeplink.present
              ? data.npssOriginalTransactionDeeplink.value
              : this.npssOriginalTransactionDeeplink,
      token: data.token.present ? data.token.value : this.token,
      accNumberLast4Digits: data.accNumberLast4Digits.present
          ? data.accNumberLast4Digits.value
          : this.accNumberLast4Digits,
      cardMaskedPan: data.cardMaskedPan.present
          ? data.cardMaskedPan.value
          : this.cardMaskedPan,
      merchantCountryCode: data.merchantCountryCode.present
          ? data.merchantCountryCode.value
          : this.merchantCountryCode,
      originalCardTransactionCurrency:
          data.originalCardTransactionCurrency.present
              ? data.originalCardTransactionCurrency.value
              : this.originalCardTransactionCurrency,
      originalCardTransactionAmount: data.originalCardTransactionAmount.present
          ? data.originalCardTransactionAmount.value
          : this.originalCardTransactionAmount,
      earnedCashback: data.earnedCashback.present
          ? data.earnedCashback.value
          : this.earnedCashback,
      cashBackPercentage: data.cashBackPercentage.present
          ? data.cashBackPercentage.value
          : this.cashBackPercentage,
      cashBackTransactionCurrency: data.cashBackTransactionCurrency.present
          ? data.cashBackTransactionCurrency.value
          : this.cashBackTransactionCurrency,
      dividendTaxAmountValue: data.dividendTaxAmountValue.present
          ? data.dividendTaxAmountValue.value
          : this.dividendTaxAmountValue,
      dividendTaxAmountCurrency: data.dividendTaxAmountCurrency.present
          ? data.dividendTaxAmountCurrency.value
          : this.dividendTaxAmountCurrency,
      dividendAmountValue: data.dividendAmountValue.present
          ? data.dividendAmountValue.value
          : this.dividendAmountValue,
      dividendAmountCurrency: data.dividendAmountCurrency.present
          ? data.dividendAmountCurrency.value
          : this.dividendAmountCurrency,
      netDividendAmountValue: data.netDividendAmountValue.present
          ? data.netDividendAmountValue.value
          : this.netDividendAmountValue,
      netDividendAmountCurrency: data.netDividendAmountCurrency.present
          ? data.netDividendAmountCurrency.value
          : this.netDividendAmountCurrency,
      amountPerInstrumentValue: data.amountPerInstrumentValue.present
          ? data.amountPerInstrumentValue.value
          : this.amountPerInstrumentValue,
      amountPerInstrumentCurrency: data.amountPerInstrumentCurrency.present
          ? data.amountPerInstrumentCurrency.value
          : this.amountPerInstrumentCurrency,
      instrumentQuantity: data.instrumentQuantity.present
          ? data.instrumentQuantity.value
          : this.instrumentQuantity,
      brokerDividendTaxType: data.brokerDividendTaxType.present
          ? data.brokerDividendTaxType.value
          : this.brokerDividendTaxType,
      dividendTaxRate: data.dividendTaxRate.present
          ? data.dividendTaxRate.value
          : this.dividendTaxRate,
      securityLendingRebateAmountValue:
          data.securityLendingRebateAmountValue.present
              ? data.securityLendingRebateAmountValue.value
              : this.securityLendingRebateAmountValue,
      securityLendingRebateAmountCurrency:
          data.securityLendingRebateAmountCurrency.present
              ? data.securityLendingRebateAmountCurrency.value
              : this.securityLendingRebateAmountCurrency,
      securityLendingRebateTaxAmountCurrency:
          data.securityLendingRebateTaxAmountCurrency.present
              ? data.securityLendingRebateTaxAmountCurrency.value
              : this.securityLendingRebateTaxAmountCurrency,
      securityLendingRebateTaxAmountValue:
          data.securityLendingRebateTaxAmountValue.present
              ? data.securityLendingRebateTaxAmountValue.value
              : this.securityLendingRebateTaxAmountValue,
      securityLendingTransferredAmountCurrency:
          data.securityLendingTransferredAmountCurrency.present
              ? data.securityLendingTransferredAmountCurrency.value
              : this.securityLendingTransferredAmountCurrency,
      securityLendingTransferredAmountValue:
          data.securityLendingTransferredAmountValue.present
              ? data.securityLendingTransferredAmountValue.value
              : this.securityLendingTransferredAmountValue,
      securityLendingMonth: data.securityLendingMonth.present
          ? data.securityLendingMonth.value
          : this.securityLendingMonth,
      wealthManagementManagedPortfolioId:
          data.wealthManagementManagedPortfolioId.present
              ? data.wealthManagementManagedPortfolioId.value
              : this.wealthManagementManagedPortfolioId,
      wealthManagementProductName: data.wealthManagementProductName.present
          ? data.wealthManagementProductName.value
          : this.wealthManagementProductName,
      wealthManagementProductCategory:
          data.wealthManagementProductCategory.present
              ? data.wealthManagementProductCategory.value
              : this.wealthManagementProductCategory,
      wealthManagementAccountName: data.wealthManagementAccountName.present
          ? data.wealthManagementAccountName.value
          : this.wealthManagementAccountName,
      wealthManagementEstimatedExecutionDeadline:
          data.wealthManagementEstimatedExecutionDeadline.present
              ? data.wealthManagementEstimatedExecutionDeadline.value
              : this.wealthManagementEstimatedExecutionDeadline,
      wealthManagementTrackerSteps: data.wealthManagementTrackerSteps.present
          ? data.wealthManagementTrackerSteps.value
          : this.wealthManagementTrackerSteps,
      acquirerInstrumentName: data.acquirerInstrumentName.present
          ? data.acquirerInstrumentName.value
          : this.acquirerInstrumentName,
      acquirerInstrumentSymbol: data.acquirerInstrumentSymbol.present
          ? data.acquirerInstrumentSymbol.value
          : this.acquirerInstrumentSymbol,
      acquirerInstrumentExchangeId: data.acquirerInstrumentExchangeId.present
          ? data.acquirerInstrumentExchangeId.value
          : this.acquirerInstrumentExchangeId,
      acquirerInstrumentImageUrl: data.acquirerInstrumentImageUrl.present
          ? data.acquirerInstrumentImageUrl.value
          : this.acquirerInstrumentImageUrl,
      acquireeInstrumentName: data.acquireeInstrumentName.present
          ? data.acquireeInstrumentName.value
          : this.acquireeInstrumentName,
      acquireeInstrumentSymbol: data.acquireeInstrumentSymbol.present
          ? data.acquireeInstrumentSymbol.value
          : this.acquireeInstrumentSymbol,
      acquireeInstrumentExchangeId: data.acquireeInstrumentExchangeId.present
          ? data.acquireeInstrumentExchangeId.value
          : this.acquireeInstrumentExchangeId,
      acquireeInstrumentImageUrl: data.acquireeInstrumentImageUrl.present
          ? data.acquireeInstrumentImageUrl.value
          : this.acquireeInstrumentImageUrl,
      brokerAcquisitionAccountAmountValue:
          data.brokerAcquisitionAccountAmountValue.present
              ? data.brokerAcquisitionAccountAmountValue.value
              : this.brokerAcquisitionAccountAmountValue,
      brokerAcquisitionAccountAmountCurrency:
          data.brokerAcquisitionAccountAmountCurrency.present
              ? data.brokerAcquisitionAccountAmountCurrency.value
              : this.brokerAcquisitionAccountAmountCurrency,
      brokerAcquisitionPositionDelta:
          data.brokerAcquisitionPositionDelta.present
              ? data.brokerAcquisitionPositionDelta.value
              : this.brokerAcquisitionPositionDelta,
      ipoCompanyName: data.ipoCompanyName.present
          ? data.ipoCompanyName.value
          : this.ipoCompanyName,
      ipoSubscriptionNumber: data.ipoSubscriptionNumber.present
          ? data.ipoSubscriptionNumber.value
          : this.ipoSubscriptionNumber,
      ipoLeverageValue: data.ipoLeverageValue.present
          ? data.ipoLeverageValue.value
          : this.ipoLeverageValue,
      ipoLeverageCurrency: data.ipoLeverageCurrency.present
          ? data.ipoLeverageCurrency.value
          : this.ipoLeverageCurrency,
      ipoAmountValue: data.ipoAmountValue.present
          ? data.ipoAmountValue.value
          : this.ipoAmountValue,
      ipoAmountCurrency: data.ipoAmountCurrency.present
          ? data.ipoAmountCurrency.value
          : this.ipoAmountCurrency,
      ipoTotalAmountValue: data.ipoTotalAmountValue.present
          ? data.ipoTotalAmountValue.value
          : this.ipoTotalAmountValue,
      ipoTotalAmountCurrency: data.ipoTotalAmountCurrency.present
          ? data.ipoTotalAmountCurrency.value
          : this.ipoTotalAmountCurrency,
      ipoCompanyLogo: data.ipoCompanyLogo.present
          ? data.ipoCompanyLogo.value
          : this.ipoCompanyLogo,
      ipoEnhancementValue: data.ipoEnhancementValue.present
          ? data.ipoEnhancementValue.value
          : this.ipoEnhancementValue,
      ipoEnhancementCurrency: data.ipoEnhancementCurrency.present
          ? data.ipoEnhancementCurrency.value
          : this.ipoEnhancementCurrency,
      ipoRefundAmountValue: data.ipoRefundAmountValue.present
          ? data.ipoRefundAmountValue.value
          : this.ipoRefundAmountValue,
      ipoRefundAmountCurrency: data.ipoRefundAmountCurrency.present
          ? data.ipoRefundAmountCurrency.value
          : this.ipoRefundAmountCurrency,
      ipoLeveragePaidValue: data.ipoLeveragePaidValue.present
          ? data.ipoLeveragePaidValue.value
          : this.ipoLeveragePaidValue,
      ipoLeveragePaidCurrency: data.ipoLeveragePaidCurrency.present
          ? data.ipoLeveragePaidCurrency.value
          : this.ipoLeveragePaidCurrency,
      ipoLeverageFeePercentage: data.ipoLeverageFeePercentage.present
          ? data.ipoLeverageFeePercentage.value
          : this.ipoLeverageFeePercentage,
      ipoLeverageFeeValue: data.ipoLeverageFeeValue.present
          ? data.ipoLeverageFeeValue.value
          : this.ipoLeverageFeeValue,
      ipoLeverageFeeCurrency: data.ipoLeverageFeeCurrency.present
          ? data.ipoLeverageFeeCurrency.value
          : this.ipoLeverageFeeCurrency,
      ipoAllocationFeePercentage: data.ipoAllocationFeePercentage.present
          ? data.ipoAllocationFeePercentage.value
          : this.ipoAllocationFeePercentage,
      ipoAllotedFeeMoney: data.ipoAllotedFeeMoney.present
          ? data.ipoAllotedFeeMoney.value
          : this.ipoAllotedFeeMoney,
      ipoAllotedFeeMoneyCurrency: data.ipoAllotedFeeMoneyCurrency.present
          ? data.ipoAllotedFeeMoneyCurrency.value
          : this.ipoAllotedFeeMoneyCurrency,
      ipoAllocatedMoney: data.ipoAllocatedMoney.present
          ? data.ipoAllocatedMoney.value
          : this.ipoAllocatedMoney,
      ipoAllocatedMoneyCurrency: data.ipoAllocatedMoneyCurrency.present
          ? data.ipoAllocatedMoneyCurrency.value
          : this.ipoAllocatedMoneyCurrency,
      dynamicDetails: data.dynamicDetails.present
          ? data.dynamicDetails.value
          : this.dynamicDetails,
      linkedTransactionFeeDetails: data.linkedTransactionFeeDetails.present
          ? data.linkedTransactionFeeDetails.value
          : this.linkedTransactionFeeDetails,
      cashbackCalculations: data.cashbackCalculations.present
          ? data.cashbackCalculations.value
          : this.cashbackCalculations,
      carbonEmissionInGrams: data.carbonEmissionInGrams.present
          ? data.carbonEmissionInGrams.value
          : this.carbonEmissionInGrams,
      carbonEmissionInOunces: data.carbonEmissionInOunces.present
          ? data.carbonEmissionInOunces.value
          : this.carbonEmissionInOunces,
      recurringTransferRuleId: data.recurringTransferRuleId.present
          ? data.recurringTransferRuleId.value
          : this.recurringTransferRuleId,
    );
  }

  @override
  String toString() {
    return (StringBuffer('TransactionsData(')
          ..write('id: $id, ')
          ..write('productType: $productType, ')
          ..write('accountId: $accountId, ')
          ..write('transactionAccountType: $transactionAccountType, ')
          ..write('transactionType: $transactionType, ')
          ..write('transactionIdentifier: $transactionIdentifier, ')
          ..write('status: $status, ')
          ..write('referenceNumber: $referenceNumber, ')
          ..write('transactionMode: $transactionMode, ')
          ..write('transactionSubType: $transactionSubType, ')
          ..write('internalTransactionStatus: $internalTransactionStatus, ')
          ..write('transactionDateTime: $transactionDateTime, ')
          ..write('debtorName: $debtorName, ')
          ..write('customerId: $customerId, ')
          ..write('coreBankingIdentifier: $coreBankingIdentifier, ')
          ..write('creditorName: $creditorName, ')
          ..write('description: $description, ')
          ..write('subDescription: $subDescription, ')
          ..write('logoType: $logoType, ')
          ..write('merchantName: $merchantName, ')
          ..write('transactionImageKey: $transactionImageKey, ')
          ..write('amountValue: $amountValue, ')
          ..write('localAmount: $localAmount, ')
          ..write('exchangeRate: $exchangeRate, ')
          ..write('availableBalance: $availableBalance, ')
          ..write('amountCurrency: $amountCurrency, ')
          ..write('executionDate: $executionDate, ')
          ..write('transactionCategory: $transactionCategory, ')
          ..write('tppExternalReferenceId: $tppExternalReferenceId, ')
          ..write('fxFromAmount: $fxFromAmount, ')
          ..write('fxToAmount: $fxToAmount, ')
          ..write('fxFromAmountCurrency: $fxFromAmountCurrency, ')
          ..write('fxToAmountCurrency: $fxToAmountCurrency, ')
          ..write('fxFormattedExchangeRate: $fxFormattedExchangeRate, ')
          ..write('orderId: $orderId, ')
          ..write('portfolioId: $portfolioId, ')
          ..write('instrumentId: $instrumentId, ')
          ..write('createdDateTime: $createdDateTime, ')
          ..write('orderSide: $orderSide, ')
          ..write('instrumentName: $instrumentName, ')
          ..write('instrumentSymbol: $instrumentSymbol, ')
          ..write('instrumentExchangeId: $instrumentExchangeId, ')
          ..write('instrumentImageUrl: $instrumentImageUrl, ')
          ..write('orderType: $orderType, ')
          ..write('errorCode: $errorCode, ')
          ..write('errorMessage: $errorMessage, ')
          ..write('executedQuantity: $executedQuantity, ')
          ..write('estimatedQuantity: $estimatedQuantity, ')
          ..write('executedAmountValue: $executedAmountValue, ')
          ..write('executedAmountCurrency: $executedAmountCurrency, ')
          ..write('executedNetAmountValue: $executedNetAmountValue, ')
          ..write('executedNetAmountCurrency: $executedNetAmountCurrency, ')
          ..write('estimatedAmountValue: $estimatedAmountValue, ')
          ..write('estimatedAmountCurrency: $estimatedAmountCurrency, ')
          ..write('estimatedNetAmountValue: $estimatedNetAmountValue, ')
          ..write('estimatedNetAmountCurrency: $estimatedNetAmountCurrency, ')
          ..write('commissionAmountValue: $commissionAmountValue, ')
          ..write('commissionAmountCurrency: $commissionAmountCurrency, ')
          ..write(
              'estimatedCommissionAmountValue: $estimatedCommissionAmountValue, ')
          ..write(
              'estimatedCommissionAmountCurrency: $estimatedCommissionAmountCurrency, ')
          ..write('vatValue: $vatValue, ')
          ..write('vatCurrency: $vatCurrency, ')
          ..write('estimatedVatValue: $estimatedVatValue, ')
          ..write('estimatedVatCurrency: $estimatedVatCurrency, ')
          ..write('averagePriceValue: $averagePriceValue, ')
          ..write('averagePriceCurrency: $averagePriceCurrency, ')
          ..write('estimatedPriceValue: $estimatedPriceValue, ')
          ..write('estimatedPriceCurrency: $estimatedPriceCurrency, ')
          ..write(
              'executedTotalCommissionAmount: $executedTotalCommissionAmount, ')
          ..write(
              'executedTotalCommissionCurrency: $executedTotalCommissionCurrency, ')
          ..write(
              'estimatedTotalCommissionAmount: $estimatedTotalCommissionAmount, ')
          ..write(
              'estimatedTotalCommissionCurrency: $estimatedTotalCommissionCurrency, ')
          ..write(
              'commissionCalculationBasisPoints: $commissionCalculationBasisPoints, ')
          ..write(
              'commissionCalculationBaseAmount: $commissionCalculationBaseAmount, ')
          ..write(
              'commissionCalculationMinAmount: $commissionCalculationMinAmount, ')
          ..write(
              'commissionCalculationBaseAmountCurrency: $commissionCalculationBaseAmountCurrency, ')
          ..write(
              'commissionCalculationMinAmountCurrency: $commissionCalculationMinAmountCurrency, ')
          ..write('instrumentExchangeCode: $instrumentExchangeCode, ')
          ..write('recurringOrderTemplateId: $recurringOrderTemplateId, ')
          ..write('recurringOrderAccountName: $recurringOrderAccountName, ')
          ..write('recurringOrderFrequencyType: $recurringOrderFrequencyType, ')
          ..write(
              'recurringOrderFrequencyDayOfWeek: $recurringOrderFrequencyDayOfWeek, ')
          ..write(
              'recurringOrderFrequencyDayOfMonth: $recurringOrderFrequencyDayOfMonth, ')
          ..write('expirationDate: $expirationDate, ')
          ..write('isPaymentProofReady: $isPaymentProofReady, ')
          ..write('isPaymentTrackingAvailable: $isPaymentTrackingAvailable, ')
          ..write('paymentFeeTotalAmount: $paymentFeeTotalAmount, ')
          ..write('paymentFeeTotalCurrency: $paymentFeeTotalCurrency, ')
          ..write('paymentTargetCountryCode: $paymentTargetCountryCode, ')
          ..write('paymentPhoneNumber: $paymentPhoneNumber, ')
          ..write('paymentWarningMessage: $paymentWarningMessage, ')
          ..write(
              'paymentFeeTypeDescriptionWio: $paymentFeeTypeDescriptionWio, ')
          ..write('paymentFeeTypeAmountWio: $paymentFeeTypeAmountWio, ')
          ..write('paymentFeeTypeCurrencyWio: $paymentFeeTypeCurrencyWio, ')
          ..write(
              'paymentFeeTypeDescriptionWioCorrespondentBank: $paymentFeeTypeDescriptionWioCorrespondentBank, ')
          ..write(
              'paymentFeeTypeAmountWioCorrespondentBank: $paymentFeeTypeAmountWioCorrespondentBank, ')
          ..write(
              'paymentFeeTypeCurrencyWioCorrespondentBank: $paymentFeeTypeCurrencyWioCorrespondentBank, ')
          ..write(
              'paymentFeeTypeDescriptionWise: $paymentFeeTypeDescriptionWise, ')
          ..write('paymentFeeTypeAmountWise: $paymentFeeTypeAmountWise, ')
          ..write('paymentFeeTypeCurrencyWise: $paymentFeeTypeCurrencyWise, ')
          ..write('internationalTargetCountry: $internationalTargetCountry, ')
          ..write(
              'internationalCompletedDateTime: $internationalCompletedDateTime, ')
          ..write(
              'internationalEstimatedDateTime: $internationalEstimatedDateTime, ')
          ..write(
              'internationalFeeChargingType: $internationalFeeChargingType, ')
          ..write('internationalSwiftCode: $internationalSwiftCode, ')
          ..write('internationalAccountNumber: $internationalAccountNumber, ')
          ..write(
              'internationalPurposeDescription: $internationalPurposeDescription, ')
          ..write('internationalPurposeCode: $internationalPurposeCode, ')
          ..write('internationalNotes: $internationalNotes, ')
          ..write('internationalBankName: $internationalBankName, ')
          ..write('internationalTargetAmount: $internationalTargetAmount, ')
          ..write(
              'internationalTargetAmountCurrency: $internationalTargetAmountCurrency, ')
          ..write('internationalTransferFee: $internationalTransferFee, ')
          ..write(
              'internationalTransferFeeCurrency: $internationalTransferFeeCurrency, ')
          ..write('fabExpirationDate: $fabExpirationDate, ')
          ..write('fabBankName: $fabBankName, ')
          ..write('fabTotalFeeAmount: $fabTotalFeeAmount, ')
          ..write('fabTotalFeeAmountCurrency: $fabTotalFeeAmountCurrency, ')
          ..write('fabUpdatedDate: $fabUpdatedDate, ')
          ..write('fabCancellationReason: $fabCancellationReason, ')
          ..write('fabChequeNumber: $fabChequeNumber, ')
          ..write('fabChequeDate: $fabChequeDate, ')
          ..write('fabFrontImageUrl: $fabFrontImageUrl, ')
          ..write('localPurposeDescription: $localPurposeDescription, ')
          ..write('localPurposeCode: $localPurposeCode, ')
          ..write('localNote: $localNote, ')
          ..write('localTargetCountry: $localTargetCountry, ')
          ..write('localCompletedDateTime: $localCompletedDateTime, ')
          ..write('localEstimatedDateTime: $localEstimatedDateTime, ')
          ..write('localAccountNumber: $localAccountNumber, ')
          ..write('localSwiftCode: $localSwiftCode, ')
          ..write('localBankName: $localBankName, ')
          ..write('localTargetAmount: $localTargetAmount, ')
          ..write('localTargetAmountCurrency: $localTargetAmountCurrency, ')
          ..write('localTransferSource: $localTransferSource, ')
          ..write('localTransferApplicationId: $localTransferApplicationId, ')
          ..write('localRecipientName: $localRecipientName, ')
          ..write('npssExpirationDate: $npssExpirationDate, ')
          ..write('npssNotes: $npssNotes, ')
          ..write('npssRtpInitiationDate: $npssRtpInitiationDate, ')
          ..write('npssShowInPendingRtps: $npssShowInPendingRtps, ')
          ..write('npssShowRtpResendButton: $npssShowRtpResendButton, ')
          ..write(
              'npssOriginalTransactionReference: $npssOriginalTransactionReference, ')
          ..write(
              'npssOriginalTransactionReason: $npssOriginalTransactionReason, ')
          ..write(
              'npssOriginalTransactionDeeplink: $npssOriginalTransactionDeeplink, ')
          ..write('token: $token, ')
          ..write('accNumberLast4Digits: $accNumberLast4Digits, ')
          ..write('cardMaskedPan: $cardMaskedPan, ')
          ..write('merchantCountryCode: $merchantCountryCode, ')
          ..write(
              'originalCardTransactionCurrency: $originalCardTransactionCurrency, ')
          ..write(
              'originalCardTransactionAmount: $originalCardTransactionAmount, ')
          ..write('earnedCashback: $earnedCashback, ')
          ..write('cashBackPercentage: $cashBackPercentage, ')
          ..write('cashBackTransactionCurrency: $cashBackTransactionCurrency, ')
          ..write('dividendTaxAmountValue: $dividendTaxAmountValue, ')
          ..write('dividendTaxAmountCurrency: $dividendTaxAmountCurrency, ')
          ..write('dividendAmountValue: $dividendAmountValue, ')
          ..write('dividendAmountCurrency: $dividendAmountCurrency, ')
          ..write('netDividendAmountValue: $netDividendAmountValue, ')
          ..write('netDividendAmountCurrency: $netDividendAmountCurrency, ')
          ..write('amountPerInstrumentValue: $amountPerInstrumentValue, ')
          ..write('amountPerInstrumentCurrency: $amountPerInstrumentCurrency, ')
          ..write('instrumentQuantity: $instrumentQuantity, ')
          ..write('brokerDividendTaxType: $brokerDividendTaxType, ')
          ..write('dividendTaxRate: $dividendTaxRate, ')
          ..write(
              'securityLendingRebateAmountValue: $securityLendingRebateAmountValue, ')
          ..write(
              'securityLendingRebateAmountCurrency: $securityLendingRebateAmountCurrency, ')
          ..write(
              'securityLendingRebateTaxAmountCurrency: $securityLendingRebateTaxAmountCurrency, ')
          ..write(
              'securityLendingRebateTaxAmountValue: $securityLendingRebateTaxAmountValue, ')
          ..write(
              'securityLendingTransferredAmountCurrency: $securityLendingTransferredAmountCurrency, ')
          ..write(
              'securityLendingTransferredAmountValue: $securityLendingTransferredAmountValue, ')
          ..write('securityLendingMonth: $securityLendingMonth, ')
          ..write(
              'wealthManagementManagedPortfolioId: $wealthManagementManagedPortfolioId, ')
          ..write('wealthManagementProductName: $wealthManagementProductName, ')
          ..write(
              'wealthManagementProductCategory: $wealthManagementProductCategory, ')
          ..write('wealthManagementAccountName: $wealthManagementAccountName, ')
          ..write(
              'wealthManagementEstimatedExecutionDeadline: $wealthManagementEstimatedExecutionDeadline, ')
          ..write(
              'wealthManagementTrackerSteps: $wealthManagementTrackerSteps, ')
          ..write('acquirerInstrumentName: $acquirerInstrumentName, ')
          ..write('acquirerInstrumentSymbol: $acquirerInstrumentSymbol, ')
          ..write(
              'acquirerInstrumentExchangeId: $acquirerInstrumentExchangeId, ')
          ..write('acquirerInstrumentImageUrl: $acquirerInstrumentImageUrl, ')
          ..write('acquireeInstrumentName: $acquireeInstrumentName, ')
          ..write('acquireeInstrumentSymbol: $acquireeInstrumentSymbol, ')
          ..write(
              'acquireeInstrumentExchangeId: $acquireeInstrumentExchangeId, ')
          ..write('acquireeInstrumentImageUrl: $acquireeInstrumentImageUrl, ')
          ..write(
              'brokerAcquisitionAccountAmountValue: $brokerAcquisitionAccountAmountValue, ')
          ..write(
              'brokerAcquisitionAccountAmountCurrency: $brokerAcquisitionAccountAmountCurrency, ')
          ..write(
              'brokerAcquisitionPositionDelta: $brokerAcquisitionPositionDelta, ')
          ..write('ipoCompanyName: $ipoCompanyName, ')
          ..write('ipoSubscriptionNumber: $ipoSubscriptionNumber, ')
          ..write('ipoLeverageValue: $ipoLeverageValue, ')
          ..write('ipoLeverageCurrency: $ipoLeverageCurrency, ')
          ..write('ipoAmountValue: $ipoAmountValue, ')
          ..write('ipoAmountCurrency: $ipoAmountCurrency, ')
          ..write('ipoTotalAmountValue: $ipoTotalAmountValue, ')
          ..write('ipoTotalAmountCurrency: $ipoTotalAmountCurrency, ')
          ..write('ipoCompanyLogo: $ipoCompanyLogo, ')
          ..write('ipoEnhancementValue: $ipoEnhancementValue, ')
          ..write('ipoEnhancementCurrency: $ipoEnhancementCurrency, ')
          ..write('ipoRefundAmountValue: $ipoRefundAmountValue, ')
          ..write('ipoRefundAmountCurrency: $ipoRefundAmountCurrency, ')
          ..write('ipoLeveragePaidValue: $ipoLeveragePaidValue, ')
          ..write('ipoLeveragePaidCurrency: $ipoLeveragePaidCurrency, ')
          ..write('ipoLeverageFeePercentage: $ipoLeverageFeePercentage, ')
          ..write('ipoLeverageFeeValue: $ipoLeverageFeeValue, ')
          ..write('ipoLeverageFeeCurrency: $ipoLeverageFeeCurrency, ')
          ..write('ipoAllocationFeePercentage: $ipoAllocationFeePercentage, ')
          ..write('ipoAllotedFeeMoney: $ipoAllotedFeeMoney, ')
          ..write('ipoAllotedFeeMoneyCurrency: $ipoAllotedFeeMoneyCurrency, ')
          ..write('ipoAllocatedMoney: $ipoAllocatedMoney, ')
          ..write('ipoAllocatedMoneyCurrency: $ipoAllocatedMoneyCurrency, ')
          ..write('dynamicDetails: $dynamicDetails, ')
          ..write('linkedTransactionFeeDetails: $linkedTransactionFeeDetails, ')
          ..write('cashbackCalculations: $cashbackCalculations, ')
          ..write('carbonEmissionInGrams: $carbonEmissionInGrams, ')
          ..write('carbonEmissionInOunces: $carbonEmissionInOunces, ')
          ..write('recurringTransferRuleId: $recurringTransferRuleId')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
        id,
        productType,
        accountId,
        transactionAccountType,
        transactionType,
        transactionIdentifier,
        status,
        referenceNumber,
        transactionMode,
        transactionSubType,
        internalTransactionStatus,
        transactionDateTime,
        debtorName,
        customerId,
        coreBankingIdentifier,
        creditorName,
        description,
        subDescription,
        logoType,
        merchantName,
        transactionImageKey,
        amountValue,
        localAmount,
        exchangeRate,
        availableBalance,
        amountCurrency,
        executionDate,
        transactionCategory,
        tppExternalReferenceId,
        fxFromAmount,
        fxToAmount,
        fxFromAmountCurrency,
        fxToAmountCurrency,
        fxFormattedExchangeRate,
        orderId,
        portfolioId,
        instrumentId,
        createdDateTime,
        orderSide,
        instrumentName,
        instrumentSymbol,
        instrumentExchangeId,
        instrumentImageUrl,
        orderType,
        errorCode,
        errorMessage,
        executedQuantity,
        estimatedQuantity,
        executedAmountValue,
        executedAmountCurrency,
        executedNetAmountValue,
        executedNetAmountCurrency,
        estimatedAmountValue,
        estimatedAmountCurrency,
        estimatedNetAmountValue,
        estimatedNetAmountCurrency,
        commissionAmountValue,
        commissionAmountCurrency,
        estimatedCommissionAmountValue,
        estimatedCommissionAmountCurrency,
        vatValue,
        vatCurrency,
        estimatedVatValue,
        estimatedVatCurrency,
        averagePriceValue,
        averagePriceCurrency,
        estimatedPriceValue,
        estimatedPriceCurrency,
        executedTotalCommissionAmount,
        executedTotalCommissionCurrency,
        estimatedTotalCommissionAmount,
        estimatedTotalCommissionCurrency,
        commissionCalculationBasisPoints,
        commissionCalculationBaseAmount,
        commissionCalculationMinAmount,
        commissionCalculationBaseAmountCurrency,
        commissionCalculationMinAmountCurrency,
        instrumentExchangeCode,
        recurringOrderTemplateId,
        recurringOrderAccountName,
        recurringOrderFrequencyType,
        recurringOrderFrequencyDayOfWeek,
        recurringOrderFrequencyDayOfMonth,
        expirationDate,
        isPaymentProofReady,
        isPaymentTrackingAvailable,
        paymentFeeTotalAmount,
        paymentFeeTotalCurrency,
        paymentTargetCountryCode,
        paymentPhoneNumber,
        paymentWarningMessage,
        paymentFeeTypeDescriptionWio,
        paymentFeeTypeAmountWio,
        paymentFeeTypeCurrencyWio,
        paymentFeeTypeDescriptionWioCorrespondentBank,
        paymentFeeTypeAmountWioCorrespondentBank,
        paymentFeeTypeCurrencyWioCorrespondentBank,
        paymentFeeTypeDescriptionWise,
        paymentFeeTypeAmountWise,
        paymentFeeTypeCurrencyWise,
        internationalTargetCountry,
        internationalCompletedDateTime,
        internationalEstimatedDateTime,
        internationalFeeChargingType,
        internationalSwiftCode,
        internationalAccountNumber,
        internationalPurposeDescription,
        internationalPurposeCode,
        internationalNotes,
        internationalBankName,
        internationalTargetAmount,
        internationalTargetAmountCurrency,
        internationalTransferFee,
        internationalTransferFeeCurrency,
        fabExpirationDate,
        fabBankName,
        fabTotalFeeAmount,
        fabTotalFeeAmountCurrency,
        fabUpdatedDate,
        fabCancellationReason,
        fabChequeNumber,
        fabChequeDate,
        fabFrontImageUrl,
        localPurposeDescription,
        localPurposeCode,
        localNote,
        localTargetCountry,
        localCompletedDateTime,
        localEstimatedDateTime,
        localAccountNumber,
        localSwiftCode,
        localBankName,
        localTargetAmount,
        localTargetAmountCurrency,
        localTransferSource,
        localTransferApplicationId,
        localRecipientName,
        npssExpirationDate,
        npssNotes,
        npssRtpInitiationDate,
        npssShowInPendingRtps,
        npssShowRtpResendButton,
        npssOriginalTransactionReference,
        npssOriginalTransactionReason,
        npssOriginalTransactionDeeplink,
        token,
        accNumberLast4Digits,
        cardMaskedPan,
        merchantCountryCode,
        originalCardTransactionCurrency,
        originalCardTransactionAmount,
        earnedCashback,
        cashBackPercentage,
        cashBackTransactionCurrency,
        dividendTaxAmountValue,
        dividendTaxAmountCurrency,
        dividendAmountValue,
        dividendAmountCurrency,
        netDividendAmountValue,
        netDividendAmountCurrency,
        amountPerInstrumentValue,
        amountPerInstrumentCurrency,
        instrumentQuantity,
        brokerDividendTaxType,
        dividendTaxRate,
        securityLendingRebateAmountValue,
        securityLendingRebateAmountCurrency,
        securityLendingRebateTaxAmountCurrency,
        securityLendingRebateTaxAmountValue,
        securityLendingTransferredAmountCurrency,
        securityLendingTransferredAmountValue,
        securityLendingMonth,
        wealthManagementManagedPortfolioId,
        wealthManagementProductName,
        wealthManagementProductCategory,
        wealthManagementAccountName,
        wealthManagementEstimatedExecutionDeadline,
        wealthManagementTrackerSteps,
        acquirerInstrumentName,
        acquirerInstrumentSymbol,
        acquirerInstrumentExchangeId,
        acquirerInstrumentImageUrl,
        acquireeInstrumentName,
        acquireeInstrumentSymbol,
        acquireeInstrumentExchangeId,
        acquireeInstrumentImageUrl,
        brokerAcquisitionAccountAmountValue,
        brokerAcquisitionAccountAmountCurrency,
        brokerAcquisitionPositionDelta,
        ipoCompanyName,
        ipoSubscriptionNumber,
        ipoLeverageValue,
        ipoLeverageCurrency,
        ipoAmountValue,
        ipoAmountCurrency,
        ipoTotalAmountValue,
        ipoTotalAmountCurrency,
        ipoCompanyLogo,
        ipoEnhancementValue,
        ipoEnhancementCurrency,
        ipoRefundAmountValue,
        ipoRefundAmountCurrency,
        ipoLeveragePaidValue,
        ipoLeveragePaidCurrency,
        ipoLeverageFeePercentage,
        ipoLeverageFeeValue,
        ipoLeverageFeeCurrency,
        ipoAllocationFeePercentage,
        ipoAllotedFeeMoney,
        ipoAllotedFeeMoneyCurrency,
        ipoAllocatedMoney,
        ipoAllocatedMoneyCurrency,
        dynamicDetails,
        linkedTransactionFeeDetails,
        cashbackCalculations,
        carbonEmissionInGrams,
        carbonEmissionInOunces,
        recurringTransferRuleId
      ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is TransactionsData &&
          other.id == this.id &&
          other.productType == this.productType &&
          other.accountId == this.accountId &&
          other.transactionAccountType == this.transactionAccountType &&
          other.transactionType == this.transactionType &&
          other.transactionIdentifier == this.transactionIdentifier &&
          other.status == this.status &&
          other.referenceNumber == this.referenceNumber &&
          other.transactionMode == this.transactionMode &&
          other.transactionSubType == this.transactionSubType &&
          other.internalTransactionStatus == this.internalTransactionStatus &&
          other.transactionDateTime == this.transactionDateTime &&
          other.debtorName == this.debtorName &&
          other.customerId == this.customerId &&
          other.coreBankingIdentifier == this.coreBankingIdentifier &&
          other.creditorName == this.creditorName &&
          other.description == this.description &&
          other.subDescription == this.subDescription &&
          other.logoType == this.logoType &&
          other.merchantName == this.merchantName &&
          other.transactionImageKey == this.transactionImageKey &&
          other.amountValue == this.amountValue &&
          other.localAmount == this.localAmount &&
          other.exchangeRate == this.exchangeRate &&
          other.availableBalance == this.availableBalance &&
          other.amountCurrency == this.amountCurrency &&
          other.executionDate == this.executionDate &&
          other.transactionCategory == this.transactionCategory &&
          other.tppExternalReferenceId == this.tppExternalReferenceId &&
          other.fxFromAmount == this.fxFromAmount &&
          other.fxToAmount == this.fxToAmount &&
          other.fxFromAmountCurrency == this.fxFromAmountCurrency &&
          other.fxToAmountCurrency == this.fxToAmountCurrency &&
          other.fxFormattedExchangeRate == this.fxFormattedExchangeRate &&
          other.orderId == this.orderId &&
          other.portfolioId == this.portfolioId &&
          other.instrumentId == this.instrumentId &&
          other.createdDateTime == this.createdDateTime &&
          other.orderSide == this.orderSide &&
          other.instrumentName == this.instrumentName &&
          other.instrumentSymbol == this.instrumentSymbol &&
          other.instrumentExchangeId == this.instrumentExchangeId &&
          other.instrumentImageUrl == this.instrumentImageUrl &&
          other.orderType == this.orderType &&
          other.errorCode == this.errorCode &&
          other.errorMessage == this.errorMessage &&
          other.executedQuantity == this.executedQuantity &&
          other.estimatedQuantity == this.estimatedQuantity &&
          other.executedAmountValue == this.executedAmountValue &&
          other.executedAmountCurrency == this.executedAmountCurrency &&
          other.executedNetAmountValue == this.executedNetAmountValue &&
          other.executedNetAmountCurrency == this.executedNetAmountCurrency &&
          other.estimatedAmountValue == this.estimatedAmountValue &&
          other.estimatedAmountCurrency == this.estimatedAmountCurrency &&
          other.estimatedNetAmountValue == this.estimatedNetAmountValue &&
          other.estimatedNetAmountCurrency == this.estimatedNetAmountCurrency &&
          other.commissionAmountValue == this.commissionAmountValue &&
          other.commissionAmountCurrency == this.commissionAmountCurrency &&
          other.estimatedCommissionAmountValue ==
              this.estimatedCommissionAmountValue &&
          other.estimatedCommissionAmountCurrency ==
              this.estimatedCommissionAmountCurrency &&
          other.vatValue == this.vatValue &&
          other.vatCurrency == this.vatCurrency &&
          other.estimatedVatValue == this.estimatedVatValue &&
          other.estimatedVatCurrency == this.estimatedVatCurrency &&
          other.averagePriceValue == this.averagePriceValue &&
          other.averagePriceCurrency == this.averagePriceCurrency &&
          other.estimatedPriceValue == this.estimatedPriceValue &&
          other.estimatedPriceCurrency == this.estimatedPriceCurrency &&
          other.executedTotalCommissionAmount ==
              this.executedTotalCommissionAmount &&
          other.executedTotalCommissionCurrency ==
              this.executedTotalCommissionCurrency &&
          other.estimatedTotalCommissionAmount ==
              this.estimatedTotalCommissionAmount &&
          other.estimatedTotalCommissionCurrency ==
              this.estimatedTotalCommissionCurrency &&
          other.commissionCalculationBasisPoints ==
              this.commissionCalculationBasisPoints &&
          other.commissionCalculationBaseAmount ==
              this.commissionCalculationBaseAmount &&
          other.commissionCalculationMinAmount ==
              this.commissionCalculationMinAmount &&
          other.commissionCalculationBaseAmountCurrency ==
              this.commissionCalculationBaseAmountCurrency &&
          other.commissionCalculationMinAmountCurrency ==
              this.commissionCalculationMinAmountCurrency &&
          other.instrumentExchangeCode == this.instrumentExchangeCode &&
          other.recurringOrderTemplateId == this.recurringOrderTemplateId &&
          other.recurringOrderAccountName == this.recurringOrderAccountName &&
          other.recurringOrderFrequencyType ==
              this.recurringOrderFrequencyType &&
          other.recurringOrderFrequencyDayOfWeek ==
              this.recurringOrderFrequencyDayOfWeek &&
          other.recurringOrderFrequencyDayOfMonth ==
              this.recurringOrderFrequencyDayOfMonth &&
          other.expirationDate == this.expirationDate &&
          other.isPaymentProofReady == this.isPaymentProofReady &&
          other.isPaymentTrackingAvailable == this.isPaymentTrackingAvailable &&
          other.paymentFeeTotalAmount == this.paymentFeeTotalAmount &&
          other.paymentFeeTotalCurrency == this.paymentFeeTotalCurrency &&
          other.paymentTargetCountryCode == this.paymentTargetCountryCode &&
          other.paymentPhoneNumber == this.paymentPhoneNumber &&
          other.paymentWarningMessage == this.paymentWarningMessage &&
          other.paymentFeeTypeDescriptionWio ==
              this.paymentFeeTypeDescriptionWio &&
          other.paymentFeeTypeAmountWio == this.paymentFeeTypeAmountWio &&
          other.paymentFeeTypeCurrencyWio == this.paymentFeeTypeCurrencyWio &&
          other.paymentFeeTypeDescriptionWioCorrespondentBank ==
              this.paymentFeeTypeDescriptionWioCorrespondentBank &&
          other.paymentFeeTypeAmountWioCorrespondentBank ==
              this.paymentFeeTypeAmountWioCorrespondentBank &&
          other.paymentFeeTypeCurrencyWioCorrespondentBank ==
              this.paymentFeeTypeCurrencyWioCorrespondentBank &&
          other.paymentFeeTypeDescriptionWise ==
              this.paymentFeeTypeDescriptionWise &&
          other.paymentFeeTypeAmountWise == this.paymentFeeTypeAmountWise &&
          other.paymentFeeTypeCurrencyWise == this.paymentFeeTypeCurrencyWise &&
          other.internationalTargetCountry == this.internationalTargetCountry &&
          other.internationalCompletedDateTime ==
              this.internationalCompletedDateTime &&
          other.internationalEstimatedDateTime ==
              this.internationalEstimatedDateTime &&
          other.internationalFeeChargingType ==
              this.internationalFeeChargingType &&
          other.internationalSwiftCode == this.internationalSwiftCode &&
          other.internationalAccountNumber == this.internationalAccountNumber &&
          other.internationalPurposeDescription ==
              this.internationalPurposeDescription &&
          other.internationalPurposeCode == this.internationalPurposeCode &&
          other.internationalNotes == this.internationalNotes &&
          other.internationalBankName == this.internationalBankName &&
          other.internationalTargetAmount == this.internationalTargetAmount &&
          other.internationalTargetAmountCurrency ==
              this.internationalTargetAmountCurrency &&
          other.internationalTransferFee == this.internationalTransferFee &&
          other.internationalTransferFeeCurrency ==
              this.internationalTransferFeeCurrency &&
          other.fabExpirationDate == this.fabExpirationDate &&
          other.fabBankName == this.fabBankName &&
          other.fabTotalFeeAmount == this.fabTotalFeeAmount &&
          other.fabTotalFeeAmountCurrency == this.fabTotalFeeAmountCurrency &&
          other.fabUpdatedDate == this.fabUpdatedDate &&
          other.fabCancellationReason == this.fabCancellationReason &&
          other.fabChequeNumber == this.fabChequeNumber &&
          other.fabChequeDate == this.fabChequeDate &&
          other.fabFrontImageUrl == this.fabFrontImageUrl &&
          other.localPurposeDescription == this.localPurposeDescription &&
          other.localPurposeCode == this.localPurposeCode &&
          other.localNote == this.localNote &&
          other.localTargetCountry == this.localTargetCountry &&
          other.localCompletedDateTime == this.localCompletedDateTime &&
          other.localEstimatedDateTime == this.localEstimatedDateTime &&
          other.localAccountNumber == this.localAccountNumber &&
          other.localSwiftCode == this.localSwiftCode &&
          other.localBankName == this.localBankName &&
          other.localTargetAmount == this.localTargetAmount &&
          other.localTargetAmountCurrency == this.localTargetAmountCurrency &&
          other.localTransferSource == this.localTransferSource &&
          other.localTransferApplicationId == this.localTransferApplicationId &&
          other.localRecipientName == this.localRecipientName &&
          other.npssExpirationDate == this.npssExpirationDate &&
          other.npssNotes == this.npssNotes &&
          other.npssRtpInitiationDate == this.npssRtpInitiationDate &&
          other.npssShowInPendingRtps == this.npssShowInPendingRtps &&
          other.npssShowRtpResendButton == this.npssShowRtpResendButton &&
          other.npssOriginalTransactionReference ==
              this.npssOriginalTransactionReference &&
          other.npssOriginalTransactionReason ==
              this.npssOriginalTransactionReason &&
          other.npssOriginalTransactionDeeplink ==
              this.npssOriginalTransactionDeeplink &&
          other.token == this.token &&
          other.accNumberLast4Digits == this.accNumberLast4Digits &&
          other.cardMaskedPan == this.cardMaskedPan &&
          other.merchantCountryCode == this.merchantCountryCode &&
          other.originalCardTransactionCurrency ==
              this.originalCardTransactionCurrency &&
          other.originalCardTransactionAmount ==
              this.originalCardTransactionAmount &&
          other.earnedCashback == this.earnedCashback &&
          other.cashBackPercentage == this.cashBackPercentage &&
          other.cashBackTransactionCurrency ==
              this.cashBackTransactionCurrency &&
          other.dividendTaxAmountValue == this.dividendTaxAmountValue &&
          other.dividendTaxAmountCurrency == this.dividendTaxAmountCurrency &&
          other.dividendAmountValue == this.dividendAmountValue &&
          other.dividendAmountCurrency == this.dividendAmountCurrency &&
          other.netDividendAmountValue == this.netDividendAmountValue &&
          other.netDividendAmountCurrency == this.netDividendAmountCurrency &&
          other.amountPerInstrumentValue == this.amountPerInstrumentValue &&
          other.amountPerInstrumentCurrency ==
              this.amountPerInstrumentCurrency &&
          other.instrumentQuantity == this.instrumentQuantity &&
          other.brokerDividendTaxType == this.brokerDividendTaxType &&
          other.dividendTaxRate == this.dividendTaxRate &&
          other.securityLendingRebateAmountValue ==
              this.securityLendingRebateAmountValue &&
          other.securityLendingRebateAmountCurrency ==
              this.securityLendingRebateAmountCurrency &&
          other.securityLendingRebateTaxAmountCurrency ==
              this.securityLendingRebateTaxAmountCurrency &&
          other.securityLendingRebateTaxAmountValue ==
              this.securityLendingRebateTaxAmountValue &&
          other.securityLendingTransferredAmountCurrency ==
              this.securityLendingTransferredAmountCurrency &&
          other.securityLendingTransferredAmountValue ==
              this.securityLendingTransferredAmountValue &&
          other.securityLendingMonth == this.securityLendingMonth &&
          other.wealthManagementManagedPortfolioId ==
              this.wealthManagementManagedPortfolioId &&
          other.wealthManagementProductName ==
              this.wealthManagementProductName &&
          other.wealthManagementProductCategory ==
              this.wealthManagementProductCategory &&
          other.wealthManagementAccountName ==
              this.wealthManagementAccountName &&
          other.wealthManagementEstimatedExecutionDeadline ==
              this.wealthManagementEstimatedExecutionDeadline &&
          other.wealthManagementTrackerSteps ==
              this.wealthManagementTrackerSteps &&
          other.acquirerInstrumentName == this.acquirerInstrumentName &&
          other.acquirerInstrumentSymbol == this.acquirerInstrumentSymbol &&
          other.acquirerInstrumentExchangeId ==
              this.acquirerInstrumentExchangeId &&
          other.acquirerInstrumentImageUrl == this.acquirerInstrumentImageUrl &&
          other.acquireeInstrumentName == this.acquireeInstrumentName &&
          other.acquireeInstrumentSymbol == this.acquireeInstrumentSymbol &&
          other.acquireeInstrumentExchangeId ==
              this.acquireeInstrumentExchangeId &&
          other.acquireeInstrumentImageUrl == this.acquireeInstrumentImageUrl &&
          other.brokerAcquisitionAccountAmountValue ==
              this.brokerAcquisitionAccountAmountValue &&
          other.brokerAcquisitionAccountAmountCurrency ==
              this.brokerAcquisitionAccountAmountCurrency &&
          other.brokerAcquisitionPositionDelta ==
              this.brokerAcquisitionPositionDelta &&
          other.ipoCompanyName == this.ipoCompanyName &&
          other.ipoSubscriptionNumber == this.ipoSubscriptionNumber &&
          other.ipoLeverageValue == this.ipoLeverageValue &&
          other.ipoLeverageCurrency == this.ipoLeverageCurrency &&
          other.ipoAmountValue == this.ipoAmountValue &&
          other.ipoAmountCurrency == this.ipoAmountCurrency &&
          other.ipoTotalAmountValue == this.ipoTotalAmountValue &&
          other.ipoTotalAmountCurrency == this.ipoTotalAmountCurrency &&
          other.ipoCompanyLogo == this.ipoCompanyLogo &&
          other.ipoEnhancementValue == this.ipoEnhancementValue &&
          other.ipoEnhancementCurrency == this.ipoEnhancementCurrency &&
          other.ipoRefundAmountValue == this.ipoRefundAmountValue &&
          other.ipoRefundAmountCurrency == this.ipoRefundAmountCurrency &&
          other.ipoLeveragePaidValue == this.ipoLeveragePaidValue &&
          other.ipoLeveragePaidCurrency == this.ipoLeveragePaidCurrency &&
          other.ipoLeverageFeePercentage == this.ipoLeverageFeePercentage &&
          other.ipoLeverageFeeValue == this.ipoLeverageFeeValue &&
          other.ipoLeverageFeeCurrency == this.ipoLeverageFeeCurrency &&
          other.ipoAllocationFeePercentage == this.ipoAllocationFeePercentage &&
          other.ipoAllotedFeeMoney == this.ipoAllotedFeeMoney &&
          other.ipoAllotedFeeMoneyCurrency == this.ipoAllotedFeeMoneyCurrency &&
          other.ipoAllocatedMoney == this.ipoAllocatedMoney &&
          other.ipoAllocatedMoneyCurrency == this.ipoAllocatedMoneyCurrency &&
          other.dynamicDetails == this.dynamicDetails &&
          other.linkedTransactionFeeDetails ==
              this.linkedTransactionFeeDetails &&
          other.cashbackCalculations == this.cashbackCalculations &&
          other.carbonEmissionInGrams == this.carbonEmissionInGrams &&
          other.carbonEmissionInOunces == this.carbonEmissionInOunces &&
          other.recurringTransferRuleId == this.recurringTransferRuleId);
}

class TransactionsCompanion extends UpdateCompanion<TransactionsData> {
  final Value<String> id;
  final Value<String> productType;
  final Value<String> accountId;
  final Value<String?> transactionAccountType;
  final Value<String> transactionType;
  final Value<String> transactionIdentifier;
  final Value<String> status;
  final Value<String?> referenceNumber;
  final Value<String> transactionMode;
  final Value<String?> transactionSubType;
  final Value<String?> internalTransactionStatus;
  final Value<DateTime?> transactionDateTime;
  final Value<String?> debtorName;
  final Value<String?> customerId;
  final Value<String?> coreBankingIdentifier;
  final Value<String?> creditorName;
  final Value<String?> description;
  final Value<String?> subDescription;
  final Value<String?> logoType;
  final Value<String?> merchantName;
  final Value<String?> transactionImageKey;
  final Value<double> amountValue;
  final Value<double?> localAmount;
  final Value<double?> exchangeRate;
  final Value<double?> availableBalance;
  final Value<String> amountCurrency;
  final Value<DateTime?> executionDate;
  final Value<String> transactionCategory;
  final Value<String?> tppExternalReferenceId;
  final Value<double?> fxFromAmount;
  final Value<double?> fxToAmount;
  final Value<String?> fxFromAmountCurrency;
  final Value<String?> fxToAmountCurrency;
  final Value<String?> fxFormattedExchangeRate;
  final Value<String?> orderId;
  final Value<String?> portfolioId;
  final Value<String?> instrumentId;
  final Value<DateTime?> createdDateTime;
  final Value<String?> orderSide;
  final Value<String?> instrumentName;
  final Value<String?> instrumentSymbol;
  final Value<String?> instrumentExchangeId;
  final Value<String?> instrumentImageUrl;
  final Value<String?> orderType;
  final Value<int?> errorCode;
  final Value<String?> errorMessage;
  final Value<double?> executedQuantity;
  final Value<double?> estimatedQuantity;
  final Value<double?> executedAmountValue;
  final Value<String?> executedAmountCurrency;
  final Value<double?> executedNetAmountValue;
  final Value<String?> executedNetAmountCurrency;
  final Value<double?> estimatedAmountValue;
  final Value<String?> estimatedAmountCurrency;
  final Value<double?> estimatedNetAmountValue;
  final Value<String?> estimatedNetAmountCurrency;
  final Value<double?> commissionAmountValue;
  final Value<String?> commissionAmountCurrency;
  final Value<double?> estimatedCommissionAmountValue;
  final Value<String?> estimatedCommissionAmountCurrency;
  final Value<double?> vatValue;
  final Value<String?> vatCurrency;
  final Value<double?> estimatedVatValue;
  final Value<String?> estimatedVatCurrency;
  final Value<double?> averagePriceValue;
  final Value<String?> averagePriceCurrency;
  final Value<double?> estimatedPriceValue;
  final Value<String?> estimatedPriceCurrency;
  final Value<double?> executedTotalCommissionAmount;
  final Value<String?> executedTotalCommissionCurrency;
  final Value<double?> estimatedTotalCommissionAmount;
  final Value<String?> estimatedTotalCommissionCurrency;
  final Value<double?> commissionCalculationBasisPoints;
  final Value<double?> commissionCalculationBaseAmount;
  final Value<double?> commissionCalculationMinAmount;
  final Value<String?> commissionCalculationBaseAmountCurrency;
  final Value<String?> commissionCalculationMinAmountCurrency;
  final Value<String?> instrumentExchangeCode;
  final Value<String?> recurringOrderTemplateId;
  final Value<String?> recurringOrderAccountName;
  final Value<String?> recurringOrderFrequencyType;
  final Value<String?> recurringOrderFrequencyDayOfWeek;
  final Value<int?> recurringOrderFrequencyDayOfMonth;
  final Value<DateTime?> expirationDate;
  final Value<bool> isPaymentProofReady;
  final Value<bool?> isPaymentTrackingAvailable;
  final Value<double?> paymentFeeTotalAmount;
  final Value<String?> paymentFeeTotalCurrency;
  final Value<String?> paymentTargetCountryCode;
  final Value<String?> paymentPhoneNumber;
  final Value<String?> paymentWarningMessage;
  final Value<String?> paymentFeeTypeDescriptionWio;
  final Value<double?> paymentFeeTypeAmountWio;
  final Value<String?> paymentFeeTypeCurrencyWio;
  final Value<String?> paymentFeeTypeDescriptionWioCorrespondentBank;
  final Value<double?> paymentFeeTypeAmountWioCorrespondentBank;
  final Value<String?> paymentFeeTypeCurrencyWioCorrespondentBank;
  final Value<String?> paymentFeeTypeDescriptionWise;
  final Value<double?> paymentFeeTypeAmountWise;
  final Value<String?> paymentFeeTypeCurrencyWise;
  final Value<String?> internationalTargetCountry;
  final Value<DateTime?> internationalCompletedDateTime;
  final Value<String?> internationalEstimatedDateTime;
  final Value<String?> internationalFeeChargingType;
  final Value<String?> internationalSwiftCode;
  final Value<String?> internationalAccountNumber;
  final Value<String?> internationalPurposeDescription;
  final Value<String?> internationalPurposeCode;
  final Value<String?> internationalNotes;
  final Value<String?> internationalBankName;
  final Value<double?> internationalTargetAmount;
  final Value<String?> internationalTargetAmountCurrency;
  final Value<double?> internationalTransferFee;
  final Value<String?> internationalTransferFeeCurrency;
  final Value<DateTime?> fabExpirationDate;
  final Value<String?> fabBankName;
  final Value<double?> fabTotalFeeAmount;
  final Value<String?> fabTotalFeeAmountCurrency;
  final Value<DateTime?> fabUpdatedDate;
  final Value<String?> fabCancellationReason;
  final Value<String?> fabChequeNumber;
  final Value<DateTime?> fabChequeDate;
  final Value<String?> fabFrontImageUrl;
  final Value<String?> localPurposeDescription;
  final Value<String?> localPurposeCode;
  final Value<String?> localNote;
  final Value<String?> localTargetCountry;
  final Value<DateTime?> localCompletedDateTime;
  final Value<DateTime?> localEstimatedDateTime;
  final Value<String?> localAccountNumber;
  final Value<String?> localSwiftCode;
  final Value<String?> localBankName;
  final Value<double?> localTargetAmount;
  final Value<String?> localTargetAmountCurrency;
  final Value<String?> localTransferSource;
  final Value<String?> localTransferApplicationId;
  final Value<String?> localRecipientName;
  final Value<DateTime?> npssExpirationDate;
  final Value<String?> npssNotes;
  final Value<DateTime?> npssRtpInitiationDate;
  final Value<bool?> npssShowInPendingRtps;
  final Value<bool?> npssShowRtpResendButton;
  final Value<String?> npssOriginalTransactionReference;
  final Value<String?> npssOriginalTransactionReason;
  final Value<String?> npssOriginalTransactionDeeplink;
  final Value<String?> token;
  final Value<String?> accNumberLast4Digits;
  final Value<String?> cardMaskedPan;
  final Value<String?> merchantCountryCode;
  final Value<String?> originalCardTransactionCurrency;
  final Value<double?> originalCardTransactionAmount;
  final Value<double?> earnedCashback;
  final Value<double?> cashBackPercentage;
  final Value<String?> cashBackTransactionCurrency;
  final Value<double?> dividendTaxAmountValue;
  final Value<String?> dividendTaxAmountCurrency;
  final Value<double?> dividendAmountValue;
  final Value<String?> dividendAmountCurrency;
  final Value<double?> netDividendAmountValue;
  final Value<String?> netDividendAmountCurrency;
  final Value<double?> amountPerInstrumentValue;
  final Value<String?> amountPerInstrumentCurrency;
  final Value<double?> instrumentQuantity;
  final Value<String?> brokerDividendTaxType;
  final Value<double?> dividendTaxRate;
  final Value<double?> securityLendingRebateAmountValue;
  final Value<String?> securityLendingRebateAmountCurrency;
  final Value<String?> securityLendingRebateTaxAmountCurrency;
  final Value<double?> securityLendingRebateTaxAmountValue;
  final Value<String?> securityLendingTransferredAmountCurrency;
  final Value<double?> securityLendingTransferredAmountValue;
  final Value<int?> securityLendingMonth;
  final Value<String?> wealthManagementManagedPortfolioId;
  final Value<String?> wealthManagementProductName;
  final Value<String?> wealthManagementProductCategory;
  final Value<String?> wealthManagementAccountName;
  final Value<String?> wealthManagementEstimatedExecutionDeadline;
  final Value<String?> wealthManagementTrackerSteps;
  final Value<String?> acquirerInstrumentName;
  final Value<String?> acquirerInstrumentSymbol;
  final Value<String?> acquirerInstrumentExchangeId;
  final Value<String?> acquirerInstrumentImageUrl;
  final Value<String?> acquireeInstrumentName;
  final Value<String?> acquireeInstrumentSymbol;
  final Value<String?> acquireeInstrumentExchangeId;
  final Value<String?> acquireeInstrumentImageUrl;
  final Value<double?> brokerAcquisitionAccountAmountValue;
  final Value<String?> brokerAcquisitionAccountAmountCurrency;
  final Value<double?> brokerAcquisitionPositionDelta;
  final Value<String?> ipoCompanyName;
  final Value<String?> ipoSubscriptionNumber;
  final Value<double?> ipoLeverageValue;
  final Value<String?> ipoLeverageCurrency;
  final Value<double?> ipoAmountValue;
  final Value<String?> ipoAmountCurrency;
  final Value<double?> ipoTotalAmountValue;
  final Value<String?> ipoTotalAmountCurrency;
  final Value<String?> ipoCompanyLogo;
  final Value<double?> ipoEnhancementValue;
  final Value<String?> ipoEnhancementCurrency;
  final Value<double?> ipoRefundAmountValue;
  final Value<String?> ipoRefundAmountCurrency;
  final Value<double?> ipoLeveragePaidValue;
  final Value<String?> ipoLeveragePaidCurrency;
  final Value<double?> ipoLeverageFeePercentage;
  final Value<double?> ipoLeverageFeeValue;
  final Value<String?> ipoLeverageFeeCurrency;
  final Value<double?> ipoAllocationFeePercentage;
  final Value<double?> ipoAllotedFeeMoney;
  final Value<String?> ipoAllotedFeeMoneyCurrency;
  final Value<double?> ipoAllocatedMoney;
  final Value<String?> ipoAllocatedMoneyCurrency;
  final Value<String> dynamicDetails;
  final Value<String> linkedTransactionFeeDetails;
  final Value<String?> cashbackCalculations;
  final Value<double?> carbonEmissionInGrams;
  final Value<double?> carbonEmissionInOunces;
  final Value<String?> recurringTransferRuleId;
  final Value<int> rowid;
  const TransactionsCompanion({
    this.id = const Value.absent(),
    this.productType = const Value.absent(),
    this.accountId = const Value.absent(),
    this.transactionAccountType = const Value.absent(),
    this.transactionType = const Value.absent(),
    this.transactionIdentifier = const Value.absent(),
    this.status = const Value.absent(),
    this.referenceNumber = const Value.absent(),
    this.transactionMode = const Value.absent(),
    this.transactionSubType = const Value.absent(),
    this.internalTransactionStatus = const Value.absent(),
    this.transactionDateTime = const Value.absent(),
    this.debtorName = const Value.absent(),
    this.customerId = const Value.absent(),
    this.coreBankingIdentifier = const Value.absent(),
    this.creditorName = const Value.absent(),
    this.description = const Value.absent(),
    this.subDescription = const Value.absent(),
    this.logoType = const Value.absent(),
    this.merchantName = const Value.absent(),
    this.transactionImageKey = const Value.absent(),
    this.amountValue = const Value.absent(),
    this.localAmount = const Value.absent(),
    this.exchangeRate = const Value.absent(),
    this.availableBalance = const Value.absent(),
    this.amountCurrency = const Value.absent(),
    this.executionDate = const Value.absent(),
    this.transactionCategory = const Value.absent(),
    this.tppExternalReferenceId = const Value.absent(),
    this.fxFromAmount = const Value.absent(),
    this.fxToAmount = const Value.absent(),
    this.fxFromAmountCurrency = const Value.absent(),
    this.fxToAmountCurrency = const Value.absent(),
    this.fxFormattedExchangeRate = const Value.absent(),
    this.orderId = const Value.absent(),
    this.portfolioId = const Value.absent(),
    this.instrumentId = const Value.absent(),
    this.createdDateTime = const Value.absent(),
    this.orderSide = const Value.absent(),
    this.instrumentName = const Value.absent(),
    this.instrumentSymbol = const Value.absent(),
    this.instrumentExchangeId = const Value.absent(),
    this.instrumentImageUrl = const Value.absent(),
    this.orderType = const Value.absent(),
    this.errorCode = const Value.absent(),
    this.errorMessage = const Value.absent(),
    this.executedQuantity = const Value.absent(),
    this.estimatedQuantity = const Value.absent(),
    this.executedAmountValue = const Value.absent(),
    this.executedAmountCurrency = const Value.absent(),
    this.executedNetAmountValue = const Value.absent(),
    this.executedNetAmountCurrency = const Value.absent(),
    this.estimatedAmountValue = const Value.absent(),
    this.estimatedAmountCurrency = const Value.absent(),
    this.estimatedNetAmountValue = const Value.absent(),
    this.estimatedNetAmountCurrency = const Value.absent(),
    this.commissionAmountValue = const Value.absent(),
    this.commissionAmountCurrency = const Value.absent(),
    this.estimatedCommissionAmountValue = const Value.absent(),
    this.estimatedCommissionAmountCurrency = const Value.absent(),
    this.vatValue = const Value.absent(),
    this.vatCurrency = const Value.absent(),
    this.estimatedVatValue = const Value.absent(),
    this.estimatedVatCurrency = const Value.absent(),
    this.averagePriceValue = const Value.absent(),
    this.averagePriceCurrency = const Value.absent(),
    this.estimatedPriceValue = const Value.absent(),
    this.estimatedPriceCurrency = const Value.absent(),
    this.executedTotalCommissionAmount = const Value.absent(),
    this.executedTotalCommissionCurrency = const Value.absent(),
    this.estimatedTotalCommissionAmount = const Value.absent(),
    this.estimatedTotalCommissionCurrency = const Value.absent(),
    this.commissionCalculationBasisPoints = const Value.absent(),
    this.commissionCalculationBaseAmount = const Value.absent(),
    this.commissionCalculationMinAmount = const Value.absent(),
    this.commissionCalculationBaseAmountCurrency = const Value.absent(),
    this.commissionCalculationMinAmountCurrency = const Value.absent(),
    this.instrumentExchangeCode = const Value.absent(),
    this.recurringOrderTemplateId = const Value.absent(),
    this.recurringOrderAccountName = const Value.absent(),
    this.recurringOrderFrequencyType = const Value.absent(),
    this.recurringOrderFrequencyDayOfWeek = const Value.absent(),
    this.recurringOrderFrequencyDayOfMonth = const Value.absent(),
    this.expirationDate = const Value.absent(),
    this.isPaymentProofReady = const Value.absent(),
    this.isPaymentTrackingAvailable = const Value.absent(),
    this.paymentFeeTotalAmount = const Value.absent(),
    this.paymentFeeTotalCurrency = const Value.absent(),
    this.paymentTargetCountryCode = const Value.absent(),
    this.paymentPhoneNumber = const Value.absent(),
    this.paymentWarningMessage = const Value.absent(),
    this.paymentFeeTypeDescriptionWio = const Value.absent(),
    this.paymentFeeTypeAmountWio = const Value.absent(),
    this.paymentFeeTypeCurrencyWio = const Value.absent(),
    this.paymentFeeTypeDescriptionWioCorrespondentBank = const Value.absent(),
    this.paymentFeeTypeAmountWioCorrespondentBank = const Value.absent(),
    this.paymentFeeTypeCurrencyWioCorrespondentBank = const Value.absent(),
    this.paymentFeeTypeDescriptionWise = const Value.absent(),
    this.paymentFeeTypeAmountWise = const Value.absent(),
    this.paymentFeeTypeCurrencyWise = const Value.absent(),
    this.internationalTargetCountry = const Value.absent(),
    this.internationalCompletedDateTime = const Value.absent(),
    this.internationalEstimatedDateTime = const Value.absent(),
    this.internationalFeeChargingType = const Value.absent(),
    this.internationalSwiftCode = const Value.absent(),
    this.internationalAccountNumber = const Value.absent(),
    this.internationalPurposeDescription = const Value.absent(),
    this.internationalPurposeCode = const Value.absent(),
    this.internationalNotes = const Value.absent(),
    this.internationalBankName = const Value.absent(),
    this.internationalTargetAmount = const Value.absent(),
    this.internationalTargetAmountCurrency = const Value.absent(),
    this.internationalTransferFee = const Value.absent(),
    this.internationalTransferFeeCurrency = const Value.absent(),
    this.fabExpirationDate = const Value.absent(),
    this.fabBankName = const Value.absent(),
    this.fabTotalFeeAmount = const Value.absent(),
    this.fabTotalFeeAmountCurrency = const Value.absent(),
    this.fabUpdatedDate = const Value.absent(),
    this.fabCancellationReason = const Value.absent(),
    this.fabChequeNumber = const Value.absent(),
    this.fabChequeDate = const Value.absent(),
    this.fabFrontImageUrl = const Value.absent(),
    this.localPurposeDescription = const Value.absent(),
    this.localPurposeCode = const Value.absent(),
    this.localNote = const Value.absent(),
    this.localTargetCountry = const Value.absent(),
    this.localCompletedDateTime = const Value.absent(),
    this.localEstimatedDateTime = const Value.absent(),
    this.localAccountNumber = const Value.absent(),
    this.localSwiftCode = const Value.absent(),
    this.localBankName = const Value.absent(),
    this.localTargetAmount = const Value.absent(),
    this.localTargetAmountCurrency = const Value.absent(),
    this.localTransferSource = const Value.absent(),
    this.localTransferApplicationId = const Value.absent(),
    this.localRecipientName = const Value.absent(),
    this.npssExpirationDate = const Value.absent(),
    this.npssNotes = const Value.absent(),
    this.npssRtpInitiationDate = const Value.absent(),
    this.npssShowInPendingRtps = const Value.absent(),
    this.npssShowRtpResendButton = const Value.absent(),
    this.npssOriginalTransactionReference = const Value.absent(),
    this.npssOriginalTransactionReason = const Value.absent(),
    this.npssOriginalTransactionDeeplink = const Value.absent(),
    this.token = const Value.absent(),
    this.accNumberLast4Digits = const Value.absent(),
    this.cardMaskedPan = const Value.absent(),
    this.merchantCountryCode = const Value.absent(),
    this.originalCardTransactionCurrency = const Value.absent(),
    this.originalCardTransactionAmount = const Value.absent(),
    this.earnedCashback = const Value.absent(),
    this.cashBackPercentage = const Value.absent(),
    this.cashBackTransactionCurrency = const Value.absent(),
    this.dividendTaxAmountValue = const Value.absent(),
    this.dividendTaxAmountCurrency = const Value.absent(),
    this.dividendAmountValue = const Value.absent(),
    this.dividendAmountCurrency = const Value.absent(),
    this.netDividendAmountValue = const Value.absent(),
    this.netDividendAmountCurrency = const Value.absent(),
    this.amountPerInstrumentValue = const Value.absent(),
    this.amountPerInstrumentCurrency = const Value.absent(),
    this.instrumentQuantity = const Value.absent(),
    this.brokerDividendTaxType = const Value.absent(),
    this.dividendTaxRate = const Value.absent(),
    this.securityLendingRebateAmountValue = const Value.absent(),
    this.securityLendingRebateAmountCurrency = const Value.absent(),
    this.securityLendingRebateTaxAmountCurrency = const Value.absent(),
    this.securityLendingRebateTaxAmountValue = const Value.absent(),
    this.securityLendingTransferredAmountCurrency = const Value.absent(),
    this.securityLendingTransferredAmountValue = const Value.absent(),
    this.securityLendingMonth = const Value.absent(),
    this.wealthManagementManagedPortfolioId = const Value.absent(),
    this.wealthManagementProductName = const Value.absent(),
    this.wealthManagementProductCategory = const Value.absent(),
    this.wealthManagementAccountName = const Value.absent(),
    this.wealthManagementEstimatedExecutionDeadline = const Value.absent(),
    this.wealthManagementTrackerSteps = const Value.absent(),
    this.acquirerInstrumentName = const Value.absent(),
    this.acquirerInstrumentSymbol = const Value.absent(),
    this.acquirerInstrumentExchangeId = const Value.absent(),
    this.acquirerInstrumentImageUrl = const Value.absent(),
    this.acquireeInstrumentName = const Value.absent(),
    this.acquireeInstrumentSymbol = const Value.absent(),
    this.acquireeInstrumentExchangeId = const Value.absent(),
    this.acquireeInstrumentImageUrl = const Value.absent(),
    this.brokerAcquisitionAccountAmountValue = const Value.absent(),
    this.brokerAcquisitionAccountAmountCurrency = const Value.absent(),
    this.brokerAcquisitionPositionDelta = const Value.absent(),
    this.ipoCompanyName = const Value.absent(),
    this.ipoSubscriptionNumber = const Value.absent(),
    this.ipoLeverageValue = const Value.absent(),
    this.ipoLeverageCurrency = const Value.absent(),
    this.ipoAmountValue = const Value.absent(),
    this.ipoAmountCurrency = const Value.absent(),
    this.ipoTotalAmountValue = const Value.absent(),
    this.ipoTotalAmountCurrency = const Value.absent(),
    this.ipoCompanyLogo = const Value.absent(),
    this.ipoEnhancementValue = const Value.absent(),
    this.ipoEnhancementCurrency = const Value.absent(),
    this.ipoRefundAmountValue = const Value.absent(),
    this.ipoRefundAmountCurrency = const Value.absent(),
    this.ipoLeveragePaidValue = const Value.absent(),
    this.ipoLeveragePaidCurrency = const Value.absent(),
    this.ipoLeverageFeePercentage = const Value.absent(),
    this.ipoLeverageFeeValue = const Value.absent(),
    this.ipoLeverageFeeCurrency = const Value.absent(),
    this.ipoAllocationFeePercentage = const Value.absent(),
    this.ipoAllotedFeeMoney = const Value.absent(),
    this.ipoAllotedFeeMoneyCurrency = const Value.absent(),
    this.ipoAllocatedMoney = const Value.absent(),
    this.ipoAllocatedMoneyCurrency = const Value.absent(),
    this.dynamicDetails = const Value.absent(),
    this.linkedTransactionFeeDetails = const Value.absent(),
    this.cashbackCalculations = const Value.absent(),
    this.carbonEmissionInGrams = const Value.absent(),
    this.carbonEmissionInOunces = const Value.absent(),
    this.recurringTransferRuleId = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  TransactionsCompanion.insert({
    required String id,
    required String productType,
    required String accountId,
    this.transactionAccountType = const Value.absent(),
    required String transactionType,
    required String transactionIdentifier,
    required String status,
    this.referenceNumber = const Value.absent(),
    required String transactionMode,
    this.transactionSubType = const Value.absent(),
    this.internalTransactionStatus = const Value.absent(),
    this.transactionDateTime = const Value.absent(),
    this.debtorName = const Value.absent(),
    this.customerId = const Value.absent(),
    this.coreBankingIdentifier = const Value.absent(),
    this.creditorName = const Value.absent(),
    this.description = const Value.absent(),
    this.subDescription = const Value.absent(),
    this.logoType = const Value.absent(),
    this.merchantName = const Value.absent(),
    this.transactionImageKey = const Value.absent(),
    required double amountValue,
    this.localAmount = const Value.absent(),
    this.exchangeRate = const Value.absent(),
    this.availableBalance = const Value.absent(),
    required String amountCurrency,
    this.executionDate = const Value.absent(),
    required String transactionCategory,
    this.tppExternalReferenceId = const Value.absent(),
    this.fxFromAmount = const Value.absent(),
    this.fxToAmount = const Value.absent(),
    this.fxFromAmountCurrency = const Value.absent(),
    this.fxToAmountCurrency = const Value.absent(),
    this.fxFormattedExchangeRate = const Value.absent(),
    this.orderId = const Value.absent(),
    this.portfolioId = const Value.absent(),
    this.instrumentId = const Value.absent(),
    this.createdDateTime = const Value.absent(),
    this.orderSide = const Value.absent(),
    this.instrumentName = const Value.absent(),
    this.instrumentSymbol = const Value.absent(),
    this.instrumentExchangeId = const Value.absent(),
    this.instrumentImageUrl = const Value.absent(),
    this.orderType = const Value.absent(),
    this.errorCode = const Value.absent(),
    this.errorMessage = const Value.absent(),
    this.executedQuantity = const Value.absent(),
    this.estimatedQuantity = const Value.absent(),
    this.executedAmountValue = const Value.absent(),
    this.executedAmountCurrency = const Value.absent(),
    this.executedNetAmountValue = const Value.absent(),
    this.executedNetAmountCurrency = const Value.absent(),
    this.estimatedAmountValue = const Value.absent(),
    this.estimatedAmountCurrency = const Value.absent(),
    this.estimatedNetAmountValue = const Value.absent(),
    this.estimatedNetAmountCurrency = const Value.absent(),
    this.commissionAmountValue = const Value.absent(),
    this.commissionAmountCurrency = const Value.absent(),
    this.estimatedCommissionAmountValue = const Value.absent(),
    this.estimatedCommissionAmountCurrency = const Value.absent(),
    this.vatValue = const Value.absent(),
    this.vatCurrency = const Value.absent(),
    this.estimatedVatValue = const Value.absent(),
    this.estimatedVatCurrency = const Value.absent(),
    this.averagePriceValue = const Value.absent(),
    this.averagePriceCurrency = const Value.absent(),
    this.estimatedPriceValue = const Value.absent(),
    this.estimatedPriceCurrency = const Value.absent(),
    this.executedTotalCommissionAmount = const Value.absent(),
    this.executedTotalCommissionCurrency = const Value.absent(),
    this.estimatedTotalCommissionAmount = const Value.absent(),
    this.estimatedTotalCommissionCurrency = const Value.absent(),
    this.commissionCalculationBasisPoints = const Value.absent(),
    this.commissionCalculationBaseAmount = const Value.absent(),
    this.commissionCalculationMinAmount = const Value.absent(),
    this.commissionCalculationBaseAmountCurrency = const Value.absent(),
    this.commissionCalculationMinAmountCurrency = const Value.absent(),
    this.instrumentExchangeCode = const Value.absent(),
    this.recurringOrderTemplateId = const Value.absent(),
    this.recurringOrderAccountName = const Value.absent(),
    this.recurringOrderFrequencyType = const Value.absent(),
    this.recurringOrderFrequencyDayOfWeek = const Value.absent(),
    this.recurringOrderFrequencyDayOfMonth = const Value.absent(),
    this.expirationDate = const Value.absent(),
    required bool isPaymentProofReady,
    this.isPaymentTrackingAvailable = const Value.absent(),
    this.paymentFeeTotalAmount = const Value.absent(),
    this.paymentFeeTotalCurrency = const Value.absent(),
    this.paymentTargetCountryCode = const Value.absent(),
    this.paymentPhoneNumber = const Value.absent(),
    this.paymentWarningMessage = const Value.absent(),
    this.paymentFeeTypeDescriptionWio = const Value.absent(),
    this.paymentFeeTypeAmountWio = const Value.absent(),
    this.paymentFeeTypeCurrencyWio = const Value.absent(),
    this.paymentFeeTypeDescriptionWioCorrespondentBank = const Value.absent(),
    this.paymentFeeTypeAmountWioCorrespondentBank = const Value.absent(),
    this.paymentFeeTypeCurrencyWioCorrespondentBank = const Value.absent(),
    this.paymentFeeTypeDescriptionWise = const Value.absent(),
    this.paymentFeeTypeAmountWise = const Value.absent(),
    this.paymentFeeTypeCurrencyWise = const Value.absent(),
    this.internationalTargetCountry = const Value.absent(),
    this.internationalCompletedDateTime = const Value.absent(),
    this.internationalEstimatedDateTime = const Value.absent(),
    this.internationalFeeChargingType = const Value.absent(),
    this.internationalSwiftCode = const Value.absent(),
    this.internationalAccountNumber = const Value.absent(),
    this.internationalPurposeDescription = const Value.absent(),
    this.internationalPurposeCode = const Value.absent(),
    this.internationalNotes = const Value.absent(),
    this.internationalBankName = const Value.absent(),
    this.internationalTargetAmount = const Value.absent(),
    this.internationalTargetAmountCurrency = const Value.absent(),
    this.internationalTransferFee = const Value.absent(),
    this.internationalTransferFeeCurrency = const Value.absent(),
    this.fabExpirationDate = const Value.absent(),
    this.fabBankName = const Value.absent(),
    this.fabTotalFeeAmount = const Value.absent(),
    this.fabTotalFeeAmountCurrency = const Value.absent(),
    this.fabUpdatedDate = const Value.absent(),
    this.fabCancellationReason = const Value.absent(),
    this.fabChequeNumber = const Value.absent(),
    this.fabChequeDate = const Value.absent(),
    this.fabFrontImageUrl = const Value.absent(),
    this.localPurposeDescription = const Value.absent(),
    this.localPurposeCode = const Value.absent(),
    this.localNote = const Value.absent(),
    this.localTargetCountry = const Value.absent(),
    this.localCompletedDateTime = const Value.absent(),
    this.localEstimatedDateTime = const Value.absent(),
    this.localAccountNumber = const Value.absent(),
    this.localSwiftCode = const Value.absent(),
    this.localBankName = const Value.absent(),
    this.localTargetAmount = const Value.absent(),
    this.localTargetAmountCurrency = const Value.absent(),
    this.localTransferSource = const Value.absent(),
    this.localTransferApplicationId = const Value.absent(),
    this.localRecipientName = const Value.absent(),
    this.npssExpirationDate = const Value.absent(),
    this.npssNotes = const Value.absent(),
    this.npssRtpInitiationDate = const Value.absent(),
    this.npssShowInPendingRtps = const Value.absent(),
    this.npssShowRtpResendButton = const Value.absent(),
    this.npssOriginalTransactionReference = const Value.absent(),
    this.npssOriginalTransactionReason = const Value.absent(),
    this.npssOriginalTransactionDeeplink = const Value.absent(),
    this.token = const Value.absent(),
    this.accNumberLast4Digits = const Value.absent(),
    this.cardMaskedPan = const Value.absent(),
    this.merchantCountryCode = const Value.absent(),
    this.originalCardTransactionCurrency = const Value.absent(),
    this.originalCardTransactionAmount = const Value.absent(),
    this.earnedCashback = const Value.absent(),
    this.cashBackPercentage = const Value.absent(),
    this.cashBackTransactionCurrency = const Value.absent(),
    this.dividendTaxAmountValue = const Value.absent(),
    this.dividendTaxAmountCurrency = const Value.absent(),
    this.dividendAmountValue = const Value.absent(),
    this.dividendAmountCurrency = const Value.absent(),
    this.netDividendAmountValue = const Value.absent(),
    this.netDividendAmountCurrency = const Value.absent(),
    this.amountPerInstrumentValue = const Value.absent(),
    this.amountPerInstrumentCurrency = const Value.absent(),
    this.instrumentQuantity = const Value.absent(),
    this.brokerDividendTaxType = const Value.absent(),
    this.dividendTaxRate = const Value.absent(),
    this.securityLendingRebateAmountValue = const Value.absent(),
    this.securityLendingRebateAmountCurrency = const Value.absent(),
    this.securityLendingRebateTaxAmountCurrency = const Value.absent(),
    this.securityLendingRebateTaxAmountValue = const Value.absent(),
    this.securityLendingTransferredAmountCurrency = const Value.absent(),
    this.securityLendingTransferredAmountValue = const Value.absent(),
    this.securityLendingMonth = const Value.absent(),
    this.wealthManagementManagedPortfolioId = const Value.absent(),
    this.wealthManagementProductName = const Value.absent(),
    this.wealthManagementProductCategory = const Value.absent(),
    this.wealthManagementAccountName = const Value.absent(),
    this.wealthManagementEstimatedExecutionDeadline = const Value.absent(),
    this.wealthManagementTrackerSteps = const Value.absent(),
    this.acquirerInstrumentName = const Value.absent(),
    this.acquirerInstrumentSymbol = const Value.absent(),
    this.acquirerInstrumentExchangeId = const Value.absent(),
    this.acquirerInstrumentImageUrl = const Value.absent(),
    this.acquireeInstrumentName = const Value.absent(),
    this.acquireeInstrumentSymbol = const Value.absent(),
    this.acquireeInstrumentExchangeId = const Value.absent(),
    this.acquireeInstrumentImageUrl = const Value.absent(),
    this.brokerAcquisitionAccountAmountValue = const Value.absent(),
    this.brokerAcquisitionAccountAmountCurrency = const Value.absent(),
    this.brokerAcquisitionPositionDelta = const Value.absent(),
    this.ipoCompanyName = const Value.absent(),
    this.ipoSubscriptionNumber = const Value.absent(),
    this.ipoLeverageValue = const Value.absent(),
    this.ipoLeverageCurrency = const Value.absent(),
    this.ipoAmountValue = const Value.absent(),
    this.ipoAmountCurrency = const Value.absent(),
    this.ipoTotalAmountValue = const Value.absent(),
    this.ipoTotalAmountCurrency = const Value.absent(),
    this.ipoCompanyLogo = const Value.absent(),
    this.ipoEnhancementValue = const Value.absent(),
    this.ipoEnhancementCurrency = const Value.absent(),
    this.ipoRefundAmountValue = const Value.absent(),
    this.ipoRefundAmountCurrency = const Value.absent(),
    this.ipoLeveragePaidValue = const Value.absent(),
    this.ipoLeveragePaidCurrency = const Value.absent(),
    this.ipoLeverageFeePercentage = const Value.absent(),
    this.ipoLeverageFeeValue = const Value.absent(),
    this.ipoLeverageFeeCurrency = const Value.absent(),
    this.ipoAllocationFeePercentage = const Value.absent(),
    this.ipoAllotedFeeMoney = const Value.absent(),
    this.ipoAllotedFeeMoneyCurrency = const Value.absent(),
    this.ipoAllocatedMoney = const Value.absent(),
    this.ipoAllocatedMoneyCurrency = const Value.absent(),
    this.dynamicDetails = const Value.absent(),
    this.linkedTransactionFeeDetails = const Value.absent(),
    this.cashbackCalculations = const Value.absent(),
    this.carbonEmissionInGrams = const Value.absent(),
    this.carbonEmissionInOunces = const Value.absent(),
    this.recurringTransferRuleId = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        productType = Value(productType),
        accountId = Value(accountId),
        transactionType = Value(transactionType),
        transactionIdentifier = Value(transactionIdentifier),
        status = Value(status),
        transactionMode = Value(transactionMode),
        amountValue = Value(amountValue),
        amountCurrency = Value(amountCurrency),
        transactionCategory = Value(transactionCategory),
        isPaymentProofReady = Value(isPaymentProofReady);
  static Insertable<TransactionsData> custom({
    Expression<String>? id,
    Expression<String>? productType,
    Expression<String>? accountId,
    Expression<String>? transactionAccountType,
    Expression<String>? transactionType,
    Expression<String>? transactionIdentifier,
    Expression<String>? status,
    Expression<String>? referenceNumber,
    Expression<String>? transactionMode,
    Expression<String>? transactionSubType,
    Expression<String>? internalTransactionStatus,
    Expression<DateTime>? transactionDateTime,
    Expression<String>? debtorName,
    Expression<String>? customerId,
    Expression<String>? coreBankingIdentifier,
    Expression<String>? creditorName,
    Expression<String>? description,
    Expression<String>? subDescription,
    Expression<String>? logoType,
    Expression<String>? merchantName,
    Expression<String>? transactionImageKey,
    Expression<double>? amountValue,
    Expression<double>? localAmount,
    Expression<double>? exchangeRate,
    Expression<double>? availableBalance,
    Expression<String>? amountCurrency,
    Expression<DateTime>? executionDate,
    Expression<String>? transactionCategory,
    Expression<String>? tppExternalReferenceId,
    Expression<double>? fxFromAmount,
    Expression<double>? fxToAmount,
    Expression<String>? fxFromAmountCurrency,
    Expression<String>? fxToAmountCurrency,
    Expression<String>? fxFormattedExchangeRate,
    Expression<String>? orderId,
    Expression<String>? portfolioId,
    Expression<String>? instrumentId,
    Expression<DateTime>? createdDateTime,
    Expression<String>? orderSide,
    Expression<String>? instrumentName,
    Expression<String>? instrumentSymbol,
    Expression<String>? instrumentExchangeId,
    Expression<String>? instrumentImageUrl,
    Expression<String>? orderType,
    Expression<int>? errorCode,
    Expression<String>? errorMessage,
    Expression<double>? executedQuantity,
    Expression<double>? estimatedQuantity,
    Expression<double>? executedAmountValue,
    Expression<String>? executedAmountCurrency,
    Expression<double>? executedNetAmountValue,
    Expression<String>? executedNetAmountCurrency,
    Expression<double>? estimatedAmountValue,
    Expression<String>? estimatedAmountCurrency,
    Expression<double>? estimatedNetAmountValue,
    Expression<String>? estimatedNetAmountCurrency,
    Expression<double>? commissionAmountValue,
    Expression<String>? commissionAmountCurrency,
    Expression<double>? estimatedCommissionAmountValue,
    Expression<String>? estimatedCommissionAmountCurrency,
    Expression<double>? vatValue,
    Expression<String>? vatCurrency,
    Expression<double>? estimatedVatValue,
    Expression<String>? estimatedVatCurrency,
    Expression<double>? averagePriceValue,
    Expression<String>? averagePriceCurrency,
    Expression<double>? estimatedPriceValue,
    Expression<String>? estimatedPriceCurrency,
    Expression<double>? executedTotalCommissionAmount,
    Expression<String>? executedTotalCommissionCurrency,
    Expression<double>? estimatedTotalCommissionAmount,
    Expression<String>? estimatedTotalCommissionCurrency,
    Expression<double>? commissionCalculationBasisPoints,
    Expression<double>? commissionCalculationBaseAmount,
    Expression<double>? commissionCalculationMinAmount,
    Expression<String>? commissionCalculationBaseAmountCurrency,
    Expression<String>? commissionCalculationMinAmountCurrency,
    Expression<String>? instrumentExchangeCode,
    Expression<String>? recurringOrderTemplateId,
    Expression<String>? recurringOrderAccountName,
    Expression<String>? recurringOrderFrequencyType,
    Expression<String>? recurringOrderFrequencyDayOfWeek,
    Expression<int>? recurringOrderFrequencyDayOfMonth,
    Expression<DateTime>? expirationDate,
    Expression<bool>? isPaymentProofReady,
    Expression<bool>? isPaymentTrackingAvailable,
    Expression<double>? paymentFeeTotalAmount,
    Expression<String>? paymentFeeTotalCurrency,
    Expression<String>? paymentTargetCountryCode,
    Expression<String>? paymentPhoneNumber,
    Expression<String>? paymentWarningMessage,
    Expression<String>? paymentFeeTypeDescriptionWio,
    Expression<double>? paymentFeeTypeAmountWio,
    Expression<String>? paymentFeeTypeCurrencyWio,
    Expression<String>? paymentFeeTypeDescriptionWioCorrespondentBank,
    Expression<double>? paymentFeeTypeAmountWioCorrespondentBank,
    Expression<String>? paymentFeeTypeCurrencyWioCorrespondentBank,
    Expression<String>? paymentFeeTypeDescriptionWise,
    Expression<double>? paymentFeeTypeAmountWise,
    Expression<String>? paymentFeeTypeCurrencyWise,
    Expression<String>? internationalTargetCountry,
    Expression<DateTime>? internationalCompletedDateTime,
    Expression<String>? internationalEstimatedDateTime,
    Expression<String>? internationalFeeChargingType,
    Expression<String>? internationalSwiftCode,
    Expression<String>? internationalAccountNumber,
    Expression<String>? internationalPurposeDescription,
    Expression<String>? internationalPurposeCode,
    Expression<String>? internationalNotes,
    Expression<String>? internationalBankName,
    Expression<double>? internationalTargetAmount,
    Expression<String>? internationalTargetAmountCurrency,
    Expression<double>? internationalTransferFee,
    Expression<String>? internationalTransferFeeCurrency,
    Expression<DateTime>? fabExpirationDate,
    Expression<String>? fabBankName,
    Expression<double>? fabTotalFeeAmount,
    Expression<String>? fabTotalFeeAmountCurrency,
    Expression<DateTime>? fabUpdatedDate,
    Expression<String>? fabCancellationReason,
    Expression<String>? fabChequeNumber,
    Expression<DateTime>? fabChequeDate,
    Expression<String>? fabFrontImageUrl,
    Expression<String>? localPurposeDescription,
    Expression<String>? localPurposeCode,
    Expression<String>? localNote,
    Expression<String>? localTargetCountry,
    Expression<DateTime>? localCompletedDateTime,
    Expression<DateTime>? localEstimatedDateTime,
    Expression<String>? localAccountNumber,
    Expression<String>? localSwiftCode,
    Expression<String>? localBankName,
    Expression<double>? localTargetAmount,
    Expression<String>? localTargetAmountCurrency,
    Expression<String>? localTransferSource,
    Expression<String>? localTransferApplicationId,
    Expression<String>? localRecipientName,
    Expression<DateTime>? npssExpirationDate,
    Expression<String>? npssNotes,
    Expression<DateTime>? npssRtpInitiationDate,
    Expression<bool>? npssShowInPendingRtps,
    Expression<bool>? npssShowRtpResendButton,
    Expression<String>? npssOriginalTransactionReference,
    Expression<String>? npssOriginalTransactionReason,
    Expression<String>? npssOriginalTransactionDeeplink,
    Expression<String>? token,
    Expression<String>? accNumberLast4Digits,
    Expression<String>? cardMaskedPan,
    Expression<String>? merchantCountryCode,
    Expression<String>? originalCardTransactionCurrency,
    Expression<double>? originalCardTransactionAmount,
    Expression<double>? earnedCashback,
    Expression<double>? cashBackPercentage,
    Expression<String>? cashBackTransactionCurrency,
    Expression<double>? dividendTaxAmountValue,
    Expression<String>? dividendTaxAmountCurrency,
    Expression<double>? dividendAmountValue,
    Expression<String>? dividendAmountCurrency,
    Expression<double>? netDividendAmountValue,
    Expression<String>? netDividendAmountCurrency,
    Expression<double>? amountPerInstrumentValue,
    Expression<String>? amountPerInstrumentCurrency,
    Expression<double>? instrumentQuantity,
    Expression<String>? brokerDividendTaxType,
    Expression<double>? dividendTaxRate,
    Expression<double>? securityLendingRebateAmountValue,
    Expression<String>? securityLendingRebateAmountCurrency,
    Expression<String>? securityLendingRebateTaxAmountCurrency,
    Expression<double>? securityLendingRebateTaxAmountValue,
    Expression<String>? securityLendingTransferredAmountCurrency,
    Expression<double>? securityLendingTransferredAmountValue,
    Expression<int>? securityLendingMonth,
    Expression<String>? wealthManagementManagedPortfolioId,
    Expression<String>? wealthManagementProductName,
    Expression<String>? wealthManagementProductCategory,
    Expression<String>? wealthManagementAccountName,
    Expression<String>? wealthManagementEstimatedExecutionDeadline,
    Expression<String>? wealthManagementTrackerSteps,
    Expression<String>? acquirerInstrumentName,
    Expression<String>? acquirerInstrumentSymbol,
    Expression<String>? acquirerInstrumentExchangeId,
    Expression<String>? acquirerInstrumentImageUrl,
    Expression<String>? acquireeInstrumentName,
    Expression<String>? acquireeInstrumentSymbol,
    Expression<String>? acquireeInstrumentExchangeId,
    Expression<String>? acquireeInstrumentImageUrl,
    Expression<double>? brokerAcquisitionAccountAmountValue,
    Expression<String>? brokerAcquisitionAccountAmountCurrency,
    Expression<double>? brokerAcquisitionPositionDelta,
    Expression<String>? ipoCompanyName,
    Expression<String>? ipoSubscriptionNumber,
    Expression<double>? ipoLeverageValue,
    Expression<String>? ipoLeverageCurrency,
    Expression<double>? ipoAmountValue,
    Expression<String>? ipoAmountCurrency,
    Expression<double>? ipoTotalAmountValue,
    Expression<String>? ipoTotalAmountCurrency,
    Expression<String>? ipoCompanyLogo,
    Expression<double>? ipoEnhancementValue,
    Expression<String>? ipoEnhancementCurrency,
    Expression<double>? ipoRefundAmountValue,
    Expression<String>? ipoRefundAmountCurrency,
    Expression<double>? ipoLeveragePaidValue,
    Expression<String>? ipoLeveragePaidCurrency,
    Expression<double>? ipoLeverageFeePercentage,
    Expression<double>? ipoLeverageFeeValue,
    Expression<String>? ipoLeverageFeeCurrency,
    Expression<double>? ipoAllocationFeePercentage,
    Expression<double>? ipoAllotedFeeMoney,
    Expression<String>? ipoAllotedFeeMoneyCurrency,
    Expression<double>? ipoAllocatedMoney,
    Expression<String>? ipoAllocatedMoneyCurrency,
    Expression<String>? dynamicDetails,
    Expression<String>? linkedTransactionFeeDetails,
    Expression<String>? cashbackCalculations,
    Expression<double>? carbonEmissionInGrams,
    Expression<double>? carbonEmissionInOunces,
    Expression<String>? recurringTransferRuleId,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (productType != null) 'product_type': productType,
      if (accountId != null) 'account_id': accountId,
      if (transactionAccountType != null)
        'transaction_account_type': transactionAccountType,
      if (transactionType != null) 'transaction_type': transactionType,
      if (transactionIdentifier != null)
        'transaction_identifier': transactionIdentifier,
      if (status != null) 'status': status,
      if (referenceNumber != null) 'reference_number': referenceNumber,
      if (transactionMode != null) 'transaction_mode': transactionMode,
      if (transactionSubType != null)
        'transaction_sub_type': transactionSubType,
      if (internalTransactionStatus != null)
        'internal_transaction_status': internalTransactionStatus,
      if (transactionDateTime != null)
        'transaction_date_time': transactionDateTime,
      if (debtorName != null) 'debtor_name': debtorName,
      if (customerId != null) 'customer_id': customerId,
      if (coreBankingIdentifier != null)
        'core_banking_identifier': coreBankingIdentifier,
      if (creditorName != null) 'creditor_name': creditorName,
      if (description != null) 'description': description,
      if (subDescription != null) 'sub_description': subDescription,
      if (logoType != null) 'logo_type': logoType,
      if (merchantName != null) 'merchant_name': merchantName,
      if (transactionImageKey != null)
        'transaction_image_key': transactionImageKey,
      if (amountValue != null) 'amount_value': amountValue,
      if (localAmount != null) 'local_amount': localAmount,
      if (exchangeRate != null) 'exchange_rate': exchangeRate,
      if (availableBalance != null) 'available_balance': availableBalance,
      if (amountCurrency != null) 'amount_currency': amountCurrency,
      if (executionDate != null) 'execution_date': executionDate,
      if (transactionCategory != null)
        'transaction_category': transactionCategory,
      if (tppExternalReferenceId != null)
        'tpp_external_reference_id': tppExternalReferenceId,
      if (fxFromAmount != null) 'fx_from_amount': fxFromAmount,
      if (fxToAmount != null) 'fx_to_amount': fxToAmount,
      if (fxFromAmountCurrency != null)
        'fx_from_amount_currency': fxFromAmountCurrency,
      if (fxToAmountCurrency != null)
        'fx_to_amount_currency': fxToAmountCurrency,
      if (fxFormattedExchangeRate != null)
        'fx_formatted_exchange_rate': fxFormattedExchangeRate,
      if (orderId != null) 'order_id': orderId,
      if (portfolioId != null) 'portfolio_id': portfolioId,
      if (instrumentId != null) 'instrument_id': instrumentId,
      if (createdDateTime != null) 'created_date_time': createdDateTime,
      if (orderSide != null) 'order_side': orderSide,
      if (instrumentName != null) 'instrument_name': instrumentName,
      if (instrumentSymbol != null) 'instrument_symbol': instrumentSymbol,
      if (instrumentExchangeId != null)
        'instrument_exchange_id': instrumentExchangeId,
      if (instrumentImageUrl != null)
        'instrument_image_url': instrumentImageUrl,
      if (orderType != null) 'order_type': orderType,
      if (errorCode != null) 'error_code': errorCode,
      if (errorMessage != null) 'error_message': errorMessage,
      if (executedQuantity != null) 'executed_quantity': executedQuantity,
      if (estimatedQuantity != null) 'estimated_quantity': estimatedQuantity,
      if (executedAmountValue != null)
        'executed_amount_value': executedAmountValue,
      if (executedAmountCurrency != null)
        'executed_amount_currency': executedAmountCurrency,
      if (executedNetAmountValue != null)
        'executed_net_amount_value': executedNetAmountValue,
      if (executedNetAmountCurrency != null)
        'executed_net_amount_currency': executedNetAmountCurrency,
      if (estimatedAmountValue != null)
        'estimated_amount_value': estimatedAmountValue,
      if (estimatedAmountCurrency != null)
        'estimated_amount_currency': estimatedAmountCurrency,
      if (estimatedNetAmountValue != null)
        'estimated_net_amount_value': estimatedNetAmountValue,
      if (estimatedNetAmountCurrency != null)
        'estimated_net_amount_currency': estimatedNetAmountCurrency,
      if (commissionAmountValue != null)
        'commission_amount_value': commissionAmountValue,
      if (commissionAmountCurrency != null)
        'commission_amount_currency': commissionAmountCurrency,
      if (estimatedCommissionAmountValue != null)
        'estimated_commission_amount_value': estimatedCommissionAmountValue,
      if (estimatedCommissionAmountCurrency != null)
        'estimated_commission_amount_currency':
            estimatedCommissionAmountCurrency,
      if (vatValue != null) 'vat_value': vatValue,
      if (vatCurrency != null) 'vat_currency': vatCurrency,
      if (estimatedVatValue != null) 'estimated_vat_value': estimatedVatValue,
      if (estimatedVatCurrency != null)
        'estimated_vat_currency': estimatedVatCurrency,
      if (averagePriceValue != null) 'average_price_value': averagePriceValue,
      if (averagePriceCurrency != null)
        'average_price_currency': averagePriceCurrency,
      if (estimatedPriceValue != null)
        'estimated_price_value': estimatedPriceValue,
      if (estimatedPriceCurrency != null)
        'estimated_price_currency': estimatedPriceCurrency,
      if (executedTotalCommissionAmount != null)
        'executed_total_commission_amount': executedTotalCommissionAmount,
      if (executedTotalCommissionCurrency != null)
        'executed_total_commission_currency': executedTotalCommissionCurrency,
      if (estimatedTotalCommissionAmount != null)
        'estimated_total_commision_amount': estimatedTotalCommissionAmount,
      if (estimatedTotalCommissionCurrency != null)
        'estimated_total_commision_currency': estimatedTotalCommissionCurrency,
      if (commissionCalculationBasisPoints != null)
        'commission_calculation_basis_points': commissionCalculationBasisPoints,
      if (commissionCalculationBaseAmount != null)
        'commission_calculation_base_amount': commissionCalculationBaseAmount,
      if (commissionCalculationMinAmount != null)
        'commission_calculation_min_amount': commissionCalculationMinAmount,
      if (commissionCalculationBaseAmountCurrency != null)
        'commission_calculation_base_amount_currency':
            commissionCalculationBaseAmountCurrency,
      if (commissionCalculationMinAmountCurrency != null)
        'commission_calculation_min_amount_currency':
            commissionCalculationMinAmountCurrency,
      if (instrumentExchangeCode != null)
        'instrument_exchange_code': instrumentExchangeCode,
      if (recurringOrderTemplateId != null)
        'recurring_order_template_id': recurringOrderTemplateId,
      if (recurringOrderAccountName != null)
        'recurring_order_account_name': recurringOrderAccountName,
      if (recurringOrderFrequencyType != null)
        'recurring_order_frequency_type': recurringOrderFrequencyType,
      if (recurringOrderFrequencyDayOfWeek != null)
        'recurring_order_frequency_day_of_week':
            recurringOrderFrequencyDayOfWeek,
      if (recurringOrderFrequencyDayOfMonth != null)
        'recurring_order_frequency_day_of_month':
            recurringOrderFrequencyDayOfMonth,
      if (expirationDate != null) 'expiration_date': expirationDate,
      if (isPaymentProofReady != null)
        'is_payment_proof_ready': isPaymentProofReady,
      if (isPaymentTrackingAvailable != null)
        'is_payment_tracking_available': isPaymentTrackingAvailable,
      if (paymentFeeTotalAmount != null)
        'payment_fee_total_amount': paymentFeeTotalAmount,
      if (paymentFeeTotalCurrency != null)
        'payment_fee_total_currency': paymentFeeTotalCurrency,
      if (paymentTargetCountryCode != null)
        'payment_target_country_code': paymentTargetCountryCode,
      if (paymentPhoneNumber != null)
        'payment_phone_number': paymentPhoneNumber,
      if (paymentWarningMessage != null)
        'payment_warning_message': paymentWarningMessage,
      if (paymentFeeTypeDescriptionWio != null)
        'payment_feetype_description_Wio': paymentFeeTypeDescriptionWio,
      if (paymentFeeTypeAmountWio != null)
        'payment_feetype_amount_Wio': paymentFeeTypeAmountWio,
      if (paymentFeeTypeCurrencyWio != null)
        'payment_feetype_currency_Wio': paymentFeeTypeCurrencyWio,
      if (paymentFeeTypeDescriptionWioCorrespondentBank != null)
        'payment_feetype_description_WioCorrespondentBank':
            paymentFeeTypeDescriptionWioCorrespondentBank,
      if (paymentFeeTypeAmountWioCorrespondentBank != null)
        'payment_feetype_amount_WioCorrespondentBank':
            paymentFeeTypeAmountWioCorrespondentBank,
      if (paymentFeeTypeCurrencyWioCorrespondentBank != null)
        'payment_feetype_currency_WioCorrespondentBank':
            paymentFeeTypeCurrencyWioCorrespondentBank,
      if (paymentFeeTypeDescriptionWise != null)
        'payment_feetype_description_Wise': paymentFeeTypeDescriptionWise,
      if (paymentFeeTypeAmountWise != null)
        'payment_feetype_amount_Wise': paymentFeeTypeAmountWise,
      if (paymentFeeTypeCurrencyWise != null)
        'payment_feetype_currency_Wise': paymentFeeTypeCurrencyWise,
      if (internationalTargetCountry != null)
        'international_target_country': internationalTargetCountry,
      if (internationalCompletedDateTime != null)
        'international_completed_date_time': internationalCompletedDateTime,
      if (internationalEstimatedDateTime != null)
        'international_estimated_date_time': internationalEstimatedDateTime,
      if (internationalFeeChargingType != null)
        'international_fee_charging_type': internationalFeeChargingType,
      if (internationalSwiftCode != null)
        'international_swift_code': internationalSwiftCode,
      if (internationalAccountNumber != null)
        'international_account_number': internationalAccountNumber,
      if (internationalPurposeDescription != null)
        'international_purpose_description': internationalPurposeDescription,
      if (internationalPurposeCode != null)
        'international_purpose_code': internationalPurposeCode,
      if (internationalNotes != null) 'international_notes': internationalNotes,
      if (internationalBankName != null)
        'international_bank_name': internationalBankName,
      if (internationalTargetAmount != null)
        'international_target_amount': internationalTargetAmount,
      if (internationalTargetAmountCurrency != null)
        'international_target_amount_currency':
            internationalTargetAmountCurrency,
      if (internationalTransferFee != null)
        'international_transfer_fee': internationalTransferFee,
      if (internationalTransferFeeCurrency != null)
        'international_transfer_fee_currency': internationalTransferFeeCurrency,
      if (fabExpirationDate != null) 'fab_expiration_date': fabExpirationDate,
      if (fabBankName != null) 'fab_bank_name': fabBankName,
      if (fabTotalFeeAmount != null) 'fab_total_fee_amount': fabTotalFeeAmount,
      if (fabTotalFeeAmountCurrency != null)
        'fab_total_fee_amount_currency': fabTotalFeeAmountCurrency,
      if (fabUpdatedDate != null) 'fab_updated_date': fabUpdatedDate,
      if (fabCancellationReason != null)
        'fab_cancellation_reason': fabCancellationReason,
      if (fabChequeNumber != null) 'fab_cheque_number': fabChequeNumber,
      if (fabChequeDate != null) 'fab_cheque_date': fabChequeDate,
      if (fabFrontImageUrl != null) 'fab_front_image_url': fabFrontImageUrl,
      if (localPurposeDescription != null)
        'local_purpose_description': localPurposeDescription,
      if (localPurposeCode != null) 'local_purpose_code': localPurposeCode,
      if (localNote != null) 'local_note': localNote,
      if (localTargetCountry != null)
        'local_target_country': localTargetCountry,
      if (localCompletedDateTime != null)
        'local_completed_date_time': localCompletedDateTime,
      if (localEstimatedDateTime != null)
        'local_estimated_date_time': localEstimatedDateTime,
      if (localAccountNumber != null)
        'local_account_number': localAccountNumber,
      if (localSwiftCode != null) 'local_swift_code': localSwiftCode,
      if (localBankName != null) 'local_bank_name': localBankName,
      if (localTargetAmount != null) 'local_target_amount': localTargetAmount,
      if (localTargetAmountCurrency != null)
        'local_target_amount_currency': localTargetAmountCurrency,
      if (localTransferSource != null)
        'local_transfer_source': localTransferSource,
      if (localTransferApplicationId != null)
        'local_transfer_application_id': localTransferApplicationId,
      if (localRecipientName != null)
        'local_recipient_name': localRecipientName,
      if (npssExpirationDate != null)
        'npss_expiration_date': npssExpirationDate,
      if (npssNotes != null) 'npss_notes': npssNotes,
      if (npssRtpInitiationDate != null)
        'npss_rtp_initiation_date': npssRtpInitiationDate,
      if (npssShowInPendingRtps != null)
        'npss_show_in_pending_rtps': npssShowInPendingRtps,
      if (npssShowRtpResendButton != null)
        'npss_show_rtp_resend_button': npssShowRtpResendButton,
      if (npssOriginalTransactionReference != null)
        'npss_original_transaction_reference': npssOriginalTransactionReference,
      if (npssOriginalTransactionReason != null)
        'npss_original_transaction_reason': npssOriginalTransactionReason,
      if (npssOriginalTransactionDeeplink != null)
        'npss_original_transaction_deeplink': npssOriginalTransactionDeeplink,
      if (token != null) 'token': token,
      if (accNumberLast4Digits != null)
        'acc_number_last_4digits': accNumberLast4Digits,
      if (cardMaskedPan != null) 'card_masked_pan': cardMaskedPan,
      if (merchantCountryCode != null)
        'merchant_country_code': merchantCountryCode,
      if (originalCardTransactionCurrency != null)
        'original_card_transaction_currency': originalCardTransactionCurrency,
      if (originalCardTransactionAmount != null)
        'original_card_transaction_amount': originalCardTransactionAmount,
      if (earnedCashback != null) 'earned_cash_back': earnedCashback,
      if (cashBackPercentage != null)
        'cash_back_percentage': cashBackPercentage,
      if (cashBackTransactionCurrency != null)
        'cash_back_transaction_currency': cashBackTransactionCurrency,
      if (dividendTaxAmountValue != null)
        'dividend_tax_amount_value': dividendTaxAmountValue,
      if (dividendTaxAmountCurrency != null)
        'dividend_tax_amount_currency': dividendTaxAmountCurrency,
      if (dividendAmountValue != null)
        'dividend_amount_value': dividendAmountValue,
      if (dividendAmountCurrency != null)
        'dividend_amount_currency': dividendAmountCurrency,
      if (netDividendAmountValue != null)
        'net_dividend_amount_value': netDividendAmountValue,
      if (netDividendAmountCurrency != null)
        'net_dividend_amount_currency': netDividendAmountCurrency,
      if (amountPerInstrumentValue != null)
        'amount_per_instrument_value': amountPerInstrumentValue,
      if (amountPerInstrumentCurrency != null)
        'amount_per_instrument_currency': amountPerInstrumentCurrency,
      if (instrumentQuantity != null) 'instrument_quantity': instrumentQuantity,
      if (brokerDividendTaxType != null)
        'broker_dividend_tax_type': brokerDividendTaxType,
      if (dividendTaxRate != null) 'dividend_tax_rate': dividendTaxRate,
      if (securityLendingRebateAmountValue != null)
        'security_lending_rebate_amount_value':
            securityLendingRebateAmountValue,
      if (securityLendingRebateAmountCurrency != null)
        'security_lending_rebate_amount_currency':
            securityLendingRebateAmountCurrency,
      if (securityLendingRebateTaxAmountCurrency != null)
        'security_lending_rebate_tax_amount_currency':
            securityLendingRebateTaxAmountCurrency,
      if (securityLendingRebateTaxAmountValue != null)
        'security_lending_rebate_tax_amount_value':
            securityLendingRebateTaxAmountValue,
      if (securityLendingTransferredAmountCurrency != null)
        'security_lending_transferred_amount_currency':
            securityLendingTransferredAmountCurrency,
      if (securityLendingTransferredAmountValue != null)
        'security_lending_transferred_amount_value':
            securityLendingTransferredAmountValue,
      if (securityLendingMonth != null)
        'security_lending_month': securityLendingMonth,
      if (wealthManagementManagedPortfolioId != null)
        'wealth_management_managed_portfolio_id':
            wealthManagementManagedPortfolioId,
      if (wealthManagementProductName != null)
        'wealth_management_product_name': wealthManagementProductName,
      if (wealthManagementProductCategory != null)
        'wealth_management_product_category': wealthManagementProductCategory,
      if (wealthManagementAccountName != null)
        'wealth_management_account_name': wealthManagementAccountName,
      if (wealthManagementEstimatedExecutionDeadline != null)
        'wealth_management_estimated_execution_deadline':
            wealthManagementEstimatedExecutionDeadline,
      if (wealthManagementTrackerSteps != null)
        'wealth_management_tracker_steps': wealthManagementTrackerSteps,
      if (acquirerInstrumentName != null)
        'acquirer_instrument_name': acquirerInstrumentName,
      if (acquirerInstrumentSymbol != null)
        'acquirer_instrument_symbol': acquirerInstrumentSymbol,
      if (acquirerInstrumentExchangeId != null)
        'acquirer_instrument_exchange_id': acquirerInstrumentExchangeId,
      if (acquirerInstrumentImageUrl != null)
        'acquirer_instrument_image_url': acquirerInstrumentImageUrl,
      if (acquireeInstrumentName != null)
        'acquiree_instrument_name': acquireeInstrumentName,
      if (acquireeInstrumentSymbol != null)
        'acquiree_instrument_symbol': acquireeInstrumentSymbol,
      if (acquireeInstrumentExchangeId != null)
        'acquiree_instrument_exchange_id': acquireeInstrumentExchangeId,
      if (acquireeInstrumentImageUrl != null)
        'acquiree_instrument_image_url': acquireeInstrumentImageUrl,
      if (brokerAcquisitionAccountAmountValue != null)
        'broker_acquisition_account_amount_value':
            brokerAcquisitionAccountAmountValue,
      if (brokerAcquisitionAccountAmountCurrency != null)
        'broker_acquisition_account_amount_currency':
            brokerAcquisitionAccountAmountCurrency,
      if (brokerAcquisitionPositionDelta != null)
        'broker_acquisition_position_delta': brokerAcquisitionPositionDelta,
      if (ipoCompanyName != null) 'ipo_company_name': ipoCompanyName,
      if (ipoSubscriptionNumber != null)
        'ipo_subscription_number': ipoSubscriptionNumber,
      if (ipoLeverageValue != null) 'ipo_leverage_value': ipoLeverageValue,
      if (ipoLeverageCurrency != null)
        'ipo_leverage_currency': ipoLeverageCurrency,
      if (ipoAmountValue != null) 'ipo_amount_value': ipoAmountValue,
      if (ipoAmountCurrency != null) 'ipo_amount_currency': ipoAmountCurrency,
      if (ipoTotalAmountValue != null)
        'ipo_total_amount_value': ipoTotalAmountValue,
      if (ipoTotalAmountCurrency != null)
        'ipo_total_amount_currency': ipoTotalAmountCurrency,
      if (ipoCompanyLogo != null) 'ipo_company_logo': ipoCompanyLogo,
      if (ipoEnhancementValue != null)
        'ipo_enhancement_value': ipoEnhancementValue,
      if (ipoEnhancementCurrency != null)
        'ipo_enhancement_currency': ipoEnhancementCurrency,
      if (ipoRefundAmountValue != null)
        'ipo_refund_amount_value': ipoRefundAmountValue,
      if (ipoRefundAmountCurrency != null)
        'ipo_refund_amount_currency': ipoRefundAmountCurrency,
      if (ipoLeveragePaidValue != null)
        'ipo_leverage_paid_value': ipoLeveragePaidValue,
      if (ipoLeveragePaidCurrency != null)
        'ipo_leverage_paid_currency': ipoLeveragePaidCurrency,
      if (ipoLeverageFeePercentage != null)
        'ipo_leverage_fee_percentage': ipoLeverageFeePercentage,
      if (ipoLeverageFeeValue != null)
        'ipo_leverage_fee_value': ipoLeverageFeeValue,
      if (ipoLeverageFeeCurrency != null)
        'ipo_leverage_fee_currency': ipoLeverageFeeCurrency,
      if (ipoAllocationFeePercentage != null)
        'ipo_allocation_fee_percentage': ipoAllocationFeePercentage,
      if (ipoAllotedFeeMoney != null)
        'ipo_alloted_fee_money': ipoAllotedFeeMoney,
      if (ipoAllotedFeeMoneyCurrency != null)
        'ipo_alloted_fee_money_currency': ipoAllotedFeeMoneyCurrency,
      if (ipoAllocatedMoney != null) 'ipo_allocated_money': ipoAllocatedMoney,
      if (ipoAllocatedMoneyCurrency != null)
        'ipo_allocated_money_currency': ipoAllocatedMoneyCurrency,
      if (dynamicDetails != null) 'dynamic_details': dynamicDetails,
      if (linkedTransactionFeeDetails != null)
        'linked_transaction_fee_details': linkedTransactionFeeDetails,
      if (cashbackCalculations != null)
        'cashback_calculations': cashbackCalculations,
      if (carbonEmissionInGrams != null)
        'carbon_emission_in_grams': carbonEmissionInGrams,
      if (carbonEmissionInOunces != null)
        'carbon_emission_in_ounces': carbonEmissionInOunces,
      if (recurringTransferRuleId != null)
        'recurring_transfer_rule_id': recurringTransferRuleId,
      if (rowid != null) 'rowid': rowid,
    });
  }

  TransactionsCompanion copyWith(
      {Value<String>? id,
      Value<String>? productType,
      Value<String>? accountId,
      Value<String?>? transactionAccountType,
      Value<String>? transactionType,
      Value<String>? transactionIdentifier,
      Value<String>? status,
      Value<String?>? referenceNumber,
      Value<String>? transactionMode,
      Value<String?>? transactionSubType,
      Value<String?>? internalTransactionStatus,
      Value<DateTime?>? transactionDateTime,
      Value<String?>? debtorName,
      Value<String?>? customerId,
      Value<String?>? coreBankingIdentifier,
      Value<String?>? creditorName,
      Value<String?>? description,
      Value<String?>? subDescription,
      Value<String?>? logoType,
      Value<String?>? merchantName,
      Value<String?>? transactionImageKey,
      Value<double>? amountValue,
      Value<double?>? localAmount,
      Value<double?>? exchangeRate,
      Value<double?>? availableBalance,
      Value<String>? amountCurrency,
      Value<DateTime?>? executionDate,
      Value<String>? transactionCategory,
      Value<String?>? tppExternalReferenceId,
      Value<double?>? fxFromAmount,
      Value<double?>? fxToAmount,
      Value<String?>? fxFromAmountCurrency,
      Value<String?>? fxToAmountCurrency,
      Value<String?>? fxFormattedExchangeRate,
      Value<String?>? orderId,
      Value<String?>? portfolioId,
      Value<String?>? instrumentId,
      Value<DateTime?>? createdDateTime,
      Value<String?>? orderSide,
      Value<String?>? instrumentName,
      Value<String?>? instrumentSymbol,
      Value<String?>? instrumentExchangeId,
      Value<String?>? instrumentImageUrl,
      Value<String?>? orderType,
      Value<int?>? errorCode,
      Value<String?>? errorMessage,
      Value<double?>? executedQuantity,
      Value<double?>? estimatedQuantity,
      Value<double?>? executedAmountValue,
      Value<String?>? executedAmountCurrency,
      Value<double?>? executedNetAmountValue,
      Value<String?>? executedNetAmountCurrency,
      Value<double?>? estimatedAmountValue,
      Value<String?>? estimatedAmountCurrency,
      Value<double?>? estimatedNetAmountValue,
      Value<String?>? estimatedNetAmountCurrency,
      Value<double?>? commissionAmountValue,
      Value<String?>? commissionAmountCurrency,
      Value<double?>? estimatedCommissionAmountValue,
      Value<String?>? estimatedCommissionAmountCurrency,
      Value<double?>? vatValue,
      Value<String?>? vatCurrency,
      Value<double?>? estimatedVatValue,
      Value<String?>? estimatedVatCurrency,
      Value<double?>? averagePriceValue,
      Value<String?>? averagePriceCurrency,
      Value<double?>? estimatedPriceValue,
      Value<String?>? estimatedPriceCurrency,
      Value<double?>? executedTotalCommissionAmount,
      Value<String?>? executedTotalCommissionCurrency,
      Value<double?>? estimatedTotalCommissionAmount,
      Value<String?>? estimatedTotalCommissionCurrency,
      Value<double?>? commissionCalculationBasisPoints,
      Value<double?>? commissionCalculationBaseAmount,
      Value<double?>? commissionCalculationMinAmount,
      Value<String?>? commissionCalculationBaseAmountCurrency,
      Value<String?>? commissionCalculationMinAmountCurrency,
      Value<String?>? instrumentExchangeCode,
      Value<String?>? recurringOrderTemplateId,
      Value<String?>? recurringOrderAccountName,
      Value<String?>? recurringOrderFrequencyType,
      Value<String?>? recurringOrderFrequencyDayOfWeek,
      Value<int?>? recurringOrderFrequencyDayOfMonth,
      Value<DateTime?>? expirationDate,
      Value<bool>? isPaymentProofReady,
      Value<bool?>? isPaymentTrackingAvailable,
      Value<double?>? paymentFeeTotalAmount,
      Value<String?>? paymentFeeTotalCurrency,
      Value<String?>? paymentTargetCountryCode,
      Value<String?>? paymentPhoneNumber,
      Value<String?>? paymentWarningMessage,
      Value<String?>? paymentFeeTypeDescriptionWio,
      Value<double?>? paymentFeeTypeAmountWio,
      Value<String?>? paymentFeeTypeCurrencyWio,
      Value<String?>? paymentFeeTypeDescriptionWioCorrespondentBank,
      Value<double?>? paymentFeeTypeAmountWioCorrespondentBank,
      Value<String?>? paymentFeeTypeCurrencyWioCorrespondentBank,
      Value<String?>? paymentFeeTypeDescriptionWise,
      Value<double?>? paymentFeeTypeAmountWise,
      Value<String?>? paymentFeeTypeCurrencyWise,
      Value<String?>? internationalTargetCountry,
      Value<DateTime?>? internationalCompletedDateTime,
      Value<String?>? internationalEstimatedDateTime,
      Value<String?>? internationalFeeChargingType,
      Value<String?>? internationalSwiftCode,
      Value<String?>? internationalAccountNumber,
      Value<String?>? internationalPurposeDescription,
      Value<String?>? internationalPurposeCode,
      Value<String?>? internationalNotes,
      Value<String?>? internationalBankName,
      Value<double?>? internationalTargetAmount,
      Value<String?>? internationalTargetAmountCurrency,
      Value<double?>? internationalTransferFee,
      Value<String?>? internationalTransferFeeCurrency,
      Value<DateTime?>? fabExpirationDate,
      Value<String?>? fabBankName,
      Value<double?>? fabTotalFeeAmount,
      Value<String?>? fabTotalFeeAmountCurrency,
      Value<DateTime?>? fabUpdatedDate,
      Value<String?>? fabCancellationReason,
      Value<String?>? fabChequeNumber,
      Value<DateTime?>? fabChequeDate,
      Value<String?>? fabFrontImageUrl,
      Value<String?>? localPurposeDescription,
      Value<String?>? localPurposeCode,
      Value<String?>? localNote,
      Value<String?>? localTargetCountry,
      Value<DateTime?>? localCompletedDateTime,
      Value<DateTime?>? localEstimatedDateTime,
      Value<String?>? localAccountNumber,
      Value<String?>? localSwiftCode,
      Value<String?>? localBankName,
      Value<double?>? localTargetAmount,
      Value<String?>? localTargetAmountCurrency,
      Value<String?>? localTransferSource,
      Value<String?>? localTransferApplicationId,
      Value<String?>? localRecipientName,
      Value<DateTime?>? npssExpirationDate,
      Value<String?>? npssNotes,
      Value<DateTime?>? npssRtpInitiationDate,
      Value<bool?>? npssShowInPendingRtps,
      Value<bool?>? npssShowRtpResendButton,
      Value<String?>? npssOriginalTransactionReference,
      Value<String?>? npssOriginalTransactionReason,
      Value<String?>? npssOriginalTransactionDeeplink,
      Value<String?>? token,
      Value<String?>? accNumberLast4Digits,
      Value<String?>? cardMaskedPan,
      Value<String?>? merchantCountryCode,
      Value<String?>? originalCardTransactionCurrency,
      Value<double?>? originalCardTransactionAmount,
      Value<double?>? earnedCashback,
      Value<double?>? cashBackPercentage,
      Value<String?>? cashBackTransactionCurrency,
      Value<double?>? dividendTaxAmountValue,
      Value<String?>? dividendTaxAmountCurrency,
      Value<double?>? dividendAmountValue,
      Value<String?>? dividendAmountCurrency,
      Value<double?>? netDividendAmountValue,
      Value<String?>? netDividendAmountCurrency,
      Value<double?>? amountPerInstrumentValue,
      Value<String?>? amountPerInstrumentCurrency,
      Value<double?>? instrumentQuantity,
      Value<String?>? brokerDividendTaxType,
      Value<double?>? dividendTaxRate,
      Value<double?>? securityLendingRebateAmountValue,
      Value<String?>? securityLendingRebateAmountCurrency,
      Value<String?>? securityLendingRebateTaxAmountCurrency,
      Value<double?>? securityLendingRebateTaxAmountValue,
      Value<String?>? securityLendingTransferredAmountCurrency,
      Value<double?>? securityLendingTransferredAmountValue,
      Value<int?>? securityLendingMonth,
      Value<String?>? wealthManagementManagedPortfolioId,
      Value<String?>? wealthManagementProductName,
      Value<String?>? wealthManagementProductCategory,
      Value<String?>? wealthManagementAccountName,
      Value<String?>? wealthManagementEstimatedExecutionDeadline,
      Value<String?>? wealthManagementTrackerSteps,
      Value<String?>? acquirerInstrumentName,
      Value<String?>? acquirerInstrumentSymbol,
      Value<String?>? acquirerInstrumentExchangeId,
      Value<String?>? acquirerInstrumentImageUrl,
      Value<String?>? acquireeInstrumentName,
      Value<String?>? acquireeInstrumentSymbol,
      Value<String?>? acquireeInstrumentExchangeId,
      Value<String?>? acquireeInstrumentImageUrl,
      Value<double?>? brokerAcquisitionAccountAmountValue,
      Value<String?>? brokerAcquisitionAccountAmountCurrency,
      Value<double?>? brokerAcquisitionPositionDelta,
      Value<String?>? ipoCompanyName,
      Value<String?>? ipoSubscriptionNumber,
      Value<double?>? ipoLeverageValue,
      Value<String?>? ipoLeverageCurrency,
      Value<double?>? ipoAmountValue,
      Value<String?>? ipoAmountCurrency,
      Value<double?>? ipoTotalAmountValue,
      Value<String?>? ipoTotalAmountCurrency,
      Value<String?>? ipoCompanyLogo,
      Value<double?>? ipoEnhancementValue,
      Value<String?>? ipoEnhancementCurrency,
      Value<double?>? ipoRefundAmountValue,
      Value<String?>? ipoRefundAmountCurrency,
      Value<double?>? ipoLeveragePaidValue,
      Value<String?>? ipoLeveragePaidCurrency,
      Value<double?>? ipoLeverageFeePercentage,
      Value<double?>? ipoLeverageFeeValue,
      Value<String?>? ipoLeverageFeeCurrency,
      Value<double?>? ipoAllocationFeePercentage,
      Value<double?>? ipoAllotedFeeMoney,
      Value<String?>? ipoAllotedFeeMoneyCurrency,
      Value<double?>? ipoAllocatedMoney,
      Value<String?>? ipoAllocatedMoneyCurrency,
      Value<String>? dynamicDetails,
      Value<String>? linkedTransactionFeeDetails,
      Value<String?>? cashbackCalculations,
      Value<double?>? carbonEmissionInGrams,
      Value<double?>? carbonEmissionInOunces,
      Value<String?>? recurringTransferRuleId,
      Value<int>? rowid}) {
    return TransactionsCompanion(
      id: id ?? this.id,
      productType: productType ?? this.productType,
      accountId: accountId ?? this.accountId,
      transactionAccountType:
          transactionAccountType ?? this.transactionAccountType,
      transactionType: transactionType ?? this.transactionType,
      transactionIdentifier:
          transactionIdentifier ?? this.transactionIdentifier,
      status: status ?? this.status,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      transactionMode: transactionMode ?? this.transactionMode,
      transactionSubType: transactionSubType ?? this.transactionSubType,
      internalTransactionStatus:
          internalTransactionStatus ?? this.internalTransactionStatus,
      transactionDateTime: transactionDateTime ?? this.transactionDateTime,
      debtorName: debtorName ?? this.debtorName,
      customerId: customerId ?? this.customerId,
      coreBankingIdentifier:
          coreBankingIdentifier ?? this.coreBankingIdentifier,
      creditorName: creditorName ?? this.creditorName,
      description: description ?? this.description,
      subDescription: subDescription ?? this.subDescription,
      logoType: logoType ?? this.logoType,
      merchantName: merchantName ?? this.merchantName,
      transactionImageKey: transactionImageKey ?? this.transactionImageKey,
      amountValue: amountValue ?? this.amountValue,
      localAmount: localAmount ?? this.localAmount,
      exchangeRate: exchangeRate ?? this.exchangeRate,
      availableBalance: availableBalance ?? this.availableBalance,
      amountCurrency: amountCurrency ?? this.amountCurrency,
      executionDate: executionDate ?? this.executionDate,
      transactionCategory: transactionCategory ?? this.transactionCategory,
      tppExternalReferenceId:
          tppExternalReferenceId ?? this.tppExternalReferenceId,
      fxFromAmount: fxFromAmount ?? this.fxFromAmount,
      fxToAmount: fxToAmount ?? this.fxToAmount,
      fxFromAmountCurrency: fxFromAmountCurrency ?? this.fxFromAmountCurrency,
      fxToAmountCurrency: fxToAmountCurrency ?? this.fxToAmountCurrency,
      fxFormattedExchangeRate:
          fxFormattedExchangeRate ?? this.fxFormattedExchangeRate,
      orderId: orderId ?? this.orderId,
      portfolioId: portfolioId ?? this.portfolioId,
      instrumentId: instrumentId ?? this.instrumentId,
      createdDateTime: createdDateTime ?? this.createdDateTime,
      orderSide: orderSide ?? this.orderSide,
      instrumentName: instrumentName ?? this.instrumentName,
      instrumentSymbol: instrumentSymbol ?? this.instrumentSymbol,
      instrumentExchangeId: instrumentExchangeId ?? this.instrumentExchangeId,
      instrumentImageUrl: instrumentImageUrl ?? this.instrumentImageUrl,
      orderType: orderType ?? this.orderType,
      errorCode: errorCode ?? this.errorCode,
      errorMessage: errorMessage ?? this.errorMessage,
      executedQuantity: executedQuantity ?? this.executedQuantity,
      estimatedQuantity: estimatedQuantity ?? this.estimatedQuantity,
      executedAmountValue: executedAmountValue ?? this.executedAmountValue,
      executedAmountCurrency:
          executedAmountCurrency ?? this.executedAmountCurrency,
      executedNetAmountValue:
          executedNetAmountValue ?? this.executedNetAmountValue,
      executedNetAmountCurrency:
          executedNetAmountCurrency ?? this.executedNetAmountCurrency,
      estimatedAmountValue: estimatedAmountValue ?? this.estimatedAmountValue,
      estimatedAmountCurrency:
          estimatedAmountCurrency ?? this.estimatedAmountCurrency,
      estimatedNetAmountValue:
          estimatedNetAmountValue ?? this.estimatedNetAmountValue,
      estimatedNetAmountCurrency:
          estimatedNetAmountCurrency ?? this.estimatedNetAmountCurrency,
      commissionAmountValue:
          commissionAmountValue ?? this.commissionAmountValue,
      commissionAmountCurrency:
          commissionAmountCurrency ?? this.commissionAmountCurrency,
      estimatedCommissionAmountValue:
          estimatedCommissionAmountValue ?? this.estimatedCommissionAmountValue,
      estimatedCommissionAmountCurrency: estimatedCommissionAmountCurrency ??
          this.estimatedCommissionAmountCurrency,
      vatValue: vatValue ?? this.vatValue,
      vatCurrency: vatCurrency ?? this.vatCurrency,
      estimatedVatValue: estimatedVatValue ?? this.estimatedVatValue,
      estimatedVatCurrency: estimatedVatCurrency ?? this.estimatedVatCurrency,
      averagePriceValue: averagePriceValue ?? this.averagePriceValue,
      averagePriceCurrency: averagePriceCurrency ?? this.averagePriceCurrency,
      estimatedPriceValue: estimatedPriceValue ?? this.estimatedPriceValue,
      estimatedPriceCurrency:
          estimatedPriceCurrency ?? this.estimatedPriceCurrency,
      executedTotalCommissionAmount:
          executedTotalCommissionAmount ?? this.executedTotalCommissionAmount,
      executedTotalCommissionCurrency: executedTotalCommissionCurrency ??
          this.executedTotalCommissionCurrency,
      estimatedTotalCommissionAmount:
          estimatedTotalCommissionAmount ?? this.estimatedTotalCommissionAmount,
      estimatedTotalCommissionCurrency: estimatedTotalCommissionCurrency ??
          this.estimatedTotalCommissionCurrency,
      commissionCalculationBasisPoints: commissionCalculationBasisPoints ??
          this.commissionCalculationBasisPoints,
      commissionCalculationBaseAmount: commissionCalculationBaseAmount ??
          this.commissionCalculationBaseAmount,
      commissionCalculationMinAmount:
          commissionCalculationMinAmount ?? this.commissionCalculationMinAmount,
      commissionCalculationBaseAmountCurrency:
          commissionCalculationBaseAmountCurrency ??
              this.commissionCalculationBaseAmountCurrency,
      commissionCalculationMinAmountCurrency:
          commissionCalculationMinAmountCurrency ??
              this.commissionCalculationMinAmountCurrency,
      instrumentExchangeCode:
          instrumentExchangeCode ?? this.instrumentExchangeCode,
      recurringOrderTemplateId:
          recurringOrderTemplateId ?? this.recurringOrderTemplateId,
      recurringOrderAccountName:
          recurringOrderAccountName ?? this.recurringOrderAccountName,
      recurringOrderFrequencyType:
          recurringOrderFrequencyType ?? this.recurringOrderFrequencyType,
      recurringOrderFrequencyDayOfWeek: recurringOrderFrequencyDayOfWeek ??
          this.recurringOrderFrequencyDayOfWeek,
      recurringOrderFrequencyDayOfMonth: recurringOrderFrequencyDayOfMonth ??
          this.recurringOrderFrequencyDayOfMonth,
      expirationDate: expirationDate ?? this.expirationDate,
      isPaymentProofReady: isPaymentProofReady ?? this.isPaymentProofReady,
      isPaymentTrackingAvailable:
          isPaymentTrackingAvailable ?? this.isPaymentTrackingAvailable,
      paymentFeeTotalAmount:
          paymentFeeTotalAmount ?? this.paymentFeeTotalAmount,
      paymentFeeTotalCurrency:
          paymentFeeTotalCurrency ?? this.paymentFeeTotalCurrency,
      paymentTargetCountryCode:
          paymentTargetCountryCode ?? this.paymentTargetCountryCode,
      paymentPhoneNumber: paymentPhoneNumber ?? this.paymentPhoneNumber,
      paymentWarningMessage:
          paymentWarningMessage ?? this.paymentWarningMessage,
      paymentFeeTypeDescriptionWio:
          paymentFeeTypeDescriptionWio ?? this.paymentFeeTypeDescriptionWio,
      paymentFeeTypeAmountWio:
          paymentFeeTypeAmountWio ?? this.paymentFeeTypeAmountWio,
      paymentFeeTypeCurrencyWio:
          paymentFeeTypeCurrencyWio ?? this.paymentFeeTypeCurrencyWio,
      paymentFeeTypeDescriptionWioCorrespondentBank:
          paymentFeeTypeDescriptionWioCorrespondentBank ??
              this.paymentFeeTypeDescriptionWioCorrespondentBank,
      paymentFeeTypeAmountWioCorrespondentBank:
          paymentFeeTypeAmountWioCorrespondentBank ??
              this.paymentFeeTypeAmountWioCorrespondentBank,
      paymentFeeTypeCurrencyWioCorrespondentBank:
          paymentFeeTypeCurrencyWioCorrespondentBank ??
              this.paymentFeeTypeCurrencyWioCorrespondentBank,
      paymentFeeTypeDescriptionWise:
          paymentFeeTypeDescriptionWise ?? this.paymentFeeTypeDescriptionWise,
      paymentFeeTypeAmountWise:
          paymentFeeTypeAmountWise ?? this.paymentFeeTypeAmountWise,
      paymentFeeTypeCurrencyWise:
          paymentFeeTypeCurrencyWise ?? this.paymentFeeTypeCurrencyWise,
      internationalTargetCountry:
          internationalTargetCountry ?? this.internationalTargetCountry,
      internationalCompletedDateTime:
          internationalCompletedDateTime ?? this.internationalCompletedDateTime,
      internationalEstimatedDateTime:
          internationalEstimatedDateTime ?? this.internationalEstimatedDateTime,
      internationalFeeChargingType:
          internationalFeeChargingType ?? this.internationalFeeChargingType,
      internationalSwiftCode:
          internationalSwiftCode ?? this.internationalSwiftCode,
      internationalAccountNumber:
          internationalAccountNumber ?? this.internationalAccountNumber,
      internationalPurposeDescription: internationalPurposeDescription ??
          this.internationalPurposeDescription,
      internationalPurposeCode:
          internationalPurposeCode ?? this.internationalPurposeCode,
      internationalNotes: internationalNotes ?? this.internationalNotes,
      internationalBankName:
          internationalBankName ?? this.internationalBankName,
      internationalTargetAmount:
          internationalTargetAmount ?? this.internationalTargetAmount,
      internationalTargetAmountCurrency: internationalTargetAmountCurrency ??
          this.internationalTargetAmountCurrency,
      internationalTransferFee:
          internationalTransferFee ?? this.internationalTransferFee,
      internationalTransferFeeCurrency: internationalTransferFeeCurrency ??
          this.internationalTransferFeeCurrency,
      fabExpirationDate: fabExpirationDate ?? this.fabExpirationDate,
      fabBankName: fabBankName ?? this.fabBankName,
      fabTotalFeeAmount: fabTotalFeeAmount ?? this.fabTotalFeeAmount,
      fabTotalFeeAmountCurrency:
          fabTotalFeeAmountCurrency ?? this.fabTotalFeeAmountCurrency,
      fabUpdatedDate: fabUpdatedDate ?? this.fabUpdatedDate,
      fabCancellationReason:
          fabCancellationReason ?? this.fabCancellationReason,
      fabChequeNumber: fabChequeNumber ?? this.fabChequeNumber,
      fabChequeDate: fabChequeDate ?? this.fabChequeDate,
      fabFrontImageUrl: fabFrontImageUrl ?? this.fabFrontImageUrl,
      localPurposeDescription:
          localPurposeDescription ?? this.localPurposeDescription,
      localPurposeCode: localPurposeCode ?? this.localPurposeCode,
      localNote: localNote ?? this.localNote,
      localTargetCountry: localTargetCountry ?? this.localTargetCountry,
      localCompletedDateTime:
          localCompletedDateTime ?? this.localCompletedDateTime,
      localEstimatedDateTime:
          localEstimatedDateTime ?? this.localEstimatedDateTime,
      localAccountNumber: localAccountNumber ?? this.localAccountNumber,
      localSwiftCode: localSwiftCode ?? this.localSwiftCode,
      localBankName: localBankName ?? this.localBankName,
      localTargetAmount: localTargetAmount ?? this.localTargetAmount,
      localTargetAmountCurrency:
          localTargetAmountCurrency ?? this.localTargetAmountCurrency,
      localTransferSource: localTransferSource ?? this.localTransferSource,
      localTransferApplicationId:
          localTransferApplicationId ?? this.localTransferApplicationId,
      localRecipientName: localRecipientName ?? this.localRecipientName,
      npssExpirationDate: npssExpirationDate ?? this.npssExpirationDate,
      npssNotes: npssNotes ?? this.npssNotes,
      npssRtpInitiationDate:
          npssRtpInitiationDate ?? this.npssRtpInitiationDate,
      npssShowInPendingRtps:
          npssShowInPendingRtps ?? this.npssShowInPendingRtps,
      npssShowRtpResendButton:
          npssShowRtpResendButton ?? this.npssShowRtpResendButton,
      npssOriginalTransactionReference: npssOriginalTransactionReference ??
          this.npssOriginalTransactionReference,
      npssOriginalTransactionReason:
          npssOriginalTransactionReason ?? this.npssOriginalTransactionReason,
      npssOriginalTransactionDeeplink: npssOriginalTransactionDeeplink ??
          this.npssOriginalTransactionDeeplink,
      token: token ?? this.token,
      accNumberLast4Digits: accNumberLast4Digits ?? this.accNumberLast4Digits,
      cardMaskedPan: cardMaskedPan ?? this.cardMaskedPan,
      merchantCountryCode: merchantCountryCode ?? this.merchantCountryCode,
      originalCardTransactionCurrency: originalCardTransactionCurrency ??
          this.originalCardTransactionCurrency,
      originalCardTransactionAmount:
          originalCardTransactionAmount ?? this.originalCardTransactionAmount,
      earnedCashback: earnedCashback ?? this.earnedCashback,
      cashBackPercentage: cashBackPercentage ?? this.cashBackPercentage,
      cashBackTransactionCurrency:
          cashBackTransactionCurrency ?? this.cashBackTransactionCurrency,
      dividendTaxAmountValue:
          dividendTaxAmountValue ?? this.dividendTaxAmountValue,
      dividendTaxAmountCurrency:
          dividendTaxAmountCurrency ?? this.dividendTaxAmountCurrency,
      dividendAmountValue: dividendAmountValue ?? this.dividendAmountValue,
      dividendAmountCurrency:
          dividendAmountCurrency ?? this.dividendAmountCurrency,
      netDividendAmountValue:
          netDividendAmountValue ?? this.netDividendAmountValue,
      netDividendAmountCurrency:
          netDividendAmountCurrency ?? this.netDividendAmountCurrency,
      amountPerInstrumentValue:
          amountPerInstrumentValue ?? this.amountPerInstrumentValue,
      amountPerInstrumentCurrency:
          amountPerInstrumentCurrency ?? this.amountPerInstrumentCurrency,
      instrumentQuantity: instrumentQuantity ?? this.instrumentQuantity,
      brokerDividendTaxType:
          brokerDividendTaxType ?? this.brokerDividendTaxType,
      dividendTaxRate: dividendTaxRate ?? this.dividendTaxRate,
      securityLendingRebateAmountValue: securityLendingRebateAmountValue ??
          this.securityLendingRebateAmountValue,
      securityLendingRebateAmountCurrency:
          securityLendingRebateAmountCurrency ??
              this.securityLendingRebateAmountCurrency,
      securityLendingRebateTaxAmountCurrency:
          securityLendingRebateTaxAmountCurrency ??
              this.securityLendingRebateTaxAmountCurrency,
      securityLendingRebateTaxAmountValue:
          securityLendingRebateTaxAmountValue ??
              this.securityLendingRebateTaxAmountValue,
      securityLendingTransferredAmountCurrency:
          securityLendingTransferredAmountCurrency ??
              this.securityLendingTransferredAmountCurrency,
      securityLendingTransferredAmountValue:
          securityLendingTransferredAmountValue ??
              this.securityLendingTransferredAmountValue,
      securityLendingMonth: securityLendingMonth ?? this.securityLendingMonth,
      wealthManagementManagedPortfolioId: wealthManagementManagedPortfolioId ??
          this.wealthManagementManagedPortfolioId,
      wealthManagementProductName:
          wealthManagementProductName ?? this.wealthManagementProductName,
      wealthManagementProductCategory: wealthManagementProductCategory ??
          this.wealthManagementProductCategory,
      wealthManagementAccountName:
          wealthManagementAccountName ?? this.wealthManagementAccountName,
      wealthManagementEstimatedExecutionDeadline:
          wealthManagementEstimatedExecutionDeadline ??
              this.wealthManagementEstimatedExecutionDeadline,
      wealthManagementTrackerSteps:
          wealthManagementTrackerSteps ?? this.wealthManagementTrackerSteps,
      acquirerInstrumentName:
          acquirerInstrumentName ?? this.acquirerInstrumentName,
      acquirerInstrumentSymbol:
          acquirerInstrumentSymbol ?? this.acquirerInstrumentSymbol,
      acquirerInstrumentExchangeId:
          acquirerInstrumentExchangeId ?? this.acquirerInstrumentExchangeId,
      acquirerInstrumentImageUrl:
          acquirerInstrumentImageUrl ?? this.acquirerInstrumentImageUrl,
      acquireeInstrumentName:
          acquireeInstrumentName ?? this.acquireeInstrumentName,
      acquireeInstrumentSymbol:
          acquireeInstrumentSymbol ?? this.acquireeInstrumentSymbol,
      acquireeInstrumentExchangeId:
          acquireeInstrumentExchangeId ?? this.acquireeInstrumentExchangeId,
      acquireeInstrumentImageUrl:
          acquireeInstrumentImageUrl ?? this.acquireeInstrumentImageUrl,
      brokerAcquisitionAccountAmountValue:
          brokerAcquisitionAccountAmountValue ??
              this.brokerAcquisitionAccountAmountValue,
      brokerAcquisitionAccountAmountCurrency:
          brokerAcquisitionAccountAmountCurrency ??
              this.brokerAcquisitionAccountAmountCurrency,
      brokerAcquisitionPositionDelta:
          brokerAcquisitionPositionDelta ?? this.brokerAcquisitionPositionDelta,
      ipoCompanyName: ipoCompanyName ?? this.ipoCompanyName,
      ipoSubscriptionNumber:
          ipoSubscriptionNumber ?? this.ipoSubscriptionNumber,
      ipoLeverageValue: ipoLeverageValue ?? this.ipoLeverageValue,
      ipoLeverageCurrency: ipoLeverageCurrency ?? this.ipoLeverageCurrency,
      ipoAmountValue: ipoAmountValue ?? this.ipoAmountValue,
      ipoAmountCurrency: ipoAmountCurrency ?? this.ipoAmountCurrency,
      ipoTotalAmountValue: ipoTotalAmountValue ?? this.ipoTotalAmountValue,
      ipoTotalAmountCurrency:
          ipoTotalAmountCurrency ?? this.ipoTotalAmountCurrency,
      ipoCompanyLogo: ipoCompanyLogo ?? this.ipoCompanyLogo,
      ipoEnhancementValue: ipoEnhancementValue ?? this.ipoEnhancementValue,
      ipoEnhancementCurrency:
          ipoEnhancementCurrency ?? this.ipoEnhancementCurrency,
      ipoRefundAmountValue: ipoRefundAmountValue ?? this.ipoRefundAmountValue,
      ipoRefundAmountCurrency:
          ipoRefundAmountCurrency ?? this.ipoRefundAmountCurrency,
      ipoLeveragePaidValue: ipoLeveragePaidValue ?? this.ipoLeveragePaidValue,
      ipoLeveragePaidCurrency:
          ipoLeveragePaidCurrency ?? this.ipoLeveragePaidCurrency,
      ipoLeverageFeePercentage:
          ipoLeverageFeePercentage ?? this.ipoLeverageFeePercentage,
      ipoLeverageFeeValue: ipoLeverageFeeValue ?? this.ipoLeverageFeeValue,
      ipoLeverageFeeCurrency:
          ipoLeverageFeeCurrency ?? this.ipoLeverageFeeCurrency,
      ipoAllocationFeePercentage:
          ipoAllocationFeePercentage ?? this.ipoAllocationFeePercentage,
      ipoAllotedFeeMoney: ipoAllotedFeeMoney ?? this.ipoAllotedFeeMoney,
      ipoAllotedFeeMoneyCurrency:
          ipoAllotedFeeMoneyCurrency ?? this.ipoAllotedFeeMoneyCurrency,
      ipoAllocatedMoney: ipoAllocatedMoney ?? this.ipoAllocatedMoney,
      ipoAllocatedMoneyCurrency:
          ipoAllocatedMoneyCurrency ?? this.ipoAllocatedMoneyCurrency,
      dynamicDetails: dynamicDetails ?? this.dynamicDetails,
      linkedTransactionFeeDetails:
          linkedTransactionFeeDetails ?? this.linkedTransactionFeeDetails,
      cashbackCalculations: cashbackCalculations ?? this.cashbackCalculations,
      carbonEmissionInGrams:
          carbonEmissionInGrams ?? this.carbonEmissionInGrams,
      carbonEmissionInOunces:
          carbonEmissionInOunces ?? this.carbonEmissionInOunces,
      recurringTransferRuleId:
          recurringTransferRuleId ?? this.recurringTransferRuleId,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (productType.present) {
      map['product_type'] = Variable<String>(productType.value);
    }
    if (accountId.present) {
      map['account_id'] = Variable<String>(accountId.value);
    }
    if (transactionAccountType.present) {
      map['transaction_account_type'] =
          Variable<String>(transactionAccountType.value);
    }
    if (transactionType.present) {
      map['transaction_type'] = Variable<String>(transactionType.value);
    }
    if (transactionIdentifier.present) {
      map['transaction_identifier'] =
          Variable<String>(transactionIdentifier.value);
    }
    if (status.present) {
      map['status'] = Variable<String>(status.value);
    }
    if (referenceNumber.present) {
      map['reference_number'] = Variable<String>(referenceNumber.value);
    }
    if (transactionMode.present) {
      map['transaction_mode'] = Variable<String>(transactionMode.value);
    }
    if (transactionSubType.present) {
      map['transaction_sub_type'] = Variable<String>(transactionSubType.value);
    }
    if (internalTransactionStatus.present) {
      map['internal_transaction_status'] =
          Variable<String>(internalTransactionStatus.value);
    }
    if (transactionDateTime.present) {
      map['transaction_date_time'] =
          Variable<DateTime>(transactionDateTime.value);
    }
    if (debtorName.present) {
      map['debtor_name'] = Variable<String>(debtorName.value);
    }
    if (customerId.present) {
      map['customer_id'] = Variable<String>(customerId.value);
    }
    if (coreBankingIdentifier.present) {
      map['core_banking_identifier'] =
          Variable<String>(coreBankingIdentifier.value);
    }
    if (creditorName.present) {
      map['creditor_name'] = Variable<String>(creditorName.value);
    }
    if (description.present) {
      map['description'] = Variable<String>(description.value);
    }
    if (subDescription.present) {
      map['sub_description'] = Variable<String>(subDescription.value);
    }
    if (logoType.present) {
      map['logo_type'] = Variable<String>(logoType.value);
    }
    if (merchantName.present) {
      map['merchant_name'] = Variable<String>(merchantName.value);
    }
    if (transactionImageKey.present) {
      map['transaction_image_key'] =
          Variable<String>(transactionImageKey.value);
    }
    if (amountValue.present) {
      map['amount_value'] = Variable<double>(amountValue.value);
    }
    if (localAmount.present) {
      map['local_amount'] = Variable<double>(localAmount.value);
    }
    if (exchangeRate.present) {
      map['exchange_rate'] = Variable<double>(exchangeRate.value);
    }
    if (availableBalance.present) {
      map['available_balance'] = Variable<double>(availableBalance.value);
    }
    if (amountCurrency.present) {
      map['amount_currency'] = Variable<String>(amountCurrency.value);
    }
    if (executionDate.present) {
      map['execution_date'] = Variable<DateTime>(executionDate.value);
    }
    if (transactionCategory.present) {
      map['transaction_category'] = Variable<String>(transactionCategory.value);
    }
    if (tppExternalReferenceId.present) {
      map['tpp_external_reference_id'] =
          Variable<String>(tppExternalReferenceId.value);
    }
    if (fxFromAmount.present) {
      map['fx_from_amount'] = Variable<double>(fxFromAmount.value);
    }
    if (fxToAmount.present) {
      map['fx_to_amount'] = Variable<double>(fxToAmount.value);
    }
    if (fxFromAmountCurrency.present) {
      map['fx_from_amount_currency'] =
          Variable<String>(fxFromAmountCurrency.value);
    }
    if (fxToAmountCurrency.present) {
      map['fx_to_amount_currency'] = Variable<String>(fxToAmountCurrency.value);
    }
    if (fxFormattedExchangeRate.present) {
      map['fx_formatted_exchange_rate'] =
          Variable<String>(fxFormattedExchangeRate.value);
    }
    if (orderId.present) {
      map['order_id'] = Variable<String>(orderId.value);
    }
    if (portfolioId.present) {
      map['portfolio_id'] = Variable<String>(portfolioId.value);
    }
    if (instrumentId.present) {
      map['instrument_id'] = Variable<String>(instrumentId.value);
    }
    if (createdDateTime.present) {
      map['created_date_time'] = Variable<DateTime>(createdDateTime.value);
    }
    if (orderSide.present) {
      map['order_side'] = Variable<String>(orderSide.value);
    }
    if (instrumentName.present) {
      map['instrument_name'] = Variable<String>(instrumentName.value);
    }
    if (instrumentSymbol.present) {
      map['instrument_symbol'] = Variable<String>(instrumentSymbol.value);
    }
    if (instrumentExchangeId.present) {
      map['instrument_exchange_id'] =
          Variable<String>(instrumentExchangeId.value);
    }
    if (instrumentImageUrl.present) {
      map['instrument_image_url'] = Variable<String>(instrumentImageUrl.value);
    }
    if (orderType.present) {
      map['order_type'] = Variable<String>(orderType.value);
    }
    if (errorCode.present) {
      map['error_code'] = Variable<int>(errorCode.value);
    }
    if (errorMessage.present) {
      map['error_message'] = Variable<String>(errorMessage.value);
    }
    if (executedQuantity.present) {
      map['executed_quantity'] = Variable<double>(executedQuantity.value);
    }
    if (estimatedQuantity.present) {
      map['estimated_quantity'] = Variable<double>(estimatedQuantity.value);
    }
    if (executedAmountValue.present) {
      map['executed_amount_value'] =
          Variable<double>(executedAmountValue.value);
    }
    if (executedAmountCurrency.present) {
      map['executed_amount_currency'] =
          Variable<String>(executedAmountCurrency.value);
    }
    if (executedNetAmountValue.present) {
      map['executed_net_amount_value'] =
          Variable<double>(executedNetAmountValue.value);
    }
    if (executedNetAmountCurrency.present) {
      map['executed_net_amount_currency'] =
          Variable<String>(executedNetAmountCurrency.value);
    }
    if (estimatedAmountValue.present) {
      map['estimated_amount_value'] =
          Variable<double>(estimatedAmountValue.value);
    }
    if (estimatedAmountCurrency.present) {
      map['estimated_amount_currency'] =
          Variable<String>(estimatedAmountCurrency.value);
    }
    if (estimatedNetAmountValue.present) {
      map['estimated_net_amount_value'] =
          Variable<double>(estimatedNetAmountValue.value);
    }
    if (estimatedNetAmountCurrency.present) {
      map['estimated_net_amount_currency'] =
          Variable<String>(estimatedNetAmountCurrency.value);
    }
    if (commissionAmountValue.present) {
      map['commission_amount_value'] =
          Variable<double>(commissionAmountValue.value);
    }
    if (commissionAmountCurrency.present) {
      map['commission_amount_currency'] =
          Variable<String>(commissionAmountCurrency.value);
    }
    if (estimatedCommissionAmountValue.present) {
      map['estimated_commission_amount_value'] =
          Variable<double>(estimatedCommissionAmountValue.value);
    }
    if (estimatedCommissionAmountCurrency.present) {
      map['estimated_commission_amount_currency'] =
          Variable<String>(estimatedCommissionAmountCurrency.value);
    }
    if (vatValue.present) {
      map['vat_value'] = Variable<double>(vatValue.value);
    }
    if (vatCurrency.present) {
      map['vat_currency'] = Variable<String>(vatCurrency.value);
    }
    if (estimatedVatValue.present) {
      map['estimated_vat_value'] = Variable<double>(estimatedVatValue.value);
    }
    if (estimatedVatCurrency.present) {
      map['estimated_vat_currency'] =
          Variable<String>(estimatedVatCurrency.value);
    }
    if (averagePriceValue.present) {
      map['average_price_value'] = Variable<double>(averagePriceValue.value);
    }
    if (averagePriceCurrency.present) {
      map['average_price_currency'] =
          Variable<String>(averagePriceCurrency.value);
    }
    if (estimatedPriceValue.present) {
      map['estimated_price_value'] =
          Variable<double>(estimatedPriceValue.value);
    }
    if (estimatedPriceCurrency.present) {
      map['estimated_price_currency'] =
          Variable<String>(estimatedPriceCurrency.value);
    }
    if (executedTotalCommissionAmount.present) {
      map['executed_total_commission_amount'] =
          Variable<double>(executedTotalCommissionAmount.value);
    }
    if (executedTotalCommissionCurrency.present) {
      map['executed_total_commission_currency'] =
          Variable<String>(executedTotalCommissionCurrency.value);
    }
    if (estimatedTotalCommissionAmount.present) {
      map['estimated_total_commision_amount'] =
          Variable<double>(estimatedTotalCommissionAmount.value);
    }
    if (estimatedTotalCommissionCurrency.present) {
      map['estimated_total_commision_currency'] =
          Variable<String>(estimatedTotalCommissionCurrency.value);
    }
    if (commissionCalculationBasisPoints.present) {
      map['commission_calculation_basis_points'] =
          Variable<double>(commissionCalculationBasisPoints.value);
    }
    if (commissionCalculationBaseAmount.present) {
      map['commission_calculation_base_amount'] =
          Variable<double>(commissionCalculationBaseAmount.value);
    }
    if (commissionCalculationMinAmount.present) {
      map['commission_calculation_min_amount'] =
          Variable<double>(commissionCalculationMinAmount.value);
    }
    if (commissionCalculationBaseAmountCurrency.present) {
      map['commission_calculation_base_amount_currency'] =
          Variable<String>(commissionCalculationBaseAmountCurrency.value);
    }
    if (commissionCalculationMinAmountCurrency.present) {
      map['commission_calculation_min_amount_currency'] =
          Variable<String>(commissionCalculationMinAmountCurrency.value);
    }
    if (instrumentExchangeCode.present) {
      map['instrument_exchange_code'] =
          Variable<String>(instrumentExchangeCode.value);
    }
    if (recurringOrderTemplateId.present) {
      map['recurring_order_template_id'] =
          Variable<String>(recurringOrderTemplateId.value);
    }
    if (recurringOrderAccountName.present) {
      map['recurring_order_account_name'] =
          Variable<String>(recurringOrderAccountName.value);
    }
    if (recurringOrderFrequencyType.present) {
      map['recurring_order_frequency_type'] =
          Variable<String>(recurringOrderFrequencyType.value);
    }
    if (recurringOrderFrequencyDayOfWeek.present) {
      map['recurring_order_frequency_day_of_week'] =
          Variable<String>(recurringOrderFrequencyDayOfWeek.value);
    }
    if (recurringOrderFrequencyDayOfMonth.present) {
      map['recurring_order_frequency_day_of_month'] =
          Variable<int>(recurringOrderFrequencyDayOfMonth.value);
    }
    if (expirationDate.present) {
      map['expiration_date'] = Variable<DateTime>(expirationDate.value);
    }
    if (isPaymentProofReady.present) {
      map['is_payment_proof_ready'] = Variable<bool>(isPaymentProofReady.value);
    }
    if (isPaymentTrackingAvailable.present) {
      map['is_payment_tracking_available'] =
          Variable<bool>(isPaymentTrackingAvailable.value);
    }
    if (paymentFeeTotalAmount.present) {
      map['payment_fee_total_amount'] =
          Variable<double>(paymentFeeTotalAmount.value);
    }
    if (paymentFeeTotalCurrency.present) {
      map['payment_fee_total_currency'] =
          Variable<String>(paymentFeeTotalCurrency.value);
    }
    if (paymentTargetCountryCode.present) {
      map['payment_target_country_code'] =
          Variable<String>(paymentTargetCountryCode.value);
    }
    if (paymentPhoneNumber.present) {
      map['payment_phone_number'] = Variable<String>(paymentPhoneNumber.value);
    }
    if (paymentWarningMessage.present) {
      map['payment_warning_message'] =
          Variable<String>(paymentWarningMessage.value);
    }
    if (paymentFeeTypeDescriptionWio.present) {
      map['payment_feetype_description_Wio'] =
          Variable<String>(paymentFeeTypeDescriptionWio.value);
    }
    if (paymentFeeTypeAmountWio.present) {
      map['payment_feetype_amount_Wio'] =
          Variable<double>(paymentFeeTypeAmountWio.value);
    }
    if (paymentFeeTypeCurrencyWio.present) {
      map['payment_feetype_currency_Wio'] =
          Variable<String>(paymentFeeTypeCurrencyWio.value);
    }
    if (paymentFeeTypeDescriptionWioCorrespondentBank.present) {
      map['payment_feetype_description_WioCorrespondentBank'] =
          Variable<String>(paymentFeeTypeDescriptionWioCorrespondentBank.value);
    }
    if (paymentFeeTypeAmountWioCorrespondentBank.present) {
      map['payment_feetype_amount_WioCorrespondentBank'] =
          Variable<double>(paymentFeeTypeAmountWioCorrespondentBank.value);
    }
    if (paymentFeeTypeCurrencyWioCorrespondentBank.present) {
      map['payment_feetype_currency_WioCorrespondentBank'] =
          Variable<String>(paymentFeeTypeCurrencyWioCorrespondentBank.value);
    }
    if (paymentFeeTypeDescriptionWise.present) {
      map['payment_feetype_description_Wise'] =
          Variable<String>(paymentFeeTypeDescriptionWise.value);
    }
    if (paymentFeeTypeAmountWise.present) {
      map['payment_feetype_amount_Wise'] =
          Variable<double>(paymentFeeTypeAmountWise.value);
    }
    if (paymentFeeTypeCurrencyWise.present) {
      map['payment_feetype_currency_Wise'] =
          Variable<String>(paymentFeeTypeCurrencyWise.value);
    }
    if (internationalTargetCountry.present) {
      map['international_target_country'] =
          Variable<String>(internationalTargetCountry.value);
    }
    if (internationalCompletedDateTime.present) {
      map['international_completed_date_time'] =
          Variable<DateTime>(internationalCompletedDateTime.value);
    }
    if (internationalEstimatedDateTime.present) {
      map['international_estimated_date_time'] =
          Variable<String>(internationalEstimatedDateTime.value);
    }
    if (internationalFeeChargingType.present) {
      map['international_fee_charging_type'] =
          Variable<String>(internationalFeeChargingType.value);
    }
    if (internationalSwiftCode.present) {
      map['international_swift_code'] =
          Variable<String>(internationalSwiftCode.value);
    }
    if (internationalAccountNumber.present) {
      map['international_account_number'] =
          Variable<String>(internationalAccountNumber.value);
    }
    if (internationalPurposeDescription.present) {
      map['international_purpose_description'] =
          Variable<String>(internationalPurposeDescription.value);
    }
    if (internationalPurposeCode.present) {
      map['international_purpose_code'] =
          Variable<String>(internationalPurposeCode.value);
    }
    if (internationalNotes.present) {
      map['international_notes'] = Variable<String>(internationalNotes.value);
    }
    if (internationalBankName.present) {
      map['international_bank_name'] =
          Variable<String>(internationalBankName.value);
    }
    if (internationalTargetAmount.present) {
      map['international_target_amount'] =
          Variable<double>(internationalTargetAmount.value);
    }
    if (internationalTargetAmountCurrency.present) {
      map['international_target_amount_currency'] =
          Variable<String>(internationalTargetAmountCurrency.value);
    }
    if (internationalTransferFee.present) {
      map['international_transfer_fee'] =
          Variable<double>(internationalTransferFee.value);
    }
    if (internationalTransferFeeCurrency.present) {
      map['international_transfer_fee_currency'] =
          Variable<String>(internationalTransferFeeCurrency.value);
    }
    if (fabExpirationDate.present) {
      map['fab_expiration_date'] = Variable<DateTime>(fabExpirationDate.value);
    }
    if (fabBankName.present) {
      map['fab_bank_name'] = Variable<String>(fabBankName.value);
    }
    if (fabTotalFeeAmount.present) {
      map['fab_total_fee_amount'] = Variable<double>(fabTotalFeeAmount.value);
    }
    if (fabTotalFeeAmountCurrency.present) {
      map['fab_total_fee_amount_currency'] =
          Variable<String>(fabTotalFeeAmountCurrency.value);
    }
    if (fabUpdatedDate.present) {
      map['fab_updated_date'] = Variable<DateTime>(fabUpdatedDate.value);
    }
    if (fabCancellationReason.present) {
      map['fab_cancellation_reason'] =
          Variable<String>(fabCancellationReason.value);
    }
    if (fabChequeNumber.present) {
      map['fab_cheque_number'] = Variable<String>(fabChequeNumber.value);
    }
    if (fabChequeDate.present) {
      map['fab_cheque_date'] = Variable<DateTime>(fabChequeDate.value);
    }
    if (fabFrontImageUrl.present) {
      map['fab_front_image_url'] = Variable<String>(fabFrontImageUrl.value);
    }
    if (localPurposeDescription.present) {
      map['local_purpose_description'] =
          Variable<String>(localPurposeDescription.value);
    }
    if (localPurposeCode.present) {
      map['local_purpose_code'] = Variable<String>(localPurposeCode.value);
    }
    if (localNote.present) {
      map['local_note'] = Variable<String>(localNote.value);
    }
    if (localTargetCountry.present) {
      map['local_target_country'] = Variable<String>(localTargetCountry.value);
    }
    if (localCompletedDateTime.present) {
      map['local_completed_date_time'] =
          Variable<DateTime>(localCompletedDateTime.value);
    }
    if (localEstimatedDateTime.present) {
      map['local_estimated_date_time'] =
          Variable<DateTime>(localEstimatedDateTime.value);
    }
    if (localAccountNumber.present) {
      map['local_account_number'] = Variable<String>(localAccountNumber.value);
    }
    if (localSwiftCode.present) {
      map['local_swift_code'] = Variable<String>(localSwiftCode.value);
    }
    if (localBankName.present) {
      map['local_bank_name'] = Variable<String>(localBankName.value);
    }
    if (localTargetAmount.present) {
      map['local_target_amount'] = Variable<double>(localTargetAmount.value);
    }
    if (localTargetAmountCurrency.present) {
      map['local_target_amount_currency'] =
          Variable<String>(localTargetAmountCurrency.value);
    }
    if (localTransferSource.present) {
      map['local_transfer_source'] =
          Variable<String>(localTransferSource.value);
    }
    if (localTransferApplicationId.present) {
      map['local_transfer_application_id'] =
          Variable<String>(localTransferApplicationId.value);
    }
    if (localRecipientName.present) {
      map['local_recipient_name'] = Variable<String>(localRecipientName.value);
    }
    if (npssExpirationDate.present) {
      map['npss_expiration_date'] =
          Variable<DateTime>(npssExpirationDate.value);
    }
    if (npssNotes.present) {
      map['npss_notes'] = Variable<String>(npssNotes.value);
    }
    if (npssRtpInitiationDate.present) {
      map['npss_rtp_initiation_date'] =
          Variable<DateTime>(npssRtpInitiationDate.value);
    }
    if (npssShowInPendingRtps.present) {
      map['npss_show_in_pending_rtps'] =
          Variable<bool>(npssShowInPendingRtps.value);
    }
    if (npssShowRtpResendButton.present) {
      map['npss_show_rtp_resend_button'] =
          Variable<bool>(npssShowRtpResendButton.value);
    }
    if (npssOriginalTransactionReference.present) {
      map['npss_original_transaction_reference'] =
          Variable<String>(npssOriginalTransactionReference.value);
    }
    if (npssOriginalTransactionReason.present) {
      map['npss_original_transaction_reason'] =
          Variable<String>(npssOriginalTransactionReason.value);
    }
    if (npssOriginalTransactionDeeplink.present) {
      map['npss_original_transaction_deeplink'] =
          Variable<String>(npssOriginalTransactionDeeplink.value);
    }
    if (token.present) {
      map['token'] = Variable<String>(token.value);
    }
    if (accNumberLast4Digits.present) {
      map['acc_number_last_4digits'] =
          Variable<String>(accNumberLast4Digits.value);
    }
    if (cardMaskedPan.present) {
      map['card_masked_pan'] = Variable<String>(cardMaskedPan.value);
    }
    if (merchantCountryCode.present) {
      map['merchant_country_code'] =
          Variable<String>(merchantCountryCode.value);
    }
    if (originalCardTransactionCurrency.present) {
      map['original_card_transaction_currency'] =
          Variable<String>(originalCardTransactionCurrency.value);
    }
    if (originalCardTransactionAmount.present) {
      map['original_card_transaction_amount'] =
          Variable<double>(originalCardTransactionAmount.value);
    }
    if (earnedCashback.present) {
      map['earned_cash_back'] = Variable<double>(earnedCashback.value);
    }
    if (cashBackPercentage.present) {
      map['cash_back_percentage'] = Variable<double>(cashBackPercentage.value);
    }
    if (cashBackTransactionCurrency.present) {
      map['cash_back_transaction_currency'] =
          Variable<String>(cashBackTransactionCurrency.value);
    }
    if (dividendTaxAmountValue.present) {
      map['dividend_tax_amount_value'] =
          Variable<double>(dividendTaxAmountValue.value);
    }
    if (dividendTaxAmountCurrency.present) {
      map['dividend_tax_amount_currency'] =
          Variable<String>(dividendTaxAmountCurrency.value);
    }
    if (dividendAmountValue.present) {
      map['dividend_amount_value'] =
          Variable<double>(dividendAmountValue.value);
    }
    if (dividendAmountCurrency.present) {
      map['dividend_amount_currency'] =
          Variable<String>(dividendAmountCurrency.value);
    }
    if (netDividendAmountValue.present) {
      map['net_dividend_amount_value'] =
          Variable<double>(netDividendAmountValue.value);
    }
    if (netDividendAmountCurrency.present) {
      map['net_dividend_amount_currency'] =
          Variable<String>(netDividendAmountCurrency.value);
    }
    if (amountPerInstrumentValue.present) {
      map['amount_per_instrument_value'] =
          Variable<double>(amountPerInstrumentValue.value);
    }
    if (amountPerInstrumentCurrency.present) {
      map['amount_per_instrument_currency'] =
          Variable<String>(amountPerInstrumentCurrency.value);
    }
    if (instrumentQuantity.present) {
      map['instrument_quantity'] = Variable<double>(instrumentQuantity.value);
    }
    if (brokerDividendTaxType.present) {
      map['broker_dividend_tax_type'] =
          Variable<String>(brokerDividendTaxType.value);
    }
    if (dividendTaxRate.present) {
      map['dividend_tax_rate'] = Variable<double>(dividendTaxRate.value);
    }
    if (securityLendingRebateAmountValue.present) {
      map['security_lending_rebate_amount_value'] =
          Variable<double>(securityLendingRebateAmountValue.value);
    }
    if (securityLendingRebateAmountCurrency.present) {
      map['security_lending_rebate_amount_currency'] =
          Variable<String>(securityLendingRebateAmountCurrency.value);
    }
    if (securityLendingRebateTaxAmountCurrency.present) {
      map['security_lending_rebate_tax_amount_currency'] =
          Variable<String>(securityLendingRebateTaxAmountCurrency.value);
    }
    if (securityLendingRebateTaxAmountValue.present) {
      map['security_lending_rebate_tax_amount_value'] =
          Variable<double>(securityLendingRebateTaxAmountValue.value);
    }
    if (securityLendingTransferredAmountCurrency.present) {
      map['security_lending_transferred_amount_currency'] =
          Variable<String>(securityLendingTransferredAmountCurrency.value);
    }
    if (securityLendingTransferredAmountValue.present) {
      map['security_lending_transferred_amount_value'] =
          Variable<double>(securityLendingTransferredAmountValue.value);
    }
    if (securityLendingMonth.present) {
      map['security_lending_month'] = Variable<int>(securityLendingMonth.value);
    }
    if (wealthManagementManagedPortfolioId.present) {
      map['wealth_management_managed_portfolio_id'] =
          Variable<String>(wealthManagementManagedPortfolioId.value);
    }
    if (wealthManagementProductName.present) {
      map['wealth_management_product_name'] =
          Variable<String>(wealthManagementProductName.value);
    }
    if (wealthManagementProductCategory.present) {
      map['wealth_management_product_category'] =
          Variable<String>(wealthManagementProductCategory.value);
    }
    if (wealthManagementAccountName.present) {
      map['wealth_management_account_name'] =
          Variable<String>(wealthManagementAccountName.value);
    }
    if (wealthManagementEstimatedExecutionDeadline.present) {
      map['wealth_management_estimated_execution_deadline'] =
          Variable<String>(wealthManagementEstimatedExecutionDeadline.value);
    }
    if (wealthManagementTrackerSteps.present) {
      map['wealth_management_tracker_steps'] =
          Variable<String>(wealthManagementTrackerSteps.value);
    }
    if (acquirerInstrumentName.present) {
      map['acquirer_instrument_name'] =
          Variable<String>(acquirerInstrumentName.value);
    }
    if (acquirerInstrumentSymbol.present) {
      map['acquirer_instrument_symbol'] =
          Variable<String>(acquirerInstrumentSymbol.value);
    }
    if (acquirerInstrumentExchangeId.present) {
      map['acquirer_instrument_exchange_id'] =
          Variable<String>(acquirerInstrumentExchangeId.value);
    }
    if (acquirerInstrumentImageUrl.present) {
      map['acquirer_instrument_image_url'] =
          Variable<String>(acquirerInstrumentImageUrl.value);
    }
    if (acquireeInstrumentName.present) {
      map['acquiree_instrument_name'] =
          Variable<String>(acquireeInstrumentName.value);
    }
    if (acquireeInstrumentSymbol.present) {
      map['acquiree_instrument_symbol'] =
          Variable<String>(acquireeInstrumentSymbol.value);
    }
    if (acquireeInstrumentExchangeId.present) {
      map['acquiree_instrument_exchange_id'] =
          Variable<String>(acquireeInstrumentExchangeId.value);
    }
    if (acquireeInstrumentImageUrl.present) {
      map['acquiree_instrument_image_url'] =
          Variable<String>(acquireeInstrumentImageUrl.value);
    }
    if (brokerAcquisitionAccountAmountValue.present) {
      map['broker_acquisition_account_amount_value'] =
          Variable<double>(brokerAcquisitionAccountAmountValue.value);
    }
    if (brokerAcquisitionAccountAmountCurrency.present) {
      map['broker_acquisition_account_amount_currency'] =
          Variable<String>(brokerAcquisitionAccountAmountCurrency.value);
    }
    if (brokerAcquisitionPositionDelta.present) {
      map['broker_acquisition_position_delta'] =
          Variable<double>(brokerAcquisitionPositionDelta.value);
    }
    if (ipoCompanyName.present) {
      map['ipo_company_name'] = Variable<String>(ipoCompanyName.value);
    }
    if (ipoSubscriptionNumber.present) {
      map['ipo_subscription_number'] =
          Variable<String>(ipoSubscriptionNumber.value);
    }
    if (ipoLeverageValue.present) {
      map['ipo_leverage_value'] = Variable<double>(ipoLeverageValue.value);
    }
    if (ipoLeverageCurrency.present) {
      map['ipo_leverage_currency'] =
          Variable<String>(ipoLeverageCurrency.value);
    }
    if (ipoAmountValue.present) {
      map['ipo_amount_value'] = Variable<double>(ipoAmountValue.value);
    }
    if (ipoAmountCurrency.present) {
      map['ipo_amount_currency'] = Variable<String>(ipoAmountCurrency.value);
    }
    if (ipoTotalAmountValue.present) {
      map['ipo_total_amount_value'] =
          Variable<double>(ipoTotalAmountValue.value);
    }
    if (ipoTotalAmountCurrency.present) {
      map['ipo_total_amount_currency'] =
          Variable<String>(ipoTotalAmountCurrency.value);
    }
    if (ipoCompanyLogo.present) {
      map['ipo_company_logo'] = Variable<String>(ipoCompanyLogo.value);
    }
    if (ipoEnhancementValue.present) {
      map['ipo_enhancement_value'] =
          Variable<double>(ipoEnhancementValue.value);
    }
    if (ipoEnhancementCurrency.present) {
      map['ipo_enhancement_currency'] =
          Variable<String>(ipoEnhancementCurrency.value);
    }
    if (ipoRefundAmountValue.present) {
      map['ipo_refund_amount_value'] =
          Variable<double>(ipoRefundAmountValue.value);
    }
    if (ipoRefundAmountCurrency.present) {
      map['ipo_refund_amount_currency'] =
          Variable<String>(ipoRefundAmountCurrency.value);
    }
    if (ipoLeveragePaidValue.present) {
      map['ipo_leverage_paid_value'] =
          Variable<double>(ipoLeveragePaidValue.value);
    }
    if (ipoLeveragePaidCurrency.present) {
      map['ipo_leverage_paid_currency'] =
          Variable<String>(ipoLeveragePaidCurrency.value);
    }
    if (ipoLeverageFeePercentage.present) {
      map['ipo_leverage_fee_percentage'] =
          Variable<double>(ipoLeverageFeePercentage.value);
    }
    if (ipoLeverageFeeValue.present) {
      map['ipo_leverage_fee_value'] =
          Variable<double>(ipoLeverageFeeValue.value);
    }
    if (ipoLeverageFeeCurrency.present) {
      map['ipo_leverage_fee_currency'] =
          Variable<String>(ipoLeverageFeeCurrency.value);
    }
    if (ipoAllocationFeePercentage.present) {
      map['ipo_allocation_fee_percentage'] =
          Variable<double>(ipoAllocationFeePercentage.value);
    }
    if (ipoAllotedFeeMoney.present) {
      map['ipo_alloted_fee_money'] = Variable<double>(ipoAllotedFeeMoney.value);
    }
    if (ipoAllotedFeeMoneyCurrency.present) {
      map['ipo_alloted_fee_money_currency'] =
          Variable<String>(ipoAllotedFeeMoneyCurrency.value);
    }
    if (ipoAllocatedMoney.present) {
      map['ipo_allocated_money'] = Variable<double>(ipoAllocatedMoney.value);
    }
    if (ipoAllocatedMoneyCurrency.present) {
      map['ipo_allocated_money_currency'] =
          Variable<String>(ipoAllocatedMoneyCurrency.value);
    }
    if (dynamicDetails.present) {
      map['dynamic_details'] = Variable<String>(dynamicDetails.value);
    }
    if (linkedTransactionFeeDetails.present) {
      map['linked_transaction_fee_details'] =
          Variable<String>(linkedTransactionFeeDetails.value);
    }
    if (cashbackCalculations.present) {
      map['cashback_calculations'] =
          Variable<String>(cashbackCalculations.value);
    }
    if (carbonEmissionInGrams.present) {
      map['carbon_emission_in_grams'] =
          Variable<double>(carbonEmissionInGrams.value);
    }
    if (carbonEmissionInOunces.present) {
      map['carbon_emission_in_ounces'] =
          Variable<double>(carbonEmissionInOunces.value);
    }
    if (recurringTransferRuleId.present) {
      map['recurring_transfer_rule_id'] =
          Variable<String>(recurringTransferRuleId.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('TransactionsCompanion(')
          ..write('id: $id, ')
          ..write('productType: $productType, ')
          ..write('accountId: $accountId, ')
          ..write('transactionAccountType: $transactionAccountType, ')
          ..write('transactionType: $transactionType, ')
          ..write('transactionIdentifier: $transactionIdentifier, ')
          ..write('status: $status, ')
          ..write('referenceNumber: $referenceNumber, ')
          ..write('transactionMode: $transactionMode, ')
          ..write('transactionSubType: $transactionSubType, ')
          ..write('internalTransactionStatus: $internalTransactionStatus, ')
          ..write('transactionDateTime: $transactionDateTime, ')
          ..write('debtorName: $debtorName, ')
          ..write('customerId: $customerId, ')
          ..write('coreBankingIdentifier: $coreBankingIdentifier, ')
          ..write('creditorName: $creditorName, ')
          ..write('description: $description, ')
          ..write('subDescription: $subDescription, ')
          ..write('logoType: $logoType, ')
          ..write('merchantName: $merchantName, ')
          ..write('transactionImageKey: $transactionImageKey, ')
          ..write('amountValue: $amountValue, ')
          ..write('localAmount: $localAmount, ')
          ..write('exchangeRate: $exchangeRate, ')
          ..write('availableBalance: $availableBalance, ')
          ..write('amountCurrency: $amountCurrency, ')
          ..write('executionDate: $executionDate, ')
          ..write('transactionCategory: $transactionCategory, ')
          ..write('tppExternalReferenceId: $tppExternalReferenceId, ')
          ..write('fxFromAmount: $fxFromAmount, ')
          ..write('fxToAmount: $fxToAmount, ')
          ..write('fxFromAmountCurrency: $fxFromAmountCurrency, ')
          ..write('fxToAmountCurrency: $fxToAmountCurrency, ')
          ..write('fxFormattedExchangeRate: $fxFormattedExchangeRate, ')
          ..write('orderId: $orderId, ')
          ..write('portfolioId: $portfolioId, ')
          ..write('instrumentId: $instrumentId, ')
          ..write('createdDateTime: $createdDateTime, ')
          ..write('orderSide: $orderSide, ')
          ..write('instrumentName: $instrumentName, ')
          ..write('instrumentSymbol: $instrumentSymbol, ')
          ..write('instrumentExchangeId: $instrumentExchangeId, ')
          ..write('instrumentImageUrl: $instrumentImageUrl, ')
          ..write('orderType: $orderType, ')
          ..write('errorCode: $errorCode, ')
          ..write('errorMessage: $errorMessage, ')
          ..write('executedQuantity: $executedQuantity, ')
          ..write('estimatedQuantity: $estimatedQuantity, ')
          ..write('executedAmountValue: $executedAmountValue, ')
          ..write('executedAmountCurrency: $executedAmountCurrency, ')
          ..write('executedNetAmountValue: $executedNetAmountValue, ')
          ..write('executedNetAmountCurrency: $executedNetAmountCurrency, ')
          ..write('estimatedAmountValue: $estimatedAmountValue, ')
          ..write('estimatedAmountCurrency: $estimatedAmountCurrency, ')
          ..write('estimatedNetAmountValue: $estimatedNetAmountValue, ')
          ..write('estimatedNetAmountCurrency: $estimatedNetAmountCurrency, ')
          ..write('commissionAmountValue: $commissionAmountValue, ')
          ..write('commissionAmountCurrency: $commissionAmountCurrency, ')
          ..write(
              'estimatedCommissionAmountValue: $estimatedCommissionAmountValue, ')
          ..write(
              'estimatedCommissionAmountCurrency: $estimatedCommissionAmountCurrency, ')
          ..write('vatValue: $vatValue, ')
          ..write('vatCurrency: $vatCurrency, ')
          ..write('estimatedVatValue: $estimatedVatValue, ')
          ..write('estimatedVatCurrency: $estimatedVatCurrency, ')
          ..write('averagePriceValue: $averagePriceValue, ')
          ..write('averagePriceCurrency: $averagePriceCurrency, ')
          ..write('estimatedPriceValue: $estimatedPriceValue, ')
          ..write('estimatedPriceCurrency: $estimatedPriceCurrency, ')
          ..write(
              'executedTotalCommissionAmount: $executedTotalCommissionAmount, ')
          ..write(
              'executedTotalCommissionCurrency: $executedTotalCommissionCurrency, ')
          ..write(
              'estimatedTotalCommissionAmount: $estimatedTotalCommissionAmount, ')
          ..write(
              'estimatedTotalCommissionCurrency: $estimatedTotalCommissionCurrency, ')
          ..write(
              'commissionCalculationBasisPoints: $commissionCalculationBasisPoints, ')
          ..write(
              'commissionCalculationBaseAmount: $commissionCalculationBaseAmount, ')
          ..write(
              'commissionCalculationMinAmount: $commissionCalculationMinAmount, ')
          ..write(
              'commissionCalculationBaseAmountCurrency: $commissionCalculationBaseAmountCurrency, ')
          ..write(
              'commissionCalculationMinAmountCurrency: $commissionCalculationMinAmountCurrency, ')
          ..write('instrumentExchangeCode: $instrumentExchangeCode, ')
          ..write('recurringOrderTemplateId: $recurringOrderTemplateId, ')
          ..write('recurringOrderAccountName: $recurringOrderAccountName, ')
          ..write('recurringOrderFrequencyType: $recurringOrderFrequencyType, ')
          ..write(
              'recurringOrderFrequencyDayOfWeek: $recurringOrderFrequencyDayOfWeek, ')
          ..write(
              'recurringOrderFrequencyDayOfMonth: $recurringOrderFrequencyDayOfMonth, ')
          ..write('expirationDate: $expirationDate, ')
          ..write('isPaymentProofReady: $isPaymentProofReady, ')
          ..write('isPaymentTrackingAvailable: $isPaymentTrackingAvailable, ')
          ..write('paymentFeeTotalAmount: $paymentFeeTotalAmount, ')
          ..write('paymentFeeTotalCurrency: $paymentFeeTotalCurrency, ')
          ..write('paymentTargetCountryCode: $paymentTargetCountryCode, ')
          ..write('paymentPhoneNumber: $paymentPhoneNumber, ')
          ..write('paymentWarningMessage: $paymentWarningMessage, ')
          ..write(
              'paymentFeeTypeDescriptionWio: $paymentFeeTypeDescriptionWio, ')
          ..write('paymentFeeTypeAmountWio: $paymentFeeTypeAmountWio, ')
          ..write('paymentFeeTypeCurrencyWio: $paymentFeeTypeCurrencyWio, ')
          ..write(
              'paymentFeeTypeDescriptionWioCorrespondentBank: $paymentFeeTypeDescriptionWioCorrespondentBank, ')
          ..write(
              'paymentFeeTypeAmountWioCorrespondentBank: $paymentFeeTypeAmountWioCorrespondentBank, ')
          ..write(
              'paymentFeeTypeCurrencyWioCorrespondentBank: $paymentFeeTypeCurrencyWioCorrespondentBank, ')
          ..write(
              'paymentFeeTypeDescriptionWise: $paymentFeeTypeDescriptionWise, ')
          ..write('paymentFeeTypeAmountWise: $paymentFeeTypeAmountWise, ')
          ..write('paymentFeeTypeCurrencyWise: $paymentFeeTypeCurrencyWise, ')
          ..write('internationalTargetCountry: $internationalTargetCountry, ')
          ..write(
              'internationalCompletedDateTime: $internationalCompletedDateTime, ')
          ..write(
              'internationalEstimatedDateTime: $internationalEstimatedDateTime, ')
          ..write(
              'internationalFeeChargingType: $internationalFeeChargingType, ')
          ..write('internationalSwiftCode: $internationalSwiftCode, ')
          ..write('internationalAccountNumber: $internationalAccountNumber, ')
          ..write(
              'internationalPurposeDescription: $internationalPurposeDescription, ')
          ..write('internationalPurposeCode: $internationalPurposeCode, ')
          ..write('internationalNotes: $internationalNotes, ')
          ..write('internationalBankName: $internationalBankName, ')
          ..write('internationalTargetAmount: $internationalTargetAmount, ')
          ..write(
              'internationalTargetAmountCurrency: $internationalTargetAmountCurrency, ')
          ..write('internationalTransferFee: $internationalTransferFee, ')
          ..write(
              'internationalTransferFeeCurrency: $internationalTransferFeeCurrency, ')
          ..write('fabExpirationDate: $fabExpirationDate, ')
          ..write('fabBankName: $fabBankName, ')
          ..write('fabTotalFeeAmount: $fabTotalFeeAmount, ')
          ..write('fabTotalFeeAmountCurrency: $fabTotalFeeAmountCurrency, ')
          ..write('fabUpdatedDate: $fabUpdatedDate, ')
          ..write('fabCancellationReason: $fabCancellationReason, ')
          ..write('fabChequeNumber: $fabChequeNumber, ')
          ..write('fabChequeDate: $fabChequeDate, ')
          ..write('fabFrontImageUrl: $fabFrontImageUrl, ')
          ..write('localPurposeDescription: $localPurposeDescription, ')
          ..write('localPurposeCode: $localPurposeCode, ')
          ..write('localNote: $localNote, ')
          ..write('localTargetCountry: $localTargetCountry, ')
          ..write('localCompletedDateTime: $localCompletedDateTime, ')
          ..write('localEstimatedDateTime: $localEstimatedDateTime, ')
          ..write('localAccountNumber: $localAccountNumber, ')
          ..write('localSwiftCode: $localSwiftCode, ')
          ..write('localBankName: $localBankName, ')
          ..write('localTargetAmount: $localTargetAmount, ')
          ..write('localTargetAmountCurrency: $localTargetAmountCurrency, ')
          ..write('localTransferSource: $localTransferSource, ')
          ..write('localTransferApplicationId: $localTransferApplicationId, ')
          ..write('localRecipientName: $localRecipientName, ')
          ..write('npssExpirationDate: $npssExpirationDate, ')
          ..write('npssNotes: $npssNotes, ')
          ..write('npssRtpInitiationDate: $npssRtpInitiationDate, ')
          ..write('npssShowInPendingRtps: $npssShowInPendingRtps, ')
          ..write('npssShowRtpResendButton: $npssShowRtpResendButton, ')
          ..write(
              'npssOriginalTransactionReference: $npssOriginalTransactionReference, ')
          ..write(
              'npssOriginalTransactionReason: $npssOriginalTransactionReason, ')
          ..write(
              'npssOriginalTransactionDeeplink: $npssOriginalTransactionDeeplink, ')
          ..write('token: $token, ')
          ..write('accNumberLast4Digits: $accNumberLast4Digits, ')
          ..write('cardMaskedPan: $cardMaskedPan, ')
          ..write('merchantCountryCode: $merchantCountryCode, ')
          ..write(
              'originalCardTransactionCurrency: $originalCardTransactionCurrency, ')
          ..write(
              'originalCardTransactionAmount: $originalCardTransactionAmount, ')
          ..write('earnedCashback: $earnedCashback, ')
          ..write('cashBackPercentage: $cashBackPercentage, ')
          ..write('cashBackTransactionCurrency: $cashBackTransactionCurrency, ')
          ..write('dividendTaxAmountValue: $dividendTaxAmountValue, ')
          ..write('dividendTaxAmountCurrency: $dividendTaxAmountCurrency, ')
          ..write('dividendAmountValue: $dividendAmountValue, ')
          ..write('dividendAmountCurrency: $dividendAmountCurrency, ')
          ..write('netDividendAmountValue: $netDividendAmountValue, ')
          ..write('netDividendAmountCurrency: $netDividendAmountCurrency, ')
          ..write('amountPerInstrumentValue: $amountPerInstrumentValue, ')
          ..write('amountPerInstrumentCurrency: $amountPerInstrumentCurrency, ')
          ..write('instrumentQuantity: $instrumentQuantity, ')
          ..write('brokerDividendTaxType: $brokerDividendTaxType, ')
          ..write('dividendTaxRate: $dividendTaxRate, ')
          ..write(
              'securityLendingRebateAmountValue: $securityLendingRebateAmountValue, ')
          ..write(
              'securityLendingRebateAmountCurrency: $securityLendingRebateAmountCurrency, ')
          ..write(
              'securityLendingRebateTaxAmountCurrency: $securityLendingRebateTaxAmountCurrency, ')
          ..write(
              'securityLendingRebateTaxAmountValue: $securityLendingRebateTaxAmountValue, ')
          ..write(
              'securityLendingTransferredAmountCurrency: $securityLendingTransferredAmountCurrency, ')
          ..write(
              'securityLendingTransferredAmountValue: $securityLendingTransferredAmountValue, ')
          ..write('securityLendingMonth: $securityLendingMonth, ')
          ..write(
              'wealthManagementManagedPortfolioId: $wealthManagementManagedPortfolioId, ')
          ..write('wealthManagementProductName: $wealthManagementProductName, ')
          ..write(
              'wealthManagementProductCategory: $wealthManagementProductCategory, ')
          ..write('wealthManagementAccountName: $wealthManagementAccountName, ')
          ..write(
              'wealthManagementEstimatedExecutionDeadline: $wealthManagementEstimatedExecutionDeadline, ')
          ..write(
              'wealthManagementTrackerSteps: $wealthManagementTrackerSteps, ')
          ..write('acquirerInstrumentName: $acquirerInstrumentName, ')
          ..write('acquirerInstrumentSymbol: $acquirerInstrumentSymbol, ')
          ..write(
              'acquirerInstrumentExchangeId: $acquirerInstrumentExchangeId, ')
          ..write('acquirerInstrumentImageUrl: $acquirerInstrumentImageUrl, ')
          ..write('acquireeInstrumentName: $acquireeInstrumentName, ')
          ..write('acquireeInstrumentSymbol: $acquireeInstrumentSymbol, ')
          ..write(
              'acquireeInstrumentExchangeId: $acquireeInstrumentExchangeId, ')
          ..write('acquireeInstrumentImageUrl: $acquireeInstrumentImageUrl, ')
          ..write(
              'brokerAcquisitionAccountAmountValue: $brokerAcquisitionAccountAmountValue, ')
          ..write(
              'brokerAcquisitionAccountAmountCurrency: $brokerAcquisitionAccountAmountCurrency, ')
          ..write(
              'brokerAcquisitionPositionDelta: $brokerAcquisitionPositionDelta, ')
          ..write('ipoCompanyName: $ipoCompanyName, ')
          ..write('ipoSubscriptionNumber: $ipoSubscriptionNumber, ')
          ..write('ipoLeverageValue: $ipoLeverageValue, ')
          ..write('ipoLeverageCurrency: $ipoLeverageCurrency, ')
          ..write('ipoAmountValue: $ipoAmountValue, ')
          ..write('ipoAmountCurrency: $ipoAmountCurrency, ')
          ..write('ipoTotalAmountValue: $ipoTotalAmountValue, ')
          ..write('ipoTotalAmountCurrency: $ipoTotalAmountCurrency, ')
          ..write('ipoCompanyLogo: $ipoCompanyLogo, ')
          ..write('ipoEnhancementValue: $ipoEnhancementValue, ')
          ..write('ipoEnhancementCurrency: $ipoEnhancementCurrency, ')
          ..write('ipoRefundAmountValue: $ipoRefundAmountValue, ')
          ..write('ipoRefundAmountCurrency: $ipoRefundAmountCurrency, ')
          ..write('ipoLeveragePaidValue: $ipoLeveragePaidValue, ')
          ..write('ipoLeveragePaidCurrency: $ipoLeveragePaidCurrency, ')
          ..write('ipoLeverageFeePercentage: $ipoLeverageFeePercentage, ')
          ..write('ipoLeverageFeeValue: $ipoLeverageFeeValue, ')
          ..write('ipoLeverageFeeCurrency: $ipoLeverageFeeCurrency, ')
          ..write('ipoAllocationFeePercentage: $ipoAllocationFeePercentage, ')
          ..write('ipoAllotedFeeMoney: $ipoAllotedFeeMoney, ')
          ..write('ipoAllotedFeeMoneyCurrency: $ipoAllotedFeeMoneyCurrency, ')
          ..write('ipoAllocatedMoney: $ipoAllocatedMoney, ')
          ..write('ipoAllocatedMoneyCurrency: $ipoAllocatedMoneyCurrency, ')
          ..write('dynamicDetails: $dynamicDetails, ')
          ..write('linkedTransactionFeeDetails: $linkedTransactionFeeDetails, ')
          ..write('cashbackCalculations: $cashbackCalculations, ')
          ..write('carbonEmissionInGrams: $carbonEmissionInGrams, ')
          ..write('carbonEmissionInOunces: $carbonEmissionInOunces, ')
          ..write('recurringTransferRuleId: $recurringTransferRuleId, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class DatabaseAtV47 extends GeneratedDatabase {
  DatabaseAtV47(QueryExecutor e) : super(e);
  late final Transactions transactions = Transactions(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [transactions];
  @override
  int get schemaVersion => 47;
}
