import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_retail_desktop/ui_kit_retail_desktop.dart';
import 'package:wio_feature_core_ui_desktop/index.dart';
import 'package:wio_feature_core_ui_desktop/src/handlers/common_error_handler/common_error_handler_state.dart';
import 'package:wio_feature_core_ui_desktop/src/handlers/common_error_handler/error_type.dart';

class CommonErrorHandlerWidget extends StatelessWidget {
  final Widget child;

  const CommonErrorHandlerWidget({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return BlocListener<CommonErrorHandlerCubit, CommonErrorHandlerState>(
      listener: _handleListener,
      listenWhen: (_, state) => state.maybeMap(
        showError: (_) => true,
        orElse: () => false,
      ),
      child: Scaffold(body: child),
    );
  }

  void _handleListener(BuildContext context, CommonErrorHandlerState state) {
    final commonLocalization = CommonLocalizations.of(context);
    final coreUiLocalizations = CoreUiDesktopLocalizations.of(context);

    state.mapOrNull(
      showError: (showErrorState) {
        final shortCorrelationId = showErrorState.error.getShortCorrelationId();

        final message = showErrorState.error.maybeMap(
          type: (value) {
            final message = getMessageFromErrorType(
              value.errorType,
              commonLocalization,
              coreUiLocalizations,
            );

            return formattedErrorMessage(message, shortCorrelationId);
          },
          message: (value) {
            return formattedErrorMessage(value.message, shortCorrelationId);
          },
          orElse: () => null,
        );

        if (message != null) {
          ScaffoldMessenger.maybeOf(context)?.showSnackBar(
            SnackbarWrapper(SnackbarModel.errorLowPriority(text: message)),
          );
        }
      },
    );
  }

  String formattedErrorMessage(String message, String? correlationId) {
    if (correlationId != null) {
      final formattedErrorMessage =
          '${message.toString().endsWith('.') ? '' : '.'}'
          ' Correlation id: $correlationId';

      return message + formattedErrorMessage;
    }

    return message;
  }

  String getMessageFromErrorType(
    ErrorType errorType,
    CommonLocalizations commonLocalizations,
    CoreUiDesktopLocalizations coreUiLocalizations,
  ) {
    return switch (errorType) {
      ErrorType.networkConnectionLost =>
        commonLocalizations.networkConnectionIsLost,
      ErrorType.timeoutException =>
        coreUiLocalizations.timeoutExceptionPleaseTryLater,
      ErrorType.twoFactorFailedTryAgain =>
        coreUiLocalizations.twoFactorFailedTryAgian,
      ErrorType.commonErrorMessage => commonLocalizations.common_error_message,
      ErrorType.twoFactorBlockedTryLater =>
        coreUiLocalizations.twoFaBlockedTryLater,
    };
  }
}
