map $host $domain {
    hostnames;
    default "retail-web-ob-dev.azurewebsites.net";
    retail-web-ob-pre.azurewebsites.net "sandbox.ob.personal.wio.io";
    retail-web-ob-prod.azurewebsites.net "ob.personal.wio.io";
}

server {
    listen       80;
    listen       [::]:80;
    server_name  localhost;
    root         /usr/share/nginx/html;
    error_page   500 502 503 504  /50x.html;

    location / {
        index  index.html index.htm;
    }

    location /.well-known/ {
        default_type application/json;
    }

    location /npss/enroll {
      return 301 "$scheme://$domain/#/npss/enroll$is_args$args";
    }
}