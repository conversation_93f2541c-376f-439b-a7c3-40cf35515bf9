import 'package:integration_test_core/integration_test_core.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';

abstract class OtpService {
  Future<String> getOtpForEmail(String email, [String? customerId]);

  Future<String> getOtpForPhone(String phone, [String? customerId]);
}

class OtpServiceImpl extends RestApiService implements OtpService {
  static const _getOtpPath = '/api/v1/otps';
  static const _otpKey = 'otp';
  static const _userIdKey = 'userId';
  static const _otpQueryParams = {
    'product': 'RETAIL',
    'channel': 'MOBILE',
    'sort': '-eventTime',
    'pageNum': '0',
    'pageSize': '1',
  };

  final IRestApiClient _httpClient;

  OtpServiceImpl(this._httpClient);

  @override
  Future<String> getOtpForEmail(String email, [String? customerId]) async {
    try {
      final otp = await execute(
        _httpClient.execute(
          RestApiRequest(
            _getOtpPath,
            method: HttpRequestMethod.get,
            queryParameters: {
              'email': email,
              if (customerId != null) _userIdKey: customerId,
              ..._otpQueryParams,
            },
          ),
        ),
        _mapOtp,
      );

      return otp;
    } on Object catch (error) {
      throw Exception(
        'Could not get otp for email $email'
        '\n ${error.toString()}',
      );
    }
  }

  @override
  Future<String> getOtpForPhone(String phone, [String? customerId]) async {
    try {
      final otp = await execute(
        _httpClient.execute(
          RestApiRequest(
            _getOtpPath,
            method: HttpRequestMethod.get,
            queryParameters: {
              'mobile': phone,
              if (customerId != null) _userIdKey: customerId,
              ..._otpQueryParams,
            },
          ),
        ),
        _mapOtp,
      );

      return otp;
    } on Object catch (error) {
      throw Exception(
        'Could not get otp for mobile number $phone'
        '\n${error.toString()}',
      );
    }
  }

  String _mapOtp(Object? json) {
    final response = json as List<Object?>;
    final firstItem = response.first as Map<String, Object?>;

    return firstItem[_otpKey] as String;
  }
}
