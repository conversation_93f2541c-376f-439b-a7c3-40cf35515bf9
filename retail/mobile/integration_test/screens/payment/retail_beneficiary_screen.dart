import 'package:flutter/material.dart';
import 'package:integration_test_core/integration_test_core.dart';
import 'package:wio_common_feature_payments_ui/src/widgets/recent_payees_view.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/payment_creation_flow.dart';

class RetailBeneficiaryScreen {
  final body = find.byKey(PaymentCreationFlow.beneficiaryCreationPageKey);

  final viewContacts = find.byKey(RecentPayeesView.viewContactsKey);
  final newPayee = find.byKey(RecentPayeesView.newPayeeKey).at(0);

  final payeeNameInput = find.byKey(const ValueKey('Payee name InputKey'));
  final ibanInput = find.byKey(const ValueKey('IBAN InputKey'));
  final swiftCodeInput =
      find.byKey(const ValueKey('Swift Code (BIC) InputKey'));
  final townCityInput = find.byKey(const ValueKey('Town/City InputKey'));
  final cityInput = find.byKey(const ValueKey('City InputKey'));
  final streetInput = find.byKey(const ValueKey('Street InputKey'));
  final postcodeInput = find.byKey(const ValueKey('Postcode InputKey'));
  final postalCodeInput = find.byKey(const ValueKey('Postal code InputKey'));
  final addressInput = find.byKey(const ValueKey('Address InputKey'));
  final accountNumberRIBInput =
      find.byKey(const ValueKey('Account number (RIB) InputKey'));
  final accountNumberInput =
      find.byKey(const ValueKey('Account number InputKey'));
  final institutionNumberInput =
      find.byKey(const ValueKey('Institution number InputKey'));
  final transitNumberInput =
      find.byKey(const ValueKey('Transit number InputKey'));
}
