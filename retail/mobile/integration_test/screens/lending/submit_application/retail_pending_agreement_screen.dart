import 'package:integration_test_core/integration_test_core.dart';
import 'package:wio_feature_lending_ui/src/screens/submit_application/flow/submit_application_flow.dart';

class RetailPendingAgreementScreen {
  final body = find.byKey(SubmitApplicationFlow.pendingAgreementKey);
  final creditStep = find.byKey(SubmitApplicationFlow.creditLimitStepKey);
  final paymentStep = find.byKey(SubmitApplicationFlow.paymentStepKey);
  final signUpStep = find.byKey(SubmitApplicationFlow.signUpStepKey);
  final activationStep = find.byKey(SubmitApplicationFlow.activationStepKey);
}
