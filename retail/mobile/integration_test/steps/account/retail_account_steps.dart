import 'package:feature_fx_ui/src/screens/widgets/fx_convert_button.dart';
import 'package:feature_fx_ui/src/screens/widgets/fx_input_field.dart';
import 'package:flutter/widgets.dart';
import 'package:integration_test_core/integration_test_core.dart';
import 'package:wio_common_feature_transaction_ui/screens/all_transactions/widgets/transaction_cell.dart';

import '../../constants/retail_test_constants.dart';
import '../../screens/index.dart';

class RetailAccountScreenSteps extends RetailTestScreenSet {
  Iterable<StepDefinitionGeneric<ScenarioContext>> get steps => [
        when2<String, String, ScenarioContext>(
          RegExp(
            r'I choose transfer from (AED|USD|GBP|EUR) to (AED|USD|GBP|EUR)$',
          ),
          (sell, buy, context) async {
            final toBuy = await fxScreen.buyInput.widget<FXInputField>();
            final toSell = await fxScreen.sellInput.widget<FXInputField>();
            if (!(toBuy.mainText == buy && toSell.mainText == sell)) {
              if (toSell.mainText == sell && toBuy.mainText != buy) {
                await _selectCurrencyToBuy(buy);
              } else if (toBuy.mainText == buy && toSell.mainText != sell) {
                await _selectCurrencyToSell(sell);
              } else if (toBuy.mainText == sell && toSell.mainText == buy) {
                await _swapCurrencies();
              } else if (toBuy.mainText == sell && toSell.mainText != buy) {
                await _swapCurrencies();
                await _selectCurrencyToBuy(buy);
              } else if (toSell.mainText == buy && toBuy.mainText != sell) {
                await _swapCurrencies();
                await _selectCurrencyToSell(sell);
              }
            }
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        when1<GherkinTable, ScenarioContext>(
          RegExp(r'I see last transfer$'),
          (table, context) async {
            final expectedTitle = table.asMap().first['title'];
            final expectedSubtitle = table.asMap().first['subtitle'];
            var expectedTrailingText =
                table.asMap().first['trailingText'] ??= '';

            final transactionWidget = await find
                .byType(TransactionCell)
                .first
                .widget<TransactionCell>();
            final transaction = transactionWidget.cellModel;
            expect(transaction.title.text, expectedTitle);
            expect(transaction.subTitle.text, expectedSubtitle);

            if (expectedTrailingText ==
                'negativeDoubleWithTwoDecimalsAndAEDPattern') {
              expect(
                RegExp(r'-\d+\.\d{2}\sAED$')
                    .hasMatch(transaction.trailingText.text),
                isTrue,
              );
            } else {
              if (double.tryParse(expectedTrailingText) != null) {
                final expectedCurr = expectedTitle!.split(' ')[0];
                final rate = await context.world
                        .getContext(RetailTestConstants.specificExchangeRateKey)
                    as double;
                final amount = double.parse(expectedTrailingText);

                if (expectedCurr == 'AED') {
                  expectedTrailingText =
                      '-${(amount * rate).toStringAsFixed(2)} AED';
                } else {
                  expectedTrailingText =
                      '-${(amount / rate).toStringAsFixed(2)} $expectedCurr';
                }
              }
              expect(transaction.trailingText.text, expectedTrailingText);
            }
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        when<ScenarioContext>(
          RegExp(r'I confirm FX transfer$'),
          (context) async {
            final element = fxReviewScreen.slideActionCta;
            await context.tester.drag(element, const Offset(300.0, 0.0));
            await element.shouldNotBeVisible();
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        when<ScenarioContext>(
          RegExp(r'I click on the Buy currency button$'),
          (context) async {
            await find.byType(FXConvertButton).click(isClickable: true);
            if (await find
                .text('Do it later')
                .isPresent(timeout: const Duration(seconds: 3))) {
              await find.text('Do it later').click();
            }
            await find.byType(FXConvertButton).shouldNotBeVisible();
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        when<ScenarioContext>(
          RegExp(r'I see the FX screen$'),
          (context) async {
            await fxScreen.screenBody.shouldBeVisible();
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
      ];

  Future<void> _swapCurrencies() async {
    await fxScreen.swapButton.click();
    await fxScreen.exchangeButton.shouldBeVisible();
  }

  Future<void> _selectCurrencyToBuy(String currency) async {
    await fxScreen.buyInput.descendant(find.byType(Icon)).click();
    await find.text('$currency account').shouldBeVisible();
    await find.text('$currency account').click();
  }

  Future<void> _selectCurrencyToSell(String currency) async {
    await fxScreen.sellInput.descendant(find.byType(Icon)).click();
    await find.text('$currency account').shouldBeVisible();
    await find.text('$currency account').click();
  }
}
