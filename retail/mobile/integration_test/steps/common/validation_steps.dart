import 'package:flutter/material.dart';
import 'package:integration_test_core/integration_test_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

import '../../screens/index.dart';

class ValidationSteps extends RetailTestScreenSet {
  Iterable<StepDefinitionGeneric<ScenarioContext>> get steps => [
        then1<String, ScenarioContext>(
          RegExp(r'I see Label with text {string}$'),
          (text, context) async {
            await find.widgetWithText(Label, text).shouldBeVisible();
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        then2<String, String, ScenarioContext>(
          RegExp(r'ElevatedButton {string} is (enabled|disabled)$'),
          (label, status, context) async {
            final button = await find
                .widgetWithText(ElevatedButton, label)
                .widget<ElevatedButton>();
            if (status == 'enabled') {
              expect(button.enabled, isTrue);
            } else {
              expect(button.enabled, isFalse);
            }
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        and2<String, String, ScenarioContext>(
          RegExp(r'I see list details with {string} label and {string} value$'),
          (label, value, context) async {
            final row = find.widgetWithText(ListDetails, label);
            final actualValue = row.descendant(find.byType(Label)).last;
            await actualValue.shouldBeVisible();
            final widget = await actualValue.widget<Label>();
            expect(widget.model.text, value);
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        and2<String, String, ScenarioContext>(
          RegExp(
            r'I see list details with {string} label and {string} pattern$',
          ),
          (label, pattern, context) async {
            final row = find.widgetWithText(ListDetails, label);
            final actualValue = row.descendant(find.byType(Label)).last;
            await actualValue.shouldBeVisible();
            final widget = await actualValue.widget<Label>();
            final expectedPattern = RegExp('^$pattern' r'$');
            final hasMatch = expectedPattern.hasMatch(widget.model.text);
            expectSync(hasMatch, true);
          },
        ),
      ];
}
