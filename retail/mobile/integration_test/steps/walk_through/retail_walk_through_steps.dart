// import 'package:di/di.dart';
import 'package:integration_test_core/integration_test_core.dart';

import '../../screens/index.dart';

class RetailWalkThroughSteps extends RetailTestScreenSet {
  Iterable<StepDefinitionGeneric<ScenarioContext>> get steps => [
        given<ScenarioContext>(
          RegExp(r'I am on the Walk Through screen$'),
          (context) async {
            await walkThroughScreen.loginButton.shouldBeVisible();
            await walkThroughScreen.signUpButton.shouldBeVisible();
            // await _testDiRegisteredFully();
            await context.tester.pumpUntilSettled();
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        when<ScenarioContext>(
          RegExp(r'I click on the Log In button$'),
          (context) async {
            await walkThroughScreen.loginButton.click();
            await walkThroughScreen.loginButton.shouldNotBeVisible();
            await context.tester.pumpUntilSettled();
          },
        ),
        when<ScenarioContext>(
          RegExp(r'I click on the Sign Up button$'),
          (context) async {
            await walkThroughScreen.signUpButton.click();
            await walkThroughScreen.signUpButton.shouldNotBeVisible();
            await context.tester.pumpUntilSettled();
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
      ];
}

// Future<void> _testDiRegisteredFully() async {
//   final configurations = DependencyProvider.configurationItems;
//
//   try {
//     for (final config in configurations) {
//       DependencyProvider.get(
//         type: config.itemType,
//         instanceName: config.instanceName,
//       );
//     }
//   } on Object catch (ex, st) {
//     Error.throwWithStackTrace(TestFailure('DI test failed: $ex'), st);
//   }
// }
