import 'package:integration_test_core/integration_test_core.dart';

import '../../constants/retail_test_constants.dart';
import '../../screens/index.dart';

class RetailPasscodeScreenSteps extends RetailTestScreenSet {
  Iterable<StepDefinitionGeneric<ScenarioContext>> get steps => [
        and<ScenarioContext>(
          RegExp(r'I click on the Forgot passcode if appear$'),
          (context) async {
            final present = await passcodeScreen.body.isPresent();
            if (present) {
              await passcodeScreen.forgotPasscode.click();
            }
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        and1<String, ScenarioContext>(
          RegExp(r'I set passcode {string}$'),
          (passcode, context) async {
            context.world.setContext(RetailTestConstants.passcode, passcode);
            await passcodeScreen.passcodeInputKey.click();
            await passcodeScreen.passcodeInputKey.enterText(passcode);

            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        and<ScenarioContext>(
          RegExp(r'I confirm passcode$'),
          (context) async {
            final passcode = context.world.getContext<String>(
              RetailTestConstants.passcode,
            );
            await passcodeScreen.passcodeInputKey.click();
            await passcodeScreen.passcodeInputKey.enterText(passcode);

            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        and<ScenarioContext>(
          RegExp(r'I enter passcode$'),
          (context) async {
            final passcode = context.world.getContext<String>(
              RetailTestConstants.passcode,
            );

            for (var i = 0; i < passcode.length; i++) {
              await find.text(passcode[i]).click();
            }

            await AttachScreenshotHook.takeScreenshot();
          },
        ),
      ];
}
