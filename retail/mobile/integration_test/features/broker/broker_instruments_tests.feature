@all
@broker-instruments
Feature: Broker instruments tests

  @order-trending-companies
  @run
  @smoke
  #  https://wio.testops.cloud/project/44/test-cases/276?treeId=0
  Scenario: 276 Trending companies
    Given I get user from pool with tag "money_usd"
    When I logged in with a user from context without OTP
    And I close all popups
    And I go to the ProductHub screen
    And I click on the Broker button
    And I close tutorial screen
    And I go to the Discovery screen
    Then I see Discovery screen
    And I scroll to trending companies instrument
    Then I check that Most Popular tab is selected
    And I choose Daily Movers tab
    Then I check that Daily Movers tab is selected
    And I pull down Discovery screen to update
    And I scroll to trending companies instrument
    And I click on Trending Companies instrument
    Then I see the Instrument Details screen
    And I click on the back button
    Then I see Discovery screen
    And I slide Trending companies carousel to right
    Then I check that Most Popular tab is selected

  @order-instrument-search
    @run
  #  https://wio.testops.cloud/project/44/test-cases/274?treeId=0
  Scenario Outline: 274 Search Field Validation
    Given I get user from pool with tag "money_usd"
    When I logged in with a user from context without OTP
    And I close all popups
    And I go to the ProductHub screen
    And I click on the Broker button
    And I close tutorial screen
    And I go to the Discovery screen
    Then I see Discovery screen
    Then I click on the Search button
    And I enter instrument code "<company>"
    And I see instrument with code "<company>" in the list
    Then I clear the search field
    Then I enter instrument code "<ticker>"
    And I see instrument with code "<ticker>" in the list
    Then I clear the search field
    Then I click on filters
    Then I clear all filters
    Then I pull down Instrument Search screen to update
    Then I swipe down to see more instruments
    Examples:
      | company            | ticker |
      | Tesla Motors, Inc. | TSLA   |

  @instrument-page-navigation-between-tabs
    @run
  #  https://wio.testops.cloud/project/44/test-cases/1151?treeId=0
  Scenario Outline: 1151 Tabs. Check navigation between tabs.
    Given I get user from pool with tag "shares_aapl"
    When I logged in with a user from context without OTP
    And I close all popups
    And I go to the ProductHub screen
    And I click on the Broker button
    And I close tutorial screen
    And I go to the Discovery screen
    Then I see Discovery screen
    Then I click on the Search button
    Then I enter instrument code "<instrumentCode>"
    And I see instrument with code "<instrumentCode>" in the list
    And I click on the found instrument "<instrumentCode>"
    And I see instrument details screen with title "<instrumentCode>"
    And I click on "News"
    And I click on the News card item in News List screen
    Then I see the News Details screen
    And I click on the back button
    And I click on "Activity"
    And I click on the first transaction card
    And I click on the back button
    And I click on "Overview"
    And I scroll down to Metrics card
    Examples:
      | instrumentCode |
      | AAPL           |

  @instrument-page-navigation-from-activities
  @run
  #  https://wio.testops.cloud/project/44/test-cases/283?treeId=0
  Scenario: 283 Activities. Check link functional.
    Given I get user from pool with tag "shares"
    When I logged in with a user from context without OTP
    And I close all popups
    And I go to the ProductHub screen
    And I click on the Broker button
    And I close tutorial screen
    And I click on "bottom Activities" button
    Then I see the "Activities" label
    And I click on the first transaction card
    Then I see transaction screen with name from context
    And I click on the company icon
    Then I see instrument details screen with name from context
    And I click on the back button
    Then I see transaction screen with name from context
    And I click on the back button
    Then I see the "Activities" label

  @transactions-history-on-instrument-page
  # brokerDividend transactions are not displaying in instrument transactions
  #  https://wiobank.atlassian.net/browse/BR-4746
  # tag will be changed when issue is fixed
  @tag_that_will_not_run
    #  https://wio.testops.cloud/project/44/test-cases/310?treeId=91
  Scenario: 310 Activity tab on instrument page.
    Given I get user from pool with tag "txns_aapl_dividend,txns_aapl_buy"
    When I logged in with a user from context without OTP
    And I close all popups
    And I go to the ProductHub screen
    And I click on the Broker button
    And I close tutorial screen
    Then I see the Portfolio page
    And I click on the "Apple" instrument on the portfolio screen
    Then I see instrument details screen with title "AAPL"
    And I click on the "Activity" label
    Then I see transaction list
    And I click on the first transaction with type "broker-brokerBuy"
    Then I see transaction screen with name from context
    And I click on the back button
    Then I see transaction list
    And I click on the first transaction with type "broker-brokerDividend"
    And I see label "DIVIDEND" on the transaction page
    Then I see transaction screen with name from context
    And I click on the back button
    Then I see transaction list

  @testops-11711
  @run
  @broker-explore-uae-companies
  Scenario: Explore UAE companies
    Given I get user from pool with tag "shares_us,shares_uae"
    And I logged in with a user from context without OTP
    And I close all popups
    And I go to the ProductHub screen
    And I click on the Broker button
    And I close tutorial screen
    And I click on "bottom Discovery" button
    When I click on Explore UAE Companies banner
    Then I see UAE Instruments List screen
    When I pull down UAE Instruments screen to update
    Then I see UAE Instruments List screen
    When I scroll to last instrument on UAE Instruments List screen
    Then I see UAE Instruments List header
