import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_user_api/domain/customer_subscription_interactor.dart';

final class FeatureToggleSubscriptionProviderImpl
    implements FeatureToggleSubscriptionProvider {
  final KeyValueStorage _storage;
  final CustomerSubscriptionInteractor _customerSubscriptionInteractor;

  const FeatureToggleSubscriptionProviderImpl({
    required KeyValueStorage storage,
    required CustomerSubscriptionInteractor customerSubscriptionInteractor,
  })  : _storage = storage,
        _customerSubscriptionInteractor = customerSubscriptionInteractor;

  @override
  Future<String?> getAssociatedSubscriptionPlan() async {
    final planType =
        await _storage.getByKey<String>(_toggleAssociatedUserPlanTypeKey);
    if (planType?.isEmpty ?? true) return null;

    return planType;
  }

  @override
  Future<String?> getCurrentSubscriptionPlan() async {
    final planType =
        await _customerSubscriptionInteractor.getCustomerSubscriptionPlan();

    if (planType?.isEmpty ?? true) return null;

    return planType;
  }

  @override
  Future<void> setAssociatedSubscriptionPlan(String? planType) {
    return _storage.put(_toggleAssociatedUserPlanTypeKey, planType ?? '');
  }

  static const _toggleAssociatedUserPlanTypeKey = '_toggleAssociatedUserPlan';
}
