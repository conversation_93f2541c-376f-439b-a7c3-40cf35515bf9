import 'package:feature_locale_api/feature_locale_api.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'retail_application_state.freezed.dart';

@freezed
class RetailApplicationState with _$RetailApplicationState {
  const factory RetailApplicationState({
    required AppLocale currentLocale,
    required List<Locale> supportedLocales,
  }) = _RetailApplicationState;
}
