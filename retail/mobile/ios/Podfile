platform :ios, '14.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def install_local_pods_uae(application_path = nil, relative_symlink_dir, platform)
  application_path ||= File.dirname(defined_in_file.realpath) if self.respond_to?(:defined_in_file)
  raise 'Could not find application path' unless application_path

  symlink_dir = File.expand_path(relative_symlink_dir, application_path)
  symlink_plugins_dir = File.expand_path('plugins', symlink_dir)
  uae_local_pods_dir = File.join(symlink_plugins_dir, 'uae_pass_plugin','ios', 'LocalPods')
  pod "UAEPassClient", :path => File.join(uae_local_pods_dir, 'UAEPassClient')
end

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  pod 'TensorFlowLiteSwift', '2.7.0'
  
  pod 'SwiftyTesseract', '3.1.3'

  pod 'PureLiveSDK', :git => 'https://gitlab.com/purelive/purelive-ios-sdk.git' , :tag => '*******'
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  install_local_pods_uae(File.dirname(File.realpath(__FILE__)), '.symlinks', 'ios')
  
end

target "NotificationServiceExtension" do
  use_frameworks!
  pod 'AirshipServiceExtension'
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)

    target.build_configurations.each do |config|
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
        '$(inherited)',
        'PERMISSION_NOTIFICATIONS=1',
        'PERMISSION_CAMERA=1',
        'PERMISSION_PHOTOS=1',
        'PERMISSION_APP_TRACKING_TRANSPARENCY=1',
        # NOTE: needed for contacts permission to work
        # Issue: https://github.com/Baseflow/flutter-permission-handler/issues/898#issuecomment-1228356164
        'PERMISSION_CONTACTS=1',
      ]

      # Get the real path of the base configuration file (xcconfig) for the current configuration
      xcconfig_path = config.base_configuration_reference.real_path
      # Read the xcconfig file into a string
      xcconfig = File.read(xcconfig_path)
      # Remove the linker flag "-l\"sqlite3\"" from the xcconfig string. 
      # This might be needed because by default, Flutter links the sqlite3 library, which may not be needed or causing issues.
      new_xcconfig = xcconfig.sub(' -l"sqlite3"', '')
      
      # Renames everywhere DT_TOOLCHAIN_DIR to TOOLCHAIN_DIR
      # This is needed cause in new IOS, Fireabse pods are using DT_TOOLCHAIN_DIR and build is failing
      new_xcconfig = new_xcconfig.sub('DT_TOOLCHAIN_DIR', 'TOOLCHAIN_DIR')
      # Overwrite the xcconfig file with the new content (without sqlite3 linker flag)
      File.open(xcconfig_path, "w") { |file| file << new_xcconfig }

       if config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'].to_f < 12.0
           config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
       end
    end
  end

  # https://stackoverflow.com/a/72600584/2733042
  installer.generated_projects.each do |project|
    project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
      end
    end
  end

  bitcode_strip_path = `xcrun --find bitcode_strip`.chop!
  def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
    framework_path = File.join(Dir.pwd, framework_relative_path)
    command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
    puts "Stripping bitcode: #{command}"
    system(command)
  end

  framework_paths = [
    "../../../common/features/feature_face_recognition/ui/ios/EFRSDK.xcframework/ios-arm64/EFRSDK.framework/EFRSDK",
    "../../../common/features/feature_face_recognition/ui/ios/EFRSDK.xcframework/ios-x86_64-simulator/EFRSDK.framework/EFRSDK",
  ]

  framework_paths.each do |framework_relative_path|
    strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
  end
end
