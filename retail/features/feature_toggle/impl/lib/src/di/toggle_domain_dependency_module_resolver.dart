import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:di/di.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_toggle_impl/src/data/server_feature_toggle_service_impl.dart';
import 'package:wio_feature_toggle_impl/src/mappers/server_feature_toggle_mapper.dart';

class ToggleDomainDependencyModuleResolver {
  static void register() {
    DependencyProvider.registerLazySingleton<ServerFeatureToggleMapper>(
      () => ServerFeatureToggleMapperImpl(),
    );

    DependencyProvider.registerLazySingleton<ServerFeatureToggleService>(
      () => ServerFeatureToggleServiceImpl(
        DependencyProvider.get<IRestApiClient>(),
        DependencyProvider.get<EnvProvider>(),
        DependencyProvider.get<ServerFeatureToggleMapper>(),
      ),
    );
  }
}
