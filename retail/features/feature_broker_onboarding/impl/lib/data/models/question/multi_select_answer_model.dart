import 'dart:convert';

import 'package:feature_broker_onboarding_impl/data/models_index.dart';
import 'package:json_annotation/json_annotation.dart';

part 'multi_select_answer_model.g.dart';

@JsonSerializable(explicitToJson: true)
class MultiSelectAnswer extends Answer {
  @Json<PERSON>ey(name: 'type', includeIfNull: false)
  final AnswerType exactType;

  @JsonKey(name: 'values', includeIfNull: false, defaultValue: <String>[])
  final List<String> values;

  MultiSelectAnswer({
    required this.exactType,
    required this.values,
  }) : super(type: exactType);

  factory MultiSelectAnswer.fromJson(Map<String, dynamic> json) =>
      _$MultiSelectAnswerFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$MultiSelectAnswerToJson(this);

  @override
  String toString() => jsonEncode(this);
}
