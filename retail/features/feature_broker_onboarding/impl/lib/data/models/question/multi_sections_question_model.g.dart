// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'multi_sections_question_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MultiSectionsQuestion _$MultiSectionsQuestionFromJson(
        Map<String, dynamic> json) =>
    MultiSectionsQuestion(
      id: json['id'] as String,
      type: $enumDecode(_$QuestionTypeEnumMap, json['type']),
      content: MultiSectionsContent.fromJson(
          json['content'] as Map<String, dynamic>),
      answer: json['answer'] == null
          ? null
          : MultiSectionsAnswer.fromJson(
              json['answer'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MultiSectionsQuestionToJson(
        MultiSectionsQuestion instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$QuestionTypeEnumMap[instance.type]!,
      if (instance.answer case final value?) 'answer': value,
      'content': instance.content,
    };

const _$QuestionTypeEnumMap = {
  QuestionType.select: 'SELECT',
  QuestionType.selectWithSearch: 'SELECT_WITH_SEARCH',
  QuestionType.input: 'INPUT',
  QuestionType.address: 'ADDRESS',
  QuestionType.termsAndConditions: 'TERMS_AND_CONDITIONS',
  QuestionType.empty: 'EMPTY',
  QuestionType.multiSelect: 'MULTI_SELECT',
  QuestionType.multiSections: 'MULTI_SECTIONS',
  QuestionType.uploadDocuments: 'UPLOAD_DOCUMENTS',
};

MultiSectionsContent _$MultiSectionsContentFromJson(
        Map<String, dynamic> json) =>
    MultiSectionsContent(
      headerText: json['headerText'] as String,
      text: json['text'] as String,
      sectionsContent: (json['sectionsContent'] as List<dynamic>?)
              ?.map((e) => SectionContent.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      buttonText: json['buttonText'] as String,
      textLinks: (json['textLinks'] as List<dynamic>?)
          ?.map((e) => TextLink.fromJson(e as Map<String, dynamic>))
          .toList(),
      secondaryButtonText: json['secondaryButtonText'] as String?,
      checkBoxTexts: (json['checkBoxTexts'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$MultiSectionsContentToJson(
        MultiSectionsContent instance) =>
    <String, dynamic>{
      'headerText': instance.headerText,
      'text': instance.text,
      'sectionsContent': instance.sectionsContent,
      'buttonText': instance.buttonText,
      if (instance.textLinks case final value?) 'textLinks': value,
      if (instance.checkBoxTexts case final value?) 'checkBoxTexts': value,
      if (instance.secondaryButtonText case final value?)
        'secondaryButtonText': value,
    };

TextLink _$TextLinkFromJson(Map<String, dynamic> json) => TextLink(
      highlightedText: json['highlightedText'] as String,
      bottomSheetContent: BottomSheetContent.fromJson(
          json['bottomSheetContent'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TextLinkToJson(TextLink instance) => <String, dynamic>{
      'highlightedText': instance.highlightedText,
      'bottomSheetContent': instance.bottomSheetContent,
    };

BottomSheetContent _$BottomSheetContentFromJson(Map<String, dynamic> json) =>
    BottomSheetContent(
      headerText: json['headerText'] as String?,
      text: json['text'] as String?,
    );

Map<String, dynamic> _$BottomSheetContentToJson(BottomSheetContent instance) =>
    <String, dynamic>{
      if (instance.headerText case final value?) 'headerText': value,
      if (instance.text case final value?) 'text': value,
    };

SectionContent _$SectionContentFromJson(Map<String, dynamic> json) =>
    SectionContent(
      sectionId: json['sectionId'] as String,
      type: $enumDecode(_$QuestionTypeEnumMap, json['type']),
      label: json['label'] as String?,
      content: SectionContent._baseContentFromJson(
          json['content'] as Map<String, dynamic>),
      conditions: (json['conditions'] as List<dynamic>)
          .map((e) => SectionCondition.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SectionContentToJson(SectionContent instance) =>
    <String, dynamic>{
      'sectionId': instance.sectionId,
      'type': _$QuestionTypeEnumMap[instance.type]!,
      if (instance.label case final value?) 'label': value,
      'conditions': instance.conditions,
    };

SectionCondition _$SectionConditionFromJson(Map<String, dynamic> json) =>
    SectionCondition(
      type: $enumDecode(_$SectionConditionTypeEnumMap, json['type']),
      answer: const AnswerJsonConverter()
          .fromJson(json['answer'] as Map<String, dynamic>),
      nextSectionId: json['nextSectionId'] as String,
    );

Map<String, dynamic> _$SectionConditionToJson(SectionCondition instance) =>
    <String, dynamic>{
      if (_$SectionConditionTypeEnumMap[instance.type] case final value?)
        'type': value,
      'nextSectionId': instance.nextSectionId,
      'answer': const AnswerJsonConverter().toJson(instance.answer),
    };

const _$SectionConditionTypeEnumMap = {
  SectionConditionType.swaggerGeneratedUnknown: null,
  SectionConditionType.nextSection: 'NEXT_SECTION',
};
