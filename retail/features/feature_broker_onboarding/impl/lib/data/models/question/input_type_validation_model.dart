import 'package:feature_broker_onboarding_impl/data/models_index.dart';
import 'package:json_annotation/json_annotation.dart';

part 'input_type_validation_model.g.dart';

@JsonSerializable()
class InputTypeValidation extends EnterTextValidation {
  @JsonKey(name: 'inputType', unknownEnumValue: InputType.unknown)
  final InputType inputType;

  InputTypeValidation({
    required this.inputType,
    required super.type,
  });

  factory InputTypeValidation.fromJson(Map<String, dynamic> json) =>
      _$InputTypeValidationFromJson(json);

  Map<String, dynamic> toJson() => _$InputTypeValidationToJson(this);
}

enum InputType {
  @JsonValue('NUMBERS')
  numbers,
  unknown,
}
