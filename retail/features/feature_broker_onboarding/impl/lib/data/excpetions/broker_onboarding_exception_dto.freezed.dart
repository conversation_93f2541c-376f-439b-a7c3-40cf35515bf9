// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'broker_onboarding_exception_dto.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OnboardingExceptionDto {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() passportNotFound,
    required TResult Function() passportFilesNotFound,
    required TResult Function() passportExpired,
    required TResult Function() emiratesIdNotFound,
    required TResult Function() emiratesIdFilesNotFound,
    required TResult Function() emiratesIdExpired,
    required TResult Function() addressNotFound,
    required TResult Function() unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? passportNotFound,
    TResult? Function()? passportFilesNotFound,
    TResult? Function()? passportExpired,
    TResult? Function()? emiratesIdNotFound,
    TResult? Function()? emiratesIdFilesNotFound,
    TResult? Function()? emiratesIdExpired,
    TResult? Function()? addressNotFound,
    TResult? Function()? unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? passportNotFound,
    TResult Function()? passportFilesNotFound,
    TResult Function()? passportExpired,
    TResult Function()? emiratesIdNotFound,
    TResult Function()? emiratesIdFilesNotFound,
    TResult Function()? emiratesIdExpired,
    TResult Function()? addressNotFound,
    TResult Function()? unknown,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PassportNotFoundException value)
        passportNotFound,
    required TResult Function(_PassportFilesNotFoundException value)
        passportFilesNotFound,
    required TResult Function(_PassportExpiredException value) passportExpired,
    required TResult Function(_EmiratesIdNotFoundException value)
        emiratesIdNotFound,
    required TResult Function(_EmiratesIdFilesNotFoundException value)
        emiratesIdFilesNotFound,
    required TResult Function(_EmiratesIdExpiredException value)
        emiratesIdExpired,
    required TResult Function(_AddressNotFoundException value) addressNotFound,
    required TResult Function(_UnknownOnboardingException value) unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PassportNotFoundException value)? passportNotFound,
    TResult? Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult? Function(_PassportExpiredException value)? passportExpired,
    TResult? Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult? Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult? Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult? Function(_AddressNotFoundException value)? addressNotFound,
    TResult? Function(_UnknownOnboardingException value)? unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PassportNotFoundException value)? passportNotFound,
    TResult Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult Function(_PassportExpiredException value)? passportExpired,
    TResult Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult Function(_AddressNotFoundException value)? addressNotFound,
    TResult Function(_UnknownOnboardingException value)? unknown,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OnboardingExceptionDtoCopyWith<$Res> {
  factory $OnboardingExceptionDtoCopyWith(OnboardingExceptionDto value,
          $Res Function(OnboardingExceptionDto) then) =
      _$OnboardingExceptionDtoCopyWithImpl<$Res, OnboardingExceptionDto>;
}

/// @nodoc
class _$OnboardingExceptionDtoCopyWithImpl<$Res,
        $Val extends OnboardingExceptionDto>
    implements $OnboardingExceptionDtoCopyWith<$Res> {
  _$OnboardingExceptionDtoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OnboardingExceptionDto
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$PassportNotFoundExceptionImplCopyWith<$Res> {
  factory _$$PassportNotFoundExceptionImplCopyWith(
          _$PassportNotFoundExceptionImpl value,
          $Res Function(_$PassportNotFoundExceptionImpl) then) =
      __$$PassportNotFoundExceptionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PassportNotFoundExceptionImplCopyWithImpl<$Res>
    extends _$OnboardingExceptionDtoCopyWithImpl<$Res,
        _$PassportNotFoundExceptionImpl>
    implements _$$PassportNotFoundExceptionImplCopyWith<$Res> {
  __$$PassportNotFoundExceptionImplCopyWithImpl(
      _$PassportNotFoundExceptionImpl _value,
      $Res Function(_$PassportNotFoundExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of OnboardingExceptionDto
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PassportNotFoundExceptionImpl implements _PassportNotFoundException {
  const _$PassportNotFoundExceptionImpl();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PassportNotFoundExceptionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() passportNotFound,
    required TResult Function() passportFilesNotFound,
    required TResult Function() passportExpired,
    required TResult Function() emiratesIdNotFound,
    required TResult Function() emiratesIdFilesNotFound,
    required TResult Function() emiratesIdExpired,
    required TResult Function() addressNotFound,
    required TResult Function() unknown,
  }) {
    return passportNotFound();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? passportNotFound,
    TResult? Function()? passportFilesNotFound,
    TResult? Function()? passportExpired,
    TResult? Function()? emiratesIdNotFound,
    TResult? Function()? emiratesIdFilesNotFound,
    TResult? Function()? emiratesIdExpired,
    TResult? Function()? addressNotFound,
    TResult? Function()? unknown,
  }) {
    return passportNotFound?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? passportNotFound,
    TResult Function()? passportFilesNotFound,
    TResult Function()? passportExpired,
    TResult Function()? emiratesIdNotFound,
    TResult Function()? emiratesIdFilesNotFound,
    TResult Function()? emiratesIdExpired,
    TResult Function()? addressNotFound,
    TResult Function()? unknown,
    required TResult orElse(),
  }) {
    if (passportNotFound != null) {
      return passportNotFound();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PassportNotFoundException value)
        passportNotFound,
    required TResult Function(_PassportFilesNotFoundException value)
        passportFilesNotFound,
    required TResult Function(_PassportExpiredException value) passportExpired,
    required TResult Function(_EmiratesIdNotFoundException value)
        emiratesIdNotFound,
    required TResult Function(_EmiratesIdFilesNotFoundException value)
        emiratesIdFilesNotFound,
    required TResult Function(_EmiratesIdExpiredException value)
        emiratesIdExpired,
    required TResult Function(_AddressNotFoundException value) addressNotFound,
    required TResult Function(_UnknownOnboardingException value) unknown,
  }) {
    return passportNotFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PassportNotFoundException value)? passportNotFound,
    TResult? Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult? Function(_PassportExpiredException value)? passportExpired,
    TResult? Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult? Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult? Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult? Function(_AddressNotFoundException value)? addressNotFound,
    TResult? Function(_UnknownOnboardingException value)? unknown,
  }) {
    return passportNotFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PassportNotFoundException value)? passportNotFound,
    TResult Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult Function(_PassportExpiredException value)? passportExpired,
    TResult Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult Function(_AddressNotFoundException value)? addressNotFound,
    TResult Function(_UnknownOnboardingException value)? unknown,
    required TResult orElse(),
  }) {
    if (passportNotFound != null) {
      return passportNotFound(this);
    }
    return orElse();
  }
}

abstract class _PassportNotFoundException implements OnboardingExceptionDto {
  const factory _PassportNotFoundException() = _$PassportNotFoundExceptionImpl;
}

/// @nodoc
abstract class _$$PassportFilesNotFoundExceptionImplCopyWith<$Res> {
  factory _$$PassportFilesNotFoundExceptionImplCopyWith(
          _$PassportFilesNotFoundExceptionImpl value,
          $Res Function(_$PassportFilesNotFoundExceptionImpl) then) =
      __$$PassportFilesNotFoundExceptionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PassportFilesNotFoundExceptionImplCopyWithImpl<$Res>
    extends _$OnboardingExceptionDtoCopyWithImpl<$Res,
        _$PassportFilesNotFoundExceptionImpl>
    implements _$$PassportFilesNotFoundExceptionImplCopyWith<$Res> {
  __$$PassportFilesNotFoundExceptionImplCopyWithImpl(
      _$PassportFilesNotFoundExceptionImpl _value,
      $Res Function(_$PassportFilesNotFoundExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of OnboardingExceptionDto
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PassportFilesNotFoundExceptionImpl
    implements _PassportFilesNotFoundException {
  const _$PassportFilesNotFoundExceptionImpl();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PassportFilesNotFoundExceptionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() passportNotFound,
    required TResult Function() passportFilesNotFound,
    required TResult Function() passportExpired,
    required TResult Function() emiratesIdNotFound,
    required TResult Function() emiratesIdFilesNotFound,
    required TResult Function() emiratesIdExpired,
    required TResult Function() addressNotFound,
    required TResult Function() unknown,
  }) {
    return passportFilesNotFound();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? passportNotFound,
    TResult? Function()? passportFilesNotFound,
    TResult? Function()? passportExpired,
    TResult? Function()? emiratesIdNotFound,
    TResult? Function()? emiratesIdFilesNotFound,
    TResult? Function()? emiratesIdExpired,
    TResult? Function()? addressNotFound,
    TResult? Function()? unknown,
  }) {
    return passportFilesNotFound?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? passportNotFound,
    TResult Function()? passportFilesNotFound,
    TResult Function()? passportExpired,
    TResult Function()? emiratesIdNotFound,
    TResult Function()? emiratesIdFilesNotFound,
    TResult Function()? emiratesIdExpired,
    TResult Function()? addressNotFound,
    TResult Function()? unknown,
    required TResult orElse(),
  }) {
    if (passportFilesNotFound != null) {
      return passportFilesNotFound();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PassportNotFoundException value)
        passportNotFound,
    required TResult Function(_PassportFilesNotFoundException value)
        passportFilesNotFound,
    required TResult Function(_PassportExpiredException value) passportExpired,
    required TResult Function(_EmiratesIdNotFoundException value)
        emiratesIdNotFound,
    required TResult Function(_EmiratesIdFilesNotFoundException value)
        emiratesIdFilesNotFound,
    required TResult Function(_EmiratesIdExpiredException value)
        emiratesIdExpired,
    required TResult Function(_AddressNotFoundException value) addressNotFound,
    required TResult Function(_UnknownOnboardingException value) unknown,
  }) {
    return passportFilesNotFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PassportNotFoundException value)? passportNotFound,
    TResult? Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult? Function(_PassportExpiredException value)? passportExpired,
    TResult? Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult? Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult? Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult? Function(_AddressNotFoundException value)? addressNotFound,
    TResult? Function(_UnknownOnboardingException value)? unknown,
  }) {
    return passportFilesNotFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PassportNotFoundException value)? passportNotFound,
    TResult Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult Function(_PassportExpiredException value)? passportExpired,
    TResult Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult Function(_AddressNotFoundException value)? addressNotFound,
    TResult Function(_UnknownOnboardingException value)? unknown,
    required TResult orElse(),
  }) {
    if (passportFilesNotFound != null) {
      return passportFilesNotFound(this);
    }
    return orElse();
  }
}

abstract class _PassportFilesNotFoundException
    implements OnboardingExceptionDto {
  const factory _PassportFilesNotFoundException() =
      _$PassportFilesNotFoundExceptionImpl;
}

/// @nodoc
abstract class _$$PassportExpiredExceptionImplCopyWith<$Res> {
  factory _$$PassportExpiredExceptionImplCopyWith(
          _$PassportExpiredExceptionImpl value,
          $Res Function(_$PassportExpiredExceptionImpl) then) =
      __$$PassportExpiredExceptionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PassportExpiredExceptionImplCopyWithImpl<$Res>
    extends _$OnboardingExceptionDtoCopyWithImpl<$Res,
        _$PassportExpiredExceptionImpl>
    implements _$$PassportExpiredExceptionImplCopyWith<$Res> {
  __$$PassportExpiredExceptionImplCopyWithImpl(
      _$PassportExpiredExceptionImpl _value,
      $Res Function(_$PassportExpiredExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of OnboardingExceptionDto
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PassportExpiredExceptionImpl implements _PassportExpiredException {
  const _$PassportExpiredExceptionImpl();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PassportExpiredExceptionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() passportNotFound,
    required TResult Function() passportFilesNotFound,
    required TResult Function() passportExpired,
    required TResult Function() emiratesIdNotFound,
    required TResult Function() emiratesIdFilesNotFound,
    required TResult Function() emiratesIdExpired,
    required TResult Function() addressNotFound,
    required TResult Function() unknown,
  }) {
    return passportExpired();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? passportNotFound,
    TResult? Function()? passportFilesNotFound,
    TResult? Function()? passportExpired,
    TResult? Function()? emiratesIdNotFound,
    TResult? Function()? emiratesIdFilesNotFound,
    TResult? Function()? emiratesIdExpired,
    TResult? Function()? addressNotFound,
    TResult? Function()? unknown,
  }) {
    return passportExpired?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? passportNotFound,
    TResult Function()? passportFilesNotFound,
    TResult Function()? passportExpired,
    TResult Function()? emiratesIdNotFound,
    TResult Function()? emiratesIdFilesNotFound,
    TResult Function()? emiratesIdExpired,
    TResult Function()? addressNotFound,
    TResult Function()? unknown,
    required TResult orElse(),
  }) {
    if (passportExpired != null) {
      return passportExpired();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PassportNotFoundException value)
        passportNotFound,
    required TResult Function(_PassportFilesNotFoundException value)
        passportFilesNotFound,
    required TResult Function(_PassportExpiredException value) passportExpired,
    required TResult Function(_EmiratesIdNotFoundException value)
        emiratesIdNotFound,
    required TResult Function(_EmiratesIdFilesNotFoundException value)
        emiratesIdFilesNotFound,
    required TResult Function(_EmiratesIdExpiredException value)
        emiratesIdExpired,
    required TResult Function(_AddressNotFoundException value) addressNotFound,
    required TResult Function(_UnknownOnboardingException value) unknown,
  }) {
    return passportExpired(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PassportNotFoundException value)? passportNotFound,
    TResult? Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult? Function(_PassportExpiredException value)? passportExpired,
    TResult? Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult? Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult? Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult? Function(_AddressNotFoundException value)? addressNotFound,
    TResult? Function(_UnknownOnboardingException value)? unknown,
  }) {
    return passportExpired?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PassportNotFoundException value)? passportNotFound,
    TResult Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult Function(_PassportExpiredException value)? passportExpired,
    TResult Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult Function(_AddressNotFoundException value)? addressNotFound,
    TResult Function(_UnknownOnboardingException value)? unknown,
    required TResult orElse(),
  }) {
    if (passportExpired != null) {
      return passportExpired(this);
    }
    return orElse();
  }
}

abstract class _PassportExpiredException implements OnboardingExceptionDto {
  const factory _PassportExpiredException() = _$PassportExpiredExceptionImpl;
}

/// @nodoc
abstract class _$$EmiratesIdNotFoundExceptionImplCopyWith<$Res> {
  factory _$$EmiratesIdNotFoundExceptionImplCopyWith(
          _$EmiratesIdNotFoundExceptionImpl value,
          $Res Function(_$EmiratesIdNotFoundExceptionImpl) then) =
      __$$EmiratesIdNotFoundExceptionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EmiratesIdNotFoundExceptionImplCopyWithImpl<$Res>
    extends _$OnboardingExceptionDtoCopyWithImpl<$Res,
        _$EmiratesIdNotFoundExceptionImpl>
    implements _$$EmiratesIdNotFoundExceptionImplCopyWith<$Res> {
  __$$EmiratesIdNotFoundExceptionImplCopyWithImpl(
      _$EmiratesIdNotFoundExceptionImpl _value,
      $Res Function(_$EmiratesIdNotFoundExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of OnboardingExceptionDto
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EmiratesIdNotFoundExceptionImpl
    implements _EmiratesIdNotFoundException {
  const _$EmiratesIdNotFoundExceptionImpl();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmiratesIdNotFoundExceptionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() passportNotFound,
    required TResult Function() passportFilesNotFound,
    required TResult Function() passportExpired,
    required TResult Function() emiratesIdNotFound,
    required TResult Function() emiratesIdFilesNotFound,
    required TResult Function() emiratesIdExpired,
    required TResult Function() addressNotFound,
    required TResult Function() unknown,
  }) {
    return emiratesIdNotFound();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? passportNotFound,
    TResult? Function()? passportFilesNotFound,
    TResult? Function()? passportExpired,
    TResult? Function()? emiratesIdNotFound,
    TResult? Function()? emiratesIdFilesNotFound,
    TResult? Function()? emiratesIdExpired,
    TResult? Function()? addressNotFound,
    TResult? Function()? unknown,
  }) {
    return emiratesIdNotFound?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? passportNotFound,
    TResult Function()? passportFilesNotFound,
    TResult Function()? passportExpired,
    TResult Function()? emiratesIdNotFound,
    TResult Function()? emiratesIdFilesNotFound,
    TResult Function()? emiratesIdExpired,
    TResult Function()? addressNotFound,
    TResult Function()? unknown,
    required TResult orElse(),
  }) {
    if (emiratesIdNotFound != null) {
      return emiratesIdNotFound();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PassportNotFoundException value)
        passportNotFound,
    required TResult Function(_PassportFilesNotFoundException value)
        passportFilesNotFound,
    required TResult Function(_PassportExpiredException value) passportExpired,
    required TResult Function(_EmiratesIdNotFoundException value)
        emiratesIdNotFound,
    required TResult Function(_EmiratesIdFilesNotFoundException value)
        emiratesIdFilesNotFound,
    required TResult Function(_EmiratesIdExpiredException value)
        emiratesIdExpired,
    required TResult Function(_AddressNotFoundException value) addressNotFound,
    required TResult Function(_UnknownOnboardingException value) unknown,
  }) {
    return emiratesIdNotFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PassportNotFoundException value)? passportNotFound,
    TResult? Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult? Function(_PassportExpiredException value)? passportExpired,
    TResult? Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult? Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult? Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult? Function(_AddressNotFoundException value)? addressNotFound,
    TResult? Function(_UnknownOnboardingException value)? unknown,
  }) {
    return emiratesIdNotFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PassportNotFoundException value)? passportNotFound,
    TResult Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult Function(_PassportExpiredException value)? passportExpired,
    TResult Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult Function(_AddressNotFoundException value)? addressNotFound,
    TResult Function(_UnknownOnboardingException value)? unknown,
    required TResult orElse(),
  }) {
    if (emiratesIdNotFound != null) {
      return emiratesIdNotFound(this);
    }
    return orElse();
  }
}

abstract class _EmiratesIdNotFoundException implements OnboardingExceptionDto {
  const factory _EmiratesIdNotFoundException() =
      _$EmiratesIdNotFoundExceptionImpl;
}

/// @nodoc
abstract class _$$EmiratesIdFilesNotFoundExceptionImplCopyWith<$Res> {
  factory _$$EmiratesIdFilesNotFoundExceptionImplCopyWith(
          _$EmiratesIdFilesNotFoundExceptionImpl value,
          $Res Function(_$EmiratesIdFilesNotFoundExceptionImpl) then) =
      __$$EmiratesIdFilesNotFoundExceptionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EmiratesIdFilesNotFoundExceptionImplCopyWithImpl<$Res>
    extends _$OnboardingExceptionDtoCopyWithImpl<$Res,
        _$EmiratesIdFilesNotFoundExceptionImpl>
    implements _$$EmiratesIdFilesNotFoundExceptionImplCopyWith<$Res> {
  __$$EmiratesIdFilesNotFoundExceptionImplCopyWithImpl(
      _$EmiratesIdFilesNotFoundExceptionImpl _value,
      $Res Function(_$EmiratesIdFilesNotFoundExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of OnboardingExceptionDto
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EmiratesIdFilesNotFoundExceptionImpl
    implements _EmiratesIdFilesNotFoundException {
  const _$EmiratesIdFilesNotFoundExceptionImpl();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmiratesIdFilesNotFoundExceptionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() passportNotFound,
    required TResult Function() passportFilesNotFound,
    required TResult Function() passportExpired,
    required TResult Function() emiratesIdNotFound,
    required TResult Function() emiratesIdFilesNotFound,
    required TResult Function() emiratesIdExpired,
    required TResult Function() addressNotFound,
    required TResult Function() unknown,
  }) {
    return emiratesIdFilesNotFound();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? passportNotFound,
    TResult? Function()? passportFilesNotFound,
    TResult? Function()? passportExpired,
    TResult? Function()? emiratesIdNotFound,
    TResult? Function()? emiratesIdFilesNotFound,
    TResult? Function()? emiratesIdExpired,
    TResult? Function()? addressNotFound,
    TResult? Function()? unknown,
  }) {
    return emiratesIdFilesNotFound?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? passportNotFound,
    TResult Function()? passportFilesNotFound,
    TResult Function()? passportExpired,
    TResult Function()? emiratesIdNotFound,
    TResult Function()? emiratesIdFilesNotFound,
    TResult Function()? emiratesIdExpired,
    TResult Function()? addressNotFound,
    TResult Function()? unknown,
    required TResult orElse(),
  }) {
    if (emiratesIdFilesNotFound != null) {
      return emiratesIdFilesNotFound();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PassportNotFoundException value)
        passportNotFound,
    required TResult Function(_PassportFilesNotFoundException value)
        passportFilesNotFound,
    required TResult Function(_PassportExpiredException value) passportExpired,
    required TResult Function(_EmiratesIdNotFoundException value)
        emiratesIdNotFound,
    required TResult Function(_EmiratesIdFilesNotFoundException value)
        emiratesIdFilesNotFound,
    required TResult Function(_EmiratesIdExpiredException value)
        emiratesIdExpired,
    required TResult Function(_AddressNotFoundException value) addressNotFound,
    required TResult Function(_UnknownOnboardingException value) unknown,
  }) {
    return emiratesIdFilesNotFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PassportNotFoundException value)? passportNotFound,
    TResult? Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult? Function(_PassportExpiredException value)? passportExpired,
    TResult? Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult? Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult? Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult? Function(_AddressNotFoundException value)? addressNotFound,
    TResult? Function(_UnknownOnboardingException value)? unknown,
  }) {
    return emiratesIdFilesNotFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PassportNotFoundException value)? passportNotFound,
    TResult Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult Function(_PassportExpiredException value)? passportExpired,
    TResult Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult Function(_AddressNotFoundException value)? addressNotFound,
    TResult Function(_UnknownOnboardingException value)? unknown,
    required TResult orElse(),
  }) {
    if (emiratesIdFilesNotFound != null) {
      return emiratesIdFilesNotFound(this);
    }
    return orElse();
  }
}

abstract class _EmiratesIdFilesNotFoundException
    implements OnboardingExceptionDto {
  const factory _EmiratesIdFilesNotFoundException() =
      _$EmiratesIdFilesNotFoundExceptionImpl;
}

/// @nodoc
abstract class _$$EmiratesIdExpiredExceptionImplCopyWith<$Res> {
  factory _$$EmiratesIdExpiredExceptionImplCopyWith(
          _$EmiratesIdExpiredExceptionImpl value,
          $Res Function(_$EmiratesIdExpiredExceptionImpl) then) =
      __$$EmiratesIdExpiredExceptionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EmiratesIdExpiredExceptionImplCopyWithImpl<$Res>
    extends _$OnboardingExceptionDtoCopyWithImpl<$Res,
        _$EmiratesIdExpiredExceptionImpl>
    implements _$$EmiratesIdExpiredExceptionImplCopyWith<$Res> {
  __$$EmiratesIdExpiredExceptionImplCopyWithImpl(
      _$EmiratesIdExpiredExceptionImpl _value,
      $Res Function(_$EmiratesIdExpiredExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of OnboardingExceptionDto
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EmiratesIdExpiredExceptionImpl implements _EmiratesIdExpiredException {
  const _$EmiratesIdExpiredExceptionImpl();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmiratesIdExpiredExceptionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() passportNotFound,
    required TResult Function() passportFilesNotFound,
    required TResult Function() passportExpired,
    required TResult Function() emiratesIdNotFound,
    required TResult Function() emiratesIdFilesNotFound,
    required TResult Function() emiratesIdExpired,
    required TResult Function() addressNotFound,
    required TResult Function() unknown,
  }) {
    return emiratesIdExpired();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? passportNotFound,
    TResult? Function()? passportFilesNotFound,
    TResult? Function()? passportExpired,
    TResult? Function()? emiratesIdNotFound,
    TResult? Function()? emiratesIdFilesNotFound,
    TResult? Function()? emiratesIdExpired,
    TResult? Function()? addressNotFound,
    TResult? Function()? unknown,
  }) {
    return emiratesIdExpired?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? passportNotFound,
    TResult Function()? passportFilesNotFound,
    TResult Function()? passportExpired,
    TResult Function()? emiratesIdNotFound,
    TResult Function()? emiratesIdFilesNotFound,
    TResult Function()? emiratesIdExpired,
    TResult Function()? addressNotFound,
    TResult Function()? unknown,
    required TResult orElse(),
  }) {
    if (emiratesIdExpired != null) {
      return emiratesIdExpired();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PassportNotFoundException value)
        passportNotFound,
    required TResult Function(_PassportFilesNotFoundException value)
        passportFilesNotFound,
    required TResult Function(_PassportExpiredException value) passportExpired,
    required TResult Function(_EmiratesIdNotFoundException value)
        emiratesIdNotFound,
    required TResult Function(_EmiratesIdFilesNotFoundException value)
        emiratesIdFilesNotFound,
    required TResult Function(_EmiratesIdExpiredException value)
        emiratesIdExpired,
    required TResult Function(_AddressNotFoundException value) addressNotFound,
    required TResult Function(_UnknownOnboardingException value) unknown,
  }) {
    return emiratesIdExpired(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PassportNotFoundException value)? passportNotFound,
    TResult? Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult? Function(_PassportExpiredException value)? passportExpired,
    TResult? Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult? Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult? Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult? Function(_AddressNotFoundException value)? addressNotFound,
    TResult? Function(_UnknownOnboardingException value)? unknown,
  }) {
    return emiratesIdExpired?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PassportNotFoundException value)? passportNotFound,
    TResult Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult Function(_PassportExpiredException value)? passportExpired,
    TResult Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult Function(_AddressNotFoundException value)? addressNotFound,
    TResult Function(_UnknownOnboardingException value)? unknown,
    required TResult orElse(),
  }) {
    if (emiratesIdExpired != null) {
      return emiratesIdExpired(this);
    }
    return orElse();
  }
}

abstract class _EmiratesIdExpiredException implements OnboardingExceptionDto {
  const factory _EmiratesIdExpiredException() =
      _$EmiratesIdExpiredExceptionImpl;
}

/// @nodoc
abstract class _$$AddressNotFoundExceptionImplCopyWith<$Res> {
  factory _$$AddressNotFoundExceptionImplCopyWith(
          _$AddressNotFoundExceptionImpl value,
          $Res Function(_$AddressNotFoundExceptionImpl) then) =
      __$$AddressNotFoundExceptionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AddressNotFoundExceptionImplCopyWithImpl<$Res>
    extends _$OnboardingExceptionDtoCopyWithImpl<$Res,
        _$AddressNotFoundExceptionImpl>
    implements _$$AddressNotFoundExceptionImplCopyWith<$Res> {
  __$$AddressNotFoundExceptionImplCopyWithImpl(
      _$AddressNotFoundExceptionImpl _value,
      $Res Function(_$AddressNotFoundExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of OnboardingExceptionDto
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$AddressNotFoundExceptionImpl implements _AddressNotFoundException {
  const _$AddressNotFoundExceptionImpl();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddressNotFoundExceptionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() passportNotFound,
    required TResult Function() passportFilesNotFound,
    required TResult Function() passportExpired,
    required TResult Function() emiratesIdNotFound,
    required TResult Function() emiratesIdFilesNotFound,
    required TResult Function() emiratesIdExpired,
    required TResult Function() addressNotFound,
    required TResult Function() unknown,
  }) {
    return addressNotFound();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? passportNotFound,
    TResult? Function()? passportFilesNotFound,
    TResult? Function()? passportExpired,
    TResult? Function()? emiratesIdNotFound,
    TResult? Function()? emiratesIdFilesNotFound,
    TResult? Function()? emiratesIdExpired,
    TResult? Function()? addressNotFound,
    TResult? Function()? unknown,
  }) {
    return addressNotFound?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? passportNotFound,
    TResult Function()? passportFilesNotFound,
    TResult Function()? passportExpired,
    TResult Function()? emiratesIdNotFound,
    TResult Function()? emiratesIdFilesNotFound,
    TResult Function()? emiratesIdExpired,
    TResult Function()? addressNotFound,
    TResult Function()? unknown,
    required TResult orElse(),
  }) {
    if (addressNotFound != null) {
      return addressNotFound();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PassportNotFoundException value)
        passportNotFound,
    required TResult Function(_PassportFilesNotFoundException value)
        passportFilesNotFound,
    required TResult Function(_PassportExpiredException value) passportExpired,
    required TResult Function(_EmiratesIdNotFoundException value)
        emiratesIdNotFound,
    required TResult Function(_EmiratesIdFilesNotFoundException value)
        emiratesIdFilesNotFound,
    required TResult Function(_EmiratesIdExpiredException value)
        emiratesIdExpired,
    required TResult Function(_AddressNotFoundException value) addressNotFound,
    required TResult Function(_UnknownOnboardingException value) unknown,
  }) {
    return addressNotFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PassportNotFoundException value)? passportNotFound,
    TResult? Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult? Function(_PassportExpiredException value)? passportExpired,
    TResult? Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult? Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult? Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult? Function(_AddressNotFoundException value)? addressNotFound,
    TResult? Function(_UnknownOnboardingException value)? unknown,
  }) {
    return addressNotFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PassportNotFoundException value)? passportNotFound,
    TResult Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult Function(_PassportExpiredException value)? passportExpired,
    TResult Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult Function(_AddressNotFoundException value)? addressNotFound,
    TResult Function(_UnknownOnboardingException value)? unknown,
    required TResult orElse(),
  }) {
    if (addressNotFound != null) {
      return addressNotFound(this);
    }
    return orElse();
  }
}

abstract class _AddressNotFoundException implements OnboardingExceptionDto {
  const factory _AddressNotFoundException() = _$AddressNotFoundExceptionImpl;
}

/// @nodoc
abstract class _$$UnknownOnboardingExceptionImplCopyWith<$Res> {
  factory _$$UnknownOnboardingExceptionImplCopyWith(
          _$UnknownOnboardingExceptionImpl value,
          $Res Function(_$UnknownOnboardingExceptionImpl) then) =
      __$$UnknownOnboardingExceptionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnknownOnboardingExceptionImplCopyWithImpl<$Res>
    extends _$OnboardingExceptionDtoCopyWithImpl<$Res,
        _$UnknownOnboardingExceptionImpl>
    implements _$$UnknownOnboardingExceptionImplCopyWith<$Res> {
  __$$UnknownOnboardingExceptionImplCopyWithImpl(
      _$UnknownOnboardingExceptionImpl _value,
      $Res Function(_$UnknownOnboardingExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of OnboardingExceptionDto
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UnknownOnboardingExceptionImpl implements _UnknownOnboardingException {
  const _$UnknownOnboardingExceptionImpl();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnknownOnboardingExceptionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() passportNotFound,
    required TResult Function() passportFilesNotFound,
    required TResult Function() passportExpired,
    required TResult Function() emiratesIdNotFound,
    required TResult Function() emiratesIdFilesNotFound,
    required TResult Function() emiratesIdExpired,
    required TResult Function() addressNotFound,
    required TResult Function() unknown,
  }) {
    return unknown();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? passportNotFound,
    TResult? Function()? passportFilesNotFound,
    TResult? Function()? passportExpired,
    TResult? Function()? emiratesIdNotFound,
    TResult? Function()? emiratesIdFilesNotFound,
    TResult? Function()? emiratesIdExpired,
    TResult? Function()? addressNotFound,
    TResult? Function()? unknown,
  }) {
    return unknown?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? passportNotFound,
    TResult Function()? passportFilesNotFound,
    TResult Function()? passportExpired,
    TResult Function()? emiratesIdNotFound,
    TResult Function()? emiratesIdFilesNotFound,
    TResult Function()? emiratesIdExpired,
    TResult Function()? addressNotFound,
    TResult Function()? unknown,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PassportNotFoundException value)
        passportNotFound,
    required TResult Function(_PassportFilesNotFoundException value)
        passportFilesNotFound,
    required TResult Function(_PassportExpiredException value) passportExpired,
    required TResult Function(_EmiratesIdNotFoundException value)
        emiratesIdNotFound,
    required TResult Function(_EmiratesIdFilesNotFoundException value)
        emiratesIdFilesNotFound,
    required TResult Function(_EmiratesIdExpiredException value)
        emiratesIdExpired,
    required TResult Function(_AddressNotFoundException value) addressNotFound,
    required TResult Function(_UnknownOnboardingException value) unknown,
  }) {
    return unknown(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PassportNotFoundException value)? passportNotFound,
    TResult? Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult? Function(_PassportExpiredException value)? passportExpired,
    TResult? Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult? Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult? Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult? Function(_AddressNotFoundException value)? addressNotFound,
    TResult? Function(_UnknownOnboardingException value)? unknown,
  }) {
    return unknown?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PassportNotFoundException value)? passportNotFound,
    TResult Function(_PassportFilesNotFoundException value)?
        passportFilesNotFound,
    TResult Function(_PassportExpiredException value)? passportExpired,
    TResult Function(_EmiratesIdNotFoundException value)? emiratesIdNotFound,
    TResult Function(_EmiratesIdFilesNotFoundException value)?
        emiratesIdFilesNotFound,
    TResult Function(_EmiratesIdExpiredException value)? emiratesIdExpired,
    TResult Function(_AddressNotFoundException value)? addressNotFound,
    TResult Function(_UnknownOnboardingException value)? unknown,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(this);
    }
    return orElse();
  }
}

abstract class _UnknownOnboardingException implements OnboardingExceptionDto {
  const factory _UnknownOnboardingException() =
      _$UnknownOnboardingExceptionImpl;
}
