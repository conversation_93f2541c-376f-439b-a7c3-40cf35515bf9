import 'package:feature_dashboard_api/feature_dashboard_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

part 'retail_feature_navigation_config.freezed.dart';

@freezed
class RetailFeatureNavigationConfig extends FeatureNavigationConfig
    with _$RetailFeatureNavigationConfig {
  static const name = 'retail_feature';

  const factory RetailFeatureNavigationConfig({
    DashboardFeatureNavigationConfig? dashboardConfig,
  }) = _RetailFeatureNavigationConfig;

  const RetailFeatureNavigationConfig._() : super(name);

  @override
  String toString() => 'RetailFeatureNavigationConfig';
}
