part of '../dashboard_screen.dart';

class _ActionItems extends StatefulWidget {
  final bool isSendAvailable;
  final bool isExchangeAvailable;
  final bool showEducationalInfo;

  const _ActionItems({
    required this.isSendAvailable,
    required this.isExchangeAvailable,
    this.showEducationalInfo = false,
  });

  @override
  State<_ActionItems> createState() => _ActionItemsState();
}

class _ActionItemsState extends State<_ActionItems> {
  final _bannerOverlay = const BannerOverlay();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future<void>.delayed(const Duration(milliseconds: 300)).then((_) {
        if (mounted && widget.showEducationalInfo && context.isTopVisible) {
          _bannerOverlay.show(
            context: context,
            builder: (_, animation) {
              return const _AddMoneyCoachMark();
            },
            onTapOut: context.read<DashboardCubit>().getNextCoachMarkIfAny,
          );
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final localize = DashboardLocalizations.of(context);
    final cubit = context.read<DashboardCubit>();

    return ColoredBox(
      color: context.colorStyling.background1,
      child: SizedBox(
        height: 96,
        child: ListView(
          padding: const EdgeInsets.symmetric(vertical: 24.0),
          scrollDirection: Axis.horizontal,
          children: [
            const Space.horizontal(24),
            Padding(
              padding: const EdgeInsetsDirectional.only(end: 16),
              child: EducationalInfoWidget(
                key: DashboardScreen.addMoneyButtonKey,
                targetLink: EducationInfoKeys.targetLayerLink,
                isInfoNeeded: widget.showEducationalInfo,
                targetWidget: const _AddMoneyButton(),
              ),
            ),
            if (widget.isSendAvailable)
              Padding(
                padding: const EdgeInsetsDirectional.only(end: 16),
                child: DashboardActionButton(
                  key: DashboardScreen.sendButtonKey,
                  icon: CompanyIconPointer.arrow_north_east,
                  title: localize.sendButtonLabel,
                  onPressed: cubit.navigateToPayments,
                ),
              ),
            if (widget.isExchangeAvailable)
              Padding(
                padding: const EdgeInsetsDirectional.only(end: 16),
                child: DashboardActionButton(
                  key: DashboardScreen.exchangeButtonKey,
                  icon: CompanyIconPointer.payments,
                  title: localize.exchangeButtonLabel,
                  onPressed: cubit.navigateToCurrencyExchange,
                ),
              ),
            const Space.horizontal(24),
          ],
        ),
      ),
    );
  }
}

class _AddMoneyButton extends StatelessWidget {
  const _AddMoneyButton();

  @override
  Widget build(BuildContext context) {
    final localize = DashboardLocalizations.of(context);
    final cubit = context.watch<DashboardCubit>();

    return DashboardActionButton(
      icon: CompanyIconPointer.plus,
      title: localize.topUpButtonLabel,
      onPressed: cubit.navigateToAddMoney,
    );
  }
}

class _AddMoneyCoachMark extends StatelessWidget {
  const _AddMoneyCoachMark();

  @override
  Widget build(BuildContext context) {
    final localizations = DashboardLocalizations.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CompositedTransformFollower(
          link: EducationInfoKeys.targetLayerLink,
          followerAnchor: Alignment.bottomLeft,
          offset: const Offset(-24, -10),
          child: CoachMark(
            coachMarkModel: CoachMarkModel(
              title: localizations.addMoneyCoachMarkTitle,
              body: localizations.addMoneyCoachMarkSubTitle,
              primaryCtaText: localizations.addMoneyTryNow,
              gradientPointer: CoachMarkGradient.indigo,
              direction: CoachMarkPointerDirection.bottom,
            ),
            primaryCallBack: Navigator.of(context).maybePop,
          ),
        ),
        CompositedTransformFollower(
          link: EducationInfoKeys.targetLayerLink,
          child: DashboardActionButton(
            icon: CompanyIconPointer.plus,
            title: localizations.topUpButtonLabel,
            onPressed: () {},
          ),
        ),
      ],
    );
  }
}
