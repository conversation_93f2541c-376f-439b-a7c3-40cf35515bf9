import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/handlers/dashboard_family_banking_handler.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late FamilyBankingFlow flow;
  late FamilyBankingGuideInteractor guideInteractor;
  late FamilyBankingInvitationInteractor invitationInteractor;
  late FeatureToggleProvider featureToggles;
  late DashboardFamilyBankingHandler handler;

  final invitation = TestEntities.randInvitation();

  void stubFamilyBankingEnabled({bool enabled = true}) {
    when(
      () => featureToggles.get(
        FamilyBankingFeatureToggles.isFamilyBankingFeatureEnabled,
      ),
    ).thenReturn(enabled);
  }

  setUp(() {
    flow = MockFamilyBankingFlow();
    guideInteractor = MockFamilyBankingGuideInteractor();
    invitationInteractor = MockFamilyBankingInvitationInteractor();
    featureToggles = MockFeatureToggleProvider();
    handler = DashboardFamilyBankingHandler(
      flow: flow,
      guideInteractor: guideInteractor,
      invitationInteractor: invitationInteractor,
      featureToggles: featureToggles,
      logger: MockLogger(),
    );

    stubFamilyBankingEnabled();
  });

  group('Get pending invitation >', () {
    test(
      'fetches pending invitation only if it was not viewed before',
      () async {
        // Arrange
        when(
          () => guideInteractor.canShowGuide(FamilyGuide.showFirstInvitation),
        ).justAnswerAsync(true);
        when(
          () => invitationInteractor.getPendingInvitations(),
        ).justAnswerAsync([invitation]);

        // Act
        final actual = await handler.getPendingInvitation();

        // Assert
        expect(actual, invitation);
      },
    );

    test(
      'does not fetch pending invitation if family banking is not enabled',
      () async {
        // Arrange
        stubFamilyBankingEnabled(enabled: false);

        // Act
        final actual = await handler.getPendingInvitation();

        // Assert
        expect(actual, isNull);
        verifyNever(invitationInteractor.getPendingInvitations);
      },
    );

    test(
      'does not fetch pending invitation if it was viewed before',
      () async {
        // Arrange
        when(
          () => guideInteractor.canShowGuide(FamilyGuide.showFirstInvitation),
        ).justAnswerAsync(false);

        // Act
        final actual = await handler.getPendingInvitation();

        // Assert
        expect(actual, isNull);
        verifyNever(invitationInteractor.getPendingInvitations);
      },
    );

    test(
      'returns null if fetching pending invitation fails',
      () async {
        // Arrange
        when(
          () => guideInteractor.canShowGuide(FamilyGuide.showFirstInvitation),
        ).justAnswerAsync(true);
        when(
          () => invitationInteractor.getPendingInvitations(),
        ).justThrowAsync(Exception('What is the meaning of life?'));

        // Act
        final actual = await handler.getPendingInvitation();

        // Assert
        expect(actual, isNull);
      },
    );

    test(
      'returns null if there is no pending invitation',
      () async {
        // Arrange
        when(
          () => guideInteractor.canShowGuide(FamilyGuide.showFirstInvitation),
        ).justAnswerAsync(true);
        when(
          () => invitationInteractor.getPendingInvitations(),
        ).justAnswerAsync([]);

        // Act
        final actual = await handler.getPendingInvitation();

        // Assert
        expect(actual, isNull);
      },
    );
  });

  group('Show pending invitation >', () {
    test('show and marks invitation as seen', () async {
      // Arrange
      when(() => flow.openPendingInvitation(invitation)).justCompleteAsync();

      // Act
      await handler.showPendingInvitation(invitation);

      // Assert
      verifyInOrder([
        () => guideInteractor.markGuideViewed(FamilyGuide.showFirstInvitation),
        () => flow.openPendingInvitation(invitation),
      ]);
    });
  });
}
