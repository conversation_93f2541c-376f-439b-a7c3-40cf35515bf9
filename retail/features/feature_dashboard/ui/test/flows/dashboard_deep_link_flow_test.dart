import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:feature_dashboard_api/feature_dashboard_api.dart';
import 'package:feature_dashboard_ui/feature_dashboard_ui.dart';
import 'package:feature_dashboard_ui/src/flows/dashboard_deep_link_flow_impl.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/handlers/dashboard_navigation_handler.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/handlers/dashboard_savings_handler.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:ui/cubit/core/loading_provider.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_transaction_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_account_api/wio_feature_account_api.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late NavigationProvider navigationProvider;
  late AccountInteractor accountInteractor;
  late DashboardSavingsHandler savingsHandler;
  late DashboardNavigationHandler dashboardNavigationHandler;
  late LoadingProvider loadingProvider;
  late FeatureToggleProvider featureToggles;
  late DashboardDeepLinkFlow dashboardDeepLinkFlow;
  late MockDashboardNavigationProvider dashboardNavigationProvider;

  setUp(() {
    navigationProvider = MockNavigationProvider();
    accountInteractor = MockAccountInteractor();
    savingsHandler = MockDashboardSavingsHandler();
    dashboardNavigationHandler = MockDashboardNavigationHandler();
    loadingProvider = MockLoadingProvider();
    featureToggles = MockFeatureToggleProvider();
    dashboardNavigationProvider = MockDashboardNavigationProvider();

    dashboardDeepLinkFlow = DashboardDeepLinkFlowImpl(
      navigationProvider: navigationProvider,
      accountInteractor: accountInteractor,
      savingsHandler: savingsHandler,
      dashboardNavigationHandler: dashboardNavigationHandler,
      loadingProvider: loadingProvider,
      featureToggles: featureToggles,
      logger: MockLogger(),
      dashboardNavigationProvider: dashboardNavigationProvider,
    );

    when(
      () => dashboardNavigationHandler.handleAddMoneyBottomSheet(
        accounts: any(named: 'accounts', that: isA<List<AccountDetails>>()),
        showSavingSpace: anyNamed('showSavingSpace'),
      ),
    ).justCompleteAsync();

    when(() => navigationProvider.push<void>(any())).justCompleteAsync();
    when(() => navigationProvider.removeStackAndPush<Object?>(any()))
        .justCompleteAsync();

    when(() => dashboardNavigationProvider.getDashboardNavigationConfig())
        .justAnswerAsync(const DashboardFeatureNavigationConfig());
  });

  final accounts = [
    TestEntities.randAccount(currency: Currency.aed, id: 'aed'),
    TestEntities.randAccount(currency: Currency.usd, id: 'usd'),
    TestEntities.randAccount(currency: Currency.euro, id: 'euro'),
    TestEntities.randAccount(currency: Currency.gbp, id: 'gbp'),
  ];

  registerFallbackValue(
    const FeatureToggleKey<bool>(key: '', defaultValue: false),
  );

  registerFallbackValue(BottomSheetNavigationConfigFake<void>());
  registerFallbackValue(
    BottomSheetNavigationConfigFake<AddMoneyBottomSheetResult>(),
  );

  group(
    'Dashboard deep link flow tests',
    () {
      test(
        'Dashboard root deeplink',
        () async {
          await dashboardDeepLinkFlow.runDeepLink(
            DashboardDeepLinkType.dashboardRoot,
          );

          verify(
            () => navigationProvider.removeStackAndPush(
              const DashboardFeatureNavigationConfig(),
            ),
          ).calledOnce;
        },
      );

      test(
        'Account Details deeplink',
        () async {
          when(() => accountInteractor.getAccountDetails())
              .justAnswerAsync(accounts);

          await dashboardDeepLinkFlow.runDeepLink(
            DashboardDeepLinkType.accountDetails,
          );

          verify(
            () => navigationProvider.removeStackAndPush<Object?>(
              const DashboardFeatureNavigationConfig(),
            ),
          ).calledOnce;

          verify(
            () => navigationProvider.push<void>(
              AccountDetailsScreenNavigationConfig(
                accounts: accounts,
                selector: AccountSelector.byCurrency(Currency.aed),
              ),
            ),
          ).calledOnce;
        },
      );

      test(
        'All transactions deeplink',
        () async {
          when(() => featureToggles.get<bool>(any())).thenReturn(true);

          await dashboardDeepLinkFlow.runDeepLink(
            DashboardDeepLinkType.transactions,
          );

          verify(
            () => navigationProvider.removeStackAndPush(
              const DashboardFeatureNavigationConfig(),
            ),
          ).calledOnce;

          verify(
            () => navigationProvider.push(
              any(that: isA<AllTransactionsScreenNavigationConfig>()),
            ),
          ).calledOnce;
        },
      );

      test(
        'Account Add Money Link',
        () async {
          when(() => loadingProvider.loading(true)).justComplete();

          when(() => accountInteractor.getAccountDetails())
              .thenAnswer((_) async => accounts);
          when(() => savingsHandler.getSavingSpaces()).justAnswerAsync([]);

          when(
            () => dashboardNavigationHandler.handleAddMoneyBottomSheet(
              accounts: accounts,
            ),
          ).justCompleteAsync();

          await dashboardDeepLinkFlow.runDeepLink(
            DashboardDeepLinkType.addMoney,
          );

          verify(
            () => navigationProvider.removeStackAndPush(
              const DashboardFeatureNavigationConfig(),
            ),
          ).called(1);

          verify(
            () => dashboardNavigationHandler.handleAddMoneyBottomSheet(
              accounts: accounts,
            ),
          ).called(1);
        },
      );

      test(
        'Account Account List deeplink',
        () async {
          final account = TestEntities.randAccount(currency: Currency.aed);

          when(() => accountInteractor.getAccountDetails())
              .thenAnswer((_) async => accounts);

          when(
            () => navigationProvider.showBottomSheet(
              any(),
            ),
          ).thenAnswer(
            (_) async =>
                AccountsBottomSheetResult.pickedAccount(account: account),
          );

          await dashboardDeepLinkFlow.runDeepLink(
            DashboardDeepLinkType.accountDetails,
          );

          verify(
            () => navigationProvider.push<void>(
              any(),
            ),
          ).called(1);
        },
      );
    },
  );

  group('Open add money >', () {
    const options = [
      AddMoneyOption.bankTransfer(),
      AddMoneyOption.savingSpace(),
    ];

    setUp(() {
      when(
        () => navigationProvider.showBottomSheet<AddMoneyBottomSheetResult>(
          any(that: isA<AddMoneyBottomSheetNavigationConfig>()),
        ),
      ).justCompleteAsync();
    });

    test(
      'opens the bottom sheet without saving space option '
      'if the customer has not saving spaces',
      () async {
        // Arrange
        when(savingsHandler.getSavingSpaces).justAnswerAsync([]);

        // Act
        await dashboardDeepLinkFlow.runAddMoneyWithResult(
          preferredOptions: options,
        );

        // Assert
        verify(savingsHandler.getSavingSpaces);
        verify(
          () => navigationProvider.showBottomSheet<AddMoneyBottomSheetResult>(
            const AddMoneyBottomSheetNavigationConfig.custom(
              preferredOptions: [AddMoneyOption.bankTransfer()],
            ),
          ),
        );
      },
    );

    test(
      'opens the bottom sheet without saving space option '
      'if fetching saving spaces fails',
      () async {
        // Arrange
        when(savingsHandler.getSavingSpaces).justThrowAsync(Exception('Oops'));

        // Act
        await dashboardDeepLinkFlow.runAddMoneyWithResult(
          preferredOptions: options,
        );

        // Assert
        verify(
          () => navigationProvider.showBottomSheet(
            const AddMoneyBottomSheetNavigationConfig.custom(
              preferredOptions: [AddMoneyOption.bankTransfer()],
            ),
          ),
        );
      },
    );

    test(
      'does not fetch saving spaces if saving space option is not requested',
      () async {
        // Arrange

        // Act
        await dashboardDeepLinkFlow.runAddMoneyWithResult(
          preferredOptions: const [AddMoneyOption.bankTransfer()],
        );

        // Assert
        verifyZeroInteractions(savingsHandler);
      },
    );
  });

  group('Open accounts >', () {
    test('opens accounts screen for the given account ID', () async {
      // Arrange
      final id = accounts.last.id;
      final config = DashboardPersonalScreenNavigationConfig.forId(
        accountId: id,
      );
      when(() => navigationProvider.push<void>(config)).justCompleteAsync();

      // Act
      await dashboardDeepLinkFlow.runAccounts(accountId: id);

      // Assert
      verify(() => navigationProvider.push<void>(config));
    });
  });
}
