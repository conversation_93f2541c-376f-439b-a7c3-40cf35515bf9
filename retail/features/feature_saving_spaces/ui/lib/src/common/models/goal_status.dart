import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/money.dart';

part 'goal_status.freezed.dart';

@freezed
class GoalStatus with _$GoalStatus {
  const factory GoalStatus.none() = _NoGoalStatus;

  const factory GoalStatus.reached({
    required Money amount,
  }) = _GoalAmountReachedStatus;

  const factory GoalStatus.missed({
    required DateTime deadline,
  }) = _GoalDeadlineMissedStatus;

  const factory GoalStatus.inProgress({
    Money? amount,
    DateTime? date,
  }) = _GoalInProgressStatus;
}
