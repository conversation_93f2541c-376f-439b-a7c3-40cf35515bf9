// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'edit_saving_space_deadline_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$EditSavingSpaceDeadlineState {
  DateInput get input => throw _privateConstructorUsedError;
  SavingSpace get savingSpace => throw _privateConstructorUsedError;
  List<DeadlinePeriod> get suggestedPeriods =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)
        idle,
    required TResult Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)
        inProgress,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)?
        idle,
    TResult? Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)?
        inProgress,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)?
        idle,
    TResult Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)?
        inProgress,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_IdleEditSavingSpaceDeadlineState value) idle,
    required TResult Function(_InProgressEditSavingSpaceDeadlineState value)
        inProgress,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_IdleEditSavingSpaceDeadlineState value)? idle,
    TResult? Function(_InProgressEditSavingSpaceDeadlineState value)?
        inProgress,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_IdleEditSavingSpaceDeadlineState value)? idle,
    TResult Function(_InProgressEditSavingSpaceDeadlineState value)? inProgress,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $EditSavingSpaceDeadlineStateCopyWith<EditSavingSpaceDeadlineState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EditSavingSpaceDeadlineStateCopyWith<$Res> {
  factory $EditSavingSpaceDeadlineStateCopyWith(
          EditSavingSpaceDeadlineState value,
          $Res Function(EditSavingSpaceDeadlineState) then) =
      _$EditSavingSpaceDeadlineStateCopyWithImpl<$Res,
          EditSavingSpaceDeadlineState>;
  @useResult
  $Res call(
      {DateInput input,
      SavingSpace savingSpace,
      List<DeadlinePeriod> suggestedPeriods});

  $DateInputCopyWith<$Res> get input;
  $SavingSpaceCopyWith<$Res> get savingSpace;
}

/// @nodoc
class _$EditSavingSpaceDeadlineStateCopyWithImpl<$Res,
        $Val extends EditSavingSpaceDeadlineState>
    implements $EditSavingSpaceDeadlineStateCopyWith<$Res> {
  _$EditSavingSpaceDeadlineStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? input = null,
    Object? savingSpace = null,
    Object? suggestedPeriods = null,
  }) {
    return _then(_value.copyWith(
      input: null == input
          ? _value.input
          : input // ignore: cast_nullable_to_non_nullable
              as DateInput,
      savingSpace: null == savingSpace
          ? _value.savingSpace
          : savingSpace // ignore: cast_nullable_to_non_nullable
              as SavingSpace,
      suggestedPeriods: null == suggestedPeriods
          ? _value.suggestedPeriods
          : suggestedPeriods // ignore: cast_nullable_to_non_nullable
              as List<DeadlinePeriod>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $DateInputCopyWith<$Res> get input {
    return $DateInputCopyWith<$Res>(_value.input, (value) {
      return _then(_value.copyWith(input: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $SavingSpaceCopyWith<$Res> get savingSpace {
    return $SavingSpaceCopyWith<$Res>(_value.savingSpace, (value) {
      return _then(_value.copyWith(savingSpace: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$IdleEditSavingSpaceDeadlineStateImplCopyWith<$Res>
    implements $EditSavingSpaceDeadlineStateCopyWith<$Res> {
  factory _$$IdleEditSavingSpaceDeadlineStateImplCopyWith(
          _$IdleEditSavingSpaceDeadlineStateImpl value,
          $Res Function(_$IdleEditSavingSpaceDeadlineStateImpl) then) =
      __$$IdleEditSavingSpaceDeadlineStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateInput input,
      SavingSpace savingSpace,
      List<DeadlinePeriod> suggestedPeriods});

  @override
  $DateInputCopyWith<$Res> get input;
  @override
  $SavingSpaceCopyWith<$Res> get savingSpace;
}

/// @nodoc
class __$$IdleEditSavingSpaceDeadlineStateImplCopyWithImpl<$Res>
    extends _$EditSavingSpaceDeadlineStateCopyWithImpl<$Res,
        _$IdleEditSavingSpaceDeadlineStateImpl>
    implements _$$IdleEditSavingSpaceDeadlineStateImplCopyWith<$Res> {
  __$$IdleEditSavingSpaceDeadlineStateImplCopyWithImpl(
      _$IdleEditSavingSpaceDeadlineStateImpl _value,
      $Res Function(_$IdleEditSavingSpaceDeadlineStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? input = null,
    Object? savingSpace = null,
    Object? suggestedPeriods = null,
  }) {
    return _then(_$IdleEditSavingSpaceDeadlineStateImpl(
      input: null == input
          ? _value.input
          : input // ignore: cast_nullable_to_non_nullable
              as DateInput,
      savingSpace: null == savingSpace
          ? _value.savingSpace
          : savingSpace // ignore: cast_nullable_to_non_nullable
              as SavingSpace,
      suggestedPeriods: null == suggestedPeriods
          ? _value._suggestedPeriods
          : suggestedPeriods // ignore: cast_nullable_to_non_nullable
              as List<DeadlinePeriod>,
    ));
  }
}

/// @nodoc

class _$IdleEditSavingSpaceDeadlineStateImpl
    extends _IdleEditSavingSpaceDeadlineState {
  const _$IdleEditSavingSpaceDeadlineStateImpl(
      {required this.input,
      required this.savingSpace,
      final List<DeadlinePeriod> suggestedPeriods = DeadlinePeriod.values})
      : _suggestedPeriods = suggestedPeriods,
        super._();

  @override
  final DateInput input;
  @override
  final SavingSpace savingSpace;
  final List<DeadlinePeriod> _suggestedPeriods;
  @override
  @JsonKey()
  List<DeadlinePeriod> get suggestedPeriods {
    if (_suggestedPeriods is EqualUnmodifiableListView)
      return _suggestedPeriods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_suggestedPeriods);
  }

  @override
  String toString() {
    return 'EditSavingSpaceDeadlineState.idle(input: $input, savingSpace: $savingSpace, suggestedPeriods: $suggestedPeriods)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdleEditSavingSpaceDeadlineStateImpl &&
            (identical(other.input, input) || other.input == input) &&
            (identical(other.savingSpace, savingSpace) ||
                other.savingSpace == savingSpace) &&
            const DeepCollectionEquality()
                .equals(other._suggestedPeriods, _suggestedPeriods));
  }

  @override
  int get hashCode => Object.hash(runtimeType, input, savingSpace,
      const DeepCollectionEquality().hash(_suggestedPeriods));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$IdleEditSavingSpaceDeadlineStateImplCopyWith<
          _$IdleEditSavingSpaceDeadlineStateImpl>
      get copyWith => __$$IdleEditSavingSpaceDeadlineStateImplCopyWithImpl<
          _$IdleEditSavingSpaceDeadlineStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)
        idle,
    required TResult Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)
        inProgress,
  }) {
    return idle(input, savingSpace, suggestedPeriods);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)?
        idle,
    TResult? Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)?
        inProgress,
  }) {
    return idle?.call(input, savingSpace, suggestedPeriods);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)?
        idle,
    TResult Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)?
        inProgress,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(input, savingSpace, suggestedPeriods);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_IdleEditSavingSpaceDeadlineState value) idle,
    required TResult Function(_InProgressEditSavingSpaceDeadlineState value)
        inProgress,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_IdleEditSavingSpaceDeadlineState value)? idle,
    TResult? Function(_InProgressEditSavingSpaceDeadlineState value)?
        inProgress,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_IdleEditSavingSpaceDeadlineState value)? idle,
    TResult Function(_InProgressEditSavingSpaceDeadlineState value)? inProgress,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class _IdleEditSavingSpaceDeadlineState
    extends EditSavingSpaceDeadlineState {
  const factory _IdleEditSavingSpaceDeadlineState(
          {required final DateInput input,
          required final SavingSpace savingSpace,
          final List<DeadlinePeriod> suggestedPeriods}) =
      _$IdleEditSavingSpaceDeadlineStateImpl;
  const _IdleEditSavingSpaceDeadlineState._() : super._();

  @override
  DateInput get input;
  @override
  SavingSpace get savingSpace;
  @override
  List<DeadlinePeriod> get suggestedPeriods;
  @override
  @JsonKey(ignore: true)
  _$$IdleEditSavingSpaceDeadlineStateImplCopyWith<
          _$IdleEditSavingSpaceDeadlineStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InProgressEditSavingSpaceDeadlineStateImplCopyWith<$Res>
    implements $EditSavingSpaceDeadlineStateCopyWith<$Res> {
  factory _$$InProgressEditSavingSpaceDeadlineStateImplCopyWith(
          _$InProgressEditSavingSpaceDeadlineStateImpl value,
          $Res Function(_$InProgressEditSavingSpaceDeadlineStateImpl) then) =
      __$$InProgressEditSavingSpaceDeadlineStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateInput input,
      SavingSpace savingSpace,
      List<DeadlinePeriod> suggestedPeriods});

  @override
  $DateInputCopyWith<$Res> get input;
  @override
  $SavingSpaceCopyWith<$Res> get savingSpace;
}

/// @nodoc
class __$$InProgressEditSavingSpaceDeadlineStateImplCopyWithImpl<$Res>
    extends _$EditSavingSpaceDeadlineStateCopyWithImpl<$Res,
        _$InProgressEditSavingSpaceDeadlineStateImpl>
    implements _$$InProgressEditSavingSpaceDeadlineStateImplCopyWith<$Res> {
  __$$InProgressEditSavingSpaceDeadlineStateImplCopyWithImpl(
      _$InProgressEditSavingSpaceDeadlineStateImpl _value,
      $Res Function(_$InProgressEditSavingSpaceDeadlineStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? input = null,
    Object? savingSpace = null,
    Object? suggestedPeriods = null,
  }) {
    return _then(_$InProgressEditSavingSpaceDeadlineStateImpl(
      input: null == input
          ? _value.input
          : input // ignore: cast_nullable_to_non_nullable
              as DateInput,
      savingSpace: null == savingSpace
          ? _value.savingSpace
          : savingSpace // ignore: cast_nullable_to_non_nullable
              as SavingSpace,
      suggestedPeriods: null == suggestedPeriods
          ? _value._suggestedPeriods
          : suggestedPeriods // ignore: cast_nullable_to_non_nullable
              as List<DeadlinePeriod>,
    ));
  }
}

/// @nodoc

class _$InProgressEditSavingSpaceDeadlineStateImpl
    extends _InProgressEditSavingSpaceDeadlineState {
  const _$InProgressEditSavingSpaceDeadlineStateImpl(
      {required this.input,
      required this.savingSpace,
      final List<DeadlinePeriod> suggestedPeriods = DeadlinePeriod.values})
      : _suggestedPeriods = suggestedPeriods,
        super._();

  @override
  final DateInput input;
  @override
  final SavingSpace savingSpace;
  final List<DeadlinePeriod> _suggestedPeriods;
  @override
  @JsonKey()
  List<DeadlinePeriod> get suggestedPeriods {
    if (_suggestedPeriods is EqualUnmodifiableListView)
      return _suggestedPeriods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_suggestedPeriods);
  }

  @override
  String toString() {
    return 'EditSavingSpaceDeadlineState.inProgress(input: $input, savingSpace: $savingSpace, suggestedPeriods: $suggestedPeriods)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InProgressEditSavingSpaceDeadlineStateImpl &&
            (identical(other.input, input) || other.input == input) &&
            (identical(other.savingSpace, savingSpace) ||
                other.savingSpace == savingSpace) &&
            const DeepCollectionEquality()
                .equals(other._suggestedPeriods, _suggestedPeriods));
  }

  @override
  int get hashCode => Object.hash(runtimeType, input, savingSpace,
      const DeepCollectionEquality().hash(_suggestedPeriods));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InProgressEditSavingSpaceDeadlineStateImplCopyWith<
          _$InProgressEditSavingSpaceDeadlineStateImpl>
      get copyWith =>
          __$$InProgressEditSavingSpaceDeadlineStateImplCopyWithImpl<
              _$InProgressEditSavingSpaceDeadlineStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)
        idle,
    required TResult Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)
        inProgress,
  }) {
    return inProgress(input, savingSpace, suggestedPeriods);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)?
        idle,
    TResult? Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)?
        inProgress,
  }) {
    return inProgress?.call(input, savingSpace, suggestedPeriods);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)?
        idle,
    TResult Function(DateInput input, SavingSpace savingSpace,
            List<DeadlinePeriod> suggestedPeriods)?
        inProgress,
    required TResult orElse(),
  }) {
    if (inProgress != null) {
      return inProgress(input, savingSpace, suggestedPeriods);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_IdleEditSavingSpaceDeadlineState value) idle,
    required TResult Function(_InProgressEditSavingSpaceDeadlineState value)
        inProgress,
  }) {
    return inProgress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_IdleEditSavingSpaceDeadlineState value)? idle,
    TResult? Function(_InProgressEditSavingSpaceDeadlineState value)?
        inProgress,
  }) {
    return inProgress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_IdleEditSavingSpaceDeadlineState value)? idle,
    TResult Function(_InProgressEditSavingSpaceDeadlineState value)? inProgress,
    required TResult orElse(),
  }) {
    if (inProgress != null) {
      return inProgress(this);
    }
    return orElse();
  }
}

abstract class _InProgressEditSavingSpaceDeadlineState
    extends EditSavingSpaceDeadlineState {
  const factory _InProgressEditSavingSpaceDeadlineState(
          {required final DateInput input,
          required final SavingSpace savingSpace,
          final List<DeadlinePeriod> suggestedPeriods}) =
      _$InProgressEditSavingSpaceDeadlineStateImpl;
  const _InProgressEditSavingSpaceDeadlineState._() : super._();

  @override
  DateInput get input;
  @override
  SavingSpace get savingSpace;
  @override
  List<DeadlinePeriod> get suggestedPeriods;
  @override
  @JsonKey(ignore: true)
  _$$InProgressEditSavingSpaceDeadlineStateImplCopyWith<
          _$InProgressEditSavingSpaceDeadlineStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DateInput {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String text, DateInputError? error) inProgress,
    required TResult Function(DateTime date) parsed,
    required TResult Function(DeadlinePeriod period, DateTime date) suggested,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String text, DateInputError? error)? inProgress,
    TResult? Function(DateTime date)? parsed,
    TResult? Function(DeadlinePeriod period, DateTime date)? suggested,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String text, DateInputError? error)? inProgress,
    TResult Function(DateTime date)? parsed,
    TResult Function(DeadlinePeriod period, DateTime date)? suggested,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InProgressDateInput value) inProgress,
    required TResult Function(_ParsedDateInput value) parsed,
    required TResult Function(_SuggestedDateInput value) suggested,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InProgressDateInput value)? inProgress,
    TResult? Function(_ParsedDateInput value)? parsed,
    TResult? Function(_SuggestedDateInput value)? suggested,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InProgressDateInput value)? inProgress,
    TResult Function(_ParsedDateInput value)? parsed,
    TResult Function(_SuggestedDateInput value)? suggested,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DateInputCopyWith<$Res> {
  factory $DateInputCopyWith(DateInput value, $Res Function(DateInput) then) =
      _$DateInputCopyWithImpl<$Res, DateInput>;
}

/// @nodoc
class _$DateInputCopyWithImpl<$Res, $Val extends DateInput>
    implements $DateInputCopyWith<$Res> {
  _$DateInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$InProgressDateInputImplCopyWith<$Res> {
  factory _$$InProgressDateInputImplCopyWith(_$InProgressDateInputImpl value,
          $Res Function(_$InProgressDateInputImpl) then) =
      __$$InProgressDateInputImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String text, DateInputError? error});
}

/// @nodoc
class __$$InProgressDateInputImplCopyWithImpl<$Res>
    extends _$DateInputCopyWithImpl<$Res, _$InProgressDateInputImpl>
    implements _$$InProgressDateInputImplCopyWith<$Res> {
  __$$InProgressDateInputImplCopyWithImpl(_$InProgressDateInputImpl _value,
      $Res Function(_$InProgressDateInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = null,
    Object? error = freezed,
  }) {
    return _then(_$InProgressDateInputImpl(
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as DateInputError?,
    ));
  }
}

/// @nodoc

class _$InProgressDateInputImpl extends _InProgressDateInput {
  const _$InProgressDateInputImpl({required this.text, this.error}) : super._();

  @override
  final String text;
  @override
  final DateInputError? error;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InProgressDateInputImpl &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, text, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InProgressDateInputImplCopyWith<_$InProgressDateInputImpl> get copyWith =>
      __$$InProgressDateInputImplCopyWithImpl<_$InProgressDateInputImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String text, DateInputError? error) inProgress,
    required TResult Function(DateTime date) parsed,
    required TResult Function(DeadlinePeriod period, DateTime date) suggested,
  }) {
    return inProgress(text, error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String text, DateInputError? error)? inProgress,
    TResult? Function(DateTime date)? parsed,
    TResult? Function(DeadlinePeriod period, DateTime date)? suggested,
  }) {
    return inProgress?.call(text, error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String text, DateInputError? error)? inProgress,
    TResult Function(DateTime date)? parsed,
    TResult Function(DeadlinePeriod period, DateTime date)? suggested,
    required TResult orElse(),
  }) {
    if (inProgress != null) {
      return inProgress(text, error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InProgressDateInput value) inProgress,
    required TResult Function(_ParsedDateInput value) parsed,
    required TResult Function(_SuggestedDateInput value) suggested,
  }) {
    return inProgress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InProgressDateInput value)? inProgress,
    TResult? Function(_ParsedDateInput value)? parsed,
    TResult? Function(_SuggestedDateInput value)? suggested,
  }) {
    return inProgress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InProgressDateInput value)? inProgress,
    TResult Function(_ParsedDateInput value)? parsed,
    TResult Function(_SuggestedDateInput value)? suggested,
    required TResult orElse(),
  }) {
    if (inProgress != null) {
      return inProgress(this);
    }
    return orElse();
  }
}

abstract class _InProgressDateInput extends DateInput {
  const factory _InProgressDateInput(
      {required final String text,
      final DateInputError? error}) = _$InProgressDateInputImpl;
  const _InProgressDateInput._() : super._();

  String get text;
  DateInputError? get error;
  @JsonKey(ignore: true)
  _$$InProgressDateInputImplCopyWith<_$InProgressDateInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ParsedDateInputImplCopyWith<$Res> {
  factory _$$ParsedDateInputImplCopyWith(_$ParsedDateInputImpl value,
          $Res Function(_$ParsedDateInputImpl) then) =
      __$$ParsedDateInputImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime date});
}

/// @nodoc
class __$$ParsedDateInputImplCopyWithImpl<$Res>
    extends _$DateInputCopyWithImpl<$Res, _$ParsedDateInputImpl>
    implements _$$ParsedDateInputImplCopyWith<$Res> {
  __$$ParsedDateInputImplCopyWithImpl(
      _$ParsedDateInputImpl _value, $Res Function(_$ParsedDateInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
  }) {
    return _then(_$ParsedDateInputImpl(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$ParsedDateInputImpl extends _ParsedDateInput {
  const _$ParsedDateInputImpl({required this.date}) : super._();

  @override
  final DateTime date;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ParsedDateInputImpl &&
            (identical(other.date, date) || other.date == date));
  }

  @override
  int get hashCode => Object.hash(runtimeType, date);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ParsedDateInputImplCopyWith<_$ParsedDateInputImpl> get copyWith =>
      __$$ParsedDateInputImplCopyWithImpl<_$ParsedDateInputImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String text, DateInputError? error) inProgress,
    required TResult Function(DateTime date) parsed,
    required TResult Function(DeadlinePeriod period, DateTime date) suggested,
  }) {
    return parsed(date);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String text, DateInputError? error)? inProgress,
    TResult? Function(DateTime date)? parsed,
    TResult? Function(DeadlinePeriod period, DateTime date)? suggested,
  }) {
    return parsed?.call(date);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String text, DateInputError? error)? inProgress,
    TResult Function(DateTime date)? parsed,
    TResult Function(DeadlinePeriod period, DateTime date)? suggested,
    required TResult orElse(),
  }) {
    if (parsed != null) {
      return parsed(date);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InProgressDateInput value) inProgress,
    required TResult Function(_ParsedDateInput value) parsed,
    required TResult Function(_SuggestedDateInput value) suggested,
  }) {
    return parsed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InProgressDateInput value)? inProgress,
    TResult? Function(_ParsedDateInput value)? parsed,
    TResult? Function(_SuggestedDateInput value)? suggested,
  }) {
    return parsed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InProgressDateInput value)? inProgress,
    TResult Function(_ParsedDateInput value)? parsed,
    TResult Function(_SuggestedDateInput value)? suggested,
    required TResult orElse(),
  }) {
    if (parsed != null) {
      return parsed(this);
    }
    return orElse();
  }
}

abstract class _ParsedDateInput extends DateInput {
  const factory _ParsedDateInput({required final DateTime date}) =
      _$ParsedDateInputImpl;
  const _ParsedDateInput._() : super._();

  DateTime get date;
  @JsonKey(ignore: true)
  _$$ParsedDateInputImplCopyWith<_$ParsedDateInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SuggestedDateInputImplCopyWith<$Res> {
  factory _$$SuggestedDateInputImplCopyWith(_$SuggestedDateInputImpl value,
          $Res Function(_$SuggestedDateInputImpl) then) =
      __$$SuggestedDateInputImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DeadlinePeriod period, DateTime date});
}

/// @nodoc
class __$$SuggestedDateInputImplCopyWithImpl<$Res>
    extends _$DateInputCopyWithImpl<$Res, _$SuggestedDateInputImpl>
    implements _$$SuggestedDateInputImplCopyWith<$Res> {
  __$$SuggestedDateInputImplCopyWithImpl(_$SuggestedDateInputImpl _value,
      $Res Function(_$SuggestedDateInputImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? period = null,
    Object? date = null,
  }) {
    return _then(_$SuggestedDateInputImpl(
      period: null == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as DeadlinePeriod,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$SuggestedDateInputImpl extends _SuggestedDateInput {
  const _$SuggestedDateInputImpl({required this.period, required this.date})
      : super._();

  @override
  final DeadlinePeriod period;
  @override
  final DateTime date;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SuggestedDateInputImpl &&
            (identical(other.period, period) || other.period == period) &&
            (identical(other.date, date) || other.date == date));
  }

  @override
  int get hashCode => Object.hash(runtimeType, period, date);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SuggestedDateInputImplCopyWith<_$SuggestedDateInputImpl> get copyWith =>
      __$$SuggestedDateInputImplCopyWithImpl<_$SuggestedDateInputImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String text, DateInputError? error) inProgress,
    required TResult Function(DateTime date) parsed,
    required TResult Function(DeadlinePeriod period, DateTime date) suggested,
  }) {
    return suggested(period, date);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String text, DateInputError? error)? inProgress,
    TResult? Function(DateTime date)? parsed,
    TResult? Function(DeadlinePeriod period, DateTime date)? suggested,
  }) {
    return suggested?.call(period, date);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String text, DateInputError? error)? inProgress,
    TResult Function(DateTime date)? parsed,
    TResult Function(DeadlinePeriod period, DateTime date)? suggested,
    required TResult orElse(),
  }) {
    if (suggested != null) {
      return suggested(period, date);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InProgressDateInput value) inProgress,
    required TResult Function(_ParsedDateInput value) parsed,
    required TResult Function(_SuggestedDateInput value) suggested,
  }) {
    return suggested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InProgressDateInput value)? inProgress,
    TResult? Function(_ParsedDateInput value)? parsed,
    TResult? Function(_SuggestedDateInput value)? suggested,
  }) {
    return suggested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InProgressDateInput value)? inProgress,
    TResult Function(_ParsedDateInput value)? parsed,
    TResult Function(_SuggestedDateInput value)? suggested,
    required TResult orElse(),
  }) {
    if (suggested != null) {
      return suggested(this);
    }
    return orElse();
  }
}

abstract class _SuggestedDateInput extends DateInput {
  const factory _SuggestedDateInput(
      {required final DeadlinePeriod period,
      required final DateTime date}) = _$SuggestedDateInputImpl;
  const _SuggestedDateInput._() : super._();

  DeadlinePeriod get period;
  DateTime get date;
  @JsonKey(ignore: true)
  _$$SuggestedDateInputImplCopyWith<_$SuggestedDateInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
