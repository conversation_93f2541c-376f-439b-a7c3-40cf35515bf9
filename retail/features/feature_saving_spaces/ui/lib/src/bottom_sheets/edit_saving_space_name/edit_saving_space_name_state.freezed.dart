// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'edit_saving_space_name_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$EditSavingSpaceNameState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(SavingsAccount account, String newName) idle,
    required TResult Function(SavingsAccount account, String newName) updating,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(SavingsAccount account, String newName)? idle,
    TResult? Function(SavingsAccount account, String newName)? updating,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(SavingsAccount account, String newName)? idle,
    TResult Function(SavingsAccount account, String newName)? updating,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitialEditSavingSpaceNameState value) initial,
    required TResult Function(_IdleEditSavingSpaceNameState value) idle,
    required TResult Function(_UpdatingSavingSpaceNameState value) updating,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitialEditSavingSpaceNameState value)? initial,
    TResult? Function(_IdleEditSavingSpaceNameState value)? idle,
    TResult? Function(_UpdatingSavingSpaceNameState value)? updating,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitialEditSavingSpaceNameState value)? initial,
    TResult Function(_IdleEditSavingSpaceNameState value)? idle,
    TResult Function(_UpdatingSavingSpaceNameState value)? updating,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EditSavingSpaceNameStateCopyWith<$Res> {
  factory $EditSavingSpaceNameStateCopyWith(EditSavingSpaceNameState value,
          $Res Function(EditSavingSpaceNameState) then) =
      _$EditSavingSpaceNameStateCopyWithImpl<$Res, EditSavingSpaceNameState>;
}

/// @nodoc
class _$EditSavingSpaceNameStateCopyWithImpl<$Res,
        $Val extends EditSavingSpaceNameState>
    implements $EditSavingSpaceNameStateCopyWith<$Res> {
  _$EditSavingSpaceNameStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$InitialEditSavingSpaceNameStateImplCopyWith<$Res> {
  factory _$$InitialEditSavingSpaceNameStateImplCopyWith(
          _$InitialEditSavingSpaceNameStateImpl value,
          $Res Function(_$InitialEditSavingSpaceNameStateImpl) then) =
      __$$InitialEditSavingSpaceNameStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialEditSavingSpaceNameStateImplCopyWithImpl<$Res>
    extends _$EditSavingSpaceNameStateCopyWithImpl<$Res,
        _$InitialEditSavingSpaceNameStateImpl>
    implements _$$InitialEditSavingSpaceNameStateImplCopyWith<$Res> {
  __$$InitialEditSavingSpaceNameStateImplCopyWithImpl(
      _$InitialEditSavingSpaceNameStateImpl _value,
      $Res Function(_$InitialEditSavingSpaceNameStateImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$InitialEditSavingSpaceNameStateImpl
    extends _InitialEditSavingSpaceNameState {
  const _$InitialEditSavingSpaceNameStateImpl() : super._();

  @override
  String toString() {
    return 'EditSavingSpaceNameState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InitialEditSavingSpaceNameStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(SavingsAccount account, String newName) idle,
    required TResult Function(SavingsAccount account, String newName) updating,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(SavingsAccount account, String newName)? idle,
    TResult? Function(SavingsAccount account, String newName)? updating,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(SavingsAccount account, String newName)? idle,
    TResult Function(SavingsAccount account, String newName)? updating,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitialEditSavingSpaceNameState value) initial,
    required TResult Function(_IdleEditSavingSpaceNameState value) idle,
    required TResult Function(_UpdatingSavingSpaceNameState value) updating,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitialEditSavingSpaceNameState value)? initial,
    TResult? Function(_IdleEditSavingSpaceNameState value)? idle,
    TResult? Function(_UpdatingSavingSpaceNameState value)? updating,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitialEditSavingSpaceNameState value)? initial,
    TResult Function(_IdleEditSavingSpaceNameState value)? idle,
    TResult Function(_UpdatingSavingSpaceNameState value)? updating,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _InitialEditSavingSpaceNameState
    extends EditSavingSpaceNameState {
  const factory _InitialEditSavingSpaceNameState() =
      _$InitialEditSavingSpaceNameStateImpl;
  const _InitialEditSavingSpaceNameState._() : super._();
}

/// @nodoc
abstract class _$$IdleEditSavingSpaceNameStateImplCopyWith<$Res> {
  factory _$$IdleEditSavingSpaceNameStateImplCopyWith(
          _$IdleEditSavingSpaceNameStateImpl value,
          $Res Function(_$IdleEditSavingSpaceNameStateImpl) then) =
      __$$IdleEditSavingSpaceNameStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({SavingsAccount account, String newName});
}

/// @nodoc
class __$$IdleEditSavingSpaceNameStateImplCopyWithImpl<$Res>
    extends _$EditSavingSpaceNameStateCopyWithImpl<$Res,
        _$IdleEditSavingSpaceNameStateImpl>
    implements _$$IdleEditSavingSpaceNameStateImplCopyWith<$Res> {
  __$$IdleEditSavingSpaceNameStateImplCopyWithImpl(
      _$IdleEditSavingSpaceNameStateImpl _value,
      $Res Function(_$IdleEditSavingSpaceNameStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? account = null,
    Object? newName = null,
  }) {
    return _then(_$IdleEditSavingSpaceNameStateImpl(
      account: null == account
          ? _value.account
          : account // ignore: cast_nullable_to_non_nullable
              as SavingsAccount,
      newName: null == newName
          ? _value.newName
          : newName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$IdleEditSavingSpaceNameStateImpl extends _IdleEditSavingSpaceNameState {
  const _$IdleEditSavingSpaceNameStateImpl(
      {required this.account, this.newName = ''})
      : super._();

  @override
  final SavingsAccount account;
  @override
  @JsonKey()
  final String newName;

  @override
  String toString() {
    return 'EditSavingSpaceNameState.idle(account: $account, newName: $newName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdleEditSavingSpaceNameStateImpl &&
            (identical(other.account, account) || other.account == account) &&
            (identical(other.newName, newName) || other.newName == newName));
  }

  @override
  int get hashCode => Object.hash(runtimeType, account, newName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$IdleEditSavingSpaceNameStateImplCopyWith<
          _$IdleEditSavingSpaceNameStateImpl>
      get copyWith => __$$IdleEditSavingSpaceNameStateImplCopyWithImpl<
          _$IdleEditSavingSpaceNameStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(SavingsAccount account, String newName) idle,
    required TResult Function(SavingsAccount account, String newName) updating,
  }) {
    return idle(account, newName);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(SavingsAccount account, String newName)? idle,
    TResult? Function(SavingsAccount account, String newName)? updating,
  }) {
    return idle?.call(account, newName);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(SavingsAccount account, String newName)? idle,
    TResult Function(SavingsAccount account, String newName)? updating,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(account, newName);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitialEditSavingSpaceNameState value) initial,
    required TResult Function(_IdleEditSavingSpaceNameState value) idle,
    required TResult Function(_UpdatingSavingSpaceNameState value) updating,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitialEditSavingSpaceNameState value)? initial,
    TResult? Function(_IdleEditSavingSpaceNameState value)? idle,
    TResult? Function(_UpdatingSavingSpaceNameState value)? updating,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitialEditSavingSpaceNameState value)? initial,
    TResult Function(_IdleEditSavingSpaceNameState value)? idle,
    TResult Function(_UpdatingSavingSpaceNameState value)? updating,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class _IdleEditSavingSpaceNameState extends EditSavingSpaceNameState {
  const factory _IdleEditSavingSpaceNameState(
      {required final SavingsAccount account,
      final String newName}) = _$IdleEditSavingSpaceNameStateImpl;
  const _IdleEditSavingSpaceNameState._() : super._();

  SavingsAccount get account;
  String get newName;
  @JsonKey(ignore: true)
  _$$IdleEditSavingSpaceNameStateImplCopyWith<
          _$IdleEditSavingSpaceNameStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdatingSavingSpaceNameStateImplCopyWith<$Res> {
  factory _$$UpdatingSavingSpaceNameStateImplCopyWith(
          _$UpdatingSavingSpaceNameStateImpl value,
          $Res Function(_$UpdatingSavingSpaceNameStateImpl) then) =
      __$$UpdatingSavingSpaceNameStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({SavingsAccount account, String newName});
}

/// @nodoc
class __$$UpdatingSavingSpaceNameStateImplCopyWithImpl<$Res>
    extends _$EditSavingSpaceNameStateCopyWithImpl<$Res,
        _$UpdatingSavingSpaceNameStateImpl>
    implements _$$UpdatingSavingSpaceNameStateImplCopyWith<$Res> {
  __$$UpdatingSavingSpaceNameStateImplCopyWithImpl(
      _$UpdatingSavingSpaceNameStateImpl _value,
      $Res Function(_$UpdatingSavingSpaceNameStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? account = null,
    Object? newName = null,
  }) {
    return _then(_$UpdatingSavingSpaceNameStateImpl(
      account: null == account
          ? _value.account
          : account // ignore: cast_nullable_to_non_nullable
              as SavingsAccount,
      newName: null == newName
          ? _value.newName
          : newName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UpdatingSavingSpaceNameStateImpl extends _UpdatingSavingSpaceNameState {
  const _$UpdatingSavingSpaceNameStateImpl(
      {required this.account, required this.newName})
      : assert(newName.length > 0, 'New name must not be empty'),
        super._();

  @override
  final SavingsAccount account;
  @override
  final String newName;

  @override
  String toString() {
    return 'EditSavingSpaceNameState.updating(account: $account, newName: $newName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatingSavingSpaceNameStateImpl &&
            (identical(other.account, account) || other.account == account) &&
            (identical(other.newName, newName) || other.newName == newName));
  }

  @override
  int get hashCode => Object.hash(runtimeType, account, newName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatingSavingSpaceNameStateImplCopyWith<
          _$UpdatingSavingSpaceNameStateImpl>
      get copyWith => __$$UpdatingSavingSpaceNameStateImplCopyWithImpl<
          _$UpdatingSavingSpaceNameStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(SavingsAccount account, String newName) idle,
    required TResult Function(SavingsAccount account, String newName) updating,
  }) {
    return updating(account, newName);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(SavingsAccount account, String newName)? idle,
    TResult? Function(SavingsAccount account, String newName)? updating,
  }) {
    return updating?.call(account, newName);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(SavingsAccount account, String newName)? idle,
    TResult Function(SavingsAccount account, String newName)? updating,
    required TResult orElse(),
  }) {
    if (updating != null) {
      return updating(account, newName);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitialEditSavingSpaceNameState value) initial,
    required TResult Function(_IdleEditSavingSpaceNameState value) idle,
    required TResult Function(_UpdatingSavingSpaceNameState value) updating,
  }) {
    return updating(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitialEditSavingSpaceNameState value)? initial,
    TResult? Function(_IdleEditSavingSpaceNameState value)? idle,
    TResult? Function(_UpdatingSavingSpaceNameState value)? updating,
  }) {
    return updating?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitialEditSavingSpaceNameState value)? initial,
    TResult Function(_IdleEditSavingSpaceNameState value)? idle,
    TResult Function(_UpdatingSavingSpaceNameState value)? updating,
    required TResult orElse(),
  }) {
    if (updating != null) {
      return updating(this);
    }
    return orElse();
  }
}

abstract class _UpdatingSavingSpaceNameState extends EditSavingSpaceNameState {
  const factory _UpdatingSavingSpaceNameState(
      {required final SavingsAccount account,
      required final String newName}) = _$UpdatingSavingSpaceNameStateImpl;
  const _UpdatingSavingSpaceNameState._() : super._();

  SavingsAccount get account;
  String get newName;
  @JsonKey(ignore: true)
  _$$UpdatingSavingSpaceNameStateImplCopyWith<
          _$UpdatingSavingSpaceNameStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
