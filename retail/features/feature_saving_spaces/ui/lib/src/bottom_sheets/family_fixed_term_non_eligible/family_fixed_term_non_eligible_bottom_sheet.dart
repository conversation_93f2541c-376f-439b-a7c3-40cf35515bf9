import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_saving_spaces_ui/l10n/saving_spaces_localization.g.dart';
import 'package:wio_feature_saving_spaces_ui/src/common/views/bottom_sheet_title.dart';

class FamilyFixedTermNonEligibleBottomSheet extends StatelessWidget {
  const FamilyFixedTermNonEligibleBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = SavingSpacesLocalizations.of(context);

    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(
        Spacing.s5.value,
        Spacing.s5.value,
        Spacing.s5.value,
        Spacing.s6.value,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          BottomSheetTitle(
            title: l10n.familyTermDepositCownerSelectionNonEligibleHeader,
          ),
          Space.fromSpacingVertical(Spacing.s3),
          Label(
            model: LabelModel(
              text: l10n.familyTermDepositCownerSelectionNonEligibleBody,
              textStyle: CompanyTextStylePointer.b3,
              color: CompanyColorPointer.secondary4,
            ),
          ),
        ],
      ),
    );
  }
}
