// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'edit_saving_space_goal_analytics.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$EditSavingSpaceGoalAnalyticsPayload {
  Money get newGoal => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $EditSavingSpaceGoalAnalyticsPayloadCopyWith<
          EditSavingSpaceGoalAnalyticsPayload>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EditSavingSpaceGoalAnalyticsPayloadCopyWith<$Res> {
  factory $EditSavingSpaceGoalAnalyticsPayloadCopyWith(
          EditSavingSpaceGoalAnalyticsPayload value,
          $Res Function(EditSavingSpaceGoalAnalyticsPayload) then) =
      _$EditSavingSpaceGoalAnalyticsPayloadCopyWithImpl<$Res,
          EditSavingSpaceGoalAnalyticsPayload>;
  @useResult
  $Res call({Money newGoal});
}

/// @nodoc
class _$EditSavingSpaceGoalAnalyticsPayloadCopyWithImpl<$Res,
        $Val extends EditSavingSpaceGoalAnalyticsPayload>
    implements $EditSavingSpaceGoalAnalyticsPayloadCopyWith<$Res> {
  _$EditSavingSpaceGoalAnalyticsPayloadCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? newGoal = null,
  }) {
    return _then(_value.copyWith(
      newGoal: null == newGoal
          ? _value.newGoal
          : newGoal // ignore: cast_nullable_to_non_nullable
              as Money,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EditSavingSpaceGoalAnalyticsPayloadImplCopyWith<$Res>
    implements $EditSavingSpaceGoalAnalyticsPayloadCopyWith<$Res> {
  factory _$$EditSavingSpaceGoalAnalyticsPayloadImplCopyWith(
          _$EditSavingSpaceGoalAnalyticsPayloadImpl value,
          $Res Function(_$EditSavingSpaceGoalAnalyticsPayloadImpl) then) =
      __$$EditSavingSpaceGoalAnalyticsPayloadImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Money newGoal});
}

/// @nodoc
class __$$EditSavingSpaceGoalAnalyticsPayloadImplCopyWithImpl<$Res>
    extends _$EditSavingSpaceGoalAnalyticsPayloadCopyWithImpl<$Res,
        _$EditSavingSpaceGoalAnalyticsPayloadImpl>
    implements _$$EditSavingSpaceGoalAnalyticsPayloadImplCopyWith<$Res> {
  __$$EditSavingSpaceGoalAnalyticsPayloadImplCopyWithImpl(
      _$EditSavingSpaceGoalAnalyticsPayloadImpl _value,
      $Res Function(_$EditSavingSpaceGoalAnalyticsPayloadImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? newGoal = null,
  }) {
    return _then(_$EditSavingSpaceGoalAnalyticsPayloadImpl(
      newGoal: null == newGoal
          ? _value.newGoal
          : newGoal // ignore: cast_nullable_to_non_nullable
              as Money,
    ));
  }
}

/// @nodoc

class _$EditSavingSpaceGoalAnalyticsPayloadImpl
    extends _EditSavingSpaceGoalAnalyticsPayload {
  const _$EditSavingSpaceGoalAnalyticsPayloadImpl({required this.newGoal})
      : super._();

  @override
  final Money newGoal;

  @override
  String toString() {
    return 'EditSavingSpaceGoalAnalyticsPayload(newGoal: $newGoal)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EditSavingSpaceGoalAnalyticsPayloadImpl &&
            (identical(other.newGoal, newGoal) || other.newGoal == newGoal));
  }

  @override
  int get hashCode => Object.hash(runtimeType, newGoal);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$EditSavingSpaceGoalAnalyticsPayloadImplCopyWith<
          _$EditSavingSpaceGoalAnalyticsPayloadImpl>
      get copyWith => __$$EditSavingSpaceGoalAnalyticsPayloadImplCopyWithImpl<
          _$EditSavingSpaceGoalAnalyticsPayloadImpl>(this, _$identity);
}

abstract class _EditSavingSpaceGoalAnalyticsPayload
    extends EditSavingSpaceGoalAnalyticsPayload {
  const factory _EditSavingSpaceGoalAnalyticsPayload(
          {required final Money newGoal}) =
      _$EditSavingSpaceGoalAnalyticsPayloadImpl;
  const _EditSavingSpaceGoalAnalyticsPayload._() : super._();

  @override
  Money get newGoal;
  @override
  @JsonKey(ignore: true)
  _$$EditSavingSpaceGoalAnalyticsPayloadImplCopyWith<
          _$EditSavingSpaceGoalAnalyticsPayloadImpl>
      get copyWith => throw _privateConstructorUsedError;
}
