part of '../saving_space_details_page.dart';

class _Transactions extends StatelessWidget {
  const _Transactions();

  @override
  Widget build(BuildContext context) {
    final state = context.watch<SavingSpaceDetailsCubit>().state;

    return state.map(
      loading: (_) => const _LoadingTransactionsSection(),
      idleSpace: (it) => _TransactionsSection(accountId: it.savingSpace.id),
      idleDeposit: (it) => _TransactionsSection(accountId: it.account.id),
      etihadDeposit: (it) => _TransactionsSection(accountId: it.account.id),
      fixedFamilyDeposit: (it) =>
          _TransactionsSection(accountId: it.account.id),
      initial: (_) => const _LoadingTransactionsSection(),
      failed: (_) => const SliverToBoxAdapter(),
    );
  }
}

class _TransactionsSection extends StatelessWidget {
  final String accountId;

  const _TransactionsSection({required this.accountId});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<SavingSpaceDetailsCubit>();

    return SliverMainAxisGroup(
      slivers: [
        const SliverToBoxAdapter(child: _TransactionsSectionTitle()),
        _SavingSpaceTransactions(
          actionStream: cubit.transactionActionStream,
          savingSpaceId: accountId,
        ),
      ],
    );
  }
}

class _LoadingTransactionsSection extends StatelessWidget {
  const _LoadingTransactionsSection();

  @override
  Widget build(BuildContext context) => const SliverMainAxisGroup(
        slivers: [
          SliverToBoxAdapter(
            child: CompanyShimmer(
              model: CompanyShimmerModel(),
              child: _TransactionsSectionTitle(),
            ),
          ),
          TransactionsShimmer.sliver(),
        ],
      );
}

class _TransactionsSectionTitle extends StatelessWidget {
  const _TransactionsSectionTitle();

  @override
  Widget build(BuildContext context) {
    final l10n = SavingSpacesLocalizations.of(context);

    return Padding(
      padding: EdgeInsetsDirectional.only(
        start: pagePadding,
        end: pagePadding,
        top: Spacing.s6.value,
      ),
      child: _SectionTitle(
        title: l10n.savingSpaceDetailsPageActivitySectionHeaderTitle,
      ),
    );
  }
}
