import 'dart:async';

import 'package:logging_api/logging.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ui/ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_pricing_plan_api/index.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';
import 'package:wio_feature_saving_spaces_ui/src/common/extensions.dart';
import 'package:wio_feature_saving_spaces_ui/src/navigation/configs/etihad_miles/agree_etihad_tnc_bottom_sheet_config.dart';
import 'package:wio_feature_saving_spaces_ui/src/navigation/configs/etihad_miles/etihad_early_closure_fees_bottom_sheet_config.dart';
import 'package:wio_feature_saving_spaces_ui/src/navigation/configs/etihad_miles/etihad_terms_and_condition_navigation_config.dart';
import 'package:wio_feature_saving_spaces_ui/src/screens/create_etihad_term_deposit/intro/etihad_term_deposit_intro_state.dart';

class EtihadTermDepositIntroCubit
    extends BaseCubit<EtihadTermDepositIntroState> {
  final EtihadTermDepositInteractor _interactor;
  final PricingPlanInteractor _pricingPlanInteractor;
  final NavigationProvider _navigationProvider;
  final Logger _logger;

  EtihadTermDepositIntroCubit({
    required EtihadTermDepositInteractor interactor,
    required PricingPlanInteractor pricingPlanInteractor,
    required NavigationProvider navigationProvider,
    required Logger logger,
  })  : _interactor = interactor,
        _pricingPlanInteractor = pricingPlanInteractor,
        _navigationProvider = navigationProvider,
        _logger = logger,
        super(const EtihadTermDepositIntroState.initial());

  void initialize() => _init();

  void _init() {
    state.maybeMap(
      initial: (_) => _getInitialData(),
      failed: (_) => _getInitialData(),
      orElse: () => _logger.error(
        'Cubit is initialized from wrong state: ${state.runtimeType}',
      ),
    );
  }

  void onRetry() {
    state.mapOrNull(
      failed: (it) => _init(),
    );
  }

  void onShowEarlyClosureFees() {
    state.mapOrNull(
      idle: (it) {
        final earlyClosureFees = it.earlyClosureFees;

        if (earlyClosureFees == null) return;

        _navigationProvider.showBottomSheet(
          EtihadEarlyClosureFeesBottomSheetConfig(
            earlyClosureFees: earlyClosureFees,
          ),
        );
      },
    );
  }

  void showAgreeEtihadTncBottomSheet() {
    state.mapOrNull(
      idle: (it) async {
        final result =
            await _navigationProvider.showBottomSheet<AgreeEtihadTncResult>(
          AgreeEtihadTncBottomSheetConfig(
            params: AgreeEtihadTncParams(
              onClickTermsAndConditions: _onClickTermsAndConditions,
              onClickCostOfMiles: onShowEarlyClosureFees,
            ),
          ),
        );

        if (result == AgreeEtihadTncResult.agree) {
          safeEmit(it.copyWith(agreedEtihadTnc: true));
        }
      },
    );
  }

  // Internal
  void _onClickTermsAndConditions() => _navigationProvider.push(
        const EtihadTermsAndConditionNavigationConfig(),
      );

  Future<void> _getInitialData() {
    return Rx.forkJoin2(
      _getSubscriptionPlanInfo(),
      _getEtihadEarlyClosureFees(),
      (subscriptionPlanInfo, earlyClosureFees) => (
        subscriptionPlanInfo: subscriptionPlanInfo,
        earlyClosureFees: earlyClosureFees
      ),
    )
        .doOnListen(() => emit(const EtihadTermDepositIntroState.loading()))
        .logError(_logger)
        .withSafeEmit(this)
        .withError<Object>(
          (_) => safeEmit(const EtihadTermDepositIntroState.failed()),
        )
        .doOnData(
          (result) => emit(
            EtihadTermDepositIntroState.idle(
              etihadDepositTerms:
                  result.subscriptionPlanInfo?.etihadTermDeposits,
              earlyClosureFees: result.earlyClosureFees,
            ),
          ),
        )
        .whereNotNull()
        .drain<void>();
  }

  Stream<SubscriptionPlanInfo?> _getSubscriptionPlanInfo() async* {
    try {
      final subscriptionPlanInfo =
          await _pricingPlanInteractor.getSubscriptionPlanInfo();

      yield subscriptionPlanInfo;
    } on Exception catch (_) {
      yield null;
    }
  }

  Stream<EtihadEarlyClosureFees?> _getEtihadEarlyClosureFees() async* {
    try {
      final earlyClosureFees = await _interactor.getEtihadEarlyClosureFees();

      yield earlyClosureFees;
    } on Exception catch (_) {
      yield null;
    }
  }

  @override
  String toString() => 'EtihadTermDepositIntroCubit{}';
}
