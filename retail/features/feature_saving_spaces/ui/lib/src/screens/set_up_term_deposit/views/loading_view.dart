part of '../set_up_term_deposit_page.dart';

class _LoadingView extends StatelessWidget {
  const _LoadingView({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = SavingSpacesLocalizations.of(context);

    // TODO(ssuleymanli): Update shimmer for creation from saving space

    return CompanyShimmer(
      model: const CompanyShimmerModel(),
      child: Padded(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CompanyLabel(const CompanyLabelModel(
              text: 'AED',
              textStyle: CompanyTextStylePointer.h2,
            )),
            Space.fromSpacingVertical(Spacing.s1),
            CompanyLabel(const CompanyLabelModel(
              text: 'Balance: 250,500.00 AED',
              textStyle: CompanyTextStylePointer.b4,
            )),
            Space.fromSpacingVertical(Spacing.s4),
            Space.fromSpacingVertical(Spacing.s3),
            <PERSON><PERSON><PERSON><PERSON>(
              height: 54,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                itemBuilder: (context, index) => BaseChip(
                  BaseChipModel(
                    label: l10n.createTermDepositTenorLabel(6),
                    description: l10n.createTermDepositInterestRateLabel('4.5'),
                  ),
                ),
                separatorBuilder: (_, __) =>
                    Space.fromSpacingHorizontal(Spacing.s2),
                itemCount: 3,
              ),
            ),
            Space.fromSpacingVertical(Spacing.s4),
            const _ItemCardShimmer(),
            Space.fromSpacingVertical(Spacing.s3),
            Space.fromSpacingVertical(Spacing.s5),
            CompanyLabel(
              CompanyLabelModel(
                text: l10n.createTermDepositInfoSectionTitle,
                textStyle: CompanyTextStylePointer.h4,
              ),
            ),
            Space.fromSpacingVertical(Spacing.s4),
            ListDetailsContainer(
              model: ListDetailsContainerModel(items: [
                _listDetailsModel,
                _listDetailsModel,
                _listDetailsModel,
              ]),
            ),
          ],
        ),
      ),
    );
  }

  ListDetailsModel get _listDetailsModel => const ListDetailsModel(
        textLabelModel: ListDetailsTextLabelModel(
          text:
              'You’ll get maximum interest in the end of the period you choose',
          tile: TileModel.iconV2(
            icon: CompanyIconModel(
              icon: GraphicAssetPointer.icon(
                CompanyIconPointer.functional_goal,
              ),
              size: CompanyIconSize.medium,
            ),
            shape: TileBoxShape.rectangle,
            surface: TileSurface.dark,
          ),
        ),
      );
}

class _ItemCardShimmer extends StatelessWidget {
  const _ItemCardShimmer();

  @override
  Widget build(BuildContext context) {
    final decoration = BoxDecoration(
      borderRadius: const BorderRadius.all(Radius.circular(14)),
      color: context.colorStyling.surface2,
    );

    // All of this is to make a loading layout without jumps
    final topTextStyle = context.textStyling.h3;
    final topTextSize = topTextStyle.fontSize ?? 24;
    final topTextSpace = topTextStyle.lineHeight - topTextSize;
    final bottomTextStyle = context.textStyling.b3;
    final bottomTextSize = bottomTextStyle.fontSize ?? 14;
    final bottomTextSpace = bottomTextStyle.lineHeight - bottomTextSize;

    return Container(
      decoration: decoration,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CompanyShimmer(
            model: const CompanyShimmerModel(),
            child: FractionallySizedBox(
              widthFactor: 0.35,
              child: Container(decoration: decoration, height: topTextSize),
            ),
          ),
          Space.vertical(topTextSpace + bottomTextSpace + 4), // 4 is from figma
          CompanyShimmer(
            model: const CompanyShimmerModel(),
            child: FractionallySizedBox(
              widthFactor: 0.9,
              child: Container(decoration: decoration, height: bottomTextSize),
            ),
          ),
        ],
      ),
    );
  }
}

extension on TextStyle {
  double get lineHeight => (fontSize ?? 1) * (height ?? 1);
}
