// ignore_for_file: constant_identifier_names
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/index.dart';

part 'name_selection_analytics.freezed.dart';

enum NameSelectionAnalyticsTarget {
  suggested_name,
}

@freezed
class NameSelectionAnalyticsPayload
    with _$NameSelectionAnalyticsPayload
    implements AnalyticsEventPayload {
  const NameSelectionAnalyticsPayload._();

  const factory NameSelectionAnalyticsPayload.suggested({
    required String name,
  }) = _SuggestedNameSelectedAnalyticsPayload;

  @override
  Map<String, dynamic> getEventPayload() =>
      map(suggested: (it) => <String, String>{'name': it.name});
}

class NameSelectionAnalytics {
  static const _screenName = 'saving_space_name_selection_page';

  final AnalyticsEventTracker _analytics;

  NameSelectionAnalytics({
    required AnalyticsAbstractTrackerFactory analyticsFactory,
  }) : _analytics = analyticsFactory.get(
          screenName: _screenName,
          tracker: AnalyticsTracker.mixpanel,
        );

  void suggestionSelected(String name) {
    _analytics.select(
      targetType: AnalyticsTargetType.list,
      target: NameSelectionAnalyticsTarget.suggested_name,
      payload: NameSelectionAnalyticsPayload.suggested(name: name),
    );
  }
}
