import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';
import 'package:wio_feature_saving_spaces_ui/src/navigation/configs/etihad_miles/set_up_etihad_guest_account_bottom_sheet_config.dart';
import 'package:wio_feature_saving_spaces_ui/src/screens/create_etihad_term_deposit/etihad_account_selection_option/cubit/etihad_account_selection_option_cubit.dart';
import 'package:wio_feature_saving_spaces_ui/src/screens/create_etihad_term_deposit/etihad_account_selection_option/cubit/etihad_account_selection_state.dart';

import '../mocks.dart';

void main() {
  final interactor = MockEtihadTermDepositInteractor();
  final navigator = MockNavigationProvider();
  final logger = MockLogger();

  var haveAnAccountSelected = false;

  final config = EtihadConfigDetails(
    etihadRegistrationUrl: 'etihadRegistrationUrl',
    userNameLength: 10,
    loyaltyNumberLength: 20,
    etihadGuestLoginUrl: 'etihadGuestLoginUrl',
    tncEnglish: 'tncEnglish',
    tncArabic: 'tncArabic',
  );

  EtihadAccountSelectionOptionCubit getCubit() {
    return EtihadAccountSelectionOptionCubit(
      interactor: interactor,
      logger: logger,
      navigationProvider: navigator,
      callBack: () => haveAnAccountSelected = true,
    );
  }

  group('Initialization', () {
    blocTest<EtihadAccountSelectionOptionCubit, EtihadAccountSelectionState>(
      'switches to loading, fetching etihad config info',
      // Arrange
      build: getCubit,
      setUp: () =>
          when(() => interactor.getEtihadTermDepositConfig()).justAnswerAsync(
        config,
      ),
      seed: () => const EtihadAccountSelectionState.initial(),
      // Act
      act: (cubit) => cubit.init(),
      // Assert
      expect: () => [
        const EtihadAccountSelectionState.loadingConfig(),
        EtihadAccountSelectionState.idle(configDetails: config),
      ],
      verify: (_) {
        verify(
          () => interactor.getEtihadTermDepositConfig(),
        ).calledOnce;
      },
    );

    blocTest<EtihadAccountSelectionOptionCubit, EtihadAccountSelectionState>(
      'switches to loading, fetching etihad config info failure',
      // Arrange
      build: getCubit,
      setUp: () =>
          when(() => interactor.getEtihadTermDepositConfig()).justThrowAsync(
        Exception(),
      ),
      seed: () => const EtihadAccountSelectionState.initial(),
      // Act
      act: (cubit) => cubit.init(),
      // Assert
      expect: () => [
        const EtihadAccountSelectionState.loadingConfig(),
        const EtihadAccountSelectionState.idle(),
      ],
      verify: (_) {
        verify(
          () => interactor.getEtihadTermDepositConfig(),
        ).calledOnce;
      },
    );
  });

  group('Verify user selection', () {
    blocTest<EtihadAccountSelectionOptionCubit, EtihadAccountSelectionState>(
      'Verify user first name input - has account',
      // Arrange
      setUp: () => haveAnAccountSelected = false,
      build: getCubit,
      seed: () => EtihadAccountSelectionState.idle(configDetails: config),
      // Act
      act: (cubit) => cubit.onSelectionOption(
        EtihadAccountSelectionOption.hasAccount,
      ),
      // Assert
      verify: (_) {
        expect(haveAnAccountSelected, true);
      },
    );

    blocTest<EtihadAccountSelectionOptionCubit, EtihadAccountSelectionState>(
      'Verify user first name input - set up guest account - success',
      // Arrange
      build: getCubit,
      seed: () => EtihadAccountSelectionState.idle(configDetails: config),
      setUp: () {
        haveAnAccountSelected = false;
        when(
          () => navigator.showBottomSheet<SetUpEtihadGuestAccountResult>(
            const SetUpEtihadGuestAccountBottomSheetConfig(
              webUrl: 'etihadRegistrationUrl',
            ),
          ),
        ).justAnswerAsync(const SetUpEtihadGuestAccountResult.proceed());
      },
      // Act
      act: (cubit) => cubit.onSelectionOption(
        EtihadAccountSelectionOption.setUpAccount,
      ),
      // Assert
      verify: (_) {
        verify(
          () => navigator.showBottomSheet<SetUpEtihadGuestAccountResult>(
            const SetUpEtihadGuestAccountBottomSheetConfig(
              webUrl: 'etihadRegistrationUrl',
            ),
          ),
        ).calledOnce;

        expect(haveAnAccountSelected, true);
      },
    );

    blocTest<EtihadAccountSelectionOptionCubit, EtihadAccountSelectionState>(
      'Verify user first name input - set up guest account - failure',
      // Arrange
      build: getCubit,
      seed: () => EtihadAccountSelectionState.idle(configDetails: config),
      setUp: () {
        haveAnAccountSelected = false;
        when(
          () => navigator.showBottomSheet<SetUpEtihadGuestAccountResult>(
            const SetUpEtihadGuestAccountBottomSheetConfig(
              webUrl: 'etihadRegistrationUrl',
            ),
          ),
        ).justAnswerAsync(const SetUpEtihadGuestAccountResult.deny());
      },
      // Act
      act: (cubit) => cubit.onSelectionOption(
        EtihadAccountSelectionOption.setUpAccount,
      ),
      // Assert
      verify: (_) {
        verify(
          () => navigator.showBottomSheet<SetUpEtihadGuestAccountResult>(
            const SetUpEtihadGuestAccountBottomSheetConfig(
              webUrl: 'etihadRegistrationUrl',
            ),
          ),
        ).calledOnce;

        expect(haveAnAccountSelected, false);
      },
    );
  });
}
