import 'package:common_feature_fx_api/feature_fx_api.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_account_data_api/account_data_api.dart';
import 'package:wio_feature_pricing_plan_api/index.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';

abstract class TestEntities {
  static SavingSpace randSavingSpace({Currency? currency, double? balance}) =>
      SavingSpace(
        id: 'id',
        name: 'name',
        balance: Money.fromNumWithCurrency(
          balance ?? randomDouble(),
          currency ?? Currency.aed,
        ),
        image: const SavingSpaceImage.local(name: 'name', path: 'path'),
      );

  static Quote randQuote({
    required Currency buyCurrency,
    required Currency sellCurrency,
    required Currency dealtCurrency,
    required double dealtAmount,
    required Money contraAmount,
  }) =>
      Quote(
        transactionKey: 'transactionKey',
        timestamp: DateTime(2024),
        echo: 'echo',
        reasonCode: 42,
        reasonDescription: 'reasonDescription',
        requester: 'requester',
        buyCurrency: buyCurrency,
        sellCurrency: sellCurrency,
        dealtCurrency: dealtCurrency,
        dealtAmount: dealtAmount,
        valueDate: DateTime(2024),
        clientRate: randomDouble(),
        contraAmount: contraAmount,
        timeToLive: randomInt(),
        transactionRef: '',
        gidid: '',
      );

  static SubscriptionPlanInfo randPlan({
    String? id,
    String? name,
    Map<Currency, InterestRateTier> rates = const {},
  }) =>
      SubscriptionPlanInfo(
        tariffId: id ?? randomString(),
        name: name ?? randomString(),
        description: randomString(),
        startDate: randomDate(),
        nextBillingDate: randomDate(),
        interestRates: rates.map((key, value) => MapEntry(key, [value])),
      );

  static AccountDetailsResponseRetail randAccountDto({
    String? id,
    AccountDetailsResponseRetailType type =
        AccountDetailsResponseRetailType.currentAccount,
    double? balance,
  }) =>
      AccountDetailsResponseRetail(
        id: id ?? randomString(),
        name: randomString(),
        accountNickName: randomString(),
        beneficiaryName: randomString(),
        type: type,
        state: AccountDetailsResponseRetailState.active,
        encodedKey: randomString(),
        currencyCode: AccountDetailsResponseRetailCurrencyCode.aed,
        creationDate: DateTime.now(),
        bankName: randomString(),
        bankAddress: randomString(),
        availableBalance: balance,
      );

  static FixedTermDepositTieredTenorDetails
      createTermDepositTieredTenorDetailsDto({
    required int tenor,
    double? amount,
    bool? active,
    bool? display,
    double? tierTo,
    double regularInterestRate = 4.5,
    double correctedInterestRate = 4.5,
    double endOfDepositInterest = 10.0,
    double? activeTotalAmount,
    double? remainingTierLimit,
    double? nextTierRegularInterestRate,
  }) {
    return FixedTermDepositTieredTenorDetails(
      tenor: tenor,
      amount: amount,
      active: active,
      display: display,
      tierTo: tierTo,
      regularInterestRate: regularInterestRate,
      correctedInterestRate: correctedInterestRate,
      endOfDepositInterest: endOfDepositInterest,
      maturityDate: DateTime(2025, 12, 31),
      activeTotalAmount: activeTotalAmount,
      remainingTierLimit: remainingTierLimit,
      nextTierRegularInterestRate: nextTierRegularInterestRate,
    );
  }
}
