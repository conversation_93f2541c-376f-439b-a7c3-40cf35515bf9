import 'package:feature_auth_api/domain/models/tax_residence_country.dart';
import 'package:feature_auth_api/domain/tax_residence_countries_interactor.dart';
import 'package:feature_countries_api/feature_countries_api.dart';

class TaxResidenceCountriesInteractorImpl
    implements TaxResidenceCountriesInteractor {
  final CountryRepository countryRepository;
  final CountryLocalizationRepository countryLocalizationRepository;

  const TaxResidenceCountriesInteractorImpl({
    required this.countryRepository,
    required this.countryLocalizationRepository,
  });

  @override
  Future<List<TaxResidenceCountry>> loadTaxResidenceCountries() async {
    final countries = await countryRepository.getCountries();
    final taxResidenceCountries = await Future.wait(
      countries.map(
        (c) async => TaxResidenceCountry(
          code: c.code,
          code3: c.code3,
          name: await countryLocalizationRepository
                  .getCountryNameLocalized(c.code) ??
              c.code,
          flag: c.flag,
          isTinMandatory: c.isTinMandatory,
        ),
      ),
    );

    return taxResidenceCountries;
  }

  @override
  Future<List<String>> getTinDescriptionForCountry(String code) async {
    return countryLocalizationRepository.getCountryTinDetailsLocalized(code);
  }
}
