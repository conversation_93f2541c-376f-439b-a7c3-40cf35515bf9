import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:wio_app_core_api/index.dart';

class CustomerSubscriptionRepositoryImpl
    implements CustomerSubscriptionRepository {
  static const _customerPlan = '_customerPlan';

  final KeyValueStorage _customerPlanStorage;

  const CustomerSubscriptionRepositoryImpl({
    required KeyValueStorage customerPlanStorage,
  }) : _customerPlanStorage = customerPlanStorage;

  @override
  Future<void> clear() async {
    await _customerPlanStorage.delete(
      _customerPlan,
    );
  }

  @override
  Future<String?> readCustomerSubscriptionPlan() {
    return _customerPlanStorage.getByKey<String>(_customerPlan);
  }

  @override
  Future<void> saveCustomerSubscriptionPlan(String planName) {
    return _customerPlanStorage.put(_customerPlan, planName);
  }
}
