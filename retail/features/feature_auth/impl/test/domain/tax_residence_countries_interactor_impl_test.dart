import 'package:feature_auth_api/domain/models/tax_residence_country.dart';
import 'package:feature_auth_impl/src/domain/tax_residence_countries_interactor_impl.dart';
import 'package:feature_countries_api/feature_countries_api.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';

import '../feature_auth_impl_mocks.dart';

void main() {
  late MockCountryRepository countryRepositoryMock;
  late MockCountryLocalizationRepository countryLocalizationRepositoryMock;
  late TaxResidenceCountriesInteractorImpl interactor;

  setUp(() {
    countryRepositoryMock = MockCountryRepository();
    countryLocalizationRepositoryMock = MockCountryLocalizationRepository();
    interactor = TaxResidenceCountriesInteractorImpl(
      countryRepository: countryRepositoryMock,
      countryLocalizationRepository: countryLocalizationRepositoryMock,
    );
  });

  test('Should return a list of countries with localized names', () async {
    // Arrange
    when(() => countryRepositoryMock.getCountries()).thenAnswer(
      (_) => Future.value([
        CountryModel(code: 'AD', code3: 'AND', flag: FlagPointer.AD),
        CountryModel(
          code: 'AE',
          code3: 'ARE',
          flag: FlagPointer.AE,
          isTinMandatory: true,
        ),
        CountryModel(code: 'AF', code3: 'AFG', flag: FlagPointer.AF),
      ]),
    );
    when(() => countryLocalizationRepositoryMock.getCountryNameLocalized('AD'))
        .thenAnswer((_) => Future.value('Andorra'));
    when(() => countryLocalizationRepositoryMock.getCountryNameLocalized('AE'))
        .thenAnswer((_) => Future.value('United Arab Emirates'));
    when(() => countryLocalizationRepositoryMock.getCountryNameLocalized('AF'))
        .thenAnswer((_) => Future.value('Afghanistan'));

    // Act
    final result = await interactor.loadTaxResidenceCountries();

    // Assert
    verify(() => countryRepositoryMock.getCountries());
    verify(
      () => countryLocalizationRepositoryMock.getCountryNameLocalized('AD'),
    );
    verify(
      () => countryLocalizationRepositoryMock.getCountryNameLocalized('AE'),
    );
    verify(
      () => countryLocalizationRepositoryMock.getCountryNameLocalized('AF'),
    );
    expect(
      result,
      equals([
        TaxResidenceCountry(
          name: 'Andorra',
          code: 'AD',
          code3: 'AND',
          flag: FlagPointer.AD,
          isTinMandatory: false,
        ),
        TaxResidenceCountry(
          name: 'United Arab Emirates',
          code: 'AE',
          code3: 'ARE',
          flag: FlagPointer.AE,
          isTinMandatory: true,
        ),
        TaxResidenceCountry(
          name: 'Afghanistan',
          code: 'AF',
          code3: 'AFG',
          flag: FlagPointer.AF,
          isTinMandatory: false,
        ),
      ]),
    );
  });

  test(
    'Should return country code instead of name '
    'if name not found in localization',
    () async {
      // Arrange
      when(() => countryRepositoryMock.getCountries()).thenAnswer(
        (_) => Future.value([
          CountryModel(code: 'AD', code3: 'AND', flag: FlagPointer.AD),
        ]),
      );
      when(
        () => countryLocalizationRepositoryMock.getCountryNameLocalized(any()),
      ).thenAnswer((_) => Future.value());

      // Act
      final result = await interactor.loadTaxResidenceCountries();

      // Assert
      expect(
        result,
        equals([
          TaxResidenceCountry(
            name: 'AD',
            code: 'AD',
            code3: 'AND',
            flag: FlagPointer.AD,
            isTinMandatory: false,
          ),
        ]),
      );
    },
  );

  test('Should return a TIN description for a country', () async {
    // Arrange
    when(
      () => countryLocalizationRepositoryMock.getCountryTinDetailsLocalized(
        any(),
      ),
    ).thenAnswer((_) => Future.value(['Some description']));

    // Act
    final result = await interactor.getTinDescriptionForCountry('AD');

    // Assert
    verify(
      () =>
          countryLocalizationRepositoryMock.getCountryTinDetailsLocalized('AD'),
    );
    expect(result, equals(['Some description']));
  });
}
