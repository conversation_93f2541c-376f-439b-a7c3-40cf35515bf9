name: wio_feature_auth_baas_ui_desktop
description: Retail BAAS package project.
version: 0.0.1
publish_to: none
environment: 
  flutter: 3.27.3
  sdk: '>=3.6.0 <4.0.0'
dependencies: 
  common_feature_toggle_api: 
    path: ../../../../common/tools/feature_toggle/api
  di: 
    path: ../../../../core/di
  domain: 
    path: ../../../../core/domain
  feature_auth_api: 
    path: ../../feature_auth/api
  feature_auth_impl: 
    path: ../../feature_auth/impl
  flutter: 
    sdk: flutter
  flutter_bloc: 9.0.0
  freezed_annotation: 2.4.4
  logging_api: 
    path: ../../../../core/logging/api
  retail_utils: 
    path: ../../../core/utils
  rxdart: 0.28.0
  ui: 
    path: ../../../../core/ui
  ui_kit_legacy_core: 
    path: ../../../../ui_kit_legacy/ui_kit_core
  ui_kit_legacy_retail_desktop: 
    path: ../../../../ui_kit_legacy/ui_kit_retail_desktop
  wio_app_core_api: 
    path: ../../../../core/app_core/api
  wio_common_feature_consent_authorization_api: 
    path: ../../../../common/features/feature_consent_authorization/api
  wio_core_navigation_api: 
    path: ../../../../core/navigation/api
  wio_core_navigation_ui: 
    path: ../../../../core/navigation/ui
  wio_feature_core_ui_desktop: 
    path: ../../../core/ui_desktop
  wio_feature_payments_v2_api: 
    path: ../../feature_payments_v2/api
  wio_retail_analytics_api: 
    path: ../../../core/analytics/api
dev_dependencies: 
  bloc_test: 10.0.0
  build_runner: 2.4.14
  core_lints: 
    path: ../../../../tooling/core_lints
  fake_async: 1.3.1
  flutter_lints: 4.0.0
  flutter_test: 
    sdk: flutter
  freezed: 2.5.7
  mocktail: 1.0.4
  tests: 
    path: ../../../../core/tests/impl
  tests_ui: 
    path: ../../../../core/tests/ui
flutter: 
  assets: 
    - assets/
  uses-material-design: true
