//ignore_for_file: constant_identifier_names

import 'package:feature_auth_ui/src/screens/flows/signup_flow/cubit/signup_flow_cubit.dart';
import 'package:flutter/foundation.dart';
import 'package:wio_app_core_api/index.dart';

enum EmailInputAnalyticsTarget {
  email,
  suggestion_email,
  userSuccessfullyCreated,
  onboardingEmailAlreadyExist,
  requiredFieldsNotFound,
}

@immutable
class EmailInputAnalytics {
  final AnalyticsEventTracker _analytics;

  EmailInputAnalytics({
    required AnalyticsAbstractTrackerFactory analyticsAbstractTrackerFactory,
  }) : _analytics = analyticsAbstractTrackerFactory.get(
          screenName: SignUpFlowStepScreensIds.onboard_enter_email_address.name,
          tracker: AnalyticsTracker.mixpanel,
        );

  void inputEmailInputFieldFinish() {
    _analytics.input(
      target: EmailInputAnalyticsTarget.email,
      targetType: AnalyticsTargetType.input_field,
    );
  }

  void inputEmailInputFieldSuccess() {
    _analytics.input(
      target: EmailInputAnalyticsTarget.email,
      targetType: AnalyticsTargetType.input_field,
      status: AnalyticsStatus.success,
    );
  }

  void clickEmailSuggestion() {
    _analytics.click(
      target: EmailInputAnalyticsTarget.suggestion_email,
      targetType: AnalyticsTargetType.list,
    );
  }

  void inputEmailInputFieldError() {
    _analytics.input(
      target: EmailInputAnalyticsTarget.email,
      targetType: AnalyticsTargetType.input_field,
      status: AnalyticsStatus.error,
    );
  }

  void viewEmailSuggestion() {
    _analytics.view(
      target: EmailInputAnalyticsTarget.suggestion_email,
      targetType: AnalyticsTargetType.button,
    );
  }

  void swipeEmailSuggestion() {
    _analytics.swipe(
      target: EmailInputAnalyticsTarget.suggestion_email,
      targetType: AnalyticsTargetType.list,
    );
  }

  void userSuccessfullyCreated() {
    _analytics.track(
      AnalyticsEvent.simple(
        EmailInputAnalyticsTarget.userSuccessfullyCreated.name,
      ),
    );
  }

  void onboardingEmailAlreadyExist() {
    _analytics.track(
      AnalyticsEvent.simple(
        EmailInputAnalyticsTarget.onboardingEmailAlreadyExist.name,
      ),
    );
  }

  void requiredFieldsNotFound() {
    _analytics.track(
      AnalyticsEvent.simple(
        EmailInputAnalyticsTarget.requiredFieldsNotFound.name,
      ),
    );
  }

  void clickNextButton() => _analytics.clickNext();

  void clickBackButton() => _analytics.clickBack();
}
