import 'package:feature_auth_ui/src/screens/flows/signup_flow/cubit/signup_flow_cubit.dart';
import 'package:feature_auth_ui/src/screens/info_and_terms/analytics/info_and_terms_analytics.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';

void main() {
  late MockAnalyticsAbstractTrackerFactory trackerFactory;
  late MockAnalyticsEventTracker tracker;
  late InfoAndTermsAnalytics analytics;

  final screenName =
      SignUpFlowStepScreensIds.onboard_view_terms_and_conditions.name;

  setUp(() {
    final analyticsMocks = getAnalyticsTracker();
    trackerFactory = analyticsMocks.analyticsAbstractTrackerFactory;
    tracker = analyticsMocks.tracker;

    analytics = InfoAndTermsAnalytics(
      analyticsAbstractTrackerFactory: trackerFactory,
    );
  });

  test(
    'Should have correct payload for view key facts statement event',
    () async {
      // Act
      analytics.clickFullViewKeyFactsStatementButton();

      // Assert
      verify(
        () => tracker.track(
          AnalyticsEvent(
            screenName: screenName,
            action: AnalyticsAction.click,
            target: InfoAndTermsAnalyticsTarget.key_facts_statement.name,
            targetType: AnalyticsTargetType.link,
          ),
        ),
      );
    },
  );

  test(
    'Should have correct payload for click view privacy button event',
    () async {
      // Act
      analytics.clickFullViewPrivacyPolicyButton();

      // Assert
      verify(
        () => tracker.track(
          AnalyticsEvent(
            screenName: screenName,
            action: AnalyticsAction.click,
            target: InfoAndTermsAnalyticsTarget.privacy.name,
            targetType: AnalyticsTargetType.link,
          ),
        ),
      );
    },
  );

  test(
    'Should have correct payload for click view standard terms button event',
    () async {
      // Act
      analytics.clickFullViewStandardTermsButton();

      // Assert
      verify(
        () => tracker.track(
          AnalyticsEvent(
            screenName: screenName,
            action: AnalyticsAction.click,
            target: InfoAndTermsAnalyticsTarget.standard_terms.name,
            targetType: AnalyticsTargetType.link,
          ),
        ),
      );
    },
  );

  test('Should have correct payload for next button click', () async {
    // Act
    analytics.clickNextButton();

    // Assert
    verify(
      () => tracker.track(
        AnalyticsEvent(
          screenName: screenName,
          action: AnalyticsAction.click,
          target: 'next',
          targetType: AnalyticsTargetType.button,
        ),
      ),
    );
  });

  test('Should have correct payload for back button click', () async {
    // Act
    analytics.clickBackButton();

    // Assert
    verify(
      () => tracker.track(
        AnalyticsEvent(
          screenName: screenName,
          action: AnalyticsAction.click,
          target: 'back',
          targetType: AnalyticsTargetType.button,
        ),
      ),
    );
  });
}
