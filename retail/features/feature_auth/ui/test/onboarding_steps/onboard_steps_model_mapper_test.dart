import 'package:feature_auth_ui/feature_auth_ui.dart';
import 'package:feature_auth_ui/src/screens/onboarding_steps/onboarding_steps_cubit.dart';
import 'package:feature_auth_ui/src/screens/onboarding_steps/onboarding_steps_model_mapper.dart';
import 'package:flutter/rendering.dart';
import 'package:tests/tests.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_onboarding_domain_api/index.dart';

void main() {
  late OnboardingStepsModelMapperImpl mapper;
  late AuthLocalizations localizations;
  late MockCommonLocalizations mockCommonLocalizations;

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    localizations = await AuthLocalizations.load(const Locale('en'));
  });

  setUp(() {
    mockCommonLocalizations = MockCommonLocalizations();
    mapper = OnboardingStepsModelMapperImpl(
      localizations: localizations,
      commonLocalizations: mockCommonLocalizations,
    );
  });

  group('buildStepCardsModels', () {
    test(
      'should build step card models correctly for invitationVerification '
      'stage and isUaePassFlow=false',
      () {
        // arrange
        const stages = [
          OnboardingStage(
            stageType: OnBoardingStageType.invitationVerification,
            status: OnBoardingCompletionStatus.notCompleted,
            pages: [],
          ),
        ];
        const isUaePassFlow = false;
        const uaePassClicked = false;

        // act
        final result = mapper.buildStepCardsModels(
          stages: stages,
          isUaePassFlow: isUaePassFlow,
          uaePassClicked: uaePassClicked,
        );

        // assert
        expect(result.length, 1);
        expect(
          result.first.id,
          OnboardingStepsCubit.stepInvitationVerification,
        );
        expect(result.first.variant, StepCardVariant.active);
        expect(
          result.first.tag,
          localizations.onboardingStepsStep(1),
        );
        expect(
          result.first.title,
          localizations.onboardingStepsCheckYourInvitations,
        );
        expect(
          result.first.buttonText,
          localizations.onboardingStepsSelectStep,
        );
        expect(
          result.first.icon,
          CompanyPictogramPointer.functions_mail.toGraphicAsset(),
        );
        expect(result.first.iconRatio, CompanyIconRatio.wide);
      },
    );

    test(
      'should build step card models correctly for docVerification stage '
      'and isUaePassFlow=true',
      () {
        // arrange
        const stages = [
          OnboardingStage(
            stageType: OnBoardingStageType.docVerification,
            status: OnBoardingCompletionStatus.completed,
            pages: [],
          ),
        ];
        const isUaePassFlow = true;
        const uaePassClicked = true;

        // act
        final result = mapper.buildStepCardsModels(
          stages: stages,
          isUaePassFlow: isUaePassFlow,
          uaePassClicked: uaePassClicked,
        );

        // assert
        expect(result.length, 1);
        expect(result.first.id, OnboardingStepsCubit.stepVerifyIdentity);
        expect(result.first.variant, StepCardVariant.done);
        expect(result.first.tag, localizations.uaePassApproval);
        expect(result.first.title, localizations.shareEmiratesIDfromUaePass);
        expect(result.first.buttonText, mockCommonLocalizations.common_done);
        expect(
          result.first.icon,
          CompanyPictogramPointer.validation_success.toGraphicAsset(),
        );
        expect(
          result.first.buttonIcon,
          CompanyIconPointer.uae_pass.toGraphicAsset(),
        );
        expect(result.first.iconRatio, CompanyIconRatio.regular);
      },
    );

    test(
      'should build step card models correctly for personalDetails stage '
      'and isUaePassFlow=false',
      () {
        // arrange
        const stages = [
          OnboardingStage(
            stageType: OnBoardingStageType.personalDetails,
            status: OnBoardingCompletionStatus.notCompleted,
            pages: [],
          ),
        ];
        const isUaePassFlow = false;
        const uaePassClicked = false;

        // act
        final result = mapper.buildStepCardsModels(
          stages: stages,
          isUaePassFlow: isUaePassFlow,
          uaePassClicked: uaePassClicked,
        );

        // assert
        expect(result.length, 1);
        expect(result.first.id, OnboardingStepsCubit.stepPersonalData);
        expect(result.first.variant, StepCardVariant.active);
        expect(
          result.first.tag,
          localizations.onboardingStepsStep(1),
        );
        expect(
          result.first.title,
          localizations.onboardingStepsPersonalDetails,
        );
        expect(
          result.first.buttonText,
          localizations.onboardingStepsSelectStep,
        );
        expect(
          result.first.icon,
          CompanyPictogramPointer.emotions_happy.toGraphicAsset(),
        );
        expect(result.first.iconRatio, CompanyIconRatio.regular);
      },
    );

    test(
      'should throw an exception for unsupported step type',
      () {
        // arrange
        const stages = [
          OnboardingStage(
            stageType: OnBoardingStageType.unknown, // unsupported type
            status: OnBoardingCompletionStatus.notCompleted,
            pages: [],
          ),
        ];
        const isUaePassFlow = false;
        const uaePassClicked = false;

        // act and assert
        expect(
          () => mapper.buildStepCardsModels(
            stages: stages,
            isUaePassFlow: isUaePassFlow,
            uaePassClicked: uaePassClicked,
          ),
          throwsException,
        );
      },
    );
  });

  test('titleText should return the correct title for isUaePassFlow=true', () {
    // arrange
    const isUaePassFlow = true;
    const uncompletedStepsCount = 3;

    // act
    final result = mapper.titleText(
      isUaePassFlow: isUaePassFlow,
      uncompletedStepsCount: uncompletedStepsCount,
    );

    // assert
    expect(result, localizations.onboardingUaePassStepsTitle);
  });

  test(
    'titleText should return the correct title for isUaePassFlow=false',
    () {
      // arrange
      const isUaePassFlow = false;
      const uncompletedStepsCount = 3;

      // act
      final result = mapper.titleText(
        isUaePassFlow: isUaePassFlow,
        uncompletedStepsCount: uncompletedStepsCount,
      );

      // assert
      expect(
        result,
        localizations.onboardingStepsTitle(
          uncompletedStepsCount,
          uncompletedStepsCount,
        ),
      );
    },
  );

  group('uaePassPageStatus', () {
    test(
      'should return the correct status for a valid page with uaepass source',
      () {
        // arrange
        const stage = OnboardingStage(
          stageType: OnBoardingStageType.docVerification,
          status: OnBoardingCompletionStatus.notCompleted,
          pages: [
            OnboardingPageInfo(
              pageType: OnBoardingPageType.unknown,
              subTypes: [],
              optional: false,
              editable: true,
              sources: [OnBoardingIdentitySourceType.uaepass],
              tinInfo: [],
              questions: [],
              status: OnBoardingPageInfoStatus.notCompleted,
              refreshNextPage: false,
            ),
          ],
        );

        // act
        final result = mapper.uaePassPageStatus(stage: stage);

        // assert
        expect(result, OnBoardingPageInfoStatus.notCompleted);
      },
    );

    test(
      'should return OnBoardingPageInfoStatus.unknown for a page without '
      'uaepass source',
      () {
        // arrange
        const stage = OnboardingStage(
          stageType: OnBoardingStageType.docVerification,
          status: OnBoardingCompletionStatus.notCompleted,
          pages: [
            OnboardingPageInfo(
              pageType: OnBoardingPageType.unknown,
              subTypes: [],
              optional: false,
              editable: true,
              sources: [],
              tinInfo: [],
              questions: [],
              status: OnBoardingPageInfoStatus.notCompleted,
              refreshNextPage: false,
            ),
          ],
        );

        // act
        final result = mapper.uaePassPageStatus(stage: stage);

        // assert
        expect(result, OnBoardingPageInfoStatus.unknown);
      },
    );

    test(
      'should return OnBoardingPageInfoStatus.unknown for a stage without '
      'any pages',
      () {
        // arrange
        const stage = OnboardingStage(
          stageType: OnBoardingStageType.docVerification,
          status: OnBoardingCompletionStatus.notCompleted,
          pages: [],
        );

        // act
        final result = mapper.uaePassPageStatus(stage: stage);

        // assert
        expect(result, OnBoardingPageInfoStatus.unknown);
      },
    );
  });

  group('getCardVariant', () {
    test(
      'should return StepCardVariant.unactive if isPrevCompleted '
      'is notCompleted',
      () {
        // arrange
        const stages = [
          OnboardingStage(
            stageType: OnBoardingStageType.docVerification,
            status: OnBoardingCompletionStatus.notCompleted,
            pages: [],
          ),
          OnboardingStage(
            stageType: OnBoardingStageType.personalDetails,
            status: OnBoardingCompletionStatus.notCompleted,
            pages: [],
          ),
        ];

        const isUaePassFlow = true;
        const uaePassClicked = true;

        // act
        final result = mapper.buildStepCardsModels(
          stages: stages,
          isUaePassFlow: isUaePassFlow,
          uaePassClicked: uaePassClicked,
        );

        // assert
        expect(result.last.variant, StepCardVariant.unactive);
      },
    );

    test(
      'should return StepCardVariant.loader if isUaePassFlow is true, '
      'uaePassCardStatus is pending, and uaePassClicked is true',
      () {
        // arrange
        const stage = OnboardingStage(
          stageType: OnBoardingStageType.docVerification,
          status: OnBoardingCompletionStatus.notCompleted,
          pages: [
            OnboardingPageInfo(
              pageType: OnBoardingPageType.documentSharing,
              subTypes: [],
              optional: false,
              editable: false,
              sources: [OnBoardingIdentitySourceType.uaepass],
              tinInfo: [],
              questions: [],
              status: OnBoardingPageInfoStatus.pending,
              refreshNextPage: false,
            ),
          ],
        );
        const isUaePassFlow = true;
        const uaePassClicked = true;

        // act
        final result = mapper.buildStepCardsModels(
          stages: [stage],
          isUaePassFlow: isUaePassFlow,
          uaePassClicked: uaePassClicked,
        );

        // assert
        expect(result.first.variant, StepCardVariant.loader);
      },
    );

    test(
      'should return StepCardVariant.uaepass if isUaePassFlow is true and '
      'uaePassCardStatus is notCompleted or pending and uaePassClicked is '
      'false',
      () {
        // arrange
        const stage = OnboardingStage(
          stageType: OnBoardingStageType.docVerification,
          status: OnBoardingCompletionStatus.notCompleted,
          pages: [
            OnboardingPageInfo(
              pageType: OnBoardingPageType.documentSharing,
              subTypes: [],
              optional: false,
              editable: false,
              sources: [OnBoardingIdentitySourceType.uaepass],
              tinInfo: [],
              questions: [],
              status: OnBoardingPageInfoStatus.notCompleted,
              refreshNextPage: false,
            ),
          ],
        );
        const isUaePassFlow = true;
        const uaePassClicked = false;

        // act
        final result = mapper.buildStepCardsModels(
          stages: [stage],
          isUaePassFlow: isUaePassFlow,
          uaePassClicked: uaePassClicked,
        );

        // assert
        expect(result.first.variant, StepCardVariant.uaepass);
      },
    );

    test(
      'should return StepCardVariant.done if status is completed',
      () {
        // arrange
        const stage = OnboardingStage(
          stageType: OnBoardingStageType.docVerification,
          status: OnBoardingCompletionStatus.completed,
          pages: [],
        );
        const isUaePassFlow = false;
        const uaePassClicked = false;

        // act
        final result = mapper.buildStepCardsModels(
          stages: [stage],
          isUaePassFlow: isUaePassFlow,
          uaePassClicked: uaePassClicked,
        );

        // assert
        expect(result.first.variant, StepCardVariant.done);
      },
    );

    test(
      'should return StepCardVariant.active if status is notCompleted',
      () {
        // arrange
        const stage = OnboardingStage(
          stageType: OnBoardingStageType.docVerification,
          status: OnBoardingCompletionStatus.notCompleted,
          pages: [],
        );
        const isUaePassFlow = false;
        const uaePassClicked = false;

        // act
        final result = mapper.buildStepCardsModels(
          stages: [stage],
          isUaePassFlow: isUaePassFlow,
          uaePassClicked: uaePassClicked,
        );

        // assert
        expect(result.first.variant, StepCardVariant.active);
      },
    );
  });

  group('getCardButtonText', () {
    test(
      'should return the expected text when isPrevCompleted is false',
      () {
        // arrange
        const stages = [
          OnboardingStage(
            stageType: OnBoardingStageType.docVerification,
            status: OnBoardingCompletionStatus.notCompleted,
            pages: [],
          ),
          OnboardingStage(
            stageType: OnBoardingStageType.personalDetails,
            status: OnBoardingCompletionStatus.notCompleted,
            pages: [],
          ),
        ];
        // act
        final result = mapper.buildStepCardsModels(
          stages: stages,
          isUaePassFlow: true,
          uaePassClicked: true,
        );

        // assert
        expect(
          result.last.buttonText,
          localizations.onboardingStepsConcludePreviouseStep,
        );
      },
    );

    test(
      'should return the expected text when step completed',
      () {
        // act
        final result = mapper.buildStepCardsModels(
          stages: [
            const OnboardingStage(
              stageType: OnBoardingStageType.docVerification,
              status: OnBoardingCompletionStatus.completed,
              pages: [],
            ),
          ],
          isUaePassFlow: true,
          uaePassClicked: true,
        );

        // assert
        expect(result.first.buttonText, mockCommonLocalizations.common_done);
      },
    );

    test(
      'should return the expected variant when uaePassCardStatus not unknown',
      () {
        // arrange
        const stage = OnboardingStage(
          stageType: OnBoardingStageType.docVerification,
          status: OnBoardingCompletionStatus.notCompleted,
          pages: [
            OnboardingPageInfo(
              pageType: OnBoardingPageType.documentSharing,
              subTypes: [],
              optional: false,
              editable: false,
              sources: [OnBoardingIdentitySourceType.uaepass],
              tinInfo: [],
              questions: [],
              status: OnBoardingPageInfoStatus.pending,
              refreshNextPage: false,
            ),
          ],
        );

        // act
        final result = mapper.buildStepCardsModels(
          stages: [stage],
          isUaePassFlow: true,
          uaePassClicked: true,
        );

        // assert
        expect(result.first.buttonText, localizations.continueWithUaePass);
      },
    );

    test(
      'should return the expected text when no one result found',
      () {
        // act
        final result = mapper.buildStepCardsModels(
          stages: [
            const OnboardingStage(
              stageType: OnBoardingStageType.docVerification,
              status: OnBoardingCompletionStatus.unknown,
              pages: [],
            ),
          ],
          isUaePassFlow: true,
          uaePassClicked: true,
        );

        // assert
        expect(result.first.buttonText, '');
      },
    );
  });

  group('getCardPictogram', () {
    test(
      'should return the expected GraphicAssetPointer for completed status',
      () {
        // arrange
        const stage = OnboardingStage(
          stageType: OnBoardingStageType.docVerification,
          status: OnBoardingCompletionStatus.completed,
          pages: [],
        );

        // act
        final result = mapper.buildStepCardsModels(
          stages: [stage],
          isUaePassFlow: false,
          uaePassClicked: false,
        );

        // assert
        expect(
          result.first.icon,
          equals(CompanyPictogramPointer.validation_success.toGraphicAsset()),
        );
      },
    );

    test(
      'should return the expected GraphicAssetPointer for not completed status '
      'in personal details flow',
      () {
        // arrange
        const stage = OnboardingStage(
          stageType: OnBoardingStageType.personalDetails,
          status: OnBoardingCompletionStatus.notCompleted,
          pages: [],
        );

        // act
        final result = mapper.buildStepCardsModels(
          stages: [stage],
          isUaePassFlow: false,
          uaePassClicked: false,
        );

        // assert
        expect(
          result.first.icon,
          equals(CompanyPictogramPointer.emotions_happy.toGraphicAsset()),
        );
      },
    );
  });

  test(
    'should return the expected GraphicAssetPointer for not completed status '
    'in document verification flow of uae pass',
    () {
      // arrange
      const stage = OnboardingStage(
        stageType: OnBoardingStageType.docVerification,
        status: OnBoardingCompletionStatus.notCompleted,
        pages: [],
      );

      // act
      final result = mapper.buildStepCardsModels(
        stages: [stage],
        isUaePassFlow: true,
        uaePassClicked: false,
      );

      // assert
      expect(
        result.first.icon,
        equals(CompanyPictogramPointer.functions_download.toGraphicAsset()),
      );
    },
  );

  test(
    'should return the expected GraphicAssetPointer for not completed status '
    'in document verification flow',
    () {
      // arrange
      const stage = OnboardingStage(
        stageType: OnBoardingStageType.docVerification,
        status: OnBoardingCompletionStatus.notCompleted,
        pages: [],
      );

      // act
      final result = mapper.buildStepCardsModels(
        stages: [stage],
        isUaePassFlow: false,
        uaePassClicked: false,
      );

      // assert
      expect(
        result.first.icon,
        equals(CompanyPictogramPointer.business_safe.toGraphicAsset()),
      );
    },
  );
}
