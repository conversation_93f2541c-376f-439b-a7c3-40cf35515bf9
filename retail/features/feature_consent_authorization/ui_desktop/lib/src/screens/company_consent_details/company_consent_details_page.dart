import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_retail_desktop/ui_kit_retail_desktop.dart';
import 'package:wio_common_feature_consent_authorization_api/consent_authorization_api.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/feature_consent_authorization_ui_desktop.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/notification/contact_support_drawer.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/company_consent_details/company_consent_details_cubit.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/company_consent_details/company_consent_details_desktop_layout.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/company_consent_details/company_consent_details_mobile_layout.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/company_consent_details/company_consent_details_state.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/consents_pagination/consent_list_cubit.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/consents_pagination/consent_list_state.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/helpers.dart';
import 'package:wio_feature_core_ui_desktop/index.dart';

class CompanyConsentDetailsPage extends StatelessWidget {
  final CompanyConsentDetailsScreenNavigationConfig config;

  const CompanyConsentDetailsPage({
    required this.config,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final companyTheme = CompanyThemeProvider.of(context);

    return DrawerDisplayer(
      builder: (_) => const ContactSupportDrawer(),
      child: MultiBlocProvider(
        providers: [
          BlocProvider<CompanyConsentDetailsCubit>(
            create: (_) => DependencyProvider.get<CompanyConsentDetailsCubit>()
              ..initialize(config.companyId),
          ),
          BlocProvider<ConsentListCubit>(
            create: (_) => DependencyProvider.get<ConsentListCubit>(),
          ),
        ],
        child: BlocConsumer<CompanyConsentDetailsCubit,
            CompanyConsentDetailsState>(
          listener: _listenState,
          builder: (context, state) {
            return PopScope(
              canPop: false,
              onPopInvoked: (didPop) {
                if (!didPop) {
                  context.read<CompanyConsentDetailsCubit>().onBackClick();
                }
              },
              child: Material(
                color: companyTheme.colorScheme.background1,
                child: ResponsiveLayout(
                  desktopLayout: (context) =>
                      CompanyConsentDetailsDesktopLayout(
                    clientId: config.companyId,
                  ),
                  mobileLayout: (context) => CompanyConsentDetailsMobileLayout(
                    clientId: config.companyId,
                  ),
                ),
              ),
            );
          },
          buildWhen: (previous, current) => current.maybeMap(
            orElse: () => false,
            loaded: (value) => true,
          ),
          listenWhen: (previous, current) => current.maybeMap(
            showError: (value) => true,
            reinitialize: (value) => true,
            orElse: () => false,
          ),
        ),
      ),
    );
  }

  void _listenState(BuildContext context, CompanyConsentDetailsState state) {
    final commonErrorHandler = context.read<CommonErrorHandlerCubit>();
    final localizations = ConsentAuthorizationLocalizations.of(context);
    state.mapOrNull(
      reinitialize: (value) => context.read<ConsentListCubit>().init(
            source: ConsentsSource.clientId(
              clientId: value.clientId,
            ),
          ),
      showError: (state) {
        if (state.errorBy == ErrorBy.companyConsentRevoke) {
          commonErrorHandler.handleError(
            state.errorBy.errorMessage(
              text: state.companyName,
              localizations: localizations,
            ),
          );
        } else if (state.errorBy == ErrorBy.exception) {
          commonErrorHandler.handleError(state.error);
        }
      },
    );
  }
}
