import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui/page/page_bloc_provider.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:wio_common_feature_consent_authorization_api/consent_authorization_api.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/payment_authorization/payment_authorization_cubit.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/payment_authorization/payment_authorization_state.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/widgets.dart';
import 'package:wio_feature_core_ui_desktop/index.dart';

class PaymentAuthorizationPage extends StatelessWidget {
  final PaymentAuthorizationScreenNavigationConfig config;

  const PaymentAuthorizationPage({
    super.key,
    required this.config,
  });

  @override
  Widget build(BuildContext context) {
    final companyTheme = CompanyThemeProvider.of(context);

    return PageBlocProvider<PaymentAuthorizationCubit,
        PaymentAuthorizationState>(
      createBloc: () => DependencyProvider.get<PaymentAuthorizationCubit>(),
      initBloc: (cubit) => cubit.init(
        config.model,
      ),
      onWillPop: _onWillPop,
      child: BlocConsumer<PaymentAuthorizationCubit, PaymentAuthorizationState>(
        listener: _listenState,
        builder: (context, state) {
          return Material(
            color: companyTheme.colorScheme.background1,
            child: const EmptyStateLoader(),
          );
        },
        listenWhen: (previous, current) => current.maybeMap(
          orElse: () => false,
          showError: (value) => true,
        ),
      ),
    );
  }

  void _listenState(BuildContext context, PaymentAuthorizationState state) {
    final commonErrorHandler = context.read<CommonErrorHandlerCubit>();

    state.mapOrNull(
      showError: (state) {
        commonErrorHandler.handleError(state.error);
      },
    );
  }

  Future<bool> _onWillPop(BuildContext context) {
    context.read<PaymentAuthorizationCubit>().onBackClick();

    return Future<bool>.value(false);
  }
}
