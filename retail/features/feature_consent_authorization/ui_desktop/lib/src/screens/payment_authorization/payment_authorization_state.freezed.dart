// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_authorization_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PaymentAuthorizationState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() empty,
    required TResult Function(Object? error, ErrorBy errorBy) showError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? empty,
    TResult? Function(Object? error, ErrorBy errorBy)? showError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? empty,
    TResult Function(Object? error, ErrorBy errorBy)? showError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PaymentStateEmpty value) empty,
    required TResult Function(_PaymentAuthorizationStateShowErrorState value)
        showError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PaymentStateEmpty value)? empty,
    TResult? Function(_PaymentAuthorizationStateShowErrorState value)?
        showError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PaymentStateEmpty value)? empty,
    TResult Function(_PaymentAuthorizationStateShowErrorState value)? showError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentAuthorizationStateCopyWith<$Res> {
  factory $PaymentAuthorizationStateCopyWith(PaymentAuthorizationState value,
          $Res Function(PaymentAuthorizationState) then) =
      _$PaymentAuthorizationStateCopyWithImpl<$Res, PaymentAuthorizationState>;
}

/// @nodoc
class _$PaymentAuthorizationStateCopyWithImpl<$Res,
        $Val extends PaymentAuthorizationState>
    implements $PaymentAuthorizationStateCopyWith<$Res> {
  _$PaymentAuthorizationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentAuthorizationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$PaymentStateEmptyImplCopyWith<$Res> {
  factory _$$PaymentStateEmptyImplCopyWith(_$PaymentStateEmptyImpl value,
          $Res Function(_$PaymentStateEmptyImpl) then) =
      __$$PaymentStateEmptyImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PaymentStateEmptyImplCopyWithImpl<$Res>
    extends _$PaymentAuthorizationStateCopyWithImpl<$Res,
        _$PaymentStateEmptyImpl>
    implements _$$PaymentStateEmptyImplCopyWith<$Res> {
  __$$PaymentStateEmptyImplCopyWithImpl(_$PaymentStateEmptyImpl _value,
      $Res Function(_$PaymentStateEmptyImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentAuthorizationState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PaymentStateEmptyImpl implements PaymentStateEmpty {
  const _$PaymentStateEmptyImpl();

  @override
  String toString() {
    return 'PaymentAuthorizationState.empty()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$PaymentStateEmptyImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() empty,
    required TResult Function(Object? error, ErrorBy errorBy) showError,
  }) {
    return empty();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? empty,
    TResult? Function(Object? error, ErrorBy errorBy)? showError,
  }) {
    return empty?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? empty,
    TResult Function(Object? error, ErrorBy errorBy)? showError,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PaymentStateEmpty value) empty,
    required TResult Function(_PaymentAuthorizationStateShowErrorState value)
        showError,
  }) {
    return empty(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PaymentStateEmpty value)? empty,
    TResult? Function(_PaymentAuthorizationStateShowErrorState value)?
        showError,
  }) {
    return empty?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PaymentStateEmpty value)? empty,
    TResult Function(_PaymentAuthorizationStateShowErrorState value)? showError,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty(this);
    }
    return orElse();
  }
}

abstract class PaymentStateEmpty implements PaymentAuthorizationState {
  const factory PaymentStateEmpty() = _$PaymentStateEmptyImpl;
}

/// @nodoc
abstract class _$$PaymentAuthorizationStateShowErrorStateImplCopyWith<$Res> {
  factory _$$PaymentAuthorizationStateShowErrorStateImplCopyWith(
          _$PaymentAuthorizationStateShowErrorStateImpl value,
          $Res Function(_$PaymentAuthorizationStateShowErrorStateImpl) then) =
      __$$PaymentAuthorizationStateShowErrorStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Object? error, ErrorBy errorBy});
}

/// @nodoc
class __$$PaymentAuthorizationStateShowErrorStateImplCopyWithImpl<$Res>
    extends _$PaymentAuthorizationStateCopyWithImpl<$Res,
        _$PaymentAuthorizationStateShowErrorStateImpl>
    implements _$$PaymentAuthorizationStateShowErrorStateImplCopyWith<$Res> {
  __$$PaymentAuthorizationStateShowErrorStateImplCopyWithImpl(
      _$PaymentAuthorizationStateShowErrorStateImpl _value,
      $Res Function(_$PaymentAuthorizationStateShowErrorStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentAuthorizationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
    Object? errorBy = null,
  }) {
    return _then(_$PaymentAuthorizationStateShowErrorStateImpl(
      error: freezed == error ? _value.error : error,
      errorBy: null == errorBy
          ? _value.errorBy
          : errorBy // ignore: cast_nullable_to_non_nullable
              as ErrorBy,
    ));
  }
}

/// @nodoc

class _$PaymentAuthorizationStateShowErrorStateImpl
    implements _PaymentAuthorizationStateShowErrorState {
  const _$PaymentAuthorizationStateShowErrorStateImpl(
      {this.error, this.errorBy = ErrorBy.exception});

  @override
  final Object? error;
  @override
  @JsonKey()
  final ErrorBy errorBy;

  @override
  String toString() {
    return 'PaymentAuthorizationState.showError(error: $error, errorBy: $errorBy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentAuthorizationStateShowErrorStateImpl &&
            const DeepCollectionEquality().equals(other.error, error) &&
            (identical(other.errorBy, errorBy) || other.errorBy == errorBy));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(error), errorBy);

  /// Create a copy of PaymentAuthorizationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentAuthorizationStateShowErrorStateImplCopyWith<
          _$PaymentAuthorizationStateShowErrorStateImpl>
      get copyWith =>
          __$$PaymentAuthorizationStateShowErrorStateImplCopyWithImpl<
              _$PaymentAuthorizationStateShowErrorStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() empty,
    required TResult Function(Object? error, ErrorBy errorBy) showError,
  }) {
    return showError(error, errorBy);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? empty,
    TResult? Function(Object? error, ErrorBy errorBy)? showError,
  }) {
    return showError?.call(error, errorBy);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? empty,
    TResult Function(Object? error, ErrorBy errorBy)? showError,
    required TResult orElse(),
  }) {
    if (showError != null) {
      return showError(error, errorBy);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PaymentStateEmpty value) empty,
    required TResult Function(_PaymentAuthorizationStateShowErrorState value)
        showError,
  }) {
    return showError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PaymentStateEmpty value)? empty,
    TResult? Function(_PaymentAuthorizationStateShowErrorState value)?
        showError,
  }) {
    return showError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PaymentStateEmpty value)? empty,
    TResult Function(_PaymentAuthorizationStateShowErrorState value)? showError,
    required TResult orElse(),
  }) {
    if (showError != null) {
      return showError(this);
    }
    return orElse();
  }
}

abstract class _PaymentAuthorizationStateShowErrorState
    implements PaymentAuthorizationState {
  const factory _PaymentAuthorizationStateShowErrorState(
      {final Object? error,
      final ErrorBy errorBy}) = _$PaymentAuthorizationStateShowErrorStateImpl;

  Object? get error;
  ErrorBy get errorBy;

  /// Create a copy of PaymentAuthorizationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentAuthorizationStateShowErrorStateImplCopyWith<
          _$PaymentAuthorizationStateShowErrorStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
