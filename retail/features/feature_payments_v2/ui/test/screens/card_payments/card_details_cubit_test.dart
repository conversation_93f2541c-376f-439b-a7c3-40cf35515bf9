import 'package:bloc_test/bloc_test.dart';
import 'package:domain/domain.dart';
import 'package:mocktail/mocktail.dart';
import 'package:rxdart/rxdart.dart';
import 'package:tests/tests.dart' hide MockCommonLocalizations;
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/card_details/card_details_cubit.dart';

import '../../feature_payments_mocks.dart';
import '../../mock_data.dart';

void main() {
  late MockCardPaymentsInteractor cardPaymentsInteractor;
  late MockNavigationProvider navigationProvider;
  late MockToastMessageProvider toastMessageProvider;
  late MockCommonLocalizations commonLocalizations;
  late CardDetailsCubit cardDetailsCubit;
  late BehaviorSubject<Data<List<CardBeneficiary>>> cardsSubject;

  setUp(() {
    cardPaymentsInteractor = MockCardPaymentsInteractor();
    navigationProvider = MockNavigationProvider();
    toastMessageProvider = MockToastMessageProvider();
    commonLocalizations = MockCommonLocalizations();

    cardDetailsCubit = CardDetailsCubit(
      cardPaymentsInteractor: cardPaymentsInteractor,
      navigationProvider: navigationProvider,
      toastMessageProvider: toastMessageProvider,
      commonLocalizations: commonLocalizations,
    );

    cardsSubject = BehaviorSubject<Data<List<CardBeneficiary>>>();

    registerFallbackValue(NotificationToastMessageConfigurationFake());
    when(() => commonLocalizations.somethingWentWrongMessage)
        .thenReturn('somethingWentWrongMessage');
  });

  final cardSkin = randomString();
  final beneficiary = MockData.cardBeneficiary();

  tearDown(() => cardsSubject.close());

  void mockInit() {
    when(() => cardPaymentsInteractor.getCardSkin()).justAnswerAsync(cardSkin);
    when(() => cardPaymentsInteractor.observeCardBeneficiaries())
        .thenAnswer((_) => cardsSubject);
    when(() => cardPaymentsInteractor.getBeneficiary(any()))
        .justAnswerAsync(beneficiary);
  }

  group('Init', () {
    blocTest<CardDetailsCubit, CardDetailsState>(
      'Success (load card via id)',
      build: () => cardDetailsCubit,
      setUp: () => mockInit(),
      act: (c) => c.init(
        input: const CardDetailsInput.byId('ID'),
        isSpecialPlan: false,
      ),
      expect: () => [
        const CardDetailsState.idle(),
        CardDetailsState.loaded(
          beneficiary: beneficiary,
          cardTextureUrl: cardSkin,
          isSpecialPlan: false,
        ),
      ],
      verify: (_) {
        verify(() => cardPaymentsInteractor.observeCardBeneficiaries())
            .calledOnce;
        verify(() => cardPaymentsInteractor.getCardSkin()).calledOnce;
        verify(() => cardPaymentsInteractor.getBeneficiary('ID')).calledOnce;
      },
    );

    blocTest<CardDetailsCubit, CardDetailsState>(
      'Success (no beneficiary loading)',
      build: () => cardDetailsCubit,
      setUp: () => mockInit(),
      act: (c) => c.init(
        input: CardDetailsInput.byCard(beneficiary),
        isSpecialPlan: false,
      ),
      expect: () => [
        const CardDetailsState.idle(),
        CardDetailsState.loaded(
          beneficiary: beneficiary,
          cardTextureUrl: cardSkin,
          isSpecialPlan: false,
        ),
      ],
      verify: (_) {
        verify(() => cardPaymentsInteractor.observeCardBeneficiaries())
            .calledOnce;
        verify(() => cardPaymentsInteractor.getCardSkin()).calledOnce;
        verifyNever(() => cardPaymentsInteractor.getBeneficiary(any()));
      },
    );

    blocTest<CardDetailsCubit, CardDetailsState>(
      'Failure',
      build: () => cardDetailsCubit,
      act: (c) => c.init(
        input: CardDetailsInput.byCard(beneficiary),
        isSpecialPlan: false,
      ),
      expect: () => [const CardDetailsState.idle()],
      verify: (_) {
        verify(navigationProvider.goBack).calledOnce;
        verify(() => commonLocalizations.somethingWentWrongMessage).calledOnce;
        verify(() => toastMessageProvider.showToastMessage(any())).calledOnce;
      },
    );

    final updatedCard = beneficiary.copyWith(cardName: 'UpdatedName');

    blocTest<CardDetailsCubit, CardDetailsState>(
      'card updates after some time',
      build: () => cardDetailsCubit,
      setUp: () => mockInit(),
      act: (c) async {
        await c.init(
          input: CardDetailsInput.byCard(beneficiary),
          isSpecialPlan: false,
        );

        final newCards = [updatedCard];
        cardsSubject.add(Data.success(newCards));

        await flushFutures();
      },
      expect: () => [
        const CardDetailsState.idle(),
        CardDetailsState.loaded(
          beneficiary: beneficiary,
          cardTextureUrl: cardSkin,
          isSpecialPlan: false,
        ),
        CardDetailsState.loaded(
          beneficiary: updatedCard,
          cardTextureUrl: cardSkin,
          isSpecialPlan: false,
        ),
      ],
    );

    blocTest<CardDetailsCubit, CardDetailsState>(
      'card gets deleted after some time',
      build: () => cardDetailsCubit,
      setUp: () => mockInit(),
      act: (c) async {
        await c.init(
          input: CardDetailsInput.byCard(beneficiary),
          isSpecialPlan: true,
        );
        cardsSubject.add(Data.success([]));

        await flushFutures();
      },
      verify: (_) {
        verify(navigationProvider.goBack).calledOnce;
      },
      expect: () => [
        const CardDetailsState.idle(),
        CardDetailsState.loaded(
          beneficiary: beneficiary,
          cardTextureUrl: cardSkin,
          isSpecialPlan: true,
        ),
      ],
    );
  });
}
