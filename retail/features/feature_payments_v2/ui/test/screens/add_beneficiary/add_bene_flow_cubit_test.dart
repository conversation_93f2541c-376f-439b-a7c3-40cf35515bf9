import 'dart:ui';

import 'package:bloc_test/bloc_test.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/ui_tests.dart';
import 'package:ui/ota_lokalisation/ota_lokalization.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_address_api/wio_common_feature_address_api.dart';
import 'package:wio_common_feature_customer_address_api/index.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_common_feature_payments_ui/wio_common_feature_payments_ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_payments_v2_ui/feature_payments_v2_ui.dart';
import 'package:wio_feature_payments_v2_ui/src/navigation/configs/index.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/add_beneficiary_flow/flow/cubit/add_bene_flow_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/add_beneficiary_flow/flow/cubit/add_bene_flow_state.dart';

import '../../factories.dart';
import '../../feature_payments_mocks.dart';
import '../../mock_data.dart';

void main() {
  late NavigationProvider navigationProvider;
  late Logger logger;
  late PaymentInteractor paymentInteractor;
  late AddBeneFlowCubit cubit;
  late WioPaymentsLocalizations commonL10n;
  late PaymentsLocalizations l10n;
  late MockCommonErrorHandler commonErrorHandler;
  late FeatureToggleProvider featureToggleProvider;
  late CustomerAddressInteractor customerAddressInteractor;
  late WiseInteractor wiseInteractor;
  late CustomerAddressFeatureNavigationFactory addressNavigationFactory;

  const countrySelected = PaymentCountry.localCountry;

  final currencySelected = PaymentCurrency(
    code: Currency.aed.code,
    name: randomString(),
    state: CurrencyStateType.available,
  );

  final supportedCurrencies = [
    CurrencyFactory.rand(),
    CurrencyFactory.rand(),
    CurrencyFactory.rand(),
  ];

  final beneficiary = MockData.germanyEurBeneficiary.copyWith(
    status: BeneficiaryStatus.unavailable,
    cooldownInfo: MockData.cooldown,
  );

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    commonL10n = await WioPaymentsLocalizations.load(const Locale('en'));
    registerFallbackValue(
      FakeSelectCurrencyBottomSheetConfig(),
    );
  });

  setUp(() {
    navigationProvider = MockNavigationProvider();
    logger = MockLogger();
    paymentInteractor = MockPaymentInteractor();
    commonL10n = MockWioPaymentsLocalizations();
    l10n = MockPaymentsLocalizations();
    commonErrorHandler = MockCommonErrorHandler();
    featureToggleProvider = MockFeatureToggleProvider();
    customerAddressInteractor = MockCustomerAddressInteractor();
    wiseInteractor = MockWiseInteractor();
    addressNavigationFactory = MockCustomerAddressFeatureNavigationFactory();

    cubit = AddBeneFlowCubit(
      navigationProvider: navigationProvider,
      logger: logger,
      interactor: paymentInteractor,
      commonl10n: commonL10n,
      l10n: l10n,
      commonErrorHandler: commonErrorHandler,
      featureToggleProvider: featureToggleProvider,
      customerAddressInteractor: customerAddressInteractor,
      wiseInteractor: wiseInteractor,
      addressFeatureFactory: addressNavigationFactory,
    );

    when(
      () => paymentInteractor.getSupportedCurrencies(
        countryCode: countrySelected.code,
      ),
    ).justAnswerAsync(supportedCurrencies);

    final selectedResult = SelectCurrencyBottomSheetResult(
      selectedCurrency: currencySelected,
    );

    when(
      () => navigationProvider.showBottomSheet<PaymentsBottomSheetResult>(
        PaymentsBottomSheetConfig.selectCurrency(
          supportedCurrencies: supportedCurrencies,
          selectedCountry: countrySelected,
        ),
      ),
    ).thenAnswer((_) async => selectedResult);

    when(
      () => l10n.beneCooldownAfterCreationSuccessSubtitleContactSaved,
    ).thenReturn('');
    when(
      () => commonL10n.beneCooldownAfterCreationSuccessTitle,
    ).thenReturn('');
    when(
      () => commonL10n.close,
    ).thenReturn('');
    when(
      () => featureToggleProvider
          .get(PaymentsFeatureToggle.isBeneficiaryAdditionLimitEnabled),
    ).thenReturn(true);
  });

  group('Init', () {
    blocTest<AddBeneFlowCubit, AddBeneFlowState>(
      'init emits stages with intro',
      build: () => cubit,
      verify: (cubit) {
        expect(cubit.state, const AddBeneFlowState());
      },
    );

    blocTest<AddBeneFlowCubit, AddBeneFlowState>(
      'init with country',
      build: () => cubit,
      act: (cubit) => cubit.init(country: countrySelected),
      verify: (cubit) {
        expect(
          cubit.state,
          const AddBeneFlowState(
            stages: [
              AddBeneStage.currencySelection(country: countrySelected),
            ],
          ),
        );
      },
    );

    blocTest<AddBeneFlowCubit, AddBeneFlowState>(
      'When Country is selected, currency selection alert shown',
      build: () => cubit,
      seed: () =>
          const AddBeneFlowState(stages: [AddBeneStage.countrySelection()]),
      act: (cubit) => cubit.onCountrySelected(countrySelected),
      verify: (_) {
        verify(
          () => paymentInteractor.getSupportedCurrencies(
            countryCode: countrySelected.code,
          ),
        ).calledOnce;
        verify(
          () => navigationProvider.showBottomSheet<PaymentsBottomSheetResult>(
            any(),
          ),
        ).calledOnce;
      },
    );

    blocTest<AddBeneFlowCubit, AddBeneFlowState>(
      'When Currency is selected for Local Payment Flow',
      build: () => cubit,
      seed: () => const AddBeneFlowState(
        stages: [AddBeneStage.currencySelection(country: countrySelected)],
      ),
      act: (cubit) =>
          cubit.onCurrencySelected(countrySelected, currencySelected),
      // Assert
      expect: () => [
        AddBeneFlowState(
          stages: [
            const AddBeneStage.currencySelection(country: countrySelected),
            AddBeneStage.beneficiaryCreation(
              country: countrySelected,
              currency: currencySelected,
            ),
          ],
        ),
      ],
    );

    blocTest<AddBeneFlowCubit, AddBeneFlowState>(
      'onPopScreen emits stages when more than one stage',
      build: () => cubit,
      seed: () => AddBeneFlowState(
        stages: [
          const AddBeneStage.countrySelection(),
          AddBeneStage.beneficiaryCreation(
            country: countrySelected,
            currency: currencySelected,
          ),
        ],
      ),
      act: (cubit) => cubit.onPopScreen(),
      expect: () => [
        const AddBeneFlowState(
          stages: [
            AddBeneStage.countrySelection(),
          ],
        ),
      ],
    );

    blocTest<AddBeneFlowCubit, AddBeneFlowState>(
      'onPopScreen calls goBack when only one stage',
      build: () => cubit,
      seed: () => const AddBeneFlowState(
        stages: [AddBeneStage.countrySelection()],
      ),
      act: (cubit) => cubit.onPopScreen(),
      verify: (_) {
        verify(() => navigationProvider.goBack()).calledOnce;
      },
    );

    blocTest<AddBeneFlowCubit, AddBeneFlowState>(
      'closeScreen',
      build: () => cubit,
      seed: () => const AddBeneFlowState(),
      act: (cubit) => cubit.closeScreen(),
      verify: (_) {
        verify(() => navigationProvider.goBack()).calledOnce;
      },
    );

    blocTest<AddBeneFlowCubit, AddBeneFlowState>(
      'After Country and Currency Selected to beneficiary creation stage',
      // Arrange
      build: () => cubit,
      seed: () => const AddBeneFlowState(
        stages: [
          AddBeneStage.countrySelection(),
        ],
      ),

      // Act
      act: (cubit) => cubit.onCountrySelected(countrySelected),

      // Assert
      expect: () => [
        AddBeneFlowState(
          stages: [
            const AddBeneStage.countrySelection(),
            AddBeneStage.beneficiaryCreation(
              country: countrySelected,
              currency: currencySelected,
            ),
          ],
        ),
      ],
    );

    blocTest<AddBeneFlowCubit, AddBeneFlowState>(
      'onBeneficiaryCreated',
      // Arrange
      build: () => cubit,
      seed: () => const AddBeneFlowState(),
      setUp: () {
        when(
          () => navigationProvider.navigateTo(any()),
        ).justCompleteAsync();
      },
      // Act
      act: (cubit) => cubit.onBeneficiaryCreated(
        BeneficiaryCreationResult(beneficiary: beneficiary),
      ),

      verify: (_) {
        verify(() => navigationProvider.navigateTo(captureAny())).calledOnce;
      },
    );

    blocTest<AddBeneFlowCubit, AddBeneFlowState>(
      'onBeneficiaryLimitReached',
      build: () => cubit,
      act: (cubit) => cubit.onBeneLimitReached(),
      verify: (_) {
        verify(() => navigationProvider.goBack()).calledOnce;
      },
    );
  });

  group('Wise account creation', () {
    blocTest(
      'Wise account exists, skips wise account creation',
      build: () => cubit,
      seed: () => const AddBeneFlowState(),
      setUp: () {
        when(
          () => wiseInteractor.getWiseStatus(),
        ).justAnswerAsync(
          const WiseStatus(
            businessProfileExists: false,
            personalProfileExists: true,
            countryRequired: false,
          ),
        );
      },
      act: (cubit) => cubit.init(),
      verify: (cubit) {
        verifyNever(
          () => wiseInteractor.createWiseAccount(WiseAccountType.personal),
        );
      },
    );

    blocTest(
      'Get Wise account exception does not call error handler',
      build: () => cubit,
      seed: () => const AddBeneFlowState(),
      setUp: () {
        when(
          () => customerAddressInteractor.getPrimaryAddress(),
        ).justAnswerAsync(const CustomerAddress());
        when(
          () => wiseInteractor.getWiseStatus(),
        ).justThrowAsync(Exception('Smth went wrong'));
      },
      act: (cubit) => cubit.init(),
      verify: (cubit) {
        verifyNever(
          () => wiseInteractor.createWiseAccount(WiseAccountType.personal),
        );
        verifyNever(
          () => commonErrorHandler.handleError(any()),
        );
      },
    );

    blocTest(
      'Missing address does not initiate create wise account call',
      build: () => cubit,
      seed: () => const AddBeneFlowState(),
      setUp: () {
        when(
          () => wiseInteractor.getWiseStatus(),
        ).justAnswerAsync(
          const WiseStatus(
            businessProfileExists: true,
            personalProfileExists: false,
            countryRequired: false,
          ),
        );
        when(
          () => customerAddressInteractor.getPrimaryAddress(),
        ).justAnswerEmptyAsync();
        when(
          () => addressNavigationFactory.navigateToCustomerAddressFeature(
            manualAddressEnabled: any(named: 'manualAddressEnabled'),
            showConfirmActualAddress: any(named: 'showConfirmActualAddress'),
          ),
        ).thenReturn(
          const AddressFeatureNavigationConfig(identifier: 'identifier'),
        );
        when(
          () => navigationProvider.navigateTo(
            any(
              that: isA<AddressFeatureNavigationConfig>(),
            ),
          ),
        ).justAnswerEmptyAsync();
      },
      act: (cubit) => cubit.init(),
      verify: (cubit) {
        verifyNever(
          () => wiseInteractor.createWiseAccount(WiseAccountType.personal),
        );
        verifyNever(
          () => commonErrorHandler.handleError(any()),
        );
      },
    );

    blocTest(
      'Selected address initiates create wise account call',
      build: () => cubit,
      seed: () => const AddBeneFlowState(),
      setUp: () {
        when(
          () => wiseInteractor.getWiseStatus(),
        ).justAnswerAsync(
          const WiseStatus(
            businessProfileExists: true,
            personalProfileExists: false,
            countryRequired: false,
          ),
        );
        when(
          () => customerAddressInteractor.getPrimaryAddress(),
        ).justAnswerEmptyAsync();
        when(
          () => addressNavigationFactory.navigateToCustomerAddressFeature(
            manualAddressEnabled: any(named: 'manualAddressEnabled'),
            showConfirmActualAddress: any(named: 'showConfirmActualAddress'),
          ),
        ).thenReturn(
          const AddressFeatureNavigationConfig(identifier: 'identifier'),
        );
        when(
          () => navigationProvider.navigateTo(
            any(
              that: isA<AddressFeatureNavigationConfig>(),
            ),
          ),
        ).justAnswerAsync(
          const AddressSuccessResult(
            addressRecord: AddressRecord(
              country: 'country',
              city: 'city',
              postalCode: 'postalCode',
              buildingName: 'buildingName',
              addressStreet: 'addressStreet',
              apartmentNumber: 'apartmentNumber',
              formattedAddress: 'formattedAddress',
            ),
          ),
        );
      },
      act: (cubit) => cubit.init(),
      verify: (cubit) {
        verify(
          () => wiseInteractor.createWiseAccount(WiseAccountType.personal),
        ).calledOnce;
        verifyNever(
          () => commonErrorHandler.handleError(any()),
        );
      },
    );
  });

  group('Address creation', () {
    setUp(() {
      when(
        () => wiseInteractor.getWiseStatus(),
      ).justAnswerAsync(
        const WiseStatus(
          businessProfileExists: false,
          personalProfileExists: true,
          countryRequired: false,
        ),
      );
    });

    blocTest(
      'If address exists, skips address creation',
      build: () => cubit,
      seed: () => const AddBeneFlowState(),
      setUp: () {
        when(
          () => customerAddressInteractor.getPrimaryAddress(),
        ).justAnswerAsync(const CustomerAddress());
      },
      act: (cubit) => cubit.init(),
      verify: (cubit) {
        verifyNever(
          () => addressNavigationFactory.navigateToCustomerAddressFeature(
            manualAddressEnabled: any(named: 'manualAddressEnabled'),
            showConfirmActualAddress: any(named: 'showConfirmActualAddress'),
          ),
        );

        verifyNever(
          () => navigationProvider.navigateTo(any()),
        );
      },
    );

    blocTest(
      'Missing address initiates navigation to address feature',
      build: () => cubit,
      seed: () => const AddBeneFlowState(),
      setUp: () {
        when(
          () => customerAddressInteractor.getPrimaryAddress(),
        ).justAnswerEmptyAsync();
        when(
          () => addressNavigationFactory.navigateToCustomerAddressFeature(
            manualAddressEnabled: any(named: 'manualAddressEnabled'),
            showConfirmActualAddress: any(named: 'showConfirmActualAddress'),
          ),
        ).thenReturn(
          const AddressFeatureNavigationConfig(identifier: 'identifier'),
        );
        when(
          () => navigationProvider.navigateTo(
            any(
              that: isA<AddressFeatureNavigationConfig>(),
            ),
          ),
        ).justAnswerAsync(
          const AddressSuccessResult(
            addressRecord: AddressRecord(
              country: 'country',
              city: 'city',
              postalCode: 'postalCode',
              buildingName: 'buildingName',
              addressStreet: 'addressStreet',
              apartmentNumber: 'apartmentNumber',
              formattedAddress: 'formattedAddress',
            ),
          ),
        );
      },
      act: (cubit) => cubit.init(),
      verify: (cubit) {
        verify(
          () => navigationProvider.navigateTo(
            any(that: isA<AddressFeatureNavigationConfig>()),
          ),
        ).calledOnce;
      },
    );

    blocTest(
      'Not existing address initiates navigation to address feature',
      build: () => cubit,
      seed: () => const AddBeneFlowState(),
      setUp: () {
        when(
          () => customerAddressInteractor.getPrimaryAddress(),
        ).justAnswerEmptyAsync();
        when(
          () => addressNavigationFactory.navigateToCustomerAddressFeature(
            manualAddressEnabled: any(named: 'manualAddressEnabled'),
            showConfirmActualAddress: any(named: 'showConfirmActualAddress'),
          ),
        ).thenReturn(
          const AddressFeatureNavigationConfig(identifier: 'identifier'),
        );
      },
      act: (cubit) => cubit.init(),
      verify: (cubit) {
        verify(
          () => navigationProvider.navigateTo(
            any(that: isA<AddressFeatureNavigationConfig>()),
          ),
        ).calledOnce;
      },
    );
  });
}
