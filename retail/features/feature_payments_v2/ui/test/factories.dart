// ignore_for_file: deprecated_member_use

import 'package:tests/tests.dart';
import 'package:wio_app_core_api/money.dart' as money;
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_feature_account_api/models/account_details.dart';

class InternationalBeneficiaryFactory {
  static CrossborderInternationalBeneficiary rand() {
    return CrossborderInternationalBeneficiary(
      id: randomString(),
      type: PaymentType.international,
      subtype: BeneficiarySubtype.wise,
      status: BeneficiaryStatus.available,
      created: DateTime.now(),
      customerId: randomString(),
      legalType: pickRandomly(CrossborderLegalType.values),
      accountHolderName: randomString(),
      nickname: randomString(),
      phoneNumber: randomString(),
      email: randomString(),
      swiftCode: randomString(),
      accountNumber: randomString(),
      countryCode: randomString(),
      countryName: randomString(),
      currencyCode: randomString(),
      residenceCountry: randomString(),
      firstLine: randomString(),
      postCode: randomString(),
      city: randomString(),
      state: randomString(),
    );
  }

  static List<CrossborderInternationalBeneficiary> randList({
    int length = 10,
  }) {
    return List.generate(
      length,
      (index) => InternationalBeneficiaryFactory.rand(),
    ).toList();
  }
}

class TransferRequirementValueAllowedFactory {
  static TransferRequirementValueAllowed rand() {
    return TransferRequirementValueAllowed(
      key: randomString(),
      name: randomString(),
    );
  }

  static List<TransferRequirementValueAllowed> randList({
    int length = 10,
  }) {
    return List.generate(
      length,
      (index) => TransferRequirementValueAllowedFactory.rand(),
    ).toList();
  }
}

class TransferRequirementInfoFactory {
  static TransferRequirementInfo rand() {
    return TransferRequirementInfo(
      key: randomString(),
      displayFormat: randomString(),
      example: randomString(),
      maxLength: randomInt(start: 10, end: 20),
      minLength: randomInt(start: 1, end: 9),
      name: randomString(),
      refreshRequirementsOnChange: randomBool(),
      required: randomBool(),
      type: pickRandomly(TransferRequirementInfoType.values),
      validationAsync: randomString(),
      validationRegexp: randomString(),
      valuesAllowed: TransferRequirementValueAllowedFactory.randList(),
      readOnly: false,
    );
  }

  static List<TransferRequirementInfo> randList({
    int length = 10,
  }) {
    return List.generate(
      length,
      (index) => TransferRequirementInfoFactory.rand(),
    ).toList();
  }
}

class TransferRequirementFactory {
  static TransferRequirement rand() {
    return TransferRequirement(
      key: randomString(),
      name: randomString(),
      group: TransferRequirementInfoFactory.randList(),
    );
  }

  static List<TransferRequirement> randList({
    int length = 10,
  }) {
    return List.generate(
      length,
      (index) => TransferRequirementFactory.rand(),
    ).toList();
  }
}

class TransferRequirementsFactory {
  static TransferRequirements rand() {
    return TransferRequirements(
      provider: randomString(),
      groups: TransferRequirementFactory.randList(),
    );
  }
}

abstract class CountryFactory {
  static PaymentCountry rand() {
    return PaymentCountry(
      code: randomString(),
      name: randomString(),
    );
  }

  static SupportedCountries getSupportedCountries() => const SupportedCountries(
        popular: [
          PaymentCountry(code: 'GB', name: 'United Kingdom'),
          PaymentCountry(code: 'DE', name: 'Germany'),
          PaymentCountry(code: 'IT', name: 'Italy'),
        ],
        all: [
          PaymentCountry(code: 'AL', name: 'Albania'),
          PaymentCountry(code: 'BR', name: 'Brazil'),
          PaymentCountry(code: 'NG', name: 'Nigeria'),
          PaymentCountry(code: 'NU', name: 'Niue'),
          PaymentCountry(code: 'NF', name: 'Norfolk Island'),
          PaymentCountry(code: 'GB-NIR', name: 'Northern Ireland'),
          PaymentCountry(code: 'MP', name: 'Northern Mariana Islands'),
          PaymentCountry(code: 'NO', name: 'Norway'),
          PaymentCountry(code: 'OM', name: 'Oman'),
          PaymentCountry(code: 'PK', name: 'Pakistan'),
          PaymentCountry(code: 'PW', name: 'Palau'),
          PaymentCountry(code: 'PS', name: 'Palestine'),
          PaymentCountry(code: 'PA', name: 'Panama'),
          PaymentCountry(code: 'PG', name: 'Papua New Guinea'),
          PaymentCountry(code: 'PY', name: 'Paraguay'),
          PaymentCountry(code: 'PE', name: 'Peru'),
          PaymentCountry(code: 'PH', name: 'Philippines'),
          PaymentCountry(code: 'PN', name: 'Pitcairn'),
          PaymentCountry(code: 'PL', name: 'Poland'),
          PaymentCountry(code: 'GB', name: 'United Kingdom'),
          PaymentCountry(code: 'DE', name: 'Germany'),
          PaymentCountry(code: 'IT', name: 'Italy'),
        ],
      );
}

abstract class AccountFactory {
  static List<AccountDetails> randList({
    int min = 2,
    int max = 10,
    AccountState state = AccountState.active,
    String currency = 'AED',
  }) =>
      List.generate(
        randomInt(start: min, end: max),
        (_) => rand(state: state, currency: currency),
      );

  static AccountDetails rand({
    AccountState state = AccountState.active,
    String currency = 'AED',
    String? id,
    AccountType? type,
  }) =>
      AccountDetails(
        id: id ?? randomString(),
        name: randomString(),
        nickname: randomString(),
        beneficary: randomString(),
        bankName: randomString(),
        bankAddress: randomString(),
        iban: randomString(),
        type: type ?? AccountType.currentAccount,
        state: state,
        bic: randomString(),
        availableBalance: money.Money.fromNum(100, code: currency),
        lockedBalance: money.Money.fromNum(100, code: currency),
        totalBalance: money.Money.fromNum(200, code: currency),
        rate: 0,
        accruedAmount: money.Money.fromNum(0, code: currency),
        creditedAmount: money.Money.fromNum(0, code: currency),
      );
}

abstract class CurrencyFactory {
  static PaymentCurrency rand({String? currency}) => PaymentCurrency(
        code: currency ?? randomString(),
        name: randomString(),
        state: CurrencyStateType.available,
      );
}

money.Money get fakeAmount => money.Money.fromInt(
      randomInt(),
      code: money.Currency.aed.code,
    );

class InternationalBeneficiaryRequirementsFactory {
  static InternationalBeneficiaryRequirements rand() {
    return InternationalBeneficiaryRequirements(
      accountHodelName: randomString(),
      groups: TransferRequirementFactory.randList(length: 1),
    );
  }
}

abstract final class DirectDebitsFactory {
  static DirectDebitAuthorityDetails directDebitAuthorityDetails() =>
      DirectDebitAuthorityDetails(
        name: randomString(),
        referenceId: randomString(),
        status: DirectDebitAuthorityStatus.completed,
        amountType: DirectDebitAuthorityAmountType.fixed,
        paymentFrequency: DirectDebitAuthorityPaymentFrequency.daily,
        hasChequeImage: true,
        hasDebitActivity: true,
        submittedDate: DateTime.now().subtract(const Duration(days: 2)),
        oic: randomString(),
        originator: randomString(),
        requestingBank: randomString(),
        issuedFor: randomString(),
        startDate: DateTime.now().subtract(const Duration(days: 1, hours: 5)),
        endDate: DateTime.now().add(const Duration(days: 1, hours: 5)),
        minAmount: money.Money.fromNumWithCurrency(100, money.Currency.aed),
        maxAmount: money.Money.fromNumWithCurrency(1000, money.Currency.aed),
      );
}
