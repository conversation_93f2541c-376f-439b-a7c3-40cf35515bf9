import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_payments_v2_ui/feature_payments_v2_ui.dart';
import 'package:wio_feature_payments_v2_ui/src/navigation/index.dart';

class DirectDebitAuthorityDetailsNameBottomSheet extends StatefulWidget {
  const DirectDebitAuthorityDetailsNameBottomSheet({super.key});

  @override
  State<DirectDebitAuthorityDetailsNameBottomSheet> createState() =>
      _DirectDebitAuthorityDetailsNameBottomSheetState();
}

class _DirectDebitAuthorityDetailsNameBottomSheetState
    extends State<DirectDebitAuthorityDetailsNameBottomSheet> {
  late final TextEditingController _controller;

  var _isNameValid = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _controller.addListener(() {
      setState(() {
        _isNameValid = _controller.text.isNotEmpty;
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = PaymentsLocalizations.of(context);

    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Label(
              model: LabelModel(
                text: l10n.directDebitNameBottomSheetTitle,
                textStyle: CompanyTextStylePointer.h3medium,
                color: CompanyColorPointer.primary3,
              ),
            ),
            const Space.vertical(8),
            Label(
              model: LabelModel(
                text: l10n.directDebitNameBottomSheetText,
                textStyle: CompanyTextStylePointer.b2,
                color: CompanyColorPointer.secondary3,
              ),
            ),
            const Space.vertical(16),
            InputField(
              model: InputFieldModel(
                size: InputFieldSize.small,
                hint: '',
                label: l10n.directDebitNameBottomSheetLabel,
                theme: InputFieldTheme.light,
              ),
              controller: _controller,
              autoFocus: true,
              maxLines: null,
              smartDashesType: SmartDashesType.disabled,
              keyboardType: TextInputType.multiline,
              onFieldSubmitted: (_) {},
              textCapitalization: TextCapitalization.sentences,
            ),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 24),
              child: Button(
                model: ButtonModel(
                  title: l10n.directDebitNameBottomSheetCta,
                ),
                onPressed: _isNameValid
                    ? () => Navigator.maybePop(
                          context,
                          DirectDebitAuthorityDetailsNameBottomSheetResult(
                            newName: _controller.text,
                          ),
                        )
                    : null,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
