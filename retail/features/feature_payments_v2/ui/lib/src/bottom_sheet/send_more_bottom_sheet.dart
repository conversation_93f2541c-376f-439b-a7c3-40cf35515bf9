import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_payments_ui/wio_common_feature_payments_ui.dart';
import 'package:wio_feature_payments_v2_ui/src/navigation/configs/index.dart';

class SendMoreBottomSheet extends StatelessWidget {
  final Money transactionLimit;
  final Money transactionDailyLimit;
  final VoidCallback? onFAQPressed;

  const SendMoreBottomSheet({
    required this.transactionLimit,
    required this.transactionDailyLimit,
    this.onFAQPressed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final localization = WioPaymentsLocalizations.of(context);

    return Padding(
      padding: EdgeInsets.only(
        left: Spacing.s5.value,
        right: Spacing.s5.value,
        bottom: Spacing.s4.value,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CompanyIcon(
            CompanyIconModel(
              icon:
                  CompanyPictogramPointer.actions_information.toGraphicAsset(),
              size: CompanyIconSize.xxxxLarge,
              color: CompanyColorPointer.secondary9,
            ),
          ),
          Space.fromSpacingVertical(Spacing.s3),
          Label(
            model: LabelModel(
              text: localization.sendMoreBottomSheetTitle,
              textStyle: CompanyTextStylePointer.h3medium,
              color: CompanyColorPointer.primary3,
              textAlign: LabelTextAlign.center,
            ),
          ),
          Label(
            model: LabelModel(
              text: localization.sendMoreBottomSheetSubTitle,
              textStyle: CompanyTextStylePointer.h3medium,
              color: CompanyColorPointer.primary3,
              textAlign: LabelTextAlign.center,
            ),
          ),
          const Space.vertical(12.0),
          Label(
            model: LabelModel(
              textAlign: LabelTextAlign.center,
              text: localization.sendMoreBottomSheetContent(
                transactionDailyLimit.toCodeOnLeftFormat(),
                transactionLimit.toCodeOnLeftFormat(),
              ),
              textStyle: CompanyTextStylePointer.b2,
              color: CompanyColorPointer.secondary4,
            ),
          ),
          if (onFAQPressed case final VoidCallback onFAQPressed)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Align(
                child: ContextFaqMessage(
                  message: localization.sendMoreBottomSheetFaq,
                  onTap: onFAQPressed,
                ),
              ),
            ),
          const Space.vertical(24.0),
          SizedBox(
            width: double.infinity,
            child: Button(
              onPressed: () => Navigator.pop(
                context,
                SendMoreBottomSheetResult(
                  accept: true,
                ),
              ),
              model: ButtonModel(
                title: localization.sendMoreBottomSheetCta,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
