import 'dart:async';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:logging_api/logging.dart';
import 'package:ui/cubit/base_cubit.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_feature_address_api/wio_common_feature_address_api.dart';
import 'package:wio_common_feature_context_faq_api/configs/context_faq_tags.dart';
import 'package:wio_common_feature_context_faq_api/navigation/bottom_sheets/context_faq_bottom_sheet_navigation_config.dart';
import 'package:wio_common_feature_customer_address_api/index.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_common_feature_payments_ui/wio_common_feature_payments_ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_payments_v2_ui/feature_payments_v2_ui.dart';
import 'package:wio_feature_payments_v2_ui/src/navigation/configs/index.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/add_beneficiary_flow/beneficiary_creation/cubit/beneficiary_creation_v2_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/add_beneficiary_flow/country_selection/cubit/add_bene_country_selection_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/add_beneficiary_flow/currency_selection/cubit/add_bene_currency_selection_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/add_beneficiary_flow/flow/cubit/add_bene_flow_state.dart';
import 'package:wio_feature_status_view_api/feature_status_view.dart';

const _beneficiaryCreationSuccess = 'beneficiary_creation_success';

class AddBeneFlowCubit extends BaseCubit<AddBeneFlowState>
    implements
        AddBeneCountrySelectionDelegate,
        BeneficiaryCreationDelegate,
        AddBeneCurrencySelectionDelegate {
  final PaymentInteractor _interactor;
  final CustomerAddressInteractor _customerAddressInteractor;
  final WiseInteractor _wiseInteractor;
  final NavigationProvider _navigationProvider;
  final Logger _logger;
  final WioPaymentsLocalizations _commonL10n;
  final PaymentsLocalizations _l10n;
  final CommonErrorHandler _commonErrorHandler;
  final FeatureToggleProvider _featureToggleProvider;
  final CustomerAddressFeatureNavigationFactory _addressFeatureFactory;

  /// loading operations via it.
  late AddBeneFlowScreenController _addBeneFlowScreenController;

  AddBeneFlowCubit({
    required NavigationProvider navigationProvider,
    required PaymentInteractor interactor,
    required Logger logger,
    required WioPaymentsLocalizations commonl10n,
    required PaymentsLocalizations l10n,
    required CommonErrorHandler commonErrorHandler,
    required FeatureToggleProvider featureToggleProvider,
    required CustomerAddressInteractor customerAddressInteractor,
    required WiseInteractor wiseInteractor,
    required CustomerAddressFeatureNavigationFactory addressFeatureFactory,
  })  : _navigationProvider = navigationProvider,
        _interactor = interactor,
        _logger = logger,
        _commonL10n = commonl10n,
        _l10n = l10n,
        _commonErrorHandler = commonErrorHandler,
        _featureToggleProvider = featureToggleProvider,
        _customerAddressInteractor = customerAddressInteractor,
        _wiseInteractor = wiseInteractor,
        _addressFeatureFactory = addressFeatureFactory,
        super(const AddBeneFlowState());

  bool get _isBeneficiaryAdditionLimitEnabled => _featureToggleProvider.get(
        PaymentsFeatureToggle.isBeneficiaryAdditionLimitEnabled,
      );

  Future<void> init({PaymentCountry? country}) async {
    try {
      _addBeneFlowScreenController = AddBeneFlowScreenController(
        loading,
      );

      final stages = <AddBeneStage>[];
      if (country != null) {
        stages.add(AddBeneStage.currencySelection(country: country));
      } else {
        stages.add(const AddBeneStage.countrySelection());
      }

      safeEmit(AddBeneFlowState(stages: stages));

      var isAddressExists = await _isAddressExists();
      if (!isAddressExists) isAddressExists = await _setupAddress();
      if (isAddressExists) {
        await safeExecute(_initWiseAccount());
      } else {
        if (!isClosed) _navigationProvider.goBack();
      }
    } on Object catch (error, st) {
      _logger.error(
        'AddBeneFlowCubit init failed',
        error: error,
        stackTrace: st,
      );
      if (!isClosed) _navigationProvider.goBack();
      _commonErrorHandler.handleError(error);
    }
  }

  Future<bool> _isAddressExists() async {
    CustomerAddress? address;
    try {
      address = await _customerAddressInteractor.getPrimaryAddress();
    } on Object catch (error, st) {
      _logger.error(
        'AddBeneFlowCubit Check customer address failed',
        error: error,
        stackTrace: st,
      );
    }

    return address != null;
  }

  Future<bool> _setupAddress() async {
    final conf = _addressFeatureFactory.navigateToCustomerAddressFeature(
      showConfirmActualAddress: true,
    );
    final selectedAddress = await _navigationProvider.navigateTo(conf);
    final isAddressSelected = selectedAddress is AddressSuccessResult;
    _logger.info('Address is ${isAddressSelected ? "" : "not "}selected');
    return isAddressSelected;
  }

  Future<void> _initWiseAccount() async {
    try {
      final wiseStatus = await _wiseInteractor.getWiseStatus();

      if (!wiseStatus.personalProfileExists) {
        await _wiseInteractor.createWiseAccount(WiseAccountType.personal);
      }
    } on Object catch (error, st) {
      _logger.error(
        'AddBeneFlowCubit Create wise account is failed',
        error: error,
        stackTrace: st,
      );
    }
  }

  void _nextStage(AddBeneStage nextStage) {
    emit(state.copyWith(stages: [...state.stages, nextStage]));
  }

  Future<List<PaymentCurrency>> _fetchSupportedCurrencies(
    PaymentCountry country,
  ) async {
    var currencies = <PaymentCurrency>[];

    try {
      loading(true);
      currencies = await _interactor.getSupportedCurrencies(
        countryCode: country.code,
      );
    } on Object catch (error, st) {
      _logger.error(
        '_fetchSupportedCurrencies failure',
        error: error,
        stackTrace: st,
      );
    } finally {
      loading(false);
    }

    return currencies;
  }

  Future<PaymentCurrency?> _selectCurrency(
    List<PaymentCurrency> supportedCurrencies,
    PaymentCountry country,
  ) async {
    final result =
        await _navigationProvider.showBottomSheet<PaymentsBottomSheetResult>(
      PaymentsBottomSheetConfig.selectCurrency(
        supportedCurrencies: supportedCurrencies,
        selectedCountry: country,
      ),
    ) as SelectCurrencyBottomSheetResult?;

    return result?.selectedCurrency;
  }

  void onPopScreen() {
    if (state.stages.length > 1) {
      emit(state.copyWith(stages: [...state.stages]..removeLast()));
    } else {
      _navigationProvider.goBack();
    }
  }

  void closeScreen() {
    _navigationProvider.goBack();
  }

  @override
  Future<void> onBeneficiaryCreated(
    BeneficiaryCreationResult result,
  ) async {
    final beneficiary = result.beneficiary;
    final beneCooldownInfo = beneficiary.cooldownInfo;
    final creationLimit = result.creationLimit;
    if (beneCooldownInfo != null) {
      final successVerificationScreenConfig = StatusViewFeatureNavigationConfig(
        variant: StatusScreenVariant.alternativeSuccess,
        iconRatio: CompanyIconRatio.regular,
        iconTint: CompanyColorPointer.secondary8,
        icon: const GraphicAssetPointer.pictogram(
          CompanyPictogramPointer.validation_success,
        ),
        additionalStatusPageInfo:
            _isBeneficiaryAdditionLimitEnabled && creationLimit != null
                ? AdditionalStatusPageInfo.announcementBanner(
                    model: AnnouncementBannerModel(
                      subtitle: creationLimit.creationLimitMessage,
                      iconColor: CompanyColorPointer.primary2,
                      backgroundColor: CompanyColorPointer.overlay3,
                      icon: CompanyIconPointer.information,
                      suffixIcon: CompanyIconPointer.chevron_right,
                      suffixIconColor: CompanyColorPointer.primary2,
                    ),
                    onButtonPressed: () =>
                        _navigationProvider.showBottomSheet<void>(
                      const ContextFaqBottomSheetNavigationConfig(
                        tags: [
                          ContextFaqTags.retailPaymentsContactLimit,
                        ],
                        fromScreen: _beneficiaryCreationSuccess,
                      ),
                    ),
                  )
                : null,
        title: _commonL10n.beneCooldownAfterCreationSuccessTitle,
        subTitleModel: CompanyRichTextModel(
          text: _l10n.beneCooldownAfterCreationSuccessSubtitleContactSaved,
        ),
        text2: beneCooldownInfo.subtitle,
        bottomConfig: StatusPageBottomConfig.customButton(
          model: ButtonModel(
            title: _commonL10n.close,
            styleOverride: const ButtonStyleOverride(
              active: ButtonStateColorScheme(
                foreground: CompanyColorPointer.primary3,
                background: CompanyColorPointer.surface2,
              ),
              pressed: ButtonStateColorScheme(
                foreground: CompanyColorPointer.primary3,
                background: CompanyColorPointer.primary2,
              ),
              textStyle: CompanyTextStylePointer.b3,
            ),
          ),
        ),
      );

      _navigationProvider.popUntilFirstRoute();

      await _navigationProvider.navigateTo(successVerificationScreenConfig);

      unawaited(
        _navigationProvider.push(
          InternationalBeneficiaryDetailsNavigationConfig(
            beneficiary: beneficiary,
            beneficiaryType: beneficiary.type,
          ),
        ),
      );
    } else {
      _navigationProvider.goBack(beneficiary);
    }
  }

  @override
  Future<void> onCountrySelected(PaymentCountry country) async {
    try {
      final currencies = await _fetchSupportedCurrencies(country);
      if (currencies.isEmpty) throw Exception('Currency can not be empty');
      final currency = await _selectCurrency(
        currencies,
        country,
      );

      if (currency == null) return;

      _nextStage(
        AddBeneStage.beneficiaryCreation(
          country: country,
          currency: currency,
        ),
      );
    } on Object catch (error, st) {
      _logger.error(
        '_onCountrySelected',
        error: error,
        stackTrace: st,
      );
      _commonErrorHandler.handleError(error);
    }
  }

  @override
  void onBeneLimitReached() {
    closeScreen();
  }

  @override
  void onCurrencySelected(PaymentCountry country, PaymentCurrency currency) {
    _nextStage(
      AddBeneStage.beneficiaryCreation(
        country: country,
        currency: currency,
      ),
    );
  }

  AddBeneFlowScreenController getController() => _addBeneFlowScreenController;

  @override
  String toString() => 'AddBeneFlowCubit{}';
}
