import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_common_feature_payments_ui/wio_common_feature_payments_ui.dart';
import 'package:wio_feature_payments_v2_ui/src/extensions/index.dart';

class RequestToPayCell extends StatelessWidget {
  final NPSSRequestToPay request;
  final VoidCallback onRequestTap;

  const RequestToPayCell(
    this.request, {
    required this.onRequestTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final commonPaymentsL10n = WioPaymentsLocalizations.of(context);
    final title = request.description(commonPaymentsL10n);

    return Cell(
      CellModel(
        leading: CellLeadingModel.icon(
          size: CompanyIconSize.large,
          icon: GraphicAssetPointer.icon(request.icon),
          backgroundColor: CompanyColorPointer.surface7,
          smallTile: const TileModel.legacyFlag(
            flag: FlagPointer.AE,
            shape: TileBoxShape.circle,
            size: TileSize.small,
          ),
        ),
        body: CellBodyModel.text(
          subtitle: commonPaymentsL10n.paymentsRequestToPayCellSubtitle,
          title: title,
          subSubtitleColor: CompanyColorPointer.primary4,
        ),
        trailing: CellTrailingModel.text(
          title: request.amountText,
          titleColor: request.amountColor,
          titleMaxLines: 1,
        ),
      ),
      onPressed: onRequestTap,
    );
  }
}
