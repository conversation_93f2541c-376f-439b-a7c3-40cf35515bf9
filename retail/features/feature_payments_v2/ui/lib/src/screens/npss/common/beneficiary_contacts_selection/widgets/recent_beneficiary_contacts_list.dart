import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_common_feature_payments_ui/wio_common_feature_payments_ui.dart';
import 'package:wio_feature_payments_v2_ui/feature_payments_v2_ui.dart';

const _horizontalPadding = 24.0;
const _contentPadding = 4.0;

class RecentBeneficiaryContactsList extends StatelessWidget {
  final List<NPSSBeneficiaryContact> contacts;
  final ValueChanged<NPSSBeneficiaryContact> onSelected;
  final VoidCallback? onNewPressed;

  const RecentBeneficiaryContactsList({
    required this.contacts,
    required this.onSelected,
    this.onNewPressed,
    super.key,
  });

  bool get _hasNewButton => onNewPressed != null;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: _horizontalPadding),
          scrollDirection: Axis.horizontal,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (_hasNewButton) _NewPayee(onNewPressed: onNewPressed),
              ...contacts.asMap().entries.map(
                (e) {
                  final index = e.key;
                  final contact = contacts[index];

                  return _RecentBeneficiaryItem(
                    contact: contact,
                    onSelected: onSelected,
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _NewPayee extends StatelessWidget {
  final VoidCallback? onNewPressed;

  const _NewPayee({this.onNewPressed});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 4, 8, 8),
      child: CircleButton(
        onPressed: onNewPressed,
        model: CircleButtonModel(
          icon: const GraphicAssetPointer.icon(
            CompanyIconPointer.plus,
          ),
          size: CircleButtonSize.xxlarge,
          type: CircleButtonType.secondary,
          negative: true,
          label: PaymentsLocalizations.of(context)
              .dashboardBeneSectionAddNewBeneCta,
          styleOverride: const ButtonStyleOverride(
            active: ButtonStateColorScheme(
              foreground: CompanyColorPointer.primary3,
            ),
            pressed: ButtonStateColorScheme(
              foreground: CompanyColorPointer.primary3,
              background: CompanyColorPointer.background1,
            ),
            textStyle: CompanyTextStylePointer.b3,
          ),
        ),
      ),
    );
  }
}

class _RecentBeneficiaryItem extends StatelessWidget {
  final NPSSBeneficiaryContact contact;

  final ValueChanged<NPSSBeneficiaryContact> onSelected;

  const _RecentBeneficiaryItem({
    required this.contact,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(
        ListBox.borderRadius,
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      child: InkWell(
        splashColor: context.colorStyling
            .fromPointer(CompanyColorPointer.primary1)
            .withValues(alpha: 0.15),
        splashFactory: InkRipple.splashFactory,
        onTap: contact.isEnrolled ? () => onSelected(contact) : null,
        child: RecentBeneficiaryContactListItem(contact),
      ),
    );
  }
}

class RecentBeneficiaryContactListItem extends StatelessWidget {
  final NPSSBeneficiaryContact contact;

  const RecentBeneficiaryContactListItem(
    this.contact, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final nameToShow = contact.displayName;
    final isEnrolled = contact.isEnrolled;
    final isWio = contact.isWio;

    final secondaryIconModel = isWio
        ? const PayeeSecondaryIconModel.icon(
            icon: GraphicAssetPointer.pictogram(
              CompanyPictogramPointer.metaphors_wio_logo,
            ),
          )
        : const PayeeSecondaryIconModel.icon(
            icon: GraphicAssetPointer.icon(
              CompanyIconPointer.aani,
            ),
            backgroundColor: CompanyColorPointer.background3,
            iconColor: CompanyColorPointer.secondary12,
          );

    return Opacity(
      opacity: isEnrolled ? 1.0 : 0.36,
      child: Container(
        width: 82,
        alignment: Alignment.center,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              PayeeCircleAvatar(
                nameToShow,
                secondaryIconModel: isEnrolled ? secondaryIconModel : null,
                size: AvatarSize.large,
                bgColor: CompanyColorPointer.background3,
              ),
              const SizedBox(height: _contentPadding),
              Flexible(
                child: Label(
                  model: LabelModel(
                    text: nameToShow,
                    textStyle: CompanyTextStylePointer.b3,
                    color: CompanyColorPointer.primary3,
                    overflow: LabelTextOverflow.ellipsis,
                    textAlign: LabelTextAlign.center,
                    maxLines: 2,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
