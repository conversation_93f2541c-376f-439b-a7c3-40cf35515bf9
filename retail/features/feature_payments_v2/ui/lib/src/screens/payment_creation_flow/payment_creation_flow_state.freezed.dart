// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_creation_flow_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PaymentCreationFlowState {
  PaymentCreationFlowStep get currentStep => throw _privateConstructorUsedError;
  List<AccountDetails>? get accounts => throw _privateConstructorUsedError;
  AccountDetails? get account => throw _privateConstructorUsedError;
  PaymentCountry? get country => throw _privateConstructorUsedError;
  bool get isChangeCurrencyAvailable => throw _privateConstructorUsedError;
  money.Currency? get defaultCurrency => throw _privateConstructorUsedError;
  money.Money? get amount => throw _privateConstructorUsedError;
  bool get isRecurringTransferEnabled => throw _privateConstructorUsedError;
  RecurringTransferSetup? get recurringTransferSetup =>
      throw _privateConstructorUsedError;
  RailOptionModel? get transferOption => throw _privateConstructorUsedError;
  BeneficiaryCreationResult? get creationResult =>
      throw _privateConstructorUsedError;
  BeneficiaryFlowType get beneficiaryFlowType =>
      throw _privateConstructorUsedError;
  TransferPurpose? get transferPurpose => throw _privateConstructorUsedError;
  TransferSubpurpose? get transferSubpurpose =>
      throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  PaymentType? get type => throw _privateConstructorUsedError;
  bool get isNewPaymentFlowEnabled => throw _privateConstructorUsedError;

  /// Create a copy of PaymentCreationFlowState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentCreationFlowStateCopyWith<PaymentCreationFlowState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentCreationFlowStateCopyWith<$Res> {
  factory $PaymentCreationFlowStateCopyWith(PaymentCreationFlowState value,
          $Res Function(PaymentCreationFlowState) then) =
      _$PaymentCreationFlowStateCopyWithImpl<$Res, PaymentCreationFlowState>;
  @useResult
  $Res call(
      {PaymentCreationFlowStep currentStep,
      List<AccountDetails>? accounts,
      AccountDetails? account,
      PaymentCountry? country,
      bool isChangeCurrencyAvailable,
      money.Currency? defaultCurrency,
      money.Money? amount,
      bool isRecurringTransferEnabled,
      RecurringTransferSetup? recurringTransferSetup,
      RailOptionModel? transferOption,
      BeneficiaryCreationResult? creationResult,
      BeneficiaryFlowType beneficiaryFlowType,
      TransferPurpose? transferPurpose,
      TransferSubpurpose? transferSubpurpose,
      String? notes,
      PaymentType? type,
      bool isNewPaymentFlowEnabled});

  $AccountDetailsCopyWith<$Res>? get account;
  $PaymentCountryCopyWith<$Res>? get country;
  $RecurringTransferSetupCopyWith<$Res>? get recurringTransferSetup;
  $RailOptionModelCopyWith<$Res>? get transferOption;
  $BeneficiaryCreationResultCopyWith<$Res>? get creationResult;
  $TransferPurposeCopyWith<$Res>? get transferPurpose;
  $TransferSubpurposeCopyWith<$Res>? get transferSubpurpose;
}

/// @nodoc
class _$PaymentCreationFlowStateCopyWithImpl<$Res,
        $Val extends PaymentCreationFlowState>
    implements $PaymentCreationFlowStateCopyWith<$Res> {
  _$PaymentCreationFlowStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentCreationFlowState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentStep = null,
    Object? accounts = freezed,
    Object? account = freezed,
    Object? country = freezed,
    Object? isChangeCurrencyAvailable = null,
    Object? defaultCurrency = freezed,
    Object? amount = freezed,
    Object? isRecurringTransferEnabled = null,
    Object? recurringTransferSetup = freezed,
    Object? transferOption = freezed,
    Object? creationResult = freezed,
    Object? beneficiaryFlowType = null,
    Object? transferPurpose = freezed,
    Object? transferSubpurpose = freezed,
    Object? notes = freezed,
    Object? type = freezed,
    Object? isNewPaymentFlowEnabled = null,
  }) {
    return _then(_value.copyWith(
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as PaymentCreationFlowStep,
      accounts: freezed == accounts
          ? _value.accounts
          : accounts // ignore: cast_nullable_to_non_nullable
              as List<AccountDetails>?,
      account: freezed == account
          ? _value.account
          : account // ignore: cast_nullable_to_non_nullable
              as AccountDetails?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as PaymentCountry?,
      isChangeCurrencyAvailable: null == isChangeCurrencyAvailable
          ? _value.isChangeCurrencyAvailable
          : isChangeCurrencyAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
      defaultCurrency: freezed == defaultCurrency
          ? _value.defaultCurrency
          : defaultCurrency // ignore: cast_nullable_to_non_nullable
              as money.Currency?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as money.Money?,
      isRecurringTransferEnabled: null == isRecurringTransferEnabled
          ? _value.isRecurringTransferEnabled
          : isRecurringTransferEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      recurringTransferSetup: freezed == recurringTransferSetup
          ? _value.recurringTransferSetup
          : recurringTransferSetup // ignore: cast_nullable_to_non_nullable
              as RecurringTransferSetup?,
      transferOption: freezed == transferOption
          ? _value.transferOption
          : transferOption // ignore: cast_nullable_to_non_nullable
              as RailOptionModel?,
      creationResult: freezed == creationResult
          ? _value.creationResult
          : creationResult // ignore: cast_nullable_to_non_nullable
              as BeneficiaryCreationResult?,
      beneficiaryFlowType: null == beneficiaryFlowType
          ? _value.beneficiaryFlowType
          : beneficiaryFlowType // ignore: cast_nullable_to_non_nullable
              as BeneficiaryFlowType,
      transferPurpose: freezed == transferPurpose
          ? _value.transferPurpose
          : transferPurpose // ignore: cast_nullable_to_non_nullable
              as TransferPurpose?,
      transferSubpurpose: freezed == transferSubpurpose
          ? _value.transferSubpurpose
          : transferSubpurpose // ignore: cast_nullable_to_non_nullable
              as TransferSubpurpose?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as PaymentType?,
      isNewPaymentFlowEnabled: null == isNewPaymentFlowEnabled
          ? _value.isNewPaymentFlowEnabled
          : isNewPaymentFlowEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of PaymentCreationFlowState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountDetailsCopyWith<$Res>? get account {
    if (_value.account == null) {
      return null;
    }

    return $AccountDetailsCopyWith<$Res>(_value.account!, (value) {
      return _then(_value.copyWith(account: value) as $Val);
    });
  }

  /// Create a copy of PaymentCreationFlowState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaymentCountryCopyWith<$Res>? get country {
    if (_value.country == null) {
      return null;
    }

    return $PaymentCountryCopyWith<$Res>(_value.country!, (value) {
      return _then(_value.copyWith(country: value) as $Val);
    });
  }

  /// Create a copy of PaymentCreationFlowState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RecurringTransferSetupCopyWith<$Res>? get recurringTransferSetup {
    if (_value.recurringTransferSetup == null) {
      return null;
    }

    return $RecurringTransferSetupCopyWith<$Res>(_value.recurringTransferSetup!,
        (value) {
      return _then(_value.copyWith(recurringTransferSetup: value) as $Val);
    });
  }

  /// Create a copy of PaymentCreationFlowState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RailOptionModelCopyWith<$Res>? get transferOption {
    if (_value.transferOption == null) {
      return null;
    }

    return $RailOptionModelCopyWith<$Res>(_value.transferOption!, (value) {
      return _then(_value.copyWith(transferOption: value) as $Val);
    });
  }

  /// Create a copy of PaymentCreationFlowState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BeneficiaryCreationResultCopyWith<$Res>? get creationResult {
    if (_value.creationResult == null) {
      return null;
    }

    return $BeneficiaryCreationResultCopyWith<$Res>(_value.creationResult!,
        (value) {
      return _then(_value.copyWith(creationResult: value) as $Val);
    });
  }

  /// Create a copy of PaymentCreationFlowState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TransferPurposeCopyWith<$Res>? get transferPurpose {
    if (_value.transferPurpose == null) {
      return null;
    }

    return $TransferPurposeCopyWith<$Res>(_value.transferPurpose!, (value) {
      return _then(_value.copyWith(transferPurpose: value) as $Val);
    });
  }

  /// Create a copy of PaymentCreationFlowState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TransferSubpurposeCopyWith<$Res>? get transferSubpurpose {
    if (_value.transferSubpurpose == null) {
      return null;
    }

    return $TransferSubpurposeCopyWith<$Res>(_value.transferSubpurpose!,
        (value) {
      return _then(_value.copyWith(transferSubpurpose: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$InternationalPaymentCreationFlowStateImplCopyWith<$Res>
    implements $PaymentCreationFlowStateCopyWith<$Res> {
  factory _$$InternationalPaymentCreationFlowStateImplCopyWith(
          _$InternationalPaymentCreationFlowStateImpl value,
          $Res Function(_$InternationalPaymentCreationFlowStateImpl) then) =
      __$$InternationalPaymentCreationFlowStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {PaymentCreationFlowStep currentStep,
      List<AccountDetails>? accounts,
      AccountDetails? account,
      PaymentCountry? country,
      bool isChangeCurrencyAvailable,
      money.Currency? defaultCurrency,
      money.Money? amount,
      bool isRecurringTransferEnabled,
      RecurringTransferSetup? recurringTransferSetup,
      RailOptionModel? transferOption,
      BeneficiaryCreationResult? creationResult,
      BeneficiaryFlowType beneficiaryFlowType,
      TransferPurpose? transferPurpose,
      TransferSubpurpose? transferSubpurpose,
      String? notes,
      PaymentType? type,
      bool isNewPaymentFlowEnabled});

  @override
  $AccountDetailsCopyWith<$Res>? get account;
  @override
  $PaymentCountryCopyWith<$Res>? get country;
  @override
  $RecurringTransferSetupCopyWith<$Res>? get recurringTransferSetup;
  @override
  $RailOptionModelCopyWith<$Res>? get transferOption;
  @override
  $BeneficiaryCreationResultCopyWith<$Res>? get creationResult;
  @override
  $TransferPurposeCopyWith<$Res>? get transferPurpose;
  @override
  $TransferSubpurposeCopyWith<$Res>? get transferSubpurpose;
}

/// @nodoc
class __$$InternationalPaymentCreationFlowStateImplCopyWithImpl<$Res>
    extends _$PaymentCreationFlowStateCopyWithImpl<$Res,
        _$InternationalPaymentCreationFlowStateImpl>
    implements _$$InternationalPaymentCreationFlowStateImplCopyWith<$Res> {
  __$$InternationalPaymentCreationFlowStateImplCopyWithImpl(
      _$InternationalPaymentCreationFlowStateImpl _value,
      $Res Function(_$InternationalPaymentCreationFlowStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentCreationFlowState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentStep = null,
    Object? accounts = freezed,
    Object? account = freezed,
    Object? country = freezed,
    Object? isChangeCurrencyAvailable = null,
    Object? defaultCurrency = freezed,
    Object? amount = freezed,
    Object? isRecurringTransferEnabled = null,
    Object? recurringTransferSetup = freezed,
    Object? transferOption = freezed,
    Object? creationResult = freezed,
    Object? beneficiaryFlowType = null,
    Object? transferPurpose = freezed,
    Object? transferSubpurpose = freezed,
    Object? notes = freezed,
    Object? type = freezed,
    Object? isNewPaymentFlowEnabled = null,
  }) {
    return _then(_$InternationalPaymentCreationFlowStateImpl(
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as PaymentCreationFlowStep,
      accounts: freezed == accounts
          ? _value._accounts
          : accounts // ignore: cast_nullable_to_non_nullable
              as List<AccountDetails>?,
      account: freezed == account
          ? _value.account
          : account // ignore: cast_nullable_to_non_nullable
              as AccountDetails?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as PaymentCountry?,
      isChangeCurrencyAvailable: null == isChangeCurrencyAvailable
          ? _value.isChangeCurrencyAvailable
          : isChangeCurrencyAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
      defaultCurrency: freezed == defaultCurrency
          ? _value.defaultCurrency
          : defaultCurrency // ignore: cast_nullable_to_non_nullable
              as money.Currency?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as money.Money?,
      isRecurringTransferEnabled: null == isRecurringTransferEnabled
          ? _value.isRecurringTransferEnabled
          : isRecurringTransferEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      recurringTransferSetup: freezed == recurringTransferSetup
          ? _value.recurringTransferSetup
          : recurringTransferSetup // ignore: cast_nullable_to_non_nullable
              as RecurringTransferSetup?,
      transferOption: freezed == transferOption
          ? _value.transferOption
          : transferOption // ignore: cast_nullable_to_non_nullable
              as RailOptionModel?,
      creationResult: freezed == creationResult
          ? _value.creationResult
          : creationResult // ignore: cast_nullable_to_non_nullable
              as BeneficiaryCreationResult?,
      beneficiaryFlowType: null == beneficiaryFlowType
          ? _value.beneficiaryFlowType
          : beneficiaryFlowType // ignore: cast_nullable_to_non_nullable
              as BeneficiaryFlowType,
      transferPurpose: freezed == transferPurpose
          ? _value.transferPurpose
          : transferPurpose // ignore: cast_nullable_to_non_nullable
              as TransferPurpose?,
      transferSubpurpose: freezed == transferSubpurpose
          ? _value.transferSubpurpose
          : transferSubpurpose // ignore: cast_nullable_to_non_nullable
              as TransferSubpurpose?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as PaymentType?,
      isNewPaymentFlowEnabled: null == isNewPaymentFlowEnabled
          ? _value.isNewPaymentFlowEnabled
          : isNewPaymentFlowEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$InternationalPaymentCreationFlowStateImpl
    extends _InternationalPaymentCreationFlowState {
  const _$InternationalPaymentCreationFlowStateImpl(
      {this.currentStep = PaymentCreationFlowStep.accountSelection,
      final List<AccountDetails>? accounts,
      this.account,
      this.country,
      this.isChangeCurrencyAvailable = true,
      this.defaultCurrency,
      this.amount,
      this.isRecurringTransferEnabled = false,
      this.recurringTransferSetup,
      this.transferOption,
      this.creationResult,
      this.beneficiaryFlowType = BeneficiaryFlowType.createNew,
      this.transferPurpose,
      this.transferSubpurpose,
      this.notes,
      this.type,
      this.isNewPaymentFlowEnabled = false})
      : _accounts = accounts,
        super._();

  @override
  @JsonKey()
  final PaymentCreationFlowStep currentStep;
  final List<AccountDetails>? _accounts;
  @override
  List<AccountDetails>? get accounts {
    final value = _accounts;
    if (value == null) return null;
    if (_accounts is EqualUnmodifiableListView) return _accounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final AccountDetails? account;
  @override
  final PaymentCountry? country;
  @override
  @JsonKey()
  final bool isChangeCurrencyAvailable;
  @override
  final money.Currency? defaultCurrency;
  @override
  final money.Money? amount;
  @override
  @JsonKey()
  final bool isRecurringTransferEnabled;
  @override
  final RecurringTransferSetup? recurringTransferSetup;
  @override
  final RailOptionModel? transferOption;
  @override
  final BeneficiaryCreationResult? creationResult;
  @override
  @JsonKey()
  final BeneficiaryFlowType beneficiaryFlowType;
  @override
  final TransferPurpose? transferPurpose;
  @override
  final TransferSubpurpose? transferSubpurpose;
  @override
  final String? notes;
  @override
  final PaymentType? type;
  @override
  @JsonKey()
  final bool isNewPaymentFlowEnabled;

  @override
  String toString() {
    return 'PaymentCreationFlowState(currentStep: $currentStep, accounts: $accounts, account: $account, country: $country, isChangeCurrencyAvailable: $isChangeCurrencyAvailable, defaultCurrency: $defaultCurrency, amount: $amount, isRecurringTransferEnabled: $isRecurringTransferEnabled, recurringTransferSetup: $recurringTransferSetup, transferOption: $transferOption, creationResult: $creationResult, beneficiaryFlowType: $beneficiaryFlowType, transferPurpose: $transferPurpose, transferSubpurpose: $transferSubpurpose, notes: $notes, type: $type, isNewPaymentFlowEnabled: $isNewPaymentFlowEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InternationalPaymentCreationFlowStateImpl &&
            (identical(other.currentStep, currentStep) ||
                other.currentStep == currentStep) &&
            const DeepCollectionEquality().equals(other._accounts, _accounts) &&
            (identical(other.account, account) || other.account == account) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.isChangeCurrencyAvailable,
                    isChangeCurrencyAvailable) ||
                other.isChangeCurrencyAvailable == isChangeCurrencyAvailable) &&
            (identical(other.defaultCurrency, defaultCurrency) ||
                other.defaultCurrency == defaultCurrency) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.isRecurringTransferEnabled,
                    isRecurringTransferEnabled) ||
                other.isRecurringTransferEnabled ==
                    isRecurringTransferEnabled) &&
            (identical(other.recurringTransferSetup, recurringTransferSetup) ||
                other.recurringTransferSetup == recurringTransferSetup) &&
            (identical(other.transferOption, transferOption) ||
                other.transferOption == transferOption) &&
            (identical(other.creationResult, creationResult) ||
                other.creationResult == creationResult) &&
            (identical(other.beneficiaryFlowType, beneficiaryFlowType) ||
                other.beneficiaryFlowType == beneficiaryFlowType) &&
            (identical(other.transferPurpose, transferPurpose) ||
                other.transferPurpose == transferPurpose) &&
            (identical(other.transferSubpurpose, transferSubpurpose) ||
                other.transferSubpurpose == transferSubpurpose) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(
                    other.isNewPaymentFlowEnabled, isNewPaymentFlowEnabled) ||
                other.isNewPaymentFlowEnabled == isNewPaymentFlowEnabled));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentStep,
      const DeepCollectionEquality().hash(_accounts),
      account,
      country,
      isChangeCurrencyAvailable,
      defaultCurrency,
      amount,
      isRecurringTransferEnabled,
      recurringTransferSetup,
      transferOption,
      creationResult,
      beneficiaryFlowType,
      transferPurpose,
      transferSubpurpose,
      notes,
      type,
      isNewPaymentFlowEnabled);

  /// Create a copy of PaymentCreationFlowState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InternationalPaymentCreationFlowStateImplCopyWith<
          _$InternationalPaymentCreationFlowStateImpl>
      get copyWith => __$$InternationalPaymentCreationFlowStateImplCopyWithImpl<
          _$InternationalPaymentCreationFlowStateImpl>(this, _$identity);
}

abstract class _InternationalPaymentCreationFlowState
    extends PaymentCreationFlowState {
  const factory _InternationalPaymentCreationFlowState(
          {final PaymentCreationFlowStep currentStep,
          final List<AccountDetails>? accounts,
          final AccountDetails? account,
          final PaymentCountry? country,
          final bool isChangeCurrencyAvailable,
          final money.Currency? defaultCurrency,
          final money.Money? amount,
          final bool isRecurringTransferEnabled,
          final RecurringTransferSetup? recurringTransferSetup,
          final RailOptionModel? transferOption,
          final BeneficiaryCreationResult? creationResult,
          final BeneficiaryFlowType beneficiaryFlowType,
          final TransferPurpose? transferPurpose,
          final TransferSubpurpose? transferSubpurpose,
          final String? notes,
          final PaymentType? type,
          final bool isNewPaymentFlowEnabled}) =
      _$InternationalPaymentCreationFlowStateImpl;
  const _InternationalPaymentCreationFlowState._() : super._();

  @override
  PaymentCreationFlowStep get currentStep;
  @override
  List<AccountDetails>? get accounts;
  @override
  AccountDetails? get account;
  @override
  PaymentCountry? get country;
  @override
  bool get isChangeCurrencyAvailable;
  @override
  money.Currency? get defaultCurrency;
  @override
  money.Money? get amount;
  @override
  bool get isRecurringTransferEnabled;
  @override
  RecurringTransferSetup? get recurringTransferSetup;
  @override
  RailOptionModel? get transferOption;
  @override
  BeneficiaryCreationResult? get creationResult;
  @override
  BeneficiaryFlowType get beneficiaryFlowType;
  @override
  TransferPurpose? get transferPurpose;
  @override
  TransferSubpurpose? get transferSubpurpose;
  @override
  String? get notes;
  @override
  PaymentType? get type;
  @override
  bool get isNewPaymentFlowEnabled;

  /// Create a copy of PaymentCreationFlowState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InternationalPaymentCreationFlowStateImplCopyWith<
          _$InternationalPaymentCreationFlowStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$PaymentCreationFlowSelectResult {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetails account) accountSelection,
    required TResult Function(PaymentCountry country,
            money.Currency? defaultCurrency, bool isChangeCurrencyAvailable)
        countrySelection,
    required TResult Function(money.Money amount, RecurringTransferSetup? setup)
        currencySelection,
    required TResult Function(RailOptionModel transferOption) railsSelection,
    required TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)
        beneficiarySelection,
    required TResult Function(BeneficiaryCreationResult creationResult)
        beneficiaryCreation,
    required TResult Function(TransferPurpose transferPurpose) purposeSelection,
    required TResult Function(TransferSubpurpose transferSubpurpose)
        subpurposeSelection,
    required TResult Function(String? notes) noteCreation,
    required TResult Function(CrossborderInternationalBeneficiary beneficiary)
        additionalBeneficiaryInfo,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetails account)? accountSelection,
    TResult? Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult? Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult? Function(RailOptionModel transferOption)? railsSelection,
    TResult? Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult? Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult? Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult? Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult? Function(String? notes)? noteCreation,
    TResult? Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetails account)? accountSelection,
    TResult Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult Function(RailOptionModel transferOption)? railsSelection,
    TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult Function(String? notes)? noteCreation,
    TResult Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AccountStepSelectResult value) accountSelection,
    required TResult Function(_CountryStepSelectResult value) countrySelection,
    required TResult Function(_CurrencySelectionSelectResult value)
        currencySelection,
    required TResult Function(_RailsSelectionSelectResult value) railsSelection,
    required TResult Function(_BeneficiarySelectionSelectResult value)
        beneficiarySelection,
    required TResult Function(_BeneficiaryCreationSelectResult value)
        beneficiaryCreation,
    required TResult Function(_PurposeSelectionSelectResult value)
        purposeSelection,
    required TResult Function(_SubpurposeSelectionSelectResult value)
        subpurposeSelection,
    required TResult Function(_NoteCreationSelectResult value) noteCreation,
    required TResult Function(_AdditionalBeneficiaryInfoResult value)
        additionalBeneficiaryInfo,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AccountStepSelectResult value)? accountSelection,
    TResult? Function(_CountryStepSelectResult value)? countrySelection,
    TResult? Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult? Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult? Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult? Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult? Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult? Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult? Function(_NoteCreationSelectResult value)? noteCreation,
    TResult? Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AccountStepSelectResult value)? accountSelection,
    TResult Function(_CountryStepSelectResult value)? countrySelection,
    TResult Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult Function(_NoteCreationSelectResult value)? noteCreation,
    TResult Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentCreationFlowSelectResultCopyWith<$Res> {
  factory $PaymentCreationFlowSelectResultCopyWith(
          PaymentCreationFlowSelectResult value,
          $Res Function(PaymentCreationFlowSelectResult) then) =
      _$PaymentCreationFlowSelectResultCopyWithImpl<$Res,
          PaymentCreationFlowSelectResult>;
}

/// @nodoc
class _$PaymentCreationFlowSelectResultCopyWithImpl<$Res,
        $Val extends PaymentCreationFlowSelectResult>
    implements $PaymentCreationFlowSelectResultCopyWith<$Res> {
  _$PaymentCreationFlowSelectResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$AccountStepSelectResultImplCopyWith<$Res> {
  factory _$$AccountStepSelectResultImplCopyWith(
          _$AccountStepSelectResultImpl value,
          $Res Function(_$AccountStepSelectResultImpl) then) =
      __$$AccountStepSelectResultImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AccountDetails account});

  $AccountDetailsCopyWith<$Res> get account;
}

/// @nodoc
class __$$AccountStepSelectResultImplCopyWithImpl<$Res>
    extends _$PaymentCreationFlowSelectResultCopyWithImpl<$Res,
        _$AccountStepSelectResultImpl>
    implements _$$AccountStepSelectResultImplCopyWith<$Res> {
  __$$AccountStepSelectResultImplCopyWithImpl(
      _$AccountStepSelectResultImpl _value,
      $Res Function(_$AccountStepSelectResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? account = null,
  }) {
    return _then(_$AccountStepSelectResultImpl(
      null == account
          ? _value.account
          : account // ignore: cast_nullable_to_non_nullable
              as AccountDetails,
    ));
  }

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountDetailsCopyWith<$Res> get account {
    return $AccountDetailsCopyWith<$Res>(_value.account, (value) {
      return _then(_value.copyWith(account: value));
    });
  }
}

/// @nodoc

class _$AccountStepSelectResultImpl extends _AccountStepSelectResult {
  _$AccountStepSelectResultImpl(this.account) : super._();

  @override
  final AccountDetails account;

  @override
  String toString() {
    return 'PaymentCreationFlowSelectResult.accountSelection(account: $account)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountStepSelectResultImpl &&
            (identical(other.account, account) || other.account == account));
  }

  @override
  int get hashCode => Object.hash(runtimeType, account);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountStepSelectResultImplCopyWith<_$AccountStepSelectResultImpl>
      get copyWith => __$$AccountStepSelectResultImplCopyWithImpl<
          _$AccountStepSelectResultImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetails account) accountSelection,
    required TResult Function(PaymentCountry country,
            money.Currency? defaultCurrency, bool isChangeCurrencyAvailable)
        countrySelection,
    required TResult Function(money.Money amount, RecurringTransferSetup? setup)
        currencySelection,
    required TResult Function(RailOptionModel transferOption) railsSelection,
    required TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)
        beneficiarySelection,
    required TResult Function(BeneficiaryCreationResult creationResult)
        beneficiaryCreation,
    required TResult Function(TransferPurpose transferPurpose) purposeSelection,
    required TResult Function(TransferSubpurpose transferSubpurpose)
        subpurposeSelection,
    required TResult Function(String? notes) noteCreation,
    required TResult Function(CrossborderInternationalBeneficiary beneficiary)
        additionalBeneficiaryInfo,
  }) {
    return accountSelection(account);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetails account)? accountSelection,
    TResult? Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult? Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult? Function(RailOptionModel transferOption)? railsSelection,
    TResult? Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult? Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult? Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult? Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult? Function(String? notes)? noteCreation,
    TResult? Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
  }) {
    return accountSelection?.call(account);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetails account)? accountSelection,
    TResult Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult Function(RailOptionModel transferOption)? railsSelection,
    TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult Function(String? notes)? noteCreation,
    TResult Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (accountSelection != null) {
      return accountSelection(account);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AccountStepSelectResult value) accountSelection,
    required TResult Function(_CountryStepSelectResult value) countrySelection,
    required TResult Function(_CurrencySelectionSelectResult value)
        currencySelection,
    required TResult Function(_RailsSelectionSelectResult value) railsSelection,
    required TResult Function(_BeneficiarySelectionSelectResult value)
        beneficiarySelection,
    required TResult Function(_BeneficiaryCreationSelectResult value)
        beneficiaryCreation,
    required TResult Function(_PurposeSelectionSelectResult value)
        purposeSelection,
    required TResult Function(_SubpurposeSelectionSelectResult value)
        subpurposeSelection,
    required TResult Function(_NoteCreationSelectResult value) noteCreation,
    required TResult Function(_AdditionalBeneficiaryInfoResult value)
        additionalBeneficiaryInfo,
  }) {
    return accountSelection(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AccountStepSelectResult value)? accountSelection,
    TResult? Function(_CountryStepSelectResult value)? countrySelection,
    TResult? Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult? Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult? Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult? Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult? Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult? Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult? Function(_NoteCreationSelectResult value)? noteCreation,
    TResult? Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
  }) {
    return accountSelection?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AccountStepSelectResult value)? accountSelection,
    TResult Function(_CountryStepSelectResult value)? countrySelection,
    TResult Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult Function(_NoteCreationSelectResult value)? noteCreation,
    TResult Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (accountSelection != null) {
      return accountSelection(this);
    }
    return orElse();
  }
}

abstract class _AccountStepSelectResult
    extends PaymentCreationFlowSelectResult {
  factory _AccountStepSelectResult(final AccountDetails account) =
      _$AccountStepSelectResultImpl;
  _AccountStepSelectResult._() : super._();

  AccountDetails get account;

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountStepSelectResultImplCopyWith<_$AccountStepSelectResultImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CountryStepSelectResultImplCopyWith<$Res> {
  factory _$$CountryStepSelectResultImplCopyWith(
          _$CountryStepSelectResultImpl value,
          $Res Function(_$CountryStepSelectResultImpl) then) =
      __$$CountryStepSelectResultImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {PaymentCountry country,
      money.Currency? defaultCurrency,
      bool isChangeCurrencyAvailable});

  $PaymentCountryCopyWith<$Res> get country;
}

/// @nodoc
class __$$CountryStepSelectResultImplCopyWithImpl<$Res>
    extends _$PaymentCreationFlowSelectResultCopyWithImpl<$Res,
        _$CountryStepSelectResultImpl>
    implements _$$CountryStepSelectResultImplCopyWith<$Res> {
  __$$CountryStepSelectResultImplCopyWithImpl(
      _$CountryStepSelectResultImpl _value,
      $Res Function(_$CountryStepSelectResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? country = null,
    Object? defaultCurrency = freezed,
    Object? isChangeCurrencyAvailable = null,
  }) {
    return _then(_$CountryStepSelectResultImpl(
      null == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as PaymentCountry,
      defaultCurrency: freezed == defaultCurrency
          ? _value.defaultCurrency
          : defaultCurrency // ignore: cast_nullable_to_non_nullable
              as money.Currency?,
      isChangeCurrencyAvailable: null == isChangeCurrencyAvailable
          ? _value.isChangeCurrencyAvailable
          : isChangeCurrencyAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaymentCountryCopyWith<$Res> get country {
    return $PaymentCountryCopyWith<$Res>(_value.country, (value) {
      return _then(_value.copyWith(country: value));
    });
  }
}

/// @nodoc

class _$CountryStepSelectResultImpl extends _CountryStepSelectResult {
  _$CountryStepSelectResultImpl(this.country,
      {this.defaultCurrency, this.isChangeCurrencyAvailable = true})
      : super._();

  @override
  final PaymentCountry country;
  @override
  final money.Currency? defaultCurrency;
  @override
  @JsonKey()
  final bool isChangeCurrencyAvailable;

  @override
  String toString() {
    return 'PaymentCreationFlowSelectResult.countrySelection(country: $country, defaultCurrency: $defaultCurrency, isChangeCurrencyAvailable: $isChangeCurrencyAvailable)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CountryStepSelectResultImpl &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.defaultCurrency, defaultCurrency) ||
                other.defaultCurrency == defaultCurrency) &&
            (identical(other.isChangeCurrencyAvailable,
                    isChangeCurrencyAvailable) ||
                other.isChangeCurrencyAvailable == isChangeCurrencyAvailable));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, country, defaultCurrency, isChangeCurrencyAvailable);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CountryStepSelectResultImplCopyWith<_$CountryStepSelectResultImpl>
      get copyWith => __$$CountryStepSelectResultImplCopyWithImpl<
          _$CountryStepSelectResultImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetails account) accountSelection,
    required TResult Function(PaymentCountry country,
            money.Currency? defaultCurrency, bool isChangeCurrencyAvailable)
        countrySelection,
    required TResult Function(money.Money amount, RecurringTransferSetup? setup)
        currencySelection,
    required TResult Function(RailOptionModel transferOption) railsSelection,
    required TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)
        beneficiarySelection,
    required TResult Function(BeneficiaryCreationResult creationResult)
        beneficiaryCreation,
    required TResult Function(TransferPurpose transferPurpose) purposeSelection,
    required TResult Function(TransferSubpurpose transferSubpurpose)
        subpurposeSelection,
    required TResult Function(String? notes) noteCreation,
    required TResult Function(CrossborderInternationalBeneficiary beneficiary)
        additionalBeneficiaryInfo,
  }) {
    return countrySelection(
        country, defaultCurrency, isChangeCurrencyAvailable);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetails account)? accountSelection,
    TResult? Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult? Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult? Function(RailOptionModel transferOption)? railsSelection,
    TResult? Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult? Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult? Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult? Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult? Function(String? notes)? noteCreation,
    TResult? Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
  }) {
    return countrySelection?.call(
        country, defaultCurrency, isChangeCurrencyAvailable);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetails account)? accountSelection,
    TResult Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult Function(RailOptionModel transferOption)? railsSelection,
    TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult Function(String? notes)? noteCreation,
    TResult Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (countrySelection != null) {
      return countrySelection(
          country, defaultCurrency, isChangeCurrencyAvailable);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AccountStepSelectResult value) accountSelection,
    required TResult Function(_CountryStepSelectResult value) countrySelection,
    required TResult Function(_CurrencySelectionSelectResult value)
        currencySelection,
    required TResult Function(_RailsSelectionSelectResult value) railsSelection,
    required TResult Function(_BeneficiarySelectionSelectResult value)
        beneficiarySelection,
    required TResult Function(_BeneficiaryCreationSelectResult value)
        beneficiaryCreation,
    required TResult Function(_PurposeSelectionSelectResult value)
        purposeSelection,
    required TResult Function(_SubpurposeSelectionSelectResult value)
        subpurposeSelection,
    required TResult Function(_NoteCreationSelectResult value) noteCreation,
    required TResult Function(_AdditionalBeneficiaryInfoResult value)
        additionalBeneficiaryInfo,
  }) {
    return countrySelection(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AccountStepSelectResult value)? accountSelection,
    TResult? Function(_CountryStepSelectResult value)? countrySelection,
    TResult? Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult? Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult? Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult? Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult? Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult? Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult? Function(_NoteCreationSelectResult value)? noteCreation,
    TResult? Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
  }) {
    return countrySelection?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AccountStepSelectResult value)? accountSelection,
    TResult Function(_CountryStepSelectResult value)? countrySelection,
    TResult Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult Function(_NoteCreationSelectResult value)? noteCreation,
    TResult Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (countrySelection != null) {
      return countrySelection(this);
    }
    return orElse();
  }
}

abstract class _CountryStepSelectResult
    extends PaymentCreationFlowSelectResult {
  factory _CountryStepSelectResult(final PaymentCountry country,
      {final money.Currency? defaultCurrency,
      final bool isChangeCurrencyAvailable}) = _$CountryStepSelectResultImpl;
  _CountryStepSelectResult._() : super._();

  PaymentCountry get country;
  money.Currency? get defaultCurrency;
  bool get isChangeCurrencyAvailable;

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CountryStepSelectResultImplCopyWith<_$CountryStepSelectResultImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CurrencySelectionSelectResultImplCopyWith<$Res> {
  factory _$$CurrencySelectionSelectResultImplCopyWith(
          _$CurrencySelectionSelectResultImpl value,
          $Res Function(_$CurrencySelectionSelectResultImpl) then) =
      __$$CurrencySelectionSelectResultImplCopyWithImpl<$Res>;
  @useResult
  $Res call({money.Money amount, RecurringTransferSetup? setup});

  $RecurringTransferSetupCopyWith<$Res>? get setup;
}

/// @nodoc
class __$$CurrencySelectionSelectResultImplCopyWithImpl<$Res>
    extends _$PaymentCreationFlowSelectResultCopyWithImpl<$Res,
        _$CurrencySelectionSelectResultImpl>
    implements _$$CurrencySelectionSelectResultImplCopyWith<$Res> {
  __$$CurrencySelectionSelectResultImplCopyWithImpl(
      _$CurrencySelectionSelectResultImpl _value,
      $Res Function(_$CurrencySelectionSelectResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? setup = freezed,
  }) {
    return _then(_$CurrencySelectionSelectResultImpl(
      null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as money.Money,
      freezed == setup
          ? _value.setup
          : setup // ignore: cast_nullable_to_non_nullable
              as RecurringTransferSetup?,
    ));
  }

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RecurringTransferSetupCopyWith<$Res>? get setup {
    if (_value.setup == null) {
      return null;
    }

    return $RecurringTransferSetupCopyWith<$Res>(_value.setup!, (value) {
      return _then(_value.copyWith(setup: value));
    });
  }
}

/// @nodoc

class _$CurrencySelectionSelectResultImpl
    extends _CurrencySelectionSelectResult {
  _$CurrencySelectionSelectResultImpl(this.amount, [this.setup]) : super._();

  @override
  final money.Money amount;
  @override
  final RecurringTransferSetup? setup;

  @override
  String toString() {
    return 'PaymentCreationFlowSelectResult.currencySelection(amount: $amount, setup: $setup)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CurrencySelectionSelectResultImpl &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.setup, setup) || other.setup == setup));
  }

  @override
  int get hashCode => Object.hash(runtimeType, amount, setup);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CurrencySelectionSelectResultImplCopyWith<
          _$CurrencySelectionSelectResultImpl>
      get copyWith => __$$CurrencySelectionSelectResultImplCopyWithImpl<
          _$CurrencySelectionSelectResultImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetails account) accountSelection,
    required TResult Function(PaymentCountry country,
            money.Currency? defaultCurrency, bool isChangeCurrencyAvailable)
        countrySelection,
    required TResult Function(money.Money amount, RecurringTransferSetup? setup)
        currencySelection,
    required TResult Function(RailOptionModel transferOption) railsSelection,
    required TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)
        beneficiarySelection,
    required TResult Function(BeneficiaryCreationResult creationResult)
        beneficiaryCreation,
    required TResult Function(TransferPurpose transferPurpose) purposeSelection,
    required TResult Function(TransferSubpurpose transferSubpurpose)
        subpurposeSelection,
    required TResult Function(String? notes) noteCreation,
    required TResult Function(CrossborderInternationalBeneficiary beneficiary)
        additionalBeneficiaryInfo,
  }) {
    return currencySelection(amount, setup);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetails account)? accountSelection,
    TResult? Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult? Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult? Function(RailOptionModel transferOption)? railsSelection,
    TResult? Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult? Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult? Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult? Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult? Function(String? notes)? noteCreation,
    TResult? Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
  }) {
    return currencySelection?.call(amount, setup);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetails account)? accountSelection,
    TResult Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult Function(RailOptionModel transferOption)? railsSelection,
    TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult Function(String? notes)? noteCreation,
    TResult Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (currencySelection != null) {
      return currencySelection(amount, setup);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AccountStepSelectResult value) accountSelection,
    required TResult Function(_CountryStepSelectResult value) countrySelection,
    required TResult Function(_CurrencySelectionSelectResult value)
        currencySelection,
    required TResult Function(_RailsSelectionSelectResult value) railsSelection,
    required TResult Function(_BeneficiarySelectionSelectResult value)
        beneficiarySelection,
    required TResult Function(_BeneficiaryCreationSelectResult value)
        beneficiaryCreation,
    required TResult Function(_PurposeSelectionSelectResult value)
        purposeSelection,
    required TResult Function(_SubpurposeSelectionSelectResult value)
        subpurposeSelection,
    required TResult Function(_NoteCreationSelectResult value) noteCreation,
    required TResult Function(_AdditionalBeneficiaryInfoResult value)
        additionalBeneficiaryInfo,
  }) {
    return currencySelection(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AccountStepSelectResult value)? accountSelection,
    TResult? Function(_CountryStepSelectResult value)? countrySelection,
    TResult? Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult? Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult? Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult? Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult? Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult? Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult? Function(_NoteCreationSelectResult value)? noteCreation,
    TResult? Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
  }) {
    return currencySelection?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AccountStepSelectResult value)? accountSelection,
    TResult Function(_CountryStepSelectResult value)? countrySelection,
    TResult Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult Function(_NoteCreationSelectResult value)? noteCreation,
    TResult Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (currencySelection != null) {
      return currencySelection(this);
    }
    return orElse();
  }
}

abstract class _CurrencySelectionSelectResult
    extends PaymentCreationFlowSelectResult {
  factory _CurrencySelectionSelectResult(final money.Money amount,
          [final RecurringTransferSetup? setup]) =
      _$CurrencySelectionSelectResultImpl;
  _CurrencySelectionSelectResult._() : super._();

  money.Money get amount;
  RecurringTransferSetup? get setup;

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CurrencySelectionSelectResultImplCopyWith<
          _$CurrencySelectionSelectResultImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RailsSelectionSelectResultImplCopyWith<$Res> {
  factory _$$RailsSelectionSelectResultImplCopyWith(
          _$RailsSelectionSelectResultImpl value,
          $Res Function(_$RailsSelectionSelectResultImpl) then) =
      __$$RailsSelectionSelectResultImplCopyWithImpl<$Res>;
  @useResult
  $Res call({RailOptionModel transferOption});

  $RailOptionModelCopyWith<$Res> get transferOption;
}

/// @nodoc
class __$$RailsSelectionSelectResultImplCopyWithImpl<$Res>
    extends _$PaymentCreationFlowSelectResultCopyWithImpl<$Res,
        _$RailsSelectionSelectResultImpl>
    implements _$$RailsSelectionSelectResultImplCopyWith<$Res> {
  __$$RailsSelectionSelectResultImplCopyWithImpl(
      _$RailsSelectionSelectResultImpl _value,
      $Res Function(_$RailsSelectionSelectResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transferOption = null,
  }) {
    return _then(_$RailsSelectionSelectResultImpl(
      null == transferOption
          ? _value.transferOption
          : transferOption // ignore: cast_nullable_to_non_nullable
              as RailOptionModel,
    ));
  }

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RailOptionModelCopyWith<$Res> get transferOption {
    return $RailOptionModelCopyWith<$Res>(_value.transferOption, (value) {
      return _then(_value.copyWith(transferOption: value));
    });
  }
}

/// @nodoc

class _$RailsSelectionSelectResultImpl extends _RailsSelectionSelectResult {
  _$RailsSelectionSelectResultImpl(this.transferOption) : super._();

  @override
  final RailOptionModel transferOption;

  @override
  String toString() {
    return 'PaymentCreationFlowSelectResult.railsSelection(transferOption: $transferOption)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RailsSelectionSelectResultImpl &&
            (identical(other.transferOption, transferOption) ||
                other.transferOption == transferOption));
  }

  @override
  int get hashCode => Object.hash(runtimeType, transferOption);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RailsSelectionSelectResultImplCopyWith<_$RailsSelectionSelectResultImpl>
      get copyWith => __$$RailsSelectionSelectResultImplCopyWithImpl<
          _$RailsSelectionSelectResultImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetails account) accountSelection,
    required TResult Function(PaymentCountry country,
            money.Currency? defaultCurrency, bool isChangeCurrencyAvailable)
        countrySelection,
    required TResult Function(money.Money amount, RecurringTransferSetup? setup)
        currencySelection,
    required TResult Function(RailOptionModel transferOption) railsSelection,
    required TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)
        beneficiarySelection,
    required TResult Function(BeneficiaryCreationResult creationResult)
        beneficiaryCreation,
    required TResult Function(TransferPurpose transferPurpose) purposeSelection,
    required TResult Function(TransferSubpurpose transferSubpurpose)
        subpurposeSelection,
    required TResult Function(String? notes) noteCreation,
    required TResult Function(CrossborderInternationalBeneficiary beneficiary)
        additionalBeneficiaryInfo,
  }) {
    return railsSelection(transferOption);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetails account)? accountSelection,
    TResult? Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult? Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult? Function(RailOptionModel transferOption)? railsSelection,
    TResult? Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult? Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult? Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult? Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult? Function(String? notes)? noteCreation,
    TResult? Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
  }) {
    return railsSelection?.call(transferOption);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetails account)? accountSelection,
    TResult Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult Function(RailOptionModel transferOption)? railsSelection,
    TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult Function(String? notes)? noteCreation,
    TResult Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (railsSelection != null) {
      return railsSelection(transferOption);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AccountStepSelectResult value) accountSelection,
    required TResult Function(_CountryStepSelectResult value) countrySelection,
    required TResult Function(_CurrencySelectionSelectResult value)
        currencySelection,
    required TResult Function(_RailsSelectionSelectResult value) railsSelection,
    required TResult Function(_BeneficiarySelectionSelectResult value)
        beneficiarySelection,
    required TResult Function(_BeneficiaryCreationSelectResult value)
        beneficiaryCreation,
    required TResult Function(_PurposeSelectionSelectResult value)
        purposeSelection,
    required TResult Function(_SubpurposeSelectionSelectResult value)
        subpurposeSelection,
    required TResult Function(_NoteCreationSelectResult value) noteCreation,
    required TResult Function(_AdditionalBeneficiaryInfoResult value)
        additionalBeneficiaryInfo,
  }) {
    return railsSelection(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AccountStepSelectResult value)? accountSelection,
    TResult? Function(_CountryStepSelectResult value)? countrySelection,
    TResult? Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult? Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult? Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult? Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult? Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult? Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult? Function(_NoteCreationSelectResult value)? noteCreation,
    TResult? Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
  }) {
    return railsSelection?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AccountStepSelectResult value)? accountSelection,
    TResult Function(_CountryStepSelectResult value)? countrySelection,
    TResult Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult Function(_NoteCreationSelectResult value)? noteCreation,
    TResult Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (railsSelection != null) {
      return railsSelection(this);
    }
    return orElse();
  }
}

abstract class _RailsSelectionSelectResult
    extends PaymentCreationFlowSelectResult {
  factory _RailsSelectionSelectResult(final RailOptionModel transferOption) =
      _$RailsSelectionSelectResultImpl;
  _RailsSelectionSelectResult._() : super._();

  RailOptionModel get transferOption;

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RailsSelectionSelectResultImplCopyWith<_$RailsSelectionSelectResultImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BeneficiarySelectionSelectResultImplCopyWith<$Res> {
  factory _$$BeneficiarySelectionSelectResultImplCopyWith(
          _$BeneficiarySelectionSelectResultImpl value,
          $Res Function(_$BeneficiarySelectionSelectResultImpl) then) =
      __$$BeneficiarySelectionSelectResultImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {BeneficiaryFlowType flowType,
      CrossborderInternationalBeneficiary? beneficiary});

  $CrossborderInternationalBeneficiaryCopyWith<$Res>? get beneficiary;
}

/// @nodoc
class __$$BeneficiarySelectionSelectResultImplCopyWithImpl<$Res>
    extends _$PaymentCreationFlowSelectResultCopyWithImpl<$Res,
        _$BeneficiarySelectionSelectResultImpl>
    implements _$$BeneficiarySelectionSelectResultImplCopyWith<$Res> {
  __$$BeneficiarySelectionSelectResultImplCopyWithImpl(
      _$BeneficiarySelectionSelectResultImpl _value,
      $Res Function(_$BeneficiarySelectionSelectResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flowType = null,
    Object? beneficiary = freezed,
  }) {
    return _then(_$BeneficiarySelectionSelectResultImpl(
      flowType: null == flowType
          ? _value.flowType
          : flowType // ignore: cast_nullable_to_non_nullable
              as BeneficiaryFlowType,
      beneficiary: freezed == beneficiary
          ? _value.beneficiary
          : beneficiary // ignore: cast_nullable_to_non_nullable
              as CrossborderInternationalBeneficiary?,
    ));
  }

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CrossborderInternationalBeneficiaryCopyWith<$Res>? get beneficiary {
    if (_value.beneficiary == null) {
      return null;
    }

    return $CrossborderInternationalBeneficiaryCopyWith<$Res>(
        _value.beneficiary!, (value) {
      return _then(_value.copyWith(beneficiary: value));
    });
  }
}

/// @nodoc

class _$BeneficiarySelectionSelectResultImpl
    extends _BeneficiarySelectionSelectResult {
  _$BeneficiarySelectionSelectResultImpl(
      {required this.flowType, this.beneficiary})
      : super._();

  @override
  final BeneficiaryFlowType flowType;
  @override
  final CrossborderInternationalBeneficiary? beneficiary;

  @override
  String toString() {
    return 'PaymentCreationFlowSelectResult.beneficiarySelection(flowType: $flowType, beneficiary: $beneficiary)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BeneficiarySelectionSelectResultImpl &&
            (identical(other.flowType, flowType) ||
                other.flowType == flowType) &&
            (identical(other.beneficiary, beneficiary) ||
                other.beneficiary == beneficiary));
  }

  @override
  int get hashCode => Object.hash(runtimeType, flowType, beneficiary);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BeneficiarySelectionSelectResultImplCopyWith<
          _$BeneficiarySelectionSelectResultImpl>
      get copyWith => __$$BeneficiarySelectionSelectResultImplCopyWithImpl<
          _$BeneficiarySelectionSelectResultImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetails account) accountSelection,
    required TResult Function(PaymentCountry country,
            money.Currency? defaultCurrency, bool isChangeCurrencyAvailable)
        countrySelection,
    required TResult Function(money.Money amount, RecurringTransferSetup? setup)
        currencySelection,
    required TResult Function(RailOptionModel transferOption) railsSelection,
    required TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)
        beneficiarySelection,
    required TResult Function(BeneficiaryCreationResult creationResult)
        beneficiaryCreation,
    required TResult Function(TransferPurpose transferPurpose) purposeSelection,
    required TResult Function(TransferSubpurpose transferSubpurpose)
        subpurposeSelection,
    required TResult Function(String? notes) noteCreation,
    required TResult Function(CrossborderInternationalBeneficiary beneficiary)
        additionalBeneficiaryInfo,
  }) {
    return beneficiarySelection(flowType, beneficiary);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetails account)? accountSelection,
    TResult? Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult? Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult? Function(RailOptionModel transferOption)? railsSelection,
    TResult? Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult? Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult? Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult? Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult? Function(String? notes)? noteCreation,
    TResult? Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
  }) {
    return beneficiarySelection?.call(flowType, beneficiary);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetails account)? accountSelection,
    TResult Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult Function(RailOptionModel transferOption)? railsSelection,
    TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult Function(String? notes)? noteCreation,
    TResult Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (beneficiarySelection != null) {
      return beneficiarySelection(flowType, beneficiary);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AccountStepSelectResult value) accountSelection,
    required TResult Function(_CountryStepSelectResult value) countrySelection,
    required TResult Function(_CurrencySelectionSelectResult value)
        currencySelection,
    required TResult Function(_RailsSelectionSelectResult value) railsSelection,
    required TResult Function(_BeneficiarySelectionSelectResult value)
        beneficiarySelection,
    required TResult Function(_BeneficiaryCreationSelectResult value)
        beneficiaryCreation,
    required TResult Function(_PurposeSelectionSelectResult value)
        purposeSelection,
    required TResult Function(_SubpurposeSelectionSelectResult value)
        subpurposeSelection,
    required TResult Function(_NoteCreationSelectResult value) noteCreation,
    required TResult Function(_AdditionalBeneficiaryInfoResult value)
        additionalBeneficiaryInfo,
  }) {
    return beneficiarySelection(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AccountStepSelectResult value)? accountSelection,
    TResult? Function(_CountryStepSelectResult value)? countrySelection,
    TResult? Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult? Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult? Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult? Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult? Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult? Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult? Function(_NoteCreationSelectResult value)? noteCreation,
    TResult? Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
  }) {
    return beneficiarySelection?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AccountStepSelectResult value)? accountSelection,
    TResult Function(_CountryStepSelectResult value)? countrySelection,
    TResult Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult Function(_NoteCreationSelectResult value)? noteCreation,
    TResult Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (beneficiarySelection != null) {
      return beneficiarySelection(this);
    }
    return orElse();
  }
}

abstract class _BeneficiarySelectionSelectResult
    extends PaymentCreationFlowSelectResult {
  factory _BeneficiarySelectionSelectResult(
          {required final BeneficiaryFlowType flowType,
          final CrossborderInternationalBeneficiary? beneficiary}) =
      _$BeneficiarySelectionSelectResultImpl;
  _BeneficiarySelectionSelectResult._() : super._();

  BeneficiaryFlowType get flowType;
  CrossborderInternationalBeneficiary? get beneficiary;

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BeneficiarySelectionSelectResultImplCopyWith<
          _$BeneficiarySelectionSelectResultImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BeneficiaryCreationSelectResultImplCopyWith<$Res> {
  factory _$$BeneficiaryCreationSelectResultImplCopyWith(
          _$BeneficiaryCreationSelectResultImpl value,
          $Res Function(_$BeneficiaryCreationSelectResultImpl) then) =
      __$$BeneficiaryCreationSelectResultImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BeneficiaryCreationResult creationResult});

  $BeneficiaryCreationResultCopyWith<$Res> get creationResult;
}

/// @nodoc
class __$$BeneficiaryCreationSelectResultImplCopyWithImpl<$Res>
    extends _$PaymentCreationFlowSelectResultCopyWithImpl<$Res,
        _$BeneficiaryCreationSelectResultImpl>
    implements _$$BeneficiaryCreationSelectResultImplCopyWith<$Res> {
  __$$BeneficiaryCreationSelectResultImplCopyWithImpl(
      _$BeneficiaryCreationSelectResultImpl _value,
      $Res Function(_$BeneficiaryCreationSelectResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? creationResult = null,
  }) {
    return _then(_$BeneficiaryCreationSelectResultImpl(
      null == creationResult
          ? _value.creationResult
          : creationResult // ignore: cast_nullable_to_non_nullable
              as BeneficiaryCreationResult,
    ));
  }

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BeneficiaryCreationResultCopyWith<$Res> get creationResult {
    return $BeneficiaryCreationResultCopyWith<$Res>(_value.creationResult,
        (value) {
      return _then(_value.copyWith(creationResult: value));
    });
  }
}

/// @nodoc

class _$BeneficiaryCreationSelectResultImpl
    extends _BeneficiaryCreationSelectResult {
  _$BeneficiaryCreationSelectResultImpl(this.creationResult) : super._();

  @override
  final BeneficiaryCreationResult creationResult;

  @override
  String toString() {
    return 'PaymentCreationFlowSelectResult.beneficiaryCreation(creationResult: $creationResult)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BeneficiaryCreationSelectResultImpl &&
            (identical(other.creationResult, creationResult) ||
                other.creationResult == creationResult));
  }

  @override
  int get hashCode => Object.hash(runtimeType, creationResult);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BeneficiaryCreationSelectResultImplCopyWith<
          _$BeneficiaryCreationSelectResultImpl>
      get copyWith => __$$BeneficiaryCreationSelectResultImplCopyWithImpl<
          _$BeneficiaryCreationSelectResultImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetails account) accountSelection,
    required TResult Function(PaymentCountry country,
            money.Currency? defaultCurrency, bool isChangeCurrencyAvailable)
        countrySelection,
    required TResult Function(money.Money amount, RecurringTransferSetup? setup)
        currencySelection,
    required TResult Function(RailOptionModel transferOption) railsSelection,
    required TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)
        beneficiarySelection,
    required TResult Function(BeneficiaryCreationResult creationResult)
        beneficiaryCreation,
    required TResult Function(TransferPurpose transferPurpose) purposeSelection,
    required TResult Function(TransferSubpurpose transferSubpurpose)
        subpurposeSelection,
    required TResult Function(String? notes) noteCreation,
    required TResult Function(CrossborderInternationalBeneficiary beneficiary)
        additionalBeneficiaryInfo,
  }) {
    return beneficiaryCreation(creationResult);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetails account)? accountSelection,
    TResult? Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult? Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult? Function(RailOptionModel transferOption)? railsSelection,
    TResult? Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult? Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult? Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult? Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult? Function(String? notes)? noteCreation,
    TResult? Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
  }) {
    return beneficiaryCreation?.call(creationResult);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetails account)? accountSelection,
    TResult Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult Function(RailOptionModel transferOption)? railsSelection,
    TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult Function(String? notes)? noteCreation,
    TResult Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (beneficiaryCreation != null) {
      return beneficiaryCreation(creationResult);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AccountStepSelectResult value) accountSelection,
    required TResult Function(_CountryStepSelectResult value) countrySelection,
    required TResult Function(_CurrencySelectionSelectResult value)
        currencySelection,
    required TResult Function(_RailsSelectionSelectResult value) railsSelection,
    required TResult Function(_BeneficiarySelectionSelectResult value)
        beneficiarySelection,
    required TResult Function(_BeneficiaryCreationSelectResult value)
        beneficiaryCreation,
    required TResult Function(_PurposeSelectionSelectResult value)
        purposeSelection,
    required TResult Function(_SubpurposeSelectionSelectResult value)
        subpurposeSelection,
    required TResult Function(_NoteCreationSelectResult value) noteCreation,
    required TResult Function(_AdditionalBeneficiaryInfoResult value)
        additionalBeneficiaryInfo,
  }) {
    return beneficiaryCreation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AccountStepSelectResult value)? accountSelection,
    TResult? Function(_CountryStepSelectResult value)? countrySelection,
    TResult? Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult? Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult? Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult? Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult? Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult? Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult? Function(_NoteCreationSelectResult value)? noteCreation,
    TResult? Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
  }) {
    return beneficiaryCreation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AccountStepSelectResult value)? accountSelection,
    TResult Function(_CountryStepSelectResult value)? countrySelection,
    TResult Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult Function(_NoteCreationSelectResult value)? noteCreation,
    TResult Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (beneficiaryCreation != null) {
      return beneficiaryCreation(this);
    }
    return orElse();
  }
}

abstract class _BeneficiaryCreationSelectResult
    extends PaymentCreationFlowSelectResult {
  factory _BeneficiaryCreationSelectResult(
          final BeneficiaryCreationResult creationResult) =
      _$BeneficiaryCreationSelectResultImpl;
  _BeneficiaryCreationSelectResult._() : super._();

  BeneficiaryCreationResult get creationResult;

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BeneficiaryCreationSelectResultImplCopyWith<
          _$BeneficiaryCreationSelectResultImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PurposeSelectionSelectResultImplCopyWith<$Res> {
  factory _$$PurposeSelectionSelectResultImplCopyWith(
          _$PurposeSelectionSelectResultImpl value,
          $Res Function(_$PurposeSelectionSelectResultImpl) then) =
      __$$PurposeSelectionSelectResultImplCopyWithImpl<$Res>;
  @useResult
  $Res call({TransferPurpose transferPurpose});

  $TransferPurposeCopyWith<$Res> get transferPurpose;
}

/// @nodoc
class __$$PurposeSelectionSelectResultImplCopyWithImpl<$Res>
    extends _$PaymentCreationFlowSelectResultCopyWithImpl<$Res,
        _$PurposeSelectionSelectResultImpl>
    implements _$$PurposeSelectionSelectResultImplCopyWith<$Res> {
  __$$PurposeSelectionSelectResultImplCopyWithImpl(
      _$PurposeSelectionSelectResultImpl _value,
      $Res Function(_$PurposeSelectionSelectResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transferPurpose = null,
  }) {
    return _then(_$PurposeSelectionSelectResultImpl(
      null == transferPurpose
          ? _value.transferPurpose
          : transferPurpose // ignore: cast_nullable_to_non_nullable
              as TransferPurpose,
    ));
  }

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TransferPurposeCopyWith<$Res> get transferPurpose {
    return $TransferPurposeCopyWith<$Res>(_value.transferPurpose, (value) {
      return _then(_value.copyWith(transferPurpose: value));
    });
  }
}

/// @nodoc

class _$PurposeSelectionSelectResultImpl extends _PurposeSelectionSelectResult {
  _$PurposeSelectionSelectResultImpl(this.transferPurpose) : super._();

  @override
  final TransferPurpose transferPurpose;

  @override
  String toString() {
    return 'PaymentCreationFlowSelectResult.purposeSelection(transferPurpose: $transferPurpose)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PurposeSelectionSelectResultImpl &&
            (identical(other.transferPurpose, transferPurpose) ||
                other.transferPurpose == transferPurpose));
  }

  @override
  int get hashCode => Object.hash(runtimeType, transferPurpose);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PurposeSelectionSelectResultImplCopyWith<
          _$PurposeSelectionSelectResultImpl>
      get copyWith => __$$PurposeSelectionSelectResultImplCopyWithImpl<
          _$PurposeSelectionSelectResultImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetails account) accountSelection,
    required TResult Function(PaymentCountry country,
            money.Currency? defaultCurrency, bool isChangeCurrencyAvailable)
        countrySelection,
    required TResult Function(money.Money amount, RecurringTransferSetup? setup)
        currencySelection,
    required TResult Function(RailOptionModel transferOption) railsSelection,
    required TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)
        beneficiarySelection,
    required TResult Function(BeneficiaryCreationResult creationResult)
        beneficiaryCreation,
    required TResult Function(TransferPurpose transferPurpose) purposeSelection,
    required TResult Function(TransferSubpurpose transferSubpurpose)
        subpurposeSelection,
    required TResult Function(String? notes) noteCreation,
    required TResult Function(CrossborderInternationalBeneficiary beneficiary)
        additionalBeneficiaryInfo,
  }) {
    return purposeSelection(transferPurpose);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetails account)? accountSelection,
    TResult? Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult? Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult? Function(RailOptionModel transferOption)? railsSelection,
    TResult? Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult? Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult? Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult? Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult? Function(String? notes)? noteCreation,
    TResult? Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
  }) {
    return purposeSelection?.call(transferPurpose);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetails account)? accountSelection,
    TResult Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult Function(RailOptionModel transferOption)? railsSelection,
    TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult Function(String? notes)? noteCreation,
    TResult Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (purposeSelection != null) {
      return purposeSelection(transferPurpose);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AccountStepSelectResult value) accountSelection,
    required TResult Function(_CountryStepSelectResult value) countrySelection,
    required TResult Function(_CurrencySelectionSelectResult value)
        currencySelection,
    required TResult Function(_RailsSelectionSelectResult value) railsSelection,
    required TResult Function(_BeneficiarySelectionSelectResult value)
        beneficiarySelection,
    required TResult Function(_BeneficiaryCreationSelectResult value)
        beneficiaryCreation,
    required TResult Function(_PurposeSelectionSelectResult value)
        purposeSelection,
    required TResult Function(_SubpurposeSelectionSelectResult value)
        subpurposeSelection,
    required TResult Function(_NoteCreationSelectResult value) noteCreation,
    required TResult Function(_AdditionalBeneficiaryInfoResult value)
        additionalBeneficiaryInfo,
  }) {
    return purposeSelection(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AccountStepSelectResult value)? accountSelection,
    TResult? Function(_CountryStepSelectResult value)? countrySelection,
    TResult? Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult? Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult? Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult? Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult? Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult? Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult? Function(_NoteCreationSelectResult value)? noteCreation,
    TResult? Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
  }) {
    return purposeSelection?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AccountStepSelectResult value)? accountSelection,
    TResult Function(_CountryStepSelectResult value)? countrySelection,
    TResult Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult Function(_NoteCreationSelectResult value)? noteCreation,
    TResult Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (purposeSelection != null) {
      return purposeSelection(this);
    }
    return orElse();
  }
}

abstract class _PurposeSelectionSelectResult
    extends PaymentCreationFlowSelectResult {
  factory _PurposeSelectionSelectResult(final TransferPurpose transferPurpose) =
      _$PurposeSelectionSelectResultImpl;
  _PurposeSelectionSelectResult._() : super._();

  TransferPurpose get transferPurpose;

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PurposeSelectionSelectResultImplCopyWith<
          _$PurposeSelectionSelectResultImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SubpurposeSelectionSelectResultImplCopyWith<$Res> {
  factory _$$SubpurposeSelectionSelectResultImplCopyWith(
          _$SubpurposeSelectionSelectResultImpl value,
          $Res Function(_$SubpurposeSelectionSelectResultImpl) then) =
      __$$SubpurposeSelectionSelectResultImplCopyWithImpl<$Res>;
  @useResult
  $Res call({TransferSubpurpose transferSubpurpose});

  $TransferSubpurposeCopyWith<$Res> get transferSubpurpose;
}

/// @nodoc
class __$$SubpurposeSelectionSelectResultImplCopyWithImpl<$Res>
    extends _$PaymentCreationFlowSelectResultCopyWithImpl<$Res,
        _$SubpurposeSelectionSelectResultImpl>
    implements _$$SubpurposeSelectionSelectResultImplCopyWith<$Res> {
  __$$SubpurposeSelectionSelectResultImplCopyWithImpl(
      _$SubpurposeSelectionSelectResultImpl _value,
      $Res Function(_$SubpurposeSelectionSelectResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transferSubpurpose = null,
  }) {
    return _then(_$SubpurposeSelectionSelectResultImpl(
      null == transferSubpurpose
          ? _value.transferSubpurpose
          : transferSubpurpose // ignore: cast_nullable_to_non_nullable
              as TransferSubpurpose,
    ));
  }

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TransferSubpurposeCopyWith<$Res> get transferSubpurpose {
    return $TransferSubpurposeCopyWith<$Res>(_value.transferSubpurpose,
        (value) {
      return _then(_value.copyWith(transferSubpurpose: value));
    });
  }
}

/// @nodoc

class _$SubpurposeSelectionSelectResultImpl
    extends _SubpurposeSelectionSelectResult {
  _$SubpurposeSelectionSelectResultImpl(this.transferSubpurpose) : super._();

  @override
  final TransferSubpurpose transferSubpurpose;

  @override
  String toString() {
    return 'PaymentCreationFlowSelectResult.subpurposeSelection(transferSubpurpose: $transferSubpurpose)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubpurposeSelectionSelectResultImpl &&
            (identical(other.transferSubpurpose, transferSubpurpose) ||
                other.transferSubpurpose == transferSubpurpose));
  }

  @override
  int get hashCode => Object.hash(runtimeType, transferSubpurpose);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubpurposeSelectionSelectResultImplCopyWith<
          _$SubpurposeSelectionSelectResultImpl>
      get copyWith => __$$SubpurposeSelectionSelectResultImplCopyWithImpl<
          _$SubpurposeSelectionSelectResultImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetails account) accountSelection,
    required TResult Function(PaymentCountry country,
            money.Currency? defaultCurrency, bool isChangeCurrencyAvailable)
        countrySelection,
    required TResult Function(money.Money amount, RecurringTransferSetup? setup)
        currencySelection,
    required TResult Function(RailOptionModel transferOption) railsSelection,
    required TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)
        beneficiarySelection,
    required TResult Function(BeneficiaryCreationResult creationResult)
        beneficiaryCreation,
    required TResult Function(TransferPurpose transferPurpose) purposeSelection,
    required TResult Function(TransferSubpurpose transferSubpurpose)
        subpurposeSelection,
    required TResult Function(String? notes) noteCreation,
    required TResult Function(CrossborderInternationalBeneficiary beneficiary)
        additionalBeneficiaryInfo,
  }) {
    return subpurposeSelection(transferSubpurpose);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetails account)? accountSelection,
    TResult? Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult? Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult? Function(RailOptionModel transferOption)? railsSelection,
    TResult? Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult? Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult? Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult? Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult? Function(String? notes)? noteCreation,
    TResult? Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
  }) {
    return subpurposeSelection?.call(transferSubpurpose);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetails account)? accountSelection,
    TResult Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult Function(RailOptionModel transferOption)? railsSelection,
    TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult Function(String? notes)? noteCreation,
    TResult Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (subpurposeSelection != null) {
      return subpurposeSelection(transferSubpurpose);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AccountStepSelectResult value) accountSelection,
    required TResult Function(_CountryStepSelectResult value) countrySelection,
    required TResult Function(_CurrencySelectionSelectResult value)
        currencySelection,
    required TResult Function(_RailsSelectionSelectResult value) railsSelection,
    required TResult Function(_BeneficiarySelectionSelectResult value)
        beneficiarySelection,
    required TResult Function(_BeneficiaryCreationSelectResult value)
        beneficiaryCreation,
    required TResult Function(_PurposeSelectionSelectResult value)
        purposeSelection,
    required TResult Function(_SubpurposeSelectionSelectResult value)
        subpurposeSelection,
    required TResult Function(_NoteCreationSelectResult value) noteCreation,
    required TResult Function(_AdditionalBeneficiaryInfoResult value)
        additionalBeneficiaryInfo,
  }) {
    return subpurposeSelection(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AccountStepSelectResult value)? accountSelection,
    TResult? Function(_CountryStepSelectResult value)? countrySelection,
    TResult? Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult? Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult? Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult? Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult? Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult? Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult? Function(_NoteCreationSelectResult value)? noteCreation,
    TResult? Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
  }) {
    return subpurposeSelection?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AccountStepSelectResult value)? accountSelection,
    TResult Function(_CountryStepSelectResult value)? countrySelection,
    TResult Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult Function(_NoteCreationSelectResult value)? noteCreation,
    TResult Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (subpurposeSelection != null) {
      return subpurposeSelection(this);
    }
    return orElse();
  }
}

abstract class _SubpurposeSelectionSelectResult
    extends PaymentCreationFlowSelectResult {
  factory _SubpurposeSelectionSelectResult(
          final TransferSubpurpose transferSubpurpose) =
      _$SubpurposeSelectionSelectResultImpl;
  _SubpurposeSelectionSelectResult._() : super._();

  TransferSubpurpose get transferSubpurpose;

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubpurposeSelectionSelectResultImplCopyWith<
          _$SubpurposeSelectionSelectResultImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NoteCreationSelectResultImplCopyWith<$Res> {
  factory _$$NoteCreationSelectResultImplCopyWith(
          _$NoteCreationSelectResultImpl value,
          $Res Function(_$NoteCreationSelectResultImpl) then) =
      __$$NoteCreationSelectResultImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? notes});
}

/// @nodoc
class __$$NoteCreationSelectResultImplCopyWithImpl<$Res>
    extends _$PaymentCreationFlowSelectResultCopyWithImpl<$Res,
        _$NoteCreationSelectResultImpl>
    implements _$$NoteCreationSelectResultImplCopyWith<$Res> {
  __$$NoteCreationSelectResultImplCopyWithImpl(
      _$NoteCreationSelectResultImpl _value,
      $Res Function(_$NoteCreationSelectResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notes = freezed,
  }) {
    return _then(_$NoteCreationSelectResultImpl(
      freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$NoteCreationSelectResultImpl extends _NoteCreationSelectResult {
  _$NoteCreationSelectResultImpl(this.notes) : super._();

  @override
  final String? notes;

  @override
  String toString() {
    return 'PaymentCreationFlowSelectResult.noteCreation(notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NoteCreationSelectResultImpl &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @override
  int get hashCode => Object.hash(runtimeType, notes);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NoteCreationSelectResultImplCopyWith<_$NoteCreationSelectResultImpl>
      get copyWith => __$$NoteCreationSelectResultImplCopyWithImpl<
          _$NoteCreationSelectResultImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetails account) accountSelection,
    required TResult Function(PaymentCountry country,
            money.Currency? defaultCurrency, bool isChangeCurrencyAvailable)
        countrySelection,
    required TResult Function(money.Money amount, RecurringTransferSetup? setup)
        currencySelection,
    required TResult Function(RailOptionModel transferOption) railsSelection,
    required TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)
        beneficiarySelection,
    required TResult Function(BeneficiaryCreationResult creationResult)
        beneficiaryCreation,
    required TResult Function(TransferPurpose transferPurpose) purposeSelection,
    required TResult Function(TransferSubpurpose transferSubpurpose)
        subpurposeSelection,
    required TResult Function(String? notes) noteCreation,
    required TResult Function(CrossborderInternationalBeneficiary beneficiary)
        additionalBeneficiaryInfo,
  }) {
    return noteCreation(notes);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetails account)? accountSelection,
    TResult? Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult? Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult? Function(RailOptionModel transferOption)? railsSelection,
    TResult? Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult? Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult? Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult? Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult? Function(String? notes)? noteCreation,
    TResult? Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
  }) {
    return noteCreation?.call(notes);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetails account)? accountSelection,
    TResult Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult Function(RailOptionModel transferOption)? railsSelection,
    TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult Function(String? notes)? noteCreation,
    TResult Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (noteCreation != null) {
      return noteCreation(notes);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AccountStepSelectResult value) accountSelection,
    required TResult Function(_CountryStepSelectResult value) countrySelection,
    required TResult Function(_CurrencySelectionSelectResult value)
        currencySelection,
    required TResult Function(_RailsSelectionSelectResult value) railsSelection,
    required TResult Function(_BeneficiarySelectionSelectResult value)
        beneficiarySelection,
    required TResult Function(_BeneficiaryCreationSelectResult value)
        beneficiaryCreation,
    required TResult Function(_PurposeSelectionSelectResult value)
        purposeSelection,
    required TResult Function(_SubpurposeSelectionSelectResult value)
        subpurposeSelection,
    required TResult Function(_NoteCreationSelectResult value) noteCreation,
    required TResult Function(_AdditionalBeneficiaryInfoResult value)
        additionalBeneficiaryInfo,
  }) {
    return noteCreation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AccountStepSelectResult value)? accountSelection,
    TResult? Function(_CountryStepSelectResult value)? countrySelection,
    TResult? Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult? Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult? Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult? Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult? Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult? Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult? Function(_NoteCreationSelectResult value)? noteCreation,
    TResult? Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
  }) {
    return noteCreation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AccountStepSelectResult value)? accountSelection,
    TResult Function(_CountryStepSelectResult value)? countrySelection,
    TResult Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult Function(_NoteCreationSelectResult value)? noteCreation,
    TResult Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (noteCreation != null) {
      return noteCreation(this);
    }
    return orElse();
  }
}

abstract class _NoteCreationSelectResult
    extends PaymentCreationFlowSelectResult {
  factory _NoteCreationSelectResult(final String? notes) =
      _$NoteCreationSelectResultImpl;
  _NoteCreationSelectResult._() : super._();

  String? get notes;

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NoteCreationSelectResultImplCopyWith<_$NoteCreationSelectResultImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AdditionalBeneficiaryInfoResultImplCopyWith<$Res> {
  factory _$$AdditionalBeneficiaryInfoResultImplCopyWith(
          _$AdditionalBeneficiaryInfoResultImpl value,
          $Res Function(_$AdditionalBeneficiaryInfoResultImpl) then) =
      __$$AdditionalBeneficiaryInfoResultImplCopyWithImpl<$Res>;
  @useResult
  $Res call({CrossborderInternationalBeneficiary beneficiary});

  $CrossborderInternationalBeneficiaryCopyWith<$Res> get beneficiary;
}

/// @nodoc
class __$$AdditionalBeneficiaryInfoResultImplCopyWithImpl<$Res>
    extends _$PaymentCreationFlowSelectResultCopyWithImpl<$Res,
        _$AdditionalBeneficiaryInfoResultImpl>
    implements _$$AdditionalBeneficiaryInfoResultImplCopyWith<$Res> {
  __$$AdditionalBeneficiaryInfoResultImplCopyWithImpl(
      _$AdditionalBeneficiaryInfoResultImpl _value,
      $Res Function(_$AdditionalBeneficiaryInfoResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? beneficiary = null,
  }) {
    return _then(_$AdditionalBeneficiaryInfoResultImpl(
      null == beneficiary
          ? _value.beneficiary
          : beneficiary // ignore: cast_nullable_to_non_nullable
              as CrossborderInternationalBeneficiary,
    ));
  }

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CrossborderInternationalBeneficiaryCopyWith<$Res> get beneficiary {
    return $CrossborderInternationalBeneficiaryCopyWith<$Res>(
        _value.beneficiary, (value) {
      return _then(_value.copyWith(beneficiary: value));
    });
  }
}

/// @nodoc

class _$AdditionalBeneficiaryInfoResultImpl
    extends _AdditionalBeneficiaryInfoResult {
  _$AdditionalBeneficiaryInfoResultImpl(this.beneficiary) : super._();

  @override
  final CrossborderInternationalBeneficiary beneficiary;

  @override
  String toString() {
    return 'PaymentCreationFlowSelectResult.additionalBeneficiaryInfo(beneficiary: $beneficiary)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdditionalBeneficiaryInfoResultImpl &&
            (identical(other.beneficiary, beneficiary) ||
                other.beneficiary == beneficiary));
  }

  @override
  int get hashCode => Object.hash(runtimeType, beneficiary);

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AdditionalBeneficiaryInfoResultImplCopyWith<
          _$AdditionalBeneficiaryInfoResultImpl>
      get copyWith => __$$AdditionalBeneficiaryInfoResultImplCopyWithImpl<
          _$AdditionalBeneficiaryInfoResultImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetails account) accountSelection,
    required TResult Function(PaymentCountry country,
            money.Currency? defaultCurrency, bool isChangeCurrencyAvailable)
        countrySelection,
    required TResult Function(money.Money amount, RecurringTransferSetup? setup)
        currencySelection,
    required TResult Function(RailOptionModel transferOption) railsSelection,
    required TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)
        beneficiarySelection,
    required TResult Function(BeneficiaryCreationResult creationResult)
        beneficiaryCreation,
    required TResult Function(TransferPurpose transferPurpose) purposeSelection,
    required TResult Function(TransferSubpurpose transferSubpurpose)
        subpurposeSelection,
    required TResult Function(String? notes) noteCreation,
    required TResult Function(CrossborderInternationalBeneficiary beneficiary)
        additionalBeneficiaryInfo,
  }) {
    return additionalBeneficiaryInfo(beneficiary);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetails account)? accountSelection,
    TResult? Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult? Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult? Function(RailOptionModel transferOption)? railsSelection,
    TResult? Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult? Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult? Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult? Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult? Function(String? notes)? noteCreation,
    TResult? Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
  }) {
    return additionalBeneficiaryInfo?.call(beneficiary);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetails account)? accountSelection,
    TResult Function(PaymentCountry country, money.Currency? defaultCurrency,
            bool isChangeCurrencyAvailable)?
        countrySelection,
    TResult Function(money.Money amount, RecurringTransferSetup? setup)?
        currencySelection,
    TResult Function(RailOptionModel transferOption)? railsSelection,
    TResult Function(BeneficiaryFlowType flowType,
            CrossborderInternationalBeneficiary? beneficiary)?
        beneficiarySelection,
    TResult Function(BeneficiaryCreationResult creationResult)?
        beneficiaryCreation,
    TResult Function(TransferPurpose transferPurpose)? purposeSelection,
    TResult Function(TransferSubpurpose transferSubpurpose)?
        subpurposeSelection,
    TResult Function(String? notes)? noteCreation,
    TResult Function(CrossborderInternationalBeneficiary beneficiary)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (additionalBeneficiaryInfo != null) {
      return additionalBeneficiaryInfo(beneficiary);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AccountStepSelectResult value) accountSelection,
    required TResult Function(_CountryStepSelectResult value) countrySelection,
    required TResult Function(_CurrencySelectionSelectResult value)
        currencySelection,
    required TResult Function(_RailsSelectionSelectResult value) railsSelection,
    required TResult Function(_BeneficiarySelectionSelectResult value)
        beneficiarySelection,
    required TResult Function(_BeneficiaryCreationSelectResult value)
        beneficiaryCreation,
    required TResult Function(_PurposeSelectionSelectResult value)
        purposeSelection,
    required TResult Function(_SubpurposeSelectionSelectResult value)
        subpurposeSelection,
    required TResult Function(_NoteCreationSelectResult value) noteCreation,
    required TResult Function(_AdditionalBeneficiaryInfoResult value)
        additionalBeneficiaryInfo,
  }) {
    return additionalBeneficiaryInfo(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AccountStepSelectResult value)? accountSelection,
    TResult? Function(_CountryStepSelectResult value)? countrySelection,
    TResult? Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult? Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult? Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult? Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult? Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult? Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult? Function(_NoteCreationSelectResult value)? noteCreation,
    TResult? Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
  }) {
    return additionalBeneficiaryInfo?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AccountStepSelectResult value)? accountSelection,
    TResult Function(_CountryStepSelectResult value)? countrySelection,
    TResult Function(_CurrencySelectionSelectResult value)? currencySelection,
    TResult Function(_RailsSelectionSelectResult value)? railsSelection,
    TResult Function(_BeneficiarySelectionSelectResult value)?
        beneficiarySelection,
    TResult Function(_BeneficiaryCreationSelectResult value)?
        beneficiaryCreation,
    TResult Function(_PurposeSelectionSelectResult value)? purposeSelection,
    TResult Function(_SubpurposeSelectionSelectResult value)?
        subpurposeSelection,
    TResult Function(_NoteCreationSelectResult value)? noteCreation,
    TResult Function(_AdditionalBeneficiaryInfoResult value)?
        additionalBeneficiaryInfo,
    required TResult orElse(),
  }) {
    if (additionalBeneficiaryInfo != null) {
      return additionalBeneficiaryInfo(this);
    }
    return orElse();
  }
}

abstract class _AdditionalBeneficiaryInfoResult
    extends PaymentCreationFlowSelectResult {
  factory _AdditionalBeneficiaryInfoResult(
          final CrossborderInternationalBeneficiary beneficiary) =
      _$AdditionalBeneficiaryInfoResultImpl;
  _AdditionalBeneficiaryInfoResult._() : super._();

  CrossborderInternationalBeneficiary get beneficiary;

  /// Create a copy of PaymentCreationFlowSelectResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AdditionalBeneficiaryInfoResultImplCopyWith<
          _$AdditionalBeneficiaryInfoResultImpl>
      get copyWith => throw _privateConstructorUsedError;
}
