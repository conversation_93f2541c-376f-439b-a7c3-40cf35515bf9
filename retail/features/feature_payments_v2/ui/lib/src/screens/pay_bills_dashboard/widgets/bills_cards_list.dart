import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_feature_payments_v2_ui/l10n/payments_localization.g.dart';
import 'package:wio_feature_payments_v2_ui/src/navigation/index.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/common/bill_card_cell.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/pay_bills_dashboard/cubit/pay_bills_cubit.dart';

class BillsCardsList extends StatelessWidget {
  final List<CardBeneficiary> beneficiaries;
  final String textureUrl;
  final bool isSpecialPlan;

  const BillsCardsList(
    this.beneficiaries,
    this.textureUrl, {
    required this.isSpecialPlan,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = PaymentsLocalizations.of(context);

    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      sliver: SliverList(
        delegate: SliverChildListDelegate(
          [
            Label(
              model: LabelModel(
                text: l10n.cardsBillsScreenExternalCardsCaption,
                textStyle: CompanyTextStylePointer.h4,
                color: CompanyColorPointer.primary3,
              ),
            ),
            const SizedBox(height: 10.0),
            for (final beneficiary in beneficiaries)
              Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: BillCardCell(
                  beneficiary,
                  textureUrl,
                  onTap: () =>
                      _onTapOnCell(context, beneficiary, isSpecialPlan),
                  onMakePayment: () =>
                      _makePayment(context, beneficiary, isSpecialPlan),
                  onAddDueDate: () => _addDueDate(context, beneficiary),
                ),
              ),
            ListBox(
              listBoxModel: ListBoxModel(
                applyPadding: true,
                isBoxed: true,
                highlightInteraction: true,
                textModel:
                    ListBoxTextModel(title: l10n.cardsBillsScreenAddCardCta),
                leftPartModel: ListBoxPartModel.icon(
                  icon: CompanyIconPointer.add_boxed.toGraphicAsset(),
                ),
              ),
              onPressed: () => context.read<PayBillsCubit>().addNewCard(),
            ),
          ],
        ),
      ),
    );
  }

  void _onTapOnCell(
    BuildContext context,
    CardBeneficiary beneficiary,
    bool isSpecialPlan,
  ) {
    context.read<PayBillsCubit>().openCardDetails(
          beneficiary: beneficiary,
          isSpecialPlan: isSpecialPlan,
        );
  }

  void _makePayment(
    BuildContext context,
    CardBeneficiary beneficiary,
    bool isSpecialPlan,
  ) {
    context
        .read<PayBillsCubit>()
        .managePayment(beneficiary: beneficiary, isSpecialPlan: isSpecialPlan);
  }

  void _addDueDate(
    BuildContext context,
    CardBeneficiary beneficiary,
  ) {
    context.read<PayBillsCubit>().openCardSettings(
          beneficiary,
          CardSettingEntryPoint.dueDate,
        );
  }
}
