import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:sliver_tools/sliver_tools.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart' hide Country;
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_feature_payments_v2_ui/feature_payments_v2_ui.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/common/beneficiaries/views/beneficiary_list_item.dart';

class BeneficiariesSearchList extends StatelessWidget {
  static const beneficiaryListItemKey = ValueKey('beneficiaryListItemKey');

  final List<CrossborderInternationalBeneficiary> payees;
  final ValueChanged<CrossborderInternationalBeneficiary> onSelected;
  final ValueChanged<String> onSearch;
  final bool useGroupedView;
  final VoidCallback? onNewSelected;

  const BeneficiariesSearchList({
    required this.payees,
    required this.onSelected,
    required this.onSearch,
    this.useGroupedView = false,
    this.onNewSelected,
    super.key,
  });

  bool get _hasAddBeneficiaryItem => onNewSelected != null;

  @override
  Widget build(BuildContext context) {
    final localization = PaymentsLocalizations.of(context);

    return MultiSliver(
      children: [
        SliverPersistentHeader(
          pinned: true,
          delegate: _SearchFieldHeader(
            lightTheme: useGroupedView,
            headerTitle:
                localization.contactPaymentsInternationalPayeesSearchPayee,
            onSearchChanged: onSearch,
          ),
        ),
        if (_hasAddBeneficiaryItem)
          _AddBeneficiaryItem(onNewSelected: onNewSelected),
        if (useGroupedView)
          _GroupedBeneficiariesSearchList(payees, onSelected)
        else
          _BeneficiariesSearchList(payees, onSelected),
      ],
    );
  }
}

class _GroupedBeneficiariesSearchList extends StatelessWidget {
  final List<CrossborderInternationalBeneficiary> payees;
  final ValueChanged<CrossborderInternationalBeneficiary> onSelected;

  const _GroupedBeneficiariesSearchList(this.payees, this.onSelected);

  @override
  Widget build(BuildContext context) {
    final beneficiariesGrouped = payees.groupListsBy((beneficiary) {
      final accountHolderName = beneficiary.accountHolderName;

      return accountHolderName.isEmpty ? '#' : accountHolderName[0];
    });

    return MultiSliver(
      children: beneficiariesGrouped.entries
          .map(
            (group) => [
              SliverToBoxAdapter(
                child: Padding(
                  padding:
                      const EdgeInsets.only(top: 16.0, left: 24.0, right: 24.0),
                  child: Label(
                    model: LabelModel(
                      text: group.key,
                      textStyle: CompanyTextStylePointer.b3,
                      color: CompanyColorPointer.secondary4,
                    ),
                  ),
                ),
              ),
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final payee = group.value[index];

                    return BeneficiaryListItem(
                      key: BeneficiariesSearchList.beneficiaryListItemKey,
                      payee: payee,
                      onPressed: () => onSelected(payee),
                      avatarBgColor: CompanyColorPointer.background1,
                    );
                  },
                  childCount: group.value.length,
                ),
              ),
            ],
          )
          .toList(growable: false)
          .flattenedToList,
    );
  }
}

class _BeneficiariesSearchList extends StatelessWidget {
  final List<CrossborderInternationalBeneficiary> payees;
  final ValueChanged<CrossborderInternationalBeneficiary> onSelected;

  const _BeneficiariesSearchList(this.payees, this.onSelected);

  @override
  Widget build(BuildContext context) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final payee = payees[index];

          return BeneficiaryListItem(
            key: BeneficiariesSearchList.beneficiaryListItemKey,
            payee: payee,
            onPressed: () => onSelected(payee),
          );
        },
        childCount: payees.length,
      ),
    );
  }
}

class _SearchFieldHeader extends SliverPersistentHeaderDelegate {
  final bool lightTheme;
  final String headerTitle;
  final ValueChanged<String> onSearchChanged;

  const _SearchFieldHeader({
    required this.lightTheme,
    required this.headerTitle,
    required this.onSearchChanged,
  });

  @override
  double get maxExtent => 76.0;

  @override
  double get minExtent => 76.0;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    final localization = PaymentsLocalizations.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 24.0,
        vertical: 16.0,
      ),
      color: lightTheme
          ? context.colorStyling.surface2
          : context.colorStyling.background1,
      child: SearchField(
        autocorrect: false,
        onSubmitted: onSearchChanged,
        model: SearchFieldModel(
          labelText: localization.contactPaymentsInternationalPayeesSearchPayee,
          fillColor: lightTheme
              ? CompanyColorPointer.background1
              : CompanyColorPointer.surface2,
        ),
        onChanged: onSearchChanged,
        onCleared: () => onSearchChanged(''),
      ),
    );
  }

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) => false;
}

class _AddBeneficiaryItem extends StatelessWidget {
  final VoidCallback? onNewSelected;

  const _AddBeneficiaryItem({
    required this.onNewSelected,
  });

  @override
  Widget build(BuildContext context) {
    final localization = PaymentsLocalizations.of(context);

    return InkWell(
      onTap: onNewSelected,
      splashColor: context.colorStyling
          .fromPointer(CompanyColorPointer.primary1)
          .withValues(alpha: 0.15),
      splashFactory: InkRipple.splashFactory,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 28.0),
        child: Row(
          children: [
            const Tile(
              size: 52.0,
              model: TileModel.icon(
                borderGradient: CompanyGradientPointer.midnight,
                backgroundColor: CompanyColorPointer.surface2,
                borderColor: CompanyColorPointer.border4,
                icon: GraphicAssetPointer.icon(
                  CompanyIconPointer.plus,
                ),
                size: TileSize.extraLarge,
              ),
            ),
            const SizedBox(width: 12.0),
            Label(
              model: LabelModel(
                text: localization.contactPaymentsPageRecentPayeesNewTitle,
                textStyle: CompanyTextStylePointer.b2,
                color: CompanyColorPointer.primary3,
                overflow: LabelTextOverflow.ellipsis,
                textAlign: LabelTextAlign.center,
                maxLines: 2,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
