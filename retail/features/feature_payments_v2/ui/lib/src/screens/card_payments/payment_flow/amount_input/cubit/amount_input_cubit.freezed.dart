// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'amount_input_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AmountInputState {
  String get accountId => throw _privateConstructorUsedError;
  String get cardId => throw _privateConstructorUsedError;
  Money get amount => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String accountId, String cardId, Money amount)
        idle,
    required TResult Function(String accountId, String cardId, Money amount)
        validating,
    required TResult Function(
            String accountId, String cardId, Money amount, String? errorMessage)
        nonValid,
    required TResult Function(String accountId, String cardId, Money amount)
        valid,
    required TResult Function(String accountId, String cardId, Money amount)
        completed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String accountId, String cardId, Money amount)? idle,
    TResult? Function(String accountId, String cardId, Money amount)?
        validating,
    TResult? Function(String accountId, String cardId, Money amount,
            String? errorMessage)?
        nonValid,
    TResult? Function(String accountId, String cardId, Money amount)? valid,
    TResult? Function(String accountId, String cardId, Money amount)? completed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String accountId, String cardId, Money amount)? idle,
    TResult Function(String accountId, String cardId, Money amount)? validating,
    TResult Function(String accountId, String cardId, Money amount,
            String? errorMessage)?
        nonValid,
    TResult Function(String accountId, String cardId, Money amount)? valid,
    TResult Function(String accountId, String cardId, Money amount)? completed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AmountInputStateIdle value) idle,
    required TResult Function(AmountInputStateValidating value) validating,
    required TResult Function(AmountInputStateNonValid value) nonValid,
    required TResult Function(AmountInputStateValid value) valid,
    required TResult Function(AmountInputStateCompleted value) completed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AmountInputStateIdle value)? idle,
    TResult? Function(AmountInputStateValidating value)? validating,
    TResult? Function(AmountInputStateNonValid value)? nonValid,
    TResult? Function(AmountInputStateValid value)? valid,
    TResult? Function(AmountInputStateCompleted value)? completed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AmountInputStateIdle value)? idle,
    TResult Function(AmountInputStateValidating value)? validating,
    TResult Function(AmountInputStateNonValid value)? nonValid,
    TResult Function(AmountInputStateValid value)? valid,
    TResult Function(AmountInputStateCompleted value)? completed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AmountInputStateCopyWith<AmountInputState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AmountInputStateCopyWith<$Res> {
  factory $AmountInputStateCopyWith(
          AmountInputState value, $Res Function(AmountInputState) then) =
      _$AmountInputStateCopyWithImpl<$Res, AmountInputState>;
  @useResult
  $Res call({String accountId, String cardId, Money amount});
}

/// @nodoc
class _$AmountInputStateCopyWithImpl<$Res, $Val extends AmountInputState>
    implements $AmountInputStateCopyWith<$Res> {
  _$AmountInputStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? cardId = null,
    Object? amount = null,
  }) {
    return _then(_value.copyWith(
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      cardId: null == cardId
          ? _value.cardId
          : cardId // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Money,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AmountInputStateIdleImplCopyWith<$Res>
    implements $AmountInputStateCopyWith<$Res> {
  factory _$$AmountInputStateIdleImplCopyWith(_$AmountInputStateIdleImpl value,
          $Res Function(_$AmountInputStateIdleImpl) then) =
      __$$AmountInputStateIdleImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String accountId, String cardId, Money amount});
}

/// @nodoc
class __$$AmountInputStateIdleImplCopyWithImpl<$Res>
    extends _$AmountInputStateCopyWithImpl<$Res, _$AmountInputStateIdleImpl>
    implements _$$AmountInputStateIdleImplCopyWith<$Res> {
  __$$AmountInputStateIdleImplCopyWithImpl(_$AmountInputStateIdleImpl _value,
      $Res Function(_$AmountInputStateIdleImpl) _then)
      : super(_value, _then);

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? cardId = null,
    Object? amount = null,
  }) {
    return _then(_$AmountInputStateIdleImpl(
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      cardId: null == cardId
          ? _value.cardId
          : cardId // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Money,
    ));
  }
}

/// @nodoc

class _$AmountInputStateIdleImpl extends AmountInputStateIdle {
  const _$AmountInputStateIdleImpl(
      {required this.accountId, required this.cardId, required this.amount})
      : super._();

  @override
  final String accountId;
  @override
  final String cardId;
  @override
  final Money amount;

  @override
  String toString() {
    return 'AmountInputState.idle(accountId: $accountId, cardId: $cardId, amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AmountInputStateIdleImpl &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.cardId, cardId) || other.cardId == cardId) &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, accountId, cardId, amount);

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AmountInputStateIdleImplCopyWith<_$AmountInputStateIdleImpl>
      get copyWith =>
          __$$AmountInputStateIdleImplCopyWithImpl<_$AmountInputStateIdleImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String accountId, String cardId, Money amount)
        idle,
    required TResult Function(String accountId, String cardId, Money amount)
        validating,
    required TResult Function(
            String accountId, String cardId, Money amount, String? errorMessage)
        nonValid,
    required TResult Function(String accountId, String cardId, Money amount)
        valid,
    required TResult Function(String accountId, String cardId, Money amount)
        completed,
  }) {
    return idle(accountId, cardId, amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String accountId, String cardId, Money amount)? idle,
    TResult? Function(String accountId, String cardId, Money amount)?
        validating,
    TResult? Function(String accountId, String cardId, Money amount,
            String? errorMessage)?
        nonValid,
    TResult? Function(String accountId, String cardId, Money amount)? valid,
    TResult? Function(String accountId, String cardId, Money amount)? completed,
  }) {
    return idle?.call(accountId, cardId, amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String accountId, String cardId, Money amount)? idle,
    TResult Function(String accountId, String cardId, Money amount)? validating,
    TResult Function(String accountId, String cardId, Money amount,
            String? errorMessage)?
        nonValid,
    TResult Function(String accountId, String cardId, Money amount)? valid,
    TResult Function(String accountId, String cardId, Money amount)? completed,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(accountId, cardId, amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AmountInputStateIdle value) idle,
    required TResult Function(AmountInputStateValidating value) validating,
    required TResult Function(AmountInputStateNonValid value) nonValid,
    required TResult Function(AmountInputStateValid value) valid,
    required TResult Function(AmountInputStateCompleted value) completed,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AmountInputStateIdle value)? idle,
    TResult? Function(AmountInputStateValidating value)? validating,
    TResult? Function(AmountInputStateNonValid value)? nonValid,
    TResult? Function(AmountInputStateValid value)? valid,
    TResult? Function(AmountInputStateCompleted value)? completed,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AmountInputStateIdle value)? idle,
    TResult Function(AmountInputStateValidating value)? validating,
    TResult Function(AmountInputStateNonValid value)? nonValid,
    TResult Function(AmountInputStateValid value)? valid,
    TResult Function(AmountInputStateCompleted value)? completed,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class AmountInputStateIdle extends AmountInputState {
  const factory AmountInputStateIdle(
      {required final String accountId,
      required final String cardId,
      required final Money amount}) = _$AmountInputStateIdleImpl;
  const AmountInputStateIdle._() : super._();

  @override
  String get accountId;
  @override
  String get cardId;
  @override
  Money get amount;

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AmountInputStateIdleImplCopyWith<_$AmountInputStateIdleImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AmountInputStateValidatingImplCopyWith<$Res>
    implements $AmountInputStateCopyWith<$Res> {
  factory _$$AmountInputStateValidatingImplCopyWith(
          _$AmountInputStateValidatingImpl value,
          $Res Function(_$AmountInputStateValidatingImpl) then) =
      __$$AmountInputStateValidatingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String accountId, String cardId, Money amount});
}

/// @nodoc
class __$$AmountInputStateValidatingImplCopyWithImpl<$Res>
    extends _$AmountInputStateCopyWithImpl<$Res,
        _$AmountInputStateValidatingImpl>
    implements _$$AmountInputStateValidatingImplCopyWith<$Res> {
  __$$AmountInputStateValidatingImplCopyWithImpl(
      _$AmountInputStateValidatingImpl _value,
      $Res Function(_$AmountInputStateValidatingImpl) _then)
      : super(_value, _then);

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? cardId = null,
    Object? amount = null,
  }) {
    return _then(_$AmountInputStateValidatingImpl(
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      cardId: null == cardId
          ? _value.cardId
          : cardId // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Money,
    ));
  }
}

/// @nodoc

class _$AmountInputStateValidatingImpl extends AmountInputStateValidating {
  const _$AmountInputStateValidatingImpl(
      {required this.accountId, required this.cardId, required this.amount})
      : super._();

  @override
  final String accountId;
  @override
  final String cardId;
  @override
  final Money amount;

  @override
  String toString() {
    return 'AmountInputState.validating(accountId: $accountId, cardId: $cardId, amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AmountInputStateValidatingImpl &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.cardId, cardId) || other.cardId == cardId) &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, accountId, cardId, amount);

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AmountInputStateValidatingImplCopyWith<_$AmountInputStateValidatingImpl>
      get copyWith => __$$AmountInputStateValidatingImplCopyWithImpl<
          _$AmountInputStateValidatingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String accountId, String cardId, Money amount)
        idle,
    required TResult Function(String accountId, String cardId, Money amount)
        validating,
    required TResult Function(
            String accountId, String cardId, Money amount, String? errorMessage)
        nonValid,
    required TResult Function(String accountId, String cardId, Money amount)
        valid,
    required TResult Function(String accountId, String cardId, Money amount)
        completed,
  }) {
    return validating(accountId, cardId, amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String accountId, String cardId, Money amount)? idle,
    TResult? Function(String accountId, String cardId, Money amount)?
        validating,
    TResult? Function(String accountId, String cardId, Money amount,
            String? errorMessage)?
        nonValid,
    TResult? Function(String accountId, String cardId, Money amount)? valid,
    TResult? Function(String accountId, String cardId, Money amount)? completed,
  }) {
    return validating?.call(accountId, cardId, amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String accountId, String cardId, Money amount)? idle,
    TResult Function(String accountId, String cardId, Money amount)? validating,
    TResult Function(String accountId, String cardId, Money amount,
            String? errorMessage)?
        nonValid,
    TResult Function(String accountId, String cardId, Money amount)? valid,
    TResult Function(String accountId, String cardId, Money amount)? completed,
    required TResult orElse(),
  }) {
    if (validating != null) {
      return validating(accountId, cardId, amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AmountInputStateIdle value) idle,
    required TResult Function(AmountInputStateValidating value) validating,
    required TResult Function(AmountInputStateNonValid value) nonValid,
    required TResult Function(AmountInputStateValid value) valid,
    required TResult Function(AmountInputStateCompleted value) completed,
  }) {
    return validating(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AmountInputStateIdle value)? idle,
    TResult? Function(AmountInputStateValidating value)? validating,
    TResult? Function(AmountInputStateNonValid value)? nonValid,
    TResult? Function(AmountInputStateValid value)? valid,
    TResult? Function(AmountInputStateCompleted value)? completed,
  }) {
    return validating?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AmountInputStateIdle value)? idle,
    TResult Function(AmountInputStateValidating value)? validating,
    TResult Function(AmountInputStateNonValid value)? nonValid,
    TResult Function(AmountInputStateValid value)? valid,
    TResult Function(AmountInputStateCompleted value)? completed,
    required TResult orElse(),
  }) {
    if (validating != null) {
      return validating(this);
    }
    return orElse();
  }
}

abstract class AmountInputStateValidating extends AmountInputState {
  const factory AmountInputStateValidating(
      {required final String accountId,
      required final String cardId,
      required final Money amount}) = _$AmountInputStateValidatingImpl;
  const AmountInputStateValidating._() : super._();

  @override
  String get accountId;
  @override
  String get cardId;
  @override
  Money get amount;

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AmountInputStateValidatingImplCopyWith<_$AmountInputStateValidatingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AmountInputStateNonValidImplCopyWith<$Res>
    implements $AmountInputStateCopyWith<$Res> {
  factory _$$AmountInputStateNonValidImplCopyWith(
          _$AmountInputStateNonValidImpl value,
          $Res Function(_$AmountInputStateNonValidImpl) then) =
      __$$AmountInputStateNonValidImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String accountId, String cardId, Money amount, String? errorMessage});
}

/// @nodoc
class __$$AmountInputStateNonValidImplCopyWithImpl<$Res>
    extends _$AmountInputStateCopyWithImpl<$Res, _$AmountInputStateNonValidImpl>
    implements _$$AmountInputStateNonValidImplCopyWith<$Res> {
  __$$AmountInputStateNonValidImplCopyWithImpl(
      _$AmountInputStateNonValidImpl _value,
      $Res Function(_$AmountInputStateNonValidImpl) _then)
      : super(_value, _then);

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? cardId = null,
    Object? amount = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_$AmountInputStateNonValidImpl(
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      cardId: null == cardId
          ? _value.cardId
          : cardId // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Money,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$AmountInputStateNonValidImpl extends AmountInputStateNonValid {
  const _$AmountInputStateNonValidImpl(
      {required this.accountId,
      required this.cardId,
      required this.amount,
      this.errorMessage})
      : super._();

  @override
  final String accountId;
  @override
  final String cardId;
  @override
  final Money amount;
  @override
  final String? errorMessage;

  @override
  String toString() {
    return 'AmountInputState.nonValid(accountId: $accountId, cardId: $cardId, amount: $amount, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AmountInputStateNonValidImpl &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.cardId, cardId) || other.cardId == cardId) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, accountId, cardId, amount, errorMessage);

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AmountInputStateNonValidImplCopyWith<_$AmountInputStateNonValidImpl>
      get copyWith => __$$AmountInputStateNonValidImplCopyWithImpl<
          _$AmountInputStateNonValidImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String accountId, String cardId, Money amount)
        idle,
    required TResult Function(String accountId, String cardId, Money amount)
        validating,
    required TResult Function(
            String accountId, String cardId, Money amount, String? errorMessage)
        nonValid,
    required TResult Function(String accountId, String cardId, Money amount)
        valid,
    required TResult Function(String accountId, String cardId, Money amount)
        completed,
  }) {
    return nonValid(accountId, cardId, amount, errorMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String accountId, String cardId, Money amount)? idle,
    TResult? Function(String accountId, String cardId, Money amount)?
        validating,
    TResult? Function(String accountId, String cardId, Money amount,
            String? errorMessage)?
        nonValid,
    TResult? Function(String accountId, String cardId, Money amount)? valid,
    TResult? Function(String accountId, String cardId, Money amount)? completed,
  }) {
    return nonValid?.call(accountId, cardId, amount, errorMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String accountId, String cardId, Money amount)? idle,
    TResult Function(String accountId, String cardId, Money amount)? validating,
    TResult Function(String accountId, String cardId, Money amount,
            String? errorMessage)?
        nonValid,
    TResult Function(String accountId, String cardId, Money amount)? valid,
    TResult Function(String accountId, String cardId, Money amount)? completed,
    required TResult orElse(),
  }) {
    if (nonValid != null) {
      return nonValid(accountId, cardId, amount, errorMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AmountInputStateIdle value) idle,
    required TResult Function(AmountInputStateValidating value) validating,
    required TResult Function(AmountInputStateNonValid value) nonValid,
    required TResult Function(AmountInputStateValid value) valid,
    required TResult Function(AmountInputStateCompleted value) completed,
  }) {
    return nonValid(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AmountInputStateIdle value)? idle,
    TResult? Function(AmountInputStateValidating value)? validating,
    TResult? Function(AmountInputStateNonValid value)? nonValid,
    TResult? Function(AmountInputStateValid value)? valid,
    TResult? Function(AmountInputStateCompleted value)? completed,
  }) {
    return nonValid?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AmountInputStateIdle value)? idle,
    TResult Function(AmountInputStateValidating value)? validating,
    TResult Function(AmountInputStateNonValid value)? nonValid,
    TResult Function(AmountInputStateValid value)? valid,
    TResult Function(AmountInputStateCompleted value)? completed,
    required TResult orElse(),
  }) {
    if (nonValid != null) {
      return nonValid(this);
    }
    return orElse();
  }
}

abstract class AmountInputStateNonValid extends AmountInputState {
  const factory AmountInputStateNonValid(
      {required final String accountId,
      required final String cardId,
      required final Money amount,
      final String? errorMessage}) = _$AmountInputStateNonValidImpl;
  const AmountInputStateNonValid._() : super._();

  @override
  String get accountId;
  @override
  String get cardId;
  @override
  Money get amount;
  String? get errorMessage;

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AmountInputStateNonValidImplCopyWith<_$AmountInputStateNonValidImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AmountInputStateValidImplCopyWith<$Res>
    implements $AmountInputStateCopyWith<$Res> {
  factory _$$AmountInputStateValidImplCopyWith(
          _$AmountInputStateValidImpl value,
          $Res Function(_$AmountInputStateValidImpl) then) =
      __$$AmountInputStateValidImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String accountId, String cardId, Money amount});
}

/// @nodoc
class __$$AmountInputStateValidImplCopyWithImpl<$Res>
    extends _$AmountInputStateCopyWithImpl<$Res, _$AmountInputStateValidImpl>
    implements _$$AmountInputStateValidImplCopyWith<$Res> {
  __$$AmountInputStateValidImplCopyWithImpl(_$AmountInputStateValidImpl _value,
      $Res Function(_$AmountInputStateValidImpl) _then)
      : super(_value, _then);

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? cardId = null,
    Object? amount = null,
  }) {
    return _then(_$AmountInputStateValidImpl(
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      cardId: null == cardId
          ? _value.cardId
          : cardId // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Money,
    ));
  }
}

/// @nodoc

class _$AmountInputStateValidImpl extends AmountInputStateValid {
  const _$AmountInputStateValidImpl(
      {required this.accountId, required this.cardId, required this.amount})
      : super._();

  @override
  final String accountId;
  @override
  final String cardId;
  @override
  final Money amount;

  @override
  String toString() {
    return 'AmountInputState.valid(accountId: $accountId, cardId: $cardId, amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AmountInputStateValidImpl &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.cardId, cardId) || other.cardId == cardId) &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, accountId, cardId, amount);

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AmountInputStateValidImplCopyWith<_$AmountInputStateValidImpl>
      get copyWith => __$$AmountInputStateValidImplCopyWithImpl<
          _$AmountInputStateValidImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String accountId, String cardId, Money amount)
        idle,
    required TResult Function(String accountId, String cardId, Money amount)
        validating,
    required TResult Function(
            String accountId, String cardId, Money amount, String? errorMessage)
        nonValid,
    required TResult Function(String accountId, String cardId, Money amount)
        valid,
    required TResult Function(String accountId, String cardId, Money amount)
        completed,
  }) {
    return valid(accountId, cardId, amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String accountId, String cardId, Money amount)? idle,
    TResult? Function(String accountId, String cardId, Money amount)?
        validating,
    TResult? Function(String accountId, String cardId, Money amount,
            String? errorMessage)?
        nonValid,
    TResult? Function(String accountId, String cardId, Money amount)? valid,
    TResult? Function(String accountId, String cardId, Money amount)? completed,
  }) {
    return valid?.call(accountId, cardId, amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String accountId, String cardId, Money amount)? idle,
    TResult Function(String accountId, String cardId, Money amount)? validating,
    TResult Function(String accountId, String cardId, Money amount,
            String? errorMessage)?
        nonValid,
    TResult Function(String accountId, String cardId, Money amount)? valid,
    TResult Function(String accountId, String cardId, Money amount)? completed,
    required TResult orElse(),
  }) {
    if (valid != null) {
      return valid(accountId, cardId, amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AmountInputStateIdle value) idle,
    required TResult Function(AmountInputStateValidating value) validating,
    required TResult Function(AmountInputStateNonValid value) nonValid,
    required TResult Function(AmountInputStateValid value) valid,
    required TResult Function(AmountInputStateCompleted value) completed,
  }) {
    return valid(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AmountInputStateIdle value)? idle,
    TResult? Function(AmountInputStateValidating value)? validating,
    TResult? Function(AmountInputStateNonValid value)? nonValid,
    TResult? Function(AmountInputStateValid value)? valid,
    TResult? Function(AmountInputStateCompleted value)? completed,
  }) {
    return valid?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AmountInputStateIdle value)? idle,
    TResult Function(AmountInputStateValidating value)? validating,
    TResult Function(AmountInputStateNonValid value)? nonValid,
    TResult Function(AmountInputStateValid value)? valid,
    TResult Function(AmountInputStateCompleted value)? completed,
    required TResult orElse(),
  }) {
    if (valid != null) {
      return valid(this);
    }
    return orElse();
  }
}

abstract class AmountInputStateValid extends AmountInputState {
  const factory AmountInputStateValid(
      {required final String accountId,
      required final String cardId,
      required final Money amount}) = _$AmountInputStateValidImpl;
  const AmountInputStateValid._() : super._();

  @override
  String get accountId;
  @override
  String get cardId;
  @override
  Money get amount;

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AmountInputStateValidImplCopyWith<_$AmountInputStateValidImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AmountInputStateCompletedImplCopyWith<$Res>
    implements $AmountInputStateCopyWith<$Res> {
  factory _$$AmountInputStateCompletedImplCopyWith(
          _$AmountInputStateCompletedImpl value,
          $Res Function(_$AmountInputStateCompletedImpl) then) =
      __$$AmountInputStateCompletedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String accountId, String cardId, Money amount});
}

/// @nodoc
class __$$AmountInputStateCompletedImplCopyWithImpl<$Res>
    extends _$AmountInputStateCopyWithImpl<$Res,
        _$AmountInputStateCompletedImpl>
    implements _$$AmountInputStateCompletedImplCopyWith<$Res> {
  __$$AmountInputStateCompletedImplCopyWithImpl(
      _$AmountInputStateCompletedImpl _value,
      $Res Function(_$AmountInputStateCompletedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? cardId = null,
    Object? amount = null,
  }) {
    return _then(_$AmountInputStateCompletedImpl(
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      cardId: null == cardId
          ? _value.cardId
          : cardId // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Money,
    ));
  }
}

/// @nodoc

class _$AmountInputStateCompletedImpl extends AmountInputStateCompleted {
  const _$AmountInputStateCompletedImpl(
      {required this.accountId, required this.cardId, required this.amount})
      : super._();

  @override
  final String accountId;
  @override
  final String cardId;
  @override
  final Money amount;

  @override
  String toString() {
    return 'AmountInputState.completed(accountId: $accountId, cardId: $cardId, amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AmountInputStateCompletedImpl &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.cardId, cardId) || other.cardId == cardId) &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, accountId, cardId, amount);

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AmountInputStateCompletedImplCopyWith<_$AmountInputStateCompletedImpl>
      get copyWith => __$$AmountInputStateCompletedImplCopyWithImpl<
          _$AmountInputStateCompletedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String accountId, String cardId, Money amount)
        idle,
    required TResult Function(String accountId, String cardId, Money amount)
        validating,
    required TResult Function(
            String accountId, String cardId, Money amount, String? errorMessage)
        nonValid,
    required TResult Function(String accountId, String cardId, Money amount)
        valid,
    required TResult Function(String accountId, String cardId, Money amount)
        completed,
  }) {
    return completed(accountId, cardId, amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String accountId, String cardId, Money amount)? idle,
    TResult? Function(String accountId, String cardId, Money amount)?
        validating,
    TResult? Function(String accountId, String cardId, Money amount,
            String? errorMessage)?
        nonValid,
    TResult? Function(String accountId, String cardId, Money amount)? valid,
    TResult? Function(String accountId, String cardId, Money amount)? completed,
  }) {
    return completed?.call(accountId, cardId, amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String accountId, String cardId, Money amount)? idle,
    TResult Function(String accountId, String cardId, Money amount)? validating,
    TResult Function(String accountId, String cardId, Money amount,
            String? errorMessage)?
        nonValid,
    TResult Function(String accountId, String cardId, Money amount)? valid,
    TResult Function(String accountId, String cardId, Money amount)? completed,
    required TResult orElse(),
  }) {
    if (completed != null) {
      return completed(accountId, cardId, amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AmountInputStateIdle value) idle,
    required TResult Function(AmountInputStateValidating value) validating,
    required TResult Function(AmountInputStateNonValid value) nonValid,
    required TResult Function(AmountInputStateValid value) valid,
    required TResult Function(AmountInputStateCompleted value) completed,
  }) {
    return completed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AmountInputStateIdle value)? idle,
    TResult? Function(AmountInputStateValidating value)? validating,
    TResult? Function(AmountInputStateNonValid value)? nonValid,
    TResult? Function(AmountInputStateValid value)? valid,
    TResult? Function(AmountInputStateCompleted value)? completed,
  }) {
    return completed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AmountInputStateIdle value)? idle,
    TResult Function(AmountInputStateValidating value)? validating,
    TResult Function(AmountInputStateNonValid value)? nonValid,
    TResult Function(AmountInputStateValid value)? valid,
    TResult Function(AmountInputStateCompleted value)? completed,
    required TResult orElse(),
  }) {
    if (completed != null) {
      return completed(this);
    }
    return orElse();
  }
}

abstract class AmountInputStateCompleted extends AmountInputState {
  const factory AmountInputStateCompleted(
      {required final String accountId,
      required final String cardId,
      required final Money amount}) = _$AmountInputStateCompletedImpl;
  const AmountInputStateCompleted._() : super._();

  @override
  String get accountId;
  @override
  String get cardId;
  @override
  Money get amount;

  /// Create a copy of AmountInputState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AmountInputStateCompletedImplCopyWith<_$AmountInputStateCompletedImpl>
      get copyWith => throw _privateConstructorUsedError;
}
