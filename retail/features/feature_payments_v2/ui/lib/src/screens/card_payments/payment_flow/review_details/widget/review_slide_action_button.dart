import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_payments_v2_ui/l10n/payments_localization.g.dart';

class ReviewSlideActionButton extends StatelessWidget {
  final bool isSubmitInProgress;
  final VoidCallback onSubmit;
  final VoidCallback onHorizontalDragEnd;

  const ReviewSlideActionButton({
    required this.isSubmitInProgress,
    required this.onSubmit,
    required this.onHorizontalDragEnd,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = PaymentsLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Label(
            model: LabelModel(
              text: l10n.cardPaymentReviewFooterText1,
              textStyle: CompanyTextStylePointer.b2,
              color: CompanyColorPointer.primary2,
              textAlign: LabelTextAlign.center,
            ),
          ),
          Label(
            model: LabelModel(
              text: l10n.cardPaymentReviewFooterText2,
              textStyle: CompanyTextStylePointer.b2,
              color: CompanyColorPointer.secondary4,
              textAlign: LabelTextAlign.center,
            ),
          ),
          const Space.vertical(24),
          SlideAction(
            onSubmit: () async => onSubmit(),
            onHorizontalDragEnd: onHorizontalDragEnd,
            model: SlideActionModel(
              ctaText: l10n.cardPaymentReviewConfirmCta,
              backgroundColor: CompanyColorPointer.background2,
              lineColor: CompanyColorPointer.secondary3,
              buttonColor: CompanyColorPointer.surface11,
              loadingWidgetColor: CompanyColorPointer.primary4,
              isSubmitted: isSubmitInProgress,
            ),
          ),
        ],
      ),
    );
  }
}
