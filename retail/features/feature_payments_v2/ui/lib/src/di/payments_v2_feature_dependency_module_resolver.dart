// ignore_for_file: deprecated_member_use
import 'package:common_bottom_sheet_api/common_bottom_sheet_api.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:common_wio_feature_kyc_api/kyc_api.dart';
import 'package:di/di.dart';
import 'package:domain/stream/exhaust_stream_executor.dart';
import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:feature_faq_api/feature_faq_api.dart';
import 'package:flow_builder/flow_builder.dart';
import 'package:flutter/material.dart';
import 'package:logging_api/logging.dart';
import 'package:ui/l10n/common_localizations.g.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_customer_address_api/domain/customer_address_interactor.dart';
import 'package:wio_common_feature_customer_address_api/navigation/customer_address_feature_navigation_factory.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_common_feature_payments_ui/wio_common_feature_payments_ui.dart';
import 'package:wio_common_feature_transaction_api/index.dart';
import 'package:wio_common_feature_tutorial_api/feature_tutorial_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_account_api/wio_feature_account_api.dart';
import 'package:wio_feature_clipboard_manager_api/wio_feature_clipboard_manager_api.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_easy_cash_api/easy_cash_api.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_payments_v2_api/payments_v2_api.dart';
import 'package:wio_feature_payments_v2_ui/l10n/payments_localization.g.dart';
import 'package:wio_feature_payments_v2_ui/src/deeplink/payments_deeplink_handler.dart';
import 'package:wio_feature_payments_v2_ui/src/navigation/flow/payments_flow_impl.dart';
import 'package:wio_feature_payments_v2_ui/src/navigation/index.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/add_beneficiary_flow/beneficiary_creation/cubit/beneficiary_creation_v2_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/add_beneficiary_flow/country_selection/cubit/add_bene_country_selection_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/add_beneficiary_flow/currency_selection/cubit/add_bene_currency_selection_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/add_beneficiary_flow/flow/cubit/add_bene_flow_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/all_beneficiaries/cubit/all_beneficiaries_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/all_transfer_limits/all_transfer_limits_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/beneficiary_details/beneficiary_details_mapper.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/beneficiary_details/beneficiary_details_router.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/beneficiary_details/cubit/beneficiary_details_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/add_card_flow/card_details_input/cubit/card_details_input_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/add_card_flow/flow/cubit/add_card_flow_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/card_details/card_details_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/card_settings/cubit/card_settings_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/lending_info/lending_info_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/payment_flow/account_selection/cubit/card_accounts_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/payment_flow/amount_input/cubit/amount_input_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/payment_flow/card_selection/cubit/card_selection_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/payment_flow/flow/cubit/card_payment_flow_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/common/beneficiaries/cubit/beneficiaries_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/common/transfer_limits/transfer_limits_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/dashboard/index.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/dashboard/widgets/direct_debits/cubit/direct_debits_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/dashboard/widgets/pending_requests/cubit/pending_requests_section_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/dashboard/widgets/recurring/cubit/recurring_transfers_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/direct_debits/direct_debit_authority_details/cubit/direct_debit_authority_details_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/authentication/cubit/authentication_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/common/beneficiary_contacts_selection/cubit/beneficiary_contacts_selection_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/common/beneficiary_creation/cubit/npss_beneficiary_creation_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/common/pending_request_card/pending_request_card_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/enrollment/enroll_confirm/cubit/enroll_confirm_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/enrollment/instant_pay_get_started/cubit/get_started_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/inward_request_to_pay/request_details/cubit/inward_rtp_details_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/outward_request_to_pay/amount_enter/cubit/npss_outward_request_amount_enter_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/outward_request_to_pay/outward_request_flow/npss_outward_request_flow_builder.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/outward_request_to_pay/request_details/cubit/outward_rtp_details_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/request_to_pay_details/cubit/request_to_pay_details_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/requests_to_pay_list/cubit/requests_to_pay_list_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/send/amount_enter/cubit/npss_amount_enter_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/send/send_flow/npss_flow_builder.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/send/transfer_details/npss_transfer_details_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/settings/npss_settings_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/pay_bills_dashboard/cubit/pay_bills_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/account_selection/cubit/account_selection_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/additional_beneficiary_info/cubit/additional_beneficiary_info_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/beneficiary_creation/cubit/beneficiary_creation_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/beneficiary_selection/cubit/beneficiary_selection_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/country_selection/cubit/country_selection_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/currency_selection/cubit/currency_selection_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/note_creation/cubit/note_creation_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/payment_creation_flow_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/payment_creation_flow_manager.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/purpose_selection/cubit/purpose_selection_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/rails_selection/cubit/rails_selection_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/subpurpose_selection/cubit/subpurpose_selection_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/recurring_payments/recurring_payment_details/cubit/recurring_payment_details_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/recurring_payments/recurring_payment_notes/cubit/recurring_payment_notes_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/recurring_payments/recurring_transfer_review/cubit/recurring_transfer_review_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/transfer_details/transfer_details_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/transaction_action_handlers/local_transaction_details_ui_actions.dart';
import 'package:wio_feature_payments_v2_ui/src/transaction_action_handlers/resend_request_to_pay_transaction_action_handler.dart';
import 'package:wio_feature_payments_v2_ui/src/transaction_action_handlers/send_reminder_rtp_action_handler.dart';
import 'package:wio_feature_phone_contacts_api/feature_phone_contacts_api.dart';
import 'package:wio_feature_pricing_plan_api/domain/pricing_plan_interactor.dart';
import 'package:wio_feature_product_hub_api/index.dart';
import 'package:wio_feature_referrals_api/index.dart';
import 'package:wio_feature_rewards_ui/feature_rewards_ui.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';
import 'package:wio_feature_share_api/feature_share_api.dart';

class PaymentsV2FeatureDependencyModuleResolver {
  static void register() {
    WioPaymentsFeatureDependencyModuleResolver.register();

    DependencyProvider.registerLazySingleton(() => const PaymentsRouter());

    DependencyProvider.registerLazySingleton<NavigationRouter>(
      () => DependencyProvider.get<PaymentsRouter>(),
      instanceName: PaymentsV2FeatureNavigationConfig.name,
    );

    DependencyProvider.registerLazySingleton(
      () => WioPaymentsLocalizations.of(DependencyProvider.get<BuildContext>()),
    );

    DependencyProvider.registerLazySingleton(
      () => PaymentsLocalizations.of(DependencyProvider.get<BuildContext>()),
    );

    DependencyProvider.registerLazySingleton<PaymentCreationFlowManager>(
      () => PaymentCreationFlowManagerImpl(
        beneficiaryInteractor: DependencyProvider.get<BeneficiaryInteractor>(),
        paymentInteractor: DependencyProvider.get<PaymentInteractor>(),
        accountInteractor: DependencyProvider.get<AccountInteractor>(),
        logger: _domainLogger,
        paymentsAnalytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        l10n: DependencyProvider.get<WioPaymentsLocalizations>(),
        navigator: DependencyProvider.get<NavigationProvider>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );

    DependencyProvider.registerLazySingleton<NpssEnrollNavigationHandler>(
      () => NpssEnrollNavigationHandler(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );
    DependencyProvider.registerLazySingleton<BaseTransactionDetailsUiActions>(
      () => LocalTransactionDetailsUiActions(
        deepLinkProcessor: DependencyProvider.get<DeepLinkProcessor>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
      ),
      instanceName: TransactionType.local.name,
    );

    _mappers();
    _payments();
    _instantPayments();
    _cardPayments();
    _recurringPayments();
    _transferLimits();
    _directDebits();
  }

  static void _mappers() {
    DependencyProvider.registerFactory<RequirementsModelMapper>(
      () => RequirementsModelMapper(
        logger: _domainLogger,
      ),
    );

    DependencyProvider.registerFactory<BeneficiaryDetailsMapper>(
      BeneficiaryDetailsMapper.new,
    );
  }

  static void _recurringPayments() {
    DependencyProvider.registerFactory<RecurringTransfersCubit>(
      () => RecurringTransfersCubit(
        savingSpacesInteractor:
            DependencyProvider.get<SavingSpacesInteractor>(),
        productFlow: DependencyProvider.get<ProductFlow>(),
        interactor: DependencyProvider.get<RecurringTransferInteractor>(),
        navigation: DependencyProvider.get<NavigationProvider>(),
        logger: _domainLogger,
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        paymentsFlow: DependencyProvider.get<PaymentsFlow>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );
  }

  static void _instantPayments() {
    DependencyProvider.registerFactory<AuthenticationCubit>(
      () => AuthenticationCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        npssInteractor: DependencyProvider.get<NPSSInteractor>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        logger: _domainLogger,
        enrollAnalytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        authInteractor: DependencyProvider.get<AuthInteractor>(),
        bottomSheetProvider: DependencyProvider.get<BottomSheetProvider>(),
        contentInteractor: DependencyProvider.get<ContentInteractor>(),
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
      ),
    );

    /// Instant payment Lets get started page
    DependencyProvider.registerFactory<InstantPayGetStartedCubit>(
      () => InstantPayGetStartedCubit(
        enrollNavigationHandler:
            DependencyProvider.get<NpssEnrollNavigationHandler>(),
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
      ),
    );

    /// Instant payment Enrollment confirmation page
    DependencyProvider.registerFactory<InstantPayEnrollConfirmCubit>(
      () => InstantPayEnrollConfirmCubit(
        npssInteractor: DependencyProvider.get<NPSSInteractor>(),
        enrollNavigationHandler:
            DependencyProvider.get<NpssEnrollNavigationHandler>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        logger: _domainLogger,
        enrollAnalytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        bottomSheetProvider: DependencyProvider.get<BottomSheetProvider>(),
        contentInteractor: DependencyProvider.get<ContentInteractor>(),
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
      ),
    );

    /// Instant payment beneficiary creation page
    DependencyProvider.registerFactoryWithParams<
        BeneficiaryContactsSelectionCubit,
        ValueChanged<NPSSBeneficiaryContact?>,
        Object?>(
      (onBeneficiarySelected, _) => BeneficiaryContactsSelectionCubit(
        onBeneficiarySelected: onBeneficiarySelected,
        phoneContactsInteractor:
            DependencyProvider.get<PhoneContactsInteractor>(),
        permissionResolver: DependencyProvider.get<CompanyPermissionResolver>(),
        npssInteractor: DependencyProvider.get<NPSSInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        logger: _domainLogger,
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        referralsFlow: DependencyProvider.get<ReferralsFlow>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );

    /// Instant payment beneficiary creation page
    DependencyProvider.registerFactoryWithParams<NPSSBeneficiaryCreationCubit,
        ValueChanged<NPSSBeneficiaryContact>, Object?>(
      (onBeneficiaryCreated, _) => NPSSBeneficiaryCreationCubit(
        onBeneficiaryCreated: onBeneficiaryCreated,
        npssInteractor: DependencyProvider.get<NPSSInteractor>(),
        requirementsModelMapper:
            DependencyProvider.get<RequirementsModelMapper>(),
        logger: _domainLogger,
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        paymentLocalizations: DependencyProvider.get<PaymentsLocalizations>(),
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<NPSSAmountEnterCubit,
        FlowController<NPSSFlowData>, Object?>(
      (flowController, _) => NPSSAmountEnterCubit(
        flowController: flowController,
        interactor: DependencyProvider.get<NPSSInteractor>(),
        logger: _domainLogger,
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        faqNavigationFlow: DependencyProvider.get<FaqNavigationFlow>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        paymentInteractor: DependencyProvider.get<PaymentInteractor>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<
        NPSSOutwardRequestAmountEnterCubit,
        FlowController<NPSSOutwardRequestFlowData>,
        ValueChanged<Money>>(
      (flowController, onAmountSubmited) => NPSSOutwardRequestAmountEnterCubit(
        flowController: flowController,
        onAmountSubmited: onAmountSubmited,
        logger: _domainLogger,
        npssInteractor: DependencyProvider.get<NPSSInteractor>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        paymentLocalizations:
            DependencyProvider.get<WioPaymentsLocalizations>(),
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    DependencyProvider.registerFactory<NPSSTransferDetailsCubit>(
      () => NPSSTransferDetailsCubit(
        interactor: DependencyProvider.get<NPSSInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        logger: _domainLogger,
        paymentsCustomerFeedbackFlow:
            DependencyProvider.get<PaymentsCustomerFeedbackFlow>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        transactionsMediator:
            DependencyProvider.isRegistered<TransactionsMediator>()
                ? DependencyProvider.get<TransactionsMediator>()
                : null,
        transferSubmissionResultNavigationHandler: DependencyProvider.get<
            NPSSTransferSubmissionResultNavigationHandler>(),
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
      ),
    );

    DependencyProvider.registerFactory<OutwardRtpDetailsCubit>(
      () => OutwardRtpDetailsCubit(
        interactor: DependencyProvider.get<NPSSInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        logger: _domainLogger,
        commonPaymentsLocalizations:
            DependencyProvider.get<WioPaymentsLocalizations>(),
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
        paymentsCustomerFeedbackFlow:
            DependencyProvider.get<PaymentsCustomerFeedbackFlow>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        transactionsMediator:
            DependencyProvider.isRegistered<TransactionsMediator>()
                ? DependencyProvider.get<TransactionsMediator>()
                : null,
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
      ),
    );

    DependencyProvider.registerFactory<InwardRtpDetailsCubit>(
      () => InwardRtpDetailsCubit(
        npssInteractor: DependencyProvider.get<NPSSInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        logger: _domainLogger,
        commonPaymentsLocalizations:
            DependencyProvider.get<WioPaymentsLocalizations>(),
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        transferSubmissionResultNavigationHandler: DependencyProvider.get<
            NPSSTransferSubmissionResultNavigationHandler>(),
        transactionsMediator:
            DependencyProvider.isRegistered<TransactionsMediator>()
                ? DependencyProvider.get<TransactionsMediator>()
                : null,
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );

    DependencyProvider.registerFactory<
        NPSSTransferSubmissionResultNavigationHandler>(
      () => NPSSTransferSubmissionResultNavigationHandler(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        commonPaymentsLocalizations:
            DependencyProvider.get<WioPaymentsLocalizations>(),
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
      ),
    );

    DependencyProvider.registerLazySingleton<TransactionCtaActionHandler>(
      () => SendReminderRequestToPayTransactionActionHandler(
        shareProvider: DependencyProvider.get<ShareProvider>(),
        npssInteractor: DependencyProvider.get<NPSSInteractor>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        logger: _domainLogger,
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
      ),
      instanceName: TransactionCtaActionType.sendRTPReminder.name,
    );

    DependencyProvider.registerLazySingleton<TransactionCtaActionHandler>(
      () => ResendRequestToPayTransactionActionHandler(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
      ),
      instanceName: TransactionCtaActionType.resendRTPRequest.name,
    );

    DependencyProvider.registerFactory<PendingRequestsSectionCubit>(
      () => PendingRequestsSectionCubit(
        npssInteractor: DependencyProvider.get<NPSSInteractor>(),
        logger: _domainLogger,
      ),
    );

    DependencyProvider.registerFactory<RequestsToPayListCubit>(
      () => RequestsToPayListCubit(
        npssInteractor: DependencyProvider.get<NPSSInteractor>(),
        logger: _domainLogger,
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    DependencyProvider.registerFactory<RequestToPayDetailsCubit>(
      () => RequestToPayDetailsCubit(
        npssInteractor: DependencyProvider.get<NPSSInteractor>(),
        logger: _domainLogger,
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        transactionsMediator: DependencyProvider.get<TransactionsMediator>(),
        transferSubmissionResultNavigationHandler: DependencyProvider.get<
            NPSSTransferSubmissionResultNavigationHandler>(),
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        commonPaymentsLocalizations:
            DependencyProvider.get<WioPaymentsLocalizations>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<PendingRequestCardCubit,
        NPSSRequestToPay, void>(
      (request, _) => PendingRequestCardCubit(
        request: request,
        npssInteractor: DependencyProvider.get<NPSSInteractor>(),
        logger: _domainLogger,
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        shareProvider: DependencyProvider.get<ShareProvider>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        commonPaymentsLocalization:
            DependencyProvider.get<WioPaymentsLocalizations>(),
        transactionsMediator: DependencyProvider.get<TransactionsMediator>(),
        transferSubmissionResultNavigationHandler: DependencyProvider.get<
            NPSSTransferSubmissionResultNavigationHandler>(),
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
      ),
    );

    DependencyProvider.registerFactory<NPSSSettingsCubit>(
      () => NPSSSettingsCubit(
        interactor: DependencyProvider.get<NPSSInteractor>(),
        logger: _domainLogger,
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        l10n: DependencyProvider.get<PaymentsLocalizations>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );
  }

  // ignore: long-method
  static void _payments() {
    DependencyProvider.registerFactory<PaymentDashboardCubit>(
      () => PaymentDashboardCubit(
        accountInteractor: DependencyProvider.get<AccountInteractor>(),
        beneficiaryInteractor: DependencyProvider.get<BeneficiaryInteractor>(),
        exhaustStreamExecutor: DependencyProvider.get<ExhaustStreamExecutor>(),
        interactor: DependencyProvider.get<PaymentInteractor>(),
        getPaymentDashboardDetailsUseCase:
            DependencyProvider.get<GetPaymentDashboardDetailsUseCase>(),
        logger: _domainLogger,
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        commonL: DependencyProvider.get<CommonLocalizations>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        paymentsAnalytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        npssInteractor: DependencyProvider.get<NPSSInteractor>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        recurringTransferInteractor:
            DependencyProvider.get<RecurringTransferInteractor>(),
        savingsInteractor: DependencyProvider.get<SavingSpacesInteractor>(),
        productFlow: DependencyProvider.get<ProductFlow>(),
        localization: DependencyProvider.get<PaymentsLocalizations>(),
        transactionsMediator: DependencyProvider.get<TransactionsMediator>(),
        tutorialInteractor: DependencyProvider.get<TutorialInteractor>(),
        easyCashInteractor: DependencyProvider.get<EasyCashInteractor>(),
        easyCashFlow: DependencyProvider.get<EasyCashFlow>(),
        pricingPlanInteractor: DependencyProvider.get<PricingPlanInteractor>(),
        faqNavigationFlow: DependencyProvider.get<FaqNavigationFlow>(),
      ),
    );

    // flow
    DependencyProvider.registerFactory<PaymentCreationFlowCubit>(
      () => PaymentCreationFlowCubit(
        accountInteractor: DependencyProvider.get<AccountInteractor>(),
        beneficiaryInteractor: DependencyProvider.get<BeneficiaryInteractor>(),
        customerAddressInteractor:
            DependencyProvider.get<CustomerAddressInteractor>(),
        paymentInteractor: DependencyProvider.get<PaymentInteractor>(),
        wiseInteractor: DependencyProvider.get<WiseInteractor>(),
        flowManager: DependencyProvider.get<PaymentCreationFlowManager>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        navigator: DependencyProvider.get<NavigationProvider>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        exhaustStreamExecutor: DependencyProvider.get<ExhaustStreamExecutor>(),
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        addressFeatureFactory:
            DependencyProvider.get<CustomerAddressFeatureNavigationFactory>(),
        logger: _domainLogger,
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );

    /// Account selection page
    DependencyProvider.registerFactory<AccountSelectionCubit>(
      () => AccountSelectionCubit(
        accountInteractor: DependencyProvider.get<AccountInteractor>(),
        logger: _domainLogger,
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        flowManager: DependencyProvider.get<PaymentCreationFlowManager>(),
      ),
    );

    /// Country selection page
    DependencyProvider.registerFactory<CountrySelectionCubit>(
      () => CountrySelectionCubit(
        logger: _domainLogger,
        interactor: DependencyProvider.get<PaymentInteractor>(),
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
      ),
    );

    /// Currency selection page
    DependencyProvider.registerFactory<CurrencySelectionCubit>(
      () => CurrencySelectionCubit(
        paymentInteractor: DependencyProvider.get<PaymentInteractor>(),
        amountValidationInteractor:
            DependencyProvider.get<TransferAmountValidationInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        logger: _domainLogger,
        flowManager: DependencyProvider.get<PaymentCreationFlowManager>(),
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        l10n: DependencyProvider.get<WioPaymentsLocalizations>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        faqNavigationFlow: DependencyProvider.get<FaqNavigationFlow>(),
      ),
    );

    /// Rails selection page
    DependencyProvider.registerFactory<RailsSelectionCubit>(
      () => RailsSelectionCubit(
        interactor: DependencyProvider.get<PaymentInteractor>(),
        flowManager: DependencyProvider.get<PaymentCreationFlowManager>(),
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        navigator: DependencyProvider.get<NavigationProvider>(),
        logger: _domainLogger,
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        l10n: DependencyProvider.get<WioPaymentsLocalizations>(),
        passportVerificationFlowService:
            DependencyProvider.get<PassportVerificationFlowService>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        faqNavigationFlow: DependencyProvider.get<FaqNavigationFlow>(),
      ),
    );

    /// Beneficiaries
    DependencyProvider.registerFactoryWithParams<BeneficiariesCubit,
        BeneficiaryFilterModel?, Object?>(
      (beneficiaryFilterModel, _) => BeneficiariesCubit(
        beneficiaryInteractor: DependencyProvider.get<BeneficiaryInteractor>(),
        logger: _domainLogger,
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        beneficiaryFilterModel: beneficiaryFilterModel,
      ),
    );

    /// Beneficiary selection page
    DependencyProvider.registerFactory<BeneficiarySelectionCubit>(
      () => BeneficiarySelectionCubit(
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        flowManager: DependencyProvider.get<PaymentCreationFlowManager>(),
        navigation: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    /// Beneficiary creation page
    DependencyProvider.registerFactory<BeneficiaryCreationCubit>(
      () => BeneficiaryCreationCubit(
        beneficiaryCreationInteractor:
            DependencyProvider.get<BeneficiaryCreationInteractor>(),
        beneficiaryInteractor: DependencyProvider.get<BeneficiaryInteractor>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        requirementsModelMapper:
            DependencyProvider.get<RequirementsModelMapper>(),
        logger: _domainLogger,
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        flowManager: DependencyProvider.get<PaymentCreationFlowManager>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    /// Purpose selection page
    DependencyProvider.registerFactory<PurposeSelectionCubit>(
      () => PurposeSelectionCubit(
        flowManager: DependencyProvider.get<PaymentCreationFlowManager>(),
        paymentInteractor: DependencyProvider.get<PaymentInteractor>(),
        logger: _domainLogger,
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
      ),
    );

    /// Subpurpose selection page
    DependencyProvider.registerFactory<SubpurposeSelectionCubit>(
      () => SubpurposeSelectionCubit(
        paymentInteractor: DependencyProvider.get<PaymentInteractor>(),
        logger: _domainLogger,
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
      ),
    );

    /// Notes creation page
    DependencyProvider.registerFactory<NoteCreationCubit>(
      () => NoteCreationCubit(
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );

    /// Transfer submission page
    DependencyProvider.registerFactoryWithParams<TransferDetailsCubit, String,
        Object?>(
      (id, _) => TransferDetailsCubit(
        id: id,
        interactor: DependencyProvider.get<PaymentInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        logger: _domainLogger,
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        paymentsCustomerFeedbackFlow:
            DependencyProvider.get<PaymentsCustomerFeedbackFlow>(),
        paymentLocalization: DependencyProvider.get<WioPaymentsLocalizations>(),
        transactionsMediator:
            DependencyProvider.isRegistered<TransactionsMediator>()
                ? DependencyProvider.get<TransactionsMediator>()
                : null,
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
      ),
    );

    DependencyProvider.registerFactory<AllBeneficiariesCubit>(
      () => AllBeneficiariesCubit(
        beneficiaryInteractor: DependencyProvider.get<BeneficiaryInteractor>(),
        logger: _domainLogger,
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        navigation: DependencyProvider.get<NavigationProvider>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );

    DependencyProvider.registerFactory<BeneficiaryDetailsCubit>(
      () => BeneficiaryDetailsCubit(
        logger: _domainLogger,
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        router: BeneficiaryDetailsScreenRouter(
          navigationProvider: DependencyProvider.get<NavigationProvider>(),
        ),
        beneficiaryInteractor: DependencyProvider.get<BeneficiaryInteractor>(),
        commonL10n: DependencyProvider.get<WioPaymentsLocalizations>(),
        l10n: DependencyProvider.get<PaymentsLocalizations>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        beneficiaryDetailsMapper:
            DependencyProvider.get<BeneficiaryDetailsMapper>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );

    DependencyProvider.registerLazySingleton<PaymentsFlow>(
      () => PaymentsFlowImpl(
        DependencyProvider.get<NavigationProvider>(),
        DependencyProvider.get<TutorialInteractor>(),
        DependencyProvider.get<PaymentsLocalizations>(),
        DependencyProvider.get<FeatureToggleProvider>(),
        _domainLogger,
        DependencyProvider.get<LendingInteractor>(),
        DependencyProvider.get<PricingPlanInteractor>(),
        DependencyProvider.get<NPSSInteractor>(),
        DependencyProvider.get<CommonErrorHandler>(),
      ),
    );

    DependencyProvider.registerLazySingleton<PaymentsDeepLinkHandler>(
      () => PaymentsDeepLinkHandler(
        paymentsFlow: DependencyProvider.get<PaymentsFlow>(),
        logger: _domainLogger,
      ),
    );

    DependencyProvider.get<DeepLinkHandlerRegister>().register(
      () => DependencyProvider.get<PaymentsDeepLinkHandler>(),
    );

    DependencyProvider.registerFactoryWithParams<AddBeneCountrySelectionCubit,
        AddBeneCountrySelectionDelegate, Object?>(
      (delegate, _) => AddBeneCountrySelectionCubit(
        delegate: delegate,
        logger: _domainLogger,
        interactor: DependencyProvider.get<PaymentInteractor>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<AddBeneCurrencySelectionCubit,
        AddBeneCurrencySelectionDelegate, Object?>(
      (delegate, _) => AddBeneCurrencySelectionCubit(
        delegate: delegate,
        logger: _domainLogger,
        interactor: DependencyProvider.get<PaymentInteractor>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<BeneficiaryCreationV2Cubit,
        BeneficiaryCreationDelegate, Object?>(
      (delegate, _) => BeneficiaryCreationV2Cubit(
        delegate: delegate,
        beneficiaryCreationInteractor:
            DependencyProvider.get<BeneficiaryCreationInteractor>(),
        beneficiaryInteractor: DependencyProvider.get<BeneficiaryInteractor>(),
        requirementsModelMapper:
            DependencyProvider.get<RequirementsModelMapper>(),
        logger: _domainLogger,
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    DependencyProvider.registerFactory<AddBeneFlowCubit>(
      () => AddBeneFlowCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        logger: _domainLogger,
        interactor: DependencyProvider.get<PaymentInteractor>(),
        commonl10n: DependencyProvider.get<WioPaymentsLocalizations>(),
        l10n: DependencyProvider.get<PaymentsLocalizations>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        customerAddressInteractor:
            DependencyProvider.get<CustomerAddressInteractor>(),
        wiseInteractor: DependencyProvider.get<WiseInteractor>(),
        addressFeatureFactory:
            DependencyProvider.get<CustomerAddressFeatureNavigationFactory>(),
      ),
    );

    DependencyProvider.registerFactory<AdditionalBeneficiaryInfoCubit>(
      () => AdditionalBeneficiaryInfoCubit(
        beneficiaryCreationInteractor:
            DependencyProvider.get<BeneficiaryCreationInteractor>(),
        beneficiaryInteractor: DependencyProvider.get<BeneficiaryInteractor>(),
        requirementsModelMapper:
            DependencyProvider.get<RequirementsModelMapper>(),
        logger: _domainLogger,
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        flowManager: DependencyProvider.get<PaymentCreationFlowManager>(),
      ),
    );
  }

  static void _cardPayments() {
    DependencyProvider.registerFactory<CardDetailsInputCubit>(
      () => CardDetailsInputCubit(
        cardPaymentsInteractor:
            DependencyProvider.get<CardPaymentsInteractor>(),
        logger: _domainLogger,
      ),
    );

    DependencyProvider.registerFactory<PayBillsCubit>(
      () => PayBillsCubit(
        cardPaymentsInteractor:
            DependencyProvider.get<CardPaymentsInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        paymentsAnalytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );

    DependencyProvider.registerFactory<AddCardFlowCubit>(
      () => AddCardFlowCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        cardPaymentsInteractor:
            DependencyProvider.get<CardPaymentsInteractor>(),
        paymentsLocalizations: DependencyProvider.get<PaymentsLocalizations>(),
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
        wioPaymentsAnalytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        pricingPlanInteractor: DependencyProvider.get<PricingPlanInteractor>(),
        logger: _domainLogger,
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );

    DependencyProvider.registerFactory<LendingInfoCubit>(
      () => LendingInfoCubit(
        lendingInteractor: DependencyProvider.get<LendingInteractor>(),
        logger: _domainLogger,
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    DependencyProvider.registerFactory<CardSelectionCubit>(
      () => CardSelectionCubit(
        cardPaymentsInteractor:
            DependencyProvider.get<CardPaymentsInteractor>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<CardPaymentFlowCubit, bool,
        Object?>(
      (isSpecialPlan, _) => CardPaymentFlowCubit(
        cardPaymentsInteractor:
            DependencyProvider.get<CardPaymentsInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        wioPaymentsAnalytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        wioPaymentsLocalizations:
            DependencyProvider.get<WioPaymentsLocalizations>(),
        paymentInteractor: DependencyProvider.get<PaymentInteractor>(),
        paymentsCustomerFeedbackFlow:
            DependencyProvider.get<PaymentsCustomerFeedbackFlow>(),
        paymentsLocalizations: DependencyProvider.get<PaymentsLocalizations>(),
        transactionsMediator: DependencyProvider.get<TransactionsMediator>(),
        isSpecialPlan: isSpecialPlan,
        lendingAccountInteractor:
            DependencyProvider.get<LoanAccountInteractor>(),
        rewardsRouter: DependencyProvider.get<RewardsRouter>(),
        lendingApplicationEntryPointHandler:
            DependencyProvider.get<LendingApplicationEntryPointHandler>(),
      ),
    );

    DependencyProvider.registerFactory<CardSettingsCubit>(
      () => CardSettingsCubit(
        paymentsLocalizations: DependencyProvider.get<PaymentsLocalizations>(),
        cardPaymentsInteractor:
            DependencyProvider.get<CardPaymentsInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
      ),
    );

    DependencyProvider.registerFactory<CardAccountsCubit>(
      () => CardAccountsCubit(
        accountInteractor: DependencyProvider.get<AccountInteractor>(),
        logger: _domainLogger,
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        pricingPlanInteractor: DependencyProvider.get<PricingPlanInteractor>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );

    DependencyProvider.registerFactory<RecurringTransferReviewCubit>(
      () => RecurringTransferReviewCubit(
        recurringTransferInteractor:
            DependencyProvider.get<RecurringTransferInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        logger: _domainLogger,
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
      ),
    );

    DependencyProvider.registerFactoryWithParams<AmountInputCubit,
        AmountInputParams, void>(
      (params, _) => AmountInputCubit(
        params: params,
        paymentInteractor: DependencyProvider.get<PaymentInteractor>(),
        amountValidationInteractor:
            DependencyProvider.get<TransferAmountValidationInteractor>(),
        logger: _domainLogger,
        faqNavigationFlow: DependencyProvider.get<FaqNavigationFlow>(),
        analytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        l10n: DependencyProvider.get<WioPaymentsLocalizations>(),
      ),
    );

    DependencyProvider.registerFactory<RecurringPaymentDetailsCubit>(
      () => RecurringPaymentDetailsCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        interactor: DependencyProvider.get<RecurringTransferInteractor>(),
        logger: _domainLogger,
        paymentsAnalytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        localization: DependencyProvider.get<PaymentsLocalizations>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
      ),
    );

    DependencyProvider.registerFactory<CardDetailsCubit>(
      () => CardDetailsCubit(
        cardPaymentsInteractor:
            DependencyProvider.get<CardPaymentsInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
      ),
    );

    DependencyProvider.registerFactory<RecurringPaymentNotesCubit>(
      () => RecurringPaymentNotesCubit(
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        navigator: DependencyProvider.get<NavigationProvider>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        interactor: DependencyProvider.get<RecurringTransferInteractor>(),
        logger: _domainLogger,
        paymentsAnalytics: DependencyProvider.get<WioPaymentsAnalytics>(),
        localization: DependencyProvider.get<PaymentsLocalizations>(),
      ),
    );

    DependencyProvider.registerFactory<TransferLimitsCubit>(
      () => TransferLimitsCubit(
        paymentInteractor: DependencyProvider.get<PaymentInteractor>(),
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        logger: _domainLogger,
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
      ),
    );
  }

  static void _transferLimits() {
    DependencyProvider.registerFactory<AllTransferLimitsCubit>(
      () => AllTransferLimitsCubit(
        accountInteractor: DependencyProvider.get<AccountInteractor>(),
        paymentInteractor: DependencyProvider.get<PaymentInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        faqFlow: DependencyProvider.get<FaqNavigationFlow>(),
        logger: _domainLogger,
      ),
    );
  }

  static void _directDebits() {
    DependencyProvider.registerFactory<DirectDebitAuthorityDetailsCubit>(
      () => DirectDebitAuthorityDetailsCubit(
        directDebitsInteractor:
            DependencyProvider.get<DirectDebitsInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        logger: _domainLogger,
        commonErrorHandler: DependencyProvider.get<CommonErrorHandler>(),
        clipboardManager: DependencyProvider.get<ClipboardManager>(),
        toastMessageProvider: DependencyProvider.get<ToastMessageProvider>(),
      ),
    );

    DependencyProvider.registerFactory<DirectDebitsCubit>(
      () => DirectDebitsCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );
  }

  static Logger get _domainLogger => DependencyProvider.get<Logger>(
        instanceName: WioDomain.payments.name,
      );
}
