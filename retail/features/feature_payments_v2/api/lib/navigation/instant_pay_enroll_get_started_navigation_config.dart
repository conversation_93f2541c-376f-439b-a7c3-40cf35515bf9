import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_payments_v2_api/navigation/payments_v2_feature_navigation_config.dart';

part 'instant_pay_enroll_get_started_navigation_config.freezed.dart';

@freezed
class InstantPayEnrollGetStartedScreenNavigationConfig
    extends ScreenNavigationConfig
    with _$InstantPayEnrollGetStartedScreenNavigationConfig {
  static const screenId = 'instant_pay_get_started_confirm';

  const factory InstantPayEnrollGetStartedScreenNavigationConfig({
    required Source source,
  }) = _InstantPayEnrollGetStartedScreenNavigationConfig;

  const InstantPayEnrollGetStartedScreenNavigationConfig._()
      : super(
          feature: PaymentsV2FeatureNavigationConfig.name,
          id: screenId,
          isScreenTracking: false,
        );

  @override
  String toString() => 'InstantPayEnrollGetStartedScreenNavigationConfig{}';
}
