import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_payments_v2_api/navigation/payments_v2_feature_navigation_config.dart';

@immutable
final class NPSSSettingsScreenNavigationConfig extends ScreenNavigationConfig {
  static const screenId = 'npss_settings_screen';

  const NPSSSettingsScreenNavigationConfig()
      : super(
          feature: PaymentsV2FeatureNavigationConfig.name,
          id: screenId,
        );

  @override
  String toString() => 'NPSSSettingsScreenNavigationConfig';
}
