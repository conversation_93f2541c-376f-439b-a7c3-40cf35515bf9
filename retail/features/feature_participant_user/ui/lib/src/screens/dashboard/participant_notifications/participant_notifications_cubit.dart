import 'package:logging_api/logging.dart';
import 'package:ui/ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_critical_notification_api/feature_critical_notification_api.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_participant_user_api/navigation/participant_dashboard_screen_navigation_config.dart';
import 'package:wio_feature_participant_user_ui/src/screens/dashboard/participant_notifications/participant_notifications_state.dart';

class ParticipantNotificationsCubit
    extends BaseCubit<ParticipantNotificationsState> {
  static const _currentScreen = ParticipantDashboardScreenNavigationConfig.name;

  final CriticalNotificationInteractor _notificationInteractor;
  final CriticalNotificationAnalytics _notificationAnalytics;
  final Logger _logger;
  final NavigationProvider _navigationProvider;
  final LoadingProvider _loadingProvider;
  final CommonErrorHandler _errorHandler;

  ParticipantNotificationsCubit({
    required CriticalNotificationInteractor notificationInteractor,
    required CriticalNotificationAnalytics notificationAnalytics,
    required Logger logger,
    required NavigationProvider navigationProvider,
    required LoadingProvider loadingProvider,
    required CommonErrorHandler errorHandler,
  })  : _notificationInteractor = notificationInteractor,
        _notificationAnalytics = notificationAnalytics,
        _logger = logger,
        _navigationProvider = navigationProvider,
        _loadingProvider = loadingProvider,
        _errorHandler = errorHandler,
        super(const ParticipantNotificationsState.initial());

  @override
  String toString() => 'ParticipantNotificationsCubit';

  void initialize() {
    state.mapOrNull(initial: (_) => _handleInitialize());
  }

  Future<void> onRefresh() {
    return state.maybeMap(
      idle: (_) => _handleInitialize(),
      orElse: Future.value,
    );
  }

  // Logic is stolen from retail dashboard
  void onOpenInbox() {
    state.mapOrNull(
      idle: (_) {
        _navigationProvider.navigateTo(
          const CriticalNotificationFeatureNavigationConfig(),
        );
        _refreshUnreadCount().ignore();
        _refreshCriticalNotifications().ignore();
      },
    );
  }

  void onOpenNotification(CriticalNotification notification) {
    state.mapOrNull(
      idle: (_) {
        _handleReadNotification(notification).ignore();
        _navigationProvider.showBottomSheet(
          NotificationDetailsBottomSheetConfig(
            notification: notification,
            fromScreen: _currentScreen,
          ),
        );
      },
    );
  }

  void onCloseNotification(CriticalNotification notification) {
    state.mapOrNull(
      idle: (_) => _handleCloseNotification(notification),
    );
  }

  // Private
  Future<void> _handleInitialize() async {
    emit(
      ParticipantNotificationsState.loading(
        hasNotifications: state.hasNotifications,
      ),
    );

    final (unreadCount, notifications) = await (
      _getUnreadCount(),
      _getCriticalNotifications(),
    ).wait;

    safeEmit(
      ParticipantNotificationsState.idle(
        unreadCount: unreadCount,
        notifications: notifications,
      ),
    );
  }

  Future<void> _refreshUnreadCount() async {
    final unreadCount = await _getUnreadCount();

    state.mapOrNull(
      idle: (it) => safeEmit(it.copyWith(unreadCount: unreadCount)),
    );
  }

  Future<void> _refreshCriticalNotifications() async {
    final notifications = await _getCriticalNotifications();

    state.mapOrNull(
      idle: (it) => safeEmit(it.copyWith(notifications: notifications)),
    );
  }

  Future<int> _getUnreadCount() async {
    try {
      return await _notificationInteractor.unreadCount();
    } on Object catch (error, trace) {
      _logger.error(
        'Failed to fetch unread notification count',
        error: error,
        stackTrace: trace,
      );

      return 0;
    }
  }

  Future<List<CriticalNotification>> _getCriticalNotifications() async {
    try {
      final result = await _notificationInteractor.getNotifications(
        priority: NotificationPriority.critical,
        closed: false,
        size: 10000,
      );

      return result.notifications;
    } on Object catch (error, trace) {
      _logger.error(
        'Failed to fetch critical notifications',
        error: error,
        stackTrace: trace,
      );

      return const [];
    }
  }

  Future<void> _handleReadNotification(
    CriticalNotification notification,
  ) async {
    if (notification.read) {
      return _logger.debug('Notification ${notification.id} is already read');
    }

    var read = false;

    try {
      read = await _notificationInteractor.read([notification.id]);
      if (read) {
        safeEmit(state.markNotificationRead(notification.id));
        _refreshUnreadCount().ignore();
      }
    } on Object catch (error, trace) {
      _logger.error(
        'Failed to read notification: ${notification.id}',
        error: error,
        stackTrace: trace,
      );
    }

    _notificationAnalytics.readNotification(
      notification: notification,
      fromScreen: _currentScreen,
      isSuccess: read,
    );
  }

  Future<void> _handleCloseNotification(
    CriticalNotification notification,
  ) async {
    _loadingProvider.loading(true);

    var closed = false;
    try {
      closed = await _notificationInteractor.close(notification.id);
      if (closed) {
        safeEmit(state.markNotificationClosed(notification.id));
      }
    } on Object catch (error, trace) {
      _errorHandler.handleError(error);
      _logger.debug(
        'Failed to close notification: ${notification.id}',
        error: error,
        stackTrace: trace,
      );
    } finally {
      _loadingProvider.loading(false);
    }

    _notificationAnalytics.closeNotification(
      notification: notification,
      fromScreen: _currentScreen,
      isSuccess: closed,
    );
  }
}
