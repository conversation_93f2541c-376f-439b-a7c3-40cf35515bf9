import 'package:di/di.dart';
import 'package:feature_cards_ui/feature_cards_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_participant_user_ui/feature_participant_user_mobile_ui.dart';
import 'package:wio_feature_participant_user_ui/src/common/helpers.dart';
import 'package:wio_feature_participant_user_ui/src/screens/dashboard/participant_accounts/participant_accounts_cubit.dart';
import 'package:wio_feature_participant_user_ui/src/screens/dashboard/participant_accounts/participant_accounts_tab.dart';
import 'package:wio_feature_participant_user_ui/src/screens/dashboard/participant_accounts/participant_balance_section.dart';
import 'package:wio_feature_participant_user_ui/src/screens/dashboard/participant_dashboard_cubit.dart';
import 'package:wio_feature_participant_user_ui/src/screens/dashboard/participant_dashboard_state.dart';
import 'package:wio_feature_participant_user_ui/src/screens/dashboard/participant_notifications/participant_inbox.dart';
import 'package:wio_feature_participant_user_ui/src/screens/dashboard/participant_notifications/participant_notifications.dart';
import 'package:wio_feature_participant_user_ui/src/screens/dashboard/participant_notifications/participant_notifications_cubit.dart';
import 'package:wio_feature_participant_user_ui/src/screens/dashboard/widgets/dashboard_animation.dart';

part 'widgets/dashboard_header.dart';

enum _DashboardTab { cards, accounts }

class ParticipantDashboardScreen extends StatelessWidget {
  const ParticipantDashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const _DashboardDependencies(
      child: ParticipantDashboardAnimationProvider(
        child: _DashboardScreen(),
      ),
    );
  }
}

class _DashboardDependencies extends StatelessWidget {
  final Widget child;

  const _DashboardDependencies({required this.child});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ParticipantAccountsCubit>(
          create: (_) =>
              DependencyProvider.get<ParticipantAccountsCubit>()..initialize(),
        ),
        BlocProvider<ParticipantNotificationsCubit>(
          create: (_) => DependencyProvider.get<ParticipantNotificationsCubit>()
            ..initialize(),
        ),
        BlocProvider<ParticipantCardsCubit>(
          create: (_) =>
              DependencyProvider.get<ParticipantCardsCubit>()..initialize(),
        ),
      ],
      child: child,
    );
  }
}

class _DashboardScreen
    extends BasePage<ParticipantDashboardState, ParticipantDashboardCubit> {
  const _DashboardScreen();

  @override
  ParticipantDashboardCubit createBloc() =>
      DependencyProvider.get<ParticipantDashboardCubit>();

  @override
  void initBloc(ParticipantDashboardCubit bloc) => bloc.initialize();

  @override
  Widget buildPage(
    BuildContext context,
    ParticipantDashboardCubit bloc,
    ParticipantDashboardState state,
  ) =>
      Scaffold(
        backgroundColor: context.colorStyling.background3,
        body: SafeArea(
          bottom: false,
          child: ColoredBox(
            color: context.colorStyling.background1,
            child: const ParticipantDashboardFade(child: _DashboardContent()),
          ),
        ),
      );
}

class _DashboardContent extends StatelessWidget {
  const _DashboardContent();

  @override
  Widget build(BuildContext context) {
    final state = context.watch<ParticipantDashboardCubit>().state;

    return switch (state) {
      InitialParticipantDashboardState() => const Space.shrink(),
      IdleParticipantDashboardState() => _IdleDashboardContent(state: state),
    };
  }
}

class _IdleDashboardContent extends StatefulWidget {
  final IdleParticipantDashboardState state;

  const _IdleDashboardContent({required this.state});

  @override
  State<_IdleDashboardContent> createState() => _IdleDashboardContentState();
}

class _IdleDashboardContentState extends State<_IdleDashboardContent>
    with SingleTickerProviderStateMixin {
  late var _currentTab = _DashboardTab.cards;
  late final _tabController = TabController(
    vsync: this,
    length: _DashboardTab.values.length,
    initialIndex: _currentTab.index,
    animationDuration: const Duration(milliseconds: 400),
  );

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final duration =
          ModalRoute.of(context)?.transitionDuration ?? Duration.zero;

      Future.delayed(duration, ParticipantDashboardAnimation.of(context).start);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<ParticipantDashboardCubit>();
    const availableTabs = _DashboardTab.values;
    final currentUser = widget.state.user;

    return _DashboardRefresher(
      child: _DashboardLayout(
        header: _DashboardHeader(
          firstName: currentUser.firstNameCapitalized,
          fullName: currentUser.fullName,
          balance: const ParticipantBalanceSection(),
          inbox: const ParticipantInbox(),
          notifications: const ParticipantNotificationsSection(
            padding: EdgeInsets.only(top: 24),
          ),
          onAvatarPressed: cubit.onOpenProfile,
        ),
        tabs: _DashboardTabs(
          tabs: availableTabs,
          currentTab: _currentTab,
          onSelect: _handleChangeTab,
        ),
        child: ParticipantDashboardSlide(
          from: const Offset(0, 0.05),
          child: _DashboardTabContent(
            tabs: availableTabs,
            controller: _tabController,
          ),
        ),
      ),
    );
  }

  void _handleChangeTab(_DashboardTab newTab) {
    if (newTab != _currentTab) {
      setState(() => _currentTab = newTab);
      _tabController.animateTo(newTab.index);
    }
  }
}

class _DashboardTabContent extends StatelessWidget {
  final List<_DashboardTab> tabs;
  final TabController controller;

  const _DashboardTabContent({
    required this.tabs,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return TabBarView(
      controller: controller,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        for (final tab in tabs)
          switch (tab) {
            _DashboardTab.cards => const ParticipantCardsTab(),
            _DashboardTab.accounts => const ParticipantAccountsTab(),
          },
      ],
    );
  }
}

class _DashboardRefresher extends StatelessWidget {
  final Widget child;

  const _DashboardRefresher({required this.child});

  @override
  Widget build(BuildContext context) {
    return MaterialRefreshIndicator(
      // OCD moment: this is to have the loading indicator on the same level
      // as the user avatar.
      displacement: 24,
      edgeOffset: 0,
      notificationPredicate: (notification) => notification.depth == 2,
      onRefresh: () => _handleRefresh(context),
      child: child,
    );
  }

  Future<void> _handleRefresh(BuildContext context) => [
        context.read<ParticipantAccountsCubit>().onRefresh(),
        context.read<ParticipantNotificationsCubit>().onRefresh(),
        context.read<ParticipantCardsCubit>().onRefresh(),
      ].wait;
}

class _DashboardLayout extends StatelessWidget {
  final Widget header;
  final Widget child;
  final Widget? tabs;

  const _DashboardLayout({
    required this.header,
    required this.child,
    this.tabs,
  });

  @override
  Widget build(BuildContext context) {
    return NestedScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      headerSliverBuilder: (_, __) => [
        SliverToBoxAdapter(child: header),
        if (tabs case final tabs?) PinnedHeaderSliver(child: tabs),
      ],
      body: child,
    );
  }
}
