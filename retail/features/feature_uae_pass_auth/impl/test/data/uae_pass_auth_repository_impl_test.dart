import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_feature_onboarding_data_api/onboarding_unsecure_data_api.dart'
    as dto;
import 'package:wio_feature_onboarding_domain_api/index.dart';
import 'package:wio_feature_uae_pass_auth_impl/data/uae_pass_auth_repository_impl.dart';
import '../feature_uae_pass_auth_impl_mocks.dart';

void main() {
  late MockUaePassMapper mapper;
  late MockUaePassAuthService service;
  late UaePassAuthRepositoryImpl repository;

  setUp(() {
    mapper = MockUaePassMapper();
    service = MockUaePassAuthService();

    repository = UaePassAuthRepositoryImpl(
      mapper: mapper,
      service: service,
    );

    registerFallbackValue(UserDetailsRequestFake());
    registerFallbackValue(UserDetailsWithTokenFake());
  });

  test('Test getUser function', () async {
    // Arrange
    final mockServiceAnswer = dto.UserDetailsWithToken(
      token: 'token',
      userAttributes: dto.UserAttributes(
        uaePassId: 'uaePassId',
        email: 'email',
        mobile: 'mobile',
      ),
      allowedAction: dto.UserDetailsWithTokenAllowedAction.signUp,
    );
    final mockRequestDto = dto.UserDetailsRequest(authCode: 'code');
    const mockDomainResult = UserDetailsWithToken(
      token: 'token',
      userAttributes: UserAttributes(
        uaePassId: 'uaePassId',
        email: 'email',
        mobile: 'mobile',
      ),
      allowedAction: UserDetailsAllowedAction.signUp,
    );

    when(() => service.getUser(body: any(named: 'body')))
        .justAnswerAsync(mockServiceAnswer);
    when(() => mapper.toUserDetailsWithToken(any()))
        .thenReturn(mockDomainResult);

    // Act
    final result = await repository.getUser(code: 'code');

    // Assert
    verify(() => service.getUser(body: mockRequestDto));
    verify(() => mapper.toUserDetailsWithToken(mockServiceAnswer));
    expect(result, mockDomainResult);
  });
}
