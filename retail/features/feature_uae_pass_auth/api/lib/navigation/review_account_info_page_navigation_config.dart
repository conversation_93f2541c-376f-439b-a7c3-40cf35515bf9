import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_onboarding_domain_api/index.dart';
import 'package:wio_feature_uae_pass_auth_api/navigation/uae_pass_auth_feature_navigation_config.dart';

part 'review_account_info_page_navigation_config.freezed.dart';

@freezed
class ReviewAccountInfoPageNavigationConfig extends ScreenNavigationConfig
    with _$ReviewAccountInfoPageNavigationConfig {
  static const screenId = 'review_account_info_page';

  const factory ReviewAccountInfoPageNavigationConfig({
    required UserAttributes userAttributes,
    required String token,
    required String acceptedTermsAndConditionsTime,
    required bool hasAgreedForMarketing,
  }) = _ReviewAccountInfoPageNavigationConfig;

  const ReviewAccountInfoPageNavigationConfig._()
      : super(
          id: screenId,
          feature: UaePassAuthFeatureNavigationConfig.name,
        );

  @override
  String toString() => 'ReviewAccountInfoPageNavigationConfig';
}
