import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_debug_menu_api/feature_debug_menu_api.dart';

part 'debug_state.freezed.dart';

@freezed
class DebugState with _$DebugState {
  const factory DebugState({
    @Default(<DebugMenuFeatureToggle>[])
    List<DebugMenuFeatureToggle> featureToggles,
    @Default(<String>[]) List<String> logsChunks,
    @Default(false) bool isSynchronizing,
    String? lastTogglesSyncTime,
    String? airshipChannelId,
    String? customerId,
  }) = _DebugState;
}
