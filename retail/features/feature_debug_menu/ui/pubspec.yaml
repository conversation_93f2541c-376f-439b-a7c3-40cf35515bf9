name: feature_debug_menu_ui
version: 0.0.1
publish_to: none
environment: 
  flutter: 3.27.3
  sdk: '>=3.6.0 <4.0.0'
dependencies: 
  common_feature_analytics_ui: 
    path: ../../../../common/features/feature_analytics/ui
  common_feature_toggle_api: 
    path: ../../../../common/tools/feature_toggle/api
  common_feature_toggle_ui: 
    path: ../../../../common/tools/feature_toggle/ui
  di: 
    path: ../../../../core/di
  firebase_core: 3.10.0
  firebase_remote_config: 5.3.0
  flutter: 
    sdk: flutter
  flutter_bloc: 9.0.0
  freezed_annotation: 2.4.4
  logging_api: 
    path: ../../../../core/logging/api
  ui: 
    path: ../../../../core/ui
  ui_kit_legacy_core: 
    path: ../../../../ui_kit_legacy/ui_kit_core
  ui_kit_legacy_mobile: 
    path: ../../../../ui_kit_legacy/ui_kit_mobile
  wio_app_core_api: 
    path: ../../../../core/app_core/api
  wio_core_navigation_api: 
    path: ../../../../core/navigation/api
  wio_core_navigation_ui: 
    path: ../../../../core/navigation/ui
  wio_feature_common_toast_message_api: 
    path: ../../../../common/tools/toast_message/api
  wio_feature_debug_menu_api: 
    path: ../../../../common/features/feature_debug_menu/api
  wio_feature_force_upgrade_api: 
    path: ../../../../common/features/feature_force_upgrade/api
  wio_feature_network_monitoring_api: 
    path: ../../../../common/features/feature_network_monitoring/api
  wio_feature_proxy_ui: 
    path: ../../../../common/features/feature_proxy/ui
  wio_feature_share_api: 
    path: ../../../../common/tools/feature_share/api
  wio_feature_user_api: 
    path: ../../../../common/features/feature_user/api
  wio_retail_dotenv: 
    path: ../../../core/dotenv
dev_dependencies: 
  build_runner: 2.4.14
  core_lints: 
    path: ../../../../tooling/core_lints
  flutter_test: 
    sdk: flutter
  freezed: 2.5.7
  mocktail: 1.0.4
  tests: 
    path: ../../../../core/tests/impl
