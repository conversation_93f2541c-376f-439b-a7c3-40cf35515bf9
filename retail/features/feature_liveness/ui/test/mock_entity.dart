import 'package:wio_feature_face_recognition_api/navigation/face_recognition_feature_navigation_config.dart';
import 'package:wio_feature_liveness_api/index.dart';
import 'package:wio_feature_retail_identity_domain_api/index.dart';
import 'package:wio_feature_two_factor_auth_api/index.dart';

const mockConfigurationData = ConfigurationData(
  configurationData: 'configurationData',
  secretKey: 'secretKey',
);

const faceRecognitionSuccessResult = FaceRecognitionSuccessResult(
  faceImage: 'faceImage',
  faceImageHash: 'faceImageHash',
  thumbnail: 'thumbnail',
);

const twoFaAuthEfrModel = TwoFactorAuthModel.efr(
  type: TwoFactorAuthType.efr,
  data: 'faceImage',
  dataHash: 'faceImageHash',
  tag: 'thumbnail',
);

const efrFailureResult = FaceRecognitionFailureResult(
  message: 'Failure result',
);
