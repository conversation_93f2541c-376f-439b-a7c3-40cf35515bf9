import 'dart:async';
import 'dart:typed_data';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_file_picker_api/file_picker_api.dart';
import 'package:wio_common_file_picker_ui/file_picker_ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/feature_lending_ui.dart';
import 'package:wio_feature_lending_ui/src/navigation/configs/lending_pdf_viewer_navigation_config.dart';
import 'package:wio_feature_lending_ui/src/screens/lending_pdf_viewer/config/lending_pdf_viewer_config.dart';
import 'package:wio_feature_lending_ui/src/screens/lending_upload/config/lending_upload_config.dart';
import 'package:wio_feature_lending_ui/src/screens/lending_upload/delegates/lending_upload_delegate.dart';

part 'lending_document_upload_cubit.freezed.dart';
part 'lending_document_upload_state.dart';

// The max. file size in bytes that can be uploaded
const maxFileSizeLimit = 1e7;

class LendingDocumentUploadCubit extends BaseCubit<LendingDocumentUploadState> {
  final Logger _logger;
  final LendingDocumentUploadDelegate _delegate;
  final LendingLocalizations _localizations;
  final NavigationProvider _navigationProvider;
  final LendingExceptionHandler _errorHandler;
  final ResponsiveDialogProvider _responsiveDialogProvider;
  final LendingUploadConfig _config;
  final LendingAnalytics _analytics;

  LendingDocumentUploadCubit({
    required Logger logger,
    required LendingDocumentUploadDelegate delegate,
    required LendingLocalizations localizations,
    required NavigationProvider navigationProvider,
    required LendingExceptionHandler errorHandler,
    required ResponsiveDialogProvider responsiveDialogProvider,
    required LendingUploadConfig config,
    required LendingAnalytics analytics,
  })  : _logger = logger,
        _delegate = delegate,
        _localizations = localizations,
        _navigationProvider = navigationProvider,
        _errorHandler = errorHandler,
        _responsiveDialogProvider = responsiveDialogProvider,
        _config = config,
        _analytics = analytics,
        super(const LendingDocumentUploadState.loading());

  Future<void> init() async {
    try {
      state.mapOrNull(
        error: (_) => emit(const LendingDocumentUploadState.loading()),
        ready: (_) => emit(const LendingDocumentUploadState.loading()),
        processing: (_) => emit(const LendingDocumentUploadState.loading()),
      );

      final (exampleUrl, docs) = await (
        _delegate.getExampleDocUrl(),
        _delegate.uploadedDocuments.wait
      ).wait;

      safeEmit(
        LendingDocumentUploadState.ready(
          documentExampleUrl: exampleUrl,
          documents: docs,
        ),
      );
    } on Object catch (error) {
      _onError(error);
      safeEmit(
        const LendingDocumentUploadState.error(message: 'INITIALISING_ERROR'),
      );
    }
  }

  void openVatExample() {
    state.mapOrNull(
      ready: (readyState) {
        _navigationProvider.push(
          LendingPdfViewerNavigationConfig(
            config: LendingPdfViewerConfig(
              documentUrl: readyState.documentExampleUrl,
            ),
            loanProductIdentifier: _delegate.loanProductIdentifier,
          ),
        );
      },
    );
  }

  Future<void> submit() async {
    await state.mapOrNull(
      ready: (readyState) async {
        try {
          safeEmit(readyState.toProcessing());

          _analytics.applicationStepSubmit(_delegate.uiId);

          await _delegate.submit(_config);

          safeEmit(readyState);

          _logger.info(
            '''Lending document(${_delegate.type}) uploaded successfully.''',
          );
        } on Object catch (e) {
          safeEmit(readyState);
          _errorHandler.handle(e, _delegate.uiId);
        }
      },
    );
  }

  Future<void> openFilePicker() async {
    await state.mapOrNull(
      ready: (readyState) async {
        _logger.info('Opening file picker...');

        final file = await _responsiveDialogProvider
            .showBottomSheetOrDialog<CrossPlatformFile>(
          content: CrossPlatformFilePickerBottomSheet(
            model: FilePickerBottomSheetModel(
              bottomSheetTitle:
                  _localizations.lendingFilePickerBottomSheetTitle,
              allowedExtensions: ['pdf'],
              showCameraOption: false,
              showGalleryOption: false,
            ),
            onOptionSelected: (file) {
              _navigationProvider.goBack(file);
            },
          ),
          config: const ResponsiveModalConfig(
            featureName: LendingFeatureNavigationConfig.name,
          ),
        );

        if (file == null) {
          return;
        }

        final (fileName, fileData) = file.map(
          mobile: (value) => (
            value.file.path.split('/').last,
            value.file.readAsBytesSync(),
          ),
          web: (value) => (value.fileName, value.bytes),
        );

        final fileAlreadyAdded = state.documents.any(
          (doc) => doc.docName == fileName,
        );

        if (fileAlreadyAdded) {
          _responsiveDialogProvider.showResponsiveToastMessage(
            NotificationToastMessageConfiguration.error(
              _localizations.lendingDocumentAlreadyUploadedError,
            ),
          );

          return;
        }

        final fileSize = fileData.length;

        if (fileSize > maxFileSizeLimit) {
          const maxSizeInMB = maxFileSizeLimit ~/ 1e6;
          _responsiveDialogProvider.showResponsiveToastMessage(
            NotificationToastMessageConfiguration.error(
              _localizations.lendingFileSizeExceedsLimit('$maxSizeInMB'),
            ),
          );

          return;
        }

        try {
          final document = await _delegate.uploadDocument(
            fileBytes: fileData,
            fileName: fileName,
            onProgress: (uploaded, total) {
              final uploadPercentage = (uploaded * 100) / total;
              safeEmit(
                LendingDocumentUploadState.processing(
                  documentExampleUrl: readyState.documentExampleUrl,
                  documents: [
                    ...readyState.documents,
                    UploadingDocument(
                      fileBytes: fileData,
                      fileName: fileName,
                      size: fileSize,
                      uploadedPercentage: uploadPercentage,
                    ),
                  ],
                ),
              );
            },
          );

          safeEmit(
            readyState.copyWith(
              documents: [
                ...readyState.documents,
                UploadedDocument(document: document, fileBytes: fileData),
              ],
            ),
          );
        } on Object catch (error) {
          _onError(error);
          _responsiveDialogProvider.showResponsiveToastMessage(
            NotificationToastMessageConfiguration.error(
              _localizations.lendingFileUploadError,
            ),
          );
          safeEmit(readyState);
        }
      },
    );
  }

  void removeDocument(UploadDocument document) {
    state.mapOrNull(
      ready: (readyState) async {
        if (document is UploadedDocument) {
          await _delegate.deleteDocument(document.document.id);
          final docs = await _delegate.uploadedDocuments.wait;
          safeEmit(
            readyState.copyWith(documents: docs),
          );
        }
      },
    );
  }

  void exitLendingApplication() => _delegate.exitLendingApplication();

  @override
  String toString() => 'LendingDocumentUploadCubit ${_delegate.type}';

  void _onError(
    Object error, {
    String? message,
  }) {
    _logger.error(
      message ?? 'Something went wrong',
      error: error,
      stackTrace: StackTrace.current,
    );
  }
}
