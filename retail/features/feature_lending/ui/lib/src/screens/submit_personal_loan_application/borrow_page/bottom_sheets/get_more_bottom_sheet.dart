import 'package:flutter/widgets.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_lending_ui/feature_lending_ui.dart';

class GetMoreBottomSheet extends StatelessWidget {
  const GetMoreBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = LendingLocalizations.of(context);

    return Padding(
      padding: EdgeInsets.all(Spacing.s5.value),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Label(
            model: LabelModel(
              text: l10n.personalLoanGetMoreBottomSheetTitle,
              textStyle: CompanyTextStylePointer.h3medium,
              color: CompanyColorPointer.primary3,
            ),
          ),
          Space.fromSpacingVertical(Spacing.s2),
          Label(
            model: LabelModel(
              text: l10n.personalLoanGetMoreBottomSheetDescription,
              textStyle: CompanyTextStylePointer.b3,
              color: CompanyColorPointer.secondary4,
            ),
          ),
          Space.fromSpacingVertical(Spacing.s4),
          Button(
            model: ButtonModel(
              title:
                  l10n.personalLoanGetMoreBottomSheetEditCreditLimitButtonTitle,
            ),
            onPressed: () => Navigator.of(context)
                .pop(GetMoreBottomSheetResult.editCreditCardLimit),
          ),
        ],
      ),
    );
  }
}

enum GetMoreBottomSheetResult {
  cancel,
  editCreditCardLimit,
}
