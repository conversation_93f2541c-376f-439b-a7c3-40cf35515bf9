import 'dart:async';

import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ui/ui.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/l10n/lending_localization.g.dart';
import 'package:wio_feature_lending_ui/src/common/extensions.dart';
import 'package:wio_feature_lending_ui/src/common/mixins/duration_timer.dart';
import 'package:wio_feature_lending_ui/src/flow/lending_application/enhanced/flow/application_stage_config.dart';
import 'package:wio_feature_lending_ui/src/flow/lending_application/enhanced/handler/application_update_request.dart';
import 'package:wio_feature_lending_ui/src/handlers/application_flow_navigation_handler.dart';
import 'package:wio_feature_lending_ui/src/screens/submit_personal_loan_application/borrow_page/bottom_sheets/loan_processing_fee_info_bottom_sheet.dart';
import 'package:wio_feature_lending_ui/src/screens/submit_personal_loan_application/repayment_plan/cubit/repayment_plan_state.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/feature_loan_ui.dart';

class PersonalLoanRepaymentPlanCubit
    extends BaseCubit<PersonalLoanRepaymentPlanState> with DurationTimer {
  final LendingInteractor _lendingInteractor;
  final LoanInteractor _loanInteractor;
  final ResponsiveDialogProvider _responsiveDialogProvider;
  final ApplicationFlowNavigationHandler _flowNavigationHandler;
  final CommonErrorHandler _errorHandler;
  final LendingExceptionHandler _exceptionHandler;
  final Logger _logger;
  final LendingLocalizations _localizations;
  final ApplicationStageConfig _config;

  PersonalLoanRepaymentPlanCubit({
    required LendingInteractor lendingInteractor,
    required LoanInteractor loanInteractor,
    required ResponsiveDialogProvider responsiveDialogProvider,
    required ApplicationFlowNavigationHandler flowNavigationHandler,
    required CommonErrorHandler errorHandler,
    required LendingExceptionHandler exceptionHandler,
    required Logger logger,
    required LendingLocalizations localizations,
    required ApplicationStageConfig config,
  })  : _lendingInteractor = lendingInteractor,
        _loanInteractor = loanInteractor,
        _responsiveDialogProvider = responsiveDialogProvider,
        _flowNavigationHandler = flowNavigationHandler,
        _errorHandler = errorHandler,
        _exceptionHandler = exceptionHandler,
        _logger = logger,
        _localizations = localizations,
        _config = config,
        super(const PersonalLoanRepaymentPlanState.loading());

  Future<void> initialize() async {
    try {
      final productApplication = _config.application.productApplication;

      final selectedAmount = productApplication?.preferences.selectedAmount;
      final period = productApplication?.preferences.loanPeriod;

      if (period == null || selectedAmount == null) {
        final errorMessage = _localizations
            .submitPersonalLoanApplicationRepaymentPlanNullPeriodOrAmount;

        _errorHandler.handleError(Exception(errorMessage));

        safeEmit(const PersonalLoanRepaymentPlanState.failed());

        // TODO(ssuleymanli): Go back to loan term and amount selection
        return;
      }

      final schedule = await _loanInteractor.evaluateDisbursement(
        request: LoanDisbursementEvaluationRequest(
          amount: selectedAmount,
          applicationId: _config.application.id,
          loanPeriod: period,
          loanProductIdentifier: _config.application.loanProductIdentifier,
        ),
      );

      safeEmit(
        PersonalLoanRepaymentPlanState.idle(
          schedule: schedule,
          period: period,
        ),
      );
    } on Object catch (e) {
      _logger.error('Error on PersonalLoanRepaymentPlanCubit $e ', error: e);
      safeEmit(const PersonalLoanRepaymentPlanState.failed());
    }
  }

  void submit() {
    state.mapOrNull(
      idle: (it) async {
        final request = switch (_config) {
          LegacyFlowApplicationStageConfig() =>
            _lendingInteractor.updateApplicationPreferences(
              applicationId: _config.application.id,
              productType: _config.application.productType,
              updatedPreferences: ApplicationPreferences(
                status: _config.nextStatus,
              ),
            ),
          EnhancedFlowApplicationStageConfig() =>
            _config.flowHandler.toNextStage(
              updateRequest: ApplicationPreferencesUpdateRequest(
                applicationId: _config.application.id,
                productType: _config.application.productType,
              ),
            ),
        };

        await request
            .toStream()
            .doOnListen(() => emit(state.toInProgress()))
            .withSafeEmit(this)
            // At this point the user will be navigated to the next stage in the
            // submission flow
            .doOnData((_) => emit(state.toIdle()))
            .logError(_logger)
            .withError<Object>(_handleSubmissionError)
            .complete();
      },
    );
  }

  Future<void> showInstallmentDetails({
    required Installment installment,
  }) async {
    final config = InstallmentBottomSheetConfig(
      installment: installment,
    );
    await _responsiveDialogProvider.showBottomSheetOrDialog<void>(
      content: InstallmentDetailsBody(config: config),
      config: const ResponsiveModalConfig(
        featureName: LendingFeatureNavigationConfig.name,
      ),
    );
  }

  void showProcessingFeeInfo() {
    state.mapOrNull(
      idle: (it) {
        _responsiveDialogProvider.showBottomSheetOrDialog<void>(
          content: const LoanProcessingFeeInfoBottomSheet(),
          config: const ResponsiveModalConfig(
            featureName: LendingFeatureNavigationConfig.name,
          ),
        );
      },
    );
  }

  void exitLendingApplication() => _flowNavigationHandler.runExitFlow();

  void _handleSubmissionError(Object error) {
    state.mapOrNull(
      inProgress: (it) {
        safeEmit(it.toIdle());
        _exceptionHandler.handle(error, LendingUiId.repayment_plan);
      },
    );
  }

  @override
  String toString() {
    return 'PersonalLoanRepaymentPlanCubit{}';
  }
}
