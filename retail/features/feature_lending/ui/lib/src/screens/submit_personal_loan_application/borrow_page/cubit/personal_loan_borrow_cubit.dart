import 'dart:async';

import 'package:collection/collection.dart';
import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ui/ui.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_account_api/wio_feature_account_api.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/src/common/extensions.dart';
import 'package:wio_feature_lending_ui/src/flow/lending_application/enhanced/flow/application_stage_config.dart';
import 'package:wio_feature_lending_ui/src/flow/lending_application/enhanced/handler/application_update_request.dart';
import 'package:wio_feature_lending_ui/src/handlers/application_flow_navigation_handler.dart';
import 'package:wio_feature_lending_ui/src/navigation/configs/update_credit_limit_page_navigation_config.dart';
import 'package:wio_feature_lending_ui/src/screens/submit_personal_loan_application/borrow_page/bottom_sheets/get_more_bottom_sheet.dart';
import 'package:wio_feature_lending_ui/src/screens/submit_personal_loan_application/borrow_page/bottom_sheets/loan_processing_fee_info_bottom_sheet.dart';
import 'package:wio_feature_lending_ui/src/screens/submit_personal_loan_application/borrow_page/cubit/personal_loan_borrow_state.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/feature_loan_ui.dart';
import 'package:wio_feature_user_api/index.dart';

class PersonalLoanBorrowCubit extends BaseCubit<PersonalLoanBorrowState> {
  final UserInteractor _userInteractor;
  final LendingInteractor _lendingInteractor;
  final AccountInteractor _accountInteractor;
  final LoanAccountInteractor _loanAccountInteractor;
  final LoanInteractor _loanInteractor;
  final CreditLimitUseCase _creditLimitUseCase;
  final NavigationProvider _navigationProvider;
  final ResponsiveDialogProvider _responsiveDialogProvider;
  final ApplicationFlowNavigationHandler _flowNavigationHandler;
  final LendingExceptionHandler _exceptionHandler;
  final Logger _logger;
  final ApplicationStageConfig _config;

  PersonalLoanBorrowCubit({
    required UserInteractor userInteractor,
    required LendingInteractor lendingInteractor,
    required AccountInteractor accountInteractor,
    required LoanAccountInteractor loanAccountInteractor,
    required LoanInteractor loanInteractor,
    required CreditLimitUseCase creditLimitUseCase,
    required NavigationProvider navigationProvider,
    required ResponsiveDialogProvider responsiveDialogProvider,
    required ApplicationFlowNavigationHandler flowNavigationHandler,
    required LendingExceptionHandler exceptionHandler,
    required Logger logger,
    required ApplicationStageConfig config,
  })  : _userInteractor = userInteractor,
        _lendingInteractor = lendingInteractor,
        _accountInteractor = accountInteractor,
        _loanAccountInteractor = loanAccountInteractor,
        _loanInteractor = loanInteractor,
        _creditLimitUseCase = creditLimitUseCase,
        _navigationProvider = navigationProvider,
        _responsiveDialogProvider = responsiveDialogProvider,
        _flowNavigationHandler = flowNavigationHandler,
        _exceptionHandler = exceptionHandler,
        _logger = logger,
        _config = config,
        super(const PersonalLoanBorrowState.loading());

  Future<void> init() async {
    // TODO(ssuleymanli): Refactor to use Streams instead
    try {
      final (application, currentAccounts, creditAccount) = await (
        _getApplication(_config.application.id),
        _getCurrentAccounts(),
        // To have the credit account info for UpdateCreditLimitPage
        _getActiveCreditAccount(),
      ).wait;

      final decisions = application.creditDecisions.loanDecisions;

      // Should not happen if user has a valid loan offer.
      // Just to be sure in case of some exception.
      if (decisions.isEmpty) {
        return safeEmit(
          PersonalLoanBorrowState.error(applicationId: _config.application.id),
        );
      }

      // Restore selected decision and selected amount percentage from the
      // application if possible
      final selectedDecision =
          _getSelectedDecision(decisions: decisions, application: application);

      final selectedAmount = _getSelectedAmount(
        selectedDecision: selectedDecision,
        application: application,
      );

      final disbursementEvaluation = await _loanInteractor.evaluateDisbursement(
        request: LoanDisbursementEvaluationRequest(
          amount: selectedAmount,
          loanPeriod: selectedDecision.loanTerm,
          applicationId: _config.application.id,
          loanProductIdentifier: _config.application.loanProductIdentifier,
        ),
      );

      CreditLimitResult? creditLimitResult;
      if (creditAccount != null) {
        creditLimitResult = await _creditLimitUseCase(creditAccount.id);
      }

      final firstName = _userInteractor.currentUser.valueOrNull?.firstName;

      safeEmit(
        PersonalLoanBorrowState.idle(
          application: application,
          userName: firstName,
          selectedTerm: selectedDecision.loanTerm,
          currentAccountBalance: currentAccounts.first.totalBalance,
          disbursementEvaluation: disbursementEvaluation,
          selectedAmount: selectedAmount,
          creditAccount: creditAccount,
          creditLimitResult: creditLimitResult,
        ),
      );
    } on Object catch (_) {
      safeEmit(
        PersonalLoanBorrowState.error(applicationId: _config.application.id),
      );
    }
  }

  void selectLoanTerm(LoanDecision selectedDecision) {
    state.mapOrNull(
      idle: (it) {
        if (!it.isIdle) return;

        // need to set input as valid if user had set
        // some invalid input on previous loan term
        safeEmit(it.copyWith(isInputValid: true));

        _evaluateInstallments(
          selectedDecision,
          selectedDecision.availableAmount,
        );
      },
    );
  }

  void selectLoanAmount(int selectedAmount) {
    state.mapOrNull(
      idle: (it) {
        if (!it.isIdle) return;

        if (it.selectedAmount.toDouble() == selectedAmount) {
          return;
        }

        _evaluateInstallments(
          it.selectedDecision,
          Money.fromNumWithCurrency(
            selectedAmount,
            it.availableAmount.currency,
          ),
        );
      },
    );
  }

  void handleInvalidInput() {
    state.mapOrNull(idle: (it) => safeEmit(it.copyWith(isInputValid: false)));
  }

  void setInputValid() {
    state.mapOrNull(idle: (it) => safeEmit(it.copyWith(isInputValid: true)));
  }

  void submit() {
    state.mapOrNull(
      idle: (it) async {
        if (!it.isIdle) return;

        final request = switch (_config) {
          LegacyFlowApplicationStageConfig() =>
            _lendingInteractor.updateApplicationPreferences(
              applicationId: _config.application.id,
              productType: _config.application.productType,
              updatedPreferences: ApplicationPreferences(
                selectedAmount: it.selectedAmount,
                loanPeriod: it.selectedTerm,
                status: _config.nextStatus,
              ),
            ),
          final EnhancedFlowApplicationStageConfig config =>
            config.flowHandler.toNextStage(
              updateRequest: ApplicationPreferencesUpdateRequest(
                applicationId: config.application.id,
                selectedAmount: it.selectedAmount,
                loanPeriod: it.selectedTerm,
                productType: _config.application.productType,
              ),
            ),
        };
        await request
            .toStream()
            .doOnListen(
              () => state.mapOrNull(
                idle: (idle) => emit(idle.toSubmittingFromIdle()),
              ),
            )
            .withSafeEmit(this)
            // At this point the user will be navigated to the next stage in the
            // submission flow
            .doOnData(
              (_) => state.mapOrNull(
                idle: (idle) => emit(idle.toIdleFromSubmitting()),
              ),
            )
            .logError(_logger)
            .withError<Object>(_handleSubmissionError)
            .complete();
      },
    );
  }

  void launchCars24Flow() {
    state.mapOrNull(
      idle: (it) async {
        if (!it.isIdle) return;

        final partner = _config.application.partner;

        if (partner == null) {
          return;
        }
        await _lendingInteractor
            .getLendingConfiguration(ProductType.autoLoan)
            .toStream()
            .doOnListen(
              () => state.mapOrNull(
                idle: (idle) => emit(idle.toSubmittingFromIdle()),
              ),
            )
            .withSafeEmit(this)
            // At this point the user will be navigated to the next stage in the
            // submission flow
            .doOnData((configuration) async {
              state.mapOrNull(
                idle: (idle) => emit(idle.toIdleFromSubmitting()),
              );
              final url = configuration.partnerDetails[partner]?.url;

              if (url == null) {
                return;
              }
              final uri = Uri.parse(url);

              if (await canLaunchUrl(uri)) {
                await launchUrl(uri);
              }
            })
            .logError(_logger)
            .withError<Object>(_handleSubmissionError)
            .complete();
      },
    );
  }

  Future<void> onGetMore() {
    return state.maybeMap<Future<void>>(
      idle: (it) async {
        if (!it.isIdle) return;

        assert(
          it.canGetMore,
          'Cannot call get more when it.canGetMore is not true',
        );

        final creditAccount = it.creditAccount;
        final creditLimitResult = it.creditLimitResult;
        if (creditAccount == null || creditLimitResult == null) return;

        // Get current reduced credit limit
        Money? reducedCreditCardLimit;
        final productApp = it.application.productApplication;
        if (productApp != null) {
          reducedCreditCardLimit =
              productApp.preferences.reducedCreditCardLimit;
        }

        final result = await _responsiveDialogProvider
            .showBottomSheetOrDialog<GetMoreBottomSheetResult>(
          content: const GetMoreBottomSheet(),
          config: const ResponsiveModalConfig(
            featureName: LendingFeatureNavigationConfig.name,
          ),
        );

        // Update value of maxLimit of limitDetails to the currentLimit and
        // value of allowIncreaseLimit to false (to hide Request More button)
        final updatedLimitDetails =
            creditLimitResult.creditLimitDetails.limitDetails.copyWith(
          maxLimit: creditLimitResult.currentLimit,
          allowIncreaseLimit: true,
        );
        final creditLimitDetails = creditLimitResult.creditLimitDetails
            .copyWith(limitDetails: updatedLimitDetails);

        if (result == GetMoreBottomSheetResult.editCreditCardLimit) {
          final updatedApplication = await _navigationProvider.push(
            UpdateCreditLimitPageNavigationConfig(
              params: UpdateCreditLimitParams.forPersonalLoan(
                initialAmount:
                    reducedCreditCardLimit ?? creditLimitResult.currentLimit,
                creditLimitDetails: creditLimitDetails,
                applicationId: _config.application.id,
              ),
            ),
          );

          if (updatedApplication is LendingApplication) {
            // Should not happen if user has a valid loan offer.
            // Just to be sure in case of some exception.
            if (updatedApplication.creditDecisions.loanDecisions.isEmpty) {
              return safeEmit(
                PersonalLoanBorrowState.error(
                  applicationId: _config.application.id,
                ),
              );
            }

            safeEmit(it.copyWith(application: updatedApplication));

            _evaluateInstallments(
              it.selectedDecision,
              it.selectedAmount,
            );
          }
        }
      },
      orElse: () async {},
    );
  }

  void showLoanAmountInformation() {
    state.mapOrNull(
      idle: (_) {
        _responsiveDialogProvider.showBottomSheetOrDialog<void>(
          content: const LoanAmountSelectionInfoDialog(),
          config: const ResponsiveModalConfig(
            featureName: LendingFeatureNavigationConfig.name,
          ),
        );
      },
    );
  }

  void showLoanProcessingFeeInformation() {
    state.mapOrNull(
      idle: (_) {
        _responsiveDialogProvider.showBottomSheetOrDialog<void>(
          content: const LoanProcessingFeeInfoBottomSheet(),
          config: const ResponsiveModalConfig(
            featureName: LendingFeatureNavigationConfig.name,
          ),
        );
      },
    );
  }

  void exitLendingApplication() => _flowNavigationHandler.runExitFlow();

  // Private

  ({Period? period, Money? amount})? _getSelectedTermAndAmount({
    required LendingApplication application,
  }) {
    final preferences = application.productApplication?.preferences;

    if (preferences == null) {
      return null;
    }

    return (period: preferences.loanPeriod, amount: preferences.selectedAmount);
  }

  LoanDecision _getSelectedDecision({
    required List<LoanDecision> decisions,
    required LendingApplication application,
  }) {
    final selections = _getSelectedTermAndAmount(application: application);
    final period = selections?.period;

    if (period == null) return decisions[0];

    return decisions
            .firstWhereOrNull((element) => element.loanTerm == period) ??
        decisions[0];
  }

  Money _getSelectedAmount({
    required LoanDecision selectedDecision,
    required LendingApplication application,
  }) {
    final selections = _getSelectedTermAndAmount(application: application);

    return selections?.amount ?? selectedDecision.availableAmount;
  }

  void _evaluateInstallments(
    LoanDecision selectedDecision,
    Money selectedAmount,
  ) {
    state.mapOrNull(
      idle: (it) {
        _loanInteractor
            .evaluateDisbursement(
              request: LoanDisbursementEvaluationRequest(
                amount: selectedAmount,
                loanPeriod: selectedDecision.loanTerm,
                applicationId: _config.application.id,
                loanProductIdentifier:
                    _config.application.loanProductIdentifier,
              ),
            )
            .asStream()
            .doOnListen(
              () => safeEmit(
                it.toEvaluatingFromIdle(
                  selectedTerm: selectedDecision.loanTerm,
                  selectedAmount: selectedAmount,
                ),
              ),
            )
            .withError<Object>(
              (error) => _handleEvaluationError(
                error,
                it.selectedTerm,
                it.selectedAmount,
              ),
            )
            .doOnData(_handleEvaluationResult)
            .complete();
      },
    );
  }

  void _handleEvaluationResult(Schedule result) {
    state.mapOrNull(
      idle: (it) {
        safeEmit(it.toIdleFromEvaluating(disbursementEvaluation: result));
      },
    );
  }

  void _handleEvaluationError(
    Object error,
    Period? previousSelectedTerm,
    Money? previousSelectedAmount,
  ) {
    state.mapOrNull(
      idle: (it) {
        safeEmit(
          it.toIdleFromEvaluating(
            selectedTerm: previousSelectedTerm,
            selectedAmount: previousSelectedAmount,
          ),
        );
        _exceptionHandler.handle(error, LendingUiId.loan_term_amount_selection);
      },
    );
  }

  void _handleSubmissionError(Object error) {
    state.mapOrNull(
      idle: (it) {
        safeEmit(it.toIdleFromSubmitting());
        _exceptionHandler.handle(error, LendingUiId.loan_term_amount_selection);
      },
    );
  }

  Future<LendingApplication> _getApplication(String applicationId) =>
      _lendingInteractor.getApplication(applicationId);

  Future<List<AccountDetails>> _getCurrentAccounts() =>
      _accountInteractor.getAccountDetails();

  Future<LoanAccount?> _getActiveCreditAccount() async {
    final loanAccounts = await _loanAccountInteractor.getLoanAccounts();

    final creditAccount = loanAccounts.firstWhereOrNull(
      (account) =>
          account.productType == ProductType.creditCard &&
          account.active &&
          account.isInActiveState,
    );

    return creditAccount;
  }

  @override
  String toString() => 'PersonalLoanBorrowCubit{}';
}
