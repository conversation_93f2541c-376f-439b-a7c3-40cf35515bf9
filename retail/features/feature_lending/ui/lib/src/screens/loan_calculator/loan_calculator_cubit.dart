import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/ui.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/src/flow/lending_application/enhanced/flow/application_stage_config.dart';
import 'package:wio_feature_lending_ui/src/handlers/application_flow_navigation_handler.dart';
import 'package:wio_feature_lending_ui/src/screens/submit_personal_loan_application/borrow_page/bottom_sheets/loan_processing_fee_info_bottom_sheet.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

part 'loan_calculator_state.dart';

class LoanCalculatorCubit extends BaseCubit<LoanCalculatorState> {
  /// only exists for auto loan for now
  static const _productType = ProductType.autoLoan;

  final LendingInteractor _interactor;
  final LoanInteractor _loanInteractor;
  final LendingExceptionHandler _exceptionHandler;
  final EnhancedFlowApplicationStageConfig _config;
  final ResponsiveDialogProvider _responsiveDialogProvider;
  final ApplicationFlowNavigationHandler _flowNavigationHandler;
  LoanCalculatorCubit({
    required LendingInteractor interactor,
    required LendingExceptionHandler exceptionHandler,
    required LoanInteractor loanInteractor,
    required EnhancedFlowApplicationStageConfig config,
    required ResponsiveDialogProvider responsiveDialogProvider,
    required ApplicationFlowNavigationHandler flowNavigationHandler,
  })  : _interactor = interactor,
        _exceptionHandler = exceptionHandler,
        _loanInteractor = loanInteractor,
        _config = config,
        _responsiveDialogProvider = responsiveDialogProvider,
        _flowNavigationHandler = flowNavigationHandler,
        super(const LoanCalculatorInitialState());

  Future<void> initialize() async {
    try {
      if (state is! LoanCalculatorInitialState) {
        emit(const LoanCalculatorInitialState());
      }

      final lendingConf =
          await _interactor.getLendingConfiguration(_productType);
      final tenures = lendingConf.tenures ?? <int>[];
      final maxLoanAmount =
          ArgumentError.checkNotNull(lendingConf.maximumLoanAmount);
      final minLoanAmount =
          ArgumentError.checkNotNull(lendingConf.minimumLoanAmount);

      if (tenures.isEmpty) {
        throw ArgumentError(
          'Tenures are empty for product type: $_productType',
        );
      }

      final selectedTenure = tenures.first;

      safeEmit(
        LoanCalculatorEvaluatingState(
          maximumLoanAmount: maxLoanAmount,
          minimumLoanAmount: minLoanAmount,
          stepSize: lendingConf.stepSize,
          tenures: tenures,
          selectedAmount: minLoanAmount,
          selectedTenure: selectedTenure,
        ),
      );

      await evaluate(amount: minLoanAmount, tenure: selectedTenure);
    } on Object catch (e) {
      _exceptionHandler.handle(e, LendingUiId.loan_calculator);
      safeEmit(const LoanCalculatorErrorState());
    }
  }

  void exitLendingApplication() => _flowNavigationHandler.runExitFlow();

  Future<void> evaluate({
    required Money amount,
    required int tenure,
  }) async {
    final state = this.state;

    if (state is! LoanCalculatorLoadedState) {
      safeEmit(const LoanCalculatorErrorState());
      return;
    }
    try {
      emit(
        LoanCalculatorEvaluatingState(
          maximumLoanAmount: state.maximumLoanAmount,
          minimumLoanAmount: state.minimumLoanAmount,
          tenures: state.tenures,
          stepSize: state.stepSize,
          selectedAmount: amount,
          selectedTenure: tenure,
        ),
      );

      final schedule = await _loanInteractor.evaluateDisbursement(
        request: LoanDisbursementEvaluationRequest(
          amount: amount,
          loanPeriod: Period(months: tenure),
          applicationId: _config.application.id,
          loanProductIdentifier: _config.application.loanProductIdentifier,
        ),
      );

      safeEmit(
        LoanCalculatorEvaluatedState(
          schedule: schedule,
          maximumLoanAmount: state.maximumLoanAmount,
          minimumLoanAmount: state.minimumLoanAmount,
          tenures: state.tenures,
          stepSize: state.stepSize,
          selectedAmount: amount,
          selectedTenure: tenure,
        ),
      );
    } on Object catch (e) {
      _exceptionHandler.handle(e, LendingUiId.loan_calculator);
      safeEmit(
        LoanCalculatorEvaluationFailedState(
          maximumLoanAmount: state.maximumLoanAmount,
          minimumLoanAmount: state.minimumLoanAmount,
          tenures: state.tenures,
          stepSize: state.stepSize,
          selectedAmount: amount,
          selectedTenure: tenure,
        ),
      );
    }
  }

  void showLoanProcessingFeeInformation() {
    _responsiveDialogProvider.showBottomSheetOrDialog<void>(
      content: const LoanProcessingFeeInfoBottomSheet(),
      config: const ResponsiveModalConfig(
        featureName: LendingFeatureNavigationConfig.name,
      ),
    );
  }

  Future<void> onContinue() async {
    await _config.flowHandler.toNextStage();
  }

  @override
  String toString() {
    return 'LoanCalculatorCubit{}';
  }
}
