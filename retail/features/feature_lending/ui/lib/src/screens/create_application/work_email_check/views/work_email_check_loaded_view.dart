import 'package:flutter/widgets.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_lending_ui/feature_lending_ui.dart';
import 'package:wio_feature_lending_ui/src/common/lending_simple_tip.dart';
import 'package:wio_feature_lending_ui/src/screens/create_application/work_email_check/cubit/work_email_check_cubit.dart';

class WorkEmailCheckLoadedView extends StatefulWidget {
  final String? email;
  const WorkEmailCheckLoadedView({this.email, super.key});

  @override
  State<WorkEmailCheckLoadedView> createState() =>
      _WorkEmailCheckLoadedViewState();
}

class _WorkEmailCheckLoadedViewState extends State<WorkEmailCheckLoadedView> {
  late final TextEditingController _emailInputController;

  @override
  void initState() {
    _emailInputController = TextEditingController(text: widget.email);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant WorkEmailCheckLoadedView oldWidget) {
    final email = widget.email;

    if (email == null) {
      _emailInputController.clear();
    } else if (email != _emailInputController.text) {
      _emailInputController.text = email;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _emailInputController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = LendingLocalizations.of(context);
    final cubit = context.read<WorkEmailCheckCubit>();
    final state = context.watch<WorkEmailCheckCubit>().state;

    return FixedButtonsPageLayout(
      onPrimaryButtonPressed:
          !state.errorMessage.hasValue && state.email.isNotEmpty
              ? cubit.onSubmitEmail
              : _onSkip,
      model: FixedButtonsScrollablePageLayoutModel(
        primaryButton: !state.errorMessage.hasValue && state.email.isNotEmpty
            ? FixedButtonsScrollablePageLayoutButton(
                label: localizations.lendingEmailContinue,
                type: ButtonType.primary,
                size: ButtonSize.medium,
                isLoading: state.isLoading,
              )
            : FixedButtonsScrollablePageLayoutButton(
                label: localizations.lendingEmailSkip,
                type: ButtonType.secondary,
                size: ButtonSize.medium,
                isLoading: state.isLoading,
              ),
      ),
      child: ListView(
        padding: EdgeInsets.zero,
        physics: const ClampingScrollPhysics(),
        children: [
          Align(
            alignment: Alignment.centerLeft,
            child: PageText(
              PageTextModel(
                title: localizations.lendingEnterYourWorkEmail,
                hasRightMargin: false,
              ),
            ),
          ),
          Space.fromSpacingVertical(Spacing.s6),
          InputField(
            autoFocus: true,
            isEnabled: !state.isLoading,
            keyboardType: TextInputType.emailAddress,
            onInputChanged: cubit.onEmailInput,
            controller: _emailInputController,
            model: InputFieldModel(
              error: state.errorMessage,
              hint: localizations.lendingEmailFieldLabel,
              label: localizations.lendingEmailFieldLabel,
              size: InputFieldSize.small,
              theme: InputFieldTheme.light,
              initialValue: state.email,
            ),
            onFieldSubmitted: (_) {},
          ),
          const SizedBox(height: 8.0),
          LendingSimpleTip(
            tip: localizations.lendingTipWorkEmailCheck1,
          ),
        ],
      ),
    );
  }

  void _onSkip() {
    _emailInputController.clear();
    context.read<WorkEmailCheckCubit>().onSkipStep();
  }
}

extension _StringExtension on String? {
  bool get hasValue => this != null && this!.isNotEmpty;
}
