part of '../credit_dashboard_page.dart';

class _ContentView extends StatefulWidget {
  final LoanAccountSummaryList accounts;
  final int selectedTabIndex;
  final DashboardFeatureFlags dashboardFeatureFlags;
  final easy_cash.EasyCashLimit? easyCashLimit;
  final CreditSummary creditSummary;
  final bool showLendingHubTabs;

  const _ContentView({
    required this.accounts,
    required this.selectedTabIndex,
    required this.dashboardFeatureFlags,
    required this.easyCashLimit,
    required this.creditSummary,
    this.showLendingHubTabs = false,
  });

  @override
  State<_ContentView> createState() => _ContentViewState();
}

class _ContentViewState extends State<_ContentView> {
  late final ScrollController _scrollController;
  late final RefreshController _refreshController;

  LoanAccountSummary get selectedAccount =>
      widget.accounts.elementAtOrNull(widget.selectedTabIndex) ??
      widget.accounts.first;

  @override
  void initState() {
    _scrollController = ScrollController();
    _refreshController = RefreshController();
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cubit = context.watch<CreditDashboardCubit>();
    final state = cubit.state;
    final l10n = LendingLocalizations.of(context);
    final languageCode = Localizations.localeOf(context).languageCode;
    final lastAutoPay = selectedAccount.lastAutoPayment;
    final autopay = selectedAccount.autoPayment;
    final delinquentProducts = widget.creditSummary.delinquentProducts ?? [];
    final isCreditLocked = cubit.isLendingHubEnabled
        ? delinquentProducts.isCategoryLocked(LoanAccountCategory.creditCard)
        : selectedAccount.loanAccount.isLocked;
    final isLastAutopayBannerShown =
        lastAutoPay != null && state.isDashboardLastAutopaymentBannerEnabled;
    final color = context.colorStyling.primary3;

    final autopaySection = SliverList(
      delegate: SliverChildListDelegate(
        [
          AnimatedSize(
            duration: const Duration(milliseconds: 400),
            child: isLastAutopayBannerShown
                ? _LastAutoPayCard(
                    data: lastAutoPay,
                    onClose: cubit.onHideLastAutoPay,
                  )
                : const Space.shrink(),
          ),
          _SectionTitle(
            title: l10n.creditDashboardAutopaySectionTitle(
              DateSuffixFormatter.getFormattedDay(
                autopay.nextPaymentDate,
                'MMMM d',
                languageCode,
              ),
            ),
          ),
          Space.fromSpacingVertical(Spacing.s4),
          _AutoPayDetails(
            autopay: autopay,
            creditCardSpent: selectedAccount.loanAccount.spentSettledAmount,
            percentageRepayment:
                selectedAccount.loanAccount.paymentSettings.percentage,
          ),
        ],
      ),
    );
    final detailsSection = selectedAccount.isEasyCash
        ? SliverToBoxAdapter(
            child: _EasyCashFeeDetailsSecion(easyCashAccount: selectedAccount),
          )
        : autopaySection;

    final splitAccounts = state.maybeMap(
      idle: (it) => it.splitAccounts,
      orElse: () => <LoanAccount>[],
    );

    return SmartRefresher(
      controller: _refreshController,
      onRefresh: _onRefresh,
      header: TwoLevelHeader(
        height: 60.0,
        refreshingIcon: Padding(
          padding: const EdgeInsets.only(left: 12.0, bottom: 2),
          child: Spinner(
            model: SpinnerModel(type: SpinnerType.secondary),
          ),
        ),
        decoration: const BoxDecoration(
          color: Colors.transparent,
        ),
        textStyle: context.textStyling.b2.copyWith(color: color),
        releaseIcon: Icon(Icons.refresh, color: color),
        completeIcon: Icon(
          CompanyIconFactory.get(CompanyIconPointer.selection_selected),
          color: color,
        ),
        idleIcon: Icon(
          CompanyIconFactory.get(CompanyIconPointer.arrow_down),
          color: color,
        ),
        completeText: l10n.completedRefreshLabel,
        refreshingText: l10n.refreshingLabel,
        releaseText: l10n.releaseToRefreshLabel,
        idleText: l10n.pullDownRefreshLabel,
      ),
      child: CustomScrollView(
        controller: _scrollController,
        physics: const ClampingScrollPhysics(),
        slivers: [
          if (widget.showLendingHubTabs)
            const SliverToBoxAdapter(
              child: LendingHubTabs(),
            ),
          SliverToBoxAdapter(
            child: _DashboardHeader(
              accounts: widget.accounts,
              selectedTabIndex: widget.selectedTabIndex,
              easyCashLimit: widget.easyCashLimit,
              splitAccounts: splitAccounts,
              creditSummary: widget.creditSummary,
              showEasyCashCreditTab: !widget.showLendingHubTabs,
            ),
          ),
          if (!isCreditLocked || selectedAccount.isEasyCash) ...[
            SliverPadding(
              padding: const EdgeInsetsDirectional.all(_pagePadding),
              sliver: detailsSection,
            ),
          ],
          SliverPadding(
            padding: const EdgeInsetsDirectional.fromSTEB(
              _pagePadding,
              0,
              _pagePadding,
              _pagePadding,
            ),
            sliver: SliverList(
              delegate: SliverChildListDelegate(
                [
                  WioOneBanner(
                    WioOneBannerModel(
                      title: l10n.lendingPayOtherBankCardDescription,
                      subtitle: l10n.lendingPayOtherBankCardTitle,
                      highlightedTextsInTitle: [
                        l10n.lendingPayOtherBankCardHighlightedText,
                      ],
                      style: WioOneBannerStyle.highlighted,
                      icon: CompanyIconPointer.arrow_right.toGraphicAsset(),
                    ),
                    onButtonPressed: cubit.onPayOtherBankCreditBanner,
                  ),
                ],
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Space.fromSpacingVertical(Spacing.s4),
          ),
          switch (selectedAccount.loanAccount.productType) {
            ProductType.easyCash => const _EasyCashTransactionHeader(),
            _ => const TransactionsHeader(),
          },
          switch (selectedAccount.loanAccount.productType) {
            ProductType.easyCash => EasyCashTransactions(
                scrollController: _scrollController,
              ),
            _ => CreditTransactions(
                scrollController: _scrollController,
              ),
          },
          SliverToBoxAdapter(
            child: Space.fromSpacingVertical(Spacing.s5),
          ),
        ],
      ),
    );
  }

  Future<void> _onRefresh() async {
    await context.read<CreditDashboardCubit>().onRefresh();
    _refreshController.refreshCompleted();
  }
}

class _EasyCashTransactionHeader extends StatelessWidget {
  const _EasyCashTransactionHeader();

  @override
  Widget build(BuildContext context) {
    final l10n = LendingLocalizations.of(context);

    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Label(
          model: LabelModel(
            text: l10n.creditDashboardTransactionsSectionTitle,
            textStyle: CompanyTextStylePointer.h4,
            color: CompanyColorPointer.primary3,
          ),
        ),
      ),
    );
  }
}

class _LastAutoPayCard extends StatelessWidget {
  final LastAutoPayment data;
  final VoidCallback? onClose;

  const _LastAutoPayCard({
    required this.data,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = LendingLocalizations.of(context);
    final languageCode = Localizations.localeOf(context).languageCode;

    return Padding(
      padding: EdgeInsets.only(bottom: Spacing.s6.value),
      child: NotificationCard(
        NotificationCardModel(
          label: l10n.creditDashboardLastAutoPayDateLabel(
            DateSuffixFormatter.getFormattedDay(
              data.paidDate,
              'MMMM d',
              languageCode,
            ),
          ),
          headline: l10n.creditDashboardLastAutoPaidAmount(
            data.paidAmount.toCodeOnRightFormat(),
          ),
          subhead: l10n.creditDashboardLastAutopayFeeLabel(
            data.feeAmount.toCodeOnRightFormat(),
          ),
          description: l10n.creditDashboardLastAutopayDescription(
            data.totalAmount.toCodeOnLeftFormat(),
            data.totalAmount.currency.code,
          ),
          background: NotificationCardBackground.highlighted,
          subheadType: NotificationCardSubheadType.danger,
        ),
        onClosePressed: onClose,
      ),
    );
  }
}

extension on DateTime {
  static final _formatter = DateFormat('MMMM d');

  String toAutoPayFormat() => _formatter.format(this);
}
