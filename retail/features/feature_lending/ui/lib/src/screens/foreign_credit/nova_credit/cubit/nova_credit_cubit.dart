import 'package:logging_api/logging.dart';
import 'package:ui/ui.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/l10n/lending_localization.g.dart';
import 'package:wio_feature_lending_ui/src/common/status_screen_config_factory.dart';
import 'package:wio_feature_lending_ui/src/navigation/configs/bottom_sheets/camera_permission_request_bottom_sheet_config.dart';

class NovaCreditCubit extends BaseCubit<void> {
  final Logger _logger;
  final LendingInteractor _lendingInteractor;
  final LendingLocalizations _localizations;
  final NavigationProvider _navigationProvider;
  final CompanyPermissionResolver _permissionResolver;
  final String _applicationId;

  NovaCreditCubit({
    required Logger logger,
    required LendingInteractor lendingInteractor,
    required LendingLocalizations localizations,
    required NavigationProvider navigationProvider,
    required String applicationId,
    required CompanyPermissionResolver permissionResolver,
  })  : _logger = logger,
        _lendingInteractor = lendingInteractor,
        _localizations = localizations,
        _navigationProvider = navigationProvider,
        _applicationId = applicationId,
        _permissionResolver = permissionResolver,
        super(null);

  Future<void> onNovaSuccess(String token, String status) async {
    try {
      await _lendingInteractor.submitFcrDetails(
        applicationId: _applicationId,
        fcrDetails: FcrDetails(publicToken: token),
      );

      await _navigationProvider.navigateTo(
        StatusScreenConfigFactory.success(
          title: _localizations.foreignCreditSuccessTitle,
          subtitle: _localizations.foreignCreditSuccessDescription,
          primaryButtonTitle: _localizations.foreignCreditSuccessCta,
        ),
      );

      _navigationProvider.popUntilFirstRoute();
    } on Object {
      await _navigationProvider.navigateTo(
        StatusScreenConfigFactory.error(
          title: _localizations.foreignCreditFailureTitle,
          subtitle: _localizations.foreignCreditFailureDescription,
          primaryButtonTitle: _localizations.foreignCreditFailureCta,
        ),
      );
      _navigationProvider.popUntilFirstRoute();
    }
  }

  void onNovaExit() {
    _navigationProvider.popUntilFirstRoute();
  }

  void onNovaError(String token, String error) {
    _logger
        .error('Error from nova credit iframe: $error. Public token: $token');
  }

  Future<bool> requestCameraPermission() async {
    const permission = PermissionType.camera;
    final status = await _permissionResolver.requestPermissions(permission);

    if (status == CompanyPermissionStatus.granted) {
      return true;
    } else if (status == CompanyPermissionStatus.permanentlyDenied) {
      final shouldOpenSettings =
          await _navigationProvider.showBottomSheet<bool>(
        const CameraPermissionRequestBottomSheetNavigationConfig(),
      );

      if (shouldOpenSettings ?? false) {
        await _permissionResolver.openDeviceSettings();
      }
    }

    return false;
  }

  @override
  String toString() => 'NovaCreditCubit';
}
