// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'amount_on_hold_bottom_sheet_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AmountOnHoldBottomSheetConfig {}

/// @nodoc
abstract class $AmountOnHoldBottomSheetConfigCopyWith<$Res> {
  factory $AmountOnHoldBottomSheetConfigCopyWith(
          AmountOnHoldBottomSheetConfig value,
          $Res Function(AmountOnHoldBottomSheetConfig) then) =
      _$AmountOnHoldBottomSheetConfigCopyWithImpl<$Res,
          AmountOnHoldBottomSheetConfig>;
}

/// @nodoc
class _$AmountOnHoldBottomSheetConfigCopyWithImpl<$Res,
        $Val extends AmountOnHoldBottomSheetConfig>
    implements $AmountOnHoldBottomSheetConfigCopyWith<$Res> {
  _$AmountOnHoldBottomSheetConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AmountOnHoldBottomSheetConfig
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$AmountOnHoldBottomSheetConfigImplCopyWith<$Res> {
  factory _$$AmountOnHoldBottomSheetConfigImplCopyWith(
          _$AmountOnHoldBottomSheetConfigImpl value,
          $Res Function(_$AmountOnHoldBottomSheetConfigImpl) then) =
      __$$AmountOnHoldBottomSheetConfigImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AmountOnHoldBottomSheetConfigImplCopyWithImpl<$Res>
    extends _$AmountOnHoldBottomSheetConfigCopyWithImpl<$Res,
        _$AmountOnHoldBottomSheetConfigImpl>
    implements _$$AmountOnHoldBottomSheetConfigImplCopyWith<$Res> {
  __$$AmountOnHoldBottomSheetConfigImplCopyWithImpl(
      _$AmountOnHoldBottomSheetConfigImpl _value,
      $Res Function(_$AmountOnHoldBottomSheetConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of AmountOnHoldBottomSheetConfig
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$AmountOnHoldBottomSheetConfigImpl
    extends _AmountOnHoldBottomSheetConfig {
  const _$AmountOnHoldBottomSheetConfigImpl() : super._();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AmountOnHoldBottomSheetConfigImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;
}

abstract class _AmountOnHoldBottomSheetConfig
    extends AmountOnHoldBottomSheetConfig {
  const factory _AmountOnHoldBottomSheetConfig() =
      _$AmountOnHoldBottomSheetConfigImpl;
  const _AmountOnHoldBottomSheetConfig._() : super._();
}
