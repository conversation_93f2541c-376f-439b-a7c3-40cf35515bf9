import 'package:ui/ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_lending_ui/src/bottom_sheets/add_iban/cubit/add_iban_bottomsheet_state.dart';
import 'package:wio_feature_lending_ui/src/common/widgets/iban_input_field/cubit/iban_input_field_state.dart';

class AddIbanBottomSheetCubit extends BaseCubit<AddIbanBottomSheetState> {
  final NavigationProvider _navigationProvider;

  AddIbanBottomSheetCubit({
    required NavigationProvider navigationProvider,
  })  : _navigationProvider = navigationProvider,
        super(
          const AddIbanBottomSheetState(
            iban: '',
            ibanInputStatus: IbanInputStatus.empty(),
          ),
        );

  void onIbanInput(String iban, IbanInputStatus ibanInputStatus) {
    safeEmit(
      state.copyWith(
        iban: iban,
        ibanInputStatus: ibanInputStatus,
      ),
    );
  }

  void onAddIban() {
    _navigationProvider.goBack(state.iban);
  }

  @override
  String toString() => 'AddIbanBottomSheetCubit';
}
