import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_lending_ui/feature_lending_ui.dart';

class AmountOnHold extends StatelessWidget {
  const AmountOnHold({super.key});

  @override
  Widget build(BuildContext context) {
    final localization = LendingLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Label(
            model: LabelModel(
              text: localization.lendingAmountOnHold,
              textStyle: CompanyTextStylePointer.h3medium,
              color: CompanyColorPointer.primary3,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 12.0),
            child: Label(
              model: LabelModel(
                text: localization.lendingAmountOnHoldBsContent,
                textStyle: CompanyTextStylePointer.b3,
                color: CompanyColorPointer.primary3,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
