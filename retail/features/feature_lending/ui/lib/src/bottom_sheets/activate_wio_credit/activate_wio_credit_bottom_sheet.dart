import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui/page/page_bloc_provider.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/feature_lending_ui.dart';
import 'package:wio_feature_lending_ui/src/bottom_sheets/activate_wio_credit/activate_wio_credit_cubit.dart';
import 'package:wio_feature_lending_ui/src/bottom_sheets/activate_wio_credit/activate_wio_credit_state.dart';

const _contentPadding = 24.0;

class ActivateWioCreditBottomSheet extends StatelessWidget {
  static const continueButtonKey = ValueKey('continueButtonKey');

  final CardsApplySource source;
  final LendingOffer offer;

  const ActivateWioCreditBottomSheet({
    required this.source,
    required this.offer,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return PageBlocProvider<ActivateWioCreditCubit, void>(
      createBloc: () => DependencyProvider.getWithParams<ActivateWioCreditCubit,
          LendingOffer, void>(param1: offer),
      initBloc: (cubit) => cubit.initialize(source),
      child: const _ActivateWioCreditBottomSheetContent(),
    );
  }
}

class _ActivateWioCreditBottomSheetContent extends StatelessWidget {
  const _ActivateWioCreditBottomSheetContent();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: _contentPadding),
      child: BlocBuilder<ActivateWioCreditCubit, ActivateWioCreditState>(
        builder: (_, state) => state.map(
          initial: (_) => const CompanyShimmer(
            model: CompanyShimmerModel(),
            child: _Content(),
          ),
          ready: (readyState) => const _Content(),
          error: (_) => const SizedBox.shrink(),
        ),
      ),
    );
  }
}

class _Content extends StatelessWidget {
  const _Content();

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      children: const [
        _Header(),
        _FeatureCards(),
        _ContinueButton(),
      ],
    );
  }
}

class _Header extends StatelessWidget {
  const _Header();

  @override
  Widget build(BuildContext context) {
    final localizations = LendingLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.only(top: 12, bottom: _contentPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Label(
            model: LabelModel(
              text: localizations.lendingActivateWioCreditBottomSheetTitle,
              color: CompanyColorPointer.primary3,
              textStyle: CompanyTextStylePointer.h2medium,
              textAlign: LabelTextAlign.center,
            ),
          ),
          Space.fromSpacingVertical(Spacing.s2),
          Label(
            model: LabelModel(
              text: localizations.lendingActivateWioCreditBottomSheetSubtitle,
              color: CompanyColorPointer.secondary4,
              textStyle: CompanyTextStylePointer.b3,
              textAlign: LabelTextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}

class _FeatureCards extends StatelessWidget {
  const _FeatureCards();

  @override
  Widget build(BuildContext context) {
    final localizations = LendingLocalizations.of(context);
    final state = context.watch<ActivateWioCreditCubit>().state;

    final cashbackPercentage = state.cashbackPercentage;
    final feeFreePeriod = state.feeFreePeriod;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Space.fromSpacingVertical(Spacing.s2),
        FeatureCard(
          model: FeatureCardModel(
            icon:
                const GraphicAssetPointer.icon(CompanyIconPointer.switch_icon),
            headline: localizations
                .lendingActivateWioCreditBottomSheetFirstCardHeadline,
            description: localizations
                .lendingActivateWioCreditBottomSheetFirstCardDescription,
            displayType: FeatureCardDisplayType.normal,
          ),
        ),
        Space.fromSpacingVertical(Spacing.s2),
        FeatureCard(
          model: FeatureCardModel(
            icon: const GraphicAssetPointer.icon(CompanyIconPointer.infinity),
            headline: localizations
                .lendingActivateWioCreditBottomSheetSecondCardHeadline(
              cashbackPercentage.toStringNoTrailingZeros(),
            ),
            description: localizations
                .lendingActivateWioCreditBottomSheetSecondCardDescription,
            displayType: FeatureCardDisplayType.normal,
          ),
        ),
        Space.fromSpacingVertical(Spacing.s2),
        FeatureCard(
          model: FeatureCardModel(
            icon: const GraphicAssetPointer.icon(CompanyIconPointer.happy),
            headline: localizations
                .lendingActivateWioCreditBottomSheetThirdCardHeadline(
              feeFreePeriod.toString(),
            ),
            description: localizations
                .lendingActivateWioCreditBottomSheetThirdCardDescription,
            displayType: FeatureCardDisplayType.normal,
          ),
        ),
        Space.fromSpacingVertical(Spacing.s2),
      ],
    );
  }
}

class _ContinueButton extends StatelessWidget {
  const _ContinueButton();

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<ActivateWioCreditCubit>();
    final localizations = LendingLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.only(top: 12, bottom: _contentPadding),
      child: Button(
        buttonKey: ActivateWioCreditBottomSheet.continueButtonKey,
        model: ButtonModel(
          title: localizations.lendingActivateWioCreditBottomSheetButtonTitle,
        ),
        onPressed: cubit.onContinue,
      ),
    );
  }
}

extension on double {
  String toStringNoTrailingZeros() =>
      truncateToDouble() == this ? toInt().toString() : toString();
}
