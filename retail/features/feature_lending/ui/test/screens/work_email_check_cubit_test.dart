import 'package:bloc_test/bloc_test.dart';
import 'package:domain/domain.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/feature_lending_ui.dart';
import 'package:wio_feature_lending_ui/src/flow/lending_application/enhanced/flow/application_stage_config.dart';
import 'package:wio_feature_lending_ui/src/handlers/application_flow_navigation_handler.dart';
import 'package:wio_feature_lending_ui/src/screens/create_application/work_email_check/cubit/work_email_check_cubit.dart';
import 'package:wio_feature_lending_ui/src/screens/create_application/work_email_check/cubit/work_email_check_state.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late LendingLocalizations localizations;
  late LendingInteractor interactor;
  late Logger logger;
  late LendingAnalytics lendingAnalytics;
  late LendingExceptionHandler exceptionHandler;
  late ApplicationFlowNavigationHandler flowNavigationHandler;
  late WorkEmailCheckCubit cubit;

  final lendingApplication = TestEntities.getApplication();
  final applications = Applications(total: 1, items: [lendingApplication]);
  const nextStatus = OnboardingStatus.incomeCheck;
  const email = '<EMAIL>';

  setUp(() {
    localizations = MockLendingLocalizations();
    interactor = MockLendingInteractor();
    logger = MockLogger();
    lendingAnalytics = MockLendingAnalytics();
    exceptionHandler = MockLendingExceptionHandler();
    flowNavigationHandler = MockApplicationFlowNavigationHandler();

    cubit = WorkEmailCheckCubit(
      localizations: localizations,
      interactor: interactor,
      logger: logger,
      lendingAnalytics: lendingAnalytics,
      exceptionHandler: exceptionHandler,
      flowNavigationHandler: flowNavigationHandler,
      config: LegacyFlowApplicationStageConfig(
        application: lendingApplication,
        nextStatus: nextStatus,
      ),
    );
  });

  void stubApplications(List<Data<Applications>> data) {
    when(() => interactor.observeApplications())
        .thenAnswer((_) => Stream.fromIterable(data));
  }

  group('WorkEmailCheckCubit', () {
    test('initial state is correct', () {
      expect(cubit.state, const WorkEmailCheckState.initial());
    });

    blocTest<WorkEmailCheckCubit, WorkEmailCheckState>(
      'initialize sets the correct state',
      build: () => cubit,
      setUp: () {
        stubApplications([
          Data.loading(null),
          Data.success(applications),
        ]);
      },
      act: (cubit) => cubit.initialize(),
      expect: () => [
        const WorkEmailCheckState.ready(
          isLoading: false,
        ),
      ],
    );

    blocTest<WorkEmailCheckCubit, WorkEmailCheckState>(
      'onSubmitEmail updates the email and sets isLoading to true and false',
      build: () => cubit,
      seed: () => const WorkEmailCheckState.ready(
        isLoading: false,
        email: email,
      ),
      setUp: () => when(
        () => interactor.updateEmail(
          applicationId: any(named: 'applicationId'),
          nextStatus: nextStatus,
          emailAddress: any(named: 'emailAddress'),
        ),
      ).justAnswerAsync(
        lendingApplication.copyWith(
          data: lendingApplication.data.copyWith(workEmail: email),
        ),
      ),
      act: (cubit) => cubit.onSubmitEmail(),
      verify: (cubit) => verify(
        () => interactor.updateEmail(
          applicationId: lendingApplication.id,
          nextStatus: nextStatus,
          emailAddress: email,
        ),
      ).calledOnce,
      expect: () => [
        const WorkEmailCheckState.ready(
          isLoading: true,
          email: email,
        ),
        const WorkEmailCheckState.ready(
          isLoading: false,
          email: email,
        ),
      ],
    );

    blocTest<WorkEmailCheckCubit, WorkEmailCheckState>(
      'onSkipStep updates the email and sets isLoading to true and false',
      build: () => cubit,
      seed: () => const WorkEmailCheckState.ready(
        isLoading: false,
        email: email,
      ),
      setUp: () => when(
        () => interactor.updateApplication(
          applicationId: any(named: 'applicationId'),
          nextStatus: nextStatus,
        ),
      ).justAnswerAsync(TestEntities.getApplication()),
      act: (cubit) => cubit.onSkipStep(),
      expect: () => [
        const WorkEmailCheckState.ready(
          isLoading: true,
          email: email,
        ),
        const WorkEmailCheckState.ready(
          isLoading: false,
        ),
      ],
    );

    blocTest<WorkEmailCheckCubit, WorkEmailCheckState>(
      'onEmailInput updates the email and calls _validateEmailAddress',
      build: () => cubit,
      seed: () => const WorkEmailCheckState.ready(
        isLoading: false,
      ),
      act: (cubit) => cubit.onEmailInput(email),
      expect: () => [
        const WorkEmailCheckState.ready(
          isLoading: false,
          email: email,
        ),
      ],
    );
  });
}
