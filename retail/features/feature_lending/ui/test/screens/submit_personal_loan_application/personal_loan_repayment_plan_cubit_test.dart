import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:tests/tests.dart';
import 'package:ui/ota_lokalisation/ota_lokalization.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_api/model/lending_application/lending_product_application.dart';
import 'package:wio_feature_lending_ui/feature_lending_ui.dart';
import 'package:wio_feature_lending_ui/src/flow/lending_application/enhanced/flow/application_stage_config.dart';
import 'package:wio_feature_lending_ui/src/handlers/application_flow_navigation_handler.dart';
import 'package:wio_feature_lending_ui/src/screens/submit_personal_loan_application/repayment_plan/cubit/repayment_plan_cubit.dart';
import 'package:wio_feature_lending_ui/src/screens/submit_personal_loan_application/repayment_plan/cubit/repayment_plan_state.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

import '../../mocks.dart';
import '../../test_entities.dart';

typedef _Cubit = PersonalLoanRepaymentPlanCubit;
typedef _State = PersonalLoanRepaymentPlanState;

void main() {
  late LendingInteractor lendingInteractor;
  late LoanInteractor loanInteractor;
  late ResponsiveDialogProvider responsiveDialogProvider;
  late ApplicationFlowNavigationHandler flowNavigationHandler;
  late CommonErrorHandler errorHandler;
  late LendingExceptionHandler exceptionHandler;
  late Logger logger;
  late LendingLocalizations localizations;

  const selectedPeriod = Period(months: 12);
  final selectedAmount = Money.fromNumWithCurrency(10000, Currency.aed);

  final application = TestEntities.getApplication(
    entryProductType: ProductType.personalLoan,
    productApplications: [
      LendingProductApplication(
        productType: ProductType.personalLoan,
        preferences: ApplicationPreferences(
          status: OnboardingStatus.repaymentPlan,
          loanPeriod: selectedPeriod,
          selectedAmount: selectedAmount,
        ),
      ),
    ],
  );

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    localizations = await LendingLocalizations.load(const Locale('en'));
  });

  late PersonalLoanRepaymentPlanCubit cubit;

  _Cubit getCubit({LendingApplication? lendingApplication}) =>
      PersonalLoanRepaymentPlanCubit(
        lendingInteractor: lendingInteractor,
        responsiveDialogProvider: responsiveDialogProvider,
        flowNavigationHandler: flowNavigationHandler,
        loanInteractor: loanInteractor,
        exceptionHandler: exceptionHandler,
        errorHandler: errorHandler,
        logger: logger,
        localizations: localizations,
        config: LegacyFlowApplicationStageConfig(
          application: lendingApplication ?? application,
          nextStatus: OnboardingStatus.agreementSigning,
        ),
      );

  setUp(() {
    lendingInteractor = MockLendingInteractor();
    loanInteractor = MockLoanInteractor();
    responsiveDialogProvider = MockResponsiveDialogProvider();
    flowNavigationHandler = MockApplicationFlowNavigationHandler();
    errorHandler = MockCommonErrorHandler();
    exceptionHandler = MockLendingExceptionHandler();
    logger = MockLogger();

    cubit = getCubit();

    registerFallbackValue(Money.fromNumWithCurrency(500, Currency.aed));
    registerFallbackValue(const Period(months: 12));
  });

  const resultingSchedule = Schedule();
  final mockInstallment = Installment.fromDueAmount(
    dueDate: DateTime(2024, 11),
    amountDue: InstallmentAmount(
      fee: Money.fromNumWithCurrency(100, Currency.aed),
      principal: Money.fromNumWithCurrency(10000, Currency.aed),
      interest: Money.fromNumWithCurrency(1000, Currency.aed),
    ),
  );
  final disbursementEvaluationRequest = LoanDisbursementEvaluationRequest(
    amount: Money.fromNumWithCurrency(10000, Currency.aed),
    loanPeriod: const Period(months: 12),
    applicationId: application.id,
    loanProductIdentifier: LoanProductIdentifier.personalLoan,
  );

  group('initialize >', () {
    blocTest<_Cubit, _State>(
      'cubit successfully initialized',
      build: () => cubit,
      setUp: () {
        when(
          () => loanInteractor.evaluateDisbursement(
            request: disbursementEvaluationRequest,
          ),
        ).justAnswerAsync(resultingSchedule);
      },
      act: (cubit) => cubit.initialize(),
      expect: () => [
        const _State.idle(period: selectedPeriod, schedule: resultingSchedule),
      ],
    );

    blocTest<_Cubit, _State>(
      'should go to error if an error occurs on evaluateDisbursement',
      build: () => cubit,
      setUp: () {
        when(
          () => loanInteractor.evaluateDisbursement(
            request: disbursementEvaluationRequest,
          ),
        ).justThrowAsync(Exception('An error occured'));
      },
      act: (cubit) => cubit.initialize(),
      expect: () => [const _State.failed()],
    );

    blocTest<_Cubit, _State>(
      'should go to error if application does not have selected loan period '
      'or selected amount',
      build: () => getCubit(
        lendingApplication: TestEntities.getApplication(
          entryProductType: ProductType.personalLoan,
        ),
      ),
      act: (cubit) => cubit.initialize(),
      expect: () => [const _State.failed()],
      verify: (cubit) {
        verify(() => errorHandler.handleError(any())).calledOnce;
      },
    );
  });

  group('submit >', () {
    blocTest<_Cubit, _State>(
      'calls updateApplicationPreferences successfully',
      build: () => cubit,
      seed: () => const _State.idle(
        period: selectedPeriod,
        schedule: resultingSchedule,
      ),
      setUp: () {
        when(
          () => lendingInteractor.updateApplicationPreferences(
            applicationId: any(named: 'applicationId'),
            productType: ProductType.personalLoan,
            updatedPreferences: any(named: 'updatedPreferences'),
          ),
        ).justAnswerAsync(
          TestEntities.getApplication(
            entryProductType: ProductType.personalLoan,
          ),
        );
      },
      act: (cubit) => cubit.submit(),
      expect: () => [
        const _State.inProgress(
          period: selectedPeriod,
          schedule: resultingSchedule,
        ),
        const _State.idle(period: selectedPeriod, schedule: resultingSchedule),
      ],
      verify: (_) {
        verify(
          () => lendingInteractor.updateApplicationPreferences(
            applicationId: any(named: 'applicationId'),
            productType: ProductType.personalLoan,
            updatedPreferences: any(named: 'updatedPreferences'),
          ),
        ).calledOnce;
      },
    );

    blocTest<_Cubit, _State>(
      'error occurs on updateApplicationPreferences',
      build: () => cubit,
      seed: () => const _State.idle(
        period: selectedPeriod,
        schedule: resultingSchedule,
      ),
      setUp: () {
        when(
          () => lendingInteractor.updateApplicationPreferences(
            applicationId: any(named: 'applicationId'),
            productType: ProductType.personalLoan,
            updatedPreferences: any(named: 'updatedPreferences'),
          ),
        ).justThrowAsync(
          Exception('Error occurred while updating preferences'),
        );
      },
      act: (cubit) => cubit.submit(),
      expect: () => [
        const _State.inProgress(
          period: selectedPeriod,
          schedule: resultingSchedule,
        ),
        const _State.idle(period: selectedPeriod, schedule: resultingSchedule),
      ],
      verify: (_) {
        verifyInOrder([
          () => lendingInteractor.updateApplicationPreferences(
                applicationId: any(named: 'applicationId'),
                productType: ProductType.personalLoan,
                updatedPreferences: any(named: 'updatedPreferences'),
              ),
          () => exceptionHandler.handle(any(), LendingUiId.repayment_plan),
        ]);
      },
    );
  });

  blocTest<_Cubit, _State>(
    'showInstallmentDetails shows a bottom sheet with the content of '
    'instruments details',
    build: () => cubit,
    setUp: () {
      registerFallbackValue(const SizedBox.shrink());
      when(
        () => responsiveDialogProvider.showBottomSheetOrDialog<void>(any()),
      ).justCompleteAsync();
    },
    act: (cubit) => cubit.showInstallmentDetails(installment: mockInstallment),
    expect: () => <_State>[],
    verify: (cubit) {
      verify(
        () => responsiveDialogProvider.showBottomSheetOrDialog<void>(
          content: any(named: 'content'),
          config: any(named: 'config'),
        ),
      ).calledOnce;
    },
  );

  blocTest<_Cubit, _State>(
    'showProcessingFeeInfo shows a bottom sheet with the content of '
    'loan processing fee',
    build: () => cubit,
    seed: () =>
        const _State.idle(period: selectedPeriod, schedule: resultingSchedule),
    setUp: () {
      registerFallbackValue(const SizedBox.shrink());
      when(
        () => responsiveDialogProvider.showBottomSheetOrDialog<void>(
          content: any(named: 'content'),
          config: any(named: 'config'),
        ),
      ).justCompleteAsync();
    },
    act: (cubit) => cubit.showProcessingFeeInfo(),
    verify: (cubit) {
      verify(
        () => responsiveDialogProvider.showBottomSheetOrDialog<void>(any()),
      ).calledOnce;
    },
  );

  blocTest<_Cubit, _State>(
    'exitLendingApplication calls runExitFlow of flowNavigationHandler',
    build: () => cubit,
    setUp: () {
      when(() => flowNavigationHandler.runExitFlow()).justCompleteAsync();
    },
    act: (cubit) => cubit.exitLendingApplication(),
    expect: () => <_State>[],
    verify: (cubit) {
      verify(() => flowNavigationHandler.runExitFlow()).calledOnce;
    },
  );
}
