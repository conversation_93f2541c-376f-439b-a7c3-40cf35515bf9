import 'dart:io';
import 'dart:typed_data';

import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_file_picker_ui/file_picker_ui.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/src/screens/lending_upload/config/lending_upload_config.dart';
import 'package:wio_feature_lending_ui/src/screens/lending_upload/cubit/lending_document_upload_cubit.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late LendingDocumentUploadCubit cubit;
  late MockLogger logger;
  late MockLendingDocumentUploadDelegate delegate;
  late MockNavigationProvider navigationProvider;
  late MockLendingExceptionHand<PERSON> errorHandler;
  late MockResponsiveDialogProvider responsiveDialogProvider;
  late MockLendingAnalytics analytics;

  setUpAll(() {
    registerFallbackValue(Uint8List(0));
    registerFallbackValue(Uint8List.fromList([]));
    registerFallbackValue(NotificationToastMessageConfiguration.error(''));
    registerFallbackValue(FakeWidget());
  });

  setUp(() {
    logger = MockLogger();
    delegate = MockLendingDocumentUploadDelegate();
    navigationProvider = MockNavigationProvider();
    errorHandler = MockLendingExceptionHandler();
    responsiveDialogProvider = MockResponsiveDialogProvider();
    analytics = MockLendingAnalytics();
    cubit = LendingDocumentUploadCubit(
      logger: logger,
      delegate: delegate,
      localizations: MockLendingLocalizations(),
      navigationProvider: navigationProvider,
      errorHandler: errorHandler,
      responsiveDialogProvider: responsiveDialogProvider,
      config: LegacyFlowLendingApplicationUploadConfig(
        type: LendingUploadedDocumentType.salaryStatement,
        application: TestEntities.getApplication(),
      ),
      analytics: analytics,
    );

    when(() => delegate.uiId).thenReturn(LendingUiId.upload_salary_certificate);
  });

  group('LendingDocumentUploadCubit', () {
    blocTest<LendingDocumentUploadCubit, LendingDocumentUploadState>(
      'emits ready when init is called successfully',
      build: () => cubit,
      setUp: () {
        when(() => delegate.getExampleDocUrl())
            .thenAnswer((_) async => 'example_url');
        when(() => delegate.uploadedDocuments)
            .thenAnswer((_) => <Future<UploadDocument>>[]);
      },
      act: (cubit) => cubit.init(),
      expect: () => [
        const LendingDocumentUploadState.ready(
          documentExampleUrl: 'example_url',
        ),
      ],
      verify: (_) {
        verify(() => delegate.getExampleDocUrl()).calledOnce;
        verify(() => delegate.uploadedDocuments).calledOnce;
      },
    );

    blocTest<LendingDocumentUploadCubit, LendingDocumentUploadState>(
      'emits error when init fails',
      build: () => cubit,
      setUp: () {
        when(() => delegate.getExampleDocUrl()).thenThrow(Exception('error'));
      },
      act: (cubit) => cubit.init(),
      expect: () => [
        const LendingDocumentUploadState.error(message: 'INITIALISING_ERROR'),
      ],
    );

    blocTest<LendingDocumentUploadCubit, LendingDocumentUploadState>(
      'emits [ready, processing, ready] when submit is called successfully',
      build: () => cubit,
      seed: () => const LendingDocumentUploadState.ready(
        documentExampleUrl: 'example_url',
      ),
      setUp: () {
        when(
          () => delegate.submit(
            LegacyFlowLendingApplicationUploadConfig(
              type: LendingUploadedDocumentType.salaryStatement,
              application: TestEntities.getApplication(),
            ),
          ),
        ).thenAnswer((_) async {});
      },
      act: (cubit) => cubit.submit(),
      expect: () => [
        const LendingDocumentUploadState.processing(
          documentExampleUrl: 'example_url',
        ),
        const LendingDocumentUploadState.ready(
          documentExampleUrl: 'example_url',
        ),
      ],
      verify: (bloc) {
        verify(
          () => analytics.applicationStepSubmit(
            LendingUiId.upload_salary_certificate,
          ),
        ).calledOnce;
        verify(
          () => delegate.submit(
            LegacyFlowLendingApplicationUploadConfig(
              type: LendingUploadedDocumentType.salaryStatement,
              application: TestEntities.getApplication(),
            ),
          ),
        ).calledOnce;
      },
    );

    blocTest<LendingDocumentUploadCubit, LendingDocumentUploadState>(
      'emits [ready, processing, ready] when submit fails',
      build: () => cubit,
      seed: () => const LendingDocumentUploadState.ready(
        documentExampleUrl: 'example_url',
      ),
      setUp: () {
        when(
          () => delegate.submit(
            LegacyFlowLendingApplicationUploadConfig(
              type: LendingUploadedDocumentType.salaryStatement,
              application: TestEntities.getApplication(),
            ),
          ),
        ).thenThrow(Exception('error'));
      },
      act: (cubit) => cubit.submit(),
      expect: () => [
        const LendingDocumentUploadState.processing(
          documentExampleUrl: 'example_url',
        ),
        const LendingDocumentUploadState.ready(
          documentExampleUrl: 'example_url',
        ),
      ],
    );
    group('openFilePicker test', () {
      late File file;

      blocTest<LendingDocumentUploadCubit, LendingDocumentUploadState>(
        'select vat statement documents',
        build: () => cubit,
        seed: () => const LendingDocumentUploadState.ready(
          documentExampleUrl: 'documentExampleUrl',
        ),
        setUp: () {
          file = MockFile('path/${TestEntities.document.fileName}.pdf');
          when(
            () => responsiveDialogProvider
                .showBottomSheetOrDialog<CrossPlatformFile>(any()),
          ).thenAnswer(
            (_) async {
              return CrossPlatformFile.mobile(file: file);
            },
          );
          when(
            () => delegate.uploadDocument(
              fileBytes: any(named: 'fileBytes'),
              onProgress: any(named: 'onProgress'),
              fileName: any(named: 'fileName'),
            ),
          ).justAnswerAsync(TestEntities.document);

          when(() => file.readAsBytesSync()).thenReturn(Uint8List.fromList([]));
          when(() => file.lengthSync()).thenReturn(10000);
        },
        act: (cubit) => cubit.openFilePicker(),
        expect: () => <LendingDocumentUploadState>[
          LendingDocumentUploadState.ready(
            documents: [
              UploadedDocument(
                document: TestEntities.document,
                fileBytes: Uint8List.fromList([]),
              ),
            ],
            documentExampleUrl: 'documentExampleUrl',
          ),
        ],
      );

      blocTest<LendingDocumentUploadCubit, LendingDocumentUploadState>(
        'select file that already exists vat statement documents',
        build: () => cubit,
        seed: () => LendingDocumentUploadState.ready(
          documentExampleUrl: 'documentExampleUrl',
          documents: [
            UploadedDocument(
              document: TestEntities.document,
              fileBytes: Uint8List.fromList([]),
            ),
          ],
        ),
        setUp: () {
          file = MockFile('path/${TestEntities.document.fileName}');
          when(
            () => responsiveDialogProvider
                .showBottomSheetOrDialog<CrossPlatformFile>(any()),
          ).thenAnswer(
            (_) async {
              return CrossPlatformFile.mobile(file: file);
            },
          );
          when(
            () => delegate.uploadDocument(
              fileBytes: any(named: 'fileBytes'),
              onProgress: any(named: 'onProgress'),
              fileName: any(named: 'fileName'),
            ),
          ).justAnswerAsync(TestEntities.document);

          when(() => file.readAsBytesSync()).thenReturn(Uint8List.fromList([]));
          when(() => file.lengthSync()).thenReturn(10000);
        },
        act: (cubit) => cubit.openFilePicker(),
        verify: (_) {
          verify(
            () => responsiveDialogProvider.showResponsiveToastMessage(any()),
          ).called(1);
        },
        expect: () {
          return <LendingDocumentUploadState>[];
        },
      );

      blocTest<LendingDocumentUploadCubit, LendingDocumentUploadState>(
        'select file selected file too big',
        build: () => cubit,
        seed: () => LendingDocumentUploadState.ready(
          documentExampleUrl: 'documentExampleUrl',
          documents: [
            UploadedDocument(
              document: TestEntities.document,
              fileBytes: Uint8List.fromList([]),
            ),
          ],
        ),
        setUp: () {
          file = MockFile('path/vat1.pdf');
          when(
            () => responsiveDialogProvider
                .showBottomSheetOrDialog<CrossPlatformFile>(
              content: any(named: 'content'),
              config: any(named: 'config'),
            ),
          ).thenAnswer(
            (_) async {
              return CrossPlatformFile.mobile(file: file);
            },
          );
          when(
            () => delegate.uploadDocument(
              fileBytes: any(named: 'fileBytes'),
              onProgress: any(named: 'onProgress'),
              fileName: any(named: 'fileName'),
            ),
          ).justAnswerAsync(TestEntities.document);

          when(() => file.readAsBytesSync())
              .thenReturn(Uint8List((maxFileSizeLimit + 5).toInt()));
          when(() => file.lengthSync())
              .thenReturn((maxFileSizeLimit + 5).toInt());
        },
        act: (cubit) => cubit.openFilePicker(),
        verify: (_) {
          verify(
            () => responsiveDialogProvider.showResponsiveToastMessage(any()),
          ).called(1);
        },
        expect: () {
          return <LendingDocumentUploadState>[];
        },
      );
    });

    blocTest<LendingDocumentUploadCubit, LendingDocumentUploadState>(
      '''emits [ready] when removeDocument is called and document is removed successfully''',
      build: () => cubit,
      seed: () => LendingDocumentUploadState.ready(
        documentExampleUrl: 'example_url',
        documents: [
          UploadedDocument(
            document: TestEntities.document,
            fileBytes: Uint8List(0),
          ),
        ],
      ),
      setUp: () {
        when(() => delegate.deleteDocument(any())).thenAnswer((_) async {});
        when(() => delegate.uploadedDocuments).thenAnswer((_) => []);
      },
      act: (cubit) => cubit.removeDocument(
        UploadedDocument(
          document: TestEntities.document,
          fileBytes: Uint8List(0),
        ),
      ),
      expect: () => [
        const LendingDocumentUploadState.ready(
          documentExampleUrl: 'example_url',
        ),
      ],
    );
  });
}
