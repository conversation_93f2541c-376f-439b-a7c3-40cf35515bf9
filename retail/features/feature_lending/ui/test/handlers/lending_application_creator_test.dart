import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_content_domain_api/index.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/src/handlers/lending_application_creator/lending_application_creator.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late LendingApplicationCreator applicationCreator;
  late MockLendingInteractor mockInteractor;
  late MockContentInteractor mockContentInteractor;
  late MockFeatureToggleProvider mockFeatureToggleProvider;
  late MockNavigationProvider mockNavigationProvider;
  late MockLendingFlow mockLendingFlow;

  setUp(() {
    mockInteractor = MockLendingInteractor();
    mockContentInteractor = MockContentInteractor();
    mockFeatureToggleProvider = MockFeatureToggleProvider();
    mockNavigationProvider = MockNavigationProvider();
    mockLendingFlow = MockLendingFlow();

    applicationCreator = LendingApplicationCreator(
      interactor: mockInteractor,
      contentInteractor: mockContentInteractor,
      navigationProvider: mockNavigationProvider,
      featureToggleProvider: mockFeatureToggleProvider,
      lendingFlow: mockLendingFlow,
    );

    registerFallbackValue(const Document(arUrl: 'ar/tnc', enUrl: 'en/tnc'));
  });

  group('LendingApplicationCreator', () {
    test('createApplication should create new application and navigate to flow',
        () async {
      final tnc = LegalDocument(
        version: '',
        type: LegalDocumentType.lendingKeyFactsStatement,
        urls: LocalizedUrls(arabicUrl: 'ar/tc', englishUrl: 'en/tc'),
      );
      final kfs = LegalDocument(
        version: '',
        type: LegalDocumentType.lendingKeyFactsStatement,
        urls: LocalizedUrls(arabicUrl: 'ar/kfs', englishUrl: 'en/kfs'),
      );

      // Mock the retrieval of documents
      when(
        () => mockContentInteractor.getDocumentByType(
          LegalDocumentType.lendingPersonalLoanTermsAndConditions,
        ),
      ).justAnswerAsync(tnc);
      when(
        () => mockContentInteractor.getDocumentByType(
          LegalDocumentType.lendingPersonalLoanKeyFactsStatement,
        ),
      ).thenAnswer((_) async => kfs);

      final application = TestEntities.getApplication();

      // Mock application creation
      when(
        () => mockInteractor.createApplication(
          productType: ProductType.personalLoan,
          tnc: any(named: 'tnc'),
          kfs: any(named: 'kfs'),
        ),
      ).thenAnswer((_) async => application);

      // Mock the navigation to the application flow
      when(() => mockLendingFlow.run(any())).thenAnswer((_) async => {});

      await applicationCreator.createApplication(ProductType.personalLoan);

      // Verify that the correct methods are called
      verify(
        () => mockInteractor.createApplication(
          productType: ProductType.personalLoan,
          tnc: any(named: 'tnc'),
          kfs: any(named: 'kfs'),
        ),
      ).called(1);

      verify(() => mockLendingFlow.run(any())).called(1);
      verify(() => mockNavigationProvider.popUntilFirstRoute()).called(1);
    });

    test(
        '''createApplicationFromCompleted should create application from completed and navigate''',
        () async {
      final completedApplication = TestEntities.getApplication();
      final application = TestEntities.getApplication();
      final tnc = LegalDocument(
        version: '',
        type: LegalDocumentType.lendingKeyFactsStatement,
        urls: LocalizedUrls(arabicUrl: 'ar/tc', englishUrl: 'en/tc'),
      );
      final kfs = LegalDocument(
        version: '',
        type: LegalDocumentType.lendingKeyFactsStatement,
        urls: LocalizedUrls(arabicUrl: 'ar/kfs', englishUrl: 'en/kfs'),
      );

      // Mock feature toggle for salary certificate upload
      when(
        () => mockFeatureToggleProvider
            .get(LendingFeatureToggles.isPlUploadSalaryCertificateEnabled),
      ).thenReturn(true);

      when(
        () => mockContentInteractor.getDocumentByType(
          LegalDocumentType.lendingPersonalLoanTermsAndConditions,
        ),
      ).justAnswerAsync(tnc);
      when(
        () => mockContentInteractor.getDocumentByType(
          LegalDocumentType.lendingPersonalLoanKeyFactsStatement,
        ),
      ).thenAnswer((_) async => kfs);

      // Mock application creation from completed
      when(
        () => mockInteractor.createNewApplicationFromApplicationData(
          productType: ProductType.personalLoan,
          applicationData: completedApplication.selfDeclaredData,
          onboardingStatus: OnboardingStatus.salaryCertificateUpload,
          kfs: any(named: 'kfs'),
          tnc: any(named: 'tnc'),
        ),
      ).thenAnswer((_) async => application);

      // Mock the navigation to the application flow
      when(() => mockLendingFlow.run(any())).thenAnswer((_) async => {});

      await applicationCreator.createApplicatiomFromCompleted(
        completedApplication: completedApplication,
        offer: LendingOffer(
          amount: Money.fromNumWithCurrency(1200, Currency.aed),
          productType: ProductType.personalLoan,
        ),
      );

      // Verify that the correct methods are called
      verify(
        () => mockInteractor.createNewApplicationFromApplicationData(
          productType: ProductType.personalLoan,
          applicationData: completedApplication.selfDeclaredData,
          onboardingStatus: OnboardingStatus.salaryCertificateUpload,
          tnc: any(named: 'tnc'),
          kfs: any(named: 'kfs'),
        ),
      ).called(1);

      verify(() => mockLendingFlow.run(any())).called(1);
      verify(() => mockNavigationProvider.popUntilFirstRoute()).called(1);
    });

    test(
        '''createApplicatiomFromExpired should create application from expired and navigate''',
        () async {
      final expiredApplication = TestEntities.getApplication();
      final application = TestEntities.getApplication();
      final tnc = LegalDocument(
        version: '',
        type: LegalDocumentType.lendingKeyFactsStatement,
        urls: LocalizedUrls(arabicUrl: 'ar/tc', englishUrl: 'en/tc'),
      );
      final kfs = LegalDocument(
        version: '',
        type: LegalDocumentType.lendingKeyFactsStatement,
        urls: LocalizedUrls(arabicUrl: 'ar/kfs', englishUrl: 'en/kfs'),
      );

      when(
        () => mockContentInteractor.getDocumentByType(
          LegalDocumentType.lendingTermsAndConditions,
        ),
      ).justAnswerAsync(tnc);
      when(
        () => mockContentInteractor.getDocumentByType(
          LegalDocumentType.lendingKeyFactsStatement,
        ),
      ).thenAnswer((_) async => kfs);

      // Mock application creation from expired
      when(
        () => mockInteractor.createNewApplicationFromApplicationData(
          productType: ProductType.creditCard,
          applicationData: expiredApplication.selfDeclaredData,
          onboardingStatus: OnboardingStatus.repaymentCheck,
          kfs: any(named: 'kfs'),
          tnc: any(named: 'tnc'),
        ),
      ).thenAnswer((_) async => application);

      // Mock the navigation to the application flow
      when(() => mockLendingFlow.run(any())).thenAnswer((_) async => {});

      await applicationCreator.createApplicatiomFromExpired(expiredApplication);

      // Verify that the correct methods are called
      verify(
        () => mockInteractor.createNewApplicationFromApplicationData(
          productType: ProductType.creditCard,
          applicationData: expiredApplication.selfDeclaredData,
          onboardingStatus: OnboardingStatus.repaymentCheck,
          kfs: any(named: 'kfs'),
          tnc: any(named: 'tnc'),
        ),
      ).called(1);

      verify(() => mockLendingFlow.run(any())).called(1);
      verify(() => mockNavigationProvider.popUntilFirstRoute()).called(1);
    });
  });
}
