import 'dart:async';
import 'dart:typed_data';

import 'package:domain/data/data.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

class LendingInteractorImpl implements LendingInteractor {
  final LendingRepository _repository;
  final LoanAccountRepository _loanAccountRepository;
  final LendingSummaryUseCase _lendingSummaryUseCase;

  const LendingInteractorImpl({
    required LendingRepository repository,
    required LoanAccountRepository loanAccountRepository,
    required LendingSummaryUseCase lendingSummaryUseCase,
  })  : _repository = repository,
        _loanAccountRepository = loanAccountRepository,
        _lendingSummaryUseCase = lendingSummaryUseCase;

  @override
  Future<LendingApplication> createApplication({
    required ProductType productType,
    required Document kfs,
    required Document tnc,
  }) async {
    final app = await _repository.createApplication(
      productType: productType,
      kfs: kfs,
      tnc: tnc,
    );
    await _repository.refreshApplications();
    await _repository.refreshOffers(force: true);

    return app;
  }

  @override
  Future<Applications> getApplications() => _repository.getApplications();

  @override
  Stream<Data<Applications>> observeApplications({bool refresh = false}) =>
      _repository.observeApplications(refresh: refresh);

  @override
  Future<void> refreshApplications({bool force = false}) =>
      _repository.refreshApplications(force: force);

  @override
  Future<LendingApplication> updateApplication({
    required String applicationId,
    required OnboardingStatus nextStatus,
    ApplicationData? updatedApplicationFields,
  }) async {
    final updatedApplication = await _repository.updateApplication(
      applicationId: applicationId,
      nextStatus: nextStatus,
      updatedApplicationFields: updatedApplicationFields,
    );

    await refreshApplications();

    return updatedApplication;
  }

  @override
  Future<LendingApplication> updateApplicationPreferences({
    required String applicationId,
    required ProductType productType,
    ApplicationPreferences? updatedPreferences,
  }) async {
    final updatedApplication = await _repository.updateApplicationPreferences(
      applicationId: applicationId,
      productType: productType,
      updatedPreferences: updatedPreferences,
    );

    await refreshApplications();

    return updatedApplication;
  }

  @override
  Future<LendingApplication> startCreditDecision(String applicationId) async {
    final app = await _repository.startCreditDecision(applicationId);
    await _repository.refreshApplications();

    return app;
  }

  @override
  Future<LendingApplication> signAgreement({
    required String applicationId,
    required ProductType productType,
  }) async {
    final app = await switch (productType) {
      ProductType.creditCard => _repository.signAgreement(applicationId),
      ProductType.personalLoan ||
      ProductType.autoLoan =>
        _repository.signApplicationAgreement(
          applicationId: applicationId,
          productType: productType,
        ),
      _ => _repository.signAgreement(applicationId),
    };

    await _repository.refreshApplications();

    return app;
  }

  @override
  Future<LendingApplication> submitAgreement(String applicationId) async {
    final application = await _repository.submitAgreement(applicationId);

    await _repository.refreshApplications();

    return application;
  }

  @override
  Future<LendingFile> getAgreement({
    required String applicationId,
    required String lang,
    required ProductType productType,
  }) =>
      switch (productType) {
        ProductType.creditCard =>
          _repository.getAgreement(applicationId: applicationId, lang: lang),
        ProductType.personalLoan ||
        ProductType.autoLoan =>
          _repository.getApplicationAgreement(
            applicationId: applicationId,
            lang: lang,
            productType: productType,
          ),
        _ => _repository.getAgreement(applicationId: applicationId, lang: lang),
      };

  @override
  Future<LendingApplication> startAccountCreation({
    required String applicationId,
    required ProductType productType,
  }) async {
    final application = await _repository.startAccountCreation(
      applicationId: applicationId,
      productType: productType,
    );

    await _repository.refreshApplications();

    return application;
  }

  @override
  Future<Offers> getOffers() => _repository.getOffers();

  @override
  Future<LendingApplication> acknowledgeCreditDecisionRejection(
    String applicationId,
  ) async {
    final application =
        await _repository.acknowledgeCreditDecisionRejection(applicationId);
    await _repository.refreshApplications();

    return application;
  }

  @override
  Future<LendingApplication> getApplication(String applicationId) async {
    final application = await _repository.getApplication(applicationId);

    return application;
  }

  @override
  Stream<Data<LendingSummary>> observeLendingSummary() =>
      _lendingSummaryUseCase();

  @override
  Stream<Data<LendingSummaryWithAutopay>> observeLendingSummaryWithAutopay() =>
      _lendingSummaryUseCase.lendingSummaryWithAutopay();

  @override
  Future<LendingApplication> updateEmail({
    required String applicationId,
    required OnboardingStatus nextStatus,
    required String? emailAddress,
  }) async {
    final lendingApplication =
        await _repository.updateEmail(applicationId, nextStatus, emailAddress);

    await refreshApplications();

    return lendingApplication;
  }

  @override
  Future<String> saveFile({
    required FileData data,
    required String fileName,
  }) =>
      _repository.saveFile(file: data, name: fileName);

  @override
  Future<void> refreshLendingSummary() {
    return Future.wait<void>([
      refreshApplications(force: true),
      _loanAccountRepository.refreshAccounts(),
      _repository.refreshOffers(force: true),
    ]);
  }

  @override
  Future<LendingApplication> createNewApplicationFromApplicationData({
    required ProductType productType,
    required ApplicationData applicationData,
    required OnboardingStatus onboardingStatus,
    required Document kfs,
    required Document tnc,
  }) async {
    final newApplication = await createApplication(
      productType: productType,
      kfs: kfs,
      tnc: tnc,
    );

    final application = await updateApplication(
      applicationId: newApplication.id,
      nextStatus: onboardingStatus,
      updatedApplicationFields: applicationData,
    );

    return application;
  }

  @override
  Future<LendingConfiguration> getLendingConfiguration(
    ProductType productType,
  ) =>
      _repository.getLendingConfiguration(productType);

  @override
  Future<LendingUploadedDocument> uploadDocument({
    required String applicationId,
    required Uint8List fileBytes,
    required LendingUploadedDocumentType type,
    required String fileName,
    UploadProgressCallback? onProgress,
  }) =>
      _repository.uploadDocument(
        applicationId: applicationId,
        fileBytes: fileBytes,
        fileName: fileName,
        type: type,
        onProgress: onProgress,
      );

  @override
  Future<void> deleteDocument({
    required String applicationId,
    required String documentId,
  }) =>
      _repository.deleteDocument(
        applicationId: applicationId,
        documentId: documentId,
      );

  @override
  Future<String> downloadDocument({
    required String applicationId,
    required String documentId,
  }) =>
      _repository.downloadDocument(
        applicationId: applicationId,
        documentId: documentId,
      );

  @override
  Future<LendingApplication> submitFcrDetails({
    required String applicationId,
    required FcrDetails fcrDetails,
  }) async {
    final application = await _repository.submitFcrDetails(
      applicationId: applicationId,
      fcrDetails: fcrDetails,
    );

    unawaited(refreshApplications());

    return application;
  }
}
