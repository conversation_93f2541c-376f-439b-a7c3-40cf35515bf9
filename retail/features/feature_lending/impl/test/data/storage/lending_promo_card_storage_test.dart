import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_feature_lending_impl/src/data/storage/lending_promo_card_storage.dart';

import '../../mocks.dart';

void main() {
  late LendingPromoCardStorageImpl lendingPromoCardStorage;
  late MockKeyValueStorage mockKeyValueStorage;

  setUp(() {
    mockKeyValueStorage = MockKeyValueStorage();
    lendingPromoCardStorage =
        LendingPromoCardStorageImpl(storage: mockKeyValueStorage);
  });

  group('LendingPromoCardStorageImpl >', () {
    test(
      'isLendingPromoCardDismissed should return false if storage returns null',
      () async {
        // Arrange
        when(() => mockKeyValueStorage.getByKey<bool>(any()))
            .justCompleteAsync();

        // Act
        final result =
            await lendingPromoCardStorage.isLendingPromoCardDismissed();

        // Assert
        expect(result, false);
      },
    );

    test(
      'isLendingPromoCardDismissed should return true if storage returns true',
      () async {
        // Arrange
        when(
          () => mockKeyValueStorage.getByKey<bool>(any()),
        ).justAnswerAsync(true);

        // Act
        final result =
            await lendingPromoCardStorage.isLendingPromoCardDismissed();

        // Assert
        expect(result, true);
      },
    );

    test('dismissLendingPromoCard should store true in the storage', () async {
      // Arrange
      when(() => mockKeyValueStorage.put(any(), true)).justCompleteAsync();

      // Act
      await lendingPromoCardStorage.dismissLendingPromoCard();

      // Assert
      verify(
        () => mockKeyValueStorage.put<bool>(any(), true),
      ).calledOnce;
    });
  });
}
