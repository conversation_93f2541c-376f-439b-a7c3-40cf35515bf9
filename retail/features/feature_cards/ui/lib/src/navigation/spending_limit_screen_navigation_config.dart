import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

part 'spending_limit_screen_navigation_config.freezed.dart';

enum SpendingLimitEditBottomSheetResult {
  edit,
  reset,
  remove,
}

@Freezed(copyWith: false)
class SpendingLimitScreenNavigationConfig extends ScreenNavigationConfig
    with _$SpendingLimitScreenNavigationConfig {
  static const name = 'spending_limit_screen';

  const factory SpendingLimitScreenNavigationConfig({
    required CardType cardType,
    required SpendingLimitScreenMaxDailyLimit maxDailyLimit,
    SpendingLimitRecord? limitRecord,
  }) = _SpendingLimitScreenNavigationConfig;

  const SpendingLimitScreenNavigationConfig._()
      : super(
          feature: CardsFeatureNavigationConfig.name,
          id: name,
        );

  @override
  String toString() => 'SpendingLimitScreenNavigationConfig';

  @override
  bool get isCriticalView => true;
}

@freezed
class SpendingLimitEditBottomSheetNavigationConfig
    with _$SpendingLimitEditBottomSheetNavigationConfig
    implements BottomSheetNavigationConfig<SpendingLimitEditBottomSheetResult> {
  const factory SpendingLimitEditBottomSheetNavigationConfig({
    required bool showResetOption,
    @Default(true) bool showRemoveOption,
  }) = _SpendingLimitEditBottomSheetNavigationConfig;

  const SpendingLimitEditBottomSheetNavigationConfig._();

  @override
  String get feature => CardsFeatureNavigationConfig.name;

  @override
  String toString() => 'SpendingLimitEditBottomSheetNavigationConfig';
}

@freezed
class SpendingLimitScreenMaxDailyLimit with _$SpendingLimitScreenMaxDailyLimit {
  /// Provided max daily limit will be applied to the spending limit selection
  const factory SpendingLimitScreenMaxDailyLimit({
    required Money limit,
  }) = _SpendingLimitScreenMaxDailyLimit;

  /// No max daily limit will be applied to the spending limit selection
  const factory SpendingLimitScreenMaxDailyLimit.none() =
      _SpendingLimitScreenNoMaxDailyLimit;

  /// Max daily limit will be fetched from config and applied to the spending
  /// limit selection
  const factory SpendingLimitScreenMaxDailyLimit.missing() =
      _SpendingLimitScreenMissingMaxDailyLimit;
}
