import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

part 'card_details_bottom_sheet_navigation_config.freezed.dart';

@freezed
class CardDetailsBottomSheetNavigationConfig
    with _$CardDetailsBottomSheetNavigationConfig
    implements BottomSheetNavigationConfig<void> {
  const CardDetailsBottomSheetNavigationConfig._();

  const factory CardDetailsBottomSheetNavigationConfig({
    required String cardId,
  }) = _CardDetailsBottomSheetNavigationConfig;

  @override
  String get feature => CardsFeatureNavigationConfig.name;

  @override
  String toString() => 'CardDetailsBottomSheetNavigationConfig';
}
