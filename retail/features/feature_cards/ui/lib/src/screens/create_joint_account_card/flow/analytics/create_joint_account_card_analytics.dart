// ignore_for_file: constant_identifier_names
import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:feature_cards_ui/src/common/models/json_payload.dart';
import 'package:feature_cards_ui/src/screens/create_joint_account_card/flow/cubit/create_joint_account_card_flow_state.dart';
import 'package:wio_app_core_api/index.dart';

enum CreateJointAccountCardAnalyticsTarget {
  complete_intro_screen,
  complete_things_to_know_screen,
  select_joint_account,
  select_card_name,
  back_to_card_name_screen,
  select_card_skin,
  complete_set_up_card,
  back_to_previous_screen,
  complete_recipient_selection,
  edit_expiry_date,
  edit_spending_limit,
  select_expiry_date,
  select_spending_limit,
  edit_recipient,
  submit,
  add_to_digital_wallet,
  go_to_card,
  back_to_hub,
}

class CreateJointAccountCardAnalytics {
  final AnalyticsEventTracker _analyticsEventTracker;

  CreateJointAccountCardAnalytics(
    AnalyticsTrackerFactory analyticsTrackerFactory, {
    required String screenName,
  }) : _analyticsEventTracker = analyticsTrackerFactory.get(screenName);

  void onPopScreen(CreateJointAccountCardStage stage) {
    final screenName = switch (stage) {
      CreateJointAccountCardStage.intro => 'intro_screen',
      CreateJointAccountCardStage.thingsToKnow => 'things_to_know_screen',
      CreateJointAccountCardStage.selectJointAccount =>
        'select_joint_account_screen',
      CreateJointAccountCardStage.setUpCard => 'set_up_card_screen',
      CreateJointAccountCardStage.selectRecipient => 'select_recipient_screen',
      CreateJointAccountCardStage.review => 'review_screen',
      CreateJointAccountCardStage.setUpSpendingLimit =>
        'set_up_spending_limit_screen',
      CreateJointAccountCardStage.success => 'success_screen',
    };

    _analyticsEventTracker.click(
      targetType: AnalyticsTargetType.button,
      target: CreateJointAccountCardAnalyticsTarget.back_to_previous_screen,
      payload: JsonPayload({'screenPopped': screenName}),
    );
  }

  void onCompleteIntroScreen() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget.complete_intro_screen,
      );

  void onCompleteThingToKnowScreen() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget
            .complete_things_to_know_screen,
      );

  void onSelectJointAccount() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget.select_joint_account,
      );

  void onSelectCardName() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget.select_card_name,
      );

  void onBackToCardNameScreen() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget.back_to_card_name_screen,
      );

  void onSelectSkin(CardImage skin) => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget.select_card_skin,
        payload: JsonPayload({'cardSkinId': skin.id}),
      );

  void onCompleteSetUpCard() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget.complete_set_up_card,
      );

  void onCompleteRecipientSelection(String recipientType) =>
      _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target:
            CreateJointAccountCardAnalyticsTarget.complete_recipient_selection,
        payload: JsonPayload({'recipientType': recipientType}),
      );

  void onEditSpendingLimit() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget.edit_spending_limit,
      );

  void onEditExpiryDate() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget.edit_expiry_date,
      );

  void onEditRecipient() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget.edit_recipient,
      );

  void onSelectSpendingLimit() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget.select_spending_limit,
      );

  void onSelectExpiryDate() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget.select_expiry_date,
      );

  void onSubmit() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget.submit,
      );

  void onClickAddToDigitalWallet() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget.add_to_digital_wallet,
      );

  void onClickGoToCard() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget.go_to_card,
      );

  void onClickBackToHub() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CreateJointAccountCardAnalyticsTarget.back_to_hub,
      );

  void onCardCreated() => _analyticsEventTracker.view(
        targetType: AnalyticsTargetType.screen,
        status: AnalyticsStatus.success,
      );

  void onCardCreationFailed() => _analyticsEventTracker.view(
        targetType: AnalyticsTargetType.toast,
        status: AnalyticsStatus.error,
      );
}
