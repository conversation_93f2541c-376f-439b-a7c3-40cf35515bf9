import 'package:feature_cards_ui/l10n/cards_localization.g.dart';
import 'package:feature_cards_ui/src/common/models/card_account_type.dart';
import 'package:feature_cards_ui/src/common/widgets/wio_credit_banner.dart';
import 'package:feature_cards_ui/src/screens/cards/cards_screen.dart';
import 'package:feature_cards_ui/src/screens/cards/cubit/cards_cubit.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_lending_api/lending_api.dart';

class CardsSpendingModeSwitcher extends StatefulWidget {
  final Money? myMoneyBalance;
  final Money? creditAvailableToSpend;
  final CardAccountType? cardAccountType;

  const CardsSpendingModeSwitcher({
    required this.myMoneyBalance,
    required this.creditAvailableToSpend,
    this.cardAccountType,
    super.key,
  });

  @override
  State<CardsSpendingModeSwitcher> createState() =>
      _CardsSpendingModeSwitcherState();
}

class _CardsSpendingModeSwitcherState extends State<CardsSpendingModeSwitcher> {
  final _bannerOverlay = const BannerOverlay();
  final lendingGuideLayerLink = LayerLink();
  final lendingGuideKey = GlobalKey();

  late final _switchController = CompanySwitchController(
    initialSide: _mapCardAccountTypeToSwitchSide(widget.cardAccountType),
  );

  static CompanySwitchSideType _mapCardAccountTypeToSwitchSide(
    CardAccountType? type,
  ) {
    switch (type) {
      case CardAccountType.loan:
        return CompanySwitchSideType.right;
      default:
        return CompanySwitchSideType.left;
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => _checkHasCoachMark());
  }

  @override
  void dispose() {
    _switchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<CardsCubit>();
    final canCreateCreditCard = context.select<CardsCubit, bool>(
      (cubit) => cubit.state.canCreateCreditCard,
    );
    final isCreditCardApplicationRejected = context.select<CardsCubit, bool>(
      (cubit) => cubit.state.isCreditCardApplicationRejected,
    );

    return Padding(
      padding: const EdgeInsets.fromLTRB(
        CardsScreen.horizontalPadding,
        24,
        CardsScreen.horizontalPadding,
        8,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Opacity(
            opacity: isCreditCardApplicationRejected ? 0.32 : 1,
            child: CompositedTransformTarget(
              link: lendingGuideLayerLink,
              child: _WioCreditSwitch(
                key: lendingGuideKey,
                myMoneyBalance: widget.myMoneyBalance,
                creditAvailableToSpend: widget.creditAvailableToSpend,
                switchController: _switchController,
                onSwitchToggle: _onToggle,
              ),
            ),
          ),
          if (isCreditCardApplicationRejected) ...[
            const SizedBox(height: 16),
            _ApplicationRejectedCard(onCtaPressed: cubit.handleWioCreditTap),
          ],
          if (canCreateCreditCard) ...[
            const SizedBox(height: 16),
            WioCreditBanner(onCtaPressed: cubit.handleWioCreditTap),
          ],
        ],
      ),
    );
  }

  void _checkHasCoachMark() {
    final cubit = context.read<CardsCubit>();
    final isCurrentRoute = ModalRoute.of(context)?.isCurrent ?? false;
    if (cubit.state.lendingGuideStage == LendingGuideStage.notStarted &&
        cubit.state.hasCreditAccount &&
        isCurrentRoute) {
      _showCoachMark(
        context,
        cubit.state.creditApplicationEntryStatus,
        cubit.updateLendingGuideProgress,
      );
    }
  }

  void _showCoachMark(
    BuildContext context,
    ApplicationEntryPointStatus status,
    AsyncCallback onUpdateGuideProgress,
  ) {
    _bannerOverlay.showAttached(
      context: context,
      targetKey: lendingGuideKey,
      targetLink: lendingGuideLayerLink,
      onTapOut: () => onUpdateGuideProgress(),
      builder: (_, animation) {
        final localization = CardsLocalizations.of(context);

        return BlocProvider.value(
          value: context.read<CardsCubit>(),
          child: Padding(
            padding: const EdgeInsetsDirectional.only(end: 52.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _WioCreditSwitch(
                  myMoneyBalance: widget.myMoneyBalance,
                  creditAvailableToSpend: widget.creditAvailableToSpend,
                  switchController: _switchController,
                  onSwitchToggle: _onToggle,
                ),
                const SizedBox(height: 8.0),
                CoachMark(
                  coachMarkModel: CoachMarkModel(
                    title: localization.cardsCoackmarkTitle,
                    body: localization.cardsCoachmarkBody,
                    primaryCtaText: localization.cardsCoachmarkCtaText,
                    gradientPointer: CoachMarkGradient.indigo,
                    direction: CoachMarkPointerDirection.none,
                  ),
                  closeAction: onUpdateGuideProgress,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _onToggle() {
    final cubit = context.read<CardsCubit>();
    final status = cubit.state.creditApplicationEntryStatus;

    if (status.isCreditCardApplicationRejected) return;

    if (widget.cardAccountType == null) {
      if (_switchController.currentSelectedSide ==
          CompanySwitchSideType.right) {
        cubit.handleWioCreditTap();
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _switchController.selectSide(CompanySwitchSideType.left);
        });
      }
    } else {
      cubit.switchAccount();
    }
  }
}

class CardsSpendingModeLoadingSwitcher extends StatelessWidget {
  const CardsSpendingModeLoadingSwitcher({super.key});

  @override
  Widget build(BuildContext context) {
    final zeroAmount = Money.fromNumWithCurrency(0, Currency.aed);

    return CompanyShimmer(
      model: const CompanyShimmerModel(),
      child: Column(
        children: [
          Space.fromSpacingVertical(Spacing.s3),
          FractionallySizedBox(
            widthFactor: 0.5,
            child: Container(
              height: 16,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(14)),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(
              CardsScreen.horizontalPadding,
              CardsScreen.horizontalPadding,
              CardsScreen.horizontalPadding,
              8,
            ),
            child: _WioCreditSwitch(
              myMoneyBalance: zeroAmount,
              creditAvailableToSpend: zeroAmount,
            ),
          ),
        ],
      ),
    );
  }
}

class _WioCreditSwitch extends StatelessWidget {
  final CompanySwitchController? switchController;
  final VoidCallback? onSwitchToggle;
  final Money? myMoneyBalance;
  final Money? creditAvailableToSpend;

  const _WioCreditSwitch({
    this.myMoneyBalance,
    this.creditAvailableToSpend,
    this.switchController,
    this.onSwitchToggle,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = CardsLocalizations.of(context);

    return CompanySwitch(
      controller: switchController,
      onToggle: onSwitchToggle,
      model: CompanySwitchModel(
        leftSide: CompanySwitchItemModel(
          title: localizations.lendingMyMoney,
          subtitle: myMoneyBalance?.toCodeOnRightFormat(),
        ),
        rightSide: CompanySwitchItemModel(
          title: localizations.cardsSpendingSwitcherTabMyCredit,
          subtitle: creditAvailableToSpend?.toCodeOnRightFormat(),
        ),
      ),
    );
  }
}

class _ApplicationRejectedCard extends StatelessWidget {
  final VoidCallback onCtaPressed;

  const _ApplicationRejectedCard({
    required this.onCtaPressed,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = CardsLocalizations.of(context);

    return ProductsInsightCard(
      ProductsInsightCardModel(
        tileModel: const TileModel.icon(
          icon: GraphicAssetPointer.emoji(CompanyEmojiPointer.frowning_face),
          size: TileSize.medium,
          backgroundColor: CompanyColorPointer.secondary6,
        ),
        title: LabelModel(
          text: localizations.lendingCreditApplication,
          textStyle: CompanyTextStylePointer.b4,
          color: CompanyColorPointer.secondary4,
        ),
        subtitle: LabelModel(
          text: localizations.lendingApplicationStatusRejectedDescription,
          textStyle: CompanyTextStylePointer.b2,
          color: CompanyColorPointer.primary3,
        ),
        cta: ButtonModel(
          title: localizations.lendingApplicationStatusRejectedCta,
        ),
      ),
      onCtaPressed: onCtaPressed,
    );
  }
}
