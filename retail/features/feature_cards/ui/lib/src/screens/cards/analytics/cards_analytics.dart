// ignore_for_file: constant_identifier_names
import 'dart:io';

import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:feature_cards_ui/src/navigation/bottom_sheets/shared_card_info_bottom_sheet_config.dart';
import 'package:feature_cards_ui/src/navigation/cards_screen_navigation_config.dart';
import 'package:wio_app_core_api/index.dart';

enum CardsAnalyticsTarget {
  dashboard,
  activate_card,
  card_activation_contextual_faq,
  freeze_card,
  unfreeze_card,
  refresh_cards,
  settings_menu,
  change_spending_limit,
  create_new_card,
  card_details_menu,
  unlock_card,
  add_to_apple_wallet,
  add_to_google_wallet,
  order_physical_card,
  see_all_transactions,
  convert_to_shared,
  view_shared_card_info,
  shared_card_action,
  create_new_card_option,
}

class CardsAnalytics {
  final AnalyticsEventTracker _analyticsEventTracker;
  final AnalyticsEventTracker _airshipTracker;

  CardsAnalytics({
    required AnalyticsTrackerFactory analyticsTrackerFactory,
    required AnalyticsAbstractTrackerFactory analyticsFactory,
  })  : _airshipTracker = analyticsFactory.get(
          screenName: CardsScreenNavigationConfig.screenId,
          tracker: AnalyticsTracker.airship,
        ),
        _analyticsEventTracker = analyticsTrackerFactory.get('cards_screen');

  void clickActiveCard() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CardsAnalyticsTarget.activate_card,
      );

  void clickCardContextualFAQClick() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CardsAnalyticsTarget.card_activation_contextual_faq,
      );

  void clickFreezeCard() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CardsAnalyticsTarget.freeze_card,
      );

  void clickUnfreezeCard() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CardsAnalyticsTarget.unfreeze_card,
      );

  void clickRefreshCards() => _analyticsEventTracker.drag(
        targetType: AnalyticsTargetType.screen,
        target: CardsAnalyticsTarget.refresh_cards,
      );

  void clickSettings() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CardsAnalyticsTarget.settings_menu,
      );

  void clickChangeSpendingLimit() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CardsAnalyticsTarget.change_spending_limit,
      );

  void clickCreateNewCard() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CardsAnalyticsTarget.create_new_card,
      );

  void clickViewCardDetails() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CardsAnalyticsTarget.card_details_menu,
      );

  void clickUnlockCard() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CardsAnalyticsTarget.unlock_card,
      );

  void clickAddToWallet() {
    _analyticsEventTracker.click(
      targetType: AnalyticsTargetType.button,
      target: Platform.isIOS
          ? CardsAnalyticsTarget.add_to_apple_wallet
          : CardsAnalyticsTarget.add_to_google_wallet,
    );
  }

  void clickOrderPhysicalCard() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CardsAnalyticsTarget.order_physical_card,
      );

  void clickSeeAllTransactions() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CardsAnalyticsTarget.see_all_transactions,
      );

  void cardsDashboardOpened() {
    _airshipTracker.view(
      targetType: AnalyticsTargetType.screen,
      target: CardsAnalyticsTarget.dashboard,
    );
  }

  void clickSharedCardBanner() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CardsAnalyticsTarget.convert_to_shared,
      );

  void clickViewSharedCard() => _analyticsEventTracker.click(
        targetType: AnalyticsTargetType.button,
        target: CardsAnalyticsTarget.view_shared_card_info,
      );

  void selectNewCardType(CardType cardType) => _analyticsEventTracker.select(
        targetType: AnalyticsTargetType.card,
        target: CardsAnalyticsTarget.create_new_card_option,
        payload: _CardsAnalyticsPayloadForNewCardCreation(type: cardType),
      );

  void selectSharedCardAction(SharedCardInfoResult action) =>
      _analyticsEventTracker.select(
        targetType: AnalyticsTargetType.button,
        target: CardsAnalyticsTarget.shared_card_action,
        payload: _CardsAnalyticsPayloadForSharedCard(action: action),
      );
}

class _CardsAnalyticsPayloadForNewCardCreation
    implements AnalyticsEventPayload {
  final CardType type;

  const _CardsAnalyticsPayloadForNewCardCreation({required this.type});

  @override
  Map<String, String> getEventPayload() => {'cardType': type.name};
}

class _CardsAnalyticsPayloadForSharedCard implements AnalyticsEventPayload {
  final SharedCardInfoResult action;

  const _CardsAnalyticsPayloadForSharedCard({required this.action});

  @override
  Map<String, String> getEventPayload() => {'nextAction': action.name};
}
