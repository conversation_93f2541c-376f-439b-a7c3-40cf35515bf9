import 'dart:async';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:di/di.dart';
import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:feature_cards_ui/l10n/cards_localization.g.dart';
import 'package:feature_cards_ui/src/common/models/card_account_type.dart';
import 'package:feature_cards_ui/src/common/widgets/animated_bank_card.dart';
import 'package:feature_cards_ui/src/common/widgets/card_skin_selector.dart';
import 'package:feature_cards_ui/src/common/widgets/card_theme_provider.dart';
import 'package:feature_cards_ui/src/common/widgets/content_slider.dart';
import 'package:feature_cards_ui/src/common/widgets/image_gallery_title.dart';
import 'package:feature_cards_ui/src/screens/create_shared_card/flow/create_shared_card_flow_cubit.dart';
import 'package:feature_cards_ui/src/screens/create_shared_card/flow/create_shared_card_flow_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

const _contentPadding = 24.0;

enum SetUpSharedCardStage {
  selectName,
  selectSkin,
}

class SetUpSharedCardPage extends StatefulWidget {
  const SetUpSharedCardPage({super.key});

  @override
  State<SetUpSharedCardPage> createState() => _SetUpSharedCardPageState();
}

class _SetUpSharedCardPageState extends State<SetUpSharedCardPage> {
  bool get _isCardContextFaqEnabled =>
      DependencyProvider.get<FeatureToggleProvider>().get(
        CardsFeatureToggles.isCardContextFaqEnabled,
      );

  final _currentStage =
      ValueNotifier<SetUpSharedCardStage>(SetUpSharedCardStage.selectName);

  bool get _isInFirstStage =>
      _currentStage.value == SetUpSharedCardStage.selectName;

  final _nameFocusNode = FocusNode();
  var _userName = '';

  @override
  void initState() {
    const delayForOpeningKeyboard = Duration(milliseconds: 200);
    _nameFocusNode.addListener(_onFocusChange);

    Future.delayed(
      AnimatedBankCard.navigationTransitionDuration +
          AnimatedBankCard.bankCardAnimationDuration +
          delayForOpeningKeyboard,
      _onTapBankCard,
    );
    super.initState();
  }

  @override
  void dispose() {
    _nameFocusNode.dispose();
    _currentStage.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final stages = [
      SetUpSharedCardStage.selectName,
      SetUpSharedCardStage.selectSkin,
    ];

    final localizations = CardsLocalizations.of(context);
    final cubit = context.read<CreateSharedCardFlowCubit>();
    final state = context.watch<CreateSharedCardFlowCubit>().state;
    const buttonSize = ButtonSize.medium;

    final selectedAccountType =
        state.selectedAccount?.accountType == CardAccountType.loan
            ? WioBankCardAccountType.credit(
                title: localizations.virtualCardIntroScreenWioCredit,
              )
            : const WioBankCardAccountType.myMoney();

    return ClipRRect(
      child: Scaffold(
        backgroundColor: context.colorStyling.background1,
        appBar: TopNavigation(
          TopNavigationModel(
            state: TopNavigationState.positive,
            title: localizations.newSharedCardAppBarTitle,
            rightAccessory: _isCardContextFaqEnabled
                ? TopNavigationIconRightAccessoryModel(
                    icon: const GraphicAssetPointer.icon(
                      CompanyIconPointer.help,
                    ),
                  )
                : null,
          ),
          onRightIconPressed: () =>
              _isCardContextFaqEnabled ? cubit.onHelpButtonPressed() : null,
        ),
        body: PopScope(
          canPop: _canPop(state),
          // ignore: deprecated_member_use
          onPopInvoked: (didPop) => _onPopInvoked(didPop, state),
          child: IgnorePointer(
            ignoring: state.isCreating,
            child: ValueListenableBuilder<SetUpSharedCardStage>(
              valueListenable: _currentStage,
              builder: (context, currentStage, _) {
                final stageIndex = stages.indexOf(currentStage);
                // Per figma there should be no label when entering the name
                final cardLabel = switch (currentStage) {
                  SetUpSharedCardStage.selectSkin =>
                    localizations.newSharedCardSharedTag,
                  _ => null,
                };

                return FixedButtonsPageLayout(
                  contentPadding: EdgeInsets.zero,
                  model: FixedButtonsScrollablePageLayoutModel(
                    buttonAlignmentMode:
                        FixedButtonScrollablePageLayoutButtonAlignmentMode
                            .aboveContent,
                    primaryButton: FixedButtonsScrollablePageLayoutButton(
                      label: localizations.newSharedCardNextButtonTitle,
                      isLoading: state.isCreating,
                      size: buttonSize,
                    ),
                  ),
                  onPrimaryButtonPressed: _canSubmit(state) ? _onSubmit : null,
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const SizedBox(height: _contentPadding),
                        ContentSlider(
                          enableTaptic: true,
                          index: stageIndex,
                          children: [
                            PageText(
                              PageTextModel(
                                title: localizations
                                    .newSharedCardNameSelectionPageTitle,
                              ),
                            ),
                            PageText(
                              PageTextModel(
                                title: localizations.cardSkinSelectionPageTitle,
                                hasRightMargin: false,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24.0),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: _contentPadding,
                          ),
                          child: AnimatedBankCard(
                            child: BankCard(
                              model: BankCardsModel.wio(
                                theme: state.selectedSkin?.cardThemeOf(context),
                                cardType: WioBankCardType.shared(
                                  shortTitle: cardLabel,
                                  nameInputModel: CardNameInputModel(
                                    hint: localizations
                                        .newVirtualCardScreenNameHint,
                                    nameInputErrorMessage:
                                        _mapCardCreationErrorToText(
                                      context,
                                      state.failure,
                                    ),
                                  ),
                                ),
                                accountType: selectedAccountType,
                              ),
                              onChangeNameInput: _onChangeNameInput,
                              onTap: _onTapBankCard,
                              focusNode: _nameFocusNode,
                            ),
                          ),
                        ),
                        ContentSlider(
                          index: stageIndex,
                          children: [
                            const SizedBox.shrink(),
                            _CardSkinSelectionWidget(
                              galleries: state.galleries ?? [],
                              selectedSkin: state.selectedSkin,
                              onSelect: _onSelectSkin,
                            ),
                          ],
                        ),
                        SizedBox(
                          height: _contentPadding +
                              Button.getButtonHeight(buttonSize),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  bool _isValidName(NewCardCreationException? failure) {
    final notValid = failure != null || _userName.trim().isEmpty;

    return !notValid;
  }

  bool _canSubmit(CreateSharedCardFlowState state) =>
      switch (_currentStage.value) {
        SetUpSharedCardStage.selectName => _isValidName(state.failure),
        SetUpSharedCardStage.selectSkin => true,
      };

  bool _isInNameInputMode(NewCardCreationException? failure) =>
      _nameFocusNode.hasFocus || !_isValidName(failure);

  bool _canPop(CreateSharedCardFlowState state) {
    // To block back button when creating
    if (state.isCreating) return false;

    return _isInFirstStage;
  }

  void _onPopInvoked(bool didPop, CreateSharedCardFlowState state) {
    if (!didPop && _currentStage.value == SetUpSharedCardStage.selectSkin) {
      _currentStage.value = SetUpSharedCardStage.selectName;
      context.read<CreateSharedCardFlowCubit>().onBackToInputScreen();
      _nameFocusNode.requestFocus();
    }
  }

  void _onTapBankCard() {
    if (!mounted) return;
    if (!_nameFocusNode.hasFocus) {
      _nameFocusNode.requestFocus();
    }
  }

  void _onChangeNameInput(String name) {
    setState(() => _userName = name);
    context.read<CreateSharedCardFlowCubit>().onInputChange(name);
  }

  void _onSelectSkin(CardImage skin) =>
      context.read<CreateSharedCardFlowCubit>().onSelectSkin(skin);

  void _onSubmit() {
    final cubit = context.read<CreateSharedCardFlowCubit>();
    final state = cubit.state;

    if (_currentStage.value == SetUpSharedCardStage.selectName &&
        _isInNameInputMode(state.failure)) {
      cubit.onFocusInput();
      _nameFocusNode.unfocus();
    } else if (_currentStage.value == SetUpSharedCardStage.selectSkin) {
      cubit.onSetUpSharedCardCompleted();
    }
  }

  String? _mapCardCreationErrorToText(
    BuildContext context,
    NewCardCreationException? reasons,
  ) {
    final localizations = CardsLocalizations.of(context);

    return reasons?.mapOrNull(
      alreadyExists: (_) => localizations.newVirtualCardScreenErrorNameExists,
      longName: (_) => localizations.newVirtualCardScreenErrorNameTooLong,
      general: (_) => localizations.newVirtualCardScreenErrorGeneral,
    );
  }

  void _onFocusChange() {
    final cubit = context.read<CreateSharedCardFlowCubit>();

    if (_nameFocusNode.hasFocus) {
      _currentStage.value = SetUpSharedCardStage.selectName;
    } else if (!_nameFocusNode.hasFocus &&
        !_isInNameInputMode(cubit.state.failure)) {
      cubit.checkSharedCardName(_userName);

      // Move to next stage
      _currentStage.value = SetUpSharedCardStage.selectSkin;
    }

    setState(() {});
  }
}

class _CardSkinSelectionWidget extends StatelessWidget {
  final List<CardImageGallery> galleries;
  final CardImage? selectedSkin;
  final ValueSetter<CardImage>? onSelect;

  const _CardSkinSelectionWidget({
    required this.galleries,
    this.selectedSkin,
    this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = CardsLocalizations.of(context);

    return CustomScrollView(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      slivers: [
        const SliverPadding(padding: EdgeInsets.only(top: _contentPadding)),
        for (final gallery in galleries) ...[
          ImageGalleryTitle(
            title: gallery.map(
              (it) => it.title,
              ungrouped: (_) => l10n.cardSkinColorSelectionSectionTitle,
            ),
          ),
          CardSkinSelector(
            skins: gallery.images,
            selectedSkin: selectedSkin,
            onSelect: onSelect,
          ),
          SliverPadding(padding: EdgeInsets.only(top: Spacing.s5.value)),
        ],
      ],
    );
  }
}
