import 'package:domain/domain.dart';
import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:feature_cards_ui/l10n/cards_localization.g.dart';
import 'package:feature_cards_ui/src/common/extensions.dart';
import 'package:feature_cards_ui/src/navigation/card_details_bottom_sheet_navigation_config.dart';
import 'package:feature_cards_ui/src/navigation/card_wallet_intro_screen_navigation_config.dart';
import 'package:feature_cards_ui/src/navigation/pin_code_flow_navigation_config.dart';
import 'package:feature_cards_ui/src/screens/cards/card_view_model.dart';
import 'package:feature_cards_ui/src/screens/cards/card_wallet_activated_screen.dart';
import 'package:feature_cards_ui/src/screens/cards/cubit/delegates/card_wallet_delegate.dart';
import 'package:feature_cards_ui/src/screens/manage_card/analytics/manage_card_analytics.dart';
import 'package:feature_cards_ui/src/screens/manage_card/cubit/manage_card_state.dart';
import 'package:flutter/material.dart' hide Card;
import 'package:logging_api/logging.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_feature_card_ui/wio_common_feature_card_ui.dart';
import 'package:wio_common_feature_transaction_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_error_domain_api/handlers/common_error_handler.dart';
import 'package:wio_feature_faq_api/faq_api.dart';
import 'package:wio_wallet_api/wallet_api.dart';

class ManageCardCubit extends BaseCubit<ManageCardState> {
  final CardInteractor _cardInteractor;
  final CardsFlow _cardsFlow;
  final CardWalletDelegate _walletDelegate;
  final NavigationProvider _navigationProvider;
  final ToastMessageProvider _toastMessageProvider;
  final ManageCardAnalytics _analytics;
  final CardsLocalizations _localizations;
  final CommonErrorHandler _commonErrorHandler;
  final Logger _logger;
  final ManageCardParams _params;

  final _refreshStream = PublishSubject<void>();

  ManageCardCubit({
    required CardInteractor cardInteractor,
    required CardsFlow cardsFlow,
    required CardWalletDelegate walletDelegate,
    required NavigationProvider navigationProvider,
    required ToastMessageProvider toastMessageProvider,
    required ManageCardAnalytics analytics,
    required CardsLocalizations localizations,
    required CommonErrorHandler commonErrorHandler,
    required Logger logger,
    required ManageCardParams params,
  })  : _cardInteractor = cardInteractor,
        _cardsFlow = cardsFlow,
        _walletDelegate = walletDelegate,
        _navigationProvider = navigationProvider,
        _toastMessageProvider = toastMessageProvider,
        _analytics = analytics,
        _localizations = localizations,
        _commonErrorHandler = commonErrorHandler,
        _logger = logger,
        _params = params,
        super(const ManageCardState.initial());

  /// The stream to notify listeners that there was a refresh in cards
  Stream<void> get refreshStream => _refreshStream.stream;

  void initialize() {
    _init();

    _listenForCardUpdates();
  }

  void onRetry() {
    state.mapOrNull(failed: (_) => _init());
  }

  Future<void> onRefresh() => state.maybeMap(
        idle: (_) => _init(isForRefresh: true),
        orElse: () async {},
      );

  void onAddToWallet() {
    state.mapOrNull(
      idle: (it) {
        _analytics.addToWallet();

        _getCardMpan(it.card)
            .toStream()
            .withLoading(this)
            .switchMap(
              (newMpan) => _walletDelegate
                  .addCardToWallet(it.card.copyWith(mpan: newMpan)),
            )
            .where((_) => !isClosed)
            .withError<Object?>(_commonErrorHandler.handleError)
            .where(
              (availability) =>
                  availability ==
                  WalletAvailabilityResult.unavailableAlreadyAdded,
            )
            .doOnData(
              (_) => emit(
                it.copyWith(
                  addWalletAvailable: false,
                  isAlreadyInWallets: true,
                ),
              ),
            )
            .doOnData((_) => _onAddToWalletSuccess(it.card))
            .complete();
      },
    );
  }

  void onClickSettings() {
    state.mapOrNull(
      idle: (it) {
        _analytics.viewSettings();
        _navigationProvider.push(
          CardSettingsScreenNavigationConfig(
            param: CardSettingsScreenParam.byCard(card: it.card),
          ),
        );
      },
    );
  }

  void onEditSpendingLimit() {
    state.mapOrNull(
      idle: (it) {
        _analytics.editSpendingLimit();
        _cardsFlow.onEditSpendingLimit(it.card);
      },
    );
  }

  void onViewCardDetails() {
    state.mapOrNull(
      idle: (it) {
        _analytics.viewCardDetails();
        _navigationProvider
            .showBottomSheet(
              CardDetailsBottomSheetNavigationConfig(cardId: it.card.id),
            )
            .ignore();
      },
    );
  }

  void onFreezeCard() {
    state.mapOrNull(
      idle: (it) {
        final cardId = it.card.id;

        _analytics.clickFreezeCard();
        _executeCardOperation(
          operation: _cardInteractor.freezeCard(cardId),
          uiState: CardUiState.freezing,
          successMessage: _localizations.cardsCardBlockedSuccessMessage,
          doOnData: () => emit(
            it.copyWith(
              card: it.card.copyWith(status: CardStatus.freeze),
              cardUiState: CardUiState.idle,
            ),
          ),
        );
      },
    );
  }

  void onUnfreezeCard() {
    state.mapOrNull(
      idle: (it) {
        final cardId = it.card.id;

        _analytics.clickUnfreezeCard();
        _executeCardOperation(
          operation: _cardInteractor.unfreezeCard(cardId),
          uiState: CardUiState.unfreezing,
          successMessage: _localizations.cardsCardUnblockedSuccessMessage,
          doOnData: () => emit(
            it.copyWith(
              card: it.card.copyWith(status: CardStatus.unfreeze),
              cardUiState: CardUiState.idle,
            ),
          ),
        );
      },
    );
  }

  Future<void> onUnblockCard() async {
    state.mapOrNull(
      idle: (it) {
        final cardId = it.card.id;
        final lastDigits = it.card.lastDigits;

        _analytics.clickUnblockCard();

        if (it.card.status == CardStatus.blockedBySupport) {
          return _openCustomerSupport();
        }

        _executeCardOperation(
          operation: _cardInteractor.unfreezeCard(cardId),
          uiState: CardUiState.unlocking,
          successMessage: _localizations.cardsUnlockedCardMessage(lastDigits),
          doOnData: () => _setUpPin(cardId, PinCodeFlowType.setPin),
        );
      },
    );
  }

  void onSeeAllTransactionsPressed() {
    state.mapOrNull(
      idle: (it) {
        _analytics.clickSeeAllTransactions();
        _navigationProvider.navigateTo(
          TransactionsFeatureNavigationConfig(
            destination: AllTransactionsScreenNavigationConfig(
              pageType: TransactionsPageType.retail,
              filterByItems: TransactionFilterByItems.card(token: it.card.id),
              filterParams: const TransactionFilterModel(
                selectFiltersState: [
                  SelectFilterDateRangeState(
                    rangeState: TransactionDateRangeState.empty(),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Future<void> close() {
    _refreshStream.close();

    return super.close();
  }

  // Private

  Future<void> _init({bool isForRefresh = false}) {
    return _getCardStream(isForRefresh: isForRefresh)
        .doOnListen(() {
          if (isForRefresh) {
            emit(state.toRefreshing());
          } else {
            emit(const ManageCardState.loading());
          }
        })
        .where((_) => !isClosed)
        .switchMap(
          (card) => !card.isForMe
              ? Stream.value((card: card, record: WalletCardRecord.none))
              : _walletDelegate
                  .getCardWalletRecord(card)
                  .toStream()
                  .map((record) => (card: card, record: record)),
        )
        .doOnData((result) {
          final card = result.card;
          final record = result.record;

          emit(
            ManageCardState.idle(
              card: card,
              addWalletAvailable: record.canAddToWallet,
              isAlreadyInWallets: record.isAlreadyInWallets,
            ),
          );
        })
        .logError(_logger)
        .withError((_) => safeEmit(const ManageCardState.failed()))
        .complete();
  }

  Stream<Card> _getCardStream({bool isForRefresh = false}) {
    if (isForRefresh) return _cardInteractor.getCard(_params.cardId).toStream();

    return _params.map(
      (it) => Stream.value(it.card),
      byId: (it) => _cardInteractor.getCard(it.cardId).toStream(),
    );
  }

  void _listenForCardUpdates() {
    void mapDataToState(Data<List<Card>> data) {
      final content = data.content;

      if (content != null) {
        loading(false);
        _handleCardsUpdate(content, _params.cardId);
      } else {
        loading(true);
      }
    }

    _cardInteractor
        .getCards()
        .map(mapDataToState)
        .doOnDone(() => loading(false))
        .doOnError((_, __) => loading(false))
        .listenSafe(this);
  }

  void _handleCardsUpdate(List<Card> cards, String cardID) {
    state.mapOrNull(
      idle: (it) {
        _logger.debug('Card where updated');
        final index = cards.indexWhere((e) => e.id == cardID);

        if (index == -1) {
          return _navigationProvider.goBack();
        } else {
          _logger.debug('Here is the updated card: ${cards[index]}');

          emit(it.copyWith(card: cards[index]));
        }
      },
    );
  }

  // TODO(ssuleymanli): Move wallet related logic to common

  // If card is postponed, it send get cardDetails request,
  // after that refreshes the cards list.
  // Therefore returns new last digits of card (replaces ****).
  Future<String> _getCardMpan(Card card) async {
    final cardId = card.id;

    if (await _cardInteractor.isCardPostponed(cardId)) {
      _logger.debug('Card is postponed, so get card details');

      return _cardInteractor
          .getCardDetails(cardId)
          .toStream()
          .asyncMap((event) => _cardInteractor.getCard(cardId))
          .map((card) => card.mpan)
          .first;
    }

    return card.mpan;
  }

  void _onAddToWalletSuccess(Card card) {
    void showSuccess() {
      _toastMessageProvider.showToastMessage(
        NotificationToastMessageConfiguration.success(
          _localizations.cardsScreenActivatedCardPinSetUpDoneMessage,
        ),
      );
    }

    final type = card.type;
    final id = card.id;

    _navigationProvider
        .push(CardWalletIntroScreenNavigationConfig(cardType: type))
        .then((result) {
      if (result is CardWalletActivatedScreenSetUpPinResult) {
        _setUpPin(id, PinCodeFlowType.setPin).then((result) {
          result?.map(success: (_) => showSuccess());
        });
      }
    });
  }

  Future<PinCodeFlowResult?> _setUpPin(
    String cardId,
    PinCodeFlowType flowType,
  ) async {
    final result = await _navigationProvider.push(
      PinCodeFlowNavigationConfig(
        flowType: flowType,
        cardId: cardId,
      ),
    );

    return result as PinCodeFlowResult?;
  }

  void _openCustomerSupport() {
    _navigationProvider.push(const FAQsTopicScreenNavigationConfig());
  }

  Future<void> _executeCardOperation({
    required Future<void> operation,
    required CardUiState uiState,
    String? successMessage,
    VoidCallback? doOnData,
  }) {
    return operation
        .toStream()
        .doOnListen(() => emit(state.updateCardUIState(uiState)))
        .where((_) => !isClosed)
        .doOnError((_, __) => emit(state.updateCardUIState(CardUiState.idle)))
        .withError<Object?>(_commonErrorHandler.handleError)
        .asyncMap((_) {
      _logger.debug('Completed $uiState');
      if (successMessage != null) {
        _toastMessageProvider.showToastMessage(
          NotificationToastMessageConfiguration.success(successMessage),
        );
      }

      doOnData?.call();
    }).complete();
  }

  @override
  String toString() => 'ManageCardCubit{}';
}
