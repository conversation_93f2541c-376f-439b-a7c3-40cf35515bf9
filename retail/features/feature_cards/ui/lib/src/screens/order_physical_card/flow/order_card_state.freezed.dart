// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_card_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OrderCardStage {
  CustomerAddress? get address => throw _privateConstructorUsedError;
  OrderCardStagePinVariant get pinVariant => throw _privateConstructorUsedError;
  OrderCardStageSetNameVariant get setNameVariant =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            CustomerAddress? address,
            OrderCardStagePinVariant pinVariant,
            OrderCardStageSetNameVariant setNameVariant)
        initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            CustomerAddress? address,
            OrderCardStagePinVariant pinVariant,
            OrderCardStageSetNameVariant setNameVariant)?
        initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            CustomerAddress? address,
            OrderCardStagePinVariant pinVariant,
            OrderCardStageSetNameVariant setNameVariant)?
        initial,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderCardStageInitial value) initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderCardStageInitial value)? initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderCardStageInitial value)? initial,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of OrderCardStage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderCardStageCopyWith<OrderCardStage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderCardStageCopyWith<$Res> {
  factory $OrderCardStageCopyWith(
          OrderCardStage value, $Res Function(OrderCardStage) then) =
      _$OrderCardStageCopyWithImpl<$Res, OrderCardStage>;
  @useResult
  $Res call(
      {CustomerAddress? address,
      OrderCardStagePinVariant pinVariant,
      OrderCardStageSetNameVariant setNameVariant});

  $CustomerAddressCopyWith<$Res>? get address;
  $OrderCardStageSetNameVariantCopyWith<$Res> get setNameVariant;
}

/// @nodoc
class _$OrderCardStageCopyWithImpl<$Res, $Val extends OrderCardStage>
    implements $OrderCardStageCopyWith<$Res> {
  _$OrderCardStageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderCardStage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = freezed,
    Object? pinVariant = null,
    Object? setNameVariant = null,
  }) {
    return _then(_value.copyWith(
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as CustomerAddress?,
      pinVariant: null == pinVariant
          ? _value.pinVariant
          : pinVariant // ignore: cast_nullable_to_non_nullable
              as OrderCardStagePinVariant,
      setNameVariant: null == setNameVariant
          ? _value.setNameVariant
          : setNameVariant // ignore: cast_nullable_to_non_nullable
              as OrderCardStageSetNameVariant,
    ) as $Val);
  }

  /// Create a copy of OrderCardStage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomerAddressCopyWith<$Res>? get address {
    if (_value.address == null) {
      return null;
    }

    return $CustomerAddressCopyWith<$Res>(_value.address!, (value) {
      return _then(_value.copyWith(address: value) as $Val);
    });
  }

  /// Create a copy of OrderCardStage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrderCardStageSetNameVariantCopyWith<$Res> get setNameVariant {
    return $OrderCardStageSetNameVariantCopyWith<$Res>(_value.setNameVariant,
        (value) {
      return _then(_value.copyWith(setNameVariant: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OrderCardStageInitialImplCopyWith<$Res>
    implements $OrderCardStageCopyWith<$Res> {
  factory _$$OrderCardStageInitialImplCopyWith(
          _$OrderCardStageInitialImpl value,
          $Res Function(_$OrderCardStageInitialImpl) then) =
      __$$OrderCardStageInitialImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CustomerAddress? address,
      OrderCardStagePinVariant pinVariant,
      OrderCardStageSetNameVariant setNameVariant});

  @override
  $CustomerAddressCopyWith<$Res>? get address;
  @override
  $OrderCardStageSetNameVariantCopyWith<$Res> get setNameVariant;
}

/// @nodoc
class __$$OrderCardStageInitialImplCopyWithImpl<$Res>
    extends _$OrderCardStageCopyWithImpl<$Res, _$OrderCardStageInitialImpl>
    implements _$$OrderCardStageInitialImplCopyWith<$Res> {
  __$$OrderCardStageInitialImplCopyWithImpl(_$OrderCardStageInitialImpl _value,
      $Res Function(_$OrderCardStageInitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderCardStage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = freezed,
    Object? pinVariant = null,
    Object? setNameVariant = null,
  }) {
    return _then(_$OrderCardStageInitialImpl(
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as CustomerAddress?,
      pinVariant: null == pinVariant
          ? _value.pinVariant
          : pinVariant // ignore: cast_nullable_to_non_nullable
              as OrderCardStagePinVariant,
      setNameVariant: null == setNameVariant
          ? _value.setNameVariant
          : setNameVariant // ignore: cast_nullable_to_non_nullable
              as OrderCardStageSetNameVariant,
    ));
  }
}

/// @nodoc

class _$OrderCardStageInitialImpl implements OrderCardStageInitial {
  const _$OrderCardStageInitialImpl(
      {this.address,
      this.pinVariant = OrderCardStagePinVariant.none,
      this.setNameVariant = const OrderCardStageSetNameVariant.none()});

  @override
  final CustomerAddress? address;
  @override
  @JsonKey()
  final OrderCardStagePinVariant pinVariant;
  @override
  @JsonKey()
  final OrderCardStageSetNameVariant setNameVariant;

  @override
  String toString() {
    return 'OrderCardStage.initial(address: $address, pinVariant: $pinVariant, setNameVariant: $setNameVariant)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderCardStageInitialImpl &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.pinVariant, pinVariant) ||
                other.pinVariant == pinVariant) &&
            (identical(other.setNameVariant, setNameVariant) ||
                other.setNameVariant == setNameVariant));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, address, pinVariant, setNameVariant);

  /// Create a copy of OrderCardStage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderCardStageInitialImplCopyWith<_$OrderCardStageInitialImpl>
      get copyWith => __$$OrderCardStageInitialImplCopyWithImpl<
          _$OrderCardStageInitialImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            CustomerAddress? address,
            OrderCardStagePinVariant pinVariant,
            OrderCardStageSetNameVariant setNameVariant)
        initial,
  }) {
    return initial(address, pinVariant, setNameVariant);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            CustomerAddress? address,
            OrderCardStagePinVariant pinVariant,
            OrderCardStageSetNameVariant setNameVariant)?
        initial,
  }) {
    return initial?.call(address, pinVariant, setNameVariant);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            CustomerAddress? address,
            OrderCardStagePinVariant pinVariant,
            OrderCardStageSetNameVariant setNameVariant)?
        initial,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(address, pinVariant, setNameVariant);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(OrderCardStageInitial value) initial,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(OrderCardStageInitial value)? initial,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(OrderCardStageInitial value)? initial,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class OrderCardStageInitial implements OrderCardStage {
  const factory OrderCardStageInitial(
          {final CustomerAddress? address,
          final OrderCardStagePinVariant pinVariant,
          final OrderCardStageSetNameVariant setNameVariant}) =
      _$OrderCardStageInitialImpl;

  @override
  CustomerAddress? get address;
  @override
  OrderCardStagePinVariant get pinVariant;
  @override
  OrderCardStageSetNameVariant get setNameVariant;

  /// Create a copy of OrderCardStage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderCardStageInitialImplCopyWith<_$OrderCardStageInitialImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$OrderCardState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(
            OrderCardStage stage, CreationFees? cardCreationFees)
        idle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(OrderCardStage stage, CreationFees? cardCreationFees)?
        idle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(OrderCardStage stage, CreationFees? cardCreationFees)?
        idle,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_OrderCardLoadingState value) loading,
    required TResult Function(_OrderCardStateIdle value) idle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_OrderCardLoadingState value)? loading,
    TResult? Function(_OrderCardStateIdle value)? idle,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_OrderCardLoadingState value)? loading,
    TResult Function(_OrderCardStateIdle value)? idle,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderCardStateCopyWith<$Res> {
  factory $OrderCardStateCopyWith(
          OrderCardState value, $Res Function(OrderCardState) then) =
      _$OrderCardStateCopyWithImpl<$Res, OrderCardState>;
}

/// @nodoc
class _$OrderCardStateCopyWithImpl<$Res, $Val extends OrderCardState>
    implements $OrderCardStateCopyWith<$Res> {
  _$OrderCardStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderCardState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$OrderCardLoadingStateImplCopyWith<$Res> {
  factory _$$OrderCardLoadingStateImplCopyWith(
          _$OrderCardLoadingStateImpl value,
          $Res Function(_$OrderCardLoadingStateImpl) then) =
      __$$OrderCardLoadingStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OrderCardLoadingStateImplCopyWithImpl<$Res>
    extends _$OrderCardStateCopyWithImpl<$Res, _$OrderCardLoadingStateImpl>
    implements _$$OrderCardLoadingStateImplCopyWith<$Res> {
  __$$OrderCardLoadingStateImplCopyWithImpl(_$OrderCardLoadingStateImpl _value,
      $Res Function(_$OrderCardLoadingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderCardState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$OrderCardLoadingStateImpl implements _OrderCardLoadingState {
  const _$OrderCardLoadingStateImpl();

  @override
  String toString() {
    return 'OrderCardState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderCardLoadingStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(
            OrderCardStage stage, CreationFees? cardCreationFees)
        idle,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(OrderCardStage stage, CreationFees? cardCreationFees)?
        idle,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(OrderCardStage stage, CreationFees? cardCreationFees)?
        idle,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_OrderCardLoadingState value) loading,
    required TResult Function(_OrderCardStateIdle value) idle,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_OrderCardLoadingState value)? loading,
    TResult? Function(_OrderCardStateIdle value)? idle,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_OrderCardLoadingState value)? loading,
    TResult Function(_OrderCardStateIdle value)? idle,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _OrderCardLoadingState implements OrderCardState {
  const factory _OrderCardLoadingState() = _$OrderCardLoadingStateImpl;
}

/// @nodoc
abstract class _$$OrderCardStateIdleImplCopyWith<$Res> {
  factory _$$OrderCardStateIdleImplCopyWith(_$OrderCardStateIdleImpl value,
          $Res Function(_$OrderCardStateIdleImpl) then) =
      __$$OrderCardStateIdleImplCopyWithImpl<$Res>;
  @useResult
  $Res call({OrderCardStage stage, CreationFees? cardCreationFees});

  $OrderCardStageCopyWith<$Res> get stage;
  $CreationFeesCopyWith<$Res>? get cardCreationFees;
}

/// @nodoc
class __$$OrderCardStateIdleImplCopyWithImpl<$Res>
    extends _$OrderCardStateCopyWithImpl<$Res, _$OrderCardStateIdleImpl>
    implements _$$OrderCardStateIdleImplCopyWith<$Res> {
  __$$OrderCardStateIdleImplCopyWithImpl(_$OrderCardStateIdleImpl _value,
      $Res Function(_$OrderCardStateIdleImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderCardState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? stage = null,
    Object? cardCreationFees = freezed,
  }) {
    return _then(_$OrderCardStateIdleImpl(
      stage: null == stage
          ? _value.stage
          : stage // ignore: cast_nullable_to_non_nullable
              as OrderCardStage,
      cardCreationFees: freezed == cardCreationFees
          ? _value.cardCreationFees
          : cardCreationFees // ignore: cast_nullable_to_non_nullable
              as CreationFees?,
    ));
  }

  /// Create a copy of OrderCardState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OrderCardStageCopyWith<$Res> get stage {
    return $OrderCardStageCopyWith<$Res>(_value.stage, (value) {
      return _then(_value.copyWith(stage: value));
    });
  }

  /// Create a copy of OrderCardState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CreationFeesCopyWith<$Res>? get cardCreationFees {
    if (_value.cardCreationFees == null) {
      return null;
    }

    return $CreationFeesCopyWith<$Res>(_value.cardCreationFees!, (value) {
      return _then(_value.copyWith(cardCreationFees: value));
    });
  }
}

/// @nodoc

class _$OrderCardStateIdleImpl implements _OrderCardStateIdle {
  const _$OrderCardStateIdleImpl(
      {this.stage = const OrderCardStage.initial(), this.cardCreationFees});

  @override
  @JsonKey()
  final OrderCardStage stage;
  @override
  final CreationFees? cardCreationFees;

  @override
  String toString() {
    return 'OrderCardState.idle(stage: $stage, cardCreationFees: $cardCreationFees)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderCardStateIdleImpl &&
            (identical(other.stage, stage) || other.stage == stage) &&
            (identical(other.cardCreationFees, cardCreationFees) ||
                other.cardCreationFees == cardCreationFees));
  }

  @override
  int get hashCode => Object.hash(runtimeType, stage, cardCreationFees);

  /// Create a copy of OrderCardState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderCardStateIdleImplCopyWith<_$OrderCardStateIdleImpl> get copyWith =>
      __$$OrderCardStateIdleImplCopyWithImpl<_$OrderCardStateIdleImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(
            OrderCardStage stage, CreationFees? cardCreationFees)
        idle,
  }) {
    return idle(stage, cardCreationFees);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(OrderCardStage stage, CreationFees? cardCreationFees)?
        idle,
  }) {
    return idle?.call(stage, cardCreationFees);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(OrderCardStage stage, CreationFees? cardCreationFees)?
        idle,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(stage, cardCreationFees);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_OrderCardLoadingState value) loading,
    required TResult Function(_OrderCardStateIdle value) idle,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_OrderCardLoadingState value)? loading,
    TResult? Function(_OrderCardStateIdle value)? idle,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_OrderCardLoadingState value)? loading,
    TResult Function(_OrderCardStateIdle value)? idle,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class _OrderCardStateIdle implements OrderCardState {
  const factory _OrderCardStateIdle(
      {final OrderCardStage stage,
      final CreationFees? cardCreationFees}) = _$OrderCardStateIdleImpl;

  OrderCardStage get stage;
  CreationFees? get cardCreationFees;

  /// Create a copy of OrderCardState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderCardStateIdleImplCopyWith<_$OrderCardStateIdleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
