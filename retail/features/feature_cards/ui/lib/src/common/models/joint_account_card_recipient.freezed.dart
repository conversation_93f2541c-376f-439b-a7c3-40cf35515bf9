// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'joint_account_card_recipient.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$JointAccountCardRecipient {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String customerId, String name, String phoneNumber)
        myself,
    required TResult Function(AccountOwner coOwner) coOwner,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String customerId, String name, String phoneNumber)?
        myself,
    TResult? Function(AccountOwner coOwner)? coOwner,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String customerId, String name, String phoneNumber)?
        myself,
    TResult Function(AccountOwner coOwner)? coOwner,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MyselfAsJointAccountCardRecipient value) myself,
    required TResult Function(_CoOwnerAsJointAccountCardRecipient value)
        coOwner,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MyselfAsJointAccountCardRecipient value)? myself,
    TResult? Function(_CoOwnerAsJointAccountCardRecipient value)? coOwner,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MyselfAsJointAccountCardRecipient value)? myself,
    TResult Function(_CoOwnerAsJointAccountCardRecipient value)? coOwner,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JointAccountCardRecipientCopyWith<$Res> {
  factory $JointAccountCardRecipientCopyWith(JointAccountCardRecipient value,
          $Res Function(JointAccountCardRecipient) then) =
      _$JointAccountCardRecipientCopyWithImpl<$Res, JointAccountCardRecipient>;
}

/// @nodoc
class _$JointAccountCardRecipientCopyWithImpl<$Res,
        $Val extends JointAccountCardRecipient>
    implements $JointAccountCardRecipientCopyWith<$Res> {
  _$JointAccountCardRecipientCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JointAccountCardRecipient
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$MyselfAsJointAccountCardRecipientImplCopyWith<$Res> {
  factory _$$MyselfAsJointAccountCardRecipientImplCopyWith(
          _$MyselfAsJointAccountCardRecipientImpl value,
          $Res Function(_$MyselfAsJointAccountCardRecipientImpl) then) =
      __$$MyselfAsJointAccountCardRecipientImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String customerId, String name, String phoneNumber});
}

/// @nodoc
class __$$MyselfAsJointAccountCardRecipientImplCopyWithImpl<$Res>
    extends _$JointAccountCardRecipientCopyWithImpl<$Res,
        _$MyselfAsJointAccountCardRecipientImpl>
    implements _$$MyselfAsJointAccountCardRecipientImplCopyWith<$Res> {
  __$$MyselfAsJointAccountCardRecipientImplCopyWithImpl(
      _$MyselfAsJointAccountCardRecipientImpl _value,
      $Res Function(_$MyselfAsJointAccountCardRecipientImpl) _then)
      : super(_value, _then);

  /// Create a copy of JointAccountCardRecipient
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerId = null,
    Object? name = null,
    Object? phoneNumber = null,
  }) {
    return _then(_$MyselfAsJointAccountCardRecipientImpl(
      customerId: null == customerId
          ? _value.customerId
          : customerId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$MyselfAsJointAccountCardRecipientImpl
    extends _MyselfAsJointAccountCardRecipient {
  const _$MyselfAsJointAccountCardRecipientImpl(
      {required this.customerId, required this.name, required this.phoneNumber})
      : super._();

  @override
  final String customerId;
  @override
  final String name;
  @override
  final String phoneNumber;

  @override
  String toString() {
    return 'JointAccountCardRecipient.myself(customerId: $customerId, name: $name, phoneNumber: $phoneNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MyselfAsJointAccountCardRecipientImpl &&
            (identical(other.customerId, customerId) ||
                other.customerId == customerId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, customerId, name, phoneNumber);

  /// Create a copy of JointAccountCardRecipient
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MyselfAsJointAccountCardRecipientImplCopyWith<
          _$MyselfAsJointAccountCardRecipientImpl>
      get copyWith => __$$MyselfAsJointAccountCardRecipientImplCopyWithImpl<
          _$MyselfAsJointAccountCardRecipientImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String customerId, String name, String phoneNumber)
        myself,
    required TResult Function(AccountOwner coOwner) coOwner,
  }) {
    return myself(customerId, name, phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String customerId, String name, String phoneNumber)?
        myself,
    TResult? Function(AccountOwner coOwner)? coOwner,
  }) {
    return myself?.call(customerId, name, phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String customerId, String name, String phoneNumber)?
        myself,
    TResult Function(AccountOwner coOwner)? coOwner,
    required TResult orElse(),
  }) {
    if (myself != null) {
      return myself(customerId, name, phoneNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MyselfAsJointAccountCardRecipient value) myself,
    required TResult Function(_CoOwnerAsJointAccountCardRecipient value)
        coOwner,
  }) {
    return myself(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MyselfAsJointAccountCardRecipient value)? myself,
    TResult? Function(_CoOwnerAsJointAccountCardRecipient value)? coOwner,
  }) {
    return myself?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MyselfAsJointAccountCardRecipient value)? myself,
    TResult Function(_CoOwnerAsJointAccountCardRecipient value)? coOwner,
    required TResult orElse(),
  }) {
    if (myself != null) {
      return myself(this);
    }
    return orElse();
  }
}

abstract class _MyselfAsJointAccountCardRecipient
    extends JointAccountCardRecipient {
  const factory _MyselfAsJointAccountCardRecipient(
          {required final String customerId,
          required final String name,
          required final String phoneNumber}) =
      _$MyselfAsJointAccountCardRecipientImpl;
  const _MyselfAsJointAccountCardRecipient._() : super._();

  String get customerId;
  String get name;
  String get phoneNumber;

  /// Create a copy of JointAccountCardRecipient
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MyselfAsJointAccountCardRecipientImplCopyWith<
          _$MyselfAsJointAccountCardRecipientImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CoOwnerAsJointAccountCardRecipientImplCopyWith<$Res> {
  factory _$$CoOwnerAsJointAccountCardRecipientImplCopyWith(
          _$CoOwnerAsJointAccountCardRecipientImpl value,
          $Res Function(_$CoOwnerAsJointAccountCardRecipientImpl) then) =
      __$$CoOwnerAsJointAccountCardRecipientImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AccountOwner coOwner});

  $AccountOwnerCopyWith<$Res> get coOwner;
}

/// @nodoc
class __$$CoOwnerAsJointAccountCardRecipientImplCopyWithImpl<$Res>
    extends _$JointAccountCardRecipientCopyWithImpl<$Res,
        _$CoOwnerAsJointAccountCardRecipientImpl>
    implements _$$CoOwnerAsJointAccountCardRecipientImplCopyWith<$Res> {
  __$$CoOwnerAsJointAccountCardRecipientImplCopyWithImpl(
      _$CoOwnerAsJointAccountCardRecipientImpl _value,
      $Res Function(_$CoOwnerAsJointAccountCardRecipientImpl) _then)
      : super(_value, _then);

  /// Create a copy of JointAccountCardRecipient
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? coOwner = null,
  }) {
    return _then(_$CoOwnerAsJointAccountCardRecipientImpl(
      coOwner: null == coOwner
          ? _value.coOwner
          : coOwner // ignore: cast_nullable_to_non_nullable
              as AccountOwner,
    ));
  }

  /// Create a copy of JointAccountCardRecipient
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountOwnerCopyWith<$Res> get coOwner {
    return $AccountOwnerCopyWith<$Res>(_value.coOwner, (value) {
      return _then(_value.copyWith(coOwner: value));
    });
  }
}

/// @nodoc

class _$CoOwnerAsJointAccountCardRecipientImpl
    extends _CoOwnerAsJointAccountCardRecipient {
  const _$CoOwnerAsJointAccountCardRecipientImpl({required this.coOwner})
      : super._();

  @override
  final AccountOwner coOwner;

  @override
  String toString() {
    return 'JointAccountCardRecipient.coOwner(coOwner: $coOwner)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CoOwnerAsJointAccountCardRecipientImpl &&
            (identical(other.coOwner, coOwner) || other.coOwner == coOwner));
  }

  @override
  int get hashCode => Object.hash(runtimeType, coOwner);

  /// Create a copy of JointAccountCardRecipient
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CoOwnerAsJointAccountCardRecipientImplCopyWith<
          _$CoOwnerAsJointAccountCardRecipientImpl>
      get copyWith => __$$CoOwnerAsJointAccountCardRecipientImplCopyWithImpl<
          _$CoOwnerAsJointAccountCardRecipientImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String customerId, String name, String phoneNumber)
        myself,
    required TResult Function(AccountOwner coOwner) coOwner,
  }) {
    return coOwner(this.coOwner);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String customerId, String name, String phoneNumber)?
        myself,
    TResult? Function(AccountOwner coOwner)? coOwner,
  }) {
    return coOwner?.call(this.coOwner);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String customerId, String name, String phoneNumber)?
        myself,
    TResult Function(AccountOwner coOwner)? coOwner,
    required TResult orElse(),
  }) {
    if (coOwner != null) {
      return coOwner(this.coOwner);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MyselfAsJointAccountCardRecipient value) myself,
    required TResult Function(_CoOwnerAsJointAccountCardRecipient value)
        coOwner,
  }) {
    return coOwner(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MyselfAsJointAccountCardRecipient value)? myself,
    TResult? Function(_CoOwnerAsJointAccountCardRecipient value)? coOwner,
  }) {
    return coOwner?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MyselfAsJointAccountCardRecipient value)? myself,
    TResult Function(_CoOwnerAsJointAccountCardRecipient value)? coOwner,
    required TResult orElse(),
  }) {
    if (coOwner != null) {
      return coOwner(this);
    }
    return orElse();
  }
}

abstract class _CoOwnerAsJointAccountCardRecipient
    extends JointAccountCardRecipient {
  const factory _CoOwnerAsJointAccountCardRecipient(
          {required final AccountOwner coOwner}) =
      _$CoOwnerAsJointAccountCardRecipientImpl;
  const _CoOwnerAsJointAccountCardRecipient._() : super._();

  AccountOwner get coOwner;

  /// Create a copy of JointAccountCardRecipient
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CoOwnerAsJointAccountCardRecipientImplCopyWith<
          _$CoOwnerAsJointAccountCardRecipientImpl>
      get copyWith => throw _privateConstructorUsedError;
}
