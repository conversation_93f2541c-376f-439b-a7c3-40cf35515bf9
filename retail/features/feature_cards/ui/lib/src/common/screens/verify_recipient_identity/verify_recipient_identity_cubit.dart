import 'dart:async';
import 'dart:convert';

import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:feature_cards_ui/l10n/cards_localization.g.dart';
import 'package:feature_cards_ui/src/common/screens/verify_recipient_identity/verification_status_factory.dart';
import 'package:feature_cards_ui/src/common/screens/verify_recipient_identity/verify_recipient_identity_state.dart';
import 'package:flutter/services.dart';
import 'package:logging_api/logging.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ui/cubit/base_cubit.dart';
import 'package:ui/cubit/extensions/bloc_extensions.dart';
import 'package:ui/l10n/common_localizations.g.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_document_reader_api/domain/enum/document_type.dart';
import 'package:wio_feature_document_reader_api/domain/model/document_reader_result.dart';
import 'package:wio_feature_document_reader_api/navigation/document_reader_feature_navigation_config.dart';
import 'package:wio_feature_document_reader_api/navigation/document_reader_scanner_page_navigation_config.dart';
import 'package:wio_feature_error_domain_api/handlers/common_error_handler.dart';
import 'package:wio_feature_liveness_api/domain/liveness_interactor.dart';
import 'package:wio_feature_status_view_api/model/status_page_result.dart';

class IntroConstants {
  static const packageName = 'feature_cards_ui';
  static const introDocumentSampleImage =
      'assets/images/doc_scanner_intro_example.webp';
}

typedef EIDVerification = Future<void> Function({required bool isVerified});

class VerifyRecipientIdentityCubit
    extends BaseCubit<VerifyRecipientIdentityState> {
  static const _documentType = DocumentType.emiratesId;

  final EIDVerification _onVerificationDone;
  final CompanyPermissionResolver _permissionResolver;
  final LivenessInteractor _efrInteractor;
  final CommonErrorHandler _commonErrorHandler;
  final NavigationProvider _navigationProvider;
  final RecipientVerificationStatusFactory _statusFactory;
  final CardsLocalizations _cardsLocalizations;
  final CommonLocalizations _commonLocalizations;
  final DocumentVerificationInteractor _documentVerificationInteractor;
  final Logger _logger;
  final bool _showVerificationStatus;

  VerifyRecipientIdentityCubit({
    required CompanyPermissionResolver permissionResolver,
    required LivenessInteractor efrInteractor,
    required CommonErrorHandler commonErrorHandler,
    required NavigationProvider navigationProvider,
    required RecipientVerificationStatusFactory statusFactory,
    required CardsLocalizations cardsLocalizations,
    required CommonLocalizations commonLocalizations,
    required DocumentVerificationInteractor documentVerificationInteractor,
    required EIDVerification onVerificationDone,
    required Logger logger,
    bool showVerificationStatus = true,
  })  : _permissionResolver = permissionResolver,
        _efrInteractor = efrInteractor,
        _commonErrorHandler = commonErrorHandler,
        _navigationProvider = navigationProvider,
        _statusFactory = statusFactory,
        _cardsLocalizations = cardsLocalizations,
        _logger = logger,
        _commonLocalizations = commonLocalizations,
        _documentVerificationInteractor = documentVerificationInteractor,
        _onVerificationDone = onVerificationDone,
        _showVerificationStatus = showVerificationStatus,
        super(VerifyRecipientIdentityState.initial());

  Future<void> init({required String contactName}) async {
    _loadDocumentConfig(contactName);
  }

  void onRetry() {
    state.mapOrNull(error: (it) => init(contactName: it.contactName));
  }

  Future<void> startScanningFlow() async {
    final shouldBeAllowedToProceed = await _checkCameraPermissions();
    if (!shouldBeAllowedToProceed) {
      return;
    }

    _logger.debug('DOC VERIFICATION: Camera permission granted');

    return _startDocumentReaderFlow()
        .toStream()
        .where((event) => !isClosed)
        .whereNotNull()
        .doOnData(
          (it) => _logger.debug(
            'DOC VERIFICATION: captured docs : ${it.runtimeType}',
          ),
        )
        .asyncMap(
          (it) => switch (it) {
            DocumentReaderResult() => _verifyCapturedDocument(it),
            DocumentReaderFailureResult() =>
              _commonErrorHandler.handleError(it.message),
            _ => null,
          },
        )
        .withError<PlatformException>(
          (e) => _commonErrorHandler.handleError(e.message),
        )
        .withError<Object>(
          (e) => _commonErrorHandler.handleError(
            _commonLocalizations.common_error_message,
          ),
        )
        .complete();
  }

  void _loadDocumentConfig(String contactName) {
    _efrInteractor
        .getConfig()
        .toStream()
        .logError(_logger)
        .doOnListen(
          () => safeEmit(
            VerifyRecipientIdentityState.loading(
              contactName: contactName,
            ),
          ),
        )
        .where((_) => !isClosed)
        .withError((error) {
          safeEmit(
            VerifyRecipientIdentityState.error(contactName: contactName),
          );
        })
        .doOnData(
          (config) => safeEmit(
            VerifyRecipientIdentityState.idle(
              configData: config,
              contactName: contactName,
            ),
          ),
        )
        .complete();
  }

  Future<Object?> _startDocumentReaderFlow() async {
    return state.mapOrNull(
      idle: (it) {
        return _navigationProvider.navigateTo(
          DocumentReaderFeatureNavigationConfig(
            destination: DocumentReaderScannerPageNavigationConfig(
              documentType: _documentType,
              config: it.configData.configurationData,
            ),
          ),
        );
      },
    );
  }

  Future<void> _verifyCapturedDocument(
    DocumentReaderResult readerResult,
  ) async {
    _logger.debug('DOC VERIFICATION: Verifying');

    return state.mapOrNull<Future<void>>(
      idle: (it) {
        // Copied logic from kyc module
        final frontImageDecoded = base64Decode(
          readerResult.imageFront.decoratedString,
        );
        final backImageDecoded = base64Decode(
          readerResult.imageBack.decoratedString,
        );

        final encodedFrontImage = base64Encode(frontImageDecoded);
        final backImageEncoded = base64Encode(backImageDecoded);

        final eidDetails = EidInputDetails(
          frontImage: encodedFrontImage,
          backImage: backImageEncoded,
        );

        return _documentVerificationInteractor
            .verifyEid(eidInputDetails: eidDetails)
            .asStream()
            .doOnListen(() => safeEmit(state.toVerifying()))
            .where((event) => !isClosed)
            .logError(_logger)
            .doOnError((p0, p1) => _handleVerificationFailed())
            .asyncMap(_handleVerificationResponse)
            .doOnData((_) => safeEmit(state.toIdle()))
            .drain<void>();
      },
    );
  }

  Future<void> _handleVerificationResponse(bool isVerified) async {
    if (isVerified) {
      await _handleVerificationSucceeded();
    } else {
      await _handleVerificationFailed();
    }

    return _onVerificationDone(isVerified: isVerified);
  }

  Future<void> _handleVerificationFailed() {
    return _navigationProvider.navigateTo<void>(
      _statusFactory.getVerificationErrorConfig(),
    );
  }

  Future<void> _handleVerificationSucceeded() {
    if (!_showVerificationStatus) {
      return Future.value();
    }

    return _navigationProvider.navigateTo<void>(
      _statusFactory.getVerificationSuccessConfig(),
    );
  }

  Future<bool> _checkCameraPermissions() async {
    final status =
        await _permissionResolver.requestPermissions(PermissionType.camera);

    switch (status) {
      case CompanyPermissionStatus.granted:
      case CompanyPermissionStatus.provisional:
      case CompanyPermissionStatus.limited:
        return true;
      case CompanyPermissionStatus.denied:
        showError(
          _cardsLocalizations.documentVerificationCameraDeniedError,
        );
        return false;
      case CompanyPermissionStatus.restricted:
      case CompanyPermissionStatus.permanentlyDenied:
        final result = await _navigationProvider.navigateTo(
          _statusFactory.getErrorConfig(),
        );

        _handleResult(result);

        return false;
    }
  }

  void _handleResult(Object? result) {
    if (result is StatusPageResult) {
      result.whenOrNull(
        primaryButtonSelection: () {
          _permissionResolver.openDeviceSettings();
        },
      );
    }
  }

  @override
  String toString() => 'VerifyRecipientIdentityCubit{}';
}

extension on String {
  String get decoratedString {
    return replaceAll(RegExp(r'\s+'), '');
  }
}
