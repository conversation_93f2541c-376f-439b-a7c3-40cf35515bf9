import 'package:bloc_test/bloc_test.dart';
import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:feature_cards_ui/src/screens/critical_banner/analytics/critical_banners_analytics.dart';
import 'package:feature_cards_ui/src/screens/critical_banner/cubit/critical_banners_cubit.dart';
import 'package:feature_cards_ui/src/screens/critical_banner/cubit/critical_banners_state.dart';
import 'package:flutter_test/flutter_test.dart' show TestWidgetsFlutterBinding;
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:url_launcher_platform_interface/link.dart';
import 'package:url_launcher_platform_interface/url_launcher_platform_interface.dart';
import 'package:wio_app_core_api/index.dart';

import '../mocks.dart';

class MockCriticalBannersAnalytics extends Mock
    implements CriticalBannersAnalytics {}

class MockDeepLinkProcessor extends Mock implements DeepLinkProcessor {}

class MockCriticalBannersInteractor extends Mock
    implements CriticalBannersInteractor {}

class MockUrlLauncherPlatform extends UrlLauncherPlatform {
  @override
  LinkDelegate? get linkDelegate => throw UnimplementedError();

  @override
  Future<bool> launchUrl(String url, LaunchOptions options) =>
      Future.value(true);
}

class FakeDeepLink extends Fake implements DeepLink {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  // mock url launcher so launchUrl works properly for tests
  UrlLauncherPlatform.instance = MockUrlLauncherPlatform();

  late MockLogger logger;
  late MockCommonErrorHandler errorHandler;
  late MockCriticalBannersAnalytics analytics;
  late MockDeepLinkProcessor deepLinkProcessor;
  late MockFeatureToggleProvider featureToggles;
  late MockCriticalBannersInteractor criticalBannersInteractor;

  late CriticalBannersCubit cubit;

  // Test data
  const cardId = 'test_card_id';
  final restrictions = [
    const RestrictionDetails(
      bannerTitle: 'Test Banner 1',
      bannerText: 'Test Description 1',
      deepLink: 'https://test.com/1',
    ),
    const RestrictionDetails(
      bannerTitle: 'Test Banner 2',
      bannerText: 'Test Description 2',
      deepLink: 'wio://test/2',
    ),
  ];

  final exception = Exception('Test error');

  setUpAll(() {
    // Register fallback values
    registerFallbackValue(FakeDeepLink());
  });

  CriticalBannersCubit getCubit() => CriticalBannersCubit(
        logger: logger,
        errorHandler: errorHandler,
        analytics: analytics,
        deepLinkProcessor: deepLinkProcessor,
        featureToggles: featureToggles,
        criticalBannersInteractor: criticalBannersInteractor,
      );

  setUp(() {
    logger = MockLogger();
    errorHandler = MockCommonErrorHandler();
    analytics = MockCriticalBannersAnalytics();
    deepLinkProcessor = MockDeepLinkProcessor();
    featureToggles = MockFeatureToggleProvider();
    criticalBannersInteractor = MockCriticalBannersInteractor();

    when(
      () => featureToggles.get(
        CardsFeatureToggles.isCardCriticalBannersEnabled,
      ),
    ).thenReturn(true);

    cubit = getCubit();
  });

  group('Initialization >', () {
    blocTest<CriticalBannersCubit, CriticalBannersState>(
      'emits correct states when feature is enabled',
      build: () => cubit,
      setUp: () {
        when(() => criticalBannersInteractor.getRestrictionsByCard(cardId))
            .thenAnswer((_) async => restrictions);
      },
      act: (cubit) => cubit.initBannersForCard(cardId),
      expect: () => [
        isA<CriticalBannersLoadingState>(),
        isA<CriticalBannersIdleState>()
            .having(
              (state) => state.cardId,
              'cardId',
              cardId,
            )
            .having(
              (state) => state.restrictionsDetails,
              'restrictionsDetails',
              restrictions,
            ),
      ],
      verify: (_) {
        verify(
          () => analytics.fetchedBanners(
            restrictions.map((e) => e.bannerTitle).toList(),
          ),
        ).calledOnce;
      },
    );

    blocTest<CriticalBannersCubit, CriticalBannersState>(
      'does not emit states when feature is disabled',
      build: () {
        final disabledFeatureToggles = MockFeatureToggleProvider();
        when(
          () => disabledFeatureToggles
              .get(CardsFeatureToggles.isCardCriticalBannersEnabled),
        ).thenReturn(false);

        return CriticalBannersCubit(
          logger: logger,
          errorHandler: errorHandler,
          analytics: analytics,
          deepLinkProcessor: deepLinkProcessor,
          featureToggles: disabledFeatureToggles,
          criticalBannersInteractor: criticalBannersInteractor,
        );
      },
      act: (cubit) => cubit.initBannersForCard(cardId),
      expect: () => <Object?>[],
      verify: (_) {
        verifyNever(
          () => criticalBannersInteractor.getRestrictionsByCard(cardId),
        );
      },
    );

    blocTest<CriticalBannersCubit, CriticalBannersState>(
      'emits empty state on error',
      build: () => cubit,
      setUp: () {
        when(() => criticalBannersInteractor.getRestrictionsByCard(cardId))
            .thenThrow(exception);
      },
      act: (cubit) => cubit.initBannersForCard(cardId),
      expect: () => [
        isA<CriticalBannersLoadingState>(),
        isA<CriticalBannersIdleState>()
            .having(
              (state) => state.cardId,
              'cardId',
              CriticalBannersIdleState.empty(cardId: cardId).cardId,
            )
            .having(
              (state) => state.restrictionsDetails,
              'restrictionsDetails',
              CriticalBannersIdleState.empty(cardId: cardId)
                  .restrictionsDetails,
            ),
      ],
    );
  });

  group('Banner tap handling >', () {
    blocTest<CriticalBannersCubit, CriticalBannersState>(
      'handles https deep link and calls analytics',
      build: () => cubit,
      setUp: () {
        when(() => criticalBannersInteractor.getRestrictionsByCard(cardId))
            .thenAnswer((_) async => restrictions);
      },
      seed: () => CriticalBannersIdleState(
        cardId: cardId,
        restrictionsDetails: restrictions,
      ),
      act: (cubit) => cubit.onBannerTap('https://test.com/1'),
      expect: () => [
        isA<CriticalBannersLoadingState>(),
        isA<CriticalBannersIdleState>()
            .having(
              (state) => state.cardId,
              'cardId',
              cardId,
            )
            .having(
              (state) => state.restrictionsDetails,
              'restrictionsDetails',
              restrictions,
            ),
      ],
      verify: (_) {
        verify(() => analytics.clickBannerDeepLink('https://test.com/1'))
            .calledOnce;
        verifyNever(
          () => deepLinkProcessor.process(
            any(
              that: isA<DeepLink>().having(
                (e) => e.uri,
                'uri',
                Uri.parse('https://test.com/1'),
              ),
            ),
          ),
        );
      },
    );

    blocTest<CriticalBannersCubit, CriticalBannersState>(
      'handles custom scheme deep link',
      build: () => cubit,
      setUp: () {
        when(() => criticalBannersInteractor.getRestrictionsByCard(cardId))
            .thenAnswer((_) async => restrictions);
        when(
          () => deepLinkProcessor.process(
            any(
              that: isA<DeepLink>().having(
                (e) => e.uri,
                'uri',
                Uri.parse('wio://test/2'),
              ),
            ),
          ),
        ).thenAnswer((_) async => {});
      },
      seed: () => CriticalBannersIdleState(
        cardId: cardId,
        restrictionsDetails: restrictions,
      ),
      act: (cubit) => cubit.onBannerTap('wio://test/2'),
      expect: () => [
        isA<CriticalBannersLoadingState>(),
        isA<CriticalBannersIdleState>()
            .having(
              (state) => state.cardId,
              'cardId',
              cardId,
            )
            .having(
              (state) => state.restrictionsDetails,
              'restrictionsDetails',
              restrictions,
            ),
      ],
      verify: (_) {
        verify(() => analytics.clickBannerDeepLink('wio://test/2')).calledOnce;
        verify(
          () => deepLinkProcessor.process(
            any(
              that: isA<DeepLink>().having(
                (e) => e.uri,
                'uri',
                Uri.parse('wio://test/2'),
              ),
            ),
          ),
        ).calledOnce;
        verify(() => criticalBannersInteractor.getRestrictionsByCard(cardId))
            .calledOnce;
      },
    );

    blocTest<CriticalBannersCubit, CriticalBannersState>(
      'handles error during deep link processing',
      build: () => cubit,
      setUp: () {
        when(
          () => deepLinkProcessor.process(
            any(
              that: isA<DeepLink>().having(
                (e) => e.uri,
                'uri',
                Uri.parse('wio://test/2'),
              ),
            ),
          ),
        ).thenThrow(exception);
      },
      seed: () => CriticalBannersIdleState(
        cardId: cardId,
        restrictionsDetails: restrictions,
      ),
      act: (cubit) => cubit.onBannerTap('wio://test/2'),
      expect: () => <Object?>[],
      verify: (_) {
        verify(() => analytics.clickBannerDeepLink('wio://test/2')).calledOnce;
        verify(() => errorHandler.handleError(exception)).calledOnce;
      },
    );

    blocTest<CriticalBannersCubit, CriticalBannersState>(
      'does not handle banner tap in non-idle state',
      build: () => cubit,
      seed: () => const CriticalBannersLoadingState(),
      act: (cubit) => cubit.onBannerTap('wio://test/2'),
      expect: () => <Object?>[],
      verify: (_) {
        verify(() => analytics.clickBannerDeepLink('wio://test/2')).calledOnce;
        verifyNever(
          () => deepLinkProcessor.process(
            any(
              that: isA<DeepLink>().having(
                (e) => e.uri,
                'uri',
                Uri.parse('wio://test/2'),
              ),
            ),
          ),
        );
      },
    );
  });
}
