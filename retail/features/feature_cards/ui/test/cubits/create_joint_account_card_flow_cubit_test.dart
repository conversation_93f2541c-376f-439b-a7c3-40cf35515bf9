import 'dart:ui';

import 'package:bloc_test/bloc_test.dart';
import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:feature_cards_ui/l10n/cards_localization.g.dart';
import 'package:feature_cards_ui/src/common/models/joint_account_card_recipient.dart';
import 'package:feature_cards_ui/src/screens/create_joint_account_card/flow/analytics/create_joint_account_card_analytics.dart';
import 'package:feature_cards_ui/src/screens/create_joint_account_card/flow/create_joint_account_card_handler.dart';
import 'package:feature_cards_ui/src/screens/create_joint_account_card/flow/cubit/create_joint_account_card_flow_cubit.dart';
import 'package:feature_cards_ui/src/screens/create_joint_account_card/flow/cubit/create_joint_account_card_flow_state.dart';
import 'package:feature_faq_api/feature_faq_api.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:rxdart/rxdart.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:ui/ota_lokalisation/ota_lokalization.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_card_ui/wio_common_feature_card_ui_for_retail.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_content_domain_api/index.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';
import 'package:wio_feature_user_api/index.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late CreateJointAccountCardFlowCubit cubit;
  late CardInteractor cardInteractor;
  late FamilyBankingAccountInteractor familyBankingAccountInteractor;
  late UserInteractor userInteractor;
  late CreateJointAccountCardHandler createCardHandler;
  late NavigationProvider navigationProvider;
  late CreateJointAccountCardAnalytics analytics;
  late CommonErrorHandler commonErrorHandler;
  late Logger logger;
  late ToastMessageProvider toastMessageProvider;
  late CardsLocalizations localizations;
  late FaqNavigationFlow faqNavigationFlow;
  late CardsFlow cardsFlow;

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    localizations = await CardsLocalizations.load(const Locale('en'));
  });

  setUp(() {
    cardInteractor = MockCardInteractor();
    familyBankingAccountInteractor = MockFamilyBankingAccountInteractor();
    userInteractor = MockUserInteractor();
    createCardHandler = MockCreateJointAccountCardHandler();
    navigationProvider = MockNavigationProvider();
    analytics = MockCreateJointAccountCardAnalytics();
    commonErrorHandler = MockCommonErrorHandler();
    logger = MockLogger();
    toastMessageProvider = MockToastMessageProvider();
    faqNavigationFlow = MockFaqNavigationFlow();
    cardsFlow = MockCardsFlow();

    cubit = CreateJointAccountCardFlowCubit(
      cardInteractor: cardInteractor,
      createCardHandler: createCardHandler,
      familyBankingAccountInteractor: familyBankingAccountInteractor,
      userInteractor: userInteractor,
      navigationProvider: navigationProvider,
      analytics: analytics,
      commonErrorHandler: commonErrorHandler,
      logger: logger,
      toastMessageProvider: toastMessageProvider,
      localizations: localizations,
      faqNavigationFlow: faqNavigationFlow,
      cardsFlow: cardsFlow,
    );
  });

  const cardType = CardType.shared;

  final cards = [
    CardFactory.getCard(id: '1', name: 'card1'),
    CardFactory.getCard(id: '2', name: 'card2'),
  ];

  final jointAccounts = [
    JointAccount(
      id: '1',
      name: 'Test Account 1',
      balance: Money.fromNum(0, code: 'AED'),
      coOwner: const AccountOwner(
        customerId: 'customerId1',
        name: 'name 1',
        mobileNumber: '+************',
      ),
    ),
    JointAccount(
      id: '2',
      name: 'Test Account 2',
      balance: Money.fromNum(0, code: 'AED'),
      coOwner: const AccountOwner(
        customerId: 'customerId2',
        name: 'name 2',
        mobileNumber: '+************',
      ),
    ),
  ];

  final jointAccount = jointAccounts.first;

  final coOwnerAsRecipient =
      JointAccountCardRecipient.coOwner(coOwner: jointAccount.coOwner);

  final cardImages = [
    CardFactory.getCardImage(),
    CardFactory.getCardImage(),
    CardFactory.getCardImage(),
  ];

  final cardImage = cardImages.first;

  final cardConfig = CardFactory.getCardConfig();

  final spendingLimitRecord = SpendingLimitRecord(
    limit: Money.fromNumWithCurrency(1000, Currency.aed),
    frequency: CardSpendingLimitFrequency.daily,
  );

  const currentUser = User(
    customerInfo: CustomerInfo(firstName: 'First', lastName: 'Last'),
    phone: '+************',
  );

  blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
    'onIntroScreenCompleted makes appropriate calls '
    'and updates the state accordingly',
    // Arrange
    build: () => cubit,
    setUp: () {
      when(cardInteractor.getCards).justAnswerData(cards);

      when(
        () => cardInteractor.getCardImages(cardType: cardType),
      ).justAnswerAsync(cardImages);

      when(
        () => cardInteractor.getCardConfig(cardType: cardType),
      ).justAnswerAsync(cardConfig);

      when(
        familyBankingAccountInteractor.getSharedAccounts,
      ).justAnswerAsync(jointAccounts);

      when(
        () => userInteractor.currentUser,
      ).thenAnswer(
        (_) => Stream<User>.fromIterable([]).shareValueSeeded(currentUser),
      );
    },
    seed: () => const CreateJointAccountCardFlowState.loaded(
      stages: [CreateJointAccountCardStage.intro],
    ),

    // Act
    act: (cubit) => cubit.onIntroScreenCompleted(),

    // Assert
    expect: () => [
      const CreateJointAccountCardFlowState.loaded(
        stages: [CreateJointAccountCardStage.intro],
        isIntroInProgress: true,
      ),
      CreateJointAccountCardFlowState.loaded(
        stages: [
          CreateJointAccountCardStage.intro,
          CreateJointAccountCardStage.thingsToKnow,
        ],
        cards: cards,
        cardConfig: cardConfig,
        availableAccounts: jointAccounts,
        user: currentUser,
        galleries: cardImages.toGalleryList(),
        inputData: JointAccountCardCreationData(skin: cardImages.first),
      ),
    ],
    verify: (_) {
      verify(analytics.onCompleteIntroScreen).calledOnce;
      verify(cardInteractor.getCards).calledOnce;
      verify(() => cardInteractor.getCardImages(cardType: cardType)).calledOnce;
      verify(() => cardInteractor.getCardConfig(cardType: cardType)).calledOnce;
      verify(familyBankingAccountInteractor.getSharedAccounts).calledOnce;
    },
  );

  group('onThingsToKnowScreenCompleted >', () {
    blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
      'When user only have a single joint account',

      // Arrange
      build: () => cubit,
      seed: () => CreateJointAccountCardFlowState.loaded(
        stages: [
          CreateJointAccountCardStage.intro,
          CreateJointAccountCardStage.thingsToKnow,
        ],
        availableAccounts: [jointAccount],
      ),

      // Act
      act: (cubit) => cubit.onThingsToKnowScreenCompleted(),

      // Assert
      expect: () => [
        CreateJointAccountCardFlowState.loaded(
          stages: [
            CreateJointAccountCardStage.intro,
            CreateJointAccountCardStage.thingsToKnow,
            CreateJointAccountCardStage.selectRecipient,
          ],
          availableAccounts: [jointAccount],
          inputData: JointAccountCardCreationData(sourceAccount: jointAccount),
        ),
      ],
      verify: (_) {
        verify(() => analytics.onCompleteThingToKnowScreen()).calledOnce;
      },
    );

    blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
      'When user have a multiple joint accounts',

      // Arrange
      build: () => cubit,
      seed: () => CreateJointAccountCardFlowState.loaded(
        stages: [
          CreateJointAccountCardStage.intro,
          CreateJointAccountCardStage.thingsToKnow,
        ],
        availableAccounts: jointAccounts,
      ),

      // Act
      act: (cubit) => cubit.onThingsToKnowScreenCompleted(),

      // Assert
      expect: () => [
        predicate<CreateJointAccountCardFlowState>(
          (state) =>
              state.stages
                  .contains(CreateJointAccountCardStage.selectJointAccount) &&
              state.inputData.sourceAccount == null &&
              state.availableAccounts?.length != 1,
        ),
      ],
      verify: (_) {
        verify(() => analytics.onCompleteThingToKnowScreen()).calledOnce;
      },
    );
  });

  group('onTermsAndConditionPressed >', () {
    blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
      'when terms and condition is pressed',

      // Arrange
      build: () => cubit,
      seed: () => const CreateJointAccountCardFlowState.loaded(
        stages: [
          CreateJointAccountCardStage.intro,
          CreateJointAccountCardStage.thingsToKnow,
        ],
      ),

      // Act
      act: (cubit) => cubit.onTermsAndConditionPressed(),

      // Assert
      verify: (_) {
        verify(
          () => navigationProvider.push(
            const TncScreenNavigationConfig(
              documentType:
                  LegalDocumentType.retailJointAccountCardsTermsAndConditions,
            ),
          ),
        ).calledOnce;
      },
    );
  });

  blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
    'onSelectJointAccount updates the sourceAccount field of inputData',

    // Arrange
    build: () => cubit,
    seed: () => const CreateJointAccountCardFlowState.loaded(
      stages: [
        CreateJointAccountCardStage.intro,
        CreateJointAccountCardStage.thingsToKnow,
        CreateJointAccountCardStage.selectJointAccount,
      ],
    ),

    // Act
    act: (cubit) => cubit.onSelectJointAccount(jointAccount),

    // Assert
    expect: () => [
      CreateJointAccountCardFlowState.loaded(
        stages: [
          CreateJointAccountCardStage.intro,
          CreateJointAccountCardStage.thingsToKnow,
          CreateJointAccountCardStage.selectJointAccount,
          CreateJointAccountCardStage.selectRecipient,
        ],
        inputData: JointAccountCardCreationData(sourceAccount: jointAccount),
      ),
    ],
    verify: (_) => verify(() => analytics.onSelectJointAccount()).calledOnce,
  );

  blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
    'onSelectRecipient updates the recipient field of inputData',

    // Arrange
    build: () => cubit,
    seed: () => CreateJointAccountCardFlowState.loaded(
      stages: [
        CreateJointAccountCardStage.intro,
        CreateJointAccountCardStage.thingsToKnow,
        CreateJointAccountCardStage.selectJointAccount,
        CreateJointAccountCardStage.selectRecipient,
      ],
      inputData: JointAccountCardCreationData(
        cardName: 'Test',
        skin: cardImage,
      ),
    ),

    // Act
    act: (cubit) => cubit.onSelectRecipient(coOwnerAsRecipient),

    // Assert
    expect: () => [
      CreateJointAccountCardFlowState.loaded(
        stages: [
          CreateJointAccountCardStage.intro,
          CreateJointAccountCardStage.thingsToKnow,
          CreateJointAccountCardStage.selectJointAccount,
          CreateJointAccountCardStage.selectRecipient,
          CreateJointAccountCardStage.setUpCard,
        ],
        inputData: JointAccountCardCreationData(
          cardName: 'Test',
          skin: cardImage,
          recipient: coOwnerAsRecipient,
        ),
      ),
    ],
    verify: (_) => verify(
      () => analytics.onCompleteRecipientSelection(coOwnerAsRecipient.type),
    ).calledOnce,
  );

  blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
    'onSelectName updates the name field of inputData',

    // Arrange
    build: () => cubit,
    seed: () => const CreateJointAccountCardFlowState.loaded(
      stages: [
        CreateJointAccountCardStage.intro,
        CreateJointAccountCardStage.thingsToKnow,
        CreateJointAccountCardStage.setUpCard,
      ],
    ),

    // Act
    act: (cubit) => cubit.onSelectName('Test'),

    // Assert
    expect: () => [
      const CreateJointAccountCardFlowState.loaded(
        stages: [
          CreateJointAccountCardStage.intro,
          CreateJointAccountCardStage.thingsToKnow,
          CreateJointAccountCardStage.setUpCard,
        ],
        inputData: JointAccountCardCreationData(cardName: 'Test'),
      ),
    ],
    verify: (_) => verify(() => analytics.onSelectCardName()).calledOnce,
  );

  blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
    'onSelectSkin updates the skin field inputData',

    // Arrange
    build: () => cubit,
    seed: () => const CreateJointAccountCardFlowState.loaded(
      stages: [
        CreateJointAccountCardStage.intro,
        CreateJointAccountCardStage.thingsToKnow,
        CreateJointAccountCardStage.setUpCard,
      ],
      inputData: JointAccountCardCreationData(cardName: 'Test'),
    ),

    // Act
    act: (cubit) => cubit.onSelectSkin(cardImage),

    // Assert
    expect: () => [
      CreateJointAccountCardFlowState.loaded(
        stages: [
          CreateJointAccountCardStage.intro,
          CreateJointAccountCardStage.thingsToKnow,
          CreateJointAccountCardStage.setUpCard,
        ],
        inputData:
            JointAccountCardCreationData(cardName: 'Test', skin: cardImage),
      ),
    ],
    verify: (_) => verify(() => analytics.onSelectSkin(cardImage)).calledOnce,
  );

  blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
    'onSetUpCardCompleted updates the state accordingly',

    // Arrange
    build: () => cubit,
    seed: () => CreateJointAccountCardFlowState.loaded(
      stages: [
        CreateJointAccountCardStage.intro,
        CreateJointAccountCardStage.thingsToKnow,
        CreateJointAccountCardStage.selectJointAccount,
        CreateJointAccountCardStage.selectRecipient,
        CreateJointAccountCardStage.setUpCard,
      ],
      inputData:
          JointAccountCardCreationData(cardName: 'Test', skin: cardImage),
    ),

    // Act
    act: (cubit) => cubit.onSetUpCardCompleted(),

    // Assert
    expect: () => [
      CreateJointAccountCardFlowState.loaded(
        stages: [
          CreateJointAccountCardStage.intro,
          CreateJointAccountCardStage.thingsToKnow,
          CreateJointAccountCardStage.selectJointAccount,
          CreateJointAccountCardStage.selectRecipient,
          CreateJointAccountCardStage.setUpCard,
          CreateJointAccountCardStage.review,
        ],
        inputData: JointAccountCardCreationData(
          cardName: 'Test',
          skin: cardImage,
        ),
      ),
    ],
  );

  group('onPopScreen >', () {
    blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
      'updated the stages when there are multiple stage',
      build: () => cubit,
      seed: () => const CreateJointAccountCardFlowState.loaded(
        stages: [
          CreateJointAccountCardStage.intro,
          CreateJointAccountCardStage.setUpCard,
        ],
      ),
      act: (cubit) => cubit.onPopScreen(),
      verify: (_) {
        verify(
          () => analytics.onPopScreen(
            CreateJointAccountCardStage.setUpCard,
          ),
        ).calledOnce;
      },
      expect: () => [
        const CreateJointAccountCardFlowState.loaded(
          stages: [CreateJointAccountCardStage.intro],
        ),
      ],
    );

    blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
      'calls goBack when there is only one stage',
      build: () => cubit,
      seed: () => const CreateJointAccountCardFlowState.loaded(
        stages: [CreateJointAccountCardStage.intro],
      ),
      act: (cubit) => cubit.onPopScreen(),
      verify: (_) {
        verify(
          () => analytics.onPopScreen(CreateJointAccountCardStage.intro),
        ).calledOnce;
        verify(() => navigationProvider.goBack()).calledOnce;
      },
    );
  });

  blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
    'onEditRecipient updates the stage',

    // Arrange
    build: () => cubit,
    seed: () => CreateJointAccountCardFlowState.loaded(
      stages: [
        CreateJointAccountCardStage.intro,
        CreateJointAccountCardStage.thingsToKnow,
        CreateJointAccountCardStage.selectJointAccount,
        CreateJointAccountCardStage.selectRecipient,
        CreateJointAccountCardStage.setUpCard,
        CreateJointAccountCardStage.review,
      ],
      inputData: JointAccountCardCreationData(
        cardName: 'Test',
        skin: cardImage,
        recipient: coOwnerAsRecipient,
      ),
    ),

    // Act
    act: (cubit) => cubit.onEditRecipient(),

    // Assert
    expect: () => [
      CreateJointAccountCardFlowState.loaded(
        stages: [
          CreateJointAccountCardStage.intro,
          CreateJointAccountCardStage.thingsToKnow,
          CreateJointAccountCardStage.selectJointAccount,
          CreateJointAccountCardStage.selectRecipient,
        ],
        inputData: JointAccountCardCreationData(
          cardName: 'Test',
          skin: cardImage,
          recipient: coOwnerAsRecipient,
        ),
      ),
    ],
    verify: (_) => verify(() => analytics.onEditRecipient()).calledOnce,
  );

  blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
    'onEditSpendingLimit updates the stage',

    // Arrange
    build: () => cubit,
    seed: () => CreateJointAccountCardFlowState.loaded(
      stages: [
        CreateJointAccountCardStage.intro,
        CreateJointAccountCardStage.thingsToKnow,
        CreateJointAccountCardStage.selectJointAccount,
        CreateJointAccountCardStage.selectRecipient,
        CreateJointAccountCardStage.setUpCard,
        CreateJointAccountCardStage.review,
      ],
      inputData:
          JointAccountCardCreationData(cardName: 'Test', skin: cardImage),
    ),

    // Act
    act: (cubit) => cubit.onEditSpendingLimit(),

    // Assert
    expect: () => [
      CreateJointAccountCardFlowState.loaded(
        stages: [
          CreateJointAccountCardStage.intro,
          CreateJointAccountCardStage.thingsToKnow,
          CreateJointAccountCardStage.selectJointAccount,
          CreateJointAccountCardStage.selectRecipient,
          CreateJointAccountCardStage.setUpCard,
          CreateJointAccountCardStage.review,
          CreateJointAccountCardStage.setUpSpendingLimit,
        ],
        inputData: JointAccountCardCreationData(
          cardName: 'Test',
          skin: cardImage,
        ),
      ),
    ],
    verify: (_) => verify(() => analytics.onEditSpendingLimit()).calledOnce,
  );

  blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
    'onSpendingLimitSelected updates the state accordingly',

    // Arrange
    build: () => cubit,
    seed: () => CreateJointAccountCardFlowState.loaded(
      stages: [],
      inputData:
          JointAccountCardCreationData(cardName: 'Test', skin: cardImage),
    ),

    // Act
    act: (cubit) => cubit.onSelectSpendingLimit(spendingLimitRecord),

    // Assert
    expect: () => [
      CreateJointAccountCardFlowState.loaded(
        stages: [],
        inputData: JointAccountCardCreationData(
          cardName: 'Test',
          skin: cardImage,
          spendingLimit: spendingLimitRecord,
        ),
      ),
    ],
  );

  group('onEditExpiryDate >', () {
    const stages = [CreateJointAccountCardStage.review];
    final expiryDate = DateTime(2042);
    final newExpiryDate = DateTime(2034);

    blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
      'set up expiry date',
      // Arrange
      build: () => cubit,
      seed: () => CreateJointAccountCardFlowState.loaded(
        stages: stages,
        inputData: JointAccountCardCreationData(
          cardName: 'Test',
          skin: cardImage,
        ),
      ),
      setUp: () {
        when(
          cardsFlow.onEditExpiryDate,
        ).justAnswerAsync(ExpiryDateEditResult.edit);
        when(cardsFlow.onSetExpiryDate).justAnswerAsync(expiryDate);
      },

      // Act
      act: (cubit) => cubit.onEditExpiryDate(),

      // Assert
      expect: () => [
        CreateJointAccountCardFlowState.loaded(
          stages: stages,
          inputData: JointAccountCardCreationData(
            cardName: 'Test',
            skin: cardImage,
            expiryDate: expiryDate,
          ),
        ),
      ],
      verify: (_) => verify(analytics.onEditExpiryDate),
    );

    blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
      'remove expiry date',
      // Arrange
      build: () => cubit,
      seed: () => CreateJointAccountCardFlowState.loaded(
        stages: stages,
        inputData: JointAccountCardCreationData(
          cardName: 'Test',
          skin: cardImage,
          expiryDate: expiryDate,
        ),
      ),
      setUp: () {
        when(
          cardsFlow.onEditExpiryDate,
        ).justAnswerAsync(ExpiryDateEditResult.remove);
      },

      // Act
      act: (cubit) => cubit.onEditExpiryDate(),

      // Assert
      expect: () => [
        CreateJointAccountCardFlowState.loaded(
          stages: stages,
          inputData: JointAccountCardCreationData(
            cardName: 'Test',
            skin: cardImage,
          ),
        ),
      ],
      verify: (_) => verify(analytics.onEditExpiryDate),
    );

    blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
      'edit expiry date',
      // Arrange
      build: () => cubit,
      seed: () => CreateJointAccountCardFlowState.loaded(
        stages: stages,
        inputData: JointAccountCardCreationData(
          cardName: 'Test',
          skin: cardImage,
          expiryDate: expiryDate,
        ),
      ),
      setUp: () {
        when(
          cardsFlow.onEditExpiryDate,
        ).justAnswerAsync(ExpiryDateEditResult.edit);
        when(
          () => cardsFlow.onSetExpiryDate(expiryDate: expiryDate),
        ).justAnswerAsync(newExpiryDate);
      },

      // Act
      act: (cubit) => cubit.onEditExpiryDate(),

      // Assert
      expect: () => [
        CreateJointAccountCardFlowState.loaded(
          stages: stages,
          inputData: JointAccountCardCreationData(
            cardName: 'Test',
            skin: cardImage,
            expiryDate: newExpiryDate,
          ),
        ),
      ],
      verify: (_) => verify(analytics.onEditExpiryDate),
    );
  });

  group('onCreateCard', () {
    const stages = [
      CreateJointAccountCardStage.intro,
      CreateJointAccountCardStage.thingsToKnow,
      CreateJointAccountCardStage.selectJointAccount,
      CreateJointAccountCardStage.selectRecipient,
      CreateJointAccountCardStage.setUpCard,
      CreateJointAccountCardStage.review,
    ];
    final spendingLimit = SpendingLimitRecord(
      limit: Money.fromNumWithCurrency(1000, Currency.aed),
      frequency: CardSpendingLimitFrequency.daily,
    );
    final inputData = JointAccountCardCreationData(
      cardName: 'Test',
      skin: cardImage,
      sourceAccount: jointAccount,
      expiryDate: DateTime(2034),
      spendingLimit: spendingLimit,
      recipient: coOwnerAsRecipient,
    );

    setUp(() {
      registerFallbackValue(NotificationToastMessageConfiguration.error(''));
    });

    blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
      'card created successfully',
      // Arrange
      build: () => cubit,
      seed: () => CreateJointAccountCardFlowState.loaded(
        stages: stages,
        inputData: inputData,
      ),
      setUp: () {
        when(() => createCardHandler.createCard(inputData)).justCompleteAsync();
      },

      // Act
      act: (cubit) => cubit.onCreateCard(),

      // Assert
      expect: () => [
        CreateJointAccountCardFlowState.creating(
          stages: stages,
          inputData: inputData,
        ),
        CreateJointAccountCardFlowState.loaded(
          stages: [...stages, CreateJointAccountCardStage.success],
          inputData: inputData,
        ),
      ],
      verify: (_) {
        verify(analytics.onSubmit).calledOnce;
        verify(analytics.onCardCreated).calledOnce;
      },
    );

    const creationLimitExceptions = [
      NewCardCreationException.dailyLimitCardCreationReached(message: ''),
      NewCardCreationException.overallLimitReached(message: ''),
    ];

    for (final creationLimitException in creationLimitExceptions) {
      blocTest<CreateJointAccountCardFlowCubit,
          CreateJointAccountCardFlowState>(
        'card creation limit reached: ${creationLimitException.runtimeType}',
        // Arrange
        build: () => cubit,
        seed: () => CreateJointAccountCardFlowState.loaded(
          stages: stages,
          inputData: inputData,
        ),
        setUp: () {
          when(() => createCardHandler.createCard(inputData))
              .justThrowAsync(creationLimitException);
          when(() => toastMessageProvider.showToastMessage(any()))
              .justComplete();
        },

        // Act
        act: (cubit) => cubit.onCreateCard(),

        // Assert
        expect: () => [
          CreateJointAccountCardFlowState.creating(
            stages: stages,
            inputData: inputData,
          ),
          CreateJointAccountCardFlowState.loaded(
            stages: stages,
            inputData: inputData,
          ),
        ],
        verify: (_) {
          verify(() => toastMessageProvider.showToastMessage(any())).calledOnce;
          verify(navigationProvider.goBack).calledOnce;
          verify(analytics.onSubmit).calledOnce;
          verifyNoMoreInteractions(analytics);
        },
      );
    }

    blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
      'general card creation error occurred',
      // Arrange
      build: () => cubit,
      seed: () => CreateJointAccountCardFlowState.loaded(
        stages: stages,
        inputData: inputData,
      ),
      setUp: () {
        const errorMessage = 'General error message';
        when(() => createCardHandler.createCard(inputData)).justThrowAsync(
          const NewCardCreationException.general(
            message: errorMessage,
          ),
        );
        when(
          () => toastMessageProvider.showToastMessage(
            NotificationToastMessageConfiguration.error(errorMessage),
          ),
        ).justComplete();
        when(navigationProvider.goBack).justComplete();
      },

      // Act
      act: (cubit) => cubit.onCreateCard(),

      // Assert
      expect: () => [
        CreateJointAccountCardFlowState.creating(
          stages: stages,
          inputData: inputData,
        ),
        CreateJointAccountCardFlowState.loaded(
          stages: stages,
          inputData: inputData,
        ),
      ],
      verify: (_) {
        verifyInOrder([
          analytics.onSubmit,
          navigationProvider.goBack,
          () => toastMessageProvider.showToastMessage(any()),
        ]);
        verifyNoMoreInteractions(analytics);
        verifyNoMoreInteractions(navigationProvider);
        verifyNoMoreInteractions(toastMessageProvider);
      },
    );

    blocTest<CreateJointAccountCardFlowCubit, CreateJointAccountCardFlowState>(
      'non-card creation error occurred',
      // Arrange
      build: () => cubit,
      seed: () => CreateJointAccountCardFlowState.loaded(
        stages: stages,
        inputData: inputData,
      ),
      setUp: () {
        final error = Exception('Some other error');
        when(() => createCardHandler.createCard(inputData))
            .justThrowAsync(error);
        when(() => commonErrorHandler.handleError(error)).justComplete();
        when(navigationProvider.goBack).justComplete();
      },

      // Act
      act: (cubit) => cubit.onCreateCard(),

      // Assert
      expect: () => [
        CreateJointAccountCardFlowState.creating(
          stages: stages,
          inputData: inputData,
        ),
        CreateJointAccountCardFlowState.loaded(
          stages: stages,
          inputData: inputData,
        ),
      ],
      verify: (_) {
        verifyInOrder([
          analytics.onSubmit,
          navigationProvider.goBack,
          () => commonErrorHandler.handleError(any()),
        ]);
        verifyNoMoreInteractions(analytics);
        verifyNoMoreInteractions(navigationProvider);
        verifyNoMoreInteractions(commonErrorHandler);
      },
    );
  });
}
