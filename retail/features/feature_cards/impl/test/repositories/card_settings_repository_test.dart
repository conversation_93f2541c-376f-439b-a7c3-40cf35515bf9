import 'package:domain/domain.dart';
import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:mocktail/mocktail.dart';
import 'package:rxdart/rxdart.dart';
import 'package:tests/tests.dart';
import 'package:wio_feature_cards_impl/data/models/cards_swagger.swagger.dart';
import 'package:wio_feature_cards_impl/data/repositories/card_settings_repository_impl.dart';

import '../mocks.dart';
import '../test_entities.dart';

Data<List<Card>>? _fakeUpdateCardsIfNeeded(
  Data<List<Card>>? currentData,
) =>
    null;

void main() {
  late CardSettingsRepositoryImpl repository;
  late MockCardMapper cardMapper;
  late MockCardStorageRefresher cardStorageRefresher;
  late MockCardService cardService;
  late MockCardStorage cardStorage;

  setUp(() {
    cardService = MockCardService();
    cardStorageRefresher = MockCardStorageRefresher();
    cardStorage = MockCardStorage();
    cardMapper = MockCardMapper();

    repository = CardSettingsRepositoryImpl(
      cardService: cardService,
      cardStorageRefresher: cardStorageRefresher,
      cardMapper: cardMapper,
      cardStorage: cardStorage,
    );

    registerFallbackValue(_fakeUpdateCardsIfNeeded);
  });

  test('Update spending limit', () async {
    // Arrange
    const cardID = 'cardID';
    final request = SetCardLimitRequest(
      limit: 24.0,
      type: SetCardLimitRequestType.monthly,
    );

    final spendingLimitRecord = CardFactory.getSpendingLimitRecord();
    when(() => cardMapper.mapToSetCardLimitRequest(spendingLimitRecord))
        .thenReturn(request);
    when(cardStorageRefresher.refreshCards).justComplete();
    when(() => cardService.setSpendingLimit(cardID, request))
        .justCompleteAsync();

    // Act
    await repository.updateSpendingLimit(
      cardID: cardID,
      spendingLimitRecord: spendingLimitRecord,
    );

    // Assert
    verify(() => cardService.setSpendingLimit(cardID, request)).calledOnce;
    verify(cardStorageRefresher.refreshCards).calledOnce;
  });

  test('Removing spending limit', () async {
    // Arrange
    const cardID = 'cardID';
    when(() => cardStorage.updateCardsIfNeeded(any())).justComplete();
    when(() => cardService.removeSpendingLimit(cardID)).justCompleteAsync();

    // Act
    await repository.removeSpendingLimit(cardID);

    // Assert
    verify(() => cardService.removeSpendingLimit(cardID)).calledOnce;
    verify(() => cardStorage.updateCardsIfNeeded(any())).calledOnce;
  });

  test('Update pin', () async {
    // Arrange
    when(() => cardService.setPin(any(), any())).justCompleteAsync();
    when(() => cardStorage.updateCardsIfNeeded(any())).justComplete();
    when(() => cardStorage.cards)
        .thenAnswer((_) => BehaviorSubject.seeded(Data.success([])));

    // Act
    await repository.updateCardPin(cardId: 'cardId', cardPin: '2222');

    // Assert
    verify(() => cardService.setPin('cardId', '2222')).calledOnce;
    verify(() => cardStorage.updateCardsIfNeeded(any())).calledOnce;
  });
}
