// ignore_for_file: avoid_redundant_argument_values

import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_cards_impl/data/mappers/chequebook_mapper.dart';
import 'package:wio_feature_cards_impl/data/models/chequebook_swagger.enums.swagger.dart';
import 'package:wio_feature_cards_impl/data/models/chequebook_swagger.swagger.dart'
    as swagger;

void main() {
  final mapper = ChequebookMapper(
    errorReporter: MockErrorReporter(),
  );
  const accountId = 'accountId';

  test(
    'mapToAccountDetails',
    () {
      final result = mapper.mapToAccountDetails(accountId);

      expect(
        result,
        isA<swagger.AccountDetails>()
            .having((p0) => p0.accountId, 'accountID matches', accountId),
      );
    },
  );

  test(
    'mapToChequebookInitiateResult',
    () {
      const firstOrder = true;
      const notEligibleReason = 'notEligibleReason';
      const leavesAvailable = [0];

      const feeAmount = '0';
      final feeCurrency = Currency.aed;
      final expectedFeeAmount = Money.fromNumWithCurrency(0, Currency.aed);
      final statuses = [
        ChequeBookOrderInitiateResponseStatus.accepted,
        ChequeBookOrderInitiateResponseStatus.notEligible,
        ChequeBookOrderInitiateResponseStatus.swaggerGeneratedUnknown,
      ];

      const feeSource = swagger.ChequeBookOrderInitiateResponse$Fees$Item(
        type: ChequeBookOrderInitiateResponse$Fees$ItemType.issuanceFee,
        amount: feeAmount,
        currency: '',
      );
      final issuanceFee = feeSource.copyWith(currency: feeCurrency.code);
      final deliveryFee = issuanceFee.copyWith(
        type: ChequeBookOrderInitiateResponse$Fees$ItemType.deliveryFee,
      );
      final fees = <swagger.ChequeBookOrderInitiateResponse$Fees$Item>[
        feeSource,
        issuanceFee,
        deliveryFee,
      ];

      for (final status in statuses) {
        final parameter = swagger.ChequeBookOrderInitiateResponse(
          status: status,
          firstOrder: firstOrder,
          notEligibleReason: notEligibleReason,
          leavesAvailable: leavesAvailable,
          fees: fees,
        );
        final result = mapper.mapToChequebookInitiateResult(parameter);
        var expectedStatus = const InitiateStatus.unknown();
        if (status == ChequeBookOrderInitiateResponseStatus.accepted) {
          expectedStatus = const InitiateStatus.accepted();
        } else if (status ==
            ChequeBookOrderInitiateResponseStatus.notEligible) {
          expectedStatus = const InitiateStatus.notEligible(
            reason: 'notEligibleReason',
          );
        }

        expect(
          result,
          isA<ChequebookInitiateResult>()
              .having(
                (p0) => p0.status,
                'status matching',
                expectedStatus,
              )
              .having((p0) => p0.firstOrder, 'order matching', firstOrder)
              .having(
                (p0) => p0.fees,
                'fees',
                CreationFees(
                  issuanceFee: expectedFeeAmount,
                  deliveryFee: expectedFeeAmount,
                ),
              ),
        );
      }
    },
  );

  group('mapChequebookHistoryToDomain', () {
    test(
      'returns correct ChequebookRequestHistory with all fields populated',
      () {
        final now = DateTime.now();
        final response = swagger.ChequeBookHistoryResponse(
          page: 2,
          size: 10,
          hasNext: true,
          data: [
            swagger.ChequeBookHistoryData(
              referenceNo: 'ref-123',
              status: swagger.ChequeBookStatus.initiated,
              createdAt: now,
              statusDescription: 'Requested',
              fees: [
                const swagger.ChequeBookFee(
                  type: swagger.ChequeBookFeeType.issuanceFee,
                  amount: '100',
                  currency: 'AED',
                ),
                const swagger.ChequeBookFee(
                  type: swagger.ChequeBookFeeType.deliveryFee,
                  amount: '50',
                  currency: 'AED',
                ),
              ],
            ),
          ],
        );

        final result = mapper.mapChequebookHistoryToDomain(response);

        expect(result, isA<ChequebookRequestHistory>());
        expect(result.page, 2);
        expect(result.size, 10);
        expect(result.hasNext, isTrue);
        expect(result.requests, hasLength(1));

        final singleRequest = result.requests.first;
        expect(singleRequest.referenceNumber, 'ref-123');
        expect(singleRequest.status, ChequebookRequestStatus.initiated);
        expect(singleRequest.createdAt, now);
        expect(singleRequest.statusDescription, 'Requested');

        // Fees
        expect(singleRequest.fees, hasLength(2));

        final fee1 = singleRequest.fees.first;
        expect(fee1.type, ChequebookRequestFeeType.issuance);
        expect(
          fee1.amount,
          equals(Money.fromNumWithCurrency(100, Currency.aed)),
        );

        final fee2 = singleRequest.fees.last;
        expect(fee2.type, ChequebookRequestFeeType.delivery);
        expect(
          fee2.amount,
          equals(Money.fromNumWithCurrency(50, Currency.aed)),
        );
      },
    );

    test(
      '''returns correct defaults (zeros, empty lists, fallback values) when fields are null''',
      () {
        // In this case, we pass nulls to ensure it defaults properly
        const response = swagger.ChequeBookHistoryResponse(
          page: null,
          size: null,
          hasNext: null,
          data: null,
        );

        final result = mapper.mapChequebookHistoryToDomain(response);

        expect(result.page, 0, reason: 'Page should default to 0');
        expect(result.size, 0, reason: 'Size should default to 0');
        expect(result.hasNext, isFalse, reason: 'hasNext defaults to false');
        expect(result.requests, isEmpty, reason: 'No data => empty list');
      },
    );
  });
}
