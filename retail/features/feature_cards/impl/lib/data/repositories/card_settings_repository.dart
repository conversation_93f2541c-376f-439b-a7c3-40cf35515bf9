import 'package:feature_cards_api/feature_cards_api.dart';

abstract class CardSettingsRepository {
  Future<void> updateCardPin({
    required String cardId,
    required String cardPin,
  });

  Future<String> getPin(String cardId);

  Future<void> updateSpendingLimit({
    required String cardID,
    required SpendingLimitRecord spendingLimitRecord,
  });

  Future<void> resetSpendingLimit(String cardId);

  Future<void> removeSpendingLimit(String cardId);

  Future<void> updateCardSettings({
    required String cardID,
    required CardSettings cardSettings,
  });

  Future<void> setDefaultAccount({
    required String cardID,
    required String accountID,
  });

  Future<void> updateCardImage({
    required String cardId,
    required String imageId,
  });

  Future<Card> replaceCard({
    required String cardId,
    required String reasonId,
  });

  Future<CardReplacementDetails> getCardReplacementDetails({
    bool validateReplacementLimit = true,
    String? replacementReasonId,
  });

  Future<List<CardReplacementReason>> getReplacementReasons();
}
