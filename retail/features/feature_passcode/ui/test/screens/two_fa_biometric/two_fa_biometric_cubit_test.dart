import 'dart:ui';

import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:ui/ota_lokalisation/ota_lokalization.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_biometrics_api/domain/biometric_feature_toggle.dart';
import 'package:wio_feature_passcode_api/index.dart';
import 'package:wio_feature_passcode_api/navigation/biometric_two_fa_page_navigation_config.dart';
import 'package:wio_feature_passcode_ui/l10n/passcode_localization.g.dart';
import 'package:wio_feature_passcode_ui/src/navigation/biometric_changed_bottom_sheet_config.dart';
import 'package:wio_feature_passcode_ui/src/screens/two_fa_biometric/cubit/two_fa_biometric_cubit.dart';
import 'package:wio_feature_passcode_ui/src/screens/two_fa_biometric/cubit/two_fa_biometric_state.dart';
import 'package:wio_feature_retail_identity_domain_api/index.dart';
import 'package:wio_feature_two_factor_auth_api/index.dart';

import '../../feature_passcode_ui_mocks.dart';

void main() {
  late PasscodeLocalizations localizations;
  final biometricInteractor = MockBiometricInteractor();
  final loggerMock = MockLogger();
  final navigation = MockPasscodeNavigationHandler();
  final bindDeviceInteractor = MockBindDeviceInteractor();
  final twoFactorAuthInteractor = MockTwoFactorAuthInteractor();
  final analytics = MockTwoFaBiometricsScreenAnalytics();
  final featureToggles = MockFeatureToggleProvider();
  final navigationProvider = MockNavigationProvider();
  const emptyDetails = FailureDetail.errorDetails();

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    localizations = await PasscodeLocalizations.load(const Locale('en'));
  });

  setUp(() {
    registerFallbackValue(TwoFactorAuthModelFake());
    when(() => bindDeviceInteractor.unbindDevice()).justCompleteAsync();
    when(
      () => featureToggles.get<bool>(
        BioMetricFeatureToggle.isReauthOnBioChange,
      ),
    ).thenAnswer(
      (_) => true,
    );
  });

  const mockedTwoFaParams = TwoFaParams(
    transactionId: 'transactionId',
    twoFaType: TwoFaType.auth,
  );

  TwoFaBiometricCubit getCubit({TwoFaBiometricState? loginState}) {
    return TwoFaBiometricCubit(
      navigationHandler: navigation,
      twoFactorAuthInteractor: twoFactorAuthInteractor,
      logger: loggerMock,
      localizations: localizations,
      analytics: analytics,
      state: loginState,
      featureToggles: featureToggles,
      biometricInteractor: biometricInteractor,
      navigationProvider: navigationProvider,
      bindDeviceInteractor: bindDeviceInteractor,
    );
  }

  group('twofa biometric', () {
    blocTest<TwoFaBiometricCubit, TwoFaBiometricState>(
      '''Should show bottom sheet when user biometrics changed''',
      build: () => getCubit(),
      setUp: () {
        when(() => biometricInteractor.isBiometryAvailable())
            .justAnswerAsync(true);
        when(() => biometricInteractor.getBiometricAuthenticationType())
            .justAnswerAsync(BiometricAuthenticationType.face);

        when(
          () => biometricInteractor.authenticate(
            reason: any(named: 'reason'),
            subtitle: any(named: 'subtitle'),
            negativeButton: any(named: 'negativeButton'),
          ),
        ).justAnswerAsync(const BiometricAuthenticatorResult.success());
        when(() => biometricInteractor.fetchBiometricStatus())
            .justAnswerAsync(BiometricStatus.changed);

        when(
          () => twoFactorAuthInteractor.solve(
            transactionId: any(named: 'transactionId'),
            challengeId: any(named: 'challengeId'),
            twoFactorAuthModel: any(named: 'twoFactorAuthModel'),
          ),
        ).justAnswerAsync('value');
        when(
          () => featureToggles.get(PasscodeFeatureToggles.passcodeDigitsCount),
        ).thenReturn(5);
      },
      act: (cubit) => cubit.initialize(
        BiometricTwoFaPageNavigationConfig(
          challengeId: 'challengeId',
          passcodeNavigationHandler: navigation,
          twoFaParams: mockedTwoFaParams,
          transactionExpiredTime: DateTime.now().millisecondsSinceEpoch,
        ),
      ),
      verify: (cubit) {
        verify(
          () => navigationProvider
              .showBottomSheet(const BiometricChangedBottomSheetConfig()),
        );
        verifyNever(() => analytics.biometricPassSuccess());
      },
    );

    blocTest<TwoFaBiometricCubit, TwoFaBiometricState>(
      '''Should not show bottom sheet when user biometrics unchanged''',
      build: () => getCubit(),
      setUp: () {
        when(() => biometricInteractor.isBiometryAvailable())
            .justAnswerAsync(true);
        when(() => biometricInteractor.getBiometricAuthenticationType())
            .justAnswerAsync(BiometricAuthenticationType.face);

        when(
          () => biometricInteractor.authenticate(
            reason: any(named: 'reason'),
            subtitle: any(named: 'subtitle'),
            negativeButton: any(named: 'negativeButton'),
          ),
        ).justAnswerAsync(const BiometricAuthenticatorResult.success());
        when(() => biometricInteractor.fetchBiometricStatus())
            .justAnswerAsync(BiometricStatus.valid);

        when(
          () => twoFactorAuthInteractor.solve(
            transactionId: any(named: 'transactionId'),
            challengeId: any(named: 'challengeId'),
            twoFactorAuthModel: any(named: 'twoFactorAuthModel'),
          ),
        ).justAnswerAsync('value');
        when(
          () => featureToggles.get(PasscodeFeatureToggles.passcodeDigitsCount),
        ).thenReturn(5);
      },
      act: (cubit) => cubit.initialize(
        BiometricTwoFaPageNavigationConfig(
          challengeId: 'challengeId',
          passcodeNavigationHandler: navigation,
          twoFaParams: mockedTwoFaParams,
          transactionExpiredTime: DateTime.now().millisecondsSinceEpoch,
        ),
      ),
      verify: (cubit) {
        verifyNever(
          () => navigationProvider
              .showBottomSheet(const BiometricChangedBottomSheetConfig()),
        );
        verify(() => analytics.biometricPassSuccess());
      },
    );

    blocTest<TwoFaBiometricCubit, TwoFaBiometricState>(
      '''Should send biometric pass success event when user log in wia bio''',
      build: () => getCubit(),
      setUp: () {
        when(() => biometricInteractor.isBiometryAvailable())
            .justAnswerAsync(true);
        when(() => biometricInteractor.getBiometricAuthenticationType())
            .justAnswerAsync(BiometricAuthenticationType.face);

        when(
          () => biometricInteractor.authenticate(
            reason: any(named: 'reason'),
            subtitle: any(named: 'subtitle'),
            negativeButton: any(named: 'negativeButton'),
          ),
        ).justAnswerAsync(const BiometricAuthenticatorResult.success());
        when(() => biometricInteractor.fetchBiometricStatus())
            .justAnswerAsync(BiometricStatus.valid);

        when(
          () => twoFactorAuthInteractor.solve(
            transactionId: any(named: 'transactionId'),
            challengeId: any(named: 'challengeId'),
            twoFactorAuthModel: any(named: 'twoFactorAuthModel'),
          ),
        ).justAnswerAsync('value');
        when(
          () => featureToggles.get(PasscodeFeatureToggles.passcodeDigitsCount),
        ).thenReturn(5);
      },
      act: (cubit) => cubit.initialize(
        BiometricTwoFaPageNavigationConfig(
          challengeId: 'challengeId',
          passcodeNavigationHandler: navigation,
          twoFaParams: mockedTwoFaParams,
          transactionExpiredTime: DateTime.now().millisecondsSinceEpoch,
        ),
      ),
      verify: (cubit) => verify(() => analytics.biometricPassSuccess()),
    );

    blocTest<TwoFaBiometricCubit, TwoFaBiometricState>(
      '''Should send biometric pass failed event when user log in wia bio failed''',
      build: () => getCubit(),
      setUp: () {
        when(() => biometricInteractor.isBiometryAvailable())
            .justAnswerAsync(true);
        when(() => biometricInteractor.getBiometricAuthenticationType())
            .justAnswerAsync(BiometricAuthenticationType.face);

        when(
          () => biometricInteractor.authenticate(
            reason: any(named: 'reason'),
            subtitle: any(named: 'subtitle'),
            negativeButton: any(named: 'negativeButton'),
          ),
        ).justAnswerAsync(
          const BiometricAuthenticatorResult.failed(
            code: BiometricAuthenticarErrorCode.canceled,
          ),
        );

        when(
          () => twoFactorAuthInteractor.solve(
            transactionId: any(named: 'transactionId'),
            challengeId: any(named: 'challengeId'),
            twoFactorAuthModel: any(named: 'twoFactorAuthModel'),
          ),
        ).justAnswerAsync('value');
        when(
          () => featureToggles.get(PasscodeFeatureToggles.passcodeDigitsCount),
        ).thenReturn(5);
      },
      act: (cubit) => cubit.initialize(
        BiometricTwoFaPageNavigationConfig(
          challengeId: 'challengeId',
          twoFaParams: mockedTwoFaParams,
          passcodeNavigationHandler: navigation,
          transactionExpiredTime: DateTime.now().millisecondsSinceEpoch,
        ),
      ),
      verify: (cubit) => verify(
        () => analytics.biometricPassFailed(
          PasscodeChallengeErrorCode.failed.name,
          'User cancel biometrics dialog',
        ),
      ),
    );

    blocTest<TwoFaBiometricCubit, TwoFaBiometricState>(
      '''Check if feature toggle works correct''',
      build: () => getCubit(),
      setUp: () {
        when(() => biometricInteractor.isBiometryAvailable())
            .justAnswerAsync(true);
        when(() => biometricInteractor.getBiometricAuthenticationType())
            .justAnswerAsync(BiometricAuthenticationType.face);

        when(
          () => biometricInteractor.authenticate(
            reason: any(named: 'reason'),
            subtitle: any(named: 'subtitle'),
            negativeButton: any(named: 'negativeButton'),
          ),
        ).justAnswerAsync(const BiometricAuthenticatorResult.success());
        when(
          () => twoFactorAuthInteractor.solve(
            transactionId: any(named: 'transactionId'),
            challengeId: any(named: 'challengeId'),
            twoFactorAuthModel: any(named: 'twoFactorAuthModel'),
          ),
        ).justAnswerAsync('value');
        when(
          () => featureToggles.get(PasscodeFeatureToggles.passcodeDigitsCount),
        ).thenReturn(2);
      },
      act: (cubit) => cubit.initialize(
        BiometricTwoFaPageNavigationConfig(
          challengeId: 'challengeId',
          passcodeNavigationHandler: navigation,
          twoFaParams: mockedTwoFaParams,
          transactionExpiredTime: DateTime.now().millisecondsSinceEpoch,
        ),
      ),
      verify: (cubit) => expect(cubit.state.passcodeLength, 2),
    );
  });

  group('Analytics test', () {
    blocTest<TwoFaBiometricCubit, TwoFaBiometricState>(
      'Should send back event when onBackPressed',
      build: () => getCubit(),
      act: (cubit) => cubit.onBackPressed(),
      verify: (cubit) => verify(() => analytics.backButtonPressed()),
    );
  });

  group('Solve challenge not allowed exception handler', () {
    setUp(() {
      when(() => biometricInteractor.isBiometryAvailable())
          .justAnswerAsync(true);
      when(() => biometricInteractor.getBiometricAuthenticationType())
          .justAnswerAsync(BiometricAuthenticationType.face);
      when(
        () => biometricInteractor.authenticate(
          reason: any(named: 'reason'),
          subtitle: any(named: 'subtitle'),
          negativeButton: any(named: 'negativeButton'),
        ),
      ).justAnswerAsync(const BiometricAuthenticatorResult.success());
      when(() => featureToggles.get(PasscodeFeatureToggles.passcodeDigitsCount))
          .thenReturn(5);
    });

    blocTest<TwoFaBiometricCubit, TwoFaBiometricState>(
      '''Should send success result if status solved''',
      build: () => getCubit(loginState: const TwoFaBiometricState()),
      setUp: () {
        when(
          () => twoFactorAuthInteractor.solve(
            transactionId: any(named: 'transactionId'),
            challengeId: any(named: 'challengeId'),
            twoFactorAuthModel: any(named: 'twoFactorAuthModel'),
          ),
        ).justThrowAsync(
          const SolveException.solveChallengeNotAllowed(
            status: TwoFactorAuthChallengeStatus.solved,
            failureDetail: emptyDetails,
          ),
        );
      },
      act: (cubit) => cubit.initialize(
        BiometricTwoFaPageNavigationConfig(
          challengeId: 'challengeId',
          passcodeNavigationHandler: navigation,
          twoFaParams: mockedTwoFaParams,
          transactionExpiredTime: DateTime.now().millisecondsSinceEpoch,
        ),
      ),
      verify: (cubit) {
        verify(
          () => navigation.handleResult(
            challengeResult: const PasscodeChallengeResult.success(),
          ),
        );
      },
    );

    blocTest<TwoFaBiometricCubit, TwoFaBiometricState>(
      '''Should send blocked result if status factorBlocked''',
      build: () => getCubit(loginState: const TwoFaBiometricState()),
      setUp: () {
        when(
          () => twoFactorAuthInteractor.solve(
            transactionId: any(named: 'transactionId'),
            challengeId: any(named: 'challengeId'),
            twoFactorAuthModel: any(named: 'twoFactorAuthModel'),
          ),
        ).justThrowAsync(
          const SolveException.solveChallengeNotAllowed(
            status: TwoFactorAuthChallengeStatus.factorBlocked,
            failureDetail: emptyDetails,
          ),
        );
      },
      act: (cubit) => cubit.initialize(
        BiometricTwoFaPageNavigationConfig(
          challengeId: 'challengeId',
          passcodeNavigationHandler: navigation,
          twoFaParams: mockedTwoFaParams,
          transactionExpiredTime: DateTime.now().millisecondsSinceEpoch,
        ),
      ),
      verify: (cubit) {
        verify(
          () => navigation.handleResult(
            challengeResult: const PasscodeChallengeResult.failed(
              code: PasscodeChallengeErrorCode.blocked,
            ),
          ),
        );
      },
    );

    blocTest<TwoFaBiometricCubit, TwoFaBiometricState>(
      '''Should send failed result if status other from factorBlock and solved''',
      build: () => getCubit(loginState: const TwoFaBiometricState()),
      setUp: () {
        when(
          () => twoFactorAuthInteractor.solve(
            transactionId: any(named: 'transactionId'),
            challengeId: any(named: 'challengeId'),
            twoFactorAuthModel: any(named: 'twoFactorAuthModel'),
          ),
        ).justThrowAsync(
          const SolveException.solveChallengeNotAllowed(
            status: TwoFactorAuthChallengeStatus.failed,
            failureDetail: emptyDetails,
          ),
        );
      },
      act: (cubit) => cubit.initialize(
        BiometricTwoFaPageNavigationConfig(
          challengeId: 'challengeId',
          passcodeNavigationHandler: navigation,
          twoFaParams: mockedTwoFaParams,
          transactionExpiredTime: DateTime.now().millisecondsSinceEpoch,
        ),
      ),
      verify: (cubit) {
        verify(
          () => navigation.handleResult(
            challengeResult: const PasscodeChallengeResult.failed(
              code: PasscodeChallengeErrorCode.failed,
            ),
          ),
        );
      },
    );
  });

  group('InappropriateTransaction exception handler', () {
    setUp(() {
      when(() => biometricInteractor.isBiometryAvailable())
          .justAnswerAsync(true);
      when(() => biometricInteractor.getBiometricAuthenticationType())
          .justAnswerAsync(BiometricAuthenticationType.face);
      when(
        () => biometricInteractor.authenticate(
          reason: any(named: 'reason'),
          subtitle: any(named: 'subtitle'),
          negativeButton: any(named: 'negativeButton'),
        ),
      ).justAnswerAsync(const BiometricAuthenticatorResult.success());
      when(() => featureToggles.get(PasscodeFeatureToggles.passcodeDigitsCount))
          .thenReturn(5);
    });

    blocTest<TwoFaBiometricCubit, TwoFaBiometricState>(
      '''Should send success result if status completed''',
      build: () => getCubit(loginState: const TwoFaBiometricState()),
      setUp: () {
        when(
          () => twoFactorAuthInteractor.solve(
            transactionId: any(named: 'transactionId'),
            challengeId: any(named: 'challengeId'),
            twoFactorAuthModel: any(named: 'twoFactorAuthModel'),
          ),
        ).justThrowAsync(
          const SolveException.inappropriateTransaction(
            status: TwoFactorTransactionStatus.completed,
            failureDetail: emptyDetails,
          ),
        );
      },
      act: (cubit) => cubit.initialize(
        BiometricTwoFaPageNavigationConfig(
          challengeId: 'challengeId',
          passcodeNavigationHandler: navigation,
          twoFaParams: mockedTwoFaParams,
          transactionExpiredTime: DateTime.now().millisecondsSinceEpoch,
        ),
      ),
      verify: (cubit) {
        verify(
          () => navigation.handleResult(
            challengeResult: const PasscodeChallengeResult.success(),
          ),
        );
      },
    );

    blocTest<TwoFaBiometricCubit, TwoFaBiometricState>(
      '''Should send success result if status verified''',
      build: () => getCubit(loginState: const TwoFaBiometricState()),
      setUp: () {
        when(
          () => twoFactorAuthInteractor.solve(
            transactionId: any(named: 'transactionId'),
            challengeId: any(named: 'challengeId'),
            twoFactorAuthModel: any(named: 'twoFactorAuthModel'),
          ),
        ).justThrowAsync(
          const SolveException.inappropriateTransaction(
            status: TwoFactorTransactionStatus.verified,
            failureDetail: emptyDetails,
          ),
        );
      },
      act: (cubit) => cubit.initialize(
        BiometricTwoFaPageNavigationConfig(
          challengeId: 'challengeId',
          passcodeNavigationHandler: navigation,
          twoFaParams: mockedTwoFaParams,
          transactionExpiredTime: DateTime.now().millisecondsSinceEpoch,
        ),
      ),
      verify: (cubit) {
        verify(
          () => navigation.handleResult(
            challengeResult: const PasscodeChallengeResult.success(),
          ),
        );
      },
    );

    blocTest<TwoFaBiometricCubit, TwoFaBiometricState>(
      '''Should send expiredTransaction result if status other from completed and verified''',
      build: () => getCubit(loginState: const TwoFaBiometricState()),
      setUp: () {
        when(
          () => twoFactorAuthInteractor.solve(
            transactionId: any(named: 'transactionId'),
            challengeId: any(named: 'challengeId'),
            twoFactorAuthModel: any(named: 'twoFactorAuthModel'),
          ),
        ).justThrowAsync(
          const SolveException.inappropriateTransaction(
            status: TwoFactorTransactionStatus.failed,
            failureDetail: emptyDetails,
          ),
        );
      },
      act: (cubit) => cubit.initialize(
        BiometricTwoFaPageNavigationConfig(
          challengeId: 'challengeId',
          passcodeNavigationHandler: navigation,
          twoFaParams: mockedTwoFaParams,
          transactionExpiredTime: DateTime.now().millisecondsSinceEpoch,
        ),
      ),
      verify: (cubit) {
        verify(
          () => navigation.handleResult(
            challengeResult: const PasscodeChallengeResult.failed(
              code: PasscodeChallengeErrorCode.expiredTransaction,
            ),
          ),
        );
      },
    );
  });
}
