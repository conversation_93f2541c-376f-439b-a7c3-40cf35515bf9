import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_passcode_api/index.dart';
import 'package:wio_feature_passcode_ui/src/screens/change_passcode/analytics/change_passcode_screen_analytics.dart';

void main() {
  late MockAnalyticsAbstractTrackerFactory trackerFactory;
  late MockAnalyticsEventTracker tracker;
  late ChangePasscodeScreenAnalytics analytics;

  const screenName = ChangePasscodePageNavigationConfig.screenId;
  const backEvent = 'back';

  setUp(() {
    final analyticsMocks = getAnalyticsTracker();
    trackerFactory = analyticsMocks.analyticsAbstractTrackerFactory;
    tracker = analyticsMocks.tracker;

    analytics = ChangePasscodeScreenAnalytics(analyticsFactory: trackerFactory);
  });

  test(
    'Should have correct payload for setupPasscodeInputFieldSuccess',
    () async {
      // Act
      analytics.setupPasscodeInputFieldSuccess();

      // Assert
      verify(
        () => tracker.track(
          AnalyticsEvent(
            screenName: screenName,
            action: AnalyticsAction.input,
            target: ChangePasscodeScreenAnalyticsTarget.setup_passcode.name,
            targetType: AnalyticsTargetType.input_field,
            status: AnalyticsStatus.success,
          ),
        ),
      );
    },
  );

  test(
    'Should have correct payload for setupPasscodeInputFieldError',
    () async {
      // Act
      analytics.setupPasscodeInputFieldError();

      // Assert
      verify(
        () => tracker.track(
          AnalyticsEvent(
            screenName: screenName,
            action: AnalyticsAction.input,
            target: ChangePasscodeScreenAnalyticsTarget.setup_passcode.name,
            targetType: AnalyticsTargetType.input_field,
            status: AnalyticsStatus.error,
          ),
        ),
      );
    },
  );

  test(
    'Should have correct payload for setupPasscodeInputFieldError',
    () async {
      // Act
      analytics.setupPasscodeInputFieldError();

      // Assert
      verify(
        () => tracker.track(
          AnalyticsEvent(
            screenName: screenName,
            action: AnalyticsAction.input,
            target: ChangePasscodeScreenAnalyticsTarget.setup_passcode.name,
            targetType: AnalyticsTargetType.input_field,
            status: AnalyticsStatus.error,
          ),
        ),
      );
    },
  );

  test(
    'Should have correct payload for repeatPasscodeInputFieldError',
    () async {
      // Act
      analytics.repeatPasscodeInputFieldError();

      // Assert
      verify(
        () => tracker.track(
          AnalyticsEvent(
            screenName: screenName,
            action: AnalyticsAction.input,
            target: ChangePasscodeScreenAnalyticsTarget.repeat_passcode.name,
            targetType: AnalyticsTargetType.input_field,
            status: AnalyticsStatus.error,
          ),
        ),
      );
    },
  );

  test('Should have correct payload for back button click event', () async {
    // Act
    analytics.backButtonPressed();

    // Assert
    verify(
      () => tracker.track(
        const AnalyticsEvent(
          screenName: screenName,
          action: AnalyticsAction.click,
          target: backEvent,
          targetType: AnalyticsTargetType.button,
        ),
      ),
    );
  });
}
