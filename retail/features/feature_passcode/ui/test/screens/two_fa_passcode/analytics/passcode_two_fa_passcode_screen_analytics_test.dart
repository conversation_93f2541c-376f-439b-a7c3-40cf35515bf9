import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_passcode_ui/src/screens/common/error_code_payload.dart';
import 'package:wio_feature_passcode_ui/src/screens/two_fa_passcode/analytics/passcode_two_fa_passcode_screen_analytics.dart';

const screenName = 'passcode_login_screen';
const backEvent = 'back';

void main() {
  late MockAnalyticsAbstractTrackerFactory trackerFactory;
  late MockAnalyticsEventTracker tracker;
  late PasscodeTwoFaPasscodeScreenAnalytics analytics;

  setUp(() {
    final analyticsMocks = getAnalyticsTracker();
    trackerFactory = analyticsMocks.analyticsAbstractTrackerFactory;
    tracker = analyticsMocks.tracker;

    analytics =
        PasscodeTwoFaPasscodeScreenAnalytics(analyticsFactory: trackerFactory);
  });

  test(
    'Should have correct payload for passcodeInputFieldSuccess',
    () async {
      // Act
      analytics.passcodeInputFieldSuccess();

      // Assert
      verify(
        () => tracker.track(
          AnalyticsEvent(
            screenName: screenName,
            action: AnalyticsAction.input,
            target: PasscodeTwoFaPasscodeScreenAnalyticsTarget.passcode.name,
            targetType: AnalyticsTargetType.input_field,
            status: AnalyticsStatus.success,
          ),
        ),
      );
    },
  );

  test(
    'Should have correct payload for passcodeInputFieldError',
    () async {
      // Act
      analytics.passcodeInputFieldError();

      // Assert
      verify(
        () => tracker.track(
          AnalyticsEvent(
            screenName: screenName,
            action: AnalyticsAction.input,
            target: PasscodeTwoFaPasscodeScreenAnalyticsTarget.passcode.name,
            targetType: AnalyticsTargetType.input_field,
            status: AnalyticsStatus.error,
          ),
        ),
      );
    },
  );

  test(
    'Should have correct payload for passcodeBlockedDialog',
    () async {
      // Act
      analytics.passcodeBlockedDialog();

      // Assert
      verify(
        () => tracker.track(
          AnalyticsEvent(
            screenName: screenName,
            action: AnalyticsAction.view,
            target: PasscodeTwoFaPasscodeScreenAnalyticsTarget
                .passcode_blocked_dialog.name,
            targetType: AnalyticsTargetType.popup,
            status: AnalyticsStatus.error,
          ),
        ),
      );
    },
  );

  test(
    'Should have correct payload for passcodeBlockedAnotherTypeDialog',
    () async {
      // Act
      analytics.passcodeBlockedAnotherTypeDialog();

      // Assert
      verify(
        () => tracker.track(
          AnalyticsEvent(
            screenName: screenName,
            action: AnalyticsAction.view,
            target: PasscodeTwoFaPasscodeScreenAnalyticsTarget
                .passcode_blocked_another_dialog.name,
            targetType: AnalyticsTargetType.popup,
            status: AnalyticsStatus.error,
          ),
        ),
      );
    },
  );

  test(
    'Should have correct payload for forgotPasscodeButtonClick',
    () async {
      // Act
      analytics.forgotPasscodeButtonClick();

      // Assert
      verify(
        () => tracker.track(
          AnalyticsEvent(
            screenName: screenName,
            action: AnalyticsAction.click,
            target:
                PasscodeTwoFaPasscodeScreenAnalyticsTarget.forgot_passcode.name,
            targetType: AnalyticsTargetType.button,
          ),
        ),
      );
    },
  );

  test(
    'Should have correct payload for logoutButtonClick',
    () async {
      // Act
      analytics.logoutButtonClick();

      // Assert
      verify(
        () => tracker.track(
          AnalyticsEvent(
            screenName: screenName,
            action: AnalyticsAction.click,
            target: PasscodeTwoFaPasscodeScreenAnalyticsTarget.logout.name,
            targetType: AnalyticsTargetType.button,
          ),
        ),
      );
    },
  );

  test('Should have correct payload for back button click event', () async {
    // Act
    analytics.backButtonPressed();

    // Assert
    verify(
      () => tracker.track(
        const AnalyticsEvent(
          screenName: screenName,
          action: AnalyticsAction.click,
          target: backEvent,
          targetType: AnalyticsTargetType.button,
        ),
      ),
    );
  });

  test('Should track tryNextFactorButtonClick correctly', () async {
    // Act
    analytics.tryNextFactorButtonClick();

    // Assert
    verify(
      () => tracker.track(
        AnalyticsEvent(
          screenName: screenName,
          action: AnalyticsAction.click,
          target: PasscodeTwoFaPasscodeScreenAnalyticsTarget.tryNextFactor.name,
          targetType: AnalyticsTargetType.button,
        ),
      ),
    );
  });

  test('Should track skipButtonClick correctly', () async {
    // Act
    analytics.skipButtonClick();

    // Assert
    verify(
      () => tracker.track(
        AnalyticsEvent(
          screenName: screenName,
          action: AnalyticsAction.click,
          target: PasscodeTwoFaPasscodeScreenAnalyticsTarget.skip.name,
          targetType: AnalyticsTargetType.button,
        ),
      ),
    );
  });

  test('Should track passcodePassFailed correctly', () async {
    const errorCode = 'error_code_example';
    const description = 'error_description_example';

    // Act
    analytics.passcodePassFailed(errorCode, description);

    // Assert
    verify(
      () => tracker.track(
        AnalyticsEvent(
          screenName: screenName,
          action: AnalyticsAction.view,
          target: PasscodeTwoFaPasscodeScreenAnalyticsTarget.passcode.name,
          targetType: AnalyticsTargetType.popup,
          status: AnalyticsStatus.error,
          payload: const ErrorCodePayload(
            errorCode: errorCode,
            description: description,
          ),
        ),
      ),
    );
  });
}
