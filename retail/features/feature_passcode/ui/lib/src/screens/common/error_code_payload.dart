import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/index.dart';

part 'error_code_payload.freezed.dart';

const payloadKeyName = 'error_code';
const payloadKeyDescription = 'description';

@freezed
class ErrorCodePayload
    with _$ErrorCodePayload
    implements AnalyticsEventPayload {
  const factory ErrorCodePayload({
    required String errorCode,
    required String description,
    BiometricStatus? status,
  }) = _ErrorCodePayload;

  const ErrorCodePayload._();

  @override
  Map<String, dynamic> getEventPayload() {
    return <String, String>{
      payloadKeyName: errorCode,
      payloadKeyDescription: description,
    };
  }
}
