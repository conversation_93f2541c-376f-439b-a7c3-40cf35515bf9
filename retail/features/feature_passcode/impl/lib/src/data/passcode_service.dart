import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_retail_dotenv/index.dart';

// ignore_for_file: avoid_types_on_closure_parameters
class Constants {
  static const subscriptionKey = 'Ocp-Apim-Subscription-Key';
  static const secureApiPath = '/retail/identity/secure/api/v1';
}

abstract class PasscodeService {
  Future<void> upsertPasscode({
    required String passcode,
  });
}

class PasscodeServiceImpl extends RestApiService implements PasscodeService {
  final IRestApiClient _httpClient;
  final EnvProvider _envProvider;

  PasscodeServiceImpl(
    this._httpClient,
    this._envProvider,
  );

  @override
  Future<void> upsertPasscode({
    required String passcode,
  }) {
    return execute(
      _httpClient.execute<Object>(
        RestApiRequest(
          '${_envProvider.get(RetailEnvKeys.environmentPrefix) + Constants.secureApiPath}/factors/passcode',
          method: HttpRequestMethod.put,
          headers: <String, String>{
            Constants.subscriptionKey:
                _envProvider.get(RetailEnvKeys.secureApimKey),
          },
          body: {
            'passcode': passcode,
          },
        ),
      ),
      (Object? json) {},
    );
  }
}
