// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sof_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SofState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(List<SofOptionsModel> options,
            Set<int> selectedIds, String otherText, bool isSubmitted)
        loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(List<SofOptionsModel> options, Set<int> selectedIds,
            String otherText, bool isSubmitted)?
        loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(List<SofOptionsModel> options, Set<int> selectedIds,
            String otherText, bool isSubmitted)?
        loaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SofInitialState value) initial,
    required TResult Function(_SofLoadedState value) loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SofInitialState value)? initial,
    TResult? Function(_SofLoadedState value)? loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SofInitialState value)? initial,
    TResult Function(_SofLoadedState value)? loaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SofStateCopyWith<$Res> {
  factory $SofStateCopyWith(SofState value, $Res Function(SofState) then) =
      _$SofStateCopyWithImpl<$Res, SofState>;
}

/// @nodoc
class _$SofStateCopyWithImpl<$Res, $Val extends SofState>
    implements $SofStateCopyWith<$Res> {
  _$SofStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SofState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$SofInitialStateImplCopyWith<$Res> {
  factory _$$SofInitialStateImplCopyWith(_$SofInitialStateImpl value,
          $Res Function(_$SofInitialStateImpl) then) =
      __$$SofInitialStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SofInitialStateImplCopyWithImpl<$Res>
    extends _$SofStateCopyWithImpl<$Res, _$SofInitialStateImpl>
    implements _$$SofInitialStateImplCopyWith<$Res> {
  __$$SofInitialStateImplCopyWithImpl(
      _$SofInitialStateImpl _value, $Res Function(_$SofInitialStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SofState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SofInitialStateImpl implements _SofInitialState {
  const _$SofInitialStateImpl();

  @override
  String toString() {
    return 'SofState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SofInitialStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(List<SofOptionsModel> options,
            Set<int> selectedIds, String otherText, bool isSubmitted)
        loaded,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(List<SofOptionsModel> options, Set<int> selectedIds,
            String otherText, bool isSubmitted)?
        loaded,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(List<SofOptionsModel> options, Set<int> selectedIds,
            String otherText, bool isSubmitted)?
        loaded,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SofInitialState value) initial,
    required TResult Function(_SofLoadedState value) loaded,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SofInitialState value)? initial,
    TResult? Function(_SofLoadedState value)? loaded,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SofInitialState value)? initial,
    TResult Function(_SofLoadedState value)? loaded,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _SofInitialState implements SofState {
  const factory _SofInitialState() = _$SofInitialStateImpl;
}

/// @nodoc
abstract class _$$SofLoadedStateImplCopyWith<$Res> {
  factory _$$SofLoadedStateImplCopyWith(_$SofLoadedStateImpl value,
          $Res Function(_$SofLoadedStateImpl) then) =
      __$$SofLoadedStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<SofOptionsModel> options,
      Set<int> selectedIds,
      String otherText,
      bool isSubmitted});
}

/// @nodoc
class __$$SofLoadedStateImplCopyWithImpl<$Res>
    extends _$SofStateCopyWithImpl<$Res, _$SofLoadedStateImpl>
    implements _$$SofLoadedStateImplCopyWith<$Res> {
  __$$SofLoadedStateImplCopyWithImpl(
      _$SofLoadedStateImpl _value, $Res Function(_$SofLoadedStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SofState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? options = null,
    Object? selectedIds = null,
    Object? otherText = null,
    Object? isSubmitted = null,
  }) {
    return _then(_$SofLoadedStateImpl(
      options: null == options
          ? _value._options
          : options // ignore: cast_nullable_to_non_nullable
              as List<SofOptionsModel>,
      selectedIds: null == selectedIds
          ? _value._selectedIds
          : selectedIds // ignore: cast_nullable_to_non_nullable
              as Set<int>,
      otherText: null == otherText
          ? _value.otherText
          : otherText // ignore: cast_nullable_to_non_nullable
              as String,
      isSubmitted: null == isSubmitted
          ? _value.isSubmitted
          : isSubmitted // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$SofLoadedStateImpl implements _SofLoadedState {
  const _$SofLoadedStateImpl(
      {required final List<SofOptionsModel> options,
      final Set<int> selectedIds = const {},
      this.otherText = '',
      this.isSubmitted = false})
      : _options = options,
        _selectedIds = selectedIds;

  final List<SofOptionsModel> _options;
  @override
  List<SofOptionsModel> get options {
    if (_options is EqualUnmodifiableListView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_options);
  }

  final Set<int> _selectedIds;
  @override
  @JsonKey()
  Set<int> get selectedIds {
    if (_selectedIds is EqualUnmodifiableSetView) return _selectedIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_selectedIds);
  }

  @override
  @JsonKey()
  final String otherText;
  @override
  @JsonKey()
  final bool isSubmitted;

  @override
  String toString() {
    return 'SofState.loaded(options: $options, selectedIds: $selectedIds, otherText: $otherText, isSubmitted: $isSubmitted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SofLoadedStateImpl &&
            const DeepCollectionEquality().equals(other._options, _options) &&
            const DeepCollectionEquality()
                .equals(other._selectedIds, _selectedIds) &&
            (identical(other.otherText, otherText) ||
                other.otherText == otherText) &&
            (identical(other.isSubmitted, isSubmitted) ||
                other.isSubmitted == isSubmitted));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_options),
      const DeepCollectionEquality().hash(_selectedIds),
      otherText,
      isSubmitted);

  /// Create a copy of SofState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SofLoadedStateImplCopyWith<_$SofLoadedStateImpl> get copyWith =>
      __$$SofLoadedStateImplCopyWithImpl<_$SofLoadedStateImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(List<SofOptionsModel> options,
            Set<int> selectedIds, String otherText, bool isSubmitted)
        loaded,
  }) {
    return loaded(options, selectedIds, otherText, isSubmitted);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(List<SofOptionsModel> options, Set<int> selectedIds,
            String otherText, bool isSubmitted)?
        loaded,
  }) {
    return loaded?.call(options, selectedIds, otherText, isSubmitted);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(List<SofOptionsModel> options, Set<int> selectedIds,
            String otherText, bool isSubmitted)?
        loaded,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(options, selectedIds, otherText, isSubmitted);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SofInitialState value) initial,
    required TResult Function(_SofLoadedState value) loaded,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SofInitialState value)? initial,
    TResult? Function(_SofLoadedState value)? loaded,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SofInitialState value)? initial,
    TResult Function(_SofLoadedState value)? loaded,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _SofLoadedState implements SofState {
  const factory _SofLoadedState(
      {required final List<SofOptionsModel> options,
      final Set<int> selectedIds,
      final String otherText,
      final bool isSubmitted}) = _$SofLoadedStateImpl;

  List<SofOptionsModel> get options;
  Set<int> get selectedIds;
  String get otherText;
  bool get isSubmitted;

  /// Create a copy of SofState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SofLoadedStateImplCopyWith<_$SofLoadedStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
