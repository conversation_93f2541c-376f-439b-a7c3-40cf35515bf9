import 'dart:async';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:common_feature_user_financial_details_api/feature_user_financial_details_api.dart';
import 'package:common_wio_feature_kyc_api/config/verification_type.dart';
import 'package:common_wio_feature_kyc_api/domain/passport_verification_flow_service.dart';
import 'package:feature_settings_api/feature_settings_api.dart';
import 'package:feature_settings_api/models/update_tax_info_config.dart';
import 'package:feature_settings_ui/feature_toggle/settings_feature_toggles.dart';
import 'package:feature_settings_ui/l10n/settings_localization.g.dart'; // import 'package:feature_settings_ui/navigation/navigation_handler/liveness_navigation_handler_base.dart';
import 'package:feature_settings_ui/navigation/sof_screen_navigation_config.dart';
import 'package:feature_settings_ui/navigation/update_tax_info_screen_config.dart';
import 'package:feature_settings_ui/screens/personal_details_screen/cubit/personal_details_state.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ui/cubit/base_cubit.dart';
import 'package:ui/cubit/extensions/bloc_extensions.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_feature_address_api/wio_common_feature_address_api.dart';
import 'package:wio_common_feature_customer_address_api/domain/customer_address_interactor.dart';
import 'package:wio_common_feature_customer_address_api/navigation/customer_address_feature_navigation_factory.dart';
import 'package:wio_common_feature_document_upload_api/document_upload_api.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_document_reader_api/domain/enum/document_type.dart';
import 'package:wio_feature_email_api/index.dart';
import 'package:wio_feature_kyc_api/feature_kyc_api.dart';
import 'package:wio_feature_liveness_api/domain/models/liveness_result.dart';
import 'package:wio_feature_liveness_api/navigation/face_recognition_onboarding_navigation_config.dart';
import 'package:wio_feature_liveness_api/navigation/liveness_feature_navigation_config.dart';
import 'package:wio_feature_payments_v2_api/navigation/npss_phone_number_alert_botom_sheet_navigation_config.dart';
import 'package:wio_feature_phone_number_api/navigation/change_phone_number_navigation_config.dart';
import 'package:wio_feature_phone_number_api/navigation/phone_number_feature_navigation_config.dart';
import 'package:wio_feature_user_api/index.dart';

class PersonalDetailsCubit extends BaseCubit<PersonalDetailsState> {
  final KycInteractor _kycInteractor;
  final ToastMessageProvider _toastMessageProvider;
  final CustomerAddressInteractor _customerAddressInteractor;
  final NavigationProvider _navigator;
  final SettingsLocalizations _localizations;
  final CustomerAddressFeatureNavigationFactory _addressNavigationFactory;
  final FeatureToggleProvider _featureToggleProvider;
  final LivenessNavigationHandlerBase _livenessNavigationHandlerBase;
  final PassportVerificationFlowService _passportVerificationFlowService;
  final NPSSInteractor _npssInteractor;
  final UserInteractor _userInteractor;

  PersonalDetailsCubit({
    required KycInteractor kycInteractor,
    required ToastMessageProvider toastMessageProvider,
    required CustomerAddressInteractor customerAddressInteractor,
    required NavigationProvider navigator,
    required SettingsLocalizations localizations,
    required CustomerAddressFeatureNavigationFactory addressNavigationFactory,
    required FeatureToggleProvider featureToggleProvider,
    required LivenessNavigationHandlerBase livenessNavigationHandlerBase,
    required PassportVerificationFlowService passportVerificationFlowService,
    required NPSSInteractor npssInteractor,
    required UserInteractor userInteractor,
  })  : _kycInteractor = kycInteractor,
        _customerAddressInteractor = customerAddressInteractor,
        _toastMessageProvider = toastMessageProvider,
        _navigator = navigator,
        _localizations = localizations,
        _addressNavigationFactory = addressNavigationFactory,
        _featureToggleProvider = featureToggleProvider,
        _livenessNavigationHandlerBase = livenessNavigationHandlerBase,
        _passportVerificationFlowService = passportVerificationFlowService,
        _npssInteractor = npssInteractor,
        _userInteractor = userInteractor,
        super(const PersonalDetailsState());

  bool get showTaxInfo =>
      _featureToggleProvider.get(SettingsFeatureToggle.showTaxInfoSection);

  bool get isChangeDepositEnabled =>
      _featureToggleProvider.get(SettingsFeatureToggles.changeDepositEnabled);

  bool get isChangeWithdrawalEnabled => _featureToggleProvider
      .get(SettingsFeatureToggles.changeWithdrawalEnabled);

  bool get isChangeIncomeEnabled =>
      _featureToggleProvider.get(SettingsFeatureToggles.changeIncomeEnabled);

  bool get isMyDetailsActive =>
      _featureToggleProvider.get(SettingsFeatureToggles.isMyDetailsEnabled);

  bool get isNPSSEnabled =>
      _featureToggleProvider.get(PaymentsFeatureToggle.isNPSSEnabled);

  bool get isFundsAndIncomeEnabled => _featureToggleProvider
      .get(SettingsFeatureToggles.isFundsAndIncomeEnabled);

  bool get isDocumentUpdateEnabled => _featureToggleProvider
      .get(SettingsFeatureToggles.isDocumentUpdateEnabled);

  Future<void> initialize() async {
    _userStream.doOnData((user) {
      final currentUser = state.user;

      safeEmit(state.copyWith(user: user));

      final shouldGetExtraDetails =
          currentUser == null || user.role != currentUser.role;

      if (shouldGetExtraDetails && user.role == UserRole.owner) {
        _getAddress();
        _getCustomerDetails();
        _getEnrollmentStatus();
      }
    }).listenSafe(this);
  }

  void changeEmail() {
    // The result will be gotten from the userInteractor.currentUser directly

    _navigator.navigateTo(
      const EmailFeatureNavigationConfig(
        destination: ChangeEmailNavigationConfig(),
      ),
    );
  }

  Future<void> changePhoneNumber() async {
    if (state.isAaniEnrolled) {
      final confirmed = await _navigator.showBottomSheet(
        NPSSPhoneNumberAlertBotomSheetNavigationConfig(state.phoneNumber),
      );

      if (!(confirmed ?? false)) {
        return;
      }
    }

    // The result will be gotten from the userInteractor.currentUser directly

    await _navigator.navigateTo(
      const PhoneNumberFeatureNavigationConfig(
        destination: ChangePhoneNumberNavigationConfig(),
      ),
    );
  }

  Future<void> changeAddress() async {
    final result = await _navigator.navigateTo(
      _addressNavigationFactory.navigateToCustomerAddressFeature(
        showConfirmActualAddress: true,
      ),
    );

    if (result is AddressSuccessResult) {
      final address = result.addressRecord;
      emit(
        state.copyWith(
          address: _formatAddress(
            buildingName: address.buildingName,
            apartmentNumber: address.apartmentNumber,
            addressStreet: address.addressStreet,
            city: address.city,
          ),
        ),
      );
      _toastMessageProvider.showToastMessage(
        NotificationToastMessageConfiguration.success(
          _localizations.successFetchAddress,
        ),
      );
    }
  }

  Future<void> navigateToSofScreen(
    String employmentType, {
    List<String> selectedCodes = const [],
  }) async {
    final result = await _navigator.push(
      SofScreenNavigationConfig(
        employmentType: employmentType,
        selectedCodes: selectedCodes,
      ),
    );

    if (result is List<SofInfoModel>) {
      safeEmit(state.copyWith(sofList: result));
    }
  }

  Future<void> navigateToUpdateTaxScreen() async {
    final result = await _navigator.push(
      UpdateTaxInfoScreenConfig(
        UpdateTaxInfoConfig(
          crsInfoList: state.crsList ?? [],
        ),
      ),
    );

    if (result is List<CrsInfo>) {
      safeEmit(state.copyWith(crsList: result));
    }
  }

  Future<void> onEditMonthlyDeposits() async {
    final depositChangeResult = await _navigator.navigateTo(
      UserFinancialDetailsFeatureNavigationConfig(
        destination: DepositChangePageNavigationConfig(
          monthlyDeposit: state.monthlyDeposit,
        ),
      ),
    ) as DepositChangeResult?;

    if (depositChangeResult != null) {
      safeEmit(state.copyWith(monthlyDeposit: depositChangeResult.amount));
    }
  }

  Future<void> onEditMonthlyWithdrawals() async {
    final withdrawalChangeResult = await _navigator.navigateTo(
      UserFinancialDetailsFeatureNavigationConfig(
        destination: WithdrawalChangePageNavigationConfig(
          monthlyWithdrawal: state.monthlyWithdrawal,
          percentage: state.cashWithdrawalPercentage,
        ),
      ),
    ) as WithdrawalChangeResult?;

    if (withdrawalChangeResult != null) {
      emit(
        state.copyWith(
          monthlyWithdrawal: withdrawalChangeResult.amount,
          cashWithdrawalPercentage: withdrawalChangeResult.percentage,
        ),
      );
    }
  }

  Future<void> onEditMonthlyIncome() async {
    final incomeChangeResult = await _navigator.navigateTo(
      UserFinancialDetailsFeatureNavigationConfig(
        destination: IncomeChangePageNavigationConfig(
          monthlyIncome: state.monthlyIncome,
        ),
      ),
    ) as double?;

    if (incomeChangeResult != null) {
      safeEmit(state.copyWith(monthlyIncome: incomeChangeResult));
    }
  }

  Future<void> navigateToEmiratesIdVerification() async {
    final completer = Completer<bool>();
    await _navigator.navigateTo(
      KycFeatureNavigationConfig(
        destination: VerifyDocsPageNavigationConfig(
          documentType: DocumentType.emiratesId,
          verificationType: VerificationType.accountMaintenance,
          kycNavigationHandler: _KycSuccessNavigationCallback(
            callback: () async {
              final result = await navigateToLivenessFeature();
              completer.complete(result is LivenessResultSuccess);
            },
          ),
        ),
      ),
    );

    if (!completer.isCompleted) {
      completer.complete(false);
    }

    final livenessSucceeded = await completer.future;
    if (livenessSucceeded) {
      _updateDocumentStatus(KycDocumentType.emiratesId, DocumentStatus.pending);
    }
  }

  Future<void> navigateToPassportUpdate() async {
    final result = await _passportVerificationFlowService.runForceUpdate();
    if (result == PassportVerificationFlowResult.pending) {
      _updateDocumentStatus(KycDocumentType.passport, DocumentStatus.pending);
    }
    if (result == PassportVerificationFlowResult.success) {
      _updateDocumentStatus(KycDocumentType.passport, DocumentStatus.valid);
    }
  }

  Future<LivenessResult> navigateToLivenessFeature() async {
    _navigator
        .navigateTo(
          LivenessFeatureNavigationConfig(
            destination: FaceRecognitionOnboardingNavigationConfig(
              livenessNavigationHandler: _livenessNavigationHandlerBase,
              verificationType: VerificationType.accountMaintenance,
            ),
          ),
        )
        .ignore();

    final result = await _livenessNavigationHandlerBase.getResult();
    _navigator.popUntilFirstRoute();

    return result;
  }

  Future<void> navigateToSignatureUpload() {
    return _navigator.navigateTo(
      DocumentUploadFeatureNavigationConfig(
        app: DocumentUploadSourceApp.retail,
        uploadConfig: DocumentUploadConfig(
          title: _localizations.signatureTitle,
          subtitle: _localizations.signatureSubtitle,
          ctaText: '',
          variant: DocumentUploadVariant.signature,
          successScreenTitle: _localizations.successScreenTitle,
          successScreenSubtitle: _localizations.signatureSuccessText,
          errorScreenTitle: _localizations.generalErrorScreenTitleError,
          errorScreenSubtitle: _localizations.errorScreenDescription,
          statusViewCtaText: _localizations.infoAndTermsBottomSheetCloseButton,
          onSubmit: (modelList) async {
            final model = modelList[0];
            await _kycInteractor.submitSignature(
              documentKey: model.uploadedPath,
              documentName: model.fileName,
              mimeType: model.mimeType,
              documentSide: 'Front',
            );
            _updateDocumentStatus(
              KycDocumentType.signature,
              DocumentStatus.valid,
            );
            safeEmit(state.copyWith(signatureurl: model.uploadedPath));
          },
          fileUploadConfigs: [
            FileUploadConfig(
              uploadListItemTitle: '',
              filePickerTitle: _localizations.filePickerTitle,
              previewUrl: state.signatureurl,
            ),
          ],
        ),
      ),
    );
  }

  // Private

  Stream<User> get _userStream => _userInteractor.currentUser.whereNotNull();

  void _getAddress() => _customerAddressInteractor
          .getAddress()
          .toStream()
          .doOnListen(() => emit(state.copyWith(isAddressLoading: true)))
          .map(
            (address) => state.copyWith(
              isAddressLoading: false,
              address: _formatAddress(
                buildingName: address?.buildingName,
                apartmentNumber: address?.apartmentNumber,
                addressStreet: address?.addressStreet,
                city: address?.city,
              ),
            ),
          )
          .doOnData(safeEmit)
          .withError((e) {
        emit(state.copyWith(isAddressLoading: false));
        _toastMessageProvider.showToastMessage(
          NotificationToastMessageConfiguration.error(
            _localizations.errorFetchAddress,
          ),
        );
      }).complete();

  void _getCustomerDetails() => _kycInteractor
          .getCustomerScreeningInfo()
          .toStream()
          .doOnListen(
            () => emit(state.copyWith(isCustomerDetailsLoading: true)),
          )
          .map(
            (info) => state.copyWith(
              isCustomerDetailsLoading: false,
              monthlyIncome: info.monthlyIncome,
              monthlyDeposit: info.monthlyDeposits,
              monthlyWithdrawal: info.monthlyWithdrawals,
              cashWithdrawalPercentage: info.cashWithdrawalPercentage,
              employmentInfo: info.employmentType,
              sofList: info.sofList,
              crsList: info.crsList,
              kycDocumentsList: info.kycDocumentDetails,
              signatureurl: info.signatureUrl,
            ),
          )
          .doOnData(safeEmit)
          .withError((e) {
        emit(state.copyWith(isCustomerDetailsLoading: false));
        _toastMessageProvider.showToastMessage(
          NotificationToastMessageConfiguration.error(
            _localizations.errorFetchAddress,
          ),
        );
      }).complete();

  void _getEnrollmentStatus() {
    if (isNPSSEnabled) {
      _npssInteractor.enrollmentStatusStream
          .doOnData(
            (data) => safeEmit(
              state.copyWith(isAaniEnrolled: data.status.isEnrolled),
            ),
          )
          .listenSafe(this);
    }
  }

  void _updateDocumentStatus(
    KycDocumentType documentType,
    DocumentStatus newStatus,
  ) {
    final docList = <KycDocumentDetailsModel>[];

    state.kycDocumentsList?.forEach((document) {
      if (document.documentType == documentType) {
        document = document.copyWith(documentStatus: newStatus);
      }
      docList.add(document);
    });
    safeEmit(state.copyWith(kycDocumentsList: docList));
  }

  @override
  String toString() => 'PersonalDetailsCubit';
}

String _formatAddress({
  String? buildingName,
  String? apartmentNumber,
  String? addressStreet,
  String? city,
}) {
  var formattedAddress = '';

  if (buildingName != null) {
    formattedAddress += '$buildingName, ';
  }
  if (apartmentNumber != null) {
    formattedAddress += '$apartmentNumber, ';
  }
  if (addressStreet != null) {
    formattedAddress += '$addressStreet, ';
  }

  if (city != null) {
    formattedAddress += city;
  }

  return formattedAddress.isNotEmpty ? '$formattedAddress.' : formattedAddress;
}

class _KycSuccessNavigationCallback implements KycNavigationHandler {
  final void Function() _callback;

  const _KycSuccessNavigationCallback({
    required void Function() callback,
  }) : _callback = callback;

  @override
  void handleResult() => _callback.call();
}
