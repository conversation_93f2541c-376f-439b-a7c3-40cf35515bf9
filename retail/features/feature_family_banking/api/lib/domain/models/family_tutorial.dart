import 'package:freezed_annotation/freezed_annotation.dart';

part 'family_tutorial.freezed.dart';

@freezed
class FamilyTutorial with _$FamilyTutorial {
  const factory FamilyTutorial({
    required List<FamilyTutorialCard> cards,
  }) = _FamilyTutorial;
}

@freezed
class FamilyTutorialCard with _$FamilyTutorialCard {
  const factory FamilyTutorialCard({
    required String pageTitle,
    required String imageUrl,
    required String title,
    required String description,
  }) = _FamilyTutorialCard;
}

/// Represents the type of the family tutorial
enum FamilyTutorialType { owner, participant }

/// Represents what happened when attempting to show the tutorial to the user.
///
/// This result is used to decide whether the user should proceed, and whether
/// the tutorial was successfully completed or skipped.
///
/// Variants:
///
/// - [completed]:
///   The user completed the tutorial all the way to the end, or skipped.
///   They understood the flow and can move forward in the app.
///
/// - [cancelled]:
///   The user chose to exit the tutorial early, such as by tapping a "Go Back"
///   button. In this case, they should not proceed further without completing
///   the tutorial.
///
/// - [unavailable]:
///   The tutorial couldn’t be shown — for example, due to a backend error, or
///   the feature flag is not enabled. The user didn’t complete the tutorial,
///   but is still allowed to continue.
enum FamilyTutorialResult {
  completed,
  cancelled,
  unavailable,
}
