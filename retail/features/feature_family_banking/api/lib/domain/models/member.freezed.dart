// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'member.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;


final _privateConstructorUsedError = UnsupportedError('It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$Member {

 String? get id => throw _privateConstructorUsedError; String get name => throw _privateConstructorUsedError; MemberRole get role => throw _privateConstructorUsedError; MemberAvatar get avatar => throw _privateConstructorUsedError; List<SharedProduct> get products => throw _privateConstructorUsedError; String? get relationshipToInviter => throw _privateConstructorUsedError;



@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( AcceptedMember value)  accepted,required TResult Function( PendingMember value)  pending,}) => throw _privateConstructorUsedError;
@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( AcceptedMember value)?  accepted,TResult? Function( PendingMember value)?  pending,}) => throw _privateConstructorUsedError;
@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( AcceptedMember value)?  accepted,TResult Function( PendingMember value)?  pending,required TResult orElse(),}) => throw _privateConstructorUsedError;

/// Create a copy of Member
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
$MemberCopyWith<Member> get copyWith => throw _privateConstructorUsedError;

}

/// @nodoc
abstract class $MemberCopyWith<$Res>  {
  factory $MemberCopyWith(Member value, $Res Function(Member) then) = _$MemberCopyWithImpl<$Res, Member>;
@useResult
$Res call({
 String id, String name, MemberRole role, MemberAvatar avatar, List<SharedProduct> products, String? relationshipToInviter
});


$MemberAvatarCopyWith<$Res> get avatar;
}

/// @nodoc
class _$MemberCopyWithImpl<$Res,$Val extends Member> implements $MemberCopyWith<$Res> {
  _$MemberCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

/// Create a copy of Member
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? role = null,Object? avatar = null,Object? products = null,Object? relationshipToInviter = freezed,}) {
  return _then(_value.copyWith(
id: null == id ? _value.id! : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _value.name : name // ignore: cast_nullable_to_non_nullable
as String,role: null == role ? _value.role : role // ignore: cast_nullable_to_non_nullable
as MemberRole,avatar: null == avatar ? _value.avatar : avatar // ignore: cast_nullable_to_non_nullable
as MemberAvatar,products: null == products ? _value.products : products // ignore: cast_nullable_to_non_nullable
as List<SharedProduct>,relationshipToInviter: freezed == relationshipToInviter ? _value.relationshipToInviter : relationshipToInviter // ignore: cast_nullable_to_non_nullable
as String?,
  )as $Val);
}
/// Create a copy of Member
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MemberAvatarCopyWith<$Res> get avatar {
  
  return $MemberAvatarCopyWith<$Res>(_value.avatar, (value) {
    return _then(_value.copyWith(avatar: value) as $Val);
  });
}
}


/// @nodoc
abstract class _$$AcceptedMemberImplCopyWith<$Res> implements $MemberCopyWith<$Res> {
  factory _$$AcceptedMemberImplCopyWith(_$AcceptedMemberImpl value, $Res Function(_$AcceptedMemberImpl) then) = __$$AcceptedMemberImplCopyWithImpl<$Res>;
@override @useResult
$Res call({
 String id, String name, MemberRole role, String mobileNumber, MemberAvatar avatar, List<SharedProduct> products, String? relationshipToInviter
});


@override $MemberAvatarCopyWith<$Res> get avatar;
}

/// @nodoc
class __$$AcceptedMemberImplCopyWithImpl<$Res> extends _$MemberCopyWithImpl<$Res, _$AcceptedMemberImpl> implements _$$AcceptedMemberImplCopyWith<$Res> {
  __$$AcceptedMemberImplCopyWithImpl(_$AcceptedMemberImpl _value, $Res Function(_$AcceptedMemberImpl) _then)
      : super(_value, _then);


/// Create a copy of Member
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? role = null,Object? mobileNumber = null,Object? avatar = null,Object? products = null,Object? relationshipToInviter = freezed,}) {
  return _then(_$AcceptedMemberImpl(
id: null == id ? _value.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _value.name : name // ignore: cast_nullable_to_non_nullable
as String,role: null == role ? _value.role : role // ignore: cast_nullable_to_non_nullable
as MemberRole,mobileNumber: null == mobileNumber ? _value.mobileNumber : mobileNumber // ignore: cast_nullable_to_non_nullable
as String,avatar: null == avatar ? _value.avatar : avatar // ignore: cast_nullable_to_non_nullable
as MemberAvatar,products: null == products ? _value._products : products // ignore: cast_nullable_to_non_nullable
as List<SharedProduct>,relationshipToInviter: freezed == relationshipToInviter ? _value.relationshipToInviter : relationshipToInviter // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class _$AcceptedMemberImpl extends AcceptedMember  {
  const _$AcceptedMemberImpl({required this.id, required this.name, required this.role, required this.mobileNumber, this.avatar = const MemberAvatar.none(), final  List<SharedProduct> products = const [], this.relationshipToInviter}): _products = products,super._();

  

@override final  String id;
@override final  String name;
@override final  MemberRole role;
@override final  String mobileNumber;
@override@JsonKey() final  MemberAvatar avatar;
 final  List<SharedProduct> _products;
@override@JsonKey() List<SharedProduct> get products {
  if (_products is EqualUnmodifiableListView) return _products;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_products);
}

@override final  String? relationshipToInviter;

@override
String toString() {
  return 'Member.accepted(id: $id, name: $name, role: $role, mobileNumber: $mobileNumber, avatar: $avatar, products: $products, relationshipToInviter: $relationshipToInviter)';
}


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _$AcceptedMemberImpl&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.role, role) || other.role == role)&&(identical(other.mobileNumber, mobileNumber) || other.mobileNumber == mobileNumber)&&(identical(other.avatar, avatar) || other.avatar == avatar)&&const DeepCollectionEquality().equals(other._products, _products)&&(identical(other.relationshipToInviter, relationshipToInviter) || other.relationshipToInviter == relationshipToInviter));
}


@override
int get hashCode => Object.hash(runtimeType,id,name,role,mobileNumber,avatar,const DeepCollectionEquality().hash(_products),relationshipToInviter);

/// Create a copy of Member
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@override
@pragma('vm:prefer-inline')
_$$AcceptedMemberImplCopyWith<_$AcceptedMemberImpl> get copyWith => __$$AcceptedMemberImplCopyWithImpl<_$AcceptedMemberImpl>(this, _$identity);




@override
@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( AcceptedMember value)  accepted,required TResult Function( PendingMember value)  pending,}) {
  return accepted(this);
}
@override
@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( AcceptedMember value)?  accepted,TResult? Function( PendingMember value)?  pending,}) {
  return accepted?.call(this);
}
@override
@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( AcceptedMember value)?  accepted,TResult Function( PendingMember value)?  pending,required TResult orElse(),}) {
  if (accepted != null) {
    return accepted(this);
  }
  return orElse();
}

}


abstract class AcceptedMember extends Member {
  const factory AcceptedMember({required final  String id, required final  String name, required final  MemberRole role, required final  String mobileNumber, final  MemberAvatar avatar, final  List<SharedProduct> products, final  String? relationshipToInviter}) = _$AcceptedMemberImpl;
  const AcceptedMember._(): super._();

  

@override String get id;@override String get name;@override MemberRole get role; String get mobileNumber;@override MemberAvatar get avatar;@override List<SharedProduct> get products;@override String? get relationshipToInviter;
/// Create a copy of Member
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
_$$AcceptedMemberImplCopyWith<_$AcceptedMemberImpl> get copyWith => throw _privateConstructorUsedError;

}

/// @nodoc
abstract class _$$PendingMemberImplCopyWith<$Res> implements $MemberCopyWith<$Res> {
  factory _$$PendingMemberImplCopyWith(_$PendingMemberImpl value, $Res Function(_$PendingMemberImpl) then) = __$$PendingMemberImplCopyWithImpl<$Res>;
@override @useResult
$Res call({
 String name, String invitationId, InvitationStatus invitationStatus, MemberRole role, MemberOnboardingStatus onboardingStatus, String phoneNumber, MemberAvatar avatar, List<SharedProduct> products, String? relationshipToInviter, String? id, DateTime? dateOfBirth
});


@override $MemberAvatarCopyWith<$Res> get avatar;
}

/// @nodoc
class __$$PendingMemberImplCopyWithImpl<$Res> extends _$MemberCopyWithImpl<$Res, _$PendingMemberImpl> implements _$$PendingMemberImplCopyWith<$Res> {
  __$$PendingMemberImplCopyWithImpl(_$PendingMemberImpl _value, $Res Function(_$PendingMemberImpl) _then)
      : super(_value, _then);


/// Create a copy of Member
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? invitationId = null,Object? invitationStatus = null,Object? role = null,Object? onboardingStatus = null,Object? phoneNumber = null,Object? avatar = null,Object? products = null,Object? relationshipToInviter = freezed,Object? id = freezed,Object? dateOfBirth = freezed,}) {
  return _then(_$PendingMemberImpl(
name: null == name ? _value.name : name // ignore: cast_nullable_to_non_nullable
as String,invitationId: null == invitationId ? _value.invitationId : invitationId // ignore: cast_nullable_to_non_nullable
as String,invitationStatus: null == invitationStatus ? _value.invitationStatus : invitationStatus // ignore: cast_nullable_to_non_nullable
as InvitationStatus,role: null == role ? _value.role : role // ignore: cast_nullable_to_non_nullable
as MemberRole,onboardingStatus: null == onboardingStatus ? _value.onboardingStatus : onboardingStatus // ignore: cast_nullable_to_non_nullable
as MemberOnboardingStatus,phoneNumber: null == phoneNumber ? _value.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String,avatar: null == avatar ? _value.avatar : avatar // ignore: cast_nullable_to_non_nullable
as MemberAvatar,products: null == products ? _value._products : products // ignore: cast_nullable_to_non_nullable
as List<SharedProduct>,relationshipToInviter: freezed == relationshipToInviter ? _value.relationshipToInviter : relationshipToInviter // ignore: cast_nullable_to_non_nullable
as String?,id: freezed == id ? _value.id : id // ignore: cast_nullable_to_non_nullable
as String?,dateOfBirth: freezed == dateOfBirth ? _value.dateOfBirth : dateOfBirth // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}

/// @nodoc


class _$PendingMemberImpl extends PendingMember  {
  const _$PendingMemberImpl({required this.name, required this.invitationId, required this.invitationStatus, required this.role, required this.onboardingStatus, required this.phoneNumber, this.avatar = const MemberAvatar.none(), final  List<SharedProduct> products = const [], this.relationshipToInviter, this.id, this.dateOfBirth}): _products = products,super._();

  

@override final  String name;
@override final  String invitationId;
@override final  InvitationStatus invitationStatus;
@override final  MemberRole role;
@override final  MemberOnboardingStatus onboardingStatus;
@override final  String phoneNumber;
@override@JsonKey() final  MemberAvatar avatar;
 final  List<SharedProduct> _products;
@override@JsonKey() List<SharedProduct> get products {
  if (_products is EqualUnmodifiableListView) return _products;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_products);
}

@override final  String? relationshipToInviter;
@override final  String? id;
@override final  DateTime? dateOfBirth;

@override
String toString() {
  return 'Member.pending(name: $name, invitationId: $invitationId, invitationStatus: $invitationStatus, role: $role, onboardingStatus: $onboardingStatus, phoneNumber: $phoneNumber, avatar: $avatar, products: $products, relationshipToInviter: $relationshipToInviter, id: $id, dateOfBirth: $dateOfBirth)';
}


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _$PendingMemberImpl&&(identical(other.name, name) || other.name == name)&&(identical(other.invitationId, invitationId) || other.invitationId == invitationId)&&(identical(other.invitationStatus, invitationStatus) || other.invitationStatus == invitationStatus)&&(identical(other.role, role) || other.role == role)&&(identical(other.onboardingStatus, onboardingStatus) || other.onboardingStatus == onboardingStatus)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.avatar, avatar) || other.avatar == avatar)&&const DeepCollectionEquality().equals(other._products, _products)&&(identical(other.relationshipToInviter, relationshipToInviter) || other.relationshipToInviter == relationshipToInviter)&&(identical(other.id, id) || other.id == id)&&(identical(other.dateOfBirth, dateOfBirth) || other.dateOfBirth == dateOfBirth));
}


@override
int get hashCode => Object.hash(runtimeType,name,invitationId,invitationStatus,role,onboardingStatus,phoneNumber,avatar,const DeepCollectionEquality().hash(_products),relationshipToInviter,id,dateOfBirth);

/// Create a copy of Member
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@override
@pragma('vm:prefer-inline')
_$$PendingMemberImplCopyWith<_$PendingMemberImpl> get copyWith => __$$PendingMemberImplCopyWithImpl<_$PendingMemberImpl>(this, _$identity);




@override
@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( AcceptedMember value)  accepted,required TResult Function( PendingMember value)  pending,}) {
  return pending(this);
}
@override
@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( AcceptedMember value)?  accepted,TResult? Function( PendingMember value)?  pending,}) {
  return pending?.call(this);
}
@override
@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( AcceptedMember value)?  accepted,TResult Function( PendingMember value)?  pending,required TResult orElse(),}) {
  if (pending != null) {
    return pending(this);
  }
  return orElse();
}

}


abstract class PendingMember extends Member {
  const factory PendingMember({required final  String name, required final  String invitationId, required final  InvitationStatus invitationStatus, required final  MemberRole role, required final  MemberOnboardingStatus onboardingStatus, required final  String phoneNumber, final  MemberAvatar avatar, final  List<SharedProduct> products, final  String? relationshipToInviter, final  String? id, final  DateTime? dateOfBirth}) = _$PendingMemberImpl;
  const PendingMember._(): super._();

  

@override String get name; String get invitationId; InvitationStatus get invitationStatus;@override MemberRole get role; MemberOnboardingStatus get onboardingStatus; String get phoneNumber;@override MemberAvatar get avatar;@override List<SharedProduct> get products;@override String? get relationshipToInviter;@override String? get id; DateTime? get dateOfBirth;
/// Create a copy of Member
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
_$$PendingMemberImplCopyWith<_$PendingMemberImpl> get copyWith => throw _privateConstructorUsedError;

}

/// @nodoc
mixin _$MemberAvatar {





@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( NoMemberAvatar value)  none,required TResult Function( ImageMemberAvatar value)  image,}) => throw _privateConstructorUsedError;
@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( NoMemberAvatar value)?  none,TResult? Function( ImageMemberAvatar value)?  image,}) => throw _privateConstructorUsedError;
@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( NoMemberAvatar value)?  none,TResult Function( ImageMemberAvatar value)?  image,required TResult orElse(),}) => throw _privateConstructorUsedError;


}

/// @nodoc
abstract class $MemberAvatarCopyWith<$Res>  {
  factory $MemberAvatarCopyWith(MemberAvatar value, $Res Function(MemberAvatar) then) = _$MemberAvatarCopyWithImpl<$Res, MemberAvatar>;



}

/// @nodoc
class _$MemberAvatarCopyWithImpl<$Res,$Val extends MemberAvatar> implements $MemberAvatarCopyWith<$Res> {
  _$MemberAvatarCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

/// Create a copy of MemberAvatar
/// with the given fields replaced by the non-null parameter values.


}


/// @nodoc
abstract class _$$NoMemberAvatarImplCopyWith<$Res>  {
  factory _$$NoMemberAvatarImplCopyWith(_$NoMemberAvatarImpl value, $Res Function(_$NoMemberAvatarImpl) then) = __$$NoMemberAvatarImplCopyWithImpl<$Res>;



}

/// @nodoc
class __$$NoMemberAvatarImplCopyWithImpl<$Res> extends _$MemberAvatarCopyWithImpl<$Res, _$NoMemberAvatarImpl> implements _$$NoMemberAvatarImplCopyWith<$Res> {
  __$$NoMemberAvatarImplCopyWithImpl(_$NoMemberAvatarImpl _value, $Res Function(_$NoMemberAvatarImpl) _then)
      : super(_value, _then);


/// Create a copy of MemberAvatar
/// with the given fields replaced by the non-null parameter values.



}

/// @nodoc


class _$NoMemberAvatarImpl  implements NoMemberAvatar {
  const _$NoMemberAvatarImpl();

  



@override
String toString() {
  return 'MemberAvatar.none()';
}


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _$NoMemberAvatarImpl);
}


@override
int get hashCode => runtimeType.hashCode;





@override
@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( NoMemberAvatar value)  none,required TResult Function( ImageMemberAvatar value)  image,}) {
  return none(this);
}
@override
@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( NoMemberAvatar value)?  none,TResult? Function( ImageMemberAvatar value)?  image,}) {
  return none?.call(this);
}
@override
@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( NoMemberAvatar value)?  none,TResult Function( ImageMemberAvatar value)?  image,required TResult orElse(),}) {
  if (none != null) {
    return none(this);
  }
  return orElse();
}

}


abstract class NoMemberAvatar implements MemberAvatar {
  const factory NoMemberAvatar() = _$NoMemberAvatarImpl;
  

  



}

/// @nodoc
abstract class _$$ImageMemberAvatarImplCopyWith<$Res>  {
  factory _$$ImageMemberAvatarImplCopyWith(_$ImageMemberAvatarImpl value, $Res Function(_$ImageMemberAvatarImpl) then) = __$$ImageMemberAvatarImplCopyWithImpl<$Res>;
@useResult
$Res call({
 String url
});



}

/// @nodoc
class __$$ImageMemberAvatarImplCopyWithImpl<$Res> extends _$MemberAvatarCopyWithImpl<$Res, _$ImageMemberAvatarImpl> implements _$$ImageMemberAvatarImplCopyWith<$Res> {
  __$$ImageMemberAvatarImplCopyWithImpl(_$ImageMemberAvatarImpl _value, $Res Function(_$ImageMemberAvatarImpl) _then)
      : super(_value, _then);


/// Create a copy of MemberAvatar
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? url = null,}) {
  return _then(_$ImageMemberAvatarImpl(
null == url ? _value.url : url // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _$ImageMemberAvatarImpl  implements ImageMemberAvatar {
  const _$ImageMemberAvatarImpl(this.url);

  

@override final  String url;

@override
String toString() {
  return 'MemberAvatar.image(url: $url)';
}


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _$ImageMemberAvatarImpl&&(identical(other.url, url) || other.url == url));
}


@override
int get hashCode => Object.hash(runtimeType,url);

/// Create a copy of MemberAvatar
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@override
@pragma('vm:prefer-inline')
_$$ImageMemberAvatarImplCopyWith<_$ImageMemberAvatarImpl> get copyWith => __$$ImageMemberAvatarImplCopyWithImpl<_$ImageMemberAvatarImpl>(this, _$identity);




@override
@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( NoMemberAvatar value)  none,required TResult Function( ImageMemberAvatar value)  image,}) {
  return image(this);
}
@override
@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( NoMemberAvatar value)?  none,TResult? Function( ImageMemberAvatar value)?  image,}) {
  return image?.call(this);
}
@override
@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( NoMemberAvatar value)?  none,TResult Function( ImageMemberAvatar value)?  image,required TResult orElse(),}) {
  if (image != null) {
    return image(this);
  }
  return orElse();
}

}


abstract class ImageMemberAvatar implements MemberAvatar {
  const factory ImageMemberAvatar(final  String url) = _$ImageMemberAvatarImpl;
  

  

 String get url;
/// Create a copy of MemberAvatar
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
_$$ImageMemberAvatarImplCopyWith<_$ImageMemberAvatarImpl> get copyWith => throw _privateConstructorUsedError;

}

/// @nodoc
mixin _$SharedProduct {

 String? get id => throw _privateConstructorUsedError; String? get name => throw _privateConstructorUsedError;



@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( SharedAccountProduct value)  account,required TResult Function( SharedSavingSpaceProduct value)  savingSpace,required TResult Function( SharedCardProduct value)  card,required TResult Function( SharedFixedSavingSpaceProduct value)  fixedSavingSpace,}) => throw _privateConstructorUsedError;
@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( SharedAccountProduct value)?  account,TResult? Function( SharedSavingSpaceProduct value)?  savingSpace,TResult? Function( SharedCardProduct value)?  card,TResult? Function( SharedFixedSavingSpaceProduct value)?  fixedSavingSpace,}) => throw _privateConstructorUsedError;
@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( SharedAccountProduct value)?  account,TResult Function( SharedSavingSpaceProduct value)?  savingSpace,TResult Function( SharedCardProduct value)?  card,TResult Function( SharedFixedSavingSpaceProduct value)?  fixedSavingSpace,required TResult orElse(),}) => throw _privateConstructorUsedError;

/// Create a copy of SharedProduct
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
$SharedProductCopyWith<SharedProduct> get copyWith => throw _privateConstructorUsedError;

}

/// @nodoc
abstract class $SharedProductCopyWith<$Res>  {
  factory $SharedProductCopyWith(SharedProduct value, $Res Function(SharedProduct) then) = _$SharedProductCopyWithImpl<$Res, SharedProduct>;
@useResult
$Res call({
 String id, String name
});



}

/// @nodoc
class _$SharedProductCopyWithImpl<$Res,$Val extends SharedProduct> implements $SharedProductCopyWith<$Res> {
  _$SharedProductCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

/// Create a copy of SharedProduct
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,}) {
  return _then(_value.copyWith(
id: null == id ? _value.id! : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _value.name! : name // ignore: cast_nullable_to_non_nullable
as String,
  )as $Val);
}

}


/// @nodoc
abstract class _$$SharedAccountProductImplCopyWith<$Res> implements $SharedProductCopyWith<$Res> {
  factory _$$SharedAccountProductImplCopyWith(_$SharedAccountProductImpl value, $Res Function(_$SharedAccountProductImpl) then) = __$$SharedAccountProductImplCopyWithImpl<$Res>;
@override @useResult
$Res call({
 Money balance, String? id, String? name
});



}

/// @nodoc
class __$$SharedAccountProductImplCopyWithImpl<$Res> extends _$SharedProductCopyWithImpl<$Res, _$SharedAccountProductImpl> implements _$$SharedAccountProductImplCopyWith<$Res> {
  __$$SharedAccountProductImplCopyWithImpl(_$SharedAccountProductImpl _value, $Res Function(_$SharedAccountProductImpl) _then)
      : super(_value, _then);


/// Create a copy of SharedProduct
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? balance = null,Object? id = freezed,Object? name = freezed,}) {
  return _then(_$SharedAccountProductImpl(
balance: null == balance ? _value.balance : balance // ignore: cast_nullable_to_non_nullable
as Money,id: freezed == id ? _value.id : id // ignore: cast_nullable_to_non_nullable
as String?,name: freezed == name ? _value.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class _$SharedAccountProductImpl  implements SharedAccountProduct {
  const _$SharedAccountProductImpl({required this.balance, this.id, this.name});

  

@override final  Money balance;
@override final  String? id;
@override final  String? name;

@override
String toString() {
  return 'SharedProduct.account(balance: $balance, id: $id, name: $name)';
}


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _$SharedAccountProductImpl&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name));
}


@override
int get hashCode => Object.hash(runtimeType,balance,id,name);

/// Create a copy of SharedProduct
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@override
@pragma('vm:prefer-inline')
_$$SharedAccountProductImplCopyWith<_$SharedAccountProductImpl> get copyWith => __$$SharedAccountProductImplCopyWithImpl<_$SharedAccountProductImpl>(this, _$identity);




@override
@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( SharedAccountProduct value)  account,required TResult Function( SharedSavingSpaceProduct value)  savingSpace,required TResult Function( SharedCardProduct value)  card,required TResult Function( SharedFixedSavingSpaceProduct value)  fixedSavingSpace,}) {
  return account(this);
}
@override
@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( SharedAccountProduct value)?  account,TResult? Function( SharedSavingSpaceProduct value)?  savingSpace,TResult? Function( SharedCardProduct value)?  card,TResult? Function( SharedFixedSavingSpaceProduct value)?  fixedSavingSpace,}) {
  return account?.call(this);
}
@override
@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( SharedAccountProduct value)?  account,TResult Function( SharedSavingSpaceProduct value)?  savingSpace,TResult Function( SharedCardProduct value)?  card,TResult Function( SharedFixedSavingSpaceProduct value)?  fixedSavingSpace,required TResult orElse(),}) {
  if (account != null) {
    return account(this);
  }
  return orElse();
}

}


abstract class SharedAccountProduct implements SharedProduct {
  const factory SharedAccountProduct({required final  Money balance, final  String? id, final  String? name}) = _$SharedAccountProductImpl;
  

  

 Money get balance;@override String? get id;@override String? get name;
/// Create a copy of SharedProduct
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
_$$SharedAccountProductImplCopyWith<_$SharedAccountProductImpl> get copyWith => throw _privateConstructorUsedError;

}

/// @nodoc
abstract class _$$SharedSavingSpaceProductImplCopyWith<$Res> implements $SharedProductCopyWith<$Res> {
  factory _$$SharedSavingSpaceProductImplCopyWith(_$SharedSavingSpaceProductImpl value, $Res Function(_$SharedSavingSpaceProductImpl) then) = __$$SharedSavingSpaceProductImplCopyWithImpl<$Res>;
@override @useResult
$Res call({
 String id, Money balance, String? name
});



}

/// @nodoc
class __$$SharedSavingSpaceProductImplCopyWithImpl<$Res> extends _$SharedProductCopyWithImpl<$Res, _$SharedSavingSpaceProductImpl> implements _$$SharedSavingSpaceProductImplCopyWith<$Res> {
  __$$SharedSavingSpaceProductImplCopyWithImpl(_$SharedSavingSpaceProductImpl _value, $Res Function(_$SharedSavingSpaceProductImpl) _then)
      : super(_value, _then);


/// Create a copy of SharedProduct
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? balance = null,Object? name = freezed,}) {
  return _then(_$SharedSavingSpaceProductImpl(
id: null == id ? _value.id : id // ignore: cast_nullable_to_non_nullable
as String,balance: null == balance ? _value.balance : balance // ignore: cast_nullable_to_non_nullable
as Money,name: freezed == name ? _value.name : name // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class _$SharedSavingSpaceProductImpl  implements SharedSavingSpaceProduct {
  const _$SharedSavingSpaceProductImpl({required this.id, required this.balance, this.name});

  

@override final  String id;
@override final  Money balance;
@override final  String? name;

@override
String toString() {
  return 'SharedProduct.savingSpace(id: $id, balance: $balance, name: $name)';
}


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _$SharedSavingSpaceProductImpl&&(identical(other.id, id) || other.id == id)&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.name, name) || other.name == name));
}


@override
int get hashCode => Object.hash(runtimeType,id,balance,name);

/// Create a copy of SharedProduct
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@override
@pragma('vm:prefer-inline')
_$$SharedSavingSpaceProductImplCopyWith<_$SharedSavingSpaceProductImpl> get copyWith => __$$SharedSavingSpaceProductImplCopyWithImpl<_$SharedSavingSpaceProductImpl>(this, _$identity);




@override
@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( SharedAccountProduct value)  account,required TResult Function( SharedSavingSpaceProduct value)  savingSpace,required TResult Function( SharedCardProduct value)  card,required TResult Function( SharedFixedSavingSpaceProduct value)  fixedSavingSpace,}) {
  return savingSpace(this);
}
@override
@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( SharedAccountProduct value)?  account,TResult? Function( SharedSavingSpaceProduct value)?  savingSpace,TResult? Function( SharedCardProduct value)?  card,TResult? Function( SharedFixedSavingSpaceProduct value)?  fixedSavingSpace,}) {
  return savingSpace?.call(this);
}
@override
@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( SharedAccountProduct value)?  account,TResult Function( SharedSavingSpaceProduct value)?  savingSpace,TResult Function( SharedCardProduct value)?  card,TResult Function( SharedFixedSavingSpaceProduct value)?  fixedSavingSpace,required TResult orElse(),}) {
  if (savingSpace != null) {
    return savingSpace(this);
  }
  return orElse();
}

}


abstract class SharedSavingSpaceProduct implements SharedProduct {
  const factory SharedSavingSpaceProduct({required final  String id, required final  Money balance, final  String? name}) = _$SharedSavingSpaceProductImpl;
  

  

@override String get id; Money get balance;@override String? get name;
/// Create a copy of SharedProduct
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
_$$SharedSavingSpaceProductImplCopyWith<_$SharedSavingSpaceProductImpl> get copyWith => throw _privateConstructorUsedError;

}

/// @nodoc
abstract class _$$SharedCardProductImplCopyWith<$Res> implements $SharedProductCopyWith<$Res> {
  factory _$$SharedCardProductImplCopyWith(_$SharedCardProductImpl value, $Res Function(_$SharedCardProductImpl) then) = __$$SharedCardProductImplCopyWithImpl<$Res>;
@override @useResult
$Res call({
 String id, String name, String number
});



}

/// @nodoc
class __$$SharedCardProductImplCopyWithImpl<$Res> extends _$SharedProductCopyWithImpl<$Res, _$SharedCardProductImpl> implements _$$SharedCardProductImplCopyWith<$Res> {
  __$$SharedCardProductImplCopyWithImpl(_$SharedCardProductImpl _value, $Res Function(_$SharedCardProductImpl) _then)
      : super(_value, _then);


/// Create a copy of SharedProduct
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? number = null,}) {
  return _then(_$SharedCardProductImpl(
id: null == id ? _value.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _value.name : name // ignore: cast_nullable_to_non_nullable
as String,number: null == number ? _value.number : number // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _$SharedCardProductImpl  implements SharedCardProduct {
  const _$SharedCardProductImpl({required this.id, required this.name, required this.number});

  

@override final  String id;
@override final  String name;
@override final  String number;

@override
String toString() {
  return 'SharedProduct.card(id: $id, name: $name, number: $number)';
}


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _$SharedCardProductImpl&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.number, number) || other.number == number));
}


@override
int get hashCode => Object.hash(runtimeType,id,name,number);

/// Create a copy of SharedProduct
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@override
@pragma('vm:prefer-inline')
_$$SharedCardProductImplCopyWith<_$SharedCardProductImpl> get copyWith => __$$SharedCardProductImplCopyWithImpl<_$SharedCardProductImpl>(this, _$identity);




@override
@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( SharedAccountProduct value)  account,required TResult Function( SharedSavingSpaceProduct value)  savingSpace,required TResult Function( SharedCardProduct value)  card,required TResult Function( SharedFixedSavingSpaceProduct value)  fixedSavingSpace,}) {
  return card(this);
}
@override
@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( SharedAccountProduct value)?  account,TResult? Function( SharedSavingSpaceProduct value)?  savingSpace,TResult? Function( SharedCardProduct value)?  card,TResult? Function( SharedFixedSavingSpaceProduct value)?  fixedSavingSpace,}) {
  return card?.call(this);
}
@override
@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( SharedAccountProduct value)?  account,TResult Function( SharedSavingSpaceProduct value)?  savingSpace,TResult Function( SharedCardProduct value)?  card,TResult Function( SharedFixedSavingSpaceProduct value)?  fixedSavingSpace,required TResult orElse(),}) {
  if (card != null) {
    return card(this);
  }
  return orElse();
}

}


abstract class SharedCardProduct implements SharedProduct {
  const factory SharedCardProduct({required final  String id, required final  String name, required final  String number}) = _$SharedCardProductImpl;
  

  

@override String get id;@override String get name; String get number;
/// Create a copy of SharedProduct
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
_$$SharedCardProductImplCopyWith<_$SharedCardProductImpl> get copyWith => throw _privateConstructorUsedError;

}

/// @nodoc
abstract class _$$SharedFixedSavingSpaceProductImplCopyWith<$Res> implements $SharedProductCopyWith<$Res> {
  factory _$$SharedFixedSavingSpaceProductImplCopyWith(_$SharedFixedSavingSpaceProductImpl value, $Res Function(_$SharedFixedSavingSpaceProductImpl) then) = __$$SharedFixedSavingSpaceProductImplCopyWithImpl<$Res>;
@override @useResult
$Res call({
 Money balance, String id, String name
});



}

/// @nodoc
class __$$SharedFixedSavingSpaceProductImplCopyWithImpl<$Res> extends _$SharedProductCopyWithImpl<$Res, _$SharedFixedSavingSpaceProductImpl> implements _$$SharedFixedSavingSpaceProductImplCopyWith<$Res> {
  __$$SharedFixedSavingSpaceProductImplCopyWithImpl(_$SharedFixedSavingSpaceProductImpl _value, $Res Function(_$SharedFixedSavingSpaceProductImpl) _then)
      : super(_value, _then);


/// Create a copy of SharedProduct
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? balance = null,Object? id = null,Object? name = null,}) {
  return _then(_$SharedFixedSavingSpaceProductImpl(
balance: null == balance ? _value.balance : balance // ignore: cast_nullable_to_non_nullable
as Money,id: null == id ? _value.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _value.name : name // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _$SharedFixedSavingSpaceProductImpl  implements SharedFixedSavingSpaceProduct {
  const _$SharedFixedSavingSpaceProductImpl({required this.balance, required this.id, required this.name});

  

@override final  Money balance;
@override final  String id;
@override final  String name;

@override
String toString() {
  return 'SharedProduct.fixedSavingSpace(balance: $balance, id: $id, name: $name)';
}


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _$SharedFixedSavingSpaceProductImpl&&(identical(other.balance, balance) || other.balance == balance)&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name));
}


@override
int get hashCode => Object.hash(runtimeType,balance,id,name);

/// Create a copy of SharedProduct
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@override
@pragma('vm:prefer-inline')
_$$SharedFixedSavingSpaceProductImplCopyWith<_$SharedFixedSavingSpaceProductImpl> get copyWith => __$$SharedFixedSavingSpaceProductImplCopyWithImpl<_$SharedFixedSavingSpaceProductImpl>(this, _$identity);




@override
@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( SharedAccountProduct value)  account,required TResult Function( SharedSavingSpaceProduct value)  savingSpace,required TResult Function( SharedCardProduct value)  card,required TResult Function( SharedFixedSavingSpaceProduct value)  fixedSavingSpace,}) {
  return fixedSavingSpace(this);
}
@override
@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( SharedAccountProduct value)?  account,TResult? Function( SharedSavingSpaceProduct value)?  savingSpace,TResult? Function( SharedCardProduct value)?  card,TResult? Function( SharedFixedSavingSpaceProduct value)?  fixedSavingSpace,}) {
  return fixedSavingSpace?.call(this);
}
@override
@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( SharedAccountProduct value)?  account,TResult Function( SharedSavingSpaceProduct value)?  savingSpace,TResult Function( SharedCardProduct value)?  card,TResult Function( SharedFixedSavingSpaceProduct value)?  fixedSavingSpace,required TResult orElse(),}) {
  if (fixedSavingSpace != null) {
    return fixedSavingSpace(this);
  }
  return orElse();
}

}


abstract class SharedFixedSavingSpaceProduct implements SharedProduct {
  const factory SharedFixedSavingSpaceProduct({required final  Money balance, required final  String id, required final  String name}) = _$SharedFixedSavingSpaceProductImpl;
  

  

 Money get balance;@override String get id;@override String get name;
/// Create a copy of SharedProduct
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
_$$SharedFixedSavingSpaceProductImplCopyWith<_$SharedFixedSavingSpaceProductImpl> get copyWith => throw _privateConstructorUsedError;

}
