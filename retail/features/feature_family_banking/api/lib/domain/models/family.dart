import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_family_banking_api/domain/models/member.dart';
import 'package:wio_feature_family_banking_api/domain/models/member_role.dart';

part 'family.freezed.dart';

@freezed
class Family with _$Family {
  const Family._();

  const factory Family({
    /// The list of all accepted and pending acceptance family members.
    required List<Member> members,
  }) = _Family;

  /// The list of all members (co-owner and participant),
  /// who have access to shared resources.
  List<AcceptedMember> get acceptedMembers =>
      members.whereType<AcceptedMember>().toList(growable: false);

  /// The list of co-owners.
  List<AcceptedMember> get owners => members
      .whereType<AcceptedMember>()
      .where((it) => it.role == MemberRole.coOwner)
      .toList(growable: false);

  /// The list of participants.
  List<AcceptedMember> get participants => members
      .whereType<AcceptedMember>()
      .where((it) => it.role == MemberRole.participant)
      .toList(growable: false);

  /// The list of possible member, those for whom there are pending invitations.
  List<PendingMember> get pendingMembers =>
      members.whereType<PendingMember>().toList(growable: false);
}
