import 'package:bloc_test/bloc_test.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:mocktail/mocktail.dart';
import 'package:rxdart/rxdart.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/ui_tests.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';
import 'package:wio_feature_family_banking_ui/src/screens/family_hub/family_hub_analytics.dart';
import 'package:wio_feature_family_banking_ui/src/screens/family_hub/family_hub_cubit.dart';
import 'package:wio_feature_family_banking_ui/src/screens/family_hub/family_hub_mediator.dart';
import 'package:wio_feature_family_banking_ui/src/screens/family_hub/family_hub_state.dart';
import 'package:wio_feature_product_hub_api/index.dart';
import 'package:wio_feature_user_api/domain/model/customer_info.dart';
import 'package:wio_feature_user_api/domain/model/user.dart';
import 'package:wio_feature_user_api/domain/user_interactor.dart';

import '../mocks.dart';

void main() {
  late NavigationProvider navigationProvider;
  late UserInteractor userInteractor;
  late FamilyHubAnalytics analytics;
  late FamilyHubMediator mediator;
  late FamilyHubCubit cubit;
  late FeatureToggleProvider featureToggles;

  // Test data
  final user = User(
    id: randomString(),
    customerInfo: const CustomerInfo(firstName: 'Brat', lastName: 'Bratishka'),
  );
  const availableSections = [
    FamilyTab.members,
    FamilyTab.accounts,
    FamilyTab.cards,
    FamilyTab.savingSpaces,
  ];

  FamilyHubCubit getCubit() => FamilyHubCubit(
        userInteractor: userInteractor,
        navigationProvider: navigationProvider,
        analytics: analytics,
        mediator: mediator,
        featureToggles: featureToggles,
        logger: MockLogger(),
      );

  setUp(() {
    userInteractor = MockUserInteractor();
    navigationProvider = MockNavigationProvider();
    analytics = MockFamilyHubAnalytics();
    mediator = MockFamilyHubMediator();
    featureToggles = MockFeatureToggleProvider();
    cubit = getCubit();

    when(
      () => featureToggles.get(
        FamilyBankingFeatureToggles.isFamilyBankingSavingsEnabled,
      ),
    ).thenReturn(true);
  });

  void stubCurrentUser() {
    when(
      () => userInteractor.currentUser,
    ).thenAnswer((_) => Stream.value(user).shareValue());
  }

  group('Initialization >', () {
    blocTest<FamilyHubCubit, FamilyHubState>(
      'initialized properly with current user data',
      // Arrange
      build: () => cubit,
      setUp: () => stubCurrentUser(),

      // Act
      act: (cubit) => cubit.initialize(),

      // Assert
      expect: () => [
        FamilyHubState.idle(
          currentUser: user,
          availableTabs: availableSections,
          selectedTab: FamilyTab.members,
        ),
      ],
    );

    blocTest<FamilyHubCubit, FamilyHubState>(
      'initialized with the provided tab selected',
      // Arrange
      build: () => cubit,
      setUp: () => stubCurrentUser(),

      // Act
      act: (cubit) => cubit.initialize(initialTab: FamilyTab.cards),

      // Assert
      expect: () => [
        FamilyHubState.idle(
          currentUser: user,
          availableTabs: availableSections,
          selectedTab: FamilyTab.cards,
        ),
      ],
    );
  });

  group('Switching tabs >', () {
    final fromTab = FamilyTab.values.first;
    final toTab = FamilyTab.values.last;

    blocTest<FamilyHubCubit, FamilyHubState>(
      'can switch to a tab only if it is available',
      // Arrange
      build: () => cubit,
      seed: () => FamilyHubState.idle(
        currentUser: user,
        availableTabs: FamilyTab.values,
        selectedTab: FamilyTab.values.first,
      ),

      // Act
      act: (cubit) => cubit.onChangeTab(FamilyTab.values.last),

      // Assert
      expect: () => [
        FamilyHubState.idle(
          currentUser: user,
          availableTabs: FamilyTab.values,
          selectedTab: toTab,
        ),
      ],
      verify: (_) {
        verify(
          () => analytics.onTabSelected(
            fromTab: fromTab.name,
            toTab: toTab.name,
          ),
        );
      },
    );

    blocTest<FamilyHubCubit, FamilyHubState>(
      'cannot switch to a tab if it is not available',
      // Arrange
      build: () => cubit,
      seed: () => FamilyHubState.idle(
        currentUser: user,
        availableTabs: [FamilyTab.members, FamilyTab.accounts, FamilyTab.cards],
        selectedTab: FamilyTab.accounts,
      ),

      // Act
      act: (cubit) => cubit.onChangeTab(FamilyTab.savingSpaces),

      // Assert
      expect: () => <FamilyHubState>[
        // no changes
      ],
      verify: (_) => verifyZeroInteractions(analytics),
    );

    blocTest<FamilyHubCubit, FamilyHubState>(
      'switches to tab if appropriate action is received from mediator',
      // Arrange
      build: () => getCubit(),
      seed: () => FamilyHubState.idle(
        currentUser: user,
        availableTabs: FamilyTab.values,
        selectedTab: FamilyTab.accounts,
      ),
      setUp: () {
        when(() => mediator.actionStream).thenAnswer(
          (_) => Stream.value(
            const FamilyHubAction.switchTab(FamilyTab.cards),
          ),
        );
      },

      // Act
      // nothing to do

      // Assert
      expect: () => [
        FamilyHubState.idle(
          currentUser: user,
          availableTabs: FamilyTab.values,
          selectedTab: FamilyTab.cards,
        ),
      ],
    );
  });

  group('Navigation >', () {
    blocTest<FamilyHubCubit, FamilyHubState>(
      'opens product hub',
      // Arrange
      build: () => cubit,

      // Act
      act: (cubit) => cubit.onShowProductHub(),

      // Assert
      verify: (_) => verify(
        () => navigationProvider.navigateTo(
          any(that: isA<ProductHubFeatureNavigationConfig>()),
        ),
      ),
    );
  });
}
