import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/ui_tests.dart';
import 'package:ui/ui.dart';
import 'package:wio_common_feature_tutorial_api/feature_tutorial_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';
import 'package:wio_feature_family_banking_ui/src/navigation/family_banking_flow.dart';
import 'package:wio_feature_family_banking_ui/src/navigation/flow/closure_flow_handler.dart';
import 'package:wio_feature_family_banking_ui/src/navigation/flow/invitation_flow_handler.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late FamilyBankingInteractor familyBankingInteractor;
  late TutorialInteractor tutorialInteractor;
  late InvitationFlowHandler invitationFlowHandler;
  late ReviewFamilyAccountClosureFlowHandler closureFlowHandler;
  late FeatureToggleProvider featureToggles;
  late LoadingProvider loadingProvider;
  late Logger logger;
  late NavigationProvider navigationProvider;

  late FamilyBankingFlow flow;

  setUp(() {
    familyBankingInteractor = MockFamilyBankingInteractor();
    tutorialInteractor = MockTutorialInteractor();
    invitationFlowHandler = MockInvitationFlowHandler();
    closureFlowHandler = MockClosureFlowHandler();
    navigationProvider = MockNavigationProvider();
    loadingProvider = MockLoadingProvider();
    featureToggles = MockFeatureToggleProvider();
    logger = MockLogger();

    flow = FamilyBankingFlowImpl(
      familyBankingInteractor: familyBankingInteractor,
      tutorialInteractor: tutorialInteractor,
      invitationFlowHandler: invitationFlowHandler,
      closureFlowHandler: closureFlowHandler,
      navigationProvider: navigationProvider,
      loadingProvider: loadingProvider,
      featureToggles: featureToggles,
      logger: logger,
    );
  });

  test(
      'shows tutorial before navigating to the Family Banking '
      'if the feature flag is enabled and tutorial is required', () async {
    // Arrange
    when(() => familyBankingInteractor.getFamilyTutorial())
        .justAnswerAsync(TestEntities.randFamilyTutorial());
    when(() => tutorialInteractor.isTutorialRequired(any()))
        .justAnswerAsync(true);
    when(() => navigationProvider.navigateTo(any())).justAnswerAsync(true);
    when(
      () => featureToggles.get(FamilyBankingFeatureToggles.isTutorialEnabled),
    ).thenAnswer((_) => true);

    // Act
    await flow.openFamilyHub();

    // Assert
    verifyInOrder([
      () => familyBankingInteractor.getFamilyTutorial(),
      () => navigationProvider
          .navigateTo(any(that: isA<TutorialFeatureNavigationConfig>())),
      () => navigationProvider.removeStackAndPush(
            any(that: isA<FamilyBankingFeatureNavigationConfig>()),
          ),
    ]);
  });

  test(
      'does not show tutorial before navigating to the Family Banking '
      'if the feature flag is disabled even though the tutorial is required',
      () async {
    // Arrange
    when(() => familyBankingInteractor.getFamilyTutorial())
        .justAnswerAsync(TestEntities.randFamilyTutorial());
    when(() => tutorialInteractor.isTutorialRequired(any()))
        .justAnswerAsync(true);
    when(() => navigationProvider.navigateTo(any())).justAnswerAsync(true);
    when(
      () => featureToggles.get(FamilyBankingFeatureToggles.isTutorialEnabled),
    ).thenAnswer((_) => false);

    // Act
    await flow.openFamilyHub();

    // Assert
    verify(
      () => navigationProvider.removeStackAndPush(
        any(that: isA<FamilyBankingFeatureNavigationConfig>()),
      ),
    ).calledOnce;
    verifyZeroInteractions(familyBankingInteractor);
    verifyNever(
      () => navigationProvider.navigateTo(
        any(that: isA<TutorialFeatureNavigationConfig>()),
      ),
    );
  });

  test(
      'does not show tutorial before navigating to the Family Banking '
      'if the tutorial is not required', () async {
    // Arrange
    when(() => familyBankingInteractor.getFamilyTutorial())
        .justAnswerAsync(TestEntities.randFamilyTutorial());
    when(() => tutorialInteractor.isTutorialRequired(any()))
        .justAnswerAsync(false);
    when(() => navigationProvider.navigateTo(any())).justAnswerAsync(true);
    when(
      () => featureToggles.get(FamilyBankingFeatureToggles.isTutorialEnabled),
    ).thenAnswer((_) => true);

    // Act
    await flow.openFamilyHub();

    // Assert
    verify(
      () => navigationProvider.removeStackAndPush(
        any(that: isA<FamilyBankingFeatureNavigationConfig>()),
      ),
    ).calledOnce;
    verifyZeroInteractions(familyBankingInteractor);
    verifyNever(
      () => navigationProvider.navigateTo(
        any(that: isA<TutorialFeatureNavigationConfig>()),
      ),
    );
  });
}
