import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';
import 'package:wio_feature_family_banking_ui/src/l10n/family_banking_localization.g.dart';
import 'package:wio_feature_family_banking_ui/src/navigation/bottom_sheets/cannot_invite_participant_bottom_sheet_config.dart';

class CannotInviteParticipantBottomSheet extends StatelessWidget {
  final MemberInvitationEligibility invitationEligibility;
  final String inviteeName;

  const CannotInviteParticipantBottomSheet({
    required this.invitationEligibility,
    required this.inviteeName,
    super.key,
  }) : assert(
          invitationEligibility != MemberInvitationEligibility.allowed,
          'Should not show the bottom sheet, the member can be invited.',
        );

  @override
  Widget build(BuildContext context) {
    final l10n = FamilyBankingLocalizations.of(context);

    final canAddAsCoOwner =
        invitationEligibility == MemberInvitationEligibility.alreadyOwner;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Space.fromSpacingVertical(Spacing.s2),
          CompanyIcon(
            CompanyIconModel(
              icon: CompanyPictogramPointer.validation_alert.toGraphicAsset(),
              size: CompanyIconSize.xxxxLarge,
              gradient: CompanyGradientPointer.indigo,
            ),
          ),
          Space.fromSpacingVertical(Spacing.s4),
          Label(
            model: LabelModel(
              text: l10n.cannotInviteParticipantBottomSheetTitle,
              textStyle: CompanyTextStylePointer.h3medium,
              textAlign: LabelTextAlign.center,
              color: CompanyColorPointer.primary3,
            ),
          ),
          Space.fromSpacingVertical(Spacing.s2),
          Label(
            model: LabelModel(
              text: l10n.cannotInviteParticipantBottomSheetDescription(
                invitationEligibility.name,
                inviteeName,
              ),
              textStyle: CompanyTextStylePointer.b2,
              textAlign: LabelTextAlign.center,
              color: CompanyColorPointer.secondary3,
            ),
          ),
          Space.fromSpacingVertical(Spacing.s6),
          if (canAddAsCoOwner) ...[
            Button(
              model: ButtonModel(
                title: l10n
                    .cannotInviteParticipantBottomSheetAddAsCoOwnerButtonTitle,
              ),
              onPressed: () => Navigator.of(context)
                  .pop(CannotInviteParticipantResult.addAsCoOwner),
            ),
            Space.fromSpacingVertical(Spacing.s4),
          ],
          Button(
            model: ButtonModel(
              title: l10n.cannotInviteParticipantBottomSheetGoBackButtonTitle,
              type: ButtonType.secondary,
            ),
            onPressed: () =>
                Navigator.of(context).pop(CannotInviteParticipantResult.goBack),
          ),
        ],
      ),
    );
  }
}
