// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'review_family_account_closure_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;


final _privateConstructorUsedError = UnsupportedError('It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ReviewFamilyAccountClosureState {

 FamilyAccountClosureRequest get request => throw _privateConstructorUsedError;



@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( _Initial value)  initial,required TResult Function( _Approving value)  approving,required TResult Function( _Rejecting value)  rejecting,}) => throw _privateConstructorUsedError;
@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( _Initial value)?  initial,TResult? Function( _Approving value)?  approving,TResult? Function( _Rejecting value)?  rejecting,}) => throw _privateConstructorUsedError;
@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( _Initial value)?  initial,TResult Function( _Approving value)?  approving,TResult Function( _Rejecting value)?  rejecting,required TResult orElse(),}) => throw _privateConstructorUsedError;

/// Create a copy of ReviewFamilyAccountClosureState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
$ReviewFamilyAccountClosureStateCopyWith<ReviewFamilyAccountClosureState> get copyWith => throw _privateConstructorUsedError;

}

/// @nodoc
abstract class $ReviewFamilyAccountClosureStateCopyWith<$Res>  {
  factory $ReviewFamilyAccountClosureStateCopyWith(ReviewFamilyAccountClosureState value, $Res Function(ReviewFamilyAccountClosureState) then) = _$ReviewFamilyAccountClosureStateCopyWithImpl<$Res, ReviewFamilyAccountClosureState>;
@useResult
$Res call({
 FamilyAccountClosureRequest request
});


$FamilyAccountClosureRequestCopyWith<$Res> get request;
}

/// @nodoc
class _$ReviewFamilyAccountClosureStateCopyWithImpl<$Res,$Val extends ReviewFamilyAccountClosureState> implements $ReviewFamilyAccountClosureStateCopyWith<$Res> {
  _$ReviewFamilyAccountClosureStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

/// Create a copy of ReviewFamilyAccountClosureState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? request = null,}) {
  return _then(_value.copyWith(
request: null == request ? _value.request : request // ignore: cast_nullable_to_non_nullable
as FamilyAccountClosureRequest,
  )as $Val);
}
/// Create a copy of ReviewFamilyAccountClosureState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FamilyAccountClosureRequestCopyWith<$Res> get request {
  
  return $FamilyAccountClosureRequestCopyWith<$Res>(_value.request, (value) {
    return _then(_value.copyWith(request: value) as $Val);
  });
}
}


/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> implements $ReviewFamilyAccountClosureStateCopyWith<$Res> {
  factory _$$InitialImplCopyWith(_$InitialImpl value, $Res Function(_$InitialImpl) then) = __$$InitialImplCopyWithImpl<$Res>;
@override @useResult
$Res call({
 FamilyAccountClosureRequest request
});


@override $FamilyAccountClosureRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res> extends _$ReviewFamilyAccountClosureStateCopyWithImpl<$Res, _$InitialImpl> implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(_$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);


/// Create a copy of ReviewFamilyAccountClosureState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? request = null,}) {
  return _then(_$InitialImpl(
request: null == request ? _value.request : request // ignore: cast_nullable_to_non_nullable
as FamilyAccountClosureRequest,
  ));
}


}

/// @nodoc


class _$InitialImpl extends _Initial  {
  const _$InitialImpl({required this.request}): super._();

  

@override final  FamilyAccountClosureRequest request;

@override
String toString() {
  return 'ReviewFamilyAccountClosureState.initial(request: $request)';
}


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _$InitialImpl&&(identical(other.request, request) || other.request == request));
}


@override
int get hashCode => Object.hash(runtimeType,request);

/// Create a copy of ReviewFamilyAccountClosureState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@override
@pragma('vm:prefer-inline')
_$$InitialImplCopyWith<_$InitialImpl> get copyWith => __$$InitialImplCopyWithImpl<_$InitialImpl>(this, _$identity);




@override
@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( _Initial value)  initial,required TResult Function( _Approving value)  approving,required TResult Function( _Rejecting value)  rejecting,}) {
  return initial(this);
}
@override
@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( _Initial value)?  initial,TResult? Function( _Approving value)?  approving,TResult? Function( _Rejecting value)?  rejecting,}) {
  return initial?.call(this);
}
@override
@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( _Initial value)?  initial,TResult Function( _Approving value)?  approving,TResult Function( _Rejecting value)?  rejecting,required TResult orElse(),}) {
  if (initial != null) {
    return initial(this);
  }
  return orElse();
}

}


abstract class _Initial extends ReviewFamilyAccountClosureState {
  const factory _Initial({required final  FamilyAccountClosureRequest request}) = _$InitialImpl;
  const _Initial._(): super._();

  

@override FamilyAccountClosureRequest get request;
/// Create a copy of ReviewFamilyAccountClosureState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
_$$InitialImplCopyWith<_$InitialImpl> get copyWith => throw _privateConstructorUsedError;

}

/// @nodoc
abstract class _$$ApprovingImplCopyWith<$Res> implements $ReviewFamilyAccountClosureStateCopyWith<$Res> {
  factory _$$ApprovingImplCopyWith(_$ApprovingImpl value, $Res Function(_$ApprovingImpl) then) = __$$ApprovingImplCopyWithImpl<$Res>;
@override @useResult
$Res call({
 FamilyAccountClosureRequest request
});


@override $FamilyAccountClosureRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$ApprovingImplCopyWithImpl<$Res> extends _$ReviewFamilyAccountClosureStateCopyWithImpl<$Res, _$ApprovingImpl> implements _$$ApprovingImplCopyWith<$Res> {
  __$$ApprovingImplCopyWithImpl(_$ApprovingImpl _value, $Res Function(_$ApprovingImpl) _then)
      : super(_value, _then);


/// Create a copy of ReviewFamilyAccountClosureState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? request = null,}) {
  return _then(_$ApprovingImpl(
request: null == request ? _value.request : request // ignore: cast_nullable_to_non_nullable
as FamilyAccountClosureRequest,
  ));
}


}

/// @nodoc


class _$ApprovingImpl extends _Approving  {
  const _$ApprovingImpl({required this.request}): super._();

  

@override final  FamilyAccountClosureRequest request;

@override
String toString() {
  return 'ReviewFamilyAccountClosureState.approving(request: $request)';
}


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _$ApprovingImpl&&(identical(other.request, request) || other.request == request));
}


@override
int get hashCode => Object.hash(runtimeType,request);

/// Create a copy of ReviewFamilyAccountClosureState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@override
@pragma('vm:prefer-inline')
_$$ApprovingImplCopyWith<_$ApprovingImpl> get copyWith => __$$ApprovingImplCopyWithImpl<_$ApprovingImpl>(this, _$identity);




@override
@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( _Initial value)  initial,required TResult Function( _Approving value)  approving,required TResult Function( _Rejecting value)  rejecting,}) {
  return approving(this);
}
@override
@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( _Initial value)?  initial,TResult? Function( _Approving value)?  approving,TResult? Function( _Rejecting value)?  rejecting,}) {
  return approving?.call(this);
}
@override
@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( _Initial value)?  initial,TResult Function( _Approving value)?  approving,TResult Function( _Rejecting value)?  rejecting,required TResult orElse(),}) {
  if (approving != null) {
    return approving(this);
  }
  return orElse();
}

}


abstract class _Approving extends ReviewFamilyAccountClosureState {
  const factory _Approving({required final  FamilyAccountClosureRequest request}) = _$ApprovingImpl;
  const _Approving._(): super._();

  

@override FamilyAccountClosureRequest get request;
/// Create a copy of ReviewFamilyAccountClosureState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
_$$ApprovingImplCopyWith<_$ApprovingImpl> get copyWith => throw _privateConstructorUsedError;

}

/// @nodoc
abstract class _$$RejectingImplCopyWith<$Res> implements $ReviewFamilyAccountClosureStateCopyWith<$Res> {
  factory _$$RejectingImplCopyWith(_$RejectingImpl value, $Res Function(_$RejectingImpl) then) = __$$RejectingImplCopyWithImpl<$Res>;
@override @useResult
$Res call({
 FamilyAccountClosureRequest request
});


@override $FamilyAccountClosureRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$RejectingImplCopyWithImpl<$Res> extends _$ReviewFamilyAccountClosureStateCopyWithImpl<$Res, _$RejectingImpl> implements _$$RejectingImplCopyWith<$Res> {
  __$$RejectingImplCopyWithImpl(_$RejectingImpl _value, $Res Function(_$RejectingImpl) _then)
      : super(_value, _then);


/// Create a copy of ReviewFamilyAccountClosureState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? request = null,}) {
  return _then(_$RejectingImpl(
request: null == request ? _value.request : request // ignore: cast_nullable_to_non_nullable
as FamilyAccountClosureRequest,
  ));
}


}

/// @nodoc


class _$RejectingImpl extends _Rejecting  {
  const _$RejectingImpl({required this.request}): super._();

  

@override final  FamilyAccountClosureRequest request;

@override
String toString() {
  return 'ReviewFamilyAccountClosureState.rejecting(request: $request)';
}


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _$RejectingImpl&&(identical(other.request, request) || other.request == request));
}


@override
int get hashCode => Object.hash(runtimeType,request);

/// Create a copy of ReviewFamilyAccountClosureState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@override
@pragma('vm:prefer-inline')
_$$RejectingImplCopyWith<_$RejectingImpl> get copyWith => __$$RejectingImplCopyWithImpl<_$RejectingImpl>(this, _$identity);




@override
@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( _Initial value)  initial,required TResult Function( _Approving value)  approving,required TResult Function( _Rejecting value)  rejecting,}) {
  return rejecting(this);
}
@override
@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( _Initial value)?  initial,TResult? Function( _Approving value)?  approving,TResult? Function( _Rejecting value)?  rejecting,}) {
  return rejecting?.call(this);
}
@override
@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( _Initial value)?  initial,TResult Function( _Approving value)?  approving,TResult Function( _Rejecting value)?  rejecting,required TResult orElse(),}) {
  if (rejecting != null) {
    return rejecting(this);
  }
  return orElse();
}

}


abstract class _Rejecting extends ReviewFamilyAccountClosureState {
  const factory _Rejecting({required final  FamilyAccountClosureRequest request}) = _$RejectingImpl;
  const _Rejecting._(): super._();

  

@override FamilyAccountClosureRequest get request;
/// Create a copy of ReviewFamilyAccountClosureState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
_$$RejectingImplCopyWith<_$RejectingImpl> get copyWith => throw _privateConstructorUsedError;

}
