import 'dart:async';
import 'dart:ui';

import 'package:feature_faq_api/flow/faq_navigation_flow.dart';
import 'package:logging_api/logging.dart';
import 'package:ui/ui.dart';
import 'package:wio_common_feature_context_faq_api/configs/context_faq_tags.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_account_api/domain/account_interactor.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';
import 'package:wio_feature_family_banking_ui/feature_family_banking_mobile_ui.dart';
import 'package:wio_feature_family_banking_ui/src/common/extensions.dart';
import 'package:wio_feature_family_banking_ui/src/common/family_banking_feedback_provider.dart';
import 'package:wio_feature_family_banking_ui/src/common/status_screen_config_factory.dart';
import 'package:wio_feature_family_banking_ui/src/screens/review_family_account_closure/cubit/review_family_account_closure_state.dart';

class ReviewFamilyAccountClosureCubit
    extends BaseCubit<ReviewFamilyAccountClosureState> {
  final AccountInteractor _accountInteractor;
  final FamilyBankingInteractor _familyBankingInteractor;
  final NavigationProvider _navigationProvider;
  final FamilyBankingFeedbackProvider _feedbackProvider;
  final FamilyBankingLocalizations _localizations;
  final FaqNavigationFlow _faqNavigationFlow;
  final Logger _logger;

  ReviewFamilyAccountClosureCubit({
    required ReviewFamilyAccountClosureParams params,
    required AccountInteractor accountInteractor,
    required FamilyBankingInteractor familyBankingInteractor,
    required NavigationProvider navigationProvider,
    required FamilyBankingFeedbackProvider feedbackProvider,
    required FamilyBankingLocalizations localizations,
    required FaqNavigationFlow faqNavigationFlow,
    required Logger logger,
  })  : _accountInteractor = accountInteractor,
        _familyBankingInteractor = familyBankingInteractor,
        _navigationProvider = navigationProvider,
        _feedbackProvider = feedbackProvider,
        _localizations = localizations,
        _faqNavigationFlow = faqNavigationFlow,
        _logger = logger,
        super(
          ReviewFamilyAccountClosureState.initial(request: params.request),
        );

  Future<void> approveRequest() async {
    await _processAction(
      action: FamilyAccountClosureRequestAction.approve,
      onSuccess: () async {
        unawaited(_accountInteractor.refreshAccounts());
        _navigationProvider.goBack();
        await _navigationProvider.navigateTo(
          StatusScreenConfigFactory.success(
            title: _localizations.reviewFamilyAccountClosureSuccessTitle,
            subtitle: _localizations.reviewFamilyAccountClosureSuccessSubtitle,
            description: _localizations.reviewFamilyAccountClosureSuccessDesc
                .prependNewLine(),
            primaryButtonTitle:
                _localizations.reviewFamilyAccountClosureSuccessButtonTitle,
          ),
        );
      },
    );
  }

  Future<void> rejectRequest() async {
    await _processAction(
      action: FamilyAccountClosureRequestAction.reject,
      onSuccess: () {
        _navigationProvider
            .goBack(ReviewFamilyAccountClosureResult.rejectSuccess);
        _feedbackProvider
            .showSuccess(_localizations.reviewFamilyAccountClosureRejectText);
      },
    );
  }

  Future<void> _processAction({
    required FamilyAccountClosureRequestAction action,
    required VoidCallback onSuccess,
  }) async {
    if (state.isProcessing) return;

    try {
      safeEmit(
        action == FamilyAccountClosureRequestAction.approve
            ? ReviewFamilyAccountClosureState.approving(request: state.request)
            : ReviewFamilyAccountClosureState.rejecting(request: state.request),
      );

      await _familyBankingInteractor.updateFamilyAccountClosureRequest(
        requestId: state.request.id,
        action: action,
      );

      safeEmit(ReviewFamilyAccountClosureState.initial(request: state.request));
      onSuccess();
    } on Object catch (error, stackTrace) {
      _handleError(action, error, stackTrace);
    }
  }

  void onFaqPressed() {
    if (state.isProcessing) return;

    _faqNavigationFlow.showContextFaqBottomSheet(
      fromScreen: ReviewFamilyAccountClosureScreenNavigationConfig.screenName,
      tags: [ContextFaqTags.familyAccountClosure],
    );
  }

  void _handleError(
    FamilyAccountClosureRequestAction action,
    Object error,
    StackTrace stackTrace,
  ) {
    _logger.error(
      'Failed to review ${action.name} family account closure request',
      error: error,
      stackTrace: stackTrace,
    );
    _navigationProvider.goBack();
    _feedbackProvider.showFromError(error);
  }

  @override
  String toString() => 'ReviewFamilyAccountClosureCubit';
}
