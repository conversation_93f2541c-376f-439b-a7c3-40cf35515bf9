import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:flutter/material.dart' hide Card;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_feature_card_ui/wio_common_feature_card_ui_for_retail.dart';
import 'package:wio_feature_family_banking_ui/feature_family_banking_mobile_ui.dart';
import 'package:wio_feature_family_banking_ui/src/common/extensions.dart';
import 'package:wio_feature_family_banking_ui/src/screens/family_hub/family_hub_config.dart';
import 'package:wio_feature_family_banking_ui/src/screens/family_hub/tabs/family_cards/family_cards_cubit.dart';
import 'package:wio_feature_family_banking_ui/src/screens/family_hub/tabs/family_cards/family_cards_state.dart';
import 'package:wio_feature_family_banking_ui/src/screens/family_hub/tabs/widgets/empty_tabs_layout_content.dart';
import 'package:wio_feature_family_banking_ui/src/screens/family_hub/tabs/widgets/error_content.dart';
import 'package:wio_feature_family_banking_ui/src/screens/family_hub/tabs/widgets/idle_content.dart';
import 'package:wio_feature_family_banking_ui/src/screens/family_hub/tabs/widgets/section_title.dart';
import 'package:wio_feature_family_scope_ui/feature_family_scope_ui.dart';

part 'widgets/common_views.dart';
part 'widgets/empty_content.dart';
part 'widgets/idle_content.dart';
part 'widgets/loading_content.dart';

const _horizontalCardPadding = 8;
const _totalCardPadding = _horizontalCardPadding * 4;

class FamilyCardsTab extends StatelessWidget {
  const FamilyCardsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const CardThemeProvider(
      child: _CardSectionController(
        child: _FamilyCardsContent(),
      ),
    );
  }
}

class _FamilyCardsContent extends StatelessWidget {
  const _FamilyCardsContent();

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<FamilyCardsCubit>();
    final state = context.watch<FamilyCardsCubit>().state;

    return switch (state) {
      // Initial loading view
      InitialFamilyCardsState() ||
      LoadingFamilyCardsState(cards: null) =>
        const _LoadingContent(),
      // Refreshing from empty state - we want to keep the view the same,
      // just disable the actions.
      LoadingFamilyCardsState(cards: []) => const _EmptyContent(),
      // Refreshing from non-empty state - we keep the same view,
      // just disable the actions.
      final LoadingFamilyCardsState it => _IdleContent.reloading(it),
      FailedFamilyCardsState() => ErrorContent(onRetry: cubit.onRefresh),
      EmptyFamilyCardsState() =>
        _EmptyContent(onButtonPressed: cubit.onCreateNewCard),
      final IdleFamilyCardsState it => _IdleContent.loaded(it, actions: cubit),
    };
  }
}
