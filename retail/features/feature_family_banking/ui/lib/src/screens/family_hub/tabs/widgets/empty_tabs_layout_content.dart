import 'package:flutter/cupertino.dart';
import 'package:wio_feature_family_banking_api/domain/models/walkthrough_video_type.dart';
import 'package:wio_feature_family_banking_ui/src/common/views/walkthrough_banner/walkthrough_banner_factory.dart';
import 'package:wio_feature_family_banking_ui/src/screens/family_hub/tabs/widgets/empty_content.dart';
import 'package:wio_feature_family_banking_ui/src/screens/family_hub/tabs/widgets/idle_content.dart';

class EmptyTabLayoutContent extends StatelessWidget {
  final Widget? icon;
  final String title;
  final String subtitle;
  final Widget? button;
  final bool isWalkthroughBannerVisible;
  final bool sliver;

  const EmptyTabLayoutContent({
    required this.title,
    required this.subtitle,
    this.icon,
    this.button,
    this.isWalkthroughBannerVisible = false,
    this.sliver = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final emptyTextWithCta = EmptyContent(
      image: icon,
      title: title,
      subtitle: subtitle,
      button: Padding(
        padding: const EdgeInsets.only(top: 24.0),
        child: button,
      ),
    );

    final emptyView = isWalkthroughBannerVisible
        ? Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 48.0),
                child: emptyTextWithCta,
              ),
              const WalkthroughBannerFactory(
                padding: EdgeInsets.all(24.0),
                type: WalkthroughVideoType.wioFamilyExplained,
              ),
            ],
          )
        : emptyTextWithCta;

    if (sliver) {
      return SliverToBoxAdapter(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 44),
          child: emptyView,
        ),
      );
    }

    return IdleContent.withAnchor(
      0.1,
      child: emptyView,
    );
  }
}
