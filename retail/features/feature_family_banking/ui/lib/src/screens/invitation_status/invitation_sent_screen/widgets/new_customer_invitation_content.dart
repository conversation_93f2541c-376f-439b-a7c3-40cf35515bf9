import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_family_banking_ui/feature_family_banking_mobile_ui.dart';
import 'package:wio_feature_family_banking_ui/src/common/models/tracker_step.dart';
import 'package:wio_feature_family_banking_ui/src/common/views/invitation_status_screen.dart';

class NewCustomerInvitationContent extends StatelessWidget {
  final String phoneNumber;
  final String name;
  final VoidCallback? onButtonPressed;

  const NewCustomerInvitationContent({
    required this.phoneNumber,
    required this.name,
    this.onButtonPressed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = FamilyBankingLocalizations.of(context);

    return InvitationStatusScreen(
      onPrimaryButtonPressed: onButtonPressed,
      title: l10n.jointAccountInviteSentPageTitle,
      subtitle: l10n.jointAccountInviteSentPageSubtitleNewUser(
        name,
        phoneNumber,
      ),
      buttonTitle: l10n.jointAccountInviteSentPageButtonTitle,
      icon: const GraphicAssetPointer.pictogram(
        CompanyPictogramPointer.functions_message_right,
      ),
      steps: [
        TrackerStep(
          title: l10n.jointAccountInviteSentStep1TitleNewUser,
          subtitle: l10n.jointAccountInviteSentStep1SubtitleNewUser(name),
          state: CompanyTrackerStateEnum.passed,
        ),
        TrackerStep(
          title: l10n.jointAccountInviteSentStep2TitleNewUser(name),
          subtitle: l10n.jointAccountInviteSentStep2SubtitleNewUser,
          state: CompanyTrackerStateEnum.upComing,
        ),
        TrackerStep(
          title: l10n.jointAccountInviteSentStep3TitleNewUser(name),
          subtitle: l10n.jointAccountInviteSentStep3SubtitleNewUser(name),
          state: CompanyTrackerStateEnum.upComing,
        ),
      ],
    );
  }
}
