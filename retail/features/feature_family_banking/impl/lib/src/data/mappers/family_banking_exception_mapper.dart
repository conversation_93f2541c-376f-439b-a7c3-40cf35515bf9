import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';

class FamilyBankingExceptionMapper {
  const FamilyBankingExceptionMapper();

  /// The errors are described here: [link](https://neobankadq-my.sharepoint.com/:x:/g/personal/yboklah_o_wio_io/EdSsFN3yHbNJg-lu6OvM7WcBGpCZ0M975oA5o8_Ak_rzYA?e=v930dS).
  /// The file with error codes: [link](https://dev.azure.com/neobank/neobank-platform/_git/app-referral?path=/api/core-service/src/main/java/io/wio/referral/exception/ErrorCode.java).
  HttpRequestException? mapToInvitationException(ApiException<Object?> error) {
    final code = _mapToErrorCode(error.code);
    if (code == null) {
      return null;
    }

    return MemberInvitationException(
      code: code,
      message: error.message ?? '',
      originalException: error,
      correlationId: error.id,
    );
  }

  MemberInvitationEligibility mapToInvitationEligibility(
    ApiException<Object?> error,
  ) {
    switch (error.code) {
      case 'INVITATION_ERROR_CUSTOMER_WITH_MOBILE_EXISTS':
        final context = error.context;

        if (context is Map<String, dynamic>) {
          final role = context['role'];
          if (role == 'PARTICIPANT') {
            return MemberInvitationEligibility.alreadyParticipant;
          } else if (role == 'OWNER') {
            return MemberInvitationEligibility.alreadyOwner;
          }
        }
    }

    return MemberInvitationEligibility.alreadyOwner;
  }

  MemberInvitationErrorCode? _mapToErrorCode(String code) {
    return switch (code) {
      'INVALID_INVITE_CREATION_REQUEST' =>
        MemberInvitationErrorCode.cannotCreate,
      'INVITATION_EXPIRED' => MemberInvitationErrorCode.expired,
      'INVITATION_ACCEPTANCE_NOT_ALLOWED' =>
        MemberInvitationErrorCode.acceptanceNotAllowed,
      'INVITATION_REJECTION_NOT_ALLOWED' =>
        MemberInvitationErrorCode.rejectionNotAllowed,
      'INVITATION_DOES_NOT_EXIST' => MemberInvitationErrorCode.notFound,
      'SELF_INVITE_NOT_ALLOWED' =>
        MemberInvitationErrorCode.selfInvitationNotAllowed,
      'INVITATION_ACCEPTANCE_DAILY_LIMIT_REACHED' =>
        MemberInvitationErrorCode.acceptanceLimitReached,
      'ACTION_FORBIDDEN' => MemberInvitationErrorCode.actionForbidden,
      'REMOVE_INVITATION_NOT_ALLOWED' =>
        MemberInvitationErrorCode.removalNotAllowed,
      'BE_RETAIL_FAMILY_BANKING_ALLOWED_JOINT_ACCOUNT_COUNT_REACHED' =>
        MemberInvitationErrorCode.approvalNotAllowed,
      _ => null,
    };
  }
}
