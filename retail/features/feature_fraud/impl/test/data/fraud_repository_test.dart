import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_common_feature_transaction_api/model/transaction.dart';
import 'package:wio_feature_fraud_impl/src/data/models/dispute_swagger.swagger.dart'
    as swagger;
import 'package:wio_feature_fraud_impl/src/data/repositories/fraud_repository_impl.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late MockDisputeService mockDisputeService;
  late MockFraudMapper mockFraudMapper;
  late FraudRepositoryImpl repository;

  // Register fallback values for non-nullable custom types.
  setUpAll(() {
    registerFallbackValue(
      const swagger.CreateUserDisputeRequest(
        transactions: <swagger.TransactionDetailsRequest>[],
      ),
    );
  });

  setUp(() {
    mockDisputeService = MockDisputeService();
    mockFraudMapper = MockFraudMapper();
    repository = FraudRepositoryImpl(
      disputeService: mockDisputeService,
      mapper: mockFraudMapper,
    );
  });

  group('getDisputesById', () {
    test(
        '''should return a UserDisputeModel when dispute service returns valid dispute data''',
        () async {
      // Arrange
      const id = 'CR20250221135422825';
      final disputeDto = TestEntities.disputeByIdResponse;
      final expectedUserDispute = TestEntities.userDisputeModel;

      when(() => mockDisputeService.getDisputesById(id))
          .thenAnswer((_) async => disputeDto);
      when(() => mockFraudMapper.mapToUserDisputeModel(disputeDto))
          .thenReturn(expectedUserDispute);

      // Act
      final result = await repository.getDisputesById(id);

      // Assert
      expect(result, expectedUserDispute);
      verify(() => mockDisputeService.getDisputesById(id)).called(1);
      verify(() => mockFraudMapper.mapToUserDisputeModel(disputeDto)).called(1);
    });
  });

  group('reportDisputes', () {
    test(
        '''should return a UserDisputeModel when dispute is reported successfully''',
        () async {
      // Arrange
      // Use a transaction set containing our test transaction.
      final transactions = <Transaction>{TestEntities.transaction};
      final reportTransactions = TestEntities.reportTransactions;
      final transactionDetailsDto = TestEntities.transactionDetailsDto;
      final disputesResponse = TestEntities.disputeByIdResponse;
      final expectedUserDispute = TestEntities.userDisputeModel;

      when(() => mockFraudMapper.mapToReportTransactionsModel(transactions))
          .thenReturn(reportTransactions);
      when(() => mockFraudMapper.mapToTransactionDetailsDto(reportTransactions))
          .thenReturn(transactionDetailsDto);
      when(() => mockDisputeService.reportDisputes(any()))
          .thenAnswer((_) async => disputesResponse);
      when(() => mockFraudMapper.mapToUserDisputeModel(disputesResponse))
          .thenReturn(expectedUserDispute);

      // Act
      final result = await repository.reportDisputes(transactions);

      // Assert
      expect(result, expectedUserDispute);
      verify(() => mockFraudMapper.mapToReportTransactionsModel(transactions))
          .called(1);
      verify(
        () => mockFraudMapper.mapToTransactionDetailsDto(reportTransactions),
      ).called(1);
      verify(
        () => mockDisputeService.reportDisputes(
          any(
            that: isA<swagger.CreateUserDisputeRequest>().having(
              (r) => r.transactions,
              'transactions',
              equals(transactionDetailsDto),
            ),
          ),
        ),
      ).called(1);
      verify(() => mockFraudMapper.mapToUserDisputeModel(disputesResponse))
          .called(1);
    });
  });

  group('getDispute', () {
    test('should return a list of UserDisputeModel', () async {
      // Arrange
      final disputesResponse = TestEntities.disputesResponse;
      final expectedUserDisputes = [TestEntities.userDisputeModel];

      when(() => mockDisputeService.getDispute())
          .thenAnswer((_) async => disputesResponse);
      when(() => mockFraudMapper.mapToUserDisputes(disputesResponse))
          .thenReturn(expectedUserDisputes);

      // Act
      final result = await repository.getDispute();

      // Assert
      expect(result, expectedUserDisputes);
      verify(() => mockDisputeService.getDispute()).called(1);
      verify(() => mockFraudMapper.mapToUserDisputes(disputesResponse))
          .called(1);
    });
  });

  group('getDisputes', () {
    test('should return a list of UserDisputeModel', () async {
      // Arrange
      final disputesResponse = TestEntities.disputesResponse;
      final expectedUserDisputes = [TestEntities.userDisputeModel];

      when(() => mockDisputeService.getDisputes())
          .thenAnswer((_) async => disputesResponse);
      when(() => mockFraudMapper.mapToUserDisputes(disputesResponse))
          .thenReturn(expectedUserDisputes);

      // Act
      final result = await repository.getDisputes();

      // Assert
      expect(result, expectedUserDisputes);
      verify(() => mockDisputeService.getDisputes()).called(1);
      verify(() => mockFraudMapper.mapToUserDisputes(disputesResponse))
          .called(1);
    });
  });
}
