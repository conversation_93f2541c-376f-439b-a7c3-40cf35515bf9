import 'package:mocktail/mocktail.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_user_api/data/user_repository.dart';
import 'package:wio_feature_user_api/domain/model/user.dart';

class MockUserRepository extends Mock implements UserRepository {
  MockUserRepository() {
    registerFallbackValue(UserFake());
  }
}

class UserFake extends Mock implements User {}

class MockKeyValueStorage extends Mock implements KeyValueStorage {}
