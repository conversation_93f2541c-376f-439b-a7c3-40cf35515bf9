import 'dart:ui';

import 'package:feature_required_actions_api/feature_required_actions_api.dart';
import 'package:feature_required_actions_ui/l10n/required_actions_ui_localizations.g.dart';
import 'package:feature_required_actions_ui/src/analytics/required_actions_analytics.dart';
import 'package:feature_required_actions_ui/src/screens/flow/cubit/required_action_flow_cubit.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:ui/ota_lokalisation/ota_lokalization.dart';
import 'package:wio_feature_required_action_common_api/feature_required_action_common.dart';

import '../mocks.dart';

void main() {
  late MockCustomerAddressFeatureNavigationFactory addressNavigationFactory;
  late RequiredActionsLocalizations localizations;
  late MockNavigationProvider navigationProvider;
  late RequiredActionFlowCubit cubit;
  late MockLogger logger;
  late RequiredActionsAnalytics analytics;

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    localizations = await RequiredActionsLocalizations.load(const Locale('en'));
  });

  setUp(() {
    addressNavigationFactory = MockCustomerAddressFeatureNavigationFactory();

    navigationProvider = MockNavigationProvider();
    logger = MockLogger();
    analytics = MockRequiredActionsAnalytics();
  });

  test(
      'flow must include info screen at first step, '
      'success screen at last step on init', () async {
    /// Arrange
    const requiredAction = RequiredAction(
      type: ActionType.kyc,
      screens: [
        ActionScreen.sourceOfFund,
      ],
      skippable: true,
    );
    cubit = RequiredActionFlowCubit(
      requiredAction: requiredAction,
      navigator: navigationProvider,
      localizations: localizations,
      addressNavigationFactory: addressNavigationFactory,
      logger: logger,
      analytics: analytics,
    );

    /// Verify
    expect(cubit.state.screens[0], ActionScreen.info);
    expect(
      cubit.state.screens[cubit.state.screens.length - 1],
      ActionScreen.success,
    );
    expect(
      cubit.state.currentScreenIndex,
      -1,
    );
  });

  test('required actions screens flow pass forward', () async {
    /// Arrange
    const requiredAction = RequiredAction(
      type: ActionType.kyc,
      screens: [
        ActionScreen.sourceOfFund,
        ActionScreen.estimatedDeposit,
        ActionScreen.estimatedWithdrawal,
      ],
      skippable: true,
    );
    cubit = RequiredActionFlowCubit(
      requiredAction: requiredAction,
      navigator: navigationProvider,
      localizations: localizations,
      addressNavigationFactory: addressNavigationFactory,
      logger: logger,
      analytics: analytics,
    );

    cubit.stream.listen((event) {
      final screen = cubit.state.screens[cubit.state.currentScreenIndex];
      cubit.navigateToScreen(screen);
    });

    /// Act
    cubit.moveToNextStep();

    /// Verify
    expect(
      cubit.state.currentScreenIndex,
      0,
    );

    /// Act
    cubit.moveToNextStep();

    /// Verify
    expect(
      cubit.state.currentScreenIndex,
      1,
    );

    /// Act
    cubit.moveToNextStep();

    /// Verify
    expect(
      cubit.state.currentScreenIndex,
      2,
    );

    /// Act
    cubit.decreaseCurrentScreenIndex();

    /// Verify
    expect(
      cubit.state.currentScreenIndex,
      1,
    );

    /// Act
    cubit.moveToNextStep();

    /// Verify
    expect(
      cubit.state.currentScreenIndex,
      2,
    );

    /// Act
    cubit.moveToNextStep();

    /// Verify
    expect(
      cubit.state.currentScreenIndex,
      3,
    );

    /// Act
    cubit.moveToNextStep();

    /// Verify
    expect(
      cubit.state.currentScreenIndex,
      4,
    );

    /// Act
    cubit.moveToNextStep();

    /// Verify
    verify(
      navigationProvider.popUntilFirstRoute,
    ).calledOnce;
  });
}
