import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_onboarding_data_api/models/customer_external_details_secure_specs.swagger.dart';
import 'package:wio_feature_onboarding_data_api/onboarding_secure_data_api.dart'
    as onboarding_specs;
import 'package:wio_retail_dotenv/index.dart';

class Constants {
  static const subscriptionKey = 'Ocp-Apim-Subscription-Key';
  static const secureApiPath = '/retail/customer';
  static const onboardingPersonalDetails =
      '/retail/onboarding/customer/personaldetails';
}

abstract class OccupationService {
  Future<List<onboarding_specs.QuestionOptionsDto>> getOccupationList();

  Future<void> updateCustomerInfo(CustomerInfoUpdateDataDto dto);
}

class OccupationServiceImpl extends RestApiService
    implements OccupationService {
  final IRestApiClient _httpClient;
  final EnvProvider _envProvider;

  OccupationServiceImpl(
    this._httpClient,
    this._envProvider,
  );

  @override
  Future<void> updateCustomerInfo(CustomerInfoUpdateDataDto dto) {
    return execute(
      _httpClient.execute<void>(
        RestApiRequest(
          '${_envProvider.get(RetailEnvKeys.environmentPrefix) + Constants.secureApiPath}/info',
          method: HttpRequestMethod.put,
          body: dto.toJson(),
          headers: <String, String>{
            Constants.subscriptionKey:
                _envProvider.get(RetailEnvKeys.secureApimKey),
          },
        ),
      ),
      (_) {},
    );
  }

  @override
  Future<List<onboarding_specs.QuestionOptionsDto>> getOccupationList() {
    return execute(
      _httpClient.execute<void>(
        RestApiRequest(
          '${_envProvider.get(RetailEnvKeys.environmentPrefix)}${Constants.onboardingPersonalDetails}/employment/options',
          method: HttpRequestMethod.get,
          headers: <String, String>{
            Constants.subscriptionKey:
                _envProvider.get(RetailEnvKeys.secureApimKey),
          },
        ),
      ),
      (json) {
        return (json as List)
            .map(
              (e) => onboarding_specs.QuestionOptionsDto.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList();
      },
    );
  }
}
