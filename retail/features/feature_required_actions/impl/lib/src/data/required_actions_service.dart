import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_onboarding_data_api/models/customer_external_details_secure_specs.swagger.dart';
import 'package:wio_retail_dotenv/index.dart';

class Constants {
  static const subscriptionKey = 'Ocp-Apim-Subscription-Key';
  static const secureApiPath = '/retail/customer';
}

abstract class RequiredActionsService {
  Future<RequiredCustomerActionsResponse> fetchRequiredAction();

  Future<void> skip(String screenName, String version);
}

class RequiredActionsServiceImpl extends RestApiService
    implements RequiredActionsService {
  final IRestApiClient _httpClient;
  final EnvProvider _envProvider;

  RequiredActionsServiceImpl(
    this._httpClient,
    this._envProvider,
  );

  @override
  Future<RequiredCustomerActionsResponse> fetchRequiredAction() {
    return execute(
      _httpClient.execute<Object>(
        RestApiRequest(
          '${_envProvider.get(RetailEnvKeys.environmentPrefix) + Constants.secureApiPath}/actions',
          method: HttpRequestMethod.get,
          headers: <String, String>{
            Constants.subscriptionKey:
                _envProvider.get(RetailEnvKeys.secureApimKey),
          },
        ),
      ),
      (json) => RequiredCustomerActionsResponse.fromJson(
        json as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<void> skip(String screenName, String version) {
    var path = _envProvider.get(RetailEnvKeys.environmentPrefix);
    path += Constants.secureApiPath;
    path += '/screen/$screenName/skip';

    return execute(
      _httpClient.execute<void>(
        RestApiRequest(
          path,
          method: HttpRequestMethod.post,
          queryParameters: {
            'version': version,
          },
          headers: <String, String>{
            Constants.subscriptionKey:
                _envProvider.get(RetailEnvKeys.secureApimKey),
          },
        ),
      ),
      (_) {},
    );
  }
}
