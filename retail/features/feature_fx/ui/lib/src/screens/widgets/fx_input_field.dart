import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

class FXInputField extends StatelessWidget {
  static const _topFxInputKey = 'fx_input_key';

  final String mainText;
  final String leftText;
  final String value;
  final String errorMessage;
  final NumPadEntryCellState cellState;
  final bool isBalanceExceeded;
  final void Function(String)? onTap;
  final VoidCallback? onMainViewPressed;

  const FXInputField({
    required this.mainText,
    required this.leftText,
    required this.value,
    required this.errorMessage,
    required this.cellState,
    required this.isBalanceExceeded,
    this.onTap,
    this.onMainViewPressed,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return isBalanceExceeded
        ? _InputField.error(
            id: _topFxInputKey,
            value: value,
            mainText: mainText,
            cellState: cellState,
            errorMessage: errorMessage,
            onTap: onTap,
            onMainViewPressed: onMainViewPressed,
          )
        : _InputField.idle(
            id: _topFxInputKey,
            value: value,
            mainText: mainText,
            cellState: cellState,
            leftText: leftText,
            onTap: onTap,
            onMainViewPressed: onMainViewPressed,
          );
  }
}

class _InputField extends StatelessWidget {
  final NumpadEntryCellModel model;
  final void Function(String)? onTap;
  final VoidCallback? onMainViewPressed;

  _InputField.idle({
    required String id,
    required String value,
    required String mainText,
    required String leftText,
    required NumPadEntryCellState cellState,
    this.onTap,
    this.onMainViewPressed,
    Key? key,
  })  : model = NumpadEntryCellModel.idle(
          id: id,
          value: value,
          mainText: mainText,
          leftText: leftText,
          cellState: cellState,
          backgroundColor: CompanyColorPointer.background3,
          mainLabelColor: CompanyColorPointer.secondary1,
          leftLabelColor: CompanyColorPointer.secondary1,
          rightLabelColor: CompanyColorPointer.secondary1,
          focusedValueColor: CompanyColorPointer.secondary1,
          unfocusedValueColor: CompanyColorPointer.secondary4,
          indicatorColor: CompanyColorPointer.secondary1,
        ),
        super(key: key);

  // ignore: unused_element
  _InputField.loading({
    required String mainText,
    required String leftText,
    required String id,
    // ignore: unused_element
    this.onTap,
    // ignore: unused_element
    this.onMainViewPressed,
    Key? key,
  })  : model = NumpadEntryCellModel.loading(
          id: id,
          mainText: mainText,
          leftText: leftText,
          shimmerModel: const CompanyShimmerModel(
            baseColor: CompanyColorPointer.background1,
            highlightColor: CompanyColorPointer.secondary6,
          ),
          backgroundColor: CompanyColorPointer.background3,
          mainLabelColor: CompanyColorPointer.secondary1,
          leftLabelColor: CompanyColorPointer.secondary1,
          rightLabelColor: CompanyColorPointer.secondary1,
          focusedValueColor: CompanyColorPointer.secondary1,
          unfocusedValueColor: CompanyColorPointer.secondary4,
          indicatorColor: CompanyColorPointer.secondary1,
        ),
        super(key: key);

  _InputField.error({
    required String id,
    required String value,
    required String errorMessage,
    required String mainText,
    required NumPadEntryCellState cellState,
    this.onTap,
    this.onMainViewPressed,
    Key? key,
  })  : model = NumpadEntryCellModel.error(
          id: id,
          value: value,
          mainText: mainText,
          errorMessage: errorMessage,
          cellState: cellState,
          backgroundColor: CompanyColorPointer.background3,
          mainLabelColor: CompanyColorPointer.secondary1,
          leftLabelColor: CompanyColorPointer.secondary1,
          rightLabelColor: CompanyColorPointer.secondary1,
          focusedValueColor: CompanyColorPointer.secondary7,
          unfocusedValueColor: CompanyColorPointer.secondary7,
          indicatorColor: CompanyColorPointer.secondary1,
        ),
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return NumpadEntryInputCell(
      model: model,
      onTap: onTap,
      onMainViewPressed: onMainViewPressed,
    );
  }
}
