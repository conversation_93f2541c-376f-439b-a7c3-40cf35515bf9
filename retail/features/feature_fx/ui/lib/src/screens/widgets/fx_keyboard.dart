import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

class FX<PERSON>eyboard extends StatelessWidget {
  final ValueChanged<NumpadKey> onTap;

  const FXKeyboard({
    required this.onTap,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.35,
      child: NumpadKeyboard(
        model: const NumpadKeyboardModel(
          colorScheme: NumpadKeyboardColorScheme.transparent,
          leftButton: NumpadKey.point,
          rightButton: NumpadKey.backspace,
        ),
        onTap: onTap,
      ),
    );
  }
}
