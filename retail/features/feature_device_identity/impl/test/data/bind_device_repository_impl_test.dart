import 'package:feature_device_identity_impl/src/data/bind_device_repository_impl.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_feature_identity_data_api/index.dart' as dto;
import 'package:wio_feature_retail_identity_domain_api/index.dart';
import '../feature_device_identity_impl_mocks.dart';

void main() {
  late BindDeviceRepositoryImpl bindDeviceRepository;

  late MockBindDeviceService bindDeviceService;
  late MockIdentityMapper identityMapper;
  late MockSecureStorage secureStorage;
  late MockKeyValueStorage trustedDeviceKeyPairIdStorage;

  setUp(() {
    bindDeviceService = MockBindDeviceService();
    identityMapper = MockIdentityMapper();
    secureStorage = MockSecureStorage();
    trustedDeviceKeyPairIdStorage = MockKeyValueStorage();

    bindDeviceRepository = BindDeviceRepositoryImpl(
      bindDeviceService: bindDeviceService,
      identityMapper: identityMapper,
      secureStorage: secureStorage,
      trustedDeviceKeyPairIdStorage: trustedDeviceKeyPairIdStorage,
    );

    registerFallbackValue(UpsertTrustedDeviceFake());
    registerFallbackValue(UpsertTrustedDeviceDtoFake());
  });

  final mockUpsertTrustedDeviceDto = dto.UpsertTrustedDevice(
    publicKey: 'publicKey',
    keyPairId: 'keyPairId',
    deviceId: 'deviceId',
  );

  const mockUpsertTrustedDeviceDomain = UpsertTrustedDevice(
    deviceId: 'deviceId',
    keyPairId: 'keyPairId',
    publicKey: 'publicKey',
  );

  test('test bindDevice', () async {
    // arrange
    when(() => identityMapper.toUpsertTrustedDeviceDto(
          mockUpsertTrustedDeviceDomain,
        )).thenReturn(mockUpsertTrustedDeviceDto);

    when(() => bindDeviceService.upsertTrustedDevice(
          body: mockUpsertTrustedDeviceDto,
        )).justAnswerAsync(mockUpsertTrustedDeviceDto);

    // act
    await bindDeviceRepository.upsertTrustedDevice(
      upsertTrustedDevice: mockUpsertTrustedDeviceDomain,
    );

    //assert
    verify(() => identityMapper.toUpsertTrustedDeviceDto(any()));
    verify(
      () => bindDeviceService.upsertTrustedDevice(body: any(named: 'body')),
    );
  });

  test('Save trusted device private key', () async {
    // act
    await bindDeviceRepository.saveTrustedDevicePrivateKey(
      privateKey: 'privateKey',
    );

    //assert
    verify(() => secureStorage.put('_trustedDevicePrivateKey', 'privateKey'));
  });

  test('Save trusted device keyPair id', () async {
    // act
    await bindDeviceRepository.saveTrustedDeviceKeyPairId(id: 'id');

    //assert
    verify(() =>
        trustedDeviceKeyPairIdStorage.put('_trustedDeviceKeyPairIdKey', 'id'));
  });

  test('Get trusted device ID', () async {
    // arrange
    when(() => secureStorage.getByKey('_trustedDevicePrivateKey'))
        .justAnswerAsync('privateKey');
    // act
    final result = await bindDeviceRepository.getTrustedDevicePrivateKey();

    //assert
    expect('privateKey', result);
  });

  test('Get trusted device keyPair id', () async {
    // arrange
    when(() => trustedDeviceKeyPairIdStorage.getByKey<String>(
          '_trustedDeviceKeyPairIdKey',
        )).justAnswerAsync('keyPairId');
    // act
    final result = await bindDeviceRepository.getTrustedDeviceKeyPairId();

    //assert
    expect('keyPairId', result);
  });

  test('Unbind device', () async {
    // arrange
    when(() =>
            trustedDeviceKeyPairIdStorage.delete('_trustedDeviceKeyPairIdKey'))
        .justCompleteAsync();
    when(() => trustedDeviceKeyPairIdStorage.delete('_trustedDeviceEmailKey'))
        .justCompleteAsync();
    when(() => secureStorage.delete('_trustedDevicePrivateKey'))
        .justCompleteAsync();

    // act
    await bindDeviceRepository.unbindDevice();

    //assert
    verify(() =>
        trustedDeviceKeyPairIdStorage.delete('_trustedDeviceKeyPairIdKey'));
    verify(
      () => trustedDeviceKeyPairIdStorage.delete('_trustedDeviceEmailKey'),
    );
    verify(() => secureStorage.delete('_trustedDevicePrivateKey'));
  });
}
