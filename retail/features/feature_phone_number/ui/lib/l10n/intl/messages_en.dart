// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.
// @dart=2.12
// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = MessageLookup();

typedef String? MessageIfAbsent(String? messageStr, List<Object>? args);

class MessageLookup extends MessageLookupByLibrary {
  @override
  String get localeName => 'en';

  static m0(mobile) =>
      "Phone number ${mobile} already registered in the system";

  @override
  final Map<String, dynamic> messages =
      _notInlinedMessages(_notInlinedMessages);

  static Map<String, dynamic> _notInlinedMessages(_) => {
        'phoneInputFieldHint':
            MessageLookupByLibrary.simpleMessage('56-000-0000'),
        'phoneInputFieldLengthValidationErrorAE':
            MessageLookupByLibrary.simpleMessage(
                'Phone number must contain 9 digits'),
        'phoneInputFieldStartDigitValidationErrorAE':
            MessageLookupByLibrary.simpleMessage(
                'Phone number must start with 5'),
        'phoneInputFieldValidationError':
            MessageLookupByLibrary.simpleMessage('Invalid phone number'),
        'phoneInputScreenActionButton':
            MessageLookupByLibrary.simpleMessage('Yalla, next'),
        'phoneInputScreenSubTitle': MessageLookupByLibrary.simpleMessage(
            'We’ll send a verification code to this number. '),
        'phoneInputScreenTitle':
            MessageLookupByLibrary.simpleMessage('What’s your phone number?'),
        'phoneNumberAlreadyExist': m0,
        'phoneUpdatedSuccessfully':
            MessageLookupByLibrary.simpleMessage('Phone updated successfully')
      };
}
