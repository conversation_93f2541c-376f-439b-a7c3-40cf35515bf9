// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'referrals_web_home_page_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ReferralsWebHomePageNavigationConfig {
  String get promoCode => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ReferralsWebHomePageNavigationConfigCopyWith<
          ReferralsWebHomePageNavigationConfig>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReferralsWebHomePageNavigationConfigCopyWith<$Res> {
  factory $ReferralsWebHomePageNavigationConfigCopyWith(
          ReferralsWebHomePageNavigationConfig value,
          $Res Function(ReferralsWebHomePageNavigationConfig) then) =
      _$ReferralsWebHomePageNavigationConfigCopyWithImpl<$Res,
          ReferralsWebHomePageNavigationConfig>;
  @useResult
  $Res call({String promoCode});
}

/// @nodoc
class _$ReferralsWebHomePageNavigationConfigCopyWithImpl<$Res,
        $Val extends ReferralsWebHomePageNavigationConfig>
    implements $ReferralsWebHomePageNavigationConfigCopyWith<$Res> {
  _$ReferralsWebHomePageNavigationConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? promoCode = null,
  }) {
    return _then(_value.copyWith(
      promoCode: null == promoCode
          ? _value.promoCode
          : promoCode // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReferralsWebHomePageNavigationConfigImplCopyWith<$Res>
    implements $ReferralsWebHomePageNavigationConfigCopyWith<$Res> {
  factory _$$ReferralsWebHomePageNavigationConfigImplCopyWith(
          _$ReferralsWebHomePageNavigationConfigImpl value,
          $Res Function(_$ReferralsWebHomePageNavigationConfigImpl) then) =
      __$$ReferralsWebHomePageNavigationConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String promoCode});
}

/// @nodoc
class __$$ReferralsWebHomePageNavigationConfigImplCopyWithImpl<$Res>
    extends _$ReferralsWebHomePageNavigationConfigCopyWithImpl<$Res,
        _$ReferralsWebHomePageNavigationConfigImpl>
    implements _$$ReferralsWebHomePageNavigationConfigImplCopyWith<$Res> {
  __$$ReferralsWebHomePageNavigationConfigImplCopyWithImpl(
      _$ReferralsWebHomePageNavigationConfigImpl _value,
      $Res Function(_$ReferralsWebHomePageNavigationConfigImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? promoCode = null,
  }) {
    return _then(_$ReferralsWebHomePageNavigationConfigImpl(
      promoCode: null == promoCode
          ? _value.promoCode
          : promoCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ReferralsWebHomePageNavigationConfigImpl
    extends _ReferralsWebHomePageNavigationConfig {
  const _$ReferralsWebHomePageNavigationConfigImpl({this.promoCode = ''})
      : super._();

  @override
  @JsonKey()
  final String promoCode;

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReferralsWebHomePageNavigationConfigImplCopyWith<
          _$ReferralsWebHomePageNavigationConfigImpl>
      get copyWith => __$$ReferralsWebHomePageNavigationConfigImplCopyWithImpl<
          _$ReferralsWebHomePageNavigationConfigImpl>(this, _$identity);
}

abstract class _ReferralsWebHomePageNavigationConfig
    extends ReferralsWebHomePageNavigationConfig {
  const factory _ReferralsWebHomePageNavigationConfig(
      {final String promoCode}) = _$ReferralsWebHomePageNavigationConfigImpl;
  const _ReferralsWebHomePageNavigationConfig._() : super._();

  @override
  String get promoCode;
  @override
  @JsonKey(ignore: true)
  _$$ReferralsWebHomePageNavigationConfigImplCopyWith<
          _$ReferralsWebHomePageNavigationConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}
