import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_referrals_api/index.dart';

part 'referrals_web_home_page_config.freezed.dart';

@freezed
class ReferralsWebHomePageNavigationConfig extends ScreenNavigationConfig
    with _$ReferralsWebHomePageNavigationConfig {
  static const screenId = 'referrals_ui_desktop_home_screen';

  const factory ReferralsWebHomePageNavigationConfig({
    @Default('') String promoCode,
  }) = _ReferralsWebHomePageNavigationConfig;

  const ReferralsWebHomePageNavigationConfig._()
      : super(
          feature: ReferralFeatureNavigationConfig.name,
          id: screenId,
          isScreenTracking: false,
        );

  @override
  String toString() => 'ReferralsWebHomePageNavigationConfig';
}
