// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'referral_dashboard_screen_navigation_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ReferralDashboardScreenNavigationConfig {}

/// @nodoc
abstract class $ReferralDashboardScreenNavigationConfigCopyWith<$Res> {
  factory $ReferralDashboardScreenNavigationConfigCopyWith(
          ReferralDashboardScreenNavigationConfig value,
          $Res Function(ReferralDashboardScreenNavigationConfig) then) =
      _$ReferralDashboardScreenNavigationConfigCopyWithImpl<$Res,
          ReferralDashboardScreenNavigationConfig>;
}

/// @nodoc
class _$ReferralDashboardScreenNavigationConfigCopyWithImpl<$Res,
        $Val extends ReferralDashboardScreenNavigationConfig>
    implements $ReferralDashboardScreenNavigationConfigCopyWith<$Res> {
  _$ReferralDashboardScreenNavigationConfigCopyWithImpl(
      this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_ReferralDashboardScreenNavigationConfigCopyWith<$Res> {
  factory _$$_ReferralDashboardScreenNavigationConfigCopyWith(
          _$_ReferralDashboardScreenNavigationConfig value,
          $Res Function(_$_ReferralDashboardScreenNavigationConfig) then) =
      __$$_ReferralDashboardScreenNavigationConfigCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ReferralDashboardScreenNavigationConfigCopyWithImpl<$Res>
    extends _$ReferralDashboardScreenNavigationConfigCopyWithImpl<$Res,
        _$_ReferralDashboardScreenNavigationConfig>
    implements _$$_ReferralDashboardScreenNavigationConfigCopyWith<$Res> {
  __$$_ReferralDashboardScreenNavigationConfigCopyWithImpl(
      _$_ReferralDashboardScreenNavigationConfig _value,
      $Res Function(_$_ReferralDashboardScreenNavigationConfig) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ReferralDashboardScreenNavigationConfig
    extends _ReferralDashboardScreenNavigationConfig {
  const _$_ReferralDashboardScreenNavigationConfig() : super._();

  @override
  String toString() {
    return 'ReferralDashboardScreenNavigationConfig()';
  }
}

abstract class _ReferralDashboardScreenNavigationConfig
    extends ReferralDashboardScreenNavigationConfig {
  const factory _ReferralDashboardScreenNavigationConfig() =
      _$_ReferralDashboardScreenNavigationConfig;
  const _ReferralDashboardScreenNavigationConfig._() : super._();
}
