import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/ui_tests.dart';
import 'package:wio_feature_kyc_api/feature_kyc_api.dart';
import 'package:wio_feature_kyc_ui/src/handlers/rfi_handler_impl.dart';

void main() {
  late MockNavigationProvider mockNavigationProvider;
  late MockLogger mockLogger;
  late RfiHandlerImpl rfiHandler;

  setUp(() {
    mockNavigationProvider = MockNavigationProvider();
    mockLogger = MockLogger();
    rfiHandler = RfiHandlerImpl(
      navigationProvider: mockNavigationProvider,
      logger: mockLogger,
    );
  });

  group('RfiHandlerImpl', () {
    test('''
should navigate to RfiListScreenNavigationConfig when rfiList has more than one item''',
        () async {
      // Arrange
      const rfiList = [
        RfiModel(
          id: '1',
          title: 'title',
          requests: [
            InformationRequest(question: 'question', questionId: 'id'),
          ],
        ),
        RfiModel(
          id: '2',
          title: 'title 2',
          requests: [
            InformationRequest(question: 'question', questionId: 'id'),
          ],
        ),
      ];
      const expectedResult = [
        RfiModel(
          id: '1',
          title: 'title',
          requests: [
            InformationRequest(question: 'question', questionId: 'id'),
          ],
        ),
        RfiModel(
          id: '2',
          title: 'title 2',
          requests: [
            InformationRequest(question: 'question', questionId: 'id'),
          ],
        ),
      ];

      when(() => mockNavigationProvider.navigateTo(any()))
          .thenAnswer((_) async => expectedResult);

      // Act
      final result = await rfiHandler.handleRfi(rfiList);

      // Assert
      expect(result, expectedResult);
      verify(
        () => mockNavigationProvider.navigateTo(
          const KycFeatureNavigationConfig(
            destination: RfiListScreenNavigationConfig(rfiList: rfiList),
          ),
        ),
      ).called(1);
      verifyNever(
        () => mockLogger.error(
          any(),
          error: any(named: 'error'),
          stackTrace: any(named: 'stackTrace'),
        ),
      );
    });

    test('''
should navigate to RfiSubmitScreenNavigationConfig when rfiList has exactly one item''',
        () async {
      // Arrange
      const rfiList = [
        RfiModel(
          id: '1',
          title: 'title',
          requests: [
            InformationRequest(question: 'question', questionId: 'id'),
          ],
        ),
      ];
      const expectedResult = [
        RfiModel(
          id: '1',
          title: 'title',
          requests: [
            InformationRequest(question: 'question', questionId: 'id'),
          ],
          submitted: true,
        ),
      ];

      when(() => mockNavigationProvider.navigateTo(any()))
          .thenAnswer((_) async => expectedResult.first);

      // Act
      final result = await rfiHandler.handleRfi(rfiList);

      // Assert
      expect(result, expectedResult);
      verify(
        () => mockNavigationProvider.navigateTo(
          KycFeatureNavigationConfig(
            destination: RfiSubmitScreenNavigationConfig(model: rfiList.first),
          ),
        ),
      ).called(1);
      verifyNever(
        () => mockLogger.error(
          any(),
          error: any(named: 'error'),
          stackTrace: any(named: 'stackTrace'),
        ),
      );
    });
  });
}
