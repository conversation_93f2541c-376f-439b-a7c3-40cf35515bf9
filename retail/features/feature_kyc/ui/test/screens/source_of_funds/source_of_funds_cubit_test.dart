import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/ui_tests.dart';
import 'package:wio_common_file_picker_ui/file_picker_ui.dart';
import 'package:wio_feature_kyc_api/feature_kyc_api.dart';
import 'package:wio_feature_kyc_ui/src/navigation/file_picker_bottom_sheet_config.dart';
import 'package:wio_feature_kyc_ui/src/screens/source_of_funds/cubit/source_of_funds_cubit.dart';
import 'package:wio_feature_kyc_ui/src/screens/source_of_funds/cubit/source_of_funds_state.dart';

import '../../feature_kyc_ui_mocks.dart';

void main() {
  late SourceOfFundsCubit cubit;
  late MockSourceOfFundsAnalytics analytics;

  late MockNavigationProvider navigationProvider;
  late MockCommonErrorHandler commonErrorHandler;
  late MockKycInteractor interactor;

  setUp(() {
    navigationProvider = MockNavigationProvider();
    analytics = MockSourceOfFundsAnalytics();
    commonErrorHandler = MockCommonErrorHandler();
    interactor = MockKycInteractor();

    cubit = SourceOfFundsCubit(
      navigator: navigationProvider,
      analytics: analytics,
      commonErrorHandler: commonErrorHandler,
      logger: MockLogger(),
      kycInteractor: interactor,
    );
  });

  final mockFile = MockFile('/test.pdf');

  test('onUploadDocumentClick with success result', () async {
    // arrange
    when(() => navigationProvider.showBottomSheet(
          const FilePickerBottomSheetConfig(
            type: FilePickerType.sourceOfFunds,
          ),
        )).justAnswerAsync(FilePickerBottomSheetResult.success(
      file: mockFile,
      option: FilePickerOption.gallery,
    ));
    // act
    await cubit.onUploadDocumentClick();
    await cubit.onUploadDocumentClick();

    //assert
    expect(
      cubit.state,
      SourceOfFundsState(
        files: [
          FileModel(
            type: UploadDocumentType.pdf,
            fileData: mockFile.readAsBytesSync(),
            fileName: 'test.pdf',
          ),
          FileModel(
            type: UploadDocumentType.pdf,
            fileData: mockFile.readAsBytesSync(),
            fileName: 'test.pdf',
          ),
        ],
      ),
    );
  });

  test('check delete item from state', () async {
    // arrange
    when(() => navigationProvider.showBottomSheet(
          const FilePickerBottomSheetConfig(
            type: FilePickerType.sourceOfFunds,
          ),
        )).justAnswerAsync(FilePickerBottomSheetResult.success(
      file: mockFile,
      option: FilePickerOption.gallery,
    ));
    // act
    await cubit.onUploadDocumentClick();
    await cubit.onUploadDocumentClick();
    cubit.onDeleteButtonClicked(0);
    //assert
    expect(
      cubit.state,
      SourceOfFundsState(
        files: [
          FileModel(
            type: UploadDocumentType.pdf,
            fileData: mockFile.readAsBytesSync(),
            fileName: 'test.pdf',
          ),
        ],
      ),
    );
  });

  test('onContinueButtonClick', () async {
    // arrange
    when(() => interactor.uploadSourceOfFunds(files: any(named: 'files')))
        .justCompleteAsync();
    when(() => navigationProvider.showBottomSheet(
          const FilePickerBottomSheetConfig(
            type: FilePickerType.sourceOfFunds,
          ),
        )).justAnswerAsync(FilePickerBottomSheetResult.success(
      file: mockFile,
      option: FilePickerOption.gallery,
    ));

    // act
    await cubit.onUploadDocumentClick();
    cubit.onContinueButtonClick();
    await flushFutures();

    //assert
    verify(() => interactor.uploadSourceOfFunds(files: [
          FileModel(
            type: UploadDocumentType.pdf,
            fileData: mockFile.readAsBytesSync(),
            fileName: 'test.pdf',
          ),
        ]));
    verify(() => navigationProvider.goBack());
  });
}
