import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_kyc_api/feature_kyc_api.dart';
import 'package:wio_feature_kyc_api/model/rfi_model.dart';

part 'rfi_list_state.freezed.dart';

@freezed
class RfiListState with _$RfiListState {
  const RfiListState._();

  const factory RfiListState({
    required List<RfiModel> items,
    required String userName,
    required int completedCount,
  }) = _RfiListState;

  factory RfiListState.initial({
    required List<RfiModel> rfiList,
    required String? userName,
  }) =>
      RfiListState(
        userName: userName ?? '',
        completedCount: rfiList.fold(0, (acc, model) {
          if (model.submitted) {
            acc++;
          }

          return acc;
        }),
        items: rfiList,
      );
}
