// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rfi_list_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RfiListState {
  List<RfiModel> get items => throw _privateConstructorUsedError;
  String get userName => throw _privateConstructorUsedError;
  int get completedCount => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RfiListStateCopyWith<RfiListState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RfiListStateCopyWith<$Res> {
  factory $RfiListStateCopyWith(
          RfiListState value, $Res Function(RfiListState) then) =
      _$RfiListStateCopyWithImpl<$Res, RfiListState>;
  @useResult
  $Res call({List<RfiModel> items, String userName, int completedCount});
}

/// @nodoc
class _$RfiListStateCopyWithImpl<$Res, $Val extends RfiListState>
    implements $RfiListStateCopyWith<$Res> {
  _$RfiListStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? userName = null,
    Object? completedCount = null,
  }) {
    return _then(_value.copyWith(
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<RfiModel>,
      userName: null == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String,
      completedCount: null == completedCount
          ? _value.completedCount
          : completedCount // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RfiListStateImplCopyWith<$Res>
    implements $RfiListStateCopyWith<$Res> {
  factory _$$RfiListStateImplCopyWith(
          _$RfiListStateImpl value, $Res Function(_$RfiListStateImpl) then) =
      __$$RfiListStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<RfiModel> items, String userName, int completedCount});
}

/// @nodoc
class __$$RfiListStateImplCopyWithImpl<$Res>
    extends _$RfiListStateCopyWithImpl<$Res, _$RfiListStateImpl>
    implements _$$RfiListStateImplCopyWith<$Res> {
  __$$RfiListStateImplCopyWithImpl(
      _$RfiListStateImpl _value, $Res Function(_$RfiListStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? userName = null,
    Object? completedCount = null,
  }) {
    return _then(_$RfiListStateImpl(
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<RfiModel>,
      userName: null == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String,
      completedCount: null == completedCount
          ? _value.completedCount
          : completedCount // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$RfiListStateImpl extends _RfiListState {
  const _$RfiListStateImpl(
      {required final List<RfiModel> items,
      required this.userName,
      required this.completedCount})
      : _items = items,
        super._();

  final List<RfiModel> _items;
  @override
  List<RfiModel> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  final String userName;
  @override
  final int completedCount;

  @override
  String toString() {
    return 'RfiListState(items: $items, userName: $userName, completedCount: $completedCount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RfiListStateImpl &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.completedCount, completedCount) ||
                other.completedCount == completedCount));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_items), userName, completedCount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RfiListStateImplCopyWith<_$RfiListStateImpl> get copyWith =>
      __$$RfiListStateImplCopyWithImpl<_$RfiListStateImpl>(this, _$identity);
}

abstract class _RfiListState extends RfiListState {
  const factory _RfiListState(
      {required final List<RfiModel> items,
      required final String userName,
      required final int completedCount}) = _$RfiListStateImpl;
  const _RfiListState._() : super._();

  @override
  List<RfiModel> get items;
  @override
  String get userName;
  @override
  int get completedCount;
  @override
  @JsonKey(ignore: true)
  _$$RfiListStateImplCopyWith<_$RfiListStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
