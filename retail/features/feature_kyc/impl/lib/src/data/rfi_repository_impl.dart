import 'package:wio_feature_kyc_api/feature_kyc_api.dart';
import 'package:wio_feature_kyc_impl/src/data/rfi_repository.dart';
import 'package:wio_feature_kyc_impl/src/data/rfi_service.dart';
import 'package:wio_feature_onboarding_data_api/models/retail_account_maintenance_secure_specs.swagger.dart';

class RfiRepositoryImpl implements RfiRepository {
  final RfiService _service;

  RfiRepositoryImpl({required RfiService service}) : _service = service;

  @override
  Future<List<RfiModel>> fetchRfi() async {
    final dtoList = await _service.fetchRfi();

    return dtoList
        .map<RfiModel>(
          (dto) => RfiModel(
            id: dto.id,
            title: dto.title,
            requests: dto.questions
                .map<InformationRequest>(
                  (e) => InformationRequest(
                    questionId: e.id,
                    question: e.title,
                    file: [
                      // adding empty file data to show file upload item
                      // with pending uploading status
                      UploadFileModel(
                        id: DateTime.now().millisecondsSinceEpoch,
                      ),
                    ],
                  ),
                )
                .toList(),
          ),
        )
        .toList();
  }

  @override
  Future<void> submitRfi(RfiModel model, Map<String, String> answers) async {
    final questions = model.requests.map((e) {
      final answer = answers[e.questionId];
      if (answer == null) {
        throw UnansweredQuestionException();
      }

      return AnsweredQuestionDto(
        id: e.questionId,
        answer: AnswerDto(
          title: answer,
          attachedDocuments: e.file
              .where((e) => e.status == FileUploadStatus.completed)
              .map(
                (e) => AttachedDocumentDto(
                  originalFileName: e.fileName,
                  url: e.uploadedPath,
                  mimeType: e.mimeType,
                ),
              )
              .toList(),
        ),
      );
    }).toList();
    final dto = RFISubmissionDto(questions: questions);
    await _service.submitRfi(model.id, dto);
  }
}
