import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_kyc_api/navigation/kyc_feature_navigation_config.dart';

part 'source_of_funds_screen_navigation_config.freezed.dart';

@freezed
class SourceOfFundsScreenNavigationConfig extends ScreenNavigationConfig
    with _$SourceOfFundsScreenNavigationConfig {
  static const name = 'source_of_funds_screen';

  const factory SourceOfFundsScreenNavigationConfig() =
      _SourceOfFundsScreenNavigationConfig;

  const SourceOfFundsScreenNavigationConfig._()
      : super(
          id: name,
          feature: KycFeatureNavigationConfig.name,
        );

  @override
  String toString() {
    return 'SourceOfFundsScreenNavigationConfig';
  }
}
