import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_spend_highlights_api/index.dart';

part 'spend_filter_date_bottom_sheet_config.freezed.dart';

/// A generic navigation config for opening date selection for monthly
/// statements.
@freezed
class DateSelectionBottomSheetConfig
    with _$DateSelectionBottomSheetConfig
    implements BottomSheetNavigationConfig<DateTime?> {
  const factory DateSelectionBottomSheetConfig({
    required DateTime firstDate,
    required DateTime lastDate,
    DateTime? selectedDate,
  }) = _DateSelectionBottomSheetConfig;

  const DateSelectionBottomSheetConfig._();

  @override
  String get feature => SpendHighlightsFeatureNavigationConfig.name;
}
