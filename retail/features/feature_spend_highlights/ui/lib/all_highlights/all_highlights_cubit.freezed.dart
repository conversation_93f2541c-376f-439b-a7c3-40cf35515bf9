// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'all_highlights_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$AllHighlightTopItem {
  double get percentage => throw _privateConstructorUsedError;
  SpendHighlight get spendHighlight => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AllHighlightTopItemCopyWith<AllHighlightTopItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AllHighlightTopItemCopyWith<$Res> {
  factory $AllHighlightTopItemCopyWith(
          AllHighlightTopItem value, $Res Function(AllHighlightTopItem) then) =
      _$AllHighlightTopItemCopyWithImpl<$Res>;
  $Res call({double percentage, SpendHighlight spendHighlight});

  $SpendHighlightCopyWith<$Res> get spendHighlight;
}

/// @nodoc
class _$AllHighlightTopItemCopyWithImpl<$Res>
    implements $AllHighlightTopItemCopyWith<$Res> {
  _$AllHighlightTopItemCopyWithImpl(this._value, this._then);

  final AllHighlightTopItem _value;
  // ignore: unused_field
  final $Res Function(AllHighlightTopItem) _then;

  @override
  $Res call({
    Object? percentage = freezed,
    Object? spendHighlight = freezed,
  }) {
    return _then(_value.copyWith(
      percentage: percentage == freezed
          ? _value.percentage
          : percentage // ignore: cast_nullable_to_non_nullable
              as double,
      spendHighlight: spendHighlight == freezed
          ? _value.spendHighlight
          : spendHighlight // ignore: cast_nullable_to_non_nullable
              as SpendHighlight,
    ));
  }

  @override
  $SpendHighlightCopyWith<$Res> get spendHighlight {
    return $SpendHighlightCopyWith<$Res>(_value.spendHighlight, (value) {
      return _then(_value.copyWith(spendHighlight: value));
    });
  }
}

/// @nodoc
abstract class _$$_AllHighlightTopItemCopyWith<$Res>
    implements $AllHighlightTopItemCopyWith<$Res> {
  factory _$$_AllHighlightTopItemCopyWith(_$_AllHighlightTopItem value,
          $Res Function(_$_AllHighlightTopItem) then) =
      __$$_AllHighlightTopItemCopyWithImpl<$Res>;
  @override
  $Res call({double percentage, SpendHighlight spendHighlight});

  @override
  $SpendHighlightCopyWith<$Res> get spendHighlight;
}

/// @nodoc
class __$$_AllHighlightTopItemCopyWithImpl<$Res>
    extends _$AllHighlightTopItemCopyWithImpl<$Res>
    implements _$$_AllHighlightTopItemCopyWith<$Res> {
  __$$_AllHighlightTopItemCopyWithImpl(_$_AllHighlightTopItem _value,
      $Res Function(_$_AllHighlightTopItem) _then)
      : super(_value, (v) => _then(v as _$_AllHighlightTopItem));

  @override
  _$_AllHighlightTopItem get _value => super._value as _$_AllHighlightTopItem;

  @override
  $Res call({
    Object? percentage = freezed,
    Object? spendHighlight = freezed,
  }) {
    return _then(_$_AllHighlightTopItem(
      percentage: percentage == freezed
          ? _value.percentage
          : percentage // ignore: cast_nullable_to_non_nullable
              as double,
      spendHighlight: spendHighlight == freezed
          ? _value.spendHighlight
          : spendHighlight // ignore: cast_nullable_to_non_nullable
              as SpendHighlight,
    ));
  }
}

/// @nodoc

class _$_AllHighlightTopItem implements _AllHighlightTopItem {
  const _$_AllHighlightTopItem(
      {required this.percentage, required this.spendHighlight});

  @override
  final double percentage;
  @override
  final SpendHighlight spendHighlight;

  @override
  String toString() {
    return 'AllHighlightTopItem(percentage: $percentage, spendHighlight: $spendHighlight)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AllHighlightTopItem &&
            const DeepCollectionEquality()
                .equals(other.percentage, percentage) &&
            const DeepCollectionEquality()
                .equals(other.spendHighlight, spendHighlight));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(percentage),
      const DeepCollectionEquality().hash(spendHighlight));

  @JsonKey(ignore: true)
  @override
  _$$_AllHighlightTopItemCopyWith<_$_AllHighlightTopItem> get copyWith =>
      __$$_AllHighlightTopItemCopyWithImpl<_$_AllHighlightTopItem>(
          this, _$identity);
}

abstract class _AllHighlightTopItem implements AllHighlightTopItem {
  const factory _AllHighlightTopItem(
      {required final double percentage,
      required final SpendHighlight spendHighlight}) = _$_AllHighlightTopItem;

  @override
  double get percentage => throw _privateConstructorUsedError;
  @override
  SpendHighlight get spendHighlight => throw _privateConstructorUsedError;
  @override
  @JsonKey(ignore: true)
  _$$_AllHighlightTopItemCopyWith<_$_AllHighlightTopItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$AllHighlightsState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(
            AllHighlightTopItem? topItem,
            DateTime? currentDateTime,
            SpendHighlights highlights,
            bool hasReachMax)
        idle,
    required TResult Function() failed,
    required TResult Function() couldNotPaginate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(AllHighlightTopItem? topItem, DateTime? currentDateTime,
            SpendHighlights highlights, bool hasReachMax)?
        idle,
    TResult Function()? failed,
    TResult Function()? couldNotPaginate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(AllHighlightTopItem? topItem, DateTime? currentDateTime,
            SpendHighlights highlights, bool hasReachMax)?
        idle,
    TResult Function()? failed,
    TResult Function()? couldNotPaginate,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AllHighlightsStateLoading value) loading,
    required TResult Function(_AllHighlightsStateIdle value) idle,
    required TResult Function(_AllHighlightsStateFailed value) failed,
    required TResult Function(_AllHighlightsStateCouldNotPaginate value)
        couldNotPaginate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult Function(_AllHighlightsStateLoading value)? loading,
    TResult Function(_AllHighlightsStateIdle value)? idle,
    TResult Function(_AllHighlightsStateFailed value)? failed,
    TResult Function(_AllHighlightsStateCouldNotPaginate value)?
        couldNotPaginate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AllHighlightsStateLoading value)? loading,
    TResult Function(_AllHighlightsStateIdle value)? idle,
    TResult Function(_AllHighlightsStateFailed value)? failed,
    TResult Function(_AllHighlightsStateCouldNotPaginate value)?
        couldNotPaginate,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AllHighlightsStateCopyWith<$Res> {
  factory $AllHighlightsStateCopyWith(
          AllHighlightsState value, $Res Function(AllHighlightsState) then) =
      _$AllHighlightsStateCopyWithImpl<$Res>;
}

/// @nodoc
class _$AllHighlightsStateCopyWithImpl<$Res>
    implements $AllHighlightsStateCopyWith<$Res> {
  _$AllHighlightsStateCopyWithImpl(this._value, this._then);

  final AllHighlightsState _value;
  // ignore: unused_field
  final $Res Function(AllHighlightsState) _then;
}

/// @nodoc
abstract class _$$_AllHighlightsStateLoadingCopyWith<$Res> {
  factory _$$_AllHighlightsStateLoadingCopyWith(
          _$_AllHighlightsStateLoading value,
          $Res Function(_$_AllHighlightsStateLoading) then) =
      __$$_AllHighlightsStateLoadingCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_AllHighlightsStateLoadingCopyWithImpl<$Res>
    extends _$AllHighlightsStateCopyWithImpl<$Res>
    implements _$$_AllHighlightsStateLoadingCopyWith<$Res> {
  __$$_AllHighlightsStateLoadingCopyWithImpl(
      _$_AllHighlightsStateLoading _value,
      $Res Function(_$_AllHighlightsStateLoading) _then)
      : super(_value, (v) => _then(v as _$_AllHighlightsStateLoading));

  @override
  _$_AllHighlightsStateLoading get _value =>
      super._value as _$_AllHighlightsStateLoading;
}

/// @nodoc

class _$_AllHighlightsStateLoading implements _AllHighlightsStateLoading {
  const _$_AllHighlightsStateLoading();

  @override
  String toString() {
    return 'AllHighlightsState.loading()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AllHighlightsStateLoading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(
            AllHighlightTopItem? topItem,
            DateTime? currentDateTime,
            SpendHighlights highlights,
            bool hasReachMax)
        idle,
    required TResult Function() failed,
    required TResult Function() couldNotPaginate,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(AllHighlightTopItem? topItem, DateTime? currentDateTime,
            SpendHighlights highlights, bool hasReachMax)?
        idle,
    TResult Function()? failed,
    TResult Function()? couldNotPaginate,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(AllHighlightTopItem? topItem, DateTime? currentDateTime,
            SpendHighlights highlights, bool hasReachMax)?
        idle,
    TResult Function()? failed,
    TResult Function()? couldNotPaginate,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AllHighlightsStateLoading value) loading,
    required TResult Function(_AllHighlightsStateIdle value) idle,
    required TResult Function(_AllHighlightsStateFailed value) failed,
    required TResult Function(_AllHighlightsStateCouldNotPaginate value)
        couldNotPaginate,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult Function(_AllHighlightsStateLoading value)? loading,
    TResult Function(_AllHighlightsStateIdle value)? idle,
    TResult Function(_AllHighlightsStateFailed value)? failed,
    TResult Function(_AllHighlightsStateCouldNotPaginate value)?
        couldNotPaginate,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AllHighlightsStateLoading value)? loading,
    TResult Function(_AllHighlightsStateIdle value)? idle,
    TResult Function(_AllHighlightsStateFailed value)? failed,
    TResult Function(_AllHighlightsStateCouldNotPaginate value)?
        couldNotPaginate,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _AllHighlightsStateLoading implements AllHighlightsState {
  const factory _AllHighlightsStateLoading() = _$_AllHighlightsStateLoading;
}

/// @nodoc
abstract class _$$_AllHighlightsStateIdleCopyWith<$Res> {
  factory _$$_AllHighlightsStateIdleCopyWith(_$_AllHighlightsStateIdle value,
          $Res Function(_$_AllHighlightsStateIdle) then) =
      __$$_AllHighlightsStateIdleCopyWithImpl<$Res>;
  $Res call(
      {AllHighlightTopItem? topItem,
      DateTime? currentDateTime,
      SpendHighlights highlights,
      bool hasReachMax});

  $AllHighlightTopItemCopyWith<$Res>? get topItem;
  $SpendHighlightsCopyWith<$Res> get highlights;
}

/// @nodoc
class __$$_AllHighlightsStateIdleCopyWithImpl<$Res>
    extends _$AllHighlightsStateCopyWithImpl<$Res>
    implements _$$_AllHighlightsStateIdleCopyWith<$Res> {
  __$$_AllHighlightsStateIdleCopyWithImpl(_$_AllHighlightsStateIdle _value,
      $Res Function(_$_AllHighlightsStateIdle) _then)
      : super(_value, (v) => _then(v as _$_AllHighlightsStateIdle));

  @override
  _$_AllHighlightsStateIdle get _value =>
      super._value as _$_AllHighlightsStateIdle;

  @override
  $Res call({
    Object? topItem = freezed,
    Object? currentDateTime = freezed,
    Object? highlights = freezed,
    Object? hasReachMax = freezed,
  }) {
    return _then(_$_AllHighlightsStateIdle(
      topItem: topItem == freezed
          ? _value.topItem
          : topItem // ignore: cast_nullable_to_non_nullable
              as AllHighlightTopItem?,
      currentDateTime: currentDateTime == freezed
          ? _value.currentDateTime
          : currentDateTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      highlights: highlights == freezed
          ? _value.highlights
          : highlights // ignore: cast_nullable_to_non_nullable
              as SpendHighlights,
      hasReachMax: hasReachMax == freezed
          ? _value.hasReachMax
          : hasReachMax // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  @override
  $AllHighlightTopItemCopyWith<$Res>? get topItem {
    if (_value.topItem == null) {
      return null;
    }

    return $AllHighlightTopItemCopyWith<$Res>(_value.topItem!, (value) {
      return _then(_value.copyWith(topItem: value));
    });
  }

  @override
  $SpendHighlightsCopyWith<$Res> get highlights {
    return $SpendHighlightsCopyWith<$Res>(_value.highlights, (value) {
      return _then(_value.copyWith(highlights: value));
    });
  }
}

/// @nodoc

class _$_AllHighlightsStateIdle implements _AllHighlightsStateIdle {
  const _$_AllHighlightsStateIdle(
      {this.topItem,
      this.currentDateTime,
      required this.highlights,
      this.hasReachMax = false});

  @override
  final AllHighlightTopItem? topItem;
  @override
  final DateTime? currentDateTime;
  @override
  final SpendHighlights highlights;
  @override
  @JsonKey()
  final bool hasReachMax;

  @override
  String toString() {
    return 'AllHighlightsState.idle(topItem: $topItem, currentDateTime: $currentDateTime, highlights: $highlights, hasReachMax: $hasReachMax)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AllHighlightsStateIdle &&
            const DeepCollectionEquality().equals(other.topItem, topItem) &&
            const DeepCollectionEquality()
                .equals(other.currentDateTime, currentDateTime) &&
            const DeepCollectionEquality()
                .equals(other.highlights, highlights) &&
            const DeepCollectionEquality()
                .equals(other.hasReachMax, hasReachMax));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(topItem),
      const DeepCollectionEquality().hash(currentDateTime),
      const DeepCollectionEquality().hash(highlights),
      const DeepCollectionEquality().hash(hasReachMax));

  @JsonKey(ignore: true)
  @override
  _$$_AllHighlightsStateIdleCopyWith<_$_AllHighlightsStateIdle> get copyWith =>
      __$$_AllHighlightsStateIdleCopyWithImpl<_$_AllHighlightsStateIdle>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(
            AllHighlightTopItem? topItem,
            DateTime? currentDateTime,
            SpendHighlights highlights,
            bool hasReachMax)
        idle,
    required TResult Function() failed,
    required TResult Function() couldNotPaginate,
  }) {
    return idle(topItem, currentDateTime, highlights, hasReachMax);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(AllHighlightTopItem? topItem, DateTime? currentDateTime,
            SpendHighlights highlights, bool hasReachMax)?
        idle,
    TResult Function()? failed,
    TResult Function()? couldNotPaginate,
  }) {
    return idle?.call(topItem, currentDateTime, highlights, hasReachMax);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(AllHighlightTopItem? topItem, DateTime? currentDateTime,
            SpendHighlights highlights, bool hasReachMax)?
        idle,
    TResult Function()? failed,
    TResult Function()? couldNotPaginate,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(topItem, currentDateTime, highlights, hasReachMax);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AllHighlightsStateLoading value) loading,
    required TResult Function(_AllHighlightsStateIdle value) idle,
    required TResult Function(_AllHighlightsStateFailed value) failed,
    required TResult Function(_AllHighlightsStateCouldNotPaginate value)
        couldNotPaginate,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult Function(_AllHighlightsStateLoading value)? loading,
    TResult Function(_AllHighlightsStateIdle value)? idle,
    TResult Function(_AllHighlightsStateFailed value)? failed,
    TResult Function(_AllHighlightsStateCouldNotPaginate value)?
        couldNotPaginate,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AllHighlightsStateLoading value)? loading,
    TResult Function(_AllHighlightsStateIdle value)? idle,
    TResult Function(_AllHighlightsStateFailed value)? failed,
    TResult Function(_AllHighlightsStateCouldNotPaginate value)?
        couldNotPaginate,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class _AllHighlightsStateIdle implements AllHighlightsState {
  const factory _AllHighlightsStateIdle(
      {final AllHighlightTopItem? topItem,
      final DateTime? currentDateTime,
      required final SpendHighlights highlights,
      final bool hasReachMax}) = _$_AllHighlightsStateIdle;

  AllHighlightTopItem? get topItem => throw _privateConstructorUsedError;
  DateTime? get currentDateTime => throw _privateConstructorUsedError;
  SpendHighlights get highlights => throw _privateConstructorUsedError;
  bool get hasReachMax => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  _$$_AllHighlightsStateIdleCopyWith<_$_AllHighlightsStateIdle> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_AllHighlightsStateFailedCopyWith<$Res> {
  factory _$$_AllHighlightsStateFailedCopyWith(
          _$_AllHighlightsStateFailed value,
          $Res Function(_$_AllHighlightsStateFailed) then) =
      __$$_AllHighlightsStateFailedCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_AllHighlightsStateFailedCopyWithImpl<$Res>
    extends _$AllHighlightsStateCopyWithImpl<$Res>
    implements _$$_AllHighlightsStateFailedCopyWith<$Res> {
  __$$_AllHighlightsStateFailedCopyWithImpl(_$_AllHighlightsStateFailed _value,
      $Res Function(_$_AllHighlightsStateFailed) _then)
      : super(_value, (v) => _then(v as _$_AllHighlightsStateFailed));

  @override
  _$_AllHighlightsStateFailed get _value =>
      super._value as _$_AllHighlightsStateFailed;
}

/// @nodoc

class _$_AllHighlightsStateFailed implements _AllHighlightsStateFailed {
  const _$_AllHighlightsStateFailed();

  @override
  String toString() {
    return 'AllHighlightsState.failed()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AllHighlightsStateFailed);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(
            AllHighlightTopItem? topItem,
            DateTime? currentDateTime,
            SpendHighlights highlights,
            bool hasReachMax)
        idle,
    required TResult Function() failed,
    required TResult Function() couldNotPaginate,
  }) {
    return failed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(AllHighlightTopItem? topItem, DateTime? currentDateTime,
            SpendHighlights highlights, bool hasReachMax)?
        idle,
    TResult Function()? failed,
    TResult Function()? couldNotPaginate,
  }) {
    return failed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(AllHighlightTopItem? topItem, DateTime? currentDateTime,
            SpendHighlights highlights, bool hasReachMax)?
        idle,
    TResult Function()? failed,
    TResult Function()? couldNotPaginate,
    required TResult orElse(),
  }) {
    if (failed != null) {
      return failed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AllHighlightsStateLoading value) loading,
    required TResult Function(_AllHighlightsStateIdle value) idle,
    required TResult Function(_AllHighlightsStateFailed value) failed,
    required TResult Function(_AllHighlightsStateCouldNotPaginate value)
        couldNotPaginate,
  }) {
    return failed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult Function(_AllHighlightsStateLoading value)? loading,
    TResult Function(_AllHighlightsStateIdle value)? idle,
    TResult Function(_AllHighlightsStateFailed value)? failed,
    TResult Function(_AllHighlightsStateCouldNotPaginate value)?
        couldNotPaginate,
  }) {
    return failed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AllHighlightsStateLoading value)? loading,
    TResult Function(_AllHighlightsStateIdle value)? idle,
    TResult Function(_AllHighlightsStateFailed value)? failed,
    TResult Function(_AllHighlightsStateCouldNotPaginate value)?
        couldNotPaginate,
    required TResult orElse(),
  }) {
    if (failed != null) {
      return failed(this);
    }
    return orElse();
  }
}

abstract class _AllHighlightsStateFailed implements AllHighlightsState {
  const factory _AllHighlightsStateFailed() = _$_AllHighlightsStateFailed;
}

/// @nodoc
abstract class _$$_AllHighlightsStateCouldNotPaginateCopyWith<$Res> {
  factory _$$_AllHighlightsStateCouldNotPaginateCopyWith(
          _$_AllHighlightsStateCouldNotPaginate value,
          $Res Function(_$_AllHighlightsStateCouldNotPaginate) then) =
      __$$_AllHighlightsStateCouldNotPaginateCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_AllHighlightsStateCouldNotPaginateCopyWithImpl<$Res>
    extends _$AllHighlightsStateCopyWithImpl<$Res>
    implements _$$_AllHighlightsStateCouldNotPaginateCopyWith<$Res> {
  __$$_AllHighlightsStateCouldNotPaginateCopyWithImpl(
      _$_AllHighlightsStateCouldNotPaginate _value,
      $Res Function(_$_AllHighlightsStateCouldNotPaginate) _then)
      : super(_value, (v) => _then(v as _$_AllHighlightsStateCouldNotPaginate));

  @override
  _$_AllHighlightsStateCouldNotPaginate get _value =>
      super._value as _$_AllHighlightsStateCouldNotPaginate;
}

/// @nodoc

class _$_AllHighlightsStateCouldNotPaginate
    implements _AllHighlightsStateCouldNotPaginate {
  const _$_AllHighlightsStateCouldNotPaginate();

  @override
  String toString() {
    return 'AllHighlightsState.couldNotPaginate()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AllHighlightsStateCouldNotPaginate);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(
            AllHighlightTopItem? topItem,
            DateTime? currentDateTime,
            SpendHighlights highlights,
            bool hasReachMax)
        idle,
    required TResult Function() failed,
    required TResult Function() couldNotPaginate,
  }) {
    return couldNotPaginate();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(AllHighlightTopItem? topItem, DateTime? currentDateTime,
            SpendHighlights highlights, bool hasReachMax)?
        idle,
    TResult Function()? failed,
    TResult Function()? couldNotPaginate,
  }) {
    return couldNotPaginate?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(AllHighlightTopItem? topItem, DateTime? currentDateTime,
            SpendHighlights highlights, bool hasReachMax)?
        idle,
    TResult Function()? failed,
    TResult Function()? couldNotPaginate,
    required TResult orElse(),
  }) {
    if (couldNotPaginate != null) {
      return couldNotPaginate();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AllHighlightsStateLoading value) loading,
    required TResult Function(_AllHighlightsStateIdle value) idle,
    required TResult Function(_AllHighlightsStateFailed value) failed,
    required TResult Function(_AllHighlightsStateCouldNotPaginate value)
        couldNotPaginate,
  }) {
    return couldNotPaginate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult Function(_AllHighlightsStateLoading value)? loading,
    TResult Function(_AllHighlightsStateIdle value)? idle,
    TResult Function(_AllHighlightsStateFailed value)? failed,
    TResult Function(_AllHighlightsStateCouldNotPaginate value)?
        couldNotPaginate,
  }) {
    return couldNotPaginate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AllHighlightsStateLoading value)? loading,
    TResult Function(_AllHighlightsStateIdle value)? idle,
    TResult Function(_AllHighlightsStateFailed value)? failed,
    TResult Function(_AllHighlightsStateCouldNotPaginate value)?
        couldNotPaginate,
    required TResult orElse(),
  }) {
    if (couldNotPaginate != null) {
      return couldNotPaginate(this);
    }
    return orElse();
  }
}

abstract class _AllHighlightsStateCouldNotPaginate
    implements AllHighlightsState {
  const factory _AllHighlightsStateCouldNotPaginate() =
      _$_AllHighlightsStateCouldNotPaginate;
}
