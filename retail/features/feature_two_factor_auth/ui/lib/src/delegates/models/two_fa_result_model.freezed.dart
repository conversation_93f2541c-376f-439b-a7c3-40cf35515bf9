// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'two_fa_result_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$TwoFaResultModel {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() success,
    required TResult Function() failure,
    required TResult Function(List<TwoFactorScenario> possibleScenarios)
        passPossibleScenarios,
    required TResult Function(TwoFactorErrorCode code) implicitlyActions,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult Function()? success,
    TResult Function()? failure,
    TResult Function(List<TwoFactorScenario> possibleScenarios)?
        passPossibleScenarios,
    TResult Function(TwoFactorErrorCode code)? implicitlyActions,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? success,
    TResult Function()? failure,
    TResult Function(List<TwoFactorScenario> possibleScenarios)?
        passPossibleScenarios,
    TResult Function(TwoFactorErrorCode code)? implicitlyActions,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(TwoFaSuccessResultModel value) success,
    required TResult Function(TwoFaFailureResultModel value) failure,
    required TResult Function(TwoFaPossibleScenariosModel value)
        passPossibleScenarios,
    required TResult Function(TwoFaImplicitlyActionsModel value)
        implicitlyActions,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult Function(TwoFaSuccessResultModel value)? success,
    TResult Function(TwoFaFailureResultModel value)? failure,
    TResult Function(TwoFaPossibleScenariosModel value)? passPossibleScenarios,
    TResult Function(TwoFaImplicitlyActionsModel value)? implicitlyActions,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(TwoFaSuccessResultModel value)? success,
    TResult Function(TwoFaFailureResultModel value)? failure,
    TResult Function(TwoFaPossibleScenariosModel value)? passPossibleScenarios,
    TResult Function(TwoFaImplicitlyActionsModel value)? implicitlyActions,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TwoFaResultModelCopyWith<$Res> {
  factory $TwoFaResultModelCopyWith(
          TwoFaResultModel value, $Res Function(TwoFaResultModel) then) =
      _$TwoFaResultModelCopyWithImpl<$Res>;
}

/// @nodoc
class _$TwoFaResultModelCopyWithImpl<$Res>
    implements $TwoFaResultModelCopyWith<$Res> {
  _$TwoFaResultModelCopyWithImpl(this._value, this._then);

  final TwoFaResultModel _value;
  // ignore: unused_field
  final $Res Function(TwoFaResultModel) _then;
}

/// @nodoc
abstract class _$$TwoFaSuccessResultModelCopyWith<$Res> {
  factory _$$TwoFaSuccessResultModelCopyWith(_$TwoFaSuccessResultModel value,
          $Res Function(_$TwoFaSuccessResultModel) then) =
      __$$TwoFaSuccessResultModelCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TwoFaSuccessResultModelCopyWithImpl<$Res>
    extends _$TwoFaResultModelCopyWithImpl<$Res>
    implements _$$TwoFaSuccessResultModelCopyWith<$Res> {
  __$$TwoFaSuccessResultModelCopyWithImpl(_$TwoFaSuccessResultModel _value,
      $Res Function(_$TwoFaSuccessResultModel) _then)
      : super(_value, (v) => _then(v as _$TwoFaSuccessResultModel));

  @override
  _$TwoFaSuccessResultModel get _value =>
      super._value as _$TwoFaSuccessResultModel;
}

/// @nodoc

class _$TwoFaSuccessResultModel implements TwoFaSuccessResultModel {
  const _$TwoFaSuccessResultModel();

  @override
  String toString() {
    return 'TwoFaResultModel.success()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TwoFaSuccessResultModel);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() success,
    required TResult Function() failure,
    required TResult Function(List<TwoFactorScenario> possibleScenarios)
        passPossibleScenarios,
    required TResult Function(TwoFactorErrorCode code) implicitlyActions,
  }) {
    return success();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult Function()? success,
    TResult Function()? failure,
    TResult Function(List<TwoFactorScenario> possibleScenarios)?
        passPossibleScenarios,
    TResult Function(TwoFactorErrorCode code)? implicitlyActions,
  }) {
    return success?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? success,
    TResult Function()? failure,
    TResult Function(List<TwoFactorScenario> possibleScenarios)?
        passPossibleScenarios,
    TResult Function(TwoFactorErrorCode code)? implicitlyActions,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(TwoFaSuccessResultModel value) success,
    required TResult Function(TwoFaFailureResultModel value) failure,
    required TResult Function(TwoFaPossibleScenariosModel value)
        passPossibleScenarios,
    required TResult Function(TwoFaImplicitlyActionsModel value)
        implicitlyActions,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult Function(TwoFaSuccessResultModel value)? success,
    TResult Function(TwoFaFailureResultModel value)? failure,
    TResult Function(TwoFaPossibleScenariosModel value)? passPossibleScenarios,
    TResult Function(TwoFaImplicitlyActionsModel value)? implicitlyActions,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(TwoFaSuccessResultModel value)? success,
    TResult Function(TwoFaFailureResultModel value)? failure,
    TResult Function(TwoFaPossibleScenariosModel value)? passPossibleScenarios,
    TResult Function(TwoFaImplicitlyActionsModel value)? implicitlyActions,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class TwoFaSuccessResultModel implements TwoFaResultModel {
  const factory TwoFaSuccessResultModel() = _$TwoFaSuccessResultModel;
}

/// @nodoc
abstract class _$$TwoFaFailureResultModelCopyWith<$Res> {
  factory _$$TwoFaFailureResultModelCopyWith(_$TwoFaFailureResultModel value,
          $Res Function(_$TwoFaFailureResultModel) then) =
      __$$TwoFaFailureResultModelCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TwoFaFailureResultModelCopyWithImpl<$Res>
    extends _$TwoFaResultModelCopyWithImpl<$Res>
    implements _$$TwoFaFailureResultModelCopyWith<$Res> {
  __$$TwoFaFailureResultModelCopyWithImpl(_$TwoFaFailureResultModel _value,
      $Res Function(_$TwoFaFailureResultModel) _then)
      : super(_value, (v) => _then(v as _$TwoFaFailureResultModel));

  @override
  _$TwoFaFailureResultModel get _value =>
      super._value as _$TwoFaFailureResultModel;
}

/// @nodoc

class _$TwoFaFailureResultModel implements TwoFaFailureResultModel {
  const _$TwoFaFailureResultModel();

  @override
  String toString() {
    return 'TwoFaResultModel.failure()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TwoFaFailureResultModel);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() success,
    required TResult Function() failure,
    required TResult Function(List<TwoFactorScenario> possibleScenarios)
        passPossibleScenarios,
    required TResult Function(TwoFactorErrorCode code) implicitlyActions,
  }) {
    return failure();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult Function()? success,
    TResult Function()? failure,
    TResult Function(List<TwoFactorScenario> possibleScenarios)?
        passPossibleScenarios,
    TResult Function(TwoFactorErrorCode code)? implicitlyActions,
  }) {
    return failure?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? success,
    TResult Function()? failure,
    TResult Function(List<TwoFactorScenario> possibleScenarios)?
        passPossibleScenarios,
    TResult Function(TwoFactorErrorCode code)? implicitlyActions,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(TwoFaSuccessResultModel value) success,
    required TResult Function(TwoFaFailureResultModel value) failure,
    required TResult Function(TwoFaPossibleScenariosModel value)
        passPossibleScenarios,
    required TResult Function(TwoFaImplicitlyActionsModel value)
        implicitlyActions,
  }) {
    return failure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult Function(TwoFaSuccessResultModel value)? success,
    TResult Function(TwoFaFailureResultModel value)? failure,
    TResult Function(TwoFaPossibleScenariosModel value)? passPossibleScenarios,
    TResult Function(TwoFaImplicitlyActionsModel value)? implicitlyActions,
  }) {
    return failure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(TwoFaSuccessResultModel value)? success,
    TResult Function(TwoFaFailureResultModel value)? failure,
    TResult Function(TwoFaPossibleScenariosModel value)? passPossibleScenarios,
    TResult Function(TwoFaImplicitlyActionsModel value)? implicitlyActions,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this);
    }
    return orElse();
  }
}

abstract class TwoFaFailureResultModel implements TwoFaResultModel {
  const factory TwoFaFailureResultModel() = _$TwoFaFailureResultModel;
}

/// @nodoc
abstract class _$$TwoFaPossibleScenariosModelCopyWith<$Res> {
  factory _$$TwoFaPossibleScenariosModelCopyWith(
          _$TwoFaPossibleScenariosModel value,
          $Res Function(_$TwoFaPossibleScenariosModel) then) =
      __$$TwoFaPossibleScenariosModelCopyWithImpl<$Res>;
  $Res call({List<TwoFactorScenario> possibleScenarios});
}

/// @nodoc
class __$$TwoFaPossibleScenariosModelCopyWithImpl<$Res>
    extends _$TwoFaResultModelCopyWithImpl<$Res>
    implements _$$TwoFaPossibleScenariosModelCopyWith<$Res> {
  __$$TwoFaPossibleScenariosModelCopyWithImpl(
      _$TwoFaPossibleScenariosModel _value,
      $Res Function(_$TwoFaPossibleScenariosModel) _then)
      : super(_value, (v) => _then(v as _$TwoFaPossibleScenariosModel));

  @override
  _$TwoFaPossibleScenariosModel get _value =>
      super._value as _$TwoFaPossibleScenariosModel;

  @override
  $Res call({
    Object? possibleScenarios = freezed,
  }) {
    return _then(_$TwoFaPossibleScenariosModel(
      possibleScenarios: possibleScenarios == freezed
          ? _value._possibleScenarios
          : possibleScenarios // ignore: cast_nullable_to_non_nullable
              as List<TwoFactorScenario>,
    ));
  }
}

/// @nodoc

class _$TwoFaPossibleScenariosModel implements TwoFaPossibleScenariosModel {
  const _$TwoFaPossibleScenariosModel(
      {required final List<TwoFactorScenario> possibleScenarios})
      : _possibleScenarios = possibleScenarios;

  final List<TwoFactorScenario> _possibleScenarios;
  @override
  List<TwoFactorScenario> get possibleScenarios {
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_possibleScenarios);
  }

  @override
  String toString() {
    return 'TwoFaResultModel.passPossibleScenarios(possibleScenarios: $possibleScenarios)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TwoFaPossibleScenariosModel &&
            const DeepCollectionEquality()
                .equals(other._possibleScenarios, _possibleScenarios));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_possibleScenarios));

  @JsonKey(ignore: true)
  @override
  _$$TwoFaPossibleScenariosModelCopyWith<_$TwoFaPossibleScenariosModel>
      get copyWith => __$$TwoFaPossibleScenariosModelCopyWithImpl<
          _$TwoFaPossibleScenariosModel>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() success,
    required TResult Function() failure,
    required TResult Function(List<TwoFactorScenario> possibleScenarios)
        passPossibleScenarios,
    required TResult Function(TwoFactorErrorCode code) implicitlyActions,
  }) {
    return passPossibleScenarios(possibleScenarios);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult Function()? success,
    TResult Function()? failure,
    TResult Function(List<TwoFactorScenario> possibleScenarios)?
        passPossibleScenarios,
    TResult Function(TwoFactorErrorCode code)? implicitlyActions,
  }) {
    return passPossibleScenarios?.call(possibleScenarios);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? success,
    TResult Function()? failure,
    TResult Function(List<TwoFactorScenario> possibleScenarios)?
        passPossibleScenarios,
    TResult Function(TwoFactorErrorCode code)? implicitlyActions,
    required TResult orElse(),
  }) {
    if (passPossibleScenarios != null) {
      return passPossibleScenarios(possibleScenarios);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(TwoFaSuccessResultModel value) success,
    required TResult Function(TwoFaFailureResultModel value) failure,
    required TResult Function(TwoFaPossibleScenariosModel value)
        passPossibleScenarios,
    required TResult Function(TwoFaImplicitlyActionsModel value)
        implicitlyActions,
  }) {
    return passPossibleScenarios(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult Function(TwoFaSuccessResultModel value)? success,
    TResult Function(TwoFaFailureResultModel value)? failure,
    TResult Function(TwoFaPossibleScenariosModel value)? passPossibleScenarios,
    TResult Function(TwoFaImplicitlyActionsModel value)? implicitlyActions,
  }) {
    return passPossibleScenarios?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(TwoFaSuccessResultModel value)? success,
    TResult Function(TwoFaFailureResultModel value)? failure,
    TResult Function(TwoFaPossibleScenariosModel value)? passPossibleScenarios,
    TResult Function(TwoFaImplicitlyActionsModel value)? implicitlyActions,
    required TResult orElse(),
  }) {
    if (passPossibleScenarios != null) {
      return passPossibleScenarios(this);
    }
    return orElse();
  }
}

abstract class TwoFaPossibleScenariosModel implements TwoFaResultModel {
  const factory TwoFaPossibleScenariosModel(
          {required final List<TwoFactorScenario> possibleScenarios}) =
      _$TwoFaPossibleScenariosModel;

  List<TwoFactorScenario> get possibleScenarios =>
      throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  _$$TwoFaPossibleScenariosModelCopyWith<_$TwoFaPossibleScenariosModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TwoFaImplicitlyActionsModelCopyWith<$Res> {
  factory _$$TwoFaImplicitlyActionsModelCopyWith(
          _$TwoFaImplicitlyActionsModel value,
          $Res Function(_$TwoFaImplicitlyActionsModel) then) =
      __$$TwoFaImplicitlyActionsModelCopyWithImpl<$Res>;
  $Res call({TwoFactorErrorCode code});
}

/// @nodoc
class __$$TwoFaImplicitlyActionsModelCopyWithImpl<$Res>
    extends _$TwoFaResultModelCopyWithImpl<$Res>
    implements _$$TwoFaImplicitlyActionsModelCopyWith<$Res> {
  __$$TwoFaImplicitlyActionsModelCopyWithImpl(
      _$TwoFaImplicitlyActionsModel _value,
      $Res Function(_$TwoFaImplicitlyActionsModel) _then)
      : super(_value, (v) => _then(v as _$TwoFaImplicitlyActionsModel));

  @override
  _$TwoFaImplicitlyActionsModel get _value =>
      super._value as _$TwoFaImplicitlyActionsModel;

  @override
  $Res call({
    Object? code = freezed,
  }) {
    return _then(_$TwoFaImplicitlyActionsModel(
      code: code == freezed
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as TwoFactorErrorCode,
    ));
  }
}

/// @nodoc

class _$TwoFaImplicitlyActionsModel implements TwoFaImplicitlyActionsModel {
  const _$TwoFaImplicitlyActionsModel({required this.code});

  @override
  final TwoFactorErrorCode code;

  @override
  String toString() {
    return 'TwoFaResultModel.implicitlyActions(code: $code)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TwoFaImplicitlyActionsModel &&
            const DeepCollectionEquality().equals(other.code, code));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(code));

  @JsonKey(ignore: true)
  @override
  _$$TwoFaImplicitlyActionsModelCopyWith<_$TwoFaImplicitlyActionsModel>
      get copyWith => __$$TwoFaImplicitlyActionsModelCopyWithImpl<
          _$TwoFaImplicitlyActionsModel>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() success,
    required TResult Function() failure,
    required TResult Function(List<TwoFactorScenario> possibleScenarios)
        passPossibleScenarios,
    required TResult Function(TwoFactorErrorCode code) implicitlyActions,
  }) {
    return implicitlyActions(code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult Function()? success,
    TResult Function()? failure,
    TResult Function(List<TwoFactorScenario> possibleScenarios)?
        passPossibleScenarios,
    TResult Function(TwoFactorErrorCode code)? implicitlyActions,
  }) {
    return implicitlyActions?.call(code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? success,
    TResult Function()? failure,
    TResult Function(List<TwoFactorScenario> possibleScenarios)?
        passPossibleScenarios,
    TResult Function(TwoFactorErrorCode code)? implicitlyActions,
    required TResult orElse(),
  }) {
    if (implicitlyActions != null) {
      return implicitlyActions(code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(TwoFaSuccessResultModel value) success,
    required TResult Function(TwoFaFailureResultModel value) failure,
    required TResult Function(TwoFaPossibleScenariosModel value)
        passPossibleScenarios,
    required TResult Function(TwoFaImplicitlyActionsModel value)
        implicitlyActions,
  }) {
    return implicitlyActions(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult Function(TwoFaSuccessResultModel value)? success,
    TResult Function(TwoFaFailureResultModel value)? failure,
    TResult Function(TwoFaPossibleScenariosModel value)? passPossibleScenarios,
    TResult Function(TwoFaImplicitlyActionsModel value)? implicitlyActions,
  }) {
    return implicitlyActions?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(TwoFaSuccessResultModel value)? success,
    TResult Function(TwoFaFailureResultModel value)? failure,
    TResult Function(TwoFaPossibleScenariosModel value)? passPossibleScenarios,
    TResult Function(TwoFaImplicitlyActionsModel value)? implicitlyActions,
    required TResult orElse(),
  }) {
    if (implicitlyActions != null) {
      return implicitlyActions(this);
    }
    return orElse();
  }
}

abstract class TwoFaImplicitlyActionsModel implements TwoFaResultModel {
  const factory TwoFaImplicitlyActionsModel(
      {required final TwoFactorErrorCode code}) = _$TwoFaImplicitlyActionsModel;

  TwoFactorErrorCode get code => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  _$$TwoFaImplicitlyActionsModelCopyWith<_$TwoFaImplicitlyActionsModel>
      get copyWith => throw _privateConstructorUsedError;
}
