// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'carbon_calculator_donation_page_navigation_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$CarbonCalculatorDonationPageNavigationConfig {}

/// @nodoc
abstract class $CarbonCalculatorDonationPageNavigationConfigCopyWith<$Res> {
  factory $CarbonCalculatorDonationPageNavigationConfigCopyWith(
          CarbonCalculatorDonationPageNavigationConfig value,
          $Res Function(CarbonCalculatorDonationPageNavigationConfig) then) =
      _$CarbonCalculatorDonationPageNavigationConfigCopyWithImpl<$Res,
          CarbonCalculatorDonationPageNavigationConfig>;
}

/// @nodoc
class _$CarbonCalculatorDonationPageNavigationConfigCopyWithImpl<$Res,
        $Val extends CarbonCalculatorDonationPageNavigationConfig>
    implements $CarbonCalculatorDonationPageNavigationConfigCopyWith<$Res> {
  _$CarbonCalculatorDonationPageNavigationConfigCopyWithImpl(
      this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_CarbonCalculatorDonationPageNavigationConfigCopyWith<$Res> {
  factory _$$_CarbonCalculatorDonationPageNavigationConfigCopyWith(
          _$_CarbonCalculatorDonationPageNavigationConfig value,
          $Res Function(_$_CarbonCalculatorDonationPageNavigationConfig) then) =
      __$$_CarbonCalculatorDonationPageNavigationConfigCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_CarbonCalculatorDonationPageNavigationConfigCopyWithImpl<$Res>
    extends _$CarbonCalculatorDonationPageNavigationConfigCopyWithImpl<$Res,
        _$_CarbonCalculatorDonationPageNavigationConfig>
    implements _$$_CarbonCalculatorDonationPageNavigationConfigCopyWith<$Res> {
  __$$_CarbonCalculatorDonationPageNavigationConfigCopyWithImpl(
      _$_CarbonCalculatorDonationPageNavigationConfig _value,
      $Res Function(_$_CarbonCalculatorDonationPageNavigationConfig) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_CarbonCalculatorDonationPageNavigationConfig
    extends _CarbonCalculatorDonationPageNavigationConfig {
  const _$_CarbonCalculatorDonationPageNavigationConfig() : super._();

  @override
  String toString() {
    return 'CarbonCalculatorDonationPageNavigationConfig()';
  }
}

abstract class _CarbonCalculatorDonationPageNavigationConfig
    extends CarbonCalculatorDonationPageNavigationConfig {
  const factory _CarbonCalculatorDonationPageNavigationConfig() =
      _$_CarbonCalculatorDonationPageNavigationConfig;
  const _CarbonCalculatorDonationPageNavigationConfig._() : super._();
}
