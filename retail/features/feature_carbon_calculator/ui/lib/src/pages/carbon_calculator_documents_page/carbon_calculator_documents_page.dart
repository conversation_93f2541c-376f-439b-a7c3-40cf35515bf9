import 'package:common_webview_ui/common_web_view_ui.dart';
import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_carbon_calculator_ui/src/pages/carbon_calculator_documents_page/carbon_calculator_documents_cubit.dart';
import 'package:wio_feature_carbon_calculator_ui/src/pages/carbon_calculator_documents_page/carbon_calculator_documents_state.dart';

class CarbonCalculatorDocumentsPage extends BasePage<
    CarbonCalculatorDocumentsState, CarbonCalculatorDocumentsCubit> {
  const CarbonCalculatorDocumentsPage({super.key});

  @override
  CarbonCalculatorDocumentsCubit createBloc() =>
      DependencyProvider.get<CarbonCalculatorDocumentsCubit>();

  @override
  void initBloc(CarbonCalculatorDocumentsCubit bloc) =>
      super.initBloc(bloc..initialize());

  @override
  Widget buildPage(
    BuildContext context,
    CarbonCalculatorDocumentsCubit bloc,
    CarbonCalculatorDocumentsState state,
  ) =>
      const _Page();
}

class _Page extends StatelessWidget {
  const _Page();

  @override
  Widget build(BuildContext context) {
    final l10n = CommonLocalizations.of(context);
    final cubit = context.read<CarbonCalculatorDocumentsCubit>();
    final state = context.watch<CarbonCalculatorDocumentsCubit>().state;

    return Scaffold(
      appBar: TopNavigation(
        TopNavigationModel(
          state: TopNavigationState.positive,
          rightAccessory: _getLanguageSwitcher(state, l10n),
        ),
        onRightIconPressed:
            state.canSwitchLanguage ? cubit.onToggleLanguage : null,
      ),
      body: state.mapOrNull(
        idle: (it) => _DocumentContent(url: it.documentUrl),
        failed: (_) => Center(
          child: ErrorBox(
            model: defaultErroModel(context),
            onRetryPressed: cubit.onRetry,
          ),
        ),
      ),
    );
  }

  TopNavigationRightAccessoryModel? _getLanguageSwitcher(
    CarbonCalculatorDocumentsState state,
    CommonLocalizations l10n,
  ) {
    return state.mapOrNull(
      idle: (it) => TopNavigationButtonRightAccessoryModel(
        button: ButtonModel(
          title: switch (it.language) {
            DocumentLanguage.english => l10n.switchToArabic,
            DocumentLanguage.arabic => l10n.switchToEnglish,
          },
          type: ButtonType.tertiary,
          styleOverride: const ButtonStyleOverride(
            textStyle: CompanyTextStylePointer.b2,
            active: ButtonStateColorScheme(
              background: CompanyColorPointer.transparent,
              foreground: CompanyColorPointer.secondary4,
            ),
          ),
        ),
      ),
    );
  }
}

class _DocumentContent extends StatelessWidget {
  final String url;

  const _DocumentContent({required this.url});

  @override
  Widget build(BuildContext context) {
    return WebViewWrapper(url: url);
  }
}
