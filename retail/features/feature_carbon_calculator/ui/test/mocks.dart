import 'package:mocktail/mocktail.dart';
import 'package:rxdart/rxdart.dart';
import 'package:wio_common_feature_transaction_api/index.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_account_api/wio_feature_account_api.dart';
import 'package:wio_feature_carbon_calculator_api/carbon_calculator_api.dart';
import 'package:wio_feature_carbon_calculator_ui/src/pages/carbon_calculation_dashboard/analytics/carbon_calculator_dashboard_analytics.dart';

class MockAccountInteractor extends Mock implements AccountInteractor {}

class MockAccountNotificationChannel extends Mock
    implements AccountNotificationChannel {}

class MockCarbonCalculationInteractor extends Mock
    implements CarbonCalculationInteractor {}

class MockTransactionMediator extends Mock implements TransactionsMediator {}

class MockCarbonCalculatorEmissionInteractor extends Mock
    implements CarbonCalculatorEmissionInteractor {}

class MockCarbonCalculatorStateController
    implements CarbonCalculatorStateController {
  final _currentState = BehaviorSubject.seeded(
    const CarbonCalculatorState.disabled(),
  );

  @override
  Stream<CarbonCalculatorState> get carbonCalculatorStateStream =>
      _currentState.stream;

  @override
  Future<void> clear() async {
    await _currentState.close();
  }

  @override
  CarbonCalculatorState get currentState => _currentState.value;

  @override
  void updateState({required CarbonCalculatorState state}) {
    _currentState.add(state);
    switch (state) {
      case CarbonCalculatorStateOnBoarded():
        _processOnboarding();
        break;
      case CarbonCalculatorStateEnabled():
        _processEnabled();
        break;
      case CarbonCalculatorStateDisabled():
      case CarbonCalculatorStateTransactionReady():
      case CarbonCalculatorStateTransactionProcessing():
        break;
    }
  }

  Future<void> _processOnboarding() async {
    _currentState
      ..add(const CarbonCalculatorState.processing())
      ..add(const CarbonCalculatorState.ready());
  }

  Future<void> _processEnabled() async {
    _currentState.add(const CarbonCalculatorState.ready());
  }
}

class FakeDateSelectionBottomSheetConfig extends Fake
    implements BottomSheetNavigationConfig<DateTime?> {
  @override
  String toString() {
    return 'FakeDateSelectionBottomSheetConfig';
  }
}

class MockUpdateCarbonCalculatorStatusUseCase extends Mock
    implements UpdateCarbonCalculatorStatusUseCase {}

class MockCarbonCalculatorOnboardingInteractor extends Mock
    implements CarbonCalculatorOnboardingInteractor {}

class MockCarbonCalculatorDashboardAnalytics extends Mock
    implements CarbonCalculatorDashboardAnalytics {}
