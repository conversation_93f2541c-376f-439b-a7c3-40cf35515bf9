import 'package:wio_feature_carbon_calculator_api/carbon_calculator_api.dart';

/// {@macro carbonCalculator.onboardingInteractor}
class CarbonCalculatorOnboardingInteractorImpl
    implements CarbonCalculatorOnboardingInteractor {
  final UpdateCarbonCalculatorStatusUseCase
      _updateCarbonCalculatorStatusUseCase;
  final GetCarbonCalculatorStatusUseCase _getCarbonCalculatorStatusUseCase;
  final CarbonCalculatorStateController _stateController;

  const CarbonCalculatorOnboardingInteractorImpl({
    required UpdateCarbonCalculatorStatusUseCase
        updateCarbonCalculatorStatusUseCase,
    required GetCarbonCalculatorStatusUseCase getCarbonCalculatorStatusUseCase,
    required CarbonCalculatorStateController stateController,
  })  : _updateCarbonCalculatorStatusUseCase =
            updateCarbonCalculatorStatusUseCase,
        _getCarbonCalculatorStatusUseCase = getCarbonCalculatorStatusUseCase,
        _stateController = stateController;

  /// {@macro carbonCalculator.getUsecase}
  @override
  Future<CarbonCalculatorStatus> getCarbonCalculatorStatus() async {
    try {
      final status = await _getCarbonCalculatorStatusUseCase.call();
      _updateCCStatus(status: status.isEnabled);

      return status;
    } on Object catch (_) {
      // If we can't get the status, we assume that the user is enrolled
      // to hide carbon calculator item
      // (copied from Slava's point in ToDoHandler)
      _stateController.updateState(
        state: const CarbonCalculatorState.enabled(),
      );
      rethrow;
    }
  }

  /// {@macro carbonCalculator.updateUsecase}
  @override
  Future<void> updateCarbonCalculatorStatus({required bool status}) async {
    await _updateCarbonCalculatorStatusUseCase(status: status);

    if (status) {
      /// Updating to onboarding status when true
      /// because after that it will move to ready
      /// state for carbon calculation on transactions.
      _stateController.updateState(
        state: const CarbonCalculatorState.onboarded(),
      );
    } else {
      _stateController.updateState(
        state: const CarbonCalculatorState.disabled(),
      );
    }
  }

  void _updateCCStatus({required bool status}) {
    if (status) {
      /// This is the case when user is already onboarded
      /// and coming back again after that.
      /// In this case it will be an enabled state.
      _stateController.updateState(
        state: const CarbonCalculatorState.enabled(),
      );
    } else {
      _stateController.updateState(
        state: const CarbonCalculatorState.disabled(),
      );
    }
  }
}
