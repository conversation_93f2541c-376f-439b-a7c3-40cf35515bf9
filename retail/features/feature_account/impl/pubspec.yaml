name: feature_account_impl
publish_to: none
version: 0.0.1
environment: 
  sdk: '>=3.6.0 <4.0.0'
dependencies: 
  collection: 1.19.0
  common_feature_fx_api:
    path: ../../../../common/features/feature_fx/api
  common_feature_toggle_api:
    path: ../../../../common/tools/feature_toggle/api
  data:
    path: ../../../../core/data
  di: 
    path: ../../../../core/di
  domain: 
    path: ../../../../core/domain
  flutter: 
    sdk: flutter
  freezed_annotation: 2.4.4
  intl: 0.19.0
  json_annotation: 4.9.0
  logging_api: 
    path: ../../../../core/logging/api
  path: 1.9.0
  path_provider: 2.1.5
  quiver: 3.2.2
  rxdart: 0.28.0
  wio_app_core_api: 
    path: ../../../../core/app_core/api
  wio_feature_account_api: 
    path: ../../../../common/features/feature_account/api
  wio_feature_account_data_api: 
    path: ../../../core/data/account/api
  wio_feature_error_domain_api:
    path: ../../../../common/features/feature_error/api
  wio_feature_family_banking_api:
    path: ../../feature_family_banking/api
  wio_feature_faq_api:
    path: ../../../../common/features/feature_faq/api
  wio_feature_saving_spaces_api: 
    path: ../../../../common/features/feature_saving_spaces/api
  wio_retail_dotenv: 
    path: ../../../core/dotenv
dev_dependencies:
  build_runner: 2.4.14
  core_lints: 
    path: ../../../../tooling/core_lints
  freezed: 2.5.7
  import_sorter: 4.6.0
  json_serializable: 6.9.0
  mocktail: 1.0.4
  test: 1.25.8
  tests: 
    path: ../../../../core/tests/impl
