name: wio_feature_account_ui
version: 0.0.1
publish_to: none
environment:
  flutter: 3.27.3
  sdk: '>=3.6.0 <4.0.0'
dependencies:
  collection: 1.19.0
  common_feature_fx_api:
    path: ../../../../common/features/feature_fx/api
  common_feature_toggle_api:
    path: ../../../../common/tools/feature_toggle/api
  country_flag:
    path: ../../../../common/country_flag
  di:
    path: ../../../../core/di
  domain:
    path: ../../../../core/domain
  feature_dashboard_api:
    path: ../../feature_dashboard/api
  feature_faq_api:
    path: ../../feature_faq/api
  flutter:
    sdk: flutter
  flutter_bloc: 9.0.0
  freezed_annotation: 2.4.4
  logging_api:
    path: ../../../../core/logging/api
  provider: 6.1.2
  rxdart: 0.28.0
  ui:
    path: ../../../../core/ui
  ui_kit_legacy_core:
    path: ../../../../ui_kit_legacy/ui_kit_core
  ui_kit_legacy_mobile:
    path: ../../../../ui_kit_legacy/ui_kit_mobile
  wio_app_core_api:
    path: ../../../../core/app_core/api
  wio_common_feature_context_faq_api:
    path: ../../../../common/features/feature_context_faq/api
  wio_common_feature_payments_api:
    path: ../../../../common/features/feature_payments/api
  wio_common_feature_transaction_api:
    path: ../../../../common/features/feature_transactions/api
  wio_core_navigation_api:
    path: ../../../../core/navigation/api
  wio_core_navigation_ui:
    path: ../../../../core/navigation/ui
  wio_feature_account_api:
    path: ../../../../common/features/feature_account/api
  wio_feature_common_toast_message_api:
    path: ../../../../common/tools/toast_message/api
  wio_feature_contact_support_api:
    path: ../../../../common/features/feature_contact_support/api
  wio_feature_error_domain_api:
    path: ../../../../common/features/feature_error/api
  wio_feature_family_banking_api:
    path: ../../../features/feature_family_banking/api
  wio_feature_payments_v2_ui:
    path: ../../feature_payments_v2/ui
  wio_feature_saving_spaces_api:
    path: ../../../../common/features/feature_saving_spaces/api
  wio_feature_statements_api:
    path: ../../feature_statements/api
  wio_feature_status_view_api:
    path: ../../../../common/features/feature_status_view/api
  wio_feature_user_api:
    path: ../../../../common/features/feature_user/api
dev_dependencies:
  bloc_test: 10.0.0
  build_runner: 2.4.14
  core_lints:
    path: ../../../../tooling/core_lints
  freezed: 2.5.7
  mocktail: 1.0.4
  tests:
    path: ../../../../core/tests/impl
  tests_ui:
    path: ../../../../core/tests/ui
flutter:
  uses-material-design: true
