import 'package:ui/ui.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_account_api/wio_feature_account_api.dart';
import 'package:wio_feature_account_ui/feature_account_mobile_ui.dart';
import 'package:wio_feature_account_ui/src/bottom_sheets/shared_account_closure/closure_confirmation/shared_account_closure_state.dart';
import 'package:wio_feature_account_ui/src/common/status_screen_config_factory.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';

final class SharedAccountClosureCubit
    extends BaseCubit<SharedAccountClosureState> {
  final NavigationProvider _navigationProvider;
  final FamilyBankingAccountInteractor _familyBankingAccountInteractor;
  final AccountInteractor _accountInteractor;
  final FamilyBankingFlow _familyBankingFlow;
  final CommonErrorHandler _commonErrorHandler;
  final AccountLocalizations _l10n;

  SharedAccountClosureCubit({
    required AccountDetails account,
    required NavigationProvider navigationProvider,
    required FamilyBankingAccountInteractor familyBankingAccountInteractor,
    required AccountInteractor accountInteractor,
    required FamilyBankingFlow familyBankingFlow,
    required CommonErrorHandler commonErrorHandler,
    required AccountLocalizations l10n,
  })  : _navigationProvider = navigationProvider,
        _familyBankingAccountInteractor = familyBankingAccountInteractor,
        _accountInteractor = accountInteractor,
        _familyBankingFlow = familyBankingFlow,
        _commonErrorHandler = commonErrorHandler,
        _l10n = l10n,
        super(SharedAccountClosureState.idle(account: account));

  void closeFamilyAccount() {
    state.mapOrNull(
      idle: (it) async {
        try {
          emit(SharedAccountClosureState.processing(account: it.account));
          final result = await _familyBankingAccountInteractor
              .initiateSharedAccountClosure(
            accountId: it.account.id,
            accountNickname: it.account.nickname ?? it.account.name,
          );
          if (result is SharedAccountClosureResultClosed) {
            await _accountInteractor.refreshAccounts();
          }
          await _navigationProvider.navigateTo(
            _statusPageFeatureNavigationConfig(it.account),
          );
          // Go to family dashboard
          await _familyBankingFlow.openFamilyHub(
            initialTab: FamilyTab.accounts,
          );
        } on Exception catch (e) {
          _commonErrorHandler.handleError(e);
          emit(SharedAccountClosureState.idle(account: it.account));
        }
      },
    );
  }

  FeatureNavigationConfig _statusPageFeatureNavigationConfig(
    AccountDetails account,
  ) {
    return switch (account.type) {
      AccountType.jointCurrentAccount => StatusScreenConfigFactory.success(
          title: _l10n.familyAccountClosureSuccessScreenTitle,
          subtitle: _l10n.familyAccountClosureSuccessScreenSubTitle,
          primaryButtonTitle:
              _l10n.familyAccountClosureSuccessScreenButtonTitle,
        ),
      AccountType.pocketAccount => StatusScreenConfigFactory.success(
          title: _l10n.pocketClosureSuccessScreenTitle,
          subtitle: _l10n.pocketClosureSuccessScreenSubTitle(
            account.nickname ?? account.name,
          ),
          primaryButtonTitle: _l10n.pocketClosureSuccessScreenButtonTitle,
        ),
      _ => throw UnimplementedError('Account type not supported'),
    };
  }

  void pop() => _navigationProvider.goBack();

  @override
  String toString() => 'FamilyAccountClosureCubit';
}
