import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_account_ui/feature_account_mobile_ui.dart';
import 'package:wio_feature_account_ui/src/bottom_sheets/multi_currency_account_closure/cubit/multi_currency_account_closure_cubit.dart';
import 'package:wio_feature_account_ui/src/common/bottom_sheet_button_list.dart';
import 'package:wio_feature_account_ui/src/common/bottom_sheet_header.dart';
import 'package:wio_feature_account_ui/src/common/bottom_sheet_structure.dart';

class MultiCurrencyAccountClosureNegativeBalanceBottomSheet
    extends StatelessWidget {
  const MultiCurrencyAccountClosureNegativeBalanceBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          DependencyProvider.get<MultiCurrencyAccountClosureCubit>(),
      child: const _Body(),
    );
  }
}

class _Body extends StatelessWidget {
  const _Body();

  @override
  Widget build(BuildContext context) {
    final l10n = AccountLocalizations.of(context);
    return BottomSheetStructure(
      children: [
        BottomSheetHeader(
          title: l10n.multiCurrencyAccountCloseNegativeBalanceTitle,
          description: l10n.multiCurrencyAccountCloseNegativeBalanceSubtitle,
        ),
        Space.fromSpacingVertical(Spacing.s6),
        BottomSheetButtonList(
          primaryButtonTitle:
              l10n.multiCurrencyAccountCloseNegativeBalanceButton,
          secondaryButtonTitle: l10n.multiCurrencyAccountCloseBackButton,
          onPrimaryButtonPressed: () =>
              context.read<MultiCurrencyAccountClosureCubit>().onOpenAddMoney(),
          onSecondaryButtonPressed: () =>
              context.read<MultiCurrencyAccountClosureCubit>().pop(),
        ),
      ],
    );
  }
}
