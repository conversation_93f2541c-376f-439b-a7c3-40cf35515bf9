import 'package:wio_feature_statements_api/statements_api.dart';
import 'package:wio_feature_statements_impl/src/data/repositories/statement_repository.dart';

class StatementInteractorImpl implements StatementInteractor {
  final StatementRepository _repository;

  const StatementInteractorImpl({
    required StatementRepository repository,
  }) : _repository = repository;

  @override
  List<StatementDate> getAvailableStatementDates(DateTime accountDate) =>
      _repository.getAvailableStatementDates(accountDate);

  @override
  Future<StatementFile> getStatementByDate(
    StatementDate date, {
    StatementFormat format = StatementFormat.pdf,
  }) =>
      _repository.getStatementByDate(date, format: format);

  @override
  Future<StatementFile> getStatementByFilters({
    required DateTime dateFrom,
    required DateTime dateTo,
    StatementFormat format = StatementFormat.pdf,
    StatementFilters? filters,
  }) =>
      _repository.getStatementByFilters(
        dateFrom: dateFrom,
        dateTo: dateTo,
        filters: filters,
        format: format,
      );

  @override
  Future<StatementFilters> getStatementFilters() =>
      _repository.getStatementFilters();

  @override
  Future<String> saveStatementFile({
    required StatementFileData data,
    required String fileName,
  }) =>
      _repository.saveStatementFile(file: data, name: fileName);
}
