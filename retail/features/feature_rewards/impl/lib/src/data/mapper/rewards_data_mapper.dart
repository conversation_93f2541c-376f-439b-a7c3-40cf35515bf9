import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_account_data_api/account_data_api.dart' as dtos;
import 'package:wio_feature_account_data_api/models/accounts.enums.swagger.dart';
import 'package:wio_feature_rewards_api/rewards_api.dart';

class ConfigConstants {
  static const myMoneyKey = 'percentageMyMoney';
  static const creditKey = 'percentageCredit';
  static const minBrokerAmountKey = 'minBrokerAmount';
  static const minBrokerCurrency = 'minBrokerCurrency';
  static const monthlyCashbackLimit = 'monthlyCashbackLimit';
  static const monthlyCashbackLimitCurrency = 'monthlyCashbackLimitCurrency';
  static const myMoneyNonAEDKey = 'percentageMyMoneyNonAED';
}

class RewardsDataMapper {
  static final _defaultCurrency = Currency.aed;

  const RewardsDataMapper();

  CashBackDetail mapDtoToCashBackDomainModel(
    dtos.CashbackRedeemHistoryResponse response,
  ) {
    if (response.id == null) {
      throw Exception('Cash back id cannot be null');
    }

    final redeemAmount = Money.fromNumWithCurrency(
      response.totalAmount ?? 0.0,
      _defaultCurrency,
    );

    return CashBackDetail(
      id: response.id!,
      // NOTE: destinationAccountId is null when status = PENDING
      destinationAccountId: response.destinationAccountId,
      status: response.status == null
          ? RedeemStatus.unknown
          : _mapRedeemStatus(response.status!),
      mode: response.transactionMode == null
          ? TransactionMode.unknown
          : _mapMode(response.transactionMode!),
      amount: redeemAmount,
      monthYear: response.monthYear!,
    );
  }

  CashbackSettings mapToCashbackSettings(dtos.CashbackSettings dto) {
    return CashbackSettings(
      customerId: dto.customerIdentifier,
      processingMode: _mapToCashbackProcessingMode(dto),
      lastChangeDetails: _mapToLastDetails(dto.lastChange),
    );
  }

  dtos.CashbackSettingsRequest mapToCashbackSettingsRequestDto(
    CashbackSettingsUpdate request,
  ) =>
      dtos.CashbackSettingsRequest(
        destinationAccountId: request.newMode.accountId,
        processingMode: _mapToCashbackProcessingModeDto(request.newMode),
        details: _mapToCashbackSettingsDetails(request.newMode),
      );

  RedeemStatus _mapRedeemStatus(
    dtos.CashbackRedeemHistoryResponseStatus status,
  ) {
    switch (status) {
      case dtos.CashbackRedeemHistoryResponseStatus.processing:
        return RedeemStatus.processing;
      case dtos.CashbackRedeemHistoryResponseStatus.pending:
        return RedeemStatus.pending;
      case dtos.CashbackRedeemHistoryResponseStatus.completed:
        return RedeemStatus.completed;
      case dtos.CashbackRedeemHistoryResponseStatus.completedDefaultDestination:
        return RedeemStatus.defaultDestination;
      case dtos.CashbackRedeemHistoryResponseStatus.terminated:
        return RedeemStatus.terminated;
      case dtos.CashbackRedeemHistoryResponseStatus.swaggerGeneratedUnknown:
        return RedeemStatus.unknown;
    }
  }

  RewardsConfig mapToConfigDomainModels(
    List<dtos.MyMoneyConfig> models,
  ) {
    final map = models.fold<Map<String, dtos.MyMoneyConfig>>(
      {},
      (result, element) => result..putIfAbsent(element.key, () => element),
    );

    final minBrokerCurrency = map[ConfigConstants.minBrokerCurrency]?.$value;

    final ownMoneyRate = RewardsConfigKey<num>(
      value: num.parse(map[ConfigConstants.myMoneyKey]?.$value ?? '0.0'),
      enabled: map[ConfigConstants.myMoneyKey]?.enabled ?? false,
    );

    final creditMoneyRate = RewardsConfigKey<num>(
      value: num.parse(map[ConfigConstants.creditKey]?.$value ?? '0.0'),
      enabled: map[ConfigConstants.creditKey]?.enabled ?? false,
    );

    final minBrokerAmount = RewardsConfigKey<Money>(
      value: Money.parse(
        map[ConfigConstants.minBrokerAmountKey]?.$value ?? '0.0',
        code: minBrokerCurrency ?? 'USD',
      ),
      enabled: map[ConfigConstants.minBrokerAmountKey]?.enabled ?? false,
    );

    final monthlyCashBackLimit = RewardsConfigKey<Money>(
      value: Money.parse(
        map[ConfigConstants.monthlyCashbackLimit]?.$value ?? '0.0',
        code:
            map[ConfigConstants.monthlyCashbackLimitCurrency]?.$value ?? 'AED',
      ),
      enabled: map[ConfigConstants.minBrokerAmountKey]?.enabled ?? false,
    );

    final ownMoneyNonAEDRate = RewardsConfigKey<num>(
      value: num.parse(map[ConfigConstants.myMoneyNonAEDKey]?.$value ?? '0.0'),
      enabled: map[ConfigConstants.myMoneyNonAEDKey]?.enabled ?? false,
    );

    return RewardsConfig(
      ownMoneyRate: ownMoneyRate,
      creditMoneyRate: creditMoneyRate,
      minBrokerAmount: minBrokerAmount,
      monthlyCashBackLimit: monthlyCashBackLimit,
      ownMoneyNonAEDRate: ownMoneyNonAEDRate,
    );
  }

  Money mapToCurrentMonthCashback(dtos.CurrentMonthCashbackResponse response) {
    return Money.fromNumWithCurrency(response.totalCashback, Currency.aed);
  }

  // Private
  TransactionMode _mapMode(
    dtos.CashbackRedeemHistoryResponseTransactionMode status,
  ) {
    switch (status) {
      case dtos.CashbackRedeemHistoryResponseTransactionMode.deposit:
        return TransactionMode.deposit;
      case dtos.CashbackRedeemHistoryResponseTransactionMode.withdrawal:
        return TransactionMode.withdrawal;
      case dtos.CashbackRedeemHistoryResponseTransactionMode
            .swaggerGeneratedUnknown:
        return TransactionMode.unknown;
    }
  }

  LastChangeDetails? _mapToLastDetails(dtos.LastChange? dto) {
    if (dto == null) {
      return null;
    }
    final lastChange = dto;
    final apiMode = dto.processingMode;
    final mode = _getMode(apiMode);

    final lastDetailsObject = LastChangeDetails(
      processingMode: mode,
      acknowledged: lastChange.acknowledged,
    );

    return lastDetailsObject;
  }

  CashbackProcessingMode _mapToCashbackProcessingMode(
    dtos.CashbackSettings dto,
  ) =>
      switch (dto.processingMode) {
        dtos.CashbackSettingsProcessingMode.currentAccount =>
          CashbackProcessingMode.currentAccount(
            accountId: dto.destinationAccountId,
          ),
        dtos.CashbackSettingsProcessingMode.savingsAccount =>
          CashbackProcessingMode.savingsAccount(
            accountId: dto.destinationAccountId,
          ),
        dtos.CashbackSettingsProcessingMode.broker =>
          CashbackProcessingMode.broker(
            accountId: dto.destinationAccountId,
            // When the 'broker' mode is selected, the details map must have
            // broker and instrument IDs.
            brokerId: ArgumentError.checkNotNull(
              dto.details?['brokerId'] as String?,
              'brokerId is missing in cashback settings details',
            ),
            instrumentId: ArgumentError.checkNotNull(
              dto.details?['instrumentId'] as String?,
              'instrumentId is missing in cashback settings details',
            ),
          ),
        _ => throw Exception(
            'Unknown cashback processing mode: ${dto.processingMode}',
          ),
      };

  dtos.CashbackSettingsRequestProcessingMode _mapToCashbackProcessingModeDto(
    CashbackProcessingMode mode,
  ) =>
      switch (mode) {
        CashbackToCurrentAccountMode() =>
          dtos.CashbackSettingsRequestProcessingMode.currentAccount,
        CashbackToSavingsAccountMode() =>
          dtos.CashbackSettingsRequestProcessingMode.savingsAccount,
        CashbackToBrokerMode() =>
          dtos.CashbackSettingsRequestProcessingMode.broker,
      };

  Map<String, String>? _mapToCashbackSettingsDetails(
    CashbackProcessingMode mode,
  ) =>
      switch (mode) {
        CashbackToBrokerMode(:final brokerId, :final instrumentId) => {
            'brokerId': brokerId,
            'instrumentId': instrumentId,
          },
        _ => null,
      };

  LastAutomationProcessingMode _getMode(LastChangeProcessingMode? mode) {
    switch (mode) {
      case LastChangeProcessingMode.currentAccount:
        return LastAutomationProcessingMode.current;
      case LastChangeProcessingMode.savingsAccount:
        return LastAutomationProcessingMode.savingAccount;
      case LastChangeProcessingMode.broker:
        return LastAutomationProcessingMode.broker;
      default:
        throw Exception('LastDetailsProcessingMode cannot be null');
    }
  }
}
