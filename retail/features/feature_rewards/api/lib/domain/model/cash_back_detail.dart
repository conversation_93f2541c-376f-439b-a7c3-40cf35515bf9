import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/money.dart';

part 'cash_back_detail.freezed.dart';

enum RedeemStatus {
  pending,
  processing,
  completed,
  terminated,

  /// Completed on default destination(selected option)
  defaultDestination,
  unknown,
}

enum TransactionMode {
  deposit,
  withdrawal,
  unknown,
}

@freezed
class CashBackDetail with _$CashBackDetail {
  const factory CashBackDetail({
    required String id,
    required RedeemStatus status,
    required TransactionMode mode,
    required Money amount,
    required String monthYear,
    String? destinationAccountId,
  }) = _CashBackDetail;
}
