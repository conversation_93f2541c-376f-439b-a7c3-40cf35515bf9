import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_pricing_plan_api/index.dart';
import 'package:wio_feature_pricing_plan_api/models/plan_contenful_details.dart';

part 'plan_list_detail_state.freezed.dart';

@freezed
class PlanListDetailsState with _$PlanListDetailsState {
  const PlanListDetailsState._();

  const factory PlanListDetailsState.idle({
    required List<PlanContentDetails> plansDetail,
    required PricingPlansContent plansContent,
    required PricingPlanSource source,
  }) = _PlanListDetailsIdleState;

  const factory PlanListDetailsState.loading() = _PlanListDetailsLoadingState;

  const factory PlanListDetailsState.error({
    required PricingPlanSource source,
  }) = _PlanListDetailsErrorState;

  bool get canGoBack => maybeMap(orElse: () => true, loading: (_) => false);
}
