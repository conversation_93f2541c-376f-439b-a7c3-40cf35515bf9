import 'package:collection/collection.dart';
import 'package:logging_api/logging.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ui/ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_pricing_plan_api/index.dart';
import 'package:wio_feature_pricing_plan_api/models/plan_contenful_details.dart';
import 'package:wio_feature_pricing_plan_api/navigation/plan_comparison_bottom_sheet_navigation_config.dart';
import 'package:wio_feature_pricing_plan_ui/bottom_sheets/plan_connection_confirmation/model/plan_connection_confirmation_bottom_sheet_model.dart';
import 'package:wio_feature_pricing_plan_ui/navigation/bottom_sheet_configs/plan_connection_confirmation_bottom_sheet_navigation_config.dart';
import 'package:wio_feature_pricing_plan_ui/navigation/screen_configs/plan_detail_screen_navigation_config.dart';
import 'package:wio_feature_pricing_plan_ui/screens/plans_list_v2/analytics/plans_list_detail_analytics.dart';
import 'package:wio_feature_pricing_plan_ui/screens/plans_list_v2/cubit/plan_list_detail_state.dart';

class PlanListDetailCubit extends BaseCubit<PlanListDetailsState> {
  final PricingPlanInteractor _interactor;
  final PricingPlansInfoAnalytics _pricingPlansInfoAnalytics;
  final Logger _logger;
  final NavigationProvider _navigation;

  PlanListDetailCubit({
    required PricingPlanInteractor interactor,
    required NavigationProvider navigation,
    required PricingPlansInfoAnalytics pricingPlansInfoAnalytics,
    required Logger logger,
  })  : _interactor = interactor,
        _navigation = navigation,
        _pricingPlansInfoAnalytics = pricingPlansInfoAnalytics,
        _logger = logger,
        super(
          const PlanListDetailsState.loading(),
        );

  @override
  String toString() => 'PlanListDetailCubit';

  Future<void> init({required PricingPlanSource source}) async {
    await Rx.forkJoin2(
      _fetchPlansContent(source),
      _fetchPlans(source),
      (plansContent, plans) => _handlePlansData(plansContent, plans, source),
    )
        .logError(_logger)
        .where((_) => !isClosed)
        .withError((p0) => _handleError(p0, source))
        .complete();
  }

  void showDetails(
    PlanContentDetails planContentDetails,
    PricingPlan? currentPlan,
  ) {
    _pricingPlansInfoAnalytics.clickPlanAllBenefits(planContentDetails.name);

    state.maybeMap(
      orElse: () => _logger.warning('Inconsistent state : $state'),
      idle: (it) => _navigation.push(
        PlanDetailScreenNavigationConfig(
          planDetails: planContentDetails,
          source: it.source,
          currentPlan: currentPlan,
        ),
      ),
    );
  }

  void comparePlans() {
    _pricingPlansInfoAnalytics.clickComparePlans();
    state.maybeMap(
      orElse: () => _logger.warning('Inconsistent state : $state'),
      idle: (it) => _navigation.showBottomSheet<void>(
        PlanComparisonBottomSheetNavigationConfig(source: it.source),
      ),
    );
  }

  Stream<List<PlanContentDetails>> _fetchPlansContent(
    PricingPlanSource source,
  ) =>
      _interactor.getAllPlanContent().toStream();

  Stream<PricingPlansContent> _fetchPlans(PricingPlanSource source) =>
      _interactor.getPricingPlan(source).toStream();

  void _handleError(Object? error, PricingPlanSource source) {
    safeEmit(PlanListDetailsState.error(source: source));
  }

  void _handlePlansData(
    List<PlanContentDetails> plansContent,
    PricingPlansContent eligiblePlans,
    PricingPlanSource source,
  ) {
    final availablePlans = <PlanContentDetails>[];

    for (final plan in eligiblePlans.pricingPlans) {
      final eligiblePlan = plansContent.firstWhereOrNull(
        (e) => e.planId == plan.planId,
      );

      if (eligiblePlan == null) {
        _logger.warning('Plan not found for planId: ${plan.planId}');
        continue;
      }

      availablePlans.add(
        eligiblePlan.copyWith(
          isSelectablePlan: plan.isSelectablePlan,
        ),
      );
    }

    if (availablePlans.isEmpty) {
      final contentfulIds = plansContent.map((it) => it.planId).toList();
      final planIds =
          eligiblePlans.pricingPlans.map((it) => it.planId).toList();

      _logger.error(
        'List of available plans after filtering is empty: '
        'IDs from BE: $planIds, '
        'IDs from contentful: $contentfulIds',
      );
    }

    safeEmit(
      PlanListDetailsState.idle(
        plansDetail: availablePlans.reversed.toList(),
        plansContent: eligiblePlans,
        source: source,
      ),
    );
  }

  void planShowed(String planName) =>
      _pricingPlansInfoAnalytics.showPlan(planName);

  void showConfirmationSheet(
    PlanContentDetails planContentDetails,
    PricingPlan? currentPlan,
  ) {
    state.mapOrNull(
      idle: (it) {
        _pricingPlansInfoAnalytics.clickPlanPreview(planContentDetails.name);

        final confirmationConfig =
            PlanConnectionConfirmationBottomSheetConfiguration.refactored(
          source: it.source,
          planId: planContentDetails.planId,
          isSpecialPlan: planContentDetails.isSpecialPlan,
          description: planContentDetails.planTitle,
          planCta: planContentDetails.getPlanCta,
          planName: planContentDetails.name,
          fee: planContentDetails.fee,
          feeInfo: planContentDetails.feeInfo,
          planType: planContentDetails.planType,
          planConfirmationSection: planContentDetails.planConfirmationSection,
        );

        _navigation.showBottomSheet(
          PlanConnectionConfirmationBottomSheetNavigationConfig(
            configuration: confirmationConfig,
            type: PlanConnectionConfirmationVersionType.refactored,
            currentPlan: currentPlan,
          ),
        );
      },
    );
  }
}
