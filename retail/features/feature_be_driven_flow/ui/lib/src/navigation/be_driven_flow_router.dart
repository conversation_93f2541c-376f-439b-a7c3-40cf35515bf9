// ignore_for_file: type_literal_in_constant_pattern

import 'package:flutter/widgets.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_be_driven_flow_api/navigation/be_driven_flow_feature_navigation_config.dart';
import 'package:wio_feature_be_driven_flow_ui/src/screens/flow_host/backend_driven_flow_host.dart';

class BeDrivenFlowRouter extends NavigationRouter {
  @override
  Route<Object?> getScreenRoute(RouteSettings settings) {
    final config = settings.arguments;
    switch (config.runtimeType) {
      case BeDrivenFlowFeatureNavigationConfig:
        final args = config as BeDrivenFlowFeatureNavigationConfig;
        return toRoute(
          BackendDrivenFlowHost(
            flowConfig: args.flowConfig,
          ),
          settings,
        );

      default:
        throw Exception('Unknown $config for the $runtimeType');
    }
  }
}
