// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.
// @dart=2.12
// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = MessageLookup();

typedef String? MessageIfAbsent(String? messageStr, List<Object>? args);

class MessageLookup extends MessageLookupByLibrary {
  @override
  String get localeName => 'ar';

  @override
  final Map<String, dynamic> messages =
      _notInlinedMessages(_notInlinedMessages);

  static Map<String, dynamic> _notInlinedMessages(_) => {
        'accountActivationInProgress':
            MessageLookupByLibrary.simpleMessage('تفعيل الحساب قيد الإجراء'),
        'accountsItemName': MessageLookupByLibrary.simpleMessage('حسابات'),
        'activeProductsSectionTitle':
            MessageLookupByLibrary.simpleMessage('منتجاتك'),
        'allProductsSectionTitle':
            MessageLookupByLibrary.simpleMessage('اكتشف المزيد من المنتجات'),
        'borrowItemName': MessageLookupByLibrary.simpleMessage('استعار'),
        'brokerActionRequiredMessage': MessageLookupByLibrary.simpleMessage(
            '🤔 ما زلنا بحاجة إلى مزيد من المعلومات منك قبل أن نتمكن من البدء في الاستثمار'),
        'brokerInProgressTryAgainLater': MessageLookupByLibrary.simpleMessage(
            'الوسيط قيد التقدم - حاول مرة أخرى لاحقًا'),
        'brokerProcessingMessage': MessageLookupByLibrary.simpleMessage(
            '🤔 ما زلنا بحاجة إلى مزيد من الوقت لمعالجة طلبك'),
        'brokerProductName':
            MessageLookupByLibrary.simpleMessage('ويو للإستثمار'),
        'cardsItemName': MessageLookupByLibrary.simpleMessage('البطاقات'),
        'chequebookItemName': MessageLookupByLibrary.simpleMessage('الشيكات'),
        'comingSoon': MessageLookupByLibrary.simpleMessage('قريباً'),
        'familyHubNotOnboardedCardSubtitle':
            MessageLookupByLibrary.simpleMessage('أضف أفراد عائلتك للبدء'),
        'familyHubNotOnboardedCardTitle':
            MessageLookupByLibrary.simpleMessage('عائلة ويو'),
        'familyHubOnboardedCardTitle':
            MessageLookupByLibrary.simpleMessage('عائلة ويو'),
        'hello': MessageLookupByLibrary.simpleMessage('مرحبًا'),
        'homeItemName': MessageLookupByLibrary.simpleMessage('منزل'),
        'investItemName': MessageLookupByLibrary.simpleMessage('استثمر'),
        'investProductNotAvailableMessage':
            MessageLookupByLibrary.simpleMessage('المنتج الاستثماري غير متوفر'),
        'ipoItemName': MessageLookupByLibrary.simpleMessage('الاكتتاب'),
        'lendingItemName': MessageLookupByLibrary.simpleMessage('ائتمان'),
        'payBillsItemName': MessageLookupByLibrary.simpleMessage('الفواتير'),
        'productBlockedToastMessage':
            MessageLookupByLibrary.simpleMessage('المنتج محظور بشكل دائم'),
        'referralsButtonTitle':
            MessageLookupByLibrary.simpleMessage('دعوة الأصدقاء 💰'),
        'retailProductName': MessageLookupByLibrary.simpleMessage('ويو'),
        'rewardsItemName': MessageLookupByLibrary.simpleMessage('المكافآت'),
        'saveItemName': MessageLookupByLibrary.simpleMessage('حفظ'),
        'sendItemName': MessageLookupByLibrary.simpleMessage('إرسال'),
        'shareHoldersSubtitle': MessageLookupByLibrary.simpleMessage(
            'مرخصة وخاضعة للرقابة من قبل البنك المركزي الإماراتي'),
        'shareHoldersTitle':
            MessageLookupByLibrary.simpleMessage('مساهمي Wio Bank PJSC'),
        'shoryInsuranceItemName': MessageLookupByLibrary.simpleMessage('تأمين'),
        'spendHubCoachMarkButtonTitle':
            MessageLookupByLibrary.simpleMessage('فهمتها'),
        'spendHubCoachMarkSubtitle': MessageLookupByLibrary.simpleMessage(
            'لقد أعدنا تسمية قسم \"البطاقات\" إلى \"الإنفاق\" لتتمكن من الاطلاع على ميزانيتك بشكل أفضل. ستظل بطاقاتك موجودة هناك، ضمن علامة تبويب خاصة بها.'),
        'spendHubCoachMarkTitle':
            MessageLookupByLibrary.simpleMessage('تصبح البطاقات إنفاقًا'),
        'spendItemName': MessageLookupByLibrary.simpleMessage('أنفق'),
        'tutorialBottomSheetActionText':
            MessageLookupByLibrary.simpleMessage('لنبدأ!'),
        'tutorialBottomSheetHeader':
            MessageLookupByLibrary.simpleMessage('دع أموالك تعمل لديك'),
        'tutorialBottomSheetText': MessageLookupByLibrary.simpleMessage(
            'تحكم في استثماراتك وابدأ التداول على الفور'),
        'unavailableProductToastMessage': MessageLookupByLibrary.simpleMessage(
            'يرجى إكمال إعداد البيع بالتجزئة أولاً'),
        'wioOne': MessageLookupByLibrary.simpleMessage('مركز')
      };
}
