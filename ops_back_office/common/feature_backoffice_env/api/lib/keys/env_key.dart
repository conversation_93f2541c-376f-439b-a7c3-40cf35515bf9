import 'package:wio_app_core_api/index.dart';

/// Class for the keys of the environment variables
class BackOfficeEnvKeys implements EnvKey {
  /// The tenant id for the msal
  static const BackOfficeEnvKeys msalTenantId =
      BackOfficeEnvKeys._('TENANT_ID');

  /// The client id for the msal
  static const BackOfficeEnvKeys msalClientId =
      BackOfficeEnvKeys._('CLIENT_ID');

  /// The code of the key for base rest api url
  static const BackOfficeEnvKeys restApiHost =
      BackOfficeEnvKeys._('REST_API_HOST');

  /// The code of the key for the firebase api key
  static const BackOfficeEnvKeys fbApiKey = BackOfficeEnvKeys._('FB_API_KEY');

  /// The code of the key for the firebase auth domain
  static const BackOfficeEnvKeys fbAuthDomain =
      BackOfficeEnvKeys._('FB_AUTH_DOMAIN');

  /// The code of the key for the firebase project id
  static const BackOfficeEnvKeys fbProjectId =
      BackOfficeEnvKeys._('FB_PROJECT_ID');

  /// The code of the key for the firebase storage bucket
  static const BackOfficeEnvKeys fbStorageBucket =
      BackOfficeEnvKeys._('FB_STORAGE_BUCKET');

  /// The code of the key for the firebase messaging sender id
  static const BackOfficeEnvKeys fbMessagingSenderId =
      BackOfficeEnvKeys._('FB_MESSAGING_SENDER_ID');

  /// The code of the key for the firebase app id
  static const BackOfficeEnvKeys fbAppId = BackOfficeEnvKeys._('FB_APP_ID');

  /// The code of the key for app environment
  static const BackOfficeEnvKeys appEnv = BackOfficeEnvKeys._('APP_ENV');

  /// The code of the key for api environment
  static const BackOfficeEnvKeys apiVersion = BackOfficeEnvKeys._('API_ENV');

  /// The code of the key for FO api environment
  static const BackOfficeEnvKeys frontOfficeDomain =
      BackOfficeEnvKeys._('FO_ENV');

  @override
  final String code;

  const BackOfficeEnvKeys._(this.code);
}
