import 'package:wio_feature_app_common_api/app_common_api.dart';

/// Encapsulates data that based on which the application is running
///
/// Some data may not be available based on how the app is run. For example:
/// if the app is run independently, the csmProductType will not be available
/// and entityId will not be available as such the [AppContext] will be
/// created with the [AppContext.independent] factory constructor
sealed class AppContext {
  const factory AppContext.csm({
    required String entityId,
    required CsmProduct csmProductType,
  }) = CsmAppContext;

  const factory AppContext.independent() = IndependentAppContext;

  /// Returns based on app context if the app is running independently
  bool get isAppRunningIndependently => switch (this) {
        final IndependentAppContext _ => true,
        final CsmAppContext _ => false,
      };
}

/// [AppContext] HAS the following type when the 360 app run within a CSM
/// product
class CsmAppContext implements AppContext {
  /// [entityId] is the id of the conversation or ticket in the csm
  final String entityId;

  /// [csmProductType] is the type of the csm product
  final CsmProduct csmProductType;

  /// Creates an instance of [CsmAppContext]
  const CsmAppContext({
    required this.entityId,
    required this.csmProductType,
  });

  @override
  bool get isAppRunningIndependently => false;
}

/// [AppContext] have the following type when the 360 app run independently of
/// any CSM product
class IndependentAppContext implements AppContext {
  /// Creates an instance of [IndependentAppContext]
  const IndependentAppContext();

  @override
  bool get isAppRunningIndependently => true;
}
