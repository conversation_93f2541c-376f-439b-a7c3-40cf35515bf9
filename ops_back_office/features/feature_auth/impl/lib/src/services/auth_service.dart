import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_auth_impl/src/data/dto/user_details_dto.dart';
import 'package:wio_feature_auth_impl/src/data/dto/user_fetching_error.dart';
import 'package:wio_feature_auth_impl/src/services/exceptions/user_fetching_exception.dart';

typedef _Json = Map<String, Object?>;

class _Endpoints {
  static const String userDetailsEndpoint =
      'https://graph.microsoft.com/v1.0/me';
}

abstract class AuthService {
  Future<UserDetails> getUserDetails();
}

class AuthServiceImpl extends RestApiService implements AuthService {
  final IRestApiClient _restApiClient;

  AuthServiceImpl({
    required IRestApiClient restApiClient,
  }) : _restApiClient = restApiClient;

  @override
  Future<UserDetails> getUserDetails() {
    return execute<UserDetails, HttpRequestException>(
      _restApiClient.execute<_Json>(
        RestApiRequest(
          _Endpoints.userDetailsEndpoint,
          method: HttpRequestMethod.get,
        ),
      ),
      (json) => UserDetails.fromJson(json as _Json),
      errorJsonParser: (json, {correlationId}) {
        final response = json as _Json;

        return UserFetchingException(
          error: UserFetchingError.fromJson(response),
        );
      },
    );
  }
}
