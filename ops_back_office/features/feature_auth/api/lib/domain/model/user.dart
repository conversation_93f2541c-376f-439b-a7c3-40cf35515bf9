import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';

/// Encapsulates the user details of the current application user
@freezed
class User with _$User {
  /// Constructor for [User]
  const factory User({
    /// Email of the user
    required String email,

    /// Display name of the user
    required String displayName,

    /// Given name of the user
    required String givenName,

    /// Last name of the user
    required String lastName,

    /// Role of the user
    required String role,
  }) = _User;
}
