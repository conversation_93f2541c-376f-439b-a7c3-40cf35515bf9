import 'package:wio_feature_permissions_api/permissions_api.dart';

/// Exposes a method to resolve permissions
abstract class PermissionsProvider {
  /// Resolves the permissions passed in the list [permissions]
  ///
  /// Return true is agent has all the permissions in the list
  bool hasAllPermission(List<Permission> permissions);

  /// Return true is agent has permissions
  bool hasPermission(Permission permissions);

  /// Return true is agent has any permissions in the list
  bool hasAnyPermission(List<Permission> permissions);
}
