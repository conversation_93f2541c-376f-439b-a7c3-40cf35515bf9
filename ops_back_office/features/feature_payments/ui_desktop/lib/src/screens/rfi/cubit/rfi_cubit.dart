import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_payments_api/domain/index.dart';
import 'package:wio_feature_payments_ui_desktop/src/constants/constants.dart';
import 'package:wio_feature_payments_ui_desktop/src/screens/rfi/cubit/rfi_state.dart';
import 'package:wio_feature_payments_ui_desktop/src/screens/rfi/mapper/rfi_ui_data_mapper.dart';
import 'package:wio_feature_payments_ui_desktop/src/screens/rfi/model/rfi_ui_data.dart';

class RfiCubit extends BaseCubit<RfiState> {
  final String _transferId;

  final PaymentInteractor _paymentInteractor;
  final NavigationProvider _navigationProvider;
  final ToastMessageProvider _toastMessageProvider;
  final RfiUiDataMapper _rfiUiDataMapper;
  final LoadingProvider _loadingProvider;

  RfiCubit({
    required String transferId,
    required PaymentInteractor paymentInteractor,
    required NavigationProvider navigationProvider,
    required ToastMessageProvider toastMessageProvider,
    required RfiUiDataMapper rfiUiDataMapper,
    required LoadingProvider loadingProvider,
  })  : _transferId = transferId,
        _paymentInteractor = paymentInteractor,
        _navigationProvider = navigationProvider,
        _toastMessageProvider = toastMessageProvider,
        _rfiUiDataMapper = rfiUiDataMapper,
        _loadingProvider = loadingProvider,
        super(const RfiState.idle());

  Future<void> onReasonChange(RfiReason? reason) async {
    switch (reason) {
      case RfiReason.amendment:
        {
          safeEmit(const RfiState.loading());

          final existingQuestions = await _paymentInteractor.getRfiQuestions(
            category: RfiQuestionCategory.amendment,
          );

          final uiExistingQuestions = _rfiUiDataMapper
              .mapToRfiUiExistingQuestions(existingQuestions)
              .toList(growable: false);

          safeEmit(
            RfiState.amendment(
              rfiUiData: RfiUiData.amendment(
                uiExistingQuestions: uiExistingQuestions,
                uiCustomQuestions: [],
              ),
            ),
          );
        }
      case null:
        {}
    }
  }

  Future<void> onSubmit(RfiUiData rfiData) async {
    await rfiData.mapOrNull(
      amendment: (amendment) async {
        try {
          _loadingProvider.loading(true);

          final existingQuestions = _rfiUiDataMapper.mapToRfiExistingQuestions(
            amendment.uiExistingQuestions.where((question) => question.checked),
          );

          final existingQuestionIds = existingQuestions.questions
              .map((question) => question.id)
              .toList(growable: false);

          final customQuestions = _rfiUiDataMapper
              .mapToRfiCustomQuestions(
                amendment.uiCustomQuestions,
                RfiQuestionCategory.amendment,
              )
              .toList(growable: false);

          final message = await _paymentInteractor.initiateRfi(
            id: _transferId,
            existingQuestionIds: existingQuestionIds,
            customQuestions: customQuestions,
          );

          if (message != null && message.isNotEmpty) {
            _showSuccessToastMessage(message);
          }
        } on ApiException<Object?> catch (e) {
          _showFailedToastMessage(
            e.message ?? Constants.commonErrorMessage,
          );
        } on Exception catch (_) {
          _showFailedToastMessage(Constants.commonErrorMessage);
        } finally {
          _loadingProvider.loading(false);

          _navigationProvider.goBack();
        }
      },
    );
  }

  void _showSuccessToastMessage(String message) {
    _toastMessageProvider.showRetailMobileThemedToastMessage(
      NotificationToastMessageConfiguration.success(
        message,
      ),
    );
  }

  void _showFailedToastMessage(String message) {
    _toastMessageProvider.showRetailMobileThemedToastMessage(
      NotificationToastMessageConfiguration.error(
        message,
      ),
    );
  }

  @override
  String toString() => 'RfiCubit{}';
}
