import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:ui/cubit/base_cubit.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_account_maintenance_api/account_maintenance_api.dart';
import 'package:wio_feature_account_maintenance_ui_desktop/src/account_maintenance_strings.dart';
import 'package:wio_feature_account_maintenance_ui_desktop/src/extension/enums_extension.dart';
import 'package:wio_feature_account_maintenance_ui_desktop/src/extension/other_extension.dart';
import 'package:wio_feature_account_maintenance_ui_desktop/src/extension/string_extension.dart';
import 'package:wio_feature_account_maintenance_ui_desktop/src/file_handler/file_handler_mixin.dart';
import 'package:wio_feature_account_maintenance_ui_desktop/src/permission_handler/permission_handler_mixin.dart';
import 'package:wio_feature_account_maintenance_ui_desktop/src/screens/accounts_case_details/widget/re_ask/state/re_ask_state.dart';
import 'package:wio_feature_common_error_handler_api/common_error_handler_api.dart';
import 'package:wio_feature_permissions_api/permissions_provider.dart';

class ReAskCubit extends BaseCubit<ReAskState>
    with FileHandlerMixin, PermissionHandlerMixin {
  final RfiInteractor _reAskInteractor;
  final CommonErrorHandler _errorHandler;
  final AccountMaintenanceInteractor _documentInteractor;
  final NavigationProvider _navigationProvider;
  final FeatureToggleProvider _featureToggleProvider;
  final PermissionsProvider _permissionsProvider;

  ReAskCubit({
    required CommonErrorHandler errorHandler,
    required RfiInteractor reAskInteractor,
    required AccountMaintenanceInteractor documentInterctor,
    required NavigationProvider navigationProvider,
    required FeatureToggleProvider featureToggleProvider,
    required PermissionsProvider permissionProvider,
    required String caseId,
    required String customerId,
  })  : _errorHandler = errorHandler,
        _reAskInteractor = reAskInteractor,
        _documentInteractor = documentInterctor,
        _featureToggleProvider = featureToggleProvider,
        _navigationProvider = navigationProvider,
        _permissionsProvider = permissionProvider,
        super(
          ReAskState.loading(
            isRfiEnabled: featureToggleProvider.get(
              AccountMaintenanceFeatureToggle.isLicensingAuthorityEmailEnabled,
            ),
            category: ReAskCategory.business,
            caseId: caseId,
            role: UserRole.maker,
            customerId: customerId,
            product: ProductType.sme,
          ),
        );

  @override
  FeatureToggleProvider get featureToggleProvider => _featureToggleProvider;

  Future<void> initialize({
    required UserRole role,
    required ProductType type,
    required CustomerType customerType,
    required ReAskDetail? reAsk,
    required bool isRfiEnabled,
  }) =>
      _reAskInteractor
          .getReAskDetail(
            caseId: state.caseId,
            reAsk: reAsk,
            customerId: state.customerId,
          )
          .then(
            (reAskModels) => safeEmit(
              ReAskState.loaded(
                role: role,
                isRfiEnabled: state.isRfiEnabled && isRfiEnabled,
                category: switch (customerType) {
                  CustomerType.business => ReAskCategory.business,
                  CustomerType.individual => ReAskCategory.individual,
                  CustomerType.retail => ReAskCategory.individual,
                },
                customerId: state.customerId,
                product: type,
                detail: reAsk,
                reAsksList: reAsk == null ? [_formModel] : reAskModels,
                reasons: List.generate(
                  ReAskDocumentType.values.length,
                  (index) => ReAskDocumentType.values
                      .elementAt(index)
                      .title
                      .concat(
                        end: AccountMaintenanceStrings.uploadDocumentSubTitle,
                        separator: Symbol.space,
                      ),
                ),
                caseId: state.caseId,
              ),
            ),
          )
          .onError(
            (err, str) => _errorHandler.handleError(
              error: err!,
              stackTrace: str,
            ),
          );

  void onAddQuestion() => safeEmit(
        state.maybeMap(
          orElse: () => state,
          loaded: (loaded) => loaded.copyWith(
            reAsksList: [
              _formModel.maybeMap(
                orElse: () => _formModel,
                question: (ques) => ques.copyWith(
                  documentType: loaded.reAsksList.length ==
                          loaded.category.documents.length - 1
                      ? loaded.category.documents.firstWhere(
                          (doc) => !loaded.reAsksList.any(
                            (ask) => ask.documentType == doc,
                          ),
                        )
                      : null,
                ),
              ),
              ...loaded.reAsksList,
            ],
          ),
        ),
      );

  ReAskModel get _formModel => ReAskModel.question(
        reAskType: ReAskType.documentUpload,
        reason: Symbol.empty,
        question: DateTime.now().millisecondsSinceEpoch.toString(),
      );

  void onRemoveQuestion(int index) => safeEmit(
        state.maybeMap(
          orElse: () => state,
          loaded: (loaded) => loaded.copyWith(
            reAsksList: List.from(
              loaded.reAsksList,
            )..removeAt(index),
          ),
        ),
      );

  void onChangeReAskQuestion(
    int index,
    ReAskModel reAsk,
  ) =>
      safeEmit(
        state.maybeMap(
          orElse: () => state,
          loaded: (loaded) {
            final reAsks = List<ReAskModel>.from(loaded.reAsksList);
            if (reAsks.isEmpty) {
              reAsks.add(reAsk);
            } else {
              reAsks.replaceRange(index, index + 1, [reAsk]);
            }

            return loaded.copyWith(reAsksList: reAsks);
          },
        ),
      );

  Future<List<MasterDetail>> getReasonList(
    ReAskDocumentType docType, [
    String query = '',
  ]) =>
      _reAskInteractor.reAskReasons(
        category: state.category,
        docType: docType,
        query: query,
      );

  Future<void> cancelReAsk() async {
    final reAskId = state.detail?.reAskId;

    if (reAskId == null) return;

    final prev = state;

    try {
      safeEmit(
        ReAskState.loading(
          role: state.role,
          caseId: state.caseId,
          customerId: state.customerId,
          product: state.product,
          category: state.category,
          detail: state.detail,
          isRfiEnabled: state.isRfiEnabled,
        ),
      );

      await _reAskInteractor.cancelReAsk(
        caseId: state.caseId,
        reAskId: reAskId,
        isRfiEnabled: state.isRfiEnabled,
      );

      _navigationProvider.goBack(true);
    } on Exception catch (err, str) {
      _errorHandler.handleError(
        error: err,
        stackTrace: str,
      );

      safeEmit(prev);
    }
  }

  Future<void> onInitiateReAsk() async {
    if (state.viewReAsk.isEmpty) return;

    final reAsks = [
      ...state.viewReAsk,
    ];

    safeEmit(
      ReAskState.loading(
        role: state.role,
        category: state.category,
        caseId: state.caseId,
        detail: state.detail,
        product: state.product,
        customerId: state.customerId,
        isRfiEnabled: state.isRfiEnabled,
      ),
    );

    try {
      await safeExecute(
        _reAskInteractor.initiateReAsk(
          role: state.role,
          isRfiEnabled: state.isRfiEnabled,
          caseId: state.caseId,
          reAskList: reAsks.mapType(
            (ask) => ask.copyWith(
              question: AccountMaintenanceStrings.question.concat(
                end: (reAsks.indexOf(ask) + 1).toString(),
                separator: Symbol.space,
              ),
            ),
          ),
          type: state.product,
          category: state.category,
          customerId: state.customerId,
        ),
      );
      _navigationProvider.goBack(true);
      return;
    } on Exception catch (error, stackStrace) {
      _errorHandler.handleError(
        error: error,
        stackTrace: stackStrace,
      );
    }

    safeEmit(
      ReAskState.loaded(
        role: state.role,
        isRfiEnabled: state.isRfiEnabled,
        category: state.category,
        caseId: state.caseId,
        detail: state.detail,
        customerId: state.customerId,
        product: state.product,
        reAsksList: reAsks,
      ),
    );
  }

  @override
  Future<DocumentFile> loadFile(DocumentFile file) =>
      _documentInteractor.downloadBase64String(
        file,
      );

  @override
  String toString() => 'ReAskCubit{}';

  @override
  PermissionsProvider get permissionsProvider => _permissionsProvider;
}
