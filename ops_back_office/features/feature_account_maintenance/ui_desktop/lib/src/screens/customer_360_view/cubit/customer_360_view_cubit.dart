import 'package:ui/cubit/base_cubit.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_account_maintenance_api/account_maintenance_api.dart';
import 'package:wio_feature_account_maintenance_ui_desktop/src/screens/customer_360_view/cubit/customer_360_view_state.dart';
import 'package:wio_feature_backoffice_env_api/keys/env_key.dart';

class Customer360ViewCubit extends BaseCubit<Customer360ViewState> {
  final EnvProvider _envProvider;

  Customer360ViewCubit({
    required EnvProvider envProvider,
  })  : _envProvider = envProvider,
        super(const Customer360ViewState.loading());

  void init({
    required ProductType productType,
    String? customerID,
    String? businessID,
  }) {
    final baseUrl = _envProvider.get(BackOfficeEnvKeys.frontOfficeDomain);

    final queryParams = <String, String>{
      'product': productType.value,
      if (businessID != null) 'businessId': businessID,
    };

    if (customerID != null) {
      if (productType == ProductType.retail) {
        queryParams['customerId'] = customerID;
      } else {
        queryParams['individualId'] = customerID;
      }
    }

    final queryString = Uri(queryParameters: queryParams).query;

    final uri = Uri.parse('$baseUrl#open-profile?$queryString');

    safeEmit(Customer360ViewState.loaded(url: uri));
  }

  @override
  String toString() => 'Customer360ViewCubit{}';
}
