openapi: 3.0.3

info:
  title: Master Data API
  description: API for managing master data
  version: 1.0.0

paths:
  /v1/internal/{type}:
    get:
      summary: Get List based on type
      parameters:
        - in: path
          name: type
          required: true
          schema:
            type: string
          description: Type name
      responses:
        '200':
          description: A list of master data
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ListofValues'
  /v1/internal/search/{type}:
    get:
      summary: Search matching values found in query
      parameters:
        - in: path
          name: type
          required: true
          schema:
            type: string
            enum:
              - BusinessActivity
          description: Type name
        - in: query
          name: q
          required: true
          schema:
            type: string
          description: The search query
        - in: query
          name: limit
          schema:
            type: integer
            format: int32
            minimum: 1
            maximum: 100
          description: The maximum number of results to return
      responses:
        '200':
          description: A list of matching values in the search query
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ListofValues'
components:
  schemas:
    ListofValues:
      type: object
      properties:
        reaskReason:
          type: string
        nameEnglish:
          type: string
        englishName:
          type: string
        countryCode:
          type: string
        industry:
          type: string
        groupName:
          type: string
        email:
          type: array
          items:
            type: string