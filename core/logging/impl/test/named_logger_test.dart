import 'package:logging_api/logging.dart'; // adjust import path
import 'package:logging_impl/src/logger/named_logger.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';

void main() {
  group('NamedLoggerImpl', () {
    const loggerName = 'loggerName';
    late Logger logger;
    late Logger namedLogger;

    setUp(() {
      logger = MockLogger();
      namedLogger = NamedLoggerImpl(loggerName, logger);
    });

    test('loggerName is passed to log methods', () {
      namedLogger
        ..log(LoggingLevel.debug, 'log')
        ..error('message')
        ..debug('message')
        ..info('message')
        ..warning('message');

      verify(
        () =>
            namedLogger.log(LoggingLevel.debug, 'log', loggerName: loggerName),
      ).calledOnce;
      verify(() => namedLogger.error('message', loggerName: loggerName))
          .calledOnce;
      verify(() => namedLogger.debug('message', loggerName: loggerName))
          .calledOnce;
      verify(() => namedLogger.info('message', loggerName: loggerName))
          .calledOnce;
      verify(() => namedLogger.warning('message', loggerName: loggerName))
          .calledOnce;
    });

    test('parameter loggerName has priority over NamedLogger name parameter',
        () {
      const updatedLoggerName = 'updatedLoggerName';
      namedLogger
        ..log(LoggingLevel.debug, 'log', loggerName: updatedLoggerName)
        ..error('message', loggerName: updatedLoggerName)
        ..debug('message', loggerName: updatedLoggerName)
        ..info('message', loggerName: updatedLoggerName)
        ..warning('message', loggerName: updatedLoggerName);

      verify(
        () => namedLogger.log(
          LoggingLevel.debug,
          'log',
          loggerName: updatedLoggerName,
        ),
      ).calledOnce;
      verify(() => namedLogger.error('message', loggerName: updatedLoggerName))
          .calledOnce;
      verify(() => namedLogger.debug('message', loggerName: updatedLoggerName))
          .calledOnce;
      verify(() => namedLogger.info('message', loggerName: updatedLoggerName))
          .calledOnce;
      verify(
        () => namedLogger.warning('message', loggerName: updatedLoggerName),
      ).calledOnce;
    });
  });
}
