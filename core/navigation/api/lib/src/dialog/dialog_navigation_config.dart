import 'package:meta/meta.dart';
import 'package:wio_core_navigation_api/src/screen/screen_navigation_config.dart';

/// An interface for configuration objects used to navigate to dialogs.
///
/// [R] - a type of returned result.
@immutable
abstract class DialogNavigationConfig<R> {
  const DialogNavigationConfig();

  /// A name of a feature the dialog belongs to.
  ///
  /// Similar to field in [ScreenNavigationConfig], should be equal to the value
  /// from feature's global navigation config.
  String get feature;

  /// requires string representation for logs in obfuscated app
  @override
  @mustBeOverridden
  String toString();
}
