import 'package:test/test.dart';
import 'package:wio_app_core_api/src/error_reporter/impl/utils/url_path_processor.dart';

void main() {
  late UrlPathProcessor urlProcessor;

  setUp(() {
    urlProcessor = UrlPathProcessor();
  });

  group('processUrlPath', () {
    test('keeps version fragment', () {
      // arrange
      const url = '/lending/retail/api/v1/offers';
      const expected = 'lending/retail/api/v1/offers';

      // act & assert
      expect(urlProcessor.processPath(url), expected);
    });

    test('drops uuid v4', () {
      // arrange
      const url =
          '/international/transfer/65f766e9-993f-4279-b5b8-1f20c6a21c60';
      const expected = 'international/transfer/xxx';

      // act & assert
      expect(urlProcessor.processPath(url), expected);
    });

    test('drops uuid v4', () {
      // arrange
      const url =
          '/sme/identity/api/v1/challenges/da1e4226-ff3a-47ea-98cf-0f80bb620a1e/solve';
      const expected = 'sme/identity/api/v1/challenges/xxx/solve';

      // act & assert
      expect(urlProcessor.processPath(url), expected);
    });

    test('drops int ids', () {
      // arrange
      const url = '/sme/account/api/v1/deliveries/***************';
      const expected = 'sme/account/api/v1/deliveries/xxx';

      // act & assert
      expect(urlProcessor.processPath(url), expected);
    });

    test('drops transfer identifier', () {
      // arrange
      const url = '/gateway/sme/transfers/****************/payment-proof';
      const expected = 'gateway/sme/transfers/xxx/payment-proof';

      // act & assert
      expect(urlProcessor.processPath(url), expected);
    });

    test('drops transfer identifier', () {
      // arrange
      const url = '/gateway/sme/transfers/****************/payment-proof';
      const expected = 'gateway/sme/transfers/xxx/payment-proof';

      // act & assert
      expect(urlProcessor.processPath(url), expected);
    });

    test('drops account number', () {
      // arrange
      const url = '/lending/retail/api/v1/accounts/**********/payment-settings';
      const expected = 'lending/retail/api/v1/accounts/xxx/payment-settings';

      // act & assert
      expect(urlProcessor.processPath(url), expected);
    });

    test('drops several uuids', () {
      // arrange
      const url =
          '/broker/portfolios/0ce73fd3-41a3-4393-954a-44fa1b5953e3/positions/198899ce-d2d4-541c-94a8-43cefe0feb93';
      const expected = 'broker/portfolios/xxx/positions/xxx';

      // act & assert
      expect(urlProcessor.processPath(url), expected);
    });

    test('drops URL query params', () {
      // arrange
      const url =
          '/sme/account/api/v1/deliveries/***************?query=foo_bar';
      const expected = 'sme/account/api/v1/deliveries/xxx?';

      // act & assert
      expect(urlProcessor.processPath(url), expected);
    });

    test('keeps GraphQL as it is', () {
      // arrange
      const url = 'MuGetMultiUserTeam';
      const expected = 'MuGetMultiUserTeam';

      // act & assert
      expect(urlProcessor.processPath(url), expected);
    });

    // We can't use random strings in algorithm, skipping it for now
    test(
      'drops random ids ',
      () {
        // arrange
        const url =
            '/invitations/api/v1/invitations/preflight?reference=OCYe2eDj';
        const expected = 'invitations/api/v1/invitations/preflight?';

        // act & assert
        expect(urlProcessor.processPath(url), expected);
      },
    );

    // We can't use random strings in algorithm, skipping it for now
    test(
      'drops file id',
      () {
        // arrange
        const url = '/payroll/files/655321fbf0b7e50cbf435a93';
        const expected = 'payroll/files/xxx';

        // act & assert
        expect(urlProcessor.processPath(url), expected);
      },
    );

    // We can't use random strings in algorithm, skipping it for now
    test(
      'drops cheque id',
      () {
        // arrange
        const url =
            '/pre/gateway/retail/deposits/cheque/Wio99VHZK1AU/frontImage';
        const expected = 'pre/gateway/retail/deposits/cheque/xxx/frontImage';

        // act & assert
        expect(urlProcessor.processPath(url), expected);
      },
    );
  });
}
