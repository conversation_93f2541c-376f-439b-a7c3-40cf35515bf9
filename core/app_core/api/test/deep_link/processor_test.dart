import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';
import 'package:wio_app_core_api/index.dart';

import '../mocks.dart';

void main() {
  late DeepLinkProcessorImpl processor;
  late DeepLink deepLink;
  late DeepLinkHandlerMock handler;

  setUp(() {
    processor = DeepLinkProcessorImpl();
    deepLink = DeepLinkMock();
    handler = DeepLinkHandlerMock();
  });

  group('processor', () {
    test('Should create handlers only once', () async {
      // Arrange
      when(() => handler.tryHandle(deepLink)).thenAnswer((_) async => true);
      var callCounter = 0;
      DeepLinkHandler factory() {
        callCounter++;

        return handler;
      }

      // Act
      processor.register(factory);
      await processor.process(deepLink);
      await processor.process(deepLink);

      // Assert
      expect(callCounter, 1);
    });

    test('Should not allow to add handlers after process was called', () async {
      // Arrange
      when(() => handler.tryHandle(deepLink)).thenAnswer((_) async => true);

      // Act
      processor.register(() => handler);
      await processor.process(deepLink);
      expect(
        () => processor.register(() => handler),
        throwsA(isA<Exception>()),
      );

      // Assert
    });

    test(
      "throws an exception if handler wasn't found for processing DeepLink",
      () {
        // arrange
        processor.register(() => handler);
        when(() => handler.tryHandle(deepLink)).thenAnswer((_) async => false);

        // act & assert
        expect(
          () async => processor.process(deepLink),
          throwsException,
        );
      },
    );

    test(
      'finishes successfully if handler could handle the deep link',
      () async {
        // arrange
        processor.register(() => handler);
        when(() => handler.tryHandle(deepLink)).thenAnswer((_) async => true);

        // act & assert

        await processor.process(deepLink);
      },
    );
  });
}
