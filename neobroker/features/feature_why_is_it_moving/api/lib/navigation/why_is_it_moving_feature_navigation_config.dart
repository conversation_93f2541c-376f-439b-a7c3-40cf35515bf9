import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
part 'why_is_it_moving_feature_navigation_config.freezed.dart';

@freezed

/// Why Is It Moving Feature Navigation Config
class WhyIsItMovingFeatureNavigationConfig extends FeatureNavigationConfig
    with _$WhyIsItMovingFeatureNavigationConfig {
  /// Why Is It Moving Feature Name
  static const name = 'why_is_it_moving_feature';

  /// Why Is It Moving Feature Navigation Config Initialization
  const factory WhyIsItMovingFeatureNavigationConfig({
    required ScreenNavigationConfig destination,
  }) = _WhyIsItMovingFeatureNavigationConfig;

  const WhyIsItMovingFeatureNavigationConfig._() : super(name);

  @override
  String toString() => 'WhyIsItMovingFeatureNavigationConfig';
}
