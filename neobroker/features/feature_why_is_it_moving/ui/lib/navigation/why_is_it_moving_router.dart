import 'package:flutter/widgets.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_why_is_it_moving_api/navigation/why_is_it_moving_feature_navigation_config.dart';
import 'package:wio_feature_why_is_it_moving_ui/screens/why_is_it_moving_page.dart';

class WhyIsItMovingRouter extends NavigationRouter {
  @override
  Route<Object?> getScreenRoute(RouteSettings settings) {
    final config = settings.arguments;
    switch (config.runtimeType) {
      // ignore: type_literal_in_constant_pattern
      case WhyIsItMovingFeatureNavigationConfig:
        return toRoute(const WhyIsItMovingPage(), settings);

      default:
        throw Exception('Unknown $config for the $runtimeType');
    }
  }
}
