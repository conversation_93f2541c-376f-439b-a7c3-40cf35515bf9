// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_placement_validator.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OrderPlacementValidationResult {
  OrderPlacementValidationAction get action =>
      throw _privateConstructorUsedError;
  String get messageId => throw _privateConstructorUsedError;

  /// Create a copy of OrderPlacementValidationResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderPlacementValidationResultCopyWith<OrderPlacementValidationResult>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderPlacementValidationResultCopyWith<$Res> {
  factory $OrderPlacementValidationResultCopyWith(
          OrderPlacementValidationResult value,
          $Res Function(OrderPlacementValidationResult) then) =
      _$OrderPlacementValidationResultCopyWithImpl<$Res,
          OrderPlacementValidationResult>;
  @useResult
  $Res call({OrderPlacementValidationAction action, String messageId});
}

/// @nodoc
class _$OrderPlacementValidationResultCopyWithImpl<$Res,
        $Val extends OrderPlacementValidationResult>
    implements $OrderPlacementValidationResultCopyWith<$Res> {
  _$OrderPlacementValidationResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderPlacementValidationResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? action = null,
    Object? messageId = null,
  }) {
    return _then(_value.copyWith(
      action: null == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as OrderPlacementValidationAction,
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderPlacementValidationResultImplCopyWith<$Res>
    implements $OrderPlacementValidationResultCopyWith<$Res> {
  factory _$$OrderPlacementValidationResultImplCopyWith(
          _$OrderPlacementValidationResultImpl value,
          $Res Function(_$OrderPlacementValidationResultImpl) then) =
      __$$OrderPlacementValidationResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({OrderPlacementValidationAction action, String messageId});
}

/// @nodoc
class __$$OrderPlacementValidationResultImplCopyWithImpl<$Res>
    extends _$OrderPlacementValidationResultCopyWithImpl<$Res,
        _$OrderPlacementValidationResultImpl>
    implements _$$OrderPlacementValidationResultImplCopyWith<$Res> {
  __$$OrderPlacementValidationResultImplCopyWithImpl(
      _$OrderPlacementValidationResultImpl _value,
      $Res Function(_$OrderPlacementValidationResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderPlacementValidationResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? action = null,
    Object? messageId = null,
  }) {
    return _then(_$OrderPlacementValidationResultImpl(
      action: null == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as OrderPlacementValidationAction,
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$OrderPlacementValidationResultImpl
    implements _OrderPlacementValidationResult {
  const _$OrderPlacementValidationResultImpl(
      {required this.action, this.messageId = ''});

  @override
  final OrderPlacementValidationAction action;
  @override
  @JsonKey()
  final String messageId;

  @override
  String toString() {
    return 'OrderPlacementValidationResult(action: $action, messageId: $messageId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderPlacementValidationResultImpl &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, action, messageId);

  /// Create a copy of OrderPlacementValidationResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderPlacementValidationResultImplCopyWith<
          _$OrderPlacementValidationResultImpl>
      get copyWith => __$$OrderPlacementValidationResultImplCopyWithImpl<
          _$OrderPlacementValidationResultImpl>(this, _$identity);
}

abstract class _OrderPlacementValidationResult
    implements OrderPlacementValidationResult {
  const factory _OrderPlacementValidationResult(
      {required final OrderPlacementValidationAction action,
      final String messageId}) = _$OrderPlacementValidationResultImpl;

  @override
  OrderPlacementValidationAction get action;
  @override
  String get messageId;

  /// Create a copy of OrderPlacementValidationResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderPlacementValidationResultImplCopyWith<
          _$OrderPlacementValidationResultImpl>
      get copyWith => throw _privateConstructorUsedError;
}
