import 'package:neobroker_models/models.dart';
import 'package:order_placement_api/index.dart';
import 'package:order_placement_impl/domain/order_placement_validator_impl.dart';
import 'package:test/test.dart';
import 'package:wio_app_core_api/money.dart';

import 'mocks.dart';

void main() {
  final validator = OrderPlacementValidatorImpl();
  final minValue = Money.parse('1', code: 'USD');
  final maxValue = Money.parse('100000', code: 'USD');
  const minQuantity = 0.0001;
  const maxQuantity = 20000.0;

  group(
    'validateBuyForMoney',
    () {
      test(
        'it fails when I try to buy for 0.9 USD',
        () {
          final result = validator.validateBuyForMoney(
            accountBalance: Money.parse('150000', code: 'USD'),
            intendedAmount: Money.parse('0.9', code: 'USD'),
            minValue: minValue,
            maxValue: maxValue,
          );
          final expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.error,
            messageId:
                'Minimum order amount: ${minValue.toCodeOnRightFormat()}',
          );
          expect(result, expected);
        },
      );

      test(
        'it fails when I try to buy for 90_001 USD for balance 90_000 USD',
        () {
          final result = validator.validateBuyForMoney(
            accountBalance: Money.parse('90000', code: 'USD'),
            intendedAmount: Money.parse('90001', code: 'USD'),
            minValue: minValue,
            maxValue: maxValue,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.error,
            messageId: 'Order amount exceeds available balance',
          );
          expect(result, expected);
        },
      );

      test(
        'it fails when I try to buy for 100_001 USD',
        () {
          final result = validator.validateBuyForMoney(
            accountBalance: Money.parse('150000', code: 'USD'),
            intendedAmount: Money.parse('100001', code: 'USD'),
            minValue: minValue,
            maxValue: maxValue,
          );
          final expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.error,
            messageId:
                'Maximum order amount: ${maxValue.toCodeOnRightFormat()}',
          );
          expect(result, expected);
        },
      );

      test(
        'it succeeds when I try to buy for 1 USD',
        () {
          final result = validator.validateBuyForMoney(
            accountBalance: Money.parse('150000', code: 'USD'),
            intendedAmount: Money.parse('1', code: 'USD'),
            minValue: minValue,
            maxValue: maxValue,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.success,
          );
          expect(result, expected);
        },
      );

      test(
        'it succeeds when I try to buy for 100_000',
        () {
          final result = validator.validateBuyForMoney(
            accountBalance: Money.parse('150000', code: 'USD'),
            intendedAmount: Money.parse('100000', code: 'USD'),
            minValue: minValue,
            maxValue: maxValue,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.success,
          );
          expect(result, expected);
        },
      );

      test(
        'it succeeds when I try to buy for my balance 50000',
        () {
          final result = validator.validateBuyForMoney(
            accountBalance: Money.parse('50000', code: 'USD'),
            intendedAmount: Money.parse('50000', code: 'USD'),
            minValue: minValue,
            maxValue: maxValue,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.success,
          );
          expect(result, expected);
        },
      );
    },
  );

  group(
    'validateBuyForQuantity',
    () {
      test(
        'it fails when I try to buy 0.00009 shares',
        () {
          final result = validator.validateBuyForQuantity(
            intendedQuantity: 0.00009,
            minQuantity: minQuantity,
            maxQuantity: maxQuantity,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.error,
            messageId: 'Minimum order quantity: $minQuantity',
          );
          expect(result, expected);
        },
      );

      test(
        'it fails when I try to buy 20001 shares',
        () {
          final result = validator.validateBuyForQuantity(
            intendedQuantity: 20001,
            minQuantity: minQuantity,
            maxQuantity: maxQuantity,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.error,
            messageId: 'Maximum order quantity: $maxQuantity',
          );
          expect(result, expected);
        },
      );

      test(
        'it succeeds when I try to buy 0.0001 shares',
        () {
          final result = validator.validateBuyForQuantity(
            intendedQuantity: 0.0001,
            minQuantity: minQuantity,
            maxQuantity: maxQuantity,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.success,
          );
          expect(result, expected);
        },
      );

      test(
        'it succeeds when I try to buy 20000 shares',
        () {
          final result = validator.validateBuyForQuantity(
            intendedQuantity: 20000,
            minQuantity: minQuantity,
            maxQuantity: maxQuantity,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.success,
          );
          expect(result, expected);
        },
      );
    },
  );

  group(
    'validateSellForMoney',
    () {
      test(
        'it fails when I try to sell for 0.9 USD',
        () {
          final result = validator.validateSellForMoney(
            intendedAmount: Money.parse('0.9', code: 'USD'),
            ownedValue: Money.parse('150000', code: 'USD'),
            minValue: minValue,
            maxValue: maxValue,
          );
          final expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.error,
            messageId:
                'Minumum order amount: ${minValue.toCodeOnRightFormat()}',
          );
          expect(result, expected);
        },
      );

      test(
        'it fails when I try to sell for 100_001 USD',
        () {
          final result = validator.validateSellForMoney(
            intendedAmount: Money.parse('100001', code: 'USD'),
            ownedValue: Money.parse('150000', code: 'USD'),
            minValue: minValue,
            maxValue: maxValue,
          );
          final expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.error,
            messageId:
                'Maximum order amount: ${maxValue.toCodeOnRightFormat()}',
          );
          expect(result, expected);
        },
      );

      test(
        'it fails when I try to sell for 50_001 USD',
        () {
          final result = validator.validateSellForMoney(
            intendedAmount: Money.parse('50001', code: 'USD'),
            ownedValue: Money.parse('50000', code: 'USD'),
            minValue: minValue,
            maxValue: maxValue,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.error,
            messageId: 'Order amount exceeds available balance',
          );
          expect(result, expected);
        },
      );

      test(
        'it succeeds when I try to sell for 1 USD',
        () {
          final result = validator.validateSellForMoney(
            intendedAmount: Money.parse('1', code: 'USD'),
            ownedValue: Money.parse('50000', code: 'USD'),
            minValue: minValue,
            maxValue: maxValue,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.success,
          );
          expect(result, expected);
        },
      );

      test(
        'it succeeds when I try to sell for 50000 USD',
        () {
          final result = validator.validateSellForMoney(
            intendedAmount: Money.parse('50000', code: 'USD'),
            ownedValue: Money.parse('50000', code: 'USD'),
            minValue: minValue,
            maxValue: maxValue,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.success,
          );
          expect(result, expected);
        },
      );

      test(
        'it succeeds when I try to sell for 100000 USD',
        () {
          final result = validator.validateSellForMoney(
            intendedAmount: Money.parse('100000', code: 'USD'),
            ownedValue: Money.parse('100000', code: 'USD'),
            minValue: minValue,
            maxValue: maxValue,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.success,
          );
          expect(result, expected);
        },
      );
    },
  );

  group(
    'validateSellForQuantity',
    () {
      test(
        'if fails when I try to sell 0.00009 shares',
        () {
          final result = validator.validateSellForQuantity(
            ownedQuantity: 10000,
            intendedQuantity: 0.00009,
            minQuantity: minQuantity,
            maxQuantity: maxQuantity,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.error,
            messageId: 'Minimum order quantity: $minQuantity',
          );
          expect(result, expected);
        },
      );

      test(
        'if fails when I try to sell 20001 shares',
        () {
          final result = validator.validateSellForQuantity(
            ownedQuantity: 100000,
            intendedQuantity: 20001,
            minQuantity: minQuantity,
            maxQuantity: maxQuantity,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.error,
            messageId: 'Maximum order quantity: $maxQuantity',
          );
          expect(result, expected);
        },
      );

      test(
        'if fails when I try to sell 10001 shares',
        () {
          final result = validator.validateSellForQuantity(
            ownedQuantity: 10000,
            intendedQuantity: 10001,
            minQuantity: minQuantity,
            maxQuantity: maxQuantity,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.error,
            messageId: 'Order quantity exceeds available quantity',
          );
          expect(result, expected);
        },
      );

      test(
        'if succeeds when I try to sell 0.0001 shares',
        () {
          final result = validator.validateSellForQuantity(
            ownedQuantity: 10000,
            intendedQuantity: 0.0001,
            minQuantity: minQuantity,
            maxQuantity: maxQuantity,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.success,
          );
          expect(result, expected);
        },
      );

      test(
        'if succeeds when I try to sell 10000 shares',
        () {
          final result = validator.validateSellForQuantity(
            ownedQuantity: 10000,
            intendedQuantity: 10000,
            minQuantity: minQuantity,
            maxQuantity: maxQuantity,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.success,
          );
          expect(result, expected);
        },
      );

      test(
        'if succeeds when I try to sell 20000 shares',
        () {
          final result = validator.validateSellForQuantity(
            ownedQuantity: 30000,
            intendedQuantity: 20000,
            minQuantity: minQuantity,
            maxQuantity: maxQuantity,
          );
          const expected = OrderPlacementValidationResult(
            action: OrderPlacementValidationAction.success,
          );
          expect(result, expected);
        },
      );
    },
  );

  group('validateOrder (single entry)', () {
    Preorder fakeBuyPreorder({
      required String balanceValue,
      required String currencyCode,
      required String minVal,
      required String maxVal,
      required double minQty,
      required double maxQty,
    }) {
      final validations = PreorderValidations(
        sum: [
          PreorderValidation(
            minValue: NumberValue(
              value: minVal,
              type: NumberValueType.money,
              sign: NumberValueSign.positive,
              currency: currencyCode,
            ),
            maxValue: NumberValue(
              value: maxVal,
              type: NumberValueType.money,
              sign: NumberValueSign.positive,
              currency: currencyCode,
            ),
            maxScale: const NumberValue(
              value: '2',
              type: NumberValueType.floatingPointNumber,
              sign: NumberValueSign.positive,
            ),
          ),
        ],
        quantity: PreorderValidation(
          minValue: NumberValue(
            value: minQty.toString(),
            sign: NumberValueSign.positive,
            type: NumberValueType.floatingPointNumber,
          ),
          maxValue: NumberValue(
            value: maxQuantity.toString(),
            sign: NumberValueSign.positive,
            type: NumberValueType.floatingPointNumber,
          ),
          maxScale: const NumberValue(
            value: '2',
            type: NumberValueType.floatingPointNumber,
            sign: NumberValueSign.positive,
          ),
        ),
      );

      final preorder = buyPreorder.copyWith(
        validations: validations,
        accounts: [
          PreorderAccount(
            accountId: 'id',
            balance: [
              NumberValue(
                sign: NumberValueSign.positive,
                value: balanceValue,
                currency: currencyCode,
                type: NumberValueType.money,
              ),
            ],
            chosen: false,
          ),
        ],
      );

      return Preorder.buy(preorder: preorder);
    }

    Preorder fakeSellPreorder({
      required String ownedAmount,
      required String ownedQuantity,
      required String currencyCode,
      required String minVal,
      required String maxVal,
      required double minQty,
      required double maxQty,
    }) {
      final validations = PreorderValidations(
        sum: [
          PreorderValidation(
            minValue: NumberValue(
              value: minVal,
              type: NumberValueType.money,
              sign: NumberValueSign.positive,
              currency: currencyCode,
            ),
            maxValue: NumberValue(
              value: maxVal,
              type: NumberValueType.money,
              sign: NumberValueSign.positive,
              currency: currencyCode,
            ),
            maxScale: const NumberValue(
              value: '2',
              type: NumberValueType.floatingPointNumber,
              sign: NumberValueSign.positive,
            ),
          ),
        ],
        quantity: PreorderValidation(
          minValue: NumberValue(
            value: minQty.toString(),
            sign: NumberValueSign.positive,
            type: NumberValueType.floatingPointNumber,
          ),
          maxValue: NumberValue(
            value: maxQuantity.toString(),
            sign: NumberValueSign.positive,
            type: NumberValueType.floatingPointNumber,
          ),
          maxScale: const NumberValue(
            value: '2',
            type: NumberValueType.floatingPointNumber,
            sign: NumberValueSign.positive,
          ),
        ),
      );

      final preorder = sellPreorder.copyWith(
        validations: validations,
        instrumentPosition: sellPreorder.instrumentPosition.copyWith(
          amount: NumberValue(
            type: NumberValueType.money,
            value: ownedAmount,
            currency: currencyCode,
            sign: NumberValueSign.positive,
          ),
          quantity: NumberValue(
            type: NumberValueType.floatingPointNumber,
            value: ownedQuantity,
            sign: NumberValueSign.positive,
          ),
        ),
      );

      return Preorder.sell(preorder: preorder);
    }

    test('fails for BUY + MONEY when typedValue = 0.9', () {
      // Build a buy preorder that has minValue=1, maxValue=100000, etc.
      final preorder = fakeBuyPreorder(
        balanceValue: '150000',
        currencyCode: 'USD',
        minVal: '1',
        maxVal: '100000',
        minQty: 0.0001,
        maxQty: 20000,
      );

      final result = validator.validateOrder(
        shareActionType: ShareActionType.buy,
        isMoney: true, // editing "money" field
        typedValue: '0.9', // less than minValue
        preorder: preorder,
        portfolioType: PortfolioType.primary,
      );

      const expected = OrderPlacementValidationResult(
        action: OrderPlacementValidationAction.error,
        messageId: 'Minimum order amount: 1.00 USD',
      );
      expect(result, expected);
    });

    test('fails for BUY + SHARES when typedValue = 0.00009', () {
      final preorder = fakeBuyPreorder(
        balanceValue: '150000',
        currencyCode: 'USD',
        minVal: '1',
        maxVal: '100000',
        minQty: 0.0001,
        maxQty: 20000,
      );

      final result = validator.validateOrder(
        shareActionType: ShareActionType.buy,
        isMoney: false, // editing "shares" field
        typedValue: '0.00009',
        preorder: preorder,
        portfolioType: PortfolioType.primary,
      );

      const expected = OrderPlacementValidationResult(
        action: OrderPlacementValidationAction.error,
        messageId: 'Minimum order quantity: 0.0001',
      );
      expect(result, expected);
    });

    test('fails for SELL + MONEY when typedValue = 0.9', () {
      final preorder = fakeSellPreorder(
        ownedAmount: '150000',
        ownedQuantity: '10000',
        currencyCode: 'USD',
        minVal: '1',
        maxVal: '100000',
        minQty: 0.0001,
        maxQty: 20000,
      );

      final result = validator.validateOrder(
        shareActionType: ShareActionType.sell,
        isMoney: true,
        typedValue: '0.9',
        preorder: preorder,
        portfolioType: PortfolioType.primary,
      );

      const expected = OrderPlacementValidationResult(
        action: OrderPlacementValidationAction.error,
        messageId: 'Minumum order amount: 1.00 USD',
      );
      expect(result, expected);
    });

    test('fails for SELL + SHARES when typedValue = 0.00009', () {
      final preorder = fakeSellPreorder(
        ownedAmount: '150000',
        ownedQuantity: '10000',
        currencyCode: 'USD',
        minVal: '1',
        maxVal: '100000',
        minQty: 0.0001,
        maxQty: 20000,
      );

      final result = validator.validateOrder(
        shareActionType: ShareActionType.sell,
        isMoney: false,
        typedValue: '0.00009',
        preorder: preorder,
        portfolioType: PortfolioType.primary,
      );

      const expected = OrderPlacementValidationResult(
        action: OrderPlacementValidationAction.error,
        messageId: 'Minimum order quantity: 0.0001',
      );
      expect(result, expected);
    });

    test('succeeds for BUY + MONEY when typedValue = 1', () {
      final preorder = fakeBuyPreorder(
        balanceValue: '150000',
        currencyCode: 'USD',
        minVal: '1',
        maxVal: '100000',
        minQty: 0.0001,
        maxQty: 20000,
      );

      final result = validator.validateOrder(
        shareActionType: ShareActionType.buy,
        isMoney: true,
        typedValue: '1',
        preorder: preorder,
        portfolioType: PortfolioType.primary,
      );

      const expected = OrderPlacementValidationResult(
        action: OrderPlacementValidationAction.success,
      );
      expect(result, expected);
    });

    test('succeeds for BUY + SHARES when typedValue = 20000', () {
      final preorder = fakeBuyPreorder(
        balanceValue: '150000',
        currencyCode: 'USD',
        minVal: '1',
        maxVal: '100000',
        minQty: 0.0001,
        maxQty: 20000,
      );

      final result = validator.validateOrder(
        shareActionType: ShareActionType.buy,
        isMoney: false,
        typedValue: '20000',
        preorder: preorder,
        portfolioType: PortfolioType.primary,
      );

      const expected = OrderPlacementValidationResult(
        action: OrderPlacementValidationAction.success,
      );
      expect(result, expected);
    });

    test('succeeds for SELL + MONEY when typedValue = 50000', () {
      final preorder = fakeSellPreorder(
        ownedAmount: '50000',
        ownedQuantity: '10000',
        currencyCode: 'USD',
        minVal: '1',
        maxVal: '100000',
        minQty: 0.0001,
        maxQty: 20000,
      );

      final result = validator.validateOrder(
        shareActionType: ShareActionType.sell,
        isMoney: true,
        typedValue: '50000',
        preorder: preorder,
        portfolioType: PortfolioType.primary,
      );

      const expected = OrderPlacementValidationResult(
        action: OrderPlacementValidationAction.success,
      );
      expect(result, expected);
    });

    test('succeeds for SELL + SHARES when typedValue = 10000', () {
      final preorder = fakeSellPreorder(
        ownedAmount: '50000',
        ownedQuantity: '10000',
        currencyCode: 'USD',
        minVal: '1',
        maxVal: '100000',
        minQty: 0.0001,
        maxQty: 20000,
      );

      final result = validator.validateOrder(
        shareActionType: ShareActionType.sell,
        isMoney: false,
        typedValue: '10000',
        preorder: preorder,
        portfolioType: PortfolioType.primary,
      );

      const expected = OrderPlacementValidationResult(
        action: OrderPlacementValidationAction.success,
      );
      expect(result, expected);
    });
  });
}
