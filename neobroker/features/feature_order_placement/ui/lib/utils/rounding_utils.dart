import 'dart:math';

extension NumberRounding on num {
  num roundDownToPrecision(int precision) {
    final isNegative = this.isNegative;
    final mod = pow(10.0, precision);
    final roundDown = ((abs() * mod).floor()) / mod;

    return isNegative ? -roundDown : roundDown;
  }

  num roundUpToPrecision(int precision) {
    final isNegative = this.isNegative;
    final mod = pow(10.0, precision);
    final roundDown = ((abs() * mod).ceil()) / mod;

    return isNegative ? -roundDown : roundDown;
  }
}
