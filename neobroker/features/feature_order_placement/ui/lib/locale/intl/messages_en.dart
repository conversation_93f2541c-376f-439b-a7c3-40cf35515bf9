// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.
// @dart=2.12
// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = MessageLookup();

typedef String? MessageIfAbsent(String? messageStr, List<Object>? args);

class MessageLookup extends MessageLookupByLibrary {
  @override
  String get localeName => 'en';

  static m0(days) => "Day trades left ${days} of 3";

  static m1(days) => "${days} of 3";

  static m2(action) => "Edit limit ${action} price";

  static m3(rate) =>
      "Your limit price is ${rate} above the current market price";

  static m4(rate) =>
      "Your limit price is ${rate} below the current market price";

  static m5(ceiling, rate) =>
      "Your limit price is ${rate} above the last traded price and is above the estimated upper limit price. Enter a price of ${ceiling} or below ";

  static m6(floor, rate) =>
      "Your limit price is ${rate} below the last traded price and is below the estimated lower circuit limit price. Enter a price of ${floor} or above";

  static m7(price) => "Market price: ${price}";

  static m8(tick) => "Please enter a value in multiples of ${tick}";

  static m9(sum) => "Placed for ${sum}";

  static m10(price) => "At the estimated price of ${price}";

  static m11(action, amount) => "Available to ${action}: ${amount}";

  static m12(amount) => "Available balance: ${amount}";

  static m13(sharesQuantity) => "Available: ${sharesQuantity}";

  static m14(amount) => "Balance: ${amount}";

  static m15(limitPrice, quantity, symbol) =>
      "You ordered to buy ${quantity} ${symbol} at ${limitPrice} each.";

  static m16(price, quantity, symbol) =>
      "You ordered to buy ${quantity} ${symbol} at the market price when the price rises to ${price} per share.";

  static m17(amount, symbol) => "Order placed to buy ${symbol} for ${amount}";

  static m18(limitPrice, quantity, symbol) =>
      "You ordered to buy ${quantity} shares of ${symbol} at ${limitPrice} each.";

  static m19(price, quantity, symbol) =>
      "You ordered to buy ${quantity} shares of ${symbol} at the market price when the price rises to ${price} per share.";

  static m20(amount, company) =>
      "Order placed to buy shares of ${company} for ${amount}";

  static m21(balance) => "Buying power: ${balance}";

  static m22(days) => "In ${days} days";

  static m23(days) => "${days} days";

  static m24(amount) => "For the equivalent of ${amount}";

  static m25(action) => "How much would you like to ${action}?";

  static m26(amount, crypto) => "1 ${crypto} = ${amount}";

  static m27(amount) => "1 share = ${amount}";

  static m28(limitPrice, quantity, symbol) =>
      "You ordered to sell ${quantity} ${symbol} at ${limitPrice} each.";

  static m29(price, quantity, symbol) =>
      "You ordered to sell ${quantity} ${symbol} at the market price when the price falls to ${price} per share.";

  static m30(amount, company) => "Order placed to sell ${amount} ${company}";

  static m31(limitPrice, quantity, symbol) =>
      "You ordered to sell ${quantity} shares of ${symbol} at ${limitPrice} each.";

  static m32(price, quantity, symbol) =>
      "You ordered to sell ${quantity} shares of ${symbol} at the market price when the price falls to ${price} per share.";

  static m33(amount, company) =>
      "Order placed to sell ${amount} shares of ${company}";

  static m34(action, amount, instrumentName) =>
      "Your ${action} order for ${amount} of ${instrumentName} shares has been placed";

  static m35(currency, price) =>
      "Stop order price must be 0.5% (${price}) above the market price and at least 0.05 ${currency} higher";

  static m36(currency, price) =>
      "Stop order price must be 0.5% (${price}) below the market price and at least 0.05 ${currency} lower";

  static m37(amount) => "UAE Dirham balance: ${amount}";

  static m38(amount) => "US Dollar balance: ${amount}";

  static m39(action, instrumentName) =>
      "I want to ${action} ${instrumentName} shares";

  static m40(amount) => "Your new balance will be: ${amount}";

  static m41(shares) => "Placed ${shares} shares";

  static m42(amount, symbol) => "Market price 1 ${symbol} = ${amount}";

  static m43(symbol) =>
      "Specify the price you want to pay per one share of ${symbol}";

  static m44(action) => "Set up the limit price you want to ${action} shares";

  static m45(action) => "Set limit ${action} price";

  static m46(rate) =>
      "Your order price is ${rate} higher than the current market price";

  static m47(rate) =>
      "Your order price is ${rate} lower than the current market price";

  @override
  final Map<String, dynamic> messages =
      _notInlinedMessages(_notInlinedMessages);

  static Map<String, dynamic> _notInlinedMessages(_) => {
        'askSellAllLabel':
            MessageLookupByLibrary.simpleMessage('Do you want to sell it all?'),
        'contitnueWithOrder':
            MessageLookupByLibrary.simpleMessage('Continue with current order'),
        'customerFeedbackOrderPlacementHighlightTitle':
            MessageLookupByLibrary.simpleMessage('How did it go?'),
        'customerFeedbackOrderPlacementTitle':
            MessageLookupByLibrary.simpleMessage(
                'You placed your order! How did it go?'),
        'dayTradesLeft': m0,
        'daysOutOf3': m1,
        'editLimitPriceCta': m2,
        'errorTitleOrderStatus':
            MessageLookupByLibrary.simpleMessage('We are sorry!'),
        'learnMore': MessageLookupByLibrary.simpleMessage('Learn more'),
        'limitOrderBottomSheetButton':
            MessageLookupByLibrary.simpleMessage('Great news!'),
        'limitOrderBottomSheetHeader':
            MessageLookupByLibrary.simpleMessage('Meet Limit Orders'),
        'limitOrderBottomSheetText': MessageLookupByLibrary.simpleMessage(
            'You now have the power to set your desired price when placing an order. Your order will be executed at the set price or better.'),
        'limitOrderBuyLongDescription': MessageLookupByLibrary.simpleMessage(
            'It’s the price you\'re willing to buy at. Your order will only be executed when the stock price either reaches your limit price or falls below it. Your limit order will expire by the end of the trading day.'),
        'limitOrderBuyShorDescription': MessageLookupByLibrary.simpleMessage(
            'Buy at selected price or lower'),
        'limitOrderDecimalInfoTitle': MessageLookupByLibrary.simpleMessage(
            'Cash-based orders \nand fractional shares aren\'t supported for limit orders'),
        'limitOrderMarketOpenRestriction': MessageLookupByLibrary.simpleMessage(
            'Only limit orders are allowed during the first 30 minutes of market open due to high price volatility.'),
        'limitOrderSellLongDescription': MessageLookupByLibrary.simpleMessage(
            'It’s the price you\'re willing to sell at. Your order will only be executed when the stock price either reaches your limit price or exceeds it. Your limit order will expire by the end of the trading day.'),
        'limitOrderSellShortDescription': MessageLookupByLibrary.simpleMessage(
            'Sell not lower than selected price'),
        'limitOrderUaeDecimalInfoTitle': MessageLookupByLibrary.simpleMessage(
            'Fractional shares aren\'t supported for limit orders'),
        'limitPrice': MessageLookupByLibrary.simpleMessage('Limit price'),
        'limitPriceIsEqual': MessageLookupByLibrary.simpleMessage(
            'Your limit price is equal to the current market price'),
        'limitPriceIsHigher': m3,
        'limitPriceIsLower': m4,
        'limitPriceTooHigh': m5,
        'limitPriceTooLow': m6,
        'marketOrderBuyLongDescription': MessageLookupByLibrary.simpleMessage(
            'Buy at the best available current market price. The order is processed promptly, without specifying a particular price.'),
        'marketOrderBuyShortDescription': MessageLookupByLibrary.simpleMessage(
            'Buy at the current market price'),
        'marketOrderSellLongDescription': MessageLookupByLibrary.simpleMessage(
            'Sell at the best available current market price. The order is processed promptly, without specifying a particular price.'),
        'marketOrderSellShortDescription': MessageLookupByLibrary.simpleMessage(
            'Sell at the current market price'),
        'marketPrice': m7,
        'noMoreDayTradesLeft':
            MessageLookupByLibrary.simpleMessage('No more day trades left'),
        'onlyLimitOrderAvailable':
            MessageLookupByLibrary.simpleMessage('Only limit orders available'),
        'onlyLimitOrderInfoDescription': MessageLookupByLibrary.simpleMessage(
            'Set your limit order price, which will be executed at the set price or better. The limit order expires by the end of the trading day.'),
        'onlyLimitOrderInfoTitle': MessageLookupByLibrary.simpleMessage(
            'Only limit orders are permitted when UAE market is closed'),
        'orderAmount': MessageLookupByLibrary.simpleMessage('Amount'),
        'orderAveragePrice':
            MessageLookupByLibrary.simpleMessage('Average price'),
        'orderCommission':
            MessageLookupByLibrary.simpleMessage('Estimated commission'),
        'orderDayTradesLeftLabel':
            MessageLookupByLibrary.simpleMessage('Day trades left'),
        'orderEstimatedAmount':
            MessageLookupByLibrary.simpleMessage('Estimated amount'),
        'orderEstimatedGrossAmount':
            MessageLookupByLibrary.simpleMessage('Estimated order amount'),
        'orderEstimatedNetAmount':
            MessageLookupByLibrary.simpleMessage('Estimated order amount'),
        'orderEstimatedPrice':
            MessageLookupByLibrary.simpleMessage('Estimated price'),
        'orderEstimatedQuantity':
            MessageLookupByLibrary.simpleMessage('Estimated quantity'),
        'orderLimitOrder': MessageLookupByLibrary.simpleMessage('Limit order'),
        'orderLimitPriceTickValidationLabel': m8,
        'orderMarketOrder':
            MessageLookupByLibrary.simpleMessage('Market order'),
        'orderNoFee': MessageLookupByLibrary.simpleMessage('NO FEE'),
        'orderPlacedForSumLabel': m9,
        'orderPlacementAllSetToBuy': MessageLookupByLibrary.simpleMessage(
            'All set! Are you ready to invest?'),
        'orderPlacementAllSetToSell': MessageLookupByLibrary.simpleMessage(
            'All set! Are you ready to sell?'),
        'orderPlacementAtPriceOfCta': m10,
        'orderPlacementAvailableAmount': m11,
        'orderPlacementAvailableBalance': m12,
        'orderPlacementAvailableShares': m13,
        'orderPlacementBalance': m14,
        'orderPlacementBuyAction': MessageLookupByLibrary.simpleMessage('buy'),
        'orderPlacementBuyCryptoLimitSuccessDescription': m15,
        'orderPlacementBuyCryptoStopSuccessDescription': m16,
        'orderPlacementBuyCryptoSuccessDescription': m17,
        'orderPlacementBuyCta': MessageLookupByLibrary.simpleMessage('Buy'),
        'orderPlacementBuyLimitSuccessDescription': m18,
        'orderPlacementBuyStopSuccessDescription': m19,
        'orderPlacementBuySuccessDescription': m20,
        'orderPlacementBuyingPowerBalance': m21,
        'orderPlacementBuyingPowerLabel':
            MessageLookupByLibrary.simpleMessage('Buying power'),
        'orderPlacementBuyingPowerSubtitle': MessageLookupByLibrary.simpleMessage(
            'The total amount of funds available for you to invest, including both your own cash and additional funds accessible through margin (leverage).'),
        'orderPlacementCancelAction':
            MessageLookupByLibrary.simpleMessage('cancel'),
        'orderPlacementCancelCta':
            MessageLookupByLibrary.simpleMessage('Cancel'),
        'orderPlacementClosedLimitWarningText':
            MessageLookupByLibrary.simpleMessage(
                'The order will be executed when the market opens and the price hits the limit price.'),
        'orderPlacementClosedMarketWarningText':
            MessageLookupByLibrary.simpleMessage(
                'The order will be executed when the market opens at the best available price.'),
        'orderPlacementClosedStopWarningText': MessageLookupByLibrary.simpleMessage(
            'The order will be executed when the market opens and the price hits the stop price.'),
        'orderPlacementCommissionWithVat':
            MessageLookupByLibrary.simpleMessage('Commission (incl. VAT)'),
        'orderPlacementConfirm':
            MessageLookupByLibrary.simpleMessage('Got it!'),
        'orderPlacementContinueCta':
            MessageLookupByLibrary.simpleMessage('Continue'),
        'orderPlacementCryptoOrderAnswerOneLabel':
            MessageLookupByLibrary.simpleMessage(
                'The prices you see when buying or selling cryptoassets are based on exchange rates provided by institutional liquidity providers and market makers. These rates are accessed through our partner, Fuze, who facilitates the transaction via the liquidity partners and market makers. The buy price is slightly higher than the sell price — this difference is known as the spread, which reflects real-time market conditions and is built into the price shown to you.'),
        'orderPlacementCryptoOrderAnswerTwoLabel':
            MessageLookupByLibrary.simpleMessage(
                'Cryptoasset prices are highly dynamic and can change from second to second due to global market demand, supply, and trading activity. As a result, the exchange rate you see may vary depending on when you initiate your transaction. The following factors may contribute to variation in rates:\n\n• Type of cryptoasset\n\n• Liquidity\n\n• Market volatility\n\n• Foreign exchange rates\n\n• Size and type of your transaction'),
        'orderPlacementCryptoOrderConfirmWarningText':
            MessageLookupByLibrary.simpleMessage(
                'The executed amount may vary based on the actual execution price, which includes a spread built into the price. Learn more'),
        'orderPlacementCryptoOrderQuestionOneTitle':
            MessageLookupByLibrary.simpleMessage(
                'Where do cryptoasset prices come from?'),
        'orderPlacementCryptoOrderQuestionTwoTitle':
            MessageLookupByLibrary.simpleMessage(
                'Why does the exchange rate change?'),
        'orderPlacementCustomDateLabel':
            MessageLookupByLibrary.simpleMessage('Custom date'),
        'orderPlacementCustomExpirationDateBottomSheetSubtitle':
            MessageLookupByLibrary.simpleMessage(
                'Select any trading day in the next 90 days'),
        'orderPlacementCustomLabelInExpirationDateBottomSheet':
            MessageLookupByLibrary.simpleMessage('Custom'),
        'orderPlacementEditCta': MessageLookupByLibrary.simpleMessage('Edit'),
        'orderPlacementEditLabel': MessageLookupByLibrary.simpleMessage('Edit'),
        'orderPlacementExpirationDateLabel':
            MessageLookupByLibrary.simpleMessage('Expiration date'),
        'orderPlacementExpirationDaysDifferenceLabel': m22,
        'orderPlacementExpirationDaysLabel': m23,
        'orderPlacementForEquivalentOfAmountCta': m24,
        'orderPlacementHowMuchToOrderTitle': m25,
        'orderPlacementInvestAction':
            MessageLookupByLibrary.simpleMessage('invest'),
        'orderPlacementLearnMoreLabel':
            MessageLookupByLibrary.simpleMessage('Learn more'),
        'orderPlacementMainPortfolio':
            MessageLookupByLibrary.simpleMessage('Main'),
        'orderPlacementMarginPortfolio':
            MessageLookupByLibrary.simpleMessage('Margin'),
        'orderPlacementNextTradingDateLabel':
            MessageLookupByLibrary.simpleMessage('Next trading date'),
        'orderPlacementOneCryptoEqualsAmount': m26,
        'orderPlacementOneShareEqualsAmount': m27,
        'orderPlacementOpenLimitWarningText':
            MessageLookupByLibrary.simpleMessage(
                'The order will be executed when price hits the limit price.'),
        'orderPlacementOpenStopWarningText':
            MessageLookupByLibrary.simpleMessage(
                'The order will be executed when price hits the stop price.'),
        'orderPlacementOrderType':
            MessageLookupByLibrary.simpleMessage('Order type'),
        'orderPlacementRelatedQuestionsLabel':
            MessageLookupByLibrary.simpleMessage('Related questions'),
        'orderPlacementRewardsTitle':
            MessageLookupByLibrary.simpleMessage('Rewards'),
        'orderPlacementSellAction':
            MessageLookupByLibrary.simpleMessage('sell'),
        'orderPlacementSellCryptoLimitSuccessDescription': m28,
        'orderPlacementSellCryptoStopSuccessDescription': m29,
        'orderPlacementSellCryptoSuccessDescription': m30,
        'orderPlacementSellCta': MessageLookupByLibrary.simpleMessage('Sell'),
        'orderPlacementSellLimitSuccessDescription': m31,
        'orderPlacementSellStopSuccessDescription': m32,
        'orderPlacementSellSuccessDescription': m33,
        'orderPlacementSetExpirationDateBottomSheetActionText':
            MessageLookupByLibrary.simpleMessage('Confirm'),
        'orderPlacementSetExpirationDateBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('Set expiration date'),
        'orderPlacementSharesTitle':
            MessageLookupByLibrary.simpleMessage('Shares'),
        'orderPlacementStatusBalanceTitle':
            MessageLookupByLibrary.simpleMessage(
                'Your new portfolio balance will be'),
        'orderPlacementStatusBuyingAction':
            MessageLookupByLibrary.simpleMessage('buying'),
        'orderPlacementStatusDescription': m34,
        'orderPlacementStatusSellingAction':
            MessageLookupByLibrary.simpleMessage('selling'),
        'orderPlacementStatusSwipeUpToCloseCta':
            MessageLookupByLibrary.simpleMessage('Swipe up to close'),
        'orderPlacementStatusTitle':
            MessageLookupByLibrary.simpleMessage('MABROOK!'),
        'orderPlacementStopOrderAboveLimit': m35,
        'orderPlacementStopOrderAboveLimitNoPrice':
            MessageLookupByLibrary.simpleMessage(
                'Stop order price must be 0.5% above the market price and at least 0.05 higher'),
        'orderPlacementStopOrderBelowLimit': m36,
        'orderPlacementStopOrderBelowLimitNoPrice':
            MessageLookupByLibrary.simpleMessage(
                'Stop order price must be 0.5% below the market price and at least 0.05 lower'),
        'orderPlacementSuccessViewTransactionHistory':
            MessageLookupByLibrary.simpleMessage('View transaction history'),
        'orderPlacementSwipeToConfirmCta':
            MessageLookupByLibrary.simpleMessage('Swipe right to confirm'),
        'orderPlacementSwitchPortfoliosOverlayText':
            MessageLookupByLibrary.simpleMessage(
                'Now you can easily switch between your portfolios from here'),
        'orderPlacementTodayLabel':
            MessageLookupByLibrary.simpleMessage('Today'),
        'orderPlacementUaeBalance': m37,
        'orderPlacementUaeDecimalInfoTitle': MessageLookupByLibrary.simpleMessage(
            'In UAE markets, you can only buy or sell whole numbers of shares.'),
        'orderPlacementUaeDisclaimerContent': MessageLookupByLibrary.simpleMessage(
            'When you buy UAE companies using market order, we’ll hold a little more money on your account to make sure that we can buy the requested share quantity. \n\nAfter the successful purchase, we will return the rest of the amount back.'),
        'orderPlacementUaeDisclaimerHeader':
            MessageLookupByLibrary.simpleMessage(
                'We’ll hold a little more money'),
        'orderPlacementUaeExpirationBottomSheetDetails':
            MessageLookupByLibrary.simpleMessage(
                'For UAE stocks, the expiration date for limit orders is “Today Only.” This means that any limit orders placed are valid only for the trading day they were entered. If the order is not executed by the end of the trading day, it will automatically expire and will not carry over to the next day.'),
        'orderPlacementUaeExpirationBottomSheetHeader':
            MessageLookupByLibrary.simpleMessage(
                'Expiration date for UAE stocks'),
        'orderPlacementUsBalance': m38,
        'orderPlacementWantToOrderSharesCta': m39,
        'orderPlacementWarningTextAmount': MessageLookupByLibrary.simpleMessage(
            'The executed amount can vary based on the actual execution price.'),
        'orderPlacementWarningTextQuantity': MessageLookupByLibrary.simpleMessage(
            'The executed quantity can vary based on the actual execution price.'),
        'orderPlacementYourNewBalance': m40,
        'orderPortfolio': MessageLookupByLibrary.simpleMessage('Portfolio'),
        'orderQuantity': MessageLookupByLibrary.simpleMessage('Quantity'),
        'orderSharesPlacedLabel': m41,
        'orderStopOrder': MessageLookupByLibrary.simpleMessage('Stop order'),
        'orderTransactionFee':
            MessageLookupByLibrary.simpleMessage('Transaction fee'),
        'orderTypes': MessageLookupByLibrary.simpleMessage('Order types'),
        'orderVatOnCommission':
            MessageLookupByLibrary.simpleMessage('Estimated VAT on commission'),
        'pdtViolation90DaysBanDescription': MessageLookupByLibrary.simpleMessage(
            'If you complete this order, you will be flagged for Pattern Day Trading violation and from tomorrow you won’t be able to place new trades for 90 days.'),
        'selectDate': MessageLookupByLibrary.simpleMessage('Select date'),
        'selectPortfolioLabel':
            MessageLookupByLibrary.simpleMessage('Select portfolio'),
        'selectPortfolioSubtitle': MessageLookupByLibrary.simpleMessage(
            'Select the portfolio to which you’d like to add this asset:'),
        'sellAllDescription': MessageLookupByLibrary.simpleMessage(
            'The order quantity is greater than 95% of current holdings. Would you like to sell all of your holdings to avoid residual quantity of negligible value?'),
        'sellAllHoldings':
            MessageLookupByLibrary.simpleMessage('Sell all shares'),
        'sellAllLabel': MessageLookupByLibrary.simpleMessage('Sell all'),
        'sellAnyway': MessageLookupByLibrary.simpleMessage('Sell anyway'),
        'setLimitMarketPriceDescription': m42,
        'setLimitPriceBuyWarningDescription': MessageLookupByLibrary.simpleMessage(
            'As your limit price is higher than the market price, your order will most likely be filled once the market is open.'),
        'setLimitPriceBuyWarningTitle': MessageLookupByLibrary.simpleMessage(
            'Your proposed price is higher than the current market price'),
        'setLimitPriceCostText': m43,
        'setLimitPriceLabelBuyDefault': MessageLookupByLibrary.simpleMessage(
            'Set the price you want to spend per share'),
        'setLimitPriceLabelDefault': m44,
        'setLimitPriceLabelSellDefault': MessageLookupByLibrary.simpleMessage(
            'Set the price for your shares'),
        'setLimitPriceSellWarningDescription': MessageLookupByLibrary.simpleMessage(
            'As your limit price is lower than the market price, your order will most likely be filled immediately when market is open.'),
        'setLimitPriceSellWarningTitle': MessageLookupByLibrary.simpleMessage(
            'You want to sell shares lower than the current market price'),
        'setLimitPriceTitle': m45,
        'setStopPriceBuyWarningDescription': MessageLookupByLibrary.simpleMessage(
            'As your stop price is lower than the market price, your order will most likely be filed at the current market price.'),
        'setStopPriceBuyWarningTitle': MessageLookupByLibrary.simpleMessage(
            'The stop order price is lower than the current market price'),
        'setStopPriceSellWarningDescription': MessageLookupByLibrary.simpleMessage(
            'As your stop price is higher than the market price, your order will most likely be filled immediately at the market price.'),
        'setStopPriceSellWarningTitle': MessageLookupByLibrary.simpleMessage(
            'The stop order price is higher than the current market price'),
        'setStopPriceTitle':
            MessageLookupByLibrary.simpleMessage('Set stop order price'),
        'somethingWentWrongOnOurSide': MessageLookupByLibrary.simpleMessage(
            'Something went wrong on our side. We are unable to place your order now. \nPlease try again in few minutes.'),
        'stopOrderBottomSheetButton':
            MessageLookupByLibrary.simpleMessage('Great news!'),
        'stopOrderBottomSheetHeader':
            MessageLookupByLibrary.simpleMessage('Meet Stop Orders'),
        'stopOrderBottomSheetText': MessageLookupByLibrary.simpleMessage(
            'Now you can manage your risk, stop order is like a safety net for your investments. You set a specific price, and if the stock drops to that price or lower, it automatically sells, helping you limit potential losses.'),
        'stopOrderBuyLongDescription': MessageLookupByLibrary.simpleMessage(
            'A buy-stop order is a specific price set above the current market price for purchasing a stock. When the stock hits or exceeds this price, a market order is triggered to buy the stock.'),
        'stopOrderBuyShorDescription': MessageLookupByLibrary.simpleMessage(
            'Buy above current market price'),
        'stopOrderSellLongDescription': MessageLookupByLibrary.simpleMessage(
            'A stop order is like a safety net for your investments. You set a specific price, and if the stock drops to that price or lower, it automatically sells, helping you limit potential losses.'),
        'stopOrderSellShortDescription': MessageLookupByLibrary.simpleMessage(
            'Sell below current market price'),
        'stopPrice': MessageLookupByLibrary.simpleMessage('Stop price'),
        'stopPriceIsEqual': MessageLookupByLibrary.simpleMessage(
            'Your order price is equal to the current market price'),
        'stopPriceIsHigher': m46,
        'stopPriceIsLower': m47,
        'understandTheRiskAndWantContinue':
            MessageLookupByLibrary.simpleMessage(
                'I understand the risk and want to continue'),
        'wealthGotIt': MessageLookupByLibrary.simpleMessage('Got it!'),
        'whatIsDayTradeDescription': MessageLookupByLibrary.simpleMessage(
            'When you buy and then sell the same stock on the same trading day, you\'ve made a day trade. In general, day trading is often seen as more risky than long term investing.'),
        'whatIsDayTradeTitle':
            MessageLookupByLibrary.simpleMessage('What is a day trade?'),
        'whatIsTheExceptionDescription': MessageLookupByLibrary.simpleMessage(
            'If your portfolio value is above \$25,000, then you can make as many day trades as you would like, but you run the risk of being tagged as PDT trader, when the value of your portfolio falls below \$25,000 and if you have already exceeded 3 day trades within a 5 day rolling period.'),
        'whatIsTheExceptionTitle':
            MessageLookupByLibrary.simpleMessage('What is the exception?'),
        'whyOnlyAllow3DayTradesDescription': MessageLookupByLibrary.simpleMessage(
            'As per pattern day trading (PDT) rules, you\'re not allowed to make more than 3 day trades in a 5 day rolling period, if your portfolio value is below \$25,000. If you exceed 3 day trades, you will be tagged as a pattern day trader and will be blocked for next 90 days from making new purchases.'),
        'whyOnlyAllow3DayTradesTitle': MessageLookupByLibrary.simpleMessage(
            'Why only allow 3 day trades?'),
        'willWaitTomorrowToSell': MessageLookupByLibrary.simpleMessage(
            'Will wait tomorrow to sell it'),
        'youWillBeBanFor90Days':
            MessageLookupByLibrary.simpleMessage('You will be ban for 90 days')
      };
}
