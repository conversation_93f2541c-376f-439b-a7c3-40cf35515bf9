// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'crypto_order_confirm_warning_bottom_sheet_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CryptoOrderConfirmWarningBottomSheetConfig {}

/// @nodoc
abstract class $CryptoOrderConfirmWarningBottomSheetConfigCopyWith<$Res> {
  factory $CryptoOrderConfirmWarningBottomSheetConfigCopyWith(
          CryptoOrderConfirmWarningBottomSheetConfig value,
          $Res Function(CryptoOrderConfirmWarningBottomSheetConfig) then) =
      _$CryptoOrderConfirmWarningBottomSheetConfigCopyWithImpl<$Res,
          CryptoOrderConfirmWarningBottomSheetConfig>;
}

/// @nodoc
class _$CryptoOrderConfirmWarningBottomSheetConfigCopyWithImpl<$Res,
        $Val extends CryptoOrderConfirmWarningBottomSheetConfig>
    implements $CryptoOrderConfirmWarningBottomSheetConfigCopyWith<$Res> {
  _$CryptoOrderConfirmWarningBottomSheetConfigCopyWithImpl(
      this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CryptoOrderConfirmWarningBottomSheetConfig
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$CryptoOrderConfirmWarningBottomSheetConfigImplCopyWith<$Res> {
  factory _$$CryptoOrderConfirmWarningBottomSheetConfigImplCopyWith(
          _$CryptoOrderConfirmWarningBottomSheetConfigImpl value,
          $Res Function(_$CryptoOrderConfirmWarningBottomSheetConfigImpl)
              then) =
      __$$CryptoOrderConfirmWarningBottomSheetConfigImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CryptoOrderConfirmWarningBottomSheetConfigImplCopyWithImpl<$Res>
    extends _$CryptoOrderConfirmWarningBottomSheetConfigCopyWithImpl<$Res,
        _$CryptoOrderConfirmWarningBottomSheetConfigImpl>
    implements _$$CryptoOrderConfirmWarningBottomSheetConfigImplCopyWith<$Res> {
  __$$CryptoOrderConfirmWarningBottomSheetConfigImplCopyWithImpl(
      _$CryptoOrderConfirmWarningBottomSheetConfigImpl _value,
      $Res Function(_$CryptoOrderConfirmWarningBottomSheetConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of CryptoOrderConfirmWarningBottomSheetConfig
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CryptoOrderConfirmWarningBottomSheetConfigImpl
    extends _CryptoOrderConfirmWarningBottomSheetConfig {
  const _$CryptoOrderConfirmWarningBottomSheetConfigImpl() : super._();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CryptoOrderConfirmWarningBottomSheetConfigImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;
}

abstract class _CryptoOrderConfirmWarningBottomSheetConfig
    extends CryptoOrderConfirmWarningBottomSheetConfig {
  const factory _CryptoOrderConfirmWarningBottomSheetConfig() =
      _$CryptoOrderConfirmWarningBottomSheetConfigImpl;
  const _CryptoOrderConfirmWarningBottomSheetConfig._() : super._();
}
