// ignore_for_file: unused_element

part of 'order_placement_page.dart';

class _OrderPlacementShimmerContent extends StatelessWidget {
  final ShareActionType shareActionType;
  const _OrderPlacementShimmerContent({
    required this.shareActionType,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final localize = OrderPlacementLocalization.of(context);
    final state = context.watch<OrderPlacementCubit>().state;

    return CompanyShimmer(
      model: const CompanyShimmerModel(),
      child: Column(
        children: [
          Expanded(
            child: CustomScrollView(
              slivers: [
                _SliverHeader(
                  largeTitle: Label(
                    model: LabelModel(
                      backgroundColor: CompanyColorPointer.background1,
                      textStyle: CompanyTextStylePointer.h3,
                      text: shareActionType.orderPlacementHeader(
                        localize,
                        symbol: 'AAPL',
                        isOrderSelectionAvailable:
                            state.isOrderSelectionAvailable,
                      ),
                    ),
                  ),
                ),
                const SliverToBoxAdapter(
                  child: Space.vertical(40),
                ),
                const SliverList(
                  delegate: SliverChildListDelegate.fixed(
                    [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 24.0),
                        child: NumpadEntryContent.skeleton(itemCount: 2),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          ...[
            const Space.vertical(16),
            SizedBox(
              height: 48,
              width: double.infinity,
              child: Button(
                model: ButtonModel(
                  textBackgroundColor: CompanyColorPointer.surface6,
                  title: localize.orderPlacementContinueCta,
                ),
              ),
            ),
            const Space.vertical(36),
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.35,
              child: const NumpadKeyboard.skeleton(),
            ),
          ].map(
            (e) => Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: e,
            ),
          ),
        ],
      ),
    );
  }
}

class _SliverHeader extends StatelessWidget {
  final String? title;
  final Widget? largeTitle;
  final CompanyColorPointer? backgroundColor;
  final GraphicAssetPointer? rightIcon;
  final void Function()? onRightIconPressed;
  final bool isShimmer;

  const _SliverHeader({
    this.title,
    this.largeTitle,
    this.backgroundColor,
    this.rightIcon,
    this.onRightIconPressed,
    this.isShimmer = false,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SliverPersistentHeader(
      pinned: true,
      delegate: _SliverHeaderDelegate(
        largeTitle: largeTitle,
      ),
    );
  }
}

class _SliverHeaderDelegate extends SliverPersistentHeaderDelegate {
  static const defaultAppBarHeight = 56.0;
  static const defaultLargeTitleHeight = 60.0;

  final Widget? largeTitle;

  const _SliverHeaderDelegate({
    this.largeTitle,
  });

  @override
  double get minExtent => defaultAppBarHeight;

  @override
  double get maxExtent => defaultAppBarHeight + defaultLargeTitleHeight;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        AppBar(
          iconTheme: IconThemeData(
            color: context.colorStyling.primary3,
          ),
          centerTitle: true,
          elevation: 0,
          toolbarHeight: defaultAppBarHeight,
          backgroundColor: Colors.transparent,
          leading: Navigator.canPop(context) ? _BackButton() : null,
        ),
        Expanded(
          child: OverflowBox(
            maxWidth: double.infinity,
            maxHeight: double.infinity,
            child: SizedBox(
              width: MediaQuery.of(context).size.width,
              height: defaultLargeTitleHeight,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsetsDirectional.only(
                      start: 24,
                      end: 24,
                      bottom: 16,
                    ),
                    child: largeTitle,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  bool shouldRebuild(SliverHeaderDelegate oldDelegate) => true;
}

class _BackButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    const backArrowInnerPadding = 8.0;

    final builder = Builder(
      builder: (context) {
        final textDirection = Directionality.of(context);
        final icon = textDirection == TextDirection.rtl
            ? CompanyIconPointer.arrow_right
            : CompanyIconPointer.arrow_left;
        final tooltip = MaterialLocalizations.of(context).backButtonTooltip;

        return IconButton(
          padding: EdgeInsets.zero,
          tooltip: tooltip,
          splashRadius: SliverHeaderDelegate.defaultAppBarHeight / 2,
          onPressed: () => Navigator.maybePop(context),
          icon: CompanyIcon(
            CompanyIconModel(
              icon: icon.toGraphicAsset(),
              size: CompanyIconSize.xLarge,
              color: CompanyColorPointer.primary3,
            ),
          ),
        );
      },
    );

    return Padding(
      padding:
          const EdgeInsetsDirectional.only(start: 24.0 - backArrowInnerPadding),
      child: builder,
    );
  }
}
