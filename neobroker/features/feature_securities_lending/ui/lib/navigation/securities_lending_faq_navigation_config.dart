import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_common_feature_securities_lending_api/index.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

part 'securities_lending_faq_navigation_config.freezed.dart';

@freezed
class SecuritiesLendingFaqNavigationConfig extends ScreenNavigationConfig
    with _$SecuritiesLendingFaqNavigationConfig {
  static const screenId = 'securities_lending_faq_config';

  const factory SecuritiesLendingFaqNavigationConfig() =
      _SecuritiesLendingFaqNavigationConfig;

  const SecuritiesLendingFaqNavigationConfig._()
      : super(
          feature: SecuritiesLendingFeatureNavigationConfig.name,
          id: screenId,
        );

  @override
  String toString() => 'SecuritiesLendingFaqNavigationConfig{}';
}
