// ignore_for_file: constant_identifier_names

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_common_feature_securities_transfer_api/index.dart';

part 'securities_transfer_flow_analytics.freezed.dart';

class SecuritiesTransferFlowScreens {
  static const selectBroker = 'select_broker_screen';
  static const accountNumber = 'account_number_screen';
  static const sharesType = 'shares_type_screen';
  static const selectInstrument = 'select_instrument_screen';
  static const sharesAmount = 'shares_amount_screen';
  static const sharesList = 'shares_list_screen';
  static const tncs = 'terms_and_conditions_screen';
}

enum SecuritiesTransferFlowAnalyticsTarget {
  learn_more,
  close,
  no_broker_found,
  select_broker,
  account_number,
  specific_shares,
  select_instrument,
  shares_amount,
  shares_list,
  add_more_shares,
  delete_shares,
  complete_tncs,
  tnc_statement,
  confirmation,
}

@freezed
class SecuritiesTransferFlowPayload
    with _$SecuritiesTransferFlowPayload
    implements AnalyticsEventPayload {
  const SecuritiesTransferFlowPayload._();

  const factory SecuritiesTransferFlowPayload({
    required String screen,
  }) = _SecuritiesTransferFlowPayload;

  const factory SecuritiesTransferFlowPayload.selectBroker({
    required String screen,
    required String selectedBroker,
    required String selectedBrokerName,
  }) = _SecuritiesTransferFlowPayloadSelectBroker;

  const factory SecuritiesTransferFlowPayload.accountNumber({
    required String screen,
    required String accountNumber,
  }) = _SecuritiesTransferFlowPayloadAccountNumber;

  const factory SecuritiesTransferFlowPayload.selectInstrument({
    required String screen,
    required String instrumentName,
    required String symbol,
    String? searchText,
  }) = _SecuritiesTransferFlowPayloadSelectInstrument;

  const factory SecuritiesTransferFlowPayload.sharesAmount({
    required String screen,
    required String sharesAmount,
    required String symbol,
  }) = _SecuritiesTransferFlowPayloadSharesAmount;

  const factory SecuritiesTransferFlowPayload.termsAndConditions({
    required String screen,
    required String document,
  }) = _SecuritiesTransferFlowPayloadTermsAndConditions;

  @override
  Map<String, dynamic> getEventPayload() => map<Map<String, Object?>>(
        (event) => <String, String>{
          'screen': event.screen,
        },
        selectBroker: (event) => {
          'screen': event.screen,
          'selectedBroker': event.selectedBroker,
          'selectedBrokerName': event.selectedBrokerName,
        },
        accountNumber: (event) => {
          'screen': event.screen,
          'accountNumber': event.accountNumber,
        },
        selectInstrument: (event) => {
          'screen': event.screen,
          'instrumentName': event.instrumentName,
          'symbol': event.symbol,
          'searchText': event.searchText ?? '',
        },
        sharesAmount: (event) => {
          'screen': event.screen,
          'sharesAmount': event.sharesAmount,
          'symbol': event.symbol,
        },
        termsAndConditions: (event) => {
          'screen': event.screen,
          'document': event.document,
        },
      );
}

class SecuritiesTransferFlowAnalytics {
  final AnalyticsEventTracker _tracker;

  SecuritiesTransferFlowAnalytics({
    required AnalyticsAbstractTrackerFactory analyticsFactory,
  }) : _tracker = analyticsFactory.get(
          screenName: SecuritiesTransferScreenNavigationConfig.screenId,
          tracker: AnalyticsTracker.mixpanel,
        );

  void onLearnMore(String screen) {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: SecuritiesTransferFlowAnalyticsTarget.learn_more,
      payload: SecuritiesTransferFlowPayload(screen: screen),
    );
  }

  void onClose(String screen) {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: SecuritiesTransferFlowAnalyticsTarget.close,
      payload: SecuritiesTransferFlowPayload(screen: screen),
    );
  }

  void onNoBrokerFoundPressed() {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: SecuritiesTransferFlowAnalyticsTarget.no_broker_found,
      payload: const SecuritiesTransferFlowPayload(
        screen: SecuritiesTransferFlowScreens.selectBroker,
      ),
    );
  }

  void onBrokerSelected({
    required String selectedBrokerId,
    required String selectedBrokerName,
  }) {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: SecuritiesTransferFlowAnalyticsTarget.select_broker,
      payload: SecuritiesTransferFlowPayload.selectBroker(
        screen: SecuritiesTransferFlowScreens.selectBroker,
        selectedBroker: selectedBrokerId,
        selectedBrokerName: selectedBrokerName,
      ),
    );
  }

  void onAccountNumberSubmitted({
    required String accountNumber,
  }) {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: SecuritiesTransferFlowAnalyticsTarget.account_number,
      payload: SecuritiesTransferFlowPayload.accountNumber(
        screen: SecuritiesTransferFlowScreens.accountNumber,
        accountNumber: accountNumber,
      ),
    );
  }

  void onSpecificSharesPressed() {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: SecuritiesTransferFlowAnalyticsTarget.specific_shares,
      payload: const SecuritiesTransferFlowPayload(
        screen: SecuritiesTransferFlowScreens.sharesType,
      ),
    );
  }

  void onInstrumentPressed({
    required String instrumentName,
    required String symbol,
    String? searchText,
  }) {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: SecuritiesTransferFlowAnalyticsTarget.select_instrument,
      payload: SecuritiesTransferFlowPayload.selectInstrument(
        screen: SecuritiesTransferFlowScreens.selectInstrument,
        instrumentName: instrumentName,
        symbol: symbol,
        searchText: searchText,
      ),
    );
  }

  void onSharesAmountSubmitted({
    required String sharesAmount,
    required String symbol,
  }) {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: SecuritiesTransferFlowAnalyticsTarget.shares_amount,
      payload: SecuritiesTransferFlowPayload.sharesAmount(
        screen: SecuritiesTransferFlowScreens.sharesAmount,
        sharesAmount: sharesAmount,
        symbol: symbol,
      ),
    );
  }

  void onSharesListSubmitted() {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: SecuritiesTransferFlowAnalyticsTarget.shares_list,
      payload: const SecuritiesTransferFlowPayload(
        screen: SecuritiesTransferFlowScreens.sharesList,
      ),
    );
  }

  void onAddMoreShares() {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: SecuritiesTransferFlowAnalyticsTarget.add_more_shares,
      payload: const SecuritiesTransferFlowPayload(
        screen: SecuritiesTransferFlowScreens.sharesList,
      ),
    );
  }

  void deleteShares({
    required String sharesAmount,
    required String symbol,
  }) {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: SecuritiesTransferFlowAnalyticsTarget.delete_shares,
      payload: SecuritiesTransferFlowPayload.sharesAmount(
        screen: SecuritiesTransferFlowScreens.sharesList,
        symbol: symbol,
        sharesAmount: sharesAmount,
      ),
    );
  }

  void clickTnc(String document) {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: SecuritiesTransferFlowAnalyticsTarget.tnc_statement,
      payload: SecuritiesTransferFlowPayload.termsAndConditions(
        screen: SecuritiesTransferFlowScreens.tncs,
        document: document,
      ),
    );
  }

  void onCompleteTncs() {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: SecuritiesTransferFlowAnalyticsTarget.complete_tncs,
    );
  }

  void onConfirmationButtonPressed() {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: SecuritiesTransferFlowAnalyticsTarget.confirmation,
    );
  }
}
