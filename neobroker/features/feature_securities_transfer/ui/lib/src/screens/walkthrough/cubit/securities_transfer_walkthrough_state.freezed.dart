// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'securities_transfer_walkthrough_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SecuritiesTransferWalkthroughState {}

/// @nodoc
abstract class $SecuritiesTransferWalkthroughStateCopyWith<$Res> {
  factory $SecuritiesTransferWalkthroughStateCopyWith(
          SecuritiesTransferWalkthroughState value,
          $Res Function(SecuritiesTransferWalkthroughState) then) =
      _$SecuritiesTransferWalkthroughStateCopyWithImpl<$Res,
          SecuritiesTransferWalkthroughState>;
}

/// @nodoc
class _$SecuritiesTransferWalkthroughStateCopyWithImpl<$Res,
        $Val extends SecuritiesTransferWalkthroughState>
    implements $SecuritiesTransferWalkthroughStateCopyWith<$Res> {
  _$SecuritiesTransferWalkthroughStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$SecuritiesTransferWalkthroughStateImplCopyWith<$Res> {
  factory _$$SecuritiesTransferWalkthroughStateImplCopyWith(
          _$SecuritiesTransferWalkthroughStateImpl value,
          $Res Function(_$SecuritiesTransferWalkthroughStateImpl) then) =
      __$$SecuritiesTransferWalkthroughStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SecuritiesTransferWalkthroughStateImplCopyWithImpl<$Res>
    extends _$SecuritiesTransferWalkthroughStateCopyWithImpl<$Res,
        _$SecuritiesTransferWalkthroughStateImpl>
    implements _$$SecuritiesTransferWalkthroughStateImplCopyWith<$Res> {
  __$$SecuritiesTransferWalkthroughStateImplCopyWithImpl(
      _$SecuritiesTransferWalkthroughStateImpl _value,
      $Res Function(_$SecuritiesTransferWalkthroughStateImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$SecuritiesTransferWalkthroughStateImpl
    implements _SecuritiesTransferWalkthroughState {
  const _$SecuritiesTransferWalkthroughStateImpl();

  @override
  String toString() {
    return 'SecuritiesTransferWalkthroughState()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SecuritiesTransferWalkthroughStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;
}

abstract class _SecuritiesTransferWalkthroughState
    implements SecuritiesTransferWalkthroughState {
  const factory _SecuritiesTransferWalkthroughState() =
      _$SecuritiesTransferWalkthroughStateImpl;
}
