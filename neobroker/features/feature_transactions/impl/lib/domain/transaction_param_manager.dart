import 'package:collection/collection.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_transaction_api/index.dart';
import 'package:wio_feature_account_api/wio_feature_account_api.dart';

class TransactionsParamManagerImpl implements TransactionsParamManager {
  final AccountInteractor _interactor;

  TransactionsParamManagerImpl({
    required AccountInteractor interactor,
  }) : _interactor = interactor;

  @override
  Future<String?> getId() async {
    final accounts = await _interactor.getAccountDetails();
    // Have to specify account type due to Margin Trading adding a new USD acc
    final usdAccount = accounts.singleWhereOrNull(
      (element) =>
          element.availableBalance.currency == Currency.usd &&
          element.type == AccountType.currentAccount,
    );

    return usdAccount?.id;
  }
}
