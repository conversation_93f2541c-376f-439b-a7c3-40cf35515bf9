import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

part 'recurring_orders_feature_navigation_config.freezed.dart';

@freezed
class RecurringOrdersFeatureNavigationConfig extends FeatureNavigationConfig
    with _$RecurringOrdersFeatureNavigationConfig {
  static const name = 'recurring_orders_feature';

  const factory RecurringOrdersFeatureNavigationConfig({
    required ScreenNavigationConfig destination,
  }) = _RecurringOrdersFeatureNavigationConfig;

  const RecurringOrdersFeatureNavigationConfig._() : super(name);

  @override
  String toString() => 'RecurringOrdersFeatureNavigationConfig';
}
