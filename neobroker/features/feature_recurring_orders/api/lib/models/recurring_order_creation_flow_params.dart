import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neobroker_models/models.dart';
import 'package:wio_app_core_api/money.dart';

part 'recurring_order_creation_flow_params.freezed.dart';

@freezed
class RecurringOrderCreationFlowParams with _$RecurringOrderCreationFlowParams {
  const factory RecurringOrderCreationFlowParams({
    required RecurringInstrument instrument,
    required String portfolioId,
    required RecurringOrderType recurringOrderType,
    required Currency currency,
    PreorderValidations? preorderValidations,
  }) = _RecurringOrderCreationFlowParams;
}
