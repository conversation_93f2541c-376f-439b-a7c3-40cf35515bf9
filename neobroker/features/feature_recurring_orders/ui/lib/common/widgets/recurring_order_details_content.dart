import 'package:flutter/widgets.dart';
import 'package:neobroker_models/models.dart';
import 'package:neobroker_utils/utils.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_common_feature_recurring_payments_ui/extensions/recurring_model_extensions.dart';
import 'package:wio_common_feature_recurring_payments_ui/l10n/recurring_payments_localizations.g.dart';
import 'package:wio_feature_recurring_orders_ui/flows/recurring_order_creation_flow/extensions/order_type_extension.dart';
import 'package:wio_feature_recurring_orders_ui/locale/recurring_orders_localization.g.dart';

class RecurringOrderDetailsContent extends StatelessWidget {
  final RecurringOrderTemplate template;
  final bool isFirst;
  final bool hasCloseIcon;
  final VoidCallback? onCommissionByAmountPressed;
  final VoidCallback? onCommissionByQuantityPressed;
  final VoidCallback? onClose;

  const RecurringOrderDetailsContent({
    required this.template,
    this.isFirst = false,
    this.hasCloseIcon = false,
    this.onCommissionByAmountPressed,
    this.onCommissionByQuantityPressed,
    this.onClose,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final localization = RecurringOrdersLocalization.of(context);
    final instrument = template.instrument;

    /// Screen size - horizontal padding - instrument tile image
    final labelMaxWidth = MediaQuery.of(context).size.width - 24.0 * 2 - 80.0;
    final instrumentNameLabelHeight = Label(
      model: LabelModel(
        text: instrument.name,
        textStyle: CompanyTextStylePointer.h3,
        color: CompanyColorPointer.primary3,
        maxLines: 2,
      ),
    ).getTextPainter(context, maxWidth: labelMaxWidth).size.height;

    return CustomScrollView(
      physics: const ClampingScrollPhysics(),
      slivers: [
        SliverHeader(
          title: localization.recurringDetailsTitle,
          largeTitleHeight: 88.0 + instrumentNameLabelHeight,
          largeTitlePadding: EdgeInsets.zero,
          rightIcon: hasCloseIcon
              ? const GraphicAssetPointer.icon(CompanyIconPointer.failure)
              : null,
          largeTitle: RecurringOrderDetailsHeader(
            instrumentName: instrument.name,
            value: template.map(
              byAmount: (e) => e.totalAmount.toCodeOnRightFormat(),
              byQuantity: (e) =>
                  localization.recurringDetailsShares(e.quantity.value),
            ),
            image: instrument.image ?? '',
          ),
          onRightIconPressed: onClose,
        ),
        SliverPadding(
          padding: const EdgeInsetsDirectional.fromSTEB(24, 14, 24, 0),
          sliver: SliverToBoxAdapter(
            child: WarningListBox(
              model: WarningListBoxModel(
                warning: template.map(
                  byAmount: (e) => localization.recurringDetailsWarningQuantity,
                  byQuantity: (e) => localization.recurringDetailsWarningAmount,
                ),
                backgroundColor: CompanyColorPointer.surface2,
                iconColor: CompanyColorPointer.secondary5,
                iconImage: CompanyPictogramPointer.actions_great_idea,
              ),
            ),
          ),
        ),
        SliverPadding(
          padding: const EdgeInsetsDirectional.fromSTEB(24, 20, 24, 0),
          sliver: SliverToBoxAdapter(
            child: _DetailItems(
              template: template,
              onCommissionByAmountPressed: onCommissionByAmountPressed,
              onCommissionByQuantityPressed: onCommissionByQuantityPressed,
            ),
          ),
        ),
        SliverPadding(
          padding: const EdgeInsetsDirectional.fromSTEB(24, 20, 24, 0),
          sliver: SliverToBoxAdapter(
            child: ListDetailsContainer(
              model: _nextInvestmentDate(
                template.nextInvestmentDate,
                isFirst: isFirst,
                localization: localization,
              ),
            ),
          ),
        ),
        const SliverPadding(padding: EdgeInsetsDirectional.only(bottom: 24)),
      ],
    );
  }

  ListDetailsContainerModel _nextInvestmentDate(
    DateTime nextDate, {
    required RecurringOrdersLocalization localization,
    required bool isFirst,
  }) {
    final items = <ListDetailsModel>[
      ListDetailsModel(
        textLabelModel: ListDetailsTextLabelModel(
          text: isFirst
              ? localization.recurringDetailsFirstDate
              : localization.recurringDetailsNextDate,
        ),
        valueModel: ListDetailsValueModel.text(
          textModel: ListDetailValueTextModel.label(
            content: nextDate.formatTo(
              const NeobrokerDateTimePatterns.dSpaceMMMM(),
            ),
          ),
        ),
      ),
    ];

    return ListDetailsContainerModel(
      items: items,
      borderRadius: 12,
    );
  }
}

class RecurringOrderDetailsHeader extends StatelessWidget {
  final String instrumentName;
  final String value;
  final String image;
  final bool isShimmer;

  const RecurringOrderDetailsHeader({
    required this.instrumentName,
    required this.value,
    required this.image,
    this.isShimmer = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final localization = RecurringOrdersLocalization.of(context);
    final textBackgroundColor = isShimmer ? CompanyColorPointer.primary3 : null;

    return Padding(
      padding: const EdgeInsetsDirectional.only(
        start: 24,
        end: 24,
        top: 20,
        bottom: 16,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Label(
                  model: LabelModel(
                    text: localization.recurringDetailsTitle.toUpperCase(),
                    textStyle: CompanyTextStylePointer.b4medium,
                    color: CompanyColorPointer.primary3,
                    backgroundColor: textBackgroundColor,
                  ),
                ),
                const SizedBox(height: 4),
                Label(
                  model: LabelModel(
                    text: instrumentName,
                    textStyle: CompanyTextStylePointer.h3,
                    color: CompanyColorPointer.primary3,
                    backgroundColor: textBackgroundColor,
                    maxLines: 2,
                  ),
                ),
                const SizedBox(height: 2),
                Label(
                  model: LabelModel(
                    text: value,
                    textStyle: CompanyTextStylePointer.h3,
                    color: CompanyColorPointer.primary3,
                    backgroundColor: textBackgroundColor,
                  ),
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          const SizedBox(width: 32),
          Tile(
            model: TileModel.image(
              image: ImageInTileModel(path: image),
              backgroundColor: CompanyColorPointer.background3,
            ),
            size: 48,
          ),
        ],
      ),
    );
  }
}

class _DetailItems extends StatelessWidget {
  static const commissionByAmountItem = 'commissionByAmountItem';
  static const commissionByQuantityItem = 'commissionByQuantityItem';

  final VoidCallback? onCommissionByAmountPressed;
  final VoidCallback? onCommissionByQuantityPressed;

  final RecurringOrderTemplate template;

  const _DetailItems({
    required this.template,
    this.onCommissionByAmountPressed,
    this.onCommissionByQuantityPressed,
  });

  @override
  Widget build(BuildContext context) {
    final localization = RecurringOrdersLocalization.of(context);
    final recurringLocalization = RecurringPaymentsLocalizations.of(context);
    final commissionByAmount = _commissionForAmountItem(localization);
    final commissionByQuantity = _commissionForQuantityItem(localization);

    final items = [
      template.map(
        byAmount: (e) => _listDetailsModel(
          text: localization.recurringDetailsItemAmount,
          value: e.amount.toCodeOnRightFormat(),
        ),
        byQuantity: (e) => _listDetailsModel(
          text: localization.recurringDetailsItemQuantity,
          value: localization.recurringDetailsShares(e.quantity.value),
        ),
      ),
      _listDetailsModel(
        text: localization.recurringDetailsItemAccount,
        value: template.account.name,
      ),
      if (commissionByAmount != null) commissionByAmount,
      if (commissionByQuantity != null) commissionByQuantity,
      _listDetailsModel(
        text: localization.recurringDetailsItemOrderType,
        value: template.orderType.orderTypeName(localization),
      ),
      ListDetailsModel(
        textLabelModel: ListDetailsTextLabelModel(
          text: localization.recurringDetailsItemFrequency,
        ),
        valueModel: ListDetailsValueModel.text(
          textModel: ListDetailValueTextModel.label(
            content: template.frequency.label(recurringLocalization),
          ),
        ),
        leadingFlex: 1,
        trailingFlex: 2,
      ),
    ];

    Future<void> onValuePressed(int index) async {
      if (items[index].id == commissionByAmountItem) {
        onCommissionByAmountPressed?.call();
        return;
      }
      if (items[index].id == commissionByQuantityItem) {
        onCommissionByQuantityPressed?.call();
        return;
      }
    }

    return ListDetailsContainer(
      model: ListDetailsContainerModel(items: items),
      onTextLabelIconPressed: onValuePressed,
      onValuePressed: onValuePressed,
    );
  }

  ListDetailsModel _listDetailsModel({
    required String text,
    required String value,
  }) {
    return ListDetailsModel(
      textLabelModel: ListDetailsTextLabelModel(
        text: text,
      ),
      valueModel: ListDetailsValueModel.text(
        textModel: ListDetailValueTextModel.label(
          content: value,
        ),
      ),
    );
  }

  ListDetailsModel? _commissionForAmountItem(
    RecurringOrdersLocalization localization,
  ) {
    return template.mapOrNull(
      byAmount: (e) {
        return ListDetailsModel(
          id: commissionByAmountItem,
          textLabelModel: ListDetailsTextLabelModel(
            text: localization.recurringDetailsItemCommission,
            icon: const GraphicAssetPointer.pictogram(
              CompanyPictogramPointer.actions_information,
            ),
            iconColor: CompanyColorPointer.secondary3,
          ),
          valueModel: ListDetailsValueModel.text(
            textModel: ListDetailValueTextModel.label(
              content: e.totalCommission.toCodeOnRightFormat(),
            ),
          ),
        );
      },
    );
  }

  ListDetailsModel? _commissionForQuantityItem(
    RecurringOrdersLocalization localization,
  ) {
    return template.mapOrNull(
      byQuantity: (e) {
        return ListDetailsModel(
          id: commissionByQuantityItem,
          textLabelModel: ListDetailsTextLabelModel(
            text: localization.recurringDetailsItemCommission,
          ),
          valueModel: ListDetailsValueModel.text(
            textModel: ListDetailValueTextModel.label(
              content: localization.recurringDetailsExplore,
              textColor: CompanyColorPointer.primary1,
            ),
          ),
        );
      },
    );
  }
}
