import 'dart:async';

import 'package:collection/collection.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:instruments_api/index.dart';
import 'package:neobroker_models/models.dart';
import 'package:ui/ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_recurring_orders_api/index.dart';
import 'package:wio_feature_recurring_orders_ui/analytics/recurring_orders_analytics.dart';

part 'all_recurring_orders_state.dart';
part 'all_recurring_orders_cubit.freezed.dart';

class AllRecurringOrdersCubit extends BaseCubit<AllRecurringOrdersState> {
  final RecurringOrdersInteractor _interactor;
  final RecurringOrdersFlow _recurringOrdersFlow;
  final NavigationProvider _navigationProvider;
  final RecurringOrdersAnalytics _analytics;

  Timer? _searchTimer;

  AllRecurringOrdersCubit({
    required RecurringOrdersInteractor interactor,
    required RecurringOrdersFlow recurringOrdersFlow,
    required NavigationProvider navigationProvider,
    required RecurringOrdersAnalytics analytics,
  })  : _interactor = interactor,
        _recurringOrdersFlow = recurringOrdersFlow,
        _navigationProvider = navigationProvider,
        _analytics = analytics,
        super(
          const AllRecurringOrdersState.loading(),
        ) {
    _init();
  }

  @override
  String toString() => 'AllRecurringOrdersCubit{}';

  @override
  Future<void> close() {
    _searchTimer?.cancel();

    return super.close();
  }

  Future<void> _init() async {
    try {
      final recurringOrderTemplates =
          await _interactor.getRecurringOrders().disposeBy(this);
      emit(
        AllRecurringOrdersState.loaded(
          recurringOrderTemplates: recurringOrderTemplates,
          templates: recurringOrderTemplates.recurringOrderTemplates,
        ),
      );
    } on Object catch (_) {}
  }

  Future<void> reload() async {
    emit(const AllRecurringOrdersState.loading());
    await _init();
  }

  void onSearch(String? query) {
    _searchTimer?.cancel();
    state.mapOrNull(
      loaded: (loaded) {
        _searchTimer = Timer(
          const Duration(milliseconds: 300),
          () {
            final searchText = query ?? '';
            final templates =
                loaded.recurringOrderTemplates.recurringOrderTemplates;
            if (searchText.isEmpty) {
              emit(
                loaded.copyWith(
                  templates:
                      loaded.recurringOrderTemplates.recurringOrderTemplates,
                ),
              );

              return;
            }

            final filtered = templates
                .where(
                  (e) => e.instrument.name.toLowerCase().contains(searchText),
                )
                .toList();

            emit(loaded.copyWith(templates: filtered));
          },
        );
      },
    );
  }

  void onRecurringItemPressed(RecurringOrderTemplate template) {
    state.mapOrNull(
      loaded: (loaded) async {
        final instrument = template.instrument;

        _analytics.recurringOrderItem(
          RecurringOrdersPayloadRecurringOrderItem(
            instrumentName: instrument.name,
            ticker: instrument.symbol,
            type: template.type,
            frequency: template.frequency,
            nextInvestmentDate: template.nextInvestmentDate,
            account: template.account,
            amount: template.mapOrNull(byAmount: (value) => value.amount),
            totalAmount:
                template.mapOrNull(byAmount: (value) => value.totalAmount),
            commission:
                template.mapOrNull(byAmount: (value) => value.commission),
            totalCommission:
                template.mapOrNull(byAmount: (value) => value.totalCommission),
            vat: template.mapOrNull(byAmount: (value) => value.vat),
            quantity: template.mapOrNull(byQuantity: (value) => value.quantity),
          ),
        );

        final recurringOrderTemplates = loaded.recurringOrderTemplates;

        final preorderValidations =
            recurringOrderTemplates.validations.firstWhereOrNull(
          (e) => e.exchangeId == template.instrument.exchangeId,
        );

        final result = await _recurringOrdersFlow.details(
          params: RecurringOrderDetailsFlowParams(
            template: template,
            termsAndConditions: recurringOrderTemplates.termsAndConditions,
            validations: preorderValidations?.validations,
            recurringOrderType: template.type,
          ),
        );

        await result?.mapOrNull(
          delete: (_) => reload(),
          edit: (value) {
            if (template != value.template) {
              reload();
            }
          },
          create: (_) => reload(),
        );
      },
    );
  }

  Future<void> onSetUpNewRecurringOrdersPressed() async {
    _analytics.setUpNewRecurringOrders();

    final result = await _navigationProvider.push<RecurringOrderTemplate>(
      const InstrumentsSearchNavigationConfig(
        selectedMarketFilterIndex: -1,
        instrumentPressedDelegateType:
            InstrumentPressedDelegateType.recurringOrders,
      ),
    );
    if (result != null) {
      return reload();
    }
  }
}
