// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'securities_transfer_list_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SettingsSecuritiesTransferListState {
  List<AssetTransfer> get assetTransfers => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SettingsSecuritiesTransferListStateCopyWith<
          SettingsSecuritiesTransferListState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SettingsSecuritiesTransferListStateCopyWith<$Res> {
  factory $SettingsSecuritiesTransferListStateCopyWith(
          SettingsSecuritiesTransferListState value,
          $Res Function(SettingsSecuritiesTransferListState) then) =
      _$SettingsSecuritiesTransferListStateCopyWithImpl<$Res,
          SettingsSecuritiesTransferListState>;
  @useResult
  $Res call({List<AssetTransfer> assetTransfers});
}

/// @nodoc
class _$SettingsSecuritiesTransferListStateCopyWithImpl<$Res,
        $Val extends SettingsSecuritiesTransferListState>
    implements $SettingsSecuritiesTransferListStateCopyWith<$Res> {
  _$SettingsSecuritiesTransferListStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? assetTransfers = null,
  }) {
    return _then(_value.copyWith(
      assetTransfers: null == assetTransfers
          ? _value.assetTransfers
          : assetTransfers // ignore: cast_nullable_to_non_nullable
              as List<AssetTransfer>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SettingsSecuritiesTransferListStateImplCopyWith<$Res>
    implements $SettingsSecuritiesTransferListStateCopyWith<$Res> {
  factory _$$SettingsSecuritiesTransferListStateImplCopyWith(
          _$SettingsSecuritiesTransferListStateImpl value,
          $Res Function(_$SettingsSecuritiesTransferListStateImpl) then) =
      __$$SettingsSecuritiesTransferListStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<AssetTransfer> assetTransfers});
}

/// @nodoc
class __$$SettingsSecuritiesTransferListStateImplCopyWithImpl<$Res>
    extends _$SettingsSecuritiesTransferListStateCopyWithImpl<$Res,
        _$SettingsSecuritiesTransferListStateImpl>
    implements _$$SettingsSecuritiesTransferListStateImplCopyWith<$Res> {
  __$$SettingsSecuritiesTransferListStateImplCopyWithImpl(
      _$SettingsSecuritiesTransferListStateImpl _value,
      $Res Function(_$SettingsSecuritiesTransferListStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? assetTransfers = null,
  }) {
    return _then(_$SettingsSecuritiesTransferListStateImpl(
      assetTransfers: null == assetTransfers
          ? _value._assetTransfers
          : assetTransfers // ignore: cast_nullable_to_non_nullable
              as List<AssetTransfer>,
    ));
  }
}

/// @nodoc

class _$SettingsSecuritiesTransferListStateImpl
    implements _SettingsSecuritiesTransferListState {
  const _$SettingsSecuritiesTransferListStateImpl(
      {required final List<AssetTransfer> assetTransfers})
      : _assetTransfers = assetTransfers;

  final List<AssetTransfer> _assetTransfers;
  @override
  List<AssetTransfer> get assetTransfers {
    if (_assetTransfers is EqualUnmodifiableListView) return _assetTransfers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_assetTransfers);
  }

  @override
  String toString() {
    return 'SettingsSecuritiesTransferListState(assetTransfers: $assetTransfers)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SettingsSecuritiesTransferListStateImpl &&
            const DeepCollectionEquality()
                .equals(other._assetTransfers, _assetTransfers));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_assetTransfers));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SettingsSecuritiesTransferListStateImplCopyWith<
          _$SettingsSecuritiesTransferListStateImpl>
      get copyWith => __$$SettingsSecuritiesTransferListStateImplCopyWithImpl<
          _$SettingsSecuritiesTransferListStateImpl>(this, _$identity);
}

abstract class _SettingsSecuritiesTransferListState
    implements SettingsSecuritiesTransferListState {
  const factory _SettingsSecuritiesTransferListState(
          {required final List<AssetTransfer> assetTransfers}) =
      _$SettingsSecuritiesTransferListStateImpl;

  @override
  List<AssetTransfer> get assetTransfers;
  @override
  @JsonKey(ignore: true)
  _$$SettingsSecuritiesTransferListStateImplCopyWith<
          _$SettingsSecuritiesTransferListStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
