import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neobroker_models/models.dart';
import 'package:ui/ui.dart';
import 'package:wio_broker_feature_settings_ui/navigation/settings_securities_transfer_details_navigation_config.dart';
import 'package:wio_broker_feature_settings_ui/screens/settings/analytics/settings_analytics.dart';
import 'package:wio_common_feature_securities_transfer_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';

part 'securities_transfer_list_cubit.freezed.dart';
part 'securities_transfer_list_state.dart';

class SettingsSecuritiesTransferListCubit
    extends BaseCubit<SettingsSecuritiesTransferListState> {
  final NavigationProvider _navigationProvider;
  final SettingsAnalytics _analytics;

  SettingsSecuritiesTransferListCubit({
    required NavigationProvider navigationProvider,
    required List<AssetTransfer> assetTransfers,
    required SettingsAnalytics analytics,
  })  : _navigationProvider = navigationProvider,
        _analytics = analytics,
        super(
          SettingsSecuritiesTransferListState(assetTransfers: assetTransfers),
        );

  @override
  String toString() => 'SettingsSecuritiesTransferListCubit';

  void onAssetTransferPressed(AssetTransfer assetTransfer) {
    _analytics.onAssetTransferPressed();
    _navigationProvider.push(
      SettingsSecuritiesTransferDetailsNavigationConfig(
        assetTransfer: assetTransfer,
      ),
    );
  }

  void onTransferMoreSharesPressed() {
    _analytics.onTransferMoreShares();
    _navigationProvider.navigateTo(
      const SecuritiesTransferFeatureNavigationConfig(
        destination: SecuritiesTransferScreenNavigationConfig(),
      ),
    );
  }
}
