import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_broker_feature_settings_ui/index.dart';
import 'package:wio_broker_feature_settings_ui/screens/settings/bottom_sheets/my_investor_numbers/cubit/settings_my_investor_numbers_cubit.dart';
import 'package:wio_common_feature_ipo_api/index.dart';

class SettingsMyInvestorNumbersBottomSheet extends StatefulWidget {
  const SettingsMyInvestorNumbersBottomSheet({
    Key? key,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() =>
      _SettingsMyInvestorNumbersBottomSheetState();
}

class _SettingsMyInvestorNumbersBottomSheetState
    extends State<SettingsMyInvestorNumbersBottomSheet> {
  SettingsMyInvestorNumbersCubit get bloc =>
      context.read<SettingsMyInvestorNumbersCubit>();
  @override
  Widget build(BuildContext context) {
    final state = context.watch<SettingsMyInvestorNumbersCubit>().state;
    final localization = SettingsLocalization.of(context);

    const uncopiedModel = ListDetailsValueModel.icon(
      colorPointer: CompanyColorPointer.primary3,
      iconPointer: CompanyIconPointer.actions_copy,
    );
    final copiedModel = ListDetailsValueModel.text(
      textModel: ListDetailValueTextModel.companyLabel(
        labelModel: CompanyLabelModel(
          text: localization.settingsCopiedLabel,
          backgroundColor: CompanyColorPointer.primary1,
          color: CompanyColorPointer.primary2,
        ),
      ),
    );

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Label(
            model: LabelModel(
              text: localization.settingsMyInvestorNumbers,
              textStyle: CompanyTextStylePointer.h3medium,
              color: CompanyColorPointer.primary3,
            ),
          ),
          const Space.vertical(22),
          ListDetailsContainer(
            onValuePressed: bloc.onInvestorNumberPressed,
            model: ListDetailsContainerModel(
              borderRadius: 12.0,
              items: state.ninStates.mapIndexed(
                (index, ninState) {
                  final isReady = ninState.status == NinStatus.ready;
                  final isCopied = index == state.copiedNinIndex;

                  return ListDetailsModel(
                    displayType: ListDetailDisplayType.inverted,
                    textLabelModel: ListDetailsTextLabelModel(
                      text: ninState.exchangeId.marketShortName,
                      subtitle: isReady
                          ? ninState.nin ?? ''
                          : localization.settingsGenerateCta,
                      subtitleColor: isReady
                          ? CompanyColorPointer.primary3
                          : CompanyColorPointer.primary1,
                      maxLines: 2,
                    ),
                    valueModel: isReady
                        ? isCopied
                            ? copiedModel
                            : uncopiedModel
                        : const ListDetailsValueModel.none(),
                  );
                },
              ).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
