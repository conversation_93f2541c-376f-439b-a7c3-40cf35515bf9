import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:instruments_api/index.dart';
import 'package:mocktail/mocktail.dart';
import 'package:neobroker_models/models.dart';
import 'package:news_api/index.dart';
import 'package:news_ui/analytics/news_section_analytics.dart';
import 'package:news_ui/screens/news_details/analytics/news_details_analytics.dart';
import 'package:news_ui/screens/news_list/analytics/news_list_analytics.dart';
import 'package:wio_feature_user_api/index.dart';

class MockNewsInteractor extends Mock implements NewsInteractor {
  MockNewsInteractor() {
    when(
      () => getNewsArticles(
        page: any(named: 'page'),
        pageSize: any(named: 'pageSize'),
        region: NewsArticleRegion.us,
      ),
    ).thenAnswer(
      (_) => Future.value(news),
    );

    when(
      () => getNewsArticlesContainingInstruments(
        region: NewsArticleRegion.us,
      ),
    ).thenAnswer(
      (_) => Future.value(news),
    );

    when(
      getInstrumentIds,
    ).thenAnswer(
      (_) => Future.value(['1', '2']),
    );
  }
}

class MockNewsSectionAnalytics extends Mock implements NewsSectionAnalytics {
  MockNewsSectionAnalytics() : super();
}

class MockInstrumentInteractor extends Mock implements InstrumentsInteractor {}

class MockFeatureToggleProvider extends Mock implements FeatureToggleProvider {}

const marketAccess = UserMarketAccess(
  usMarketStatus: UserFeatureStatus.active,
  uaeMarketStatus: UserFeatureStatus.active,
  cryptoMarketStatus: UserFeatureStatus.unknown,
);

class UserMarketAccessFake extends Fake implements UserMarketAccess {}

class MockInstrumentMarketAccessHandler extends Mock
    implements InstrumentMarketAccessHandler {}

class MockUserInteractor extends Mock implements BrokerUserInteractor {}

class MockInstrumentsInteractor extends Mock implements InstrumentsInteractor {
  MockInstrumentsInteractor() {
    when(
      () => getMarketData(any()),
    ).thenAnswer(
      (_) => Future.value(marketDataList),
    );
  }
}

class MockNewsListAnalytics extends Mock implements NewsListAnalytics {
  MockNewsListAnalytics() : super();
}

class MockDetailsListAnalytics extends Mock implements NewsDetailsAnalytics {
  MockDetailsListAnalytics() : super();
}

final marketDataList = [
  MarketData(
    instrumentId: '1',
    value: const NumberValue(
      sign: NumberValueSign.positive,
      type: NumberValueType.money,
      value: '100',
    ),
    relativePriceChange: const NumberValue(
      sign: NumberValueSign.positive,
      type: NumberValueType.percentage,
      value: '1',
    ),
  ),
  MarketData(
    instrumentId: '2',
    value: const NumberValue(
      sign: NumberValueSign.positive,
      type: NumberValueType.money,
      value: '200',
    ),
    relativePriceChange: const NumberValue(
      sign: NumberValueSign.negative,
      type: NumberValueType.percentage,
      value: '2',
    ),
  ),
];

final news = [
  NewsArticle(
    id: '1',
    executionDate: DateTime(2022),
    headline: 'News headline 1',
    body: '<ul><li><p>News body 1</p></li></ul>',
    instruments: [
      const NewsArticleInstrument(
        id: '1',
        name: 'Name 1',
        symbol: 'Symbol 1',
      ),
    ],
    categories: [
      const NewsCategory(
        name: 'name',
        type: NewsCategoryType.dividends,
      ),
    ],
  ),
  NewsArticle(
    id: '2',
    executionDate: DateTime(2022),
    headline: 'News headline 2',
    body: '<ul><li><p>News body 2</p></li></ul>',
    instruments: [
      const NewsArticleInstrument(
        id: '2',
        name: 'Name 2',
        symbol: 'Symbol 2',
      ),
    ],
    categories: [
      const NewsCategory(
        name: 'name',
        type: NewsCategoryType.marketSummary,
      ),
    ],
  ),
];

final newsWithRelativeChanges = [
  NewsArticle(
    id: '1',
    executionDate: DateTime(2022),
    headline: 'News headline 1',
    body: '<ul><li><p>News body 1</p></li></ul>',
    instruments: [
      const NewsArticleInstrument(
        id: '1',
        name: 'Name 1',
        symbol: 'Symbol 1',
        marketValue: NumberValue(
          sign: NumberValueSign.positive,
          type: NumberValueType.money,
          value: '100',
        ),
        relativePriceChange: NumberValue(
          sign: NumberValueSign.positive,
          type: NumberValueType.percentage,
          value: '1',
        ),
      ),
    ],
    categories: [
      const NewsCategory(
        name: 'name',
        type: NewsCategoryType.dividends,
      ),
    ],
  ),
  NewsArticle(
    id: '2',
    executionDate: DateTime(2022),
    headline: 'News headline 2',
    body: '<ul><li><p>News body 2</p></li></ul>',
    instruments: [
      const NewsArticleInstrument(
        id: '2',
        name: 'Name 2',
        symbol: 'Symbol 2',
        marketValue: NumberValue(
          sign: NumberValueSign.positive,
          type: NumberValueType.money,
          value: '200',
        ),
        relativePriceChange: NumberValue(
          sign: NumberValueSign.negative,
          type: NumberValueType.percentage,
          value: '2',
        ),
      ),
    ],
    categories: [
      const NewsCategory(
        name: 'name',
        type: NewsCategoryType.marketSummary,
      ),
    ],
  ),
];

const categories = [
  NewsCategory(
    name: '⭐ Top news',
    type: NewsCategoryType.topNews,
  ),
  NewsCategory(
    name: '📈 Market summary',
    type: NewsCategoryType.marketSummary,
  ),
  NewsCategory(
    name: '💵️ Dividends',
    type: NewsCategoryType.dividends,
  ),
  NewsCategory(
    name: '🚀 Mergers Acquisitions',
    type: NewsCategoryType.mergersAcquisitions,
  ),
  NewsCategory(
    name: '✨ Others',
    type: NewsCategoryType.others,
  ),
];

const mockNumberValue = NumberValue(
  type: NumberValueType.money,
  value: '10.0',
  currency: 'USD',
  sign: NumberValueSign.positive,
);

final mockMarketData = [
  MarketData(
    instrumentId: '123',
    value: mockNumberValue,
    relativePriceChange: mockNumberValue,
  ),
];

class MockDiscoveryPageRefreshMediator extends Mock
    implements DiscoveryPageRefreshMediator {
  MockDiscoveryPageRefreshMediator() {
    when(() => refreshStream).thenAnswer(
      (_) => const Stream.empty(),
    );
  }
}
