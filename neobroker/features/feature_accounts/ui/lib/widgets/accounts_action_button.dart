import 'package:flutter/cupertino.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

class AccountsActionButton extends StatelessWidget {
  final CompanyIconPointer icon;
  final ButtonType buttonType;
  final VoidCallback onPressed;
  final CompanyIconSize? iconSize;
  final String title;

  const AccountsActionButton({
    required this.icon,
    required this.buttonType,
    required this.onPressed,
    required this.title,
    this.iconSize,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Button(
      model: ButtonModel(
        title: title,
        type: buttonType,
        iconSize: iconSize ?? CompanyIconSize.large,
        contentAlignment: ButtonContentAlignment.spaceBetween,
        graphicAssetPointer: GraphicAssetPointer.icon(icon),
      ),
      onPressed: onPressed,
    );
  }
}
