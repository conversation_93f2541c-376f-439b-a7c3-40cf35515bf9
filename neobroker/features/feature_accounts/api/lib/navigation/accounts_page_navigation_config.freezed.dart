// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'accounts_page_navigation_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$AccountsPageNavigationConfig {
  List<AccountDetails> get accounts => throw _privateConstructorUsedError;
  int get currentAccountIndex => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AccountsPageNavigationConfigCopyWith<AccountsPageNavigationConfig>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountsPageNavigationConfigCopyWith<$Res> {
  factory $AccountsPageNavigationConfigCopyWith(
          AccountsPageNavigationConfig value,
          $Res Function(AccountsPageNavigationConfig) then) =
      _$AccountsPageNavigationConfigCopyWithImpl<$Res,
          AccountsPageNavigationConfig>;
  @useResult
  $Res call({List<AccountDetails> accounts, int currentAccountIndex});
}

/// @nodoc
class _$AccountsPageNavigationConfigCopyWithImpl<$Res,
        $Val extends AccountsPageNavigationConfig>
    implements $AccountsPageNavigationConfigCopyWith<$Res> {
  _$AccountsPageNavigationConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accounts = null,
    Object? currentAccountIndex = null,
  }) {
    return _then(_value.copyWith(
      accounts: null == accounts
          ? _value.accounts
          : accounts // ignore: cast_nullable_to_non_nullable
              as List<AccountDetails>,
      currentAccountIndex: null == currentAccountIndex
          ? _value.currentAccountIndex
          : currentAccountIndex // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AccountsPageNavigationConfigCopyWith<$Res>
    implements $AccountsPageNavigationConfigCopyWith<$Res> {
  factory _$$_AccountsPageNavigationConfigCopyWith(
          _$_AccountsPageNavigationConfig value,
          $Res Function(_$_AccountsPageNavigationConfig) then) =
      __$$_AccountsPageNavigationConfigCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<AccountDetails> accounts, int currentAccountIndex});
}

/// @nodoc
class __$$_AccountsPageNavigationConfigCopyWithImpl<$Res>
    extends _$AccountsPageNavigationConfigCopyWithImpl<$Res,
        _$_AccountsPageNavigationConfig>
    implements _$$_AccountsPageNavigationConfigCopyWith<$Res> {
  __$$_AccountsPageNavigationConfigCopyWithImpl(
      _$_AccountsPageNavigationConfig _value,
      $Res Function(_$_AccountsPageNavigationConfig) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accounts = null,
    Object? currentAccountIndex = null,
  }) {
    return _then(_$_AccountsPageNavigationConfig(
      accounts: null == accounts
          ? _value._accounts
          : accounts // ignore: cast_nullable_to_non_nullable
              as List<AccountDetails>,
      currentAccountIndex: null == currentAccountIndex
          ? _value.currentAccountIndex
          : currentAccountIndex // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$_AccountsPageNavigationConfig extends _AccountsPageNavigationConfig {
  const _$_AccountsPageNavigationConfig(
      {required final List<AccountDetails> accounts,
      required this.currentAccountIndex})
      : _accounts = accounts,
        super._();

  final List<AccountDetails> _accounts;
  @override
  List<AccountDetails> get accounts {
    if (_accounts is EqualUnmodifiableListView) return _accounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_accounts);
  }

  @override
  final int currentAccountIndex;

  @override
  String toString() {
    return 'AccountsPageNavigationConfig(accounts: $accounts, currentAccountIndex: $currentAccountIndex)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AccountsPageNavigationConfigCopyWith<_$_AccountsPageNavigationConfig>
      get copyWith => __$$_AccountsPageNavigationConfigCopyWithImpl<
          _$_AccountsPageNavigationConfig>(this, _$identity);
}

abstract class _AccountsPageNavigationConfig
    extends AccountsPageNavigationConfig {
  const factory _AccountsPageNavigationConfig(
          {required final List<AccountDetails> accounts,
          required final int currentAccountIndex}) =
      _$_AccountsPageNavigationConfig;
  const _AccountsPageNavigationConfig._() : super._();

  @override
  List<AccountDetails> get accounts;
  @override
  int get currentAccountIndex;
  @override
  @JsonKey(ignore: true)
  _$$_AccountsPageNavigationConfigCopyWith<_$_AccountsPageNavigationConfig>
      get copyWith => throw _privateConstructorUsedError;
}
