import 'package:accounts_api/index.dart';
import 'package:accounts_impl/index.dart';
import 'package:neobroker_models/models.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_user_api/index.dart';

class AccountsInteractorImpl implements AccountsInteractor {
  final AccountsRepository _repository;
  final BrokerUserRepository _userRepository;

  AccountsInteractorImpl(
    this._repository,
    this._userRepository,
  );

  @override
  Future<AccountBalances> getAccountBalances() async {
    final userId = await _userRepository.getUserId();

    return _repository.getAccountBalances(userId: userId);
  }

  @override
  Future<List<AccountData>> getAccounts({Currency? currency}) async {
    final userId = await _userRepository.getUserId();

    return _repository.getAccounts(userId: userId, currency: currency);
  }
}
