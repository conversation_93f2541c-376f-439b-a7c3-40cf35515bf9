// ignore_for_file: deprecated_member_use

import 'dart:ui';

import 'package:bloc_test/bloc_test.dart';
import 'package:instruments_api/index.dart';
import 'package:instruments_util/index.dart';
import 'package:mocktail/mocktail.dart';
import 'package:neobroker_models/models.dart';
import 'package:portfolio_api/index.dart';
import 'package:portfolio_ui/index.dart';
import 'package:portfolio_ui/screens/portfolios/handlers/portfolios_action_button_handler.dart';
import 'package:portfolio_ui/screens/portfolios/mediators/portfolios_tabs_mediator.dart';
import 'package:portfolio_ui/screens/portfolios/model/portfolio_action_button.dart';
import 'package:portfolio_ui/screens/portfolios/portfolios_tabs/main/cubit/base_main_portfolio_cubit.dart';
import 'package:portfolio_ui/screens/portfolios/portfolios_tabs/main/cubit/main_portfolio_cubit.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/ui_tests.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_rewards_api/navigation/flow/rewards_flow.dart';

import '../../../portfolio/mocks/portfolio_mock.dart';

void main() {
  late MockBottomSheetProvider bottomSheetProvider;
  late MockBrokerUserInteractor brokerUserInteractor;
  late MockLogger logger;
  late NavigationProvider navigationProvider;
  late PortfolioInteractor portfolioInteractor;
  late PortfolioLocalization localization;
  late MockPortfolioStocksDataProvider portfolioStocksDataProvider;
  late PortfolioActionButtonHandler actionButtonHandler;
  late RewardsFlow rewardsFlow;
  late MockPortfoliosAnalytics analytics;
  late PortfoliosTabsMediator portfoliosTabsMediator;

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    localization = await PortfolioLocalization.load(const Locale('en'));
  });

  setUp(() {
    registerFallbackValue(PortfolioActionButton.activities);
    registerFallbackValue(PortfolioType.primary);
    bottomSheetProvider = MockBottomSheetProvider();
    brokerUserInteractor = MockBrokerUserInteractor();
    logger = MockLogger();
    navigationProvider = MockNavigationProvider();
    portfolioInteractor = MockPortfolioInteractor();
    portfolioStocksDataProvider = MockPortfolioStocksDataProvider();
    actionButtonHandler = MockPortfolioActionButtonHandler();
    rewardsFlow = MockRewardsFlow();
    analytics = MockPortfoliosAnalytics();
    portfoliosTabsMediator = MockPortfoliosTabsMediator();

    registerFallbackValue(TopStockType.mixed);
  });

  MainPortfolioCubit getCubit() => MainPortfolioCubit(
        bottomSheetProvider: bottomSheetProvider,
        brokerUserInteractor: brokerUserInteractor,
        logger: logger,
        navigationProvider: navigationProvider,
        portfolioInteractor: portfolioInteractor,
        localization: localization,
        portfolioStocksDataProvider: portfolioStocksDataProvider,
        actionButtonHandler: actionButtonHandler,
        portfolios: [],
        rewardsFlow: rewardsFlow,
        analytics: analytics,
        portfoliosTabsMediator: portfoliosTabsMediator,
        portfolioType: PortfolioType.primary,
      );

  group('init', () {
    blocTest<BaseMainPortfolioCubit, BaseMainPortfolioState>(
      'emits idle state with fetched data when successful',
      setUp: () {
        when(
          () => portfolioInteractor.getMergedPortfolioPositionsData(
            portfolioType: any(named: 'portfolioType'),
          ),
        ).thenAnswer((_) async => mockMergedPositionData);
        when(() => portfolioStocksDataProvider.getStocks(any()))
            .thenAnswer((_) => <PortfolioStock>[]);
      },
      build: getCubit,
      expect: () => [
        const BaseMainPortfolioState.idle(
          selectedCurrencyIndex: 0,
          positions: [position],
          selectedTimeframe: PortfolioPositionTimeframeValue.allTime,
          topStocks: [],
          portfolioType: PortfolioType.primary,
        ),
      ],
    );

    blocTest<BaseMainPortfolioCubit, BaseMainPortfolioState>(
      'emits error state when an exception occurs',
      setUp: () {
        when(
          () => portfolioInteractor.getMergedPortfolioPositionsData(
            portfolioType: any(named: 'portfolioType'),
          ),
        ).justThrowAsync(Exception('Error fetching positions'));
        when(
          () => logger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).thenReturn(null);
      },
      build: getCubit,
      expect: () => [
        const BaseMainPortfolioState.error(selectedCurrencyIndex: 0),
      ],
      verify: (_) {
        verify(
          () => logger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).calledOnce;
      },
    );
  });

  group('retry', () {
    blocTest<BaseMainPortfolioCubit, BaseMainPortfolioState>(
      'calls init method with correct parameters',
      setUp: () {
        when(
          () => portfolioInteractor.getMergedPortfolioPositionsData(
            portfolioType: any(named: 'portfolioType'),
          ),
        ).thenAnswer((_) async => mockMergedPositionData);
        when(() => portfolioStocksDataProvider.getStocks(any()))
            .thenAnswer((_) => <PortfolioStock>[]);
      },
      build: getCubit,
      act: (cubit) => cubit.retry(),
      expect: () => [
        const BaseMainPortfolioState.fetching(
          selectedCurrencyIndex: 0,
        ),
        const BaseMainPortfolioState.idle(
          selectedCurrencyIndex: 0,
          positions: [position],
          selectedTimeframe: PortfolioPositionTimeframeValue.allTime,
          topStocks: [],
          portfolioType: PortfolioType.primary,
        ),
      ],
    );
  });

  group('onPositionPressed', () {
    blocTest<BaseMainPortfolioCubit, BaseMainPortfolioState>(
      '''when position presssed without instrument then navogates to instrument search screen''',
      setUp: () {
        when(() => navigationProvider.push(any())).justAnswerEmptyAsync();
      },
      build: getCubit,
      act: (cubit) => cubit.onPositionPressed(position),
      verify: (_) {
        verify(
          () => navigationProvider.navigateTo(
            const InstrumentsFeatureNavigationConfig(
              initialScreen:
                  InstrumentsFeatureNavigationConfig.instrumentsSearchScreen,
            ),
          ),
        ).called(1);
      },
    );

    blocTest<BaseMainPortfolioCubit, BaseMainPortfolioState>(
      '''when position presssed with instrument then navogates to instrument search screen''',
      setUp: () {
        when(() => navigationProvider.push(any())).justAnswerEmptyAsync();
      },
      build: getCubit,
      act: (cubit) => cubit.onPositionPressed(positionWithInstrument),
      verify: (_) {
        verify(
          () => navigationProvider.push(
            InstrumentDetailsNavigationConfig(
              instrumentId: instrument.id,
              instrumentWithMarketData:
                  InstrumentWithMarketData(instrument: instrument),
            ),
          ),
        ).called(1);
      },
    );
  });

  group('onFilterPressed', () {
    blocTest<BaseMainPortfolioCubit, BaseMainPortfolioState>(
      'when a filter is selected then emits updates selectedTimeframe',
      setUp: () {
        when(
          () => bottomSheetProvider.showBottomSheet<SelectBottomSheetResponse>(
            SelectBottomSheetConfiguration(
              model: BottomSheetSelectModel(
                selectableItemModels: [
                  BottomSheetSelectItemModel(
                    id: PortfolioPositionTimeframeValue.allTime.id,
                    title: localization.allTimePerformance,
                  ),
                  BottomSheetSelectItemModel(
                    id: PortfolioPositionTimeframeValue.oneDay.id,
                    title: localization.dailyPerformance,
                  ),
                ],
                selectedId: PortfolioPositionTimeframeValue.allTime.id,
              ),
              useRootNavigator: true,
              headerText: localization.performanceType,
            ),
            'FilterBottomSheet',
          ),
        ).thenAnswer(
          (_) async => SelectBottomSheetResponse(
            filter: BottomSheetSelectItemModel(
              id: PortfolioPositionTimeframeValue.oneDay.id,
              title: localization.allTimePerformance,
            ),
          ),
        );
      },
      build: getCubit,
      seed: () => const BaseMainPortfolioState.idle(
        selectedCurrencyIndex: 0,
        positions: [position],
        selectedTimeframe: PortfolioPositionTimeframeValue.allTime,
        topStocks: [],
        portfolioType: PortfolioType.primary,
      ),
      act: (cubit) => cubit.onFilterPressed(),
      expect: () => [
        const BaseMainPortfolioState.idle(
          selectedCurrencyIndex: 0,
          positions: [position],
          selectedTimeframe: PortfolioPositionTimeframeValue.oneDay,
          topStocks: [],
          portfolioType: PortfolioType.primary,
        ),
      ],
    );
  });

  group('onStockCardPressed', () {
    blocTest<BaseMainPortfolioCubit, BaseMainPortfolioState>(
      'when onStockCardPressed then navigates to instruments search screen',
      setUp: () {
        when(() => navigationProvider.navigateTo(any())).justAnswerEmptyAsync();
      },
      build: getCubit,
      act: (cubit) => cubit.onStockCardPressed(),
      verify: (_) {
        verify(
          () => navigationProvider.navigateTo(
            const InstrumentsFeatureNavigationConfig(
              initialScreen:
                  InstrumentsFeatureNavigationConfig.instrumentsSearchScreen,
            ),
          ),
        ).calledOnce;
      },
    );
  });

  group('onStockItemPressed', () {
    blocTest<BaseMainPortfolioCubit, BaseMainPortfolioState>(
      'when onStockItemPressed then navigates to instrument details screen',
      setUp: () {
        when(() => navigationProvider.navigateTo(any())).justAnswerEmptyAsync();
      },
      build: getCubit,
      act: (cubit) => cubit.onStockItemPressed('id'),
      verify: (_) {
        verify(
          () => navigationProvider.navigateTo(
            const InstrumentsFeatureNavigationConfig(
              initialScreen:
                  InstrumentsFeatureNavigationConfig.instrumentDetailsScreen,
              instrumentId: 'id',
            ),
          ),
        ).calledOnce;
      },
    );
  });

  group('portfolio action button test', () {
    blocTest<BaseMainPortfolioCubit, BaseMainPortfolioState>(
      'when action button is pressed verify handler is called',
      build: getCubit,
      seed: () => const BaseMainPortfolioState.idle(
        selectedCurrencyIndex: 0,
        positions: [position],
        selectedTimeframe: PortfolioPositionTimeframeValue.allTime,
        topStocks: [],
        portfolioType: PortfolioType.primary,
      ),
      act: (cubit) => cubit.onPortfolioActionButtonPressed(
        PortfolioActionButton.activities,
      ),
      verify: (_) => verify(
        () => actionButtonHandler.onPortfolioActionPressed(
          portfolioType: PortfolioType.primary,
          action: PortfolioActionButton.activities,
          portfolios: [],
        ),
      ).called(1),
    );
  });

  group('on rewards banner pressed', () {
    blocTest<BaseMainPortfolioCubit, BaseMainPortfolioState>(
      'when rewards banner pressed then calls rewards flow',
      setUp: () {
        when(() => rewardsFlow.run()).justCompleteAsync();
      },
      build: getCubit,
      act: (cubit) => cubit.onRewardsPressed(),
      verify: (_) {
        verify(
          () => rewardsFlow.run(),
        ).calledOnce;
      },
    );
  });
}
