import 'dart:ui';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:feature_neobroker_onboarding_api/feature_toggles/broker_onboarding_feature_toggles.dart';
import 'package:feature_neobroker_onboarding_api/index.dart';
import 'package:instruments_api/index.dart';
import 'package:mocktail/mocktail.dart';
import 'package:neobroker_models/models.dart';
import 'package:portfolio_ui/index.dart';
import 'package:portfolio_ui/screens/portfolio/analytics/portfolio_analytics.dart';
import 'package:portfolio_ui/screens/portfolio/handlers/portfolio_to_do_handler.dart';
import 'package:portfolio_ui/screens/portfolio/models/to_do_item.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/ui_tests.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_feature_securities_lending_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_kyc_api/feature_kyc_api.dart';
import 'package:wio_feature_recurring_orders_api/index.dart';
import 'package:wio_neobroker_bottom_tab_mediator/index.dart';

import '../mocks/portfolio_mock.dart';

void main() {
  late PortfolioToDoHandler toDoHandler;
  late MockBrokerUserInteractor brokerUserInteractor;
  late NavigationProvider navigationProvider;
  late MockFeatureToggleProvider featureToggleProvider;
  late MockRecurringOrdersInteractor recurringOrdersInteractor;
  late MockRecurringOrdersTutorialHandler recurringOrdersTutorialHandler;
  late MockSecuritiesLendingInteractor securitiesLendingInteractor;
  late MockWealthManagementInteractor wealthManagementInteractor;
  late MockWealthManagementStarterHandler wealthManagementStarterHandler;
  late MockBrokerToDoInteractor brokerToDoInteractor;
  late MockLogger logger;
  late KycInteractor kycInteractor;
  late ToastMessageProvider toastMessageProvider;
  late PortfolioLocalization localization;
  late PortfolioAnalytics analytics;
  late MockBottomTabMediator mediator;

  setUpAll(() async {
    featureToggleProvider = MockFeatureToggleProvider();
    brokerUserInteractor = MockBrokerUserInteractor();
    brokerToDoInteractor = MockBrokerToDoInteractor();
    recurringOrdersInteractor = MockRecurringOrdersInteractor();
    recurringOrdersTutorialHandler = MockRecurringOrdersTutorialHandler();
    securitiesLendingInteractor = MockSecuritiesLendingInteractor();
    wealthManagementInteractor = MockWealthManagementInteractor();
    wealthManagementStarterHandler = MockWealthManagementStarterHandler();
    logger = MockLogger();
    toastMessageProvider = MockToastMessageProvider();
    analytics = MockPortfolioAnalytics();
    mediator = MockBottomTabMediator();
    await OtaLocalizationImpl().initMock();
    localization = await PortfolioLocalization.load(const Locale('en'));

    registerFallbackValue(
      const FeatureToggleKey<Object?>(key: '', defaultValue: ''),
    );

    registerFallbackValue(FeatureToggleKeyFake());

    when(() => featureToggleProvider.get<bool>(any())).thenReturn(true);

    when(
      () => mediator.passAction(
        const BottomTabNavigationAction.etfCollections(),
      ),
    ).justComplete();
  });

  setUp(() {
    kycInteractor = MockKycInteractor();
    navigationProvider = MockNavigationProvider();

    toDoHandler = PortfolioToDoHandler(
      brokerUserInteractor: brokerUserInteractor,
      brokerToDoInteractor: brokerToDoInteractor,
      recurringOrdersInteractor: recurringOrdersInteractor,
      recurringTutorialHandler: recurringOrdersTutorialHandler,
      securitiesLendingInteractor: securitiesLendingInteractor,
      wealthManagementInteractor: wealthManagementInteractor,
      wealthManagementStarterHandler: wealthManagementStarterHandler,
      navigationProvider: navigationProvider,
      featureToggleProvider: featureToggleProvider,
      logger: logger,
      kycInteractor: kycInteractor,
      toastMessageProvider: toastMessageProvider,
      localization: localization,
      analytics: analytics,
      mediator: mediator,
    );
  });

  group('''get to do item method tests''', () {
    test('''recurring orders not hidden''', () async {
      //act
      when(
        () => brokerToDoInteractor
            .isToDoItemHidden(BrokerToDoItemType.recurringOrders),
      ).thenAnswer((_) => Future.value(false));

      when(
        () => recurringOrdersInteractor.getRecurringOrders(),
      ).thenAnswer(
        (_) => Future.value(
          const RecurringOrderTemplates(
            recurringOrderTemplates: [],
            termsAndConditions: [],
            validations: [],
          ),
        ),
      );

      final toDoItems = await toDoHandler.getToDoItems();

      //verify

      final containsItem = toDoItems.contains(const ToDoItem.recurringOrders());

      expect(containsItem, true);
    });
    test('''etf not hidden''', () async {
      //act
      when(
        () => brokerToDoInteractor
            .isToDoItemHidden(BrokerToDoItemType.etfCollections),
      ).thenAnswer((_) => Future.value(false));

      when(
        () => brokerUserInteractor.getUserMarketAccess(),
      ).thenAnswer(
        (_) => Future.value(inactiveCryptoMarketAccess),
      );

      final toDoItems = await toDoHandler.getToDoItems();

      //verify

      final containsItem = toDoItems.contains(const ToDoItem.etfCollections());

      expect(containsItem, true);
    });
  });

  test('''securities lending not hidden''', () async {
    //act
    when(
      () => brokerToDoInteractor
          .isToDoItemHidden(BrokerToDoItemType.securitiesLending),
    ).thenAnswer((_) => Future.value(false));

    when(
      () => securitiesLendingInteractor.isSubscribedToSecuritiesLending(
        feature: UserFeatureType.usEquities,
      ),
    ).thenAnswer(
      (_) => Future.value(false),
    );

    final toDoItems = await toDoHandler.getToDoItems();

    //verify

    final containsItem = toDoItems.contains(const ToDoItem.securitiesLending());

    expect(containsItem, true);
  });

  test('''wealth management not hidden''', () async {
    //act
    when(
      () => brokerToDoInteractor
          .isToDoItemHidden(BrokerToDoItemType.wealthManagement),
    ).thenAnswer((_) => Future.value(false));

    when(
      () => wealthManagementInteractor.getManagedPortfolios(),
    ).thenAnswer(
      (_) => Future.value([]),
    );

    final toDoItems = await toDoHandler.getToDoItems();

    //verify

    final containsItem = toDoItems.contains(const ToDoItem.wealthManagement());

    expect(containsItem, false);
  });

  test('''crypto is hidden if status is pending''', () async {
    //act
    when(
      () => brokerToDoInteractor.isToDoItemHidden(BrokerToDoItemType.crypto),
    ).thenAnswer((_) => Future.value(false));

    when(
      () => brokerUserInteractor.getUserMarketAccess(),
    ).thenAnswer(
      (_) => Future.value(
        const UserMarketAccess(
          cryptoMarketStatus: UserFeatureStatus.pending,
          usMarketStatus: UserFeatureStatus.active,
          uaeMarketStatus: UserFeatureStatus.active,
        ),
      ),
    );

    final toDoItems = await toDoHandler.getToDoItems();
    //verify

    final doesNotContainItem = !toDoItems.contains(const ToDoItem.crypto());

    expect(doesNotContainItem, true);
  });

  test('''crypto is hidden if status is pending''', () async {
    //act
    when(
      () => brokerToDoInteractor.isToDoItemHidden(BrokerToDoItemType.crypto),
    ).thenAnswer((_) => Future.value(false));

    when(
      () => brokerUserInteractor.getUserMarketAccess(),
    ).thenAnswer(
      (_) => Future.value(
        const UserMarketAccess(
          cryptoMarketStatus: UserFeatureStatus.active,
          usMarketStatus: UserFeatureStatus.active,
          uaeMarketStatus: UserFeatureStatus.pending,
        ),
      ),
    );

    final toDoItems = await toDoHandler.getToDoItems();

    //verify

    final doesNotContainItem = !toDoItems.contains(
      const ToDoItem.uaeSecurities(),
    );

    expect(doesNotContainItem, true);
  });

  test('''securitiesTransfer not hidden''', () async {
    //act
    when(
      () => brokerToDoInteractor
          .isToDoItemHidden(BrokerToDoItemType.securitiesTransfer),
    ).thenAnswer((_) => Future.value(false));

    when(
      () => brokerUserInteractor.getUserMarketAccess(),
    ).thenAnswer(
      (_) => Future.value(inactiveCryptoMarketAccess),
    );
    final toDoItems = await toDoHandler.getToDoItems();

    //verify

    final containsItem = toDoItems.contains(
      const ToDoItem.securitiesTransfer(),
    );

    expect(containsItem, true);
  });

  group('''to do items method tests''', () {
    test('uae securities item pressed test', () async {
      //act
      await toDoHandler.onUaeSecuritiesItemPressed();

      //verify
      verify(
        () => navigationProvider.push(
          const BrokerOnboardingNavigationFlowConfig.onboardingFromStatus(
            onboardingType: OnboardingType.brokerUae,
            marketStatusPageType:
                OnboardingMarketStatusPageType.usActiveUaeInactive,
          ),
        ),
      ).calledOnce;
    });

    test('''uae securities item pressed test with v3 onboarding ff disabled''',
        () async {
      //act
      when(
        () => featureToggleProvider
            .get(BrokerOnboardingFeatureToggles.isV3BrokerOnboardingEnabled),
      ).thenReturn(false);
      await toDoHandler.onUaeSecuritiesItemPressed();

      //verify
      verify(
        () => navigationProvider.push(
          const OnboardingMarketStatusNavigationConfig(
            type: OnboardingMarketStatusPageType.usActiveUaeInactive,
          ),
        ),
      ).calledOnce;
    });

    test('crypto item pressed test', () async {
      //act
      await toDoHandler.onCryptoPressed();

      //verify
      verify(
        () => navigationProvider.push(
          const BrokerOnboardingNavigationFlowConfig.onboardingFromStatus(
            onboardingType: OnboardingType.crypto,
            marketStatusPageType: OnboardingMarketStatusPageType.cryptoInactive,
          ),
        ),
      ).calledOnce;
    });

    test('complex instruments item pressed test', () async {
      //act
      await toDoHandler.onComplexInstrumentsPressed();

      //verify
      verify(
        () => navigationProvider.push(
          const BrokerOnboardingNavigationFlowConfig.onboarding(
            onboardingType: OnboardingType.complexInstruments,
          ),
        ),
      ).calledOnce;
    });

    test(
      'recurring orders item pressed test with recurring orders v2 disabled',
      () async {
        // arrange
        when(
          () => featureToggleProvider
              .get(RecurringOrdersFeatureToggles.isRecurringOrdersV2Enabled),
        ).thenReturn(false);
        when(
          () => recurringOrdersTutorialHandler.handleTutorial(),
        ).justCompleteAsync();

        // act
        await toDoHandler.onRecurringOrdersItemPressed();

        // verify
        verify(
          () => recurringOrdersTutorialHandler.handleTutorial(),
        ).calledOnce;
        verify(
          () => navigationProvider.navigateTo(
            const InstrumentsFeatureNavigationConfig(
              initialScreen:
                  InstrumentsFeatureNavigationConfig.instrumentsSearchScreen,
            ),
          ),
        ).calledOnce;

        verify(
          () => brokerToDoInteractor
              .hideToDoItem(BrokerToDoItemType.recurringOrders),
        ).calledOnce;
      },
    );

    test(
      'recurring orders item pressed test with recurring orders v2 enabled',
      () async {
        // arrange
        when(
          () => navigationProvider.push<RecurringOrderTemplate>(
            const InstrumentsSearchNavigationConfig(
              selectedMarketFilterIndex: -1,
              instrumentPressedDelegateType:
                  InstrumentPressedDelegateType.recurringOrders,
            ),
          ),
        ).thenAnswer((invocation) async => null);
        when(
          () => featureToggleProvider
              .get(RecurringOrdersFeatureToggles.isRecurringOrdersV2Enabled),
        ).thenReturn(true);
        when(
          () => recurringOrdersTutorialHandler.handleTutorial(),
        ).justCompleteAsync();

        // act
        await toDoHandler.onRecurringOrdersItemPressed();

        // verify
        verify(
          () => recurringOrdersTutorialHandler.handleTutorial(),
        ).calledOnce;
        verify(
          () => navigationProvider.push<RecurringOrderTemplate>(
            const InstrumentsSearchNavigationConfig(
              selectedMarketFilterIndex: -1,
              instrumentPressedDelegateType:
                  InstrumentPressedDelegateType.recurringOrders,
            ),
          ),
        ).calledOnce;
      },
    );

    test('uae securities item closed test', () async {
      //act
      when(
        () =>
            brokerToDoInteractor.hideToDoItem(BrokerToDoItemType.uaeSecurities),
      ).justCompleteAsync();
      await toDoHandler.onUaeSecuritiesItemClosed();

      //verify
      verify(
        () =>
            brokerToDoInteractor.hideToDoItem(BrokerToDoItemType.uaeSecurities),
      ).calledOnce;
    });

    test('complex instruments item closed test', () async {
      //act
      when(
        () => brokerToDoInteractor
            .hideToDoItem(BrokerToDoItemType.complexInstruments),
      ).justCompleteAsync();
      await toDoHandler.onComplexInstrumentsClosed();

      //verify
      verify(
        () => brokerToDoInteractor
            .hideToDoItem(BrokerToDoItemType.complexInstruments),
      ).calledOnce;
    });

    test('recurring order item closed test', () async {
      //act
      when(
        () => brokerToDoInteractor
            .hideToDoItem(BrokerToDoItemType.recurringOrders),
      ).justCompleteAsync();
      await toDoHandler.onRecurringOrdersItemClosed();

      //verify
      verify(
        () => brokerToDoInteractor
            .hideToDoItem(BrokerToDoItemType.recurringOrders),
      ).calledOnce;
    });

    test('securities lending item pressed test', () async {
      //act
      await toDoHandler.onSecuritiesLendingItemPressed();

      //verify
      verify(
        () => navigationProvider.navigateTo(
          const SecuritiesLendingFeatureNavigationConfig(
            destination: SecuritiesLendingWalkthroughNavigationConfig(),
          ),
        ),
      ).calledOnce;
    });

    test('securities transfer item pressed test', () async {
      //act
      await toDoHandler.onSecuritiesTransferItemPressed();

      //verify
      verify(
        () => analytics
            .clickToDo(PortfolioAnalyticsTarget.securities_transfer_to_do),
      ).calledOnce;
    });
    test('crypto item closed test', () async {
      //act
      when(
        () => brokerToDoInteractor.hideToDoItem(BrokerToDoItemType.crypto),
      ).justCompleteAsync();
      await toDoHandler.onCryptoClosed();

      //verify

      verify(
        () => brokerToDoInteractor.hideToDoItem(BrokerToDoItemType.crypto),
      ).calledOnce;
    });
    test('securities lending item closed test', () async {
      //act
      when(
        () => brokerToDoInteractor
            .hideToDoItem(BrokerToDoItemType.securitiesLending),
      ).justCompleteAsync();
      await toDoHandler.onSecuritiesLendingItemClosed();

      //verify
      verify(
        () => brokerToDoInteractor
            .hideToDoItem(BrokerToDoItemType.securitiesLending),
      ).calledOnce;
    });

    test('securities transfer item closed test', () async {
      //act
      when(
        () => brokerToDoInteractor
            .hideToDoItem(BrokerToDoItemType.securitiesTransfer),
      ).justCompleteAsync();
      await toDoHandler.onSecuritiesTransferItemClosed();

      //verify
      verify(
        () => brokerToDoInteractor
            .hideToDoItem(BrokerToDoItemType.securitiesTransfer),
      ).calledOnce;
    });

    test('requiredInformationPressed with non-empty RFI list and all submitted',
        () async {
      final rfiList = [
        const RfiModel(
          id: 'id1',
          title: 'title1',
          submitted: true,
          requests: [
            InformationRequest(
              questionId: 'questionId1',
              question: 'question1',
            ),
          ],
        ),
        const RfiModel(
          id: 'id1',
          title: 'title2',
          submitted: true,
          requests: [
            InformationRequest(
              questionId: 'questionId2',
              question: 'question2',
            ),
          ],
        ),
      ];
      when(() => kycInteractor.getRfiList()).thenReturn(rfiList);
      when(() => navigationProvider.navigateTo(any()))
          .thenAnswer((_) async => rfiList);

      await toDoHandler.requiredInformationPressed();

      verify(
        () => toastMessageProvider.showToastMessage(
          NotificationToastMessageConfiguration.success(
            localization.portfolioRFISuccessSubmitText,
          ),
        ),
      ).called(1);
      verifyNever(() => kycInteractor.updateRfiList(any()));
    });

    test('requiredInformationPressed with empty RFI list', () async {
      when(() => kycInteractor.getRfiList()).thenReturn([]);

      await toDoHandler.requiredInformationPressed();

      verifyNever(() => navigationProvider.navigateTo(any()));
      verifyNever(() => kycInteractor.updateRfiList(any()));
    });

    test('requiredInformationPressed with null RFI list', () async {
      when(() => kycInteractor.getRfiList()).thenReturn(null);

      await toDoHandler.requiredInformationPressed();

      verifyNever(() => navigationProvider.navigateTo(any()));
      verifyNever(() => kycInteractor.updateRfiList(any()));
    });

    test('''
requiredInformationPressed with non-empty RFI list and not all submitted''',
        () async {
      final rfiList = [
        const RfiModel(
          id: 'id1',
          title: 'title1',
          requests: [
            InformationRequest(
              questionId: 'questionId1',
              question: 'question1',
            ),
          ],
        ),
        const RfiModel(
          id: 'id2',
          title: 'title2',
          requests: [
            InformationRequest(
              questionId: 'questionId2',
              question: 'question2',
            ),
          ],
        ),
      ];
      final updatedRfiList = [
        const RfiModel(
          id: 'id1',
          title: 'title1',
          submitted: true,
          requests: [
            InformationRequest(
              questionId: 'questionId1',
              question: 'question1',
            ),
          ],
        ),
        const RfiModel(
          id: 'id2',
          title: 'title2',
          requests: [
            InformationRequest(
              questionId: 'questionId2',
              question: 'question2',
            ),
          ],
        ),
      ];
      when(() => kycInteractor.getRfiList()).thenReturn(rfiList);
      when(() => navigationProvider.navigateTo(any()))
          .thenAnswer((_) async => updatedRfiList);

      await toDoHandler.requiredInformationPressed();

      verify(() => kycInteractor.updateRfiList(updatedRfiList)).called(1);
    });
    test('etf collection item closed test', () async {
      //act
      when(
        () => brokerToDoInteractor
            .hideToDoItem(BrokerToDoItemType.etfCollections),
      ).justCompleteAsync();
      await toDoHandler.onEtfCollectionsItemClosed();

      //verify
      verify(
        () => brokerToDoInteractor
            .hideToDoItem(BrokerToDoItemType.etfCollections),
      ).calledOnce;
    });

    test('etf colletions pressed test', () async {
      //act
      when(
        () => brokerToDoInteractor
            .hideToDoItem(BrokerToDoItemType.etfCollections),
      ).justCompleteAsync();
      await toDoHandler.onEtfCollectionsItemPressed();

      //verify
      verify(
        () => mediator
            .passAction(const BottomTabNavigationAction.etfCollections()),
      ).calledOnce;
    });

    test('wealth management item pressed test', () async {
      // arrange
      when(
        () => wealthManagementStarterHandler.startWealthManagement(),
      ).justCompleteAsync();

      //act
      await toDoHandler.onWealthManagementItemPressed();

      //verify
      verify(
        () => wealthManagementStarterHandler.startWealthManagement(),
      ).calledOnce;
    });

    test('wealth management item closed test', () async {
      //act
      when(
        () => brokerToDoInteractor
            .hideToDoItem(BrokerToDoItemType.wealthManagement),
      ).justCompleteAsync();
      await toDoHandler.onWealthManagementItemClosed();

      //verify
      verify(
        () => brokerToDoInteractor
            .hideToDoItem(BrokerToDoItemType.wealthManagement),
      ).calledOnce;
    });
  });
}
