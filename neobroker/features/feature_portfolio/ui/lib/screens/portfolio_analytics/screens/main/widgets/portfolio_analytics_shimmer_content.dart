part of '../portfolio_analytics_screen.dart';

class _PortfolioAnalyticsShimmer extends StatelessWidget {
  const _PortfolioAnalyticsShimmer();

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: [
        const SliverHeader(
          title: '',
          largeTitle: Padding(
            padding: EdgeInsetsDirectional.only(
              top: 24.0,
            ),
            child: CompanyShimmer(
              model: CompanyShimmerModel(),
              child: Label(
                model: LabelModel(
                  text: 'Analytics',
                  textStyle: CompanyTextStylePointer.h2,
                  color: CompanyColorPointer.primary3,
                  backgroundColor: CompanyColorPointer.primary2,
                ),
              ),
            ),
          ),
        ),
        SliverToBoxAdapter(
          child: CompanyShimmer(
            model: const CompanyShimmerModel(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Padding(
                  padding: EdgeInsetsDirectional.symmetric(
                    horizontal: 24.0,
                  ),
                  child: FiltersSkeleton(),
                ),
                Space.fromSpacingVertical(Spacing.s6),
                const Padding(
                  padding: EdgeInsetsDirectional.symmetric(horizontal: 24.0),
                  child: Label(
                    model: LabelModel(
                      text: 'Total portfolio value',
                      color: CompanyColorPointer.secondary3,
                      textStyle: CompanyTextStylePointer.h2,
                      backgroundColor: CompanyColorPointer.primary2,
                    ),
                  ),
                ),
                Space.fromSpacingVertical(Spacing.s1),
                const Padding(
                  padding: EdgeInsetsDirectional.symmetric(horizontal: 24.0),
                  child: Label(
                    model: LabelModel(
                      text: '0.00 USD AED',
                      color: CompanyColorPointer.secondary3,
                      textStyle: CompanyTextStylePointer.b3,
                      backgroundColor: CompanyColorPointer.primary2,
                    ),
                  ),
                ),
                const Space.vertical(40.0),
                SplineChartSkeleton(),
                Space.fromSpacingVertical(Spacing.s2),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: TimeFramePickerSkeleton(),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
