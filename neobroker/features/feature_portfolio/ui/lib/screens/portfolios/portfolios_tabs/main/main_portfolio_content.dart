import 'package:flutter/material.dart';
import 'package:instruments_util/index.dart';
import 'package:neobroker_models/models.dart';
import 'package:neobroker_ui/ui.dart';
import 'package:portfolio_ui/assets.dart';
import 'package:portfolio_ui/locale/portfolio_localization.g.dart';
import 'package:portfolio_ui/screens/portfolios/extensions/portfolio_type_extension.dart';
import 'package:portfolio_ui/screens/portfolios/page/widgets/portfolios_error_content.dart';
import 'package:portfolio_ui/screens/portfolios/portfolios_tabs/extensions/portfolio_tabs_extension.dart';
import 'package:portfolio_ui/screens/portfolios/portfolios_tabs/main/cubit/base_main_portfolio_cubit.dart';
import 'package:portfolio_ui/screens/portfolios/portfolios_tabs/main/widgets/license_numbers_sliver.dart';
import 'package:portfolio_ui/screens/portfolios/portfolios_tabs/main/widgets/portfolio_action_buttons.dart';
import 'package:portfolio_ui/screens/portfolios/portfolios_tabs/main/widgets/portfolio_position_item.dart';
import 'package:portfolio_ui/screens/portfolios/portfolios_tabs/main/widgets/positions_filter.dart';
import 'package:sliver_tools/sliver_tools.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

class MainPortfolioContent extends StatefulWidget {
  final PortfolioType portfolioType;
  const MainPortfolioContent({required this.portfolioType, super.key});

  @override
  State<MainPortfolioContent> createState() => _MainPortfolioContentState();
}

class _MainPortfolioContentState extends State<MainPortfolioContent> {
  @override
  Widget build(BuildContext context) {
    final cubit = context.read<BaseMainPortfolioCubit>();
    final state = context.watch<BaseMainPortfolioCubit>().state;
    final portfolioType = widget.portfolioType;
    return SliverAnimatedSwitcher(
      duration: const Duration(milliseconds: 400),
      child: state.map(
        fetching: (_) => const _ShimmerContent(),
        idle: (idle) => idle.positions.isNotEmpty
            ? _MainPortfolioIdleContent(
                portfolioType: portfolioType,
              )
            : _MainPortfolioEmptyContent(
                portfolioType: portfolioType,
              ),
        error: (error) => SliverFillRemaining(
          child: PortfoliosErrorContent(
            onRetry: cubit.retry,
          ),
        ),
      ),
    );
  }
}

class _MainPortfolioEmptyContent extends StatelessWidget {
  final PortfolioType portfolioType;

  const _MainPortfolioEmptyContent({required this.portfolioType});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<BaseMainPortfolioCubit>().state;
    final localization = PortfolioLocalization.of(context);

    return SliverToBoxAdapter(
      child: state.maybeMap(
        idle: (idle) => SingleChildScrollView(
          child: Column(
            children: [
              _PortfolioInactiveStateBanner(
                type: portfolioType,
              ),
              Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(24, 32, 24, 8),
                child: Label(
                  model: LabelModel(
                    text: localization.portfolioLegalText(
                      scaLicenseNumber,
                      uaeLicensenumber,
                    ),
                    textAlign: LabelTextAlign.center,
                    textStyle: CompanyTextStylePointer.b4,
                    color: CompanyColorPointer.secondary4,
                    maxLines: 5,
                  ),
                ),
              ),
            ],
          ),
        ),
        orElse: () => const SizedBox(),
      ),
    );
  }
}

class _PortfolioInactiveStateBanner extends StatelessWidget {
  final PortfolioType type;
  const _PortfolioInactiveStateBanner({
    required this.type,
  });

  @override
  Widget build(BuildContext context) {
    final localization = PortfolioLocalization.of(context);
    final cubit = context.read<BaseMainPortfolioCubit>();

    switch (type) {
      case PortfolioType.primary:
        return Padding(
          padding: const EdgeInsetsDirectional.only(
            top: 32.0,
          ),
          child: PortfolioInactiveStateBanner(
            header: localization.portfolioPrimaryInactiveTitle,
            image: PortfolioAssets.mainInactiveBg,
            buttonText: localization.portfolioStockCardInvestCta,
            onButtonPressed: cubit.onStockCardPressed,
          ),
        );
      case PortfolioType.secondary:
      case PortfolioType.rewards:
        return Padding(
          padding: const EdgeInsetsDirectional.only(
            top: 32.0,
          ),
          child: PortfolioInactiveStateBanner(
            header: localization.portfolioRewardsInactiveTitle,
            image: PortfolioAssets.rewardsInactiveBg,
            buttonText: localization.portfolioStockCardInvestCta,
            onButtonPressed: () => cubit.onRewardsPressed(),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }
}

class _MainPortfolioIdleContent extends StatefulWidget {
  final PortfolioType portfolioType;

  const _MainPortfolioIdleContent({required this.portfolioType});

  @override
  State<_MainPortfolioIdleContent> createState() =>
      _MainPortfolioIdleContentState();
}

class _MainPortfolioIdleContentState extends State<_MainPortfolioIdleContent> {
  @override
  Widget build(BuildContext context) {
    final cubit = context.read<BaseMainPortfolioCubit>();
    final state = context.watch<BaseMainPortfolioCubit>().state;

    final localization = PortfolioLocalization.of(context);
    final actionButtons = widget.portfolioType.actionButtons;

    return state.maybeMap(
      idle: (idle) {
        final positions = idle.positions;
        final timeFrame = idle.selectedTimeframe;

        final timeFrameText = getTimeframeText(timeFrame, localization);
        return MultiSliver(
          children: [
            SliverToBoxAdapter(
              child: PortfolioActionButtons(
                actionButtons: actionButtons,
                onButtonPressed: cubit.onPortfolioActionButtonPressed,
              ),
            ),
            SliverPadding(
              padding: const EdgeInsetsDirectional.fromSTEB(24, 24, 24, 16),
              sliver: SliverToBoxAdapter(
                child: PositionsFilter(
                  title: localization.portfolioTitle,
                  onFilter: cubit.onFilterPressed,
                  timeframeText: timeFrameText,
                ),
              ),
            ),
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              sliver: SliverGroupedListView(
                physics: const NeverScrollableScrollPhysics(),
                padding: const EdgeInsets.symmetric(
                  horizontal: 24.0,
                ),
                elements: positions,
                indexedItemBuilder: (_, position, index, isFirst, isLast) {
                  final topRadius =
                      isFirst ? const Radius.circular(14) : Radius.zero;
                  final bottomRadius =
                      isLast ? const Radius.circular(14) : Radius.zero;
                  return PortfolioPositionItem(
                    position: position,
                    timeFrame: idle.selectedTimeframe,
                    selectedCurrencyIndex: idle.selectedCurrencyIndex,
                    onTap: () => cubit.onPositionPressed(position),
                    borderRadius: BorderRadius.only(
                      topLeft: topRadius,
                      topRight: topRadius,
                      bottomLeft: bottomRadius,
                      bottomRight: bottomRadius,
                    ),
                  );
                },
                groupBy: (position) => position.type,
                separator: Divider(
                  height: 1,
                  color: context.colorStyling.background1,
                ),
                groupSeparatorBuilder: (position, _) => Padding(
                  padding: const EdgeInsetsDirectional.only(
                    bottom: 12.0,
                    top: 20.0,
                  ),
                  child: Label(
                    model: LabelModel(
                      text: position.type.humanReadableName.toUpperCase(),
                      textStyle: CompanyTextStylePointer.b4,
                      color: CompanyColorPointer.secondary4,
                    ),
                  ),
                ),
              ),
            ),
            const LicenseNumbersSliver(),
          ],
        );
      },
      orElse: () => const SizedBox(),
    );
  }
}

class _ShimmerContent extends StatelessWidget {
  const _ShimmerContent();

  @override
  Widget build(BuildContext context) {
    return const SliverToBoxAdapter(
      child: CompanyShimmer(
        model: CompanyShimmerModel(),
        child: MainPortfolioShimmer(),
      ),
    );
  }
}

class MainPortfolioShimmer extends StatelessWidget {
  const MainPortfolioShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        PortfolioActionButtons(
          isShimmer: true,
          actionButtons: PortfolioType.primary.actionButtons,
        ),
        const Padding(
          padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 16),
          child: PositionsFilter(
            title: 'Investments',
            isShimmer: true,
            timeframeText: 'All time',
          ),
        ),
        const Padding(
          padding: EdgeInsetsDirectional.only(
            start: 24,
            end: 24,
            bottom: 12.0,
            top: 20.0,
          ),
          child: Label(
            model: LabelModel(
              text: 'ETFs',
              textStyle: CompanyTextStylePointer.b4,
              color: CompanyColorPointer.secondary4,
              backgroundColor: CompanyColorPointer.background1,
            ),
          ),
        ),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 24),
          height: 260,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(13),
            color: Colors.white,
          ),
        ),
      ],
    );
  }
}
