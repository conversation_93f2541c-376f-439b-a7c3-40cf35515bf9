part of 'accounts_list_cubit.dart';

@freezed
class AccountsListState with _$AccountsListState {
  const factory AccountsListState.fetching() = _AccountsListStateFetching;
  const factory AccountsListState.idle({
    required List<AccountDetails> allAccounts,
    required List<AccountDetails> accounts,
  }) = _AccountsListStateIdle;
  const factory AccountsListState.error() = _AccountsListStateError;
}
