import 'package:instruments_api/index.dart';
import 'package:instruments_impl/index.dart';
import 'package:neobroker_models/models.dart';
import 'package:wio_feature_user_api/index.dart';

class InstrumentsInteractorImpl implements InstrumentsInteractor {
  final InstrumentsRepository _instrumentsRepository;
  final BrokerUserRepository _userRepository;

  InstrumentsInteractorImpl(this._instrumentsRepository, this._userRepository);

  @override
  Future<List<Instrument>> getInstrumentsByKeyword({
    required InstrumentSearchParams params,
  }) async {
    final userId = await _getUserId();

    return _instrumentsRepository.getInstrumentsByKeyword(
      params: params,
      userId: userId,
    );
  }

  @override
  Future<Instrument> getInstrumentById({required String instrumentId}) async {
    final userId = await _getUserId();
    final instruments = await _instrumentsRepository.getInstrumentsByIds(
      [instrumentId],
      userId,
    );

    if (instruments.isEmpty) {
      throw Exception(
        'Empty instruments list for instrument with id $instrumentId',
      );
    }

    return instruments[0];
  }

  @override
  Future<List<Instrument>> getInstrumentByIds({
    required List<String> instrumentIds,
  }) async {
    final userId = await _getUserId();
    final instruments = await _instrumentsRepository.getInstrumentsByIds(
      instrumentIds,
      userId,
    );

    return instruments;
  }

  @override
  Future<List<MarketData>> getMarketData(List<String> ids) async {
    final userId = await _getUserId();

    return _instrumentsRepository.getMarketData(
      ids,
      userId,
    );
  }

  @override
  Future<List<Sector>> getSectors({
    InstrumentSearchFeatureType featureType = InstrumentSearchFeatureType.all,
  }) async {
    final userId = await _getUserId();

    return _instrumentsRepository.getSectors(
      userId: userId,
      featureType: featureType,
    );
  }

  @override
  Future<bool> shouldShowBuyingFromRewardsInfo() async {
    final isAlreadyShown = await _instrumentsRepository
        .isRewardsInfoShown(InstrumentsRepository.buyingFromRewardsInfoKey);
    final shouldShow = !isAlreadyShown;

    if (shouldShow) {
      _instrumentsRepository
          .setRewardsInfoShown(InstrumentsRepository.buyingFromRewardsInfoKey)
          .ignore();
    }

    return shouldShow;
  }

  @override
  Future<bool> shouldShowSellingFromRewardsInfo() async {
    final isAlreadyShown = await _instrumentsRepository
        .isRewardsInfoShown(InstrumentsRepository.sellingFromRewardsInfoKey);
    final shouldShow = !isAlreadyShown;

    if (shouldShow) {
      _instrumentsRepository
          .setRewardsInfoShown(InstrumentsRepository.sellingFromRewardsInfoKey)
          .ignore();
    }

    return shouldShow;
  }

  @override
  Future<bool> isBrokerPromoBannerEnabled(String fromFeatureName) =>
      _instrumentsRepository.isBrokerPromoBannerEnabled(fromFeatureName);

  @override
  Future<void> hideBrokerPromoBanner(String fromFeatureName) =>
      _instrumentsRepository.hideBrokerPromoBanner(fromFeatureName);

  Future<String> _getUserId() {
    return _userRepository.getUserId();
  }
}
