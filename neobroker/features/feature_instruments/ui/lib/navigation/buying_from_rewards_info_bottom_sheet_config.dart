import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:instruments_api/index.dart';
import 'package:neobroker_models/models.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

part 'buying_from_rewards_info_bottom_sheet_config.freezed.dart';

@freezed
class BuyingFromRewardsInfoBottomSheetConfig
    with _$BuyingFromRewardsInfoBottomSheetConfig
    implements BottomSheetNavigationConfig<bool> {
  const factory BuyingFromRewardsInfoBottomSheetConfig({
    required Portfolio portfolio,
  }) = _BuyingFromRewardsInfoBottomSheetConfig;

  const BuyingFromRewardsInfoBottomSheetConfig._();

  @override
  String get feature => InstrumentsFeatureNavigationConfig.name;

  @override
  String toString() => 'BuyingFromRewardsInfoBottomSheetConfig';
}
