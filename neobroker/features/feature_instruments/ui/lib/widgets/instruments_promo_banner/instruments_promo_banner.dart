import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:instruments_ui/index.dart';
import 'package:instruments_ui/widgets/instruments_promo_banner/analytics/instruments_promo_banner_analytics.dart';
import 'package:instruments_ui/widgets/instruments_promo_banner/cubit/instruments_promo_cubit.dart';
import 'package:instruments_ui/widgets/instruments_promo_banner/trending_instruments_content/trending_instruments_content.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_product_hub_api/index.dart';

class InstrumentsPromoBannerCubitParams {
  final String featureName;
  final InstrumentsPromoBannerOrigin origin;
  final ProductHubItem? brokerProductItem;

  const InstrumentsPromoBannerCubitParams({
    required this.featureName,
    required this.origin,
    this.brokerProductItem,
  });
}

class InstrumentsPromoBanner extends StatefulWidget {
  final String featureName;
  final InstrumentsPromoBannerOrigin origin;
  final ProductHubItem? brokerProductItem;

  const InstrumentsPromoBanner({
    required this.origin,
    required this.featureName,
    this.brokerProductItem,
    super.key,
  });

  @override
  State<InstrumentsPromoBanner> createState() => _InstrumentsPromoBannerState();
}

class _InstrumentsPromoBannerState extends State<InstrumentsPromoBanner> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => DependencyProvider.getWithParams<InstrumentsPromoCubit,
          InstrumentsPromoBannerCubitParams, void>(
        param1: InstrumentsPromoBannerCubitParams(
          featureName: widget.featureName,
          origin: widget.origin,
          brokerProductItem: widget.brokerProductItem,
        ),
      ),
      child: Builder(
        builder: (_) =>
            BlocBuilder<InstrumentsPromoCubit, InstumentsPromoState>(
          builder: (_, state) {
            return state.map(
              banner: (_) => const _InstrumentsNotBrokerContent(),
              dailyMovers: (state) => TrendingInstrumentsContent(
                dailyMovers: state.dailyMovers,
              ),
              error: (_) => const SizedBox(),
              loading: (_) => const SizedBox(),
              hidden: (_) => const SizedBox(),
            );
          },
        ),
      ),
    );
  }
}

class _InstrumentsNotBrokerContent extends StatelessWidget {
  const _InstrumentsNotBrokerContent();

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<InstrumentsPromoCubit>();
    final localization = InstrumentsLocalization.of(context);

    return Container(
      padding: const EdgeInsetsDirectional.fromSTEB(16, 10, 16, 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: context.colorStyling.primary3,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              GestureDetector(
                onTap: cubit.closeBanner,
                child: CompanyIcon(
                  CompanyIconModel(
                    icon: CompanyIconPointer.close.toGraphicAsset(),
                    size: CompanyIconSize.small,
                    color: CompanyColorPointer.primary2,
                  ),
                ),
              ),
            ],
          ),
          Label(
            model: LabelModel(
              text: localization.brokerPromoBannerText,
              textStyle: CompanyTextStylePointer.h4,
              color: CompanyColorPointer.primary2,
            ),
          ),
          const SizedBox(
            height: 16,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                height: 40,
                child: Button(
                  model: ButtonModel(
                    title: localization.brokerPromoBannerActionText,
                    styleOverride: const ButtonStyleOverride(
                      active: ButtonStateColorScheme(
                        background: CompanyColorPointer.background3,
                        foreground: CompanyColorPointer.primary3,
                      ),
                    ),
                  ),
                  onPressed: cubit.onStartTap,
                ),
              ),
              const Expanded(
                child: _PositionedImages(
                  imageUrls: [
                    'https://images.ctfassets.net/l65m9bcr2nac/64790d64-4790-5c69-be2b-11fe8f4485b7/151259f39d4cef3be64064082ad2eca1/TSLA',
                    'https://images.ctfassets.net/l65m9bcr2nac/e2e51455-8931-5577-a0cc-7664f3c95594/648f87d88f3f0d7eac17761ea8b137ca/AAPL',
                    'https://images.ctfassets.net/l65m9bcr2nac/a0f9e2f2-60ae-5003-8cc2-aa9cebfb4ab3/5a7ed2d803ca48ee846134d9ccbe913d/DIS',
                    'https://images.ctfassets.net/l65m9bcr2nac/f9f93980-ec2f-5847-a2d3-514c37b8cee4/ce2ccd6ea3da51d4a35f7abbdfa5b8b9/NVDA',
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _PositionedImages extends StatelessWidget {
  final List<String> imageUrls;

  const _PositionedImages({
    required this.imageUrls,
  });

  @override
  Widget build(BuildContext context) {
    if (imageUrls.isEmpty) {
      return const SizedBox();
    }

    return SizedBox(
      width: double.infinity,
      height: 40,
      child: Stack(
        textDirection: Directionality.of(context),
        alignment: AlignmentDirectional.centerEnd,
        children: imageUrls
            .asMap()
            .entries
            .map<Widget>(
              (entry) => Positioned.directional(
                textDirection: Directionality.of(context),
                end: (imageUrls.length - (entry.key + 1)) * 25.0,
                child: Tile(
                  model: TileModel.image(
                    backgroundColor: CompanyColorPointer.primary3,
                    image: ImageInTileModel(
                      path: entry.value,
                    ),
                  ),
                  size: 40,
                ),
              ),
            )
            .toList(),
      ),
    );
  }
}
