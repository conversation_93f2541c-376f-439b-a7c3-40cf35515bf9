// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'instrument_market_access_handler_analytics.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$InstrumentMarketAccessHandlerAnalyticsEventPayload {
  String get userFeatureStatus => throw _privateConstructorUsedError;
  String get errorReason => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String userFeatureStatus, String errorReason)
        error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String userFeatureStatus, String errorReason)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String userFeatureStatus, String errorReason)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(
            InstrumentMarketAccessHandlerAnalyticsEventPayloadError value)
        error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(
            InstrumentMarketAccessHandlerAnalyticsEventPayloadError value)?
        error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(
            InstrumentMarketAccessHandlerAnalyticsEventPayloadError value)?
        error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of InstrumentMarketAccessHandlerAnalyticsEventPayload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InstrumentMarketAccessHandlerAnalyticsEventPayloadCopyWith<
          InstrumentMarketAccessHandlerAnalyticsEventPayload>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InstrumentMarketAccessHandlerAnalyticsEventPayloadCopyWith<
    $Res> {
  factory $InstrumentMarketAccessHandlerAnalyticsEventPayloadCopyWith(
          InstrumentMarketAccessHandlerAnalyticsEventPayload value,
          $Res Function(InstrumentMarketAccessHandlerAnalyticsEventPayload)
              then) =
      _$InstrumentMarketAccessHandlerAnalyticsEventPayloadCopyWithImpl<$Res,
          InstrumentMarketAccessHandlerAnalyticsEventPayload>;
  @useResult
  $Res call({String userFeatureStatus, String errorReason});
}

/// @nodoc
class _$InstrumentMarketAccessHandlerAnalyticsEventPayloadCopyWithImpl<$Res,
        $Val extends InstrumentMarketAccessHandlerAnalyticsEventPayload>
    implements
        $InstrumentMarketAccessHandlerAnalyticsEventPayloadCopyWith<$Res> {
  _$InstrumentMarketAccessHandlerAnalyticsEventPayloadCopyWithImpl(
      this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InstrumentMarketAccessHandlerAnalyticsEventPayload
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userFeatureStatus = null,
    Object? errorReason = null,
  }) {
    return _then(_value.copyWith(
      userFeatureStatus: null == userFeatureStatus
          ? _value.userFeatureStatus
          : userFeatureStatus // ignore: cast_nullable_to_non_nullable
              as String,
      errorReason: null == errorReason
          ? _value.errorReason
          : errorReason // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImplCopyWith<
        $Res>
    implements
        $InstrumentMarketAccessHandlerAnalyticsEventPayloadCopyWith<$Res> {
  factory _$$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImplCopyWith(
          _$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImpl value,
          $Res Function(
                  _$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImpl)
              then) =
      __$$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImplCopyWithImpl<
          $Res>;
  @override
  @useResult
  $Res call({String userFeatureStatus, String errorReason});
}

/// @nodoc
class __$$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImplCopyWithImpl<
        $Res>
    extends _$InstrumentMarketAccessHandlerAnalyticsEventPayloadCopyWithImpl<
        $Res, _$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImpl>
    implements
        _$$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImplCopyWith<
            $Res> {
  __$$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImplCopyWithImpl(
      _$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImpl _value,
      $Res Function(
              _$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImpl)
          _then)
      : super(_value, _then);

  /// Create a copy of InstrumentMarketAccessHandlerAnalyticsEventPayload
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userFeatureStatus = null,
    Object? errorReason = null,
  }) {
    return _then(_$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImpl(
      userFeatureStatus: null == userFeatureStatus
          ? _value.userFeatureStatus
          : userFeatureStatus // ignore: cast_nullable_to_non_nullable
              as String,
      errorReason: null == errorReason
          ? _value.errorReason
          : errorReason // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImpl
    extends InstrumentMarketAccessHandlerAnalyticsEventPayloadError {
  const _$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImpl(
      {required this.userFeatureStatus, required this.errorReason})
      : super._();

  @override
  final String userFeatureStatus;
  @override
  final String errorReason;

  @override
  String toString() {
    return 'InstrumentMarketAccessHandlerAnalyticsEventPayload.error(userFeatureStatus: $userFeatureStatus, errorReason: $errorReason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other
                is _$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImpl &&
            (identical(other.userFeatureStatus, userFeatureStatus) ||
                other.userFeatureStatus == userFeatureStatus) &&
            (identical(other.errorReason, errorReason) ||
                other.errorReason == errorReason));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userFeatureStatus, errorReason);

  /// Create a copy of InstrumentMarketAccessHandlerAnalyticsEventPayload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImplCopyWith<
          _$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImpl>
      get copyWith =>
          __$$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImplCopyWithImpl<
                  _$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String userFeatureStatus, String errorReason)
        error,
  }) {
    return error(userFeatureStatus, errorReason);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String userFeatureStatus, String errorReason)? error,
  }) {
    return error?.call(userFeatureStatus, errorReason);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String userFeatureStatus, String errorReason)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(userFeatureStatus, errorReason);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(
            InstrumentMarketAccessHandlerAnalyticsEventPayloadError value)
        error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(
            InstrumentMarketAccessHandlerAnalyticsEventPayloadError value)?
        error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(
            InstrumentMarketAccessHandlerAnalyticsEventPayloadError value)?
        error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class InstrumentMarketAccessHandlerAnalyticsEventPayloadError
    extends InstrumentMarketAccessHandlerAnalyticsEventPayload {
  const factory InstrumentMarketAccessHandlerAnalyticsEventPayloadError(
          {required final String userFeatureStatus,
          required final String errorReason}) =
      _$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImpl;
  const InstrumentMarketAccessHandlerAnalyticsEventPayloadError._() : super._();

  @override
  String get userFeatureStatus;
  @override
  String get errorReason;

  /// Create a copy of InstrumentMarketAccessHandlerAnalyticsEventPayload
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImplCopyWith<
          _$InstrumentMarketAccessHandlerAnalyticsEventPayloadErrorImpl>
      get copyWith => throw _privateConstructorUsedError;
}
