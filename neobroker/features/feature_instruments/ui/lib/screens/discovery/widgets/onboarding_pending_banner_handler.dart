import 'package:flutter/material.dart';
import 'package:instruments_ui/locale/instruments_localization.g.dart';
import 'package:instruments_ui/screens/discovery/widgets/onboarding_pending_banner.dart';
import 'package:neobroker_models/models.dart';

class OnboardingPendingBannerHandler extends StatelessWidget {
  final UserMarketAccess marketAccess;
  const OnboardingPendingBannerHandler({
    required this.marketAccess,
    super.key,
  });

  String _getBannerText(InstrumentsLocalization localization) {
    if (marketAccess.isCryptoMarketPending && marketAccess.isUaeMarketPending) {
      return localization.discoveryOnboardingBannerCryptoUaePending;
    }
    if (marketAccess.isCryptoMarketPending) {
      return localization.discoveryOnboardingBannerCryptoPending;
    }
    if (marketAccess.isUaeMarketPending) {
      return localization.discoveryUaePendingLabel;
    }

    return '';
  }

  @override
  Widget build(BuildContext context) {
    final localization = InstrumentsLocalization.of(context);
    final text = _getBannerText(localization);

    if (text.isEmpty) return const SizedBox.shrink();

    return OnboardingPendingBanner(
      text: text,
    );
  }
}
