import 'package:domain/domain.dart';
import 'package:flutter/material.dart';
import 'package:instruments_ui/extensions/trending_instrument_extensions.dart';
import 'package:instruments_ui/index.dart';
import 'package:instruments_ui/screens/discovery/ui_models/trending_companies_ui_model.dart';
import 'package:neobroker_utils/utils.dart' as utils;
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

/// Content of the Trending companies section
class TrendingCompaniesSection extends StatefulWidget {
  static const tabsKey = ValueKey('discoveryTrendingCompaniesTabsKey');
  static const stockTileKey =
      ValueKey('discoveryTrendingCompaniesStockTileKey');

  final Data<TrendingCompaniesUiModel> trendingCompaniesUiModel;
  final void Function(int)? onTabSelected;
  final void Function(
    String instrumentName,
  )? onTrendingInstrumentPressed;

  const TrendingCompaniesSection({
    required this.trendingCompaniesUiModel,
    super.key,
    this.onTabSelected,
    this.onTrendingInstrumentPressed,
  });

  @override
  State<StatefulWidget> createState() => _TrendingCompaniesSectionState();
}

class _TrendingCompaniesSectionState extends State<TrendingCompaniesSection> {
  @override
  Widget build(BuildContext context) {
    final trendingCompaniesUiModel = widget.trendingCompaniesUiModel;

    final content = trendingCompaniesUiModel.content;

    if (trendingCompaniesUiModel.isLoading()) {
      return const TrendingCompaniesShimmer();
    } else if (trendingCompaniesUiModel.isError() || content == null) {
      return const SizedBox.shrink();
    } else {
      return _TrendingCompaniesContent(
        trendingCompaniesUiModel: content,
        onTabSelected: widget.onTabSelected,
        onTrendingInstrumentPressed: widget.onTrendingInstrumentPressed,
      );
    }
  }
}

class _TrendingCompaniesContent extends StatefulWidget {
  final TrendingCompaniesUiModel trendingCompaniesUiModel;
  final void Function(int)? onTabSelected;
  final void Function(
    String instrumentName,
  )? onTrendingInstrumentPressed;

  const _TrendingCompaniesContent({
    required this.trendingCompaniesUiModel,
    this.onTabSelected,
    this.onTrendingInstrumentPressed,
  });

  @override
  State<StatefulWidget> createState() => _TrendingCompaniesContentState();
}

class _TrendingCompaniesContentState extends State<_TrendingCompaniesContent> {
  @override
  Widget build(BuildContext context) {
    final localization = InstrumentsLocalization.of(context);
    final colorScheme = context.colorStyling;

    final contentController = PageController();

    final trendingCompaniesUiModel = widget.trendingCompaniesUiModel;
    final mostPopularInstruments = trendingCompaniesUiModel
        .trendingInstruments.mostPopular
        .map((e) => e.model)
        .toList();
    final dailyMoversInstruments = trendingCompaniesUiModel
        .trendingInstruments.dailyMovers
        .map((e) => e.model)
        .toList();

    if (mostPopularInstruments.isEmpty && dailyMoversInstruments.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsetsDirectional.only(top: 28.0),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14),
          color: colorScheme.surface2,
        ),
        margin: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SectionHeader(
                SectionHeaderModel(
                  header: localization.instrumentsDiscoveryTrendingTitle,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Tabs(
              TabsModel(
                variant: TabsVariant.small,
                tabNames: [
                  if (mostPopularInstruments.isNotEmpty)
                    localization.instrumentsDiscoveryTrendingPopularTitle,
                  if (dailyMoversInstruments.isNotEmpty)
                    localization.instrumentsDiscoveryTrendingMoversTitle,
                ],
                selectedIndex: trendingCompaniesUiModel.selectedTabIndex,
              ),
              key: TrendingCompaniesSection.tabsKey,
              padding: const EdgeInsetsDirectional.only(
                start: 24,
                end: 24,
              ),
              // ignore: prefer-extracting-callbacks
              onTabPressed: (index) {
                widget.onTabSelected?.call(index);
                contentController.jumpToPage(index);
              },
            ),
            const SizedBox(height: 24),
            AspectRatio(
              aspectRatio: (StockTile.width * 4 + 24) / (StockTile.height * 2),
              child: PageView(
                controller: contentController,
                onPageChanged: widget.onTabSelected,
                children: <GridModel<StockTileModel>>[
                  if (mostPopularInstruments.isNotEmpty)
                    GridModel<StockTileModel>(
                      items: mostPopularInstruments,
                    ),
                  if (dailyMoversInstruments.isNotEmpty)
                    GridModel<StockTileModel>(
                      items: dailyMoversInstruments,
                    ),
                ]
                    .mapIndexed(
                      (grid, index) => Grid(
                        model: grid,
                        crossAxisCount: 4,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 16,
                        childAspectRatio: StockTile.widgetSizeRatio,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        // ignore: avoid_types_on_closure_parameters
                        gridItemBuilder: (StockTileModel tileModel) =>
                            GestureDetector(
                          key: TrendingCompaniesSection.stockTileKey,
                          onTap: () => widget.onTrendingInstrumentPressed?.call(
                            tileModel.titleModel.text,
                          ),
                          child: StockTile(model: tileModel),
                        ),
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                      ),
                    )
                    .toList(),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
