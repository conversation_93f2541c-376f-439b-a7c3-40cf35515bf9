import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

class TrendingCompaniesSectionPreviewShimmer extends StatelessWidget {
  final bool showScrollingFilter;
  final int itemCount;
  const TrendingCompaniesSectionPreviewShimmer({
    this.showScrollingFilter = true,
    this.itemCount = 8,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(24, 0, 24, 0),
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: context.colorStyling.primary2,
          borderRadius: BorderRadius.circular(14.0),
        ),
        child: CompanyShimmer(
          model: const CompanyShimmerModel(),
          child: Column(
            children: [
              const SizedBox(height: 24),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 24.0),
                child: SectionHeaderSkeleton(),
              ),
              const SizedBox(height: 16),
              if (showScrollingFilter) const ScrollingFiltersSkeleton(),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 24,
                ),
                child: Align(
                  alignment: Alignment.topCenter,
                  child: Wrap(
                    runSpacing: 28.0,
                    spacing: 18.0,
                    children: List.generate(
                      itemCount,
                      (_) => const _TrendingCompanyShimmer(),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _TrendingCompanyShimmer extends StatelessWidget {
  const _TrendingCompanyShimmer();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 44.0,
          height: 44.0,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: context.colorStyling.surface6,
          ),
        ),
        const Space.vertical(8.0),
        const Label(
          model: LabelModel(
            text: 'Symbol12',
            textStyle: CompanyTextStylePointer.b4,
            backgroundColor: CompanyColorPointer.surface6,
          ),
        ),
        const Space.vertical(4.0),
        const Label(
          model: LabelModel(
            text: '00.00%',
            textStyle: CompanyTextStylePointer.b4,
            backgroundColor: CompanyColorPointer.surface6,
          ),
        ),
      ],
    );
  }
}
