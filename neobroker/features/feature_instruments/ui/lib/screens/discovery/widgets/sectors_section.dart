import 'package:domain/domain.dart';
import 'package:flutter/material.dart';
import 'package:instruments_ui/index.dart';
import 'package:neobroker_models/models.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

/// Content of the Sectors section
class SectorsSection extends StatelessWidget {
  static const sectorKey = ValueKey('discoverySectorsItemKey');
  static const sectorLabelKey = ValueKey('discoverySectorLabelKey');

  final Data<List<Sector>> sectors;
  final void Function(Sector) onSectorPressed;

  const SectorsSection({
    required this.sectors,
    required this.onSectorPressed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (sectors.isLoading()) {
      return const _SectorsShimmer();
    } else if (sectors.isError() ||
        (sectors.content != null && sectors.content!.isEmpty)) {
      return const SizedBox.shrink();
    } else {
      return _SectorsContent(
        sectors: sectors.content ?? [],
        onSectorPressed: onSectorPressed,
      );
    }
  }
}

class _SectorsContent extends StatelessWidget {
  final List<Sector> sectors;
  final void Function(Sector) onSectorPressed;

  const _SectorsContent({
    required this.sectors,
    required this.onSectorPressed,
  });

  @override
  Widget build(BuildContext context) {
    final localization = InstrumentsLocalization.of(context);

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: SectionHeader(
            SectionHeaderModel(
              header: localization.instrumentsDiscoverySectorsTitle,
            ),
          ),
        ),
        const SizedBox(height: 16),
        SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          scrollDirection: Axis.horizontal,
          child: SizedBox(
            width: MediaQuery.of(context).size.width * 1.7,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Wrap(
                spacing: 12,
                runSpacing: 12,
                children: sectors
                    .map(
                      (sector) => _CollectionItem(
                        sector: sector,
                        onSectorPressed: onSectorPressed,
                      ),
                    )
                    .toList(),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _SectorsShimmer extends StatelessWidget {
  const _SectorsShimmer();
  @override
  Widget build(BuildContext context) {
    final localization = InstrumentsLocalization.of(context);

    return CompanyShimmer(
      model: const CompanyShimmerModel(),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: SectionHeader(
              SectionHeaderModel(
                header: localization.instrumentsDiscoverySectorsTitle,
              ),
            ),
          ),
          const SizedBox(height: 16),
          SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              width: MediaQuery.of(context).size.width * 1.6,
              child: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 24),
                child: Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  children: [
                    _CollectionItem(
                      sector: Sector(
                        name: 'Basic Materials',
                        emoji: '🏗️',
                        images: [],
                      ),
                    ),
                    _CollectionItem(
                      sector: Sector(
                        name: 'Communication',
                        emoji: '💬',
                        images: [],
                      ),
                    ),
                    _CollectionItem(
                      sector: Sector(
                        name: 'Consumer',
                        emoji: '🛍️',
                        images: [],
                      ),
                    ),
                    _CollectionItem(
                      sector: Sector(
                        name: 'Energy',
                        emoji: '💡',
                        images: [],
                      ),
                    ),
                    _CollectionItem(
                      sector: Sector(
                        name: 'Financial Services',
                        emoji: '💵',
                        images: [],
                      ),
                    ),
                    _CollectionItem(
                      sector: Sector(
                        name: 'Healthcare',
                        emoji: '💊',
                        images: [],
                      ),
                    ),
                    _CollectionItem(
                      sector: Sector(
                        name: 'Industrial',
                        emoji: '🚧',
                        images: [],
                      ),
                    ),
                    _CollectionItem(
                      sector: Sector(
                        name: 'Real Estate',
                        emoji: '🏠',
                        images: [],
                      ),
                    ),
                    _CollectionItem(
                      sector: Sector(
                        name: 'Technology',
                        emoji: '💻',
                        images: [],
                      ),
                    ),
                    _CollectionItem(
                      sector: Sector(
                        name: 'Utilities',
                        emoji: '🔋',
                        images: [],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _CollectionItem extends StatelessWidget {
  final Sector sector;
  final void Function(Sector)? onSectorPressed;

  const _CollectionItem({
    required this.sector,
    this.onSectorPressed,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      key: SectorsSection.sectorKey,
      borderRadius: BorderRadius.circular(32),
      onTap: () => onSectorPressed?.call(sector),
      child: Ink(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 9,
        ),
        decoration: BoxDecoration(
          color: context.colorStyling.fromPointer(
            CompanyColorPointer.surface2,
          ),
          borderRadius: BorderRadius.circular(32),
        ),
        child: Label(
          key: SectorsSection.sectorLabelKey,
          model: LabelModel(
            text: sector.nameWithEmoji,
            textStyle: CompanyTextStylePointer.b2medium,
            color: CompanyColorPointer.primary3,
          ),
        ),
      ),
    );
  }
}
