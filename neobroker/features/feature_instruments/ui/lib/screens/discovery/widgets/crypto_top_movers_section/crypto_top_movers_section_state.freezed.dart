// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'crypto_top_movers_section_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CryptoTopMoversSectionState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<TrendingInstrument> topCryptoCurrencies,
            UserMarketAccess marketAccess, bool wealthEligible)
        loaded,
    required TResult Function() error,
    required TResult Function() fetching,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<TrendingInstrument> topCryptoCurrencies,
            UserMarketAccess marketAccess, bool wealthEligible)?
        loaded,
    TResult? Function()? error,
    TResult? Function()? fetching,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<TrendingInstrument> topCryptoCurrencies,
            UserMarketAccess marketAccess, bool wealthEligible)?
        loaded,
    TResult Function()? error,
    TResult Function()? fetching,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CryptoTopMoversSectionState value) loaded,
    required TResult Function(_CryptoTopMoversSectionErrorState value) error,
    required TResult Function(_CryptoTopMoversSectionFetchingState value)
        fetching,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CryptoTopMoversSectionState value)? loaded,
    TResult? Function(_CryptoTopMoversSectionErrorState value)? error,
    TResult? Function(_CryptoTopMoversSectionFetchingState value)? fetching,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CryptoTopMoversSectionState value)? loaded,
    TResult Function(_CryptoTopMoversSectionErrorState value)? error,
    TResult Function(_CryptoTopMoversSectionFetchingState value)? fetching,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CryptoTopMoversSectionStateCopyWith<$Res> {
  factory $CryptoTopMoversSectionStateCopyWith(
          CryptoTopMoversSectionState value,
          $Res Function(CryptoTopMoversSectionState) then) =
      _$CryptoTopMoversSectionStateCopyWithImpl<$Res,
          CryptoTopMoversSectionState>;
}

/// @nodoc
class _$CryptoTopMoversSectionStateCopyWithImpl<$Res,
        $Val extends CryptoTopMoversSectionState>
    implements $CryptoTopMoversSectionStateCopyWith<$Res> {
  _$CryptoTopMoversSectionStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CryptoTopMoversSectionState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$CryptoTopMoversSectionStateImplCopyWith<$Res> {
  factory _$$CryptoTopMoversSectionStateImplCopyWith(
          _$CryptoTopMoversSectionStateImpl value,
          $Res Function(_$CryptoTopMoversSectionStateImpl) then) =
      __$$CryptoTopMoversSectionStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<TrendingInstrument> topCryptoCurrencies,
      UserMarketAccess marketAccess,
      bool wealthEligible});

  $UserMarketAccessCopyWith<$Res> get marketAccess;
}

/// @nodoc
class __$$CryptoTopMoversSectionStateImplCopyWithImpl<$Res>
    extends _$CryptoTopMoversSectionStateCopyWithImpl<$Res,
        _$CryptoTopMoversSectionStateImpl>
    implements _$$CryptoTopMoversSectionStateImplCopyWith<$Res> {
  __$$CryptoTopMoversSectionStateImplCopyWithImpl(
      _$CryptoTopMoversSectionStateImpl _value,
      $Res Function(_$CryptoTopMoversSectionStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CryptoTopMoversSectionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? topCryptoCurrencies = null,
    Object? marketAccess = null,
    Object? wealthEligible = null,
  }) {
    return _then(_$CryptoTopMoversSectionStateImpl(
      topCryptoCurrencies: null == topCryptoCurrencies
          ? _value._topCryptoCurrencies
          : topCryptoCurrencies // ignore: cast_nullable_to_non_nullable
              as List<TrendingInstrument>,
      marketAccess: null == marketAccess
          ? _value.marketAccess
          : marketAccess // ignore: cast_nullable_to_non_nullable
              as UserMarketAccess,
      wealthEligible: null == wealthEligible
          ? _value.wealthEligible
          : wealthEligible // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of CryptoTopMoversSectionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserMarketAccessCopyWith<$Res> get marketAccess {
    return $UserMarketAccessCopyWith<$Res>(_value.marketAccess, (value) {
      return _then(_value.copyWith(marketAccess: value));
    });
  }
}

/// @nodoc

class _$CryptoTopMoversSectionStateImpl
    implements _CryptoTopMoversSectionState {
  const _$CryptoTopMoversSectionStateImpl(
      {final List<TrendingInstrument> topCryptoCurrencies = const [],
      this.marketAccess = const UserMarketAccess(
          usMarketStatus: UserFeatureStatus.unknown,
          uaeMarketStatus: UserFeatureStatus.unknown,
          cryptoMarketStatus: UserFeatureStatus.unknown),
      this.wealthEligible = false})
      : _topCryptoCurrencies = topCryptoCurrencies;

  final List<TrendingInstrument> _topCryptoCurrencies;
  @override
  @JsonKey()
  List<TrendingInstrument> get topCryptoCurrencies {
    if (_topCryptoCurrencies is EqualUnmodifiableListView)
      return _topCryptoCurrencies;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_topCryptoCurrencies);
  }

  @override
  @JsonKey()
  final UserMarketAccess marketAccess;
  @override
  @JsonKey()
  final bool wealthEligible;

  @override
  String toString() {
    return 'CryptoTopMoversSectionState.loaded(topCryptoCurrencies: $topCryptoCurrencies, marketAccess: $marketAccess, wealthEligible: $wealthEligible)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CryptoTopMoversSectionStateImpl &&
            const DeepCollectionEquality()
                .equals(other._topCryptoCurrencies, _topCryptoCurrencies) &&
            (identical(other.marketAccess, marketAccess) ||
                other.marketAccess == marketAccess) &&
            (identical(other.wealthEligible, wealthEligible) ||
                other.wealthEligible == wealthEligible));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_topCryptoCurrencies),
      marketAccess,
      wealthEligible);

  /// Create a copy of CryptoTopMoversSectionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CryptoTopMoversSectionStateImplCopyWith<_$CryptoTopMoversSectionStateImpl>
      get copyWith => __$$CryptoTopMoversSectionStateImplCopyWithImpl<
          _$CryptoTopMoversSectionStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<TrendingInstrument> topCryptoCurrencies,
            UserMarketAccess marketAccess, bool wealthEligible)
        loaded,
    required TResult Function() error,
    required TResult Function() fetching,
  }) {
    return loaded(topCryptoCurrencies, marketAccess, wealthEligible);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<TrendingInstrument> topCryptoCurrencies,
            UserMarketAccess marketAccess, bool wealthEligible)?
        loaded,
    TResult? Function()? error,
    TResult? Function()? fetching,
  }) {
    return loaded?.call(topCryptoCurrencies, marketAccess, wealthEligible);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<TrendingInstrument> topCryptoCurrencies,
            UserMarketAccess marketAccess, bool wealthEligible)?
        loaded,
    TResult Function()? error,
    TResult Function()? fetching,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(topCryptoCurrencies, marketAccess, wealthEligible);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CryptoTopMoversSectionState value) loaded,
    required TResult Function(_CryptoTopMoversSectionErrorState value) error,
    required TResult Function(_CryptoTopMoversSectionFetchingState value)
        fetching,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CryptoTopMoversSectionState value)? loaded,
    TResult? Function(_CryptoTopMoversSectionErrorState value)? error,
    TResult? Function(_CryptoTopMoversSectionFetchingState value)? fetching,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CryptoTopMoversSectionState value)? loaded,
    TResult Function(_CryptoTopMoversSectionErrorState value)? error,
    TResult Function(_CryptoTopMoversSectionFetchingState value)? fetching,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _CryptoTopMoversSectionState
    implements CryptoTopMoversSectionState {
  const factory _CryptoTopMoversSectionState(
      {final List<TrendingInstrument> topCryptoCurrencies,
      final UserMarketAccess marketAccess,
      final bool wealthEligible}) = _$CryptoTopMoversSectionStateImpl;

  List<TrendingInstrument> get topCryptoCurrencies;
  UserMarketAccess get marketAccess;
  bool get wealthEligible;

  /// Create a copy of CryptoTopMoversSectionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CryptoTopMoversSectionStateImplCopyWith<_$CryptoTopMoversSectionStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CryptoTopMoversSectionErrorStateImplCopyWith<$Res> {
  factory _$$CryptoTopMoversSectionErrorStateImplCopyWith(
          _$CryptoTopMoversSectionErrorStateImpl value,
          $Res Function(_$CryptoTopMoversSectionErrorStateImpl) then) =
      __$$CryptoTopMoversSectionErrorStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CryptoTopMoversSectionErrorStateImplCopyWithImpl<$Res>
    extends _$CryptoTopMoversSectionStateCopyWithImpl<$Res,
        _$CryptoTopMoversSectionErrorStateImpl>
    implements _$$CryptoTopMoversSectionErrorStateImplCopyWith<$Res> {
  __$$CryptoTopMoversSectionErrorStateImplCopyWithImpl(
      _$CryptoTopMoversSectionErrorStateImpl _value,
      $Res Function(_$CryptoTopMoversSectionErrorStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CryptoTopMoversSectionState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CryptoTopMoversSectionErrorStateImpl
    implements _CryptoTopMoversSectionErrorState {
  const _$CryptoTopMoversSectionErrorStateImpl();

  @override
  String toString() {
    return 'CryptoTopMoversSectionState.error()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CryptoTopMoversSectionErrorStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<TrendingInstrument> topCryptoCurrencies,
            UserMarketAccess marketAccess, bool wealthEligible)
        loaded,
    required TResult Function() error,
    required TResult Function() fetching,
  }) {
    return error();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<TrendingInstrument> topCryptoCurrencies,
            UserMarketAccess marketAccess, bool wealthEligible)?
        loaded,
    TResult? Function()? error,
    TResult? Function()? fetching,
  }) {
    return error?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<TrendingInstrument> topCryptoCurrencies,
            UserMarketAccess marketAccess, bool wealthEligible)?
        loaded,
    TResult Function()? error,
    TResult Function()? fetching,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CryptoTopMoversSectionState value) loaded,
    required TResult Function(_CryptoTopMoversSectionErrorState value) error,
    required TResult Function(_CryptoTopMoversSectionFetchingState value)
        fetching,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CryptoTopMoversSectionState value)? loaded,
    TResult? Function(_CryptoTopMoversSectionErrorState value)? error,
    TResult? Function(_CryptoTopMoversSectionFetchingState value)? fetching,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CryptoTopMoversSectionState value)? loaded,
    TResult Function(_CryptoTopMoversSectionErrorState value)? error,
    TResult Function(_CryptoTopMoversSectionFetchingState value)? fetching,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _CryptoTopMoversSectionErrorState
    implements CryptoTopMoversSectionState {
  const factory _CryptoTopMoversSectionErrorState() =
      _$CryptoTopMoversSectionErrorStateImpl;
}

/// @nodoc
abstract class _$$CryptoTopMoversSectionFetchingStateImplCopyWith<$Res> {
  factory _$$CryptoTopMoversSectionFetchingStateImplCopyWith(
          _$CryptoTopMoversSectionFetchingStateImpl value,
          $Res Function(_$CryptoTopMoversSectionFetchingStateImpl) then) =
      __$$CryptoTopMoversSectionFetchingStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CryptoTopMoversSectionFetchingStateImplCopyWithImpl<$Res>
    extends _$CryptoTopMoversSectionStateCopyWithImpl<$Res,
        _$CryptoTopMoversSectionFetchingStateImpl>
    implements _$$CryptoTopMoversSectionFetchingStateImplCopyWith<$Res> {
  __$$CryptoTopMoversSectionFetchingStateImplCopyWithImpl(
      _$CryptoTopMoversSectionFetchingStateImpl _value,
      $Res Function(_$CryptoTopMoversSectionFetchingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CryptoTopMoversSectionState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CryptoTopMoversSectionFetchingStateImpl
    implements _CryptoTopMoversSectionFetchingState {
  const _$CryptoTopMoversSectionFetchingStateImpl();

  @override
  String toString() {
    return 'CryptoTopMoversSectionState.fetching()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CryptoTopMoversSectionFetchingStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<TrendingInstrument> topCryptoCurrencies,
            UserMarketAccess marketAccess, bool wealthEligible)
        loaded,
    required TResult Function() error,
    required TResult Function() fetching,
  }) {
    return fetching();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<TrendingInstrument> topCryptoCurrencies,
            UserMarketAccess marketAccess, bool wealthEligible)?
        loaded,
    TResult? Function()? error,
    TResult? Function()? fetching,
  }) {
    return fetching?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<TrendingInstrument> topCryptoCurrencies,
            UserMarketAccess marketAccess, bool wealthEligible)?
        loaded,
    TResult Function()? error,
    TResult Function()? fetching,
    required TResult orElse(),
  }) {
    if (fetching != null) {
      return fetching();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CryptoTopMoversSectionState value) loaded,
    required TResult Function(_CryptoTopMoversSectionErrorState value) error,
    required TResult Function(_CryptoTopMoversSectionFetchingState value)
        fetching,
  }) {
    return fetching(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CryptoTopMoversSectionState value)? loaded,
    TResult? Function(_CryptoTopMoversSectionErrorState value)? error,
    TResult? Function(_CryptoTopMoversSectionFetchingState value)? fetching,
  }) {
    return fetching?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CryptoTopMoversSectionState value)? loaded,
    TResult Function(_CryptoTopMoversSectionErrorState value)? error,
    TResult Function(_CryptoTopMoversSectionFetchingState value)? fetching,
    required TResult orElse(),
  }) {
    if (fetching != null) {
      return fetching(this);
    }
    return orElse();
  }
}

abstract class _CryptoTopMoversSectionFetchingState
    implements CryptoTopMoversSectionState {
  const factory _CryptoTopMoversSectionFetchingState() =
      _$CryptoTopMoversSectionFetchingStateImpl;
}
