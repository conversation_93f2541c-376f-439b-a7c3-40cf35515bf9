import 'dart:async';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:feature_neobroker_onboarding_api/index.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:instruments_api/index.dart';
import 'package:instruments_ui/screens/discovery/analytics/discovery_analytics.dart';
import 'package:logging_api/logging.dart';
import 'package:neobroker_models/models.dart';
import 'package:ui/ui.dart';
import 'package:wio_common_feature_wealth_management_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_margin_trading_api/feature_toggles/margin_trading_feature_toggles.dart';
import 'package:wio_feature_user_api/domain/broker_user_interactor.dart';

part 'onboarding_banners_v2_cubit.freezed.dart';
part 'onboarding_banners_v2_state.dart';

class OnboardingBannersV2Cubit extends BaseCubit<OnboardingBannersV2State> {
  final NavigationProvider _navigationProvider;

  final DiscoveryAnalytics _analytics;

  final WealthManagementInteractor _wealthManagementInteractor;
  final WealthManagementStarterHandler _wealthManagementStarterHandler;
  final BrokerUserInteractor _userInteractor;
  final InstrumentMarketAccessHandler _marketAccessHandler;
  final FeatureToggleProvider _featureToggleProvider;
  final DiscoveryPageRefreshMediator _refreshMediator;
  final FeatureEligibilityProvider _featureEligibilityProvider;

  final Logger _logger;

  OnboardingBannersV2Cubit({
    required Logger logger,
    required NavigationProvider navigationProvider,
    required DiscoveryAnalytics analytics,
    required WealthManagementInteractor wealthManagementInteractor,
    required WealthManagementStarterHandler wealthManagementStarterHandler,
    required BrokerUserInteractor userInteractor,
    required InstrumentMarketAccessHandler marketAccessHandler,
    required FeatureToggleProvider featureToggleProvider,
    required DiscoveryPageRefreshMediator refreshMediator,
    required FeatureEligibilityProvider featureEligibilityProvider,
  })  : _logger = logger,
        _navigationProvider = navigationProvider,
        _analytics = analytics,
        _wealthManagementInteractor = wealthManagementInteractor,
        _wealthManagementStarterHandler = wealthManagementStarterHandler,
        _userInteractor = userInteractor,
        _marketAccessHandler = marketAccessHandler,
        _featureToggleProvider = featureToggleProvider,
        _refreshMediator = refreshMediator,
        _featureEligibilityProvider = featureEligibilityProvider,
        super(
          const OnboardingBannersV2State.fetching(),
        ) {
    _wealthManagementInteractor.managedPortfoliosStream.listenSafe(
      this,
      onData: (data) {
        state.mapOrNull(
          loaded: (state) async {
            final wealthEligible =
                await _featureEligibilityProvider.getFeatureEligibility(
              onboardingType: OnboardingType.wealthManagementUnqualified,
            );
            safeEmit(
              state.copyWith(showWealthBanner: data.isEmpty && wealthEligible),
            );
          },
        );
      },
      onError: (error) {
        _logger.error(
          'Error fetching managed portfolios',
          error: error,
        );
      },
    );
    _refreshMediator.refreshStream.listenSafe(this).onData(
      (action) async {
        await action.map(
          refresh: (action) => initialize(),
        );
      },
    );
    initialize();
  }

  bool get isWealthManagementEnabled => _featureToggleProvider
      .get(WealthManagementFeatureToggles.isWealthManagementEnabled);

  bool get isMarginTradingEnabled => _featureToggleProvider
      .get(MarginTradingFeatureToggles.isMarginTradingEnabled);

  Future<void> initialize() async {
    final requests = await Future.wait([
      _loadUserMarketAccess(),
      _loadComplexInstrumentsStatus(),
    ]);

    final marketAccess = requests[0] as UserMarketAccess;
    final complexInstrumentsStatus = requests[1] as ComplexInstrumentsStatus;

    if (isClosed) return;

    state.mapOrNull(
      loaded: (loaded) {
        safeEmit(
          loaded.copyWith(
            marketAccess: marketAccess,
            complexInstrumentsStatus: complexInstrumentsStatus,
          ),
        );
      },
      fetching: (fetching) => safeEmit(
        OnboardingBannersV2State.loaded(
          marketAccess: marketAccess,
          complexInstrumentsStatus: complexInstrumentsStatus,
        ),
      ),
    );

    if (isWealthManagementEnabled) {
      _wealthManagementInteractor.fetchManagedPortfolios();
    }
  }

  Future<UserMarketAccess> _loadUserMarketAccess() async {
    try {
      final data = await _userInteractor.getUserMarketAccess();
      return data;
    } on Object catch (e) {
      _logger.debug('Error fetching user features $e');
      return const UserMarketAccess(
        usMarketStatus: UserFeatureStatus.unknown,
        uaeMarketStatus: UserFeatureStatus.unknown,
        cryptoMarketStatus: UserFeatureStatus.unknown,
      );
    }
  }

  Future<ComplexInstrumentsStatus> _loadComplexInstrumentsStatus() async {
    try {
      final data = await _userInteractor.getUserComplexInstrumentsData();
      return data.complexInstrumentStatus;
    } on Object catch (e) {
      _logger.debug('Error fetching complex instrument status $e');
      return ComplexInstrumentsStatus.unknown;
    }
  }

  Future<void> unlockUaeCompanies() async {
    await state.mapOrNull(
      loaded: (state) async {
        _analytics.unlockUaeCompanies();
        loading(true);
        await _marketAccessHandler.checkUaeMarketIsActive(state.marketAccess);
        loading(false);
      },
    );
  }

  void onUaeExplorePressed() {
    _analytics.exploreUaeCompanies();

    _navigationProvider.push(
      const UaeInstrumentsListPageNavigationConfig(),
    );
  }

  Future<void> unlockCrypto() async {
    await state.mapOrNull(
      loaded: (state) async {
        _analytics.unlockCrypto();
        loading(true);
        await _marketAccessHandler.checkCryptoMarketIsActive(
          marketAccess: state.marketAccess,
          status: state.complexInstrumentsStatus,
        );
        loading(false);
      },
    );
  }

  Future<void> unlockComplexInstruments() async {
    await state.mapOrNull(
      loaded: (state) async {
        _analytics.unlockComplexInstruments();
        loading(true);
        await _marketAccessHandler.checkComplexInstrumentsIsActive(
          state.complexInstrumentsStatus,
        );
        loading(false);
      },
    );
  }

  Future<void> onWealthMangementBannerPressed() async {
    _analytics.wealthManagementBanner();
    await _wealthManagementStarterHandler.startWealthManagement();
  }

  Future<void> onMarginTradingBannerPressed() async {
    await state.mapOrNull(
      loaded: (state) async {
        _analytics.unlockMarginTrading();
        await _marketAccessHandler.checkMarginTradingIsActive(
          marketAccess: state.marketAccess,
        );
      },
    );
  }

  @override
  String toString() => 'OnboardingBannersV2Cubit';
}
