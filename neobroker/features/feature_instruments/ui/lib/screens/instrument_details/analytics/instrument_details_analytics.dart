// ignore_for_file: constant_identifier_names

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neobroker_models/models.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';

part 'instrument_details_analytics.freezed.dart';

enum InstrumentDetailsAnalyticsTarget {
  instruments,
  trade_instruments,
  buy,
  sell,
  instrument_description,
  chart,
  timeframe,
  news_tab,
  activity_tab,
  overview_tab,
  no_tab,
  delay_icon,
  uae_closed_buy,
  uae_closed_sell,
  add_to_watchlist,
  remove_from_watchlist,
  set_up_recurring_order,
  view_recurring_order,
  view_all_recurring_orders,
  position_error,
  portfolio_tab,
}

@freezed
class InstrumentDetailsEventPayload
    with _$InstrumentDetailsEventPayload
    implements AnalyticsEventPayload {
  const factory InstrumentDetailsEventPayload({
    required String instrumentName,
    required String ticker,
    required InstrumentType instrumentType,
    required Currency currency,
    String? price,
    String? relativePerformance,
    String? timeframe,
    String? portfolioName,
  }) = _InstrumentDetailsEventPayload;

  const factory InstrumentDetailsEventPayload.tradeInstruments({
    required String instrumentName,
    required String instrumentType,
    required String instrumentCurrency,
    required String instrumentSymbol,
    required String instrumentStatus,
    required String instrumentPartnerName,
    required String orderType,
    required String actionType,
    required String portfolioName,
    required String portfolioCurrency,
    required String portfolioPartnerName,
    required List<Portfolio> availablePortfolios,
  }) = _InstrumentDetailsEventPayloadTradeInstruments;

  const factory InstrumentDetailsEventPayload.actionButton({
    required String instrumentName,
    required String instrumentType,
    required String instrumentCurrency,
    required String instrumentSymbol,
    required String instrumentStatus,
    required bool isMarketClosed,
    required bool isMarketOpenWithinLimit,
    required String orderType,
    required List<Portfolio> availablePortfolios,
  }) = _InstrumentDetailsEventPayloadActionButton;

  const factory InstrumentDetailsEventPayload.setUpRecurring({
    required String instrumentName,
    required String ticker,
    required RecurringOrderType type,
    required Currency currency,
  }) = _InstrumentDetailsEventPayloadSetUpRecurring;

  const factory InstrumentDetailsEventPayload.viewRecurring({
    required String instrumentName,
    required String ticker,
    required RecurringOrderType type,
    required Currency currency,
    required RecurringFrequency frequency,
    required DateTime nextInvestmentDate,
    required RecurringAccountDetails account,
    Money? amount,
    Money? totalAmount,
    Money? commission,
    Money? totalCommission,
    Money? vat,
    NumberValue? quantity,
  }) = _InstrumentDetailsEventPayloadViewRecurring;

  const factory InstrumentDetailsEventPayload.positionError({
    required bool isPositionLoaded,
  }) = _InstrumentDetailsEventPayloadPositionError;

  const InstrumentDetailsEventPayload._();

  @override
  Map<String, dynamic> getEventPayload() {
    return map(
      (value) => <String, dynamic>{
        'instrumentName': value.instrumentName,
        'ticker': value.ticker,
        'instrumentType': value.instrumentType.name,
        'currency': value.currency.name,
        'price': value.price,
        'relativePerformance': value.relativePerformance,
        'timeframe': value.timeframe,
        'portfolioName': value.portfolioName,
      },
      actionButton: (value) => <String, dynamic>{
        'instrumentName': value.instrumentName,
        'instrumentType': value.instrumentType,
        'instrumentCurrency': value.instrumentCurrency,
        'instrumentSymbol': value.instrumentSymbol,
        'instrumentStatus': value.instrumentStatus,
        'isMarketClosed': value.isMarketClosed,
        'isMarketOpenWithinLimit': value.isMarketOpenWithinLimit,
        'orderType': value.orderType,
        'availablePortfoliosName':
            value.availablePortfolios.map((e) => e.name).toList(),
        'availablePortfoliosIds':
            value.availablePortfolios.map((e) => e.portfolioId).toList(),
      },
      tradeInstruments: (value) => <String, dynamic>{
        'instrumentName': value.instrumentName,
        'instrumentType': value.instrumentType,
        'instrumentCurrency': value.instrumentCurrency,
        'instrumentSymbol': value.instrumentSymbol,
        'instrumentStatus': value.instrumentStatus,
        'instrumentPartnerName': value.instrumentPartnerName,
        'orderType': value.orderType,
        'actionType': value.actionType,
        'portfolioName': value.portfolioName,
        'portfolioCurrency': value.portfolioCurrency,
        'portfolioPartnerName': value.portfolioPartnerName,
        'availablePortfoliosName':
            value.availablePortfolios.map((e) => e.name).toList(),
        'availablePortfoliosIds':
            value.availablePortfolios.map((e) => e.portfolioId).toList(),
      },
      setUpRecurring: (value) => <String, dynamic>{
        'instrumentName': value.instrumentName,
        'ticker': value.ticker,
        'recurringOrderType': value.type.name,
        'currency': value.currency.name,
      },
      viewRecurring: (value) => <String, dynamic>{
        'instrumentName': value.instrumentName,
        'ticker': value.ticker,
        'recurringOrderType': value.type.name,
        'currency': value.currency.name,
        'frequencyType': value.frequency.frequencyType,
        'frequencyDay': value.frequency.day,
        'nextInvestmentDate': value.nextInvestmentDate,
        'account': value.account.name,
        'amount': value.amount?.toCodeOnRightFormat(),
        'totalAmount': value.totalAmount?.toCodeOnRightFormat(),
        'commission': value.commission?.toCodeOnRightFormat(),
        'totalCommission': value.totalCommission?.toCodeOnRightFormat(),
        'vat': value.vat?.toCodeOnRightFormat(),
        'quantity': value.quantity?.value,
      },
      positionError: (value) => <String, dynamic>{
        'isPositionLoaded': value.isPositionLoaded,
      },
    );
  }
}

InstrumentDetailsAnalyticsTarget? returnHeaderTarget(int index) {
  switch (index) {
    case 0:
      return InstrumentDetailsAnalyticsTarget.overview_tab;
    case 1:
      return InstrumentDetailsAnalyticsTarget.news_tab;
    case 2:
      return InstrumentDetailsAnalyticsTarget.activity_tab;
  }

  return InstrumentDetailsAnalyticsTarget.no_tab;
}

extension RecurringFrequencyExtension on RecurringFrequency {
  String get frequencyType => map(
        weekly: (_) => 'WEEKLY',
        biweekly: (_) => 'BIWEEKLY',
        monthly: (_) => 'MONTHLY',
        quarterly: (_) => 'QUARTERLY',
        unknown: (_) => 'UNKNOWN',
      );

  String get day => map(
        weekly: (value) => value.dayOfTheWeek.name,
        biweekly: (value) => value.dayOfTheWeek.name,
        monthly: (value) => value.dayOfTheMonth.toString(),
        quarterly: (value) => value.dayOfTheMonth.toString(),
        unknown: (value) => '',
      );
}
