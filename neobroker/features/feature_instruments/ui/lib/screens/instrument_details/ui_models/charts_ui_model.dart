import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neobroker_models/models.dart';
import 'package:neobroker_ui/ui.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
part 'charts_ui_model.freezed.dart';

@freezed
class ChartsUiModel with _$ChartsUiModel {
  const factory ChartsUiModel({
    @Default(<Chart>[]) List<Chart> charts,
    @Default('') String period,
    @Default(false) bool isLoadingCharts,
    @Default(false) bool isChartInteracted,
    @Default(LabelModel(text: '')) LabelModel maxChartLabelModel,
    @Default(LabelModel(text: '')) LabelModel minChartLabelModel,
    ChartDataUiModel? chartModel,
    Chart? selectedChart,
    Map<String, String>? segments,
    @Default(true) bool showAnimation,
  }) = _ChartsUiModel;
  const ChartsUiModel._();
}
