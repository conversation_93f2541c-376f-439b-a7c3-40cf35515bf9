import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neobroker_models/models.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

part 'instruments_feature_navigation_config.freezed.dart';

@freezed
class InstrumentsFeatureNavigationConfig extends FeatureNavigationConfig
    with _$InstrumentsFeatureNavigationConfig {
  const factory InstrumentsFeatureNavigationConfig({
    @Default(InstrumentsFeatureNavigationConfig.discoveryScreen)
    String initialScreen,
    String? instrumentId,
    Instrument? instrument,
    @Default(false) bool isDiscoveryRefactorEnabled,
  }) = _InstrumentsFeatureNavigationConfig;

  static const name = 'instruments_feature';

  static const discoveryScreen = 'discovery_view';
  static const collectionsScreen = 'collections_screen';
  static const instrumentDetailsScreen = 'instrument_details_screen';
  static const instrumentsListScreen = 'instruments_list_screen';
  static const instrumentsSearchScreen = 'instrumentlist_view';
  static const uaeInstrumentsListScreen = 'uae_instruments_list_screen';

  const InstrumentsFeatureNavigationConfig._() : super(name);

  @override
  String toString() => 'InstrumentsFeatureNavigationConfig';
}

@freezed
class InstrumentsScreenNavigationConfig extends ScreenNavigationConfig
    with _$InstrumentsScreenNavigationConfig {
  static const name = 'instruments_feature';

  static const discoveryScreen = 'discovery_screen';

  const factory InstrumentsScreenNavigationConfig({
    @Default(false) bool isDiscoveryRefactorEnabled,
  }) = _InstrumentsScreenNavigationConfig;

  const InstrumentsScreenNavigationConfig._()
      : super(id: discoveryScreen, feature: name);

  @override
  String toString() => 'InstrumentsScreenNavigationConfig';
}
