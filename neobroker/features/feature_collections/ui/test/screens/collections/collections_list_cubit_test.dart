import 'package:bloc_test/bloc_test.dart';
import 'package:fake_async/fake_async.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';

import 'package:wio_feature_collections_ui/src/screens/collections_screen/cubit/collections_list_cubit.dart';
import 'package:wio_feature_margin_trading_api/feature_toggles/margin_trading_feature_toggles.dart';

import '../../collections_mock.dart';

const _loadDataPeriod300Millis = Duration(milliseconds: 300);

void main() {
  late CollectionsListCubit cubit;

  late MockCollectionsInteractor interactor;
  late MockMarketDataLoader marketDataLoader;
  late MockCommonErrorHandler commonErrorHandler;
  late MockCollectionsAnalytics analytics;
  late MockBrokerUserInteractor userInteractor;
  late MockLogger logger;
  late MockInstrumentMarketAccessHandler marketAccessHandler;
  late MockFeatureToggleProvider featureToggleProvider;

  setUp(() {
    interactor = MockCollectionsInteractor();
    marketDataLoader = MockMarketDataLoader();
    commonErrorHandler = MockCommonErrorHandler();
    analytics = MockCollectionsAnalytics();
    marketAccessHandler = MockInstrumentMarketAccessHandler();
    userInteractor = MockBrokerUserInteractor();
    logger = MockLogger();
    featureToggleProvider = MockFeatureToggleProvider();

    when(
      () => interactor.getCollectionById(
        any(),
      ),
    ).thenAnswer((_) async => mockCollections);

    when(
      () => userInteractor.getUserMarketAccess(),
    ).thenAnswer(
      (_) => Future.value(marketAccess),
    );

    cubit = CollectionsListCubit(
      collectionsInteractor: interactor,
      id: 'id0',
      marketDataLoader: marketDataLoader,
      commonErrorHandler: commonErrorHandler,
      analytics: analytics,
      logger: logger,
      marketAccessHandler: marketAccessHandler,
      userInteractor: userInteractor,
      featureToggleProvider: featureToggleProvider,
    );
  });

  tearDown(() => cubit.close());

  group(
    'onInstrumentPressed test',
    () {
      blocTest<CollectionsListCubit, CollectionsListState>(
        '''
when an instrument is pressed with inactive market status verify logger''',
        build: () => cubit,
        setUp: () {
          fakeAsync((async) => async.elapse(_loadDataPeriod300Millis));
          when(
            () => marketAccessHandler.openInstrument(
              marketAccess: marketAccess,
              instrumentId: any(named: 'instrumentId'),
              currency: any(named: 'currency'),
            ),
          ).justThrowAsync(Exception());
        },
        act: (cubit) {
          cubit.onInstrumentPressed(
            instrument: instrument1,
            title: mockCollections.title,
          );
        },
        verify: (_) {
          verify(
            () => logger.debug('Error opening instrument'),
          ).calledOnce;
        },
      );
    },
  );
  group(
    'on pull to refresh test',
    () {
      blocTest<CollectionsListCubit, CollectionsListState>(
        '''
when screen is pulled to refresh, it should emit correct states''',
        build: () => cubit,
        setUp: () => fakeAsync(
          (async) => async.elapse(_loadDataPeriod300Millis),
        ),
        act: (cubit) {
          cubit.onRefresh();
        },
        expect: () {
          const initialState = CollectionsListState.loading(
            id: 'id0',
          );

          final loadedState = CollectionsListState.idle(
            id: 'id0',
            instruments: mockCollections.instruments,
          );

          return [
            initialState,
            loadedState,
          ];
        },
      );
    },
  );

  group(
    'on pull to refresh test',
    () {
      blocTest<CollectionsListCubit, CollectionsListState>(
        '''
when screen is pulled to refresh with error, it should emit correct states''',
        build: () => cubit,
        setUp: () {
          fakeAsync((async) => async.elapse(_loadDataPeriod300Millis));
          when(
            () => interactor.getCollectionById(
              any(),
            ),
          ).thenThrow(Exception());
        },
        act: (cubit) {
          cubit.onRefresh();
        },
        expect: () {
          const initialState = CollectionsListState.loading(
            id: 'id0',
          );

          const loadedState = CollectionsListState.error(
            id: 'id0',
          );

          return [
            initialState,
            loadedState,
          ];
        },
      );
    },
  );

  group('is margin trading FF enabled test', () {
    test(
      '''when margin trading FF is enabled, the value should be true''',
      () {
        when(
          () => featureToggleProvider
              .get(MarginTradingFeatureToggles.isMarginTradingEnabled),
        ).thenAnswer((_) => true);

        expect(cubit.isMarginTradingEnabled, true);
      },
    );
  });

  group(
    'to string',
    () {
      test(
        'to string method for collections cubit',
        () {
          const cubitName = 'CollectionsListCubit';

          expect(cubit.toString(), cubitName);
        },
      );
    },
  );
}
