import 'package:bloc_test/bloc_test.dart';
import 'package:onboarding_ui/broker_full_onboarding/mediator/onboarding_mediator.dart';
import 'package:onboarding_ui/complex_instruments_tutorial/bottom_sheets/complex_financial_products_bottom_sheet_config.dart';
import 'package:onboarding_ui/complex_instruments_tutorial/cubit/complex_instruments_tutorial_cubit.dart';
import 'package:tests/tests.dart';

import 'package:mocktail/mocktail.dart';
import 'package:tests_ui/mocks/mocks.dart';

import '../mocks.dart';

void main() {
  late MockOnboardingMediator mediator;
  late MockNavigationProvider navigationProvider;
  setUp(() {
    mediator = MockOnboardingMediator();
    navigationProvider = MockNavigationProvider();
    when(
      () => navigationProvider
          .showBottomSheet(const ComplexFinancialProductsBottomSheetConfig()),
    ).justCompleteAsync();
  });

  ComplexInstrumentsTutorialCubit getCubit() => ComplexInstrumentsTutorialCubit(
        mediator: mediator,
        navigationProvider: navigationProvider,
      );

  group('Complex instruments tutorial cubit tests', () {
    blocTest<ComplexInstrumentsTutorialCubit, ComplexInstrumentsTutorialState>(
      '''when on continue called, verify mediator call''',
      build: () => getCubit(),
      setUp: () => when(() => mediator.passAction(
              OnboardingStepAction.completeComplexInstrumentsTutorial()))
          .justComplete(),
      act: (cubit) => cubit.onContinue(),
      verify: (_) {},
    );

    test('complex instruments calls navigation.goBack', () {
      getCubit().onMaybeLaterPressed();

      verify(() => navigationProvider.goBack()).called(1);
    });

    test('complex instruments calls navigation.goBack', () {
      getCubit().onComplexFinancialProductsPressed();

      verify(() => navigationProvider.showBottomSheet(
          const ComplexFinancialProductsBottomSheetConfig())).called(1);
    });
  });

  group(
    'to string',
    () {
      test(
        'to string method for cubit',
        () {
          const cubitName = 'ComplexInstrumentsTutorialCubit';

          final cubit = getCubit();

          expect(cubit.toString(), cubitName);
        },
      );
    },
  );
}
