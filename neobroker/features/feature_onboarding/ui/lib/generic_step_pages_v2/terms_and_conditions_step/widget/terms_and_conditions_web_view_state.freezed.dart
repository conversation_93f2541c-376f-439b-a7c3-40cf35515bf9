// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'terms_and_conditions_web_view_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TermsAndConditionsWebViewState {
  bool get isArabicEmpty => throw _privateConstructorUsedError;
  bool get isEnglish => throw _privateConstructorUsedError;
  String? get url => throw _privateConstructorUsedError;

  /// Create a copy of TermsAndConditionsWebViewState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TermsAndConditionsWebViewStateCopyWith<TermsAndConditionsWebViewState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TermsAndConditionsWebViewStateCopyWith<$Res> {
  factory $TermsAndConditionsWebViewStateCopyWith(
          TermsAndConditionsWebViewState value,
          $Res Function(TermsAndConditionsWebViewState) then) =
      _$TermsAndConditionsWebViewStateCopyWithImpl<$Res,
          TermsAndConditionsWebViewState>;
  @useResult
  $Res call({bool isArabicEmpty, bool isEnglish, String? url});
}

/// @nodoc
class _$TermsAndConditionsWebViewStateCopyWithImpl<$Res,
        $Val extends TermsAndConditionsWebViewState>
    implements $TermsAndConditionsWebViewStateCopyWith<$Res> {
  _$TermsAndConditionsWebViewStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TermsAndConditionsWebViewState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isArabicEmpty = null,
    Object? isEnglish = null,
    Object? url = freezed,
  }) {
    return _then(_value.copyWith(
      isArabicEmpty: null == isArabicEmpty
          ? _value.isArabicEmpty
          : isArabicEmpty // ignore: cast_nullable_to_non_nullable
              as bool,
      isEnglish: null == isEnglish
          ? _value.isEnglish
          : isEnglish // ignore: cast_nullable_to_non_nullable
              as bool,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TermsAndConditionsWebViewStateImplCopyWith<$Res>
    implements $TermsAndConditionsWebViewStateCopyWith<$Res> {
  factory _$$TermsAndConditionsWebViewStateImplCopyWith(
          _$TermsAndConditionsWebViewStateImpl value,
          $Res Function(_$TermsAndConditionsWebViewStateImpl) then) =
      __$$TermsAndConditionsWebViewStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isArabicEmpty, bool isEnglish, String? url});
}

/// @nodoc
class __$$TermsAndConditionsWebViewStateImplCopyWithImpl<$Res>
    extends _$TermsAndConditionsWebViewStateCopyWithImpl<$Res,
        _$TermsAndConditionsWebViewStateImpl>
    implements _$$TermsAndConditionsWebViewStateImplCopyWith<$Res> {
  __$$TermsAndConditionsWebViewStateImplCopyWithImpl(
      _$TermsAndConditionsWebViewStateImpl _value,
      $Res Function(_$TermsAndConditionsWebViewStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of TermsAndConditionsWebViewState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isArabicEmpty = null,
    Object? isEnglish = null,
    Object? url = freezed,
  }) {
    return _then(_$TermsAndConditionsWebViewStateImpl(
      isArabicEmpty: null == isArabicEmpty
          ? _value.isArabicEmpty
          : isArabicEmpty // ignore: cast_nullable_to_non_nullable
              as bool,
      isEnglish: null == isEnglish
          ? _value.isEnglish
          : isEnglish // ignore: cast_nullable_to_non_nullable
              as bool,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$TermsAndConditionsWebViewStateImpl
    implements _TermsAndConditionsWebViewState {
  const _$TermsAndConditionsWebViewStateImpl(
      {required this.isArabicEmpty,
      required this.isEnglish,
      required this.url});

  @override
  final bool isArabicEmpty;
  @override
  final bool isEnglish;
  @override
  final String? url;

  @override
  String toString() {
    return 'TermsAndConditionsWebViewState(isArabicEmpty: $isArabicEmpty, isEnglish: $isEnglish, url: $url)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TermsAndConditionsWebViewStateImpl &&
            (identical(other.isArabicEmpty, isArabicEmpty) ||
                other.isArabicEmpty == isArabicEmpty) &&
            (identical(other.isEnglish, isEnglish) ||
                other.isEnglish == isEnglish) &&
            (identical(other.url, url) || other.url == url));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isArabicEmpty, isEnglish, url);

  /// Create a copy of TermsAndConditionsWebViewState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TermsAndConditionsWebViewStateImplCopyWith<
          _$TermsAndConditionsWebViewStateImpl>
      get copyWith => __$$TermsAndConditionsWebViewStateImplCopyWithImpl<
          _$TermsAndConditionsWebViewStateImpl>(this, _$identity);
}

abstract class _TermsAndConditionsWebViewState
    implements TermsAndConditionsWebViewState {
  const factory _TermsAndConditionsWebViewState(
      {required final bool isArabicEmpty,
      required final bool isEnglish,
      required final String? url}) = _$TermsAndConditionsWebViewStateImpl;

  @override
  bool get isArabicEmpty;
  @override
  bool get isEnglish;
  @override
  String? get url;

  /// Create a copy of TermsAndConditionsWebViewState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TermsAndConditionsWebViewStateImplCopyWith<
          _$TermsAndConditionsWebViewStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
