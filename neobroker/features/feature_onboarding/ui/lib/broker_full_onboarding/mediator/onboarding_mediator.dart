import 'dart:async';

import 'package:feature_neobroker_onboarding_api/index.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'onboarding_mediator.freezed.dart';

abstract class OnboardingMediator {
  Stream<OnboardingStepAction> get stepActionStream;

  void passAction(OnboardingStepAction action);
}

class OnboardingMediatorImpl implements OnboardingMediator {
  final _actionController = StreamController<OnboardingStepAction>.broadcast();

  @override
  Stream<OnboardingStepAction> get stepActionStream => _actionController.stream;

  void dispose() {
    _actionController.close();
  }

  @override
  void passAction(OnboardingStepAction action) {
    _actionController.sink.add(action);
  }
}

@freezed
class OnboardingStepAction with _$OnboardingStepAction {
  const factory OnboardingStepAction.completeUsInit() = _CompleteUsInit;

  const factory OnboardingStepAction.completeUs() = _CompleteUs;

  const factory OnboardingStepAction.completeCrypto() = _CompleteCrypto;

  const factory OnboardingStepAction.completeComplexInstruments() =
      _CompleteComplexInstruments;

  const factory OnboardingStepAction.completeUae() = _CompleteUae;

  const factory OnboardingStepAction.completeMarginTrading() =
      _CompleteMarginTrading;

  const factory OnboardingStepAction.continueOtherOnboarding({
    required Set<OnboardingType> onboardingTypes,
  }) = _ContinueOtherOnboarding;

  const factory OnboardingStepAction.completeCryptoTutorial() =
      _CompleteCryptoTutorial;

  const factory OnboardingStepAction.completeComplexInstrumentsTutorial() =
      _CompleteComplexInstrumentsTutorial;

  const factory OnboardingStepAction.completeMarginTradingTutorial() =
      _CompleteMarginTradingTutorial;

  const factory OnboardingStepAction.completeWealthManagement() =
      _CompleteWealthManagement;

  const factory OnboardingStepAction.completeWealthManagementUnqualified() =
      _CompleteWealthManagementUnqualified;
}
