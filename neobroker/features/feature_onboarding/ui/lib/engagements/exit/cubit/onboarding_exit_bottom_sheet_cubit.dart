import 'package:wio_app_core_api/index.dart';
import 'package:onboarding_ui/engagements/analytics/onboarding_engagements_analytics.dart';
import 'package:onboarding_ui/navigation/onboarding_exit_bottom_sheet_config.dart';
import 'package:onboarding_ui/navigation/onboarding_exit_reason_bottom_sheet_config.dart';
import 'package:ui/ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';

class OnboardingExitBottomSheetCubit extends BaseCubit<Object?> {
  final AnalyticsEventTracker _analyticsTracker;
  final NavigationProvider _navigationProvider;
  final String _currentStepIndex;
  final String _currentScreen;

  OnboardingExitBottomSheetCubit({
    required AnalyticsTrackerFactory analyticsTrackerFactory,
    required NavigationProvider navigationProvider,
    required String currentStepIndex,
    required String currentScreen,
  })  : _analyticsTracker =
            analyticsTrackerFactory.get(OnboardingExitBottomSheetConfig.name),
        _navigationProvider = navigationProvider,
        _currentStepIndex = currentStepIndex,
        _currentScreen = currentScreen,
        super(null);

  Future<void> onExitPressed() async {
    _analyticsTracker.click(
      targetType: AnalyticsTargetType.button,
      target: OnboardingEngagementsAnalyticsTarget.exit,
      payload: OnboardingEngagementsEventPayload.exit(
        currentStepIndex: _currentStepIndex,
        currentScreen: _currentScreen,
      ),
    );

    _navigationProvider.goBack();
    final shouldExit = await _navigationProvider.showBottomSheet(
      OnboardingExitReasonBottomSheetConfig(
        currentStepIndex: _currentStepIndex,
        currentScreen: _currentScreen,
      ),
    );

    if (shouldExit == true) {
      _navigationProvider.goBack(true);
    }
  }

  void onCancelPressed() {
    _analyticsTracker.click(
      targetType: AnalyticsTargetType.button,
      target: OnboardingEngagementsAnalyticsTarget.cancel,
      payload: OnboardingEngagementsEventPayload.exit(
        currentStepIndex: _currentStepIndex,
        currentScreen: _currentScreen,
      ),
    );
    _navigationProvider.goBack(false);
  }

  @override
  String toString() => 'OnboardingExitBottomSheetCubit';
}
