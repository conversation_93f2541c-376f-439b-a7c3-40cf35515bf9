import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:onboarding_ui/broker_full_onboarding/mediator/onboarding_mediator.dart';
import 'package:ui/ui.dart';

part 'margin_trading_tutorial_cubit.freezed.dart';
part 'margin_trading_tutorial_state.dart';

class MarginTradingTutorialCubit extends BaseCubit<MarginTradingTutorialState> {
  final OnboardingMediator _mediator;

  MarginTradingTutorialCubit({
    required OnboardingMediator mediator,
  })  : _mediator = mediator,
        super(
          const MarginTradingTutorialState(),
        );

  void onContinue() {
    _mediator.passAction(OnboardingStepAction.completeMarginTradingTutorial());
  }

  @override
  String toString() => 'MarginTradingTutorialCubit';
}
