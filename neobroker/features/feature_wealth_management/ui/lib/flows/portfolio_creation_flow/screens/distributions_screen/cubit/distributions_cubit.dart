import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neobroker_models/models.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_feature_wealth_management_api/domain/wealth_management_interactor.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/mediator/portfolio_creation_flow_mediator.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/distributions_screen/analytics/distributions_analytics.dart';

part 'distributions_state.dart';
part 'distributions_cubit.freezed.dart';

abstract class BaseDistributionCubit<S extends DistributionsState>
    extends BaseCubit<S> {
  BaseDistributionCubit(super.state);

  Future<void> onDistributionSelected(DistributionOption distribution);

  @override
  String toString() => 'BaseDistributionCubit';
}

class DistributionsChooseCubit
    extends BaseDistributionCubit<DistributionsState> {
  static String screenName = 'distributions_step';

  final PortfolioCreationFlowMediator _mediator;
  final DistributionsAnalytics _analytics;

  DistributionsChooseCubit({
    required PortfolioCreationFlowMediator mediator,
    required DistributionsAnalytics analytics,
  })  : _mediator = mediator,
        _analytics = analytics,
        super(const DistributionsState());

  @override
  String toString() => 'DistributionsChooseCubit';

  @override
  Future<void> onDistributionSelected(
    DistributionOption distribution,
  ) async {
    _analytics.onSelectDistribution(distribution: distribution);
    _mediator.passAction(
      PortfolioCreationMediatorAction.distributionComplete(
        distribution: distribution,
      ),
    );
  }
}

class DistributionEditCubit extends BaseDistributionCubit {
  final NavigationProvider _navigation;
  final WealthManagementInteractor _interactor;
  final String _portfolioId;
  final ToastMessageProvider _toastMessageProvider;
  final CommonLocalizations _commonLocalizations;
  final DistributionsAnalytics _analytics;

  DistributionEditCubit({
    required NavigationProvider navigation,
    required WealthManagementInteractor interactor,
    required ToastMessageProvider toastMessageProvider,
    required String portfolioId,
    required CommonLocalizations commonLocalizations,
    required DistributionsAnalytics analytics,
  })  : _navigation = navigation,
        _interactor = interactor,
        _commonLocalizations = commonLocalizations,
        _toastMessageProvider = toastMessageProvider,
        _analytics = analytics,
        _portfolioId = portfolioId,
        super(const DistributionsState());

  @override
  String toString() => 'DistributionEditCubit';

  @override
  Future<void> onDistributionSelected(
    DistributionOption distribution,
  ) async {
    _analytics.onSelectDistribution(distribution: distribution);

    try {
      await state.mapOrNull((_) async {
        emit(
          const DistributionsState.loading(),
        );

        await _interactor.updateDistributionForManagedPortfolio(
          distributionOption: distribution,
          portfolioId: _portfolioId,
        );

        _navigation.goBack(distribution);
      });
    } on Object catch (_) {
      _toastMessageProvider.showRetailMobileThemedToastMessage(
        NotificationToastMessageConfiguration.error(
          _commonLocalizations.common_error_message,
        ),
      );
    } finally {
      emit(const DistributionsState());
    }
  }
}
