import 'package:accounts_api/index.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:logging_api/logging.dart';
import 'package:neobroker_models/models.dart';
import 'package:ui/ui.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/mediator/portfolio_creation_flow_mediator.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/initial_payment_screen/analytics/initial_payment_analytics.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/source_account_screen/extensions/account_data_extension.dart';

part 'initial_payment_cubit.freezed.dart';
part 'initial_payment_state.dart';

class InitialPaymentCubit extends BaseCubit<InitialPaymentState> {
  final PortfolioCreationFlowMediator _mediator;
  final AccountsInteractor _accountsInteractor;
  final InitialPaymentAnalytics _analytics;
  final Logger _logger;

  InitialPaymentCubit({
    required PortfolioCreationFlowMediator mediator,
    required AccountsInteractor accountsInteractor,
    required InitialPaymentAnalytics analytics,
    required Logger logger,
  })  : _mediator = mediator,
        _accountsInteractor = accountsInteractor,
        _analytics = analytics,
        _logger = logger,
        super(const InitialPaymentState.loading()) {
    _init();
  }

  Future<void> _init() async {
    try {
      final accounts = await _accountsInteractor
          .getAccounts(currency: Currency.usd)
          .disposeBy(this);

      final selectedAccount = accounts.firstWhere(
        (account) =>
            account.type == AccountDataType.current &&
            account.balance.currency == Currency.usd,
      );

      emit(InitialPaymentState.idle(selectedAccount: selectedAccount));
    } on Object catch (e, stackTrace) {
      _logger.error(
        'Failed to load accounts',
        error: e,
        stackTrace: stackTrace,
      );
      emit(
        const InitialPaymentState.error(),
      );
    }
  }

  void retry() {
    emit(const InitialPaymentState.loading());
    _init();
  }

  void onSubmit(Money amount) {
    state.mapOrNull(
      idle: (idle) {
        _analytics.onTapNext(amount: amount);
        _mediator.passAction(
          PortfolioCreationMediatorAction.initialPaymentComplete(
            amount: amount,
            account: idle.selectedAccount.toRecurringAccount(),
          ),
        );
      },
    );
  }

  @override
  String toString() => 'InitialPaymentCubit';
}
