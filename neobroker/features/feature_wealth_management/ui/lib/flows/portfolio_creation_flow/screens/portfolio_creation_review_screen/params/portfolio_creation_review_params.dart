import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neobroker_models/models.dart';
import 'package:uuid/uuid.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_recurring_payments_api/index.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/portfolio_creation_review_screen/cubit/portfolio_creation_review_cubit.dart';
import 'package:wio_feature_wealth_management_ui/screens/wealth_portfolios_list/page/extensions/portfolio_template_extensions.dart';

part 'portfolio_creation_review_params.freezed.dart';

@freezed
class PortfolioCreationReviewParams with _$PortfolioCreationReviewParams {
  const factory PortfolioCreationReviewParams({
    required final PortfolioTemplate template,
    required final Money initialPayment,
    required final RecurringAccountWrapper account,
    DistributionOption? distribution,
    RecurringPaymentWrapper? recurringPayment,
    String? goalName,
    Money? goalAmount,
    WealthGoalImage? goalImage,
  }) = _PortfolioCreationReviewParams;
}

extension PortfolioCreationReviewParamsExtension
    on PortfolioCreationReviewParams {
  ManagedPortfolioCreationModel get portfolioCreationModel {
    const uuid = Uuid();
    final isGoalBased = template.isGoalBased;
    final goal = isGoalBased &&
            goalName != null &&
            goalAmount != null &&
            goalImage != null
        ? WealthGoalCreationModel(
            image: goalImage!.id,
            name: goalName!,
            amount: goalAmount!,
          )
        : null;

    final recurringPayments = <RecurringPaymentCreationModel>[];
    final now = DateTime.now();

    if (recurringPayment != null) {
      recurringPayments.add(
        RecurringPaymentCreationModel(
          idempotencyKey: uuid.v4(),
          amount: recurringPayment!.amount,
          endDate: DateTime(
            now.year + recurringPayment!.yearsToGrow,
            now.month,
            now.day,
            now.hour,
            now.minute,
          ).toUtc(),
          frequency: recurringPayment!.frequency,
        ),
      );
    }

    return ManagedPortfolioCreationModel(
      idempotencyKey: uuid.v4(),
      portfolioTemplateId: template.id,
      accountId: account.id,
      initialPaymentAmount: initialPayment,
      goal: goal,
      recurringPayments: recurringPayments,
      distributionOption: distribution,
    );
  }
}
