import 'package:flutter/material.dart';
import 'package:neobroker_models/models.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_wealth_management_ui/common_widgets/wealth_app_bar.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/cubit/portfolio_creation_flow_cubit.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/distributions_screen/cubit/distributions_cubit.dart';
import 'package:wio_feature_wealth_management_ui/locale/wealth_management_localization.g.dart';
import 'package:wio_feature_wealth_management_ui/screens/edit_goal/screen/edit_goal_screen.dart';

class DistributionsScreen extends StatefulWidget {
  final List<DistributionOption> distributions;
  final DistributionOption? selectedDistribution;

  const DistributionsScreen({
    required this.distributions,
    this.selectedDistribution,
    super.key,
  });

  @override
  State<DistributionsScreen> createState() => _DistributionsScreenState();
}

class _DistributionsScreenState extends State<DistributionsScreen> {
  PortfolioCreationFlowCubit? get portfolioCreationFlowCubit {
    try {
      return context.read<PortfolioCreationFlowCubit>();
    } on Object catch (_) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = context.watch<BaseDistributionCubit>().state;
    final cubit = portfolioCreationFlowCubit;

    late PreferredSizeWidget appBar;

    if (cubit != null) {
      appBar = WealthAppBar(
        onClose: cubit.onClosePressed,
      );
    } else {
      appBar = const TopNavigation(
        TopNavigationModel(state: TopNavigationState.positive),
      );
    }

    return Stack(
      children: [
        Scaffold(
          appBar: appBar,
          body: SafeArea(
            child: _DistributionsContent(
              distributions: widget.distributions,
              selectedDistribution: widget.selectedDistribution,
            ),
          ),
        ),
        Visibility(
          visible: state.isLoading,
          child: const Positioned.fill(
            child: BlurredProgressIndicator(),
          ),
        ),
      ],
    );
  }
}

class _DistributionsContent extends StatelessWidget {
  final List<DistributionOption> distributions;
  final DistributionOption? selectedDistribution;

  const _DistributionsContent({
    required this.distributions,
    this.selectedDistribution,
  });

  @override
  Widget build(BuildContext context) {
    final localization = WealthManagementLocalization.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        children: [
          const SizedBox(height: 24),
          PageText(
            PageTextModel(
              title: localization.wealthDistributionsTitle,
              subtitle: localization.wealthDistributionsSubtitle,
              subtitleMargin: 8,
            ),
          ),
          const SizedBox(height: 24),
          Expanded(
            child: ListView.separated(
              physics: const ClampingScrollPhysics(),
              separatorBuilder: (_, __) => const SizedBox(height: 8),
              itemCount: distributions.length,
              itemBuilder: (_, i) {
                final distribution = distributions[i];
                return ListBox(
                  listBoxModel: ListBoxModel(
                    isBoxed: true,
                    isSelected: selectedDistribution == distribution,
                    textModel: ListBoxTextModel(
                      title: distribution.getName(localization),
                    ),
                  ),
                  onPressed: () => context
                      .read<BaseDistributionCubit>()
                      .onDistributionSelected(distribution),
                );
              },
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
