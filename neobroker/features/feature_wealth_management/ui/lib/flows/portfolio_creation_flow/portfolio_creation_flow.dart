import 'package:di/di.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:neobroker_ui/ui.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_recurring_payments_api/index.dart';
import 'package:wio_common_feature_recurring_payments_ui/screens/recurring_payment_creation/cubit/recurring_payment_creation_cubit.dart';
import 'package:wio_common_feature_recurring_payments_ui/screens/recurring_payment_creation/recurring_payment_creation_screen.dart';
import 'package:wio_common_feature_wealth_management_api/index.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/cubit/portfolio_creation_flow_cubit.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/distributions_screen/cubit/distributions_cubit.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/distributions_screen/distributions_screen.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/goal_images_screen/cubit/goal_images_cubit.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/goal_images_screen/goal_images_screen.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/goal_suggestions_screen/cubit/goal_suggestions_cubit.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/goal_suggestions_screen/goal_suggestions_screen.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/initial_payment_screen/cubit/initial_payment_cubit.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/initial_payment_screen/initial_payment_screen.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/portfolio_creation_goal_screen/cubit/portfolio_creation_goal_cubit.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/portfolio_creation_goal_screen/portfolio_creation_goal_screen.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/portfolio_creation_name_screen/cubit/portfolio_creation_name_cubit.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/portfolio_creation_name_screen/portfolio_creation_name_screen.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/portfolio_creation_review_screen/cubit/portfolio_creation_review_cubit.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/portfolio_creation_review_screen/params/portfolio_creation_review_params.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/portfolio_creation_review_screen/portfolio_creation_review_screen.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/portfolio_creation_tncs_screen/cubit/portfolio_creation_tncs_cubit.dart';
import 'package:wio_feature_wealth_management_ui/flows/portfolio_creation_flow/screens/portfolio_creation_tncs_screen/portfolio_creation_tncs_screen.dart';
import 'package:wio_feature_wealth_management_ui/locale/wealth_management_localization.g.dart';

class PortfolioCreationFlow extends StatefulWidget {
  const PortfolioCreationFlow({super.key});

  @override
  State<PortfolioCreationFlow> createState() => _PortfolioCreationFlowState();
}

class _PortfolioCreationFlowState extends State<PortfolioCreationFlow> {
  final _navKey = GlobalKey<NavigatorState>();

  PortfolioCreationFlowCubit get _cubit =>
      context.read<PortfolioCreationFlowCubit>();

  @override
  Widget build(BuildContext context) {
    final localization = WealthManagementLocalization.of(context);
    return BlocBuilder<PortfolioCreationFlowCubit, PortfolioCreationFlowState>(
      builder: (context, state) => NavigatorPopHandler(
        // ignore: deprecated_member_use
        onPop: () => _navKey.currentState!.pop(),
        child: Navigator(
          key: _navKey,
          // ignore: deprecated_member_use
          onPopPage: (route, result) =>
              _onPopPage(context, state, route, result),
          pages: state.steps
              .map(
                (e) => e
                    .map(
                      goalSuggestionStep: (_) => const GoalSuggestionsScreen()
                          .withBloc<GoalSuggestionsCubit,
                              GoalSuggestionsState>(),
                      goalImageStep: (_) => BlocProvider<BaseGoalImagesCubit>(
                        create: (_) =>
                            DependencyProvider.get<GoalImagesCubit>(),
                        child: const GoalImagesScreen(),
                      ),
                      goalNameStep: (_) => const PortfolioCreationNameScreen()
                          .withBloc<PortfolioCreationNameCubit,
                              PortfolioCreationNameState>(),
                      goalAmountStep: (e) => PortfolioCreationGoalScreen(
                        title: e.goalImage.title,
                        validation: e.template.validation.goalValidationLimits,
                      ).withBloc<PortfolioCreationGoalCubit,
                          PortfolioCreationGoalState>(),
                      initialPayment: (e) => InitialPaymentScreen(
                        goal: e.goalAmount,
                        validation: e
                            .template.validation.initialDepositValidationLimits,
                      ).withBloc<InitialPaymentCubit, InitialPaymentState>(),
                      recurringPayment: (e) =>
                          const RecurringPaymentCreationScreen(
                        isClosable: true,
                      ).withParamsBloc<
                              RecurringPaymentCreationCubit,
                              RecurringPaymentCreationState,
                              String,
                              RecurringPaymentCreationParams>(
                        param1: WealthManagementFeatureNavigationConfig.name,
                        param2: RecurringPaymentCreationParams(
                          currency: Currency.usd,
                          validation: e.template.validation
                              .subsequentDepositValidationLimits,
                          showDuration: true,
                          canSkip: true,
                          pageTitle: localization.wealthRecurringScreenTitle,
                          valueType: RecurringValueType.amount,
                          enableDecimals: true,
                        ),
                      ),
                      distribution: (e) => BlocProvider<
                          BaseDistributionCubit<DistributionsState>>(
                        create: (_) =>
                            DependencyProvider.get<DistributionsChooseCubit>(),
                        child: DistributionsScreen(
                          distributions: e.template.distributionOptions,
                        ),
                      ),
                      reviewPlan: (e) => const PortfolioCreationReviewScreen()
                          .withParamsBloc<
                              PortfolioCreationReviewCubit,
                              PortfolioCreationReviewState,
                              PortfolioCreationReviewParams,
                              void>(
                        param1: PortfolioCreationReviewParams(
                          goalName: e.goalName,
                          goalAmount: e.goalAmount,
                          goalImage: e.goalImage,
                          template: e.template,
                          initialPayment: e.initialPayment,
                          account: e.account,
                          recurringPayment: e.recurringPayment,
                          distribution: e.distribution,
                        ),
                      ),
                      tncs: (e) => PortfolioCreationTncsScreen(
                        params: PortfolioCreationReviewParams(
                          goalName: e.goalName,
                          goalAmount: e.goalAmount,
                          goalImage: e.goalImage,
                          template: e.template,
                          initialPayment: e.initialPayment,
                          account: e.account,
                          recurringPayment: e.recurringPayment,
                          distribution: e.distribution,
                        ),
                      ).withBloc<PortfolioCreationTncsCubit,
                          PortfolioCreationTncsState>(),
                    )
                    .toPage(),
              )
              .toList(),
        ),
      ),
    );
  }

  bool _onPopPage(
    BuildContext context,
    PortfolioCreationFlowState state,
    Route<Object?> route,
    Object? result,
  ) {
    if (route.settings is Page) {
      _cubit.onPopScreen();

      if (state.steps.length > 1) {
        return route.didPop(result);
      } else {
        return false;
      }
    } else {
      return route.didPop(result);
    }
  }
}
