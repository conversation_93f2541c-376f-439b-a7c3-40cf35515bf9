// ignore_for_file: constant_identifier_names

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neobroker_models/models.dart';
import 'package:wio_app_core_api/index.dart';

part 'managed_portfolio_positions_analytics.freezed.dart';

enum ManagedPortfolioPositionsAnalyticsTarget {
  managed_portfolio,
  sell,
  invest,
  restriction,
}

@freezed
class ManagedPortfolioPositionsAnalyticsEventPayload
    with _$ManagedPortfolioPositionsAnalyticsEventPayload
    implements AnalyticsEventPayload {
  const ManagedPortfolioPositionsAnalyticsEventPayload._();

  const factory ManagedPortfolioPositionsAnalyticsEventPayload.portfolio({
    required String id,
    required String portfolioId,
    required String templateId,
    required String templateName,
    required String templateFamily,
    required String templateCategory,
    required String riskLevel,
    required String totalInvestAmount,
    required String totalWithdrawalAmount,
    String? goalName,
    String? goalAmount,
  }) = _ManagedPortfolioPositionsAnalyticsEventPayloadPortfolio;

  const factory ManagedPortfolioPositionsAnalyticsEventPayload.restriction({
    required String operation,
    required String messageType,
    required String title,
    String? text,
  }) = _ManagedPortfolioPositionsAnalyticsEventPayloadRestriction;

  @override
  Map<String, dynamic> getEventPayload() => map(
        portfolio: (e) => {
          'id': e.id,
          'portfolioId': e.portfolioId,
          'templateId': e.templateId,
          'templateName': e.templateName,
          'templateFamily': e.templateFamily,
          'templateCategory': e.templateCategory,
          'riskLevel': e.riskLevel,
          'totalInvestAmount': e.totalInvestAmount,
          'totalWithdrawalAmount': e.totalWithdrawalAmount,
          'goalName': e.goalName,
          'goalAmount': e.goalAmount,
        },
        restriction: (e) => {
          'operation': e.operation,
          'messageType': e.messageType,
          'title': e.title,
          'text': e.text,
        },
      );
}

class ManagedPortfolioPositionsAnalytics {
  final AnalyticsEventTracker _tracker;

  ManagedPortfolioPositionsAnalytics({
    required AnalyticsAbstractTrackerFactory analyticsFactory,
  }) : _tracker = analyticsFactory.get(
          screenName: 'managed_portfolio_positions',
          tracker: AnalyticsTracker.mixpanel,
        );

  void onTapManagedPortfolio({
    required ManagedPortfolio portfolio,
  }) {
    _tracker.click(
      targetType: AnalyticsTargetType.list,
      target: ManagedPortfolioPositionsAnalyticsTarget.managed_portfolio,
      payload: portfolio.toPortfolioPayload,
    );
  }

  void onTapSell({
    required ManagedPortfolio portfolio,
  }) {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: ManagedPortfolioPositionsAnalyticsTarget.sell,
      payload: portfolio.toPortfolioPayload,
    );
  }

  void onTapInvest({
    required ManagedPortfolio portfolio,
  }) {
    _tracker.click(
      targetType: AnalyticsTargetType.button,
      target: ManagedPortfolioPositionsAnalyticsTarget.invest,
      payload: portfolio.toPortfolioPayload,
    );
  }

  void onRestrictionShown({
    required RecurringMessageType messageType,
    required PortfolioTemplateValidationRestrictionOperation operation,
    required String title,
    String? text,
  }) {
    _tracker.view(
      targetType: AnalyticsTargetType.bottom_sheet,
      target: ManagedPortfolioPositionsAnalyticsTarget.restriction,
      payload: ManagedPortfolioPositionsAnalyticsEventPayload.restriction(
        operation: operation.name,
        messageType: messageType.name,
        title: title,
        text: text,
      ),
    );
  }
}

extension on ManagedPortfolio {
  ManagedPortfolioPositionsAnalyticsEventPayload get toPortfolioPayload {
    final template = portfolioTemplate;

    return ManagedPortfolioPositionsAnalyticsEventPayload.portfolio(
      id: id,
      portfolioId: portfolioId,
      templateId: template.id,
      templateName: template.name,
      templateFamily: template.family.name,
      templateCategory: template.category.name,
      riskLevel: template.riskLevel.name,
      totalInvestAmount: totalInvestAmount.toCodeOnRightFormat(),
      totalWithdrawalAmount: totalWithdrawalAmount.toCodeOnRightFormat(),
      goalName: goal?.name,
      goalAmount: goal?.amount.toCodeOnRightFormat(),
    );
  }
}
