// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'edit_distribution_navigation_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$EditDistributionScreenNavigationConfig {
  String get portfolioId => throw _privateConstructorUsedError;
  DistributionOption get selectedDistributionOption =>
      throw _privateConstructorUsedError;

  /// Create a copy of EditDistributionScreenNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EditDistributionScreenNavigationConfigCopyWith<
          EditDistributionScreenNavigationConfig>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EditDistributionScreenNavigationConfigCopyWith<$Res> {
  factory $EditDistributionScreenNavigationConfigCopyWith(
          EditDistributionScreenNavigationConfig value,
          $Res Function(EditDistributionScreenNavigationConfig) then) =
      _$EditDistributionScreenNavigationConfigCopyWithImpl<$Res,
          EditDistributionScreenNavigationConfig>;
  @useResult
  $Res call(
      {String portfolioId, DistributionOption selectedDistributionOption});
}

/// @nodoc
class _$EditDistributionScreenNavigationConfigCopyWithImpl<$Res,
        $Val extends EditDistributionScreenNavigationConfig>
    implements $EditDistributionScreenNavigationConfigCopyWith<$Res> {
  _$EditDistributionScreenNavigationConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EditDistributionScreenNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? portfolioId = null,
    Object? selectedDistributionOption = null,
  }) {
    return _then(_value.copyWith(
      portfolioId: null == portfolioId
          ? _value.portfolioId
          : portfolioId // ignore: cast_nullable_to_non_nullable
              as String,
      selectedDistributionOption: null == selectedDistributionOption
          ? _value.selectedDistributionOption
          : selectedDistributionOption // ignore: cast_nullable_to_non_nullable
              as DistributionOption,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EditDistributionScreenNavigationConfigImplCopyWith<$Res>
    implements $EditDistributionScreenNavigationConfigCopyWith<$Res> {
  factory _$$EditDistributionScreenNavigationConfigImplCopyWith(
          _$EditDistributionScreenNavigationConfigImpl value,
          $Res Function(_$EditDistributionScreenNavigationConfigImpl) then) =
      __$$EditDistributionScreenNavigationConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String portfolioId, DistributionOption selectedDistributionOption});
}

/// @nodoc
class __$$EditDistributionScreenNavigationConfigImplCopyWithImpl<$Res>
    extends _$EditDistributionScreenNavigationConfigCopyWithImpl<$Res,
        _$EditDistributionScreenNavigationConfigImpl>
    implements _$$EditDistributionScreenNavigationConfigImplCopyWith<$Res> {
  __$$EditDistributionScreenNavigationConfigImplCopyWithImpl(
      _$EditDistributionScreenNavigationConfigImpl _value,
      $Res Function(_$EditDistributionScreenNavigationConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of EditDistributionScreenNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? portfolioId = null,
    Object? selectedDistributionOption = null,
  }) {
    return _then(_$EditDistributionScreenNavigationConfigImpl(
      portfolioId: null == portfolioId
          ? _value.portfolioId
          : portfolioId // ignore: cast_nullable_to_non_nullable
              as String,
      selectedDistributionOption: null == selectedDistributionOption
          ? _value.selectedDistributionOption
          : selectedDistributionOption // ignore: cast_nullable_to_non_nullable
              as DistributionOption,
    ));
  }
}

/// @nodoc

class _$EditDistributionScreenNavigationConfigImpl
    extends _EditDistributionScreenNavigationConfig {
  const _$EditDistributionScreenNavigationConfigImpl(
      {required this.portfolioId, required this.selectedDistributionOption})
      : super._();

  @override
  final String portfolioId;
  @override
  final DistributionOption selectedDistributionOption;

  /// Create a copy of EditDistributionScreenNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EditDistributionScreenNavigationConfigImplCopyWith<
          _$EditDistributionScreenNavigationConfigImpl>
      get copyWith =>
          __$$EditDistributionScreenNavigationConfigImplCopyWithImpl<
              _$EditDistributionScreenNavigationConfigImpl>(this, _$identity);
}

abstract class _EditDistributionScreenNavigationConfig
    extends EditDistributionScreenNavigationConfig {
  const factory _EditDistributionScreenNavigationConfig(
          {required final String portfolioId,
          required final DistributionOption selectedDistributionOption}) =
      _$EditDistributionScreenNavigationConfigImpl;
  const _EditDistributionScreenNavigationConfig._() : super._();

  @override
  String get portfolioId;
  @override
  DistributionOption get selectedDistributionOption;

  /// Create a copy of EditDistributionScreenNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EditDistributionScreenNavigationConfigImplCopyWith<
          _$EditDistributionScreenNavigationConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}
