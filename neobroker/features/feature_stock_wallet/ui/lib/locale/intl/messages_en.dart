// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.
// @dart=2.12
// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = MessageLookup();

typedef String? MessageIfAbsent(String? messageStr, List<Object>? args);

class MessageLookup extends MessageLookupByLibrary {
  @override
  String get localeName => 'en';

  @override
  final Map<String, dynamic> messages =
      _notInlinedMessages(_notInlinedMessages);

  static Map<String, dynamic> _notInlinedMessages(_) => {
        'addFunds': MessageLookupByLibrary.simpleMessage('Add funds'),
        'availableBalanceIsTotalSum': MessageLookupByLibrary.simpleMessage(
            'Available balance is the total sum you can spend on buying stocks.'),
        'availableToInvest':
            MessageLookupByLibrary.simpleMessage('Available to invest'),
        'instantSettlement':
            MessageLookupByLibrary.simpleMessage('What is instant settlement?'),
        'instantSettlementInfo': MessageLookupByLibrary.simpleMessage(
            'When you sell any shares, brokers usually take 2 days to credit your money into your account. With Wio Invest, there’s no waiting time – you can use the money from your sell orders instantly!\n\nSo, you can take the money you earned on a shopping spree right after selling your shares. Isn’t that cool?\n'),
        'instantText': MessageLookupByLibrary.simpleMessage('Instant'),
        'loveIt': MessageLookupByLibrary.simpleMessage('Love it!'),
        'noRequiredAccountsLabel': MessageLookupByLibrary.simpleMessage(
            'You do not have required accounts for making transfers.'),
        'reservedCash': MessageLookupByLibrary.simpleMessage('Reserved cash'),
        'reservedCashInfo': MessageLookupByLibrary.simpleMessage(
            'Reserved cash is the sum of all your pending buy order amounts. You can cancel these orders at any time before market opens and unblock this amount. '),
        'seePendingOrders':
            MessageLookupByLibrary.simpleMessage('See Pending Orders'),
        'somethingWentWrongLongLabel': MessageLookupByLibrary.simpleMessage(
            'Something went wrong, please try again later.'),
        'unsettledCash': MessageLookupByLibrary.simpleMessage('Unsettled cash'),
        'unsettledCashInfo': MessageLookupByLibrary.simpleMessage(
            'This is the money from your recent sell orders that hasn’t been received yet. It usually takes 2 business days to settle the cash from your sell orders. \n\nWith Wio Invest, you can use your unsettled cash instantly to make any purchases.\n'),
        'useUnsettledCash': MessageLookupByLibrary.simpleMessage(
            'Use unsettled cash from your sell orders instantly!'),
        'walletLearnMore': MessageLookupByLibrary.simpleMessage('Learn More'),
        'wealthGotIt': MessageLookupByLibrary.simpleMessage('Got it!'),
        'whyWait': MessageLookupByLibrary.simpleMessage(
            'Why wait to spend your cash?'),
        'withdrawalCash':
            MessageLookupByLibrary.simpleMessage('Withdrawable cash'),
        'withdrawalCashInfo': MessageLookupByLibrary.simpleMessage(
            'This is your USD wallet balance. You can exchange this balance to your AED account instantly.')
      };
}
