import 'package:accounts_api/index.dart';
import 'package:common_feature_fx_api/feature_fx_api.dart';
import 'package:mocktail/mocktail.dart';
import 'package:neobroker_models/models.dart';
import 'package:stock_wallet_ui/screens/wallet/analytics/stock_wallet_analytics.dart';

// // ignore: deprecated_member_use
// class MockBottomSheetProvider extends Mock implements BottomSheetProvider {}

class MockAccountsInteractor extends Mock implements AccountsInteractor {
  MockAccountsInteractor() {
    when(getAccountBalances).thenAnswer(
      (_) => Future.value(accountBalances),
    );
  }
}

class MockFXFlow extends Mock implements FXFlow {}

final accountBalances = AccountBalances(
  freeBalance: const NumberValue(
    sign: NumberValueSign.positive,
    value: '175551.9',
    currency: 'USD',
    type: NumberValueType.money,
  ).toMoney(),
  operativeBalance: const NumberValue(
    sign: NumberValueSign.positive,
    value: '182194.39',
    currency: 'USD',
    type: NumberValueType.money,
  ).toMoney(),
  forwardBalance: const NumberValue(
    sign: NumberValueSign.positive,
    value: '6642.49',
    currency: 'USD',
    type: NumberValueType.money,
  ).toMoney(),
  reservedCash: const NumberValue(
    sign: NumberValueSign.positive,
    value: '1111.49',
    currency: 'USD',
    type: NumberValueType.money,
  ).toMoney(),
);

final balances = AccountBalances(
  freeBalance: const NumberValue(
    sign: NumberValueSign.positive,
    value: '500.9',
    currency: 'USD',
    type: NumberValueType.money,
  ).toMoney(),
  operativeBalance: const NumberValue(
    sign: NumberValueSign.positive,
    value: '600.39',
    currency: 'USD',
    type: NumberValueType.money,
  ).toMoney(),
  forwardBalance: const NumberValue(
    sign: NumberValueSign.positive,
    value: '100.49',
    currency: 'USD',
    type: NumberValueType.money,
  ).toMoney(),
  reservedCash: const NumberValue(
    sign: NumberValueSign.positive,
    value: '100.49',
    currency: 'USD',
    type: NumberValueType.money,
  ).toMoney(),
);

class MockStockWalletAnalytics extends Mock implements StockWalletAnalytics {
  MockStockWalletAnalytics() : super();
}
