import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'accepted_document_dto.g.dart';

@JsonSerializable(explicitToJson: true)
class AcceptedDocumentDto {
  @Json<PERSON>ey(name: 'type', includeIfNull: false)
  final String type;
  @Json<PERSON>ey(name: 'version', includeIfNull: false)
  final String version;
  @Json<PERSON>ey(name: 'accepted', includeIfNull: false)
  final bool accepted;

  AcceptedDocumentDto({
    required this.type,
    required this.version,
    required this.accepted,
  });

  factory AcceptedDocumentDto.fromJson(Map<String, dynamic> json) =>
      _$AcceptedDocumentDtoFromJson(json);

  Map<String, dynamic> toJson() => _$AcceptedDocumentDtoToJson(this);

  @override
  String toString() => jsonEncode(this);
}
