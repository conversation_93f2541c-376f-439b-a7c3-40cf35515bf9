// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sse_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SseEvent {
  String? get eventName => throw _privateConstructorUsedError;
  String? get data => throw _privateConstructorUsedError;

  /// Create a copy of SseEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SseEventCopyWith<SseEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SseEventCopyWith<$Res> {
  factory $SseEventCopyWith(SseEvent value, $Res Function(SseEvent) then) =
      _$SseEventCopyWithImpl<$Res, SseEvent>;
  @useResult
  $Res call({String? eventName, String? data});
}

/// @nodoc
class _$SseEventCopyWithImpl<$Res, $Val extends SseEvent>
    implements $SseEventCopyWith<$Res> {
  _$SseEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SseEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? eventName = freezed,
    Object? data = freezed,
  }) {
    return _then(_value.copyWith(
      eventName: freezed == eventName
          ? _value.eventName
          : eventName // ignore: cast_nullable_to_non_nullable
              as String?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SseEventImplCopyWith<$Res>
    implements $SseEventCopyWith<$Res> {
  factory _$$SseEventImplCopyWith(
          _$SseEventImpl value, $Res Function(_$SseEventImpl) then) =
      __$$SseEventImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? eventName, String? data});
}

/// @nodoc
class __$$SseEventImplCopyWithImpl<$Res>
    extends _$SseEventCopyWithImpl<$Res, _$SseEventImpl>
    implements _$$SseEventImplCopyWith<$Res> {
  __$$SseEventImplCopyWithImpl(
      _$SseEventImpl _value, $Res Function(_$SseEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of SseEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? eventName = freezed,
    Object? data = freezed,
  }) {
    return _then(_$SseEventImpl(
      eventName: freezed == eventName
          ? _value.eventName
          : eventName // ignore: cast_nullable_to_non_nullable
              as String?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$SseEventImpl implements _SseEvent {
  const _$SseEventImpl({required this.eventName, required this.data});

  @override
  final String? eventName;
  @override
  final String? data;

  @override
  String toString() {
    return 'SseEvent(eventName: $eventName, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SseEventImpl &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.data, data) || other.data == data));
  }

  @override
  int get hashCode => Object.hash(runtimeType, eventName, data);

  /// Create a copy of SseEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SseEventImplCopyWith<_$SseEventImpl> get copyWith =>
      __$$SseEventImplCopyWithImpl<_$SseEventImpl>(this, _$identity);
}

abstract class _SseEvent implements SseEvent {
  const factory _SseEvent(
      {required final String? eventName,
      required final String? data}) = _$SseEventImpl;

  @override
  String? get eventName;
  @override
  String? get data;

  /// Create a copy of SseEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SseEventImplCopyWith<_$SseEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
