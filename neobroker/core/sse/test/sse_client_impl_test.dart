// sse_client_impl_test.dart

import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:neobroker_sse/index.dart';

// -------------------
// Mock Classes
// -------------------
class MockDio extends Mock implements Dio {}

class MockResponseBody extends Mock implements ResponseBody {}

void main() {
  late MockDio mockDio;
  late SseClientImpl sseClient;

  setUpAll(() {
    // Register fallback values so mocktail can handle them
    registerFallbackValue(RequestOptions());
    registerFallbackValue(Options());
  });

  setUp(() {
    mockDio = MockDio();
    sseClient = SseClientImpl(dio: mockDio);
  });

  tearDown(() async {
    // Ensure we always disconnect between tests
    await sseClient.disconnect();
  });

  test('connect - successfully receives SSE events', () async {
    // 1. Prepare a fake SSE stream
    final dataLines = [
      // First event
      utf8.encode('event: testEvent\n'),
      utf8.encode('data: Hello SSE\n'),
      utf8.encode('\n'), // signals end of this event

      // Second event (no event name, just data)
      utf8.encode('data: Another message\n'),
      utf8.encode('\n'),

      // Just to show we can handle multiple lines of data in one event
      utf8.encode('event: multiline\n'),
      utf8.encode('data: line1\n'),
      utf8.encode('data: line2\n'),
      utf8.encode('\n'),
    ];

    final fakeResponseBody = ResponseBody(
      Stream.fromIterable(dataLines),
      200,
      headers: {},
    );

    // 2. Mock Dio to return the above response
    when(
      () => mockDio.get<ResponseBody>(
        any(),
        options: any(named: 'options'),
        cancelToken: any(named: 'cancelToken'),
      ),
    ).thenAnswer(
      (_) async => Response<ResponseBody>(
        data: fakeResponseBody,
        statusCode: 200,
        requestOptions: RequestOptions(path: 'test_path'),
      ),
    );

    // 3. Connect SSE client
    final stream = await sseClient.connect('/test-sse');

    final receivedEvents = <SseEvent>[];
    final subscription = stream.listen((e) {
      receivedEvents.add(e);
    });

    // We need to wait for the stream to complete
    await Future<void>.delayed(const Duration(milliseconds: 100));

    // 4. Check the parsed events
    expect(receivedEvents.length, equals(3));
    expect(receivedEvents[0].eventName, equals('testEvent'));
    expect(receivedEvents[0].data, equals('Hello SSE'));

    expect(receivedEvents[1].eventName, isNull);
    expect(receivedEvents[1].data, equals('Another message'));

    expect(receivedEvents[2].eventName, equals('multiline'));
    expect(receivedEvents[2].data, equals('line1\nline2'));

    await subscription.cancel();
  });

  test('connect - onError triggers reconnection when reconnect is true',
      () async {
    // 1. Mock an initial failure
    when(() => mockDio.get<ResponseBody>(
          any(),
          options: any(named: 'options'),
          cancelToken: any(named: 'cancelToken'),
        )).thenThrow(
      DioException(
        requestOptions: RequestOptions(path: '/test-sse'),
      ),
    );

    // 2. Connect with reconnect set to true
    final stream = await sseClient.connect(
      '/test-sse',
      reconnect: true,
      reconnectDelay: const Duration(milliseconds: 100),
    );

    // 3. Capture errors
    final errors = <Object>[];
    final sub = stream.handleError(errors.add).listen((_) {});

    // 4. Wait enough time for reconnect attempt to be scheduled
    await Future<void>.delayed(const Duration(milliseconds: 200));

    expect(errors.length, 1);

    verify(
      () => mockDio.get<ResponseBody>(
        any(),
        options: any(named: 'options'),
        cancelToken: any(named: 'cancelToken'),
      ),
    ).called(greaterThanOrEqualTo(2));

    await sub.cancel();
  });

  test('disconnect - cancels existing subscription', () async {
    // 1. Prepare a never-ending stream
    final fakeResponseBody = ResponseBody(
      Stream.periodic(const Duration(milliseconds: 10), (count) {
        return utf8.encode('data: msg$count\n\n');
      }),
      200,
      headers: {},
    );

    when(() => mockDio.get<ResponseBody>(
          any(),
          options: any(named: 'options'),
          cancelToken: any(named: 'cancelToken'),
        )).thenAnswer((_) async {
      return Response<ResponseBody>(
        data: fakeResponseBody,
        statusCode: 200,
        requestOptions: RequestOptions(path: 'test_path'),
      );
    });

    // 2. Connect
    final sseStream = await sseClient.connect('/test');
    final subscription = sseStream.listen((_) {});

    // 3. Disconnect
    await sseClient.disconnect();

    expect(subscription.isPaused, false);

    await subscription.cancel();
  });

  test('dispose - stops reconnect attempts', () async {
    // 1. We'll mock the first call to throw to trigger reconnect logic
    when(() => mockDio.get<ResponseBody>(
          any(),
          options: any(named: 'options'),
          cancelToken: any(named: 'cancelToken'),
        )).thenThrow(
      DioException(requestOptions: RequestOptions(path: '/test-sse')),
    );

    // 2. Connect with reconnect
    await sseClient.connect(
      '/test-sse',
      reconnect: true,
      reconnectDelay: const Duration(milliseconds: 100),
    );

    // 3. Dispose the client
    await sseClient.dispose();

    // 4. Wait enough time for a reconnect to potentially fire
    await Future<void>.delayed(const Duration(milliseconds: 200));

    verify(
      () => mockDio.get<ResponseBody>(
        any(),
        options: any(named: 'options'),
        cancelToken: any(named: 'cancelToken'),
      ),
    ).called(1);
  });
}
