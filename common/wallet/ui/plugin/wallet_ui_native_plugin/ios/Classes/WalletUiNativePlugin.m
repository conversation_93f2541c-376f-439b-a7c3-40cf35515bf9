#import "WalletUiNativePlugin.h"
#if __has_include(<wallet_ui_native_plugin/wallet_ui_native_plugin-Swift.h>)
#import <wallet_ui_native_plugin/wallet_ui_native_plugin-Swift.h>
#else
// Support project import fallback if the generated compatibility header
// is not copied when this plugin is created as a library.
// https://forums.swift.org/t/swift-static-libraries-dont-copy-generated-objective-c-header/19816
#import "wallet_ui_native_plugin-Swift.h"
#endif

@implementation WalletUiNativePlugin
+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
  [SwiftWalletUiNativePlugin registerWithRegistrar:registrar];
}
@end
