import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

part 'force_upgrade_feature_navigation_config.freezed.dart';

@freezed
class ForceUpgradeFeatureNavigationConfig extends FeatureNavigationConfig
    with _$ForceUpgradeFeatureNavigationConfig {
  static const name = 'force_upgrade_feature';

  const factory ForceUpgradeFeatureNavigationConfig() =
      _ForceUpgradeFeatureNavigationConfig;

  const ForceUpgradeFeatureNavigationConfig._() : super(name);

  @override
  String toString() => 'ForceUpgradeFeatureNavigationConfig';
}
