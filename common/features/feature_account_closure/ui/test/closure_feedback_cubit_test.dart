import 'package:bloc_test/bloc_test.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_account_closure_api/domain/account_closure_interactor.dart';
import 'package:wio_feature_account_closure_ui/bottom_sheet/cubit/closure_feedback_cubit.dart';
import 'package:wio_feature_account_closure_ui/bottom_sheet/cubit/closure_feedback_state.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_error_domain_api/handlers/common_error_handler.dart';

import 'feature_account_closure_mocks.dart';

void main() {
  const closureReason = 'reason';
  const closureFeedback = 'feedback';
  late AccountClosureInteractor accountClosureInteractor;
  late NavigationProvider navigationProvider;
  late Logger logger;
  late CommonErrorHandler commonErrorHandler;
  late ToastMessageProvider toastMessageProvider;

  setUpAll(() {
    accountClosureInteractor = MockAccountClosureInteractor();
    navigationProvider = MockNavigationProvider();
    logger = MockLogger();
    commonErrorHandler = MockCommonErrorHandler();
    toastMessageProvider = MockToastMessageProvider();
  });

  blocTest<ClosureFeedbackCubit, ClosureFeedbackState>(
    'Feedback and reason submitted and got successful',
    build: () => ClosureFeedbackCubit(
      closureReason: closureReason,
      accountClosureInteractor: accountClosureInteractor,
      navigationProvider: navigationProvider,
      logger: logger,
      commonErrorHandler: commonErrorHandler,
      toastMessageProvider: toastMessageProvider,
    ),
    setUp: () {
      when(
        () => accountClosureInteractor.submitFeedback(
          reason: closureReason,
          feedback: anyNamed('feedback'),
        ),
      ).justCompleteAsync();
    },
    act: (c) => c.submit(feedback: closureFeedback),
    expect: () => [
      const ClosureFeedbackState.submitting(),
      const ClosureFeedbackState.success(),
    ],
    verify: (_) {
      verify(
        () => accountClosureInteractor.submitFeedback(
          reason: closureReason,
          feedback: closureFeedback,
        ),
      ).calledOnce;
    },
  );

  blocTest<ClosureFeedbackCubit, ClosureFeedbackState>(
    'Failed to submit the feedback',
    build: () => ClosureFeedbackCubit(
      closureReason: closureReason,
      accountClosureInteractor: accountClosureInteractor,
      navigationProvider: navigationProvider,
      logger: logger,
      commonErrorHandler: commonErrorHandler,
      toastMessageProvider: toastMessageProvider,
    ),
    setUp: () {
      when(
        () => accountClosureInteractor.submitFeedback(
          reason: closureReason,
          feedback: anyNamed('feedback'),
        ),
      ).thenThrow(Exception());
      when(() => commonErrorHandler.errorMessage(any())).thenReturn('');
    },
    act: (c) => c.submit(feedback: closureFeedback),
    expect: () => [
      const ClosureFeedbackState.submitting(),
      const ClosureFeedbackState.initial(),
    ],
    verify: (_) {
      verify(
        () => commonErrorHandler.errorMessage(any()),
      ).calledOnce;
      verify(
        () => toastMessageProvider.showRetailMobileThemedToastMessage(
          NotificationToastMessageConfiguration.error(''),
        ),
      ).calledOnce;
    },
  );

  blocTest<ClosureFeedbackCubit, ClosureFeedbackState>(
    'Navigated to Closure Process Information screen',
    build: () => ClosureFeedbackCubit(
      closureReason: closureReason,
      accountClosureInteractor: accountClosureInteractor,
      navigationProvider: navigationProvider,
      logger: logger,
      commonErrorHandler: commonErrorHandler,
      toastMessageProvider: toastMessageProvider,
    ),
    act: (c) => c.navigateToNext(),
    verify: (_) {
      verify(
        () => navigationProvider.goBack(true),
      ).calledOnce;
    },
  );
}
