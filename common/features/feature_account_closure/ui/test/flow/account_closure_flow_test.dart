import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_account_closure_api/feature_account_closure_api.dart';
import 'package:wio_feature_account_closure_ui/account_closure_flow.dart';
import 'package:wio_feature_account_closure_ui/navigation/account_closure_intro_screen_navigation_config.dart';

void main() {
  late AccountClosureFlowRunner flowRunner;
  late NavigationProvider navigationProvider;

  setUp(() {
    navigationProvider = MockNavigationProvider();

    flowRunner = AccountClosureFlowRunnerImpl(
      navigationProvider: navigationProvider,
    );
  });

  test('run should push AccountClosureIntroNavigationConfig', () {
    // Act
    flowRunner.run();

    // Assert
    verify(
      () => navigationProvider.push(
        const AccountClosureIntroNavigationConfig(),
      ),
    );
  });
}
