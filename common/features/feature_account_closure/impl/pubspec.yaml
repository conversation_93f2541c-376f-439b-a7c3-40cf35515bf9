name: wio_feature_account_closure_impl
description: A new Flutter package project.
version: 0.0.1
publish_to: none
environment: 
  sdk: '>=3.6.0 <4.0.0'
dependencies: 
  di: 
    path: ../../../../core/di
  flutter: 
    sdk: flutter
  freezed_annotation: 2.4.4
  json_annotation: 4.9.0
  logging_api: 
    path: ../../../../core/logging/api
  wio_app_core_api: 
    path: ../../../../core/app_core/api
  wio_feature_account_closure_api: 
    path: ../api
  wio_feature_user_api: 
    path: ../../../../common/features/feature_user/api
dev_dependencies: 
  build_runner: 2.4.14
  core_lints: 
    path: ../../../../tooling/core_lints
  flutter_test: 
    sdk: flutter
  freezed: 2.5.7
  json_serializable: 6.9.0
  mocktail: 1.0.4
  swagger_dart_code_generator: 3.0.1
  tests: 
    path: ../../../../core/tests/impl
