import 'package:domain/data/data.dart';
import 'package:wio_feature_cases_api/models/countries/country.dart';
import 'package:wio_feature_cases_api/models/models.dart';
import 'package:wio_feature_cases_api/models/salary/salary_details_list.dart';

/// The tasks interactor can be used to perform operations on tasks such as
/// approval or for fetching tasks
abstract class TasksInteractor {
  /// {@template TasksInteractor.createTask}
  /// The method to create a task
  ///
  /// The [taskCreationModel] contains the metadata and payload for the task
  ///
  /// [TaskCreationModel.payload] type would be used to route the body to
  /// the correct endpoint
  ///
  /// The method will return the case number of the created task
  /// {@endtemplate}
  Future<String?> createTask(TaskCreationModel taskCreationModel);

  /// {@template TasksInteractor.getGlAccounts}
  /// This method will be used to fetch the list of GL accounts
  ///
  /// If the [refresh] flag is set to true or if the cache is empty, the
  /// interactor will fetch the data from the server
  /// {@endtemplate}
  Future<List<GlAccount>> getGlAccounts({
    int offset = 0,
    int limit = 1000,
    bool refresh = false,
  });

  /// {@template TasksInteractor.getGlChannels}
  /// This method will be used to fetch the list of GL accounts
  ///
  /// If the [refresh] flag is set to true or if the cache is empty, the
  /// interactor will fetch the data from the server`
  /// {@endtemplate}
  Future<List<GlChannel>> getGlChannels({
    bool refresh = false,
  });

  /// {@template TasksInteractor.getBackOfficeCases}
  /// This method will be used to fetch the list of back office cases
  ///
  /// The [filter] can be used to filter the cases based on the status
  /// or other criteria. A null filter will return all the cases without any
  /// filtering
  ///
  /// The method will return a list of [BackOfficeCase] and will update the
  /// cache
  /// {@endtemplate}
  @Deprecated('Use the getBackOfficeCasesWithPaginationData')
  Future<List<BackOfficeCase>> getBackOfficeCases({
    BackOfficeCasesFilter? filter,
  });

  /// {@macro TasksRepository.getSalaryCases}
  /// This method will be used to fetch the list of back office salary cases
  Future<List<SalaryCase>> getSalaryTasks();

  /// {@template TasksInteractor.getBackOfficeCases}
  /// This method will be observe a stream of the back office cases data
  ///
  /// The [makerUserGroups] and [checkerUserGroups] are used to filter the
  /// cases based on the user groups
  ///
  /// If the [forceRefresh] flag is set to true, the interactor will fetch the
  /// data from the server
  /// {@endtemplate}
  Stream<Data<List<BackOfficeCase>>> observeCases({
    List<MakerGroup> makerUserGroups = MakerGroup.values,
    List<CheckerGroup> checkerUserGroups = const [],
    bool forceRefresh = false,
  });

  /// {@template TasksInteractor.completeTask}
  /// This method will be used to complete a task
  /// {@endtemplate}
  Future<void> completeTask({
    required String caseId,
    required TaskType taskType,
    required TaskDecision decision,
    required String notes,
    required String revisionVersion,
  });

  /// {@macro TasksInteractor.completeSalaryTask}
  Future<void> completeSalaryTask({
    required String user,
    required String taskId,
    required String status,
  });

  /// {@template TasksInteractor.getCountry}
  /// This method will be used to list and show for birth place selection
  /// {@endtemplate}
  Future<List<Country>> getCountriesOfBirth();

  /// {@template TasksInteractor.updateTask}
  /// This method will be used to update a task
  /// {@endtemplate}
  Future<void> updateTask({
    required String caseId,
    required TaskUpdateModel taskUpdateModel,
  });

  /// {@template TasksInteractor.getSalary}
  /// This method will be used to get salary details
  /// {@endtemplate}
  Future<SalaryDetailsList> getSalary({required String key});

  /// {@template TasksInteractor.getBackOfficeCase}
  /// This method will be used to fetch a single back office case
  /// {@endtemplate}
  Future<BackOfficeCase> getBackOfficeCase({
    required String caseId,
    bool force = false,
  });

  /// {@template TasksInteractor.downloadSalaryExcel}
  /// {@endtemplate}
  Future<void> downloadSalaryFile({required String businessId});

  /// {@template TasksInteractor.getAgentGroups}
  /// This method will be used to fetch the list of agent groups
  /// {@endtemplate}
  Future<List<AgentGroup>> getAgentGroups();

  /// {@template TasksInteractor.getBackOfficeCasesWithPaginationData}
  /// This method will be used to fetch the list of back office cases
  ///
  /// The [filter] can be used to filter the cases based on the status
  /// or other criteria. A null filter will return all the cases without any
  /// filtering
  ///
  /// The method will return a BackofficeCaseTaskListResponse
  /// {@endtemplate}
  Future<BackofficeCaseTaskListResponse> getBackOfficeCasesWithPaginationData({
    BackOfficeCasesFilter? filter,
  });
}
