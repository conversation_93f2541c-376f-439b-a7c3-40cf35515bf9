/// This enum represents the type of task that would be created
enum CaseType {
  /// This task type represents a task that would be created for the purpose of
  /// transferring money from one gl to another gl
  glToGl,

  /// This task type represents a task that would be created for the purpose of
  /// transferring money from a customer to a gl
  customerToGl,

  /// This task type represents a task that would be created for the purpose of
  /// transferring money from one gl to a customer
  glToCustomer,

  /// This task type represents a task that would be created for the purpose of
  /// transferring money from one gl to a customer
  inWardTransferValidation,

  /// This task type represents a task that would be created for the purpose of
  /// wise settlement review
  wiseSettlementReview,

  /// This task is related to salary
  wioPayrollProcess,

  /// Fallback value for when the case type is unknown
  unknown,
}

/// Extension on [CaseType] to provide additional functionality
extension CaseTypeExtension on CaseType {
  /// Returns true if the case type is unknown
  bool get isUnknown => this == CaseType.unknown;
}
