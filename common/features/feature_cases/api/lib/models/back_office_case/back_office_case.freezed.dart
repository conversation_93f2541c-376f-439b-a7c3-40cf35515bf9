// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'back_office_case.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BackOfficeCase {
  /// ID of backoffice case
  String get id => throw _privateConstructorUsedError;

  /// ID of conversation that agent had with customer
  String get conversationId => throw _privateConstructorUsedError;

  /// The name of the process that the case is associated with
  String get processName => throw _privateConstructorUsedError;

  /// The type of the case (e.g. GL to GL, Customer to GL)
  CaseType get caseType => throw _privateConstructorUsedError;

  /// The status of the case (e.g. active, completed)
  BackOfficeCaseStatus get status => throw _privateConstructorUsedError;

  /// Represents the user that created the case
  CaseAuthor get createdBy => throw _privateConstructorUsedError;

  /// The date and time the case was created
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// The date and time the case was last updated
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// The tasks associated with the case
  List<BackOfficeTask> get tasks => throw _privateConstructorUsedError;

  /// Revision Version
  String get revisionVersion => throw _privateConstructorUsedError;

  /// Raw payload
  Map<String, dynamic> get rawPayload => throw _privateConstructorUsedError;

  /// Checkers
  List<Checker> get checkers => throw _privateConstructorUsedError;

  /// List of files attached to a case
  List<BackOfficeCaseAttachment> get attachments =>
      throw _privateConstructorUsedError;

  /// The group of agents currently assigned to this case
  String? get groupName => throw _privateConstructorUsedError;

  /// The status of the ticket in Freshdesk
  BackOfficeCaseTicketStatus? get ticketStatus =>
      throw _privateConstructorUsedError;

  /// User readable name of the case
  String? get title => throw _privateConstructorUsedError;

  /// The individual ID that the case is associated with
  String? get individualId => throw _privateConstructorUsedError;

  /// The business ID that the case is associated with
  String? get businessId => throw _privateConstructorUsedError;

  /// The product type that the case is associated with
  CasesProductType? get productType => throw _privateConstructorUsedError;

  /// The case number that can be used as reference for communication
  String? get caseNumber => throw _privateConstructorUsedError;

  /// The payload of the case
  BackOfficeCasePayload? get payload => throw _privateConstructorUsedError;

  /// The ticket ID in Freshdesk
  String? get ticketId => throw _privateConstructorUsedError;

  /// The priority of the ticket
  BackOfficeCasePriority? get priority => throw _privateConstructorUsedError;

  /// The Jira URL for the case if it exists
  Uri? get jiraUrl => throw _privateConstructorUsedError;

  /// The due by date for the case
  DateTime? get dueBy => throw _privateConstructorUsedError;

  /// Create a copy of BackOfficeCase
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BackOfficeCaseCopyWith<BackOfficeCase> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BackOfficeCaseCopyWith<$Res> {
  factory $BackOfficeCaseCopyWith(
          BackOfficeCase value, $Res Function(BackOfficeCase) then) =
      _$BackOfficeCaseCopyWithImpl<$Res, BackOfficeCase>;
  @useResult
  $Res call(
      {String id,
      String conversationId,
      String processName,
      CaseType caseType,
      BackOfficeCaseStatus status,
      CaseAuthor createdBy,
      DateTime createdAt,
      DateTime updatedAt,
      List<BackOfficeTask> tasks,
      String revisionVersion,
      Map<String, dynamic> rawPayload,
      List<Checker> checkers,
      List<BackOfficeCaseAttachment> attachments,
      String? groupName,
      BackOfficeCaseTicketStatus? ticketStatus,
      String? title,
      String? individualId,
      String? businessId,
      CasesProductType? productType,
      String? caseNumber,
      BackOfficeCasePayload? payload,
      String? ticketId,
      BackOfficeCasePriority? priority,
      Uri? jiraUrl,
      DateTime? dueBy});

  $CaseAuthorCopyWith<$Res> get createdBy;
  $BackOfficeCasePayloadCopyWith<$Res>? get payload;
}

/// @nodoc
class _$BackOfficeCaseCopyWithImpl<$Res, $Val extends BackOfficeCase>
    implements $BackOfficeCaseCopyWith<$Res> {
  _$BackOfficeCaseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BackOfficeCase
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? conversationId = null,
    Object? processName = null,
    Object? caseType = null,
    Object? status = null,
    Object? createdBy = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? tasks = null,
    Object? revisionVersion = null,
    Object? rawPayload = null,
    Object? checkers = null,
    Object? attachments = null,
    Object? groupName = freezed,
    Object? ticketStatus = freezed,
    Object? title = freezed,
    Object? individualId = freezed,
    Object? businessId = freezed,
    Object? productType = freezed,
    Object? caseNumber = freezed,
    Object? payload = freezed,
    Object? ticketId = freezed,
    Object? priority = freezed,
    Object? jiraUrl = freezed,
    Object? dueBy = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      conversationId: null == conversationId
          ? _value.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as String,
      processName: null == processName
          ? _value.processName
          : processName // ignore: cast_nullable_to_non_nullable
              as String,
      caseType: null == caseType
          ? _value.caseType
          : caseType // ignore: cast_nullable_to_non_nullable
              as CaseType,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as BackOfficeCaseStatus,
      createdBy: null == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as CaseAuthor,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      tasks: null == tasks
          ? _value.tasks
          : tasks // ignore: cast_nullable_to_non_nullable
              as List<BackOfficeTask>,
      revisionVersion: null == revisionVersion
          ? _value.revisionVersion
          : revisionVersion // ignore: cast_nullable_to_non_nullable
              as String,
      rawPayload: null == rawPayload
          ? _value.rawPayload
          : rawPayload // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      checkers: null == checkers
          ? _value.checkers
          : checkers // ignore: cast_nullable_to_non_nullable
              as List<Checker>,
      attachments: null == attachments
          ? _value.attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<BackOfficeCaseAttachment>,
      groupName: freezed == groupName
          ? _value.groupName
          : groupName // ignore: cast_nullable_to_non_nullable
              as String?,
      ticketStatus: freezed == ticketStatus
          ? _value.ticketStatus
          : ticketStatus // ignore: cast_nullable_to_non_nullable
              as BackOfficeCaseTicketStatus?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      individualId: freezed == individualId
          ? _value.individualId
          : individualId // ignore: cast_nullable_to_non_nullable
              as String?,
      businessId: freezed == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as String?,
      productType: freezed == productType
          ? _value.productType
          : productType // ignore: cast_nullable_to_non_nullable
              as CasesProductType?,
      caseNumber: freezed == caseNumber
          ? _value.caseNumber
          : caseNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      payload: freezed == payload
          ? _value.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as BackOfficeCasePayload?,
      ticketId: freezed == ticketId
          ? _value.ticketId
          : ticketId // ignore: cast_nullable_to_non_nullable
              as String?,
      priority: freezed == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as BackOfficeCasePriority?,
      jiraUrl: freezed == jiraUrl
          ? _value.jiraUrl
          : jiraUrl // ignore: cast_nullable_to_non_nullable
              as Uri?,
      dueBy: freezed == dueBy
          ? _value.dueBy
          : dueBy // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  /// Create a copy of BackOfficeCase
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CaseAuthorCopyWith<$Res> get createdBy {
    return $CaseAuthorCopyWith<$Res>(_value.createdBy, (value) {
      return _then(_value.copyWith(createdBy: value) as $Val);
    });
  }

  /// Create a copy of BackOfficeCase
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BackOfficeCasePayloadCopyWith<$Res>? get payload {
    if (_value.payload == null) {
      return null;
    }

    return $BackOfficeCasePayloadCopyWith<$Res>(_value.payload!, (value) {
      return _then(_value.copyWith(payload: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BackOfficeCaseImplCopyWith<$Res>
    implements $BackOfficeCaseCopyWith<$Res> {
  factory _$$BackOfficeCaseImplCopyWith(_$BackOfficeCaseImpl value,
          $Res Function(_$BackOfficeCaseImpl) then) =
      __$$BackOfficeCaseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String conversationId,
      String processName,
      CaseType caseType,
      BackOfficeCaseStatus status,
      CaseAuthor createdBy,
      DateTime createdAt,
      DateTime updatedAt,
      List<BackOfficeTask> tasks,
      String revisionVersion,
      Map<String, dynamic> rawPayload,
      List<Checker> checkers,
      List<BackOfficeCaseAttachment> attachments,
      String? groupName,
      BackOfficeCaseTicketStatus? ticketStatus,
      String? title,
      String? individualId,
      String? businessId,
      CasesProductType? productType,
      String? caseNumber,
      BackOfficeCasePayload? payload,
      String? ticketId,
      BackOfficeCasePriority? priority,
      Uri? jiraUrl,
      DateTime? dueBy});

  @override
  $CaseAuthorCopyWith<$Res> get createdBy;
  @override
  $BackOfficeCasePayloadCopyWith<$Res>? get payload;
}

/// @nodoc
class __$$BackOfficeCaseImplCopyWithImpl<$Res>
    extends _$BackOfficeCaseCopyWithImpl<$Res, _$BackOfficeCaseImpl>
    implements _$$BackOfficeCaseImplCopyWith<$Res> {
  __$$BackOfficeCaseImplCopyWithImpl(
      _$BackOfficeCaseImpl _value, $Res Function(_$BackOfficeCaseImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackOfficeCase
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? conversationId = null,
    Object? processName = null,
    Object? caseType = null,
    Object? status = null,
    Object? createdBy = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? tasks = null,
    Object? revisionVersion = null,
    Object? rawPayload = null,
    Object? checkers = null,
    Object? attachments = null,
    Object? groupName = freezed,
    Object? ticketStatus = freezed,
    Object? title = freezed,
    Object? individualId = freezed,
    Object? businessId = freezed,
    Object? productType = freezed,
    Object? caseNumber = freezed,
    Object? payload = freezed,
    Object? ticketId = freezed,
    Object? priority = freezed,
    Object? jiraUrl = freezed,
    Object? dueBy = freezed,
  }) {
    return _then(_$BackOfficeCaseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      conversationId: null == conversationId
          ? _value.conversationId
          : conversationId // ignore: cast_nullable_to_non_nullable
              as String,
      processName: null == processName
          ? _value.processName
          : processName // ignore: cast_nullable_to_non_nullable
              as String,
      caseType: null == caseType
          ? _value.caseType
          : caseType // ignore: cast_nullable_to_non_nullable
              as CaseType,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as BackOfficeCaseStatus,
      createdBy: null == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as CaseAuthor,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      tasks: null == tasks
          ? _value._tasks
          : tasks // ignore: cast_nullable_to_non_nullable
              as List<BackOfficeTask>,
      revisionVersion: null == revisionVersion
          ? _value.revisionVersion
          : revisionVersion // ignore: cast_nullable_to_non_nullable
              as String,
      rawPayload: null == rawPayload
          ? _value._rawPayload
          : rawPayload // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      checkers: null == checkers
          ? _value._checkers
          : checkers // ignore: cast_nullable_to_non_nullable
              as List<Checker>,
      attachments: null == attachments
          ? _value._attachments
          : attachments // ignore: cast_nullable_to_non_nullable
              as List<BackOfficeCaseAttachment>,
      groupName: freezed == groupName
          ? _value.groupName
          : groupName // ignore: cast_nullable_to_non_nullable
              as String?,
      ticketStatus: freezed == ticketStatus
          ? _value.ticketStatus
          : ticketStatus // ignore: cast_nullable_to_non_nullable
              as BackOfficeCaseTicketStatus?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      individualId: freezed == individualId
          ? _value.individualId
          : individualId // ignore: cast_nullable_to_non_nullable
              as String?,
      businessId: freezed == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as String?,
      productType: freezed == productType
          ? _value.productType
          : productType // ignore: cast_nullable_to_non_nullable
              as CasesProductType?,
      caseNumber: freezed == caseNumber
          ? _value.caseNumber
          : caseNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      payload: freezed == payload
          ? _value.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as BackOfficeCasePayload?,
      ticketId: freezed == ticketId
          ? _value.ticketId
          : ticketId // ignore: cast_nullable_to_non_nullable
              as String?,
      priority: freezed == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as BackOfficeCasePriority?,
      jiraUrl: freezed == jiraUrl
          ? _value.jiraUrl
          : jiraUrl // ignore: cast_nullable_to_non_nullable
              as Uri?,
      dueBy: freezed == dueBy
          ? _value.dueBy
          : dueBy // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$BackOfficeCaseImpl extends _BackOfficeCase {
  const _$BackOfficeCaseImpl(
      {required this.id,
      required this.conversationId,
      required this.processName,
      required this.caseType,
      required this.status,
      required this.createdBy,
      required this.createdAt,
      required this.updatedAt,
      required final List<BackOfficeTask> tasks,
      required this.revisionVersion,
      required final Map<String, dynamic> rawPayload,
      required final List<Checker> checkers,
      required final List<BackOfficeCaseAttachment> attachments,
      this.groupName,
      this.ticketStatus,
      this.title,
      this.individualId,
      this.businessId,
      this.productType,
      this.caseNumber,
      this.payload,
      this.ticketId,
      this.priority,
      this.jiraUrl,
      this.dueBy})
      : _tasks = tasks,
        _rawPayload = rawPayload,
        _checkers = checkers,
        _attachments = attachments,
        super._();

  /// ID of backoffice case
  @override
  final String id;

  /// ID of conversation that agent had with customer
  @override
  final String conversationId;

  /// The name of the process that the case is associated with
  @override
  final String processName;

  /// The type of the case (e.g. GL to GL, Customer to GL)
  @override
  final CaseType caseType;

  /// The status of the case (e.g. active, completed)
  @override
  final BackOfficeCaseStatus status;

  /// Represents the user that created the case
  @override
  final CaseAuthor createdBy;

  /// The date and time the case was created
  @override
  final DateTime createdAt;

  /// The date and time the case was last updated
  @override
  final DateTime updatedAt;

  /// The tasks associated with the case
  final List<BackOfficeTask> _tasks;

  /// The tasks associated with the case
  @override
  List<BackOfficeTask> get tasks {
    if (_tasks is EqualUnmodifiableListView) return _tasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tasks);
  }

  /// Revision Version
  @override
  final String revisionVersion;

  /// Raw payload
  final Map<String, dynamic> _rawPayload;

  /// Raw payload
  @override
  Map<String, dynamic> get rawPayload {
    if (_rawPayload is EqualUnmodifiableMapView) return _rawPayload;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_rawPayload);
  }

  /// Checkers
  final List<Checker> _checkers;

  /// Checkers
  @override
  List<Checker> get checkers {
    if (_checkers is EqualUnmodifiableListView) return _checkers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_checkers);
  }

  /// List of files attached to a case
  final List<BackOfficeCaseAttachment> _attachments;

  /// List of files attached to a case
  @override
  List<BackOfficeCaseAttachment> get attachments {
    if (_attachments is EqualUnmodifiableListView) return _attachments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_attachments);
  }

  /// The group of agents currently assigned to this case
  @override
  final String? groupName;

  /// The status of the ticket in Freshdesk
  @override
  final BackOfficeCaseTicketStatus? ticketStatus;

  /// User readable name of the case
  @override
  final String? title;

  /// The individual ID that the case is associated with
  @override
  final String? individualId;

  /// The business ID that the case is associated with
  @override
  final String? businessId;

  /// The product type that the case is associated with
  @override
  final CasesProductType? productType;

  /// The case number that can be used as reference for communication
  @override
  final String? caseNumber;

  /// The payload of the case
  @override
  final BackOfficeCasePayload? payload;

  /// The ticket ID in Freshdesk
  @override
  final String? ticketId;

  /// The priority of the ticket
  @override
  final BackOfficeCasePriority? priority;

  /// The Jira URL for the case if it exists
  @override
  final Uri? jiraUrl;

  /// The due by date for the case
  @override
  final DateTime? dueBy;

  @override
  String toString() {
    return 'BackOfficeCase(id: $id, conversationId: $conversationId, processName: $processName, caseType: $caseType, status: $status, createdBy: $createdBy, createdAt: $createdAt, updatedAt: $updatedAt, tasks: $tasks, revisionVersion: $revisionVersion, rawPayload: $rawPayload, checkers: $checkers, attachments: $attachments, groupName: $groupName, ticketStatus: $ticketStatus, title: $title, individualId: $individualId, businessId: $businessId, productType: $productType, caseNumber: $caseNumber, payload: $payload, ticketId: $ticketId, priority: $priority, jiraUrl: $jiraUrl, dueBy: $dueBy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BackOfficeCaseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.conversationId, conversationId) ||
                other.conversationId == conversationId) &&
            (identical(other.processName, processName) ||
                other.processName == processName) &&
            (identical(other.caseType, caseType) ||
                other.caseType == caseType) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality().equals(other._tasks, _tasks) &&
            (identical(other.revisionVersion, revisionVersion) ||
                other.revisionVersion == revisionVersion) &&
            const DeepCollectionEquality()
                .equals(other._rawPayload, _rawPayload) &&
            const DeepCollectionEquality().equals(other._checkers, _checkers) &&
            const DeepCollectionEquality()
                .equals(other._attachments, _attachments) &&
            (identical(other.groupName, groupName) ||
                other.groupName == groupName) &&
            (identical(other.ticketStatus, ticketStatus) ||
                other.ticketStatus == ticketStatus) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.individualId, individualId) ||
                other.individualId == individualId) &&
            (identical(other.businessId, businessId) ||
                other.businessId == businessId) &&
            (identical(other.productType, productType) ||
                other.productType == productType) &&
            (identical(other.caseNumber, caseNumber) ||
                other.caseNumber == caseNumber) &&
            (identical(other.payload, payload) || other.payload == payload) &&
            (identical(other.ticketId, ticketId) ||
                other.ticketId == ticketId) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            (identical(other.jiraUrl, jiraUrl) || other.jiraUrl == jiraUrl) &&
            (identical(other.dueBy, dueBy) || other.dueBy == dueBy));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        conversationId,
        processName,
        caseType,
        status,
        createdBy,
        createdAt,
        updatedAt,
        const DeepCollectionEquality().hash(_tasks),
        revisionVersion,
        const DeepCollectionEquality().hash(_rawPayload),
        const DeepCollectionEquality().hash(_checkers),
        const DeepCollectionEquality().hash(_attachments),
        groupName,
        ticketStatus,
        title,
        individualId,
        businessId,
        productType,
        caseNumber,
        payload,
        ticketId,
        priority,
        jiraUrl,
        dueBy
      ]);

  /// Create a copy of BackOfficeCase
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BackOfficeCaseImplCopyWith<_$BackOfficeCaseImpl> get copyWith =>
      __$$BackOfficeCaseImplCopyWithImpl<_$BackOfficeCaseImpl>(
          this, _$identity);
}

abstract class _BackOfficeCase extends BackOfficeCase {
  const factory _BackOfficeCase(
      {required final String id,
      required final String conversationId,
      required final String processName,
      required final CaseType caseType,
      required final BackOfficeCaseStatus status,
      required final CaseAuthor createdBy,
      required final DateTime createdAt,
      required final DateTime updatedAt,
      required final List<BackOfficeTask> tasks,
      required final String revisionVersion,
      required final Map<String, dynamic> rawPayload,
      required final List<Checker> checkers,
      required final List<BackOfficeCaseAttachment> attachments,
      final String? groupName,
      final BackOfficeCaseTicketStatus? ticketStatus,
      final String? title,
      final String? individualId,
      final String? businessId,
      final CasesProductType? productType,
      final String? caseNumber,
      final BackOfficeCasePayload? payload,
      final String? ticketId,
      final BackOfficeCasePriority? priority,
      final Uri? jiraUrl,
      final DateTime? dueBy}) = _$BackOfficeCaseImpl;
  const _BackOfficeCase._() : super._();

  /// ID of backoffice case
  @override
  String get id;

  /// ID of conversation that agent had with customer
  @override
  String get conversationId;

  /// The name of the process that the case is associated with
  @override
  String get processName;

  /// The type of the case (e.g. GL to GL, Customer to GL)
  @override
  CaseType get caseType;

  /// The status of the case (e.g. active, completed)
  @override
  BackOfficeCaseStatus get status;

  /// Represents the user that created the case
  @override
  CaseAuthor get createdBy;

  /// The date and time the case was created
  @override
  DateTime get createdAt;

  /// The date and time the case was last updated
  @override
  DateTime get updatedAt;

  /// The tasks associated with the case
  @override
  List<BackOfficeTask> get tasks;

  /// Revision Version
  @override
  String get revisionVersion;

  /// Raw payload
  @override
  Map<String, dynamic> get rawPayload;

  /// Checkers
  @override
  List<Checker> get checkers;

  /// List of files attached to a case
  @override
  List<BackOfficeCaseAttachment> get attachments;

  /// The group of agents currently assigned to this case
  @override
  String? get groupName;

  /// The status of the ticket in Freshdesk
  @override
  BackOfficeCaseTicketStatus? get ticketStatus;

  /// User readable name of the case
  @override
  String? get title;

  /// The individual ID that the case is associated with
  @override
  String? get individualId;

  /// The business ID that the case is associated with
  @override
  String? get businessId;

  /// The product type that the case is associated with
  @override
  CasesProductType? get productType;

  /// The case number that can be used as reference for communication
  @override
  String? get caseNumber;

  /// The payload of the case
  @override
  BackOfficeCasePayload? get payload;

  /// The ticket ID in Freshdesk
  @override
  String? get ticketId;

  /// The priority of the ticket
  @override
  BackOfficeCasePriority? get priority;

  /// The Jira URL for the case if it exists
  @override
  Uri? get jiraUrl;

  /// The due by date for the case
  @override
  DateTime? get dueBy;

  /// Create a copy of BackOfficeCase
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BackOfficeCaseImplCopyWith<_$BackOfficeCaseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
