openapi: 3.0.1
info:
  title: Backoffice Business Params Configuration API Documentation
  version: 1.0.0
servers:
  - url: http://localhost:9090
    description: localhost

security:
  - Authorization: []

paths:
  /business_config_parameters :
    get:
      tags:
        - backoffice-param-config
      summary: Get business param configuration for given key.
      operationId: findByBusinessKey
      parameters:
        - name: business_key
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ODSEntity'
components:
  schemas:
    ODSEntity:
      type: object
      properties:
        odsActiveFlag:
          type: boolean
        payload:
          $ref: '#/components/schemas/Payload'
    Payload:
      type: object
      properties:
        Business_key:
          type: string
        config:
          type : array
          xml:
            wrapped: true
          items:
            $ref: '#/components/schemas/Config'
    Config:
      type: object
      properties:
        chargeId:
          type: string
        description:
          type: string
  securitySchemes:
    Authorization:
      type: "http"
      scheme: "bearer"
      bearerFormat: "JWT"