openapi: 3.0.1
info:
  title: Backoffice Freshworks-Integration service Ingress API Documentation for Ticket API
  version: 1.0.0
servers:
  - url: http://localhost:9090
    description: localhost

security:
  - Authorization: []

paths:
  /api/v1/tickets/filter:
    get:
      tags:
        - ticket-management-controller
      summary: Get ticket filtered based on query
      operationId: filterTicket
      parameters:
        - name: query
          in: query
          required: true
          style: deepObject
          explode: true
          schema:
            type: string
          description: A map of filter queries
      responses:
        '200':
          description: Successful response of filtered tickets
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FilterTicketResponse'

  /api/v1/tickets/fields/{ticketFieldName}:
    get:
      tags:
        - ticket-management-controller
      summary: Get Ticket Field based on field name
      operationId: viewTicketFieldDetails
      parameters:
        - in: path
          name: ticketFieldName
          schema:
            type: string
            enum:
              - status
              - priority
          required: true
          description: ticket field name
      responses:
        '200':
          description: Successful response of ticket field value
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TicketFieldValueResponse'

  /api/v1/tickets/{id}:
    get:
      tags:
        - ticket-management-controller
      summary: View ticket based on ticketId
      operationId: viewTicket
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
          description: ticket id
      responses:
        '200':
          description: Successful response of view tickets
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ViewTicketResponse'

  /api/v1/tickets:
    post:
      tags:
        - ticket-management-controller
      summary: Create ticket Request
      operationId: createTicketRequest
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TicketRequestModel'
        required: true
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TicketResponse'

  /api/v1/tickets/{updateTicketId}:
    put:
      tags:
        - ticket-management-controller
      summary: Update ticket Request
      operationId: updateTicket
      parameters:
        - in: path
          name: updateTicketId
          schema:
            type: string
          required: true
          description: ticket id
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TicketRequestModel'
        required: true
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TicketResponse'

  /api/v1/tickets/{id}/notes:
    post:
      tags:
        - ticket-management-controller
      summary: Add Notes to ticket
      operationId: addNotes
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
          description: ticket id
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TicketNotesReq'
        required: true
      responses:
        '200':
          description: Ticket notes are added.

  /api/v1/tickets/{ticketId}:
    delete:
      tags:
        - ticket-management-controller
      summary: Delete ticket
      operationId: deleteTicket
      parameters:
        - in: path
          name: ticketId
          schema:
            type: string
          required: true
          description: ticket id
      responses:
        '200':
          description: The ticket is deleted.

  /api/v1/tickets/agents:
    get:
      tags:
        - ticket-management-controller
      summary: Get all agents
      operationId: getListOfAgents
      responses:
        '200':
          description: Successful response of agents list
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TicketAgentResponse'

  /api/v1/chat/agents:
    get:
      tags:
        - chat-management-controller
      summary: Get all agents
      operationId: getListAgents
      responses:
        '200':
          description: Successful response of agents list
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatAgentResponse'

  /api/v1/tickets/groups:
    get:
      tags:
        - ticket-management-controller
      summary: Get all groups
      operationId: getListOfGroups
      responses:
        '200':
          description: Successful response of groups list
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TicketGroupsResponse'

  /api/v1/conversation-history/contact/{individualId}:
    get:
      tags:
        - conversation-history-controller
      summary: Get filtered search contacts
      operationId: getFilteredSearchContact
      parameters:
        - in: path
          name: individualId
          schema:
            type: string
          required: true
          description: individual id
      responses:
        '200':
          description: Successful response of filtered search contacts
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContactSearchResult'

  /api/v1/conversation-history/user/filter:
    get:
      tags:
        - conversation-history-controller
      operationId: getConversationHistory
      parameters:
        - name: externalId
          in: query
          required: true
          schema:
            type: string
        - name: page
          in: query
          required: false
          schema:
            type: integer
            format: int32
            default: 1
        - name: size
          in: query
          required: false
          schema:
            type: integer
            format: int32
            default: 20
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConversationDto'

  /api/v1/chat/channels:
    get:
      tags:
        - coversation-channel-controller
      operationId: getAllChannels
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ConversationChannelsDto'

  /api/v1/conversations/search:
    post:
      summary: Search Conversations
      description: Retrieve a list of conversations based on the specified search criteria, pagination, and sorting options.
      tags:
        - Conversations
      parameters:
        - name: x-correlation-id
          in: header
          required: true
          description: Unique identifier for tracing the request.
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConversationQueryRequest'
      responses:
        '200':
          description: Successful response with conversations.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConversationQueryResponse'
        '400':
          description: Invalid input.
        '401':
          description: Unauthorized access.
        '500':
          description: Internal server error.

components:
  schemas:
    FilterTicketResponse:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/FilterTicketResponseModel'
        total:
          type: integer
          format: int32
        link:
          type: string
    FilterTicketResponseModel:
      type: object
      properties:
        description:
          type: string
        status:
          type: integer
          format: int32
        dueBy:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        id:
          type: integer
          format: int64
        subject:
          type: string
    TicketFieldValueResponse:
      type: object
      properties:
        choices:
          type: array
          items:
            $ref: '#/components/schemas/TicketFieldDropDownChoiceResponseModel'
    TicketFieldDropDownChoiceResponseModel:
      type: object
      properties:
        label:
          type: string
        labelForCustomers:
          type: string
        value:
          type: string
        deleted:
          type: boolean
    ViewTicketResponse:
      type: object
      properties:
        individualId:
          type: string
        product:
          type: string
        businessId:
          type: string
        caseId:
          type: string
        custom_fields:
          type: array
          items:
            $ref: '#/components/schemas/CustomFields'
        subject:
          type: string
        description:
          type: string
    CustomFields:
      type: object
      properties:
        cf_business_id:
          type: string
        cf_individual_id:
          type: string
        cf_case_id:
          type: string
        cf_product:
          type: string
    TicketRequestModel:
      type: object
      properties:
        title:
          type: string
        phone:
          type: string
        source:
          type: string
        status:
          type: string
        priority:
          type: string
        id:
          type: integer
          format: int64
        description:
          type: string
        lookupParameter:
          type: string
        customFields:
          type: array
          items:
            $ref: '#/components/schemas/CustomFields'
        name:
          type: string
        requesterId:
          type: integer
          format: int64
        email:
          type: string
        uniqueExternalId:
          type: string
        type:
          type: string
        responderId:
          type: integer
          format: int64
        ccEmails:
          type: array
          items:
            type: string
        dueBy:
          type: string
        emailConfigId:
          type: integer
          format: int64
        frDueBy:
          type: string
        groupId:
          type: integer
          format: int64
        tags:
          type: array
          items:
            type: string
        companyId:
          type: integer
          format: int64
        internalAgentId:
          type: integer
          format: int64
        internalGroupId:
          type: integer
          format: int64
    TicketResponse:
      type: object
      properties:
        attachments:
          type: array
          items:
            type: object
        phone:
          type: string
        source:
          type: string
        status:
          type: string
        priority:
          type: string
        id:
          type: integer
          format: int64
        description:
          type: string
        lookupParameter:
          type: string
        relatedTicketIds:
          type: array
          items:
            type: string
        customFields:
          type: array
          items:
            $ref: '#/components/schemas/CustomFields'
        name:
          type: string
        requesterId:
          type: integer
          format: int64
        email:
          type: string
        uniqueExternalId:
          type: string
        type:
          type: string
        responderId:
          type: integer
          format: int64
        ccEmails:
          type: array
          items:
            type: string
        dueBy:
          type: string
        emailConfigId:
          type: integer
          format: int64
        frDueBy:
          type: string
        groupId:
          type: integer
          format: int64
        tags:
          type: array
          items:
            type: string
        companyId:
          type: integer
          format: int64
        internalAgentId:
          type: integer
          format: int64
        internalGroupId:
          type: integer
          format: int64
        productId:
          type: string
        parentId:
          type: integer
          format: int64
        fwdEmails:
          type: array
          items:
            type: string
        replyCcEmails:
          type: array
          items:
            type: string
        toEmails:
          type: array
          items:
            type: string
        frEscalated:
          type: boolean
        spam:
          type: boolean
        urgent:
          type: boolean
        isEscalated:
          type: boolean
        createdAt:
          type: string
        updatedAt:
          type: string
        descriptionText:
          type: string
        associatedTicketsList:
          type: integer
          format: int64
        deleted:
          type: boolean
    TicketNotesReq:
      type: object
      properties:
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/TicketNotesAttachment'
        isPrivate:
          type: boolean
    TicketNotesAttachment:
      type: object
      properties:
        base64File:
          type: string
        byteArrayFile:
          type: object
          format: byte
        fileName:
          type: string
    TicketAgentResponse:
      type: object
      properties:
        available:
          type: boolean
        occasional:
          type: boolean
        signature:
          type: string
        ticketScope:
          type: integer
          format: int64
        id:
          type: string
        createdAt:
          type: string
        contact:
          $ref: '#/components/schemas/Contact'
        updatedAt:
          type: string
        availableSince:
          type: string
        deactivated:
          type: boolean
        type:
          type: string
        lastActiveAt:
          type: string
        freshCallerAgent:
          type: boolean
        freshChatAgent:
          type: boolean
        focusMode:
          type: boolean
    Contact:
      type: object
      properties:
        active:
          type: boolean
        email:
          type: string
        jobTitle:
          type: string
        language:
          type: string
        lastLoginAt:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        mobile:
          type: string
        name:
          type: string
        phone:
          type: string
        timeZone:
          type: string
    TicketGroupsResponse:
      type: object
      properties:
        escalateTo:
          type: boolean
        unassignedFor:
          type: boolean
        autoTicketAssign:
          type: boolean
        id:
          type: string
        name:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        businessHourId:
          type: string
        description:
          type: string
    ChatAgentResponse:
      type: object
      properties:
        groups:
          type: array
          items:
            type: string
        biography:
          type: string
        signature:
          type: string
        roleId:
          type: string
        roleName:
          type: string
        isDeactivated:
          type: boolean
        agentStatus:
          $ref: '#/components/schemas/AgentStatus'
        socialProfiles:
          $ref: '#/components/schemas/SocialProfiles'
        locale:
          type: string
        availabilityStatus:
          type: string
        routingType:
          type: string
        agentCapacity:
          type: string
        createdTime:
          type: string
        id:
          type: string
        firstName:
          type: boolean
        lastName:
          type: string
        email:
          type: string
        social_profiles:
          type: string
        loginStatus:
          type: boolean
    AgentStatus:
      type: object
      properties:
        name:
          type: string
    SocialProfiles:
      type: object
      properties:
        type:
          type: string
        id:
          type: string
    ContactSearchResult:
      type: object
      properties:
        contacts:
          type: array
          items:
            $ref: '#/components/schemas/ContactInfo'
        meta:
          $ref: '#/components/schemas/Meta'
    ContactInfo:
      type: object
      properties:
        partial:
          type: boolean
        id:
          type: integer
          format: int64
        jobTitle:
          type: string
          nullable: true
        leadScore:
          type: integer
          nullable: true
        email:
          type: string
          nullable: true
        emails:
          type: array
          items:
            $ref: '#/components/schemas/Email'
        workNumber:
          type: string
          nullable: true
        mobileNumber:
          type: string
          nullable: true
        openDealsAmount:
          type: string
          nullable: true
        wonDealsAmount:
          type: string
          nullable: true
        displayName:
          type: string
          nullable: true
        avatar:
          type: string
          nullable: true
        lastContactedMode:
          type: string
          nullable: true
        lastContacted:
          type: string
          format: date-time
          nullable: true
        lastContactedViaSalesActivity:
          type: string
          nullable: true
        firstName:
          type: string
          nullable: true
        lastName:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        country:
          type: string
          nullable: true
        createdAt:
          type: string
          format: date-time
          nullable: true
        updatedAt:
          type: string
          format: date-time
          nullable: true
        recentNote:
          type: string
          nullable: true
        lastContactedSalesActivityMode:
          type: string
          nullable: true
        webFormIds:
          type: string
          nullable: true
        lastAssignedAt:
          type: string
          format: date-time
          nullable: true
        externalId:
          type: string
          nullable: true
        facebook:
          type: string
          nullable: true
        twitter:
          type: string
          nullable: true
        linkedin:
          type: string
          nullable: true
        salesAccounts:
          type: array
          items:
            $ref: '#/components/schemas/SalesAccount'
    SalesAccount:
      type: object
      properties:
        partial:
          type: boolean
        id:
          type: integer
          format: int64
        name:
          type: string
          nullable: true
        avatar:
          type: string
          nullable: true
        website:
          type: string
          nullable: true
        lastContacted:
          type: string
          format: date-time
          nullable: true
        isPrimary:
          type: boolean
          nullable: true
    ConversationDto:
      type: object
      properties:
        conversationId:
          type: string
        title:
          type: string
        status:
          type: string
        channelId:
          type: string
        channel:
          type: string
        deepLink:
          type: string
        assignedAgentId:
          type: string
        createdTime:
          type: string
          format: date-time
        updatedTime:
          type: string
          format: date-time
    ConversationChannelDto:
      type: object
      properties:
        id:
          type: string
        source:
          type: string
    ConversationChannelsDto:
      type: object
      properties:
        channels:
          type: array
          items:
            $ref: '#/components/schemas/ConversationChannelDto'
    ConversationQueryRequest:
      type: object
      properties:
        fields:
          type: array
          description: List of search filters.
          items:
            $ref: '#/components/schemas/QueryField'
        pageSize:
          type: integer
          description: Number of results per page.
        searchAfter:
          type: string
          description: Cursor-based pagination value.
      example:
        fields:
          - field: cf_product
            operator: in
            value:
              - SME
        pageSize: 2
        searchAfter: MTczMzkxNzg4OTcxMywxODQ2OTc3MjY3ODU0OTc3MDI0
    QueryField:
      type: object
      properties:
        field:
          type: string
          description: Field to filter.
        operator:
          type: string
          description: Operator to use for filtering (e.g., in, gt).
        value:
          type: array
          items:
            type: string
          description: Values to compare against.
    ConversationQueryResponse:
      type: object
      properties:
        nextPage:
          type: string
          description: Cursor for the next page.
        totalCount:
          type: integer
          description: Total number of conversations matching the query.
        conversations:
          type: array
          description: List of conversations.
          items:
            $ref: '#/components/schemas/Conversation'
      example:
        nextPage: MTczMzgyNzkwMzc5NiwxODY2MDcxMjEzMDc5NTUwOTc2
        totalCount: 142
        conversations:
          - $ref: '#/components/schemas/Conversation'
    Conversation:
      type: object
      properties:
        conversationId:
          type: string
          description: Unique ID for the conversation.
        status:
          type: string
          description: Status of the conversation (e.g., resolved).
        channelId:
          type: string
          description: Channel ID of the conversation.
        assignedAgentId:
          type: string
          description: Agent assigned to the conversation.
        createdTime:
          type: string
          format: date-time
          description: Time the conversation was created.
        updatedTime:
          type: string
          format: date-time
          description: Time the conversation was last updated.
        properties:
          type: object
          additionalProperties:
            type: string
          description: Custom properties of the conversation.
      example:
        conversationId: 46201c3b-2b08-449a-ba5a-d760d96390ba
        status: resolved
        channelId: d21f03b5-9a2a-4382-9d28-8209a3d95d02
        assignedAgentId: 16d3e409-b8f6-47ab-8f60-3fa1ebdd68cb
        createdTime: 2024-12-11T05:04:26.02
        updatedTime: 2024-12-11T05:05:53.437
        properties:
          cf_type: FCR-Info
          cf_product: SME
          cf_verification_status: Emergency
    Email:
      type: object
      properties:
        id:
          type: integer
          format: int64
        value:
          type: string
        isPrimary:
          type: boolean
          nullable: true
        label:
          type: string
          nullable: true
        destroy:
          type: boolean
          nullable: true
    Meta:
      type: object
      properties:
        total:
          type: integer
          nullable: true
  securitySchemes:
    Authorization:
      type: "http"
      scheme: "bearer"
      bearerFormat: "JWT"