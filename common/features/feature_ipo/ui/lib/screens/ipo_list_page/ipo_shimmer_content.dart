part of 'ipo_list_page.dart';

class _IpoShimmerContent extends StatelessWidget {
  const _IpoShimmerContent({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final localizations = IpoLocalizations.of(context);

    return NestedScrollView(
      headerSliverBuilder: (_, __) => [
        SliverHeader(
          title: localizations.uaeIposLabel,
        ),
        SliverToBoxAdapter(
          child: IgnorePointer(
            child: Tabs(
              TabsModel(
                tabNames: [
                  localizations.activeLabel,
                  localizations.appliedLabel,
                ],
              ),
              padding: const EdgeInsetsDirectional.only(
                top: 24,
                start: 24,
                end: 24,
              ),
            ),
          ),
        ),
      ],
      body: CompanyShimmer(
        model: const CompanyShimmerModel(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: ListView.separated(
            itemCount: 8,
            padding: const EdgeInsets.only(top: 24, bottom: 36),
            separatorBuilder: (_, __) => const Space.vertical(24),
            itemBuilder: (_, __) => Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: Colors.white,
              ),
              width: double.infinity,
              height: 176,
            ),
          ),
        ),
      ),
    );
  }
}
