import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neobroker_models/models.dart';
import 'package:ui/ui.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_common_feature_ipo_api/index.dart';
import 'package:wio_common_feature_ipo_ui/data_providers/ipo_flow_data.dart';
import 'package:wio_common_feature_ipo_ui/navigation/ipo_details_navigation_config.dart';
import 'package:wio_common_feature_ipo_ui/screens/ipo_list_page/analytics/ipo_list_analytics.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_pricing_plan_api/feature_toggle/models/plan_advertisements.dart';
import 'package:wio_feature_pricing_plan_api/index.dart';
import 'package:wio_feature_product_hub_api/index.dart';
import 'package:wio_feature_user_api/index.dart';

part 'ipo_list_cubit.freezed.dart';
part 'ipo_list_state.dart';

class IpoListCubit extends BaseCubit<IpoListState> {
  final IpoListAnalytics _analytics;
  final IpoInteractor _interactor;
  final BrokerUserInteractor _brokerUserInteractor;
  final PricingPlanInteractor _pricingPlanInteractor;
  final NavigationProvider _navigation;
  final FeatureToggleProvider _featureToggleProvider;

  final _exceptionProvider = CoreExceptionProvider();

  IpoListCubit({
    required IpoListAnalytics analytics,
    required IpoInteractor interactor,
    required BrokerUserInteractor brokerUserInteractor,
    required NavigationProvider navigation,
    required PricingPlanInteractor pricingPlanInteractor,
    required FeatureToggleProvider featureToggleProvider,
  })  : _analytics = analytics,
        _interactor = interactor,
        _brokerUserInteractor = brokerUserInteractor,
        _pricingPlanInteractor = pricingPlanInteractor,
        _navigation = navigation,
        _featureToggleProvider = featureToggleProvider,
        super(const IpoListState.loading()) {
    analytics.ipoDashboardOpened();
  }

  Stream<Exception> get generalErrorStream =>
      _exceptionProvider.exceptionStream.generalErrorStream;

  Stream<NoInternetConnectionException> get noInternetStream =>
      _exceptionProvider.exceptionStream.noInternetExceptionStream;

  bool get isEmployeeEnhancementEnabled => _featureToggleProvider
      .get(IpoFeatureToggles.isEmployeeTrancheEnhancementEnabled);

  bool get isParallaxImageEnabled =>
      _featureToggleProvider.get(IpoFeatureToggles.isParallaxEnabled);

  void reload() {
    emit(const IpoListState.loading());
    fetch();
  }

  @override
  Future<void> close() {
    _exceptionProvider.disposeExceptionStream();

    return super.close();
  }

  void onResume() {
    state.mapOrNull(
      idle: (idle) => fetch(),
    );
  }

  void onShowHub() {
    _navigation.navigateTo(
      const ProductHubFeatureNavigationConfig(
        destination: ProductHubSlideNavigationConfig(
          fromProductType: ItemType.ipo,
        ),
      ),
    );
  }

  Future<void> fetch({
    int? pageSize,
  }) async {
    final iposRequest = _interactor.getIpos(pageSize: pageSize);
    final ninsRequest = _interactor.getNins();
    final planAdvertisementsRequest =
        _pricingPlanInteractor.getUserPlanAdvertisements();

    await safeExecute(
      Future.wait([
        iposRequest,
        ninsRequest,
        planAdvertisementsRequest.onError(
          // Ignore error, so it won't break the whole feature
          (error, stackTrace) => const PlanAdvertisements.empty(),
        ),
        _fetchMarketAccess(),
      ]).toStream().withError(
        (_) {
          emit(const IpoListState.error());
        },
        _exceptionProvider,
      ).asyncMap(
        (data) {
          final ipos = data[0] as SortedIpos;
          final nins = data[1] as List<Nin>?;
          final planAdvertisements = data[2] as PlanAdvertisements;
          final marketAccess = data[3] as UserMarketAccess?;
          emit(
            IpoListState.idle(
              upcomingIpos: ipos.upcomingIpos,
              appliedIpos: ipos.appliedIpos,
              planAdvertisements: planAdvertisements,
              nins: nins,
              marketAccess: marketAccess,
            ),
          );
        },
      ).drain<void>(),
    );
  }

  void onIpoPressed(Ipo ipo) {
    _analytics.ipo(ipo.name);

    final nin = _getNinPerIpo(ipo.exchangeId);

    _navigation.push(
      IpoDetailsNavigationConfig(
        flowData: IpoFlowInitialData(ipo: ipo, nin: nin),
      ),
    );
  }

  void navigateToPlanSelection() {
    _navigation.navigateTo(
      PricingPlanFeatureNavigationConfig(
        destination: PricingPlanScreenNavigationConfig(
          source: PricingPlanSource.settings,
          preselectedPlanId: state.mapOrNull(
            idle: (value) =>
                value.planAdvertisements.mapOrNull((value) => value.planId) ??
                '',
          ),
        ),
      ),
    );
  }

  Future<UserMarketAccess?> _fetchMarketAccess() async {
    try {
      final marketAccess = await _brokerUserInteractor.getUserMarketAccess();

      return marketAccess;
    } on Object catch (_) {
      return null;
    }
  }

  Nin? _getNinPerIpo(IpoExchangeId exchangeId) {
    return state.mapOrNull<Nin?>(
      idle: (idle) {
        final nins = idle.nins;
        if (nins != null && nins.isNotEmpty) {
          return _getNinPerExchange(exchangeId, nins);
        }

        return null;
      },
    );
  }

  Nin? _getNinPerExchange(IpoExchangeId id, List<Nin> nins) {
    final ninIndex = nins.indexWhere((element) => element.exchangeId == id);
    if (ninIndex != -1) {
      return nins[ninIndex];
    }

    return null;
  }

  @override
  String toString() => 'IpoListCubit{}';
}
