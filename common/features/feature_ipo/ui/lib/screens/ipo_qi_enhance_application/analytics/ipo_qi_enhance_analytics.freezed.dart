// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ipo_qi_enhance_analytics.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$IpoQiEnhanceEventPayload {
  Money get minimumAmount => throw _privateConstructorUsedError;
  num get allocationFeePercent => throw _privateConstructorUsedError;
  Money get userApplyAmount => throw _privateConstructorUsedError;
  Money get enhancement => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $IpoQiEnhanceEventPayloadCopyWith<IpoQiEnhanceEventPayload> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IpoQiEnhanceEventPayloadCopyWith<$Res> {
  factory $IpoQiEnhanceEventPayloadCopyWith(IpoQiEnhanceEventPayload value,
          $Res Function(IpoQiEnhanceEventPayload) then) =
      _$IpoQiEnhanceEventPayloadCopyWithImpl<$Res, IpoQiEnhanceEventPayload>;
  @useResult
  $Res call(
      {Money minimumAmount,
      num allocationFeePercent,
      Money userApplyAmount,
      Money enhancement});
}

/// @nodoc
class _$IpoQiEnhanceEventPayloadCopyWithImpl<$Res,
        $Val extends IpoQiEnhanceEventPayload>
    implements $IpoQiEnhanceEventPayloadCopyWith<$Res> {
  _$IpoQiEnhanceEventPayloadCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minimumAmount = null,
    Object? allocationFeePercent = null,
    Object? userApplyAmount = null,
    Object? enhancement = null,
  }) {
    return _then(_value.copyWith(
      minimumAmount: null == minimumAmount
          ? _value.minimumAmount
          : minimumAmount // ignore: cast_nullable_to_non_nullable
              as Money,
      allocationFeePercent: null == allocationFeePercent
          ? _value.allocationFeePercent
          : allocationFeePercent // ignore: cast_nullable_to_non_nullable
              as num,
      userApplyAmount: null == userApplyAmount
          ? _value.userApplyAmount
          : userApplyAmount // ignore: cast_nullable_to_non_nullable
              as Money,
      enhancement: null == enhancement
          ? _value.enhancement
          : enhancement // ignore: cast_nullable_to_non_nullable
              as Money,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$IpoQiEnhanceEventPayloadIpoImplCopyWith<$Res>
    implements $IpoQiEnhanceEventPayloadCopyWith<$Res> {
  factory _$$IpoQiEnhanceEventPayloadIpoImplCopyWith(
          _$IpoQiEnhanceEventPayloadIpoImpl value,
          $Res Function(_$IpoQiEnhanceEventPayloadIpoImpl) then) =
      __$$IpoQiEnhanceEventPayloadIpoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Money minimumAmount,
      num allocationFeePercent,
      Money userApplyAmount,
      Money enhancement});
}

/// @nodoc
class __$$IpoQiEnhanceEventPayloadIpoImplCopyWithImpl<$Res>
    extends _$IpoQiEnhanceEventPayloadCopyWithImpl<$Res,
        _$IpoQiEnhanceEventPayloadIpoImpl>
    implements _$$IpoQiEnhanceEventPayloadIpoImplCopyWith<$Res> {
  __$$IpoQiEnhanceEventPayloadIpoImplCopyWithImpl(
      _$IpoQiEnhanceEventPayloadIpoImpl _value,
      $Res Function(_$IpoQiEnhanceEventPayloadIpoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minimumAmount = null,
    Object? allocationFeePercent = null,
    Object? userApplyAmount = null,
    Object? enhancement = null,
  }) {
    return _then(_$IpoQiEnhanceEventPayloadIpoImpl(
      minimumAmount: null == minimumAmount
          ? _value.minimumAmount
          : minimumAmount // ignore: cast_nullable_to_non_nullable
              as Money,
      allocationFeePercent: null == allocationFeePercent
          ? _value.allocationFeePercent
          : allocationFeePercent // ignore: cast_nullable_to_non_nullable
              as num,
      userApplyAmount: null == userApplyAmount
          ? _value.userApplyAmount
          : userApplyAmount // ignore: cast_nullable_to_non_nullable
              as Money,
      enhancement: null == enhancement
          ? _value.enhancement
          : enhancement // ignore: cast_nullable_to_non_nullable
              as Money,
    ));
  }
}

/// @nodoc

class _$IpoQiEnhanceEventPayloadIpoImpl extends _IpoQiEnhanceEventPayloadIpo {
  const _$IpoQiEnhanceEventPayloadIpoImpl(
      {required this.minimumAmount,
      required this.allocationFeePercent,
      required this.userApplyAmount,
      required this.enhancement})
      : super._();

  @override
  final Money minimumAmount;
  @override
  final num allocationFeePercent;
  @override
  final Money userApplyAmount;
  @override
  final Money enhancement;

  @override
  String toString() {
    return 'IpoQiEnhanceEventPayload(minimumAmount: $minimumAmount, allocationFeePercent: $allocationFeePercent, userApplyAmount: $userApplyAmount, enhancement: $enhancement)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IpoQiEnhanceEventPayloadIpoImpl &&
            (identical(other.minimumAmount, minimumAmount) ||
                other.minimumAmount == minimumAmount) &&
            (identical(other.allocationFeePercent, allocationFeePercent) ||
                other.allocationFeePercent == allocationFeePercent) &&
            (identical(other.userApplyAmount, userApplyAmount) ||
                other.userApplyAmount == userApplyAmount) &&
            (identical(other.enhancement, enhancement) ||
                other.enhancement == enhancement));
  }

  @override
  int get hashCode => Object.hash(runtimeType, minimumAmount,
      allocationFeePercent, userApplyAmount, enhancement);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$IpoQiEnhanceEventPayloadIpoImplCopyWith<_$IpoQiEnhanceEventPayloadIpoImpl>
      get copyWith => __$$IpoQiEnhanceEventPayloadIpoImplCopyWithImpl<
          _$IpoQiEnhanceEventPayloadIpoImpl>(this, _$identity);
}

abstract class _IpoQiEnhanceEventPayloadIpo extends IpoQiEnhanceEventPayload {
  const factory _IpoQiEnhanceEventPayloadIpo(
      {required final Money minimumAmount,
      required final num allocationFeePercent,
      required final Money userApplyAmount,
      required final Money enhancement}) = _$IpoQiEnhanceEventPayloadIpoImpl;
  const _IpoQiEnhanceEventPayloadIpo._() : super._();

  @override
  Money get minimumAmount;
  @override
  num get allocationFeePercent;
  @override
  Money get userApplyAmount;
  @override
  Money get enhancement;
  @override
  @JsonKey(ignore: true)
  _$$IpoQiEnhanceEventPayloadIpoImplCopyWith<_$IpoQiEnhanceEventPayloadIpoImpl>
      get copyWith => throw _privateConstructorUsedError;
}
