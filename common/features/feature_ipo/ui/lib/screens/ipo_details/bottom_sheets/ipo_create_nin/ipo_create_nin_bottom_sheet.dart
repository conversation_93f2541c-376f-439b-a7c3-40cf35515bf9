import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_feature_ipo_api/index.dart';
import 'package:wio_common_feature_ipo_ui/index.dart';
import 'package:wio_common_feature_ipo_ui/screens/ipo_details/bottom_sheets/ipo_create_nin/ipo_create_nin_bottom_sheet_cubit.dart';

class IpoCreateNinBottomSheet extends StatelessWidget {
  final Ipo ipo;
  final String accoundId;

  const IpoCreateNinBottomSheet({
    required this.ipo,
    required this.accoundId,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => DependencyProvider.getWithParams<
          IpoCreateNinBottomSheetCubit, String, void>(param1: accoundId),
      child: _IpoCreateNinBottomSheetContent(
        ipo: ipo,
      ),
    );
  }
}

class _IpoCreateNinBottomSheetContent extends StatelessWidget {
  final Ipo ipo;

  const _IpoCreateNinBottomSheetContent({
    required this.ipo,
  });

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<IpoCreateNinBottomSheetCubit>();
    final localization = IpoLocalizations.of(context);
    final isCreatingNin = context.watch<IpoCreateNinBottomSheetCubit>().state;

    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async => !isCreatingNin,
      child: IgnorePointer(
        ignoring: isCreatingNin,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CompanyIcon(
                const CompanyIconModel(
                  icon: GraphicAssetPointer.pictogram(
                    CompanyPictogramPointer.actions_great_idea,
                  ),
                  gradient: CompanyGradientPointer.indigo,
                  size: CompanyIconSize.xxxxxLarge,
                ),
              ),
              const SizedBox(height: 20),
              Label(
                model: LabelModel(
                  text: localization.ipoCreateNinBottomSheetTitle,
                  textStyle: CompanyTextStylePointer.h3medium,
                  color: CompanyColorPointer.primary3,
                  textAlign: LabelTextAlign.center,
                ),
              ),
              const SizedBox(height: 8),
              Label(
                model: LabelModel(
                  text: localization
                      .ipoCreateNinBottomSheetBody(ipo.exchangeName),
                  textStyle: CompanyTextStylePointer.b2,
                  color: CompanyColorPointer.secondary1,
                  textAlign: LabelTextAlign.center,
                ),
              ),
              _DfmInfoMessage(
                exchangeId: ipo.exchangeId,
              ),
              const SizedBox(height: 43),
              SizedBox(
                width: double.infinity,
                child: Button(
                  model: ButtonModel(
                    title: localization.ipoCreateNinBottomSheetAction,
                    loading: isCreatingNin,
                  ),
                  onPressed: !isCreatingNin ? () => bloc.onSubmit(ipo) : () {},
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _DfmInfoMessage extends StatelessWidget {
  final IpoExchangeId exchangeId;

  const _DfmInfoMessage({
    required this.exchangeId,
  });

  @override
  Widget build(BuildContext context) {
    if (exchangeId != IpoExchangeId.dfm) {
      return const SizedBox();
    }
    final localization = IpoLocalizations.of(context);

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: 20),
      padding: const EdgeInsetsDirectional.fromSTEB(12, 14, 12, 14),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: context.colorStyling.primary2,
      ),
      child: Row(
        children: [
          CompanyIcon(
            CompanyIconModel(
              icon: CompanyIconPointer.information.toGraphicAsset(),
              size: CompanyIconSize.xLarge,
              color: CompanyColorPointer.primary1,
            ),
          ),
          const Space.horizontal(12),
          Expanded(
            child: Label(
              model: LabelModel(
                color: CompanyColorPointer.primary3,
                textStyle: CompanyTextStylePointer.b2,
                text: localization.ipoDfmImportantInfo,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
