import Foundation

class Constants {
    // Channel
    static let livenessChannel = "LivenessChannel"

    // Initialize
    static let secretKeyArgumentKey = "secretKey"
    static let codeJsonKey = "code"
    static let valueJsonKey = "value"
    static let expiryDateTimeJsonKey = "expiryDateTime"
    static let configDataArgumentKey = "configData"

    // Start Liveness
    static let faceResultJsonKey = "faceResult"
    static let processStatusJsonKey = "processStatus"
    static let faceImageJsonKey = "faceImage"
    static let faceImageHashJsonKey = "faceImageHash"
    static let feedbackMessageJsonKey = "feedbackMessage"
    static let thumbnailJsonKey = "thumbnail"
    static let elapsesTimeSecJsonKey = "elapsesTimeSec"

    static let livenessActionResultJsonKey = "livenessActionResult"
    static let livenessActionJsonKey = "livenessAction"
    static let elapsedSecJsonKey = "elapsedSec"

    static let lastWarningMessageJsonKey = "lastWarningMessage"
}
