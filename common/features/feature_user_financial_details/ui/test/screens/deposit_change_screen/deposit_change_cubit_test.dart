import 'dart:async';
import 'dart:ui';

import 'package:bloc_test/bloc_test.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:common_feature_user_financial_details_api/feature_user_financial_details_api.dart';
import 'package:common_feature_user_financial_details_ui/l10n/user_financial_details_localizations.g.dart';
import 'package:common_feature_user_financial_details_ui/screens/deposit_change_screen/cubit/deposit_change_cubit.dart';
import 'package:common_feature_user_financial_details_ui/screens/deposit_change_screen/cubit/deposit_change_state.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:ui/ota_lokalisation/ota_lokalization.dart';
import 'package:wio_feature_required_action_common_api/feature_required_action_common.dart';
import '../mocks.dart';

void main() {
  late MockUserFinancialDetailsInteractor interactor;
  late UserFinancialDetailsLocalizations localizations;
  late MockNavigationProvider navigationProvider;
  late MockToastMessageProvider toastMessageProvider;
  late ValidationInteractor validationInteractor;
  late FeatureToggleProvider toggleProvider;
  late Logger logger;
  late DepositChangeCubit cubit;

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    localizations =
        await UserFinancialDetailsLocalizations.load(const Locale('en'));
  });

  setUp(() {
    registerFallbackValue(ToastMessageConfigurationFake());
    interactor = MockUserFinancialDetailsInteractor();

    navigationProvider = MockNavigationProvider();
    toastMessageProvider = MockToastMessageProvider();
    validationInteractor = MockValidationInteractor();
    toggleProvider = MockFeatureToggleProvider();
    logger = MockLogger();

    when(
      () => validationInteractor.inputStateStream(),
    ).thenAnswer(
      (_) => Stream.fromIterable(
        [
          const AmountInputState.validating(),
          const AmountInputState.valid(),
        ],
      ),
    );

    cubit = DepositChangeCubit(
      localizations: localizations,
      navigator: navigationProvider,
      toastMessageProvider: toastMessageProvider,
      config: MockConfigDeposit(),
      interactor: interactor,
      validationInteractor: validationInteractor,
      logger: logger,
      featureToggles: toggleProvider,
    );
  });

  test('update deposit success', () async {
    /// Arrange
    const depositAmount = 1000.0;
    const depositResult = DepositChangeResult(amount: 1000.0);

    when(() => interactor.updateMonthlyDeposit(amount: depositAmount))
        .justCompleteAsync();

    /// Act
    cubit.updateDeposit(amount: depositAmount);

    await flushFutures();

    expect(cubit.state.isUpdating, false);
    verify(() => navigationProvider.goBack(depositResult));
    verifyNever(
      () => toastMessageProvider.showToastMessage(any()),
    );
  });

  test('call navigate next on success', () async {
    /// Arrange
    const depositAmount = 1000.0;
    const depositResult = DepositChangeResult(amount: depositAmount);

    final navigateNextCommand = MockNavigateNextCommand();
    const actionType = ActionType.kyc;
    const actionScreen = ActionScreen.estimatedDeposit;
    final requiredActionScreenConfig = RequiredActionScreenConfig(
      actionType: actionType,
      actionScreen: actionScreen,
      navigateNextCommand: navigateNextCommand.call,
    );

    cubit = DepositChangeCubit(
      localizations: localizations,
      navigator: navigationProvider,
      toastMessageProvider: toastMessageProvider,
      config: MockConfigDeposit(),
      interactor: interactor,
      requiredActionScreenConfig: requiredActionScreenConfig,
      validationInteractor: validationInteractor,
      featureToggles: toggleProvider,
      logger: logger,
    );

    when(
      () => interactor.updateMonthlyDeposit(
        amount: depositAmount,
        actionType: actionType,
        actionScreen: actionScreen,
      ),
    ).justCompleteAsync();

    /// Act
    cubit.updateDeposit(amount: depositAmount);

    await flushFutures();

    expect(cubit.state.isUpdating, false);
    verify(
      () => navigateNextCommand.call(
        {'deposit': depositAmount, 'percentage': null},
      ),
    ).calledOnce;
    verifyNever(() => navigationProvider.goBack(depositResult));
    verifyNever(
      () => toastMessageProvider.showToastMessage(any()),
    );
  });

  test('update deposit error', () async {
    /// Arrange
    const depositAmount = 1000.0;

    when(() => interactor.updateMonthlyDeposit(amount: depositAmount))
        .justThrowAsync(Exception('Failed to update Monthly Deposits'));

    /// Act
    cubit.updateDeposit(amount: depositAmount);

    await flushFutures();

    expect(cubit.state.isUpdating, false);

    verifyNever(() => navigationProvider.goBack(depositAmount));
    verify(
      () => toastMessageProvider.showToastMessage(any()),
    ).calledOnce;
  });

  test('navigates back on back press', () async {
    /// Act
    cubit.goBack();

    verify(() => navigationProvider.goBack()).calledOnce;
  });

  final validationInteractorWithError =
      TestValidationInteractorImpl.mockError();
  final validationInteractorWithSuccess =
      TestValidationInteractorImpl.mockSuccess();

  group('onInputChanged', () {
    const testAmount = '100.0';

    blocTest<DepositChangeCubit, DepositChangeState>(
      'emits validating and then failed state when validationInteractor emits '
      'validating and failed states',
      build: () {
        when(
          () => toggleProvider.get<bool>(
            FinancialDetailsFeatureToggles.isInputValidationEnabled,
          ),
        ).thenReturn(true);

        return DepositChangeCubit(
          localizations: localizations,
          navigator: navigationProvider,
          toastMessageProvider: toastMessageProvider,
          config: MockConfigDeposit(),
          interactor: interactor,
          validationInteractor: validationInteractorWithError,
          logger: logger,
          featureToggles: toggleProvider,
        );
      },
      act: (cubit) => cubit.onInputChanged(testAmount),
      expect: () => [
        isA<DepositChangeState>().having(
          (state) => state.inputState,
          'inputState',
          isA<AmountInputStateValidating>(),
        ),
        isA<DepositChangeState>().having(
          (state) => state.inputState,
          'inputState',
          isA<AmountInputStateFailed>()
              .having(
                (state) => state.error.message,
                'error.message',
                'Error message',
              )
              .having(
                (state) => state.error.code,
                'error.code',
                ValidationErrorCode.beInvalidFinancialReferenceInfoMissing,
              ),
        ),
      ],
      verify: (cubit) {
        expect(cubit.isAmountValid(testAmount), false);
      },
    );

    blocTest<DepositChangeCubit, DepositChangeState>(
      'emits validating and then valid state when validationInteractor emits '
      'validating and valid states',
      build: () {
        when(
          () => toggleProvider.get<bool>(
            FinancialDetailsFeatureToggles.isInputValidationEnabled,
          ),
        ).thenReturn(true);

        return DepositChangeCubit(
          localizations: localizations,
          navigator: navigationProvider,
          toastMessageProvider: toastMessageProvider,
          config: MockConfigDeposit(),
          interactor: interactor,
          validationInteractor: validationInteractorWithSuccess,
          logger: logger,
          featureToggles: toggleProvider,
        );
      },
      act: (cubit) => cubit.onInputChanged(testAmount),
      expect: () => [
        isA<DepositChangeState>().having(
          (state) => state.inputState,
          'inputState',
          isA<AmountInputStateValidating>(),
        ),
        isA<DepositChangeState>().having(
          (state) => state.inputState,
          'inputState',
          isA<AmountInputStateValid>(),
        ),
      ],
      verify: (cubit) {
        expect(cubit.isAmountValid(testAmount), true);
      },
    );
  });
}
