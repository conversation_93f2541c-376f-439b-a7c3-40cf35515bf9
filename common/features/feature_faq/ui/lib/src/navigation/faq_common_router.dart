import 'dart:async';

import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_contact_support_api/common/contact_us_provider.dart';
import 'package:wio_feature_contact_support_api/navigation/contact_us_bottom_sheet_navigation_config.dart';
import 'package:wio_feature_contact_support_ui/feature_contact_support_ui.dart';
import 'package:wio_feature_faq_api/faq_api.dart';
import 'package:wio_feature_faq_ui/src/handlers/faq_content_rate_handler.dart';

// TODO(amitrica): to be removed in https://wiobank.atlassian.net/browse/MU-2046
class FaqCommonRouter {
  final ApplicationType _applicationType;
  final FaqContentRateHandler _faqContentRateHandler;
  final NavigationProvider _navigationProvider;
  final ContactUsProvider _contactUsProvider;
  final Logger _logger;
  final ResponsiveDialogProvider _responsiveDialogProvider;

  const FaqCommonRouter({
    required ApplicationType faqApplicationType,
    required FaqContentRateHandler faqContentRateHandler,
    required Logger logger,
    required NavigationProvider navigationProvider,
    required ContactUsProvider contactUsProvider,
    required ResponsiveDialogProvider responsiveDialogProvider,
  })  : _applicationType = faqApplicationType,
        _faqContentRateHandler = faqContentRateHandler,
        _logger = logger,
        _navigationProvider = navigationProvider,
        _contactUsProvider = contactUsProvider,
        _responsiveDialogProvider = responsiveDialogProvider,
        super();

  ApplicationType get applicationType => _applicationType;

  // TODO(amitrica): to be removed after https://wiobank.atlassian.net/browse/MU-2046
  Future<void> openContactUsBottomSheet() async {
    final result = await _responsiveDialogProvider
        .showBottomSheetOrDialog<ContactUsBottomSheetResult?>(
      content: const ContactUsBottomSheet(),
      config: const ResponsiveModalConfig(
        featureName: CommonFaqFeatureNavigationConfig.name,
      ),
    );

    if (result == null) return;

    try {
      await _contactUsProvider.contactUsSelector(result.feedback);
    } on Object catch (error) {
      _logger.debug(error.toString());
    }
  }

  void onFaqItemTap(
    FaqItem faqItem, {
    bool useResponsiveWrapper = false,
    ScreenNavigationConfig? fromScreen,
  }) {
    unawaited(_faqContentRateHandler.rateFaqContent(faqItem.faqId));
    _navigationProvider.push(
      FAQsQuestionDetailScreenNavigationConfig(
        question: faqItem.question,
        answer: faqItem.answer,
        faqId: faqItem.faqId,
        videoSupportLink: '',
        mobileInteractionText: '',
        useResponsiveWrapper: useResponsiveWrapper,
        fromScreen: fromScreen,
      ),
    );
  }

  void onSearchIconPressed() {
    _navigationProvider.push(
      const FAQsSearchQuestionScreenNavigationConfig(),
    );
  }

  void onCloseIconPressed({ScreenNavigationConfig? fromScreen}) {
    // If fromScreen is not null, just pop routes until that screen config.
    if (fromScreen != null) {
      return _navigationProvider.backUntil(fromScreen);
    }

    if (_applicationType == ApplicationType.sme) {
      goBack();
      goBack();
      goBack();
      return;
    }
    _navigationProvider.backUntilFeatureInitScreen(
      CommonFaqFeatureNavigationConfig.name,
    );
  }

  void goBack() {
    if (!_navigationProvider.canPop()) return;
    _navigationProvider.goBack();
  }
}
