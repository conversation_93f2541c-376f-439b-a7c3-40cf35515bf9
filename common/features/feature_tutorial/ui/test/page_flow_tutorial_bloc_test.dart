import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_common_feature_tutorial_api/feature_tutorial_api.dart';
import 'package:wio_common_feature_tutorial_ui/src/analytics/page_flow_tutorial_analytics.dart';
import 'package:wio_common_feature_tutorial_ui/src/screens/page_flow_tutorial/page_flow_tutorial_cubit.dart';
import 'package:wio_common_feature_tutorial_ui/src/screens/page_flow_tutorial/page_flow_tutorial_state.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';

import 'mocks.dart';

void main() {
  late PageFlowTutorialCubit tutorialCubit;
  late NavigationProvider navigationProvider;
  late PageFlowTutorialAnalytics analytics;

  const assetsPackageName = 'assetsPackageName';

  final tutorialSteps = [
    const PageFlowTutorialStepModel(
      assetPath: 'assetPath0',
      title: 'Title0',
      buttonTitle: 'ButtonTitle0',
      highlightedTitle: 'highlightedTitle0',
    ),
    const PageFlowTutorialStepModel(
      assetPath: 'assetPath1',
      title: 'Title1',
      subtitle: 'Subtitle1',
      buttonTitle: 'ButtonTitle1',
      highlightedTitle: 'highlightedTitle1',
    ),
    const PageFlowTutorialStepModel(
      assetPath: 'assetPath2',
      title: 'Title2',
      subtitle: 'Subtitle2',
      buttonTitle: 'ButtonTitle2',
      highlightedTitle: 'highlightedTitle2',
    ),
    const PageFlowTutorialStepModel(
      assetPath: 'assetPath3',
      title: 'Title3',
      subtitle: 'Subtitle3',
      buttonTitle: 'ButtonTitle3',
      highlightedTitle: 'highlightedTitle3',
    ),
  ];

  final tutorialConfig = PageFlowTutorialConfig(
    assetsPackageName: assetsPackageName,
    steps: tutorialSteps,
  );

  setUp(
    () {
      navigationProvider = MockNavigationProvider();
      analytics = MockPageFlowTutorialAnalytics();
      tutorialCubit = PageFlowTutorialCubit(
        config: tutorialConfig,
        analytics: analytics,
        navigatorProvider: navigationProvider,
      );

      registerFallbackValue(PageFlowTutorialPayloadFake());
    },
  );

  group(
    'Tutorial cubit tests',
    () {
      blocTest<PageFlowTutorialCubit, PageFlowTutorialState>(
        'Validate initial tutorial cubit state',
        build: () => tutorialCubit,
        verify: (cubit) {
          expect(
            cubit.state,
            PageFlowTutorialState(
              assetsPackageName: assetsPackageName,
              steps: tutorialSteps,
              isFirstStepDifferent: true,
            ),
          );
        },
      );

      blocTest<PageFlowTutorialCubit, PageFlowTutorialState>(
        'Validate that nextStep method increase index in state',
        build: () => tutorialCubit,
        act: (cubit) => cubit.nextStep(),
        verify: (cubit) {
          expect(
            cubit.state,
            PageFlowTutorialState(
              assetsPackageName: assetsPackageName,
              steps: tutorialSteps,
              isFirstStepDifferent: true,
              index: 1,
            ),
          );
        },
      );

      blocTest<PageFlowTutorialCubit, PageFlowTutorialState>(
        'Validate that previousStep method decreases index in state',
        build: () => tutorialCubit,
        act: (cubit) => cubit.previousStep(),
        seed: () => PageFlowTutorialState(
          assetsPackageName: assetsPackageName,
          steps: tutorialSteps,
          isFirstStepDifferent: true,
          index: 1,
        ),
        verify: (cubit) {
          expect(
            cubit.state,
            PageFlowTutorialState(
              assetsPackageName: assetsPackageName,
              steps: tutorialSteps,
              isFirstStepDifferent: true,
            ),
          );
        },
      );

      blocTest<PageFlowTutorialCubit, PageFlowTutorialState>(
        'Validate that the previous step does nothing if this first step',
        build: () => tutorialCubit,
        act: (cubit) => cubit.previousStep(),
        verify: (cubit) {
          expect(
            cubit.state,
            PageFlowTutorialState(
              assetsPackageName: assetsPackageName,
              steps: tutorialSteps,
              isFirstStepDifferent: true,
            ),
          );
        },
      );

      blocTest<PageFlowTutorialCubit, PageFlowTutorialState>(
        'Validate that tutorial closes if we call nextStep on the last step',
        build: () => tutorialCubit,
        seed: () => PageFlowTutorialState(
          assetsPackageName: assetsPackageName,
          steps: tutorialSteps,
          isFirstStepDifferent: true,
          index: tutorialSteps.length - 1,
        ),
        act: (cubit) => cubit.nextStep(),
        verify: (_) => verify(() => navigationProvider.goBack(true)).calledOnce,
      );

      blocTest<PageFlowTutorialCubit, PageFlowTutorialState>(
        'Validate that tutorial closes if we press close button',
        build: () => tutorialCubit,
        act: (cubit) => cubit.closeTutorial(),
        verify: (_) {
          verify(() => navigationProvider.goBack(true)).calledOnce;
          verify(() => analytics.close(any())).calledOnce;
        },
      );

      blocTest<PageFlowTutorialCubit, PageFlowTutorialState>(
        'Validate that analytics called when we press next button',
        build: () => tutorialCubit,
        act: (cubit) => cubit.onNextButtonPressed(),
        verify: (_) {
          verify(() => analytics.next(any())).calledOnce;
        },
      );
    },
  );
}
