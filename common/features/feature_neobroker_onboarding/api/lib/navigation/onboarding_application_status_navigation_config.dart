import 'package:feature_neobroker_onboarding_api/index.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

part 'onboarding_application_status_navigation_config.freezed.dart';

@freezed
class OnboardingAplicationStatusScreenNavigationConfig
    extends ScreenNavigationConfig
    with _$OnboardingAplicationStatusScreenNavigationConfig {
  const factory OnboardingAplicationStatusScreenNavigationConfig({
    required String individualId,
  }) = _OnboardingAplicationStatusScreenNavigationConfig;

  const OnboardingAplicationStatusScreenNavigationConfig._()
      : super(
          feature: OnboardingFeatureNavigationConfig.name,
          id: screenId,
        );

  static const screenId = 'onboarding_application_status';

  @override
  String toString() => 'OnboardingAplicationStatusScreenNavigationConfig';
}
