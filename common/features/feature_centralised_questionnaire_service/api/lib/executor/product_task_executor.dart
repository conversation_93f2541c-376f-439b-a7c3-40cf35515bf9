import 'package:feature_remote_file_api/remote_file.dart';
import 'package:wio_feature_centralised_questionnaire_service_api/centralised_questionnaire_service_api.dart';

/// Progress callback for doc upload
typedef ProgressCallback = void Function(int, int);

/// The Backend Drive flow is a common module
/// Sometimes we need to perform some product specific task for SME and Retail
/// In that case, it will be hard to execute from common module
/// So, [ProductTaskExecutor] will be responsible for this
/// Each product module will extend this and execute the task
abstract class ProductTaskExecutor {
  /// Perform the logout operation
  Future<void> logout();

  /// Uploads document to the server
  ///
  /// [UploadDocumentInput] document to be uploaded
  /// Returns the uploaded file path on the server
  Future<String> uploadDocument({
    required DocumentUploadComponentConfig componentConfig,
    required List<int> fileBytes,
    required String fileName,
    ProgressCallback? progressCallback,
  });

  /// Opens the subscription plan screen for the specific product
  /// Returns true if subscription was successfully selected,
  /// false if cancelled/failed, null if operation completed without selection
  Future<bool?> openSubscriptionPlan();
}
