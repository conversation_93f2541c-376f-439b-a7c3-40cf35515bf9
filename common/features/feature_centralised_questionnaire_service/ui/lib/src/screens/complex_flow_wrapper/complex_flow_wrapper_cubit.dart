import 'package:logging_api/logging.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_centralised_questionnaire_service_api/centralised_questionnaire_service_api.dart';
import 'package:wio_feature_centralised_questionnaire_service_ui/src/screens/complex_flow_wrapper/complex_flow_wrapper_state.dart';
import 'package:wio_feature_status_view_api/feature_status_view.dart';

class ComplexFlowWrapperCubit extends BaseCubit<ComplexFlowWrapperState> {
  final ComplexFlowType _complexFlowType;
  final NavigationProvider _navigationProvider;
  final ProductTaskExecutor _productTaskExecutor;
  final CQSInteractor _cqsInteractor;
  final CQSMediator _cqsMediator;
  final Logger _logger;
  final CommonLocalizations _commonLocalizations;

  ComplexFlowWrapperCubit({
    required ComplexFlowType complexFlowType,
    required NavigationProvider navigationProvider,
    required ProductTaskExecutor productTaskExecutor,
    required CQSInteractor cqsInteractor,
    required CQSMediator cqsMediator,
    required Logger logger,
    required CommonLocalizations commonLocalizations,
  })  : _complexFlowType = complexFlowType,
        _navigationProvider = navigationProvider,
        _productTaskExecutor = productTaskExecutor,
        _cqsInteractor = cqsInteractor,
        _cqsMediator = cqsMediator,
        _logger = logger,
        _commonLocalizations = commonLocalizations,
        super(const ComplexFlowWrapperState());

  void initialize() {
    _navigateToComplexFlow();
  }

  void goBack() {
    _moveBack();
  }

  Future<void> _navigateToComplexFlow() async {
    if (_complexFlowType == ComplexFlowType.priceSelection) {
      await openPricingSelection();
    }
  }

  /// Opens the pricing selection flow and handles the result.
  /// If the result is successful from SubscriptionCubit,
  /// calls the fetchQuestion API.
  Future<void> openPricingSelection() async {
    final result = await _productTaskExecutor.openSubscriptionPlan();

    // Handle the result from subscription selection
    // true = success, false = failed/cancelled, null = completed without selection
    if (result ?? false) {
      await _handleSuccessfulSubscriptionSelection();
    } else {
      _moveBack();
    }
  }

  /// Handles successful subscription selection by calling
  /// the fetchQuestion API.
  Future<void> _handleSuccessfulSubscriptionSelection() async {
    try {
      final workFlowInstanceId = _cqsMediator.getWorkFlowInstanceId();
      final sectionId = _cqsMediator.getSectionId();
      final questionId = _cqsMediator.getQuestionId();

      final request = FetchQuestionRequestModel(
        questionnaireFlowInstanceId: workFlowInstanceId,
        sectionId: sectionId,
        questionId: questionId,
      );

      final nextQuestion = await safeExecute(
        _cqsInteractor.fetchQuestion(request),
      );

      if (!isClosed) {
        // Move to the next step in the CQS flow
        _cqsMediator.moveToNextStep(nextQuestion: nextQuestion);
      }
    } on Object catch (error, stackTrace) {
      _logError(error, stackTrace: stackTrace);
      await _handleError(error);
    }
  }

  void _logError(
    Object error, {
    StackTrace? stackTrace,
  }) {
    if (error is CQSKnownException) {
      _logger.info(error.message, error: error, stackTrace: stackTrace);
    } else {
      _logger.error(
        error.toString(),
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  Future<void> _handleError(Object error) async {
    String? errorMessage;

    if (error is CQSKnownException) {
      errorMessage = error.message;
    } else if (error is CQSGeneralException) {
      errorMessage = error.message;
    }

    await _showErrorView(message: errorMessage);
  }

  Future<void> _showErrorView({
    String? message,
  }) async {
    await _navigationProvider.navigateTo(
      StatusViewFeatureNavigationConfig.error(
        icon: const GraphicAssetPointer.pictogram(
          CompanyPictogramPointer.validation_failure,
        ),
        title: _commonLocalizations.transactionCancelStatusErrorTitle,
        subTitleModel: CompanyRichTextModel(
          text: message ?? _commonLocalizations.somethingWentWrongMessage,
        ),
        bottomConfig: StatusPageBottomConfig.button(
          primaryButtonTitle: _commonLocalizations.close,
        ),
      ),
    );
    _moveBack();
  }

  void _moveBack() {
    _navigationProvider.goBack();
    _cqsMediator.moveToPreviousStep();
  }

  @override
  String toString() => 'ComplexFlowWrapperCubit';
}
