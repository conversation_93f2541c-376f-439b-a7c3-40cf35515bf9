import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:ui/page/base_page.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_centralised_questionnaire_service_api/centralised_questionnaire_service_api.dart';
import 'package:wio_feature_centralised_questionnaire_service_ui/src/screens/complex_flow_wrapper/complex_flow_wrapper_cubit.dart';
import 'package:wio_feature_centralised_questionnaire_service_ui/src/screens/complex_flow_wrapper/complex_flow_wrapper_state.dart';

class ComplexFlowWrapperPage
    extends BasePage<ComplexFlowWrapperState, ComplexFlowWrapperCubit> {
  final ComplexFlowType complexFlowType;

  const ComplexFlowWrapperPage({
    required this.complexFlowType,
    super.key,
  });

  @override
  ComplexFlowWrapperCubit createBloc() => DependencyProvider.getWithParams<
          ComplexFlowWrapperCubit, ComplexFlowType, void>(
        param1: complexFlowType,
      );

  @override
  void initBloc(ComplexFlowWrapperCubit bloc) {
    super.initBloc(bloc);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      bloc.initialize();
    });
  }

  @override
  Widget buildPage(
    BuildContext context,
    ComplexFlowWrapperCubit bloc,
    ComplexFlowWrapperState state,
  ) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: context.colorStyling.background1,
        appBar: TopNavigation(
          const TopNavigationModel(
            state: TopNavigationState.positive,
          ),
          onLeftIconPressed: bloc.goBack,
        ),
        body: const SizedBox.shrink(),
      ),
    );
  }
}
