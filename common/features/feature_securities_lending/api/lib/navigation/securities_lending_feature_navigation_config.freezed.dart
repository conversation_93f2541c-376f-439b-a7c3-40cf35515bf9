// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'securities_lending_feature_navigation_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SecuritiesLendingFeatureNavigationConfig {
  ScreenNavigationConfig get destination => throw _privateConstructorUsedError;

  /// Create a copy of SecuritiesLendingFeatureNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SecuritiesLendingFeatureNavigationConfigCopyWith<
          SecuritiesLendingFeatureNavigationConfig>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SecuritiesLendingFeatureNavigationConfigCopyWith<$Res> {
  factory $SecuritiesLendingFeatureNavigationConfigCopyWith(
          SecuritiesLendingFeatureNavigationConfig value,
          $Res Function(SecuritiesLendingFeatureNavigationConfig) then) =
      _$SecuritiesLendingFeatureNavigationConfigCopyWithImpl<$Res,
          SecuritiesLendingFeatureNavigationConfig>;
  @useResult
  $Res call({ScreenNavigationConfig destination});
}

/// @nodoc
class _$SecuritiesLendingFeatureNavigationConfigCopyWithImpl<$Res,
        $Val extends SecuritiesLendingFeatureNavigationConfig>
    implements $SecuritiesLendingFeatureNavigationConfigCopyWith<$Res> {
  _$SecuritiesLendingFeatureNavigationConfigCopyWithImpl(
      this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SecuritiesLendingFeatureNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? destination = null,
  }) {
    return _then(_value.copyWith(
      destination: null == destination
          ? _value.destination
          : destination // ignore: cast_nullable_to_non_nullable
              as ScreenNavigationConfig,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SecuritiesLendingFeatureNavigationConfigImplCopyWith<$Res>
    implements $SecuritiesLendingFeatureNavigationConfigCopyWith<$Res> {
  factory _$$SecuritiesLendingFeatureNavigationConfigImplCopyWith(
          _$SecuritiesLendingFeatureNavigationConfigImpl value,
          $Res Function(_$SecuritiesLendingFeatureNavigationConfigImpl) then) =
      __$$SecuritiesLendingFeatureNavigationConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({ScreenNavigationConfig destination});
}

/// @nodoc
class __$$SecuritiesLendingFeatureNavigationConfigImplCopyWithImpl<$Res>
    extends _$SecuritiesLendingFeatureNavigationConfigCopyWithImpl<$Res,
        _$SecuritiesLendingFeatureNavigationConfigImpl>
    implements _$$SecuritiesLendingFeatureNavigationConfigImplCopyWith<$Res> {
  __$$SecuritiesLendingFeatureNavigationConfigImplCopyWithImpl(
      _$SecuritiesLendingFeatureNavigationConfigImpl _value,
      $Res Function(_$SecuritiesLendingFeatureNavigationConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of SecuritiesLendingFeatureNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? destination = null,
  }) {
    return _then(_$SecuritiesLendingFeatureNavigationConfigImpl(
      destination: null == destination
          ? _value.destination
          : destination // ignore: cast_nullable_to_non_nullable
              as ScreenNavigationConfig,
    ));
  }
}

/// @nodoc

class _$SecuritiesLendingFeatureNavigationConfigImpl
    extends _SecuritiesLendingFeatureNavigationConfig {
  const _$SecuritiesLendingFeatureNavigationConfigImpl(
      {required this.destination})
      : super._();

  @override
  final ScreenNavigationConfig destination;

  /// Create a copy of SecuritiesLendingFeatureNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SecuritiesLendingFeatureNavigationConfigImplCopyWith<
          _$SecuritiesLendingFeatureNavigationConfigImpl>
      get copyWith =>
          __$$SecuritiesLendingFeatureNavigationConfigImplCopyWithImpl<
              _$SecuritiesLendingFeatureNavigationConfigImpl>(this, _$identity);
}

abstract class _SecuritiesLendingFeatureNavigationConfig
    extends SecuritiesLendingFeatureNavigationConfig {
  const factory _SecuritiesLendingFeatureNavigationConfig(
          {required final ScreenNavigationConfig destination}) =
      _$SecuritiesLendingFeatureNavigationConfigImpl;
  const _SecuritiesLendingFeatureNavigationConfig._() : super._();

  @override
  ScreenNavigationConfig get destination;

  /// Create a copy of SecuritiesLendingFeatureNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SecuritiesLendingFeatureNavigationConfigImplCopyWith<
          _$SecuritiesLendingFeatureNavigationConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}
