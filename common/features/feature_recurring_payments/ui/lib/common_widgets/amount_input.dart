import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:neobroker_models/models.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_recurring_payments_ui/l10n/recurring_payments_localizations.g.dart';

class AmountInput extends StatefulWidget {
  final Currency currency;
  final NumberRange? validation;
  final Money? initialAmountValue;
  final bool autoFocus;
  final bool enableDecimals;
  final void Function(
    Money? amount, {
    required bool isError,
  }) onAmountChange;

  const AmountInput({
    required this.currency,
    required this.onAmountChange,
    this.validation,
    this.autoFocus = false,
    this.initialAmountValue,
    this.enableDecimals = false,
    super.key,
  });

  @override
  State<AmountInput> createState() => _AmountInputState();
}

class _AmountInputState extends State<AmountInput> {
  final _amountController = TextEditingController();
  late TextInputFormatter formatter;

  num? _amount;

  @override
  void initState() {
    super.initState();

    formatter = widget.enableDecimals
        ? const FlexibleNumberFormatter.decimal()
        : CurrencyInputFormatter(decimalDigits: 0);

    final initialAmountValue = widget.initialAmountValue;

    if (initialAmountValue != null) {
      _setInitialAmountValue(initialAmountValue);
    }
    _amountController.addListener(_handleTextChanged);
  }

  void _handleTextChanged() {
    final amountText = _amountController.text.replaceAll(',', '');
    final amount = double.tryParse(amountText);
    setState(() {
      _amount = amount;
      if (amount != null) {
        widget.onAmountChange(
          Money.fromNumWithCurrency(amount, widget.currency),
          isError: _errorValidate() != null,
        );
      } else {
        widget.onAmountChange(null, isError: true);
      }
    });
  }

  void _setInitialAmountValue(Money amount) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final value = TextEditingValue(text: amount.stringAmount);
      _amountController.value = formatter.formatEditUpdate(value, value);
      _handleTextChanged();
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localization = RecurringPaymentsLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: CurrencyInputField(
        CurrencyInputFieldModel(
          flagLabel: widget.currency.code,
          label: localization.recurringCreationInputLabel,
          error: _errorValidate(),
          enableDecimal: widget.enableDecimals,
        ),
        controller: _amountController,
        autofocus: widget.autoFocus,
        inputFormatters: [formatter],
      ),
    );
  }

  String? _errorValidate() {
    final localization = RecurringPaymentsLocalizations.of(context);
    final minAmountString =
        widget.validation?.min.value.replaceAll(',', '') ?? '';
    final minAmount = num.tryParse(minAmountString);
    final amount = _amount;

    if (minAmount != null && amount != null) {
      if (minAmount > amount) {
        return localization.recurringValidationMinAmount(
          Money.fromNumWithCurrency(minAmount, widget.currency)
              .toCodeOnRightFormat(),
        );
      }
    }
    final maxAmountString =
        widget.validation?.max.value.replaceAll(',', '') ?? '';
    final maxAmount = num.tryParse(maxAmountString);
    if (maxAmount != null && amount != null) {
      if (maxAmount < amount) {
        return localization.recurringValidationMaxAmount(
          Money.fromNumWithCurrency(maxAmount, widget.currency)
              .toCodeOnRightFormat(),
        );
      }
    }

    return null;
  }
}
