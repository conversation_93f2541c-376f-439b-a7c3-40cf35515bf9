// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.
// @dart=2.12
// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = MessageLookup();

typedef String? MessageIfAbsent(String? messageStr, List<Object>? args);

class MessageLookup extends MessageLookupByLibrary {
  @override
  String get localeName => 'ar';

  static m0(day) => "${day} من كل أسبوعين";

  static m1(day) => "كل ثانية ${day}";

  static m2(day) => "${day} ش";

  static m3(day) => "${day} ث";

  static m4(day) => "${day} الثاني";

  static m5(day) => "${day} الثالث";

  static m6(years) => "${years} سنوات";

  static m7(years) => "أقصى مدة هي ${years} سنة";

  static m8(years) => "الحد الأدنى للمدة هو ${years} سنة";

  static m9(day) => "${day} من كل شهر";

  static m10(day) => "في ${day} من كل شهر";

  static m11(day) => "${day} من شهر يناير وأبريل ويوليو وأكتوبر";

  static m12(day) =>
      "في ${day} من الشهر الأول من كل ربع سنة (يناير، إبريل، يوليو، أكتوبر)";

  static m13(amount) => "الحد الأقصى للمبلغ ${amount}";

  static m14(quantity) => "أقصى كمية ${quantity}";

  static m15(amount) => "الحد الأدنى للمبلغ ${amount}";

  static m16(quantity) => "الحد الأدنى للكمية ${quantity}";

  static m17(day) => "${day} من كل أسبوع";

  static m18(day) => "كل ${day}";

  @override
  final Map<String, dynamic> messages =
      _notInlinedMessages(_notInlinedMessages);

  static Map<String, dynamic> _notInlinedMessages(_) => {
        'recurringAccountSavingSpace':
            MessageLookupByLibrary.simpleMessage('توفير المساحة'),
        'recurringAccountUae':
            MessageLookupByLibrary.simpleMessage('الإمارات العربية المتحدة'),
        'recurringAccountUsd':
            MessageLookupByLibrary.simpleMessage('دولار أمريكي'),
        'recurringActionTypeDeleteLabel':
            MessageLookupByLibrary.simpleMessage('إزالة الاستثمارات المتكررة'),
        'recurringActionTypeEditLabel':
            MessageLookupByLibrary.simpleMessage('تحرير الاستثمارات المتكررة'),
        'recurringBiweeklyDescription': m0,
        'recurringBiweeklyDescriptionConfirm': m1,
        'recurringBiweeklyLabel':
            MessageLookupByLibrary.simpleMessage('كل أسبوعين'),
        'recurringConnectedAccount':
            MessageLookupByLibrary.simpleMessage('حساب متصل'),
        'recurringCreationActionText':
            MessageLookupByLibrary.simpleMessage('التالي'),
        'recurringCreationInputLabel':
            MessageLookupByLibrary.simpleMessage('مبلغ الاستثمار المتكرر'),
        'recurringCreationSecondaryButtonText':
            MessageLookupByLibrary.simpleMessage('تخطي في الوقت الراهن'),
        'recurringCreationSelectFrequencyError':
            MessageLookupByLibrary.simpleMessage('حدد التردد'),
        'recurringDayFirst': m2,
        'recurringDayOrderly': m3,
        'recurringDaySecond': m4,
        'recurringDayThird': m5,
        'recurringDeletionPrimaryCta':
            MessageLookupByLibrary.simpleMessage('إزالة الاستثمارات المتكررة'),
        'recurringDeletionSecondaryCta': MessageLookupByLibrary.simpleMessage(
            'حافظ على الاستثمارات المتكررة'),
        'recurringDeletionTitle': MessageLookupByLibrary.simpleMessage(
            'هل أنت متأكد أنك تريد إزالة الاستثمارات المتكررة؟'),
        'recurringDuration': MessageLookupByLibrary.simpleMessage('مدة'),
        'recurringDurationActionText':
            MessageLookupByLibrary.simpleMessage('يتأكد'),
        'recurringDurationSubTitle': MessageLookupByLibrary.simpleMessage(
            'يمكنك دائمًا إيقاف الدفعات المتكررة وإعادة تشغيلها.'),
        'recurringDurationTitle': MessageLookupByLibrary.simpleMessage(
            'قم بإعداد مدة الدفعات المتكررة'),
        'recurringDurationYears': m6,
        'recurringEditActionText':
            MessageLookupByLibrary.simpleMessage('حفظ التغييرات'),
        'recurringEditActionsTitle':
            MessageLookupByLibrary.simpleMessage('ماذا تريد ان تفعل؟'),
        'recurringEditFailureLabel': MessageLookupByLibrary.simpleMessage(
            'لم يتم حفظ التكوينات. حاول مرة اخرى.'),
        'recurringFrequency': MessageLookupByLibrary.simpleMessage('تكرار'),
        'recurringFrequencySelectionCta':
            MessageLookupByLibrary.simpleMessage('التالي'),
        'recurringFrequencySelectionTitle':
            MessageLookupByLibrary.simpleMessage('تكرار دفع'),
        'recurringFriday': MessageLookupByLibrary.simpleMessage('جمعة'),
        'recurringMaximumYearsDuration': m7,
        'recurringMinimumYearsDuration': m8,
        'recurringMonday': MessageLookupByLibrary.simpleMessage('الاثنين'),
        'recurringMonthlyDescription': m9,
        'recurringMonthlyDescriptionConfirm': m10,
        'recurringMonthlyLabel': MessageLookupByLibrary.simpleMessage('شهريا'),
        'recurringPaymentsBrokerSelectDateMonthlyDesc':
            MessageLookupByLibrary.simpleMessage(
                'في أي تاريخ من الشهر تريد جدولة استثماراتك الشهرية؟ سيتم تنفيذ الأمر اعتبارًا من الشهر المقبل.'),
        'recurringPaymentsBrokerSelectDateQuarterlyDesc':
            MessageLookupByLibrary.simpleMessage(
                'في أي تاريخ من الشهر تريد جدولة استثماراتك الفصلية؟ سيتم تنفيذ الأمر في الشهر الأول من كل ربع سنة (يناير، أبريل، يوليو، أكتوبر).'),
        'recurringPaymentsSelectDate':
            MessageLookupByLibrary.simpleMessage('حدد تاريخ'),
        'recurringPaymentsSelectDateBiweeklyDesc':
            MessageLookupByLibrary.simpleMessage(
                'في أي يوم من أيام الأسبوع ترغب في جدولة استثماراتك كل أسبوعين؟ سيتم تنفيذ الأوامر كل أسبوعين في اليوم المحدد، ابتداءً من الأسبوع المقبل.'),
        'recurringPaymentsSelectDateMonthlyDesc':
            MessageLookupByLibrary.simpleMessage(
                'في أي تاريخ من الشهر تريد جدولة الاستثمارات؟ سيتم الدفعة الأولى الشهر المقبل.'),
        'recurringPaymentsSelectDateQuarterlyDesc':
            MessageLookupByLibrary.simpleMessage(
                'في أي تاريخ من الشهر تريد جدولة الاستثمارات؟ سيتم الدفعة الأولى الشهر المقبل. سيحدث التالي في 3 أشهر.'),
        'recurringPaymentsSelectDateWeeklyDesc':
            MessageLookupByLibrary.simpleMessage(
                'في أي يوم من الأسبوع ترغب في جدولة استثماراتك الأسبوعية؟ سيتم تنفيذ الأوامر اعتبارا من الأسبوع المقبل.'),
        'recurringQuantityInputLabel':
            MessageLookupByLibrary.simpleMessage('حجم الاستثمار المتكرر'),
        'recurringQuarterlyDescription': m11,
        'recurringQuarterlyDescriptionConfirm': m12,
        'recurringQuarterlyLabel': MessageLookupByLibrary.simpleMessage('ربعي'),
        'recurringSaturday': MessageLookupByLibrary.simpleMessage('السبت'),
        'recurringScreenSubTitle': MessageLookupByLibrary.simpleMessage(
            'ويمكنك تعديلها أو إيقافها في أي وقت.'),
        'recurringScreenTitle': MessageLookupByLibrary.simpleMessage(
            'قم بإعداد الاستثمارات المتكررة'),
        'recurringSelectAccountDescription': MessageLookupByLibrary.simpleMessage(
            'سنقوم تلقائيًا بإجراء جميع الدفعات المتكررة من هذا الحساب. إذا قمت بحذف أي من هذه الحسابات، فسنستخدم حسابك بالدولار الأمريكي كحساب افتراضي.'),
        'recurringSelectAccountTitle':
            MessageLookupByLibrary.simpleMessage('حدد الحساب المتصل'),
        'recurringSunday': MessageLookupByLibrary.simpleMessage('الأحد'),
        'recurringThursday': MessageLookupByLibrary.simpleMessage('يوم الخميس'),
        'recurringTncs':
            MessageLookupByLibrary.simpleMessage('الأحكام والشروط'),
        'recurringTncsReadLabel': MessageLookupByLibrary.simpleMessage(
            'بالنقر على \"حفظ التغييرات\"، فإنك توافق على الشروط والأحكام.'),
        'recurringTuesday':
            MessageLookupByLibrary.simpleMessage('يوم الثلاثاء'),
        'recurringValidationMaxAmount': m13,
        'recurringValidationMaxQuantity': m14,
        'recurringValidationMinAmount': m15,
        'recurringValidationMinQuantity': m16,
        'recurringWednesday': MessageLookupByLibrary.simpleMessage('الأربعاء'),
        'recurringWeeklyDescription': m17,
        'recurringWeeklyDescriptionConfirm': m18,
        'recurringWeeklyLabel': MessageLookupByLibrary.simpleMessage('أسبوعي'),
        'recurringYearsLabel': MessageLookupByLibrary.simpleMessage('سنين'),
        'recurringYearsToGrowLabel':
            MessageLookupByLibrary.simpleMessage('مدة الاستثمار')
      };
}
