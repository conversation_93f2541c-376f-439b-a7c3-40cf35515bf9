import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_common_feature_recurring_payments_api/index.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

part 'recurring_payment_deletion_bottom_sheet_config.freezed.dart';

@freezed
class RecurringPaymentDeletionBottomSheetConfig
    with _$RecurringPaymentDeletionBottomSheetConfig
    implements BottomSheetNavigationConfig<Object> {
  const RecurringPaymentDeletionBottomSheetConfig._();

  const factory RecurringPaymentDeletionBottomSheetConfig({
    required String delegateId,
    required String assetId,
  }) = _RecurringPaymentDeletionBottomSheetConfig;

  @override
  String get feature => RecurringPaymentsFeatureNavigationConfig.name;

  @override
  String toString() => 'RecurringPaymentDeletionBottomSheetConfig{}';
}
