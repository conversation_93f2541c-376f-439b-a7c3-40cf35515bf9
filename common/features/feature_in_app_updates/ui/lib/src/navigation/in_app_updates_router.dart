import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_in_app_updates_ui/src/navigation/configs/in_app_update_consent_bottom_sheet_config.dart';
import 'package:wio_feature_in_app_updates_ui/src/widgets/in_app_update_consent_bottom_sheet.dart';

final class InAppUpdatesRouter extends NavigationRouter {
  @override
  Future<T?> showBottomSheet<T>(
    BuildContext context,
    BottomSheetNavigationConfig<T> config,
    RouteSettings routeSettings,
  ) =>
      switch (config) {
        InAppUpdateConsentBottomSheetConfig() =>
          CompanyBottomSheet.showRetailMobileThemeBasedAdaptiveModal<T>(
            context: context,
            routeSettings: routeSettings,
            useRootNavigator: true,
            builder: (_) => const InAppUpdateConsentBottomSheet(),
          ),
        _ => throw Exception('Unknown BottomSheetNavigationConfig: $config'),
      };
}
