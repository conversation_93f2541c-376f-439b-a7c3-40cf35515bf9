import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:domain/stream/exhaust_stream_executor.dart';
import 'package:mocktail/mocktail.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/cubit/extensions/bloc_extensions.dart';
import 'package:wio_common_feature_transaction_api/index.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_faq_api/faq_api.dart';
import 'package:wio_feature_loan_api/domain/adapter/account_interactor_to_loan_domain_adapter.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/l10n/loan_localizations.g.dart';
import 'package:wio_feature_loan_ui/src/handlers/loan_error_handler.dart';
import 'package:wio_feature_loan_ui/src/widgets/autopay_from_saving_spaces_switcher/delegate/account_autopay_from_saving_spaces_switcher_delegate.dart';
import 'package:wio_feature_share_api/feature_share_api.dart';
import 'package:wio_feature_status_view_api/navigation/status_view_feature_navigation_config.dart';

class MockNavigationProvider extends Mock implements NavigationProvider {}

class MockResponsiveDialogProvider extends Mock
    implements ResponsiveDialogProvider {}

class MockLoanLocalizations extends Mock implements LoanLocalizations {}

class MockLoanInteractor extends Mock implements LoanInteractor {}

class MockAutopayFromSavingSpacesSwitcherDelegate extends Mock
    implements AccountAutopayFromSavingSpacesSwitcherDelegate {}

class MockLoanErrorHandler extends Mock implements LoanErrorHandler {}

class MockAccountInteractorToLoanDomainAdapter extends Mock
    implements AccountInteractorToLoanDomainAdapter {}

class MockThingsToKnowDelegate extends Mock implements ThingsToKnowDelegate {}

class MockExhaustStreamExecutor extends Mock implements ExhaustStreamExecutor {
  @override
  void run(StreamFactory bloc) {
    bloc().firstOrNull;
  }
}

class FakeStatusScreenConfig extends Fake
    implements StatusViewFeatureNavigationConfig {
  @override
  String toString() {
    return 'FakeStatusScreenConfig';
  }
}

class FakeScreenNavigationConfig extends Fake
    implements
        // ignore: avoid_implementing_value_types
        ScreenNavigationConfig {
  @override
  String toString() {
    return 'ScreenNavigationConfig';
  }
}

class FakeFeatureNavigationConfig extends Fake
    implements
        // ignore: avoid_implementing_value_types
        FeatureNavigationConfig {
  @override
  String toString() => '';
}

class FakeBottomSheetNavigationConfig extends Fake
    implements
        // ignore: strict_raw_type
        BottomSheetNavigationConfig {
  @override
  String toString() => '';
}

class FakeLoanDocument extends Fake implements LoanDocument {}

class FakeLoanRepaymentEvaluationRequest extends Fake
    implements
        // ignore: avoid_implementing_value_types
        LoanRepaymentEvaluationRequest {
  @override
  bool operator ==(Object other) {
    return true;
  }

  @override
  int get hashCode => 0;
}

class FakeDocumentViewerConfig extends Fake
    implements DocumentViewerDelegateConfig {
  @override
  LoanProductIdentifier get productIdentifier =>
      LoanProductIdentifier.personalLoan;
}

class FakeCrossFile extends Fake implements CrossFileWithData {}

class MockShareProvider extends Mock implements ShareProvider {}

class MockFaqInteractor extends Mock implements FAQsInteractor {}

class MockFeatureToggleProvider extends Mock implements FeatureToggleProvider {}

class MockLoanStatementsInteractor extends Mock
    implements LoanStatementsInteractor {}

class MockDocumentViewerDelegate extends Mock
    implements DocumentViewerDelegate {}

class MockLoanAnalytics extends Mock implements LoanAnalytics {}

class MockSplitTransactionsInteractor extends Mock
    implements SplitTransactionsInteractor {}

class MockTransactionsInteractor extends Mock
    implements TransactionsInteractor {}

class FakeResponsiveModalConfig extends Fake implements ResponsiveModalConfig {}
