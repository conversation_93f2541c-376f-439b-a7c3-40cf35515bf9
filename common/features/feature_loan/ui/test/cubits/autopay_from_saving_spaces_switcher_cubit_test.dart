import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:mocktail/mocktail.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_loan_ui/src/widgets/autopay_from_saving_spaces_switcher/cubit/autopay_from_saving_spaces_switcher_cubit.dart';

import '../mocks.dart';

void main() {
  group('AutopayFromSavingSpacesSwitcherCubit >', () {
    late AutopayFromSavingSpacesSwitcherCubit cubit;
    late MockAutopayFromSavingSpacesSwitcherDelegate delegate;
    late ResponsiveDialogProvider responsiveDialogProvider;
    late MockLoanErrorHandler loanErrorHandler;

    setUp(() {
      delegate = MockAutopayFromSavingSpacesSwitcherDelegate();
      responsiveDialogProvider = MockResponsiveDialogProvider();
      loanErrorHandler = MockLoanErrorHandler();

      cubit = AutopayFromSavingSpacesSwitcherCubit(
        delegate: delegate,
        responsiveDialogProvider: responsiveDialogProvider,
        loanErrorHandler: loanErrorHandler,
      );

      registerFallbackValue(const Placeholder());
    });

    test('initial state is loading', () {
      expect(cubit.state, const AutopayFromSavingSpacesSwitcherState.loading());
    });

    blocTest<AutopayFromSavingSpacesSwitcherCubit,
        AutopayFromSavingSpacesSwitcherState>(
      'initialize sets the correct state',
      build: () => cubit,
      setUp: () {
        when(() => delegate.fetchCurrentValue()).justAnswerAsync(true);
      },
      act: (cubit) => cubit.initialize(),
      expect: () => const [
        AutopayFromSavingSpacesSwitcherState.idle(
          isAutopayFromSavingSpacesEnabled: true,
        ),
      ],
    );

    blocTest<AutopayFromSavingSpacesSwitcherCubit,
        AutopayFromSavingSpacesSwitcherState>(
      'tryToToggleSwitcher toggles the switcher with confirmation '
      'from enabled to disabled',
      build: () => cubit,
      seed: () => const AutopayFromSavingSpacesSwitcherState.idle(
        isAutopayFromSavingSpacesEnabled: true,
      ),
      setUp: () {
        when(
          () => responsiveDialogProvider.showBottomSheetOrDialog<bool>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).justAnswerAsync(true);
        when(() => delegate.switchAutopayFromSavingSpacesTo(value: false))
            .justAnswerAsync(false);
      },
      act: (cubit) => cubit.tryToToggleSwitcher(WioAppType.retailMobile),
      expect: () => [
        const AutopayFromSavingSpacesSwitcherState.processing(
          isAutopayFromSavingSpacesEnabled: true,
        ),
        const AutopayFromSavingSpacesSwitcherState.idle(
          isAutopayFromSavingSpacesEnabled: false,
        ),
      ],
      verify: (cubit) {
        verify(
          () => responsiveDialogProvider.showBottomSheetOrDialog<bool>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).calledOnce;
        verify(
          () => delegate.switchAutopayFromSavingSpacesTo(
            value: any(named: 'value'),
          ),
        ).calledOnce;
      },
    );

    blocTest<AutopayFromSavingSpacesSwitcherCubit,
        AutopayFromSavingSpacesSwitcherState>(
      'tryToToggleSwitcher toggles the switcher with confirmation canceled '
      'from enabled to disabled',
      build: () => cubit,
      seed: () => const AutopayFromSavingSpacesSwitcherState.idle(
        isAutopayFromSavingSpacesEnabled: true,
      ),
      setUp: () {
        when(
          () => responsiveDialogProvider.showBottomSheetOrDialog<bool>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).justAnswerAsync(false);
        when(() => delegate.switchAutopayFromSavingSpacesTo(value: false))
            .justAnswerAsync(false);
      },
      act: (cubit) => cubit.tryToToggleSwitcher(WioAppType.retailMobile),
      expect: () => const <AutopayFromSavingSpacesSwitcherState>[],
      verify: (cubit) {
        verify(
          () => responsiveDialogProvider.showBottomSheetOrDialog<bool>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).calledOnce;
        verifyNever(
          () => delegate.switchAutopayFromSavingSpacesTo(
            value: any(named: 'value'),
          ),
        );
      },
    );

    blocTest<AutopayFromSavingSpacesSwitcherCubit,
        AutopayFromSavingSpacesSwitcherState>(
      'tryToToggleSwitcher toggles the switcher without confirmation '
      'from disabled to enabled',
      build: () => cubit,
      seed: () => const AutopayFromSavingSpacesSwitcherState.idle(
        isAutopayFromSavingSpacesEnabled: false,
      ),
      setUp: () {
        when(() => delegate.switchAutopayFromSavingSpacesTo(value: true))
            .justAnswerAsync(true);
      },
      act: (cubit) => cubit.tryToToggleSwitcher(WioAppType.retailMobile),
      expect: () => [
        const AutopayFromSavingSpacesSwitcherState.processing(
          isAutopayFromSavingSpacesEnabled: false,
        ),
        const AutopayFromSavingSpacesSwitcherState.idle(
          isAutopayFromSavingSpacesEnabled: true,
        ),
      ],
      verify: (cubit) {
        verifyNever(
          () => responsiveDialogProvider.showBottomSheetOrDialog<bool>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        );
        verify(
          () => delegate.switchAutopayFromSavingSpacesTo(
            value: any(named: 'value'),
          ),
        ).calledOnce;
      },
    );
  });
}
