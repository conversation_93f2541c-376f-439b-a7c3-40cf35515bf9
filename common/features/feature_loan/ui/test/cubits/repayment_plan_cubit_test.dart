import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/widgets.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_feature_loan_ui/feature_loan_ui.dart';
import 'package:wio_feature_loan_ui/src/navigation/bottom_sheets/installment_bottomsheet_navigation_config.dart';
import 'package:wio_feature_loan_ui/src/navigation/dialogs/installment_dialog_config.dart';
import 'package:wio_feature_loan_ui/src/screens/repayment/repayment_plan_cubit.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late MockResponsiveDialogProvider mockResponsiveDialogProvider;

  setUpAll(() {
    registerFallbackValue(
      InstallmentDialogNavigationConfig(
        config: InstallmentBottomSheetConfig(
          autopayHour: LoanTestHelpers.autopayHour,
          installment: LoanTestHelpers.upcomingInstallment,
          latePaymentFee: LoanTestHelpers.amount,
        ),
      ),
    );

    registerFallbackValue(
      InstallmentBottomSheetNavigationConfig(
        config: InstallmentBottomSheetConfig(
          autopayHour: LoanTestHelpers.autopayHour,
          installment: LoanTestHelpers.upcomingInstallment,
          latePaymentFee: LoanTestHelpers.amount,
        ),
      ),
    );
    registerFallbackValue(FakeResponsiveModalConfig());
  });

  setUp(() {
    mockResponsiveDialogProvider = MockResponsiveDialogProvider();
  });

  group('RepaymentPlanPageCubit', () {
    blocTest<RepaymentPlanPageCubit, RepaymentPlanState>(
      'emits initial state with upcoming tab and filtered installments',
      build: () => RepaymentPlanPageCubit(
        config: LoanTestHelpers.repaymentPlanPageConfig,
        responsiveDialogProvider: mockResponsiveDialogProvider,
      ),
      verify: (cubit) {
        final initialState = cubit.state;
        expect(initialState.selectedTab, RepaymentTab.upcoming);
        expect(
          initialState.filteredInstallments,
          [LoanTestHelpers.upcomingInstallment],
        );
      },
    );

    blocTest<RepaymentPlanPageCubit, RepaymentPlanState>(
      'emits new state with history tab and filtered installments',
      build: () => RepaymentPlanPageCubit(
        config: LoanTestHelpers.repaymentPlanPageConfig,
        responsiveDialogProvider: mockResponsiveDialogProvider,
      ),
      act: (cubit) => cubit.repaymentTabChanged(RepaymentTab.history),
      expect: () => [
        RepaymentPlanState(
          selectedTab: RepaymentTab.history,
          autopayHour: LoanTestHelpers.autopayHour,
          latePaymentFee: LoanTestHelpers.amount,
          allInstallments: LoanTestHelpers.installments,
          filteredInstallments: [
            LoanTestHelpers.missedInstallment,
            LoanTestHelpers.paidInstallment,
          ],
        ),
      ],
    );

    blocTest<RepaymentPlanPageCubit, RepaymentPlanState>(
      '''emits state with upcoming tab and filters upcoming installments after changing back from history tab''',
      build: () => RepaymentPlanPageCubit(
        config: LoanTestHelpers.repaymentPlanPageConfig,
        responsiveDialogProvider: mockResponsiveDialogProvider,
      ),
      act: (cubit) {
        cubit
          ..repaymentTabChanged(RepaymentTab.history)
          ..repaymentTabChanged(RepaymentTab.upcoming);
      },
      expect: () => [
        RepaymentPlanState(
          selectedTab: RepaymentTab.history,
          allInstallments: LoanTestHelpers.installments,
          autopayHour: LoanTestHelpers.autopayHour,
          latePaymentFee: LoanTestHelpers.amount,
          filteredInstallments: [
            LoanTestHelpers.missedInstallment,
            LoanTestHelpers.paidInstallment,
          ],
        ),
        RepaymentPlanState(
          selectedTab: RepaymentTab.upcoming,
          allInstallments: LoanTestHelpers.installments,
          filteredInstallments: [LoanTestHelpers.upcomingInstallment],
          autopayHour: LoanTestHelpers.autopayHour,
          latePaymentFee: LoanTestHelpers.amount,
        ),
      ],
    );

    blocTest<RepaymentPlanPageCubit, RepaymentPlanState>(
      '''emits state with upcoming tab and filters upcoming installments after changing back from history tab''',
      build: () => RepaymentPlanPageCubit(
        config: LoanTestHelpers.repaymentPlanPageConfig,
        responsiveDialogProvider: mockResponsiveDialogProvider,
      ),
      setUp: () {
        registerFallbackValue(const Placeholder());
        when(
          () => mockResponsiveDialogProvider.showBottomSheetOrDialog<void>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).justCompleteAsync();
      },
      act: (cubit) => cubit
          .showCurrentOutstandingInfo(LoanTestHelpers.loanAccount.balances),
      verify: (_) {
        verify(
          () => mockResponsiveDialogProvider.showBottomSheetOrDialog<void>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).calledOnce;
      },
    );
  });
}
