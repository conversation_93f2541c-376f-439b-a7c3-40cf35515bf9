import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_loan_api/domain/adapter/account_interactor_to_loan_domain_adapter.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/feature_loan_ui.dart';
import 'package:wio_feature_loan_ui/src/screens/installments/payback_setup/cubit/installments_payback_setup_cubit.dart';
import 'package:wio_feature_loan_ui/src/screens/installments/payback_setup/cubit/installments_payback_setup_state.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late SplitTransactionsInteractor interactor;
  late LoanInteractor loanInteractor;
  late Logger logger;
  late NavigationProvider navigationProvider;
  late ResponsiveDialogProvider dialogProvider;
  late InstallmentsPaybackSetupCubit cubit;
  late AccountInteractorToLoanDomainAdapter
      accountInteractorToLoanDomainAdapter;
  late LoanLocalizations loanLocalizations;
  late MockLoanErrorHandler loanErrorHandler;

  setUpAll(() {
    registerFallbackValue(FakeBottomSheetNavigationConfig());
    registerFallbackValue(FakeFeatureNavigationConfig());
    registerFallbackValue(Exception());
    registerFallbackValue(StackTrace.empty);
    registerFallbackValue(const Placeholder());
    registerFallbackValue(
      SplitTransactionDisbursementEvaluationRequest(
        amount: LoanTestHelpers.defaultMoney,
        transactionCoreBankingId: 'transactionCoreBankingId',
        productSubType: ProductSubType.cardPurchase,
      ),
    );
    registerFallbackValue(
      SplitTransactionDisbursementRequest(
        amount: Money.fromNumWithCurrency(10000, Currency.aed),
        transactionCoreBankingId: 'coreBankingIdentifier',
        productSubType: ProductSubType.cardPurchase,
        loanPeriod: const Period(),
      ),
    );
    registerFallbackValue(FakeResponsiveModalConfig());
  });

  setUp(() {
    interactor = MockSplitTransactionsInteractor();
    loanInteractor = MockLoanInteractor();
    logger = MockLogger();
    navigationProvider = MockNavigationProvider();
    dialogProvider = MockResponsiveDialogProvider();
    accountInteractorToLoanDomainAdapter =
        MockAccountInteractorToLoanDomainAdapter();
    loanLocalizations = MockLoanLocalizations();
    loanErrorHandler = MockLoanErrorHandler();

    cubit = InstallmentsPaybackSetupCubit(
      interactor: interactor,
      loanInteractor: loanInteractor,
      logger: logger,
      dialogProvider: dialogProvider,
      navigationProvider: navigationProvider,
      accountInteractorToLoanDomainAdapter:
          accountInteractorToLoanDomainAdapter,
      loanLocalizations: loanLocalizations,
      productSubType: ProductSubType.cardPurchase,
      sourceApp: WioAppType.retailMobile,
      loanErrorHandler: loanErrorHandler,
    );
  });

  group('InstallmentsPaybackSetupCubit >', () {
    blocTest<InstallmentsPaybackSetupCubit, InstallmentsPaybackSetupState>(
      'init should fetch productInfo, evaluate disbursement and debit account',
      build: () => cubit,
      setUp: () {
        when(
          () => interactor.evaluateDisbursement(any()),
        ).justAnswerAsync(LoanTestHelpers.schedule);
        when(
          () => loanInteractor.getProductInfo(subtype: anyNamed('subtype')),
        ).justAnswerAsync(LoanTestHelpers.productInfo);
        when(
          () => accountInteractorToLoanDomainAdapter.fetchDebitAccount(),
        ).justAnswerAsync(LoanTestHelpers.debitAccount);
      },
      act: (cubit) => cubit.init(
        LoanTestHelpers.transaction,
      ),
      expect: () => [
        InstallmentsPaybackSetupState.idle(
          transaction: LoanTestHelpers.transaction,
          installmentOptions: LoanTestHelpers.schedule.installmentOptions!,
          selectedLoanPeriod:
              LoanTestHelpers.schedule.installmentOptions!.first.loanPeriod,
          productInfo: LoanTestHelpers.productInfo,
          debitAccount: LoanTestHelpers.debitAccount,
        ),
      ],
      verify: (_) {
        verify(
          () => interactor.evaluateDisbursement(any()),
        ).calledOnce;
        verify(
          () => loanInteractor.getProductInfo(subtype: anyNamed('subtype')),
        ).calledOnce;
        verify(
          () => accountInteractorToLoanDomainAdapter.fetchDebitAccount(),
        ).calledOnce;
      },
    );

    blocTest<InstallmentsPaybackSetupCubit, InstallmentsPaybackSetupState>(
      'init fails',
      build: () => cubit,
      setUp: () {
        when(
          () => interactor.evaluateDisbursement(any()),
        ).justThrowAsync(Exception());
        when(
          () => loanInteractor.getProductInfo(subtype: anyNamed('subtype')),
        ).justAnswerAsync(LoanTestHelpers.productInfo);
        when(
          () => accountInteractorToLoanDomainAdapter.fetchDebitAccount(),
        ).justAnswerAsync(LoanTestHelpers.debitAccount);
      },
      act: (cubit) => cubit.init(
        LoanTestHelpers.transaction,
      ),
      expect: () => [
        const InstallmentsPaybackSetupState.error(),
      ],
    );

    blocTest<InstallmentsPaybackSetupCubit, InstallmentsPaybackSetupState>(
      'select loan period',
      build: () => cubit,
      seed: () => InstallmentsPaybackSetupState.idle(
        transaction: LoanTestHelpers.transaction,
        installmentOptions: [
          LoanTestHelpers.installmentOption,
          LoanTestHelpers.installmentOption,
        ],
        selectedLoanPeriod: const Period(months: 3),
        productInfo: LoanTestHelpers.productInfo,
        debitAccount: LoanTestHelpers.debitAccount,
      ),
      act: (cubit) => cubit.selectLoanPeriod(const Period(months: 1)),
      expect: () => [
        InstallmentsPaybackSetupState.idle(
          transaction: LoanTestHelpers.transaction,
          installmentOptions: [
            LoanTestHelpers.installmentOption,
            LoanTestHelpers.installmentOption,
          ],
          selectedLoanPeriod: const Period(months: 1),
          productInfo: LoanTestHelpers.productInfo,
          debitAccount: LoanTestHelpers.debitAccount,
        ),
      ],
    );

    blocTest<InstallmentsPaybackSetupCubit, InstallmentsPaybackSetupState>(
      'show InstallmentDetails',
      build: () => cubit,
      seed: () => InstallmentsPaybackSetupState.idle(
        transaction: LoanTestHelpers.transaction,
        installmentOptions: [
          LoanTestHelpers.installmentOption,
        ],
        selectedLoanPeriod: const Period(months: 1),
        productInfo: LoanTestHelpers.productInfo,
        debitAccount: LoanTestHelpers.debitAccount,
      ),
      setUp: () {
        when(
          () => dialogProvider.showBottomSheetOrDialog<void>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).justCompleteAsync();
      },
      act: (cubit) => cubit.showInstallmentDetails(),
      verify: (_) {
        verify(
          () => dialogProvider.showBottomSheetOrDialog<void>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).calledOnce;
      },
    );

    blocTest<InstallmentsPaybackSetupCubit, InstallmentsPaybackSetupState>(
      'show InstallmentDetails logs error if no installment is available',
      build: () => cubit,
      seed: () => InstallmentsPaybackSetupState.idle(
        transaction: LoanTestHelpers.transaction,
        installmentOptions: [
          LoanTestHelpers.installmentOption,
        ],
        selectedLoanPeriod: const Period(months: 3),
        productInfo: LoanTestHelpers.productInfo,
        debitAccount: LoanTestHelpers.debitAccount,
      ),
      setUp: () {
        when(
          () => logger.error(any()),
        ).justComplete();
      },
      act: (cubit) => cubit.showInstallmentDetails(),
      verify: (_) {
        verifyNever(
          () => dialogProvider.showBottomSheetOrDialog<void>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        );
        verify(() => logger.error(any())).calledOnce;
      },
    );

    blocTest<InstallmentsPaybackSetupCubit, InstallmentsPaybackSetupState>(
      'submit',
      build: () => cubit,
      seed: () => InstallmentsPaybackSetupState.idle(
        transaction: LoanTestHelpers.transaction,
        installmentOptions: [
          LoanTestHelpers.installmentOption,
          LoanTestHelpers.installmentOption,
        ],
        selectedLoanPeriod: const Period(months: 1),
        productInfo: LoanTestHelpers.productInfo,
        debitAccount: LoanTestHelpers.debitAccount,
      ),
      setUp: () {
        when(() => interactor.makeDisbursement(any())).thenAnswer((_) async {});
        when(() => navigationProvider.popUntilFirstRoute())
            .thenAnswer((_) async {});
        when(() => navigationProvider.navigateTo<void>(any()))
            .thenAnswer((_) async {
          return;
        });

        when(() => loanLocalizations.transactionLoanSuccessScreenTitle)
            .thenReturn('title');

        when(
          () => loanLocalizations.transactionLoanSuccessScreenSubtitle(
            any(),
            any(),
          ),
        ).thenReturn('subtitle');
        when(() => loanLocalizations.transactionLoanSuccessScreenCta)
            .thenReturn('cta');
        when(() => loanLocalizations.transactionLoanSuccessScreenInfo)
            .thenReturn('info');

        when(
          () => dialogProvider.showBottomSheetOrDialog<bool>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).justAnswerAsync(true);
      },
      act: (cubit) => cubit.submit(),
      expect: () => [
        InstallmentsPaybackSetupState.idle(
          transaction: LoanTestHelpers.transaction,
          installmentOptions: [
            LoanTestHelpers.installmentOption,
            LoanTestHelpers.installmentOption,
          ],
          selectedLoanPeriod: const Period(months: 1),
          productInfo: LoanTestHelpers.productInfo,
          debitAccount: LoanTestHelpers.debitAccount,
          isProcessing: true,
        ),
      ],
      verify: (_) {
        verify(() => interactor.makeDisbursement(any())).called(1);
        verify(() => navigationProvider.popUntilFirstRoute()).called(1);
        verify(() => navigationProvider.navigateTo<void>(any())).called(1);
        verify(
          () => dialogProvider.showBottomSheetOrDialog<bool>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).calledOnce;
      },
    );

    blocTest<InstallmentsPaybackSetupCubit, InstallmentsPaybackSetupState>(
      'submit handle error if fails',
      build: () => cubit,
      seed: () => InstallmentsPaybackSetupState.idle(
        transaction: LoanTestHelpers.transaction,
        installmentOptions: [
          LoanTestHelpers.installmentOption,
          LoanTestHelpers.installmentOption,
        ],
        selectedLoanPeriod: const Period(months: 1),
        productInfo: LoanTestHelpers.productInfo,
        debitAccount: LoanTestHelpers.debitAccount,
      ),
      setUp: () {
        when(
          () => dialogProvider.showBottomSheetOrDialog<bool>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).justAnswerAsync(true);

        when(() => interactor.makeDisbursement(any())).thenThrow(Exception());
      },
      act: (cubit) => cubit.submit(),
      expect: () => [
        InstallmentsPaybackSetupState.idle(
          transaction: LoanTestHelpers.transaction,
          installmentOptions: [
            LoanTestHelpers.installmentOption,
            LoanTestHelpers.installmentOption,
          ],
          selectedLoanPeriod: const Period(months: 1),
          productInfo: LoanTestHelpers.productInfo,
          debitAccount: LoanTestHelpers.debitAccount,
          isProcessing: true,
        ),
        InstallmentsPaybackSetupState.idle(
          transaction: LoanTestHelpers.transaction,
          installmentOptions: [
            LoanTestHelpers.installmentOption,
            LoanTestHelpers.installmentOption,
          ],
          selectedLoanPeriod: const Period(months: 1),
          productInfo: LoanTestHelpers.productInfo,
          debitAccount: LoanTestHelpers.debitAccount,
        ),
      ],
      verify: (_) {
        verify(
          () => loanErrorHandler.handleError(
            any(),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).called(1);
        verify(
          () => dialogProvider.showBottomSheetOrDialog<bool>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).calledOnce;
      },
    );

    blocTest<InstallmentsPaybackSetupCubit, InstallmentsPaybackSetupState>(
      'submit does nothing if bottom sheet returns false',
      build: () => cubit,
      seed: () => InstallmentsPaybackSetupState.idle(
        transaction: LoanTestHelpers.transaction,
        installmentOptions: [
          LoanTestHelpers.installmentOption,
          LoanTestHelpers.installmentOption,
        ],
        selectedLoanPeriod: const Period(months: 1),
        productInfo: LoanTestHelpers.productInfo,
        debitAccount: LoanTestHelpers.debitAccount,
      ),
      setUp: () {
        when(
          () => dialogProvider.showBottomSheetOrDialog<bool>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).justAnswerAsync(false);
      },
      act: (cubit) => cubit.submit(),
      verify: (_) {
        verifyZeroInteractions(loanErrorHandler);
        verifyZeroInteractions(interactor);
        verifyZeroInteractions(navigationProvider);
        verify(
          () => dialogProvider.showBottomSheetOrDialog<bool>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).calledOnce;
      },
    );

    blocTest<InstallmentsPaybackSetupCubit, InstallmentsPaybackSetupState>(
      'submit do nothing if core banking identifier is null',
      build: () => cubit,
      seed: () => InstallmentsPaybackSetupState.idle(
        transaction: LoanTestHelpers.transaction.copyWith(
          coreBankingIdentifier: null,
        ),
        installmentOptions: [
          LoanTestHelpers.installmentOption,
          LoanTestHelpers.installmentOption,
        ],
        selectedLoanPeriod: const Period(months: 1),
        productInfo: LoanTestHelpers.productInfo,
        debitAccount: LoanTestHelpers.debitAccount,
      ),
      act: (cubit) => cubit.submit(),
      verify: (_) {
        verify(
          () => loanErrorHandler.handleError(
            any(),
            message: any(named: 'message'),
          ),
        );
        verifyZeroInteractions(dialogProvider);
        verifyZeroInteractions(interactor);
        verifyZeroInteractions(navigationProvider);
      },
    );

    blocTest<InstallmentsPaybackSetupCubit, InstallmentsPaybackSetupState>(
      'submit do nothing if selected installment option is null',
      build: () => cubit,
      seed: () => InstallmentsPaybackSetupState.idle(
        transaction: LoanTestHelpers.transaction.copyWith(
          coreBankingIdentifier: null,
        ),
        installmentOptions: [],
        selectedLoanPeriod: const Period(months: 1),
        productInfo: LoanTestHelpers.productInfo,
        debitAccount: LoanTestHelpers.debitAccount,
      ),
      act: (cubit) => cubit.submit(),
      verify: (_) {
        verify(
          () => loanErrorHandler.handleError(
            any(),
            message: any(named: 'message'),
          ),
        );
        verifyZeroInteractions(dialogProvider);
        verifyZeroInteractions(interactor);
        verifyZeroInteractions(navigationProvider);
      },
    );

    blocTest<InstallmentsPaybackSetupCubit, InstallmentsPaybackSetupState>(
      'submit do nothing if isProcessing true',
      build: () => cubit,
      seed: () => InstallmentsPaybackSetupState.idle(
        transaction: LoanTestHelpers.transaction,
        installmentOptions: [
          LoanTestHelpers.installmentOption,
          LoanTestHelpers.installmentOption,
        ],
        selectedLoanPeriod: const Period(months: 1),
        productInfo: LoanTestHelpers.productInfo,
        debitAccount: LoanTestHelpers.debitAccount,
        isProcessing: true,
      ),
      act: (cubit) => cubit.submit(),
      verify: (_) {
        verifyZeroInteractions(dialogProvider);
        verifyZeroInteractions(loanErrorHandler);
        verifyZeroInteractions(interactor);
        verifyZeroInteractions(navigationProvider);
      },
    );

    blocTest<InstallmentsPaybackSetupCubit, InstallmentsPaybackSetupState>(
      'submit do nothing if state is not idle',
      build: () => cubit,
      seed: () => const InstallmentsPaybackSetupState.error(),
      act: (cubit) => cubit.submit(),
      verify: (_) {
        verifyZeroInteractions(dialogProvider);
        verifyZeroInteractions(loanErrorHandler);
        verifyZeroInteractions(interactor);
        verifyZeroInteractions(navigationProvider);
      },
    );

    blocTest<InstallmentsPaybackSetupCubit, InstallmentsPaybackSetupState>(
      'shows faq',
      build: () => cubit,
      setUp: () => when(
        () => navigationProvider.showBottomSheet<void>(any()),
      ).justCompleteAsync(),
      act: (cubit) => cubit.openFaq(),
      expect: () => <InstallmentsPaybackSetupState>[],
      verify: (_) {
        verify(() => navigationProvider.showBottomSheet<void>(any())).called(1);
      },
    );

    blocTest<InstallmentsPaybackSetupCubit, InstallmentsPaybackSetupState>(
      'shows interest info bottom sheet',
      build: () => cubit,
      setUp: () => when(
        () => dialogProvider.showBottomSheetOrDialog<void>(
          content: any(named: 'content'),
          config: any(named: 'config'),
        ),
      ).justCompleteAsync(),
      act: (cubit) => cubit.showInterestInfoBottomSheet(),
      expect: () => <InstallmentsPaybackSetupState>[],
      verify: (_) {
        verify(
          () => dialogProvider.showBottomSheetOrDialog<void>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).called(1);
      },
    );

    blocTest<InstallmentsPaybackSetupCubit, InstallmentsPaybackSetupState>(
      'shows one time processing fee info bottom sheet',
      build: () => cubit,
      setUp: () => when(
        () => dialogProvider.showBottomSheetOrDialog<void>(
          content: any(named: 'content'),
          config: any(named: 'config'),
        ),
      ).justCompleteAsync(),
      act: (cubit) => cubit.showProcessingFeeInfo(),
      expect: () => <InstallmentsPaybackSetupState>[],
      verify: (_) {
        verify(
          () => dialogProvider.showBottomSheetOrDialog<void>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).called(1);
      },
    );
  });
}
