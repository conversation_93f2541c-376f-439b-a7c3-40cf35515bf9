import 'package:bloc_test/bloc_test.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:tests/tests.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/src/handlers/loan_error_handler.dart';
import 'package:wio_feature_loan_ui/src/screens/statements/loan_statements/cubit/loan_statements_cubit.dart';
import 'package:wio_feature_loan_ui/src/screens/statements/loan_statements/cubit/loan_statements_state.dart';
import 'package:wio_feature_loan_ui/src/screens/statements/loan_statements/dialogs/year_selection_dialog.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  group('LoanStatementsCubit', () {
    late LoanStatementsCubit cubit;
    late LoanStatementsInteractor mockStatementsInteractor;
    late LoanErrorHandler mockErrorHandler;
    late Logger mockLogger;
    late NavigationProvider mockNavigationProvider;
    late ResponsiveDialogProvider mockResponsiveDialogProvider;
    late FeatureToggleProvider mockFeatureToggleProvider;
    late LoanAnalytics mockLoanAnalytics;

    setUp(() {
      mockStatementsInteractor = MockLoanStatementsInteractor();
      mockErrorHandler = MockLoanErrorHandler();
      mockLogger = MockLogger();
      mockNavigationProvider = MockNavigationProvider();
      mockResponsiveDialogProvider = MockResponsiveDialogProvider();
      mockFeatureToggleProvider = MockFeatureToggleProvider();
      mockLoanAnalytics = MockLoanAnalytics();

      cubit = LoanStatementsCubit(
        loanAccountId: 'id',
        accountType: LoanProductIdentifier.smeCreditCard,
        statementsInteractor: mockStatementsInteractor,
        errorHandler: mockErrorHandler,
        logger: mockLogger,
        navigationProvider: mockNavigationProvider,
        responsiveDialogProvider: mockResponsiveDialogProvider,
        featureToggleProvider: mockFeatureToggleProvider,
        analytics: mockLoanAnalytics,
      );

      when(
        () => mockFeatureToggleProvider
            .get(LoanFeatureToggles.isSmePosCustomStatementEnabled),
      ).thenReturn(true);

      registerFallbackValue(FakeResponsiveModalConfig());
    });

    const datedStatement = LoanTestHelpers.datedStatement;
    const datedStatement2 = LoanTestHelpers.datedStatement2;

    blocTest<LoanStatementsCubit, LoanStatementsState>(
      'Initialize should fetch all statements',
      build: () => cubit,
      setUp: () {
        when(() => mockStatementsInteractor.getStatements(any()))
            .justAnswerAsync([datedStatement]);
      },
      act: (cubit) => cubit.initialize(),
      verify: (cubit) {
        verify(() => mockStatementsInteractor.getStatements(any())).calledOnce;
        verify(
          () => mockLoanAnalytics
              .statementsOpened(LoanProductIdentifier.smeCreditCard),
        ).calledOnce;
      },
      expect: () => [
        const LoanStatementsState.loading(),
        LoanStatementsState.idle(
          selectedYear: datedStatement.year,
          statements: [datedStatement],
        ),
      ],
    );

    blocTest<LoanStatementsCubit, LoanStatementsState>(
      'Initialize should fetch all statements and '
      'emit empty state if no statements found',
      build: () => cubit,
      setUp: () {
        when(() => mockStatementsInteractor.getStatements(any()))
            .justAnswerAsync([]);
      },
      act: (cubit) => cubit.initialize(),
      verify: (cubit) =>
          verify(() => mockStatementsInteractor.getStatements(any()))
              .calledOnce,
      expect: () => [
        const LoanStatementsState.loading(),
        const LoanStatementsState.empty(),
      ],
    );

    blocTest<LoanStatementsCubit, LoanStatementsState>(
      'Retry should fetch all statements from failed state',
      build: () => cubit,
      seed: () => const LoanStatementsState.failed(),
      setUp: () {
        when(() => mockStatementsInteractor.getStatements(any()))
            .justAnswerAsync([datedStatement]);
      },
      act: (cubit) => cubit.onRetry(),
      verify: (cubit) =>
          verify(() => mockStatementsInteractor.getStatements(any()))
              .calledOnce,
      expect: () => [
        const LoanStatementsState.loading(),
        LoanStatementsState.idle(
          selectedYear: datedStatement.year,
          statements: [datedStatement],
        ),
      ],
    );

    blocTest<LoanStatementsCubit, LoanStatementsState>(
      'on Select year should navigate to select year and '
      'updated state with selected year',
      build: () => cubit,
      seed: () => LoanStatementsState.idle(
        selectedYear: datedStatement.year,
        statements: [datedStatement, datedStatement2],
      ),
      setUp: () {
        registerFallbackValue(
          YearSelectionDialog(
            years: [datedStatement.year, datedStatement2.year],
          ),
        );
        when(
          () => mockResponsiveDialogProvider.showBottomSheetOrDialog<int>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).justAnswerAsync(datedStatement2.year);
      },
      act: (cubit) => cubit.onSelectYear(),
      verify: (cubit) {
        verify(
          () => mockResponsiveDialogProvider.showBottomSheetOrDialog<int>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).calledOnce;
      },
      expect: () => [
        LoanStatementsState.idle(
          selectedYear: datedStatement2.year,
          statements: [datedStatement, datedStatement2],
        ),
      ],
    );

    blocTest<LoanStatementsCubit, LoanStatementsState>(
      'onSelectStatement navigate to statement file viewer',
      build: () => cubit,
      seed: () => LoanStatementsState.idle(
        selectedYear: datedStatement.year,
        statements: [datedStatement],
      ),
      setUp: () {
        when(
          () => mockNavigationProvider.push(
            LoanStatementFileViewerNavigationConfig(
              config: LoanStatementFileViewerConfig.byStatementId(
                statementId: datedStatement.id,
                statementDate: datedStatement.date,
                productIdentifier: LoanProductIdentifier.smeCreditCard,
              ),
            ),
          ),
        ).justCompleteAsync();
      },
      act: (cubit) => cubit.onSelectStatement(datedStatement),
      verify: (_) {
        verify(
          () => mockNavigationProvider.push(
            LoanStatementFileViewerNavigationConfig(
              config: LoanStatementFileViewerConfig.byStatementId(
                statementId: datedStatement.id,
                statementDate: datedStatement.date,
                productIdentifier: LoanProductIdentifier.smeCreditCard,
              ),
            ),
          ),
        ).calledOnce;
      },
    );

    blocTest(
      'onCustomStatement should show custom statements',
      build: () => cubit,
      seed: () => LoanStatementsState.idle(
        selectedYear: datedStatement.year,
        statements: [datedStatement],
        showCustomStatements: true,
      ),
      setUp: () {
        when(
          () => mockNavigationProvider.push(
            LoanCustomStatementPageNavigationConfig(
              LoanTestHelpers.loanAccount.id,
              productIdentifier: LoanTestHelpers.loanAccount.productIdentifier,
            ),
          ),
        ).justCompleteAsync();
      },
      act: (cubit) => cubit.onCustomStatementPressed(),
      verify: (_) {
        verify(
          () => mockNavigationProvider.push(
            LoanCustomStatementPageNavigationConfig(
              LoanTestHelpers.loanAccount.id,
              productIdentifier: LoanTestHelpers.loanAccount.productIdentifier,
            ),
          ),
        ).calledOnce;
      },
    );
  });
}
