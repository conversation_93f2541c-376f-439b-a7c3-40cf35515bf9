import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/widgets.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/src/screens/statements/custom_statements/cubit/loan_custom_statement_cubit.dart';
import 'package:wio_feature_loan_ui/src/screens/statements/custom_statements/cubit/loan_custom_statement_state.dart';
import 'package:wio_feature_loan_ui/src/screens/statements/custom_statements/date_selection/loan_statement_date_selection_dialog.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late LoanCustomStatementCubit cubit;
  late LoanInteractor loanInteractor;
  late NavigationProvider navigationProvider;
  late TimeProvider timeProvider;
  late Logger logger;
  late ResponsiveDialogProvider responsiveDialogProvider;

  const accountId = 'accountId';
  final accountCreateDate = DateTime(1, 1, 10);
  final currentDate = DateTime(1, 2, 20);
  final updatedDate = DateTime(1, 5, 25);
  final loadedState = LoanCustomStatementState.loaded(
    accountCreationDate: accountCreateDate,
    startDate: currentDate.subtract(const Duration(days: 30)),
    endDate: currentDate,
    selectedPeriod: LoanCustomStatementPeriod.oneMonth,
  );

  setUp(() {
    loanInteractor = MockLoanInteractor();
    navigationProvider = MockNavigationProvider();
    timeProvider = MockTimeProvider();
    logger = MockLogger();
    responsiveDialogProvider = MockResponsiveDialogProvider();

    cubit = LoanCustomStatementCubit(
      loanInteractor: loanInteractor,
      navigationProvider: navigationProvider,
      timeProvider: timeProvider,
      accountId: accountId,
      logger: logger,
      responsiveDialogProvider: responsiveDialogProvider,
    );

    registerFallbackValue(ScreenNavigationConfigFake());
    registerFallbackValue(const Placeholder());
    registerFallbackValue(FakeResponsiveModalConfig());
  });

  blocTest<LoanCustomStatementCubit, void>(
    'Init cubit success',
    build: () => cubit,
    setUp: () {
      when(() => loanInteractor.getAccount(accountId)).justAnswerAsync(
        LoanTestHelpers.loanAccount,
      );
      when(() => timeProvider.getDateTime()).thenReturn(currentDate);
    },
    act: (cubit) => cubit.init(),
    verify: (cubit) {
      verify(() => loanInteractor.getAccount(accountId)).calledOnce;
      verify(() => timeProvider.getDateTime()).calledOnce;
      final currentState = cubit.state;
      expect(currentState, isA<LoanCustomStatementIdleState>());
      final state = currentState as LoanCustomStatementIdleState;
      expect(
        state.accountCreationDate,
        LoanTestHelpers.loanAccount.createdAt,
      );
      expect(state.startDate, isA<DateTime>());
      expect(state.endDate, currentDate);
      expect(state.selectedPeriod, LoanCustomStatementPeriod.oneMonth);
    },
  );

  blocTest<LoanCustomStatementCubit, void>(
    'Init cubit failed',
    build: () => cubit,
    setUp: () {
      when(() => loanInteractor.getAccount(accountId)).thenThrow(
        Exception(),
      );
    },
    act: (cubit) => cubit.init(),
    expect: () => const [LoanCustomStatementState.failed()],
  );

  blocTest<LoanCustomStatementCubit, void>(
    'Select start date',
    build: () => cubit,
    seed: () => loadedState,
    setUp: () {
      when(
        () => responsiveDialogProvider.showBottomSheetOrDialog<DateTime?>(
          content: any(
            named: 'content',
            that: isA<LoanStatementDateSelectionDialog>(),
          ),
          config: any(named: 'config'),
        ),
      ).thenAnswer((_) async => updatedDate);
    },
    act: (cubit) => cubit.selectStartDate(),
    verify: (cubit) {
      verify(
        () => responsiveDialogProvider.showBottomSheetOrDialog<DateTime?>(
          content: any(
            named: 'content',
            that: isA<LoanStatementDateSelectionDialog>(),
          ),
          config: any(named: 'config'),
        ),
      ).calledOnce;
      verify(() => timeProvider.getDateTime()).calledOnce;
      final currentState = cubit.state;
      expect(currentState, isA<LoanCustomStatementIdleState>());
      final state = currentState as LoanCustomStatementIdleState;
      expect(state.startDate, updatedDate);
    },
  );

  blocTest<LoanCustomStatementCubit, void>(
    'Select end date',
    build: () => cubit,
    seed: () => loadedState,
    setUp: () {
      when(
        () => responsiveDialogProvider.showBottomSheetOrDialog<DateTime?>(
          content: any(
            named: 'content',
            that: isA<LoanStatementDateSelectionDialog>(),
          ),
          config: any(named: 'config'),
        ),
      ).thenAnswer((_) async => updatedDate);
    },
    act: (cubit) => cubit.selectEndDate(),
    verify: (cubit) {
      verify(
        () => responsiveDialogProvider.showBottomSheetOrDialog<DateTime?>(
          content: any(
            named: 'content',
            that: isA<LoanStatementDateSelectionDialog>(),
          ),
          config: any(named: 'config'),
        ),
      ).calledOnce;
      verify(() => timeProvider.getDateTime()).calledOnce;
      final currentState = cubit.state;
      expect(currentState, isA<LoanCustomStatementIdleState>());
      final state = currentState as LoanCustomStatementIdleState;
      expect(state.endDate, updatedDate);
    },
  );

  blocTest<LoanCustomStatementCubit, void>(
    'On generate statement pressed',
    build: () => cubit,
    seed: () => loadedState,
    setUp: () {
      when(
        () => navigationProvider.push(
          any(that: isA<LoanStatementFileViewerNavigationConfig>()),
        ),
      ).justCompleteAsync();
    },
    act: (cubit) => cubit.onGenerateStatement(),
    verify: (cubit) {
      verify(
        () => navigationProvider.push(
          any(that: isA<LoanStatementFileViewerNavigationConfig>()),
        ),
      ).calledOnce;
    },
  );

  blocTest<LoanCustomStatementCubit, void>(
    'On cancel pressed',
    build: () => cubit,
    seed: () => loadedState,
    setUp: () {
      when(() => navigationProvider.goBack()).justComplete();
    },
    act: (cubit) => cubit.cancel(),
    verify: (cubit) {
      verify(() => navigationProvider.goBack()).calledOnce;
    },
  );

  blocTest<LoanCustomStatementCubit, void>(
    'On select new period',
    build: () => cubit,
    seed: () => loadedState,
    act: (cubit) => cubit.selectPeriod(LoanCustomStatementPeriod.oneYear),
    verify: (cubit) {
      final currentState = cubit.state;
      expect(currentState, isA<LoanCustomStatementIdleState>());
      final state = currentState as LoanCustomStatementIdleState;
      expect(state.selectedPeriod, LoanCustomStatementPeriod.oneYear);
    },
  );
}
