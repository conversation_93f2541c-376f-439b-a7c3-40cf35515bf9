import 'package:bloc_test/bloc_test.dart';
import 'package:domain/data/data.dart';
import 'package:flutter/material.dart';
import 'package:mocktail/mocktail.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_api/navigation/screens/loan_payback_navigation_config.dart';
import 'package:wio_feature_loan_ui/src/navigation/configs/manage_loan_page_navigation_config.dart';
import 'package:wio_feature_loan_ui/src/navigation/screens/repayment_plan_page_navigation_config.dart';
import 'package:wio_feature_loan_ui/src/screens/dashboard/cubit/loan_dashboard_cubit.dart';
import 'package:wio_feature_loan_ui/src/screens/dashboard/cubit/loan_dashboard_state.dart';
import 'package:wio_feature_loan_ui/src/screens/repayment/config/repayment_plan_page_config.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late LoanInteractor loanInteractor;
  late NavigationProvider navigationProvider;
  late ResponsiveDialogProvider dialogProvider;
  late LoanDashboardCubit cubit;

  setUp(() {
    loanInteractor = MockLoanInteractor();
    navigationProvider = MockNavigationProvider();
    dialogProvider = MockResponsiveDialogProvider();

    when(() => loanInteractor.observeLoanDetails()).justAnswerAsync(Data());

    cubit = LoanDashboardCubit(
      loanAccountId: 'loanAccountId',
      loanInteractor: loanInteractor,
      navigationProvider: navigationProvider,
      responsiveDialogProvider: dialogProvider,
    );

    registerFallbackValue(LoanTestHelpers.loanAccount);
    registerFallbackValue(const Placeholder());
    registerFallbackValue(FakeResponsiveModalConfig());
  });

  group('LoanDashboardCubit >', () {
    final loanAccount = LoanTestHelpers.loanAccount;

    blocTest<LoanDashboardCubit, LoanDashboardState>(
      'init should fetch the loanAccount',
      build: () => cubit,
      setUp: () {
        when(
          () => loanInteractor.getAccount(any()),
        ).justAnswerAsync(loanAccount);
        when(
          () => loanInteractor.getAutopayInfo(any()),
        ).justAnswerAsync(
          Autopay(
            accountId: 'accountId',
            amount: LoanTestHelpers.defaultMoney,
            fee: LoanTestHelpers.defaultMoney,
            totalOutstanding: LoanTestHelpers.defaultMoney,
            minimumPaymentAmount: LoanTestHelpers.defaultMoney,
            isAutopayFromSavingSpaceDisabled: false,
            nextPaymentDate: DateTime(2024),
            fullRepaymentDoneInCurrentCycle: false,
            feeFreeDate: null,
            delinquentAccountDetails: null,
            schedule: Schedule(
              installmentSummary: [
                LoanTestHelpers.paidInstallment,
                LoanTestHelpers.upcomingInstallment,
                LoanTestHelpers.missedInstallment,
              ],
            ),
            linkedAccountsOutstandingAmount:
                Money.fromNumWithCurrency(100, Currency.aed),
          ),
        );
        when(
          () => loanInteractor.getProductInfo(),
        ).justAnswerAsync(
          ProductInfo(
            autopayHour: LoanTestHelpers.autopayHour,
            latePaymentFee: LoanTestHelpers.defaultMoney,
          ),
        );
        when(
          () => loanInteractor.getRepaymentRestrictionDecision(
            account: any(named: 'account'),
          ),
        ).justAnswerAsync(const RepaymentAllowedDecision());
      },
      act: (cubit) => cubit.init(),
      expect: () => [
        LoanDashboardState.idle(
          loanAccount: loanAccount,
          autopayHour: LoanTestHelpers.autopayHour,
          latePaymentFee: LoanTestHelpers.defaultMoney,
          installments: [
            LoanTestHelpers.paidInstallment,
            LoanTestHelpers.upcomingInstallment,
            LoanTestHelpers.missedInstallment,
          ],
          totalOutstanding: LoanTestHelpers.defaultMoney,
          repaymentRestrictionDecision: const RepaymentAllowedDecision(),
        ),
      ],
    );

    blocTest<LoanDashboardCubit, LoanDashboardState>(
      'error on init',
      build: () => cubit,
      setUp: () {
        when(
          () => loanInteractor.getAccount(any()),
        ).justThrowAsync(Exception);
      },
      act: (cubit) => cubit.init(),
      expect: () =>
          [const LoanDashboardState.error(loanAccountId: 'loanAccountId')],
    );

    blocTest<LoanDashboardCubit, LoanDashboardState>(
      'on manage',
      build: () => cubit,
      setUp: () {
        when(
          () => navigationProvider
              .push(ManageLoanPageNavigationConfig(loanAccount: loanAccount)),
        ).justCompleteAsync();
      },
      seed: () => LoanDashboardState.idle(
        loanAccount: loanAccount,
        autopayHour: LoanTestHelpers.autopayHour,
        latePaymentFee: LoanTestHelpers.defaultMoney,
        installments: [],
        missedPaymentCount: 0,
        totalOutstanding: LoanTestHelpers.defaultMoney,
        repaymentRestrictionDecision: const RepaymentAllowedDecision(),
      ),
      act: (cubit) => cubit.manage(),
      verify: (cubit) {
        verify(
          () => navigationProvider
              .push(ManageLoanPageNavigationConfig(loanAccount: loanAccount)),
        ).calledOnce;
      },
    );
    blocTest<LoanDashboardCubit, LoanDashboardState>(
      'on payback with returning null',
      build: () => cubit,
      setUp: () {
        when(
          () => navigationProvider.push<Money?>(
            LoanPaybackNavigationConfig(
              loanAccount: loanAccount,
              minPaybackLimit: Money.fromNumWithCurrency(0, Currency.aed),
              totalOutstanding: LoanTestHelpers.defaultMoney,
            ),
          ),
        ).thenAnswer(
          (_) async => null,
        );
      },
      seed: () => LoanDashboardState.idle(
        loanAccount: loanAccount,
        autopayHour: LoanTestHelpers.autopayHour,
        latePaymentFee: LoanTestHelpers.defaultMoney,
        installments: [],
        missedPaymentCount: 0,
        minPaybackLimit: Money.fromNumWithCurrency(0, Currency.aed),
        totalOutstanding: LoanTestHelpers.defaultMoney,
        repaymentRestrictionDecision: const RepaymentAllowedDecision(),
      ),
      act: (cubit) => cubit.payback(),
      verify: (cubit) {
        verify(
          () => navigationProvider.push<Money?>(
            LoanPaybackNavigationConfig(
              loanAccount: loanAccount,
              minPaybackLimit: Money.fromNumWithCurrency(0, Currency.aed),
              totalOutstanding: LoanTestHelpers.defaultMoney,
            ),
          ),
        ).calledOnce;
        verifyNever(() => navigationProvider.goBack());
      },
    );

    blocTest<LoanDashboardCubit, LoanDashboardState>(
      'on payback with returning money should call goBack',
      build: () => cubit,
      setUp: () {
        when(
          () => navigationProvider.push<Money?>(
            LoanPaybackNavigationConfig(
              loanAccount: loanAccount,
              minPaybackLimit: Money.fromNumWithCurrency(0, Currency.aed),
              totalOutstanding: LoanTestHelpers.defaultMoney,
            ),
          ),
        ).thenAnswer(
          (_) async => LoanTestHelpers.defaultMoney,
        );
      },
      seed: () => LoanDashboardState.idle(
        loanAccount: loanAccount,
        autopayHour: LoanTestHelpers.autopayHour,
        latePaymentFee: LoanTestHelpers.defaultMoney,
        installments: [],
        missedPaymentCount: 0,
        minPaybackLimit: Money.fromNumWithCurrency(0, Currency.aed),
        totalOutstanding: LoanTestHelpers.defaultMoney,
        repaymentRestrictionDecision: const RepaymentAllowedDecision(),
      ),
      act: (cubit) => cubit.payback(),
      verify: (cubit) {
        verify(
          () => navigationProvider.push<Money?>(
            LoanPaybackNavigationConfig(
              loanAccount: loanAccount,
              minPaybackLimit: Money.fromNumWithCurrency(0, Currency.aed),
              totalOutstanding: LoanTestHelpers.defaultMoney,
            ),
          ),
        ).calledOnce;
        verify(
          () => navigationProvider.goBack(),
        ).calledOnce;
      },
    );

    blocTest<LoanDashboardCubit, LoanDashboardState>(
      'on payback with restricted repayment decision',
      build: () => cubit,
      setUp: () {
        when(
          () => dialogProvider.showBottomSheetOrDialog<void>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).justCompleteAsync();
      },
      seed: () => LoanDashboardState.idle(
        loanAccount: loanAccount,
        autopayHour: LoanTestHelpers.autopayHour,
        latePaymentFee: LoanTestHelpers.defaultMoney,
        installments: [],
        missedPaymentCount: 0,
        minPaybackLimit: Money.fromNumWithCurrency(0, Currency.aed),
        totalOutstanding: LoanTestHelpers.defaultMoney,
        repaymentRestrictionDecision: const RepaymentRestrictedDecision(
          restrictionPeriod: Period(days: 7),
        ),
      ),
      act: (cubit) => cubit.payback(),
      verify: (cubit) {
        verify(
          () => dialogProvider.showBottomSheetOrDialog<void>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).calledOnce;
        verifyNever(
          () => navigationProvider.push<Money?>(
            LoanPaybackNavigationConfig(
              loanAccount: loanAccount,
              minPaybackLimit: Money.fromNumWithCurrency(0, Currency.aed),
              totalOutstanding: LoanTestHelpers.defaultMoney,
            ),
          ),
        );
        verifyNever(() => navigationProvider.goBack());
      },
    );

    blocTest<LoanDashboardCubit, LoanDashboardState>(
      'on repayment plan page ',
      build: () => cubit,
      setUp: () {
        when(
          () => navigationProvider.push(
            RepaymentPlanPageNavigationConfig(
              RepaymentPlanPageConfig(
                autopayHour: LoanTestHelpers.autopayHour,
                latePaymentFee: LoanTestHelpers.defaultMoney,
                installments: const [],
                productIdentifier: loanAccount.productIdentifier,
              ),
            ),
          ),
        ).justCompleteAsync();
      },
      seed: () => LoanDashboardState.idle(
        loanAccount: loanAccount,
        autopayHour: LoanTestHelpers.autopayHour,
        latePaymentFee: LoanTestHelpers.defaultMoney,
        installments: [],
        missedPaymentCount: 0,
        minPaybackLimit: Money.fromNumWithCurrency(0, Currency.aed),
        totalOutstanding: LoanTestHelpers.defaultMoney,
        repaymentRestrictionDecision: const RepaymentAllowedDecision(),
      ),
      act: (cubit) => cubit.navigateToRepaymentPlan(),
      verify: (cubit) {
        verify(
          () => navigationProvider.push(
            RepaymentPlanPageNavigationConfig(
              RepaymentPlanPageConfig(
                autopayHour: LoanTestHelpers.autopayHour,
                latePaymentFee: LoanTestHelpers.defaultMoney,
                installments: const [],
                productIdentifier: loanAccount.productIdentifier,
              ),
            ),
          ),
        ).calledOnce;
      },
    );
  });
}
