import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/widgets.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_transaction_api/index.dart'
    hide TransactionDetails;
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/feature_loan_ui.dart';
import 'package:wio_feature_loan_ui/src/screens/installments/details/cubit/installments_details_cubit.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late MockNavigationProvider mockNavigationProvider;
  late MockResponsiveDialogProvider mockDialogProvider;
  late MockLoanInteractor mockLoanInteractor;
  late MockLoanErrorHandler mockLoanErrorHandler;
  late MockTransactionsInteractor mockTransactionInteractor;
  late MockSplitTransactionsInteractor mockSplitTransactionsInteractor;
  late MockFeatureToggleProvider mockFeatureToggleProvider;
  late MockAccountInteractorToLoanDomainAdapter
      accountInteractorToLoanDomainAdapter;
  late LoanLocalizations loanLocalizations;
  const creditCardAccountId = 'creditCardAccountId';
  final autopay = LoanTestHelpers.autopay.copyWith(
    schedule: Schedule(
      applicableFees: ApplicableFees(
        loanIssuanceFee: LoanFee(
          txnId: 'txnId',
          amount: Money.fromIntWithCurrency(1000, Currency.aed),
        ),
      ),
      installmentSummary: [
        LoanTestHelpers.upcomingInstallment,
      ],
    ),
  );
  final autopayMissedPayment = LoanTestHelpers.autopay.copyWith(
    schedule: Schedule(
      missedPaymentCount: 2,
      overdue: Money.fromIntWithCurrency(1000, Currency.aed),
    ),
  );
  final productInfo = ProductInfo(
    latePaymentFee: Money.fromIntWithCurrency(500, Currency.aed),
    autopayHour: '12:00 PM',
  );
  const subtype = ProductSubType.cardPurchase;

  setUpAll(() {
    registerFallbackValue(const SizedBox());
    registerFallbackValue(StackTrace.fromString(''));
    registerFallbackValue(Exception());
    registerFallbackValue(FakeFeatureNavigationConfig());
    registerFallbackValue(FakeScreenNavigationConfig());
    registerFallbackValue(FakeBottomSheetNavigationConfig());
    registerFallbackValue(FakeLoanRepaymentEvaluationRequest());
    registerFallbackValue(
      NotificationToastMessageConfiguration.error(''),
    );
    registerFallbackValue(FakeResponsiveModalConfig());
  });

  setUp(() {
    mockNavigationProvider = MockNavigationProvider();
    mockDialogProvider = MockResponsiveDialogProvider();
    mockLoanInteractor = MockLoanInteractor();
    mockLoanErrorHandler = MockLoanErrorHandler();
    mockTransactionInteractor = MockTransactionsInteractor();
    accountInteractorToLoanDomainAdapter =
        MockAccountInteractorToLoanDomainAdapter();
    loanLocalizations = MockLoanLocalizations();
    mockFeatureToggleProvider = MockFeatureToggleProvider();
    mockSplitTransactionsInteractor = MockSplitTransactionsInteractor();

    when(
      () => mockFeatureToggleProvider
          .get(LoanFeatureToggles.isInstallmentsMissedPaymentEnabled),
    ).thenReturn(false);
  });

  group('InstallmentsDetailsCubit Tests', () {
    blocTest<InstallmentsDetailsCubit, InstallmentsDetailsState>(
      '''emits TransactionLoanLoaded on successful initialization with TransactionLoanDetailsAccountIdConfig''',
      build: () {
        final config = InstallmentsDetailsConfig(
          transactionCoreBankingIdentifier: 'coreBankingIdentifier',
          accountId: 'account123',
          category: TransactionCategory.general,
          imageUrl: 'imageUrl',
          merchantName: 'merchantName',
          transactionType: TransactionType.card,
          subtype: subtype,
          creditCardAccountId: creditCardAccountId,
          isAccountActive: true,
        );
        return InstallmentsDetailsCubit(
          config: config,
          navigationProvider: mockNavigationProvider,
          exceptionHandler: mockLoanErrorHandler,
          loanInteractor: mockLoanInteractor,
          dialogProvider: mockDialogProvider,
          transactionInteractor: mockTransactionInteractor,
          accountInteractorAdapter: accountInteractorToLoanDomainAdapter,
          loanLocalizations: loanLocalizations,
          featureToggleProvider: mockFeatureToggleProvider,
          splitTransactionsInteractor: mockSplitTransactionsInteractor,
        );
      },
      setUp: () {
        when(
          () => mockTransactionInteractor.getTransactionByCoreBankingId(
            coreBankingIdentifier: any(named: 'coreBankingIdentifier'),
            filterByItems:
                const TransactionFilterByItems(accountId: creditCardAccountId),
          ),
        ).thenAnswer(
          (_) async => LoanTestHelpers.transaction,
        );
        when(
          () => mockLoanInteractor.getAccount(any()),
        ).thenAnswer((_) async => LoanTestHelpers.loanAccount);
        when(() => mockLoanInteractor.getAutopayInfo(any()))
            .thenAnswer((_) async => autopay);
        when(
          () => mockLoanInteractor.getProductInfo(
            subtype: any(named: 'subtype'),
          ),
        ).thenAnswer((_) async => productInfo);
      },
      act: (cubit) => cubit.initialize(),
      expect: () => [
        InstallmentsDetailsLoaded(
          autopay: autopay,
          loanAccount: LoanTestHelpers.loanAccount,
          productInfo: productInfo,
          transaction: LoanTestHelpers.transaction,
          isInstallmentsMissedPaymentEnabled: false,
        ),
      ],
      verify: (_) {
        verify(
          () => mockLoanInteractor.getAccount('account123'),
        ).calledOnce;
        verify(
          () => mockLoanInteractor.getAutopayInfo('account123'),
        ).calledOnce;
        verify(
          () => mockLoanInteractor.getProductInfo(
            subtype: any(named: 'subtype'),
          ),
        ).calledOnce;
        verify(
          () => mockTransactionInteractor.getTransactionByCoreBankingId(
            coreBankingIdentifier: any(named: 'coreBankingIdentifier'),
            filterByItems:
                const TransactionFilterByItems(accountId: creditCardAccountId),
          ),
        );
      },
    );

    blocTest<InstallmentsDetailsCubit, InstallmentsDetailsState>(
      '''makes loan account request with correct configuration when account is not active''',
      build: () {
        final config = InstallmentsDetailsConfig(
          transactionCoreBankingIdentifier: 'coreBankingIdentifier',
          accountId: 'account123',
          category: TransactionCategory.general,
          imageUrl: 'imageUrl',
          merchantName: 'merchantName',
          transactionType: TransactionType.card,
          subtype: subtype,
          creditCardAccountId: creditCardAccountId,
          isAccountActive: false,
        );
        return InstallmentsDetailsCubit(
          config: config,
          navigationProvider: mockNavigationProvider,
          exceptionHandler: mockLoanErrorHandler,
          loanInteractor: mockLoanInteractor,
          dialogProvider: mockDialogProvider,
          transactionInteractor: mockTransactionInteractor,
          accountInteractorAdapter: accountInteractorToLoanDomainAdapter,
          loanLocalizations: loanLocalizations,
          featureToggleProvider: mockFeatureToggleProvider,
          splitTransactionsInteractor: mockSplitTransactionsInteractor,
        );
      },
      setUp: () {
        when(
          () => mockTransactionInteractor.getTransactionByCoreBankingId(
            coreBankingIdentifier: any(named: 'coreBankingIdentifier'),
            filterByItems:
                const TransactionFilterByItems(accountId: creditCardAccountId),
          ),
        ).thenAnswer(
          (_) async => LoanTestHelpers.transaction,
        );
        when(
          () => mockLoanInteractor.getAccount(
            any(),
            isActive: any(named: 'isActive'),
          ),
        ).thenAnswer((_) async => LoanTestHelpers.loanAccount);
        when(() => mockLoanInteractor.getAutopayInfo(any()))
            .thenAnswer((_) async => autopay);
        when(
          () => mockLoanInteractor.getProductInfo(
            subtype: any(named: 'subtype'),
          ),
        ).thenAnswer((_) async => productInfo);
      },
      act: (cubit) => cubit.initialize(),
      expect: () => [
        InstallmentsDetailsLoaded(
          autopay: autopay,
          loanAccount: LoanTestHelpers.loanAccount,
          productInfo: productInfo,
          transaction: LoanTestHelpers.transaction,
          isInstallmentsMissedPaymentEnabled: false,
        ),
      ],
      verify: (_) {
        verify(
          () => mockLoanInteractor.getAccount(
            'account123',
            isActive: false,
          ),
        ).calledOnce;
        verify(
          () => mockLoanInteractor.getAutopayInfo('account123'),
        ).calledOnce;
        verify(
          () => mockLoanInteractor.getProductInfo(
            subtype: any(named: 'subtype'),
          ),
        ).calledOnce;
        verify(
          () => mockTransactionInteractor.getTransactionByCoreBankingId(
            coreBankingIdentifier: any(named: 'coreBankingIdentifier'),
            filterByItems:
                const TransactionFilterByItems(accountId: creditCardAccountId),
          ),
        );
      },
    );

    blocTest<InstallmentsDetailsCubit, InstallmentsDetailsState>(
      'handles errors during initialization',
      build: () {
        final config = InstallmentsDetailsConfig(
          transactionCoreBankingIdentifier: 'coreBankingIdentifier',
          accountId: 'account123',
          category: TransactionCategory.general,
          imageUrl: 'imageUrl',
          merchantName: 'merchantName',
          transactionType: TransactionType.card,
          subtype: subtype,
          creditCardAccountId: creditCardAccountId,
          isAccountActive: true,
        );
        return InstallmentsDetailsCubit(
          config: config,
          navigationProvider: mockNavigationProvider,
          exceptionHandler: mockLoanErrorHandler,
          loanInteractor: mockLoanInteractor,
          dialogProvider: mockDialogProvider,
          transactionInteractor: mockTransactionInteractor,
          accountInteractorAdapter: accountInteractorToLoanDomainAdapter,
          loanLocalizations: loanLocalizations,
          featureToggleProvider: mockFeatureToggleProvider,
          splitTransactionsInteractor: mockSplitTransactionsInteractor,
        );
      },
      setUp: () {
        when(
          () => mockTransactionInteractor.getTransactionByCoreBankingId(
            coreBankingIdentifier: any(named: 'coreBankingIdentifier'),
            filterByItems:
                const TransactionFilterByItems(accountId: creditCardAccountId),
          ),
        ).thenThrow(Exception('Error'));

        when(
          () => mockLoanErrorHandler.handleError(
            any(),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).justComplete();
      },
      act: (cubit) => cubit.initialize(),
      expect: () => [
        const InstallmentsDetailsError(),
      ],
      verify: (_) {
        verify(
          () => mockLoanErrorHandler.handleError(
            any(),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).calledOnce;
      },
    );

    blocTest<InstallmentsDetailsCubit, InstallmentsDetailsState>(
      '''retry emits loading first''',
      build: () {
        final config = InstallmentsDetailsConfig(
          transactionCoreBankingIdentifier: 'coreBankingIdentifier',
          accountId: 'account123',
          category: TransactionCategory.general,
          imageUrl: 'imageUrl',
          merchantName: 'merchantName',
          transactionType: TransactionType.card,
          subtype: subtype,
          creditCardAccountId: creditCardAccountId,
          isAccountActive: true,
        );
        return InstallmentsDetailsCubit(
          config: config,
          navigationProvider: mockNavigationProvider,
          exceptionHandler: mockLoanErrorHandler,
          loanInteractor: mockLoanInteractor,
          dialogProvider: mockDialogProvider,
          transactionInteractor: mockTransactionInteractor,
          accountInteractorAdapter: accountInteractorToLoanDomainAdapter,
          loanLocalizations: loanLocalizations,
          featureToggleProvider: mockFeatureToggleProvider,
          splitTransactionsInteractor: mockSplitTransactionsInteractor,
        );
      },
      seed: () => const InstallmentsDetailsError(),
      setUp: () {
        when(
          () => mockTransactionInteractor.getTransactionByCoreBankingId(
            coreBankingIdentifier: any(named: 'coreBankingIdentifier'),
            filterByItems:
                const TransactionFilterByItems(accountId: creditCardAccountId),
          ),
        ).thenAnswer(
          (_) async => LoanTestHelpers.transaction,
        );
        when(() => mockLoanInteractor.getAccount(any()))
            .thenAnswer((_) async => LoanTestHelpers.loanAccount);
        when(() => mockLoanInteractor.getAutopayInfo(any()))
            .thenAnswer((_) async => autopay);
        when(
          () => mockLoanInteractor.getProductInfo(
            subtype: any(named: 'subtype'),
          ),
        ).thenAnswer((_) async => productInfo);
      },
      act: (cubit) => cubit.retry(),
      expect: () => [
        const InstallmentsDetailsLoading(),
        InstallmentsDetailsLoaded(
          autopay: autopay,
          loanAccount: LoanTestHelpers.loanAccount,
          productInfo: productInfo,
          transaction: LoanTestHelpers.transaction,
          isInstallmentsMissedPaymentEnabled: false,
        ),
      ],
      verify: (_) {
        verify(
          () => mockLoanInteractor.getAccount('account123'),
        ).calledOnce;
        verify(
          () => mockLoanInteractor.getAutopayInfo('account123'),
        ).calledOnce;
        verify(
          () => mockLoanInteractor.getProductInfo(
            subtype: any(named: 'subtype'),
          ),
        ).calledOnce;
        verify(
          () => mockTransactionInteractor.getTransactionByCoreBankingId(
            coreBankingIdentifier: any(named: 'coreBankingIdentifier'),
            filterByItems:
                const TransactionFilterByItems(accountId: creditCardAccountId),
          ),
        );
      },
    );

    blocTest<InstallmentsDetailsCubit, InstallmentsDetailsState>(
      'shows installment details',
      build: () {
        final config = InstallmentsDetailsConfig(
          transactionCoreBankingIdentifier: 'coreBankingIdentifier',
          accountId: 'account123',
          category: TransactionCategory.general,
          imageUrl: 'imageUrl',
          merchantName: 'merchantName',
          transactionType: TransactionType.card,
          subtype: subtype,
          creditCardAccountId: creditCardAccountId,
          isAccountActive: true,
        );
        return InstallmentsDetailsCubit(
          config: config,
          navigationProvider: mockNavigationProvider,
          exceptionHandler: mockLoanErrorHandler,
          loanInteractor: mockLoanInteractor,
          dialogProvider: mockDialogProvider,
          transactionInteractor: mockTransactionInteractor,
          accountInteractorAdapter: accountInteractorToLoanDomainAdapter,
          loanLocalizations: loanLocalizations,
          featureToggleProvider: mockFeatureToggleProvider,
          splitTransactionsInteractor: mockSplitTransactionsInteractor,
        );
      },
      seed: () => InstallmentsDetailsLoaded(
        autopay: autopay,
        loanAccount: LoanTestHelpers.loanAccount,
        productInfo: productInfo,
        transaction: LoanTestHelpers.transaction,
        isInstallmentsMissedPaymentEnabled: false,
      ),
      setUp: () => when(
        () => mockDialogProvider.showBottomSheetOrDialog<void>(
          content: any(named: 'content'),
          config: any(named: 'config'),
        ),
      ).justCompleteAsync(),
      act: (cubit) => cubit.showInstallmentDetails(
        installment: LoanTestHelpers.upcomingInstallment,
      ),
      expect: () => <InstallmentsDetailsState>[],
      verify: (_) {
        verify(
          () => mockDialogProvider.showBottomSheetOrDialog<void>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).called(1);
      },
    );

    blocTest<InstallmentsDetailsCubit, InstallmentsDetailsState>(
      'shows faq',
      build: () {
        final config = InstallmentsDetailsConfig(
          transactionCoreBankingIdentifier: 'coreBankingIdentifier',
          accountId: 'account123',
          category: TransactionCategory.general,
          imageUrl: 'imageUrl',
          merchantName: 'merchantName',
          transactionType: TransactionType.card,
          subtype: subtype,
          creditCardAccountId: creditCardAccountId,
          isAccountActive: true,
        );
        return InstallmentsDetailsCubit(
          config: config,
          navigationProvider: mockNavigationProvider,
          exceptionHandler: mockLoanErrorHandler,
          loanInteractor: mockLoanInteractor,
          dialogProvider: mockDialogProvider,
          transactionInteractor: mockTransactionInteractor,
          accountInteractorAdapter: accountInteractorToLoanDomainAdapter,
          loanLocalizations: loanLocalizations,
          featureToggleProvider: mockFeatureToggleProvider,
          splitTransactionsInteractor: mockSplitTransactionsInteractor,
        );
      },
      setUp: () => when(
        () => mockNavigationProvider.showBottomSheet<void>(any()),
      ).justCompleteAsync(),
      act: (cubit) => cubit.openFaq(),
      expect: () => <InstallmentsDetailsState>[],
      verify: (_) {
        verify(() => mockNavigationProvider.showBottomSheet<void>(any()))
            .called(1);
      },
    );

    blocTest<InstallmentsDetailsCubit, InstallmentsDetailsState>(
      'shows interest info bottom sheet',
      build: () {
        final config = InstallmentsDetailsConfig(
          transactionCoreBankingIdentifier: 'coreBankingIdentifier',
          accountId: 'account123',
          category: TransactionCategory.general,
          imageUrl: 'imageUrl',
          merchantName: 'merchantName',
          transactionType: TransactionType.card,
          subtype: subtype,
          creditCardAccountId: creditCardAccountId,
          isAccountActive: true,
        );
        return InstallmentsDetailsCubit(
          config: config,
          navigationProvider: mockNavigationProvider,
          exceptionHandler: mockLoanErrorHandler,
          loanInteractor: mockLoanInteractor,
          dialogProvider: mockDialogProvider,
          transactionInteractor: mockTransactionInteractor,
          accountInteractorAdapter: accountInteractorToLoanDomainAdapter,
          loanLocalizations: loanLocalizations,
          featureToggleProvider: mockFeatureToggleProvider,
          splitTransactionsInteractor: mockSplitTransactionsInteractor,
        );
      },
      seed: () => InstallmentsDetailsLoaded(
        autopay: autopay,
        loanAccount: LoanTestHelpers.loanAccount,
        productInfo: productInfo,
        transaction: LoanTestHelpers.transaction,
        isInstallmentsMissedPaymentEnabled: false,
      ),
      setUp: () => when(
        () => mockDialogProvider.showBottomSheetOrDialog<void>(
          content: any(named: 'content'),
          config: any(named: 'config'),
        ),
      ).justCompleteAsync(),
      act: (cubit) => cubit.showInterestInfoBottomSheet(),
      expect: () => <InstallmentsDetailsState>[],
      verify: (_) {
        verify(
          () => mockDialogProvider.showBottomSheetOrDialog<void>(
            content: any(named: 'content'),
            config: any(named: 'config'),
          ),
        ).called(1);
      },
    );

    blocTest<InstallmentsDetailsCubit, InstallmentsDetailsState>(
      'cta does nothing if state not loaded',
      build: () {
        final config = InstallmentsDetailsConfig(
          transactionCoreBankingIdentifier: 'coreBankingIdentifier',
          accountId: 'account123',
          category: TransactionCategory.general,
          imageUrl: 'imageUrl',
          merchantName: 'merchantName',
          transactionType: TransactionType.card,
          subtype: subtype,
          creditCardAccountId: creditCardAccountId,
          isAccountActive: true,
        );
        return InstallmentsDetailsCubit(
          config: config,
          navigationProvider: mockNavigationProvider,
          exceptionHandler: mockLoanErrorHandler,
          loanInteractor: mockLoanInteractor,
          dialogProvider: mockDialogProvider,
          transactionInteractor: mockTransactionInteractor,
          accountInteractorAdapter: accountInteractorToLoanDomainAdapter,
          loanLocalizations: loanLocalizations,
          featureToggleProvider: mockFeatureToggleProvider,
          splitTransactionsInteractor: mockSplitTransactionsInteractor,
        );
      },
      seed: () => const InstallmentsDetailsLoading(),
      act: (cubit) => cubit.onCta(),
      expect: () => <InstallmentsDetailsState>[],
      verify: (_) {
        verifyZeroInteractions(accountInteractorToLoanDomainAdapter);
        verifyZeroInteractions(mockLoanInteractor);
      },
    );

    blocTest<InstallmentsDetailsCubit, InstallmentsDetailsState>(
      'cta does nothing if evalution is true',
      build: () {
        final config = InstallmentsDetailsConfig(
          transactionCoreBankingIdentifier: 'coreBankingIdentifier',
          accountId: 'account123',
          category: TransactionCategory.general,
          imageUrl: 'imageUrl',
          merchantName: 'merchantName',
          transactionType: TransactionType.card,
          subtype: subtype,
          creditCardAccountId: creditCardAccountId,
          isAccountActive: true,
        );
        return InstallmentsDetailsCubit(
          config: config,
          navigationProvider: mockNavigationProvider,
          exceptionHandler: mockLoanErrorHandler,
          loanInteractor: mockLoanInteractor,
          dialogProvider: mockDialogProvider,
          transactionInteractor: mockTransactionInteractor,
          accountInteractorAdapter: accountInteractorToLoanDomainAdapter,
          loanLocalizations: loanLocalizations,
          featureToggleProvider: mockFeatureToggleProvider,
          splitTransactionsInteractor: mockSplitTransactionsInteractor,
        );
      },
      seed: () => InstallmentsDetailsLoaded(
        autopay: autopay,
        loanAccount: LoanTestHelpers.loanAccount,
        productInfo: productInfo,
        transaction: LoanTestHelpers.transaction,
        isEvaluatingPayback: true,
        isInstallmentsMissedPaymentEnabled: false,
      ),
      act: (cubit) => cubit.onCta(),
      expect: () => <InstallmentsDetailsState>[],
      verify: (_) {
        verifyZeroInteractions(accountInteractorToLoanDomainAdapter);
        verifyZeroInteractions(mockLoanInteractor);
      },
    );

    blocTest<InstallmentsDetailsCubit, InstallmentsDetailsState>(
      'cta makes necessary calls and navigate to confirmation page',
      build: () {
        final config = InstallmentsDetailsConfig(
          transactionCoreBankingIdentifier: 'coreBankingIdentifier',
          accountId: 'account123',
          category: TransactionCategory.general,
          imageUrl: 'imageUrl',
          merchantName: 'merchantName',
          transactionType: TransactionType.card,
          subtype: subtype,
          creditCardAccountId: creditCardAccountId,
          isAccountActive: true,
        );
        return InstallmentsDetailsCubit(
          config: config,
          navigationProvider: mockNavigationProvider,
          exceptionHandler: mockLoanErrorHandler,
          loanInteractor: mockLoanInteractor,
          dialogProvider: mockDialogProvider,
          transactionInteractor: mockTransactionInteractor,
          accountInteractorAdapter: accountInteractorToLoanDomainAdapter,
          loanLocalizations: loanLocalizations,
          featureToggleProvider: mockFeatureToggleProvider,
          splitTransactionsInteractor: mockSplitTransactionsInteractor,
        );
      },
      seed: () => InstallmentsDetailsLoaded(
        autopay: autopay,
        loanAccount: LoanTestHelpers.loanAccount,
        productInfo: productInfo,
        transaction: LoanTestHelpers.transaction,
        isInstallmentsMissedPaymentEnabled: false,
      ),
      setUp: () {
        when(
          () => accountInteractorToLoanDomainAdapter.fetchDebitAccount(),
        ).justAnswerAsync(
          DebitAccount(
            id: 'id',
            name: 'name',
            balance: Money.fromNumWithCurrency(1000000, Currency.aed),
          ),
        );
        when(
          () => mockLoanInteractor.evaluateRepayment(
            request: any(named: 'request'),
          ),
        ).justAnswerAsync(LoanTestHelpers.schedule);

        when(() => mockNavigationProvider.push<Money?>(any()))
            .justAnswerAsync(Money.fromIntWithCurrency(0, Currency.aed));
      },
      act: (cubit) => cubit.onCta(),
      expect: () => <InstallmentsDetailsState>[
        InstallmentsDetailsLoaded(
          autopay: autopay,
          loanAccount: LoanTestHelpers.loanAccount,
          productInfo: productInfo,
          transaction: LoanTestHelpers.transaction,
          isEvaluatingPayback: true,
          isInstallmentsMissedPaymentEnabled: false,
        ),
      ],
      verify: (_) {
        verify(() => mockNavigationProvider.push<Money?>(any())).calledOnce;
      },
    );

    blocTest<InstallmentsDetailsCubit, InstallmentsDetailsState>(
      'cta overdue makes necessary calls and navigate to payback page',
      build: () {
        final config = InstallmentsDetailsConfig(
          transactionCoreBankingIdentifier: 'coreBankingIdentifier',
          accountId: 'account123',
          category: TransactionCategory.general,
          imageUrl: 'imageUrl',
          merchantName: 'merchantName',
          transactionType: TransactionType.card,
          subtype: subtype,
          creditCardAccountId: creditCardAccountId,
          isAccountActive: true,
        );
        return InstallmentsDetailsCubit(
          config: config,
          navigationProvider: mockNavigationProvider,
          exceptionHandler: mockLoanErrorHandler,
          loanInteractor: mockLoanInteractor,
          dialogProvider: mockDialogProvider,
          transactionInteractor: mockTransactionInteractor,
          accountInteractorAdapter: accountInteractorToLoanDomainAdapter,
          loanLocalizations: loanLocalizations,
          featureToggleProvider: mockFeatureToggleProvider,
          splitTransactionsInteractor: mockSplitTransactionsInteractor,
        );
      },
      seed: () => InstallmentsDetailsLoaded(
        autopay: autopayMissedPayment,
        loanAccount: LoanTestHelpers.loanAccount,
        productInfo: productInfo,
        transaction: LoanTestHelpers.transaction,
        isInstallmentsMissedPaymentEnabled: true,
      ),
      setUp: () {
        when(
          () => accountInteractorToLoanDomainAdapter.fetchDebitAccount(),
        ).justAnswerAsync(
          DebitAccount(
            id: 'id',
            name: 'name',
            balance: Money.fromNumWithCurrency(1000000, Currency.aed),
          ),
        );
        when(
          () => mockLoanInteractor.evaluateRepayment(
            request: any(named: 'request'),
          ),
        ).justAnswerAsync(LoanTestHelpers.schedule);

        when(() => mockNavigationProvider.push<Money?>(any()))
            .justAnswerAsync(Money.fromIntWithCurrency(0, Currency.aed));
      },
      act: (cubit) => cubit.onCta(),
      expect: () => <InstallmentsDetailsState>[
        InstallmentsDetailsLoaded(
          autopay: autopayMissedPayment,
          loanAccount: LoanTestHelpers.loanAccount,
          productInfo: productInfo,
          transaction: LoanTestHelpers.transaction,
          isEvaluatingPayback: true,
          isInstallmentsMissedPaymentEnabled: true,
        ),
        InstallmentsDetailsLoaded(
          autopay: autopayMissedPayment,
          loanAccount: LoanTestHelpers.loanAccount,
          productInfo: productInfo,
          transaction: LoanTestHelpers.transaction,
          isInstallmentsMissedPaymentEnabled: true,
        ),
      ],
      verify: (_) {
        verify(() => mockNavigationProvider.push<Money?>(any())).calledOnce;
      },
    );

    blocTest<InstallmentsDetailsCubit, InstallmentsDetailsState>(
      'cta gives error if balance is not enough to pay fully',
      build: () {
        final config = InstallmentsDetailsConfig(
          transactionCoreBankingIdentifier: 'coreBankingIdentifier',
          accountId: 'account123',
          category: TransactionCategory.general,
          imageUrl: 'imageUrl',
          merchantName: 'merchantName',
          transactionType: TransactionType.card,
          subtype: subtype,
          creditCardAccountId: creditCardAccountId,
          isAccountActive: true,
        );
        return InstallmentsDetailsCubit(
          config: config,
          navigationProvider: mockNavigationProvider,
          exceptionHandler: mockLoanErrorHandler,
          loanInteractor: mockLoanInteractor,
          dialogProvider: mockDialogProvider,
          transactionInteractor: mockTransactionInteractor,
          accountInteractorAdapter: accountInteractorToLoanDomainAdapter,
          loanLocalizations: loanLocalizations,
          featureToggleProvider: mockFeatureToggleProvider,
          splitTransactionsInteractor: mockSplitTransactionsInteractor,
        );
      },
      seed: () => InstallmentsDetailsLoaded(
        autopay: autopay,
        loanAccount: LoanTestHelpers.loanAccount,
        productInfo: productInfo,
        transaction: LoanTestHelpers.transaction,
        isInstallmentsMissedPaymentEnabled: false,
      ),
      setUp: () {
        when(
          () => accountInteractorToLoanDomainAdapter.fetchDebitAccount(),
        ).justAnswerAsync(
          DebitAccount(
            id: 'id',
            name: 'name',
            balance: Money.fromNumWithCurrency(10, Currency.aed),
          ),
        );
        when(
          () => mockLoanInteractor.evaluateRepayment(
            request: any(named: 'request'),
          ),
        ).justAnswerAsync(LoanTestHelpers.schedule);

        when(
          () => loanLocalizations.transactionLoanInsufficientBalancePay(any()),
        ).thenReturn('Insufficient balance to pay back the loan');

        when(() => mockDialogProvider.showResponsiveToastMessage(any()))
            .justComplete();
      },
      act: (cubit) => cubit.onCta(),
      expect: () => <InstallmentsDetailsState>[
        InstallmentsDetailsLoaded(
          autopay: autopay,
          loanAccount: LoanTestHelpers.loanAccount,
          productInfo: productInfo,
          transaction: LoanTestHelpers.transaction,
          isEvaluatingPayback: true,
          isInstallmentsMissedPaymentEnabled: false,
        ),
        InstallmentsDetailsLoaded(
          autopay: autopay,
          loanAccount: LoanTestHelpers.loanAccount,
          productInfo: productInfo,
          transaction: LoanTestHelpers.transaction,
          isInstallmentsMissedPaymentEnabled: false,
        ),
      ],
      verify: (_) {
        verify(() => mockDialogProvider.showResponsiveToastMessage(any()))
            .calledOnce;
      },
    );

    blocTest<InstallmentsDetailsCubit, InstallmentsDetailsState>(
      'cta if paid amount is equal or bigger than loan go back',
      build: () {
        final config = InstallmentsDetailsConfig(
          transactionCoreBankingIdentifier: 'coreBankingIdentifier',
          accountId: 'account123',
          category: TransactionCategory.general,
          imageUrl: 'imageUrl',
          merchantName: 'merchantName',
          transactionType: TransactionType.card,
          subtype: subtype,
          creditCardAccountId: creditCardAccountId,
          isAccountActive: true,
        );
        return InstallmentsDetailsCubit(
          config: config,
          navigationProvider: mockNavigationProvider,
          exceptionHandler: mockLoanErrorHandler,
          loanInteractor: mockLoanInteractor,
          dialogProvider: mockDialogProvider,
          transactionInteractor: mockTransactionInteractor,
          accountInteractorAdapter: accountInteractorToLoanDomainAdapter,
          loanLocalizations: loanLocalizations,
          featureToggleProvider: mockFeatureToggleProvider,
          splitTransactionsInteractor: mockSplitTransactionsInteractor,
        );
      },
      seed: () => InstallmentsDetailsLoaded(
        autopay: autopay,
        loanAccount: LoanTestHelpers.loanAccount,
        productInfo: productInfo,
        transaction: LoanTestHelpers.transaction,
        isInstallmentsMissedPaymentEnabled: false,
      ),
      setUp: () {
        when(
          () => accountInteractorToLoanDomainAdapter.fetchDebitAccount(),
        ).justAnswerAsync(
          DebitAccount(
            id: 'id',
            name: 'name',
            balance: Money.fromNumWithCurrency(1000000, Currency.aed),
          ),
        );
        when(
          () => mockLoanInteractor.evaluateRepayment(
            request: any(named: 'request'),
          ),
        ).justAnswerAsync(LoanTestHelpers.schedule);

        when(() => mockNavigationProvider.push<Money?>(any())).justAnswerAsync(
          Money.fromIntWithCurrency(**************, Currency.aed),
        );
      },
      act: (cubit) => cubit.onCta(),
      expect: () => <InstallmentsDetailsState>[
        InstallmentsDetailsLoaded(
          autopay: autopay,
          loanAccount: LoanTestHelpers.loanAccount,
          productInfo: productInfo,
          transaction: LoanTestHelpers.transaction,
          isEvaluatingPayback: true,
          isInstallmentsMissedPaymentEnabled: false,
        ),
      ],
      verify: (_) {
        verify(() => mockNavigationProvider.push<Money?>(any())).calledOnce;
        verify(() => mockNavigationProvider.popUntilFirstRoute()).calledOnce;
      },
    );

    blocTest<InstallmentsDetailsCubit, InstallmentsDetailsState>(
      'cta handles error',
      build: () {
        final config = InstallmentsDetailsConfig(
          transactionCoreBankingIdentifier: 'coreBankingIdentifier',
          accountId: 'account123',
          category: TransactionCategory.general,
          imageUrl: 'imageUrl',
          merchantName: 'merchantName',
          transactionType: TransactionType.card,
          subtype: subtype,
          creditCardAccountId: creditCardAccountId,
          isAccountActive: true,
        );
        return InstallmentsDetailsCubit(
          config: config,
          navigationProvider: mockNavigationProvider,
          exceptionHandler: mockLoanErrorHandler,
          loanInteractor: mockLoanInteractor,
          dialogProvider: mockDialogProvider,
          transactionInteractor: mockTransactionInteractor,
          accountInteractorAdapter: accountInteractorToLoanDomainAdapter,
          loanLocalizations: loanLocalizations,
          featureToggleProvider: mockFeatureToggleProvider,
          splitTransactionsInteractor: mockSplitTransactionsInteractor,
        );
      },
      seed: () => InstallmentsDetailsLoaded(
        autopay: autopay,
        loanAccount: LoanTestHelpers.loanAccount,
        productInfo: productInfo,
        transaction: LoanTestHelpers.transaction,
        isInstallmentsMissedPaymentEnabled: false,
      ),
      setUp: () {
        when(
          () => accountInteractorToLoanDomainAdapter.fetchDebitAccount(),
        ).thenThrow(Exception());
        when(
          () => mockLoanInteractor.evaluateRepayment(
            request: any(named: 'request'),
          ),
        ).justAnswerAsync(LoanTestHelpers.schedule);

        when(() => mockNavigationProvider.push<Money?>(any()))
            .justAnswerAsync(Money.fromIntWithCurrency(0, Currency.aed));
      },
      act: (cubit) => cubit.onCta(),
      expect: () => <InstallmentsDetailsState>[
        InstallmentsDetailsLoaded(
          autopay: autopay,
          loanAccount: LoanTestHelpers.loanAccount,
          productInfo: productInfo,
          transaction: LoanTestHelpers.transaction,
          isEvaluatingPayback: true,
          isInstallmentsMissedPaymentEnabled: false,
        ),
        InstallmentsDetailsLoaded(
          autopay: autopay,
          loanAccount: LoanTestHelpers.loanAccount,
          productInfo: productInfo,
          transaction: LoanTestHelpers.transaction,
          isInstallmentsMissedPaymentEnabled: false,
        ),
      ],
      verify: (_) {
        verify(
          () => mockLoanErrorHandler.handleError(
            any(),
            stackTrace: any(
              named: 'stackTrace',
            ),
          ),
        ).calledOnce;
      },
    );
  });
}
