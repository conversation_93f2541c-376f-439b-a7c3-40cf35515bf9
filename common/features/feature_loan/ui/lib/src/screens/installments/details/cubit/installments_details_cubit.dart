import 'package:collection/collection.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_context_faq_api/wio_common_feature_context_faq_api.dart';
import 'package:wio_common_feature_transaction_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_loan_api/domain/adapter/account_interactor_to_loan_domain_adapter.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_api/navigation/screens/loan_payback_navigation_config.dart';
import 'package:wio_feature_loan_ui/feature_loan_ui.dart';
import 'package:wio_feature_loan_ui/src/bottom_sheets/transaction_loan_interest_info_bottom_sheet.dart';
import 'package:wio_feature_loan_ui/src/handlers/loan_error_handler.dart';
import 'package:wio_feature_loan_ui/src/navigation/screens/payback_confirmation_navigation_config.dart';
import 'package:wio_feature_loan_ui/src/screens/payback/confirmation/config/payback_confirmation_config.dart';

part 'installments_details_state.dart';

class InstallmentsDetailsCubit extends BaseCubit<InstallmentsDetailsState> {
  final InstallmentsDetailsConfig _config;
  final NavigationProvider _navigationProvider;
  final LoanErrorHandler _exceptionHandler;
  final LoanInteractor _loanInteractor;
  final ResponsiveDialogProvider _dialogProvider;
  final TransactionsInteractor _transactionsInteractor;
  final AccountInteractorToLoanDomainAdapter _accountInteractorAdapter;
  final LoanLocalizations _loanLocalizations;
  final FeatureToggleProvider _featureToggleProvider;
  final SplitTransactionsInteractor _splitTransactionsInteractor;

  InstallmentsDetailsCubit({
    required InstallmentsDetailsConfig config,
    required NavigationProvider navigationProvider,
    required LoanErrorHandler exceptionHandler,
    required LoanInteractor loanInteractor,
    required ResponsiveDialogProvider dialogProvider,
    required TransactionsInteractor transactionInteractor,
    required AccountInteractorToLoanDomainAdapter accountInteractorAdapter,
    required LoanLocalizations loanLocalizations,
    required FeatureToggleProvider featureToggleProvider,
    required SplitTransactionsInteractor splitTransactionsInteractor,
  })  : _config = config,
        _navigationProvider = navigationProvider,
        _exceptionHandler = exceptionHandler,
        _loanInteractor = loanInteractor,
        _dialogProvider = dialogProvider,
        _transactionsInteractor = transactionInteractor,
        _accountInteractorAdapter = accountInteractorAdapter,
        _loanLocalizations = loanLocalizations,
        _featureToggleProvider = featureToggleProvider,
        _splitTransactionsInteractor = splitTransactionsInteractor,
        super(const InstallmentsDetailsLoading());

  bool get _isInstallmentsMissedPaymentEnabled => _featureToggleProvider
      .get(LoanFeatureToggles.isInstallmentsMissedPaymentEnabled);

  Future<void> initialize() async {
    try {
      final (loanAccount, autopay, productDetails, transaction) = await (
        _getLoanAccount,
        _loanInteractor.getAutopayInfo(_config.accountId),
        _loanInteractor.getProductInfo(subtype: _config.subtype),
        _transactionsInteractor.getTransactionByCoreBankingId(
          coreBankingIdentifier: _config.transactionCoreBankingIdentifier,
          filterByItems:
              TransactionFilterByItems(accountId: _config.creditCardAccountId),
        )
      ).wait;

      safeEmit(
        InstallmentsDetailsLoaded(
          autopay: autopay,
          loanAccount: loanAccount,
          productInfo: productDetails,
          transaction: transaction,
          isInstallmentsMissedPaymentEnabled:
              _isInstallmentsMissedPaymentEnabled,
        ),
      );
    } on Object catch (e, st) {
      _exceptionHandler.handleError(e, stackTrace: st);
      safeEmit(state.toErrorState());
    }
  }

  Future<void> showInstallmentDetails({
    required Installment installment,
  }) async {
    final state = this.state;

    if (state is! InstallmentsDetailsLoaded) {
      return;
    }

    final config = InstallmentBottomSheetConfig(
      autopayHour: state.paymentTime,
      installment: installment,
      latePaymentFee: state.latePaymentFee,
      bottomSheetSource: BottomSheetSource.transactionLoan,
    );
    await _dialogProvider.showBottomSheetOrDialog<void>(
      content: InstallmentDetailsBody(config: config),
      config: const ResponsiveModalConfig(
        featureName: LoanFeatureNavigationConfig.name,
      ),
    );
  }

  Future<void> onCta() async {
    final state = this.state;

    if (state is! InstallmentsDetailsLoaded || state.isEvaluatingPayback) {
      return;
    }

    try {
      emit(state.updateEvaluating(isEvaluatingPayback: true));
      if (state.hasMissedInstallment) {
        final paidOverDueAmount = await _navigationProvider.push<Money?>(
          LoanPaybackNavigationConfig(
            minPaybackLimit: state.overdue,
            loanAccount: state.loanAccount,
            overdueAmount: state.overdue,
            totalOutstanding: state.amountDue,
          ),
        );

        if (paidOverDueAmount != null) {
          await _splitTransactionsInteractor.refreshInstallments();
          _navigationProvider.goBack();
        } else {
          safeEmit(state.updateEvaluating(isEvaluatingPayback: false));
        }
        return;
      } else {
        final amountDue = state.amountDue;

        final (schedule, debitAccount) =
            await _fetchScheduleAndDebitAccount(amountDue);

        if (isClosed) {
          return;
        }

        if (amountDue > debitAccount.balance) {
          _showInsufficientBalanceToPayError(debitAccount.balance);
          safeEmit(state.updateEvaluating(isEvaluatingPayback: false));
          return;
        }
        // LoanPaybackNavigationConfig
        final paidAmount = await _navigationProvider.push<Money?>(
          PaybackConfirmationNavigationConfig(
            config: InstallmentsPaybackConfirmationConfig(
              amount: amountDue,
              schedule: schedule,
              loanAccountId: _config.accountId,
              debitAccount: debitAccount,
              merchantName: _config.merchantName ?? '',
            ),
          ),
        );

        if (paidAmount != null) {
          /// if paid amount is not null it means payment done
          /// and loan should be closed
          /// return to the first route
          _navigationProvider.popUntilFirstRoute();
        } else {
          safeEmit(state.updateEvaluating(isEvaluatingPayback: false));
        }
      }
    } on Object catch (e, st) {
      if (isClosed) {
        return;
      }

      _exceptionHandler.handleError(e, stackTrace: st);
      safeEmit(state.updateEvaluating(isEvaluatingPayback: false));
    }
  }

  void openFaq() {
    _navigationProvider.showBottomSheet<void>(
      ContextFaqBottomSheetNavigationConfig(
        tags: [ContextFaqTags.installments],
        fromScreen: InstallmentsLearnMorePageNavigationConfig.screenName,
      ),
    );
  }

  void showInterestInfoBottomSheet() {
    _dialogProvider.showBottomSheetOrDialog<void>(
      content: const TransactionLoanInterestInfoBottomSheet(),
      config: const ResponsiveModalConfig(
        featureName: LoanFeatureNavigationConfig.name,
      ),
    );
  }

  void retry() {
    safeEmit(state.toLoadingState());
    initialize();
  }

  Future<LoanAccount> get _getLoanAccount => _config.isAccountActive
      ? _loanInteractor.getAccount(_config.accountId)
      : _loanInteractor.getAccount(
          _config.accountId,
          isActive: _config.isAccountActive,
        );

  Future<(Schedule schedule, DebitAccount debitAccount)>
      _fetchScheduleAndDebitAccount(Money amountDue) => (
            _loanInteractor.evaluateRepayment(
              request: LoanRepaymentEvaluationRequest(
                accountId: _config.accountId,
                amount: amountDue,
              ),
            ),
            _accountInteractorAdapter.fetchDebitAccount()
          ).wait;

  void _showInsufficientBalanceToPayError(Money balance) =>
      _dialogProvider.showResponsiveToastMessage(
        NotificationToastMessageConfiguration.error(
          _loanLocalizations.transactionLoanInsufficientBalancePay(
            balance.toCodeOnRightFormat(),
          ),
        ),
      );

  @override
  String toString() => 'InstallmentsDetailsCubit';
}
