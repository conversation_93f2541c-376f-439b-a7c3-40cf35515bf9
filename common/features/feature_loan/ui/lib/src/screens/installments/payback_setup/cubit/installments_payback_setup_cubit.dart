import 'package:collection/collection.dart';
import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/ui.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_common_feature_context_faq_api/wio_common_feature_context_faq_api.dart';
import 'package:wio_common_feature_transaction_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_loan_api/domain/adapter/account_interactor_to_loan_domain_adapter.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/feature_loan_ui.dart';
import 'package:wio_feature_loan_ui/src/bottom_sheets/convert_transaction_confirmation_bottom_sheet.dart';
import 'package:wio_feature_loan_ui/src/bottom_sheets/loan_processing_fee_info_bottom_sheet.dart';
import 'package:wio_feature_loan_ui/src/bottom_sheets/transaction_loan_interest_info_bottom_sheet.dart';
import 'package:wio_feature_loan_ui/src/common/status_screen_config_factory.dart';
import 'package:wio_feature_loan_ui/src/handlers/loan_error_handler.dart';
import 'package:wio_feature_loan_ui/src/screens/installments/payback_setup/cubit/installments_payback_setup_state.dart';

class InstallmentsPaybackSetupCubit
    extends BaseCubit<InstallmentsPaybackSetupState> {
  final SplitTransactionsInteractor _interactor;
  final LoanInteractor _loanInteractor;
  final Logger _logger;
  final ResponsiveDialogProvider _dialogProvider;
  final NavigationProvider _navigationProvider;
  final WioAppType _sourceApp;
  final LoanLocalizations _loanLocalizations;
  final ProductSubType _productSubType;
  final AccountInteractorToLoanDomainAdapter
      _accountInteractorToLoanDomainAdapter;
  final LoanErrorHandler _loanErrorHandler;

  InstallmentsPaybackSetupCubit({
    required SplitTransactionsInteractor interactor,
    required LoanInteractor loanInteractor,
    required Logger logger,
    required ResponsiveDialogProvider dialogProvider,
    required NavigationProvider navigationProvider,
    required WioAppType sourceApp,
    required LoanLocalizations loanLocalizations,
    required ProductSubType productSubType,
    required AccountInteractorToLoanDomainAdapter
        accountInteractorToLoanDomainAdapter,
    required LoanErrorHandler loanErrorHandler,
  })  : _interactor = interactor,
        _loanInteractor = loanInteractor,
        _logger = logger,
        _dialogProvider = dialogProvider,
        _navigationProvider = navigationProvider,
        _sourceApp = sourceApp,
        _loanLocalizations = loanLocalizations,
        _productSubType = productSubType,
        _accountInteractorToLoanDomainAdapter =
            accountInteractorToLoanDomainAdapter,
        _loanErrorHandler = loanErrorHandler,
        super(const InstallmentsPaybackSetupState.loading());

  @override
  String toString() => 'InstallmentsPaybackSetupCubit';

  void init(Transaction transaction) => _handleInitialization(transaction);

  void selectLoanPeriod(Period loanPeriod) {
    state.mapOrNull(
      idle: (it) => safeEmit(it.copyWith(selectedLoanPeriod: loanPeriod)),
    );
  }

  Future<void> showInstallmentDetails() async {
    state.mapOrNull(
      idle: (it) {
        final installments = it.selectedInstallmentOption?.installmentSummary;

        final installmentToShow = installments?.firstWhereOrNull(
              (installment) => installment is UpcomingInstallment,
            ) ??
            installments?.firstWhereOrNull(
              (installment) => installment is PaidInstallment,
            ) ??
            installments?.firstWhereOrNull(
              (installment) => installment is MissedInstallment,
            );

        if (installmentToShow == null) {
          _logger.error(
            '''There is no available installment in ${it.selectedInstallmentOption?.loanPeriod}''',
          );
          return;
        }

        final config = InstallmentBottomSheetConfig(
          autopayHour: it.productInfo.autopayHour,
          installment: installmentToShow,
          latePaymentFee: it.productInfo.latePaymentFee,
          bottomSheetSource: BottomSheetSource.transactionLoan,
        );

        _dialogProvider.showBottomSheetOrDialog<void>(
          content: InstallmentDetailsBody(config: config),
          config: const ResponsiveModalConfig(
            featureName: LoanFeatureNavigationConfig.name,
          ),
        );
      },
    );
  }

  Future<void> submit() async {
    await state.mapOrNull(
      idle: (it) async {
        try {
          final installmentOption = it.selectedInstallmentOption;
          final coreBankingId = it.transaction.coreBankingIdentifier;

          if (coreBankingId == null) {
            _loanErrorHandler.handleError(
              Exception('CoreBankingId is null'),
              message: 'CoreBankingId is missing',
            );

            return;
          }

          if (installmentOption == null) {
            _loanErrorHandler.handleError(
              Exception('selectedInstallmentOption is null'),
              message: 'Selected installment option is missing',
            );

            return;
          }

          if (it.isProcessing) {
            return;
          }

          final confirmed = await _dialogProvider.showBottomSheetOrDialog<bool>(
            content: const ConvertTransactionConfirmationBottomSheet(),
            config: const ResponsiveModalConfig(
              featureName: LoanFeatureNavigationConfig.name,
            ),
          );

          if (confirmed != true) {
            return;
          }

          emit(it.copyWith(isProcessing: true));

          await _submit(
            coreBankingId: coreBankingId,
            installmentOption: installmentOption,
          );
        } on Object catch (e, st) {
          _loanErrorHandler.handleError(e, stackTrace: st);
          safeEmit(it.copyWith(isProcessing: false));
        }
      },
    );
  }

  Future<void> _submit({
    required String coreBankingId,
    required InstallmentOption installmentOption,
  }) async {
    await _interactor.makeDisbursement(
      SplitTransactionDisbursementRequest(
        amount: installmentOption.loanAmount.principal,
        transactionCoreBankingId: coreBankingId,
        productSubType: _productSubType,
        loanPeriod: installmentOption.loanPeriod,
      ),
    );

    final installmentCount = installmentOption.installmentSummary?.length;

    final installment = installmentOption.installmentSummary?.firstOrNull;

    final installmentAmount = installment?.amountDue.totalAmount;

    final payDayOfMonth = installment?.dueDate.day;

    _navigationProvider.popUntilFirstRoute();
    await _navigationProvider.navigateTo<void>(
      LoanStatusScreenConfigFactory.success(
        sourceApp: _sourceApp,
        title: _loanLocalizations.transactionLoanSuccessScreenTitle,
        subtitle: payDayOfMonth != null && installmentCount != null
            ? _loanLocalizations.transactionLoanSuccessScreenSubtitle(
                installmentCount.toString(),
                OrdinalSuffixFormatter.formattedNumber(
                  payDayOfMonth,
                  'en',
                ),
              )
            : '',
        primaryButtonTitle: _loanLocalizations.transactionLoanSuccessScreenCta,
        description:
            '''${installmentAmount?.toCodeOnLeftFormat() ?? '--'} x${installmentCount ?? '-'}''',
        descriptionTextStyle: CompanyTextStylePointer.h2,
        text2: _loanLocalizations.transactionLoanSuccessScreenInfo,
      ),
    );
  }

  void openFaq() {
    _navigationProvider.showBottomSheet<void>(
      ContextFaqBottomSheetNavigationConfig(
        tags: [ContextFaqTags.installments],
        fromScreen: InstallmentsLearnMorePageNavigationConfig.screenName,
      ),
    );
  }

  void showInterestInfoBottomSheet() {
    _dialogProvider.showBottomSheetOrDialog<void>(
      content: const TransactionLoanInterestInfoBottomSheet(),
      config: const ResponsiveModalConfig(
        featureName: LoanFeatureNavigationConfig.name,
      ),
    );
  }

  void showProcessingFeeInfo() {
    _dialogProvider.showBottomSheetOrDialog<void>(
      content: const LoanProcessingFeeInfoBottomSheet(),
      config: const ResponsiveModalConfig(
        featureName: LoanFeatureNavigationConfig.name,
      ),
    );
  }

  Future<void> _handleInitialization(Transaction transaction) async {
    try {
      final (productInfo, installmentOptions, debitAccount) = await (
        _getProductInfo(),
        _getInstallmentOptions(transaction),
        _accountInteractorToLoanDomainAdapter.fetchDebitAccount(),
      ).wait;

      return safeEmit(
        InstallmentsPaybackSetupState.idle(
          transaction: transaction,
          installmentOptions: installmentOptions,
          selectedLoanPeriod: installmentOptions.first.loanPeriod,
          productInfo: productInfo,
          debitAccount: debitAccount,
        ),
      );
    } on Object catch (_) {
      return safeEmit(const InstallmentsPaybackSetupState.error());
    }
  }

  Future<ProductInfo> _getProductInfo() async {
    try {
      return _loanInteractor.getProductInfo(
        subtype: ProductSubType.cardPurchase,
      );
    } on Object catch (error, stackTrace) {
      _logger.error(
        'Something went wrong while fetching product info',
        error: error,
        stackTrace: stackTrace,
      );

      rethrow;
    }
  }

  Future<List<InstallmentOption>> _getInstallmentOptions(
    Transaction transaction,
  ) async {
    try {
      final coreBankingIdentifier = transaction.coreBankingIdentifier;

      // should not happen
      if (coreBankingIdentifier == null) {
        throw Exception('CoreBankingIdentifier not found');
      }

      final schedule = await _interactor.evaluateDisbursement(
        SplitTransactionDisbursementEvaluationRequest(
          amount: transaction.amount,
          transactionCoreBankingId: coreBankingIdentifier,
          productSubType: ProductSubType.cardPurchase,
        ),
      );

      final installmentOptions = schedule.installmentOptions;

      if (installmentOptions == null || installmentOptions.isEmpty) {
        throw Exception('InstallmentOptions not found');
      }

      return installmentOptions;
    } on Object catch (error, stackTrace) {
      _logger.error(
        'Something went wrong while fetching split transaction installment '
        'options. CoreBankingId: ${transaction.coreBankingIdentifier}',
        error: error,
        stackTrace: stackTrace,
      );

      rethrow;
    }
  }
}
