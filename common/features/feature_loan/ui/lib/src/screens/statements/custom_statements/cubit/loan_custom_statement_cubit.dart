import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/ui.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/src/screens/statements/custom_statements/cubit/loan_custom_statement_state.dart';
import 'package:wio_feature_loan_ui/src/screens/statements/custom_statements/date_selection/loan_statement_date_selection_dialog.dart';

class LoanCustomStatementCubit extends BaseCubit<LoanCustomStatementState> {
  static const _defaultPeriod = LoanCustomStatementPeriod.oneMonth;

  final LoanInteractor _loanInteractor;
  final NavigationProvider _navigationProvider;
  final ResponsiveDialogProvider _responsiveDialogProvider;
  final TimeProvider _timeProvider;
  final String _accountId;
  final Logger _logger;

  LoanCustomStatementCubit({
    required LoanInteractor loanInteractor,
    required NavigationProvider navigationProvider,
    required ResponsiveDialogProvider responsiveDialogProvider,
    required TimeProvider timeProvider,
    required String accountId,
    required Logger logger,
  })  : _loanInteractor = loanInteractor,
        _navigationProvider = navigationProvider,
        _responsiveDialogProvider = responsiveDialogProvider,
        _timeProvider = timeProvider,
        _accountId = accountId,
        _logger = logger,
        super(const LoanCustomStatementState.loading());

  DateTime get _currentDate => _timeProvider.getDateTime();

  Future<void> init() async {
    try {
      state.mapOrNull(
        failed: (_) => emit(const LoanCustomStatementState.loading()),
      );
      final loanAccount = await _loanInteractor.getAccount(_accountId);
      final accountCreationDate = loanAccount.createdAt;
      final endDate = _timeProvider.getDateTime();
      final startDate = _subtractMonths(endDate, _defaultPeriod.inMonths);
      final periods = _calculatePeriods(
        endDate: endDate,
        accountCreationDate: accountCreationDate,
      );

      emit(
        LoanCustomStatementState.loaded(
          accountCreationDate: accountCreationDate,
          startDate: startDate.isBefore(accountCreationDate)
              ? accountCreationDate
              : startDate,
          endDate: endDate,
          periods: periods,
          selectedPeriod: _defaultPeriod,
        ),
      );
    } on Object catch (error, stackTrace) {
      _logger.error(
        'Could not get custom statements data\n${error.toString()}',
        error: error,
        stackTrace: stackTrace,
      );

      safeEmit(const LoanCustomStatementState.failed());
    }
  }

  void cancel() => _navigationProvider.goBack();

  Future<void> selectStartDate() async {
    await state.mapOrNull(
      loaded: (loaded) async {
        final selectedDate =
            await _responsiveDialogProvider.showBottomSheetOrDialog<DateTime?>(
          content: LoanStatementDateSelectionDialog(
            dateSelectionType: LoanStatementDateSelectionType.startDate,
            firstDate: loaded.accountCreationDate,
            lastDate: _currentDate,
            selectedDate: loaded.startDate,
          ),
          config: const ResponsiveModalConfig(
            featureName: LoanFeatureNavigationConfig.name,
          ),
        );

        _logger.info('New selected startDate: $selectedDate');

        if (selectedDate == null || selectedDate == loaded.startDate) return;

        emit(
          loaded.copyWith(
            startDate: selectedDate,
            endDate: selectedDate.isAfter(loaded.endDate)
                ? selectedDate
                : loaded.endDate,
            selectedPeriod: null,
          ),
        );
      },
    );
  }

  Future<void> selectEndDate() async {
    await state.mapOrNull(
      loaded: (loaded) async {
        final selectedDate =
            await _responsiveDialogProvider.showBottomSheetOrDialog<DateTime?>(
          content: LoanStatementDateSelectionDialog(
            dateSelectionType: LoanStatementDateSelectionType.endDate,
            firstDate: loaded.accountCreationDate,
            lastDate: _currentDate,
            selectedDate: loaded.endDate,
          ),
          config: const ResponsiveModalConfig(
            featureName: LoanFeatureNavigationConfig.name,
          ),
        );

        _logger.info('New selected endDate: $selectedDate');

        if (selectedDate == null || selectedDate == loaded.endDate) return;

        emit(
          loaded.copyWith(
            endDate: selectedDate,
            startDate: selectedDate.isBefore(loaded.startDate)
                ? selectedDate
                : loaded.startDate,
            selectedPeriod: null,
          ),
        );
      },
    );
  }

  void selectPeriod(LoanCustomStatementPeriod period) {
    state.mapOrNull(
      loaded: (loaded) {
        final startDate = _subtractMonths(_currentDate, period.inMonths);
        emit(
          loaded.copyWith(
            selectedPeriod: period,
            startDate: startDate.isBefore(loaded.accountCreationDate)
                ? loaded.accountCreationDate
                : startDate,
            endDate: _currentDate,
          ),
        );
      },
    );
  }

  void onGenerateStatement() {
    state.mapOrNull(
      loaded: (loaded) {
        _navigationProvider.push(
          LoanStatementFileViewerNavigationConfig(
            config: LoanStatementFileViewerConfig.byDateRange(
              filter: CustomStatementFilter(
                accountId: _accountId,
                startDate: loaded.startDate,
                endDate: loaded.endDate,
              ),
            ),
          ),
        );
      },
    );
  }

  List<LoanCustomStatementPeriod> _calculatePeriods({
    required DateTime endDate,
    required DateTime accountCreationDate,
  }) {
    return LoanCustomStatementPeriod.values
        .where(
          (period) => _subtractMonths(endDate, period.inMonths)
              .isAfter(accountCreationDate),
        )
        .toList();
  }

  DateTime _subtractMonths(DateTime date, int amount) {
    return _addMonths(date, -amount);
  }

  DateTime _addMonths(DateTime date, int amount) {
    if (amount == 0) return date;

    final dayOfMonth = date.day;
    final desiredDate = DateTime(date.year, date.month + amount + 1, 0);
    final daysInDesiredMonth = desiredDate.day;
    final isAtTheEndOfMonth = dayOfMonth >= daysInDesiredMonth;

    return isAtTheEndOfMonth
        ? desiredDate
        : DateTime(desiredDate.year, desiredDate.month, dayOfMonth);
  }

  @override
  String toString() => 'CreditCustomStatementCubit';
}
