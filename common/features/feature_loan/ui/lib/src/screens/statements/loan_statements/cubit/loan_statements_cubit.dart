import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:logging_api/logging.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ui/ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/src/extensions/stream_extensions.dart';
import 'package:wio_feature_loan_ui/src/handlers/loan_error_handler.dart';
import 'package:wio_feature_loan_ui/src/screens/statements/loan_statements/cubit/loan_statements_state.dart';
import 'package:wio_feature_loan_ui/src/screens/statements/loan_statements/dialogs/year_selection_dialog.dart';

class LoanStatementsCubit extends BaseCubit<LoanStatementsState> {
  final String _loanAccountId;
  final LoanProductIdentifier _accountType;
  final LoanStatementsInteractor _statementsInteractor;
  final LoanErrorHandler _errorHandler;
  final Logger _logger;
  final NavigationProvider _navigationProvider;
  final ResponsiveDialogProvider _responsiveDialogProvider;
  final FeatureToggleProvider _featureToggleProvider;
  final LoanAnalytics _analytics;

  LoanStatementsCubit({
    required String loanAccountId,
    required LoanProductIdentifier accountType,
    required LoanStatementsInteractor statementsInteractor,
    required LoanErrorHandler errorHandler,
    required Logger logger,
    required NavigationProvider navigationProvider,
    required ResponsiveDialogProvider responsiveDialogProvider,
    required FeatureToggleProvider featureToggleProvider,
    required LoanAnalytics analytics,
  })  : _loanAccountId = loanAccountId,
        _accountType = accountType,
        _statementsInteractor = statementsInteractor,
        _errorHandler = errorHandler,
        _logger = logger,
        _navigationProvider = navigationProvider,
        _responsiveDialogProvider = responsiveDialogProvider,
        _featureToggleProvider = featureToggleProvider,
        _analytics = analytics,
        super(const LoanStatementsState.loading());

  bool get _isSmePosCustomStatementEnabled => _featureToggleProvider
      .get(LoanFeatureToggles.isSmePosCustomStatementEnabled);

  Future<void> initialize() async {
    _logger.debug('Initializing statements for $_loanAccountId');
    _analytics.statementsOpened(_accountType);
    _initStatements();
  }

  void onRetry() {
    state.mapOrNull(
      failed: (_) {
        initialize();
      },
    );
  }

  void onSelectStatement(DatedStatement statement) {
    state.mapOrNull(
      idle: (_) {
        _navigationProvider.push(
          LoanStatementFileViewerNavigationConfig(
            config: LoanStatementFileViewerConfig.byStatementId(
              statementId: statement.id,
              statementDate: statement.date,
              productIdentifier: _accountType,
            ),
          ),
        );
      },
    );
  }

  void onSelectYear() {
    state.mapOrNull(idle: (it) => _handleSelectYear());
  }

  void onCustomStatementPressed() {
    state.mapOrNull(
      idle: (it) {
        if (it.showCustomStatements) {
          _navigationProvider.push(
            LoanCustomStatementPageNavigationConfig(
              _loanAccountId,
              productIdentifier: _accountType,
            ),
          );
        }
      },
    );
  }

  void _initStatements() {
    _statementsInteractor
        .getStatements(_loanAccountId)
        .toStream()
        .doOnListen(() => safeEmit(const LoanStatementsState.loading()))
        .withSafeEmit(this)
        .map(_handleStatementsLoaded)
        .logError(_logger)
        .handleError(_handleErrors)
        .doOnData(safeEmit)
        .drain<void>();
  }

  LoanStatementsState _handleStatementsLoaded(
    List<DatedStatement> statements,
  ) {
    final availableYears = statements.map((it) => it.year).toSet().toList()
      ..sort();

    if (availableYears.isEmpty) {
      return const LoanStatementsState.empty();
    }

    final lastYear = availableYears.last;
    final showCustomStatements = _isSmePosCustomStatementEnabled &&
        _accountType == LoanProductIdentifier.pos;

    return LoanStatementsState.idle(
      selectedYear: lastYear,
      statements: statements,
      showCustomStatements: showCustomStatements,
    );
  }

  Future<void> _handleSelectYear() async {
    await state.mapOrNull(
      idle: (it) async {
        final availableYears = it.selectableYears;
        final newYear =
            await _responsiveDialogProvider.showBottomSheetOrDialog<int>(
          content: YearSelectionDialog(years: availableYears),
          config: const ResponsiveModalConfig(
            featureName: LoanFeatureNavigationConfig.name,
          ),
        );

        if (newYear == null || newYear == it.selectedYear) return;

        emit(it.copyWith(selectedYear: newYear));
      },
    );
  }

  void _handleErrors(Object error) {
    state.mapOrNull(
      loading: (_) {
        _errorHandler.handleError(error);
        safeEmit(const LoanStatementsState.failed());
      },
    );
  }

  @override
  String toString() => 'LoanStatementsCubit{}';
}
