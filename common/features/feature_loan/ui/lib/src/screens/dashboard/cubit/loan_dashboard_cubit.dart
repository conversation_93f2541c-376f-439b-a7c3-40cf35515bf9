import 'dart:async';

import 'package:collection/collection.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/ui.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_api/navigation/screens/loan_payback_navigation_config.dart';
import 'package:wio_feature_loan_ui/src/bottom_sheets/repayment_restricted_bottom_sheet.dart';
import 'package:wio_feature_loan_ui/src/navigation/configs/manage_loan_page_navigation_config.dart';
import 'package:wio_feature_loan_ui/src/navigation/screens/repayment_plan_page_navigation_config.dart';
import 'package:wio_feature_loan_ui/src/screens/dashboard/cubit/loan_dashboard_state.dart';
import 'package:wio_feature_loan_ui/src/screens/repayment/config/repayment_plan_page_config.dart';
import 'package:wio_feature_loan_ui/src/screens/repayment/repayment_plan_cubit.dart';

class LoanDashboardCubit extends BaseCubit<LoanDashboardState> {
  final String _loanAccountId;
  final LoanInteractor _loanInteractor;
  final NavigationProvider _navigationProvider;
  final ResponsiveDialogProvider _responsiveDialogProvider;

  LoanDashboardCubit({
    required String loanAccountId,
    required LoanInteractor loanInteractor,
    required NavigationProvider navigationProvider,
    required ResponsiveDialogProvider responsiveDialogProvider,
  })  : _loanAccountId = loanAccountId,
        _loanInteractor = loanInteractor,
        _navigationProvider = navigationProvider,
        _responsiveDialogProvider = responsiveDialogProvider,
        super(const LoanDashboardState.loading()) {
    _listenCreditAccountsChange();
  }

  Future<void> init({LoanAccount? loanAccount}) async {
    try {
      final account =
          loanAccount ?? await _loanInteractor.getAccount(_loanAccountId);

      final (autopay, productInfo, repaymentRestrictionDecision) = await (
        _loanInteractor.getAutopayInfo(account.id),
        _loanInteractor.getProductInfo(),
        _loanInteractor.getRepaymentRestrictionDecision(account: account)
      ).wait;

      safeEmit(
        LoanDashboardState.idle(
          loanAccount: account,
          autopayHour: productInfo.autopayHour,
          installments: autopay.schedule?.installmentSummary ?? [],
          latePaymentFee: productInfo.latePaymentFee,
          missedPaymentCount: autopay.schedule?.missedPaymentCount,
          minPaybackLimit: productInfo.minimumPaymentAmount,
          overdueAmount: autopay.schedule?.overdue,
          totalOutstanding: autopay.totalOutstanding,
          repaymentRestrictionDecision: repaymentRestrictionDecision,
        ),
      );
    } on Object {
      safeEmit(LoanDashboardState.error(loanAccountId: _loanAccountId));
    }
  }

  void payback() {
    state.mapOrNull(
      idle: (it) async {
        final repaymentRestrictionDecision = it.repaymentRestrictionDecision;

        return switch (repaymentRestrictionDecision) {
          RepaymentRestrictedDecision() => _handleRepaymentRestrictedDecision(
              repaymentRestrictionDecision.restrictionPeriod,
            ),
          RepaymentAllowedDecision() => _handleRepaymentAllowedDecision(),
        };
      },
    );
  }

  void _handleRepaymentAllowedDecision() {
    state.mapOrNull(
      idle: (it) async {
        final totalOutstanding = it.totalOutstanding;
        final paidAmount = await _navigationProvider.push<Money?>(
          LoanPaybackNavigationConfig(
            minPaybackLimit: it.minPaybackLimit ??
                Money.fromNumWithCurrency(0, Currency.aed),
            loanAccount: it.loanAccount,
            overdueAmount: it.overdueAmount,
            totalOutstanding: totalOutstanding,
          ),
        );

        if (paidAmount != null && paidAmount >= totalOutstanding) {
          /// if some loan is paid
          /// and paid amount is bigger than totalOutstanding
          /// it means loan is closed so we need to go back from dashboard
          /// closed account won't return from accounts call
          /// so entry point in dashboard should be removed
          _navigationProvider.goBack();
        }
      },
    );
  }

  void _handleRepaymentRestrictedDecision(Period restrictionPeriod) {
    _responsiveDialogProvider.showBottomSheetOrDialog<void>(
      content: RepaymentRestrictedBottomSheet(
        restrictionPeriod: restrictionPeriod,
      ),
      config: const ResponsiveModalConfig(
        featureName: LoanFeatureNavigationConfig.name,
      ),
    );
  }

  void manage() {
    state.mapOrNull(
      idle: (it) {
        _navigationProvider
            .push(ManageLoanPageNavigationConfig(loanAccount: it.loanAccount));
      },
    );
  }

  void navigateToRepaymentPlan({
    RepaymentTab initialTab = RepaymentTab.upcoming,
  }) {
    state.mapOrNull(
      idle: (it) {
        _navigationProvider.push(
          RepaymentPlanPageNavigationConfig(
            RepaymentPlanPageConfig(
              installments: it.installments,
              latePaymentFee: it.latePaymentFee,
              autopayHour: it.autopayHour,
              initialTab: initialTab,
              productIdentifier: it.loanAccount.productIdentifier,
            ),
          ),
        );
      },
    );
  }

  void _listenCreditAccountsChange() {
    _loanInteractor.observeLoanDetails().listenSafe(
      this,
      onData: (data) {
        final accounts = data.content?.accounts;
        if (accounts != null) {
          final loanAccount = accounts
              .firstWhereOrNull((account) => account.id == _loanAccountId);

          init(loanAccount: loanAccount);
        }
      },
    );
  }

  @override
  String toString() => 'LoanDashboardCubit';
}
