import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui/cubit/base_cubit.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/feature_loan_ui.dart';
import 'package:wio_feature_loan_ui/src/bottom_sheets/current_outstanding_info_bottom_sheet.dart';
import 'package:wio_feature_loan_ui/src/screens/repayment/config/repayment_plan_page_config.dart';

part 'repayment_plan_state.dart';

class RepaymentPlanPageCubit extends BaseCubit<RepaymentPlanState> {
  final ResponsiveDialogProvider _responsiveDialogProvider;

  RepaymentPlanPageCubit({
    required RepaymentPlanPageConfig config,
    required ResponsiveDialogProvider responsiveDialogProvider,
  })  : _responsiveDialogProvider = responsiveDialogProvider,
        super(
          RepaymentPlanState(
            selectedTab: config.initialTab,
            allInstallments: config.installments,
            autopayHour: config.autopayHour,
            latePaymentFee: config.latePaymentFee,
            filteredInstallments: _filteredInstallments(
              config.initialTab,
              config.installments,
            ),
          ),
        );

  @override
  String toString() {
    return 'RepaymentPlanPageCubit';
  }

  void repaymentTabChanged(RepaymentTab tab) {
    final installments = List.of(state.allInstallments);
    emit(
      RepaymentPlanState(
        selectedTab: tab,
        allInstallments: installments,
        filteredInstallments: _filteredInstallments(
          tab,
          installments,
        ),
        autopayHour: state.autopayHour,
        latePaymentFee: state.latePaymentFee,
      ),
    );
  }

  Future<void> showInstallmentDetails({
    required Installment installment,
  }) async {
    final config = InstallmentBottomSheetConfig(
      autopayHour: state.autopayHour,
      installment: installment,
      latePaymentFee: state.latePaymentFee,
    );
    await _responsiveDialogProvider.showBottomSheetOrDialog<void>(
      content: InstallmentDetailsBody(config: config),
      config: const ResponsiveModalConfig(
        featureName: LoanFeatureNavigationConfig.name,
      ),
    );
  }

  void showCurrentOutstandingInfo(Balances balances) {
    _responsiveDialogProvider.showBottomSheetOrDialog<void>(
      content: CurrentOutstandingInfoBottomSheet(balances: balances),
      config: const ResponsiveModalConfig(
        featureName: LoanFeatureNavigationConfig.name,
      ),
    );
  }
}

List<Installment> _filteredInstallments(
  RepaymentTab tab,
  List<Installment> installments,
) =>
    switch (tab) {
      RepaymentTab.upcoming => [
          ...installments.whereType<UpcomingInstallment>(),
        ],
      RepaymentTab.history => [
          ...installments.whereType<MissedInstallment>(),
          ...installments.whereType<PaidInstallment>(),
        ],
    };

String repaymentTabName(LoanLocalizations localization, RepaymentTab tab) =>
    switch (tab) {
      RepaymentTab.upcoming => localization.repaymentPlanPageUpcomingTabLabel,
      RepaymentTab.history => localization.repaymentPlanPageHistoryTabLabel
    };
