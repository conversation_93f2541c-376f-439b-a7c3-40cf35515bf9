import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/money.dart';

part 'product_info.freezed.dart';

/// Returns the static details about the product
@freezed
class ProductInfo with _$ProductInfo {
  /// Default constructor
  const factory ProductInfo({
    /// The fee that will be charged for late payment
    required Money latePaymentFee,
    double? revolvingFeePercentage,
    String? autopayHour,
    Money? minimumPaymentAmount,
    int? lateFeeGracePeriod,
    InterestRange? interestRange,

    /// minumum transaction amount to split
    Money? minimumDisbursementAmount,
  }) = _ProductInfo;
}

/// Model for interest rate range of a product
@freezed
class InterestRange with _$InterestRange {
  /// Constructor
  const factory InterestRange({
    /// The minimum interest rate
    required int minimum,

    /// The maximum interest rate
    required int maximum,
  }) = _InterestRange;
}

/// Model for amount range of a product
@freezed
class AmountRange with _$AmountRange {
  /// Constructor
  const factory AmountRange({
    /// The minimum amount that needs to be borrowed
    required Money minimum,

    /// The maximum amount that the user can borrow
    required Money maximum,
  }) = _AmountRange;
}
