// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ProductInfo {
  /// The fee that will be charged for late payment
  Money get latePaymentFee => throw _privateConstructorUsedError;
  double? get revolvingFeePercentage => throw _privateConstructorUsedError;
  String? get autopayHour => throw _privateConstructorUsedError;
  Money? get minimumPaymentAmount => throw _privateConstructorUsedError;
  int? get lateFeeGracePeriod => throw _privateConstructorUsedError;
  InterestRange? get interestRange => throw _privateConstructorUsedError;

  /// minumum transaction amount to split
  Money? get minimumDisbursementAmount => throw _privateConstructorUsedError;

  /// Create a copy of ProductInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductInfoCopyWith<ProductInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductInfoCopyWith<$Res> {
  factory $ProductInfoCopyWith(
          ProductInfo value, $Res Function(ProductInfo) then) =
      _$ProductInfoCopyWithImpl<$Res, ProductInfo>;
  @useResult
  $Res call(
      {Money latePaymentFee,
      double? revolvingFeePercentage,
      String? autopayHour,
      Money? minimumPaymentAmount,
      int? lateFeeGracePeriod,
      InterestRange? interestRange,
      Money? minimumDisbursementAmount});

  $InterestRangeCopyWith<$Res>? get interestRange;
}

/// @nodoc
class _$ProductInfoCopyWithImpl<$Res, $Val extends ProductInfo>
    implements $ProductInfoCopyWith<$Res> {
  _$ProductInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latePaymentFee = null,
    Object? revolvingFeePercentage = freezed,
    Object? autopayHour = freezed,
    Object? minimumPaymentAmount = freezed,
    Object? lateFeeGracePeriod = freezed,
    Object? interestRange = freezed,
    Object? minimumDisbursementAmount = freezed,
  }) {
    return _then(_value.copyWith(
      latePaymentFee: null == latePaymentFee
          ? _value.latePaymentFee
          : latePaymentFee // ignore: cast_nullable_to_non_nullable
              as Money,
      revolvingFeePercentage: freezed == revolvingFeePercentage
          ? _value.revolvingFeePercentage
          : revolvingFeePercentage // ignore: cast_nullable_to_non_nullable
              as double?,
      autopayHour: freezed == autopayHour
          ? _value.autopayHour
          : autopayHour // ignore: cast_nullable_to_non_nullable
              as String?,
      minimumPaymentAmount: freezed == minimumPaymentAmount
          ? _value.minimumPaymentAmount
          : minimumPaymentAmount // ignore: cast_nullable_to_non_nullable
              as Money?,
      lateFeeGracePeriod: freezed == lateFeeGracePeriod
          ? _value.lateFeeGracePeriod
          : lateFeeGracePeriod // ignore: cast_nullable_to_non_nullable
              as int?,
      interestRange: freezed == interestRange
          ? _value.interestRange
          : interestRange // ignore: cast_nullable_to_non_nullable
              as InterestRange?,
      minimumDisbursementAmount: freezed == minimumDisbursementAmount
          ? _value.minimumDisbursementAmount
          : minimumDisbursementAmount // ignore: cast_nullable_to_non_nullable
              as Money?,
    ) as $Val);
  }

  /// Create a copy of ProductInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InterestRangeCopyWith<$Res>? get interestRange {
    if (_value.interestRange == null) {
      return null;
    }

    return $InterestRangeCopyWith<$Res>(_value.interestRange!, (value) {
      return _then(_value.copyWith(interestRange: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProductInfoImplCopyWith<$Res>
    implements $ProductInfoCopyWith<$Res> {
  factory _$$ProductInfoImplCopyWith(
          _$ProductInfoImpl value, $Res Function(_$ProductInfoImpl) then) =
      __$$ProductInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Money latePaymentFee,
      double? revolvingFeePercentage,
      String? autopayHour,
      Money? minimumPaymentAmount,
      int? lateFeeGracePeriod,
      InterestRange? interestRange,
      Money? minimumDisbursementAmount});

  @override
  $InterestRangeCopyWith<$Res>? get interestRange;
}

/// @nodoc
class __$$ProductInfoImplCopyWithImpl<$Res>
    extends _$ProductInfoCopyWithImpl<$Res, _$ProductInfoImpl>
    implements _$$ProductInfoImplCopyWith<$Res> {
  __$$ProductInfoImplCopyWithImpl(
      _$ProductInfoImpl _value, $Res Function(_$ProductInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? latePaymentFee = null,
    Object? revolvingFeePercentage = freezed,
    Object? autopayHour = freezed,
    Object? minimumPaymentAmount = freezed,
    Object? lateFeeGracePeriod = freezed,
    Object? interestRange = freezed,
    Object? minimumDisbursementAmount = freezed,
  }) {
    return _then(_$ProductInfoImpl(
      latePaymentFee: null == latePaymentFee
          ? _value.latePaymentFee
          : latePaymentFee // ignore: cast_nullable_to_non_nullable
              as Money,
      revolvingFeePercentage: freezed == revolvingFeePercentage
          ? _value.revolvingFeePercentage
          : revolvingFeePercentage // ignore: cast_nullable_to_non_nullable
              as double?,
      autopayHour: freezed == autopayHour
          ? _value.autopayHour
          : autopayHour // ignore: cast_nullable_to_non_nullable
              as String?,
      minimumPaymentAmount: freezed == minimumPaymentAmount
          ? _value.minimumPaymentAmount
          : minimumPaymentAmount // ignore: cast_nullable_to_non_nullable
              as Money?,
      lateFeeGracePeriod: freezed == lateFeeGracePeriod
          ? _value.lateFeeGracePeriod
          : lateFeeGracePeriod // ignore: cast_nullable_to_non_nullable
              as int?,
      interestRange: freezed == interestRange
          ? _value.interestRange
          : interestRange // ignore: cast_nullable_to_non_nullable
              as InterestRange?,
      minimumDisbursementAmount: freezed == minimumDisbursementAmount
          ? _value.minimumDisbursementAmount
          : minimumDisbursementAmount // ignore: cast_nullable_to_non_nullable
              as Money?,
    ));
  }
}

/// @nodoc

class _$ProductInfoImpl implements _ProductInfo {
  const _$ProductInfoImpl(
      {required this.latePaymentFee,
      this.revolvingFeePercentage,
      this.autopayHour,
      this.minimumPaymentAmount,
      this.lateFeeGracePeriod,
      this.interestRange,
      this.minimumDisbursementAmount});

  /// The fee that will be charged for late payment
  @override
  final Money latePaymentFee;
  @override
  final double? revolvingFeePercentage;
  @override
  final String? autopayHour;
  @override
  final Money? minimumPaymentAmount;
  @override
  final int? lateFeeGracePeriod;
  @override
  final InterestRange? interestRange;

  /// minumum transaction amount to split
  @override
  final Money? minimumDisbursementAmount;

  @override
  String toString() {
    return 'ProductInfo(latePaymentFee: $latePaymentFee, revolvingFeePercentage: $revolvingFeePercentage, autopayHour: $autopayHour, minimumPaymentAmount: $minimumPaymentAmount, lateFeeGracePeriod: $lateFeeGracePeriod, interestRange: $interestRange, minimumDisbursementAmount: $minimumDisbursementAmount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductInfoImpl &&
            (identical(other.latePaymentFee, latePaymentFee) ||
                other.latePaymentFee == latePaymentFee) &&
            (identical(other.revolvingFeePercentage, revolvingFeePercentage) ||
                other.revolvingFeePercentage == revolvingFeePercentage) &&
            (identical(other.autopayHour, autopayHour) ||
                other.autopayHour == autopayHour) &&
            (identical(other.minimumPaymentAmount, minimumPaymentAmount) ||
                other.minimumPaymentAmount == minimumPaymentAmount) &&
            (identical(other.lateFeeGracePeriod, lateFeeGracePeriod) ||
                other.lateFeeGracePeriod == lateFeeGracePeriod) &&
            (identical(other.interestRange, interestRange) ||
                other.interestRange == interestRange) &&
            (identical(other.minimumDisbursementAmount,
                    minimumDisbursementAmount) ||
                other.minimumDisbursementAmount == minimumDisbursementAmount));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      latePaymentFee,
      revolvingFeePercentage,
      autopayHour,
      minimumPaymentAmount,
      lateFeeGracePeriod,
      interestRange,
      minimumDisbursementAmount);

  /// Create a copy of ProductInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductInfoImplCopyWith<_$ProductInfoImpl> get copyWith =>
      __$$ProductInfoImplCopyWithImpl<_$ProductInfoImpl>(this, _$identity);
}

abstract class _ProductInfo implements ProductInfo {
  const factory _ProductInfo(
      {required final Money latePaymentFee,
      final double? revolvingFeePercentage,
      final String? autopayHour,
      final Money? minimumPaymentAmount,
      final int? lateFeeGracePeriod,
      final InterestRange? interestRange,
      final Money? minimumDisbursementAmount}) = _$ProductInfoImpl;

  /// The fee that will be charged for late payment
  @override
  Money get latePaymentFee;
  @override
  double? get revolvingFeePercentage;
  @override
  String? get autopayHour;
  @override
  Money? get minimumPaymentAmount;
  @override
  int? get lateFeeGracePeriod;
  @override
  InterestRange? get interestRange;

  /// minumum transaction amount to split
  @override
  Money? get minimumDisbursementAmount;

  /// Create a copy of ProductInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductInfoImplCopyWith<_$ProductInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$InterestRange {
  /// The minimum interest rate
  int get minimum => throw _privateConstructorUsedError;

  /// The maximum interest rate
  int get maximum => throw _privateConstructorUsedError;

  /// Create a copy of InterestRange
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InterestRangeCopyWith<InterestRange> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InterestRangeCopyWith<$Res> {
  factory $InterestRangeCopyWith(
          InterestRange value, $Res Function(InterestRange) then) =
      _$InterestRangeCopyWithImpl<$Res, InterestRange>;
  @useResult
  $Res call({int minimum, int maximum});
}

/// @nodoc
class _$InterestRangeCopyWithImpl<$Res, $Val extends InterestRange>
    implements $InterestRangeCopyWith<$Res> {
  _$InterestRangeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InterestRange
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minimum = null,
    Object? maximum = null,
  }) {
    return _then(_value.copyWith(
      minimum: null == minimum
          ? _value.minimum
          : minimum // ignore: cast_nullable_to_non_nullable
              as int,
      maximum: null == maximum
          ? _value.maximum
          : maximum // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InterestRangeImplCopyWith<$Res>
    implements $InterestRangeCopyWith<$Res> {
  factory _$$InterestRangeImplCopyWith(
          _$InterestRangeImpl value, $Res Function(_$InterestRangeImpl) then) =
      __$$InterestRangeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int minimum, int maximum});
}

/// @nodoc
class __$$InterestRangeImplCopyWithImpl<$Res>
    extends _$InterestRangeCopyWithImpl<$Res, _$InterestRangeImpl>
    implements _$$InterestRangeImplCopyWith<$Res> {
  __$$InterestRangeImplCopyWithImpl(
      _$InterestRangeImpl _value, $Res Function(_$InterestRangeImpl) _then)
      : super(_value, _then);

  /// Create a copy of InterestRange
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minimum = null,
    Object? maximum = null,
  }) {
    return _then(_$InterestRangeImpl(
      minimum: null == minimum
          ? _value.minimum
          : minimum // ignore: cast_nullable_to_non_nullable
              as int,
      maximum: null == maximum
          ? _value.maximum
          : maximum // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$InterestRangeImpl implements _InterestRange {
  const _$InterestRangeImpl({required this.minimum, required this.maximum});

  /// The minimum interest rate
  @override
  final int minimum;

  /// The maximum interest rate
  @override
  final int maximum;

  @override
  String toString() {
    return 'InterestRange(minimum: $minimum, maximum: $maximum)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InterestRangeImpl &&
            (identical(other.minimum, minimum) || other.minimum == minimum) &&
            (identical(other.maximum, maximum) || other.maximum == maximum));
  }

  @override
  int get hashCode => Object.hash(runtimeType, minimum, maximum);

  /// Create a copy of InterestRange
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InterestRangeImplCopyWith<_$InterestRangeImpl> get copyWith =>
      __$$InterestRangeImplCopyWithImpl<_$InterestRangeImpl>(this, _$identity);
}

abstract class _InterestRange implements InterestRange {
  const factory _InterestRange(
      {required final int minimum,
      required final int maximum}) = _$InterestRangeImpl;

  /// The minimum interest rate
  @override
  int get minimum;

  /// The maximum interest rate
  @override
  int get maximum;

  /// Create a copy of InterestRange
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InterestRangeImplCopyWith<_$InterestRangeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$AmountRange {
  /// The minimum amount that needs to be borrowed
  Money get minimum => throw _privateConstructorUsedError;

  /// The maximum amount that the user can borrow
  Money get maximum => throw _privateConstructorUsedError;

  /// Create a copy of AmountRange
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AmountRangeCopyWith<AmountRange> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AmountRangeCopyWith<$Res> {
  factory $AmountRangeCopyWith(
          AmountRange value, $Res Function(AmountRange) then) =
      _$AmountRangeCopyWithImpl<$Res, AmountRange>;
  @useResult
  $Res call({Money minimum, Money maximum});
}

/// @nodoc
class _$AmountRangeCopyWithImpl<$Res, $Val extends AmountRange>
    implements $AmountRangeCopyWith<$Res> {
  _$AmountRangeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AmountRange
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minimum = null,
    Object? maximum = null,
  }) {
    return _then(_value.copyWith(
      minimum: null == minimum
          ? _value.minimum
          : minimum // ignore: cast_nullable_to_non_nullable
              as Money,
      maximum: null == maximum
          ? _value.maximum
          : maximum // ignore: cast_nullable_to_non_nullable
              as Money,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AmountRangeImplCopyWith<$Res>
    implements $AmountRangeCopyWith<$Res> {
  factory _$$AmountRangeImplCopyWith(
          _$AmountRangeImpl value, $Res Function(_$AmountRangeImpl) then) =
      __$$AmountRangeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Money minimum, Money maximum});
}

/// @nodoc
class __$$AmountRangeImplCopyWithImpl<$Res>
    extends _$AmountRangeCopyWithImpl<$Res, _$AmountRangeImpl>
    implements _$$AmountRangeImplCopyWith<$Res> {
  __$$AmountRangeImplCopyWithImpl(
      _$AmountRangeImpl _value, $Res Function(_$AmountRangeImpl) _then)
      : super(_value, _then);

  /// Create a copy of AmountRange
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minimum = null,
    Object? maximum = null,
  }) {
    return _then(_$AmountRangeImpl(
      minimum: null == minimum
          ? _value.minimum
          : minimum // ignore: cast_nullable_to_non_nullable
              as Money,
      maximum: null == maximum
          ? _value.maximum
          : maximum // ignore: cast_nullable_to_non_nullable
              as Money,
    ));
  }
}

/// @nodoc

class _$AmountRangeImpl implements _AmountRange {
  const _$AmountRangeImpl({required this.minimum, required this.maximum});

  /// The minimum amount that needs to be borrowed
  @override
  final Money minimum;

  /// The maximum amount that the user can borrow
  @override
  final Money maximum;

  @override
  String toString() {
    return 'AmountRange(minimum: $minimum, maximum: $maximum)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AmountRangeImpl &&
            (identical(other.minimum, minimum) || other.minimum == minimum) &&
            (identical(other.maximum, maximum) || other.maximum == maximum));
  }

  @override
  int get hashCode => Object.hash(runtimeType, minimum, maximum);

  /// Create a copy of AmountRange
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AmountRangeImplCopyWith<_$AmountRangeImpl> get copyWith =>
      __$$AmountRangeImplCopyWithImpl<_$AmountRangeImpl>(this, _$identity);
}

abstract class _AmountRange implements AmountRange {
  const factory _AmountRange(
      {required final Money minimum,
      required final Money maximum}) = _$AmountRangeImpl;

  /// The minimum amount that needs to be borrowed
  @override
  Money get minimum;

  /// The maximum amount that the user can borrow
  @override
  Money get maximum;

  /// Create a copy of AmountRange
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AmountRangeImplCopyWith<_$AmountRangeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
