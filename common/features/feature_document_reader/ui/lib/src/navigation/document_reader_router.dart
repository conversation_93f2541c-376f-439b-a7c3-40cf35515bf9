import 'package:flutter/widgets.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_document_reader_api/feature_document_reader_api.dart';
import 'package:wio_feature_document_reader_ui/src/screens/document_reader/document_reader_page.dart';

class DocumentReaderRouter extends NavigationRouter {
  @override
  Route<Object?> getScreenRoute(RouteSettings settings) {
    final Object? config;
    if (settings.arguments is DocumentReaderFeatureNavigationConfig) {
      final featureConfig =
          settings.arguments as DocumentReaderFeatureNavigationConfig;
      config = featureConfig.destination;
    } else {
      config = settings.arguments;
    }

    final currentScreenConfigSettings = RouteSettings(
      name: settings.name,
      arguments: config,
    );

    if (config is DocumentReaderScannerPageNavigationConfig) {
      final configFile = config.config;

      return PageRouteBuilder<Object?>(
        opaque: false,
        transitionDuration: Duration.zero,
        reverseTransitionDuration: Duration.zero,
        settings: currentScreenConfigSettings,
        pageBuilder: (_, __, ___) => DocumentReaderPage(
          config: configFile,
        ),
      );
    }

    throw Exception('Unknown $config for the $runtimeType');
  }
}
