import 'package:di/di.dart';
import 'package:logging_api/logging.dart';
import 'package:ui/l10n/common_localizations.g.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_document_reader_api/domain/document_reader_interactor.dart';
import 'package:wio_feature_document_reader_api/navigation/document_reader_feature_navigation_config.dart';
import 'package:wio_feature_document_reader_ui/src/navigation/document_reader_router.dart';
import 'package:wio_feature_document_reader_ui/src/screens/document_reader/config_generator.dart';
import 'package:wio_feature_document_reader_ui/src/screens/document_reader/document_reader_cubit.dart';

class DocumentReaderFeatureDependencyModuleResolver {
  static void register() {
    DependencyProvider.registerLazySingleton<NavigationRouter>(
      () => DocumentReaderRouter(),
      instanceName: DocumentReaderFeatureNavigationConfig.name,
    );

    _documentReader();
  }

  static void _documentReader() {
    DependencyProvider.registerFactory<ConfigGenerator>(
      () => ConfigGeneratorImpl(
        commonLocalizations: DependencyProvider.get<CommonLocalizations>(),
      ),
    );
    DependencyProvider.registerFactory(
      () => DocumentReaderCubit(
        documentReaderInteractor:
            DependencyProvider.get<DocumentReaderInteractor>(),
        navigator: DependencyProvider.get<NavigationProvider>(),
        logger: DependencyProvider.get<Logger>(
          instanceName: WioDomain.core.name,
        ),
        configGenerator: DependencyProvider.get<ConfigGenerator>(),
      ),
    );
  }
}
