// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'document_reader_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$DocumentReaderResult {
  String get imageFront => throw _privateConstructorUsedError;
  String get imageBack => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DocumentReaderResultCopyWith<DocumentReaderResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DocumentReaderResultCopyWith<$Res> {
  factory $DocumentReaderResultCopyWith(DocumentReaderResult value,
          $Res Function(DocumentReaderResult) then) =
      _$DocumentReaderResultCopyWithImpl<$Res>;
  $Res call({String imageFront, String imageBack});
}

/// @nodoc
class _$DocumentReaderResultCopyWithImpl<$Res>
    implements $DocumentReaderResultCopyWith<$Res> {
  _$DocumentReaderResultCopyWithImpl(this._value, this._then);

  final DocumentReaderResult _value;
  // ignore: unused_field
  final $Res Function(DocumentReaderResult) _then;

  @override
  $Res call({
    Object? imageFront = freezed,
    Object? imageBack = freezed,
  }) {
    return _then(_value.copyWith(
      imageFront: imageFront == freezed
          ? _value.imageFront
          : imageFront // ignore: cast_nullable_to_non_nullable
              as String,
      imageBack: imageBack == freezed
          ? _value.imageBack
          : imageBack // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
abstract class _$$_DocumentReaderResultCopyWith<$Res>
    implements $DocumentReaderResultCopyWith<$Res> {
  factory _$$_DocumentReaderResultCopyWith(_$_DocumentReaderResult value,
          $Res Function(_$_DocumentReaderResult) then) =
      __$$_DocumentReaderResultCopyWithImpl<$Res>;
  @override
  $Res call({String imageFront, String imageBack});
}

/// @nodoc
class __$$_DocumentReaderResultCopyWithImpl<$Res>
    extends _$DocumentReaderResultCopyWithImpl<$Res>
    implements _$$_DocumentReaderResultCopyWith<$Res> {
  __$$_DocumentReaderResultCopyWithImpl(_$_DocumentReaderResult _value,
      $Res Function(_$_DocumentReaderResult) _then)
      : super(_value, (v) => _then(v as _$_DocumentReaderResult));

  @override
  _$_DocumentReaderResult get _value => super._value as _$_DocumentReaderResult;

  @override
  $Res call({
    Object? imageFront = freezed,
    Object? imageBack = freezed,
  }) {
    return _then(_$_DocumentReaderResult(
      imageFront: imageFront == freezed
          ? _value.imageFront
          : imageFront // ignore: cast_nullable_to_non_nullable
              as String,
      imageBack: imageBack == freezed
          ? _value.imageBack
          : imageBack // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_DocumentReaderResult implements _DocumentReaderResult {
  _$_DocumentReaderResult({required this.imageFront, required this.imageBack});

  @override
  final String imageFront;
  @override
  final String imageBack;

  @override
  String toString() {
    return 'DocumentReaderResult(imageFront: $imageFront, imageBack: $imageBack)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DocumentReaderResult &&
            const DeepCollectionEquality()
                .equals(other.imageFront, imageFront) &&
            const DeepCollectionEquality().equals(other.imageBack, imageBack));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(imageFront),
      const DeepCollectionEquality().hash(imageBack));

  @JsonKey(ignore: true)
  @override
  _$$_DocumentReaderResultCopyWith<_$_DocumentReaderResult> get copyWith =>
      __$$_DocumentReaderResultCopyWithImpl<_$_DocumentReaderResult>(
          this, _$identity);
}

abstract class _DocumentReaderResult implements DocumentReaderResult {
  factory _DocumentReaderResult(
      {required final String imageFront,
      required final String imageBack}) = _$_DocumentReaderResult;

  @override
  String get imageFront => throw _privateConstructorUsedError;
  @override
  String get imageBack => throw _privateConstructorUsedError;
  @override
  @JsonKey(ignore: true)
  _$$_DocumentReaderResultCopyWith<_$_DocumentReaderResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DocumentReaderFailureResult {
  String get code => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DocumentReaderFailureResultCopyWith<DocumentReaderFailureResult>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DocumentReaderFailureResultCopyWith<$Res> {
  factory $DocumentReaderFailureResultCopyWith(
          DocumentReaderFailureResult value,
          $Res Function(DocumentReaderFailureResult) then) =
      _$DocumentReaderFailureResultCopyWithImpl<$Res>;
  $Res call({String code, String message});
}

/// @nodoc
class _$DocumentReaderFailureResultCopyWithImpl<$Res>
    implements $DocumentReaderFailureResultCopyWith<$Res> {
  _$DocumentReaderFailureResultCopyWithImpl(this._value, this._then);

  final DocumentReaderFailureResult _value;
  // ignore: unused_field
  final $Res Function(DocumentReaderFailureResult) _then;

  @override
  $Res call({
    Object? code = freezed,
    Object? message = freezed,
  }) {
    return _then(_value.copyWith(
      code: code == freezed
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      message: message == freezed
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
abstract class _$$_DocumentReaderFailureResultCopyWith<$Res>
    implements $DocumentReaderFailureResultCopyWith<$Res> {
  factory _$$_DocumentReaderFailureResultCopyWith(
          _$_DocumentReaderFailureResult value,
          $Res Function(_$_DocumentReaderFailureResult) then) =
      __$$_DocumentReaderFailureResultCopyWithImpl<$Res>;
  @override
  $Res call({String code, String message});
}

/// @nodoc
class __$$_DocumentReaderFailureResultCopyWithImpl<$Res>
    extends _$DocumentReaderFailureResultCopyWithImpl<$Res>
    implements _$$_DocumentReaderFailureResultCopyWith<$Res> {
  __$$_DocumentReaderFailureResultCopyWithImpl(
      _$_DocumentReaderFailureResult _value,
      $Res Function(_$_DocumentReaderFailureResult) _then)
      : super(_value, (v) => _then(v as _$_DocumentReaderFailureResult));

  @override
  _$_DocumentReaderFailureResult get _value =>
      super._value as _$_DocumentReaderFailureResult;

  @override
  $Res call({
    Object? code = freezed,
    Object? message = freezed,
  }) {
    return _then(_$_DocumentReaderFailureResult(
      code: code == freezed
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      message: message == freezed
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_DocumentReaderFailureResult implements _DocumentReaderFailureResult {
  _$_DocumentReaderFailureResult({required this.code, required this.message});

  @override
  final String code;
  @override
  final String message;

  @override
  String toString() {
    return 'DocumentReaderFailureResult(code: $code, message: $message)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DocumentReaderFailureResult &&
            const DeepCollectionEquality().equals(other.code, code) &&
            const DeepCollectionEquality().equals(other.message, message));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(code),
      const DeepCollectionEquality().hash(message));

  @JsonKey(ignore: true)
  @override
  _$$_DocumentReaderFailureResultCopyWith<_$_DocumentReaderFailureResult>
      get copyWith => __$$_DocumentReaderFailureResultCopyWithImpl<
          _$_DocumentReaderFailureResult>(this, _$identity);
}

abstract class _DocumentReaderFailureResult
    implements DocumentReaderFailureResult {
  factory _DocumentReaderFailureResult(
      {required final String code,
      required final String message}) = _$_DocumentReaderFailureResult;

  @override
  String get code => throw _privateConstructorUsedError;
  @override
  String get message => throw _privateConstructorUsedError;
  @override
  @JsonKey(ignore: true)
  _$$_DocumentReaderFailureResultCopyWith<_$_DocumentReaderFailureResult>
      get copyWith => throw _privateConstructorUsedError;
}
