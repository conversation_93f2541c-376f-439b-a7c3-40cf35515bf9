import 'package:di/di.dart';
import 'package:logging_api/logging.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_document_reader_api/domain/document_reader_interactor.dart';
import 'package:wio_feature_document_reader_api/domain/use_case/initialize_sdk_use_case.dart';
import 'package:wio_feature_document_reader_api/domain/use_case/show_document_reader_use_case.dart';
import 'package:wio_feature_document_reader_impl/feature_document_reader_impl.dart';
import 'package:wio_feature_document_reader_impl/src/domain/use_case/common/document_reader_channel.dart';
import 'package:wio_feature_document_reader_impl/src/domain/use_case/initialize_sdk_use_case_impl.dart';
import 'package:wio_feature_document_reader_impl/src/domain/use_case/show_document_reader_use_case_impl.dart';

class DocumentReaderDomainDependencyModuleResolver {
  static void register() {
    _useCases();
    _interactors();
  }

  static void _interactors() {
    DependencyProvider.registerLazySingleton<DocumentReaderInteractor>(
      () => DocumentReaderInteractorImpl(
        initializeSdkUseCase: DependencyProvider.get<InitializeSdkUseCase>(),
        showDocumentReaderUseCase:
            DependencyProvider.get<ShowDocumentReaderUseCase>(),
      ),
    );
  }

  static void _useCases() {
    DependencyProvider.registerLazySingleton<DocumentReaderChannel>(
      () => const DocumentReaderChannel(),
    );

    DependencyProvider.registerLazySingleton<InitializeSdkUseCase>(
      () => InitializeSdkUseCaseImpl(
        channel: DependencyProvider.get<DocumentReaderChannel>(),
        channelInvoker: DependencyProvider.get<ChannelInvoker>(),
            logger: DependencyProvider.get<Logger>(
              instanceName: WioDomain.core.name,
            ),
      ),
    );

    DependencyProvider.registerLazySingleton<ShowDocumentReaderUseCase>(
      () => ShowDocumentReaderUseCaseImpl(
        channel: DependencyProvider.get<DocumentReaderChannel>(),
        channelInvoker: DependencyProvider.get<ChannelInvoker>(),
            logger: DependencyProvider.get<Logger>(
              instanceName: WioDomain.core.name,
            ),
      ),
    );
  }
}
