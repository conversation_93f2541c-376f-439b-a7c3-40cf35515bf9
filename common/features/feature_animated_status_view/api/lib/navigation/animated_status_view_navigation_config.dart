import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_animated_status_view_api/models/animated_status_screen_model.dart';

part 'animated_status_view_navigation_config.freezed.dart';

@freezed
class AnimatedStatusViewNavigationConfig extends FeatureNavigationConfig
    with _$AnimatedStatusViewNavigationConfig {
  static const name = 'animated_status_view_feature';

  const factory AnimatedStatusViewNavigationConfig({
    required AnimatedStatusScreenModel model,
  }) = _AnimatedStatusViewNavigationConfig;

  const AnimatedStatusViewNavigationConfig._() : super(name);

  @override
  String toString() => 'AnimatedStatusViewNavigationConfig';
}
