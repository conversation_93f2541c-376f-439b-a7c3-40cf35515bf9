import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_account_api/wio_feature_account_api.dart';

part 'account_details.freezed.dart';

@freezed
class PaymentAccountDetails with _$PaymentAccountDetails {
  const PaymentAccountDetails._();

  factory PaymentAccountDetails({
    required AccountDetails accountDetails,
    // TODO(mcheshulko): Add payment fields
  }) = _PaymentAccountDetails;
}
