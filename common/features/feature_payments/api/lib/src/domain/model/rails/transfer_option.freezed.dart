// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transfer_option.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TransferOption {
  String get id => throw _privateConstructorUsedError;
  ProviderType get providerType => throw _privateConstructorUsedError;
  Money get initialSourceAmount => throw _privateConstructorUsedError;
  Money get initialTargetAmount => throw _privateConstructorUsedError;
  Money get chargedSourceAmount => throw _privateConstructorUsedError;
  Money get chargedTargetAmount => throw _privateConstructorUsedError;
  double get exchangeRate => throw _privateConstructorUsedError;
  FeeDetails get feeDetails => throw _privateConstructorUsedError;
  String get estimatedDeliveryInterval => throw _privateConstructorUsedError;

  /// Option validation error message (limit, insufficient balance etc.)
  String get errorMessage => throw _privateConstructorUsedError;

  /// Fee charging type customer can choose on rails selection (can be empty)
  List<FeeChargingType> get allowedFeeTypes =>
      throw _privateConstructorUsedError;
  bool get skipOptionUserView => throw _privateConstructorUsedError;
  ChannelType get channel => throw _privateConstructorUsedError;
  EstimatedDeliveryType get estimatedDeliveryType =>
      throw _privateConstructorUsedError;
  String? get providerLogo => throw _privateConstructorUsedError;

  /// It is null for [ProviderType.local] transfer options
  DateTime? get deliveryTime => throw _privateConstructorUsedError;
  bool? get beneficiaryUpdateRequired => throw _privateConstructorUsedError;

  /// Create a copy of TransferOption
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TransferOptionCopyWith<TransferOption> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransferOptionCopyWith<$Res> {
  factory $TransferOptionCopyWith(
          TransferOption value, $Res Function(TransferOption) then) =
      _$TransferOptionCopyWithImpl<$Res, TransferOption>;
  @useResult
  $Res call(
      {String id,
      ProviderType providerType,
      Money initialSourceAmount,
      Money initialTargetAmount,
      Money chargedSourceAmount,
      Money chargedTargetAmount,
      double exchangeRate,
      FeeDetails feeDetails,
      String estimatedDeliveryInterval,
      String errorMessage,
      List<FeeChargingType> allowedFeeTypes,
      bool skipOptionUserView,
      ChannelType channel,
      EstimatedDeliveryType estimatedDeliveryType,
      String? providerLogo,
      DateTime? deliveryTime,
      bool? beneficiaryUpdateRequired});

  $FeeDetailsCopyWith<$Res> get feeDetails;
}

/// @nodoc
class _$TransferOptionCopyWithImpl<$Res, $Val extends TransferOption>
    implements $TransferOptionCopyWith<$Res> {
  _$TransferOptionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TransferOption
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? providerType = null,
    Object? initialSourceAmount = null,
    Object? initialTargetAmount = null,
    Object? chargedSourceAmount = null,
    Object? chargedTargetAmount = null,
    Object? exchangeRate = null,
    Object? feeDetails = null,
    Object? estimatedDeliveryInterval = null,
    Object? errorMessage = null,
    Object? allowedFeeTypes = null,
    Object? skipOptionUserView = null,
    Object? channel = null,
    Object? estimatedDeliveryType = null,
    Object? providerLogo = freezed,
    Object? deliveryTime = freezed,
    Object? beneficiaryUpdateRequired = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      providerType: null == providerType
          ? _value.providerType
          : providerType // ignore: cast_nullable_to_non_nullable
              as ProviderType,
      initialSourceAmount: null == initialSourceAmount
          ? _value.initialSourceAmount
          : initialSourceAmount // ignore: cast_nullable_to_non_nullable
              as Money,
      initialTargetAmount: null == initialTargetAmount
          ? _value.initialTargetAmount
          : initialTargetAmount // ignore: cast_nullable_to_non_nullable
              as Money,
      chargedSourceAmount: null == chargedSourceAmount
          ? _value.chargedSourceAmount
          : chargedSourceAmount // ignore: cast_nullable_to_non_nullable
              as Money,
      chargedTargetAmount: null == chargedTargetAmount
          ? _value.chargedTargetAmount
          : chargedTargetAmount // ignore: cast_nullable_to_non_nullable
              as Money,
      exchangeRate: null == exchangeRate
          ? _value.exchangeRate
          : exchangeRate // ignore: cast_nullable_to_non_nullable
              as double,
      feeDetails: null == feeDetails
          ? _value.feeDetails
          : feeDetails // ignore: cast_nullable_to_non_nullable
              as FeeDetails,
      estimatedDeliveryInterval: null == estimatedDeliveryInterval
          ? _value.estimatedDeliveryInterval
          : estimatedDeliveryInterval // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      allowedFeeTypes: null == allowedFeeTypes
          ? _value.allowedFeeTypes
          : allowedFeeTypes // ignore: cast_nullable_to_non_nullable
              as List<FeeChargingType>,
      skipOptionUserView: null == skipOptionUserView
          ? _value.skipOptionUserView
          : skipOptionUserView // ignore: cast_nullable_to_non_nullable
              as bool,
      channel: null == channel
          ? _value.channel
          : channel // ignore: cast_nullable_to_non_nullable
              as ChannelType,
      estimatedDeliveryType: null == estimatedDeliveryType
          ? _value.estimatedDeliveryType
          : estimatedDeliveryType // ignore: cast_nullable_to_non_nullable
              as EstimatedDeliveryType,
      providerLogo: freezed == providerLogo
          ? _value.providerLogo
          : providerLogo // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryTime: freezed == deliveryTime
          ? _value.deliveryTime
          : deliveryTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      beneficiaryUpdateRequired: freezed == beneficiaryUpdateRequired
          ? _value.beneficiaryUpdateRequired
          : beneficiaryUpdateRequired // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }

  /// Create a copy of TransferOption
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FeeDetailsCopyWith<$Res> get feeDetails {
    return $FeeDetailsCopyWith<$Res>(_value.feeDetails, (value) {
      return _then(_value.copyWith(feeDetails: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TransferOptionImplCopyWith<$Res>
    implements $TransferOptionCopyWith<$Res> {
  factory _$$TransferOptionImplCopyWith(_$TransferOptionImpl value,
          $Res Function(_$TransferOptionImpl) then) =
      __$$TransferOptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      ProviderType providerType,
      Money initialSourceAmount,
      Money initialTargetAmount,
      Money chargedSourceAmount,
      Money chargedTargetAmount,
      double exchangeRate,
      FeeDetails feeDetails,
      String estimatedDeliveryInterval,
      String errorMessage,
      List<FeeChargingType> allowedFeeTypes,
      bool skipOptionUserView,
      ChannelType channel,
      EstimatedDeliveryType estimatedDeliveryType,
      String? providerLogo,
      DateTime? deliveryTime,
      bool? beneficiaryUpdateRequired});

  @override
  $FeeDetailsCopyWith<$Res> get feeDetails;
}

/// @nodoc
class __$$TransferOptionImplCopyWithImpl<$Res>
    extends _$TransferOptionCopyWithImpl<$Res, _$TransferOptionImpl>
    implements _$$TransferOptionImplCopyWith<$Res> {
  __$$TransferOptionImplCopyWithImpl(
      _$TransferOptionImpl _value, $Res Function(_$TransferOptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of TransferOption
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? providerType = null,
    Object? initialSourceAmount = null,
    Object? initialTargetAmount = null,
    Object? chargedSourceAmount = null,
    Object? chargedTargetAmount = null,
    Object? exchangeRate = null,
    Object? feeDetails = null,
    Object? estimatedDeliveryInterval = null,
    Object? errorMessage = null,
    Object? allowedFeeTypes = null,
    Object? skipOptionUserView = null,
    Object? channel = null,
    Object? estimatedDeliveryType = null,
    Object? providerLogo = freezed,
    Object? deliveryTime = freezed,
    Object? beneficiaryUpdateRequired = freezed,
  }) {
    return _then(_$TransferOptionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      providerType: null == providerType
          ? _value.providerType
          : providerType // ignore: cast_nullable_to_non_nullable
              as ProviderType,
      initialSourceAmount: null == initialSourceAmount
          ? _value.initialSourceAmount
          : initialSourceAmount // ignore: cast_nullable_to_non_nullable
              as Money,
      initialTargetAmount: null == initialTargetAmount
          ? _value.initialTargetAmount
          : initialTargetAmount // ignore: cast_nullable_to_non_nullable
              as Money,
      chargedSourceAmount: null == chargedSourceAmount
          ? _value.chargedSourceAmount
          : chargedSourceAmount // ignore: cast_nullable_to_non_nullable
              as Money,
      chargedTargetAmount: null == chargedTargetAmount
          ? _value.chargedTargetAmount
          : chargedTargetAmount // ignore: cast_nullable_to_non_nullable
              as Money,
      exchangeRate: null == exchangeRate
          ? _value.exchangeRate
          : exchangeRate // ignore: cast_nullable_to_non_nullable
              as double,
      feeDetails: null == feeDetails
          ? _value.feeDetails
          : feeDetails // ignore: cast_nullable_to_non_nullable
              as FeeDetails,
      estimatedDeliveryInterval: null == estimatedDeliveryInterval
          ? _value.estimatedDeliveryInterval
          : estimatedDeliveryInterval // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      allowedFeeTypes: null == allowedFeeTypes
          ? _value._allowedFeeTypes
          : allowedFeeTypes // ignore: cast_nullable_to_non_nullable
              as List<FeeChargingType>,
      skipOptionUserView: null == skipOptionUserView
          ? _value.skipOptionUserView
          : skipOptionUserView // ignore: cast_nullable_to_non_nullable
              as bool,
      channel: null == channel
          ? _value.channel
          : channel // ignore: cast_nullable_to_non_nullable
              as ChannelType,
      estimatedDeliveryType: null == estimatedDeliveryType
          ? _value.estimatedDeliveryType
          : estimatedDeliveryType // ignore: cast_nullable_to_non_nullable
              as EstimatedDeliveryType,
      providerLogo: freezed == providerLogo
          ? _value.providerLogo
          : providerLogo // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryTime: freezed == deliveryTime
          ? _value.deliveryTime
          : deliveryTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      beneficiaryUpdateRequired: freezed == beneficiaryUpdateRequired
          ? _value.beneficiaryUpdateRequired
          : beneficiaryUpdateRequired // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$TransferOptionImpl extends _TransferOption {
  const _$TransferOptionImpl(
      {required this.id,
      required this.providerType,
      required this.initialSourceAmount,
      required this.initialTargetAmount,
      required this.chargedSourceAmount,
      required this.chargedTargetAmount,
      required this.exchangeRate,
      required this.feeDetails,
      required this.estimatedDeliveryInterval,
      required this.errorMessage,
      required final List<FeeChargingType> allowedFeeTypes,
      this.skipOptionUserView = false,
      this.channel = ChannelType.unknown,
      this.estimatedDeliveryType = EstimatedDeliveryType.unknown,
      this.providerLogo,
      this.deliveryTime,
      this.beneficiaryUpdateRequired})
      : _allowedFeeTypes = allowedFeeTypes,
        super._();

  @override
  final String id;
  @override
  final ProviderType providerType;
  @override
  final Money initialSourceAmount;
  @override
  final Money initialTargetAmount;
  @override
  final Money chargedSourceAmount;
  @override
  final Money chargedTargetAmount;
  @override
  final double exchangeRate;
  @override
  final FeeDetails feeDetails;
  @override
  final String estimatedDeliveryInterval;

  /// Option validation error message (limit, insufficient balance etc.)
  @override
  final String errorMessage;

  /// Fee charging type customer can choose on rails selection (can be empty)
  final List<FeeChargingType> _allowedFeeTypes;

  /// Fee charging type customer can choose on rails selection (can be empty)
  @override
  List<FeeChargingType> get allowedFeeTypes {
    if (_allowedFeeTypes is EqualUnmodifiableListView) return _allowedFeeTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allowedFeeTypes);
  }

  @override
  @JsonKey()
  final bool skipOptionUserView;
  @override
  @JsonKey()
  final ChannelType channel;
  @override
  @JsonKey()
  final EstimatedDeliveryType estimatedDeliveryType;
  @override
  final String? providerLogo;

  /// It is null for [ProviderType.local] transfer options
  @override
  final DateTime? deliveryTime;
  @override
  final bool? beneficiaryUpdateRequired;

  @override
  String toString() {
    return 'TransferOption(id: $id, providerType: $providerType, initialSourceAmount: $initialSourceAmount, initialTargetAmount: $initialTargetAmount, chargedSourceAmount: $chargedSourceAmount, chargedTargetAmount: $chargedTargetAmount, exchangeRate: $exchangeRate, feeDetails: $feeDetails, estimatedDeliveryInterval: $estimatedDeliveryInterval, errorMessage: $errorMessage, allowedFeeTypes: $allowedFeeTypes, skipOptionUserView: $skipOptionUserView, channel: $channel, estimatedDeliveryType: $estimatedDeliveryType, providerLogo: $providerLogo, deliveryTime: $deliveryTime, beneficiaryUpdateRequired: $beneficiaryUpdateRequired)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransferOptionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.providerType, providerType) ||
                other.providerType == providerType) &&
            (identical(other.initialSourceAmount, initialSourceAmount) ||
                other.initialSourceAmount == initialSourceAmount) &&
            (identical(other.initialTargetAmount, initialTargetAmount) ||
                other.initialTargetAmount == initialTargetAmount) &&
            (identical(other.chargedSourceAmount, chargedSourceAmount) ||
                other.chargedSourceAmount == chargedSourceAmount) &&
            (identical(other.chargedTargetAmount, chargedTargetAmount) ||
                other.chargedTargetAmount == chargedTargetAmount) &&
            (identical(other.exchangeRate, exchangeRate) ||
                other.exchangeRate == exchangeRate) &&
            (identical(other.feeDetails, feeDetails) ||
                other.feeDetails == feeDetails) &&
            (identical(other.estimatedDeliveryInterval,
                    estimatedDeliveryInterval) ||
                other.estimatedDeliveryInterval == estimatedDeliveryInterval) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality()
                .equals(other._allowedFeeTypes, _allowedFeeTypes) &&
            (identical(other.skipOptionUserView, skipOptionUserView) ||
                other.skipOptionUserView == skipOptionUserView) &&
            (identical(other.channel, channel) || other.channel == channel) &&
            (identical(other.estimatedDeliveryType, estimatedDeliveryType) ||
                other.estimatedDeliveryType == estimatedDeliveryType) &&
            (identical(other.providerLogo, providerLogo) ||
                other.providerLogo == providerLogo) &&
            (identical(other.deliveryTime, deliveryTime) ||
                other.deliveryTime == deliveryTime) &&
            (identical(other.beneficiaryUpdateRequired,
                    beneficiaryUpdateRequired) ||
                other.beneficiaryUpdateRequired == beneficiaryUpdateRequired));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      providerType,
      initialSourceAmount,
      initialTargetAmount,
      chargedSourceAmount,
      chargedTargetAmount,
      exchangeRate,
      feeDetails,
      estimatedDeliveryInterval,
      errorMessage,
      const DeepCollectionEquality().hash(_allowedFeeTypes),
      skipOptionUserView,
      channel,
      estimatedDeliveryType,
      providerLogo,
      deliveryTime,
      beneficiaryUpdateRequired);

  /// Create a copy of TransferOption
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TransferOptionImplCopyWith<_$TransferOptionImpl> get copyWith =>
      __$$TransferOptionImplCopyWithImpl<_$TransferOptionImpl>(
          this, _$identity);
}

abstract class _TransferOption extends TransferOption {
  const factory _TransferOption(
      {required final String id,
      required final ProviderType providerType,
      required final Money initialSourceAmount,
      required final Money initialTargetAmount,
      required final Money chargedSourceAmount,
      required final Money chargedTargetAmount,
      required final double exchangeRate,
      required final FeeDetails feeDetails,
      required final String estimatedDeliveryInterval,
      required final String errorMessage,
      required final List<FeeChargingType> allowedFeeTypes,
      final bool skipOptionUserView,
      final ChannelType channel,
      final EstimatedDeliveryType estimatedDeliveryType,
      final String? providerLogo,
      final DateTime? deliveryTime,
      final bool? beneficiaryUpdateRequired}) = _$TransferOptionImpl;
  const _TransferOption._() : super._();

  @override
  String get id;
  @override
  ProviderType get providerType;
  @override
  Money get initialSourceAmount;
  @override
  Money get initialTargetAmount;
  @override
  Money get chargedSourceAmount;
  @override
  Money get chargedTargetAmount;
  @override
  double get exchangeRate;
  @override
  FeeDetails get feeDetails;
  @override
  String get estimatedDeliveryInterval;

  /// Option validation error message (limit, insufficient balance etc.)
  @override
  String get errorMessage;

  /// Fee charging type customer can choose on rails selection (can be empty)
  @override
  List<FeeChargingType> get allowedFeeTypes;
  @override
  bool get skipOptionUserView;
  @override
  ChannelType get channel;
  @override
  EstimatedDeliveryType get estimatedDeliveryType;
  @override
  String? get providerLogo;

  /// It is null for [ProviderType.local] transfer options
  @override
  DateTime? get deliveryTime;
  @override
  bool? get beneficiaryUpdateRequired;

  /// Create a copy of TransferOption
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TransferOptionImplCopyWith<_$TransferOptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
