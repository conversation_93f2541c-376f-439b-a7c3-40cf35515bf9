import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';

/// Tranfer channel type
/// Used for option (rail) [TransferOption] and transfer [PaymentTransfer] type
/// channels
enum ChannelType {
  wioToWio,
  local,
  swift,
  wise,
  unknown;

  String? get title => switch (this) {
        ChannelType.wioToWio => 'Wio-to-wio',
        ChannelType.local => 'Local',
        ChannelType.swift => 'Swift',
        ChannelType.wise => 'Wise',
        ChannelType.unknown => null,
      };

  String? get name => switch (this) {
        ChannelType.wioToWio => 'Wio',
        ChannelType.local => 'Local',
        ChannelType.swift => 'Swift',
        ChannelType.wise => 'Wise',
        ChannelType.unknown => null,
      };
}
