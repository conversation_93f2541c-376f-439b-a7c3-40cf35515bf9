import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_common_feature_payments_impl/src/domain/index.dart';

import '../factories.dart';
import '../feature_mocks.dart';

void main() {
  late TransferRepository transferRepository;
  late BeneficiaryCreationInteractor interactor;

  setUp(() {
    registerFallbackValue(TransferRequirementsFake());
    registerFallbackValue(InternationalBeneficiaryFake());
    registerFallbackValue(TransferRequiremnetRefreshDataEntryFake());

    transferRepository = MockTransferRepository();
    interactor = BeneficiaryCreationInteractorImpl(
      transferRepository: transferRepository,
    );
  });

  test('Should get transfer requirements', () async {
    // Arrange
    final optionId = randomString();
    when(
      () => transferRepository.getTransferRequirements(
        optionId: any(named: 'optionId'),
      ),
    ).thenAnswer((_) async => TransferRequirementsFake());

    // Act
    await interactor.getTransferRequirements(
      optionId: optionId,
    );

    // Assert
    verify(
      () => transferRepository.getTransferRequirements(
        optionId: optionId,
      ),
    ).calledOnce;
  });

  test(
    'Should get refreshed on change transfer requirements properly',
    () async {
      // Arrange
      final optionId = randomString();
      final fields = TransferRequiremnetRefreshDataEntryFactory.randList();
      when(
        () => transferRepository.getRefreshedOnChangeTransferRequirements(
          optionId: any(named: 'optionId'),
          fields: any(named: 'fields'),
        ),
      ).thenAnswer((_) async => TransferRequirementsFake());

      // Act
      await interactor.getRefreshedOnChangeTransferRequirements(
        optionId: optionId,
        fields: fields,
      );

      // Assert
      verify(
        () => transferRepository.getRefreshedOnChangeTransferRequirements(
          optionId: optionId,
          fields: fields,
        ),
      ).calledOnce;
    },
  );

  test('Should get beneficiary transfer requirements', () async {
    // Arrange
    final countryCode = randomString();
    final currencyCode = randomString();
    when(
      () => transferRepository.getBeneficiaryRequirements(
        countryCode: any(named: 'countryCode'),
        currencyCode: any(named: 'currencyCode'),
      ),
    ).thenAnswer((_) async => TransferRequirementsFake());

    // Act
    await interactor.getBeneficiaryRequirements(
      countryCode: countryCode,
      currencyCode: currencyCode,
    );

    // Assert
    verify(
      () => transferRepository.getBeneficiaryRequirements(
        countryCode: countryCode,
        currencyCode: currencyCode,
      ),
    ).calledOnce;
  });

  test(
    'Should get refreshed on change beneficiary transfer requirements properly',
    () async {
      // Arrange
      final countryCode = randomString();
      final currencyCode = randomString();
      final fields = TransferRequiremnetRefreshDataEntryFactory.randList();
      when(
        () => transferRepository.getRefreshedOnChangeBeneficiaryRequirements(
          countryCode: any(named: 'countryCode'),
          currencyCode: any(named: 'currencyCode'),
          fields: any(named: 'fields'),
        ),
      ).thenAnswer((_) async => TransferRequirementsFake());

      // Act
      await interactor.getRefreshedOnChangeBeneficiaryRequirements(
        countryCode: countryCode,
        currencyCode: currencyCode,
        fields: fields,
      );

      // Assert
      verify(
        () => transferRepository.getRefreshedOnChangeBeneficiaryRequirements(
          countryCode: countryCode,
          currencyCode: currencyCode,
          fields: fields,
        ),
      ).calledOnce;
    },
  );

  test(
    'Should get refreshed on change beneficiary transfer requirements properly',
    () async {
      // Arrange
      final beneficiaryId = randomString();
      final optionId = randomString();
      final fields = TransferRequiremnetRefreshDataEntryFactory.randList();
      when(
        () => transferRepository.getAdditionalBeneficiaryRefreshRequirements(
          beneficiaryId: any(named: 'beneficiaryId'),
          fields: any(named: 'fields'),
          optionId: any(named: 'optionId'),
        ),
      ).thenAnswer((_) async => TransferRequirementsFake());

      // Act
      await interactor.getAdditionalBeneficiaryRefreshRequirements(
        beneficiaryId: beneficiaryId,
        fields: fields,
        optionId: optionId,
      );

      // Assert
      verify(
        () => transferRepository.getAdditionalBeneficiaryRefreshRequirements(
          beneficiaryId: beneficiaryId,
          fields: fields,
          optionId: optionId,
        ),
      ).calledOnce;
    },
  );

  test(
    'Should get additional beneficiary transfer requirements',
    () async {
      // Arrange
      final beneficiaryId = randomString();
      final optionId = randomString();
      when(
        () => transferRepository.getAdditionalBeneficiaryRequirements(
          beneficiaryId: any(named: 'beneficiaryId'),
          optionId: any(named: 'optionId'),
        ),
      ).thenAnswer((_) async => TransferRequirementsFake());

      // Act
      await interactor.getAdditionalBeneficiaryRequirements(
        beneficiaryId: beneficiaryId,
        optionId: optionId,
      );

      // Assert
      verify(
        () => transferRepository.getAdditionalBeneficiaryRequirements(
          beneficiaryId: beneficiaryId,
          optionId: optionId,
        ),
      ).calledOnce;
    },
  );
}
