// ignore_for_file: type=lint

import 'package:json_annotation/json_annotation.dart';
import 'package:json_annotation/json_annotation.dart' as json;
import 'package:collection/collection.dart';
import 'dart:convert';

import 'transfer_options.enums.swagger.dart' as enums;
export 'transfer_options.enums.swagger.dart';

part 'transfer_options.swagger.g.dart';

@JsonSerializable(explicitToJson: true)
class TransferInfoFeeDetails {
  const TransferInfoFeeDetails({
    this.vat,
    this.transferFees,
    this.possibleFees,
    this.total,
    this.freeChargeFeeBalance,
    this.freeChargeFeeTotal,
    this.currency,
    this.selectedChargingType,
  });

  factory TransferInfoFeeDetails.fromJson(Map<String, dynamic> json) =>
      _$TransferInfoFeeDetailsFromJson(json);

  static const toJsonFactory = _$TransferInfoFeeDetailsToJson;
  Map<String, dynamic> toJson() => _$TransferInfoFeeDetailsToJson(this);

  @JsonKey(name: 'vat', includeIfNull: false)
  final double? vat;
  @JsonKey(
      name: 'transferFees',
      includeIfNull: false,
      defaultValue: <TransferFeeInfo>[])
  final List<TransferFeeInfo>? transferFees;
  @JsonKey(
      name: 'possibleFees',
      includeIfNull: false,
      defaultValue: <PossibleFeeInfo>[])
  final List<PossibleFeeInfo>? possibleFees;
  @JsonKey(name: 'total', includeIfNull: false)
  final double? total;
  @JsonKey(name: 'freeChargeFeeBalance', includeIfNull: false)
  final int? freeChargeFeeBalance;
  @JsonKey(name: 'freeChargeFeeTotal', includeIfNull: false)
  final int? freeChargeFeeTotal;
  @JsonKey(name: 'currency', includeIfNull: false)
  final String? currency;
  @JsonKey(
    name: 'selectedChargingType',
    includeIfNull: false,
    toJson: feeChargingTypeDtoNullableToJson,
    fromJson: feeChargingTypeDtoNullableFromJson,
  )
  final enums.FeeChargingTypeDto? selectedChargingType;
  static const fromJsonFactory = _$TransferInfoFeeDetailsFromJson;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other is TransferInfoFeeDetails &&
            (identical(other.vat, vat) ||
                const DeepCollectionEquality().equals(other.vat, vat)) &&
            (identical(other.transferFees, transferFees) ||
                const DeepCollectionEquality()
                    .equals(other.transferFees, transferFees)) &&
            (identical(other.possibleFees, possibleFees) ||
                const DeepCollectionEquality()
                    .equals(other.possibleFees, possibleFees)) &&
            (identical(other.total, total) ||
                const DeepCollectionEquality().equals(other.total, total)) &&
            (identical(other.freeChargeFeeBalance, freeChargeFeeBalance) ||
                const DeepCollectionEquality().equals(
                    other.freeChargeFeeBalance, freeChargeFeeBalance)) &&
            (identical(other.freeChargeFeeTotal, freeChargeFeeTotal) ||
                const DeepCollectionEquality()
                    .equals(other.freeChargeFeeTotal, freeChargeFeeTotal)) &&
            (identical(other.currency, currency) ||
                const DeepCollectionEquality()
                    .equals(other.currency, currency)) &&
            (identical(other.selectedChargingType, selectedChargingType) ||
                const DeepCollectionEquality()
                    .equals(other.selectedChargingType, selectedChargingType)));
  }

  @override
  String toString() => jsonEncode(this);

  @override
  int get hashCode =>
      const DeepCollectionEquality().hash(vat) ^
      const DeepCollectionEquality().hash(transferFees) ^
      const DeepCollectionEquality().hash(possibleFees) ^
      const DeepCollectionEquality().hash(total) ^
      const DeepCollectionEquality().hash(freeChargeFeeBalance) ^
      const DeepCollectionEquality().hash(freeChargeFeeTotal) ^
      const DeepCollectionEquality().hash(currency) ^
      const DeepCollectionEquality().hash(selectedChargingType) ^
      runtimeType.hashCode;
}

extension $TransferInfoFeeDetailsExtension on TransferInfoFeeDetails {
  TransferInfoFeeDetails copyWith(
      {double? vat,
      List<TransferFeeInfo>? transferFees,
      List<PossibleFeeInfo>? possibleFees,
      double? total,
      int? freeChargeFeeBalance,
      int? freeChargeFeeTotal,
      String? currency,
      enums.FeeChargingTypeDto? selectedChargingType}) {
    return TransferInfoFeeDetails(
        vat: vat ?? this.vat,
        transferFees: transferFees ?? this.transferFees,
        possibleFees: possibleFees ?? this.possibleFees,
        total: total ?? this.total,
        freeChargeFeeBalance: freeChargeFeeBalance ?? this.freeChargeFeeBalance,
        freeChargeFeeTotal: freeChargeFeeTotal ?? this.freeChargeFeeTotal,
        currency: currency ?? this.currency,
        selectedChargingType:
            selectedChargingType ?? this.selectedChargingType);
  }

  TransferInfoFeeDetails copyWithWrapped(
      {Wrapped<double?>? vat,
      Wrapped<List<TransferFeeInfo>?>? transferFees,
      Wrapped<List<PossibleFeeInfo>?>? possibleFees,
      Wrapped<double?>? total,
      Wrapped<int?>? freeChargeFeeBalance,
      Wrapped<int?>? freeChargeFeeTotal,
      Wrapped<String?>? currency,
      Wrapped<enums.FeeChargingTypeDto?>? selectedChargingType}) {
    return TransferInfoFeeDetails(
        vat: (vat != null ? vat.value : this.vat),
        transferFees:
            (transferFees != null ? transferFees.value : this.transferFees),
        possibleFees:
            (possibleFees != null ? possibleFees.value : this.possibleFees),
        total: (total != null ? total.value : this.total),
        freeChargeFeeBalance: (freeChargeFeeBalance != null
            ? freeChargeFeeBalance.value
            : this.freeChargeFeeBalance),
        freeChargeFeeTotal: (freeChargeFeeTotal != null
            ? freeChargeFeeTotal.value
            : this.freeChargeFeeTotal),
        currency: (currency != null ? currency.value : this.currency),
        selectedChargingType: (selectedChargingType != null
            ? selectedChargingType.value
            : this.selectedChargingType));
  }
}

@JsonSerializable(explicitToJson: true)
class TransferFeeInfo {
  const TransferFeeInfo({
    this.feeType,
    this.description,
    this.amount,
    this.currency,
  });

  factory TransferFeeInfo.fromJson(Map<String, dynamic> json) =>
      _$TransferFeeInfoFromJson(json);

  static const toJsonFactory = _$TransferFeeInfoToJson;
  Map<String, dynamic> toJson() => _$TransferFeeInfoToJson(this);

  @JsonKey(
    name: 'feeType',
    includeIfNull: false,
    toJson: transferFeeInfoFeeTypeNullableToJson,
    fromJson: transferFeeInfoFeeTypeNullableFromJson,
  )
  final enums.TransferFeeInfoFeeType? feeType;
  @JsonKey(name: 'description', includeIfNull: false)
  final String? description;
  @JsonKey(name: 'amount', includeIfNull: false)
  final double? amount;
  @JsonKey(name: 'currency', includeIfNull: false)
  final String? currency;
  static const fromJsonFactory = _$TransferFeeInfoFromJson;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other is TransferFeeInfo &&
            (identical(other.feeType, feeType) ||
                const DeepCollectionEquality()
                    .equals(other.feeType, feeType)) &&
            (identical(other.description, description) ||
                const DeepCollectionEquality()
                    .equals(other.description, description)) &&
            (identical(other.amount, amount) ||
                const DeepCollectionEquality().equals(other.amount, amount)) &&
            (identical(other.currency, currency) ||
                const DeepCollectionEquality()
                    .equals(other.currency, currency)));
  }

  @override
  String toString() => jsonEncode(this);

  @override
  int get hashCode =>
      const DeepCollectionEquality().hash(feeType) ^
      const DeepCollectionEquality().hash(description) ^
      const DeepCollectionEquality().hash(amount) ^
      const DeepCollectionEquality().hash(currency) ^
      runtimeType.hashCode;
}

extension $TransferFeeInfoExtension on TransferFeeInfo {
  TransferFeeInfo copyWith(
      {enums.TransferFeeInfoFeeType? feeType,
      String? description,
      double? amount,
      String? currency}) {
    return TransferFeeInfo(
        feeType: feeType ?? this.feeType,
        description: description ?? this.description,
        amount: amount ?? this.amount,
        currency: currency ?? this.currency);
  }

  TransferFeeInfo copyWithWrapped(
      {Wrapped<enums.TransferFeeInfoFeeType?>? feeType,
      Wrapped<String?>? description,
      Wrapped<double?>? amount,
      Wrapped<String?>? currency}) {
    return TransferFeeInfo(
        feeType: (feeType != null ? feeType.value : this.feeType),
        description:
            (description != null ? description.value : this.description),
        amount: (amount != null ? amount.value : this.amount),
        currency: (currency != null ? currency.value : this.currency));
  }
}

@JsonSerializable(explicitToJson: true)
class PossibleFeeInfo {
  const PossibleFeeInfo({
    this.feeCharging,
    this.transferFees,
  });

  factory PossibleFeeInfo.fromJson(Map<String, dynamic> json) =>
      _$PossibleFeeInfoFromJson(json);

  static const toJsonFactory = _$PossibleFeeInfoToJson;
  Map<String, dynamic> toJson() => _$PossibleFeeInfoToJson(this);

  @JsonKey(
    name: 'feeCharging',
    includeIfNull: false,
    toJson: feeChargingTypeDtoNullableToJson,
    fromJson: feeChargingTypeDtoNullableFromJson,
  )
  final enums.FeeChargingTypeDto? feeCharging;
  @JsonKey(
      name: 'transferFees',
      includeIfNull: false,
      defaultValue: <TransferFeeInfo>[])
  final List<TransferFeeInfo>? transferFees;
  static const fromJsonFactory = _$PossibleFeeInfoFromJson;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other is PossibleFeeInfo &&
            (identical(other.feeCharging, feeCharging) ||
                const DeepCollectionEquality()
                    .equals(other.feeCharging, feeCharging)) &&
            (identical(other.transferFees, transferFees) ||
                const DeepCollectionEquality()
                    .equals(other.transferFees, transferFees)));
  }

  @override
  String toString() => jsonEncode(this);

  @override
  int get hashCode =>
      const DeepCollectionEquality().hash(feeCharging) ^
      const DeepCollectionEquality().hash(transferFees) ^
      runtimeType.hashCode;
}

extension $PossibleFeeInfoExtension on PossibleFeeInfo {
  PossibleFeeInfo copyWith(
      {enums.FeeChargingTypeDto? feeCharging,
      List<TransferFeeInfo>? transferFees}) {
    return PossibleFeeInfo(
        feeCharging: feeCharging ?? this.feeCharging,
        transferFees: transferFees ?? this.transferFees);
  }

  PossibleFeeInfo copyWithWrapped(
      {Wrapped<enums.FeeChargingTypeDto?>? feeCharging,
      Wrapped<List<TransferFeeInfo>?>? transferFees}) {
    return PossibleFeeInfo(
        feeCharging:
            (feeCharging != null ? feeCharging.value : this.feeCharging),
        transferFees:
            (transferFees != null ? transferFees.value : this.transferFees));
  }
}

@JsonSerializable(explicitToJson: true)
class TransferOptionsRequest {
  const TransferOptionsRequest({
    required this.account,
    required this.country,
    required this.currency,
    required this.amount,
    this.feesChargingType,
    this.beneficiaryId,
  });

  factory TransferOptionsRequest.fromJson(Map<String, dynamic> json) =>
      _$TransferOptionsRequestFromJson(json);

  static const toJsonFactory = _$TransferOptionsRequestToJson;
  Map<String, dynamic> toJson() => _$TransferOptionsRequestToJson(this);

  @JsonKey(name: 'account', includeIfNull: false)
  final String account;
  @JsonKey(name: 'country', includeIfNull: false)
  final String country;
  @JsonKey(name: 'currency', includeIfNull: false)
  final String currency;
  @JsonKey(name: 'amount', includeIfNull: false)
  final double amount;
  @JsonKey(
    name: 'feesChargingType',
    includeIfNull: false,
    toJson: feeChargingTypeDtoNullableToJson,
    fromJson: feeChargingTypeDtoNullableFromJson,
  )
  final enums.FeeChargingTypeDto? feesChargingType;
  @JsonKey(name: 'beneficiaryId', includeIfNull: false)
  final String? beneficiaryId;
  static const fromJsonFactory = _$TransferOptionsRequestFromJson;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other is TransferOptionsRequest &&
            (identical(other.account, account) ||
                const DeepCollectionEquality()
                    .equals(other.account, account)) &&
            (identical(other.country, country) ||
                const DeepCollectionEquality()
                    .equals(other.country, country)) &&
            (identical(other.currency, currency) ||
                const DeepCollectionEquality()
                    .equals(other.currency, currency)) &&
            (identical(other.amount, amount) ||
                const DeepCollectionEquality().equals(other.amount, amount)) &&
            (identical(other.feesChargingType, feesChargingType) ||
                const DeepCollectionEquality()
                    .equals(other.feesChargingType, feesChargingType)) &&
            (identical(other.beneficiaryId, beneficiaryId) ||
                const DeepCollectionEquality()
                    .equals(other.beneficiaryId, beneficiaryId)));
  }

  @override
  String toString() => jsonEncode(this);

  @override
  int get hashCode =>
      const DeepCollectionEquality().hash(account) ^
      const DeepCollectionEquality().hash(country) ^
      const DeepCollectionEquality().hash(currency) ^
      const DeepCollectionEquality().hash(amount) ^
      const DeepCollectionEquality().hash(feesChargingType) ^
      const DeepCollectionEquality().hash(beneficiaryId) ^
      runtimeType.hashCode;
}

extension $TransferOptionsRequestExtension on TransferOptionsRequest {
  TransferOptionsRequest copyWith(
      {String? account,
      String? country,
      String? currency,
      double? amount,
      enums.FeeChargingTypeDto? feesChargingType,
      String? beneficiaryId}) {
    return TransferOptionsRequest(
        account: account ?? this.account,
        country: country ?? this.country,
        currency: currency ?? this.currency,
        amount: amount ?? this.amount,
        feesChargingType: feesChargingType ?? this.feesChargingType,
        beneficiaryId: beneficiaryId ?? this.beneficiaryId);
  }

  TransferOptionsRequest copyWithWrapped(
      {Wrapped<String>? account,
      Wrapped<String>? country,
      Wrapped<String>? currency,
      Wrapped<double>? amount,
      Wrapped<enums.FeeChargingTypeDto?>? feesChargingType,
      Wrapped<String?>? beneficiaryId}) {
    return TransferOptionsRequest(
        account: (account != null ? account.value : this.account),
        country: (country != null ? country.value : this.country),
        currency: (currency != null ? currency.value : this.currency),
        amount: (amount != null ? amount.value : this.amount),
        feesChargingType: (feesChargingType != null
            ? feesChargingType.value
            : this.feesChargingType),
        beneficiaryId:
            (beneficiaryId != null ? beneficiaryId.value : this.beneficiaryId));
  }
}

@JsonSerializable(explicitToJson: true)
class TransferInfoAmount {
  const TransferInfoAmount({
    this.sourceAmount,
    this.targetAmount,
  });

  factory TransferInfoAmount.fromJson(Map<String, dynamic> json) =>
      _$TransferInfoAmountFromJson(json);

  static const toJsonFactory = _$TransferInfoAmountToJson;
  Map<String, dynamic> toJson() => _$TransferInfoAmountToJson(this);

  @JsonKey(name: 'sourceAmount', includeIfNull: false)
  final double? sourceAmount;
  @JsonKey(name: 'targetAmount', includeIfNull: false)
  final double? targetAmount;
  static const fromJsonFactory = _$TransferInfoAmountFromJson;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other is TransferInfoAmount &&
            (identical(other.sourceAmount, sourceAmount) ||
                const DeepCollectionEquality()
                    .equals(other.sourceAmount, sourceAmount)) &&
            (identical(other.targetAmount, targetAmount) ||
                const DeepCollectionEquality()
                    .equals(other.targetAmount, targetAmount)));
  }

  @override
  String toString() => jsonEncode(this);

  @override
  int get hashCode =>
      const DeepCollectionEquality().hash(sourceAmount) ^
      const DeepCollectionEquality().hash(targetAmount) ^
      runtimeType.hashCode;
}

extension $TransferInfoAmountExtension on TransferInfoAmount {
  TransferInfoAmount copyWith({double? sourceAmount, double? targetAmount}) {
    return TransferInfoAmount(
        sourceAmount: sourceAmount ?? this.sourceAmount,
        targetAmount: targetAmount ?? this.targetAmount);
  }

  TransferInfoAmount copyWithWrapped(
      {Wrapped<double?>? sourceAmount, Wrapped<double?>? targetAmount}) {
    return TransferInfoAmount(
        sourceAmount:
            (sourceAmount != null ? sourceAmount.value : this.sourceAmount),
        targetAmount:
            (targetAmount != null ? targetAmount.value : this.targetAmount));
  }
}

@JsonSerializable(explicitToJson: true)
class TransferOptionResponse {
  const TransferOptionResponse({
    this.id,
    this.providerType,
    this.providerLogo,
    this.sourceCurrency,
    this.targetCurrency,
    this.exchangeRate,
    this.feeDetails,
    this.deliveryTime,
    this.estimatedDeliveryInterval,
    this.errorMessage,
    this.initialAmount,
    this.chargedAmount,
    this.allowedFeeCharging,
    this.errorCode,
    this.preValidationErrorMessage,
    this.quoteTTLSeconds,
    this.beneficiaryUpdateRequired,
    this.skipOptionUserView,
    this.channel,
    this.estimatedDeliveryType,
  });

  factory TransferOptionResponse.fromJson(Map<String, dynamic> json) =>
      _$TransferOptionResponseFromJson(json);

  static const toJsonFactory = _$TransferOptionResponseToJson;
  Map<String, dynamic> toJson() => _$TransferOptionResponseToJson(this);

  @JsonKey(name: 'id', includeIfNull: false)
  final String? id;
  @JsonKey(
    name: 'providerType',
    includeIfNull: false,
    toJson: transferOptionResponseProviderTypeNullableToJson,
    fromJson: transferOptionResponseProviderTypeNullableFromJson,
  )
  final enums.TransferOptionResponseProviderType? providerType;
  @JsonKey(name: 'providerLogo', includeIfNull: false)
  final String? providerLogo;
  @JsonKey(name: 'sourceCurrency', includeIfNull: false)
  final String? sourceCurrency;
  @JsonKey(name: 'targetCurrency', includeIfNull: false)
  final String? targetCurrency;
  @JsonKey(name: 'exchangeRate', includeIfNull: false)
  final double? exchangeRate;
  @JsonKey(name: 'feeDetails', includeIfNull: false)
  final TransferInfoFeeDetails? feeDetails;
  @JsonKey(name: 'deliveryTime', includeIfNull: false)
  final DateTime? deliveryTime;
  @JsonKey(name: 'estimatedDeliveryInterval', includeIfNull: false)
  final String? estimatedDeliveryInterval;
  @JsonKey(name: 'errorMessage', includeIfNull: false)
  final String? errorMessage;
  @JsonKey(name: 'initialAmount', includeIfNull: false)
  final TransferInfoAmount? initialAmount;
  @JsonKey(name: 'chargedAmount', includeIfNull: false)
  final TransferInfoAmount? chargedAmount;
  @JsonKey(
    name: 'allowedFeeCharging',
    includeIfNull: false,
    toJson: feeChargingTypeDtoListToJson,
    fromJson: feeChargingTypeDtoListFromJson,
  )
  final List<enums.FeeChargingTypeDto>? allowedFeeCharging;
  @JsonKey(name: 'errorCode', includeIfNull: false)
  final String? errorCode;
  @JsonKey(name: 'preValidationErrorMessage', includeIfNull: false)
  final String? preValidationErrorMessage;
  @JsonKey(name: 'quoteTTLSeconds', includeIfNull: false)
  final int? quoteTTLSeconds;
  @JsonKey(name: 'beneficiaryUpdateRequired', includeIfNull: false)
  final bool? beneficiaryUpdateRequired;
  @JsonKey(name: 'skipOptionUserView', includeIfNull: false)
  final bool? skipOptionUserView;
  @JsonKey(
    name: 'channel',
    includeIfNull: false,
    toJson: transferOptionResponseChannelNullableToJson,
    fromJson: transferOptionResponseChannelNullableFromJson,
  )
  final enums.TransferOptionResponseChannel? channel;
  @JsonKey(
    name: 'estimatedDeliveryType',
    includeIfNull: false,
    toJson: transferOptionResponseEstimatedDeliveryTypeNullableToJson,
    fromJson: transferOptionResponseEstimatedDeliveryTypeNullableFromJson,
  )
  final enums.TransferOptionResponseEstimatedDeliveryType?
      estimatedDeliveryType;
  static const fromJsonFactory = _$TransferOptionResponseFromJson;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other is TransferOptionResponse &&
            (identical(other.id, id) ||
                const DeepCollectionEquality().equals(other.id, id)) &&
            (identical(other.providerType, providerType) ||
                const DeepCollectionEquality()
                    .equals(other.providerType, providerType)) &&
            (identical(other.providerLogo, providerLogo) ||
                const DeepCollectionEquality()
                    .equals(other.providerLogo, providerLogo)) &&
            (identical(other.sourceCurrency, sourceCurrency) ||
                const DeepCollectionEquality()
                    .equals(other.sourceCurrency, sourceCurrency)) &&
            (identical(other.targetCurrency, targetCurrency) ||
                const DeepCollectionEquality()
                    .equals(other.targetCurrency, targetCurrency)) &&
            (identical(other.exchangeRate, exchangeRate) ||
                const DeepCollectionEquality()
                    .equals(other.exchangeRate, exchangeRate)) &&
            (identical(other.feeDetails, feeDetails) ||
                const DeepCollectionEquality()
                    .equals(other.feeDetails, feeDetails)) &&
            (identical(other.deliveryTime, deliveryTime) ||
                const DeepCollectionEquality()
                    .equals(other.deliveryTime, deliveryTime)) &&
            (identical(other.estimatedDeliveryInterval, estimatedDeliveryInterval) ||
                const DeepCollectionEquality().equals(
                    other.estimatedDeliveryInterval,
                    estimatedDeliveryInterval)) &&
            (identical(other.errorMessage, errorMessage) ||
                const DeepCollectionEquality()
                    .equals(other.errorMessage, errorMessage)) &&
            (identical(other.initialAmount, initialAmount) ||
                const DeepCollectionEquality()
                    .equals(other.initialAmount, initialAmount)) &&
            (identical(other.chargedAmount, chargedAmount) ||
                const DeepCollectionEquality()
                    .equals(other.chargedAmount, chargedAmount)) &&
            (identical(other.allowedFeeCharging, allowedFeeCharging) ||
                const DeepCollectionEquality()
                    .equals(other.allowedFeeCharging, allowedFeeCharging)) &&
            (identical(other.errorCode, errorCode) ||
                const DeepCollectionEquality()
                    .equals(other.errorCode, errorCode)) &&
            (identical(other.preValidationErrorMessage, preValidationErrorMessage) ||
                const DeepCollectionEquality().equals(
                    other.preValidationErrorMessage,
                    preValidationErrorMessage)) &&
            (identical(other.quoteTTLSeconds, quoteTTLSeconds) ||
                const DeepCollectionEquality()
                    .equals(other.quoteTTLSeconds, quoteTTLSeconds)) &&
            (identical(other.beneficiaryUpdateRequired, beneficiaryUpdateRequired) ||
                const DeepCollectionEquality().equals(
                    other.beneficiaryUpdateRequired,
                    beneficiaryUpdateRequired)) &&
            (identical(other.skipOptionUserView, skipOptionUserView) ||
                const DeepCollectionEquality().equals(other.skipOptionUserView, skipOptionUserView)) &&
            (identical(other.channel, channel) || const DeepCollectionEquality().equals(other.channel, channel)) &&
            (identical(other.estimatedDeliveryType, estimatedDeliveryType) || const DeepCollectionEquality().equals(other.estimatedDeliveryType, estimatedDeliveryType)));
  }

  @override
  String toString() => jsonEncode(this);

  @override
  int get hashCode =>
      const DeepCollectionEquality().hash(id) ^
      const DeepCollectionEquality().hash(providerType) ^
      const DeepCollectionEquality().hash(providerLogo) ^
      const DeepCollectionEquality().hash(sourceCurrency) ^
      const DeepCollectionEquality().hash(targetCurrency) ^
      const DeepCollectionEquality().hash(exchangeRate) ^
      const DeepCollectionEquality().hash(feeDetails) ^
      const DeepCollectionEquality().hash(deliveryTime) ^
      const DeepCollectionEquality().hash(estimatedDeliveryInterval) ^
      const DeepCollectionEquality().hash(errorMessage) ^
      const DeepCollectionEquality().hash(initialAmount) ^
      const DeepCollectionEquality().hash(chargedAmount) ^
      const DeepCollectionEquality().hash(allowedFeeCharging) ^
      const DeepCollectionEquality().hash(errorCode) ^
      const DeepCollectionEquality().hash(preValidationErrorMessage) ^
      const DeepCollectionEquality().hash(quoteTTLSeconds) ^
      const DeepCollectionEquality().hash(beneficiaryUpdateRequired) ^
      const DeepCollectionEquality().hash(skipOptionUserView) ^
      const DeepCollectionEquality().hash(channel) ^
      const DeepCollectionEquality().hash(estimatedDeliveryType) ^
      runtimeType.hashCode;
}

extension $TransferOptionResponseExtension on TransferOptionResponse {
  TransferOptionResponse copyWith(
      {String? id,
      enums.TransferOptionResponseProviderType? providerType,
      String? providerLogo,
      String? sourceCurrency,
      String? targetCurrency,
      double? exchangeRate,
      TransferInfoFeeDetails? feeDetails,
      DateTime? deliveryTime,
      String? estimatedDeliveryInterval,
      String? errorMessage,
      TransferInfoAmount? initialAmount,
      TransferInfoAmount? chargedAmount,
      List<enums.FeeChargingTypeDto>? allowedFeeCharging,
      String? errorCode,
      String? preValidationErrorMessage,
      int? quoteTTLSeconds,
      bool? beneficiaryUpdateRequired,
      bool? skipOptionUserView,
      enums.TransferOptionResponseChannel? channel,
      enums.TransferOptionResponseEstimatedDeliveryType?
          estimatedDeliveryType}) {
    return TransferOptionResponse(
        id: id ?? this.id,
        providerType: providerType ?? this.providerType,
        providerLogo: providerLogo ?? this.providerLogo,
        sourceCurrency: sourceCurrency ?? this.sourceCurrency,
        targetCurrency: targetCurrency ?? this.targetCurrency,
        exchangeRate: exchangeRate ?? this.exchangeRate,
        feeDetails: feeDetails ?? this.feeDetails,
        deliveryTime: deliveryTime ?? this.deliveryTime,
        estimatedDeliveryInterval:
            estimatedDeliveryInterval ?? this.estimatedDeliveryInterval,
        errorMessage: errorMessage ?? this.errorMessage,
        initialAmount: initialAmount ?? this.initialAmount,
        chargedAmount: chargedAmount ?? this.chargedAmount,
        allowedFeeCharging: allowedFeeCharging ?? this.allowedFeeCharging,
        errorCode: errorCode ?? this.errorCode,
        preValidationErrorMessage:
            preValidationErrorMessage ?? this.preValidationErrorMessage,
        quoteTTLSeconds: quoteTTLSeconds ?? this.quoteTTLSeconds,
        beneficiaryUpdateRequired:
            beneficiaryUpdateRequired ?? this.beneficiaryUpdateRequired,
        skipOptionUserView: skipOptionUserView ?? this.skipOptionUserView,
        channel: channel ?? this.channel,
        estimatedDeliveryType:
            estimatedDeliveryType ?? this.estimatedDeliveryType);
  }

  TransferOptionResponse copyWithWrapped(
      {Wrapped<String?>? id,
      Wrapped<enums.TransferOptionResponseProviderType?>? providerType,
      Wrapped<String?>? providerLogo,
      Wrapped<String?>? sourceCurrency,
      Wrapped<String?>? targetCurrency,
      Wrapped<double?>? exchangeRate,
      Wrapped<TransferInfoFeeDetails?>? feeDetails,
      Wrapped<DateTime?>? deliveryTime,
      Wrapped<String?>? estimatedDeliveryInterval,
      Wrapped<String?>? errorMessage,
      Wrapped<TransferInfoAmount?>? initialAmount,
      Wrapped<TransferInfoAmount?>? chargedAmount,
      Wrapped<List<enums.FeeChargingTypeDto>?>? allowedFeeCharging,
      Wrapped<String?>? errorCode,
      Wrapped<String?>? preValidationErrorMessage,
      Wrapped<int?>? quoteTTLSeconds,
      Wrapped<bool?>? beneficiaryUpdateRequired,
      Wrapped<bool?>? skipOptionUserView,
      Wrapped<enums.TransferOptionResponseChannel?>? channel,
      Wrapped<enums.TransferOptionResponseEstimatedDeliveryType?>?
          estimatedDeliveryType}) {
    return TransferOptionResponse(
        id: (id != null ? id.value : this.id),
        providerType:
            (providerType != null ? providerType.value : this.providerType),
        providerLogo:
            (providerLogo != null ? providerLogo.value : this.providerLogo),
        sourceCurrency: (sourceCurrency != null
            ? sourceCurrency.value
            : this.sourceCurrency),
        targetCurrency: (targetCurrency != null
            ? targetCurrency.value
            : this.targetCurrency),
        exchangeRate:
            (exchangeRate != null ? exchangeRate.value : this.exchangeRate),
        feeDetails: (feeDetails != null ? feeDetails.value : this.feeDetails),
        deliveryTime:
            (deliveryTime != null ? deliveryTime.value : this.deliveryTime),
        estimatedDeliveryInterval: (estimatedDeliveryInterval != null
            ? estimatedDeliveryInterval.value
            : this.estimatedDeliveryInterval),
        errorMessage:
            (errorMessage != null ? errorMessage.value : this.errorMessage),
        initialAmount:
            (initialAmount != null ? initialAmount.value : this.initialAmount),
        chargedAmount:
            (chargedAmount != null ? chargedAmount.value : this.chargedAmount),
        allowedFeeCharging: (allowedFeeCharging != null
            ? allowedFeeCharging.value
            : this.allowedFeeCharging),
        errorCode: (errorCode != null ? errorCode.value : this.errorCode),
        preValidationErrorMessage: (preValidationErrorMessage != null
            ? preValidationErrorMessage.value
            : this.preValidationErrorMessage),
        quoteTTLSeconds: (quoteTTLSeconds != null
            ? quoteTTLSeconds.value
            : this.quoteTTLSeconds),
        beneficiaryUpdateRequired: (beneficiaryUpdateRequired != null
            ? beneficiaryUpdateRequired.value
            : this.beneficiaryUpdateRequired),
        skipOptionUserView: (skipOptionUserView != null
            ? skipOptionUserView.value
            : this.skipOptionUserView),
        channel: (channel != null ? channel.value : this.channel),
        estimatedDeliveryType: (estimatedDeliveryType != null
            ? estimatedDeliveryType.value
            : this.estimatedDeliveryType));
  }
}

@JsonSerializable(explicitToJson: true)
class UpdateTransferFeeChargingTypeRequest {
  const UpdateTransferFeeChargingTypeRequest({
    this.feesChargingType,
  });

  factory UpdateTransferFeeChargingTypeRequest.fromJson(
          Map<String, dynamic> json) =>
      _$UpdateTransferFeeChargingTypeRequestFromJson(json);

  static const toJsonFactory = _$UpdateTransferFeeChargingTypeRequestToJson;
  Map<String, dynamic> toJson() =>
      _$UpdateTransferFeeChargingTypeRequestToJson(this);

  @JsonKey(
    name: 'feesChargingType',
    includeIfNull: false,
    toJson: feeChargingTypeDtoNullableToJson,
    fromJson: feeChargingTypeDtoNullableFromJson,
  )
  final enums.FeeChargingTypeDto? feesChargingType;
  static const fromJsonFactory = _$UpdateTransferFeeChargingTypeRequestFromJson;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other is UpdateTransferFeeChargingTypeRequest &&
            (identical(other.feesChargingType, feesChargingType) ||
                const DeepCollectionEquality()
                    .equals(other.feesChargingType, feesChargingType)));
  }

  @override
  String toString() => jsonEncode(this);

  @override
  int get hashCode =>
      const DeepCollectionEquality().hash(feesChargingType) ^
      runtimeType.hashCode;
}

extension $UpdateTransferFeeChargingTypeRequestExtension
    on UpdateTransferFeeChargingTypeRequest {
  UpdateTransferFeeChargingTypeRequest copyWith(
      {enums.FeeChargingTypeDto? feesChargingType}) {
    return UpdateTransferFeeChargingTypeRequest(
        feesChargingType: feesChargingType ?? this.feesChargingType);
  }

  UpdateTransferFeeChargingTypeRequest copyWithWrapped(
      {Wrapped<enums.FeeChargingTypeDto?>? feesChargingType}) {
    return UpdateTransferFeeChargingTypeRequest(
        feesChargingType: (feesChargingType != null
            ? feesChargingType.value
            : this.feesChargingType));
  }
}

@JsonSerializable(explicitToJson: true)
class TransferOptionRequestV2 {
  const TransferOptionRequestV2({
    required this.account,
    required this.country,
    this.targetCurrency,
    this.targetAmount,
    this.sourceCurrency,
    this.sourceAmount,
    this.feesChargingType,
    this.beneficiaryId,
  });

  factory TransferOptionRequestV2.fromJson(Map<String, dynamic> json) =>
      _$TransferOptionRequestV2FromJson(json);

  static const toJsonFactory = _$TransferOptionRequestV2ToJson;
  Map<String, dynamic> toJson() => _$TransferOptionRequestV2ToJson(this);

  @JsonKey(name: 'account', includeIfNull: false)
  final String account;
  @JsonKey(name: 'country', includeIfNull: false)
  final String country;
  @JsonKey(name: 'targetCurrency', includeIfNull: false)
  final String? targetCurrency;
  @JsonKey(name: 'targetAmount', includeIfNull: false)
  final double? targetAmount;
  @JsonKey(name: 'sourceCurrency', includeIfNull: false)
  final String? sourceCurrency;
  @JsonKey(name: 'sourceAmount', includeIfNull: false)
  final double? sourceAmount;
  @JsonKey(
    name: 'feesChargingType',
    includeIfNull: false,
    toJson: feeChargingTypeDtoNullableToJson,
    fromJson: feeChargingTypeDtoNullableFromJson,
  )
  final enums.FeeChargingTypeDto? feesChargingType;
  @JsonKey(name: 'beneficiaryId', includeIfNull: false)
  final String? beneficiaryId;
  static const fromJsonFactory = _$TransferOptionRequestV2FromJson;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other is TransferOptionRequestV2 &&
            (identical(other.account, account) ||
                const DeepCollectionEquality()
                    .equals(other.account, account)) &&
            (identical(other.country, country) ||
                const DeepCollectionEquality()
                    .equals(other.country, country)) &&
            (identical(other.targetCurrency, targetCurrency) ||
                const DeepCollectionEquality()
                    .equals(other.targetCurrency, targetCurrency)) &&
            (identical(other.targetAmount, targetAmount) ||
                const DeepCollectionEquality()
                    .equals(other.targetAmount, targetAmount)) &&
            (identical(other.sourceCurrency, sourceCurrency) ||
                const DeepCollectionEquality()
                    .equals(other.sourceCurrency, sourceCurrency)) &&
            (identical(other.sourceAmount, sourceAmount) ||
                const DeepCollectionEquality()
                    .equals(other.sourceAmount, sourceAmount)) &&
            (identical(other.feesChargingType, feesChargingType) ||
                const DeepCollectionEquality()
                    .equals(other.feesChargingType, feesChargingType)) &&
            (identical(other.beneficiaryId, beneficiaryId) ||
                const DeepCollectionEquality()
                    .equals(other.beneficiaryId, beneficiaryId)));
  }

  @override
  String toString() => jsonEncode(this);

  @override
  int get hashCode =>
      const DeepCollectionEquality().hash(account) ^
      const DeepCollectionEquality().hash(country) ^
      const DeepCollectionEquality().hash(targetCurrency) ^
      const DeepCollectionEquality().hash(targetAmount) ^
      const DeepCollectionEquality().hash(sourceCurrency) ^
      const DeepCollectionEquality().hash(sourceAmount) ^
      const DeepCollectionEquality().hash(feesChargingType) ^
      const DeepCollectionEquality().hash(beneficiaryId) ^
      runtimeType.hashCode;
}

extension $TransferOptionRequestV2Extension on TransferOptionRequestV2 {
  TransferOptionRequestV2 copyWith(
      {String? account,
      String? country,
      String? targetCurrency,
      double? targetAmount,
      String? sourceCurrency,
      double? sourceAmount,
      enums.FeeChargingTypeDto? feesChargingType,
      String? beneficiaryId}) {
    return TransferOptionRequestV2(
        account: account ?? this.account,
        country: country ?? this.country,
        targetCurrency: targetCurrency ?? this.targetCurrency,
        targetAmount: targetAmount ?? this.targetAmount,
        sourceCurrency: sourceCurrency ?? this.sourceCurrency,
        sourceAmount: sourceAmount ?? this.sourceAmount,
        feesChargingType: feesChargingType ?? this.feesChargingType,
        beneficiaryId: beneficiaryId ?? this.beneficiaryId);
  }

  TransferOptionRequestV2 copyWithWrapped(
      {Wrapped<String>? account,
      Wrapped<String>? country,
      Wrapped<String?>? targetCurrency,
      Wrapped<double?>? targetAmount,
      Wrapped<String?>? sourceCurrency,
      Wrapped<double?>? sourceAmount,
      Wrapped<enums.FeeChargingTypeDto?>? feesChargingType,
      Wrapped<String?>? beneficiaryId}) {
    return TransferOptionRequestV2(
        account: (account != null ? account.value : this.account),
        country: (country != null ? country.value : this.country),
        targetCurrency: (targetCurrency != null
            ? targetCurrency.value
            : this.targetCurrency),
        targetAmount:
            (targetAmount != null ? targetAmount.value : this.targetAmount),
        sourceCurrency: (sourceCurrency != null
            ? sourceCurrency.value
            : this.sourceCurrency),
        sourceAmount:
            (sourceAmount != null ? sourceAmount.value : this.sourceAmount),
        feesChargingType: (feesChargingType != null
            ? feesChargingType.value
            : this.feesChargingType),
        beneficiaryId:
            (beneficiaryId != null ? beneficiaryId.value : this.beneficiaryId));
  }
}

String? transferFeeInfoFeeTypeNullableToJson(
    enums.TransferFeeInfoFeeType? transferFeeInfoFeeType) {
  return transferFeeInfoFeeType?.value;
}

String? transferFeeInfoFeeTypeToJson(
    enums.TransferFeeInfoFeeType transferFeeInfoFeeType) {
  return transferFeeInfoFeeType.value;
}

enums.TransferFeeInfoFeeType transferFeeInfoFeeTypeFromJson(
  Object? transferFeeInfoFeeType, [
  enums.TransferFeeInfoFeeType? defaultValue,
]) {
  return enums.TransferFeeInfoFeeType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          transferFeeInfoFeeType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.TransferFeeInfoFeeType.swaggerGeneratedUnknown;
}

enums.TransferFeeInfoFeeType? transferFeeInfoFeeTypeNullableFromJson(
  Object? transferFeeInfoFeeType, [
  enums.TransferFeeInfoFeeType? defaultValue,
]) {
  if (transferFeeInfoFeeType == null) {
    return null;
  }
  return enums.TransferFeeInfoFeeType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          transferFeeInfoFeeType.toString().toLowerCase()) ??
      defaultValue;
}

String transferFeeInfoFeeTypeExplodedListToJson(
    List<enums.TransferFeeInfoFeeType>? transferFeeInfoFeeType) {
  return transferFeeInfoFeeType?.map((e) => e.value!).join(',') ?? '';
}

List<String> transferFeeInfoFeeTypeListToJson(
    List<enums.TransferFeeInfoFeeType>? transferFeeInfoFeeType) {
  if (transferFeeInfoFeeType == null) {
    return [];
  }

  return transferFeeInfoFeeType.map((e) => e.value!).toList();
}

List<enums.TransferFeeInfoFeeType> transferFeeInfoFeeTypeListFromJson(
  List? transferFeeInfoFeeType, [
  List<enums.TransferFeeInfoFeeType>? defaultValue,
]) {
  if (transferFeeInfoFeeType == null) {
    return defaultValue ?? [];
  }

  return transferFeeInfoFeeType
      .map((e) => transferFeeInfoFeeTypeFromJson(e.toString()))
      .toList();
}

List<enums.TransferFeeInfoFeeType>? transferFeeInfoFeeTypeNullableListFromJson(
  List? transferFeeInfoFeeType, [
  List<enums.TransferFeeInfoFeeType>? defaultValue,
]) {
  if (transferFeeInfoFeeType == null) {
    return defaultValue;
  }

  return transferFeeInfoFeeType
      .map((e) => transferFeeInfoFeeTypeFromJson(e.toString()))
      .toList();
}

String? transferOptionResponseProviderTypeNullableToJson(
    enums.TransferOptionResponseProviderType?
        transferOptionResponseProviderType) {
  return transferOptionResponseProviderType?.value;
}

String? transferOptionResponseProviderTypeToJson(
    enums.TransferOptionResponseProviderType
        transferOptionResponseProviderType) {
  return transferOptionResponseProviderType.value;
}

enums.TransferOptionResponseProviderType
    transferOptionResponseProviderTypeFromJson(
  Object? transferOptionResponseProviderType, [
  enums.TransferOptionResponseProviderType? defaultValue,
]) {
  return enums.TransferOptionResponseProviderType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          transferOptionResponseProviderType?.toString().toLowerCase()) ??
      defaultValue ??
      enums.TransferOptionResponseProviderType.swaggerGeneratedUnknown;
}

enums.TransferOptionResponseProviderType?
    transferOptionResponseProviderTypeNullableFromJson(
  Object? transferOptionResponseProviderType, [
  enums.TransferOptionResponseProviderType? defaultValue,
]) {
  if (transferOptionResponseProviderType == null) {
    return null;
  }
  return enums.TransferOptionResponseProviderType.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          transferOptionResponseProviderType.toString().toLowerCase()) ??
      defaultValue;
}

String transferOptionResponseProviderTypeExplodedListToJson(
    List<enums.TransferOptionResponseProviderType>?
        transferOptionResponseProviderType) {
  return transferOptionResponseProviderType?.map((e) => e.value!).join(',') ??
      '';
}

List<String> transferOptionResponseProviderTypeListToJson(
    List<enums.TransferOptionResponseProviderType>?
        transferOptionResponseProviderType) {
  if (transferOptionResponseProviderType == null) {
    return [];
  }

  return transferOptionResponseProviderType.map((e) => e.value!).toList();
}

List<enums.TransferOptionResponseProviderType>
    transferOptionResponseProviderTypeListFromJson(
  List? transferOptionResponseProviderType, [
  List<enums.TransferOptionResponseProviderType>? defaultValue,
]) {
  if (transferOptionResponseProviderType == null) {
    return defaultValue ?? [];
  }

  return transferOptionResponseProviderType
      .map((e) => transferOptionResponseProviderTypeFromJson(e.toString()))
      .toList();
}

List<enums.TransferOptionResponseProviderType>?
    transferOptionResponseProviderTypeNullableListFromJson(
  List? transferOptionResponseProviderType, [
  List<enums.TransferOptionResponseProviderType>? defaultValue,
]) {
  if (transferOptionResponseProviderType == null) {
    return defaultValue;
  }

  return transferOptionResponseProviderType
      .map((e) => transferOptionResponseProviderTypeFromJson(e.toString()))
      .toList();
}

String? transferOptionResponseChannelNullableToJson(
    enums.TransferOptionResponseChannel? transferOptionResponseChannel) {
  return transferOptionResponseChannel?.value;
}

String? transferOptionResponseChannelToJson(
    enums.TransferOptionResponseChannel transferOptionResponseChannel) {
  return transferOptionResponseChannel.value;
}

enums.TransferOptionResponseChannel transferOptionResponseChannelFromJson(
  Object? transferOptionResponseChannel, [
  enums.TransferOptionResponseChannel? defaultValue,
]) {
  return enums.TransferOptionResponseChannel.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          transferOptionResponseChannel?.toString().toLowerCase()) ??
      defaultValue ??
      enums.TransferOptionResponseChannel.swaggerGeneratedUnknown;
}

enums.TransferOptionResponseChannel?
    transferOptionResponseChannelNullableFromJson(
  Object? transferOptionResponseChannel, [
  enums.TransferOptionResponseChannel? defaultValue,
]) {
  if (transferOptionResponseChannel == null) {
    return null;
  }
  return enums.TransferOptionResponseChannel.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          transferOptionResponseChannel.toString().toLowerCase()) ??
      defaultValue;
}

String transferOptionResponseChannelExplodedListToJson(
    List<enums.TransferOptionResponseChannel>? transferOptionResponseChannel) {
  return transferOptionResponseChannel?.map((e) => e.value!).join(',') ?? '';
}

List<String> transferOptionResponseChannelListToJson(
    List<enums.TransferOptionResponseChannel>? transferOptionResponseChannel) {
  if (transferOptionResponseChannel == null) {
    return [];
  }

  return transferOptionResponseChannel.map((e) => e.value!).toList();
}

List<enums.TransferOptionResponseChannel>
    transferOptionResponseChannelListFromJson(
  List? transferOptionResponseChannel, [
  List<enums.TransferOptionResponseChannel>? defaultValue,
]) {
  if (transferOptionResponseChannel == null) {
    return defaultValue ?? [];
  }

  return transferOptionResponseChannel
      .map((e) => transferOptionResponseChannelFromJson(e.toString()))
      .toList();
}

List<enums.TransferOptionResponseChannel>?
    transferOptionResponseChannelNullableListFromJson(
  List? transferOptionResponseChannel, [
  List<enums.TransferOptionResponseChannel>? defaultValue,
]) {
  if (transferOptionResponseChannel == null) {
    return defaultValue;
  }

  return transferOptionResponseChannel
      .map((e) => transferOptionResponseChannelFromJson(e.toString()))
      .toList();
}

String? transferOptionResponseEstimatedDeliveryTypeNullableToJson(
    enums.TransferOptionResponseEstimatedDeliveryType?
        transferOptionResponseEstimatedDeliveryType) {
  return transferOptionResponseEstimatedDeliveryType?.value;
}

String? transferOptionResponseEstimatedDeliveryTypeToJson(
    enums.TransferOptionResponseEstimatedDeliveryType
        transferOptionResponseEstimatedDeliveryType) {
  return transferOptionResponseEstimatedDeliveryType.value;
}

enums.TransferOptionResponseEstimatedDeliveryType
    transferOptionResponseEstimatedDeliveryTypeFromJson(
  Object? transferOptionResponseEstimatedDeliveryType, [
  enums.TransferOptionResponseEstimatedDeliveryType? defaultValue,
]) {
  return enums.TransferOptionResponseEstimatedDeliveryType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              transferOptionResponseEstimatedDeliveryType
                  ?.toString()
                  .toLowerCase()) ??
      defaultValue ??
      enums.TransferOptionResponseEstimatedDeliveryType.swaggerGeneratedUnknown;
}

enums.TransferOptionResponseEstimatedDeliveryType?
    transferOptionResponseEstimatedDeliveryTypeNullableFromJson(
  Object? transferOptionResponseEstimatedDeliveryType, [
  enums.TransferOptionResponseEstimatedDeliveryType? defaultValue,
]) {
  if (transferOptionResponseEstimatedDeliveryType == null) {
    return null;
  }
  return enums.TransferOptionResponseEstimatedDeliveryType.values
          .firstWhereOrNull((e) =>
              e.value.toString().toLowerCase() ==
              transferOptionResponseEstimatedDeliveryType
                  .toString()
                  .toLowerCase()) ??
      defaultValue;
}

String transferOptionResponseEstimatedDeliveryTypeExplodedListToJson(
    List<enums.TransferOptionResponseEstimatedDeliveryType>?
        transferOptionResponseEstimatedDeliveryType) {
  return transferOptionResponseEstimatedDeliveryType
          ?.map((e) => e.value!)
          .join(',') ??
      '';
}

List<String> transferOptionResponseEstimatedDeliveryTypeListToJson(
    List<enums.TransferOptionResponseEstimatedDeliveryType>?
        transferOptionResponseEstimatedDeliveryType) {
  if (transferOptionResponseEstimatedDeliveryType == null) {
    return [];
  }

  return transferOptionResponseEstimatedDeliveryType
      .map((e) => e.value!)
      .toList();
}

List<enums.TransferOptionResponseEstimatedDeliveryType>
    transferOptionResponseEstimatedDeliveryTypeListFromJson(
  List? transferOptionResponseEstimatedDeliveryType, [
  List<enums.TransferOptionResponseEstimatedDeliveryType>? defaultValue,
]) {
  if (transferOptionResponseEstimatedDeliveryType == null) {
    return defaultValue ?? [];
  }

  return transferOptionResponseEstimatedDeliveryType
      .map((e) =>
          transferOptionResponseEstimatedDeliveryTypeFromJson(e.toString()))
      .toList();
}

List<enums.TransferOptionResponseEstimatedDeliveryType>?
    transferOptionResponseEstimatedDeliveryTypeNullableListFromJson(
  List? transferOptionResponseEstimatedDeliveryType, [
  List<enums.TransferOptionResponseEstimatedDeliveryType>? defaultValue,
]) {
  if (transferOptionResponseEstimatedDeliveryType == null) {
    return defaultValue;
  }

  return transferOptionResponseEstimatedDeliveryType
      .map((e) =>
          transferOptionResponseEstimatedDeliveryTypeFromJson(e.toString()))
      .toList();
}

String? feeChargingTypeDtoNullableToJson(
    enums.FeeChargingTypeDto? feeChargingTypeDto) {
  return feeChargingTypeDto?.value;
}

String? feeChargingTypeDtoToJson(enums.FeeChargingTypeDto feeChargingTypeDto) {
  return feeChargingTypeDto.value;
}

enums.FeeChargingTypeDto feeChargingTypeDtoFromJson(
  Object? feeChargingTypeDto, [
  enums.FeeChargingTypeDto? defaultValue,
]) {
  return enums.FeeChargingTypeDto.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          feeChargingTypeDto?.toString().toLowerCase()) ??
      defaultValue ??
      enums.FeeChargingTypeDto.swaggerGeneratedUnknown;
}

enums.FeeChargingTypeDto? feeChargingTypeDtoNullableFromJson(
  Object? feeChargingTypeDto, [
  enums.FeeChargingTypeDto? defaultValue,
]) {
  if (feeChargingTypeDto == null) {
    return null;
  }
  return enums.FeeChargingTypeDto.values.firstWhereOrNull((e) =>
          e.value.toString().toLowerCase() ==
          feeChargingTypeDto.toString().toLowerCase()) ??
      defaultValue;
}

String feeChargingTypeDtoExplodedListToJson(
    List<enums.FeeChargingTypeDto>? feeChargingTypeDto) {
  return feeChargingTypeDto?.map((e) => e.value!).join(',') ?? '';
}

List<String> feeChargingTypeDtoListToJson(
    List<enums.FeeChargingTypeDto>? feeChargingTypeDto) {
  if (feeChargingTypeDto == null) {
    return [];
  }

  return feeChargingTypeDto.map((e) => e.value!).toList();
}

List<enums.FeeChargingTypeDto> feeChargingTypeDtoListFromJson(
  List? feeChargingTypeDto, [
  List<enums.FeeChargingTypeDto>? defaultValue,
]) {
  if (feeChargingTypeDto == null) {
    return defaultValue ?? [];
  }

  return feeChargingTypeDto
      .map((e) => feeChargingTypeDtoFromJson(e.toString()))
      .toList();
}

List<enums.FeeChargingTypeDto>? feeChargingTypeDtoNullableListFromJson(
  List? feeChargingTypeDto, [
  List<enums.FeeChargingTypeDto>? defaultValue,
]) {
  if (feeChargingTypeDto == null) {
    return defaultValue;
  }

  return feeChargingTypeDto
      .map((e) => feeChargingTypeDtoFromJson(e.toString()))
      .toList();
}

// ignore: unused_element
String? _dateToJson(DateTime? date) {
  if (date == null) {
    return null;
  }

  final year = date.year.toString();
  final month = date.month < 10 ? '0${date.month}' : date.month.toString();
  final day = date.day < 10 ? '0${date.day}' : date.day.toString();

  return '$year-$month-$day';
}

class Wrapped<T> {
  final T value;
  const Wrapped.value(this.value);
}
