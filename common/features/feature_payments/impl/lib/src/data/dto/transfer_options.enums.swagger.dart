import 'package:json_annotation/json_annotation.dart';
import 'package:collection/collection.dart';

enum TransferFeeInfoFeeType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('WIO')
  wio('WIO'),
  @JsonValue('WIO_CORRESPONDENT_BANK')
  wioCorrespondentBank('WIO_CORRESPONDENT_BANK'),
  @JsonValue('WISE')
  wise('WISE'),
  @JsonValue('UNKNOWN')
  unknown('UNKNOWN');

  final String? value;

  const TransferFeeInfoFeeType(this.value);
}

enum TransferOptionResponseProviderType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('SWIFT')
  swift('SWIFT'),
  @JsonValue('WISE')
  wise('WISE'),
  @JsonValue('LOCAL')
  local('LOCAL'),
  @JsonValue('UNKNOWN')
  unknown('UNKNOWN');

  final String? value;

  const TransferOptionResponseProviderType(this.value);
}

enum TransferOptionResponseChannel {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('WIO_TO_WIO')
  wioToWio('WIO_TO_WIO'),
  @JsonValue('LOCAL')
  local('LOCAL'),
  @JsonValue('SWIFT')
  swift('SWIFT'),
  @JsonValue('WISE')
  wise('WISE');

  final String? value;

  const TransferOptionResponseChannel(this.value);
}

enum TransferOptionResponseEstimatedDeliveryType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('TEXT')
  text('TEXT'),
  @JsonValue('INSTANT_ICON')
  instantIcon('INSTANT_ICON');

  final String? value;

  const TransferOptionResponseEstimatedDeliveryType(this.value);
}

enum FeeChargingTypeDto {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('CUSTOMER')
  customer('CUSTOMER'),
  @JsonValue('SHARED')
  shared('SHARED'),
  @JsonValue('BENEFICIARY')
  beneficiary('BENEFICIARY'),
  @JsonValue('UNKNOWN')
  unknown('UNKNOWN');

  final String? value;

  const FeeChargingTypeDto(this.value);
}
