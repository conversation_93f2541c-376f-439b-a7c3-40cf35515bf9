// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_options.swagger.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TransferInfoFeeDetails _$TransferInfoFeeDetailsFromJson(
        Map<String, dynamic> json) =>
    TransferInfoFeeDetails(
      vat: (json['vat'] as num?)?.toDouble(),
      transferFees: (json['transferFees'] as List<dynamic>?)
              ?.map((e) => TransferFeeInfo.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      possibleFees: (json['possibleFees'] as List<dynamic>?)
              ?.map((e) => PossibleFeeInfo.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      total: (json['total'] as num?)?.toDouble(),
      freeChargeFeeBalance: (json['freeChargeFeeBalance'] as num?)?.toInt(),
      freeChargeFeeTotal: (json['freeChargeFeeTotal'] as num?)?.toInt(),
      currency: json['currency'] as String?,
      selectedChargingType:
          feeChargingTypeDtoNullableFromJson(json['selectedChargingType']),
    );

Map<String, dynamic> _$TransferInfoFeeDetailsToJson(
        TransferInfoFeeDetails instance) =>
    <String, dynamic>{
      if (instance.vat case final value?) 'vat': value,
      if (instance.transferFees?.map((e) => e.toJson()).toList()
          case final value?)
        'transferFees': value,
      if (instance.possibleFees?.map((e) => e.toJson()).toList()
          case final value?)
        'possibleFees': value,
      if (instance.total case final value?) 'total': value,
      if (instance.freeChargeFeeBalance case final value?)
        'freeChargeFeeBalance': value,
      if (instance.freeChargeFeeTotal case final value?)
        'freeChargeFeeTotal': value,
      if (instance.currency case final value?) 'currency': value,
      if (feeChargingTypeDtoNullableToJson(instance.selectedChargingType)
          case final value?)
        'selectedChargingType': value,
    };

TransferFeeInfo _$TransferFeeInfoFromJson(Map<String, dynamic> json) =>
    TransferFeeInfo(
      feeType: transferFeeInfoFeeTypeNullableFromJson(json['feeType']),
      description: json['description'] as String?,
      amount: (json['amount'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
    );

Map<String, dynamic> _$TransferFeeInfoToJson(TransferFeeInfo instance) =>
    <String, dynamic>{
      if (transferFeeInfoFeeTypeNullableToJson(instance.feeType)
          case final value?)
        'feeType': value,
      if (instance.description case final value?) 'description': value,
      if (instance.amount case final value?) 'amount': value,
      if (instance.currency case final value?) 'currency': value,
    };

PossibleFeeInfo _$PossibleFeeInfoFromJson(Map<String, dynamic> json) =>
    PossibleFeeInfo(
      feeCharging: feeChargingTypeDtoNullableFromJson(json['feeCharging']),
      transferFees: (json['transferFees'] as List<dynamic>?)
              ?.map((e) => TransferFeeInfo.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$PossibleFeeInfoToJson(PossibleFeeInfo instance) =>
    <String, dynamic>{
      if (feeChargingTypeDtoNullableToJson(instance.feeCharging)
          case final value?)
        'feeCharging': value,
      if (instance.transferFees?.map((e) => e.toJson()).toList()
          case final value?)
        'transferFees': value,
    };

TransferOptionsRequest _$TransferOptionsRequestFromJson(
        Map<String, dynamic> json) =>
    TransferOptionsRequest(
      account: json['account'] as String,
      country: json['country'] as String,
      currency: json['currency'] as String,
      amount: (json['amount'] as num).toDouble(),
      feesChargingType:
          feeChargingTypeDtoNullableFromJson(json['feesChargingType']),
      beneficiaryId: json['beneficiaryId'] as String?,
    );

Map<String, dynamic> _$TransferOptionsRequestToJson(
        TransferOptionsRequest instance) =>
    <String, dynamic>{
      'account': instance.account,
      'country': instance.country,
      'currency': instance.currency,
      'amount': instance.amount,
      if (feeChargingTypeDtoNullableToJson(instance.feesChargingType)
          case final value?)
        'feesChargingType': value,
      if (instance.beneficiaryId case final value?) 'beneficiaryId': value,
    };

TransferInfoAmount _$TransferInfoAmountFromJson(Map<String, dynamic> json) =>
    TransferInfoAmount(
      sourceAmount: (json['sourceAmount'] as num?)?.toDouble(),
      targetAmount: (json['targetAmount'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$TransferInfoAmountToJson(TransferInfoAmount instance) =>
    <String, dynamic>{
      if (instance.sourceAmount case final value?) 'sourceAmount': value,
      if (instance.targetAmount case final value?) 'targetAmount': value,
    };

TransferOptionResponse _$TransferOptionResponseFromJson(
        Map<String, dynamic> json) =>
    TransferOptionResponse(
      id: json['id'] as String?,
      providerType: transferOptionResponseProviderTypeNullableFromJson(
          json['providerType']),
      providerLogo: json['providerLogo'] as String?,
      sourceCurrency: json['sourceCurrency'] as String?,
      targetCurrency: json['targetCurrency'] as String?,
      exchangeRate: (json['exchangeRate'] as num?)?.toDouble(),
      feeDetails: json['feeDetails'] == null
          ? null
          : TransferInfoFeeDetails.fromJson(
              json['feeDetails'] as Map<String, dynamic>),
      deliveryTime: json['deliveryTime'] == null
          ? null
          : DateTime.parse(json['deliveryTime'] as String),
      estimatedDeliveryInterval: json['estimatedDeliveryInterval'] as String?,
      errorMessage: json['errorMessage'] as String?,
      initialAmount: json['initialAmount'] == null
          ? null
          : TransferInfoAmount.fromJson(
              json['initialAmount'] as Map<String, dynamic>),
      chargedAmount: json['chargedAmount'] == null
          ? null
          : TransferInfoAmount.fromJson(
              json['chargedAmount'] as Map<String, dynamic>),
      allowedFeeCharging:
          feeChargingTypeDtoListFromJson(json['allowedFeeCharging'] as List?),
      errorCode: json['errorCode'] as String?,
      preValidationErrorMessage: json['preValidationErrorMessage'] as String?,
      quoteTTLSeconds: (json['quoteTTLSeconds'] as num?)?.toInt(),
      beneficiaryUpdateRequired: json['beneficiaryUpdateRequired'] as bool?,
      skipOptionUserView: json['skipOptionUserView'] as bool?,
      channel: transferOptionResponseChannelNullableFromJson(json['channel']),
      estimatedDeliveryType:
          transferOptionResponseEstimatedDeliveryTypeNullableFromJson(
              json['estimatedDeliveryType']),
    );

Map<String, dynamic> _$TransferOptionResponseToJson(
        TransferOptionResponse instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (transferOptionResponseProviderTypeNullableToJson(
              instance.providerType)
          case final value?)
        'providerType': value,
      if (instance.providerLogo case final value?) 'providerLogo': value,
      if (instance.sourceCurrency case final value?) 'sourceCurrency': value,
      if (instance.targetCurrency case final value?) 'targetCurrency': value,
      if (instance.exchangeRate case final value?) 'exchangeRate': value,
      if (instance.feeDetails?.toJson() case final value?) 'feeDetails': value,
      if (instance.deliveryTime?.toIso8601String() case final value?)
        'deliveryTime': value,
      if (instance.estimatedDeliveryInterval case final value?)
        'estimatedDeliveryInterval': value,
      if (instance.errorMessage case final value?) 'errorMessage': value,
      if (instance.initialAmount?.toJson() case final value?)
        'initialAmount': value,
      if (instance.chargedAmount?.toJson() case final value?)
        'chargedAmount': value,
      'allowedFeeCharging':
          feeChargingTypeDtoListToJson(instance.allowedFeeCharging),
      if (instance.errorCode case final value?) 'errorCode': value,
      if (instance.preValidationErrorMessage case final value?)
        'preValidationErrorMessage': value,
      if (instance.quoteTTLSeconds case final value?) 'quoteTTLSeconds': value,
      if (instance.beneficiaryUpdateRequired case final value?)
        'beneficiaryUpdateRequired': value,
      if (instance.skipOptionUserView case final value?)
        'skipOptionUserView': value,
      if (transferOptionResponseChannelNullableToJson(instance.channel)
          case final value?)
        'channel': value,
      if (transferOptionResponseEstimatedDeliveryTypeNullableToJson(
              instance.estimatedDeliveryType)
          case final value?)
        'estimatedDeliveryType': value,
    };

UpdateTransferFeeChargingTypeRequest
    _$UpdateTransferFeeChargingTypeRequestFromJson(Map<String, dynamic> json) =>
        UpdateTransferFeeChargingTypeRequest(
          feesChargingType:
              feeChargingTypeDtoNullableFromJson(json['feesChargingType']),
        );

Map<String, dynamic> _$UpdateTransferFeeChargingTypeRequestToJson(
        UpdateTransferFeeChargingTypeRequest instance) =>
    <String, dynamic>{
      if (feeChargingTypeDtoNullableToJson(instance.feesChargingType)
          case final value?)
        'feesChargingType': value,
    };

TransferOptionRequestV2 _$TransferOptionRequestV2FromJson(
        Map<String, dynamic> json) =>
    TransferOptionRequestV2(
      account: json['account'] as String,
      country: json['country'] as String,
      targetCurrency: json['targetCurrency'] as String?,
      targetAmount: (json['targetAmount'] as num?)?.toDouble(),
      sourceCurrency: json['sourceCurrency'] as String?,
      sourceAmount: (json['sourceAmount'] as num?)?.toDouble(),
      feesChargingType:
          feeChargingTypeDtoNullableFromJson(json['feesChargingType']),
      beneficiaryId: json['beneficiaryId'] as String?,
    );

Map<String, dynamic> _$TransferOptionRequestV2ToJson(
        TransferOptionRequestV2 instance) =>
    <String, dynamic>{
      'account': instance.account,
      'country': instance.country,
      if (instance.targetCurrency case final value?) 'targetCurrency': value,
      if (instance.targetAmount case final value?) 'targetAmount': value,
      if (instance.sourceCurrency case final value?) 'sourceCurrency': value,
      if (instance.sourceAmount case final value?) 'sourceAmount': value,
      if (feeChargingTypeDtoNullableToJson(instance.feesChargingType)
          case final value?)
        'feesChargingType': value,
      if (instance.beneficiaryId case final value?) 'beneficiaryId': value,
    };
