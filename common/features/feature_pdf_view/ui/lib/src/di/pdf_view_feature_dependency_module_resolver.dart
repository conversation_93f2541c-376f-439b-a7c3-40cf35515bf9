import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:logging_api/logging.dart';
import 'package:wio_common_feature_pdf_view_api/wio_common_feature_pdf_view_api.dart';
import 'package:wio_common_feature_pdf_view_ui/l10n/pdf_view_localization.g.dart';
import 'package:wio_common_feature_pdf_view_ui/src/navigation/flow/pdf_view_flow_impl.dart';
import 'package:wio_common_feature_pdf_view_ui/src/navigation/pdf_view_router.dart';
import 'package:wio_common_feature_pdf_view_ui/src/screens/pdf_view_cubit.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';
import 'package:wio_feature_share_api/feature_share_api.dart';

class PdfViewerFeatureDependencyModuleResolver {
  const PdfViewerFeatureDependencyModuleResolver._();

  static void register() {
    _localizations();
    _registerFlow();
    _registerNavigation();
    _registerCubits();
  }

  static void _localizations() {
    DependencyProvider.registerLazySingleton(
      () => PDFViewLocalizations.of(DependencyProvider.get<BuildContext>()),
    );

    DependencyProvider.get<List<LocalizationsDelegate<Object?>>>()
        .add(PDFViewLocalizations.delegate);
  }

  static void _registerNavigation() {
    DependencyProvider.registerLazySingleton<NavigationRouter>(
      () => PDFViewRouter(),
      instanceName: PdfViewFeatureNavigationConfig.name,
    );
  }

  static void _registerCubits() {
    DependencyProvider.registerLazySingleton<PDFViewCubit>(
      () => PDFViewCubit(
        DependencyProvider.get<ShareProvider>(),
      ),
    );
  }

  static void _registerFlow() {
    DependencyProvider.registerLazySingleton<PDFViewFlow>(
      () => PDFViewFlowImpl(
        DependencyProvider.get<NavigationProvider>(),
        DependencyProvider.get<ToastMessageProvider>(),
        DependencyProvider.get<PDFViewLocalizations>(),
        DependencyProvider.get<Logger>(instanceName: WioDomain.core.name),
      ),
    );
  }
}
