import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

part 'pdf_view_feature_navigation_config.freezed.dart';

@freezed
class PdfViewFeatureNavigationConfig extends FeatureNavigationConfig
    with _$PdfViewFeatureNavigationConfig {
  static const name = 'pdf_view_feature';

  const factory PdfViewFeatureNavigationConfig({
    required String path,
    String? title,
  }) = _PdfViewFeatureNavigationConfig;

  const PdfViewFeatureNavigationConfig._() : super(name);

  @override
  String toString() => 'PdfViewFeatureNavigationConfig';
}
