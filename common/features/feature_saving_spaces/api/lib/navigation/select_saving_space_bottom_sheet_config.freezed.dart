// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'select_saving_space_bottom_sheet_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SelectSavingSpaceBottomSheetConfig {
  List<SavingSpace> get savingSpaces => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SelectSavingSpaceBottomSheetConfigCopyWith<
          SelectSavingSpaceBottomSheetConfig>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SelectSavingSpaceBottomSheetConfigCopyWith<$Res> {
  factory $SelectSavingSpaceBottomSheetConfigCopyWith(
          SelectSavingSpaceBottomSheetConfig value,
          $Res Function(SelectSavingSpaceBottomSheetConfig) then) =
      _$SelectSavingSpaceBottomSheetConfigCopyWithImpl<$Res,
          SelectSavingSpaceBottomSheetConfig>;
  @useResult
  $Res call({List<SavingSpace> savingSpaces});
}

/// @nodoc
class _$SelectSavingSpaceBottomSheetConfigCopyWithImpl<$Res,
        $Val extends SelectSavingSpaceBottomSheetConfig>
    implements $SelectSavingSpaceBottomSheetConfigCopyWith<$Res> {
  _$SelectSavingSpaceBottomSheetConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? savingSpaces = null,
  }) {
    return _then(_value.copyWith(
      savingSpaces: null == savingSpaces
          ? _value.savingSpaces
          : savingSpaces // ignore: cast_nullable_to_non_nullable
              as List<SavingSpace>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SelectSavingSpaceBottomSheetConfigImplCopyWith<$Res>
    implements $SelectSavingSpaceBottomSheetConfigCopyWith<$Res> {
  factory _$$SelectSavingSpaceBottomSheetConfigImplCopyWith(
          _$SelectSavingSpaceBottomSheetConfigImpl value,
          $Res Function(_$SelectSavingSpaceBottomSheetConfigImpl) then) =
      __$$SelectSavingSpaceBottomSheetConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<SavingSpace> savingSpaces});
}

/// @nodoc
class __$$SelectSavingSpaceBottomSheetConfigImplCopyWithImpl<$Res>
    extends _$SelectSavingSpaceBottomSheetConfigCopyWithImpl<$Res,
        _$SelectSavingSpaceBottomSheetConfigImpl>
    implements _$$SelectSavingSpaceBottomSheetConfigImplCopyWith<$Res> {
  __$$SelectSavingSpaceBottomSheetConfigImplCopyWithImpl(
      _$SelectSavingSpaceBottomSheetConfigImpl _value,
      $Res Function(_$SelectSavingSpaceBottomSheetConfigImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? savingSpaces = null,
  }) {
    return _then(_$SelectSavingSpaceBottomSheetConfigImpl(
      savingSpaces: null == savingSpaces
          ? _value._savingSpaces
          : savingSpaces // ignore: cast_nullable_to_non_nullable
              as List<SavingSpace>,
    ));
  }
}

/// @nodoc

class _$SelectSavingSpaceBottomSheetConfigImpl
    extends _SelectSavingSpaceBottomSheetConfig {
  const _$SelectSavingSpaceBottomSheetConfigImpl(
      {required final List<SavingSpace> savingSpaces})
      : _savingSpaces = savingSpaces,
        super._();

  final List<SavingSpace> _savingSpaces;
  @override
  List<SavingSpace> get savingSpaces {
    if (_savingSpaces is EqualUnmodifiableListView) return _savingSpaces;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_savingSpaces);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelectSavingSpaceBottomSheetConfigImpl &&
            const DeepCollectionEquality()
                .equals(other._savingSpaces, _savingSpaces));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_savingSpaces));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SelectSavingSpaceBottomSheetConfigImplCopyWith<
          _$SelectSavingSpaceBottomSheetConfigImpl>
      get copyWith => __$$SelectSavingSpaceBottomSheetConfigImplCopyWithImpl<
          _$SelectSavingSpaceBottomSheetConfigImpl>(this, _$identity);
}

abstract class _SelectSavingSpaceBottomSheetConfig
    extends SelectSavingSpaceBottomSheetConfig {
  const factory _SelectSavingSpaceBottomSheetConfig(
          {required final List<SavingSpace> savingSpaces}) =
      _$SelectSavingSpaceBottomSheetConfigImpl;
  const _SelectSavingSpaceBottomSheetConfig._() : super._();

  @override
  List<SavingSpace> get savingSpaces;
  @override
  @JsonKey(ignore: true)
  _$$SelectSavingSpaceBottomSheetConfigImplCopyWith<
          _$SelectSavingSpaceBottomSheetConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$SelectSavingSpaceResult {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(SavingSpace space) select,
    required TResult Function() createNew,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(SavingSpace space)? select,
    TResult? Function()? createNew,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(SavingSpace space)? select,
    TResult Function()? createNew,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SelectSavingSpaceResult value) select,
    required TResult Function(_CreateNewSavingSpaceResult value) createNew,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SelectSavingSpaceResult value)? select,
    TResult? Function(_CreateNewSavingSpaceResult value)? createNew,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SelectSavingSpaceResult value)? select,
    TResult Function(_CreateNewSavingSpaceResult value)? createNew,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SelectSavingSpaceResultCopyWith<$Res> {
  factory $SelectSavingSpaceResultCopyWith(SelectSavingSpaceResult value,
          $Res Function(SelectSavingSpaceResult) then) =
      _$SelectSavingSpaceResultCopyWithImpl<$Res, SelectSavingSpaceResult>;
}

/// @nodoc
class _$SelectSavingSpaceResultCopyWithImpl<$Res,
        $Val extends SelectSavingSpaceResult>
    implements $SelectSavingSpaceResultCopyWith<$Res> {
  _$SelectSavingSpaceResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$SelectSavingSpaceResultImplCopyWith<$Res> {
  factory _$$SelectSavingSpaceResultImplCopyWith(
          _$SelectSavingSpaceResultImpl value,
          $Res Function(_$SelectSavingSpaceResultImpl) then) =
      __$$SelectSavingSpaceResultImplCopyWithImpl<$Res>;
  @useResult
  $Res call({SavingSpace space});

  $SavingSpaceCopyWith<$Res> get space;
}

/// @nodoc
class __$$SelectSavingSpaceResultImplCopyWithImpl<$Res>
    extends _$SelectSavingSpaceResultCopyWithImpl<$Res,
        _$SelectSavingSpaceResultImpl>
    implements _$$SelectSavingSpaceResultImplCopyWith<$Res> {
  __$$SelectSavingSpaceResultImplCopyWithImpl(
      _$SelectSavingSpaceResultImpl _value,
      $Res Function(_$SelectSavingSpaceResultImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? space = null,
  }) {
    return _then(_$SelectSavingSpaceResultImpl(
      null == space
          ? _value.space
          : space // ignore: cast_nullable_to_non_nullable
              as SavingSpace,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $SavingSpaceCopyWith<$Res> get space {
    return $SavingSpaceCopyWith<$Res>(_value.space, (value) {
      return _then(_value.copyWith(space: value));
    });
  }
}

/// @nodoc

class _$SelectSavingSpaceResultImpl implements _SelectSavingSpaceResult {
  const _$SelectSavingSpaceResultImpl(this.space);

  @override
  final SavingSpace space;

  @override
  String toString() {
    return 'SelectSavingSpaceResult.select(space: $space)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelectSavingSpaceResultImpl &&
            (identical(other.space, space) || other.space == space));
  }

  @override
  int get hashCode => Object.hash(runtimeType, space);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SelectSavingSpaceResultImplCopyWith<_$SelectSavingSpaceResultImpl>
      get copyWith => __$$SelectSavingSpaceResultImplCopyWithImpl<
          _$SelectSavingSpaceResultImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(SavingSpace space) select,
    required TResult Function() createNew,
  }) {
    return select(space);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(SavingSpace space)? select,
    TResult? Function()? createNew,
  }) {
    return select?.call(space);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(SavingSpace space)? select,
    TResult Function()? createNew,
    required TResult orElse(),
  }) {
    if (select != null) {
      return select(space);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SelectSavingSpaceResult value) select,
    required TResult Function(_CreateNewSavingSpaceResult value) createNew,
  }) {
    return select(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SelectSavingSpaceResult value)? select,
    TResult? Function(_CreateNewSavingSpaceResult value)? createNew,
  }) {
    return select?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SelectSavingSpaceResult value)? select,
    TResult Function(_CreateNewSavingSpaceResult value)? createNew,
    required TResult orElse(),
  }) {
    if (select != null) {
      return select(this);
    }
    return orElse();
  }
}

abstract class _SelectSavingSpaceResult implements SelectSavingSpaceResult {
  const factory _SelectSavingSpaceResult(final SavingSpace space) =
      _$SelectSavingSpaceResultImpl;

  SavingSpace get space;
  @JsonKey(ignore: true)
  _$$SelectSavingSpaceResultImplCopyWith<_$SelectSavingSpaceResultImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CreateNewSavingSpaceResultImplCopyWith<$Res> {
  factory _$$CreateNewSavingSpaceResultImplCopyWith(
          _$CreateNewSavingSpaceResultImpl value,
          $Res Function(_$CreateNewSavingSpaceResultImpl) then) =
      __$$CreateNewSavingSpaceResultImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CreateNewSavingSpaceResultImplCopyWithImpl<$Res>
    extends _$SelectSavingSpaceResultCopyWithImpl<$Res,
        _$CreateNewSavingSpaceResultImpl>
    implements _$$CreateNewSavingSpaceResultImplCopyWith<$Res> {
  __$$CreateNewSavingSpaceResultImplCopyWithImpl(
      _$CreateNewSavingSpaceResultImpl _value,
      $Res Function(_$CreateNewSavingSpaceResultImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$CreateNewSavingSpaceResultImpl implements _CreateNewSavingSpaceResult {
  const _$CreateNewSavingSpaceResultImpl();

  @override
  String toString() {
    return 'SelectSavingSpaceResult.createNew()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateNewSavingSpaceResultImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(SavingSpace space) select,
    required TResult Function() createNew,
  }) {
    return createNew();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(SavingSpace space)? select,
    TResult? Function()? createNew,
  }) {
    return createNew?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(SavingSpace space)? select,
    TResult Function()? createNew,
    required TResult orElse(),
  }) {
    if (createNew != null) {
      return createNew();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SelectSavingSpaceResult value) select,
    required TResult Function(_CreateNewSavingSpaceResult value) createNew,
  }) {
    return createNew(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SelectSavingSpaceResult value)? select,
    TResult? Function(_CreateNewSavingSpaceResult value)? createNew,
  }) {
    return createNew?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SelectSavingSpaceResult value)? select,
    TResult Function(_CreateNewSavingSpaceResult value)? createNew,
    required TResult orElse(),
  }) {
    if (createNew != null) {
      return createNew(this);
    }
    return orElse();
  }
}

abstract class _CreateNewSavingSpaceResult implements SelectSavingSpaceResult {
  const factory _CreateNewSavingSpaceResult() =
      _$CreateNewSavingSpaceResultImpl;
}
