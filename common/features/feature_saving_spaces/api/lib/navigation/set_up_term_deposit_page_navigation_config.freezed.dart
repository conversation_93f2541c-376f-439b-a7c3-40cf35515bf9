// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'set_up_term_deposit_page_navigation_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CreateTermDepositResult {
  TermDeposit get termDeposit => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TermDeposit termDeposit) showDetails,
    required TResult Function(TermDeposit termDeposit) cancelled,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TermDeposit termDeposit)? showDetails,
    TResult? Function(TermDeposit termDeposit)? cancelled,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TermDeposit termDeposit)? showDetails,
    TResult Function(TermDeposit termDeposit)? cancelled,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CreateTermDepositShowDetailsIntent value)
        showDetails,
    required TResult Function(_CreateTermDepositCancelledIntent value)
        cancelled,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CreateTermDepositShowDetailsIntent value)? showDetails,
    TResult? Function(_CreateTermDepositCancelledIntent value)? cancelled,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CreateTermDepositShowDetailsIntent value)? showDetails,
    TResult Function(_CreateTermDepositCancelledIntent value)? cancelled,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of CreateTermDepositResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateTermDepositResultCopyWith<CreateTermDepositResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateTermDepositResultCopyWith<$Res> {
  factory $CreateTermDepositResultCopyWith(CreateTermDepositResult value,
          $Res Function(CreateTermDepositResult) then) =
      _$CreateTermDepositResultCopyWithImpl<$Res, CreateTermDepositResult>;
  @useResult
  $Res call({TermDeposit termDeposit});
}

/// @nodoc
class _$CreateTermDepositResultCopyWithImpl<$Res,
        $Val extends CreateTermDepositResult>
    implements $CreateTermDepositResultCopyWith<$Res> {
  _$CreateTermDepositResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateTermDepositResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? termDeposit = null,
  }) {
    return _then(_value.copyWith(
      termDeposit: null == termDeposit
          ? _value.termDeposit
          : termDeposit // ignore: cast_nullable_to_non_nullable
              as TermDeposit,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateTermDepositShowDetailsIntentImplCopyWith<$Res>
    implements $CreateTermDepositResultCopyWith<$Res> {
  factory _$$CreateTermDepositShowDetailsIntentImplCopyWith(
          _$CreateTermDepositShowDetailsIntentImpl value,
          $Res Function(_$CreateTermDepositShowDetailsIntentImpl) then) =
      __$$CreateTermDepositShowDetailsIntentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({TermDeposit termDeposit});
}

/// @nodoc
class __$$CreateTermDepositShowDetailsIntentImplCopyWithImpl<$Res>
    extends _$CreateTermDepositResultCopyWithImpl<$Res,
        _$CreateTermDepositShowDetailsIntentImpl>
    implements _$$CreateTermDepositShowDetailsIntentImplCopyWith<$Res> {
  __$$CreateTermDepositShowDetailsIntentImplCopyWithImpl(
      _$CreateTermDepositShowDetailsIntentImpl _value,
      $Res Function(_$CreateTermDepositShowDetailsIntentImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateTermDepositResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? termDeposit = null,
  }) {
    return _then(_$CreateTermDepositShowDetailsIntentImpl(
      null == termDeposit
          ? _value.termDeposit
          : termDeposit // ignore: cast_nullable_to_non_nullable
              as TermDeposit,
    ));
  }
}

/// @nodoc

class _$CreateTermDepositShowDetailsIntentImpl
    implements _CreateTermDepositShowDetailsIntent {
  const _$CreateTermDepositShowDetailsIntentImpl(this.termDeposit);

  @override
  final TermDeposit termDeposit;

  @override
  String toString() {
    return 'CreateTermDepositResult.showDetails(termDeposit: $termDeposit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateTermDepositShowDetailsIntentImpl &&
            (identical(other.termDeposit, termDeposit) ||
                other.termDeposit == termDeposit));
  }

  @override
  int get hashCode => Object.hash(runtimeType, termDeposit);

  /// Create a copy of CreateTermDepositResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateTermDepositShowDetailsIntentImplCopyWith<
          _$CreateTermDepositShowDetailsIntentImpl>
      get copyWith => __$$CreateTermDepositShowDetailsIntentImplCopyWithImpl<
          _$CreateTermDepositShowDetailsIntentImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TermDeposit termDeposit) showDetails,
    required TResult Function(TermDeposit termDeposit) cancelled,
  }) {
    return showDetails(termDeposit);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TermDeposit termDeposit)? showDetails,
    TResult? Function(TermDeposit termDeposit)? cancelled,
  }) {
    return showDetails?.call(termDeposit);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TermDeposit termDeposit)? showDetails,
    TResult Function(TermDeposit termDeposit)? cancelled,
    required TResult orElse(),
  }) {
    if (showDetails != null) {
      return showDetails(termDeposit);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CreateTermDepositShowDetailsIntent value)
        showDetails,
    required TResult Function(_CreateTermDepositCancelledIntent value)
        cancelled,
  }) {
    return showDetails(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CreateTermDepositShowDetailsIntent value)? showDetails,
    TResult? Function(_CreateTermDepositCancelledIntent value)? cancelled,
  }) {
    return showDetails?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CreateTermDepositShowDetailsIntent value)? showDetails,
    TResult Function(_CreateTermDepositCancelledIntent value)? cancelled,
    required TResult orElse(),
  }) {
    if (showDetails != null) {
      return showDetails(this);
    }
    return orElse();
  }
}

abstract class _CreateTermDepositShowDetailsIntent
    implements CreateTermDepositResult {
  const factory _CreateTermDepositShowDetailsIntent(
      final TermDeposit termDeposit) = _$CreateTermDepositShowDetailsIntentImpl;

  @override
  TermDeposit get termDeposit;

  /// Create a copy of CreateTermDepositResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateTermDepositShowDetailsIntentImplCopyWith<
          _$CreateTermDepositShowDetailsIntentImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CreateTermDepositCancelledIntentImplCopyWith<$Res>
    implements $CreateTermDepositResultCopyWith<$Res> {
  factory _$$CreateTermDepositCancelledIntentImplCopyWith(
          _$CreateTermDepositCancelledIntentImpl value,
          $Res Function(_$CreateTermDepositCancelledIntentImpl) then) =
      __$$CreateTermDepositCancelledIntentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({TermDeposit termDeposit});
}

/// @nodoc
class __$$CreateTermDepositCancelledIntentImplCopyWithImpl<$Res>
    extends _$CreateTermDepositResultCopyWithImpl<$Res,
        _$CreateTermDepositCancelledIntentImpl>
    implements _$$CreateTermDepositCancelledIntentImplCopyWith<$Res> {
  __$$CreateTermDepositCancelledIntentImplCopyWithImpl(
      _$CreateTermDepositCancelledIntentImpl _value,
      $Res Function(_$CreateTermDepositCancelledIntentImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateTermDepositResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? termDeposit = null,
  }) {
    return _then(_$CreateTermDepositCancelledIntentImpl(
      null == termDeposit
          ? _value.termDeposit
          : termDeposit // ignore: cast_nullable_to_non_nullable
              as TermDeposit,
    ));
  }
}

/// @nodoc

class _$CreateTermDepositCancelledIntentImpl
    implements _CreateTermDepositCancelledIntent {
  const _$CreateTermDepositCancelledIntentImpl(this.termDeposit);

  @override
  final TermDeposit termDeposit;

  @override
  String toString() {
    return 'CreateTermDepositResult.cancelled(termDeposit: $termDeposit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateTermDepositCancelledIntentImpl &&
            (identical(other.termDeposit, termDeposit) ||
                other.termDeposit == termDeposit));
  }

  @override
  int get hashCode => Object.hash(runtimeType, termDeposit);

  /// Create a copy of CreateTermDepositResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateTermDepositCancelledIntentImplCopyWith<
          _$CreateTermDepositCancelledIntentImpl>
      get copyWith => __$$CreateTermDepositCancelledIntentImplCopyWithImpl<
          _$CreateTermDepositCancelledIntentImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(TermDeposit termDeposit) showDetails,
    required TResult Function(TermDeposit termDeposit) cancelled,
  }) {
    return cancelled(termDeposit);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(TermDeposit termDeposit)? showDetails,
    TResult? Function(TermDeposit termDeposit)? cancelled,
  }) {
    return cancelled?.call(termDeposit);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(TermDeposit termDeposit)? showDetails,
    TResult Function(TermDeposit termDeposit)? cancelled,
    required TResult orElse(),
  }) {
    if (cancelled != null) {
      return cancelled(termDeposit);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CreateTermDepositShowDetailsIntent value)
        showDetails,
    required TResult Function(_CreateTermDepositCancelledIntent value)
        cancelled,
  }) {
    return cancelled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CreateTermDepositShowDetailsIntent value)? showDetails,
    TResult? Function(_CreateTermDepositCancelledIntent value)? cancelled,
  }) {
    return cancelled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CreateTermDepositShowDetailsIntent value)? showDetails,
    TResult Function(_CreateTermDepositCancelledIntent value)? cancelled,
    required TResult orElse(),
  }) {
    if (cancelled != null) {
      return cancelled(this);
    }
    return orElse();
  }
}

abstract class _CreateTermDepositCancelledIntent
    implements CreateTermDepositResult {
  const factory _CreateTermDepositCancelledIntent(
      final TermDeposit termDeposit) = _$CreateTermDepositCancelledIntentImpl;

  @override
  TermDeposit get termDeposit;

  /// Create a copy of CreateTermDepositResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateTermDepositCancelledIntentImplCopyWith<
          _$CreateTermDepositCancelledIntentImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$SetUpTermDepositParams {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fromDepositAccount,
    required TResult Function(SavingSpace savingSpace) fromSavingSpace,
    required TResult Function(FamilyMember coOwner) forFamily,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fromDepositAccount,
    TResult? Function(SavingSpace savingSpace)? fromSavingSpace,
    TResult? Function(FamilyMember coOwner)? forFamily,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fromDepositAccount,
    TResult Function(SavingSpace savingSpace)? fromSavingSpace,
    TResult Function(FamilyMember coOwner)? forFamily,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetUpTermDepositParams value) fromDepositAccount,
    required TResult Function(_SetUpTermDepositFromSavingSpaceParams value)
        fromSavingSpace,
    required TResult Function(SetUpTermDepositForFamilyParams value) forFamily,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetUpTermDepositParams value)? fromDepositAccount,
    TResult? Function(_SetUpTermDepositFromSavingSpaceParams value)?
        fromSavingSpace,
    TResult? Function(SetUpTermDepositForFamilyParams value)? forFamily,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetUpTermDepositParams value)? fromDepositAccount,
    TResult Function(_SetUpTermDepositFromSavingSpaceParams value)?
        fromSavingSpace,
    TResult Function(SetUpTermDepositForFamilyParams value)? forFamily,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SetUpTermDepositParamsCopyWith<$Res> {
  factory $SetUpTermDepositParamsCopyWith(SetUpTermDepositParams value,
          $Res Function(SetUpTermDepositParams) then) =
      _$SetUpTermDepositParamsCopyWithImpl<$Res, SetUpTermDepositParams>;
}

/// @nodoc
class _$SetUpTermDepositParamsCopyWithImpl<$Res,
        $Val extends SetUpTermDepositParams>
    implements $SetUpTermDepositParamsCopyWith<$Res> {
  _$SetUpTermDepositParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SetUpTermDepositParams
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$SetUpTermDepositParamsImplCopyWith<$Res> {
  factory _$$SetUpTermDepositParamsImplCopyWith(
          _$SetUpTermDepositParamsImpl value,
          $Res Function(_$SetUpTermDepositParamsImpl) then) =
      __$$SetUpTermDepositParamsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SetUpTermDepositParamsImplCopyWithImpl<$Res>
    extends _$SetUpTermDepositParamsCopyWithImpl<$Res,
        _$SetUpTermDepositParamsImpl>
    implements _$$SetUpTermDepositParamsImplCopyWith<$Res> {
  __$$SetUpTermDepositParamsImplCopyWithImpl(
      _$SetUpTermDepositParamsImpl _value,
      $Res Function(_$SetUpTermDepositParamsImpl) _then)
      : super(_value, _then);

  /// Create a copy of SetUpTermDepositParams
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SetUpTermDepositParamsImpl implements _SetUpTermDepositParams {
  const _$SetUpTermDepositParamsImpl();

  @override
  String toString() {
    return 'SetUpTermDepositParams.fromDepositAccount()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetUpTermDepositParamsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fromDepositAccount,
    required TResult Function(SavingSpace savingSpace) fromSavingSpace,
    required TResult Function(FamilyMember coOwner) forFamily,
  }) {
    return fromDepositAccount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fromDepositAccount,
    TResult? Function(SavingSpace savingSpace)? fromSavingSpace,
    TResult? Function(FamilyMember coOwner)? forFamily,
  }) {
    return fromDepositAccount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fromDepositAccount,
    TResult Function(SavingSpace savingSpace)? fromSavingSpace,
    TResult Function(FamilyMember coOwner)? forFamily,
    required TResult orElse(),
  }) {
    if (fromDepositAccount != null) {
      return fromDepositAccount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetUpTermDepositParams value) fromDepositAccount,
    required TResult Function(_SetUpTermDepositFromSavingSpaceParams value)
        fromSavingSpace,
    required TResult Function(SetUpTermDepositForFamilyParams value) forFamily,
  }) {
    return fromDepositAccount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetUpTermDepositParams value)? fromDepositAccount,
    TResult? Function(_SetUpTermDepositFromSavingSpaceParams value)?
        fromSavingSpace,
    TResult? Function(SetUpTermDepositForFamilyParams value)? forFamily,
  }) {
    return fromDepositAccount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetUpTermDepositParams value)? fromDepositAccount,
    TResult Function(_SetUpTermDepositFromSavingSpaceParams value)?
        fromSavingSpace,
    TResult Function(SetUpTermDepositForFamilyParams value)? forFamily,
    required TResult orElse(),
  }) {
    if (fromDepositAccount != null) {
      return fromDepositAccount(this);
    }
    return orElse();
  }
}

abstract class _SetUpTermDepositParams implements SetUpTermDepositParams {
  const factory _SetUpTermDepositParams() = _$SetUpTermDepositParamsImpl;
}

/// @nodoc
abstract class _$$SetUpTermDepositFromSavingSpaceParamsImplCopyWith<$Res> {
  factory _$$SetUpTermDepositFromSavingSpaceParamsImplCopyWith(
          _$SetUpTermDepositFromSavingSpaceParamsImpl value,
          $Res Function(_$SetUpTermDepositFromSavingSpaceParamsImpl) then) =
      __$$SetUpTermDepositFromSavingSpaceParamsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({SavingSpace savingSpace});

  $SavingSpaceCopyWith<$Res> get savingSpace;
}

/// @nodoc
class __$$SetUpTermDepositFromSavingSpaceParamsImplCopyWithImpl<$Res>
    extends _$SetUpTermDepositParamsCopyWithImpl<$Res,
        _$SetUpTermDepositFromSavingSpaceParamsImpl>
    implements _$$SetUpTermDepositFromSavingSpaceParamsImplCopyWith<$Res> {
  __$$SetUpTermDepositFromSavingSpaceParamsImplCopyWithImpl(
      _$SetUpTermDepositFromSavingSpaceParamsImpl _value,
      $Res Function(_$SetUpTermDepositFromSavingSpaceParamsImpl) _then)
      : super(_value, _then);

  /// Create a copy of SetUpTermDepositParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? savingSpace = null,
  }) {
    return _then(_$SetUpTermDepositFromSavingSpaceParamsImpl(
      null == savingSpace
          ? _value.savingSpace
          : savingSpace // ignore: cast_nullable_to_non_nullable
              as SavingSpace,
    ));
  }

  /// Create a copy of SetUpTermDepositParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SavingSpaceCopyWith<$Res> get savingSpace {
    return $SavingSpaceCopyWith<$Res>(_value.savingSpace, (value) {
      return _then(_value.copyWith(savingSpace: value));
    });
  }
}

/// @nodoc

class _$SetUpTermDepositFromSavingSpaceParamsImpl
    implements _SetUpTermDepositFromSavingSpaceParams {
  const _$SetUpTermDepositFromSavingSpaceParamsImpl(this.savingSpace);

  @override
  final SavingSpace savingSpace;

  @override
  String toString() {
    return 'SetUpTermDepositParams.fromSavingSpace(savingSpace: $savingSpace)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetUpTermDepositFromSavingSpaceParamsImpl &&
            (identical(other.savingSpace, savingSpace) ||
                other.savingSpace == savingSpace));
  }

  @override
  int get hashCode => Object.hash(runtimeType, savingSpace);

  /// Create a copy of SetUpTermDepositParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SetUpTermDepositFromSavingSpaceParamsImplCopyWith<
          _$SetUpTermDepositFromSavingSpaceParamsImpl>
      get copyWith => __$$SetUpTermDepositFromSavingSpaceParamsImplCopyWithImpl<
          _$SetUpTermDepositFromSavingSpaceParamsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fromDepositAccount,
    required TResult Function(SavingSpace savingSpace) fromSavingSpace,
    required TResult Function(FamilyMember coOwner) forFamily,
  }) {
    return fromSavingSpace(savingSpace);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fromDepositAccount,
    TResult? Function(SavingSpace savingSpace)? fromSavingSpace,
    TResult? Function(FamilyMember coOwner)? forFamily,
  }) {
    return fromSavingSpace?.call(savingSpace);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fromDepositAccount,
    TResult Function(SavingSpace savingSpace)? fromSavingSpace,
    TResult Function(FamilyMember coOwner)? forFamily,
    required TResult orElse(),
  }) {
    if (fromSavingSpace != null) {
      return fromSavingSpace(savingSpace);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetUpTermDepositParams value) fromDepositAccount,
    required TResult Function(_SetUpTermDepositFromSavingSpaceParams value)
        fromSavingSpace,
    required TResult Function(SetUpTermDepositForFamilyParams value) forFamily,
  }) {
    return fromSavingSpace(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetUpTermDepositParams value)? fromDepositAccount,
    TResult? Function(_SetUpTermDepositFromSavingSpaceParams value)?
        fromSavingSpace,
    TResult? Function(SetUpTermDepositForFamilyParams value)? forFamily,
  }) {
    return fromSavingSpace?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetUpTermDepositParams value)? fromDepositAccount,
    TResult Function(_SetUpTermDepositFromSavingSpaceParams value)?
        fromSavingSpace,
    TResult Function(SetUpTermDepositForFamilyParams value)? forFamily,
    required TResult orElse(),
  }) {
    if (fromSavingSpace != null) {
      return fromSavingSpace(this);
    }
    return orElse();
  }
}

abstract class _SetUpTermDepositFromSavingSpaceParams
    implements SetUpTermDepositParams {
  const factory _SetUpTermDepositFromSavingSpaceParams(
          final SavingSpace savingSpace) =
      _$SetUpTermDepositFromSavingSpaceParamsImpl;

  SavingSpace get savingSpace;

  /// Create a copy of SetUpTermDepositParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SetUpTermDepositFromSavingSpaceParamsImplCopyWith<
          _$SetUpTermDepositFromSavingSpaceParamsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SetUpTermDepositForFamilyParamsImplCopyWith<$Res> {
  factory _$$SetUpTermDepositForFamilyParamsImplCopyWith(
          _$SetUpTermDepositForFamilyParamsImpl value,
          $Res Function(_$SetUpTermDepositForFamilyParamsImpl) then) =
      __$$SetUpTermDepositForFamilyParamsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({FamilyMember coOwner});

  $FamilyMemberCopyWith<$Res> get coOwner;
}

/// @nodoc
class __$$SetUpTermDepositForFamilyParamsImplCopyWithImpl<$Res>
    extends _$SetUpTermDepositParamsCopyWithImpl<$Res,
        _$SetUpTermDepositForFamilyParamsImpl>
    implements _$$SetUpTermDepositForFamilyParamsImplCopyWith<$Res> {
  __$$SetUpTermDepositForFamilyParamsImplCopyWithImpl(
      _$SetUpTermDepositForFamilyParamsImpl _value,
      $Res Function(_$SetUpTermDepositForFamilyParamsImpl) _then)
      : super(_value, _then);

  /// Create a copy of SetUpTermDepositParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? coOwner = null,
  }) {
    return _then(_$SetUpTermDepositForFamilyParamsImpl(
      coOwner: null == coOwner
          ? _value.coOwner
          : coOwner // ignore: cast_nullable_to_non_nullable
              as FamilyMember,
    ));
  }

  /// Create a copy of SetUpTermDepositParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FamilyMemberCopyWith<$Res> get coOwner {
    return $FamilyMemberCopyWith<$Res>(_value.coOwner, (value) {
      return _then(_value.copyWith(coOwner: value));
    });
  }
}

/// @nodoc

class _$SetUpTermDepositForFamilyParamsImpl
    implements SetUpTermDepositForFamilyParams {
  const _$SetUpTermDepositForFamilyParamsImpl({required this.coOwner});

  @override
  final FamilyMember coOwner;

  @override
  String toString() {
    return 'SetUpTermDepositParams.forFamily(coOwner: $coOwner)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SetUpTermDepositForFamilyParamsImpl &&
            (identical(other.coOwner, coOwner) || other.coOwner == coOwner));
  }

  @override
  int get hashCode => Object.hash(runtimeType, coOwner);

  /// Create a copy of SetUpTermDepositParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SetUpTermDepositForFamilyParamsImplCopyWith<
          _$SetUpTermDepositForFamilyParamsImpl>
      get copyWith => __$$SetUpTermDepositForFamilyParamsImplCopyWithImpl<
          _$SetUpTermDepositForFamilyParamsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() fromDepositAccount,
    required TResult Function(SavingSpace savingSpace) fromSavingSpace,
    required TResult Function(FamilyMember coOwner) forFamily,
  }) {
    return forFamily(coOwner);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? fromDepositAccount,
    TResult? Function(SavingSpace savingSpace)? fromSavingSpace,
    TResult? Function(FamilyMember coOwner)? forFamily,
  }) {
    return forFamily?.call(coOwner);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? fromDepositAccount,
    TResult Function(SavingSpace savingSpace)? fromSavingSpace,
    TResult Function(FamilyMember coOwner)? forFamily,
    required TResult orElse(),
  }) {
    if (forFamily != null) {
      return forFamily(coOwner);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SetUpTermDepositParams value) fromDepositAccount,
    required TResult Function(_SetUpTermDepositFromSavingSpaceParams value)
        fromSavingSpace,
    required TResult Function(SetUpTermDepositForFamilyParams value) forFamily,
  }) {
    return forFamily(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SetUpTermDepositParams value)? fromDepositAccount,
    TResult? Function(_SetUpTermDepositFromSavingSpaceParams value)?
        fromSavingSpace,
    TResult? Function(SetUpTermDepositForFamilyParams value)? forFamily,
  }) {
    return forFamily?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SetUpTermDepositParams value)? fromDepositAccount,
    TResult Function(_SetUpTermDepositFromSavingSpaceParams value)?
        fromSavingSpace,
    TResult Function(SetUpTermDepositForFamilyParams value)? forFamily,
    required TResult orElse(),
  }) {
    if (forFamily != null) {
      return forFamily(this);
    }
    return orElse();
  }
}

abstract class SetUpTermDepositForFamilyParams
    implements SetUpTermDepositParams {
  const factory SetUpTermDepositForFamilyParams(
          {required final FamilyMember coOwner}) =
      _$SetUpTermDepositForFamilyParamsImpl;

  FamilyMember get coOwner;

  /// Create a copy of SetUpTermDepositParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SetUpTermDepositForFamilyParamsImplCopyWith<
          _$SetUpTermDepositForFamilyParamsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$SetUpTermDepositPageNavigationConfig {
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(SetUpTermDepositParams params) $default, {
    required TResult Function() forFamily,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(SetUpTermDepositParams params)? $default, {
    TResult? Function()? forFamily,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(SetUpTermDepositParams params)? $default, {
    TResult Function()? forFamily,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SetUpTermDepositPageNavigationConfig value) $default, {
    required TResult Function(_SetUpTermDepositForFamilyConfig value) forFamily,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SetUpTermDepositPageNavigationConfig value)? $default, {
    TResult? Function(_SetUpTermDepositForFamilyConfig value)? forFamily,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SetUpTermDepositPageNavigationConfig value)? $default, {
    TResult Function(_SetUpTermDepositForFamilyConfig value)? forFamily,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SetUpTermDepositPageNavigationConfigCopyWith<$Res> {
  factory $SetUpTermDepositPageNavigationConfigCopyWith(
          SetUpTermDepositPageNavigationConfig value,
          $Res Function(SetUpTermDepositPageNavigationConfig) then) =
      _$SetUpTermDepositPageNavigationConfigCopyWithImpl<$Res,
          SetUpTermDepositPageNavigationConfig>;
}

/// @nodoc
class _$SetUpTermDepositPageNavigationConfigCopyWithImpl<$Res,
        $Val extends SetUpTermDepositPageNavigationConfig>
    implements $SetUpTermDepositPageNavigationConfigCopyWith<$Res> {
  _$SetUpTermDepositPageNavigationConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SetUpTermDepositPageNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$SetUpTermDepositForFamilyConfigImplCopyWith<$Res> {
  factory _$$SetUpTermDepositForFamilyConfigImplCopyWith(
          _$SetUpTermDepositForFamilyConfigImpl value,
          $Res Function(_$SetUpTermDepositForFamilyConfigImpl) then) =
      __$$SetUpTermDepositForFamilyConfigImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SetUpTermDepositForFamilyConfigImplCopyWithImpl<$Res>
    extends _$SetUpTermDepositPageNavigationConfigCopyWithImpl<$Res,
        _$SetUpTermDepositForFamilyConfigImpl>
    implements _$$SetUpTermDepositForFamilyConfigImplCopyWith<$Res> {
  __$$SetUpTermDepositForFamilyConfigImplCopyWithImpl(
      _$SetUpTermDepositForFamilyConfigImpl _value,
      $Res Function(_$SetUpTermDepositForFamilyConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of SetUpTermDepositPageNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SetUpTermDepositForFamilyConfigImpl
    extends _SetUpTermDepositForFamilyConfig {
  const _$SetUpTermDepositForFamilyConfigImpl() : super._();

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(SetUpTermDepositParams params) $default, {
    required TResult Function() forFamily,
  }) {
    return forFamily();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(SetUpTermDepositParams params)? $default, {
    TResult? Function()? forFamily,
  }) {
    return forFamily?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(SetUpTermDepositParams params)? $default, {
    TResult Function()? forFamily,
    required TResult orElse(),
  }) {
    if (forFamily != null) {
      return forFamily();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SetUpTermDepositPageNavigationConfig value) $default, {
    required TResult Function(_SetUpTermDepositForFamilyConfig value) forFamily,
  }) {
    return forFamily(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SetUpTermDepositPageNavigationConfig value)? $default, {
    TResult? Function(_SetUpTermDepositForFamilyConfig value)? forFamily,
  }) {
    return forFamily?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SetUpTermDepositPageNavigationConfig value)? $default, {
    TResult Function(_SetUpTermDepositForFamilyConfig value)? forFamily,
    required TResult orElse(),
  }) {
    if (forFamily != null) {
      return forFamily(this);
    }
    return orElse();
  }
}

abstract class _SetUpTermDepositForFamilyConfig
    extends SetUpTermDepositPageNavigationConfig {
  const factory _SetUpTermDepositForFamilyConfig() =
      _$SetUpTermDepositForFamilyConfigImpl;
  const _SetUpTermDepositForFamilyConfig._() : super._();
}

/// @nodoc
abstract class _$$SetUpTermDepositPageNavigationConfigImplCopyWith<$Res> {
  factory _$$SetUpTermDepositPageNavigationConfigImplCopyWith(
          _$SetUpTermDepositPageNavigationConfigImpl value,
          $Res Function(_$SetUpTermDepositPageNavigationConfigImpl) then) =
      __$$SetUpTermDepositPageNavigationConfigImplCopyWithImpl<$Res>;
  @useResult
  $Res call({SetUpTermDepositParams params});

  $SetUpTermDepositParamsCopyWith<$Res> get params;
}

/// @nodoc
class __$$SetUpTermDepositPageNavigationConfigImplCopyWithImpl<$Res>
    extends _$SetUpTermDepositPageNavigationConfigCopyWithImpl<$Res,
        _$SetUpTermDepositPageNavigationConfigImpl>
    implements _$$SetUpTermDepositPageNavigationConfigImplCopyWith<$Res> {
  __$$SetUpTermDepositPageNavigationConfigImplCopyWithImpl(
      _$SetUpTermDepositPageNavigationConfigImpl _value,
      $Res Function(_$SetUpTermDepositPageNavigationConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of SetUpTermDepositPageNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? params = null,
  }) {
    return _then(_$SetUpTermDepositPageNavigationConfigImpl(
      params: null == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as SetUpTermDepositParams,
    ));
  }

  /// Create a copy of SetUpTermDepositPageNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SetUpTermDepositParamsCopyWith<$Res> get params {
    return $SetUpTermDepositParamsCopyWith<$Res>(_value.params, (value) {
      return _then(_value.copyWith(params: value));
    });
  }
}

/// @nodoc

class _$SetUpTermDepositPageNavigationConfigImpl
    extends _SetUpTermDepositPageNavigationConfig {
  const _$SetUpTermDepositPageNavigationConfigImpl({required this.params})
      : super._();

  @override
  final SetUpTermDepositParams params;

  /// Create a copy of SetUpTermDepositPageNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SetUpTermDepositPageNavigationConfigImplCopyWith<
          _$SetUpTermDepositPageNavigationConfigImpl>
      get copyWith => __$$SetUpTermDepositPageNavigationConfigImplCopyWithImpl<
          _$SetUpTermDepositPageNavigationConfigImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(SetUpTermDepositParams params) $default, {
    required TResult Function() forFamily,
  }) {
    return $default(params);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(SetUpTermDepositParams params)? $default, {
    TResult? Function()? forFamily,
  }) {
    return $default?.call(params);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(SetUpTermDepositParams params)? $default, {
    TResult Function()? forFamily,
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(params);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SetUpTermDepositPageNavigationConfig value) $default, {
    required TResult Function(_SetUpTermDepositForFamilyConfig value) forFamily,
  }) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SetUpTermDepositPageNavigationConfig value)? $default, {
    TResult? Function(_SetUpTermDepositForFamilyConfig value)? forFamily,
  }) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SetUpTermDepositPageNavigationConfig value)? $default, {
    TResult Function(_SetUpTermDepositForFamilyConfig value)? forFamily,
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }
}

abstract class _SetUpTermDepositPageNavigationConfig
    extends SetUpTermDepositPageNavigationConfig {
  const factory _SetUpTermDepositPageNavigationConfig(
          {required final SetUpTermDepositParams params}) =
      _$SetUpTermDepositPageNavigationConfigImpl;
  const _SetUpTermDepositPageNavigationConfig._() : super._();

  SetUpTermDepositParams get params;

  /// Create a copy of SetUpTermDepositPageNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SetUpTermDepositPageNavigationConfigImplCopyWith<
          _$SetUpTermDepositPageNavigationConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}
