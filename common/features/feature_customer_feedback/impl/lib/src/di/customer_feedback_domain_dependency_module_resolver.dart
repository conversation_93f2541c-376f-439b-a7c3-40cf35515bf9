import 'package:di/di.dart';
import 'package:logging_api/logging.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_customer_feedback_api/index.dart';
import 'package:wio_feature_customer_feedback_impl/index.dart';

class CustomerFeedbackDomainDependencyModuleResolver {
  static const _customerFeedbackStorage = '_customer_feedback_storage';

  static Future<void> register() async {
    await _registerStorages();
    await _registerRepositories();
    await _registerInteractors();
  }

  static Future<void> _registerStorages() async {
    DependencyProvider.registerLazySingleton<KeyValueStorage>(
      () => DependencyProvider.get<KeyValueStorageFactory>(
        instanceName: sharedPrefsStorageKey,
      ).getStorage(_customerFeedbackStorage),
      instanceName: _customerFeedbackStorage,
    );
  }

  static Future<void> _registerRepositories() async {
    DependencyProvider.registerLazySingleton<CustomerFeedbackRepository>(
      () => CustomerFeedbackRepositoryImpl(
        storage: DependencyProvider.get<KeyValueStorage>(
          instanceName: _customerFeedbackStorage,
        ),
      ),
    );
  }

  static Future<void> _registerInteractors() async {
    DependencyProvider.registerLazySingleton<CustomerFeedbackInteractor>(
      () => CustomerFeedbackInteractorImpl(
        repository: DependencyProvider.get<CustomerFeedbackRepository>(),
        logger: DependencyProvider.get<Logger>(
          instanceName: WioDomain.core.name,
        ),
      ),
    );
  }
}
