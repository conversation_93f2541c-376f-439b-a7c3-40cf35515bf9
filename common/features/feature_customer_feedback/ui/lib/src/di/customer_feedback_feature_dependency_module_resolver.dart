import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:logging_api/logging.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_customer_feedback_api/index.dart';
import 'package:wio_feature_customer_feedback_ui/l10n/customer_feedback.g.dart';
import 'package:wio_feature_customer_feedback_ui/src/app_store/app_store_service_impl.dart';
import 'package:wio_feature_customer_feedback_ui/src/bottom_sheet/customer_feedback_bottom_sheet/analytics/customer_feedback_bottom_sheet_analytics.dart';
import 'package:wio_feature_customer_feedback_ui/src/bottom_sheet/customer_feedback_bottom_sheet/cubit/customer_feedback_bottom_sheet_cubit.dart';
import 'package:wio_feature_customer_feedback_ui/src/bottom_sheet/nps_feedback_bottom_sheet/analytics/nps_feedback_bottom_sheet_analytics.dart';
import 'package:wio_feature_customer_feedback_ui/src/bottom_sheet/nps_feedback_bottom_sheet/cubit/nps_feedback_bottom_sheet_cubit.dart';
import 'package:wio_feature_customer_feedback_ui/src/bottom_sheet/text_feedback_bottom_sheet/analytics/text_feedback_bottom_sheet_analytics.dart';
import 'package:wio_feature_customer_feedback_ui/src/bottom_sheet/text_feedback_bottom_sheet/cubit/text_feedback_bottom_sheet_cubit.dart';
import 'package:wio_feature_customer_feedback_ui/src/bottom_sheet/thank_you_for_feedback_bottom_sheet/analytics/thank_you_for_feedback_bottom_sheet_analytics.dart';
import 'package:wio_feature_customer_feedback_ui/src/bottom_sheet/thank_you_for_feedback_bottom_sheet/cubit/thank_you_for_feedback_bottom_sheet_cubit.dart';
import 'package:wio_feature_customer_feedback_ui/src/deeplink/customer_feedback_deep_link_handler.dart';
import 'package:wio_feature_customer_feedback_ui/src/flows/customer_feedback/customer_feedback_deep_link_flow_impl.dart';
import 'package:wio_feature_customer_feedback_ui/src/flows/customer_feedback/customer_feedback_flow_analytics.dart';
import 'package:wio_feature_customer_feedback_ui/src/flows/customer_feedback/customer_feedback_flow_controller.dart';
import 'package:wio_feature_customer_feedback_ui/src/navigation/customer_feedback_router.dart';
import 'package:wio_feature_customer_feedback_ui/src/screens/customer_survey/analytics/customer_survey_analytics.dart';
import 'package:wio_feature_customer_feedback_ui/src/screens/customer_survey/customer_survey_cubit.dart';
import 'package:wio_feature_user_api/index.dart';

class CustomerFeedbackFeatureDependencyModuleResolver {
  static Future<void> register() async {
    DependencyProvider.registerLazySingleton<CustomerFeedbackRouter>(
      CustomerFeedbackRouter.new,
    );

    DependencyProvider.registerLazySingleton<NavigationRouter>(
      () => DependencyProvider.get<CustomerFeedbackRouter>(),
      instanceName: CustomerFeedbackFeatureNavigationConfig.name,
    );

    DependencyProvider.registerLazySingleton(
      () => CustomerFeedbackLocalizations.of(
        DependencyProvider.get<BuildContext>(),
      ),
    );

    _registerAppStoreService();
    _registerCustomerFeedbackFlow();
    _registerCustomerFeedbackBottomSheet();
    _registerFeedbackBottomSheet();
    _registerThankYouForFeedbackBottomSheet();
    _registerCustomerSurvey();
    _registerDeepLinkHandler();
    _registerNpsFeedbackBottomSheet();
  }

  static void _registerAppStoreService() {
    DependencyProvider.registerLazySingleton<AppStoreService>(
      () => AppStoreServiceImpl(),
    );
  }

  static void _registerCustomerFeedbackFlow() {
    DependencyProvider.registerFactory<CustomerFeedbackFlowAnalytics>(
      () => CustomerFeedbackFlowAnalytics(
        customerFeedback:
            DependencyProvider.get<CustomerFeedbackBottomSheetAnalytics>(),
        feedback: DependencyProvider.get<TextFeedbackBottomSheetAnalytics>(),
        thankYouForFeedback:
            DependencyProvider.get<ThankYouForFeedbackBottomSheetAnalytics>(),
      ),
    );

    DependencyProvider.registerFactory(
      () => CustomerFeedbackFlowController(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        interactor: DependencyProvider.get<CustomerFeedbackInteractor>(),
        analytics: DependencyProvider.get<CustomerFeedbackFlowAnalytics>(),
        appStoreService: DependencyProvider.get<AppStoreService>(),
        userInteractor: DependencyProvider.get<UserInteractor>(),
        errorReporter: DependencyProvider.get<ErrorReporter>(),
        toggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        logger: DependencyProvider.get<Logger>(
          instanceName: WioDomain.core.name,
        ),
      ),
    );

    DependencyProvider.registerLazySingleton<CustomerFeedbackDeepLinkFlow>(
      () => CustomerFeedbackDeepLinkFlowImpl(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        localizations: DependencyProvider.get<CustomerFeedbackLocalizations>(),
      ),
    );
  }

  static void _registerNpsFeedbackBottomSheet() {
    DependencyProvider.registerFactory<NpsFeedbackBottomSheetCubit>(
      () => NpsFeedbackBottomSheetCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    DependencyProvider.registerFactory<NpsFeedbackBottomSheetAnalytics>(
      () => NpsFeedbackBottomSheetAnalytics(
        analyticsFactory:
            DependencyProvider.get<AnalyticsAbstractTrackerFactory>(),
      ),
    );
  }

  static void _registerCustomerFeedbackBottomSheet() {
    DependencyProvider.registerFactory<CustomerFeedbackBottomSheetCubit>(
      () => CustomerFeedbackBottomSheetCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    DependencyProvider.registerFactory<CustomerFeedbackBottomSheetAnalytics>(
      () => CustomerFeedbackBottomSheetAnalytics(
        analyticsFactory:
            DependencyProvider.get<AnalyticsAbstractTrackerFactory>(),
      ),
    );
  }

  static void _registerFeedbackBottomSheet() {
    DependencyProvider.registerFactory<TextFeedbackBottomSheetCubit>(
      () => TextFeedbackBottomSheetCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    DependencyProvider.registerFactory<TextFeedbackBottomSheetAnalytics>(
      () => TextFeedbackBottomSheetAnalytics(
        analyticsFactory:
            DependencyProvider.get<AnalyticsAbstractTrackerFactory>(),
      ),
    );
  }

  static void _registerThankYouForFeedbackBottomSheet() {
    DependencyProvider.registerFactory<ThankYouForFeedbackBottomSheetCubit>(
      () => ThankYouForFeedbackBottomSheetCubit(
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    DependencyProvider.registerFactory<ThankYouForFeedbackBottomSheetAnalytics>(
      () => ThankYouForFeedbackBottomSheetAnalytics(
        analyticsFactory:
            DependencyProvider.get<AnalyticsAbstractTrackerFactory>(),
      ),
    );
  }

  static void _registerDeepLinkHandler() {
    DependencyProvider.registerLazySingleton<CustomerFeedbackDeepLinkHandler>(
      () => CustomerFeedbackDeepLinkHandler(
        deepLinkFlow: DependencyProvider.get<CustomerFeedbackDeepLinkFlow>(),
      ),
    );

    DependencyProvider.get<DeepLinkHandlerRegister>().register(
      () => DependencyProvider.get<CustomerFeedbackDeepLinkHandler>(),
    );
  }

  static void _registerCustomerSurvey() {
    DependencyProvider.registerFactory<CustomerSurveyAnalytics>(
      () => CustomerSurveyAnalytics(
        analyticsFactory:
            DependencyProvider.get<AnalyticsAbstractTrackerFactory>(),
      ),
    );

    DependencyProvider.registerFactory<CustomerSurveyCubit>(
      () => CustomerSurveyCubit(
        localizations: DependencyProvider.get<CustomerFeedbackLocalizations>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        interactor: DependencyProvider.get<CustomerFeedbackInteractor>(),
        analytics: DependencyProvider.get<CustomerSurveyAnalytics>(),
      ),
    );
  }
}
