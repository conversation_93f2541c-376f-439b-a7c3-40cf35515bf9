import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:common_feature_toggle_impl/common_feature_toggle_impl.dart';
import 'package:logging_api/logging.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

import '../mocks.dart';

void main() {
  const testToggleKey = FeatureToggleKey<bool>(
    key: 'test_key',
    defaultValue: false,
  );

  late ServerFeatureToggleSourceImpl source;
  late ServerFeatureToggleService serverFeatureToggleService;
  late Logger logger;

  setUp(() {
    serverFeatureToggleService = MockBeDrivenService();
    logger = MockLogger();

    source = ServerFeatureToggleSourceImpl(
      serverFeatureToggleService: serverFeatureToggleService,
      logger: logger,
    );
  });

  test('Valid source type', () {
    expect(source.source, FeatureToggleSourceType.backend);
  });

  test('Should fetch feature toggles from service', () async {
    // Arrange
    when(() => serverFeatureToggleService.fetch()).thenAnswer((_) async => {});

    // Act
    await source.initialize();

    // Assert
    verify(() => serverFeatureToggleService.fetch());
  });

  test('Should return feature toggle values runtime variable cache', () async {
    // Arrange
    when(() => serverFeatureToggleService.fetch()).thenAnswer(
      (_) async => {
        'test_key': true,
      },
    );

    // Act
    await source.initialize();

    // Assert
    final result = source.get(testToggleKey);
    expect(result?.value, true);
  });

  test('Should return null for unknown flags', () async {
    // Arrange
    when(() => serverFeatureToggleService.fetch()).thenAnswer((_) async => {});

    // Act
    await source.initialize();

    // Assert
    final result = source.get(testToggleKey);
    expect(result, isNull);
  });

  test('Check what would be if type would be nullable', () async {
    // Arrange
    const nullableTestKey = FeatureToggleKey<bool?>(
      key: 'test_key',
      defaultValue: null,
    );

    when(() => serverFeatureToggleService.fetch()).thenAnswer(
      (_) async => {
        'test_key': true,
      },
    );

    // Act
    await source.initialize();

    // Assert
    final result = source.get(nullableTestKey);
    expect(result?.value, true);
  });

  test('Should throw an Exception if Type incorrect', () async {
    // Arrange
    const incorrectTypeTestKey = FeatureToggleKey<int>(
      key: 'test_key',
      defaultValue: 0,
    );

    when(() => serverFeatureToggleService.fetch()).thenAnswer(
      (_) async => {
        'test_key': true,
      },
    );

    // Act
    await source.initialize();

    // Assert
    expect(
      () => source.get(incorrectTypeTestKey),
      throwsA(isA<Exception>()),
    );
  });
}
