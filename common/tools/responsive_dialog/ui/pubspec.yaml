name: responsive_dialog_ui
version: 1.0.0
publish_to: none
environment: 
  sdk: '>=3.6.0 <4.0.0'
dependencies: 
  di: 
    path: ../../../../core/di
  flutter: 
    sdk: flutter
  freezed_annotation: 2.4.4
  ui_kit_legacy_core: 
    path: ../../../../ui_kit_legacy/ui_kit_core
  ui_kit_legacy_mobile: 
    path: ../../../../ui_kit_legacy/ui_kit_mobile
  wio_core_navigation_api:
    path: ../../../../core/navigation/api
  wio_feature_common_toast_message_api:
    path: ../../../../common/tools/toast_message/api
dev_dependencies:
  build_runner: 2.4.14
  core_lints:
    path: ../../../../tooling/core_lints
  freezed: 2.5.7
  lints: 4.0.0
  test: 1.25.8
