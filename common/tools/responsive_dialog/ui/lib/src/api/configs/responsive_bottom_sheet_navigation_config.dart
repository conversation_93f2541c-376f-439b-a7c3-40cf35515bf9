import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

class ResponsiveBottomSheetNavigationConfig
    extends BottomSheetNavigationConfig<void> {
  final String featureName;

  const ResponsiveBottomSheetNavigationConfig({
    required this.featureName,
  });

  @override
  String get feature => featureName;

  @override
  String toString() => 'ResponsiveBottomSheetNavigationConfig';
}
