// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'responsive_modal_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ResponsiveModalConfig {
  String get featureName => throw _privateConstructorUsedError;
  ResponsiveBottomSheetConfig get bottomSheetConfig =>
      throw _privateConstructorUsedError;
  ResponsiveDialogConfig get dialogConfig => throw _privateConstructorUsedError;
  double get mobileWidthBreakpoint => throw _privateConstructorUsedError;

  /// Create a copy of ResponsiveModalConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ResponsiveModalConfigCopyWith<ResponsiveModalConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ResponsiveModalConfigCopyWith<$Res> {
  factory $ResponsiveModalConfigCopyWith(ResponsiveModalConfig value,
          $Res Function(ResponsiveModalConfig) then) =
      _$ResponsiveModalConfigCopyWithImpl<$Res, ResponsiveModalConfig>;
  @useResult
  $Res call(
      {String featureName,
      ResponsiveBottomSheetConfig bottomSheetConfig,
      ResponsiveDialogConfig dialogConfig,
      double mobileWidthBreakpoint});

  $ResponsiveBottomSheetConfigCopyWith<$Res> get bottomSheetConfig;
  $ResponsiveDialogConfigCopyWith<$Res> get dialogConfig;
}

/// @nodoc
class _$ResponsiveModalConfigCopyWithImpl<$Res,
        $Val extends ResponsiveModalConfig>
    implements $ResponsiveModalConfigCopyWith<$Res> {
  _$ResponsiveModalConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ResponsiveModalConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? featureName = null,
    Object? bottomSheetConfig = null,
    Object? dialogConfig = null,
    Object? mobileWidthBreakpoint = null,
  }) {
    return _then(_value.copyWith(
      featureName: null == featureName
          ? _value.featureName
          : featureName // ignore: cast_nullable_to_non_nullable
              as String,
      bottomSheetConfig: null == bottomSheetConfig
          ? _value.bottomSheetConfig
          : bottomSheetConfig // ignore: cast_nullable_to_non_nullable
              as ResponsiveBottomSheetConfig,
      dialogConfig: null == dialogConfig
          ? _value.dialogConfig
          : dialogConfig // ignore: cast_nullable_to_non_nullable
              as ResponsiveDialogConfig,
      mobileWidthBreakpoint: null == mobileWidthBreakpoint
          ? _value.mobileWidthBreakpoint
          : mobileWidthBreakpoint // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }

  /// Create a copy of ResponsiveModalConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ResponsiveBottomSheetConfigCopyWith<$Res> get bottomSheetConfig {
    return $ResponsiveBottomSheetConfigCopyWith<$Res>(_value.bottomSheetConfig,
        (value) {
      return _then(_value.copyWith(bottomSheetConfig: value) as $Val);
    });
  }

  /// Create a copy of ResponsiveModalConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ResponsiveDialogConfigCopyWith<$Res> get dialogConfig {
    return $ResponsiveDialogConfigCopyWith<$Res>(_value.dialogConfig, (value) {
      return _then(_value.copyWith(dialogConfig: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ResponsiveModalConfigImplCopyWith<$Res>
    implements $ResponsiveModalConfigCopyWith<$Res> {
  factory _$$ResponsiveModalConfigImplCopyWith(
          _$ResponsiveModalConfigImpl value,
          $Res Function(_$ResponsiveModalConfigImpl) then) =
      __$$ResponsiveModalConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String featureName,
      ResponsiveBottomSheetConfig bottomSheetConfig,
      ResponsiveDialogConfig dialogConfig,
      double mobileWidthBreakpoint});

  @override
  $ResponsiveBottomSheetConfigCopyWith<$Res> get bottomSheetConfig;
  @override
  $ResponsiveDialogConfigCopyWith<$Res> get dialogConfig;
}

/// @nodoc
class __$$ResponsiveModalConfigImplCopyWithImpl<$Res>
    extends _$ResponsiveModalConfigCopyWithImpl<$Res,
        _$ResponsiveModalConfigImpl>
    implements _$$ResponsiveModalConfigImplCopyWith<$Res> {
  __$$ResponsiveModalConfigImplCopyWithImpl(_$ResponsiveModalConfigImpl _value,
      $Res Function(_$ResponsiveModalConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResponsiveModalConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? featureName = null,
    Object? bottomSheetConfig = null,
    Object? dialogConfig = null,
    Object? mobileWidthBreakpoint = null,
  }) {
    return _then(_$ResponsiveModalConfigImpl(
      featureName: null == featureName
          ? _value.featureName
          : featureName // ignore: cast_nullable_to_non_nullable
              as String,
      bottomSheetConfig: null == bottomSheetConfig
          ? _value.bottomSheetConfig
          : bottomSheetConfig // ignore: cast_nullable_to_non_nullable
              as ResponsiveBottomSheetConfig,
      dialogConfig: null == dialogConfig
          ? _value.dialogConfig
          : dialogConfig // ignore: cast_nullable_to_non_nullable
              as ResponsiveDialogConfig,
      mobileWidthBreakpoint: null == mobileWidthBreakpoint
          ? _value.mobileWidthBreakpoint
          : mobileWidthBreakpoint // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$ResponsiveModalConfigImpl implements _ResponsiveModalConfig {
  const _$ResponsiveModalConfigImpl(
      {required this.featureName,
      this.bottomSheetConfig = const ResponsiveBottomSheetConfig(),
      this.dialogConfig = const ResponsiveDialogConfig(),
      this.mobileWidthBreakpoint = 450.0});

  @override
  final String featureName;
  @override
  @JsonKey()
  final ResponsiveBottomSheetConfig bottomSheetConfig;
  @override
  @JsonKey()
  final ResponsiveDialogConfig dialogConfig;
  @override
  @JsonKey()
  final double mobileWidthBreakpoint;

  @override
  String toString() {
    return 'ResponsiveModalConfig(featureName: $featureName, bottomSheetConfig: $bottomSheetConfig, dialogConfig: $dialogConfig, mobileWidthBreakpoint: $mobileWidthBreakpoint)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResponsiveModalConfigImpl &&
            (identical(other.featureName, featureName) ||
                other.featureName == featureName) &&
            (identical(other.bottomSheetConfig, bottomSheetConfig) ||
                other.bottomSheetConfig == bottomSheetConfig) &&
            (identical(other.dialogConfig, dialogConfig) ||
                other.dialogConfig == dialogConfig) &&
            (identical(other.mobileWidthBreakpoint, mobileWidthBreakpoint) ||
                other.mobileWidthBreakpoint == mobileWidthBreakpoint));
  }

  @override
  int get hashCode => Object.hash(runtimeType, featureName, bottomSheetConfig,
      dialogConfig, mobileWidthBreakpoint);

  /// Create a copy of ResponsiveModalConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ResponsiveModalConfigImplCopyWith<_$ResponsiveModalConfigImpl>
      get copyWith => __$$ResponsiveModalConfigImplCopyWithImpl<
          _$ResponsiveModalConfigImpl>(this, _$identity);
}

abstract class _ResponsiveModalConfig implements ResponsiveModalConfig {
  const factory _ResponsiveModalConfig(
      {required final String featureName,
      final ResponsiveBottomSheetConfig bottomSheetConfig,
      final ResponsiveDialogConfig dialogConfig,
      final double mobileWidthBreakpoint}) = _$ResponsiveModalConfigImpl;

  @override
  String get featureName;
  @override
  ResponsiveBottomSheetConfig get bottomSheetConfig;
  @override
  ResponsiveDialogConfig get dialogConfig;
  @override
  double get mobileWidthBreakpoint;

  /// Create a copy of ResponsiveModalConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ResponsiveModalConfigImplCopyWith<_$ResponsiveModalConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ResponsiveBottomSheetConfig {
  double get paddingTop => throw _privateConstructorUsedError;
  bool get enableDrag => throw _privateConstructorUsedError;
  bool get useRootNavigator => throw _privateConstructorUsedError;
  CompanyBottomSheetModel get model => throw _privateConstructorUsedError;

  /// Create a copy of ResponsiveBottomSheetConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ResponsiveBottomSheetConfigCopyWith<ResponsiveBottomSheetConfig>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ResponsiveBottomSheetConfigCopyWith<$Res> {
  factory $ResponsiveBottomSheetConfigCopyWith(
          ResponsiveBottomSheetConfig value,
          $Res Function(ResponsiveBottomSheetConfig) then) =
      _$ResponsiveBottomSheetConfigCopyWithImpl<$Res,
          ResponsiveBottomSheetConfig>;
  @useResult
  $Res call(
      {double paddingTop,
      bool enableDrag,
      bool useRootNavigator,
      CompanyBottomSheetModel model});

  $CompanyBottomSheetModelCopyWith<$Res> get model;
}

/// @nodoc
class _$ResponsiveBottomSheetConfigCopyWithImpl<$Res,
        $Val extends ResponsiveBottomSheetConfig>
    implements $ResponsiveBottomSheetConfigCopyWith<$Res> {
  _$ResponsiveBottomSheetConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ResponsiveBottomSheetConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paddingTop = null,
    Object? enableDrag = null,
    Object? useRootNavigator = null,
    Object? model = null,
  }) {
    return _then(_value.copyWith(
      paddingTop: null == paddingTop
          ? _value.paddingTop
          : paddingTop // ignore: cast_nullable_to_non_nullable
              as double,
      enableDrag: null == enableDrag
          ? _value.enableDrag
          : enableDrag // ignore: cast_nullable_to_non_nullable
              as bool,
      useRootNavigator: null == useRootNavigator
          ? _value.useRootNavigator
          : useRootNavigator // ignore: cast_nullable_to_non_nullable
              as bool,
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as CompanyBottomSheetModel,
    ) as $Val);
  }

  /// Create a copy of ResponsiveBottomSheetConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CompanyBottomSheetModelCopyWith<$Res> get model {
    return $CompanyBottomSheetModelCopyWith<$Res>(_value.model, (value) {
      return _then(_value.copyWith(model: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ResponsiveBottomSheetConfigImplCopyWith<$Res>
    implements $ResponsiveBottomSheetConfigCopyWith<$Res> {
  factory _$$ResponsiveBottomSheetConfigImplCopyWith(
          _$ResponsiveBottomSheetConfigImpl value,
          $Res Function(_$ResponsiveBottomSheetConfigImpl) then) =
      __$$ResponsiveBottomSheetConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double paddingTop,
      bool enableDrag,
      bool useRootNavigator,
      CompanyBottomSheetModel model});

  @override
  $CompanyBottomSheetModelCopyWith<$Res> get model;
}

/// @nodoc
class __$$ResponsiveBottomSheetConfigImplCopyWithImpl<$Res>
    extends _$ResponsiveBottomSheetConfigCopyWithImpl<$Res,
        _$ResponsiveBottomSheetConfigImpl>
    implements _$$ResponsiveBottomSheetConfigImplCopyWith<$Res> {
  __$$ResponsiveBottomSheetConfigImplCopyWithImpl(
      _$ResponsiveBottomSheetConfigImpl _value,
      $Res Function(_$ResponsiveBottomSheetConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResponsiveBottomSheetConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paddingTop = null,
    Object? enableDrag = null,
    Object? useRootNavigator = null,
    Object? model = null,
  }) {
    return _then(_$ResponsiveBottomSheetConfigImpl(
      paddingTop: null == paddingTop
          ? _value.paddingTop
          : paddingTop // ignore: cast_nullable_to_non_nullable
              as double,
      enableDrag: null == enableDrag
          ? _value.enableDrag
          : enableDrag // ignore: cast_nullable_to_non_nullable
              as bool,
      useRootNavigator: null == useRootNavigator
          ? _value.useRootNavigator
          : useRootNavigator // ignore: cast_nullable_to_non_nullable
              as bool,
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as CompanyBottomSheetModel,
    ));
  }
}

/// @nodoc

class _$ResponsiveBottomSheetConfigImpl
    implements _ResponsiveBottomSheetConfig {
  const _$ResponsiveBottomSheetConfigImpl(
      {this.paddingTop = 16.0,
      this.enableDrag = true,
      this.useRootNavigator = false,
      this.model = const CompanyBottomSheetModel()});

  @override
  @JsonKey()
  final double paddingTop;
  @override
  @JsonKey()
  final bool enableDrag;
  @override
  @JsonKey()
  final bool useRootNavigator;
  @override
  @JsonKey()
  final CompanyBottomSheetModel model;

  @override
  String toString() {
    return 'ResponsiveBottomSheetConfig(paddingTop: $paddingTop, enableDrag: $enableDrag, useRootNavigator: $useRootNavigator, model: $model)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResponsiveBottomSheetConfigImpl &&
            (identical(other.paddingTop, paddingTop) ||
                other.paddingTop == paddingTop) &&
            (identical(other.enableDrag, enableDrag) ||
                other.enableDrag == enableDrag) &&
            (identical(other.useRootNavigator, useRootNavigator) ||
                other.useRootNavigator == useRootNavigator) &&
            (identical(other.model, model) || other.model == model));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, paddingTop, enableDrag, useRootNavigator, model);

  /// Create a copy of ResponsiveBottomSheetConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ResponsiveBottomSheetConfigImplCopyWith<_$ResponsiveBottomSheetConfigImpl>
      get copyWith => __$$ResponsiveBottomSheetConfigImplCopyWithImpl<
          _$ResponsiveBottomSheetConfigImpl>(this, _$identity);
}

abstract class _ResponsiveBottomSheetConfig
    implements ResponsiveBottomSheetConfig {
  const factory _ResponsiveBottomSheetConfig(
      {final double paddingTop,
      final bool enableDrag,
      final bool useRootNavigator,
      final CompanyBottomSheetModel model}) = _$ResponsiveBottomSheetConfigImpl;

  @override
  double get paddingTop;
  @override
  bool get enableDrag;
  @override
  bool get useRootNavigator;
  @override
  CompanyBottomSheetModel get model;

  /// Create a copy of ResponsiveBottomSheetConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ResponsiveBottomSheetConfigImplCopyWith<_$ResponsiveBottomSheetConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ResponsiveDialogConfig {
  CompanyColorPointer get backgroundColor => throw _privateConstructorUsedError;
  EdgeInsets get contentPadding => throw _privateConstructorUsedError;
  BorderRadius get borderRadius => throw _privateConstructorUsedError;
  EdgeInsets get closeButtonPadding => throw _privateConstructorUsedError;
  bool get showCloseButton => throw _privateConstructorUsedError;
  double get maxWidth => throw _privateConstructorUsedError;
  double get maxHeight => throw _privateConstructorUsedError;

  /// Create a copy of ResponsiveDialogConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ResponsiveDialogConfigCopyWith<ResponsiveDialogConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ResponsiveDialogConfigCopyWith<$Res> {
  factory $ResponsiveDialogConfigCopyWith(ResponsiveDialogConfig value,
          $Res Function(ResponsiveDialogConfig) then) =
      _$ResponsiveDialogConfigCopyWithImpl<$Res, ResponsiveDialogConfig>;
  @useResult
  $Res call(
      {CompanyColorPointer backgroundColor,
      EdgeInsets contentPadding,
      BorderRadius borderRadius,
      EdgeInsets closeButtonPadding,
      bool showCloseButton,
      double maxWidth,
      double maxHeight});
}

/// @nodoc
class _$ResponsiveDialogConfigCopyWithImpl<$Res,
        $Val extends ResponsiveDialogConfig>
    implements $ResponsiveDialogConfigCopyWith<$Res> {
  _$ResponsiveDialogConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ResponsiveDialogConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? backgroundColor = null,
    Object? contentPadding = null,
    Object? borderRadius = null,
    Object? closeButtonPadding = null,
    Object? showCloseButton = null,
    Object? maxWidth = null,
    Object? maxHeight = null,
  }) {
    return _then(_value.copyWith(
      backgroundColor: null == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as CompanyColorPointer,
      contentPadding: null == contentPadding
          ? _value.contentPadding
          : contentPadding // ignore: cast_nullable_to_non_nullable
              as EdgeInsets,
      borderRadius: null == borderRadius
          ? _value.borderRadius
          : borderRadius // ignore: cast_nullable_to_non_nullable
              as BorderRadius,
      closeButtonPadding: null == closeButtonPadding
          ? _value.closeButtonPadding
          : closeButtonPadding // ignore: cast_nullable_to_non_nullable
              as EdgeInsets,
      showCloseButton: null == showCloseButton
          ? _value.showCloseButton
          : showCloseButton // ignore: cast_nullable_to_non_nullable
              as bool,
      maxWidth: null == maxWidth
          ? _value.maxWidth
          : maxWidth // ignore: cast_nullable_to_non_nullable
              as double,
      maxHeight: null == maxHeight
          ? _value.maxHeight
          : maxHeight // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ResponsiveDialogConfigImplCopyWith<$Res>
    implements $ResponsiveDialogConfigCopyWith<$Res> {
  factory _$$ResponsiveDialogConfigImplCopyWith(
          _$ResponsiveDialogConfigImpl value,
          $Res Function(_$ResponsiveDialogConfigImpl) then) =
      __$$ResponsiveDialogConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CompanyColorPointer backgroundColor,
      EdgeInsets contentPadding,
      BorderRadius borderRadius,
      EdgeInsets closeButtonPadding,
      bool showCloseButton,
      double maxWidth,
      double maxHeight});
}

/// @nodoc
class __$$ResponsiveDialogConfigImplCopyWithImpl<$Res>
    extends _$ResponsiveDialogConfigCopyWithImpl<$Res,
        _$ResponsiveDialogConfigImpl>
    implements _$$ResponsiveDialogConfigImplCopyWith<$Res> {
  __$$ResponsiveDialogConfigImplCopyWithImpl(
      _$ResponsiveDialogConfigImpl _value,
      $Res Function(_$ResponsiveDialogConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResponsiveDialogConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? backgroundColor = null,
    Object? contentPadding = null,
    Object? borderRadius = null,
    Object? closeButtonPadding = null,
    Object? showCloseButton = null,
    Object? maxWidth = null,
    Object? maxHeight = null,
  }) {
    return _then(_$ResponsiveDialogConfigImpl(
      backgroundColor: null == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as CompanyColorPointer,
      contentPadding: null == contentPadding
          ? _value.contentPadding
          : contentPadding // ignore: cast_nullable_to_non_nullable
              as EdgeInsets,
      borderRadius: null == borderRadius
          ? _value.borderRadius
          : borderRadius // ignore: cast_nullable_to_non_nullable
              as BorderRadius,
      closeButtonPadding: null == closeButtonPadding
          ? _value.closeButtonPadding
          : closeButtonPadding // ignore: cast_nullable_to_non_nullable
              as EdgeInsets,
      showCloseButton: null == showCloseButton
          ? _value.showCloseButton
          : showCloseButton // ignore: cast_nullable_to_non_nullable
              as bool,
      maxWidth: null == maxWidth
          ? _value.maxWidth
          : maxWidth // ignore: cast_nullable_to_non_nullable
              as double,
      maxHeight: null == maxHeight
          ? _value.maxHeight
          : maxHeight // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$ResponsiveDialogConfigImpl implements _ResponsiveDialogConfig {
  const _$ResponsiveDialogConfigImpl(
      {this.backgroundColor = CompanyColorPointer.background1,
      this.contentPadding = const EdgeInsets.all(24),
      this.borderRadius = const BorderRadius.all(Radius.circular(24)),
      this.closeButtonPadding = const EdgeInsets.only(top: 16.0, right: 16.0),
      this.showCloseButton = true,
      this.maxWidth = 600,
      this.maxHeight = double.infinity});

  @override
  @JsonKey()
  final CompanyColorPointer backgroundColor;
  @override
  @JsonKey()
  final EdgeInsets contentPadding;
  @override
  @JsonKey()
  final BorderRadius borderRadius;
  @override
  @JsonKey()
  final EdgeInsets closeButtonPadding;
  @override
  @JsonKey()
  final bool showCloseButton;
  @override
  @JsonKey()
  final double maxWidth;
  @override
  @JsonKey()
  final double maxHeight;

  @override
  String toString() {
    return 'ResponsiveDialogConfig(backgroundColor: $backgroundColor, contentPadding: $contentPadding, borderRadius: $borderRadius, closeButtonPadding: $closeButtonPadding, showCloseButton: $showCloseButton, maxWidth: $maxWidth, maxHeight: $maxHeight)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResponsiveDialogConfigImpl &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.contentPadding, contentPadding) ||
                other.contentPadding == contentPadding) &&
            (identical(other.borderRadius, borderRadius) ||
                other.borderRadius == borderRadius) &&
            (identical(other.closeButtonPadding, closeButtonPadding) ||
                other.closeButtonPadding == closeButtonPadding) &&
            (identical(other.showCloseButton, showCloseButton) ||
                other.showCloseButton == showCloseButton) &&
            (identical(other.maxWidth, maxWidth) ||
                other.maxWidth == maxWidth) &&
            (identical(other.maxHeight, maxHeight) ||
                other.maxHeight == maxHeight));
  }

  @override
  int get hashCode => Object.hash(runtimeType, backgroundColor, contentPadding,
      borderRadius, closeButtonPadding, showCloseButton, maxWidth, maxHeight);

  /// Create a copy of ResponsiveDialogConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ResponsiveDialogConfigImplCopyWith<_$ResponsiveDialogConfigImpl>
      get copyWith => __$$ResponsiveDialogConfigImplCopyWithImpl<
          _$ResponsiveDialogConfigImpl>(this, _$identity);
}

abstract class _ResponsiveDialogConfig implements ResponsiveDialogConfig {
  const factory _ResponsiveDialogConfig(
      {final CompanyColorPointer backgroundColor,
      final EdgeInsets contentPadding,
      final BorderRadius borderRadius,
      final EdgeInsets closeButtonPadding,
      final bool showCloseButton,
      final double maxWidth,
      final double maxHeight}) = _$ResponsiveDialogConfigImpl;

  @override
  CompanyColorPointer get backgroundColor;
  @override
  EdgeInsets get contentPadding;
  @override
  BorderRadius get borderRadius;
  @override
  EdgeInsets get closeButtonPadding;
  @override
  bool get showCloseButton;
  @override
  double get maxWidth;
  @override
  double get maxHeight;

  /// Create a copy of ResponsiveDialogConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ResponsiveDialogConfigImplCopyWith<_$ResponsiveDialogConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}
