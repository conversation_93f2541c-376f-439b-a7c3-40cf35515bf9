import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

part 'responsive_modal_config.freezed.dart';

@freezed
class ResponsiveModalConfig with _$ResponsiveModalConfig {
  const factory ResponsiveModalConfig({
    required String featureName,
    @Default(ResponsiveBottomSheetConfig())
    ResponsiveBottomSheetConfig bottomSheetConfig,
    @Default(ResponsiveDialogConfig()) ResponsiveDialogConfig dialogConfig,
    @Default(450.0) double mobileWidthBreakpoint,
  }) = _ResponsiveModalConfig;
}

@freezed
class ResponsiveBottomSheetConfig with _$ResponsiveBottomSheetConfig {
  const factory ResponsiveBottomSheetConfig({
    @Default(16.0) double paddingTop,
    @Default(true) bool enableDrag,
    @Default(false) bool useRootNavigator,
    @Default(CompanyBottomSheetModel()) CompanyBottomSheetModel model,
  }) = _ResponsiveBottomSheetConfig;
}

@freezed
class ResponsiveDialogConfig with _$ResponsiveDialogConfig {
  const factory ResponsiveDialogConfig({
    @Default(CompanyColorPointer.background1)
    CompanyColorPointer backgroundColor,
    @Default(EdgeInsets.all(24)) EdgeInsets contentPadding,
    @Default(BorderRadius.all(Radius.circular(24))) BorderRadius borderRadius,
    @Default(EdgeInsets.only(top: 16.0, right: 16.0))
    EdgeInsets closeButtonPadding,
    @Default(true) bool showCloseButton,
    @Default(600) double maxWidth,
    @Default(double.infinity) double maxHeight,
  }) = _ResponsiveDialogConfig;
}
