import 'package:flutter/material.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';

abstract class ResponsiveDialogProvider {
  /// Displays either a bottom sheet or a dialog depending on the screen width.
  /// For mobile devices (width <= mobileWidthBreakpoint),
  /// it shows a bottom sheet with the specified content and configuration.
  /// For wider screens, it shows a dialog.
  ///
  /// - [content]: The widget to display inside the modal.
  /// - [config]: Configuration settings for the responsive modal,
  /// including bottom sheet and dialog options.
  Future<T?> showBottomSheetOrDialog<T>({
    required Widget content,
    required ResponsiveModalConfig config,
  });

  /// Displays a responsive toast message based on the screen width.
  /// Shows a standard toast message at the top of the screen on mobile devices,
  /// and a web-specific, toast at the bottom of the screen on wider
  /// screens by default, its position can be configured through [config].
  ///
  /// - [toastConfig]: The toast message configuration settings.
  /// - [config]: The responsive configuration for the toast,
  /// allowing customization for web display options.
  void showResponsiveToastMessage(
    ToastMessageConfiguration toastConfig, {
    ResponsiveToastConfig config = const ResponsiveToastConfig(),
  });
}
