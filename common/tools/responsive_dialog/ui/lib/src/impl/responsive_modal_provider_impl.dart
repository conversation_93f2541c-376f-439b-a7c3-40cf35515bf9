import 'package:flutter/material.dart';
import 'package:responsive_dialog_ui/index.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_common_toast_message_api/common_toast_message_api.dart';

class ResponsiveDialogProviderImpl implements ResponsiveDialogProvider {
  final BuildContext _context;
  final ToastMessageProvider _toastMessageProvider;

  const ResponsiveDialogProviderImpl(this._context, this._toastMessageProvider);

  @override
  Future<T?> showBottomSheetOrDialog<T>({
    required Widget content,
    required ResponsiveModalConfig config,
  }) {
    if (_isWebLayout(config.mobileWidthBreakpoint)) {
      return _showDialog<T>(content, config.dialogConfig, config.featureName);
    } else {
      return _showBottomSheet<T>(
        content,
        config.bottomSheetConfig,
        config.featureName,
      );
    }
  }

  @override
  void showResponsiveToastMessage(
    ToastMessageConfiguration toastConfig, {
    ResponsiveToastConfig config = const ResponsiveToastConfig(),
  }) {
    if (_isWebLayout(config.mobileWidthBreakpoint)) {
      _toastMessageProvider.showWebToastMessage(
        toastConfig,
        toastAlignmentConfiguration: config.webToastAlignmentConfig,
      );
    } else {
      _toastMessageProvider.showRetailMobileThemedToastMessage(toastConfig);
    }
  }

  bool _isWebLayout(double mobileWidthBreakpoint) {
    return MediaQuery.sizeOf(_context).width > mobileWidthBreakpoint;
  }

  Future<T?> _showBottomSheet<T>(
    Widget content,
    ResponsiveBottomSheetConfig bottomSheetConfig,
    String featureName,
  ) {
    return CompanyBottomSheet.showRetailMobileThemeBasedAdaptiveModal<T>(
      context: _context,
      model: bottomSheetConfig.model,
      paddingTop: bottomSheetConfig.paddingTop,
      enableDrag: bottomSheetConfig.enableDrag,
      useRootNavigator: bottomSheetConfig.useRootNavigator,
      builder: (_) => SingleChildScrollView(child: content),
      routeSettings: RouteSettings(
        arguments: ResponsiveBottomSheetNavigationConfig(
          featureName: featureName,
        ),
      ),
    );
  }

  Future<T?> _showDialog<T>(
    Widget content,
    ResponsiveDialogConfig dialogConfig,
    String featureName,
  ) {
    return showDialog<T>(
      context: _context,
      routeSettings: RouteSettings(
        arguments: ResponsiveDialogNavigationConfig(
          featureName: featureName,
        ),
      ),
      builder: (context) {
        return Dialog(
          backgroundColor: dialogConfig.backgroundColor.colorOf(context),
          shape: RoundedRectangleBorder(
            borderRadius: dialogConfig.borderRadius,
          ),
          child: Container(
            constraints: BoxConstraints(
              maxWidth: dialogConfig.maxWidth,
              maxHeight: dialogConfig.maxHeight,
            ),
            child: Stack(
              children: [
                SingleChildScrollView(
                  child: Padding(
                    padding: dialogConfig.contentPadding,
                    child: content,
                  ),
                ),
                if (dialogConfig.showCloseButton)
                  Positioned(
                    top: dialogConfig.closeButtonPadding.top,
                    right: dialogConfig.closeButtonPadding.right,
                    child: GestureDetector(
                      onTap: Navigator.of(context).pop,
                      child: Icon(
                        CompanyIconFactory.get(CompanyIconPointer.close),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
